
<p align="center">
 <img alt="JeeSite" src="https://jeesite.com/assets/images/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>
<h3 align="center" style="margin:30px 0 30px;font-weight:bold;font-size:30px;">快速开发平台 - Spring Boot</h3>
<p align="center">
 <a href="https://jeesite.com/docs/upgrade/" target="__blank"><img alt="JeeSite-5.9" src="https://img.shields.io/badge/JeeSite-V5.9-success.svg"></a>
 <a href="https://spring.io/projects/spring-boot#learn" target="__blank"><img alt="SpringBoot-2.7/3.2" src="https://img.shields.io/badge/SpringBoot-2.7/3.2-blue.svg"></a>
 <a href="https://gitee.com/thinkgem/jeesite5/stargazers" target="__blank"><img alt="star" src="https://gitee.com/thinkgem/jeesite5/badge/star.svg?theme=dark"></a>
 <a href="https://gitee.com/thinkgem/jeesite-vue/stargazers" target="__blank"><img alt="star" src="https://gitee.com/thinkgem/jeesite-vue/badge/star.svg?theme=dark"></a>
</p>

------

<div align="center">
 如果你喜欢 JeeSite，请给她一个 ⭐️ Star，您的支持将是我们前行的动力。
</div>

------

## 技术交流

* 官方网站：<https://jeesite.com>
* 使用文档：<https://jeesite.com/docs>
* 问题反馈：<http://jeesite.net> [【新手必读】](https://gitee.com/thinkgem/jeesite5/issues/I18ARR)
* 需求收集：<https://gitee.com/thinkgem/jeesite5/issues/new>
* QQ 群：`127515876`、`209330483`、`223507718`、`709534275`、`730390092`、`1373527`、`183903863(外包)`
* 微信群：添加客服微信 <http://s.jeesite.com> 邀请您进群
* 关注微信公众号，了解最新动态：

<p style="padding-left:40px">　　
 <img alt="JeeSite微信公众号" src="https://jeesite.com/assets/images/mp.png" width="200">
</p>

* 源码仓库地址1：<https://gitee.com/thinkgem/jeesite5>
* 源码仓库地址2：<https://github.com/thinkgem/jeesite5>
* 源码仓库地址3：<https://gitcode.com/thinkgem/jeesite5>
* 分离版前端源码仓库地址1：<https://gitee.com/thinkgem/jeesite-vue>
* 分离版前端源码仓库地址2：<https://github.com/thinkgem/jeesite-vue>
* 分离版前端源码仓库地址3：<https://gitcode.com/thinkgem/jeesite-vue>

## 平台介绍

* JeeSite 快速开发平台，不仅仅是一个后台开发框架，它是一个企业级快速开发解决方案，后端基于经典组合 Spring Boot、Shiro、MyBatis，前端采用 Beetl、Bootstrap、AdminLTE 经典开发模式，或者分离版 Vue3、Vite、Ant Design Vue、TypeScript、Vben Admin 最先进技术栈。

* 提供在线数据源管理、数据表建模、代码生成等功能，可自动创建业务模块代码工程和微服务模块代码工程，自动生成前端代码和后端代码；包括核心功能模块如：组织机构、用户、角色、岗位、管理员、权限审计、菜单及按钮权限、数据权限、模块管理、系统参数、字典管理、系统监控、数据监控等；扩展功能如：工作流引擎、内容管理、消息推送、单点登录、第三方登录、在线作业调度、对象存储、可视化数据大屏、报表设计器、在线文件预览、国际化、全文检索、统一认证服务等。

* 本平台采用松耦合设计，微内核和插件架构，模块增减便捷，支持集群，支持 SaaS 架构，支持读写分离、分库分表、Spring Cloud 微服务架构；并内置了众多账号安全设置、密码策略、系统访问限制等安全解决方案，支持等保评测。

* 本平台专注于为初级研发人员提供强大的支持，使他们能够高效、快速地开发出复杂的业务功能，同时为中高级人员腾出宝贵的时间，专注于更具战略性和创新性的任务。我们致力于让开发者能够全心投入业务逻辑中，而将繁琐的技术细节交由平台来封装处理。这不仅降低了技术实现的难度，还确保了系统架构的稳定性和安全性，进而帮助企业节省人力成本、缩短项目周期，并提高整体软件的安全性和质量。

* 2013 年发布以来已被广大爱好者用到了企业、政府、医疗、金融、互联网等各个领域中，拥有：精良架构、易于扩展、大众思维的设计模式，工匠精神，用心打磨每一个细节，深入开发者的内心，并荣获开源中国《最受欢迎中国开源软件》多次奖项，期间也帮助了不少刚毕业的大学生，教师作为入门教材，快速的去实践。

* 2019 年换代升级，我们结合了多年总结和经验，以及各方面的应用案例，对架构完成了一次全部重构，也纳入很多新的思想。不管是从开发者模式、底层架构、逻辑处理还是到用户界面，用户交互体验上都有很大的进步，在不忘学习成本、提高开发效率的情况下，安全方面也做和很多工作，包括：身份认证、密码策略、安全审计、日志收集等众多安全选项供您选择。努力为大中小微企业打造全方位企业级快速开发解决方案。

* 2021 年终发布 Vue3 的前后分离版本，使得 JeeSite 拥有同一个后台服务 Web 来支撑分离版和全栈版两套前端技术栈。

* 支持国产化软硬件环境，如国产芯片、操作系统、数据库、中间件、国密算法等。

## 核心优势

* JeeSite 非常易于二次开发，可控性高，整体架构清晰、技术稳定而先进、源代码书写规范、经典技术会的人多、易于维护、易于扩展、安全稳定。

* JeeSite 功能全，知识点非常多，也非常少。因为她使用的都是一些通用的技术，通俗的设计风格，大多数基础知识点，多数人都能掌握，所以每一个 JeeSite 的功能点都非常容易掌握。只要您学会使用这些功能和组件的应用，就可以顺利的完成系统开发了。

* JeeSite 是一个低代码开发平台，具有较高的封装度、扩展性，封装不是限制您去做一些事情，而是在便捷的同时，也具有较好的扩展性，在不具备一些功能的情况下，JeeSite 提供了扩展接口，提供了原生调用方法。

* 大家都在用 Spring，也在学习 Spring 的优点，Spring 提供了较好的扩展性，可又有多少人去修改它的源代码呢，退一步说，大家去修改了 Spring 的源码，反而会对未来升级造成很大困扰，您说不是呢？这样的例子很多，所以不要纠结，我们非常注重这一点，JeeSite 也一样具备强大的扩展性。为你解决升级的困扰。

* 为什么说 JeeSite 比较易于学习？JeeSite 很好的把握了设计的 “度”，避免过度设计的情况。过度设计是在产品设计过程中忽略了产品和用户的实际需求，反而带来了不必要的复杂性，而忽略了系统的学习、开发和维护成本。

------

* 至今 JeeSite 平台架构已经非常稳定，我们持续升级，并不失架构的先进性。
* JeeSite 精益求精，用心打磨每一个细节，界面 UI 操作便捷，体验性好。
* JeeSite 是一个专业的平台，是一个可以让您使用放心的平台。
* 社区版基于 Apache License 2.0 开源协议，永久免费使用。

### 架构特点及安全方面的优势：<https://jeesite.com/docs/feature/>

## 技术选型

* 主框架：Spring Boot 2.7、Spring Framework 5.3、Apache Shiro 1.12、J2Cache
* 持久层：Apache MyBatis 3.5、Hibernate Validator 6.2、Alibaba Druid 1.2
* 视图层：Spring MVC 5.3、Beetl 3.10（替换JSP）、Bootstrap 3.3、AdminLTE 2.4
* 前端组件：jQuery 3.7、jqGrid 4.7、layer 3.5、zTree 3.5、jQuery Validation
* 分离前端版：Node.js、TypeScript、Vue3、Vite、Ant Design Vue、Vue Vben Admin
* 工作流引擎：Flowable 6.6、符合 BPMN 规范、在线流程设计器、中国式流程、退回、撤回、自由流
* Bootstrap 版 支持 IE9 及以上版本及其他所有现代浏览器，如：谷歌、火狐、国产浏览器 等
* Vue3 版 支持现代浏览器，如：谷歌 Chrome 86+、火狐、国产浏览器 等
* 技术选型（详细）：<http://jeesite.com/docs/technology/>
* JeeSite Vue 版本：<https://gitee.com/thinkgem/jeesite-vue>
* Spring Boot 3.x 版本：<https://gitee.com/thinkgem/jeesite5/tree/v5.springboot3>

## 更多介绍

* 内置功能：<https://jeesite.com/docs/function/>
* 目录结构：<https://jeesite.com/docs/catalog/>
* 架构特点：<https://jeesite.com/docs/feature/>
* 开发规范：<https://jeesite.com/docs/standard/>
* 代码生成：<https://jeesite.com/docs/code-gen/>

## 生态系统

* 分布式微服务（Spring Cloud）：<https://gitee.com/thinkgem/jeesite-cloud>
* Flowable业务流程引擎（BPM）：<http://jeesite.com/docs/bpm/>
* 多站点内容管理模块（CMS）：<https://jeesite.com/docs/cms/>
* 手机端移动端：<https://gitee.com/thinkgem/jeesite-uniapp>
* PC客户端程序：<https://gitee.com/thinkgem/jeesite-client>
* Vue3分离版本：<https://gitee.com/thinkgem/jeesite-vue>
* JeeSite统一认证：<https://jeesite.com/docs/oauth2-server>
* JFlow工作流引擎：<https://gitee.com/thinkgem/jeesite-jflow>
* Mybatis-Plus: <https://gitee.com/thinkgem/jeesite-mybatisplus>
* Magic接口快速开发：<https://gitee.com/thinkgem/jeesite-magic-api>
* 内外网中间件：<https://my.oschina.net/thinkgem/blog/4624519>

## 快速体验

### 在线演示

1. 全栈版地址：<http://demo.jeesite.com>
2. Vue3分离版地址：<http://vue.jeesite.com>

### 本地运行

1. 环境准备：`JDK 1.8 or 11、17`、`Maven 3.6+`、使用 `MySQL 5.7 or 8.0` 数据库、[其它数据库](https://jeesite.com/docs/technology/#_8、已支持数据库)
2. 下载源码：<https://gitee.com/thinkgem/jeesite5/repository/archive/v5.9.zip> 并解压
3. 打开文件：`/web/src/main/resources/config/application.yml` 配置JDBC连接
4. 执行脚本：`/web/bin/init-data.bat` 初始化数据库
5. 执行脚本：`/web/bin/run-tomcat.bat` 启动服务即可
6. 浏览器访问：<http://127.0.0.1:8980/js>  账号 system 密码 admin
7. 部署常见问题：<https://jeesite.com/docs/faq/>
8. 分离端安装：<https://jeesite.com/docs/vue-install-deploy/>

### 快速运行

1. 环境准备：`JDK 1.8 or 11、17`、`Maven 3.6+`、无需准备数据库（使用内嵌 H2 DB、Vue资源包）
2. 下载源码：<https://gitee.com/thinkgem/jeesite5/repository/archive/v5.9.zip> 并解压
3. 执行脚本：`/web-fast/bin/run-tomcat.bat` 启动服务即可（自动初始化库）
4. Vue分离版本地址：<http://127.0.0.1:8980/vue/login>
5. 全栈版本地址：<http://127.0.0.1:8980/a/login>
6. 初始登录账号：超级管理员：system  密码：admin
7. 部署常见问题：<https://jeesite.com/docs/faq/>

### 容器运行

- 拉取 Docker 镜像（演示使用，JeeSite版本较久）：
```sh
docker pull thinkgem/jeesite-web
```
- 启动脚本：
```sh
docker run --name jeesite-web -p 8980:8980 -d --restart unless-stopped \
    -v ~/:/data thinkgem/jeesite-web && docker logs -f jeesite-web
```
- 浏览器访问：<http://127.0.0.1:8980/js/>  账号 system 密码 admin
- 分离端安装：<https://jeesite.com/docs/vue-install-deploy/>

### 开发环境

1. 部署运行文档：<https://jeesite.com/docs/install-deploy/>
2. 部署常见问题：<https://jeesite.com/docs/faq/>
3. 分离端安装：<https://jeesite.com/docs/vue-install-deploy/>

## 技术文章

* 菜单和按钮权限：<https://jeesite.com/docs/permi-shiro/>
* 强大的数据权限：<https://jeesite.com/docs/service-datascope/#数据权限>
* 表结构数据字典：<https://jeesite.com/docs/code-gen/#表结构数据字典>
* 持久层设计：<https://jeesite.com/docs/dao-mybatis/>
* 后端工具：<https://jeesite.com/docs/sys-utils/>
* 表单组件：<https://jeesite.com/docs/views-beetl/>
* 表格组件：<https://jeesite.com/docs/datagrid/>
* js工具：<https://jeesite.com/docs/jeesite-js/>

## 专题文章

* 自定义主题：<https://jeesite.com/docs/custom-views/>
* 国际化多语言：<https://jeesite.com/docs/i18n-locale/>
* 接口文档：<https://jeesite.com/docs/mobile-rest-api/>
* BPM工作流引擎：<https://jeesite.com/docs/bpm/>
* 用户类型：<https://jeesite.com/docs/user-type/>
* 消息推送：<https://jeesite.com/docs/msg-push-use/>
* 单点登录：<https://jeesite.com/docs/sso-cas/>
* 在线任务调度：<https://jeesite.com/docs/job/>
* 对象存储：<https://jeesite.com/docs/oss-client/>
* 大屏设计器：<https://jeesite.com/docs/visual/>
* 报表设计器：<https://jeesite.com/docs/ureport/>
* 文件在线预览：<https://jeesite.com/docs/filepreview/>
* 三员管理员：<https://jeesite.com/docs/manager3/>
* 手机端框架：<https://jeesite.com/docs/uniapp/>
* 统一认证服务：<https://jeesite.com/docs/oauth2-server/>
* 树表结构设计：<https://jeesite.com/docs/tree-table-use/>

## 云服务架构

* 多租户、SaaS服务：<https://jeesite.com/docs/saas-corp-use/>
* 集群、负载均衡、高可用：<https://jeesite.com/docs/cluster/>
* Spring Cloud 微服务：<https://jeesite.com/docs/springcloud/>
* 分布式事务 Seata：<https://jeesite.com/docs/springcloud-seata/>
* 读写分离、分库分表：<https://jeesite.com/docs/sharding/>

## 前后分离版

* Vue 版介绍：<https://jeesite.com/docs/jeesite-vue/>
* Vue 安装部署：<https://jeesite.com/docs/vue-install-deploy/>
* Vue 参数配置：<https://jeesite.com/docs/vue-settings/>
* Vue 前端权限：<https://jeesite.com/docs/vue-auth/>
* Vue 源码解析：<https://jeesite.com/docs/vue-crud-view/>
* Vue 表单组件：<https://jeesite.com/docs/vue-basic-form/>
* Vue 表格组件：<https://jeesite.com/docs/vue-basic-table/>
* Vue 常用组件：<https://jeesite.com/docs/vue-comp/>
* Vue 图标组件：<https://jeesite.com/docs/vue-icon/>
* Vue 国际化多语言：<https://jeesite.com/docs/vue-i18n/>
* Vue 样式库：<https://jeesite.com/docs/vue-style/>

## 授权协议声明

1. 基于 Apache License Version 2.0 协议发布，可用于商业项目，但必须遵守以下补充条款。
2. 不得将本软件应用于危害国家安全、荣誉和利益的行为，不能以任何形式用于非法为目的的行为。
3. 在延伸的代码中（修改和有源代码衍生的代码中）需要带有原来代码中的协议、版权声明和其他原作者
   规定需要包含的说明（请尊重原作者的著作权，不要删除或修改文件中的`Copyright`和`@author`信息）
   更不要，全局替换源代码中的 jeesite 或 ThinkGem 等字样，否则你将违反本协议条款承担责任。
4. 您若套用本软件的一些代码或功能参考，请保留源文件中的版权和作者，需要在您的软件介绍明显位置
   说明出处，举例：本软件基于 JeeSite 快速开发平台，并附带链接：http://jeesite.com
5. 任何基于本软件而产生的一切法律纠纷和责任，均于我司无关。
6. 如果你对本软件有改进，希望可以贡献给我们，共同进步。
7. 本项目已申请软件著作权，请尊重开源，感谢阅读。
8. 无用户数限制，无在线人数限制，放心使用。

## 技术服务与支持

* 没有资金的支撑就很难得到发展，特别是一个好的产品，如果 JeeSite 帮助了您，请为我们点赞。支持我们，您可以获得更多回馈，我们会把公益事业做的更好，开放更多资源，回报社区和社会。请给我们一些动力吧，在此非常感谢已支持我们的朋友！
* **联系我们**：请访问技术支持服务页面：<https://jeesite.com/docs/support/> 

## 今后如何升级？

尽量不修改 web 项目以外的源码项目，如 jeesite-common、jeesite-modele-core，如果修改了，请 Pull Requests 上来，否则代码与官方不同步，可能会将对你的日后升级增加难度。

如果您修改了依赖模块代码，也没关系，这时你需要利用 Git 版本控制工具，与官方仓库代码进行同步，合并代码即可。

每个版本升级，我们都会附带详细更新日志：<https://jeesite.com/docs/upgrade/>。

在这里，你可以看到 JeeSite 新增哪些新功能和改进，在每个版本下都有对应升级方法。

如果跨版本升级，可以将版本号直接修改为最新版本，然后去看每个版本的升级方法，修改对应业务。

# Git 全局设置技巧

```
1、提交检出均不转换换行符

git config --global core.autocrlf false

2、拒绝提交包含混合换行符的文件

git config --global core.safecrlf true
```