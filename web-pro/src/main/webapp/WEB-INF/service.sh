#!/bin/sh
# /**
#  * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
#  * No deletion without permission, or be held responsible to law.
#  *
#  * Author: <EMAIL>
#  */
echo ""
echo "=============================================================="
echo ""
echo "  JeeSite Web 服务管理脚本"
echo ""
echo "  示例："
echo "  1）运行服务：sh service.sh run"
echo "  2）启动服务：sh service.sh start"
echo "  3）重启服务：sh service.sh restart"
echo "  4）停止服务：sh service.sh stop"
echo "  5）查看日志：sh service.sh"
echo ""
echo "  提示：run 命令"
echo "  1）如果服务未启动，会启动服务"
echo "  2）如果服务已启动，会终止已启动的服务，并重启服务"
echo "  3）启动服务后，会自动 tail -f 流方式查看服务日志"
echo "  4）可直接 Ctrl + C 终止查看日志，但服务仍在后台会继续运行"
echo ""
echo "=============================================================="
echo ""

cd "$(cd "$(dirname "$0")"; pwd)"

if [ "$1" = "run" ] || [ "$1" = "restart" ] || [ "$1" = "stop" ]; then
	ps -ef|grep "$PWD"|grep -v "grep"|awk '{print $2}'|xargs kill -9
fi

if [ "$1" = "run" ] || [ "$1" = "restart" ] || [ "$1" = "start" ]; then
	nohup sh startup.sh >> /dev/null 2>&1 &
fi

if [ "$1" = "run" ] || [ "$1" = "" ]; then
	tail -n35 -f $PWD/classes/logs/debug.log
	sleep 3
	tail -n35 -f $PWD/classes/logs/debug.log
	sleep 3
	tail -n35 -f $PWD/classes/logs/debug.log
	sleep 3
	tail -n35 -f $PWD/classes/logs/debug.log
fi

if [ "$1" = "start" ]; then
  echo "启动服务命令已发送成功。"
fi
if [ "$1" = "restart" ]; then
  echo "重启服务命令已发送成功。"
fi
if [ "$1" = "stop" ]; then
  echo "停止服务命令已发送成功。"
fi
echo ""
