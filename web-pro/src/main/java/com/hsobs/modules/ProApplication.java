/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.hsobs.modules;

import com.hsobs.hs.modules.utils.WordUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.io.FileUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

/**
 * Application
 *
 * <AUTHOR>
 */
@EnableAsync
@SpringBootApplication(scanBasePackages = "com.hsobs")
public class ProApplication extends SpringBootServletInitializer {

    private static final Logger logger = LoggerFactory.getLogger(ProApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(ProApplication.class, args);
        logger.info(
                "\r\n\r\n==============================================================\r\n"
                        + "\r\n   启动完成，访问地址：http://127.0.0.1:"
                        + Global.getProperty("server.port") + FileUtils.path("/"
                                + Global.getProperty("server.servlet.context-path"))
                        + "\r\n\r\n   默认管理账号： system   密码： HsObs@tt951."
                        + "\r\n\r\n   访问数据： " + Global.getProperty("jdbc.url")
                        + "\r\n\r\n==============================================================\r\n");
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        this.setRegisterErrorPageFilter(false); // 错误页面有容器来处理，而不是SpringBoot
        return builder.sources(ProApplication.class);
    }

    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.hsobs"))
                .paths(PathSelectors.any())
                .build();
    }

	public static CloseableHttpClient createHttpClient() throws KeyManagementException, NoSuchAlgorithmException, KeyStoreException {
		// 创建一个信任所有证书的 SSLContext
		SSLContext sslContext = new SSLContextBuilder()
				.loadTrustMaterial(null, (certificate, authType) -> true) // 信任所有证书
				.build();

		// 创建一个 SSLConnectionSocketFactory，跳过主机名验证
		SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
				sslContext,
				NoopHostnameVerifier.INSTANCE);

		// 创建 HttpClient
		return HttpClients.custom()
				.setSSLSocketFactory(socketFactory)
				.build();
	}


	@Bean
	public RestTemplate restTemplate() {
		try {
			// 创建一个信任所有证书的 SSLContext
			SSLContext sslContext = new SSLContextBuilder()
					.loadTrustMaterial(null, (certificate, authType) -> true) // 信任所有证书
					.build();

			// 创建一个 SSLConnectionSocketFactory，跳过主机名验证
			SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
					sslContext,
					NoopHostnameVerifier.INSTANCE);

			// 创建 HttpClient
			CloseableHttpClient httpClient = HttpClients.custom()
					.setSSLSocketFactory(socketFactory)
					.build();

			// 创建跳过 SSL 验证的 HttpClient

			// 配置 RestTemplate 使用自定义的 HttpClient
			HttpComponentsClientHttpRequestFactory requestFactory =
					new HttpComponentsClientHttpRequestFactory(httpClient);

			return new RestTemplate(requestFactory);
		} catch (Exception e) {
			return new RestTemplate();
		}
	}

}