/*!
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * 项目自定义的公共CSS，可覆盖jeesite.css里的样式
 */

.productLogo{
    width: 44px;
    height: 44px;
    margin-left: 1px;
    margin-right: 8px;
}

/*i#productLogo {*/
/*    margin-right: 16px;*/
/*}*/

.addTabPage {
    width: auto;            /* 宽度自适应，即根据内容自动调整 */
    font-size: 16px;        /* 字体大小设置为16像素 */
    color: #FFFFFF;         /* 字体颜色设置为白色 */
}
body, p, h1, h2, h3, h4, h5, h6, div, span, a, input, textarea, button, select, option {
    font-family: 'Microsoft YaHei', Arial, '苹方', Helvetica;
}

.sidebar {
    display: flex;
}

.sidebar-menu {
    padding: 0 4px 0 4px;
}

.sidebar a {
    color: #008EE6;
}
.sidebar-menu .treeview-item.active>a {
    color: #FFFFFF;
    background-color: #3094FF;
    border-right: 0;
    border-radius: 6px;
}
/*.sidebar-menu > li.active > .addTabPage {*/
/*    background: #C7DEFB;*/
/*    border-radius: 6px;*/
/*}*/
.main-header .navbar-custom-menu{
    height: 70px;
    display: grid;
    place-items: center;
}
.main-header .logo {
    height: 70px;
    display: flex;
    place-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0);
}
.main-header .navbar .nav > li > a:hover, .main-header .navbar .nav > li > a:active, .main-header .navbar .nav > li > a:focus, .main-header .navbar .nav .open > a, .main-header .navbar .nav .open > a:hover, .main-header .navbar .nav .open > a:focus, .main-header .navbar .nav > .active > a {
    background: #ffffff;
    color: #1bb1d9;
    border-radius: 6px;
    font-weight: bold;
}
#topMenu>.nav>li>a.addTabPage {
    margin-right: 5px;
}
.tabpanel_tab_content .tabpanel_move_content {
    min-height: 40px;
}
.main-header .navbar {
    /*background: -webkit-gradient(linear, left bottom, right top, color-stop(0, #2b67e2), color-stop(1, #24a6e7));*/
    background: url(../headbg1.171205a9.png);
}
button.btn.btn-primary.btn-sm {
    background-color: #3094ff;
    height: 32px;
    min-width: 80px;
}
button.btn.btn-default.btn-sm.isQuick {
    /* background-color: #C0C4CC; */
    height: 32px;
}
button#btnCancel {
    height: 36px;
}

.box>.box-title {
    font-size: 16px;
    margin: 13px 6px;
    display: inline-block;
}
.box>.box-title .fa{
    color: #1890ff;
}

label.has-error {
    background: #ff4a5c;
}
.form-control {
    border-radius: 0;
}
.btn {
    border-radius: 0;
}
.btn-default {
    color: #3e9bff;
    border-color: #3e9bff;
    background: #FFF;
}
.btn-default.active, .btn-default:active, .btn-default.active:hover, .btn-default:hover, .btn-default:focus, .open>.dropdown-toggle.btn-default {
    background-color: #FFF;
    border-color: #3e9bff;
}
.btn-default.active, .btn-default:active, .open>.dropdown-toggle.btn-default {
    color: #3e9bff;
    background-color: #FFF;
    background-image: none;
    border-color: #3e9bff;
}
.box-main>.box-header .box-tools .btn {
    padding: 3px 10px 5px 10px;
    font-size: 14px;
    margin-bottom: 2px;
    box-shadow: none;
    min-width: 80px;
}
.box-main>.box-header .box-tools .btn-box-tool {
    padding: 4px 2px !important;
    width: 24px !important;
    min-width: 24px !important;
}
.select2-container .select2-selection--single {
    height: 32px;
    min-height: 32px;
    padding: 6px;
    border-color: #d9d9d9;
    border-radius: 0;
}
.main-sidebar {
    width: 400px;
}
.menuBox{
    display: inline-block; /* 设置为行内块元素 */
    vertical-align: top;
}
.dropdown > .sidebar-menu > li.active {
    background: url(../backImge.png);
}
.dropdown > .sidebar-menu > li.active > .addTabPage {
    background: none;
    color: #ffffff;
    /* border-radius: 6px; */
}
.dropdown > .sidebar-menu > .treeview {
    width: 72px;
    height: 79px;
    background-size: cover;
    overflow: auto;
    white-space: normal;
    overflow: hidden;
}
.dropdown > .sidebar-menu > li > a {
    display: grid;
    justify-items: center;
    text-align: center;
}
#leftSideMenu {
    box-shadow: 2px 0 6px rgba(0, 0, 0, .05);
}

#leftMenu {
    max-width: 200px;
}
#topMenu {
    display: none;
}
.table thead tr, .ui-jqgrid-htable thead tr, .ui-jqgrid-hdiv, .ui-jqgrid-hbox {
    background-color: #d6eaff;
}
.ui-jqgrid .ui-jqgrid-labels th {
    border-right: 0px;
    font-weight: bolder;
}
.ui-jqgrid tr.ui-row-ltr td {
    border-right-width: 0px;
    text-align: left;
}
.ui-jqgrid .ui-jqgrid-hdiv {
    border-radius: 0;
}
.ui-widget-content {
    border: 0;
}
.tabpanel_mover li {
    margin: 11px 0 0px 0px;
    width: 160px;
    border-radius: 0px;
}
.tabpanel_tab_content .tabpanel_move_content {
    margin-left: 10px;
}
.ui-th-column, .ui-jqgrid .ui-jqgrid-htable th.ui-th-column {
    text-align: left;
}
.sidebar-menu > li.active > a {
    font-weight: 400;
}
.sidebar-menu > li > a {
    font-weight: 400;
}
.dropdown > .sidebar-menu > li > a >.fa {
    font-size: 24px;
    margin-bottom: 8px;
}
#leftMenu .sidebar-menu>li>a {
    min-width: 120px;
}

.flex-container-end {
    display: flex;
    justify-content: flex-end;
}

/* 表单 table 样式 */
.hs-table-div {
    width: 100%;
    padding: 0 25px 0 30px;
}
.hs-table-div .hs-table-form {
    width: 100%;
}
.hs-table-div .hs-table-form .hs-form-label {
    background: #f8f9fe;
    padding: 11px;
    text-align: left;
    width: 14.666667%;
}
.hs-table-div .hs-table-form td {
    height: 42px;
    padding: 3.2px;
}

/* 底部按钮栏固定 */
.hs-footer-block {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #f0f2f5;
    border-top: 1px solid #ccc;
    z-index: 19999;
}
.hs-box-body-bpm {
    margin-bottom: 180px;
}
.process_details_btn {
    font-size: 14px;
    color: #1e5edb;
}