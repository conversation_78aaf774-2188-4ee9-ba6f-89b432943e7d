/*!
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * <AUTHOR>
 * @version 2019-2-16
 */
.login-page {background:#E3E7EC;}
.login-page .login-box {width:auto;}
.login-page .login-logo a {color:#666;}
.login-page .login-logo small {font-size:16px;}
.login-page .login-box-body {width:360px;margin:auto;padding:28px;background:#fff;box-shadow:0 0 8px #999;border-radius:10px;}
.login-page .login-box-body .nav-tabs {margin-bottom:25px;}
.login-page .login-box-body .form-control-feedback {cursor:pointer;pointer-events:auto;}
.login-page .form-group {margin-top:5px;margin-bottom:20px;}
.login-page .has-feedback .form-control {height:34px;padding:4px 10px;}
.login-page .input-group-btn .btn {height:34px;background:#efefef;}
.login-page .btn {padding:6px 10px 5px 10px;font-size:15px;letter-spacing:1px;}
.login-page .select2-container .select2-selection--single {padding:6px 12px;height:34px;}
.login-page .select2-container--default.select2-container--focus .select2-selection--single {border-color:#3c8dbc;}
.login-page .select2-container .select2-selection--single .select2-selection__rendered {margin-top:-4px;}
.login-page .select2-container .select2-selection--single .select2-selection__arrow {top:1px;}
.login-page .select2-search--dropdown .select2-search__field {padding:4px;}
.login-page .select2-results__option {padding:6px 12px;}
.login-page .login-copyright {text-align:center;margin-top:20px;}
.login-page .login-copyright, .login-page .login-copyright a {color:#666}

a, a:hover, a:active, a:focus, .form-unit {color:#1e83e3;font-size:15px;}
.btn-primary, .btn-primary:hover, .btn-primary:active,
.btn-primary.hover, .btn-primary.focus, .btn-primary:focus,
.btn-primary.active.focus, .btn-primary.active:focus, .btn-primary.active:hover,
.btn-primary:active.focus, .btn-primary:active:focus, .btn-primary:active:hover,
.open>.dropdown-toggle.btn-primary.focus, .open>.dropdown-toggle.btn-primary:focus,
.open>.dropdown-toggle.btn-primary:hover, .layui-layer-btn .layui-layer-btn0,
.select2-container--default .select2-selection--multiple .select2-selection__choice,
.select2-container--default .select2-results__option--highlighted[aria-selected]
	{background-color:#1b8af1!important;border-color:#1b8af1;}

.login-page {overflow:auto;} .login-page .wrapper {overflow:visible;}
.login-page .login-box, .login-page .register-box {margin:4% auto;margin-bottom:20px;}
.login-page .login-box-msg, .login-page .register-box-msg {padding:0 0 15px;color:#f33c3c;}
.login-page .has-feedback .form-control, .form-control-feedback {height:39px;padding:2px 10px 4px;font-size:15px;}
.login-page .login-tools {float:right;padding-top:14px;font-size:15px;}
.login-page .btn {font-size:16px;font-weight:normal;border-radius:6px;}
.login-page .input-group-btn .btn {height:39px;}

.login-page .login-box-body{width:1000px;padding:0;display:-ms-flexbox;display:flex;margin-top:50px;box-shadow:0 20px 80px 0 rgba(0,0,0,0.1);position:relative;}
.login-page .login-left, .login-page .login-right {width:50%;padding:40px 80px 48px;display:inline-block;position:absolute\9;}
.login-page .login-left {color:#fff;position:relative;padding:45px 50px 48px 80px;background:#1890ff;background:linear-gradient(0deg, #2378ca 0%, #1890ff 100%);border-radius:10px 0 0 10px;}
.login-page .login-left::before {background:url(images/left-1.png) no-repeat 0 0;}
.login-page .login-left::after {background:url(images/left-2.png) no-repeat right bottom;}
.login-page .login-left::before, .login-page .login-left::after {content:'';position:absolute;top:0;left:0;bottom:0;right:0;}
.login-page .login-left a {color:#fff;}
.login-page .login-left-info {padding-top:40px;padding-left:20px;line-height:30px;}
.login-page .login-left-cont {position:relative;z-index:1;height:100%;}
.login-page .login-right {top:0\9;right:0\9;}
.login-page .social-auth-links {padding:15px;}
.login-page .social-auth-links .border-bottom {border-top:1px solid #ddd;margin-top:25px;}
.login-page .login-copyright, .login-page .login-copyright a {margin-top:22px;}

.login-page .login-box-body .nav-tabs {margin:15px 0 35px}
.login-page .login-box-body .nav-tabs .login-tools,
.login-page .login-box-body .nav-tabs .login-tools a {font-size:12px;}
.login-page .input-group-btn .btn {font-weight:normal;font-size:14px;}

.login-page .social-auth-links {padding-top:10px;}
.login-page .social-auth-links .title {margin-bottom:30px;}
.login-page .social-auth-links a {display:inline-block;margin:0px 10px;}
.login-page .social-auth-links .iconfont {font-size:32px;}

@media screen and (max-width: 768px) {
	.login-page .login-box-body {width:80%;display:block;width:85%;margin-top:30px;}
	.login-page .login-left, .login-page .login-right {width:100%;padding: 25px 40px 40px;}
	.login-page .login-left{padding:20px 30px 40px 50px;border-radius:10px 10px 0 0;}
}

.skin-dark .login-page {background-color:#293146;}
.skin-dark .login-page .login-box-body {background-color:#1c2333;box-shadow:none;}
.skin-dark .login-page .login-left {background:#003868;background:linear-gradient(0deg, #0c2843 0%, #044576 100%);}
.skin-dark .login-page .login-logo a {color:#a8a4a4;}
.skin-dark .nav-tabs {border-color:#6c6c6c;}
.skin-dark .nav-tabs>li.active>a, .skin-dark .nav-tabs>li.active>a:focus, .skin-dark .nav-tabs>li.active>a:hover {background-color:#1c2333;border-color:#6c6c6c;border-bottom-color:#1c2333;color:#d6d6d6;}
.skin-dark .nav>li>a:hover, .skin-dark .nav>li>a:active, .skin-dark .nav>li>a:focus {background-color:#1c2333;border-color:#4b4b4b;border-bottom-color:#1c2333;color:#ddd;}
.skin-dark .login-page .login-left, .skin-dark .login-page .login-left a {color:#ccc;}
.skin-dark .login-page .input-group-btn .btn {background-color:#313131;}
.skin-dark .login-page .social-auth-links .border-bottom {border-color:#5b5b5b;}
.skin-dark .btn-primary {background-color:#054d81!important;border-color:#054d81!important;color:#ddd;}
.skin-dark a, .skin-dark a:hover, .skin-dark a:active, .skin-dark a:focus, .form-unit {color:#3799cf;}
.skin-dark .dropdown .dropdown-menu {background-color:#292929;border-color:#444444;}
.skin-dark .dropdown-menu>li>a {color:#b6b6b6;}
.skin-dark .dropdown-menu>li>a:hover {background-color:#3f3f3f;color:#ddd;}
.switchSkin, .switchSkin:hover{position:absolute;top:10px;right:20px;font-size:24px;opacity:0.8}
.skin-dark .icongitee:before {color:#9f1200;}
.skin-dark .iconoschina:before {color:#249209;}
.skin-dark .icongithub:before {color:#8c8c8c;}
