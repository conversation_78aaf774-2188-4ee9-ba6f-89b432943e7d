/*
 Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 No deletion without permission, or be held responsible to law.
 <AUTHOR>
 @version 2022-5-16
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(a,c,b){a instanceof String&&(a=String(a));for(var e=a.length,d=0;d<e;d++){var f=a[d];if(c.call(b,f,d,a))return{i:d,v:f}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,c,b){a!=Array.prototype&&a!=Object.prototype&&(a[c]=b.value)};$jscomp.getGlobal=function(a){a=["object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global,a];for(var c=0;c<a.length;++c){var b=a[c];if(b&&b.Math==Math)return b}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);$jscomp.polyfill=function(a,c,b,e){if(c){b=$jscomp.global;a=a.split(".");for(e=0;e<a.length-1;e++){var d=a[e];d in b||(b[d]={});b=b[d]}a=a[a.length-1];e=b[a];c=c(e);c!=e&&null!=c&&$jscomp.defineProperty(b,a,{configurable:!0,writable:!0,value:c})}};$jscomp.polyfill("Array.prototype.find",function(a){return a?a:function(a,b){return $jscomp.findInternal(this,a,b).v}},"es6","es3");function updateCurrentMenuTitle(title){var titleBar=$('#leftMenu .menu-title-bar');if(titleBar.length===0){titleBar=$('<div class="menu-title-bar bg-gray-100 p-3 mb-2 rounded"></div>');$('#leftMenu').prepend(titleBar)}titleBar.html(title)}$(function(){updateCurrentMenuTitle('');$("#leftSideMenu").on("click",".addTabPage",function(b){$("#leftSideMenu li").removeClass("active");$(this).closest("li").addClass("active");var c=$(this).data("code");js.cookie("currentMenuCode",c);var menuTitle=$(this).find('span').text()||'未知菜单';updateCurrentMenuTitle(menuTitle);b=$("#leftMenu-"+c);0<b.length?($("#leftMenu > ul").hide(),b.show(),a(c)):js.ajaxSubmit(ctx+"/index/menuTree?parentCode="+c+"&t="+(new Date).getTime(),function(b){b&&-1==b.indexOf('"result":"login"')?($("#leftMenu > ul").hide(),$("#leftMenu").append(b),1==$(".sidebar-menu").length&&$(window).trigger("hashchange"),a(c)):location=ctx+"/login"},"html")});var a=function(a){""==js.trim($("#leftMenu-"+a).text())?($(".main-sidebar,.logo small,.tabpanel_tab_content").hide(),$(".content-wrapper").css("cssText","margin-left:0!important;"),window.initMenu&&767>=$(window).width()&&$("body").hasClass("sidebar-open")&&$('[data-toggle="push-menu"]').click()):($(".main-sidebar,.logo small,.tabpanel_tab_content").show(),$(".content-wrapper").css("margin-left",""),window.initMenu&&767>=$(window).width()&&!$("body").hasClass("sidebar-open")&&$('[data-toggle="push-menu"]').click(),a=$("#leftMenu-"+a+".first-open > li"),a.hasClass("menu-open")||(a=a.eq(0).find("> a"),"blank"==a.data("href")&&a.click()));$("#leftMenu").triggerHandler("initd");window.initMenu=!0;$(window).resize()},c=js.cookie("currentMenuCode"),b;c&&""!=c&&(b=$("#leftSideMenu .addTabPage[data-code="+c+"]"));b&&0<b.length&&""!=window.location.hash?b.click():$("#leftSideMenu .addTabPage:first").click();var e=$(".main-header>.navbar"),d=$("#leftSideMenuMore>ul:eq(0)"),f=$("#leftSideMenu>ul:eq(0)"),g=$(window).width();0<d.length&&$(window).resize(function(){var a=0;e.find(">div:not(#leftSideMenu)").each(function(){a+=$(this).width()});var b=e.width()-a-150,c=$(window).width();g<=c?d.find(">li").each(function(){f.width()<b&&f.find(">li:last").before($(this))}):f.find(">li:not(#leftSideMenuMore)").each(function(){f.width()>b&&d.prepend(f.find(">li:not(#leftSideMenuMore):last"))});d.parent().toggle(0<d.find(">li").length);g=c}).resize()});