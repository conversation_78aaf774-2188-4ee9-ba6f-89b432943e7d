
# 使用环境配置，只需 JVM 参数里加：-Dspring.profiles.active=prod

#======================================#
#========== Server settings ===========#
#======================================#

server:

  port: 8980
  servlet:
    context-path: /js

#======================================#
#========== Database sttings ==========#
#======================================#

# 数据库连接
jdbc: 
  
  # Mysql 数据库配置
  type: dameng
  driver: dm.jdbc.driver.DmDriver
  url: jdbc:dm://106.227.74.200:30236?schema=HSOBSN&compatibleMode=mysql&characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
  username: HSOBSN
  password: v8yjYl9J5TVB
  testSql: SELECT 1

  # 数据库连接池配置
  pool:
  
    # 初始化连接数
    init: 1
    # 最小连接数
    minIdle: 3
    # 最大连接数
    maxActive: 20

#======================================#
#========== Spring settings ===========#
#======================================#

# 日志配置
logging:
  config: classpath:config/logback-spring-prod.xml