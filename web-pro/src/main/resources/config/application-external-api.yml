# 外部API接口配置
hsobs:
  external-api:
    # 公安部门API配置
    police:
      base-url: http://api.police.example.com
      token: 
      app-id: 
      app-secret: 
      enabled: false
      apis:
        # 身份证信息核验
        verifyIdCard:
          path: /api/idcard/verify
          method: POST
          timeout: 10000
          enabled: true
          params:
            header.Content-Type: application/json
        
        # 户籍信息查询
        queryHouseholdInfo:
          path: /api/household/query
          method: POST
          timeout: 10000
          enabled: true
        
        # 婚姻状况查询
        queryMarriageStatus:
          path: /api/marriage/query
          method: POST
          timeout: 10000
          enabled: true
    
    # 房产部门API配置
    real-estate:
      base-url: http://api.realestate.example.com
      token: 
      app-id: 
      app-secret: 
      enabled: false
      apis:
        # 房产信息查询
        queryPropertyInfo:
          path: /api/property/query
          method: POST
          timeout: 10000
          enabled: true
        
        # 房产核验
        verifyProperty:
          path: /api/property/verify
          method: POST
          timeout: 10000
          enabled: true
        
        # 房产交易记录查询
        queryTransactionRecords:
          path: /api/property/transaction/query
          method: POST
          timeout: 10000
          enabled: true
    
    # 公积金部门API配置
    housing-fund:
      base-url: http://api.housingfund.example.com
      token: 
      app-id: 
      app-secret: 
      enabled: false
      apis:
        # 公积金账户信息查询
        queryAccountInfo:
          path: /api/account/query
          method: POST
          timeout: 10000
          enabled: true
        
        # 公积金缴存记录查询
        queryDepositRecords:
          path: /api/deposit/query
          method: POST
          timeout: 10000
          enabled: true
        
        # 公积金贷款信息查询
        queryLoanInfo:
          path: /api/loan/query
          method: POST
          timeout: 10000
          enabled: true
        
        # 公积金提取记录查询
        queryWithdrawalRecords:
          path: /api/withdrawal/query
          method: POST
          timeout: 10000
          enabled: true
    
    # 其他部门API配置
    others:
      # 局直公房智能核验
      bureauVerification:
        base-url: http://api.bureauverification.example.com
        token: 
        app-id: 
        app-secret: 
        enabled: false
        apis:
          # 局直公房申请核验
          verifyBureauApplication:
            path: /api/bureau/verify
            method: POST
            timeout: 10000
            enabled: true
