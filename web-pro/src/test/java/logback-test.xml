<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
	
	<!-- Logger level setting -->
	<include resource="org/springframework/boot/logging/logback/defaults.xml" />
	<include resource="config/logger-core.xml"/>

	<!-- Console log output -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{HH:mm:ss.SSS} %clr(%-5p) %clr([%-39logger{39}]){cyan} - %m%n%wEx</pattern>
		</encoder>
	</appender>
	
	<!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
	<root level="WARN">
		<appender-ref ref="console" />
	</root>
	
</configuration>