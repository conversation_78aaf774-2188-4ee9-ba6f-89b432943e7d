<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.hsobs</groupId>
		<artifactId>hsobs-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<!--<relativePath>../../jeesite/parent/pom.xml</relativePath>-->
		<relativePath />
	</parent>
	
	<artifactId>hsobs-web-pro</artifactId>
	<packaging>war</packaging>

	<description>专业版的 Web 服务，提供了专业版相关的依赖模块</description>

	<properties>

		<finalName>web</finalName><!-- war or jar 包的名称 -->
		<start-class>com.hsobs.modules.ProApplication</start-class>

		<!-- docker setting -->
		<docker.run.port>8980:8980</docker.run.port>

	</properties>

	<dependencies>

		<!-- 住房保障 -->
		<dependency>
			<groupId>com.hsobs</groupId>
			<artifactId>hsobs-module-hs</artifactId>
			<version>${project.parent.version}</version>
		</dependency>

		<!-- 办公用房 -->
		<dependency>
			<groupId>com.hsobs</groupId>
			<artifactId>hsobs-module-ob</artifactId>
			<version>${project.parent.version}</version>
		</dependency>

		<!-- 核心模块 -->
		<dependency>
			<groupId>com.hsobs</groupId>
			<artifactId>hsobs-module-core</artifactId>
			<version>${project.parent.version}</version>
		</dependency>

		<!-- 测试模块 -->
		<dependency>
			<groupId>com.hsobs</groupId>
			<artifactId>hsobs-module-test</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
		
		<!-- 在线文档接口 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-swagger</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>
		
		<!-- 文件管理共享 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-filemanager</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>
		
		<!-- 文件在线预览 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-filepreview</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>
		
		<!-- 对象存储（MinIO、阿里、腾讯、七牛） -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-oss-client</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>
		
		<!-- 第三方账号登录 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-oauth2</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>
		
		<!-- 微信相关集成 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-weixin</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>
		
		<!-- 读写分离、分区分表 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-sharding</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>
		
		<!-- ES 全文检索客户端实例
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-elasticsearch</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency> -->
		
		<!-- MQ 消息队列发送与消费实例
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-rabbitmq</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency> -->
		
		<!-- UReport报表
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-ureport</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency> -->

		<!-- 数据大屏设计 -->
		<!-- <dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-visual</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency> -->

		<!-- 移动端模块 -->
		<dependency>
			<groupId>com.hsobs</groupId>
			<artifactId>hsobs-module-app</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
		
		<!-- 内容管理模块 -->
		<dependency>
			<groupId>com.hsobs</groupId>
			<artifactId>hsobs-module-cms</artifactId>
			<version>${project.parent.version}</version>
		</dependency>

		<!-- 内容管理-网站全文检索 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-cms-elasticsearch</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>

		<!-- 内容管理-页面静态化 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-cms-pagecache</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>

		<!-- 业务流程管理 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-bpm</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>

		<!-- 文件在线预览 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-filepreview</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>


	</dependencies>
	
	<build>
		<finalName>${finalName}</finalName>
		<!--<outputDirectory>${project.basedir}/src/main/webapp/WEB-INF/classes/</outputDirectory>-->
		<plugins>
			
			<!-- Spring Boot -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<includeSystemScope>true</includeSystemScope>
					<fork>true</fork>
					<addResources>true</addResources>
				</configuration>
			</plugin>

			<!-- war插件，war包名称不带版本号 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<configuration>
					<warName>${finalName}</warName>
				</configuration>
			</plugin>
			
			<!-- Eclipse 插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-eclipse-plugin</artifactId>
				<configuration>
					<wtpContextName>${finalName}</wtpContextName>
				</configuration>
			</plugin>
			
		</plugins>
	</build>

	
	<repositories>
		<repository>
			<id>aliyun-repos</id>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases><enabled>true</enabled></releases>
			<snapshots><enabled>false</enabled></snapshots>
		</repository>
		<repository>
			<id>jeesite-repos</id>
			<url>https://maven.jeesite.net/repository/maven-public</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>aliyun-repos</id>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases><enabled>true</enabled></releases>
			<snapshots><enabled>false</enabled></snapshots>
		</pluginRepository>
		<pluginRepository>
			<id>jeesite-repos</id>
			<url>https://maven.jeesite.net/repository/maven-public</url>
		</pluginRepository>
	</pluginRepositories>
	
</project>
