var fabric=fabric||{version:"5.2.1"};if("undefined"!=typeof exports?exports.fabric=fabric:"function"==typeof define&&define.amd&&define([],function(){return fabric}),"undefined"!=typeof document&&"undefined"!=typeof window)fabric.document=document instanceof("undefined"!=typeof HTMLDocument?HTMLDocument:Document)?document:document.implementation.createHTMLDocument(""),fabric.window=window;else{var jsdom=require("jsdom"),virtualWindow=new jsdom.JSDOM(decodeURIComponent("%3C!DOCTYPE%20html%3E%3Chtml%3E%3Chead%3E%3C%2Fhead%3E%3Cbody%3E%3C%2Fbody%3E%3C%2Fhtml%3E"),{features:{FetchExternalResources:["img"]},resources:"usable"}).window;fabric.document=virtualWindow.document,fabric.jsdomImplForWrapper=require("jsdom/lib/jsdom/living/generated/utils").implForWrapper,fabric.nodeCanvas=require("jsdom/lib/jsdom/utils").Canvas,fabric.window=virtualWindow,DOMParser=fabric.window.DOMParser}fabric.isTouchSupported="ontouchstart"in fabric.window||"ontouchstart"in fabric.document||fabric.window&&fabric.window.navigator&&fabric.window.navigator.maxTouchPoints>0,fabric.isLikelyNode="undefined"!=typeof Buffer&&"undefined"==typeof window,fabric.DPI=96,fabric.reNum="(?:[-+]?(?:\\d+|\\d*\\.\\d+)(?:[eE][-+]?\\d+)?)",fabric.commaWsp="(?:\\s+,?\\s*|,\\s*)",fabric.rePathCommand=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:[eE][-+]?\d+)?)/gi,fabric.reNonWord=/[ \n\.,;!\?\-]/,fabric.fontPaths={},fabric.iMatrix=[1,0,0,1,0,0],fabric.svgNS="http://www.w3.org/2000/svg",fabric.perfLimitSizeTotal=2097152,fabric.maxCacheSideLimit=4096,fabric.minCacheSideLimit=256,fabric.charWidthsCache={},fabric.textureSize=2048,fabric.disableStyleCopyPaste=!1,fabric.enableGLFiltering=!0,fabric.devicePixelRatio=fabric.window.devicePixelRatio||fabric.window.webkitDevicePixelRatio||fabric.window.mozDevicePixelRatio||1,fabric.browserShadowBlurConstant=1,fabric.arcToSegmentsCache={},fabric.boundsOfCurveCache={},fabric.cachesBoundsOfCurve=!0,fabric.forceGLPutImageData=!1,fabric.initFilterBackend=function(){return fabric.enableGLFiltering&&fabric.isWebglSupported&&fabric.isWebglSupported(fabric.textureSize)?(console.log("max texture size: "+fabric.maxTextureSize),new fabric.WebglFilterBackend({tileSize:fabric.textureSize})):fabric.Canvas2dFilterBackend?new fabric.Canvas2dFilterBackend:void 0};"undefined"!=typeof document&&"undefined"!=typeof window&&(window.fabric=fabric);if("undefined"==typeof eventjs)var eventjs={};if(function(e){e.modifyEventListener=!1,e.modifySelectors=!1,e.configure=function(t){isFinite(t.modifyEventListener)&&(e.modifyEventListener=t.modifyEventListener),isFinite(t.modifySelectors)&&(e.modifySelectors=t.modifySelectors),l===!1&&e.modifyEventListener&&p(),g===!1&&e.modifySelectors&&m()},e.add=function(e,t,r,o){return n(e,t,r,o,"add")},e.remove=function(e,t,r,o){return n(e,t,r,o,"remove")},e.returnFalse=function(){return!1},e.stop=function(e){e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0,e.cancelBubbleCount=0)},e.prevent=function(e){e&&(e.preventDefault?e.preventDefault():e.preventManipulation?e.preventManipulation():e.returnValue=!1)},e.cancel=function(t){e.stop(t),e.prevent(t)},e.blur=function(){var e=document.activeElement;if(e){var t=document.activeElement.nodeName;("INPUT"===t||"TEXTAREA"===t||"true"===e.contentEditable)&&e.blur&&e.blur()}},e.getEventSupport=function(e,t){if("string"==typeof e&&(t=e,e=window),t="on"+t,t in e)return!0;if(e.setAttribute||(e=document.createElement("div")),e.setAttribute&&e.removeAttribute){e.setAttribute(t,"");var n="function"==typeof e[t];return"undefined"!=typeof e[t]&&(e[t]=null),e.removeAttribute(t),n}};var t=function(e){if(!e||"object"!=typeof e)return e;var n=new e.constructor;for(var r in e)n[r]=e[r]&&"object"==typeof e[r]?t(e[r]):e[r];return n},n=function(i,s,v,l,p,g){if(l=l||{},"[object Object]"===String(i)){var m=i;if(i=m.target,delete m.target,!m.type||!m.listener){for(var y in m){var h=m[y];"function"!=typeof h&&(l[y]=h)}var j={};for(var x in m){var y=x.split(","),w=m[x],b={};for(var P in l)b[P]=l[P];if("function"==typeof w)var v=w;else{if("function"!=typeof w.listener)continue;var v=w.listener;for(var P in w)"function"!=typeof w[P]&&(b[P]=w[P])}for(var M=0;M<y.length;M++)j[x]=eventjs.add(i,y[M],v,b,p)}return j}s=m.type,delete m.type,v=m.listener,delete m.listener;for(var x in m)l[x]=m[x]}if(i&&s&&v){if("string"==typeof i&&"ready"===s){if(!window.eventjs_stallOnReady){var E=(new Date).getTime(),L=l.timeout,T=l.interval||1e3/60,G=window.setInterval(function(){(new Date).getTime()-E>L&&window.clearInterval(G),document.querySelector(i)&&(window.clearInterval(G),setTimeout(v,1))},T);return}s="load",i=window}if("string"==typeof i){if(i=document.querySelectorAll(i),0===i.length)return o("Missing target on listener!",arguments);1===i.length&&(i=i[0])}var k,D={};if(i.length>0&&i!==window){for(var C=0,X=i.length;X>C;C++)k=n(i[C],s,v,t(l),p),k&&(D[C]=k);return r(D)}if("string"==typeof s&&(s=s.toLowerCase(),-1!==s.indexOf(" ")?s=s.split(" "):-1!==s.indexOf(",")&&(s=s.split(","))),"string"!=typeof s){if("number"==typeof s.length)for(var Y=0,S=s.length;S>Y;Y++)k=n(i,s[Y],v,t(l),p),k&&(D[s[Y]]=k);else for(var x in s)k="function"==typeof s[x]?n(i,x,s[x],t(l),p):n(i,x,s[x].listener,t(s[x]),p),k&&(D[x]=k);return r(D)}if(0===s.indexOf("on")&&(s=s.slice(2)),"object"!=typeof i)return o("Target is not defined!",arguments);if("function"!=typeof v)return o("Listener is not a function!",arguments);var H=l.useCapture||!1,F=c(i)+"."+c(v)+"."+(H?1:0);if(e.Gesture&&e.Gesture._gestureHandlers[s]){if(F=s+F,"remove"===p){if(!u[F])return;u[F].remove(),delete u[F]}else if("add"===p){if(u[F])return u[F].add(),u[F];if(l.useCall&&!e.modifyEventListener){var _=v;v=function(e,t){for(var n in t)e[n]=t[n];return _.call(i,e)}}l.gesture=s,l.target=i,l.listener=v,l.fromOverwrite=g,u[F]=e.proxy[s](l)}return u[F]}for(var O,U=a(s),M=0;M<U.length;M++)if(s=U[M],O=s+"."+F,"remove"===p){if(!u[O])continue;i[d](s,v,H),delete u[O]}else if("add"===p){if(u[O])return u[O];i[f](s,v,H),u[O]={id:O,type:s,target:i,listener:v,remove:function(){for(var t=0;t<U.length;t++)e.remove(i,U[t],v,l)}}}return u[O]}},r=function(e){return{remove:function(){for(var t in e)e[t].remove()},add:function(){for(var t in e)e[t].add()}}},o=function(e,t){"undefined"!=typeof console&&"undefined"!=typeof console.error&&console.error(e,t)},i={msPointer:["MSPointerDown","MSPointerMove","MSPointerUp"],touch:["touchstart","touchmove","touchend"],mouse:["mousedown","mousemove","mouseup"]},s={MSPointerDown:0,MSPointerMove:1,MSPointerUp:2,touchstart:0,touchmove:1,touchend:2,mousedown:0,mousemove:1,mouseup:2},a=(function(){e.supports={},window.navigator.msPointerEnabled&&(e.supports.msPointer=!0),e.getEventSupport("touchstart")&&(e.supports.touch=!0),e.getEventSupport("mousedown")&&(e.supports.mouse=!0)}(),function(){return function(t){var n=document.addEventListener?"":"on",r=s[t];if(isFinite(r)){var o=[];for(var a in e.supports)o.push(n+i[a][r]);return o}return[n+t]}}()),u={},v=0,c=function(e){return e===window?"#window":e===document?"#document":(e.uniqueID||(e.uniqueID="e"+v++),e.uniqueID)},f=document.addEventListener?"addEventListener":"attachEvent",d=document.removeEventListener?"removeEventListener":"detachEvent";e.createPointerEvent=function(t,n,r){var o=n.gesture,i=n.target,s=t.changedTouches||e.proxy.getCoords(t);if(s.length){var a=s[0];n.pointers=r?[]:s,n.pageX=a.pageX,n.pageY=a.pageY,n.x=n.pageX,n.y=n.pageY}var u=document.createEvent("Event");u.initEvent(o,!0,!0),u.originalEvent=t;for(var v in n)"target"!==v&&(u[v]=n[v]);var c=u.type;e.Gesture&&e.Gesture._gestureHandlers[c]&&n.oldListener.call(i,u,n,!1)};var l=!1,p=function(){if(window.HTMLElement){var t=function(t){var r=function(r){var o=r+"EventListener",i=t[o];t[o]=function(t,o,s){if(e.Gesture&&e.Gesture._gestureHandlers[t]){var u=s;"object"==typeof s?u.useCall=!0:u={useCall:!0,useCapture:s},n(this,t,o,u,r,!0)}else for(var v=a(t),c=0;c<v.length;c++)i.call(this,v[c],o,s)}};r("add"),r("remove")};navigator.userAgent.match(/Firefox/)?(t(HTMLDivElement.prototype),t(HTMLCanvasElement.prototype)):t(HTMLElement.prototype),t(document),t(window)}},g=!1,m=function(){var e=NodeList.prototype;e.removeEventListener=function(e,t,n){for(var r=0,o=this.length;o>r;r++)this[r].removeEventListener(e,t,n)},e.addEventListener=function(e,t,n){for(var r=0,o=this.length;o>r;r++)this[r].addEventListener(e,t,n)}};return e}(eventjs),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";e.pointerSetup=function(e,t){e.target=e.target||window,e.doc=e.target.ownerDocument||e.target,e.minFingers=e.minFingers||e.fingers||1,e.maxFingers=e.maxFingers||e.fingers||1/0,e.position=e.position||"relative",delete e.fingers,t=t||{},t.enabled=!0,t.gesture=e.gesture,t.target=e.target,t.env=e.env,eventjs.modifyEventListener&&e.fromOverwrite&&(e.oldListener=e.listener,e.listener=eventjs.createPointerEvent);var n=0,r=0===t.gesture.indexOf("pointer")&&eventjs.modifyEventListener?"pointer":"mouse";return e.oldListener&&(t.oldListener=e.oldListener),t.listener=e.listener,t.proxy=function(n){t.defaultListener=e.listener,e.listener=n,n(e.event,t)},t.add=function(){t.enabled!==!0&&(e.onPointerDown&&eventjs.add(e.target,r+"down",e.onPointerDown),e.onPointerMove&&eventjs.add(e.doc,r+"move",e.onPointerMove),e.onPointerUp&&eventjs.add(e.doc,r+"up",e.onPointerUp),t.enabled=!0)},t.remove=function(){t.enabled!==!1&&(e.onPointerDown&&eventjs.remove(e.target,r+"down",e.onPointerDown),e.onPointerMove&&eventjs.remove(e.doc,r+"move",e.onPointerMove),e.onPointerUp&&eventjs.remove(e.doc,r+"up",e.onPointerUp),t.reset(),t.enabled=!1)},t.pause=function(t){!e.onPointerMove||t&&!t.move||eventjs.remove(e.doc,r+"move",e.onPointerMove),!e.onPointerUp||t&&!t.up||eventjs.remove(e.doc,r+"up",e.onPointerUp),n=e.fingers,e.fingers=0},t.resume=function(t){!e.onPointerMove||t&&!t.move||eventjs.add(e.doc,r+"move",e.onPointerMove),!e.onPointerUp||t&&!t.up||eventjs.add(e.doc,r+"up",e.onPointerUp),e.fingers=n},t.reset=function(){e.tracker={},e.fingers=0},t};var t=eventjs.supports;eventjs.isMouse=!!t.mouse,eventjs.isMSPointer=!!t.touch,eventjs.isTouch=!!t.msPointer,e.pointerStart=function(t,n,r){var o=(t.type||"mousedown").toUpperCase();0===o.indexOf("MOUSE")?(eventjs.isMouse=!0,eventjs.isTouch=!1,eventjs.isMSPointer=!1):0===o.indexOf("TOUCH")?(eventjs.isMouse=!1,eventjs.isTouch=!0,eventjs.isMSPointer=!1):0===o.indexOf("MSPOINTER")&&(eventjs.isMouse=!1,eventjs.isTouch=!1,eventjs.isMSPointer=!0);var i=function(e,t){var n=r.bbox,o=a[t]={};switch(r.position){case"absolute":o.offsetX=0,o.offsetY=0;break;case"differenceFromLast":o.offsetX=e.pageX,o.offsetY=e.pageY;break;case"difference":o.offsetX=e.pageX,o.offsetY=e.pageY;break;case"move":o.offsetX=e.pageX-n.x1,o.offsetY=e.pageY-n.y1;break;default:o.offsetX=n.x1-n.scrollLeft,o.offsetY=n.y1-n.scrollTop}var i=e.pageX-o.offsetX,s=e.pageY-o.offsetY;o.rotation=0,o.scale=1,o.startTime=o.moveTime=(new Date).getTime(),o.move={x:i,y:s},o.start={x:i,y:s},r.fingers++};r.event=t,n.defaultListener&&(r.listener=n.defaultListener,delete n.defaultListener);for(var s=!r.fingers,a=r.tracker,u=t.changedTouches||e.getCoords(t),v=u.length,c=0;v>c;c++){var f=u[c],d=f.identifier||1/0;if(r.fingers){if(r.fingers>=r.maxFingers){var l=[];for(var d in r.tracker)l.push(d);return n.identifier=l.join(","),s}var p=0;for(var g in a){if(a[g].up){delete a[g],i(f,d),r.cancel=!0;break}p++}if(a[d])continue;i(f,d)}else a=r.tracker={},n.bbox=r.bbox=e.getBoundingBox(r.target),r.fingers=0,r.cancel=!1,i(f,d)}var l=[];for(var d in r.tracker)l.push(d);return n.identifier=l.join(","),s},e.pointerEnd=function(e,t,n,r){for(var o=e.touches||[],i=o.length,s={},a=0;i>a;a++){var u=o[a],v=u.identifier;s[v||1/0]=!0}for(var v in n.tracker){var c=n.tracker[v];s[v]||c.up||(r&&r({pageX:c.pageX,pageY:c.pageY,changedTouches:[{pageX:c.pageX,pageY:c.pageY,identifier:"Infinity"===v?1/0:v}]},"up"),c.up=!0,n.fingers--)}if(0!==n.fingers)return!1;var f=[];n.gestureFingers=0;for(var v in n.tracker)n.gestureFingers++,f.push(v);return t.identifier=f.join(","),!0},e.getCoords=function(t){return e.getCoords="undefined"!=typeof t.pageX?function(e){return Array({type:"mouse",x:e.pageX,y:e.pageY,pageX:e.pageX,pageY:e.pageY,identifier:e.pointerId||1/0})}:function(e){var t=document.documentElement;return e=e||window.event,Array({type:"mouse",x:e.clientX+t.scrollLeft,y:e.clientY+t.scrollTop,pageX:e.clientX+t.scrollLeft,pageY:e.clientY+t.scrollTop,identifier:1/0})},e.getCoords(t)},e.getCoord=function(t){if("ontouchstart"in window){var n=0,r=0;e.getCoord=function(e){var t=e.changedTouches;return t&&t.length?{x:n=t[0].pageX,y:r=t[0].pageY}:{x:n,y:r}}}else e.getCoord="undefined"!=typeof t.pageX&&"undefined"!=typeof t.pageY?function(e){return{x:e.pageX,y:e.pageY}}:function(e){var t=document.documentElement;return e=e||window.event,{x:e.clientX+t.scrollLeft,y:e.clientY+t.scrollTop}};return e.getCoord(t)};var n=function(e,t){var n=parseFloat(e.getPropertyValue(t),10);return isFinite(n)?n:0};return e.getBoundingBox=function(e){(e===window||e===document)&&(e=document.body);var t={},r=e.getBoundingClientRect();t.width=r.width,t.height=r.height,t.x1=r.left,t.y1=r.top,t.scaleX=r.width/e.offsetWidth||1,t.scaleY=r.height/e.offsetHeight||1,t.scrollLeft=0,t.scrollTop=0;var o=window.getComputedStyle(e),i="border-box"===o.getPropertyValue("box-sizing");if(i===!1){var s=n(o,"border-left-width"),a=n(o,"border-right-width"),u=n(o,"border-bottom-width"),v=n(o,"border-top-width");t.border=[s,a,v,u],t.x1+=s,t.y1+=v,t.width-=a+s,t.height-=u+v}t.x2=t.x1+t.width,t.y2=t.y1+t.height;for(var c=o.getPropertyValue("position"),f="fixed"===c?e:e.parentNode;null!==f&&f!==document.body&&void 0!==f.scrollTop;){var o=window.getComputedStyle(f),c=o.getPropertyValue("position");if("absolute"===c);else{if("fixed"===c){t.scrollTop-=f.parentNode.scrollTop,t.scrollLeft-=f.parentNode.scrollLeft;break}t.scrollLeft+=f.scrollLeft,t.scrollTop+=f.scrollTop}f=f.parentNode}return t.scrollBodyLeft=void 0!==window.pageXOffset?window.pageXOffset:(document.documentElement||document.body.parentNode||document.body).scrollLeft,t.scrollBodyTop=void 0!==window.pageYOffset?window.pageYOffset:(document.documentElement||document.body.parentNode||document.body).scrollTop,t.scrollLeft-=t.scrollBodyLeft,t.scrollTop-=t.scrollBodyTop,t},function(){var t,n=navigator.userAgent.toLowerCase(),r=-1!==n.indexOf("macintosh");t=r&&-1!==n.indexOf("khtml")?{91:!0,93:!0}:r&&-1!==n.indexOf("firefox")?{224:!0}:{17:!0},(e.metaTrackerReset=function(){eventjs.fnKey=e.fnKey=!1,eventjs.metaKey=e.metaKey=!1,eventjs.escKey=e.escKey=!1,eventjs.ctrlKey=e.ctrlKey=!1,eventjs.shiftKey=e.shiftKey=!1,eventjs.altKey=e.altKey=!1})(),e.metaTracker=function(n){var r="keydown"===n.type;27===n.keyCode&&(eventjs.escKey=e.escKey=r),t[n.keyCode]&&(eventjs.metaKey=e.metaKey=r),eventjs.ctrlKey=e.ctrlKey=n.ctrlKey,eventjs.shiftKey=e.shiftKey=n.shiftKey,eventjs.altKey=e.altKey=n.altKey}}(),e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if(eventjs.MutationObserver=function(){var e=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver,t=!e&&function(){var e=document.createElement("p"),t=!1,n=function(){t=!0};if(e.addEventListener)e.addEventListener("DOMAttrModified",n,!1);else{if(!e.attachEvent)return!1;e.attachEvent("onDOMAttrModified",n)}return e.setAttribute("id","target"),t}();return function(n,r){if(e){var o={subtree:!1,attributes:!0},i=new e(function(e){e.forEach(function(e){r.call(e.target,e.attributeName)})});i.observe(n,o)}else t?eventjs.add(n,"DOMAttrModified",function(e){r.call(n,e.attrName)}):"onpropertychange"in document.body&&eventjs.add(n,"propertychange",function(){r.call(n,window.event.propertyName)})}}(),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";return e.click=function(t){t.gesture=t.gesture||"click",t.maxFingers=t.maxFingers||t.fingers||1,t.onPointerDown=function(r){e.pointerStart(r,n,t)&&eventjs.add(t.target,"mouseup",t.onPointerUp)},t.onPointerUp=function(r){if(e.pointerEnd(r,n,t)){eventjs.remove(t.target,"mouseup",t.onPointerUp);var o=r.changedTouches||e.getCoords(r),i=o[0],s=t.bbox,a=e.getBoundingBox(t.target),u=i.pageY-a.scrollBodyTop,v=i.pageX-a.scrollBodyLeft;if(v>s.x1&&u>s.y1&&v<s.x2&&u<s.y2&&s.scrollTop===a.scrollTop){for(var c in t.tracker)break;var f=t.tracker[c];n.x=f.start.x,n.y=f.start.y,t.listener(r,n)}}};var n=e.pointerSetup(t);return n.state="click",eventjs.add(t.target,"mousedown",t.onPointerDown),n},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.click=e.click,e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";return e.dbltap=e.dblclick=function(t){t.gesture=t.gesture||"dbltap",t.maxFingers=t.maxFingers||t.fingers||1;var n,r,o,i,s,a=700;t.onPointerDown=function(v){var c=v.changedTouches||e.getCoords(v);n&&!r?(s=c[0],r=(new Date).getTime()-n):(i=c[0],n=(new Date).getTime(),r=0,clearTimeout(o),o=setTimeout(function(){n=0},a)),e.pointerStart(v,u,t)&&(eventjs.add(t.target,"mousemove",t.onPointerMove).listener(v),eventjs.add(t.target,"mouseup",t.onPointerUp))},t.onPointerMove=function(a){if(n&&!r){var u=a.changedTouches||e.getCoords(a);s=u[0]}var v=t.bbox,c=s.pageX-v.x1,f=s.pageY-v.y1;c>0&&c<v.width&&f>0&&f<v.height&&Math.abs(s.pageX-i.pageX)<=25&&Math.abs(s.pageY-i.pageY)<=25||(eventjs.remove(t.target,"mousemove",t.onPointerMove),clearTimeout(o),n=r=0)},t.onPointerUp=function(i){if(e.pointerEnd(i,u,t)&&(eventjs.remove(t.target,"mousemove",t.onPointerMove),eventjs.remove(t.target,"mouseup",t.onPointerUp)),n&&r){if(a>=r){u.state=t.gesture;for(var s in t.tracker)break;var v=t.tracker[s];u.x=v.start.x,u.y=v.start.y,t.listener(i,u)}clearTimeout(o),n=r=0}};var u=e.pointerSetup(t);return u.state="dblclick",eventjs.add(t.target,"mousedown",t.onPointerDown),u},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.dbltap=e.dbltap,eventjs.Gesture._gestureHandlers.dblclick=e.dblclick,e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";return e.dragElement=function(t,n){e.drag({event:n,target:t,position:"move",listener:function(e,n){t.style.left=n.x+"px",t.style.top=n.y+"px",eventjs.prevent(e)}})},e.drag=function(t){t.gesture="drag",t.onPointerDown=function(r){e.pointerStart(r,n,t)&&(t.monitor||(eventjs.add(t.doc,"mousemove",t.onPointerMove),eventjs.add(t.doc,"mouseup",t.onPointerUp))),t.onPointerMove(r,"down")},t.onPointerMove=function(r,o){if(!t.tracker)return t.onPointerDown(r);for(var i=(t.bbox,r.changedTouches||e.getCoords(r)),s=i.length,a=0;s>a;a++){var u=i[a],v=u.identifier||1/0,c=t.tracker[v];c&&(c.pageX=u.pageX,c.pageY=u.pageY,n.state=o||"move",n.identifier=v,n.start=c.start,n.fingers=t.fingers,"differenceFromLast"===t.position?(n.x=c.pageX-c.offsetX,n.y=c.pageY-c.offsetY,c.offsetX=c.pageX,c.offsetY=c.pageY):(n.x=c.pageX-c.offsetX,n.y=c.pageY-c.offsetY),t.listener(r,n))}},t.onPointerUp=function(r){e.pointerEnd(r,n,t,t.onPointerMove)&&(t.monitor||(eventjs.remove(t.doc,"mousemove",t.onPointerMove),eventjs.remove(t.doc,"mouseup",t.onPointerUp)))};var n=e.pointerSetup(t);return t.event?t.onPointerDown(t.event):(eventjs.add(t.target,"mousedown",t.onPointerDown),t.monitor&&(eventjs.add(t.doc,"mousemove",t.onPointerMove),eventjs.add(t.doc,"mouseup",t.onPointerUp))),n},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.drag=e.drag,e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";var t=Math.PI/180,n=function(e,t){var n=0,r=0,o=0;for(var i in t){var s=t[i];s.up||(n+=s.move.x,r+=s.move.y,o++)}return e.x=n/=o,e.y=r/=o,e};return e.gesture=function(r){r.gesture=r.gesture||"gesture",r.minFingers=r.minFingers||r.fingers||2,r.onPointerDown=function(t){var i=r.fingers;if(e.pointerStart(t,o,r)&&(eventjs.add(r.doc,"mousemove",r.onPointerMove),eventjs.add(r.doc,"mouseup",r.onPointerUp)),r.fingers===r.minFingers&&i!==r.fingers){o.fingers=r.minFingers,o.scale=1,o.rotation=0,o.state="start";var s="";for(var a in r.tracker)s+=a;o.identifier=parseInt(s),n(o,r.tracker),r.listener(t,o)}},r.onPointerMove=function(i){for(var s=r.bbox,a=r.tracker,u=i.changedTouches||e.getCoords(i),v=u.length,c=0;v>c;c++){var f=u[c],d=f.identifier||1/0,l=a[d];l&&(l.move.x=f.pageX-s.x1,l.move.y=f.pageY-s.y1)}if(!(r.fingers<r.minFingers)){var u=[],p=0,g=0;n(o,a);for(var d in a){var f=a[d];if(!f.up){var m=f.start;if(!m.distance){var y=m.x-o.x,h=m.y-o.y;m.distance=Math.sqrt(y*y+h*h),m.angle=Math.atan2(y,h)/t}var y=f.move.x-o.x,h=f.move.y-o.y,j=Math.sqrt(y*y+h*h);0!==m.distance&&(p+=j/m.distance);var x=Math.atan2(y,h)/t,w=(m.angle-x+360)%360-180;f.DEG2=f.DEG1,f.DEG1=w>0?w:-w,"undefined"!=typeof f.DEG2&&(w>0?f.rotation+=f.DEG1-f.DEG2:f.rotation-=f.DEG1-f.DEG2,g+=f.rotation),u.push(f.move)}}o.touches=u,o.fingers=r.fingers,o.scale=p/r.fingers,o.rotation=g/r.fingers,o.state="change",r.listener(i,o)}},r.onPointerUp=function(t){var n=r.fingers;e.pointerEnd(t,o,r)&&(eventjs.remove(r.doc,"mousemove",r.onPointerMove),eventjs.remove(r.doc,"mouseup",r.onPointerUp)),n===r.minFingers&&r.fingers<r.minFingers&&(o.fingers=r.fingers,o.state="end",r.listener(t,o))};var o=e.pointerSetup(r);return eventjs.add(r.target,"mousedown",r.onPointerDown),o},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.gesture=e.gesture,e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";return e.pointerdown=e.pointermove=e.pointerup=function(t){if(t.gesture=t.gesture||"pointer",!t.target.isPointerEmitter){var n=!0;t.onPointerDown=function(e){n=!1,r.gesture="pointerdown",t.listener(e,r)},t.onPointerMove=function(e){r.gesture="pointermove",t.listener(e,r,n)},t.onPointerUp=function(e){n=!0,r.gesture="pointerup",t.listener(e,r,!0)};var r=e.pointerSetup(t);return eventjs.add(t.target,"mousedown",t.onPointerDown),eventjs.add(t.target,"mousemove",t.onPointerMove),eventjs.add(t.doc,"mouseup",t.onPointerUp),t.target.isPointerEmitter=!0,r}},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.pointerdown=e.pointerdown,eventjs.Gesture._gestureHandlers.pointermove=e.pointermove,eventjs.Gesture._gestureHandlers.pointerup=e.pointerup,e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";return e.shake=function(e){var t={gesture:"devicemotion",acceleration:{},accelerationIncludingGravity:{},target:e.target,listener:e.listener,remove:function(){window.removeEventListener("devicemotion",v,!1)}},n=4,r=1e3,o=200,i=3,s=(new Date).getTime(),a={x:0,y:0,z:0},u={x:{count:0,value:0},y:{count:0,value:0},z:{count:0,value:0}},v=function(v){var c=.8,f=v.accelerationIncludingGravity;if(a.x=c*a.x+(1-c)*f.x,a.y=c*a.y+(1-c)*f.y,a.z=c*a.z+(1-c)*f.z,t.accelerationIncludingGravity=a,t.acceleration.x=f.x-a.x,t.acceleration.y=f.y-a.y,t.acceleration.z=f.z-a.z,"devicemotion"===e.gesture)return void e.listener(v,t);for(var d="xyz",l=(new Date).getTime(),p=0,g=d.length;g>p;p++){var m=d[p],y=t.acceleration[m],h=u[m],j=Math.abs(y);if(!(r>l-s)&&j>n){var x=l*y/j,w=Math.abs(x+h.value);h.value&&o>w?(h.value=x,h.count++,h.count===i&&(e.listener(v,t),s=l,h.value=0,h.count=0)):(h.value=x,h.count=1)}}};return window.addEventListener?(window.addEventListener("devicemotion",v,!1),t):void 0},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.shake=e.shake,e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";var t=Math.PI/180;return e.swipe=function(n){n.snap=n.snap||90,n.threshold=n.threshold||1,n.gesture=n.gesture||"swipe",n.onPointerDown=function(t){e.pointerStart(t,r,n)&&(eventjs.add(n.doc,"mousemove",n.onPointerMove).listener(t),eventjs.add(n.doc,"mouseup",n.onPointerUp))},n.onPointerMove=function(t){for(var r=t.changedTouches||e.getCoords(t),o=r.length,i=0;o>i;i++){var s=r[i],a=s.identifier||1/0,u=n.tracker[a];u&&(u.move.x=s.pageX,u.move.y=s.pageY,u.moveTime=(new Date).getTime())}},n.onPointerUp=function(o){if(e.pointerEnd(o,r,n)){eventjs.remove(n.doc,"mousemove",n.onPointerMove),eventjs.remove(n.doc,"mouseup",n.onPointerUp);var i,s,a,u,v={x:0,y:0},c=0,f=0,d=0;for(var l in n.tracker){var p=n.tracker[l],g=p.move.x-p.start.x,m=p.move.y-p.start.y;c+=p.move.x,f+=p.move.y,v.x+=p.start.x,v.y+=p.start.y,d++;var y=Math.sqrt(g*g+m*m),h=p.moveTime-p.startTime,u=Math.atan2(g,m)/t+180,s=h?y/h:0;if("undefined"==typeof a)a=u,i=s;else{if(!(Math.abs(u-a)<=20))return;a=(a+u)/2,i=(i+s)/2}}var j=n.gestureFingers;n.minFingers<=j&&n.maxFingers>=j&&i>n.threshold&&(v.x/=d,v.y/=d,r.start=v,r.x=c/d,r.y=f/d,r.angle=-(((a/n.snap+.5>>0)*n.snap||360)-360),r.velocity=i,r.fingers=j,r.state="swipe",n.listener(o,r))}};var r=e.pointerSetup(n);return eventjs.add(n.target,"mousedown",n.onPointerDown),r},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.swipe=e.swipe,e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";return e.longpress=function(t){return t.gesture="longpress",e.tap(t)},e.tap=function(t){t.delay=t.delay||500,t.timeout=t.timeout||250,t.driftDeviance=t.driftDeviance||10,t.gesture=t.gesture||"tap";var n,r;t.onPointerDown=function(i){if(e.pointerStart(i,o,t)){if(n=(new Date).getTime(),eventjs.add(t.doc,"mousemove",t.onPointerMove).listener(i),eventjs.add(t.doc,"mouseup",t.onPointerUp),"longpress"!==t.gesture)return;r=setTimeout(function(){if(!(i.cancelBubble&&++i.cancelBubbleCount>1)){var e=0;for(var n in t.tracker){var r=t.tracker[n];if(r.end===!0)return;if(t.cancel)return;e++}t.minFingers<=e&&t.maxFingers>=e&&(o.state="start",o.fingers=e,o.x=r.start.x,o.y=r.start.y,t.listener(i,o))}},t.delay)}},t.onPointerMove=function(n){for(var r=t.bbox,o=n.changedTouches||e.getCoords(n),i=o.length,s=0;i>s;s++){var a=o[s],u=a.identifier||1/0,v=t.tracker[u];if(v){var c=a.pageX-r.x1-parseInt(window.scrollX),f=a.pageY-r.y1-parseInt(window.scrollY),d=c-v.start.x,l=f-v.start.y,p=Math.sqrt(d*d+l*l);if(!(c>0&&c<r.width&&f>0&&f<r.height&&p<=t.driftDeviance))return eventjs.remove(t.doc,"mousemove",t.onPointerMove),void(t.cancel=!0)}}},t.onPointerUp=function(i){if(e.pointerEnd(i,o,t)){if(clearTimeout(r),eventjs.remove(t.doc,"mousemove",t.onPointerMove),eventjs.remove(t.doc,"mouseup",t.onPointerUp),i.cancelBubble&&++i.cancelBubbleCount>1)return;if("longpress"===t.gesture)return void("start"===o.state&&(o.state="end",t.listener(i,o)));if(t.cancel)return;if((new Date).getTime()-n>t.timeout)return;var s=t.gestureFingers;t.minFingers<=s&&t.maxFingers>=s&&(o.state="tap",o.fingers=t.gestureFingers,t.listener(i,o))}};var o=e.pointerSetup(t);return eventjs.add(t.target,"mousedown",t.onPointerDown),o},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.tap=e.tap,eventjs.Gesture._gestureHandlers.longpress=e.longpress,e}(eventjs.proxy),"undefined"==typeof eventjs)var eventjs={};if("undefined"==typeof eventjs.proxy&&(eventjs.proxy={}),eventjs.proxy=function(e){"use strict";return e.wheelPreventElasticBounce=function(e){e&&("string"==typeof e&&(e=document.querySelector(e)),eventjs.add(e,"wheel",function(e,t){t.preventElasticBounce(),eventjs.stop(e)}))},e.wheel=function(e){var t,n=e.timeout||150,r=0,o={gesture:"wheel",state:"start",wheelDelta:0,target:e.target,listener:e.listener,preventElasticBounce:function(e){var t=this.target,n=t.scrollTop,r=n+t.offsetHeight,o=t.scrollHeight;r===o&&this.wheelDelta<=0?eventjs.cancel(e):0===n&&this.wheelDelta>=0&&eventjs.cancel(e),eventjs.stop(e)},add:function(){e.target[s](u,i,!1)},remove:function(){e.target[a](u,i,!1)}},i=function(i){i=i||window.event,o.state=r++?"change":"start",o.wheelDelta=i.detail?-20*i.detail:i.wheelDelta,e.listener(i,o),clearTimeout(t),t=setTimeout(function(){r=0,o.state="end",o.wheelDelta=0,e.listener(i,o)},n)},s=document.addEventListener?"addEventListener":"attachEvent",a=document.removeEventListener?"removeEventListener":"detachEvent",u=eventjs.getEventSupport("mousewheel")?"mousewheel":"DOMMouseScroll";return e.target[s](u,i,!1),o},eventjs.Gesture=eventjs.Gesture||{},eventjs.Gesture._gestureHandlers=eventjs.Gesture._gestureHandlers||{},eventjs.Gesture._gestureHandlers.wheel=e.wheel,e}(eventjs.proxy),"undefined"==typeof Event)var Event={};"undefined"==typeof Event.proxy&&(Event.proxy={}),Event.proxy=function(e){"use strict";return e.orientation=function(e){var t={gesture:"orientationchange",previous:null,current:window.orientation,target:e.target,listener:e.listener,remove:function(){window.removeEventListener("orientationchange",n,!1)}},n=function(n){return t.previous=t.current,t.current=window.orientation,null!==t.previous&&t.previous!=t.current?void e.listener(n,t):void 0};return window.DeviceOrientationEvent&&window.addEventListener("orientationchange",n,!1),t},Event.Gesture=Event.Gesture||{},Event.Gesture._gestureHandlers=Event.Gesture._gestureHandlers||{},Event.Gesture._gestureHandlers.orientation=e.orientation,e}(Event.proxy);!function(){function e(e,t){if(this.__eventListeners[e]){var n=this.__eventListeners[e];t?n[n.indexOf(t)]=!1:fabric.util.array.fill(n,!1)}}function t(e,t){if(this.__eventListeners||(this.__eventListeners={}),1===arguments.length)for(var n in e)this.on(n,e[n]);else this.__eventListeners[e]||(this.__eventListeners[e]=[]),this.__eventListeners[e].push(t);return this}function n(e,t){var n=function(){t.apply(this,arguments),this.off(e,n)}.bind(this);this.on(e,n)}function r(e,t){if(1===arguments.length)for(var r in e)n.call(this,r,e[r]);else n.call(this,e,t);return this}function o(t,n){if(!this.__eventListeners)return this;if(0===arguments.length)for(t in this.__eventListeners)e.call(this,t);else if(1===arguments.length&&"object"==typeof arguments[0])for(var r in t)e.call(this,r,t[r]);else e.call(this,t,n);return this}function i(e,t){if(!this.__eventListeners)return this;var n=this.__eventListeners[e];if(!n)return this;for(var r=0,o=n.length;o>r;r++)n[r]&&n[r].call(this,t||{});return this.__eventListeners[e]=n.filter(function(e){return e!==!1}),this}fabric.Observable={fire:i,on:t,once:r,off:o}}();fabric.Collection={_objects:[],add:function(){if(this._objects.push.apply(this._objects,arguments),this._onObjectAdded)for(var e=0,t=arguments.length;t>e;e++)this._onObjectAdded(arguments[e]);return this.renderOnAddRemove&&this.requestRenderAll(),this},insertAt:function(e,t,n){var r=this._objects;return n?r[t]=e:r.splice(t,0,e),this._onObjectAdded&&this._onObjectAdded(e),this.renderOnAddRemove&&this.requestRenderAll(),this},remove:function(){for(var e,t=this._objects,n=!1,r=0,o=arguments.length;o>r;r++)e=t.indexOf(arguments[r]),-1!==e&&(n=!0,t.splice(e,1),this._onObjectRemoved&&this._onObjectRemoved(arguments[r]));return this.renderOnAddRemove&&n&&this.requestRenderAll(),this},forEachObject:function(e,t){for(var n=this.getObjects(),r=0,o=n.length;o>r;r++)e.call(t,n[r],r,n);return this},getObjects:function(e){return"undefined"==typeof e?this._objects.concat():this._objects.filter(function(t){return t.type===e})},item:function(e){return this._objects[e]},isEmpty:function(){return 0===this._objects.length},size:function(){return this._objects.length},contains:function(e,t){return this._objects.indexOf(e)>-1?!0:t?this._objects.some(function(t){return"function"==typeof t.contains&&t.contains(e,!0)}):!1},complexity:function(){return this._objects.reduce(function(e,t){return e+=t.complexity?t.complexity():0},0)}};fabric.CommonMethods={_setOptions:function(e){for(var t in e)this.set(t,e[t])},_initGradient:function(e,t){!e||!e.colorStops||e instanceof fabric.Gradient||this.set(t,new fabric.Gradient(e))},_initPattern:function(e,t,n){!e||!e.source||e instanceof fabric.Pattern?n&&n():this.set(t,new fabric.Pattern(e,n))},_setObject:function(e){for(var t in e)this._set(t,e[t])},set:function(e,t){return"object"==typeof e?this._setObject(e):this._set(e,t),this},_set:function(e,t){this[e]=t},toggle:function(e){var t=this.get(e);return"boolean"==typeof t&&this.set(e,!t),this},get:function(e){return this[e]}};!function(e){var t=Math.sqrt,n=Math.atan2,r=Math.pow,o=Math.PI/180,i=Math.PI/2;fabric.util={cos:function(e){if(0===e)return 1;0>e&&(e=-e);var t=e/i;switch(t){case 1:case 3:return 0;case 2:return-1}return Math.cos(e)},sin:function(e){if(0===e)return 0;var t=e/i,n=1;switch(0>e&&(n=-1),t){case 1:return n;case 2:return 0;case 3:return-n}return Math.sin(e)},removeFromArray:function(e,t){var n=e.indexOf(t);return-1!==n&&e.splice(n,1),e},getRandomInt:function(e,t){return Math.floor(Math.random()*(t-e+1))+e},degreesToRadians:function(e){return e*o},radiansToDegrees:function(e){return e/o},rotatePoint:function(e,t,n){var r=new fabric.Point(e.x-t.x,e.y-t.y),o=fabric.util.rotateVector(r,n);return new fabric.Point(o.x,o.y).addEquals(t)},rotateVector:function(e,t){var n=fabric.util.sin(t),r=fabric.util.cos(t),o=e.x*r-e.y*n,i=e.x*n+e.y*r;return{x:o,y:i}},createVector:function(e,t){return new fabric.Point(t.x-e.x,t.y-e.y)},calcAngleBetweenVectors:function(e,t){return Math.acos((e.x*t.x+e.y*t.y)/(Math.hypot(e.x,e.y)*Math.hypot(t.x,t.y)))},getHatVector:function(e){return new fabric.Point(e.x,e.y).multiply(1/Math.hypot(e.x,e.y))},getBisector:function(e,t,n){var r=fabric.util.createVector(e,t),o=fabric.util.createVector(e,n),i=fabric.util.calcAngleBetweenVectors(r,o),a=fabric.util.calcAngleBetweenVectors(fabric.util.rotateVector(r,i),o),s=i*(0===a?1:-1)/2;return{vector:fabric.util.getHatVector(fabric.util.rotateVector(r,s)),angle:i}},projectStrokeOnPoints:function(e,t,n){var r=[],o=t.strokeWidth/2,i=t.strokeUniform?new fabric.Point(1/t.scaleX,1/t.scaleY):new fabric.Point(1,1),a=function(e){var t=o/Math.hypot(e.x,e.y);return new fabric.Point(e.x*t*i.x,e.y*t*i.y)};return e.length<=1?r:(e.forEach(function(s,u){var c,f,l=new fabric.Point(s.x,s.y);0===u?(f=e[u+1],c=n?a(fabric.util.createVector(f,l)).addEquals(l):e[e.length-1]):u===e.length-1?(c=e[u-1],f=n?a(fabric.util.createVector(c,l)).addEquals(l):e[0]):(c=e[u-1],f=e[u+1]);var v,d,p=fabric.util.getBisector(l,c,f),g=p.vector,m=p.angle;return"miter"===t.strokeLineJoin&&(v=-o/Math.sin(m/2),d=new fabric.Point(g.x*v*i.x,g.y*v*i.y),Math.hypot(d.x,d.y)/o<=t.strokeMiterLimit)?(r.push(l.add(d)),void r.push(l.subtract(d))):(v=-o*Math.SQRT2,d=new fabric.Point(g.x*v*i.x,g.y*v*i.y),r.push(l.add(d)),void r.push(l.subtract(d)))}),r)},transformPoint:function(e,t,n){return n?new fabric.Point(t[0]*e.x+t[2]*e.y,t[1]*e.x+t[3]*e.y):new fabric.Point(t[0]*e.x+t[2]*e.y+t[4],t[1]*e.x+t[3]*e.y+t[5])},makeBoundingBoxFromPoints:function(e,t){if(t)for(var n=0;n<e.length;n++)e[n]=fabric.util.transformPoint(e[n],t);var r=[e[0].x,e[1].x,e[2].x,e[3].x],o=fabric.util.array.min(r),i=fabric.util.array.max(r),a=i-o,s=[e[0].y,e[1].y,e[2].y,e[3].y],u=fabric.util.array.min(s),c=fabric.util.array.max(s),f=c-u;return{left:o,top:u,width:a,height:f}},invertTransform:function(e){var t=1/(e[0]*e[3]-e[1]*e[2]),n=[t*e[3],-t*e[1],-t*e[2],t*e[0]],r=fabric.util.transformPoint({x:e[4],y:e[5]},n,!0);return n[4]=-r.x,n[5]=-r.y,n},toFixed:function(e,t){return parseFloat(Number(e).toFixed(t))},parseUnit:function(e,t){var n=/\D{0,2}$/.exec(e),r=parseFloat(e);switch(t||(t=fabric.Text.DEFAULT_SVG_FONT_SIZE),n[0]){case"mm":return r*fabric.DPI/25.4;case"cm":return r*fabric.DPI/2.54;case"in":return r*fabric.DPI;case"pt":return r*fabric.DPI/72;case"pc":return r*fabric.DPI/72*12;case"em":return r*t;default:return r}},falseFunction:function(){return!1},getKlass:function(e,t){return e=fabric.util.string.camelize(e.charAt(0).toUpperCase()+e.slice(1)),fabric.util.resolveNamespace(t)[e]},getSvgAttributes:function(e){var t=["instantiated_by_use","style","id","class"];switch(e){case"linearGradient":t=t.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);break;case"radialGradient":t=t.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);break;case"stop":t=t.concat(["offset","stop-color","stop-opacity"])}return t},resolveNamespace:function(t){if(!t)return fabric;var n,r=t.split("."),o=r.length,i=e||fabric.window;for(n=0;o>n;++n)i=i[r[n]];return i},loadImage:function(e,t,n,r){if(!e)return void(t&&t.call(n,e));var o=fabric.util.createImage(),i=function(){t&&t.call(n,o,!1),o=o.onload=o.onerror=null};o.onload=i,o.onerror=function(){fabric.log("Error loading "+o.src),t&&t.call(n,null,!0),o=o.onload=o.onerror=null},0!==e.indexOf("data")&&void 0!==r&&null!==r&&(o.crossOrigin=r),"data:image/svg"===e.substring(0,14)&&(o.onload=null,fabric.util.loadImageInDom(o,i)),o.src=e},loadImageInDom:function(e,t){var n=fabric.document.createElement("div");n.style.width=n.style.height="1px",n.style.left=n.style.top="-100%",n.style.position="absolute",n.appendChild(e),fabric.document.querySelector("body").appendChild(n),e.onload=function(){t(),n.parentNode.removeChild(n),n=null}},enlivenObjects:function(e,t,n,r){function o(){++a===s&&t&&t(i.filter(function(e){return e}))}e=e||[];var i=[],a=0,s=e.length;return s?void e.forEach(function(e,t){if(!e||!e.type)return void o();var a=fabric.util.getKlass(e.type,n);a.fromObject(e,function(n,a){a||(i[t]=n),r&&r(e,n,a),o()})}):void(t&&t(i))},enlivenObjectEnlivables:function(e,t,n){var r=fabric.Object.ENLIVEN_PROPS.filter(function(t){return!!e[t]});fabric.util.enlivenObjects(r.map(function(t){return e[t]}),function(e){var o={};r.forEach(function(n,r){o[n]=e[r],t&&(t[n]=e[r])}),n&&n(o)})},enlivenPatterns:function(e,t){function n(){++o===i&&t&&t(r)}e=e||[];var r=[],o=0,i=e.length;return i?void e.forEach(function(e,t){e&&e.source?new fabric.Pattern(e,function(e){r[t]=e,n()}):(r[t]=e,n())}):void(t&&t(r))},groupSVGElements:function(e,t,n){var r;return e&&1===e.length?e[0]:(t&&(t.width&&t.height?t.centerPoint={x:t.width/2,y:t.height/2}:(delete t.width,delete t.height)),r=new fabric.Group(e,t),"undefined"!=typeof n&&(r.sourcePath=n),r)},populateWithProperties:function(e,t,n){if(n&&Array.isArray(n))for(var r=0,o=n.length;o>r;r++)n[r]in e&&(t[n[r]]=e[n[r]])},createCanvasElement:function(){return fabric.document.createElement("canvas")},copyCanvasElement:function(e){var t=fabric.util.createCanvasElement();return t.width=e.width,t.height=e.height,t.getContext("2d").drawImage(e,0,0),t},toDataURL:function(e,t,n){return e.toDataURL("image/"+t,n)},createImage:function(){return fabric.document.createElement("img")},multiplyTransformMatrices:function(e,t,n){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],n?0:e[0]*t[4]+e[2]*t[5]+e[4],n?0:e[1]*t[4]+e[3]*t[5]+e[5]]},qrDecompose:function(e){var i=n(e[1],e[0]),a=r(e[0],2)+r(e[1],2),s=t(a),u=(e[0]*e[3]-e[2]*e[1])/s,c=n(e[0]*e[2]+e[1]*e[3],a);return{angle:i/o,scaleX:s,scaleY:u,skewX:c/o,skewY:0,translateX:e[4],translateY:e[5]}},calcRotateMatrix:function(e){if(!e.angle)return fabric.iMatrix.concat();var t=fabric.util.degreesToRadians(e.angle),n=fabric.util.cos(t),r=fabric.util.sin(t);return[n,r,-r,n,0,0]},calcDimensionsMatrix:function(e){var t="undefined"==typeof e.scaleX?1:e.scaleX,n="undefined"==typeof e.scaleY?1:e.scaleY,r=[e.flipX?-t:t,0,0,e.flipY?-n:n,0,0],o=fabric.util.multiplyTransformMatrices,i=fabric.util.degreesToRadians;return e.skewX&&(r=o(r,[1,0,Math.tan(i(e.skewX)),1],!0)),e.skewY&&(r=o(r,[1,Math.tan(i(e.skewY)),0,1],!0)),r},composeMatrix:function(e){var t=[1,0,0,1,e.translateX||0,e.translateY||0],n=fabric.util.multiplyTransformMatrices;return e.angle&&(t=n(t,fabric.util.calcRotateMatrix(e))),(1!==e.scaleX||1!==e.scaleY||e.skewX||e.skewY||e.flipX||e.flipY)&&(t=n(t,fabric.util.calcDimensionsMatrix(e))),t},resetObjectTransform:function(e){e.scaleX=1,e.scaleY=1,e.skewX=0,e.skewY=0,e.flipX=!1,e.flipY=!1,e.rotate(0)},saveObjectTransform:function(e){return{scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,angle:e.angle,left:e.left,flipX:e.flipX,flipY:e.flipY,top:e.top}},isTransparent:function(e,t,n,r){r>0&&(t>r?t-=r:t=0,n>r?n-=r:n=0);var o,i,a=!0,s=e.getImageData(t,n,2*r||1,2*r||1),u=s.data.length;for(o=3;u>o&&(i=s.data[o],a=0>=i,a!==!1);o+=4);return s=null,a},parsePreserveAspectRatioAttribute:function(e){var t,n="meet",r="Mid",o="Mid",i=e.split(" ");return i&&i.length&&(n=i.pop(),"meet"!==n&&"slice"!==n?(t=n,n="meet"):i.length&&(t=i.pop())),r="none"!==t?t.slice(1,4):"none",o="none"!==t?t.slice(5,8):"none",{meetOrSlice:n,alignX:r,alignY:o}},clearFabricFontCache:function(e){e=(e||"").toLowerCase(),e?fabric.charWidthsCache[e]&&delete fabric.charWidthsCache[e]:fabric.charWidthsCache={}},limitDimsByArea:function(e,t){var n=Math.sqrt(t*e),r=Math.floor(t/n);return{x:Math.floor(n),y:r}},capValue:function(e,t,n){return Math.max(e,Math.min(t,n))},findScaleToFit:function(e,t){return Math.min(t.width/e.width,t.height/e.height)},findScaleToCover:function(e,t){return Math.max(t.width/e.width,t.height/e.height)},matrixToSVG:function(e){return"matrix("+e.map(function(e){return fabric.util.toFixed(e,fabric.Object.NUM_FRACTION_DIGITS)}).join(" ")+")"},removeTransformFromObject:function(e,t){var n=fabric.util.invertTransform(t),r=fabric.util.multiplyTransformMatrices(n,e.calcOwnMatrix());fabric.util.applyTransformToObject(e,r)},addTransformToObject:function(e,t){fabric.util.applyTransformToObject(e,fabric.util.multiplyTransformMatrices(t,e.calcOwnMatrix()))},applyTransformToObject:function(e,t){var n=fabric.util.qrDecompose(t),r=new fabric.Point(n.translateX,n.translateY);e.flipX=!1,e.flipY=!1,e.set("scaleX",n.scaleX),e.set("scaleY",n.scaleY),e.skewX=n.skewX,e.skewY=n.skewY,e.angle=n.angle,e.setPositionByOrigin(r,"center","center")},sizeAfterTransform:function(e,t,n){var r=e/2,o=t/2,i=[{x:-r,y:-o},{x:r,y:-o},{x:-r,y:o},{x:r,y:o}],a=fabric.util.calcDimensionsMatrix(n),s=fabric.util.makeBoundingBoxFromPoints(i,a);return{x:s.width,y:s.height}},mergeClipPaths:function(e,t){var n=e,r=t;n.inverted&&!r.inverted&&(n=t,r=e),fabric.util.applyTransformToObject(r,fabric.util.multiplyTransformMatrices(fabric.util.invertTransform(n.calcTransformMatrix()),r.calcTransformMatrix()));var o=n.inverted&&r.inverted;return o&&(n.inverted=r.inverted=!1),new fabric.Group([n],{clipPath:r,inverted:o})},hasStyleChanged:function(e,t,n){return n=n||!1,e.fill!==t.fill||e.stroke!==t.stroke||e.strokeWidth!==t.strokeWidth||e.fontSize!==t.fontSize||e.fontFamily!==t.fontFamily||e.fontWeight!==t.fontWeight||e.fontStyle!==t.fontStyle||e.deltaY!==t.deltaY||n&&(e.overline!==t.overline||e.underline!==t.underline||e.linethrough!==t.linethrough)},stylesToArray:function(e,t){for(var e=fabric.util.object.clone(e,!0),n=t.split("\n"),r=-1,o={},i=[],a=0;a<n.length;a++)if(e[a])for(var s=0;s<n[a].length;s++){r++;var u=e[a][s];if(u){var c=fabric.util.hasStyleChanged(o,u,!0);c?i.push({start:r,end:r+1,style:u}):i[i.length-1].end++}o=u||{}}else r+=n[a].length;return i},stylesFromArray:function(e,t){if(!Array.isArray(e))return e;for(var n=t.split("\n"),r=-1,o=0,i={},a=0;a<n.length;a++)for(var s=0;s<n[a].length;s++)r++,e[o]&&e[o].start<=r&&r<e[o].end&&(i[a]=i[a]||{},i[a][s]=Object.assign({},e[o].style),r===e[o].end-1&&o++);return i}}}("undefined"!=typeof exports?exports:this);!function(){fabric.util.createAccessors=function(e){var t,n,r,i,o,u=e.prototype;for(t=u.stateProperties.length;t--;)n=u.stateProperties[t],r=n.charAt(0).toUpperCase()+n.slice(1),i="set"+r,o="get"+r,u[o]||(u[o]=function(e){return new Function('return this.get("'+e+'")')}(n)),u[i]||(u[i]=function(e){return new Function("value",'return this.set("'+e+'", value)')}(n))}}("undefined"!=typeof exports?exports:this);!function(){function t(t,r,e,n,a,i,c,u,s,o,f){var h=fabric.util.cos(t),l=fabric.util.sin(t),b=fabric.util.cos(r),v=fabric.util.sin(r),y=e*a*b-n*i*v+c,p=n*a*b+e*i*v+u,g=o+s*(-e*a*l-n*i*h),x=f+s*(-n*a*l+e*i*h),m=y+s*(e*a*v+n*i*b),d=p+s*(n*a*v-e*i*b);return["C",g,x,m,d,y,p]}function r(r,n,a,i,c,u,s){var o=Math.PI,f=s*o/180,h=fabric.util.sin(f),l=fabric.util.cos(f),b=0,v=0;a=Math.abs(a),i=Math.abs(i);var y=-l*r*.5-h*n*.5,p=-l*n*.5+h*r*.5,g=a*a,x=i*i,m=p*p,d=y*y,M=g*x-g*m-x*d,P=0;if(0>M){var C=Math.sqrt(1-M/(g*x));a*=C,i*=C}else P=(c===u?-1:1)*Math.sqrt(M/(g*m+x*d));var w=P*a*p/i,k=-P*i*y/a,L=l*w-h*k+.5*r,q=h*w+l*k+.5*n,F=e(1,0,(y-w)/a,(p-k)/i),O=e((y-w)/a,(p-k)/i,(-y-w)/a,(-p-k)/i);0===u&&O>0?O-=2*o:1===u&&0>O&&(O+=2*o);for(var Q=Math.ceil(Math.abs(O/o*2)),A=[],z=O/Q,S=8/3*Math.sin(z/4)*Math.sin(z/4)/Math.sin(z/2),j=F+z,B=0;Q>B;B++)A[B]=t(F,j,l,h,a,i,L,q,S,b,v),b=A[B][5],v=A[B][6],F=j,j+=z;return A}function e(t,r,e,n){var a=Math.atan2(r,t),i=Math.atan2(n,e);return i>=a?i-a:2*Math.PI-(a-i)}function n(t,r,e,n,a,i,c,u){var s;if(fabric.cachesBoundsOfCurve&&(s=k.call(arguments),fabric.boundsOfCurveCache[s]))return fabric.boundsOfCurveCache[s];var o,f,h,l,b,v,y,p,g=Math.sqrt,x=Math.min,m=Math.max,d=Math.abs,M=[],P=[[],[]];f=6*t-12*e+6*a,o=-3*t+9*e-9*a+3*c,h=3*e-3*t;for(var C=0;2>C;++C)if(C>0&&(f=6*r-12*n+6*i,o=-3*r+9*n-9*i+3*u,h=3*n-3*r),d(o)<1e-12){if(d(f)<1e-12)continue;l=-h/f,l>0&&1>l&&M.push(l)}else y=f*f-4*h*o,0>y||(p=g(y),b=(-f+p)/(2*o),b>0&&1>b&&M.push(b),v=(-f-p)/(2*o),v>0&&1>v&&M.push(v));for(var w,L,q,F=M.length,O=F;F--;)l=M[F],q=1-l,w=q*q*q*t+3*q*q*l*e+3*q*l*l*a+l*l*l*c,P[0][F]=w,L=q*q*q*r+3*q*q*l*n+3*q*l*l*i+l*l*l*u,P[1][F]=L;P[0][O]=t,P[1][O]=r,P[0][O+1]=c,P[1][O+1]=u;var Q=[{x:x.apply(null,P[0]),y:x.apply(null,P[1])},{x:m.apply(null,P[0]),y:m.apply(null,P[1])}];return fabric.cachesBoundsOfCurve&&(fabric.boundsOfCurveCache[s]=Q),Q}function a(t,e,n){for(var a=n[1],i=n[2],c=n[3],u=n[4],s=n[5],o=n[6],f=n[7],h=r(o-t,f-e,a,i,u,s,c),l=0,b=h.length;b>l;l++)h[l][1]+=t,h[l][2]+=e,h[l][3]+=t,h[l][4]+=e,h[l][5]+=t,h[l][6]+=e;return h}function i(t){var r,e,n,i,c,u,s=0,o=0,f=t.length,h=0,l=0,b=[];for(e=0;f>e;++e){switch(n=!1,r=t[e].slice(0),r[0]){case"l":r[0]="L",r[1]+=s,r[2]+=o;case"L":s=r[1],o=r[2];break;case"h":r[1]+=s;case"H":r[0]="L",r[2]=o,s=r[1];break;case"v":r[1]+=o;case"V":r[0]="L",o=r[1],r[1]=s,r[2]=o;break;case"m":r[0]="M",r[1]+=s,r[2]+=o;case"M":s=r[1],o=r[2],h=r[1],l=r[2];break;case"c":r[0]="C",r[1]+=s,r[2]+=o,r[3]+=s,r[4]+=o,r[5]+=s,r[6]+=o;case"C":c=r[3],u=r[4],s=r[5],o=r[6];break;case"s":r[0]="S",r[1]+=s,r[2]+=o,r[3]+=s,r[4]+=o;case"S":"C"===i?(c=2*s-c,u=2*o-u):(c=s,u=o),s=r[3],o=r[4],r[0]="C",r[5]=r[3],r[6]=r[4],r[3]=r[1],r[4]=r[2],r[1]=c,r[2]=u,c=r[3],u=r[4];break;case"q":r[0]="Q",r[1]+=s,r[2]+=o,r[3]+=s,r[4]+=o;case"Q":c=r[1],u=r[2],s=r[3],o=r[4];break;case"t":r[0]="T",r[1]+=s,r[2]+=o;case"T":"Q"===i?(c=2*s-c,u=2*o-u):(c=s,u=o),r[0]="Q",s=r[1],o=r[2],r[1]=c,r[2]=u,r[3]=s,r[4]=o;break;case"a":r[0]="A",r[6]+=s,r[7]+=o;case"A":n=!0,b=b.concat(a(s,o,r)),s=r[6],o=r[7];break;case"z":case"Z":s=h,o=l}n||b.push(r),i=r[0]}return b}function c(t,r,e,n){return Math.sqrt((e-t)*(e-t)+(n-r)*(n-r))}function u(t){return t*t*t}function s(t){return 3*t*t*(1-t)}function o(t){return 3*t*(1-t)*(1-t)}function f(t){return(1-t)*(1-t)*(1-t)}function h(t,r,e,n,a,i,c,h){return function(l){var b=u(l),v=s(l),y=o(l),p=f(l);return{x:c*b+a*v+e*y+t*p,y:h*b+i*v+n*y+r*p}}}function l(t,r,e,n,a,i,c,u){return function(s){var o=1-s,f=3*o*o*(e-t)+6*o*s*(a-e)+3*s*s*(c-a),h=3*o*o*(n-r)+6*o*s*(i-n)+3*s*s*(u-i);return Math.atan2(h,f)}}function b(t){return t*t}function v(t){return 2*t*(1-t)}function y(t){return(1-t)*(1-t)}function p(t,r,e,n,a,i){return function(c){var u=b(c),s=v(c),o=y(c);return{x:a*u+e*s+t*o,y:i*u+n*s+r*o}}}function g(t,r,e,n,a,i){return function(c){var u=1-c,s=2*u*(e-t)+2*c*(a-e),o=2*u*(n-r)+2*c*(i-n);return Math.atan2(o,s)}}function x(t,r,e){var n,a,i={x:r,y:e},u=0;for(a=1;100>=a;a+=1)n=t(a/100),u+=c(i.x,i.y,n.x,n.y),i=n;return u}function m(t,r){for(var e,n,a,i=0,u=0,s=t.iterator,o={x:t.x,y:t.y},f=.01,h=t.angleFinder;r>u&&f>1e-4;)e=s(i),a=i,n=c(o.x,o.y,e.x,e.y),n+u>r?(i-=f,f/=2):(o=e,i+=f,u+=n);return e.angle=h(a),e}function d(t){for(var r,e,n,a,i=0,u=t.length,s=0,o=0,f=0,b=0,v=[],y=0;u>y;y++){switch(r=t[y],n={x:s,y:o,command:r[0]},r[0]){case"M":n.length=0,f=s=r[1],b=o=r[2];break;case"L":n.length=c(s,o,r[1],r[2]),s=r[1],o=r[2];break;case"C":e=h(s,o,r[1],r[2],r[3],r[4],r[5],r[6]),a=l(s,o,r[1],r[2],r[3],r[4],r[5],r[6]),n.iterator=e,n.angleFinder=a,n.length=x(e,s,o),s=r[5],o=r[6];break;case"Q":e=p(s,o,r[1],r[2],r[3],r[4]),a=g(s,o,r[1],r[2],r[3],r[4]),n.iterator=e,n.angleFinder=a,n.length=x(e,s,o),s=r[3],o=r[4];break;case"Z":case"z":n.destX=f,n.destY=b,n.length=c(s,o,f,b),s=f,o=b}i+=n.length,v.push(n)}return v.push({length:i,x:s,y:o}),v}function M(t,r,e){e||(e=d(t));for(var n=0;r-e[n].length>0&&n<e.length-2;)r-=e[n].length,n++;var a,i=e[n],c=r/i.length,u=i.command,s=t[n];switch(u){case"M":return{x:i.x,y:i.y,angle:0};case"Z":case"z":return a=new fabric.Point(i.x,i.y).lerp(new fabric.Point(i.destX,i.destY),c),a.angle=Math.atan2(i.destY-i.y,i.destX-i.x),a;case"L":return a=new fabric.Point(i.x,i.y).lerp(new fabric.Point(s[1],s[2]),c),a.angle=Math.atan2(s[2]-i.y,s[1]-i.x),a;case"C":return m(i,r);case"Q":return m(i,r)}}function P(t){var r,e,n,a,i,c=[],u=[],s=fabric.rePathCommand,o="[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?\\s*",f="("+o+")"+fabric.commaWsp,h="([01])"+fabric.commaWsp+"?",l=f+"?"+f+"?"+f+h+h+f+"?("+o+")",b=new RegExp(l,"g");if(!t||!t.match)return c;i=t.match(/[mzlhvcsqta][^mzlhvcsqta]*/gi);for(var v,y=0,p=i.length;p>y;y++){r=i[y],a=r.slice(1).trim(),u.length=0;var g=r.charAt(0);if(v=[g],"a"===g.toLowerCase())for(var x;x=b.exec(a);)for(var m=1;m<x.length;m++)u.push(x[m]);else for(;n=s.exec(a);)u.push(n[0]);for(var m=0,d=u.length;d>m;m++)e=parseFloat(u[m]),isNaN(e)||v.push(e);var M=L[g.toLowerCase()],P=q[g]||g;if(v.length-1>M)for(var C=1,w=v.length;w>C;C+=M)c.push([g].concat(v.slice(C,C+M))),g=P;else c.push(v)}return c}function C(t,r){var e,n=[],a=new fabric.Point(t[0].x,t[0].y),i=new fabric.Point(t[1].x,t[1].y),c=t.length,u=1,s=0,o=c>2;for(r=r||0,o&&(u=t[2].x<i.x?-1:t[2].x===i.x?0:1,s=t[2].y<i.y?-1:t[2].y===i.y?0:1),n.push(["M",a.x-u*r,a.y-s*r]),e=1;c>e;e++){if(!a.eq(i)){var f=a.midPointFrom(i);n.push(["Q",a.x,a.y,f.x,f.y])}a=t[e],e+1<t.length&&(i=t[e+1])}return o&&(u=a.x>t[e-2].x?1:a.x===t[e-2].x?0:-1,s=a.y>t[e-2].y?1:a.y===t[e-2].y?0:-1),n.push(["L",a.x+u*r,a.y+s*r]),n}function w(t,r,e){return e&&(r=fabric.util.multiplyTransformMatrices(r,[1,0,0,1,-e.x,-e.y])),t.map(function(t){for(var e=t.slice(0),n={},a=1;a<t.length-1;a+=2)n.x=t[a],n.y=t[a+1],n=fabric.util.transformPoint(n,r),e[a]=n.x,e[a+1]=n.y;return e})}var k=Array.prototype.join,L={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},q={m:"l",M:"L"};fabric.util.joinPath=function(t){return t.map(function(t){return t.join(" ")}).join(" ")},fabric.util.parsePath=P,fabric.util.makePathSimpler=i,fabric.util.getSmoothPathFromPoints=C,fabric.util.getPathSegmentsInfo=d,fabric.util.getBoundsOfCurve=n,fabric.util.getPointOnPath=M,fabric.util.transformPath=w}();!function(){function e(e,t){for(var n=i.call(arguments,2),r=[],o=0,a=e.length;a>o;o++)r[o]=n.length?e[o][t].apply(e[o],n):e[o][t].call(e[o]);return r}function t(e,t){return o(e,t,function(e,t){return e>=t})}function n(e,t){return o(e,t,function(e,t){return t>e})}function r(e,t){for(var n=e.length;n--;)e[n]=t;return e}function o(e,t,n){if(e&&0!==e.length){var r=e.length-1,o=t?e[r][t]:e[r];if(t)for(;r--;)n(e[r][t],o)&&(o=e[r][t]);else for(;r--;)n(e[r],o)&&(o=e[r]);return o}}var i=Array.prototype.slice;fabric.util.array={fill:r,invoke:e,min:n,max:t}}();!function(){function e(t,n,r){if(r)if(!fabric.isLikelyNode&&n instanceof Element)t=n;else if(n instanceof Array){t=[];for(var o=0,i=n.length;i>o;o++)t[o]=e({},n[o],r)}else if(n&&"object"==typeof n)for(var a in n)"canvas"===a||"group"===a?t[a]=null:n.hasOwnProperty(a)&&(t[a]=e({},n[a],r));else t=n;else for(var a in n)t[a]=n[a];return t}function t(t,n){return e({},t,n)}fabric.util.object={extend:e,clone:t},fabric.util.object.extend(fabric.util,fabric.Observable)}();!function(){function e(e){return e.replace(/-+(.)?/g,function(e,t){return t?t.toUpperCase():""})}function t(e,t){return e.charAt(0).toUpperCase()+(t?e.slice(1):e.slice(1).toLowerCase())}function n(e){return e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function r(e){var t,n=0,r=[];for(n=0,t;n<e.length;n++)(t=o(e,n))!==!1&&r.push(t);return r}function o(e,t){var n=e.charCodeAt(t);if(isNaN(n))return"";if(55296>n||n>57343)return e.charAt(t);if(n>=55296&&56319>=n){if(e.length<=t+1)throw"High surrogate without following low surrogate";var r=e.charCodeAt(t+1);if(56320>r||r>57343)throw"High surrogate without following low surrogate";return e.charAt(t)+e.charAt(t+1)}if(0===t)throw"Low surrogate without preceding high surrogate";var o=e.charCodeAt(t-1);if(55296>o||o>56319)throw"Low surrogate without preceding high surrogate";return!1}fabric.util.string={camelize:e,capitalize:t,escapeXml:n,graphemeSplit:r}}();!function(){function e(){}function t(e){for(var t=null,n=this;n.constructor.superclass;){var o=n.constructor.superclass.prototype[e];if(n[e]!==o){t=o;break}n=n.constructor.superclass.prototype}return t?arguments.length>1?t.apply(this,r.call(arguments,1)):t.call(this):console.log("tried to callSuper "+e+", method not found in prototype chain",this)}function n(){function n(){this.initialize.apply(this,arguments)}var i=null,s=r.call(arguments,0);"function"==typeof s[0]&&(i=s.shift()),n.superclass=i,n.subclasses=[],i&&(e.prototype=i.prototype,n.prototype=new e,i.subclasses.push(n));for(var u=0,c=s.length;c>u;u++)a(n,s[u],i);return n.prototype.initialize||(n.prototype.initialize=o),n.prototype.constructor=n,n.prototype.callSuper=t,n}var r=Array.prototype.slice,o=function(){},i=function(){for(var e in{toString:1})if("toString"===e)return!1;return!0}(),a=function(e,t,n){for(var r in t)e.prototype[r]=r in e.prototype&&"function"==typeof e.prototype[r]&&(t[r]+"").indexOf("callSuper")>-1?function(e){return function(){var r=this.constructor.superclass;this.constructor.superclass=n;var o=t[e].apply(this,arguments);return this.constructor.superclass=r,"initialize"!==e?o:void 0}}(r):t[r],i&&(t.toString!==Object.prototype.toString&&(e.prototype.toString=t.toString),t.valueOf!==Object.prototype.valueOf&&(e.prototype.valueOf=t.valueOf))};fabric.util.createClass=n}();!function(){function t(t){var e=t.changedTouches;return e&&e[0]?e[0]:t}var e=!!fabric.document.createElement("div").attachEvent,r=["touchstart","touchmove","touchend"];fabric.util.addListener=function(t,r,i,n){t&&t.addEventListener(r,i,e?!1:n)},fabric.util.removeListener=function(t,r,i,n){t&&t.removeEventListener(r,i,e?!1:n)},fabric.util.getPointer=function(e){var r=e.target,i=fabric.util.getScrollLeftTop(r),n=t(e);return{x:n.clientX+i.left,y:n.clientY+i.top}},fabric.util.isTouchEvent=function(t){return r.indexOf(t.type)>-1||"touch"===t.pointerType}}();!function(){function t(t,e){var i=t.style;if(!i)return t;if("string"==typeof e)return t.style.cssText+=";"+e,e.indexOf("opacity")>-1?s(t,e.match(/opacity:\s*(\d?\.?\d*)/)[1]):t;for(var r in e)if("opacity"===r)s(t,e[r]);else{var n="float"===r||"cssFloat"===r?"undefined"==typeof i.styleFloat?"cssFloat":"styleFloat":r;i.setProperty(n,e[r])}return t}var e=fabric.document.createElement("div"),i="string"==typeof e.style.opacity,r="string"==typeof e.style.filter,n=/alpha\s*\(\s*opacity\s*=\s*([^\)]+)\)/,s=function(t){return t};i?s=function(t,e){return t.style.opacity=e,t}:r&&(s=function(t,e){var i=t.style;return t.currentStyle&&!t.currentStyle.hasLayout&&(i.zoom=1),n.test(i.filter)?(e=e>=.9999?"":"alpha(opacity="+100*e+")",i.filter=i.filter.replace(n,e)):i.filter+=" alpha(opacity="+100*e+")",t}),fabric.util.setStyle=t}();!function(){function e(e){return"string"==typeof e?fabric.document.getElementById(e):e}function t(e,t){var n=fabric.document.createElement(e);for(var r in t)"class"===r?n.className=t[r]:"for"===r?n.htmlFor=t[r]:n.setAttribute(r,t[r]);return n}function n(e,t){e&&-1===(" "+e.className+" ").indexOf(" "+t+" ")&&(e.className+=(e.className?" ":"")+t)}function r(e,n,r){return"string"==typeof n&&(n=t(n,r)),e.parentNode&&e.parentNode.replaceChild(n,e),n.appendChild(e),n}function i(e){for(var t=0,n=0,r=fabric.document.documentElement,i=fabric.document.body||{scrollLeft:0,scrollTop:0};e&&(e.parentNode||e.host)&&(e=e.parentNode||e.host,e===fabric.document?(t=i.scrollLeft||r.scrollLeft||0,n=i.scrollTop||r.scrollTop||0):(t+=e.scrollLeft||0,n+=e.scrollTop||0),1!==e.nodeType||"fixed"!==e.style.position););return{left:t,top:n}}function o(e){var t,n,r=e&&e.ownerDocument,o={left:0,top:0},s={left:0,top:0},a={borderLeftWidth:"left",borderTopWidth:"top",paddingLeft:"left",paddingTop:"top"};if(!r)return s;for(var c in a)s[a[c]]+=parseInt(d(e,c),10)||0;return t=r.documentElement,"undefined"!=typeof e.getBoundingClientRect&&(o=e.getBoundingClientRect()),n=i(e),{left:o.left+n.left-(t.clientLeft||0)+s.left,top:o.top+n.top-(t.clientTop||0)+s.top}}function s(e){var t=fabric.jsdomImplForWrapper(e);return t._canvas||t._image}function a(e){if(fabric.isLikelyNode){var t=fabric.jsdomImplForWrapper(e);t&&(t._image=null,t._canvas=null,t._currentSrc=null,t._attributes=null,t._classList=null)}}function c(e,t){e.imageSmoothingEnabled=e.imageSmoothingEnabled||e.webkitImageSmoothingEnabled||e.mozImageSmoothingEnabled||e.msImageSmoothingEnabled||e.oImageSmoothingEnabled,e.imageSmoothingEnabled=t}var u,l=Array.prototype.slice,f=function(e){return l.call(e,0)};try{u=f(fabric.document.childNodes)instanceof Array}catch(h){}u||(f=function(e){for(var t=new Array(e.length),n=e.length;n--;)t[n]=e[n];return t});var d;d=fabric.document.defaultView&&fabric.document.defaultView.getComputedStyle?function(e,t){var n=fabric.document.defaultView.getComputedStyle(e,null);return n?n[t]:void 0}:function(e,t){var n=e.style[t];return!n&&e.currentStyle&&(n=e.currentStyle[t]),n},function(){function e(e){return"undefined"!=typeof e.onselectstart&&(e.onselectstart=fabric.util.falseFunction),r?e.style[r]="none":"string"==typeof e.unselectable&&(e.unselectable="on"),e}function t(e){return"undefined"!=typeof e.onselectstart&&(e.onselectstart=null),r?e.style[r]="":"string"==typeof e.unselectable&&(e.unselectable=""),e}var n=fabric.document.documentElement.style,r="userSelect"in n?"userSelect":"MozUserSelect"in n?"MozUserSelect":"WebkitUserSelect"in n?"WebkitUserSelect":"KhtmlUserSelect"in n?"KhtmlUserSelect":"";fabric.util.makeElementUnselectable=e,fabric.util.makeElementSelectable=t}(),fabric.util.setImageSmoothing=c,fabric.util.getById=e,fabric.util.toArray=f,fabric.util.addClass=n,fabric.util.makeElement=t,fabric.util.wrapElement=r,fabric.util.getScrollLeftTop=i,fabric.util.getElementOffset=o,fabric.util.getNodeCanvas=s,fabric.util.cleanUpJsdomNode=a}();!function(){function e(e,t){return e+(/\?/.test(e)?"&":"?")+t}function t(){}function r(r,n){n||(n={});var i=n.method?n.method.toUpperCase():"GET",o=n.onComplete||function(){},s=new fabric.window.XMLHttpRequest,a=n.body||n.parameters;return s.onreadystatechange=function(){4===s.readyState&&(o(s),s.onreadystatechange=t)},"GET"===i&&(a=null,"string"==typeof n.parameters&&(r=e(r,n.parameters))),s.open(i,r,!0),("POST"===i||"PUT"===i)&&s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),s.send(a),s}fabric.util.request=r}();fabric.log=console.log,fabric.warn=console.warn;!function(){function e(){return!1}function t(e,t,r,n){return-r*Math.cos(e/n*(Math.PI/2))+r+t}function r(r){r||(r={});var i,s=!1,c=function(){var e=fabric.runningAnimations.indexOf(i);return e>-1&&fabric.runningAnimations.splice(e,1)[0]};return i=o(a(r),{cancel:function(){return s=!0,c()},currentValue:"startValue"in r?r.startValue:0,completionRate:0,durationRate:0}),fabric.runningAnimations.push(i),n(function(o){var a,l=o||+new Date,u=r.duration||500,f=l+u,h=r.onChange||e,d=r.abort||e,v=r.onComplete||e,g=r.easing||t,p="startValue"in r?r.startValue.length>0:!1,m="startValue"in r?r.startValue:0,b="endValue"in r?r.endValue:100,y=r.byValue||(p?m.map(function(e,t){return b[t]-m[t]}):b-m);r.onStart&&r.onStart(),function x(e){a=e||+new Date;var t=a>f?u:a-l,r=t/u,o=p?m.map(function(e,r){return g(t,m[r],y[r],u)}):g(t,m,y,u),w=Math.abs(p?(o[0]-m[0])/y[0]:(o-m)/y);return i.currentValue=p?o.slice():o,i.completionRate=w,i.durationRate=r,s?void 0:d(o,w,r)?void c():a>f?(i.currentValue=p?b.slice():b,i.completionRate=1,i.durationRate=1,h(p?b.slice():b,1,1),v(b,1,1),void c()):(h(o,w,r),void n(x))}(l)}),i.cancel}function n(){return c.apply(fabric.window,arguments)}function i(){return l.apply(fabric.window,arguments)}var o=fabric.util.object.extend,a=fabric.util.object.clone,s=[];fabric.util.object.extend(s,{cancelAll:function(){var e=this.splice(0);return e.forEach(function(e){e.cancel()}),e},cancelByCanvas:function(e){if(!e)return[];var t=this.filter(function(t){return"object"==typeof t.target&&t.target.canvas===e});return t.forEach(function(e){e.cancel()}),t},cancelByTarget:function(e){var t=this.findAnimationsByTarget(e);return t.forEach(function(e){e.cancel()}),t},findAnimationIndex:function(e){return this.indexOf(this.findAnimation(e))},findAnimation:function(e){return this.find(function(t){return t.cancel===e})},findAnimationsByTarget:function(e){return e?this.filter(function(t){return t.target===e}):[]}});var c=fabric.window.requestAnimationFrame||fabric.window.webkitRequestAnimationFrame||fabric.window.mozRequestAnimationFrame||fabric.window.oRequestAnimationFrame||fabric.window.msRequestAnimationFrame||function(e){return fabric.window.setTimeout(e,1e3/60)},l=fabric.window.cancelAnimationFrame||fabric.window.clearTimeout;fabric.util.animate=r,fabric.util.requestAnimFrame=n,fabric.util.cancelAnimFrame=i,fabric.runningAnimations=s}();!function(){function t(t,e,i){var r="rgba("+parseInt(t[0]+i*(e[0]-t[0]),10)+","+parseInt(t[1]+i*(e[1]-t[1]),10)+","+parseInt(t[2]+i*(e[2]-t[2]),10);return r+=","+(t&&e?parseFloat(t[3]+i*(e[3]-t[3])):1),r+=")"}function e(e,i,r,n){var s=new fabric.Color(e).getSource(),o=new fabric.Color(i).getSource(),a=n.onComplete,c=n.onChange;return n=n||{},fabric.util.animate(fabric.util.object.extend(n,{duration:r||500,startValue:s,endValue:o,byValue:o,easing:function(e,i,r,s){var o=n.colorEasing?n.colorEasing(e,s):1-Math.cos(e/s*(Math.PI/2));return t(i,r,o)},onComplete:function(e,i,r){return a?a(t(o,o,0),i,r):void 0},onChange:function(e,i,r){if(c){if(Array.isArray(e))return c(t(e,e,0),i,r);c(e,i,r)}}}))}fabric.util.animateColor=e}();!function(){function t(t,e,i,r){return t<Math.abs(e)?(t=e,r=i/4):r=0===e&&0===t?i/(2*Math.PI)*Math.asin(1):i/(2*Math.PI)*Math.asin(e/t),{a:t,c:e,p:i,s:r}}function e(t,e,i){return t.a*Math.pow(2,10*(e-=1))*Math.sin(2*(e*i-t.s)*Math.PI/t.p)}function i(t,e,i,r){return i*((t=t/r-1)*t*t+1)+e}function r(t,e,i,r){return t/=r/2,1>t?i/2*t*t*t+e:i/2*((t-=2)*t*t+2)+e}function n(t,e,i,r){return i*(t/=r)*t*t*t+e}function s(t,e,i,r){return-i*((t=t/r-1)*t*t*t-1)+e}function o(t,e,i,r){return t/=r/2,1>t?i/2*t*t*t*t+e:-i/2*((t-=2)*t*t*t-2)+e}function a(t,e,i,r){return i*(t/=r)*t*t*t*t+e}function c(t,e,i,r){return i*((t=t/r-1)*t*t*t*t+1)+e}function l(t,e,i,r){return t/=r/2,1>t?i/2*t*t*t*t*t+e:i/2*((t-=2)*t*t*t*t+2)+e}function h(t,e,i,r){return-i*Math.cos(t/r*(Math.PI/2))+i+e}function u(t,e,i,r){return i*Math.sin(t/r*(Math.PI/2))+e}function f(t,e,i,r){return-i/2*(Math.cos(Math.PI*t/r)-1)+e}function d(t,e,i,r){return 0===t?e:i*Math.pow(2,10*(t/r-1))+e}function g(t,e,i,r){return t===r?e+i:i*(-Math.pow(2,-10*t/r)+1)+e}function p(t,e,i,r){return 0===t?e:t===r?e+i:(t/=r/2,1>t?i/2*Math.pow(2,10*(t-1))+e:i/2*(-Math.pow(2,-10*--t)+2)+e)}function v(t,e,i,r){return-i*(Math.sqrt(1-(t/=r)*t)-1)+e}function m(t,e,i,r){return i*Math.sqrt(1-(t=t/r-1)*t)+e}function b(t,e,i,r){return t/=r/2,1>t?-i/2*(Math.sqrt(1-t*t)-1)+e:i/2*(Math.sqrt(1-(t-=2)*t)+1)+e}function y(i,r,n,s){var o=1.70158,a=0,c=n;if(0===i)return r;if(i/=s,1===i)return r+n;a||(a=.3*s);var l=t(c,n,a,o);return-e(l,i,s)+r}function x(e,i,r,n){var s=1.70158,o=0,a=r;if(0===e)return i;if(e/=n,1===e)return i+r;o||(o=.3*n);var c=t(a,r,o,s);return c.a*Math.pow(2,-10*e)*Math.sin(2*(e*n-c.s)*Math.PI/c.p)+c.c+i}function C(i,r,n,s){var o=1.70158,a=0,c=n;if(0===i)return r;if(i/=s/2,2===i)return r+n;a||(a=.3*s*1.5);var l=t(c,n,a,o);return 1>i?-.5*e(l,i,s)+r:l.a*Math.pow(2,-10*(i-=1))*Math.sin(2*(i*s-l.s)*Math.PI/l.p)*.5+l.c+r}function _(t,e,i,r,n){return void 0===n&&(n=1.70158),i*(t/=r)*t*((n+1)*t-n)+e}function w(t,e,i,r,n){return void 0===n&&(n=1.70158),i*((t=t/r-1)*t*((n+1)*t+n)+1)+e}function S(t,e,i,r,n){return void 0===n&&(n=1.70158),t/=r/2,1>t?i/2*t*t*(((n*=1.525)+1)*t-n)+e:i/2*((t-=2)*t*(((n*=1.525)+1)*t+n)+2)+e}function T(t,e,i,r){return i-O(r-t,0,i,r)+e}function O(t,e,i,r){return(t/=r)<1/2.75?7.5625*i*t*t+e:2/2.75>t?i*(7.5625*(t-=1.5/2.75)*t+.75)+e:2.5/2.75>t?i*(7.5625*(t-=2.25/2.75)*t+.9375)+e:i*(7.5625*(t-=2.625/2.75)*t+.984375)+e}function j(t,e,i,r){return r/2>t?.5*T(2*t,0,i,r)+e:.5*O(2*t-r,0,i,r)+.5*i+e}fabric.util.ease={easeInQuad:function(t,e,i,r){return i*(t/=r)*t+e},easeOutQuad:function(t,e,i,r){return-i*(t/=r)*(t-2)+e},easeInOutQuad:function(t,e,i,r){return t/=r/2,1>t?i/2*t*t+e:-i/2*(--t*(t-2)-1)+e},easeInCubic:function(t,e,i,r){return i*(t/=r)*t*t+e},easeOutCubic:i,easeInOutCubic:r,easeInQuart:n,easeOutQuart:s,easeInOutQuart:o,easeInQuint:a,easeOutQuint:c,easeInOutQuint:l,easeInSine:h,easeOutSine:u,easeInOutSine:f,easeInExpo:d,easeOutExpo:g,easeInOutExpo:p,easeInCirc:v,easeOutCirc:m,easeInOutCirc:b,easeInElastic:y,easeOutElastic:x,easeInOutElastic:C,easeInBack:_,easeOutBack:w,easeInOutBack:S,easeInBounce:T,easeOutBounce:O,easeInOutBounce:j}}();!function(e){"use strict";function t(e){return e in O?O[e]:e}function r(e,t,r,n){var i,o=Array.isArray(t);if("fill"!==e&&"stroke"!==e||"none"!==t){if("strokeUniform"===e)return"non-scaling-stroke"===t;if("strokeDashArray"===e)t="none"===t?null:t.replace(/,/g," ").split(/\s+/).map(parseFloat);else if("transformMatrix"===e)t=r&&r.transformMatrix?C(r.transformMatrix,m.parseTransformAttribute(t)):m.parseTransformAttribute(t);else if("visible"===e)t="none"!==t&&"hidden"!==t,r&&r.visible===!1&&(t=!1);else if("opacity"===e)t=parseFloat(t),r&&"undefined"!=typeof r.opacity&&(t*=r.opacity);else if("textAnchor"===e)t="start"===t?"left":"end"===t?"right":"center";else if("charSpacing"===e)i=w(t,n)/n*1e3;else if("paintFirst"===e){var s=t.indexOf("fill"),a=t.indexOf("stroke"),t="fill";s>-1&&a>-1&&s>a?t="stroke":-1===s&&a>-1&&(t="stroke")}else{if("href"===e||"xlink:href"===e||"font"===e)return t;if("imageSmoothing"===e)return"optimizeQuality"===t;i=o?t.map(w):w(t,n)}}else t="";return!o&&isNaN(i)?t:i}function n(e){return new RegExp("^("+e.join("|")+")\\b","i")}function i(e){for(var t in E)if("undefined"!=typeof e[E[t]]&&""!==e[t]){if("undefined"==typeof e[t]){if(!m.Object.prototype[t])continue;e[t]=m.Object.prototype[t]}if(0!==e[t].indexOf("url(")){var r=new m.Color(e[t]);e[t]=r.setAlpha(x(r.getAlpha()*e[E[t]],2)).toRgba()}}return e}function o(e,t){var r,n,i,o,s=[];for(i=0,o=t.length;o>i;i++)r=t[i],n=e.getElementsByTagName(r),s=s.concat(Array.prototype.slice.call(n));return s}function s(e,t){var r,n;e.replace(/;\s*$/,"").split(";").forEach(function(e){var i=e.split(":");r=i[0].trim().toLowerCase(),n=i[1].trim(),t[r]=n})}function a(e,t){var r,n;for(var i in e)"undefined"!=typeof e[i]&&(r=i.toLowerCase(),n=e[i],t[r]=n)}function c(e,t){var r={};for(var n in m.cssRules[t])if(l(e,n.split(" ")))for(var i in m.cssRules[t][n])r[i]=m.cssRules[t][n][i];return r}function l(e,t){var r,n=!0;return r=f(e,t.pop()),r&&t.length&&(n=u(e,t)),r&&n&&0===t.length}function u(e,t){for(var r,n=!0;e.parentNode&&1===e.parentNode.nodeType&&t.length;)n&&(r=t.pop()),e=e.parentNode,n=f(e,r);return 0===t.length}function f(e,t){var r,n,i=e.nodeName,o=e.getAttribute("class"),s=e.getAttribute("id");if(r=new RegExp("^"+i,"i"),t=t.replace(r,""),s&&t.length&&(r=new RegExp("#"+s+"(?![a-zA-Z\\-]+)","i"),t=t.replace(r,"")),o&&t.length)for(o=o.split(" "),n=o.length;n--;)r=new RegExp("\\."+o[n]+"(?![a-zA-Z\\-]+)","i"),t=t.replace(r,"");return 0===t.length}function h(e,t){var r;if(e.getElementById&&(r=e.getElementById(t)),r)return r;var n,i,o,s=e.getElementsByTagName("*");for(i=0,o=s.length;o>i;i++)if(n=s[i],t===n.getAttribute("id"))return n}function d(e){for(var t=o(e,["use","svg:use"]),r=0;t.length&&r<t.length;){var n=t[r],i=n.getAttribute("xlink:href")||n.getAttribute("href");if(null===i)return;var s,a,c,l,u,f=i.slice(1),d=n.getAttribute("x")||0,g=n.getAttribute("y")||0,p=h(e,f).cloneNode(!0),b=(p.getAttribute("transform")||"")+" translate("+d+", "+g+")",y=t.length,x=m.svgNS;if(v(p),/^svg$/i.test(p.nodeName)){var w=p.ownerDocument.createElementNS(x,"g");for(c=0,l=p.attributes,u=l.length;u>c;c++)a=l.item(c),w.setAttributeNS(x,a.nodeName,a.nodeValue);for(;p.firstChild;)w.appendChild(p.firstChild);p=w}for(c=0,l=n.attributes,u=l.length;u>c;c++)a=l.item(c),"x"!==a.nodeName&&"y"!==a.nodeName&&"xlink:href"!==a.nodeName&&"href"!==a.nodeName&&("transform"===a.nodeName?b=a.nodeValue+" "+b:p.setAttribute(a.nodeName,a.nodeValue));p.setAttribute("transform",b),p.setAttribute("instantiated_by_use","1"),p.removeAttribute("id"),s=n.parentNode,s.replaceChild(p,n),t.length===y&&r++}}function v(e){if(!m.svgViewBoxElementsRegEx.test(e.nodeName))return{};var t,r,n,i,o=e.getAttribute("viewBox"),s=1,a=1,c=0,l=0,u=e.getAttribute("width"),f=e.getAttribute("height"),h=e.getAttribute("x")||0,d=e.getAttribute("y")||0,v=e.getAttribute("preserveAspectRatio")||"",g=!o||!(o=o.match(P)),p=!u||!f||"100%"===u||"100%"===f,b=g&&p,y={},x="",C=0,j=0;if(y.width=0,y.height=0,y.toBeParsed=b,g&&(h||d)&&e.parentNode&&"#document"!==e.parentNode.nodeName&&(x=" translate("+w(h)+" "+w(d)+") ",n=(e.getAttribute("transform")||"")+x,e.setAttribute("transform",n),e.removeAttribute("x"),e.removeAttribute("y")),b)return y;if(g)return y.width=w(u),y.height=w(f),y;if(c=-parseFloat(o[1]),l=-parseFloat(o[2]),t=parseFloat(o[3]),r=parseFloat(o[4]),y.minX=c,y.minY=l,y.viewBoxWidth=t,y.viewBoxHeight=r,p?(y.width=t,y.height=r):(y.width=w(u),y.height=w(f),s=y.width/t,a=y.height/r),v=m.util.parsePreserveAspectRatioAttribute(v),"none"!==v.alignX&&("meet"===v.meetOrSlice&&(a=s=s>a?a:s),"slice"===v.meetOrSlice&&(a=s=s>a?s:a),C=y.width-t*s,j=y.height-r*s,"Mid"===v.alignX&&(C/=2),"Mid"===v.alignY&&(j/=2),"Min"===v.alignX&&(C=0),"Min"===v.alignY&&(j=0)),1===s&&1===a&&0===c&&0===l&&0===h&&0===d)return y;if((h||d)&&"#document"!==e.parentNode.nodeName&&(x=" translate("+w(h)+" "+w(d)+") "),n=x+" matrix("+s+" 0 0 "+a+" "+(c*s+C)+" "+(l*a+j)+") ","svg"===e.nodeName){for(i=e.ownerDocument.createElementNS(m.svgNS,"g");e.firstChild;)i.appendChild(e.firstChild);e.appendChild(i)}else i=e,i.removeAttribute("x"),i.removeAttribute("y"),n=i.getAttribute("transform")+n;return i.setAttribute("transform",n),y}function g(e,t){for(;e&&(e=e.parentNode);)if(e.nodeName&&t.test(e.nodeName.replace("svg:",""))&&!e.getAttribute("instantiated_by_use"))return!0;return!1}function p(e,t){var r=["gradientTransform","x1","x2","y1","y2","gradientUnits","cx","cy","r","fx","fy"],n="xlink:href",i=t.getAttribute(n).slice(1),o=h(e,i);if(o&&o.getAttribute(n)&&p(e,o),r.forEach(function(e){o&&!t.hasAttribute(e)&&o.hasAttribute(e)&&t.setAttribute(e,o.getAttribute(e))}),!t.children.length)for(var s=o.cloneNode(!0);s.firstChild;)t.appendChild(s.firstChild);t.removeAttribute(n)}var m=e.fabric||(e.fabric={}),b=m.util.object.extend,y=m.util.object.clone,x=m.util.toFixed,w=m.util.parseUnit,C=m.util.multiplyTransformMatrices,j=["path","circle","polygon","polyline","ellipse","rect","line","image","text"],_=["symbol","image","marker","pattern","view","svg"],S=["pattern","defs","symbol","metadata","clipPath","mask","desc"],k=["symbol","g","a","svg","clipPath","defs"],O={cx:"left",x:"left",r:"radius",cy:"top",y:"top",display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},E={stroke:"strokeOpacity",fill:"fillOpacity"},F="font-size",A="clip-path";m.svgValidTagNamesRegEx=n(j),m.svgViewBoxElementsRegEx=n(_),m.svgInvalidAncestorsRegEx=n(S),m.svgValidParentsRegEx=n(k),m.cssRules={},m.gradientDefs={},m.clipPaths={},m.parseTransformAttribute=function(){function e(e,t){var r=m.util.cos(t[0]),n=m.util.sin(t[0]),i=0,o=0;3===t.length&&(i=t[1],o=t[2]),e[0]=r,e[1]=n,e[2]=-n,e[3]=r,e[4]=i-(r*i-n*o),e[5]=o-(n*i+r*o)}function t(e,t){var r=t[0],n=2===t.length?t[1]:t[0];e[0]=r,e[3]=n}function r(e,t,r){e[r]=Math.tan(m.util.degreesToRadians(t[0]))}function n(e,t){e[4]=t[0],2===t.length&&(e[5]=t[1])}var i=m.iMatrix,o=m.reNum,s=m.commaWsp,a="(?:(skewX)\\s*\\(\\s*("+o+")\\s*\\))",c="(?:(skewY)\\s*\\(\\s*("+o+")\\s*\\))",l="(?:(rotate)\\s*\\(\\s*("+o+")(?:"+s+"("+o+")"+s+"("+o+"))?\\s*\\))",u="(?:(scale)\\s*\\(\\s*("+o+")(?:"+s+"("+o+"))?\\s*\\))",f="(?:(translate)\\s*\\(\\s*("+o+")(?:"+s+"("+o+"))?\\s*\\))",h="(?:(matrix)\\s*\\(\\s*("+o+")"+s+"("+o+")"+s+"("+o+")"+s+"("+o+")"+s+"("+o+")"+s+"("+o+")\\s*\\))",d="(?:"+h+"|"+f+"|"+u+"|"+l+"|"+a+"|"+c+")",v="(?:"+d+"(?:"+s+"*"+d+")*)",g="^\\s*(?:"+v+"?)\\s*$",p=new RegExp(g),b=new RegExp(d,"g");return function(o){var s=i.concat(),a=[];if(!o||o&&!p.test(o))return s;o.replace(b,function(o){var c=new RegExp(d).exec(o).filter(function(e){return!!e}),l=c[1],u=c.slice(2).map(parseFloat);switch(l){case"translate":n(s,u);break;case"rotate":u[0]=m.util.degreesToRadians(u[0]),e(s,u);break;case"scale":t(s,u);break;case"skewX":r(s,u,2);break;case"skewY":r(s,u,1);break;case"matrix":s=u}a.push(s.concat()),s=i.concat()});for(var c=a[0];a.length>1;)a.shift(),c=m.util.multiplyTransformMatrices(c,a[0]);return c}}();var P=new RegExp("^\\s*("+m.reNum+"+)\\s*,?\\s*("+m.reNum+"+)\\s*,?\\s*("+m.reNum+"+)\\s*,?\\s*("+m.reNum+"+)\\s*$");m.parseSVGDocument=function(e,t,r,n){if(e){d(e);var i,o,s=m.Object.__uid++,a=v(e),c=m.util.toArray(e.getElementsByTagName("*"));if(a.crossOrigin=n&&n.crossOrigin,a.svgUid=s,0===c.length&&m.isLikelyNode){c=e.selectNodes('//*[name(.)!="svg"]');var l=[];for(i=0,o=c.length;o>i;i++)l[i]=c[i];c=l}var u=c.filter(function(e){return v(e),m.svgValidTagNamesRegEx.test(e.nodeName.replace("svg:",""))&&!g(e,m.svgInvalidAncestorsRegEx)});if(!u||u&&!u.length)return void(t&&t([],{}));var f={};c.filter(function(e){return"clipPath"===e.nodeName.replace("svg:","")}).forEach(function(e){var t=e.getAttribute("id");f[t]=m.util.toArray(e.getElementsByTagName("*")).filter(function(e){return m.svgValidTagNamesRegEx.test(e.nodeName.replace("svg:",""))})}),m.gradientDefs[s]=m.getGradientDefs(e),m.cssRules[s]=m.getCSSRules(e),m.clipPaths[s]=f,m.parseElements(u,function(e,r){t&&(t(e,a,r,c),delete m.gradientDefs[s],delete m.cssRules[s],delete m.clipPaths[s])},y(a),r,n)}};var T=new RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+m.reNum+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+m.reNum+"))?\\s+(.*)");b(m,{parseFontDeclaration:function(e,t){var r=e.match(T);if(r){var n=r[1],i=r[3],o=r[4],s=r[5],a=r[6];n&&(t.fontStyle=n),i&&(t.fontWeight=isNaN(parseFloat(i))?i:parseFloat(i)),o&&(t.fontSize=w(o)),a&&(t.fontFamily=a),s&&(t.lineHeight="normal"===s?1:s)}},getGradientDefs:function(e){var t,r=["linearGradient","radialGradient","svg:linearGradient","svg:radialGradient"],n=o(e,r),i=0,s={};for(i=n.length;i--;)t=n[i],t.getAttribute("xlink:href")&&p(e,t),s[t.getAttribute("id")]=t;return s},parseAttributes:function(e,n,o){if(e){var s,a,l,u={};"undefined"==typeof o&&(o=e.getAttribute("svgUid")),e.parentNode&&m.svgValidParentsRegEx.test(e.parentNode.nodeName)&&(u=m.parseAttributes(e.parentNode,n,o));var f=n.reduce(function(t,r){return s=e.getAttribute(r),s&&(t[r]=s),t},{}),h=b(c(e,o),m.parseStyleAttribute(e));f=b(f,h),h[A]&&e.setAttribute(A,h[A]),a=l=u.fontSize||m.Text.DEFAULT_SVG_FONT_SIZE,f[F]&&(f[F]=a=w(f[F],l));var d,v,g={};for(var p in f)d=t(p),v=r(d,f[p],u,a),g[d]=v;g&&g.font&&m.parseFontDeclaration(g.font,g);var y=b(u,g);return m.svgValidParentsRegEx.test(e.nodeName)?y:i(y)}},parseElements:function(e,t,r,n,i){new m.ElementsParser(e,t,r,n,i).parse()},parseStyleAttribute:function(e){var t={},r=e.getAttribute("style");return r?("string"==typeof r?s(r,t):a(r,t),t):t},parsePointsAttribute:function(e){if(!e)return null;e=e.replace(/,/g," ").trim(),e=e.split(/\s+/);var t,r,n=[];for(t=0,r=e.length;r>t;t+=2)n.push({x:parseFloat(e[t]),y:parseFloat(e[t+1])});return n},getCSSRules:function(e){var t,r,n,i=e.getElementsByTagName("style"),o={};for(t=0,r=i.length;r>t;t++){var s=i[t].textContent;s=s.replace(/\/\*[\s\S]*?\*\//g,""),""!==s.trim()&&(n=s.split("}"),n=n.filter(function(e){return e.trim()}),n.forEach(function(e){var n=e.split("{"),i={},s=n[1].trim(),a=s.split(";").filter(function(e){return e.trim()});for(t=0,r=a.length;r>t;t++){var c=a[t].split(":"),l=c[0].trim(),u=c[1].trim();i[l]=u}e=n[0].trim(),e.split(",").forEach(function(e){e=e.replace(/^svg/i,"").trim(),""!==e&&(o[e]?m.util.object.extend(o[e],i):o[e]=m.util.object.clone(i))})}))}return o},loadSVGFromURL:function(e,t,r,n){function i(e){var i=e.responseXML;return i&&i.documentElement?void m.parseSVGDocument(i.documentElement,function(e,r,n,i){t&&t(e,r,n,i)},r,n):(t&&t(null),!1)}e=e.replace(/^\n\s*/,"").trim(),new m.util.request(e,{method:"get",onComplete:i})},loadSVGFromString:function(e,t,r,n){var i=new m.window.DOMParser,o=i.parseFromString(e.trim(),"text/xml");m.parseSVGDocument(o.documentElement,function(e,r,n,i){t(e,r,n,i)},r,n)}})}("undefined"!=typeof exports?exports:this);fabric.ElementsParser=function(e,t,r,n,i,o){this.elements=e,this.callback=t,this.options=r,this.reviver=n,this.svgUid=r&&r.svgUid||0,this.parsingOptions=i,this.regexUrl=/^url\(['"]?#([^'"]+)['"]?\)/g,this.doc=o},function(e){e.parse=function(){this.instances=new Array(this.elements.length),this.numElements=this.elements.length,this.createObjects()},e.createObjects=function(){var e=this;this.elements.forEach(function(t,r){t.setAttribute("svgUid",e.svgUid),e.createObject(t,r)})},e.findTag=function(e){return fabric[fabric.util.string.capitalize(e.tagName.replace("svg:",""))]},e.createObject=function(e,t){var r=this.findTag(e);if(r&&r.fromElement)try{r.fromElement(e,this.createCallback(t,e),this.options)}catch(n){fabric.log(n)}else this.checkIfDone()},e.createCallback=function(e,t){var r=this;return function(n){var i;r.resolveGradient(n,t,"fill"),r.resolveGradient(n,t,"stroke"),n instanceof fabric.Image&&n._originalElement&&(i=n.parsePreserveAspectRatioAttribute(t)),n._removeTransformMatrix(i),r.resolveClipPath(n,t),r.reviver&&r.reviver(t,n),r.instances[e]=n,r.checkIfDone()}},e.extractPropertyDefinition=function(e,t,r){var n=e[t],i=this.regexUrl;if(i.test(n)){i.lastIndex=0;var o=i.exec(n)[1];return i.lastIndex=0,fabric[r][this.svgUid][o]}},e.resolveGradient=function(e,t,r){var n=this.extractPropertyDefinition(e,r,"gradientDefs");if(n){var i=t.getAttribute(r+"-opacity"),o=fabric.Gradient.fromElement(n,e,i,this.options);e.set(r,o)}},e.createClipPathCallback=function(e,t){return function(e){e._removeTransformMatrix(),e.fillRule=e.clipRule,t.push(e)}},e.resolveClipPath=function(e,t){var r,n,i,o,s,a,c=this.extractPropertyDefinition(e,"clipPath","clipPaths");if(c){o=[],i=fabric.util.invertTransform(e.calcTransformMatrix());for(var l=c[0].parentNode,u=t;u.parentNode&&u.getAttribute("clip-path")!==e.clipPath;)u=u.parentNode;u.parentNode.appendChild(l);for(var f=0;f<c.length;f++)r=c[f],n=this.findTag(r),n.fromElement(r,this.createClipPathCallback(e,o),this.options);c=1===o.length?o[0]:new fabric.Group(o),s=fabric.util.multiplyTransformMatrices(i,c.calcTransformMatrix()),c.clipPath&&this.resolveClipPath(c,u);var a=fabric.util.qrDecompose(s);c.flipX=!1,c.flipY=!1,c.set("scaleX",a.scaleX),c.set("scaleY",a.scaleY),c.angle=a.angle,c.skewX=a.skewX,c.skewY=0,c.setPositionByOrigin({x:a.translateX,y:a.translateY},"center","center"),e.clipPath=c}else delete e.clipPath},e.checkIfDone=function(){0===--this.numElements&&(this.instances=this.instances.filter(function(e){return null!=e}),this.callback(this.instances,this.elements))}}(fabric.ElementsParser.prototype);!function(e){"use strict";function t(e,t){this.x=e,this.y=t}var n=e.fabric||(e.fabric={});return n.Point?void n.warn("fabric.Point is already defined"):(n.Point=t,void(t.prototype={type:"point",constructor:t,add:function(e){return new t(this.x+e.x,this.y+e.y)},addEquals:function(e){return this.x+=e.x,this.y+=e.y,this},scalarAdd:function(e){return new t(this.x+e,this.y+e)},scalarAddEquals:function(e){return this.x+=e,this.y+=e,this},subtract:function(e){return new t(this.x-e.x,this.y-e.y)},subtractEquals:function(e){return this.x-=e.x,this.y-=e.y,this},scalarSubtract:function(e){return new t(this.x-e,this.y-e)},scalarSubtractEquals:function(e){return this.x-=e,this.y-=e,this},multiply:function(e){return new t(this.x*e,this.y*e)},multiplyEquals:function(e){return this.x*=e,this.y*=e,this},divide:function(e){return new t(this.x/e,this.y/e)},divideEquals:function(e){return this.x/=e,this.y/=e,this},eq:function(e){return this.x===e.x&&this.y===e.y},lt:function(e){return this.x<e.x&&this.y<e.y},lte:function(e){return this.x<=e.x&&this.y<=e.y},gt:function(e){return this.x>e.x&&this.y>e.y},gte:function(e){return this.x>=e.x&&this.y>=e.y},lerp:function(e,n){return"undefined"==typeof n&&(n=.5),n=Math.max(Math.min(1,n),0),new t(this.x+(e.x-this.x)*n,this.y+(e.y-this.y)*n)},distanceFrom:function(e){var t=this.x-e.x,n=this.y-e.y;return Math.sqrt(t*t+n*n)},midPointFrom:function(e){return this.lerp(e)},min:function(e){return new t(Math.min(this.x,e.x),Math.min(this.y,e.y))},max:function(e){return new t(Math.max(this.x,e.x),Math.max(this.y,e.y))},toString:function(){return this.x+","+this.y},setXY:function(e,t){return this.x=e,this.y=t,this},setX:function(e){return this.x=e,this},setY:function(e){return this.y=e,this},setFromPoint:function(e){return this.x=e.x,this.y=e.y,this},swap:function(e){var t=this.x,n=this.y;this.x=e.x,this.y=e.y,e.x=t,e.y=n},clone:function(){return new t(this.x,this.y)}}))}("undefined"!=typeof exports?exports:this);!function(t){"use strict";function e(t){this.status=t,this.points=[]}var i=t.fabric||(t.fabric={});return i.Intersection?void i.warn("fabric.Intersection is already defined"):(i.Intersection=e,i.Intersection.prototype={constructor:e,appendPoint:function(t){return this.points.push(t),this},appendPoints:function(t){return this.points=this.points.concat(t),this}},i.Intersection.intersectLineLine=function(t,r,n,s){var o,a=(s.x-n.x)*(t.y-n.y)-(s.y-n.y)*(t.x-n.x),c=(r.x-t.x)*(t.y-n.y)-(r.y-t.y)*(t.x-n.x),l=(s.y-n.y)*(r.x-t.x)-(s.x-n.x)*(r.y-t.y);if(0!==l){var h=a/l,u=c/l;h>=0&&1>=h&&u>=0&&1>=u?(o=new e("Intersection"),o.appendPoint(new i.Point(t.x+h*(r.x-t.x),t.y+h*(r.y-t.y)))):o=new e}else o=new e(0===a||0===c?"Coincident":"Parallel");return o},i.Intersection.intersectLinePolygon=function(t,i,r){var n,s,o,a,c=new e,l=r.length;for(a=0;l>a;a++)n=r[a],s=r[(a+1)%l],o=e.intersectLineLine(t,i,n,s),c.appendPoints(o.points);return c.points.length>0&&(c.status="Intersection"),c},i.Intersection.intersectPolygonPolygon=function(t,i){var r,n=new e,s=t.length;for(r=0;s>r;r++){var o=t[r],a=t[(r+1)%s],c=e.intersectLinePolygon(o,a,i);n.appendPoints(c.points)}return n.points.length>0&&(n.status="Intersection"),n},void(i.Intersection.intersectPolygonRectangle=function(t,r,n){var s=r.min(n),o=r.max(n),a=new i.Point(o.x,s.y),c=new i.Point(s.x,o.y),l=e.intersectLinePolygon(s,a,t),h=e.intersectLinePolygon(a,o,t),u=e.intersectLinePolygon(o,c,t),f=e.intersectLinePolygon(c,s,t),d=new e;return d.appendPoints(l.points),d.appendPoints(h.points),d.appendPoints(u.points),d.appendPoints(f.points),d.points.length>0&&(d.status="Intersection"),d}))}("undefined"!=typeof exports?exports:this);!function(e){"use strict";function t(e){e?this._tryParsingColor(e):this.setSource([0,0,0,1])}function n(e,t,n){return 0>n&&(n+=1),n>1&&(n-=1),1/6>n?e+6*(t-e)*n:.5>n?t:2/3>n?e+(t-e)*(2/3-n)*6:e}var r=e.fabric||(e.fabric={});return r.Color?void r.warn("fabric.Color is already defined."):(r.Color=t,r.Color.prototype={_tryParsingColor:function(e){var n;e in t.colorNameMap&&(e=t.colorNameMap[e]),"transparent"===e&&(n=[255,255,255,0]),n||(n=t.sourceFromHex(e)),n||(n=t.sourceFromRgb(e)),n||(n=t.sourceFromHsl(e)),n||(n=[0,0,0,1]),n&&this.setSource(n)},_rgbToHsl:function(e,t,n){e/=255,t/=255,n/=255;var i,o,s,a=r.util.array.max([e,t,n]),c=r.util.array.min([e,t,n]);if(s=(a+c)/2,a===c)i=o=0;else{var u=a-c;switch(o=s>.5?u/(2-a-c):u/(a+c),a){case e:i=(t-n)/u+(n>t?6:0);break;case t:i=(n-e)/u+2;break;case n:i=(e-t)/u+4}i/=6}return[Math.round(360*i),Math.round(100*o),Math.round(100*s)]},getSource:function(){return this._source},setSource:function(e){this._source=e},toRgb:function(){var e=this.getSource();return"rgb("+e[0]+","+e[1]+","+e[2]+")"},toRgba:function(){var e=this.getSource();return"rgba("+e[0]+","+e[1]+","+e[2]+","+e[3]+")"},toHsl:function(){var e=this.getSource(),t=this._rgbToHsl(e[0],e[1],e[2]);return"hsl("+t[0]+","+t[1]+"%,"+t[2]+"%)"},toHsla:function(){var e=this.getSource(),t=this._rgbToHsl(e[0],e[1],e[2]);return"hsla("+t[0]+","+t[1]+"%,"+t[2]+"%,"+e[3]+")"},toHex:function(){var e,t,n,r=this.getSource();return e=r[0].toString(16),e=1===e.length?"0"+e:e,t=r[1].toString(16),t=1===t.length?"0"+t:t,n=r[2].toString(16),n=1===n.length?"0"+n:n,e.toUpperCase()+t.toUpperCase()+n.toUpperCase()},toHexa:function(){var e,t=this.getSource();return e=Math.round(255*t[3]),e=e.toString(16),e=1===e.length?"0"+e:e,this.toHex()+e.toUpperCase()},getAlpha:function(){return this.getSource()[3]},setAlpha:function(e){var t=this.getSource();return t[3]=e,this.setSource(t),this},toGrayscale:function(){var e=this.getSource(),t=parseInt((.3*e[0]+.59*e[1]+.11*e[2]).toFixed(0),10),n=e[3];return this.setSource([t,t,t,n]),this},toBlackWhite:function(e){var t=this.getSource(),n=(.3*t[0]+.59*t[1]+.11*t[2]).toFixed(0),r=t[3];return e=e||127,n=Number(n)<Number(e)?0:255,this.setSource([n,n,n,r]),this},overlayWith:function(e){e instanceof t||(e=new t(e));var n,r=[],i=this.getAlpha(),o=.5,s=this.getSource(),a=e.getSource();for(n=0;3>n;n++)r.push(Math.round(s[n]*(1-o)+a[n]*o));return r[3]=i,this.setSource(r),this}},r.Color.reRGBa=/^rgba?\(\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*(?:\s*,\s*((?:\d*\.?\d+)?)\s*)?\)$/i,r.Color.reHSLa=/^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3}\%)\s*,\s*(\d{1,3}\%)\s*(?:\s*,\s*(\d+(?:\.\d+)?)\s*)?\)$/i,r.Color.reHex=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,r.Color.colorNameMap={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#00FFFF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000000",blanchedalmond:"#FFEBCD",blue:"#0000FF",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#00FFFF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#FF00FF",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#00FF00",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#FF00FF",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#663399",red:"#FF0000",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFFFFF",whitesmoke:"#F5F5F5",yellow:"#FFFF00",yellowgreen:"#9ACD32"},r.Color.fromRgb=function(e){return t.fromSource(t.sourceFromRgb(e))},r.Color.sourceFromRgb=function(e){var n=e.match(t.reRGBa);if(n){var r=parseInt(n[1],10)/(/%$/.test(n[1])?100:1)*(/%$/.test(n[1])?255:1),i=parseInt(n[2],10)/(/%$/.test(n[2])?100:1)*(/%$/.test(n[2])?255:1),o=parseInt(n[3],10)/(/%$/.test(n[3])?100:1)*(/%$/.test(n[3])?255:1);return[parseInt(r,10),parseInt(i,10),parseInt(o,10),n[4]?parseFloat(n[4]):1]}},r.Color.fromRgba=t.fromRgb,r.Color.fromHsl=function(e){return t.fromSource(t.sourceFromHsl(e))},r.Color.sourceFromHsl=function(e){var r=e.match(t.reHSLa);if(r){var i,o,s,a=(parseFloat(r[1])%360+360)%360/360,c=parseFloat(r[2])/(/%$/.test(r[2])?100:1),u=parseFloat(r[3])/(/%$/.test(r[3])?100:1);if(0===c)i=o=s=u;else{var l=.5>=u?u*(c+1):u+c-u*c,f=2*u-l;i=n(f,l,a+1/3),o=n(f,l,a),s=n(f,l,a-1/3)}return[Math.round(255*i),Math.round(255*o),Math.round(255*s),r[4]?parseFloat(r[4]):1]}},r.Color.fromHsla=t.fromHsl,r.Color.fromHex=function(e){return t.fromSource(t.sourceFromHex(e))},r.Color.sourceFromHex=function(e){if(e.match(t.reHex)){var n=e.slice(e.indexOf("#")+1),r=3===n.length||4===n.length,i=8===n.length||4===n.length,o=r?n.charAt(0)+n.charAt(0):n.substring(0,2),s=r?n.charAt(1)+n.charAt(1):n.substring(2,4),a=r?n.charAt(2)+n.charAt(2):n.substring(4,6),c=i?r?n.charAt(3)+n.charAt(3):n.substring(6,8):"FF";return[parseInt(o,16),parseInt(s,16),parseInt(a,16),parseFloat((parseInt(c,16)/255).toFixed(2))]}},void(r.Color.fromSource=function(e){var n=new t;return n.setSource(e),n}))}("undefined"!=typeof exports?exports:this);!function(e){"use strict";function t(e,t){var r=e.angle+I(Math.atan2(t.y,t.x))+360;return Math.round(r%360/45)}function r(e,t){var r=t.transform.target,n=r.canvas,i=P.util.object.clone(t);i.target=r,n&&n.fire("object:"+e,i),r.fire(e,t)}function n(e,t){var r=t.canvas,n=r.uniScaleKey,i=e[n];return r.uniformScaling&&!i||!r.uniformScaling&&i}function i(e){return e.originX===L&&e.originY===L}function o(e,t,r){var n=e.lockScalingX,i=e.lockScalingY;return n&&i?!0:!t&&(n||i)&&r?!0:n&&"x"===t?!0:i&&"y"===t?!0:!1}function s(e,r,i){var s="not-allowed",a=n(e,i),c="";if(0!==r.x&&0===r.y?c="x":0===r.x&&0!==r.y&&(c="y"),o(i,c,a))return s;var u=t(i,r);return T[u]+"-resize"}function a(e,r,n){var i="not-allowed";if(0!==r.x&&n.lockSkewingY)return i;if(0!==r.y&&n.lockSkewingX)return i;var o=t(n,r)%4;return A[o]+"-resize"}function c(e,t,r){return e[r.canvas.altActionKey]?M.skewCursorStyleHandler(e,t,r):M.scaleCursorStyleHandler(e,t,r)}function u(e,t,r){var n=e[r.canvas.altActionKey];return 0===t.x?n?"skewX":"scaleY":0===t.y?n?"skewY":"scaleX":void 0}function l(e,t,r){return r.lockRotation?"not-allowed":t.cursorStyle}function f(e,t,r,n){return{e:e,transform:t,pointer:{x:r,y:n}}}function h(e){return function(t,r,n,i){var o=r.target,s=o.getCenterPoint(),a=o.translateToOriginPoint(s,r.originX,r.originY),c=e(t,r,n,i);return o.setPositionByOrigin(a,r.originX,r.originY),c}}function d(e,t){return function(n,i,o,s){var a=t(n,i,o,s);return a&&r(e,f(n,i,o,s)),a}}function v(e,t,r,n,i){var o=e.target,s=o.controls[e.corner],a=o.canvas.getZoom(),c=o.padding/a,u=o.toLocalPoint(new P.Point(n,i),t,r);return u.x>=c&&(u.x-=c),u.x<=-c&&(u.x+=c),u.y>=c&&(u.y-=c),u.y<=c&&(u.y+=c),u.x-=s.offsetX,u.y-=s.offsetY,u}function g(e){return e.flipX!==e.flipY}function p(e,t,r,n,i){if(0!==e[t]){var o=e._getTransformedDimensions()[n],s=i/o*e[r];e.set(r,s)}}function m(e,t,r,n){var i,o=t.target,s=o._getTransformedDimensions(0,o.skewY),a=v(t,t.originX,t.originY,r,n),c=Math.abs(2*a.x)-s.x,u=o.skewX;2>c?i=0:(i=I(Math.atan2(c/o.scaleX,s.y/o.scaleY)),t.originX===D&&t.originY===B&&(i=-i),t.originX===Y&&t.originY===X&&(i=-i),g(o)&&(i=-i));var l=u!==i;if(l){var f=o._getTransformedDimensions().y;o.set("skewX",i),p(o,"skewY","scaleY","y",f)}return l}function b(e,t,r,n){var i,o=t.target,s=o._getTransformedDimensions(o.skewX,0),a=v(t,t.originX,t.originY,r,n),c=Math.abs(2*a.y)-s.y,u=o.skewY;2>c?i=0:(i=I(Math.atan2(c/o.scaleY,s.x/o.scaleX)),t.originX===D&&t.originY===B&&(i=-i),t.originX===Y&&t.originY===X&&(i=-i),g(o)&&(i=-i));var l=u!==i;if(l){var f=o._getTransformedDimensions().x;o.set("skewY",i),p(o,"skewX","scaleX","x",f)}return l}function y(e,t,r,n){var i,o=t.target,s=o.skewX,a=t.originY;if(o.lockSkewingX)return!1;if(0===s){var c=v(t,L,L,r,n);i=c.x>0?D:Y}else s>0&&(i=a===X?D:Y),0>s&&(i=a===X?Y:D),g(o)&&(i=i===D?Y:D);t.originX=i;var u=d("skewing",h(m));return u(e,t,r,n)}function w(e,t,r,n){var i,o=t.target,s=o.skewY,a=t.originX;if(o.lockSkewingY)return!1;if(0===s){var c=v(t,L,L,r,n);i=c.y>0?X:B}else s>0&&(i=a===D?X:B),0>s&&(i=a===D?B:X),g(o)&&(i=i===X?B:X);t.originY=i;var u=d("skewing",h(b));return u(e,t,r,n)}function x(e,t,r,n){var i=t,o=i.target,s=o.translateToOriginPoint(o.getCenterPoint(),i.originX,i.originY);if(o.lockRotation)return!1;var a=Math.atan2(i.ey-s.y,i.ex-s.x),c=Math.atan2(n-s.y,r-s.x),u=I(c-a+i.theta),l=!0;if(o.snapAngle>0){var f=o.snapAngle,h=o.snapThreshold||f,d=Math.ceil(u/f)*f,v=Math.floor(u/f)*f;Math.abs(u-v)<h?u=v:Math.abs(u-d)<h&&(u=d)}return 0>u&&(u=360+u),u%=360,l=o.angle!==u,o.angle=u,l}function C(e,t,r,s,a){a=a||{};var c,u,l,f,h,d,g=t.target,p=g.lockScalingX,m=g.lockScalingY,b=a.by,y=n(e,g),w=o(g,b,y),x=t.gestureScale;if(w)return!1;if(x)u=t.scaleX*x,l=t.scaleY*x;else{if(c=v(t,t.originX,t.originY,r,s),h="y"!==b?H(c.x):1,d="x"!==b?H(c.y):1,t.signX||(t.signX=h),t.signY||(t.signY=d),g.lockScalingFlip&&(t.signX!==h||t.signY!==d))return!1;if(f=g._getTransformedDimensions(),y&&!b){var C=Math.abs(c.x)+Math.abs(c.y),j=t.original,_=Math.abs(f.x*j.scaleX/g.scaleX)+Math.abs(f.y*j.scaleY/g.scaleY),S=C/_;u=j.scaleX*S,l=j.scaleY*S}else u=Math.abs(c.x*g.scaleX/f.x),l=Math.abs(c.y*g.scaleY/f.y);i(t)&&(u*=2,l*=2),t.signX!==h&&"y"!==b&&(t.originX=R[t.originX],u*=-1,t.signX=h),t.signY!==d&&"x"!==b&&(t.originY=R[t.originY],l*=-1,t.signY=d)}var O=g.scaleX,k=g.scaleY;return b?("x"===b&&g.set("scaleX",u),"y"===b&&g.set("scaleY",l)):(!p&&g.set("scaleX",u),!m&&g.set("scaleY",l)),O!==g.scaleX||k!==g.scaleY}function j(e,t,r,n){return C(e,t,r,n)}function _(e,t,r,n){return C(e,t,r,n,{by:"x"})}function S(e,t,r,n){return C(e,t,r,n,{by:"y"})}function O(e,t,r,n){return e[t.target.canvas.altActionKey]?M.skewHandlerX(e,t,r,n):M.scalingY(e,t,r,n)}function k(e,t,r,n){return e[t.target.canvas.altActionKey]?M.skewHandlerY(e,t,r,n):M.scalingX(e,t,r,n)}function F(e,t,r,n){var o=t.target,s=v(t,t.originX,t.originY,r,n),a=o.strokeWidth/(o.strokeUniform?o.scaleX:1),c=i(t)?2:1,u=o.width,l=Math.abs(s.x*c/o.scaleX)-a;return o.set("width",Math.max(l,0)),u!==l}function E(e,t,n,i){var o=t.target,s=n-t.offsetX,a=i-t.offsetY,c=!o.get("lockMovementX")&&o.left!==s,u=!o.get("lockMovementY")&&o.top!==a;return c&&o.set("left",s),u&&o.set("top",a),(c||u)&&r("moving",f(e,t,n,i)),c||u}var P=e.fabric||(e.fabric={}),T=["e","se","s","sw","w","nw","n","ne","e"],A=["ns","nesw","ew","nwse"],M={},D="left",X="top",Y="right",B="bottom",L="center",R={top:B,bottom:X,left:Y,right:D,center:L},I=P.util.radiansToDegrees,H=Math.sign||function(e){return(e>0)-(0>e)||+e};M.scaleCursorStyleHandler=s,M.skewCursorStyleHandler=a,M.scaleSkewCursorStyleHandler=c,M.rotationWithSnapping=d("rotating",h(x)),M.scalingEqually=d("scaling",h(j)),M.scalingX=d("scaling",h(_)),M.scalingY=d("scaling",h(S)),M.scalingYOrSkewingX=O,M.scalingXOrSkewingY=k,M.changeWidth=d("resizing",h(F)),M.skewHandlerX=y,M.skewHandlerY=w,M.dragHandler=E,M.scaleOrSkewActionName=u,M.rotationStyleHandler=l,M.fireEvent=r,M.wrapWithFixedAnchor=h,M.wrapWithFireEvent=d,M.getLocalPoint=v,P.controlsUtils=M}("undefined"!=typeof exports?exports:this);!function(e){"use strict";function t(e,t,r,n,i){n=n||{};var o,s=this.sizeX||n.cornerSize||i.cornerSize,a=this.sizeY||n.cornerSize||i.cornerSize,c="undefined"!=typeof n.transparentCorners?n.transparentCorners:i.transparentCorners,u=c?"stroke":"fill",l=!c&&(n.cornerStrokeColor||i.cornerStrokeColor),f=t,h=r;e.save(),e.fillStyle=n.cornerColor||i.cornerColor,e.strokeStyle=n.cornerStrokeColor||i.cornerStrokeColor,s>a?(o=s,e.scale(1,a/s),h=r*s/a):a>s?(o=a,e.scale(s/a,1),f=t*a/s):o=s,e.lineWidth=1,e.beginPath(),e.arc(f,h,o/2,0,2*Math.PI,!1),e[u](),l&&e.stroke(),e.restore()}function r(e,t,r,n,o){n=n||{};var s=this.sizeX||n.cornerSize||o.cornerSize,a=this.sizeY||n.cornerSize||o.cornerSize,c="undefined"!=typeof n.transparentCorners?n.transparentCorners:o.transparentCorners,u=c?"stroke":"fill",l=!c&&(n.cornerStrokeColor||o.cornerStrokeColor),f=s/2,h=a/2;e.save(),e.fillStyle=n.cornerColor||o.cornerColor,e.strokeStyle=n.cornerStrokeColor||o.cornerStrokeColor,e.lineWidth=1,e.translate(t,r),e.rotate(i(o.angle)),e[u+"Rect"](-f,-h,s,a),l&&e.strokeRect(-f,-h,s,a),e.restore()}var n=e.fabric||(e.fabric={}),i=n.util.degreesToRadians,o=n.controlsUtils;o.renderCircleControl=t,o.renderSquareControl=r}("undefined"!=typeof exports?exports:this);!function(e){"use strict";function t(e){for(var t in e)this[t]=e[t]}var n=e.fabric||(e.fabric={});n.Control=t,n.Control.prototype={visible:!0,actionName:"scale",angle:0,x:0,y:0,offsetX:0,offsetY:0,sizeX:null,sizeY:null,touchSizeX:null,touchSizeY:null,cursorStyle:"crosshair",withConnection:!1,actionHandler:function(){},mouseDownHandler:function(){},mouseUpHandler:function(){},getActionHandler:function(){return this.actionHandler},getMouseDownHandler:function(){return this.mouseDownHandler},getMouseUpHandler:function(){return this.mouseUpHandler},cursorStyleHandler:function(e,t){return t.cursorStyle},getActionName:function(e,t){return t.actionName},getVisibility:function(e,t){var n=e._controlsVisibility;return n&&"undefined"!=typeof n[t]?n[t]:this.visible},setVisibility:function(e){this.visible=e},positionHandler:function(e,t){var r=n.util.transformPoint({x:this.x*e.x+this.offsetX,y:this.y*e.y+this.offsetY},t);return r},calcCornerCoords:function(e,t,r,i,o){var s,a,u,c,l=o?this.touchSizeX:this.sizeX,f=o?this.touchSizeY:this.sizeY;if(l&&f&&l!==f){var h=Math.atan2(f,l),d=Math.sqrt(l*l+f*f)/2,v=h-n.util.degreesToRadians(e),g=Math.PI/2-h-n.util.degreesToRadians(e);s=d*n.util.cos(v),a=d*n.util.sin(v),u=d*n.util.cos(g),c=d*n.util.sin(g)}else{var p=l&&f?l:t;d=.7071067812*p;var v=n.util.degreesToRadians(45-e);s=u=d*n.util.cos(v),a=c=d*n.util.sin(v)}return{tl:{x:r-c,y:i-u},tr:{x:r+s,y:i-a},bl:{x:r-s,y:i+a},br:{x:r+c,y:i+u}}},render:function(e,t,r,i,o){switch(i=i||{},i.cornerStyle||o.cornerStyle){case"circle":n.controlsUtils.renderCircleControl.call(this,e,t,r,i,o);break;default:n.controlsUtils.renderSquareControl.call(this,e,t,r,i,o)}}}}("undefined"!=typeof exports?exports:this);!function(){fabric.util.object.clone;fabric.Gradient=fabric.util.createClass({offsetX:0,offsetY:0,gradientTransform:null,gradientUnits:"pixels",type:"linear",initialize:function(e){e||(e={}),e.coords||(e.coords={});var t,r=this;Object.keys(e).forEach(function(t){r[t]=e[t]}),this.id?this.id+="_"+fabric.Object.__uid++:this.id=fabric.Object.__uid++,t={x1:e.coords.x1||0,y1:e.coords.y1||0,x2:e.coords.x2||0,y2:e.coords.y2||0},"radial"===this.type&&(t.r1=e.coords.r1||0,t.r2=e.coords.r2||0),this.coords=t,this.colorStops=e.colorStops.slice()},addColorStop:function(e){for(var t in e){var r=new fabric.Color(e[t]);this.colorStops.push({offset:parseFloat(t),color:r.toRgb(),opacity:r.getAlpha()})}return this},toObject:function(e){var t={type:this.type,coords:this.coords,colorStops:this.colorStops,offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?this.gradientTransform.concat():this.gradientTransform};return fabric.util.populateWithProperties(this,t,e),t},toLive:function(e){var t,r,n,i=fabric.util.object.clone(this.coords);if(this.type){for("linear"===this.type?t=e.createLinearGradient(i.x1,i.y1,i.x2,i.y2):"radial"===this.type&&(t=e.createRadialGradient(i.x1,i.y1,i.r1,i.x2,i.y2,i.r2)),r=0,n=this.colorStops.length;n>r;r++){var o=this.colorStops[r].color,s=this.colorStops[r].opacity,a=this.colorStops[r].offset;"undefined"!=typeof s&&(o=new fabric.Color(o).setAlpha(s).toRgba()),t.addColorStop(a,o)}return t}}}),fabric.util.object.extend(fabric.Gradient,{})}();!function(){"use strict";var t=fabric.util.toFixed;fabric.Pattern=fabric.util.createClass({repeat:"repeat",offsetX:0,offsetY:0,crossOrigin:"",patternTransform:null,initialize:function(t,e){if(t||(t={}),this.id=fabric.Object.__uid++,this.setOptions(t),!t.source||t.source&&"string"!=typeof t.source)return void(e&&e(this));var i=this;this.source=fabric.util.createImage(),fabric.util.loadImage(t.source,function(t,r){i.source=t,e&&e(i,r)},null,this.crossOrigin)},toObject:function(e){var i,r,n=fabric.Object.NUM_FRACTION_DIGITS;return"string"==typeof this.source.src?i=this.source.src:"object"==typeof this.source&&this.source.toDataURL&&(i=this.source.toDataURL()),r={type:"pattern",source:i,repeat:this.repeat,crossOrigin:this.crossOrigin,offsetX:t(this.offsetX,n),offsetY:t(this.offsetY,n),patternTransform:this.patternTransform?this.patternTransform.concat():null},fabric.util.populateWithProperties(this,r,e),r},setOptions:function(t){for(var e in t)this[e]=t[e]},toLive:function(t){var e=this.source;if(!e)return"";if("undefined"!=typeof e.src){if(!e.complete)return"";if(0===e.naturalWidth||0===e.naturalHeight)return""}return t.createPattern(e,this.repeat)}})}();!function(e){"use strict";{var t=e.fabric||(e.fabric={});t.util.toFixed}return t.Shadow?void t.warn("fabric.Shadow is already defined."):(t.Shadow=t.util.createClass({color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1,initialize:function(e){"string"==typeof e&&(e=this._parseShadow(e));for(var n in e)this[n]=e[n];this.id=t.Object.__uid++},_parseShadow:function(e){var n=e.trim(),r=t.Shadow.reOffsetsAndBlur.exec(n)||[],i=n.replace(t.Shadow.reOffsetsAndBlur,"")||"rgb(0,0,0)";return{color:i.trim(),offsetX:parseFloat(r[1],10)||0,offsetY:parseFloat(r[2],10)||0,blur:parseFloat(r[3],10)||0}},toString:function(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")},toObject:function(){if(this.includeDefaultValues)return{color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling};var e={},n=t.Shadow.prototype;return["color","blur","offsetX","offsetY","affectStroke","nonScaling"].forEach(function(t){this[t]!==n[t]&&(e[t]=this[t])},this),e}}),void(t.Shadow.reOffsetsAndBlur=/(?:\s|^)(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(\d+(?:\.\d*)?(?:px)?)?(?:\s?|$)(?:$|\s)/))}("undefined"!=typeof exports?exports:this);!function(){"use strict";if(fabric.StaticCanvas)return void fabric.warn("fabric.StaticCanvas is already defined.");var e=fabric.util.object.extend,t=fabric.util.getElementOffset,n=fabric.util.removeFromArray,r=(fabric.util.toFixed,fabric.util.transformPoint),i=fabric.util.invertTransform,o=fabric.util.getNodeCanvas,s=fabric.util.createCanvasElement,a=new Error("Could not initialize `canvas` element");fabric.StaticCanvas=fabric.util.createClass(fabric.CommonMethods,{initialize:function(e,t){t||(t={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(e,t)},backgroundColor:"",backgroundImage:null,overlayColor:"",overlayImage:null,includeDefaultValues:!0,stateful:!1,renderOnAddRemove:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,imageSmoothingEnabled:!0,viewportTransform:fabric.iMatrix.concat(),backgroundVpt:!0,overlayVpt:!0,enableRetinaScaling:!0,vptCoords:{},skipOffscreen:!0,clipPath:void 0,_initStatic:function(e,t){var n=this.requestRenderAllBound;this._objects=[],this._createLowerCanvas(e),this._initOptions(t),this.interactive||this._initRetinaScaling(),t.overlayImage&&this.setOverlayImage(t.overlayImage,n),t.backgroundImage&&this.setBackgroundImage(t.backgroundImage,n),t.backgroundColor&&this.setBackgroundColor(t.backgroundColor,n),t.overlayColor&&this.setOverlayColor(t.overlayColor,n),this.calcOffset()},_isRetinaScaling:function(){return fabric.devicePixelRatio>1&&this.enableRetinaScaling},getRetinaScaling:function(){return this._isRetinaScaling()?Math.max(1,fabric.devicePixelRatio):1},_initRetinaScaling:function(){if(this._isRetinaScaling()){var e=fabric.devicePixelRatio;this.__initRetinaScaling(e,this.lowerCanvasEl,this.contextContainer),this.upperCanvasEl&&this.__initRetinaScaling(e,this.upperCanvasEl,this.contextTop)}},__initRetinaScaling:function(e,t,n){t.setAttribute("width",this.width*e),t.setAttribute("height",this.height*e),n.scale(e,e)},calcOffset:function(){return this._offset=t(this.lowerCanvasEl),this},setOverlayImage:function(e,t,n){return this.__setBgOverlayImage("overlayImage",e,t,n)},setBackgroundImage:function(e,t,n){return this.__setBgOverlayImage("backgroundImage",e,t,n)},setOverlayColor:function(e,t){return this.__setBgOverlayColor("overlayColor",e,t)},setBackgroundColor:function(e,t){return this.__setBgOverlayColor("backgroundColor",e,t)},__setBgOverlayImage:function(e,t,n,r){return"string"==typeof t?fabric.util.loadImage(t,function(t,i){if(t){var o=new fabric.Image(t,r);this[e]=o,o.canvas=this}n&&n(t,i)},this,r&&r.crossOrigin):(r&&t.setOptions(r),this[e]=t,t&&(t.canvas=this),n&&n(t,!1)),this},__setBgOverlayColor:function(e,t,n){return this[e]=t,this._initGradient(t,e),this._initPattern(t,e,n),this},_createCanvasElement:function(){var e=s();if(!e)throw a;if(e.style||(e.style={}),"undefined"==typeof e.getContext)throw a;return e},_initOptions:function(e){var t=this.lowerCanvasEl;this._setOptions(e),this.width=this.width||parseInt(t.width,10)||0,this.height=this.height||parseInt(t.height,10)||0,this.lowerCanvasEl.style&&(t.width=this.width,t.height=this.height,t.style.width=this.width+"px",t.style.height=this.height+"px",this.viewportTransform=this.viewportTransform.slice())},_createLowerCanvas:function(e){this.lowerCanvasEl=e&&e.getContext?e:fabric.util.getById(e)||this._createCanvasElement(),fabric.util.addClass(this.lowerCanvasEl,"lower-canvas"),this._originalCanvasStyle=this.lowerCanvasEl.style,this.interactive&&this._applyCanvasStyle(this.lowerCanvasEl),this.contextContainer=this.lowerCanvasEl.getContext("2d")},getWidth:function(){return this.width},getHeight:function(){return this.height},setWidth:function(e,t){return this.setDimensions({width:e},t)},setHeight:function(e,t){return this.setDimensions({height:e},t)},setDimensions:function(e,t){var n;t=t||{};for(var r in e)n=e[r],t.cssOnly||(this._setBackstoreDimension(r,e[r]),n+="px",this.hasLostContext=!0),t.backstoreOnly||this._setCssDimension(r,n);return this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop),this._initRetinaScaling(),this.calcOffset(),t.cssOnly||this.requestRenderAll(),this},_setBackstoreDimension:function(e,t){return this.lowerCanvasEl[e]=t,this.upperCanvasEl&&(this.upperCanvasEl[e]=t),this.cacheCanvasEl&&(this.cacheCanvasEl[e]=t),this[e]=t,this},_setCssDimension:function(e,t){return this.lowerCanvasEl.style[e]=t,this.upperCanvasEl&&(this.upperCanvasEl.style[e]=t),this.wrapperEl&&(this.wrapperEl.style[e]=t),this},getZoom:function(){return this.viewportTransform[0]},setViewportTransform:function(e){var t,n,r,i=this._activeObject,o=this.backgroundImage,s=this.overlayImage;for(this.viewportTransform=e,n=0,r=this._objects.length;r>n;n++)t=this._objects[n],t.group||t.setCoords(!0);return i&&i.setCoords(),o&&o.setCoords(!0),s&&s.setCoords(!0),this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll(),this},zoomToPoint:function(e,t){var n=e,o=this.viewportTransform.slice(0);e=r(e,i(this.viewportTransform)),o[0]=t,o[3]=t;var s=r(e,o);return o[4]+=n.x-s.x,o[5]+=n.y-s.y,this.setViewportTransform(o)},setZoom:function(e){return this.zoomToPoint(new fabric.Point(0,0),e),this},absolutePan:function(e){var t=this.viewportTransform.slice(0);return t[4]=-e.x,t[5]=-e.y,this.setViewportTransform(t)},relativePan:function(e){return this.absolutePan(new fabric.Point(-e.x-this.viewportTransform[4],-e.y-this.viewportTransform[5]))},getElement:function(){return this.lowerCanvasEl},_onObjectAdded:function(e){this.stateful&&e.setupState(),e._set("canvas",this),e.setCoords(),this.fire("object:added",{target:e}),e.fire("added")},_onObjectRemoved:function(e){this.fire("object:removed",{target:e}),e.fire("removed"),delete e.canvas},clearContext:function(e){return e.clearRect(0,0,this.width,this.height),this},getContext:function(){return this.contextContainer},clear:function(){return this.remove.apply(this,this.getObjects()),this.backgroundImage=null,this.overlayImage=null,this.backgroundColor="",this.overlayColor="",this._hasITextHandlers&&(this.off("mouse:up",this._mouseUpITextHandler),this._iTextInstances=null,this._hasITextHandlers=!1),this.clearContext(this.contextContainer),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll(),this},renderAll:function(){var e=this.contextContainer;return this.renderCanvas(e,this._objects),this},renderAndReset:function(){this.isRendering=0,this.renderAll()},requestRenderAll:function(){return this.isRendering||(this.isRendering=fabric.util.requestAnimFrame(this.renderAndResetBound)),this},calcViewportBoundaries:function(){var e={},t=this.width,n=this.height,o=i(this.viewportTransform);return e.tl=r({x:0,y:0},o),e.br=r({x:t,y:n},o),e.tr=new fabric.Point(e.br.x,e.tl.y),e.bl=new fabric.Point(e.tl.x,e.br.y),this.vptCoords=e,e},cancelRequestedRender:function(){this.isRendering&&(fabric.util.cancelAnimFrame(this.isRendering),this.isRendering=0)},renderCanvas:function(e,t){var n=this.viewportTransform,r=this.clipPath;this.cancelRequestedRender(),this.calcViewportBoundaries(),this.clearContext(e),fabric.util.setImageSmoothing(e,this.imageSmoothingEnabled),this.fire("before:render",{ctx:e}),this._renderBackground(e),e.save(),e.transform(n[0],n[1],n[2],n[3],n[4],n[5]),this._renderObjects(e,t),e.restore(),!this.controlsAboveOverlay&&this.interactive&&this.drawControls(e),r&&(r.canvas=this,r.shouldCache(),r._transformDone=!0,r.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(e)),this._renderOverlay(e),this.controlsAboveOverlay&&this.interactive&&this.drawControls(e),this.fire("after:render",{ctx:e})},drawClipPathOnCanvas:function(e){var t=this.viewportTransform,n=this.clipPath;e.save(),e.transform(t[0],t[1],t[2],t[3],t[4],t[5]),e.globalCompositeOperation="destination-in",n.transform(e),e.scale(1/n.zoomX,1/n.zoomY),e.drawImage(n._cacheCanvas,-n.cacheTranslationX,-n.cacheTranslationY),e.restore()},_renderObjects:function(e,t){var n,r;for(n=0,r=t.length;r>n;++n)t[n]&&t[n].render(e)},_renderBackgroundOrOverlay:function(e,t){var n=this[t+"Color"],r=this[t+"Image"],i=this.viewportTransform,o=this[t+"Vpt"];if(n||r){if(n){e.save(),e.beginPath(),e.moveTo(0,0),e.lineTo(this.width,0),e.lineTo(this.width,this.height),e.lineTo(0,this.height),e.closePath(),e.fillStyle=n.toLive?n.toLive(e,this):n,o&&e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),e.transform(1,0,0,1,n.offsetX||0,n.offsetY||0);var s=n.gradientTransform||n.patternTransform;s&&e.transform(s[0],s[1],s[2],s[3],s[4],s[5]),e.fill(),e.restore()}r&&(e.save(),o&&e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),r.render(e),e.restore())}},_renderBackground:function(e){this._renderBackgroundOrOverlay(e,"background")},_renderOverlay:function(e){this._renderBackgroundOrOverlay(e,"overlay")},getCenter:function(){return{top:this.height/2,left:this.width/2}},getCenterPoint:function(){return new fabric.Point(this.width/2,this.height/2)},centerObjectH:function(e){return this._centerObject(e,new fabric.Point(this.getCenterPoint().x,e.getCenterPoint().y))},centerObjectV:function(e){return this._centerObject(e,new fabric.Point(e.getCenterPoint().x,this.getCenterPoint().y))},centerObject:function(e){var t=this.getCenterPoint();return this._centerObject(e,t)},viewportCenterObject:function(e){var t=this.getVpCenter();return this._centerObject(e,t)},viewportCenterObjectH:function(e){var t=this.getVpCenter();return this._centerObject(e,new fabric.Point(t.x,e.getCenterPoint().y)),this},viewportCenterObjectV:function(e){var t=this.getVpCenter();return this._centerObject(e,new fabric.Point(e.getCenterPoint().x,t.y))},getVpCenter:function(){var e=this.getCenterPoint(),t=i(this.viewportTransform);return r(e,t)},_centerObject:function(e,t){return e.setPositionByOrigin(t,"center","center"),e.setCoords(),this.renderOnAddRemove&&this.requestRenderAll(),this},toDatalessJSON:function(e){return this.toDatalessObject(e)},toObject:function(e){return this._toObjectMethod("toObject",e)},toDatalessObject:function(e){return this._toObjectMethod("toDatalessObject",e)},_toObjectMethod:function(t,n){var r=this.clipPath,i={version:fabric.version,objects:this._toObjects(t,n)};return r&&!r.excludeFromExport&&(i.clipPath=this._toObject(this.clipPath,t,n)),e(i,this.__serializeBgOverlay(t,n)),fabric.util.populateWithProperties(this,i,n),i},_toObjects:function(e,t){return this._objects.filter(function(e){return!e.excludeFromExport}).map(function(n){return this._toObject(n,e,t)},this)},_toObject:function(e,t,n){var r;this.includeDefaultValues||(r=e.includeDefaultValues,e.includeDefaultValues=!1);var i=e[t](n);return this.includeDefaultValues||(e.includeDefaultValues=r),i},__serializeBgOverlay:function(e,t){var n={},r=this.backgroundImage,i=this.overlayImage,o=this.backgroundColor,s=this.overlayColor;return o&&o.toObject?o.excludeFromExport||(n.background=o.toObject(t)):o&&(n.background=o),s&&s.toObject?s.excludeFromExport||(n.overlay=s.toObject(t)):s&&(n.overlay=s),r&&!r.excludeFromExport&&(n.backgroundImage=this._toObject(r,e,t)),i&&!i.excludeFromExport&&(n.overlayImage=this._toObject(i,e,t)),n},sendToBack:function(e){if(!e)return this;var t,r,i,o=this._activeObject;if(e===o&&"activeSelection"===e.type)for(i=o._objects,t=i.length;t--;)r=i[t],n(this._objects,r),this._objects.unshift(r);else n(this._objects,e),this._objects.unshift(e);return this.renderOnAddRemove&&this.requestRenderAll(),this},bringToFront:function(e){if(!e)return this;var t,r,i,o=this._activeObject;if(e===o&&"activeSelection"===e.type)for(i=o._objects,t=0;t<i.length;t++)r=i[t],n(this._objects,r),this._objects.push(r);else n(this._objects,e),this._objects.push(e);return this.renderOnAddRemove&&this.requestRenderAll(),this},sendBackwards:function(e,t){if(!e)return this;var r,i,o,s,a,c=this._activeObject,u=0;if(e===c&&"activeSelection"===e.type)for(a=c._objects,r=0;r<a.length;r++)i=a[r],o=this._objects.indexOf(i),o>0+u&&(s=o-1,n(this._objects,i),this._objects.splice(s,0,i)),u++;else o=this._objects.indexOf(e),0!==o&&(s=this._findNewLowerIndex(e,o,t),n(this._objects,e),this._objects.splice(s,0,e));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewLowerIndex:function(e,t,n){var r,i;if(n)for(r=t,i=t-1;i>=0;--i){var o=e.intersectsWithObject(this._objects[i])||e.isContainedWithinObject(this._objects[i])||this._objects[i].isContainedWithinObject(e);if(o){r=i;break}}else r=t-1;return r},bringForward:function(e,t){if(!e)return this;var r,i,o,s,a,c=this._activeObject,u=0;if(e===c&&"activeSelection"===e.type)for(a=c._objects,r=a.length;r--;)i=a[r],o=this._objects.indexOf(i),o<this._objects.length-1-u&&(s=o+1,n(this._objects,i),this._objects.splice(s,0,i)),u++;else o=this._objects.indexOf(e),o!==this._objects.length-1&&(s=this._findNewUpperIndex(e,o,t),n(this._objects,e),this._objects.splice(s,0,e));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewUpperIndex:function(e,t,n){var r,i,o;if(n)for(r=t,i=t+1,o=this._objects.length;o>i;++i){var s=e.intersectsWithObject(this._objects[i])||e.isContainedWithinObject(this._objects[i])||this._objects[i].isContainedWithinObject(e);if(s){r=i;break}}else r=t+1;return r},moveTo:function(e,t){return n(this._objects,e),this._objects.splice(t,0,e),this.renderOnAddRemove&&this.requestRenderAll()},dispose:function(){return this.isRendering&&(fabric.util.cancelAnimFrame(this.isRendering),this.isRendering=0),this.forEachObject(function(e){e.dispose&&e.dispose()}),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose&&this.backgroundImage.dispose(),this.backgroundImage=null,this.overlayImage&&this.overlayImage.dispose&&this.overlayImage.dispose(),this.overlayImage=null,this._iTextInstances=null,this.contextContainer=null,this.lowerCanvasEl.classList.remove("lower-canvas"),fabric.util.setStyle(this.lowerCanvasEl,this._originalCanvasStyle),delete this._originalCanvasStyle,this.lowerCanvasEl.setAttribute("width",this.width),this.lowerCanvasEl.setAttribute("height",this.height),fabric.util.cleanUpJsdomNode(this.lowerCanvasEl),this.lowerCanvasEl=void 0,this},toString:function(){return"#<fabric.Canvas ("+this.complexity()+"): { objects: "+this._objects.length+" }>"}}),e(fabric.StaticCanvas.prototype,fabric.Observable),e(fabric.StaticCanvas.prototype,fabric.Collection),e(fabric.StaticCanvas.prototype,fabric.DataURLExporter),e(fabric.StaticCanvas,{EMPTY_JSON:'{"objects": [], "background": "white"}',supports:function(e){var t=s();if(!t||!t.getContext)return null;var n=t.getContext("2d");if(!n)return null;switch(e){case"setLineDash":return"undefined"!=typeof n.setLineDash;default:return null}}}),fabric.StaticCanvas.prototype.toJSON=fabric.StaticCanvas.prototype.toObject,fabric.isLikelyNode&&(fabric.StaticCanvas.prototype.createPNGStream=function(){var e=o(this.lowerCanvasEl);return e&&e.createPNGStream()},fabric.StaticCanvas.prototype.createJPEGStream=function(e){var t=o(this.lowerCanvasEl);return t&&t.createJPEGStream(e)})}();fabric.BaseBrush=fabric.util.createClass({color:"rgb(0, 0, 0)",width:1,shadow:null,strokeLineCap:"round",strokeLineJoin:"round",strokeMiterLimit:10,strokeDashArray:null,limitedToCanvasSize:!1,_setBrushStyles:function(e){e.strokeStyle=this.color,e.lineWidth=this.width,e.lineCap=this.strokeLineCap,e.miterLimit=this.strokeMiterLimit,e.lineJoin=this.strokeLineJoin,e.setLineDash(this.strokeDashArray||[])},_saveAndTransform:function(e){var t=this.canvas.viewportTransform;e.save(),e.transform(t[0],t[1],t[2],t[3],t[4],t[5])},_setShadow:function(){if(this.shadow){var e=this.canvas,t=this.shadow,n=e.contextTop,r=e.getZoom();e&&e._isRetinaScaling()&&(r*=fabric.devicePixelRatio),n.shadowColor=t.color,n.shadowBlur=t.blur*r,n.shadowOffsetX=t.offsetX*r,n.shadowOffsetY=t.offsetY*r}},needsFullRender:function(){var e=new fabric.Color(this.color);return e.getAlpha()<1||!!this.shadow},_resetShadow:function(){var e=this.canvas.contextTop;e.shadowColor="",e.shadowBlur=e.shadowOffsetX=e.shadowOffsetY=0},_isOutSideCanvas:function(e){return e.x<0||e.x>this.canvas.getWidth()||e.y<0||e.y>this.canvas.getHeight()}});!function(){fabric.PencilBrush=fabric.util.createClass(fabric.BaseBrush,{decimate:.4,drawStraightLine:!1,straightLineKey:"shiftKey",initialize:function(t){this.canvas=t,this._points=[]},needsFullRender:function(){return this.callSuper("needsFullRender")||this._hasStraightLine},_drawSegment:function(t,e,i){var r=e.midPointFrom(i);return t.quadraticCurveTo(e.x,e.y,r.x,r.y),r},onMouseDown:function(t,e){this.canvas._isMainEvent(e.e)&&(this.drawStraightLine=e.e[this.straightLineKey],this._prepareForDrawing(t),this._captureDrawingPath(t),this._render())},onMouseMove:function(t,e){if(this.canvas._isMainEvent(e.e)&&(this.drawStraightLine=e.e[this.straightLineKey],(this.limitedToCanvasSize!==!0||!this._isOutSideCanvas(t))&&this._captureDrawingPath(t)&&this._points.length>1))if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{var i=this._points,r=i.length,n=this.canvas.contextTop;this._saveAndTransform(n),this.oldEnd&&(n.beginPath(),n.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=this._drawSegment(n,i[r-2],i[r-1],!0),n.stroke(),n.restore()}},onMouseUp:function(t){return this.canvas._isMainEvent(t.e)?(this.drawStraightLine=!1,this.oldEnd=void 0,this._finalizeAndAddPath(),!1):!0},_prepareForDrawing:function(t){var e=new fabric.Point(t.x,t.y);this._reset(),this._addPoint(e),this.canvas.contextTop.moveTo(e.x,e.y)},_addPoint:function(t){return this._points.length>1&&t.eq(this._points[this._points.length-1])?!1:(this.drawStraightLine&&this._points.length>1&&(this._hasStraightLine=!0,this._points.pop()),this._points.push(t),!0)},_reset:function(){this._points=[],this._setBrushStyles(this.canvas.contextTop),this._setShadow(),this._hasStraightLine=!1},_captureDrawingPath:function(t){var e=new fabric.Point(t.x,t.y);return this._addPoint(e)},_render:function(t){var e,i,r=this._points[0],n=this._points[1];if(t=t||this.canvas.contextTop,this._saveAndTransform(t),t.beginPath(),2===this._points.length&&r.x===n.x&&r.y===n.y){var s=this.width/1e3;r=new fabric.Point(r.x,r.y),n=new fabric.Point(n.x,n.y),r.x-=s,n.x+=s}for(t.moveTo(r.x,r.y),e=1,i=this._points.length;i>e;e++)this._drawSegment(t,r,n),r=this._points[e],n=this._points[e+1];t.lineTo(r.x,r.y),t.stroke(),t.restore()},convertPointsToSVGPath:function(t){var e=this.width/1e3;return fabric.util.getSmoothPathFromPoints(t,e)},_isEmptySVGPath:function(t){var e=fabric.util.joinPath(t);return"M 0 0 Q 0 0 0 0 L 0 0"===e},createPath:function(t){var e=new fabric.Path(t,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,e.shadow=new fabric.Shadow(this.shadow)),e},decimatePoints:function(t,e){if(t.length<=2)return t;var i,r,n=this.canvas.getZoom(),s=Math.pow(e/n,2),o=t.length-1,a=t[0],c=[a];for(i=1;o-1>i;i++)r=Math.pow(a.x-t[i].x,2)+Math.pow(a.y-t[i].y,2),r>=s&&(a=t[i],c.push(a));return c.push(t[o]),c},_finalizeAndAddPath:function(){var t=this.canvas.contextTop;t.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));var e=this.convertPointsToSVGPath(this._points);if(this._isEmptySVGPath(e))return void this.canvas.requestRenderAll();var i=this.createPath(e);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:i}),this.canvas.add(i),this.canvas.requestRenderAll(),i.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:i})}})}();fabric.CircleBrush=fabric.util.createClass(fabric.BaseBrush,{width:10,initialize:function(t){this.canvas=t,this.points=[]},drawDot:function(t){var e=this.addPoint(t),i=this.canvas.contextTop;this._saveAndTransform(i),this.dot(i,e),i.restore()},dot:function(t,e){t.fillStyle=e.fill,t.beginPath(),t.arc(e.x,e.y,e.radius,0,2*Math.PI,!1),t.closePath(),t.fill()},onMouseDown:function(t){this.points.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.drawDot(t)},_render:function(){var t,e,i=this.canvas.contextTop,r=this.points;for(this._saveAndTransform(i),t=0,e=r.length;e>t;t++)this.dot(i,r[t]);i.restore()},onMouseMove:function(t){this.limitedToCanvasSize===!0&&this._isOutSideCanvas(t)||(this.needsFullRender()?(this.canvas.clearContext(this.canvas.contextTop),this.addPoint(t),this._render()):this.drawDot(t))},onMouseUp:function(){var t,e,i=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;var r=[];for(t=0,e=this.points.length;e>t;t++){var n=this.points[t],o=new fabric.Circle({radius:n.radius,left:n.x,top:n.y,originX:"center",originY:"center",fill:n.fill});this.shadow&&(o.shadow=new fabric.Shadow(this.shadow)),r.push(o)}var s=new fabric.Group(r);s.canvas=this.canvas,this.canvas.fire("before:path:created",{path:s}),this.canvas.add(s),this.canvas.fire("path:created",{path:s}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=i,this.canvas.requestRenderAll()},addPoint:function(t){var e=new fabric.Point(t.x,t.y),i=fabric.util.getRandomInt(Math.max(0,this.width-20),this.width+20)/2,r=new fabric.Color(this.color).setAlpha(fabric.util.getRandomInt(0,100)/100).toRgba();return e.radius=i,e.fill=r,this.points.push(e),e}});fabric.SprayBrush=fabric.util.createClass(fabric.BaseBrush,{width:10,density:20,dotWidth:1,dotWidthVariance:1,randomOpacity:!1,optimizeOverlapping:!0,initialize:function(e){this.canvas=e,this.sprayChunks=[]},onMouseDown:function(e){this.sprayChunks.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.addSprayChunk(e),this.render(this.sprayChunkPoints)},onMouseMove:function(e){this.limitedToCanvasSize===!0&&this._isOutSideCanvas(e)||(this.addSprayChunk(e),this.render(this.sprayChunkPoints))},onMouseUp:function(){var e=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;for(var t=[],n=0,r=this.sprayChunks.length;r>n;n++)for(var i=this.sprayChunks[n],o=0,s=i.length;s>o;o++){var a=new fabric.Rect({width:i[o].width,height:i[o].width,left:i[o].x+1,top:i[o].y+1,originX:"center",originY:"center",fill:this.color});t.push(a)}this.optimizeOverlapping&&(t=this._getOptimizedRects(t));var c=new fabric.Group(t);this.shadow&&c.set("shadow",new fabric.Shadow(this.shadow)),this.canvas.fire("before:path:created",{path:c}),this.canvas.add(c),this.canvas.fire("path:created",{path:c}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=e,this.canvas.requestRenderAll()},_getOptimizedRects:function(e){var t,n,r,i={};for(n=0,r=e.length;r>n;n++)t=e[n].left+""+e[n].top,i[t]||(i[t]=e[n]);var o=[];for(t in i)o.push(i[t]);return o},render:function(e){var t,n,r=this.canvas.contextTop;for(r.fillStyle=this.color,this._saveAndTransform(r),t=0,n=e.length;n>t;t++){var i=e[t];"undefined"!=typeof i.opacity&&(r.globalAlpha=i.opacity),r.fillRect(i.x,i.y,i.width,i.width)}r.restore()},_render:function(){var e,t,n=this.canvas.contextTop;for(n.fillStyle=this.color,this._saveAndTransform(n),e=0,t=this.sprayChunks.length;t>e;e++)this.render(this.sprayChunks[e]);n.restore()},addSprayChunk:function(e){this.sprayChunkPoints=[];var t,n,r,i,o=this.width/2;for(i=0;i<this.density;i++){t=fabric.util.getRandomInt(e.x-o,e.x+o),n=fabric.util.getRandomInt(e.y-o,e.y+o),r=this.dotWidthVariance?fabric.util.getRandomInt(Math.max(1,this.dotWidth-this.dotWidthVariance),this.dotWidth+this.dotWidthVariance):this.dotWidth;var s=new fabric.Point(t,n);s.width=r,this.randomOpacity&&(s.opacity=fabric.util.getRandomInt(0,100)/100),this.sprayChunkPoints.push(s)}this.sprayChunks.push(this.sprayChunkPoints)}});fabric.PatternBrush=fabric.util.createClass(fabric.PencilBrush,{getPatternSrc:function(){var e=20,t=5,r=fabric.util.createCanvasElement(),n=r.getContext("2d");return r.width=r.height=e+t,n.fillStyle=this.color,n.beginPath(),n.arc(e/2,e/2,e/2,0,2*Math.PI,!1),n.closePath(),n.fill(),r},getPatternSrcFunction:function(){return String(this.getPatternSrc).replace("this.color",'"'+this.color+'"')},getPattern:function(e){return e.createPattern(this.source||this.getPatternSrc(),"repeat")},_setBrushStyles:function(e){this.callSuper("_setBrushStyles",e),e.strokeStyle=this.getPattern(e)},createPath:function(e){var t=this.callSuper("createPath",e),r=t._getLeftTopCoords().scalarAdd(t.strokeWidth/2);return t.stroke=new fabric.Pattern({source:this.source||this.getPatternSrcFunction(),offsetX:-r.x,offsetY:-r.y}),t}});!function(){var e=fabric.util.getPointer,t=fabric.util.degreesToRadians,r=fabric.util.isTouchEvent;fabric.Canvas=fabric.util.createClass(fabric.StaticCanvas,{initialize:function(e,t){t||(t={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(e,t),this._initInteractive(),this._createCacheCanvas()},uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",interactive:!0,selection:!0,selectionKey:"shiftKey",altSelectionKey:null,selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",notAllowedCursor:"not-allowed",containerClass:"canvas-container",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,isDrawingMode:!1,preserveObjectStacking:!1,snapAngle:0,snapThreshold:null,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,targets:[],enablePointerEvents:!1,_hoveredTarget:null,_hoveredTargets:[],_initInteractive:function(){this._currentTransform=null,this._groupSelector=null,this._initWrapperElement(),this._createUpperCanvas(),this._initEventListeners(),this._initRetinaScaling(),this.freeDrawingBrush=fabric.PencilBrush&&new fabric.PencilBrush(this),this.calcOffset()},_chooseObjectsToRender:function(){var e,t,r,n=this.getActiveObjects();if(n.length>0&&!this.preserveObjectStacking){t=[],r=[];for(var i=0,o=this._objects.length;o>i;i++)e=this._objects[i],-1===n.indexOf(e)?t.push(e):r.push(e);n.length>1&&(this._activeObject._objects=r),t.push.apply(t,r)}else t=this._objects;return t},renderAll:function(){!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1);var e=this.contextContainer;return this.renderCanvas(e,this._chooseObjectsToRender()),this},renderTopLayer:function(e){e.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(e),this.contextTopDirty=!0),e.restore()},renderTop:function(){var e=this.contextTop;return this.clearContext(e),this.renderTopLayer(e),this.fire("after:render"),this},_normalizePointer:function(e,t){var r=e.calcTransformMatrix(),n=fabric.util.invertTransform(r),i=this.restorePointerVpt(t);return fabric.util.transformPoint(i,n)},isTargetTransparent:function(e,t,r){if(e.shouldCache()&&e._cacheCanvas&&e!==this._activeObject){var n=this._normalizePointer(e,{x:t,y:r}),i=Math.max(e.cacheTranslationX+n.x*e.zoomX,0),o=Math.max(e.cacheTranslationY+n.y*e.zoomY,0),s=fabric.util.isTransparent(e._cacheContext,Math.round(i),Math.round(o),this.targetFindTolerance);return s}var a=this.contextCache,c=e.selectionBackgroundColor,u=this.viewportTransform;e.selectionBackgroundColor="",this.clearContext(a),a.save(),a.transform(u[0],u[1],u[2],u[3],u[4],u[5]),e.render(a),a.restore(),e.selectionBackgroundColor=c;var s=fabric.util.isTransparent(a,t,r,this.targetFindTolerance);return s},_isSelectionKeyPressed:function(e){var t=!1;return t=Array.isArray(this.selectionKey)?!!this.selectionKey.find(function(t){return e[t]===!0}):e[this.selectionKey]},_shouldClearSelection:function(e,t){var r=this.getActiveObjects(),n=this._activeObject;return!t||t&&n&&r.length>1&&-1===r.indexOf(t)&&n!==t&&!this._isSelectionKeyPressed(e)||t&&!t.evented||t&&!t.selectable&&n&&n!==t},_shouldCenterTransform:function(e,t,r){if(e){var n;return"scale"===t||"scaleX"===t||"scaleY"===t||"resizing"===t?n=this.centeredScaling||e.centeredScaling:"rotate"===t&&(n=this.centeredRotation||e.centeredRotation),n?!r:r}},_getOriginFromCorner:function(e,t){var r={x:e.originX,y:e.originY};return"ml"===t||"tl"===t||"bl"===t?r.x="right":("mr"===t||"tr"===t||"br"===t)&&(r.x="left"),"tl"===t||"mt"===t||"tr"===t?r.y="bottom":("bl"===t||"mb"===t||"br"===t)&&(r.y="top"),r},_getActionFromCorner:function(e,t,r,n){if(!t||!e)return"drag";var i=n.controls[t];return i.getActionName(r,i,n)},_setupCurrentTransform:function(e,r,n){if(r){var i=this.getPointer(e),o=r.__corner,s=r.controls[o],a=n&&o?s.getActionHandler(e,r,s):fabric.controlsUtils.dragHandler,c=this._getActionFromCorner(n,o,e,r),u=this._getOriginFromCorner(r,o),l=e[this.centeredKey],f={target:r,action:c,actionHandler:a,corner:o,scaleX:r.scaleX,scaleY:r.scaleY,skewX:r.skewX,skewY:r.skewY,offsetX:i.x-r.left,offsetY:i.y-r.top,originX:u.x,originY:u.y,ex:i.x,ey:i.y,lastX:i.x,lastY:i.y,theta:t(r.angle),width:r.width*r.scaleX,shiftKey:e.shiftKey,altKey:l,original:fabric.util.saveObjectTransform(r)};this._shouldCenterTransform(r,c,l)&&(f.originX="center",f.originY="center"),f.original.originX=u.x,f.original.originY=u.y,this._currentTransform=f,this._beforeTransform(e)}},setCursor:function(e){this.upperCanvasEl.style.cursor=e},_drawSelection:function(e){var t=this._groupSelector,r=new fabric.Point(t.ex,t.ey),n=fabric.util.transformPoint(r,this.viewportTransform),i=new fabric.Point(t.ex+t.left,t.ey+t.top),o=fabric.util.transformPoint(i,this.viewportTransform),s=Math.min(n.x,o.x),a=Math.min(n.y,o.y),c=Math.max(n.x,o.x),u=Math.max(n.y,o.y),l=this.selectionLineWidth/2;this.selectionColor&&(e.fillStyle=this.selectionColor,e.fillRect(s,a,c-s,u-a)),this.selectionLineWidth&&this.selectionBorderColor&&(e.lineWidth=this.selectionLineWidth,e.strokeStyle=this.selectionBorderColor,s+=l,a+=l,c-=l,u-=l,fabric.Object.prototype._setLineDash.call(this,e,this.selectionDashArray),e.strokeRect(s,a,c-s,u-a))},findTarget:function(e,t){if(!this.skipTargetFind){var n,i,o=!0,s=this.getPointer(e,o),a=this._activeObject,c=this.getActiveObjects(),u=r(e),l=c.length>1&&!t||1===c.length;if(this.targets=[],l&&a._findTargetCorner(s,u))return a;if(c.length>1&&!t&&a===this._searchPossibleTargets([a],s))return a;if(1===c.length&&a===this._searchPossibleTargets([a],s)){if(!this.preserveObjectStacking)return a;n=a,i=this.targets,this.targets=[]}var f=this._searchPossibleTargets(this._objects,s);return e[this.altSelectionKey]&&f&&n&&f!==n&&(f=n,this.targets=i),f}},_checkTarget:function(e,t,r){if(t&&t.visible&&t.evented&&t.containsPoint(e)){if(!this.perPixelTargetFind&&!t.perPixelTargetFind||t.isEditing)return!0;var n=this.isTargetTransparent(t,r.x,r.y);if(!n)return!0}},_searchPossibleTargets:function(e,t){for(var r,n,i=e.length;i--;){var o=e[i],s=o.group?this._normalizePointer(o.group,t):t;if(this._checkTarget(s,o,t)){r=e[i],r.subTargetCheck&&r instanceof fabric.Group&&(n=this._searchPossibleTargets(r._objects,t),n&&this.targets.push(n));break}}return r},restorePointerVpt:function(e){return fabric.util.transformPoint(e,fabric.util.invertTransform(this.viewportTransform))},getPointer:function(t,r){if(this._absolutePointer&&!r)return this._absolutePointer;if(this._pointer&&r)return this._pointer;var n,i=e(t),o=this.upperCanvasEl,s=o.getBoundingClientRect(),a=s.width||0,c=s.height||0;a&&c||("top"in s&&"bottom"in s&&(c=Math.abs(s.top-s.bottom)),"right"in s&&"left"in s&&(a=Math.abs(s.right-s.left))),this.calcOffset(),i.x=i.x-this._offset.left,i.y=i.y-this._offset.top,r||(i=this.restorePointerVpt(i));var u=this.getRetinaScaling();return 1!==u&&(i.x/=u,i.y/=u),n=0===a||0===c?{width:1,height:1}:{width:o.width/a,height:o.height/c},{x:i.x*n.width,y:i.y*n.height}},_createUpperCanvas:function(){var e=this.lowerCanvasEl.className.replace(/\s*lower-canvas\s*/,""),t=this.lowerCanvasEl,r=this.upperCanvasEl;r?r.className="":(r=this._createCanvasElement(),this.upperCanvasEl=r),fabric.util.addClass(r,"upper-canvas "+e),this.wrapperEl.appendChild(r),this._copyCanvasStyle(t,r),this._applyCanvasStyle(r),this.contextTop=r.getContext("2d")},getTopContext:function(){return this.contextTop},_createCacheCanvas:function(){this.cacheCanvasEl=this._createCanvasElement(),this.cacheCanvasEl.setAttribute("width",this.width),this.cacheCanvasEl.setAttribute("height",this.height),this.contextCache=this.cacheCanvasEl.getContext("2d")},_initWrapperElement:function(){this.wrapperEl=fabric.util.wrapElement(this.lowerCanvasEl,"div",{"class":this.containerClass}),fabric.util.setStyle(this.wrapperEl,{width:this.width+"px",height:this.height+"px",position:"relative"}),fabric.util.makeElementUnselectable(this.wrapperEl)},_applyCanvasStyle:function(e){var t=this.width||e.width,r=this.height||e.height;fabric.util.setStyle(e,{position:"absolute",width:t+"px",height:r+"px",left:0,top:0,"touch-action":this.allowTouchScrolling?"manipulation":"none","-ms-touch-action":this.allowTouchScrolling?"manipulation":"none"}),e.width=t,e.height=r,fabric.util.makeElementUnselectable(e)},_copyCanvasStyle:function(e,t){t.style.cssText=e.style.cssText},getSelectionContext:function(){return this.contextTop},getSelectionElement:function(){return this.upperCanvasEl},getActiveObject:function(){return this._activeObject},getActiveObjects:function(){var e=this._activeObject;return e?"activeSelection"===e.type&&e._objects?e._objects.slice(0):[e]:[]},_onObjectRemoved:function(e){e===this._activeObject&&(this.fire("before:selection:cleared",{target:e}),this._discardActiveObject(),this.fire("selection:cleared",{target:e}),e.fire("deselected")),e===this._hoveredTarget&&(this._hoveredTarget=null,this._hoveredTargets=[]),this.callSuper("_onObjectRemoved",e)},_fireSelectionEvents:function(e,t){var r=!1,n=this.getActiveObjects(),i=[],o=[];e.forEach(function(e){-1===n.indexOf(e)&&(r=!0,e.fire("deselected",{e:t,target:e}),o.push(e))}),n.forEach(function(n){-1===e.indexOf(n)&&(r=!0,n.fire("selected",{e:t,target:n}),i.push(n))}),e.length>0&&n.length>0?r&&this.fire("selection:updated",{e:t,selected:i,deselected:o}):n.length>0?this.fire("selection:created",{e:t,selected:i}):e.length>0&&this.fire("selection:cleared",{e:t,deselected:o})},setActiveObject:function(e,t){var r=this.getActiveObjects();return this._setActiveObject(e,t),this._fireSelectionEvents(r,t),this},_setActiveObject:function(e,t){return this._activeObject===e?!1:this._discardActiveObject(t,e)?e.onSelect({e:t})?!1:(this._activeObject=e,!0):!1},_discardActiveObject:function(e,t){var r=this._activeObject;if(r){if(r.onDeselect({e:e,object:t}))return!1;this._activeObject=null}return!0},discardActiveObject:function(e){var t=this.getActiveObjects(),r=this.getActiveObject();return t.length&&this.fire("before:selection:cleared",{target:r,e:e}),this._discardActiveObject(e),this._fireSelectionEvents(t,e),this},dispose:function(){var e=this.wrapperEl;return this.removeListeners(),e.removeChild(this.upperCanvasEl),e.removeChild(this.lowerCanvasEl),this.contextCache=null,this.contextTop=null,["upperCanvasEl","cacheCanvasEl"].forEach(function(e){fabric.util.cleanUpJsdomNode(this[e]),this[e]=void 0}.bind(this)),e.parentNode&&e.parentNode.replaceChild(this.lowerCanvasEl,this.wrapperEl),delete this.wrapperEl,fabric.StaticCanvas.prototype.dispose.call(this),this},clear:function(){return this.discardActiveObject(),this.clearContext(this.contextTop),this.callSuper("clear")},drawControls:function(e){var t=this._activeObject;t&&t._renderControls(e)},_toObject:function(e,t,r){var n=this._realizeGroupTransformOnObject(e),i=this.callSuper("_toObject",e,t,r);return this._unwindGroupTransformOnObject(e,n),i},_realizeGroupTransformOnObject:function(e){if(e.group&&"activeSelection"===e.group.type&&this._activeObject===e.group){var t=["angle","flipX","flipY","left","scaleX","scaleY","skewX","skewY","top"],r={};return t.forEach(function(t){r[t]=e[t]}),fabric.util.addTransformToObject(e,this._activeObject.calcOwnMatrix()),r}return null},_unwindGroupTransformOnObject:function(e,t){t&&e.set(t)},_setSVGObject:function(e,t,r){var n=this._realizeGroupTransformOnObject(t);this.callSuper("_setSVGObject",e,t,r),this._unwindGroupTransformOnObject(t,n)},setViewportTransform:function(e){this.renderOnAddRemove&&this._activeObject&&this._activeObject.isEditing&&this._activeObject.clearContextTop(),fabric.StaticCanvas.prototype.setViewportTransform.call(this,e)}});for(var n in fabric.StaticCanvas)"prototype"!==n&&(fabric.Canvas[n]=fabric.StaticCanvas[n])}();!function(){function t(t,e){return t.button&&t.button===e-1}var e=fabric.util.addListener,i=fabric.util.removeListener,r=3,n=2,s=1,o={passive:!1};fabric.util.object.extend(fabric.Canvas.prototype,{mainTouchId:null,_initEventListeners:function(){this.removeListeners(),this._bindEvents(),this.addOrRemove(e,"add")},_getEventPrefix:function(){return this.enablePointerEvents?"pointer":"mouse"},addOrRemove:function(t,e){var i=this.upperCanvasEl,r=this._getEventPrefix();t(fabric.window,"resize",this._onResize),t(i,r+"down",this._onMouseDown),t(i,r+"move",this._onMouseMove,o),t(i,r+"out",this._onMouseOut),t(i,r+"enter",this._onMouseEnter),t(i,"wheel",this._onMouseWheel),t(i,"contextmenu",this._onContextMenu),t(i,"dblclick",this._onDoubleClick),t(i,"dragover",this._onDragOver),t(i,"dragenter",this._onDragEnter),t(i,"dragleave",this._onDragLeave),t(i,"drop",this._onDrop),this.enablePointerEvents||t(i,"touchstart",this._onTouchStart,o),"undefined"!=typeof eventjs&&e in eventjs&&(eventjs[e](i,"gesture",this._onGesture),eventjs[e](i,"drag",this._onDrag),eventjs[e](i,"orientation",this._onOrientationChange),eventjs[e](i,"shake",this._onShake),eventjs[e](i,"longpress",this._onLongPress))},removeListeners:function(){this.addOrRemove(i,"remove");var t=this._getEventPrefix();i(fabric.document,t+"up",this._onMouseUp),i(fabric.document,"touchend",this._onTouchEnd,o),i(fabric.document,t+"move",this._onMouseMove,o),i(fabric.document,"touchmove",this._onMouseMove,o)},_bindEvents:function(){this.eventsBound||(this._onMouseDown=this._onMouseDown.bind(this),this._onTouchStart=this._onTouchStart.bind(this),this._onMouseMove=this._onMouseMove.bind(this),this._onMouseUp=this._onMouseUp.bind(this),this._onTouchEnd=this._onTouchEnd.bind(this),this._onResize=this._onResize.bind(this),this._onGesture=this._onGesture.bind(this),this._onDrag=this._onDrag.bind(this),this._onShake=this._onShake.bind(this),this._onLongPress=this._onLongPress.bind(this),this._onOrientationChange=this._onOrientationChange.bind(this),this._onMouseWheel=this._onMouseWheel.bind(this),this._onMouseOut=this._onMouseOut.bind(this),this._onMouseEnter=this._onMouseEnter.bind(this),this._onContextMenu=this._onContextMenu.bind(this),this._onDoubleClick=this._onDoubleClick.bind(this),this._onDragOver=this._onDragOver.bind(this),this._onDragEnter=this._simpleEventHandler.bind(this,"dragenter"),this._onDragLeave=this._simpleEventHandler.bind(this,"dragleave"),this._onDrop=this._onDrop.bind(this),this.eventsBound=!0)},_onGesture:function(t,e){this.__onTransformGesture&&this.__onTransformGesture(t,e)},_onDrag:function(t,e){this.__onDrag&&this.__onDrag(t,e)},_onMouseWheel:function(t){this.__onMouseWheel(t)},_onMouseOut:function(t){var e=this._hoveredTarget;this.fire("mouse:out",{target:e,e:t}),this._hoveredTarget=null,e&&e.fire("mouseout",{e:t});var i=this;this._hoveredTargets.forEach(function(r){i.fire("mouse:out",{target:e,e:t}),r&&e.fire("mouseout",{e:t})}),this._hoveredTargets=[],this._iTextInstances&&this._iTextInstances.forEach(function(t){t.isEditing&&t.hiddenTextarea.focus()})},_onMouseEnter:function(t){this._currentTransform||this.findTarget(t)||(this.fire("mouse:over",{target:null,e:t}),this._hoveredTarget=null,this._hoveredTargets=[])},_onOrientationChange:function(t,e){this.__onOrientationChange&&this.__onOrientationChange(t,e)},_onShake:function(t,e){this.__onShake&&this.__onShake(t,e)},_onLongPress:function(t,e){this.__onLongPress&&this.__onLongPress(t,e)},_onDragOver:function(t){t.preventDefault();var e=this._simpleEventHandler("dragover",t);this._fireEnterLeaveEvents(e,t)},_onDrop:function(t){return this._simpleEventHandler("drop:before",t),this._simpleEventHandler("drop",t)},_onContextMenu:function(t){return this.stopContextMenu&&(t.stopPropagation(),t.preventDefault()),!1},_onDoubleClick:function(t){this._cacheTransformEventData(t),this._handleEvent(t,"dblclick"),this._resetTransformEventData(t)},getPointerId:function(t){var e=t.changedTouches;return e?e[0]&&e[0].identifier:this.enablePointerEvents?t.pointerId:-1},_isMainEvent:function(t){return t.isPrimary===!0?!0:t.isPrimary===!1?!1:"touchend"===t.type&&0===t.touches.length?!0:t.changedTouches?t.changedTouches[0].identifier===this.mainTouchId:!0},_onTouchStart:function(t){t.preventDefault(),null===this.mainTouchId&&(this.mainTouchId=this.getPointerId(t)),this.__onMouseDown(t),this._resetTransformEventData();var r=this.upperCanvasEl,n=this._getEventPrefix();e(fabric.document,"touchend",this._onTouchEnd,o),e(fabric.document,"touchmove",this._onMouseMove,o),i(r,n+"down",this._onMouseDown)},_onMouseDown:function(t){this.__onMouseDown(t),this._resetTransformEventData();var r=this.upperCanvasEl,n=this._getEventPrefix();i(r,n+"move",this._onMouseMove,o),e(fabric.document,n+"up",this._onMouseUp),e(fabric.document,n+"move",this._onMouseMove,o)},_onTouchEnd:function(t){if(!(t.touches.length>0)){this.__onMouseUp(t),this._resetTransformEventData(),this.mainTouchId=null;var r=this._getEventPrefix();i(fabric.document,"touchend",this._onTouchEnd,o),i(fabric.document,"touchmove",this._onMouseMove,o);var n=this;this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout(function(){e(n.upperCanvasEl,r+"down",n._onMouseDown),n._willAddMouseDown=0},400)}},_onMouseUp:function(t){this.__onMouseUp(t),this._resetTransformEventData();var r=this.upperCanvasEl,n=this._getEventPrefix();this._isMainEvent(t)&&(i(fabric.document,n+"up",this._onMouseUp),i(fabric.document,n+"move",this._onMouseMove,o),e(r,n+"move",this._onMouseMove,o))},_onMouseMove:function(t){!this.allowTouchScrolling&&t.preventDefault&&t.preventDefault(),this.__onMouseMove(t)},_onResize:function(){this.calcOffset()},_shouldRender:function(t){var e=this._activeObject;return!!e!=!!t||e&&t&&e!==t?!0:e&&e.isEditing?!1:!1},__onMouseUp:function(e){var i,o=this._currentTransform,a=this._groupSelector,c=!1,h=!a||0===a.left&&0===a.top;if(this._cacheTransformEventData(e),i=this._target,this._handleEvent(e,"up:before"),t(e,r))return void(this.fireRightClick&&this._handleEvent(e,"up",r,h));if(t(e,n))return this.fireMiddleClick&&this._handleEvent(e,"up",n,h),void this._resetTransformEventData();if(this.isDrawingMode&&this._isCurrentlyDrawing)return void this._onMouseUpInDrawingMode(e);if(this._isMainEvent(e)){if(o&&(this._finalizeCurrentTransform(e),c=o.actionPerformed),!h){var l=i===this._activeObject;this._maybeGroupObjects(e),c||(c=this._shouldRender(i)||!l&&i===this._activeObject)}var u,f;if(i){if(u=i._findTargetCorner(this.getPointer(e,!0),fabric.util.isTouchEvent(e)),i.selectable&&i!==this._activeObject&&"up"===i.activeOn)this.setActiveObject(i,e),c=!0;else{var d=i.controls[u],g=d&&d.getMouseUpHandler(e,i,d);g&&(f=this.getPointer(e),g(e,o,f.x,f.y))}i.isMoving=!1}if(o&&(o.target!==i||o.corner!==u)){var p=o.target&&o.target.controls[o.corner],v=p&&p.getMouseUpHandler(e,i,d);f=f||this.getPointer(e),v&&v(e,o,f.x,f.y)}this._setCursorFromEvent(e,i),this._handleEvent(e,"up",s,h),this._groupSelector=null,this._currentTransform=null,i&&(i.__corner=0),c?this.requestRenderAll():h||this.renderTop()}},_simpleEventHandler:function(t,e){var i=this.findTarget(e),r=this.targets,n={e:e,target:i,subTargets:r};if(this.fire(t,n),i&&i.fire(t,n),!r)return i;for(var s=0;s<r.length;s++)r[s].fire(t,n);return i},_handleEvent:function(t,e,i,r){var n=this._target,o=this.targets||[],a={e:t,target:n,subTargets:o,button:i||s,isClick:r||!1,pointer:this._pointer,absolutePointer:this._absolutePointer,transform:this._currentTransform};"up"===e&&(a.currentTarget=this.findTarget(t),a.currentSubTargets=this.targets),this.fire("mouse:"+e,a),n&&n.fire("mouse"+e,a);for(var c=0;c<o.length;c++)o[c].fire("mouse"+e,a)},_finalizeCurrentTransform:function(t){var e=this._currentTransform,i=e.target,r={e:t,target:i,transform:e,action:e.action};i._scaling&&(i._scaling=!1),i.setCoords(),(e.actionPerformed||this.stateful&&i.hasStateChanged())&&this._fire("modified",r)},_onMouseDownInDrawingMode:function(t){this._isCurrentlyDrawing=!0,this.getActiveObject()&&this.discardActiveObject(t).requestRenderAll();var e=this.getPointer(t);this.freeDrawingBrush.onMouseDown(e,{e:t,pointer:e}),this._handleEvent(t,"down")},_onMouseMoveInDrawingMode:function(t){if(this._isCurrentlyDrawing){var e=this.getPointer(t);this.freeDrawingBrush.onMouseMove(e,{e:t,pointer:e})}this.setCursor(this.freeDrawingCursor),this._handleEvent(t,"move")},_onMouseUpInDrawingMode:function(t){var e=this.getPointer(t);this._isCurrentlyDrawing=this.freeDrawingBrush.onMouseUp({e:t,pointer:e}),this._handleEvent(t,"up")},__onMouseDown:function(e){this._cacheTransformEventData(e),this._handleEvent(e,"down:before");var i=this._target;if(t(e,r))return void(this.fireRightClick&&this._handleEvent(e,"down",r));if(t(e,n))return void(this.fireMiddleClick&&this._handleEvent(e,"down",n));if(this.isDrawingMode)return void this._onMouseDownInDrawingMode(e);if(this._isMainEvent(e)&&!this._currentTransform){var s=this._pointer;this._previousPointer=s;var o=this._shouldRender(i),a=this._shouldGroup(e,i);if(this._shouldClearSelection(e,i)?this.discardActiveObject(e):a&&(this._handleGrouping(e,i),i=this._activeObject),!this.selection||i&&(i.selectable||i.isEditing||i===this._activeObject)||(this._groupSelector={ex:this._absolutePointer.x,ey:this._absolutePointer.y,top:0,left:0}),i){var c=i===this._activeObject;i.selectable&&"down"===i.activeOn&&this.setActiveObject(i,e);var h=i._findTargetCorner(this.getPointer(e,!0),fabric.util.isTouchEvent(e));if(i.__corner=h,i===this._activeObject&&(h||!a)){this._setupCurrentTransform(e,i,c);var l=i.controls[h],s=this.getPointer(e),u=l&&l.getMouseDownHandler(e,i,l);u&&u(e,this._currentTransform,s.x,s.y)}}this._handleEvent(e,"down"),(o||a)&&this.requestRenderAll()}},_resetTransformEventData:function(){this._target=null,this._pointer=null,this._absolutePointer=null},_cacheTransformEventData:function(t){this._resetTransformEventData(),this._pointer=this.getPointer(t,!0),this._absolutePointer=this.restorePointerVpt(this._pointer),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(t)||null},_beforeTransform:function(t){var e=this._currentTransform;this.stateful&&e.target.saveState(),this.fire("before:transform",{e:t,transform:e})},__onMouseMove:function(t){this._handleEvent(t,"move:before"),this._cacheTransformEventData(t);var e,i;if(this.isDrawingMode)return void this._onMouseMoveInDrawingMode(t);if(this._isMainEvent(t)){var r=this._groupSelector;r?(i=this._absolutePointer,r.left=i.x-r.ex,r.top=i.y-r.ey,this.renderTop()):this._currentTransform?this._transformObject(t):(e=this.findTarget(t)||null,this._setCursorFromEvent(t,e),this._fireOverOutEvents(e,t)),this._handleEvent(t,"move"),this._resetTransformEventData()}},_fireOverOutEvents:function(t,e){var i=this._hoveredTarget,r=this._hoveredTargets,n=this.targets,s=Math.max(r.length,n.length);this.fireSyntheticInOutEvents(t,e,{oldTarget:i,evtOut:"mouseout",canvasEvtOut:"mouse:out",evtIn:"mouseover",canvasEvtIn:"mouse:over"});for(var o=0;s>o;o++)this.fireSyntheticInOutEvents(n[o],e,{oldTarget:r[o],evtOut:"mouseout",evtIn:"mouseover"});this._hoveredTarget=t,this._hoveredTargets=this.targets.concat()},_fireEnterLeaveEvents:function(t,e){var i=this._draggedoverTarget,r=this._hoveredTargets,n=this.targets,s=Math.max(r.length,n.length);this.fireSyntheticInOutEvents(t,e,{oldTarget:i,evtOut:"dragleave",evtIn:"dragenter"});for(var o=0;s>o;o++)this.fireSyntheticInOutEvents(n[o],e,{oldTarget:r[o],evtOut:"dragleave",evtIn:"dragenter"});this._draggedoverTarget=t},fireSyntheticInOutEvents:function(t,e,i){var r,n,s,o,a=i.oldTarget,c=a!==t,h=i.canvasEvtIn,l=i.canvasEvtOut;c&&(r={e:e,target:t,previousTarget:a},n={e:e,target:a,nextTarget:t}),o=t&&c,s=a&&c,s&&(l&&this.fire(l,n),a.fire(i.evtOut,n)),o&&(h&&this.fire(h,r),t.fire(i.evtIn,r))},__onMouseWheel:function(t){this._cacheTransformEventData(t),this._handleEvent(t,"wheel"),this._resetTransformEventData()},_transformObject:function(t){var e=this.getPointer(t),i=this._currentTransform;i.reset=!1,i.shiftKey=t.shiftKey,i.altKey=t[this.centeredKey],this._performTransformAction(t,i,e),i.actionPerformed&&this.requestRenderAll()},_performTransformAction:function(t,e,i){var r=i.x,n=i.y,s=e.action,o=!1,a=e.actionHandler;a&&(o=a(t,e,r,n)),"drag"===s&&o&&(e.target.isMoving=!0,this.setCursor(e.target.moveCursor||this.moveCursor)),e.actionPerformed=e.actionPerformed||o},_fire:fabric.controlsUtils.fireEvent,_setCursorFromEvent:function(t,e){if(!e)return this.setCursor(this.defaultCursor),!1;var i=e.hoverCursor||this.hoverCursor,r=this._activeObject&&"activeSelection"===this._activeObject.type?this._activeObject:null,n=(!r||!r.contains(e))&&e._findTargetCorner(this.getPointer(t,!0));n?this.setCursor(this.getCornerCursor(n,e,t)):(e.subTargetCheck&&this.targets.concat().reverse().map(function(t){i=t.hoverCursor||i}),this.setCursor(i))},getCornerCursor:function(t,e,i){var r=e.controls[t];return r.cursorStyleHandler(i,r,e)}})}();!function(){var e=Math.min,t=Math.max;fabric.util.object.extend(fabric.Canvas.prototype,{_shouldGroup:function(e,t){var r=this._activeObject;return r&&this._isSelectionKeyPressed(e)&&t&&t.selectable&&this.selection&&(r!==t||"activeSelection"===r.type)&&!t.onSelect({e:e})},_handleGrouping:function(e,t){var r=this._activeObject;r.__corner||(t!==r||(t=this.findTarget(e,!0),t&&t.selectable))&&(r&&"activeSelection"===r.type?this._updateActiveSelection(t,e):this._createActiveSelection(t,e))},_updateActiveSelection:function(e,t){var r=this._activeObject,n=r._objects.slice(0);r.contains(e)?(r.removeWithUpdate(e),this._hoveredTarget=e,this._hoveredTargets=this.targets.concat(),1===r.size()&&this._setActiveObject(r.item(0),t)):(r.addWithUpdate(e),this._hoveredTarget=r,this._hoveredTargets=this.targets.concat()),this._fireSelectionEvents(n,t)},_createActiveSelection:function(e,t){var r=this.getActiveObjects(),n=this._createGroup(e);this._hoveredTarget=n,this._setActiveObject(n,t),this._fireSelectionEvents(r,t)},_createGroup:function(e){var t=this._objects,r=t.indexOf(this._activeObject)<t.indexOf(e),n=r?[this._activeObject,e]:[e,this._activeObject];return this._activeObject.isEditing&&this._activeObject.exitEditing(),new fabric.ActiveSelection(n,{canvas:this})},_groupSelectedObjects:function(e){var t,r=this._collectObjects(e);1===r.length?this.setActiveObject(r[0],e):r.length>1&&(t=new fabric.ActiveSelection(r.reverse(),{canvas:this}),this.setActiveObject(t,e))},_collectObjects:function(r){for(var n,i=[],o=this._groupSelector.ex,s=this._groupSelector.ey,a=o+this._groupSelector.left,c=s+this._groupSelector.top,l=new fabric.Point(e(o,a),e(s,c)),u=new fabric.Point(t(o,a),t(s,c)),f=!this.selectionFullyContained,h=o===a&&s===c,d=this._objects.length;d--&&(n=this._objects[d],!(n&&n.selectable&&n.visible&&(f&&n.intersectsWithRect(l,u,!0)||n.isContainedWithinRect(l,u,!0)||f&&n.containsPoint(l,null,!0)||f&&n.containsPoint(u,null,!0))&&(i.push(n),h))););return i.length>1&&(i=i.filter(function(e){return!e.onSelect({e:r})})),i},_maybeGroupObjects:function(e){this.selection&&this._groupSelector&&this._groupSelectedObjects(e),this.setCursor(this.defaultCursor),this._groupSelector=null}})}();!function(){fabric.util.object.extend(fabric.StaticCanvas.prototype,{toDataURL:function(t){t||(t={});var e=t.format||"png",i=t.quality||1,r=(t.multiplier||1)*(t.enableRetinaScaling?this.getRetinaScaling():1),n=this.toCanvasElement(r,t);return fabric.util.toDataURL(n,e,i)},toCanvasElement:function(t,e){t=t||1,e=e||{};var i=(e.width||this.width)*t,r=(e.height||this.height)*t,n=this.getZoom(),s=this.width,o=this.height,a=n*t,c=this.viewportTransform,l=(c[4]-(e.left||0))*t,h=(c[5]-(e.top||0))*t,u=this.interactive,f=[a,0,0,a,l,h],d=this.enableRetinaScaling,p=fabric.util.createCanvasElement(),g=this.contextTop;return p.width=i,p.height=r,this.contextTop=null,this.enableRetinaScaling=!1,this.interactive=!1,this.viewportTransform=f,this.width=i,this.height=r,this.calcViewportBoundaries(),this.renderCanvas(p.getContext("2d"),this._objects),this.viewportTransform=c,this.width=s,this.height=o,this.calcViewportBoundaries(),this.interactive=u,this.enableRetinaScaling=d,this.contextTop=g,p}})}();fabric.util.object.extend(fabric.StaticCanvas.prototype,{loadFromJSON:function(e,t,r){if(e){var n="string"==typeof e?JSON.parse(e):fabric.util.object.clone(e),i=this,o=n.clipPath,s=this.renderOnAddRemove;return this.renderOnAddRemove=!1,delete n.clipPath,this._enlivenObjects(n.objects,function(e){i.clear(),i._setBgOverlay(n,function(){o?i._enlivenObjects([o],function(r){i.clipPath=r[0],i.__setupCanvas.call(i,n,e,s,t)}):i.__setupCanvas.call(i,n,e,s,t)})},r),this}},__setupCanvas:function(e,t,r,n){var i=this;t.forEach(function(e,t){i.insertAt(e,t)}),this.renderOnAddRemove=r,delete e.objects,delete e.backgroundImage,delete e.overlayImage,delete e.background,delete e.overlay,this._setOptions(e),this.renderAll(),n&&n()},_setBgOverlay:function(e,t){var r={backgroundColor:!1,overlayColor:!1,backgroundImage:!1,overlayImage:!1};if(!(e.backgroundImage||e.overlayImage||e.background||e.overlay))return void(t&&t());var n=function(){r.backgroundImage&&r.overlayImage&&r.backgroundColor&&r.overlayColor&&t&&t()};this.__setBgOverlay("backgroundImage",e.backgroundImage,r,n),this.__setBgOverlay("overlayImage",e.overlayImage,r,n),this.__setBgOverlay("backgroundColor",e.background,r,n),this.__setBgOverlay("overlayColor",e.overlay,r,n)},__setBgOverlay:function(e,t,r,n){var i=this;return t?void("backgroundImage"===e||"overlayImage"===e?fabric.util.enlivenObjects([t],function(t){i[e]=t[0],r[e]=!0,n&&n()}):this["set"+fabric.util.string.capitalize(e,!0)](t,function(){r[e]=!0,n&&n()})):(r[e]=!0,void(n&&n()))},_enlivenObjects:function(e,t,r){return e&&0!==e.length?void fabric.util.enlivenObjects(e,function(e){t&&t(e)},null,r):void(t&&t([]))},_toDataURL:function(e,t){this.clone(function(r){t(r.toDataURL(e))})},_toDataURLWithMultiplier:function(e,t,r){this.clone(function(n){r(n.toDataURLWithMultiplier(e,t))})},clone:function(e,t){var r=JSON.stringify(this.toJSON(t));this.cloneWithoutData(function(t){t.loadFromJSON(r,function(){e&&e(t)})})},cloneWithoutData:function(e){var t=fabric.util.createCanvasElement();t.width=this.width,t.height=this.height;var r=new fabric.Canvas(t);this.backgroundImage?(r.setBackgroundImage(this.backgroundImage.src,function(){r.renderAll(),e&&e(r)}),r.backgroundImageOpacity=this.backgroundImageOpacity,r.backgroundImageStretch=this.backgroundImageStretch):e&&e(r)}});!function(){var t=fabric.util.degreesToRadians,e=fabric.util.radiansToDegrees;fabric.util.object.extend(fabric.Canvas.prototype,{__onTransformGesture:function(t,e){if(!this.isDrawingMode&&t.touches&&2===t.touches.length&&"gesture"===e.gesture){var i=this.findTarget(t);"undefined"!=typeof i&&(this.__gesturesParams={e:t,self:e,target:i},this.__gesturesRenderer()),this.fire("touch:gesture",{target:i,e:t,self:e})}},__gesturesParams:null,__gesturesRenderer:function(){if(null!==this.__gesturesParams&&null!==this._currentTransform){var t=this.__gesturesParams.self,e=this._currentTransform,i=this.__gesturesParams.e;e.action="scale",e.originX=e.originY="center",this._scaleObjectBy(t.scale,i),0!==t.rotation&&(e.action="rotate",this._rotateObjectByAngle(t.rotation,i)),this.requestRenderAll(),e.action="drag"}},__onDrag:function(t,e){this.fire("touch:drag",{e:t,self:e})},__onOrientationChange:function(t,e){this.fire("touch:orientation",{e:t,self:e})},__onShake:function(t,e){this.fire("touch:shake",{e:t,self:e})},__onLongPress:function(t,e){this.fire("touch:longpress",{e:t,self:e})},_scaleObjectBy:function(t,e){var i=this._currentTransform,r=i.target;return i.gestureScale=t,r._scaling=!0,fabric.controlsUtils.scalingEqually(e,i,0,0)},_rotateObjectByAngle:function(i,r){var n=this._currentTransform;n.target.get("lockRotation")||(n.target.rotate(e(t(i)+n.theta)),this._fire("rotating",{target:n.target,e:r,transform:n}))}})}();!function(t){"use strict";var e=t.fabric||(t.fabric={}),r=e.util.object.extend,i=e.util.object.clone,n=e.util.toFixed,o=e.util.string.capitalize,s=e.util.degreesToRadians,a=!e.isLikelyNode,c=2;e.Object||(e.Object=e.util.createClass(e.CommonMethods,{type:"object",originX:"left",originY:"top",top:0,left:0,width:0,height:0,scaleX:1,scaleY:1,flipX:!1,flipY:!1,opacity:1,angle:0,skewX:0,skewY:0,cornerSize:13,touchCornerSize:24,transparentCorners:!0,hoverCursor:null,moveCursor:null,padding:0,borderColor:"rgb(178,204,255)",borderDashArray:null,cornerColor:"rgb(178,204,255)",cornerStrokeColor:null,cornerStyle:"rect",cornerDashArray:null,centeredScaling:!1,centeredRotation:!0,fill:"rgb(0,0,0)",fillRule:"nonzero",globalCompositeOperation:"source-over",backgroundColor:"",selectionBackgroundColor:"",stroke:null,strokeWidth:1,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,shadow:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,minScaleLimit:0,selectable:!0,evented:!0,visible:!0,hasControls:!0,hasBorders:!0,perPixelTargetFind:!1,includeDefaultValues:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,excludeFromExport:!1,objectCaching:a,statefullCache:!1,noScaleCache:!0,strokeUniform:!1,dirty:!0,__corner:0,paintFirst:"fill",activeOn:"down",stateProperties:"top left width height scaleX scaleY flipX flipY originX originY transformMatrix stroke strokeWidth strokeDashArray strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit angle opacity fill globalCompositeOperation shadow visible backgroundColor skewX skewY fillRule paintFirst clipPath strokeUniform".split(" "),cacheProperties:"fill stroke strokeWidth strokeDashArray width height paintFirst strokeUniform strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit backgroundColor clipPath".split(" "),colorProperties:"fill stroke backgroundColor".split(" "),clipPath:void 0,inverted:!1,absolutePositioned:!1,initialize:function(t){t&&this.setOptions(t)},_createCacheCanvas:function(){this._cacheProperties={},this._cacheCanvas=e.util.createCanvasElement(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0},_limitCacheSize:function(t){var r=e.perfLimitSizeTotal,i=t.width,n=t.height,o=e.maxCacheSideLimit,s=e.minCacheSideLimit;if(o>=i&&o>=n&&r>=i*n)return s>i&&(t.width=s),s>n&&(t.height=s),t;var a=i/n,c=e.util.limitDimsByArea(a,r),l=e.util.capValue,u=l(s,c.x,o),h=l(s,c.y,o);return i>u&&(t.zoomX/=i/u,t.width=u,t.capped=!0),n>h&&(t.zoomY/=n/h,t.height=h,t.capped=!0),t},_getCacheCanvasDimensions:function(){var t=this.getTotalObjectScaling(),e=this._getTransformedDimensions(0,0),r=e.x*t.scaleX/this.scaleX,i=e.y*t.scaleY/this.scaleY;return{width:r+c,height:i+c,zoomX:t.scaleX,zoomY:t.scaleY,x:r,y:i}},_updateCacheCanvas:function(){var t=this.canvas;if(this.noScaleCache&&t&&t._currentTransform){var r=t._currentTransform.target,i=t._currentTransform.action;if(this===r&&i.slice&&"scale"===i.slice(0,5))return!1}var n,o,s=this._cacheCanvas,a=this._limitCacheSize(this._getCacheCanvasDimensions()),c=e.minCacheSideLimit,l=a.width,u=a.height,h=a.zoomX,f=a.zoomY,d=l!==this.cacheWidth||u!==this.cacheHeight,g=this.zoomX!==h||this.zoomY!==f,p=d||g,v=0,m=0,b=!1;if(d){var y=this._cacheCanvas.width,w=this._cacheCanvas.height,x=l>y||u>w,C=(.9*y>l||.9*w>u)&&y>c&&w>c;b=x||C,x&&!a.capped&&(l>c||u>c)&&(v=.1*l,m=.1*u)}return this instanceof e.Text&&this.path&&(p=!0,b=!0,v+=this.getHeightOfLine(0)*this.zoomX,m+=this.getHeightOfLine(0)*this.zoomY),p?(b?(s.width=Math.ceil(l+v),s.height=Math.ceil(u+m)):(this._cacheContext.setTransform(1,0,0,1,0,0),this._cacheContext.clearRect(0,0,s.width,s.height)),n=a.x/2,o=a.y/2,this.cacheTranslationX=Math.round(s.width/2-n)+n,this.cacheTranslationY=Math.round(s.height/2-o)+o,this.cacheWidth=l,this.cacheHeight=u,this._cacheContext.translate(this.cacheTranslationX,this.cacheTranslationY),this._cacheContext.scale(h,f),this.zoomX=h,this.zoomY=f,!0):!1},setOptions:function(t){this._setOptions(t),this._initGradient(t.fill,"fill"),this._initGradient(t.stroke,"stroke"),this._initPattern(t.fill,"fill"),this._initPattern(t.stroke,"stroke")},transform:function(t){var e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,r=this.calcTransformMatrix(!e);t.transform(r[0],r[1],r[2],r[3],r[4],r[5])},toObject:function(t){var r=e.Object.NUM_FRACTION_DIGITS,i={type:this.type,version:e.version,originX:this.originX,originY:this.originY,left:n(this.left,r),top:n(this.top,r),width:n(this.width,r),height:n(this.height,r),fill:this.fill&&this.fill.toObject?this.fill.toObject():this.fill,stroke:this.stroke&&this.stroke.toObject?this.stroke.toObject():this.stroke,strokeWidth:n(this.strokeWidth,r),strokeDashArray:this.strokeDashArray?this.strokeDashArray.concat():this.strokeDashArray,strokeLineCap:this.strokeLineCap,strokeDashOffset:this.strokeDashOffset,strokeLineJoin:this.strokeLineJoin,strokeUniform:this.strokeUniform,strokeMiterLimit:n(this.strokeMiterLimit,r),scaleX:n(this.scaleX,r),scaleY:n(this.scaleY,r),angle:n(this.angle,r),flipX:this.flipX,flipY:this.flipY,opacity:n(this.opacity,r),shadow:this.shadow&&this.shadow.toObject?this.shadow.toObject():this.shadow,visible:this.visible,backgroundColor:this.backgroundColor,fillRule:this.fillRule,paintFirst:this.paintFirst,globalCompositeOperation:this.globalCompositeOperation,skewX:n(this.skewX,r),skewY:n(this.skewY,r)};return this.clipPath&&!this.clipPath.excludeFromExport&&(i.clipPath=this.clipPath.toObject(t),i.clipPath.inverted=this.clipPath.inverted,i.clipPath.absolutePositioned=this.clipPath.absolutePositioned),e.util.populateWithProperties(this,i,t),this.includeDefaultValues||(i=this._removeDefaultValues(i)),i},toDatalessObject:function(t){return this.toObject(t)},_removeDefaultValues:function(t){var r=e.util.getKlass(t.type).prototype,i=r.stateProperties;return i.forEach(function(e){"left"!==e&&"top"!==e&&(t[e]===r[e]&&delete t[e],Array.isArray(t[e])&&Array.isArray(r[e])&&0===t[e].length&&0===r[e].length&&delete t[e])}),t},toString:function(){return"#<fabric."+o(this.type)+">"},getObjectScaling:function(){if(!this.group)return{scaleX:this.scaleX,scaleY:this.scaleY};var t=e.util.qrDecompose(this.calcTransformMatrix());return{scaleX:Math.abs(t.scaleX),scaleY:Math.abs(t.scaleY)}},getTotalObjectScaling:function(){var t=this.getObjectScaling(),e=t.scaleX,r=t.scaleY;if(this.canvas){var i=this.canvas.getZoom(),n=this.canvas.getRetinaScaling();e*=i*n,r*=i*n}return{scaleX:e,scaleY:r}},getObjectOpacity:function(){var t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t},_set:function(t,r){var i="scaleX"===t||"scaleY"===t,n=this[t]!==r,o=!1;return i&&(r=this._constrainScale(r)),"scaleX"===t&&0>r?(this.flipX=!this.flipX,r*=-1):"scaleY"===t&&0>r?(this.flipY=!this.flipY,r*=-1):"shadow"!==t||!r||r instanceof e.Shadow?"dirty"===t&&this.group&&this.group.set("dirty",r):r=new e.Shadow(r),this[t]=r,n&&(o=this.group&&this.group.isOnACache(),this.cacheProperties.indexOf(t)>-1?(this.dirty=!0,o&&this.group.set("dirty",!0)):o&&this.stateProperties.indexOf(t)>-1&&this.group.set("dirty",!0)),this},setOnGroup:function(){},getViewportTransform:function(){return this.canvas&&this.canvas.viewportTransform?this.canvas.viewportTransform:e.iMatrix.concat()},isNotVisible:function(){return 0===this.opacity||!this.width&&!this.height&&0===this.strokeWidth||!this.visible},render:function(t){this.isNotVisible()||(!this.canvas||!this.canvas.skipOffscreen||this.group||this.isOnScreen())&&(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t,this),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.dirty=!1,this.drawObject(t),this.objectCaching&&this.statefullCache&&this.saveState({propertySet:"cacheProperties"})),t.restore())},renderCache:function(t){t=t||{},this._cacheCanvas&&this._cacheContext||this._createCacheCanvas(),this.isCacheDirty()&&(this.statefullCache&&this.saveState({propertySet:"cacheProperties"}),this.drawObject(this._cacheContext,t.forClipping),this.dirty=!1)},_removeCacheCanvas:function(){this._cacheCanvas=null,this._cacheContext=null,this.cacheWidth=0,this.cacheHeight=0},hasStroke:function(){return this.stroke&&"transparent"!==this.stroke&&0!==this.strokeWidth},hasFill:function(){return this.fill&&"transparent"!==this.fill},needsItsOwnCache:function(){return"stroke"===this.paintFirst&&this.hasFill()&&this.hasStroke()&&"object"==typeof this.shadow?!0:this.clipPath?!0:!1},shouldCache:function(){return this.ownCaching=this.needsItsOwnCache()||this.objectCaching&&(!this.group||!this.group.isOnACache()),this.ownCaching},willDrawShadow:function(){return!!this.shadow&&(0!==this.shadow.offsetX||0!==this.shadow.offsetY)},drawClipPathOnCache:function(t,r){if(t.save(),t.globalCompositeOperation=r.inverted?"destination-out":"destination-in",r.absolutePositioned){var i=e.util.invertTransform(this.calcTransformMatrix());t.transform(i[0],i[1],i[2],i[3],i[4],i[5])}r.transform(t),t.scale(1/r.zoomX,1/r.zoomY),t.drawImage(r._cacheCanvas,-r.cacheTranslationX,-r.cacheTranslationY),t.restore()},drawObject:function(t,e){var r=this.fill,i=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t,this.clipPath),this.fill=r,this.stroke=i},_drawClipPath:function(t,e){e&&(e.canvas=this.canvas,e.shouldCache(),e._transformDone=!0,e.renderCache({forClipping:!0}),this.drawClipPathOnCache(t,e))},drawCacheOnCanvas:function(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)},isCacheDirty:function(t){if(this.isNotVisible())return!1;if(this._cacheCanvas&&this._cacheContext&&!t&&this._updateCacheCanvas())return!0;if(this.dirty||this.clipPath&&this.clipPath.absolutePositioned||this.statefullCache&&this.hasStateChanged("cacheProperties")){if(this._cacheCanvas&&this._cacheContext&&!t){var e=this.cacheWidth/this.zoomX,r=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-e/2,-r/2,e,r)}return!0}return!1},_renderBackground:function(t){if(this.backgroundColor){var e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}},_setOpacity:function(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity},_setStrokeStyles:function(t,e){var r=e.stroke;r&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,r.toLive?"percentage"===r.gradientUnits||r.gradientTransform||r.patternTransform?this._applyPatternForTransformedGradient(t,r):(t.strokeStyle=r.toLive(t,this),this._applyPatternGradientTransform(t,r)):t.strokeStyle=e.stroke)},_setFillStyles:function(t,e){var r=e.fill;r&&(r.toLive?(t.fillStyle=r.toLive(t,this),this._applyPatternGradientTransform(t,e.fill)):t.fillStyle=r)},_setClippingProperties:function(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"},_setLineDash:function(t,e){e&&0!==e.length&&(1&e.length&&e.push.apply(e,e),t.setLineDash(e))},_renderControls:function(t,r){var i,n,o,a=this.getViewportTransform(),c=this.calcTransformMatrix();r=r||{},n="undefined"!=typeof r.hasBorders?r.hasBorders:this.hasBorders,o="undefined"!=typeof r.hasControls?r.hasControls:this.hasControls,c=e.util.multiplyTransformMatrices(a,c),i=e.util.qrDecompose(c),t.save(),t.translate(i.translateX,i.translateY),t.lineWidth=1*this.borderScaleFactor,this.group||(t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),this.flipX&&(i.angle-=180),t.rotate(s(this.group?i.angle:this.angle)),r.forActiveSelection||this.group?n&&this.drawBordersInGroup(t,i,r):n&&this.drawBorders(t,r),o&&this.drawControls(t,r),t.restore()},_setShadow:function(t){if(this.shadow){var r,i=this.shadow,n=this.canvas,o=n&&n.viewportTransform[0]||1,s=n&&n.viewportTransform[3]||1;r=i.nonScaling?{scaleX:1,scaleY:1}:this.getObjectScaling(),n&&n._isRetinaScaling()&&(o*=e.devicePixelRatio,s*=e.devicePixelRatio),t.shadowColor=i.color,t.shadowBlur=i.blur*e.browserShadowBlurConstant*(o+s)*(r.scaleX+r.scaleY)/4,t.shadowOffsetX=i.offsetX*o*r.scaleX,t.shadowOffsetY=i.offsetY*s*r.scaleY}},_removeShadow:function(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)},_applyPatternGradientTransform:function(t,e){if(!e||!e.toLive)return{offsetX:0,offsetY:0};var r=e.gradientTransform||e.patternTransform,i=-this.width/2+e.offsetX||0,n=-this.height/2+e.offsetY||0;return"percentage"===e.gradientUnits?t.transform(this.width,0,0,this.height,i,n):t.transform(1,0,0,1,i,n),r&&t.transform(r[0],r[1],r[2],r[3],r[4],r[5]),{offsetX:i,offsetY:n}},_renderPaintInOrder:function(t){"stroke"===this.paintFirst?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))},_render:function(){},_renderFill:function(t){this.fill&&(t.save(),this._setFillStyles(t,this),"evenodd"===this.fillRule?t.fill("evenodd"):t.fill(),t.restore())},_renderStroke:function(t){if(this.stroke&&0!==this.strokeWidth){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform&&this.group){var e=this.getObjectScaling();t.scale(1/e.scaleX,1/e.scaleY)}else this.strokeUniform&&t.scale(1/this.scaleX,1/this.scaleY);this._setLineDash(t,this.strokeDashArray),this._setStrokeStyles(t,this),t.stroke(),t.restore()}},_applyPatternForTransformedGradient:function(t,r){var i,n=this._limitCacheSize(this._getCacheCanvasDimensions()),o=e.util.createCanvasElement(),s=this.canvas.getRetinaScaling(),a=n.x/this.scaleX/s,c=n.y/this.scaleY/s;o.width=a,o.height=c,i=o.getContext("2d"),i.beginPath(),i.moveTo(0,0),i.lineTo(a,0),i.lineTo(a,c),i.lineTo(0,c),i.closePath(),i.translate(a/2,c/2),i.scale(n.zoomX/this.scaleX/s,n.zoomY/this.scaleY/s),this._applyPatternGradientTransform(i,r),i.fillStyle=r.toLive(t),i.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(s*this.scaleX/n.zoomX,s*this.scaleY/n.zoomY),t.strokeStyle=i.createPattern(o,"no-repeat")},_findCenterFromElement:function(){return{x:this.left+this.width/2,y:this.top+this.height/2}},_assignTransformMatrixProps:function(){if(this.transformMatrix){var t=e.util.qrDecompose(this.transformMatrix);this.flipX=!1,this.flipY=!1,this.set("scaleX",t.scaleX),this.set("scaleY",t.scaleY),this.angle=t.angle,this.skewX=t.skewX,this.skewY=0}},_removeTransformMatrix:function(t){var r=this._findCenterFromElement();this.transformMatrix&&(this._assignTransformMatrixProps(),r=e.util.transformPoint(r,this.transformMatrix)),this.transformMatrix=null,t&&(this.scaleX*=t.scaleX,this.scaleY*=t.scaleY,this.cropX=t.cropX,this.cropY=t.cropY,r.x+=t.offsetLeft,r.y+=t.offsetTop,this.width=t.width,this.height=t.height),this.setPositionByOrigin(r,"center","center")},clone:function(t,r){var i=this.toObject(r);this.constructor.fromObject?this.constructor.fromObject(i,t):e.Object._fromObject("Object",i,t)},cloneAsImage:function(t,r){var i=this.toCanvasElement(r);return t&&t(new e.Image(i)),this},toCanvasElement:function(t){t||(t={});var r=e.util,i=r.saveObjectTransform(this),n=this.group,o=this.shadow,s=Math.abs,a=(t.multiplier||1)*(t.enableRetinaScaling?e.devicePixelRatio:1);delete this.group,t.withoutTransform&&r.resetObjectTransform(this),t.withoutShadow&&(this.shadow=null);var c,l,u,h,f=e.util.createCanvasElement(),d=this.getBoundingRect(!0,!0),g=this.shadow,p={x:0,y:0};g&&(l=g.blur,c=g.nonScaling?{scaleX:1,scaleY:1}:this.getObjectScaling(),p.x=2*Math.round(s(g.offsetX)+l)*s(c.scaleX),p.y=2*Math.round(s(g.offsetY)+l)*s(c.scaleY)),u=d.width+p.x,h=d.height+p.y,f.width=Math.ceil(u),f.height=Math.ceil(h);var v=new e.StaticCanvas(f,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1});"jpeg"===t.format&&(v.backgroundColor="#fff"),this.setPositionByOrigin(new e.Point(v.width/2,v.height/2),"center","center");var m=this.canvas;v.add(this);var b=v.toCanvasElement(a||1,t);return this.shadow=o,this.set("canvas",m),n&&(this.group=n),this.set(i).setCoords(),v._objects=[],v.dispose(),v=null,b},toDataURL:function(t){return t||(t={}),e.util.toDataURL(this.toCanvasElement(t),t.format||"png",t.quality||1)},isType:function(t){return arguments.length>1?Array.from(arguments).includes(this.type):this.type===t},complexity:function(){return 1},toJSON:function(t){return this.toObject(t)},rotate:function(t){var e=("center"!==this.originX||"center"!==this.originY)&&this.centeredRotation;return e&&this._setOriginToCenter(),this.set("angle",t),e&&this._resetOrigin(),this},centerH:function(){return this.canvas&&this.canvas.centerObjectH(this),this},viewportCenterH:function(){return this.canvas&&this.canvas.viewportCenterObjectH(this),this},centerV:function(){return this.canvas&&this.canvas.centerObjectV(this),this},viewportCenterV:function(){return this.canvas&&this.canvas.viewportCenterObjectV(this),this},center:function(){return this.canvas&&this.canvas.centerObject(this),this},viewportCenter:function(){return this.canvas&&this.canvas.viewportCenterObject(this),this},getLocalPointer:function(t,r){r=r||this.canvas.getPointer(t);var i=new e.Point(r.x,r.y),n=this._getLeftTopCoords();return this.angle&&(i=e.util.rotatePoint(i,n,s(-this.angle))),{x:i.x-n.x,y:i.y-n.y}},_setupCompositeOperation:function(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)},dispose:function(){e.runningAnimations&&e.runningAnimations.cancelByTarget(this)}}),e.util.createAccessors&&e.util.createAccessors(e.Object),r(e.Object.prototype,e.Observable),e.Object.NUM_FRACTION_DIGITS=2,e.Object.ENLIVEN_PROPS=["clipPath"],e.Object._fromObject=function(t,r,n,o){var s=e[t];r=i(r,!0),e.util.enlivenPatterns([r.fill,r.stroke],function(t){"undefined"!=typeof t[0]&&(r.fill=t[0]),"undefined"!=typeof t[1]&&(r.stroke=t[1]),e.util.enlivenObjectEnlivables(r,r,function(){var t=o?new s(r[o],r):new s(r);n&&n(t)})})},e.Object.__uid=0)}("undefined"!=typeof exports?exports:this);!function(){var t=fabric.util.degreesToRadians,e={left:-.5,center:0,right:.5},i={top:-.5,center:0,bottom:.5};fabric.util.object.extend(fabric.Object.prototype,{translateToGivenOrigin:function(t,r,n,s,o){var a,c,l,h=t.x,u=t.y;return"string"==typeof r?r=e[r]:r-=.5,"string"==typeof s?s=e[s]:s-=.5,a=s-r,"string"==typeof n?n=i[n]:n-=.5,"string"==typeof o?o=i[o]:o-=.5,c=o-n,(a||c)&&(l=this._getTransformedDimensions(),h=t.x+a*l.x,u=t.y+c*l.y),new fabric.Point(h,u)},translateToCenterPoint:function(e,i,r){var n=this.translateToGivenOrigin(e,i,r,"center","center");return this.angle?fabric.util.rotatePoint(n,e,t(this.angle)):n},translateToOriginPoint:function(e,i,r){var n=this.translateToGivenOrigin(e,"center","center",i,r);return this.angle?fabric.util.rotatePoint(n,e,t(this.angle)):n},getCenterPoint:function(){var t=new fabric.Point(this.left,this.top);return this.translateToCenterPoint(t,this.originX,this.originY)},getPointByOrigin:function(t,e){var i=this.getCenterPoint();return this.translateToOriginPoint(i,t,e)},toLocalPoint:function(e,i,r){var n,s,o=this.getCenterPoint();return n="undefined"!=typeof i&&"undefined"!=typeof r?this.translateToGivenOrigin(o,"center","center",i,r):new fabric.Point(this.left,this.top),s=new fabric.Point(e.x,e.y),this.angle&&(s=fabric.util.rotatePoint(s,o,-t(this.angle))),s.subtractEquals(n)},setPositionByOrigin:function(t,e,i){var r=this.translateToCenterPoint(t,e,i),n=this.translateToOriginPoint(r,this.originX,this.originY);this.set("left",n.x),this.set("top",n.y)},adjustPosition:function(i){var r,n,s=t(this.angle),o=this.getScaledWidth(),a=fabric.util.cos(s)*o,c=fabric.util.sin(s)*o;r="string"==typeof this.originX?e[this.originX]:this.originX-.5,n="string"==typeof i?e[i]:i-.5,this.left+=a*(n-r),this.top+=c*(n-r),this.setCoords(),this.originX=i},_setOriginToCenter:function(){this._originalOriginX=this.originX,this._originalOriginY=this.originY;var t=this.getCenterPoint();this.originX="center",this.originY="center",this.left=t.x,this.top=t.y},_resetOrigin:function(){var t=this.translateToOriginPoint(this.getCenterPoint(),this._originalOriginX,this._originalOriginY);this.originX=this._originalOriginX,this.originY=this._originalOriginY,this.left=t.x,this.top=t.y,this._originalOriginX=null,this._originalOriginY=null},_getLeftTopCoords:function(){return this.translateToOriginPoint(this.getCenterPoint(),"left","top")}})}();!function(){function e(e){return[new fabric.Point(e.tl.x,e.tl.y),new fabric.Point(e.tr.x,e.tr.y),new fabric.Point(e.br.x,e.br.y),new fabric.Point(e.bl.x,e.bl.y)]}var t=fabric.util,r=t.degreesToRadians,n=t.multiplyTransformMatrices,i=t.transformPoint;t.object.extend(fabric.Object.prototype,{oCoords:null,aCoords:null,lineCoords:null,ownMatrixCache:null,matrixCache:null,controls:{},_getCoords:function(e,t){return t?e?this.calcACoords():this.calcLineCoords():(this.aCoords&&this.lineCoords||this.setCoords(!0),e?this.aCoords:this.lineCoords)},getCoords:function(t,r){return e(this._getCoords(t,r))},intersectsWithRect:function(e,t,r,n){var i=this.getCoords(r,n),o=fabric.Intersection.intersectPolygonRectangle(i,e,t);return"Intersection"===o.status},intersectsWithObject:function(e,t,r){var n=fabric.Intersection.intersectPolygonPolygon(this.getCoords(t,r),e.getCoords(t,r));return"Intersection"===n.status||e.isContainedWithinObject(this,t,r)||this.isContainedWithinObject(e,t,r)},isContainedWithinObject:function(e,t,r){for(var n=this.getCoords(t,r),i=t?e.aCoords:e.lineCoords,o=0,s=e._getImageLines(i);4>o;o++)if(!e.containsPoint(n[o],s))return!1;return!0},isContainedWithinRect:function(e,t,r,n){var i=this.getBoundingRect(r,n);return i.left>=e.x&&i.left+i.width<=t.x&&i.top>=e.y&&i.top+i.height<=t.y},containsPoint:function(e,t,r,n){var i=this._getCoords(r,n),t=t||this._getImageLines(i),o=this._findCrossPoints(e,t);return 0!==o&&o%2===1},isOnScreen:function(e){if(!this.canvas)return!1;var t=this.canvas.vptCoords.tl,r=this.canvas.vptCoords.br,n=this.getCoords(!0,e);return n.some(function(e){return e.x<=r.x&&e.x>=t.x&&e.y<=r.y&&e.y>=t.y})?!0:this.intersectsWithRect(t,r,!0,e)?!0:this._containsCenterOfCanvas(t,r,e)},_containsCenterOfCanvas:function(e,t,r){var n={x:(e.x+t.x)/2,y:(e.y+t.y)/2};return this.containsPoint(n,null,!0,r)?!0:!1},isPartiallyOnScreen:function(e){if(!this.canvas)return!1;var t=this.canvas.vptCoords.tl,r=this.canvas.vptCoords.br;if(this.intersectsWithRect(t,r,!0,e))return!0;var n=this.getCoords(!0,e).every(function(e){return(e.x>=r.x||e.x<=t.x)&&(e.y>=r.y||e.y<=t.y)});return n&&this._containsCenterOfCanvas(t,r,e)},_getImageLines:function(e){var t={topline:{o:e.tl,d:e.tr},rightline:{o:e.tr,d:e.br},bottomline:{o:e.br,d:e.bl},leftline:{o:e.bl,d:e.tl}};return t},_findCrossPoints:function(e,t){var r,n,i,o,s,a,c=0;for(var l in t)if(a=t[l],!(a.o.y<e.y&&a.d.y<e.y||a.o.y>=e.y&&a.d.y>=e.y||(a.o.x===a.d.x&&a.o.x>=e.x?s=a.o.x:(r=0,n=(a.d.y-a.o.y)/(a.d.x-a.o.x),i=e.y-r*e.x,o=a.o.y-n*a.o.x,s=-(i-o)/(r-n)),s>=e.x&&(c+=1),2!==c)))break;return c},getBoundingRect:function(e,r){var n=this.getCoords(e,r);return t.makeBoundingBoxFromPoints(n)},getScaledWidth:function(){return this._getTransformedDimensions().x},getScaledHeight:function(){return this._getTransformedDimensions().y},_constrainScale:function(e){return Math.abs(e)<this.minScaleLimit?0>e?-this.minScaleLimit:this.minScaleLimit:0===e?1e-4:e},scale:function(e){return this._set("scaleX",e),this._set("scaleY",e),this.setCoords()},scaleToWidth:function(e,t){var r=this.getBoundingRect(t).width/this.getScaledWidth();return this.scale(e/this.width/r)},scaleToHeight:function(e,t){var r=this.getBoundingRect(t).height/this.getScaledHeight();return this.scale(e/this.height/r)},calcLineCoords:function(){var e=this.getViewportTransform(),n=this.padding,o=r(this.angle),s=t.cos(o),a=t.sin(o),c=s*n,l=a*n,u=c+l,f=c-l,h=this.calcACoords(),d={tl:i(h.tl,e),tr:i(h.tr,e),bl:i(h.bl,e),br:i(h.br,e)};return n&&(d.tl.x-=f,d.tl.y-=u,d.tr.x+=u,d.tr.y-=f,d.bl.x-=u,d.bl.y+=f,d.br.x+=f,d.br.y+=u),d},calcOCoords:function(){var e=this._calcRotateMatrix(),t=this._calcTranslateMatrix(),r=this.getViewportTransform(),i=n(r,t),o=n(i,e),o=n(o,[1/r[0],0,0,1/r[3],0,0]),s=this._calculateCurrentDimensions(),a={};return this.forEachControl(function(e,t,r){a[t]=e.positionHandler(s,o,r)}),a},calcACoords:function(){var e=this._calcRotateMatrix(),t=this._calcTranslateMatrix(),r=n(t,e),o=this._getTransformedDimensions(),s=o.x/2,a=o.y/2;return{tl:i({x:-s,y:-a},r),tr:i({x:s,y:-a},r),bl:i({x:-s,y:a},r),br:i({x:s,y:a},r)}},setCoords:function(e){return this.aCoords=this.calcACoords(),this.lineCoords=this.group?this.aCoords:this.calcLineCoords(),e?this:(this.oCoords=this.calcOCoords(),this._setCornerCoords&&this._setCornerCoords(),this)},_calcRotateMatrix:function(){return t.calcRotateMatrix(this)},_calcTranslateMatrix:function(){var e=this.getCenterPoint();return[1,0,0,1,e.x,e.y]},transformMatrixKey:function(e){var t="_",r="";return!e&&this.group&&(r=this.group.transformMatrixKey(e)+t),r+this.top+t+this.left+t+this.scaleX+t+this.scaleY+t+this.skewX+t+this.skewY+t+this.angle+t+this.originX+t+this.originY+t+this.width+t+this.height+t+this.strokeWidth+this.flipX+this.flipY},calcTransformMatrix:function(e){var t=this.calcOwnMatrix();if(e||!this.group)return t;var r=this.transformMatrixKey(e),i=this.matrixCache||(this.matrixCache={});return i.key===r?i.value:(this.group&&(t=n(this.group.calcTransformMatrix(!1),t)),i.key=r,i.value=t,t)},calcOwnMatrix:function(){var e=this.transformMatrixKey(!0),r=this.ownMatrixCache||(this.ownMatrixCache={});if(r.key===e)return r.value;var n=this._calcTranslateMatrix(),i={angle:this.angle,translateX:n[4],translateY:n[5],scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY};return r.key=e,r.value=t.composeMatrix(i),r.value},_getNonTransformedDimensions:function(){var e=this.strokeWidth,t=this.width+e,r=this.height+e;return{x:t,y:r}},_getTransformedDimensions:function(e,r){"undefined"==typeof e&&(e=this.skewX),"undefined"==typeof r&&(r=this.skewY);var n,i,o,s=0===e&&0===r;if(this.strokeUniform?(i=this.width,o=this.height):(n=this._getNonTransformedDimensions(),i=n.x,o=n.y),s)return this._finalizeDimensions(i*this.scaleX,o*this.scaleY);var a=t.sizeAfterTransform(i,o,{scaleX:this.scaleX,scaleY:this.scaleY,skewX:e,skewY:r});return this._finalizeDimensions(a.x,a.y)},_finalizeDimensions:function(e,t){return this.strokeUniform?{x:e+this.strokeWidth,y:t+this.strokeWidth}:{x:e,y:t}},_calculateCurrentDimensions:function(){var e=this.getViewportTransform(),t=this._getTransformedDimensions(),r=i(t,e,!0);return r.scalarAdd(2*this.padding)}})}();fabric.util.object.extend(fabric.Object.prototype,{sendToBack:function(){return this.group?fabric.StaticCanvas.prototype.sendToBack.call(this.group,this):this.canvas&&this.canvas.sendToBack(this),this},bringToFront:function(){return this.group?fabric.StaticCanvas.prototype.bringToFront.call(this.group,this):this.canvas&&this.canvas.bringToFront(this),this},sendBackwards:function(e){return this.group?fabric.StaticCanvas.prototype.sendBackwards.call(this.group,this,e):this.canvas&&this.canvas.sendBackwards(this,e),this},bringForward:function(e){return this.group?fabric.StaticCanvas.prototype.bringForward.call(this.group,this,e):this.canvas&&this.canvas.bringForward(this,e),this},moveTo:function(e){return this.group&&"activeSelection"!==this.group.type?fabric.StaticCanvas.prototype.moveTo.call(this.group,this,e):this.canvas&&this.canvas.moveTo(this,e),this}});!function(){function t(t,e,r){var n={},s=!0;r.forEach(function(e){n[e]=t[e]}),i(t[e],n,s)}function e(t,i,r){if(t===i)return!0;if(Array.isArray(t)){if(!Array.isArray(i)||t.length!==i.length)return!1;for(var n=0,s=t.length;s>n;n++)if(!e(t[n],i[n]))return!1;return!0}if(t&&"object"==typeof t){var o,a=Object.keys(t);if(!i||"object"!=typeof i||!r&&a.length!==Object.keys(i).length)return!1;for(var n=0,s=a.length;s>n;n++)if(o=a[n],"canvas"!==o&&"group"!==o&&!e(t[o],i[o]))return!1;return!0}}var i=fabric.util.object.extend,r="stateProperties";fabric.util.object.extend(fabric.Object.prototype,{hasStateChanged:function(t){t=t||r;var i="_"+t;return Object.keys(this[i]).length<this[t].length?!0:!e(this[i],this,!0)},saveState:function(e){var i=e&&e.propertySet||r,n="_"+i;return this[n]?(t(this,n,this[i]),e&&e.stateProperties&&t(this,n,e.stateProperties),this):this.setupState(e)},setupState:function(t){t=t||{};var e=t.propertySet||r;return t.propertySet=e,this["_"+e]={},this.saveState(t),this}})}();!function(){var t=fabric.util.degreesToRadians;fabric.util.object.extend(fabric.Object.prototype,{_findTargetCorner:function(t,e){if(!this.hasControls||this.group||!this.canvas||this.canvas._activeObject!==this)return!1;var i,r,n,s=t.x,o=t.y,a=Object.keys(this.oCoords),c=a.length-1;for(this.__corner=0;c>=0;c--)if(n=a[c],this.isControlVisible(n)&&(r=this._getImageLines(e?this.oCoords[n].touchCorner:this.oCoords[n].corner),i=this._findCrossPoints({x:s,y:o},r),0!==i&&i%2===1))return this.__corner=n,n;return!1},forEachControl:function(t){for(var e in this.controls)t(this.controls[e],e,this)},_setCornerCoords:function(){var t=this.oCoords;for(var e in t){var i=this.controls[e];t[e].corner=i.calcCornerCoords(this.angle,this.cornerSize,t[e].x,t[e].y,!1),t[e].touchCorner=i.calcCornerCoords(this.angle,this.touchCornerSize,t[e].x,t[e].y,!0)}},drawSelectionBackground:function(e){if(!this.selectionBackgroundColor||this.canvas&&!this.canvas.interactive||this.canvas&&this.canvas._activeObject!==this)return this;e.save();var i=this.getCenterPoint(),r=this._calculateCurrentDimensions(),n=this.canvas.viewportTransform;return e.translate(i.x,i.y),e.scale(1/n[0],1/n[3]),e.rotate(t(this.angle)),e.fillStyle=this.selectionBackgroundColor,e.fillRect(-r.x/2,-r.y/2,r.x,r.y),e.restore(),this},drawBorders:function(t,e){e=e||{};var i=this._calculateCurrentDimensions(),r=this.borderScaleFactor,n=i.x+r,s=i.y+r,o="undefined"!=typeof e.hasControls?e.hasControls:this.hasControls,a=!1;return t.save(),t.strokeStyle=e.borderColor||this.borderColor,this._setLineDash(t,e.borderDashArray||this.borderDashArray),t.strokeRect(-n/2,-s/2,n,s),o&&(t.beginPath(),this.forEachControl(function(e,i,r){e.withConnection&&e.getVisibility(r,i)&&(a=!0,t.moveTo(e.x*n,e.y*s),t.lineTo(e.x*n+e.offsetX,e.y*s+e.offsetY))}),a&&t.stroke()),t.restore(),this},drawBordersInGroup:function(t,e,i){i=i||{};var r=fabric.util.sizeAfterTransform(this.width,this.height,e),n=this.strokeWidth,s=this.strokeUniform,o=this.borderScaleFactor,a=r.x+n*(s?this.canvas.getZoom():e.scaleX)+o,c=r.y+n*(s?this.canvas.getZoom():e.scaleY)+o;return t.save(),this._setLineDash(t,i.borderDashArray||this.borderDashArray),t.strokeStyle=i.borderColor||this.borderColor,t.strokeRect(-a/2,-c/2,a,c),t.restore(),this},drawControls:function(t,e){e=e||{},t.save();var i,r,n=this.canvas.getRetinaScaling();return t.setTransform(n,0,0,n,0,0),t.strokeStyle=t.fillStyle=e.cornerColor||this.cornerColor,this.transparentCorners||(t.strokeStyle=e.cornerStrokeColor||this.cornerStrokeColor),this._setLineDash(t,e.cornerDashArray||this.cornerDashArray),this.setCoords(),this.group&&(i=this.group.calcTransformMatrix()),this.forEachControl(function(n,s,o){r=o.oCoords[s],n.getVisibility(o,s)&&(i&&(r=fabric.util.transformPoint(r,i)),n.render(t,r.x,r.y,e,o))}),t.restore(),this},isControlVisible:function(t){return this.controls[t]&&this.controls[t].getVisibility(this,t)},setControlVisible:function(t,e){return this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[t]=e,this},setControlsVisibility:function(t){t||(t={});for(var e in t)this.setControlVisible(e,t[e]);return this},onDeselect:function(){},onSelect:function(){}})}();fabric.util.object.extend(fabric.StaticCanvas.prototype,{FX_DURATION:500,fxCenterObjectH:function(t,e){e=e||{};var i=function(){},r=e.onComplete||i,n=e.onChange||i,o=this;return fabric.util.animate({target:this,startValue:t.left,endValue:this.getCenterPoint().x,duration:this.FX_DURATION,onChange:function(e){t.set("left",e),o.requestRenderAll(),n()},onComplete:function(){t.setCoords(),r()}})},fxCenterObjectV:function(t,e){e=e||{};var i=function(){},r=e.onComplete||i,n=e.onChange||i,o=this;return fabric.util.animate({target:this,startValue:t.top,endValue:this.getCenterPoint().y,duration:this.FX_DURATION,onChange:function(e){t.set("top",e),o.requestRenderAll(),n()},onComplete:function(){t.setCoords(),r()}})},fxRemove:function(t,e){e=e||{};var i=function(){},r=e.onComplete||i,n=e.onChange||i,o=this;return fabric.util.animate({target:this,startValue:t.opacity,endValue:0,duration:this.FX_DURATION,onChange:function(e){t.set("opacity",e),o.requestRenderAll(),n()},onComplete:function(){o.remove(t),r()}})}}),fabric.util.object.extend(fabric.Object.prototype,{animate:function(){if(arguments[0]&&"object"==typeof arguments[0]){var t,e,i=[],r=[];for(t in arguments[0])i.push(t);for(var n=0,o=i.length;o>n;n++)t=i[n],e=n!==o-1,r.push(this._animate(t,arguments[0][t],arguments[1],e));return r}return this._animate.apply(this,arguments)},_animate:function(t,e,i,r){var n,o=this;e=e.toString(),i=i?fabric.util.object.clone(i):{},~t.indexOf(".")&&(n=t.split("."));var s=o.colorProperties.indexOf(t)>-1||n&&o.colorProperties.indexOf(n[1])>-1,a=n?this.get(n[0])[n[1]]:this.get(t);"from"in i||(i.from=a),s||(e=~e.indexOf("=")?a+parseFloat(e.replace("=","")):parseFloat(e));var c={target:this,startValue:i.from,endValue:e,byValue:i.by,easing:i.easing,duration:i.duration,abort:i.abort&&function(t,e,r){return i.abort.call(o,t,e,r)},onChange:function(e,s,a){n?o[n[0]][n[1]]=e:o.set(t,e),r||i.onChange&&i.onChange(e,s,a)},onComplete:function(t,e,n){r||(o.setCoords(),i.onComplete&&i.onComplete(t,e,n))}};return s?fabric.util.animateColor(c.startValue,c.endValue,c.duration,c):fabric.util.animate(c)}});!function(t){"use strict";function e(t,e){var i=t.origin,r=t.axis1,n=t.axis2,s=t.dimension,o=e.nearest,a=e.center,c=e.farthest;return function(){switch(this.get(i)){case o:return Math.min(this.get(r),this.get(n));case a:return Math.min(this.get(r),this.get(n))+.5*this.get(s);case c:return Math.max(this.get(r),this.get(n))}}}var i=t.fabric||(t.fabric={}),r=i.util.object.extend,n=i.util.object.clone,s={x1:1,x2:1,y1:1,y2:1};return i.Line?void i.warn("fabric.Line is already defined"):(i.Line=i.util.createClass(i.Object,{type:"line",x1:0,y1:0,x2:0,y2:0,cacheProperties:i.Object.prototype.cacheProperties.concat("x1","x2","y1","y2"),initialize:function(t,e){t||(t=[0,0,0,0]),this.callSuper("initialize",e),this.set("x1",t[0]),this.set("y1",t[1]),this.set("x2",t[2]),this.set("y2",t[3]),this._setWidthHeight(e)},_setWidthHeight:function(t){t||(t={}),this.width=Math.abs(this.x2-this.x1),this.height=Math.abs(this.y2-this.y1),this.left="left"in t?t.left:this._getLeftToOriginX(),this.top="top"in t?t.top:this._getTopToOriginY()},_set:function(t,e){return this.callSuper("_set",t,e),"undefined"!=typeof s[t]&&this._setWidthHeight(),this},_getLeftToOriginX:e({origin:"originX",axis1:"x1",axis2:"x2",dimension:"width"},{nearest:"left",center:"center",farthest:"right"}),_getTopToOriginY:e({origin:"originY",axis1:"y1",axis2:"y2",dimension:"height"},{nearest:"top",center:"center",farthest:"bottom"}),_render:function(t){t.beginPath();var e=this.calcLinePoints();t.moveTo(e.x1,e.y1),t.lineTo(e.x2,e.y2),t.lineWidth=this.strokeWidth;var i=t.strokeStyle;t.strokeStyle=this.stroke||t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=i},_findCenterFromElement:function(){return{x:(this.x1+this.x2)/2,y:(this.y1+this.y2)/2}},toObject:function(t){return r(this.callSuper("toObject",t),this.calcLinePoints())},_getNonTransformedDimensions:function(){var t=this.callSuper("_getNonTransformedDimensions");return"butt"===this.strokeLineCap&&(0===this.width&&(t.y-=this.strokeWidth),0===this.height&&(t.x-=this.strokeWidth)),t},calcLinePoints:function(){var t=this.x1<=this.x2?-1:1,e=this.y1<=this.y2?-1:1,i=t*this.width*.5,r=e*this.height*.5,n=t*this.width*-.5,s=e*this.height*-.5;return{x1:i,x2:n,y1:r,y2:s}}}),void(i.Line.fromObject=function(t,e){function r(t){delete t.points,e&&e(t)}var s=n(t,!0);s.points=[t.x1,t.y1,t.x2,t.y2],i.Object._fromObject("Line",s,r,"points")}))}("undefined"!=typeof exports?exports:this);!function(e){"use strict";var t=e.fabric||(e.fabric={}),r=t.util.degreesToRadians;return t.Circle?void t.warn("fabric.Circle is already defined."):(t.Circle=t.util.createClass(t.Object,{type:"circle",radius:0,startAngle:0,endAngle:360,cacheProperties:t.Object.prototype.cacheProperties.concat("radius","startAngle","endAngle"),_set:function(e,t){return this.callSuper("_set",e,t),"radius"===e&&this.setRadius(t),this},toObject:function(e){return this.callSuper("toObject",["radius","startAngle","endAngle"].concat(e))},_render:function(e){e.beginPath(),e.arc(0,0,this.radius,r(this.startAngle),r(this.endAngle),!1),this._renderPaintInOrder(e)},getRadiusX:function(){return this.get("radius")*this.get("scaleX")},getRadiusY:function(){return this.get("radius")*this.get("scaleY")},setRadius:function(e){return this.radius=e,this.set("width",2*e).set("height",2*e)}}),void(t.Circle.fromObject=function(e,r){t.Object._fromObject("Circle",e,r)}))}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={});return e.Triangle?void e.warn("fabric.Triangle is already defined"):(e.Triangle=e.util.createClass(e.Object,{type:"triangle",width:100,height:100,_render:function(t){var e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,i),t.lineTo(0,-i),t.lineTo(e,i),t.closePath(),this._renderPaintInOrder(t)}}),void(e.Triangle.fromObject=function(t,i){return e.Object._fromObject("Triangle",t,i)}))}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=2*Math.PI;return e.Ellipse?void e.warn("fabric.Ellipse is already defined."):(e.Ellipse=e.util.createClass(e.Object,{type:"ellipse",rx:0,ry:0,cacheProperties:e.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(t){this.callSuper("initialize",t),this.set("rx",t&&t.rx||0),this.set("ry",t&&t.ry||0)},_set:function(t,e){switch(this.callSuper("_set",t,e),t){case"rx":this.rx=e,this.set("width",2*e);break;case"ry":this.ry=e,this.set("height",2*e)}return this},getRx:function(){return this.get("rx")*this.get("scaleX")},getRy:function(){return this.get("ry")*this.get("scaleY")},toObject:function(t){return this.callSuper("toObject",["rx","ry"].concat(t))},_render:function(t){t.beginPath(),t.save(),t.transform(1,0,0,this.ry/this.rx,0,0),t.arc(0,0,this.rx,0,i,!1),t.restore(),this._renderPaintInOrder(t)}}),void(e.Ellipse.fromObject=function(t,i){e.Object._fromObject("Ellipse",t,i)}))}("undefined"!=typeof exports?exports:this);!function(e){"use strict";{var t=e.fabric||(e.fabric={});t.util.object.extend}return t.Rect?void t.warn("fabric.Rect is already defined"):(t.Rect=t.util.createClass(t.Object,{stateProperties:t.Object.prototype.stateProperties.concat("rx","ry"),type:"rect",rx:0,ry:0,cacheProperties:t.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(e){this.callSuper("initialize",e),this._initRxRy()},_initRxRy:function(){this.rx&&!this.ry?this.ry=this.rx:this.ry&&!this.rx&&(this.rx=this.ry)},_render:function(e){var t=this.rx?Math.min(this.rx,this.width/2):0,r=this.ry?Math.min(this.ry,this.height/2):0,n=this.width,i=this.height,o=-this.width/2,s=-this.height/2,a=0!==t||0!==r,c=.4477152502;e.beginPath(),e.moveTo(o+t,s),e.lineTo(o+n-t,s),a&&e.bezierCurveTo(o+n-c*t,s,o+n,s+c*r,o+n,s+r),e.lineTo(o+n,s+i-r),a&&e.bezierCurveTo(o+n,s+i-c*r,o+n-c*t,s+i,o+n-t,s+i),e.lineTo(o+t,s+i),a&&e.bezierCurveTo(o+c*t,s+i,o,s+i-c*r,o,s+i-r),e.lineTo(o,s+r),a&&e.bezierCurveTo(o,s+c*r,o+c*t,s,o+t,s),e.closePath(),this._renderPaintInOrder(e)},toObject:function(e){return this.callSuper("toObject",["rx","ry"].concat(e))}}),void(t.Rect.fromObject=function(e,r){return t.Object._fromObject("Rect",e,r)}))}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,r=e.util.array.min,n=e.util.array.max,s=(e.util.toFixed,e.util.projectStrokeOnPoints);return e.Polyline?void e.warn("fabric.Polyline is already defined"):(e.Polyline=e.util.createClass(e.Object,{type:"polyline",points:null,exactBoundingBox:!1,cacheProperties:e.Object.prototype.cacheProperties.concat("points"),initialize:function(t,e){e=e||{},this.points=t||[],this.callSuper("initialize",e),this._setPositionDimensions(e)},_projectStrokeOnPoints:function(){return s(this.points,this,!0)},_setPositionDimensions:function(t){var e,i=this._calcDimensions(t),r=this.exactBoundingBox?this.strokeWidth:0;this.width=i.width-r,this.height=i.height-r,t.fromSVG||(e=this.translateToGivenOrigin({x:i.left-this.strokeWidth/2+r/2,y:i.top-this.strokeWidth/2+r/2},"left","top",this.originX,this.originY)),"undefined"==typeof t.left&&(this.left=t.fromSVG?i.left:e.x),"undefined"==typeof t.top&&(this.top=t.fromSVG?i.top:e.y),this.pathOffset={x:i.left+this.width/2+r/2,y:i.top+this.height/2+r/2}},_calcDimensions:function(){var t=this.exactBoundingBox?this._projectStrokeOnPoints():this.points,e=r(t,"x")||0,i=r(t,"y")||0,s=n(t,"x")||0,o=n(t,"y")||0,a=s-e,c=o-i;return{left:e,top:i,width:a,height:c}},toObject:function(t){return i(this.callSuper("toObject",t),{points:this.points.concat()})},commonRender:function(t){var e,i=this.points.length,r=this.pathOffset.x,n=this.pathOffset.y;if(!i||isNaN(this.points[i-1].y))return!1;t.beginPath(),t.moveTo(this.points[0].x-r,this.points[0].y-n);for(var s=0;i>s;s++)e=this.points[s],t.lineTo(e.x-r,e.y-n);return!0},_render:function(t){this.commonRender(t)&&this._renderPaintInOrder(t)},complexity:function(){return this.get("points").length}}),void(e.Polyline.fromObject=function(t,i){return e.Object._fromObject("Polyline",t,i,"points")}))}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.projectStrokeOnPoints;return e.Polygon?void e.warn("fabric.Polygon is already defined"):(e.Polygon=e.util.createClass(e.Polyline,{type:"polygon",_projectStrokeOnPoints:function(){return i(this.points,this)},_render:function(t){this.commonRender(t)&&(t.closePath(),this._renderPaintInOrder(t))}}),void(e.Polygon.fromObject=function(t,i){e.Object._fromObject("Polygon",t,i,"points")}))}("undefined"!=typeof exports?exports:this);!function(t){"use strict";{var e=t.fabric||(t.fabric={}),r=e.util.array.min,i=e.util.array.max,n=e.util.object.extend,o=e.util.object.clone;e.util.toFixed}return e.Path?void e.warn("fabric.Path is already defined"):(e.Path=e.util.createClass(e.Object,{type:"path",path:null,cacheProperties:e.Object.prototype.cacheProperties.concat("path","fillRule"),stateProperties:e.Object.prototype.stateProperties.concat("path"),initialize:function(t,e){e=o(e||{}),delete e.path,this.callSuper("initialize",e),this._setPath(t||[],e)},_setPath:function(t,r){this.path=e.util.makePathSimpler(Array.isArray(t)?t:e.util.parsePath(t)),e.Polyline.prototype._setPositionDimensions.call(this,r||{})},_renderPathCommands:function(t){var e,r=0,i=0,n=0,o=0,s=0,a=0,c=-this.pathOffset.x,l=-this.pathOffset.y;t.beginPath();for(var u=0,h=this.path.length;h>u;++u)switch(e=this.path[u],e[0]){case"L":n=e[1],o=e[2],t.lineTo(n+c,o+l);break;case"M":n=e[1],o=e[2],r=n,i=o,t.moveTo(n+c,o+l);break;case"C":n=e[5],o=e[6],s=e[3],a=e[4],t.bezierCurveTo(e[1]+c,e[2]+l,s+c,a+l,n+c,o+l);break;case"Q":t.quadraticCurveTo(e[1]+c,e[2]+l,e[3]+c,e[4]+l),n=e[3],o=e[4],s=e[1],a=e[2];break;case"z":case"Z":n=r,o=i,t.closePath()}},_render:function(t){this._renderPathCommands(t),this._renderPaintInOrder(t)},toString:function(){return"#<fabric.Path ("+this.complexity()+'): { "top": '+this.top+', "left": '+this.left+" }>"},toObject:function(t){return n(this.callSuper("toObject",t),{path:this.path.map(function(t){return t.slice()})})},toDatalessObject:function(t){var e=this.toObject(["sourcePath"].concat(t));return e.sourcePath&&delete e.path,e},complexity:function(){return this.path.length},_calcDimensions:function(){for(var t,n,o=[],s=[],a=0,c=0,l=0,u=0,h=0,f=this.path.length;f>h;++h){switch(t=this.path[h],t[0]){case"L":l=t[1],u=t[2],n=[];break;case"M":l=t[1],u=t[2],a=l,c=u,n=[];break;case"C":n=e.util.getBoundsOfCurve(l,u,t[1],t[2],t[3],t[4],t[5],t[6]),l=t[5],u=t[6];break;case"Q":n=e.util.getBoundsOfCurve(l,u,t[1],t[2],t[1],t[2],t[3],t[4]),l=t[3],u=t[4];break;case"z":case"Z":l=a,u=c}n.forEach(function(t){o.push(t.x),s.push(t.y)}),o.push(l),s.push(u)}var d=r(o)||0,g=r(s)||0,p=i(o)||0,v=i(s)||0,m=p-d,b=v-g;return{left:d,top:g,width:m,height:b}}}),void(e.Path.fromObject=function(t,r){if("string"==typeof t.sourcePath){var i=t.sourcePath;e.loadSVGFromURL(i,function(e){var i=e[0];i.setOptions(t),r&&r(i)})}else e.Object._fromObject("Path",t,r,"path")}))}("undefined"!=typeof exports?exports:this);!function(e){"use strict";var t=e.fabric||(e.fabric={}),r=t.util.array.min,n=t.util.array.max;t.Group||(t.Group=t.util.createClass(t.Object,t.Collection,{type:"group",strokeWidth:0,subTargetCheck:!1,cacheProperties:[],useSetOnGroup:!1,initialize:function(e,t,r){t=t||{},this._objects=[],r&&this.callSuper("initialize",t),this._objects=e||[];for(var n=this._objects.length;n--;)this._objects[n].group=this;if(r)this._updateObjectsACoords();else{var i=t&&t.centerPoint;void 0!==t.originX&&(this.originX=t.originX),void 0!==t.originY&&(this.originY=t.originY),i||this._calcBounds(),this._updateObjectsCoords(i),delete t.centerPoint,this.callSuper("initialize",t)}this.setCoords()},_updateObjectsACoords:function(){for(var e=!0,t=this._objects.length;t--;)this._objects[t].setCoords(e)},_updateObjectsCoords:function(e){for(var e=e||this.getCenterPoint(),t=this._objects.length;t--;)this._updateObjectCoords(this._objects[t],e)},_updateObjectCoords:function(e,t){var r=e.left,n=e.top,i=!0;e.set({left:r-t.x,top:n-t.y}),e.group=this,e.setCoords(i)},toString:function(){return"#<fabric.Group: ("+this.complexity()+")>"},addWithUpdate:function(e){var r=!!this.group;return this._restoreObjectsState(),t.util.resetObjectTransform(this),e&&(r&&t.util.removeTransformFromObject(e,this.group.calcTransformMatrix()),this._objects.push(e),e.group=this,e._set("canvas",this.canvas)),this._calcBounds(),this._updateObjectsCoords(),this.dirty=!0,r?this.group.addWithUpdate():this.setCoords(),this},removeWithUpdate:function(e){return this._restoreObjectsState(),t.util.resetObjectTransform(this),this.remove(e),this._calcBounds(),this._updateObjectsCoords(),this.setCoords(),this.dirty=!0,this},_onObjectAdded:function(e){this.dirty=!0,e.group=this,e._set("canvas",this.canvas)},_onObjectRemoved:function(e){this.dirty=!0,delete e.group},_set:function(e,r){var n=this._objects.length;if(this.useSetOnGroup)for(;n--;)this._objects[n].setOnGroup(e,r);if("canvas"===e)for(;n--;)this._objects[n]._set(e,r);t.Object.prototype._set.call(this,e,r)},toObject:function(e){var r=this.includeDefaultValues,n=this._objects.filter(function(e){return!e.excludeFromExport}).map(function(t){var n=t.includeDefaultValues;t.includeDefaultValues=r;var i=t.toObject(e);return t.includeDefaultValues=n,i}),i=t.Object.prototype.toObject.call(this,e);return i.objects=n,i},toDatalessObject:function(e){var r,n=this.sourcePath;if(n)r=n;else{var i=this.includeDefaultValues;r=this._objects.map(function(t){var r=t.includeDefaultValues;t.includeDefaultValues=i;var n=t.toDatalessObject(e);return t.includeDefaultValues=r,n})}var o=t.Object.prototype.toDatalessObject.call(this,e);return o.objects=r,o},render:function(e){this._transformDone=!0,this.callSuper("render",e),this._transformDone=!1},shouldCache:function(){var e=t.Object.prototype.shouldCache.call(this);if(e)for(var r=0,n=this._objects.length;n>r;r++)if(this._objects[r].willDrawShadow())return this.ownCaching=!1,!1;return e},willDrawShadow:function(){if(t.Object.prototype.willDrawShadow.call(this))return!0;for(var e=0,r=this._objects.length;r>e;e++)if(this._objects[e].willDrawShadow())return!0;return!1},isOnACache:function(){return this.ownCaching||this.group&&this.group.isOnACache()},drawObject:function(e){for(var t=0,r=this._objects.length;r>t;t++)this._objects[t].render(e);this._drawClipPath(e,this.clipPath)},isCacheDirty:function(e){if(this.callSuper("isCacheDirty",e))return!0;if(!this.statefullCache)return!1;for(var t=0,r=this._objects.length;r>t;t++)if(this._objects[t].isCacheDirty(!0)){if(this._cacheCanvas){var n=this.cacheWidth/this.zoomX,i=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-n/2,-i/2,n,i)}return!0}return!1},_restoreObjectsState:function(){var e=this.calcOwnMatrix();return this._objects.forEach(function(r){t.util.addTransformToObject(r,e),delete r.group,r.setCoords()}),this},destroy:function(){return this._objects.forEach(function(e){e.set("dirty",!0)}),this._restoreObjectsState()},dispose:function(){this.callSuper("dispose"),this.forEachObject(function(e){e.dispose&&e.dispose()}),this._objects=[]},toActiveSelection:function(){if(this.canvas){var e=this._objects,r=this.canvas;this._objects=[];var n=this.toObject();delete n.objects;var i=new t.ActiveSelection([]);return i.set(n),i.type="activeSelection",r.remove(this),e.forEach(function(e){e.group=i,e.dirty=!0,r.add(e)}),i.canvas=r,i._objects=e,r._activeObject=i,i.setCoords(),i}},ungroupOnCanvas:function(){return this._restoreObjectsState()},setObjectsCoords:function(){var e=!0;return this.forEachObject(function(t){t.setCoords(e)}),this},_calcBounds:function(e){for(var t,r,n,i,o=[],s=[],a=["tr","br","bl","tl"],c=0,l=this._objects.length,u=a.length;l>c;++c){for(t=this._objects[c],n=t.calcACoords(),i=0;u>i;i++)r=a[i],o.push(n[r].x),s.push(n[r].y);t.aCoords=n}this._getBounds(o,s,e)},_getBounds:function(e,i,o){var s=new t.Point(r(e),r(i)),a=new t.Point(n(e),n(i)),c=s.y||0,l=s.x||0,u=a.x-s.x||0,f=a.y-s.y||0;this.width=u,this.height=f,o||this.setPositionByOrigin({x:l,y:c},"left","top")}}),t.Group.fromObject=function(e,r){var n=e.objects,i=t.util.object.clone(e,!0);return delete i.objects,"string"==typeof n?void t.loadSVGFromURL(n,function(o){var s=t.util.groupSVGElements(o,e,n);s.set(i),r&&r(s)}):void t.util.enlivenObjects(n,function(n){var i=t.util.object.clone(e,!0);delete i.objects,t.util.enlivenObjectEnlivables(e,i,function(){r&&r(new t.Group(n,i,!0))})})})}("undefined"!=typeof exports?exports:this);!function(e){"use strict";var t=e.fabric||(e.fabric={});t.ActiveSelection||(t.ActiveSelection=t.util.createClass(t.Group,{type:"activeSelection",initialize:function(e,r){r=r||{},this._objects=e||[];for(var n=this._objects.length;n--;)this._objects[n].group=this;r.originX&&(this.originX=r.originX),r.originY&&(this.originY=r.originY),this._calcBounds(),this._updateObjectsCoords(),t.Object.prototype.initialize.call(this,r),this.setCoords()},toGroup:function(){var e=this._objects.concat();this._objects=[];var r=t.Object.prototype.toObject.call(this),n=new t.Group([]);if(delete r.type,n.set(r),e.forEach(function(e){e.canvas.remove(e),e.group=n}),n._objects=e,!this.canvas)return n;var i=this.canvas;return i.add(n),i._activeObject=n,n.setCoords(),n},onDeselect:function(){return this.destroy(),!1},toString:function(){return"#<fabric.ActiveSelection: ("+this.complexity()+")>"},shouldCache:function(){return!1},isOnACache:function(){return!1},_renderControls:function(e,t,r){e.save(),e.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1,this.callSuper("_renderControls",e,t),r=r||{},"undefined"==typeof r.hasControls&&(r.hasControls=!1),r.forActiveSelection=!0;for(var n=0,i=this._objects.length;i>n;n++)this._objects[n]._renderControls(e,r);e.restore()}}),t.ActiveSelection.fromObject=function(e,r){t.util.enlivenObjects(e.objects,function(n){delete e.objects,r&&r(new t.ActiveSelection(n,e,!0))})})}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=fabric.util.object.extend;return t.fabric||(t.fabric={}),t.fabric.Image?void fabric.warn("fabric.Image is already defined."):(fabric.Image=fabric.util.createClass(fabric.Object,{type:"image",strokeWidth:0,srcFromAttribute:!1,_lastScaleX:1,_lastScaleY:1,_filterScalingX:1,_filterScalingY:1,minimumScaleTrigger:.5,stateProperties:fabric.Object.prototype.stateProperties.concat("cropX","cropY"),cacheProperties:fabric.Object.prototype.cacheProperties.concat("cropX","cropY"),cacheKey:"",cropX:0,cropY:0,imageSmoothing:!0,initialize:function(t,e){e||(e={}),this.filters=[],this.cacheKey="texture"+fabric.Object.__uid++,this.callSuper("initialize",e),this._initElement(t,e)},getElement:function(){return this._element||{}},setElement:function(t,e){return this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._element=t,this._originalElement=t,this._initConfig(e),0!==this.filters.length&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters(),this},removeTexture:function(t){var e=fabric.filterBackend;e&&e.evictCachesForKey&&e.evictCachesForKey(t)},dispose:function(){this.callSuper("dispose"),this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._cacheContext=void 0,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach(function(t){fabric.util.cleanUpJsdomNode(this[t]),this[t]=void 0}.bind(this))},getCrossOrigin:function(){return this._originalElement&&(this._originalElement.crossOrigin||null)},getOriginalSize:function(){var t=this.getElement();return{width:t.naturalWidth||t.width,height:t.naturalHeight||t.height}},_stroke:function(t){if(this.stroke&&0!==this.strokeWidth){var e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,-i),t.lineTo(e,-i),t.lineTo(e,i),t.lineTo(-e,i),t.lineTo(-e,-i),t.closePath()}},toObject:function(t){var i=[];this.filters.forEach(function(t){t&&i.push(t.toObject())});var r=e(this.callSuper("toObject",["cropX","cropY"].concat(t)),{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:i});return this.resizeFilter&&(r.resizeFilter=this.resizeFilter.toObject()),r},hasCrop:function(){return this.cropX||this.cropY||this.width<this._element.width||this.height<this._element.height},getSrc:function(t){var e=t?this._element:this._originalElement;return e?e.toDataURL?e.toDataURL():this.srcFromAttribute?e.getAttribute("src"):e.src:this.src||""},setSrc:function(t,e,i){return fabric.util.loadImage(t,function(t,r){this.setElement(t,i),this._setWidthHeight(),e&&e(this,r)},this,i&&i.crossOrigin),this},toString:function(){return'#<fabric.Image: { src: "'+this.getSrc()+'" }>'},applyResizeFilters:function(){var t=this.resizeFilter,e=this.minimumScaleTrigger,i=this.getTotalObjectScaling(),r=i.scaleX,n=i.scaleY,o=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!t||r>e&&n>e)return this._element=o,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=r,void(this._lastScaleY=n);fabric.filterBackend||(fabric.filterBackend=fabric.initFilterBackend());var s=fabric.util.createCanvasElement(),a=this._filteredEl?this.cacheKey+"_filtered":this.cacheKey,c=o.width,l=o.height;s.width=c,s.height=l,this._element=s,this._lastScaleX=t.scaleX=r,this._lastScaleY=t.scaleY=n,fabric.filterBackend.applyFilters([t],o,c,l,this._element,a),this._filterScalingX=s.width/this._originalElement.width,this._filterScalingY=s.height/this._originalElement.height},applyFilters:function(t){if(t=t||this.filters||[],t=t.filter(function(t){return t&&!t.isNeutralState()}),this.set("dirty",!0),this.removeTexture(this.cacheKey+"_filtered"),0===t.length)return this._element=this._originalElement,this._filteredEl=null,this._filterScalingX=1,this._filterScalingY=1,this;var e=this._originalElement,i=e.naturalWidth||e.width,r=e.naturalHeight||e.height;if(this._element===this._originalElement){var n=fabric.util.createCanvasElement();n.width=i,n.height=r,this._element=n,this._filteredEl=n}else this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,i,r),this._lastScaleX=1,this._lastScaleY=1;return fabric.filterBackend||(fabric.filterBackend=fabric.initFilterBackend()),fabric.filterBackend.applyFilters(t,this._originalElement,i,r,this._element,this.cacheKey),(this._originalElement.width!==this._element.width||this._originalElement.height!==this._element.height)&&(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height),this},_render:function(t){fabric.util.setImageSmoothing(t,this.imageSmoothing),this.isMoving!==!0&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(t),this._renderPaintInOrder(t)},drawCacheOnCanvas:function(t){fabric.util.setImageSmoothing(t,this.imageSmoothing),fabric.Object.prototype.drawCacheOnCanvas.call(this,t)},shouldCache:function(){return this.needsItsOwnCache()},_renderFill:function(t){var e=this._element;if(e){var i=this._filterScalingX,r=this._filterScalingY,n=this.width,o=this.height,s=Math.min,a=Math.max,c=a(this.cropX,0),l=a(this.cropY,0),h=e.naturalWidth||e.width,u=e.naturalHeight||e.height,f=c*i,d=l*r,g=s(n*i,h-f),p=s(o*r,u-d),v=-n/2,m=-o/2,b=s(n,h/i-c),y=s(o,u/r-l);e&&t.drawImage(e,f,d,g,p,v,m,b,y)}},_needsResize:function(){var t=this.getTotalObjectScaling();return t.scaleX!==this._lastScaleX||t.scaleY!==this._lastScaleY},_resetWidthHeight:function(){this.set(this.getOriginalSize())},_initElement:function(t,e){this.setElement(fabric.util.getById(t),e),fabric.util.addClass(this.getElement(),fabric.Image.CSS_CANVAS)},_initConfig:function(t){t||(t={}),this.setOptions(t),this._setWidthHeight(t)},_initFilters:function(t,e){t&&t.length?fabric.util.enlivenObjects(t,function(t){e&&e(t)},"fabric.Image.filters"):e&&e()},_setWidthHeight:function(t){t||(t={});var e=this.getElement();this.width=t.width||e.naturalWidth||e.width||0,this.height=t.height||e.naturalHeight||e.height||0},parsePreserveAspectRatioAttribute:function(){var t,e=fabric.util.parsePreserveAspectRatioAttribute(this.preserveAspectRatio||""),i=this._element.width,r=this._element.height,n=1,o=1,s=0,a=0,c=0,l=0,h=this.width,u=this.height,f={width:h,height:u};return!e||"none"===e.alignX&&"none"===e.alignY?(n=h/i,o=u/r):("meet"===e.meetOrSlice&&(n=o=fabric.util.findScaleToFit(this._element,f),t=(h-i*n)/2,"Min"===e.alignX&&(s=-t),"Max"===e.alignX&&(s=t),t=(u-r*o)/2,"Min"===e.alignY&&(a=-t),"Max"===e.alignY&&(a=t)),"slice"===e.meetOrSlice&&(n=o=fabric.util.findScaleToCover(this._element,f),t=i-h/n,"Mid"===e.alignX&&(c=t/2),"Max"===e.alignX&&(c=t),t=r-u/o,"Mid"===e.alignY&&(l=t/2),"Max"===e.alignY&&(l=t),i=h/n,r=u/o)),{width:i,height:r,scaleX:n,scaleY:o,offsetLeft:s,offsetTop:a,cropX:c,cropY:l}}}),fabric.Image.CSS_CANVAS="canvas-img",fabric.Image.prototype.getSvgSrc=fabric.Image.prototype.getSrc,fabric.Image.fromObject=function(t,e){var i=fabric.util.object.clone(t);fabric.util.loadImage(i.src,function(t,r){return r?void(e&&e(null,!0)):void fabric.Image.prototype._initFilters.call(i,i.filters,function(r){i.filters=r||[],fabric.Image.prototype._initFilters.call(i,[i.resizeFilter],function(r){i.resizeFilter=r[0],fabric.util.enlivenObjectEnlivables(i,i,function(){var r=new fabric.Image(t,i);e(r,!1)})})})},null,i.crossOrigin)},void(fabric.Image.fromURL=function(t,e,i){fabric.util.loadImage(t,function(t,r){e&&e(new fabric.Image(t,i),r)},null,i&&i.crossOrigin)}))}("undefined"!=typeof exports?exports:this);fabric.util.object.extend(fabric.Object.prototype,{_getAngleValueForStraighten:function(){var t=this.angle%360;return t>0?90*Math.round((t-1)/90):90*Math.round(t/90)},straighten:function(){return this.rotate(this._getAngleValueForStraighten())},fxStraighten:function(t){t=t||{};var e=function(){},i=t.onComplete||e,r=t.onChange||e,n=this;return fabric.util.animate({target:this,startValue:this.get("angle"),endValue:this._getAngleValueForStraighten(),duration:this.FX_DURATION,onChange:function(t){n.rotate(t),r()},onComplete:function(){n.setCoords(),i()}})}}),fabric.util.object.extend(fabric.StaticCanvas.prototype,{straightenObject:function(t){return t.straighten(),this.requestRenderAll(),this},fxStraightenObject:function(t){return t.fxStraighten({onChange:this.requestRenderAllBound})}});function resizeCanvasIfNeeded(e){var t=e.targetCanvas,r=t.width,n=t.height,i=e.destinationWidth,o=e.destinationHeight;(r!==i||n!==o)&&(t.width=i,t.height=o)}function copyGLTo2DDrawImage(e,t){var r=e.canvas,n=t.targetCanvas,i=n.getContext("2d");i.translate(0,n.height),i.scale(1,-1);var o=r.height-n.height;i.drawImage(r,0,o,n.width,n.height,0,0,n.width,n.height)}function copyGLTo2DPutImageData(e,t){var r=t.targetCanvas,n=r.getContext("2d"),i=t.destinationWidth,o=t.destinationHeight,a=i*o*4,s=new Uint8Array(this.imageBuffer,0,a),c=new Uint8ClampedArray(this.imageBuffer,0,a);e.readPixels(0,0,i,o,e.RGBA,e.UNSIGNED_BYTE,s);var l=new ImageData(c,i,o);n.putImageData(l,0,0)}!function(){"use strict";function e(e,t){var r="precision "+t+" float;\nvoid main(){}",n=e.createShader(e.FRAGMENT_SHADER);return e.shaderSource(n,r),e.compileShader(n),e.getShaderParameter(n,e.COMPILE_STATUS)?!0:!1}function t(e){e&&e.tileSize&&(this.tileSize=e.tileSize),this.setupGLContext(this.tileSize,this.tileSize),this.captureGPUInfo()}fabric.isWebglSupported=function(t){if(fabric.isLikelyNode)return!1;t=t||fabric.WebglFilterBackend.prototype.tileSize;var r=document.createElement("canvas"),n=r.getContext("webgl")||r.getContext("experimental-webgl"),i=!1;if(n){fabric.maxTextureSize=n.getParameter(n.MAX_TEXTURE_SIZE),i=fabric.maxTextureSize>=t;for(var o=["highp","mediump","lowp"],a=0;3>a;a++)if(e(n,o[a])){fabric.webGlPrecision=o[a];break}}return this.isSupported=i,i},fabric.WebglFilterBackend=t,t.prototype={tileSize:2048,resources:{},setupGLContext:function(e,t){this.dispose(),this.createWebGLCanvas(e,t),this.aPosition=new Float32Array([0,0,0,1,1,0,1,1]),this.chooseFastestCopyGLTo2DMethod(e,t)},chooseFastestCopyGLTo2DMethod:function(e,t){var r,n="undefined"!=typeof window.performance;try{new ImageData(1,1),r=!0}catch(i){r=!1}var o="undefined"!=typeof ArrayBuffer,a="undefined"!=typeof Uint8ClampedArray;if(n&&r&&o&&a){var s=fabric.util.createCanvasElement(),c=new ArrayBuffer(e*t*4);if(fabric.forceGLPutImageData)return this.imageBuffer=c,void(this.copyGLTo2D=copyGLTo2DPutImageData);var l,u,f,h={imageBuffer:c,destinationWidth:e,destinationHeight:t,targetCanvas:s};s.width=e,s.height=t,l=window.performance.now(),copyGLTo2DDrawImage.call(h,this.gl,h),u=window.performance.now()-l,l=window.performance.now(),copyGLTo2DPutImageData.call(h,this.gl,h),f=window.performance.now()-l,u>f?(this.imageBuffer=c,this.copyGLTo2D=copyGLTo2DPutImageData):this.copyGLTo2D=copyGLTo2DDrawImage}},createWebGLCanvas:function(e,t){var r=fabric.util.createCanvasElement();r.width=e,r.height=t;var n={alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1},i=r.getContext("webgl",n);i||(i=r.getContext("experimental-webgl",n)),i&&(i.clearColor(0,0,0,0),this.canvas=r,this.gl=i)},applyFilters:function(e,t,r,n,i,o){var a,s=this.gl;o&&(a=this.getCachedTexture(o,t));var c={originalWidth:t.width||t.originalWidth,originalHeight:t.height||t.originalHeight,sourceWidth:r,sourceHeight:n,destinationWidth:r,destinationHeight:n,context:s,sourceTexture:this.createTexture(s,r,n,!a&&t),targetTexture:this.createTexture(s,r,n),originalTexture:a||this.createTexture(s,r,n,!a&&t),passes:e.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:i},l=s.createFramebuffer();return s.bindFramebuffer(s.FRAMEBUFFER,l),e.forEach(function(e){e&&e.applyTo(c)}),resizeCanvasIfNeeded(c),this.copyGLTo2D(s,c),s.bindTexture(s.TEXTURE_2D,null),s.deleteTexture(c.sourceTexture),s.deleteTexture(c.targetTexture),s.deleteFramebuffer(l),i.getContext("2d").setTransform(1,0,0,1,0,0),c},dispose:function(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()},clearWebGLCaches:function(){this.programCache={},this.textureCache={}},createTexture:function(e,t,r,n){var i=e.createTexture();return e.bindTexture(e.TEXTURE_2D,i),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),n?e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,n):e.texImage2D(e.TEXTURE_2D,0,e.RGBA,t,r,0,e.RGBA,e.UNSIGNED_BYTE,null),i},getCachedTexture:function(e,t){if(this.textureCache[e])return this.textureCache[e];var r=this.createTexture(this.gl,t.width,t.height,t);return this.textureCache[e]=r,r},evictCachesForKey:function(e){this.textureCache[e]&&(this.gl.deleteTexture(this.textureCache[e]),delete this.textureCache[e])},copyGLTo2D:copyGLTo2DDrawImage,captureGPUInfo:function(){if(this.gpuInfo)return this.gpuInfo;var e=this.gl,t={renderer:"",vendor:""};if(!e)return t;var r=e.getExtension("WEBGL_debug_renderer_info");if(r){var n=e.getParameter(r.UNMASKED_RENDERER_WEBGL),i=e.getParameter(r.UNMASKED_VENDOR_WEBGL);n&&(t.renderer=n.toLowerCase()),i&&(t.vendor=i.toLowerCase())}return this.gpuInfo=t,t}}}();!function(){"use strict";function t(){}var e=function(){};fabric.Canvas2dFilterBackend=t,t.prototype={evictCachesForKey:e,dispose:e,clearWebGLCaches:e,resources:{},applyFilters:function(t,e,i,r,n){var s=n.getContext("2d");s.drawImage(e,0,0,i,r);var o=s.getImageData(0,0,i,r),a=s.getImageData(0,0,i,r),c={sourceWidth:i,sourceHeight:r,imageData:o,originalEl:e,originalImageData:a,canvasEl:n,ctx:s,filterBackend:this};return t.forEach(function(t){t.applyTo(c)}),(c.imageData.width!==i||c.imageData.height!==r)&&(n.width=c.imageData.width,n.height=c.imageData.height),s.putImageData(c.imageData,0,0),c}}}();fabric.Image=fabric.Image||{},fabric.Image.filters=fabric.Image.filters||{},fabric.Image.filters.BaseFilter=fabric.util.createClass({type:"BaseFilter",vertexSource:"attribute vec2 aPosition;\nvarying vec2 vTexCoord;\nvoid main() {\nvTexCoord = aPosition;\ngl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n}",fragmentSource:"precision highp float;\nvarying vec2 vTexCoord;\nuniform sampler2D uTexture;\nvoid main() {\ngl_FragColor = texture2D(uTexture, vTexCoord);\n}",initialize:function(t){t&&this.setOptions(t)},setOptions:function(t){for(var e in t)this[e]=t[e]},createProgram:function(t,e,i){e=e||this.fragmentSource,i=i||this.vertexSource,"highp"!==fabric.webGlPrecision&&(e=e.replace(/precision highp float/g,"precision "+fabric.webGlPrecision+" float"));var r=t.createShader(t.VERTEX_SHADER);if(t.shaderSource(r,i),t.compileShader(r),!t.getShaderParameter(r,t.COMPILE_STATUS))throw new Error("Vertex shader compile error for "+this.type+": "+t.getShaderInfoLog(r));var n=t.createShader(t.FRAGMENT_SHADER);if(t.shaderSource(n,e),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw new Error("Fragment shader compile error for "+this.type+": "+t.getShaderInfoLog(n));var o=t.createProgram();if(t.attachShader(o,r),t.attachShader(o,n),t.linkProgram(o),!t.getProgramParameter(o,t.LINK_STATUS))throw new Error('Shader link error for "${this.type}" '+t.getProgramInfoLog(o));var s=this.getAttributeLocations(t,o),a=this.getUniformLocations(t,o)||{};return a.uStepW=t.getUniformLocation(o,"uStepW"),a.uStepH=t.getUniformLocation(o,"uStepH"),{program:o,attributeLocations:s,uniformLocations:a}},getAttributeLocations:function(t,e){return{aPosition:t.getAttribLocation(e,"aPosition")}},getUniformLocations:function(){return{}},sendAttributeData:function(t,e,i){var r=e.aPosition,n=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,n),t.enableVertexAttribArray(r),t.vertexAttribPointer(r,2,t.FLOAT,!1,0,0),t.bufferData(t.ARRAY_BUFFER,i,t.STATIC_DRAW)},_setupFrameBuffer:function(t){var e,i,r=t.context;t.passes>1?(e=t.destinationWidth,i=t.destinationHeight,(t.sourceWidth!==e||t.sourceHeight!==i)&&(r.deleteTexture(t.targetTexture),t.targetTexture=t.filterBackend.createTexture(r,e,i)),r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.TEXTURE_2D,t.targetTexture,0)):(r.bindFramebuffer(r.FRAMEBUFFER,null),r.finish())},_swapTextures:function(t){t.passes--,t.pass++;var e=t.targetTexture;t.targetTexture=t.sourceTexture,t.sourceTexture=e},isNeutralState:function(){var t=this.mainParameter,e=fabric.Image.filters[this.type].prototype;if(t){if(Array.isArray(e[t])){for(var i=e[t].length;i--;)if(this[t][i]!==e[t][i])return!1;return!0}return e[t]===this[t]}return!1},applyTo:function(t){t.webgl?(this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)},retrieveShader:function(t){return t.programCache.hasOwnProperty(this.type)||(t.programCache[this.type]=this.createProgram(t.context)),t.programCache[this.type]},applyToWebGL:function(t){var e=t.context,i=this.retrieveShader(t);0===t.pass&&t.originalTexture?e.bindTexture(e.TEXTURE_2D,t.originalTexture):e.bindTexture(e.TEXTURE_2D,t.sourceTexture),e.useProgram(i.program),this.sendAttributeData(e,i.attributeLocations,t.aPosition),e.uniform1f(i.uniformLocations.uStepW,1/t.sourceWidth),e.uniform1f(i.uniformLocations.uStepH,1/t.sourceHeight),this.sendUniformData(e,i.uniformLocations),e.viewport(0,0,t.destinationWidth,t.destinationHeight),e.drawArrays(e.TRIANGLE_STRIP,0,4)},bindAdditionalTexture:function(t,e,i){t.activeTexture(i),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE0)},unbindAdditionalTexture:function(t,e){t.activeTexture(e),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE0)},getMainParameter:function(){return this[this.mainParameter]},setMainParameter:function(t){this[this.mainParameter]=t},sendUniformData:function(){},createHelpLayer:function(t){if(!t.helpLayer){var e=document.createElement("canvas");e.width=t.sourceWidth,e.height=t.sourceHeight,t.helpLayer=e}},toObject:function(){var t={type:this.type},e=this.mainParameter;return e&&(t[e]=this[e]),t},toJSON:function(){return this.toObject()}}),fabric.Image.filters.BaseFilter.fromObject=function(t,e){var i=new fabric.Image.filters[t.type](t);return e&&e(i),i};!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.ColorMatrix=r(i.BaseFilter,{type:"ColorMatrix",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nvarying vec2 vTexCoord;\nuniform mat4 uColorMatrix;\nuniform vec4 uConstants;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor *= uColorMatrix;\ncolor += uConstants;\ngl_FragColor = color;\n}",matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],mainParameter:"matrix",colorsOnly:!0,initialize:function(t){this.callSuper("initialize",t),this.matrix=this.matrix.slice(0)},applyTo2d:function(t){var e,i,r,n,s,o=t.imageData,a=o.data,c=a.length,l=this.matrix,h=this.colorsOnly;for(s=0;c>s;s+=4)e=a[s],i=a[s+1],r=a[s+2],h?(a[s]=e*l[0]+i*l[1]+r*l[2]+255*l[4],a[s+1]=e*l[5]+i*l[6]+r*l[7]+255*l[9],a[s+2]=e*l[10]+i*l[11]+r*l[12]+255*l[14]):(n=a[s+3],a[s]=e*l[0]+i*l[1]+r*l[2]+n*l[3]+255*l[4],a[s+1]=e*l[5]+i*l[6]+r*l[7]+n*l[8]+255*l[9],a[s+2]=e*l[10]+i*l[11]+r*l[12]+n*l[13]+255*l[14],a[s+3]=e*l[15]+i*l[16]+r*l[17]+n*l[18]+255*l[19])},getUniformLocations:function(t,e){return{uColorMatrix:t.getUniformLocation(e,"uColorMatrix"),uConstants:t.getUniformLocation(e,"uConstants")}},sendUniformData:function(t,e){var i=this.matrix,r=[i[0],i[1],i[2],i[3],i[5],i[6],i[7],i[8],i[10],i[11],i[12],i[13],i[15],i[16],i[17],i[18]],n=[i[4],i[9],i[14],i[19]];t.uniformMatrix4fv(e.uColorMatrix,!1,r),t.uniform4fv(e.uConstants,n)}}),e.Image.filters.ColorMatrix.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.Brightness=r(i.BaseFilter,{type:"Brightness",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uBrightness;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor.rgb += uBrightness;\ngl_FragColor = color;\n}",brightness:0,mainParameter:"brightness",applyTo2d:function(t){if(0!==this.brightness){var e,i=t.imageData,r=i.data,n=r.length,s=Math.round(255*this.brightness);for(e=0;n>e;e+=4)r[e]=r[e]+s,r[e+1]=r[e+1]+s,r[e+2]=r[e+2]+s}},getUniformLocations:function(t,e){return{uBrightness:t.getUniformLocation(e,"uBrightness")}},sendUniformData:function(t,e){t.uniform1f(e.uBrightness,this.brightness)}}),e.Image.filters.Brightness.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,r=e.Image.filters,n=e.util.createClass;r.Convolute=n(r.BaseFilter,{type:"Convolute",opaque:!1,matrix:[0,0,0,0,1,0,0,0,0],fragmentSource:{Convolute_3_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[9];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 3.0; h+=1.0) {\nfor (float w = 0.0; w < 3.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_3_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[9];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 3.0; h+=1.0) {\nfor (float w = 0.0; w < 3.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_5_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[25];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 5.0; h+=1.0) {\nfor (float w = 0.0; w < 5.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_5_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[25];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 5.0; h+=1.0) {\nfor (float w = 0.0; w < 5.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_7_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[49];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 7.0; h+=1.0) {\nfor (float w = 0.0; w < 7.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_7_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[49];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 7.0; h+=1.0) {\nfor (float w = 0.0; w < 7.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_9_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[81];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 9.0; h+=1.0) {\nfor (float w = 0.0; w < 9.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_9_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[81];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 9.0; h+=1.0) {\nfor (float w = 0.0; w < 9.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}"},retrieveShader:function(t){var e=Math.sqrt(this.matrix.length),i=this.type+"_"+e+"_"+(this.opaque?1:0),r=this.fragmentSource[i];return t.programCache.hasOwnProperty(i)||(t.programCache[i]=this.createProgram(t.context,r)),t.programCache[i]},applyTo2d:function(t){var e,i,r,n,o,s,a,c,l,h,u,f,d,p=t.imageData,g=p.data,v=this.matrix,m=Math.round(Math.sqrt(v.length)),b=Math.floor(m/2),y=p.width,x=p.height,C=t.ctx.createImageData(y,x),_=C.data,w=this.opaque?1:0;for(u=0;x>u;u++)for(h=0;y>h;h++){for(o=4*(u*y+h),e=0,i=0,r=0,n=0,d=0;m>d;d++)for(f=0;m>f;f++)a=u+d-b,s=h+f-b,0>a||a>=x||0>s||s>=y||(c=4*(a*y+s),l=v[d*m+f],e+=g[c]*l,i+=g[c+1]*l,r+=g[c+2]*l,w||(n+=g[c+3]*l));_[o]=e,_[o+1]=i,_[o+2]=r,_[o+3]=w?g[o+3]:n}t.imageData=C},getUniformLocations:function(t,e){return{uMatrix:t.getUniformLocation(e,"uMatrix"),uOpaque:t.getUniformLocation(e,"uOpaque"),uHalfSize:t.getUniformLocation(e,"uHalfSize"),uSize:t.getUniformLocation(e,"uSize")}},sendUniformData:function(t,e){t.uniform1fv(e.uMatrix,this.matrix)},toObject:function(){return i(this.callSuper("toObject"),{opaque:this.opaque,matrix:this.matrix})}}),e.Image.filters.Convolute.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.Grayscale=r(i.BaseFilter,{type:"Grayscale",fragmentSource:{average:"precision highp float;\nuniform sampler2D uTexture;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat average = (color.r + color.b + color.g) / 3.0;\ngl_FragColor = vec4(average, average, average, color.a);\n}",lightness:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uMode;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 col = texture2D(uTexture, vTexCoord);\nfloat average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;\ngl_FragColor = vec4(average, average, average, col.a);\n}",luminosity:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uMode;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 col = texture2D(uTexture, vTexCoord);\nfloat average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;\ngl_FragColor = vec4(average, average, average, col.a);\n}"},mode:"average",mainParameter:"mode",applyTo2d:function(t){var e,i,r=t.imageData,n=r.data,o=n.length,s=this.mode;for(e=0;o>e;e+=4)"average"===s?i=(n[e]+n[e+1]+n[e+2])/3:"lightness"===s?i=(Math.min(n[e],n[e+1],n[e+2])+Math.max(n[e],n[e+1],n[e+2]))/2:"luminosity"===s&&(i=.21*n[e]+.72*n[e+1]+.07*n[e+2]),n[e]=i,n[e+1]=i,n[e+2]=i},retrieveShader:function(t){var e=this.type+"_"+this.mode;if(!t.programCache.hasOwnProperty(e)){var i=this.fragmentSource[this.mode];t.programCache[e]=this.createProgram(t.context,i)}return t.programCache[e]},getUniformLocations:function(t,e){return{uMode:t.getUniformLocation(e,"uMode")}},sendUniformData:function(t,e){var i=1;t.uniform1i(e.uMode,i)},isNeutralState:function(){return!1}}),e.Image.filters.Grayscale.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.Invert=r(i.BaseFilter,{type:"Invert",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uInvert;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nif (uInvert == 1) {\ngl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);\n} else {\ngl_FragColor = color;\n}\n}",invert:!0,mainParameter:"invert",applyTo2d:function(t){var e,i=t.imageData,r=i.data,n=r.length;for(e=0;n>e;e+=4)r[e]=255-r[e],r[e+1]=255-r[e+1],r[e+2]=255-r[e+2]},isNeutralState:function(){return!this.invert},getUniformLocations:function(t,e){return{uInvert:t.getUniformLocation(e,"uInvert")}},sendUniformData:function(t,e){t.uniform1i(e.uInvert,this.invert)}}),e.Image.filters.Invert.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,r=e.Image.filters,n=e.util.createClass;r.Noise=n(r.BaseFilter,{type:"Noise",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uStepH;\nuniform float uNoise;\nuniform float uSeed;\nvarying vec2 vTexCoord;\nfloat rand(vec2 co, float seed, float vScale) {\nreturn fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);\n}\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;\ngl_FragColor = color;\n}",mainParameter:"noise",noise:0,applyTo2d:function(t){if(0!==this.noise){var e,i,r=t.imageData,n=r.data,o=n.length,s=this.noise;for(e=0,o=n.length;o>e;e+=4)i=(.5-Math.random())*s,n[e]+=i,n[e+1]+=i,n[e+2]+=i}},getUniformLocations:function(t,e){return{uNoise:t.getUniformLocation(e,"uNoise"),uSeed:t.getUniformLocation(e,"uSeed")}},sendUniformData:function(t,e){t.uniform1f(e.uNoise,this.noise/255),t.uniform1f(e.uSeed,Math.random())},toObject:function(){return i(this.callSuper("toObject"),{noise:this.noise})}}),e.Image.filters.Noise.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.Pixelate=r(i.BaseFilter,{type:"Pixelate",blocksize:4,mainParameter:"blocksize",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uBlocksize;\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nfloat blockW = uBlocksize * uStepW;\nfloat blockH = uBlocksize * uStepW;\nint posX = int(vTexCoord.x / blockW);\nint posY = int(vTexCoord.y / blockH);\nfloat fposX = float(posX);\nfloat fposY = float(posY);\nvec2 squareCoords = vec2(fposX * blockW, fposY * blockH);\nvec4 color = texture2D(uTexture, squareCoords);\ngl_FragColor = color;\n}",applyTo2d:function(t){var e,i,r,n,o,s,a,c,l,h,u,f=t.imageData,d=f.data,g=f.height,p=f.width;for(i=0;g>i;i+=this.blocksize)for(r=0;p>r;r+=this.blocksize)for(e=4*i*p+4*r,n=d[e],o=d[e+1],s=d[e+2],a=d[e+3],h=Math.min(i+this.blocksize,g),u=Math.min(r+this.blocksize,p),c=i;h>c;c++)for(l=r;u>l;l++)e=4*c*p+4*l,d[e]=n,d[e+1]=o,d[e+2]=s,d[e+3]=a},isNeutralState:function(){return 1===this.blocksize},getUniformLocations:function(t,e){return{uBlocksize:t.getUniformLocation(e,"uBlocksize"),uStepW:t.getUniformLocation(e,"uStepW"),uStepH:t.getUniformLocation(e,"uStepH")}},sendUniformData:function(t,e){t.uniform1f(e.uBlocksize,this.blocksize)}}),e.Image.filters.Pixelate.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,r=e.Image.filters,n=e.util.createClass;r.RemoveColor=n(r.BaseFilter,{type:"RemoveColor",color:"#FFFFFF",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec4 uLow;\nuniform vec4 uHigh;\nvarying vec2 vTexCoord;\nvoid main() {\ngl_FragColor = texture2D(uTexture, vTexCoord);\nif(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {\ngl_FragColor.a = 0.0;\n}\n}",distance:.02,useAlpha:!1,applyTo2d:function(t){var i,r,n,o,s=t.imageData,a=s.data,c=255*this.distance,l=new e.Color(this.color).getSource(),h=[l[0]-c,l[1]-c,l[2]-c],u=[l[0]+c,l[1]+c,l[2]+c];for(i=0;i<a.length;i+=4)r=a[i],n=a[i+1],o=a[i+2],r>h[0]&&n>h[1]&&o>h[2]&&r<u[0]&&n<u[1]&&o<u[2]&&(a[i+3]=0)},getUniformLocations:function(t,e){return{uLow:t.getUniformLocation(e,"uLow"),uHigh:t.getUniformLocation(e,"uHigh")}},sendUniformData:function(t,i){var r=new e.Color(this.color).getSource(),n=parseFloat(this.distance),o=[0+r[0]/255-n,0+r[1]/255-n,0+r[2]/255-n,1],s=[r[0]/255+n,r[1]/255+n,r[2]/255+n,1];t.uniform4fv(i.uLow,o),t.uniform4fv(i.uHigh,s)},toObject:function(){return i(this.callSuper("toObject"),{color:this.color,distance:this.distance})}}),e.Image.filters.RemoveColor.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass,n={Brownie:[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0],Vintage:[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0],Kodachrome:[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0],Technicolor:[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0],Polaroid:[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0],Sepia:[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0],BlackWhite:[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]};for(var o in n)i[o]=r(i.ColorMatrix,{type:o,matrix:n[o],mainParameter:!1,colorsOnly:!0}),e.Image.filters[o].fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric,r=e.Image.filters,i=e.util.createClass;r.BlendColor=i(r.BaseFilter,{type:"BlendColor",color:"#F95C63",mode:"multiply",alpha:1,fragmentSource:{multiply:"gl_FragColor.rgb *= uColor.rgb;\n",screen:"gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);\n",add:"gl_FragColor.rgb += uColor.rgb;\n",diff:"gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);\n",subtract:"gl_FragColor.rgb -= uColor.rgb;\n",lighten:"gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);\n",darken:"gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);\n",exclusion:"gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);\n",overlay:"if (uColor.r < 0.5) {\ngl_FragColor.r *= 2.0 * uColor.r;\n} else {\ngl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);\n}\nif (uColor.g < 0.5) {\ngl_FragColor.g *= 2.0 * uColor.g;\n} else {\ngl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);\n}\nif (uColor.b < 0.5) {\ngl_FragColor.b *= 2.0 * uColor.b;\n} else {\ngl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);\n}\n",tint:"gl_FragColor.rgb *= (1.0 - uColor.a);\ngl_FragColor.rgb += uColor.rgb;\n"},buildSource:function(t){return"precision highp float;\nuniform sampler2D uTexture;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ngl_FragColor = color;\nif (color.a > 0.0) {\n"+this.fragmentSource[t]+"}\n}"},retrieveShader:function(t){var e,r=this.type+"_"+this.mode;return t.programCache.hasOwnProperty(r)||(e=this.buildSource(this.mode),t.programCache[r]=this.createProgram(t.context,e)),t.programCache[r]},applyTo2d:function(t){var r,i,n,o,s,a,c,l=t.imageData,u=l.data,h=u.length,f=1-this.alpha;c=new e.Color(this.color).getSource(),r=c[0]*this.alpha,i=c[1]*this.alpha,n=c[2]*this.alpha;for(var d=0;h>d;d+=4)switch(o=u[d],s=u[d+1],a=u[d+2],this.mode){case"multiply":u[d]=o*r/255,u[d+1]=s*i/255,u[d+2]=a*n/255;break;case"screen":u[d]=255-(255-o)*(255-r)/255,u[d+1]=255-(255-s)*(255-i)/255,u[d+2]=255-(255-a)*(255-n)/255;break;case"add":u[d]=o+r,u[d+1]=s+i,u[d+2]=a+n;break;case"diff":case"difference":u[d]=Math.abs(o-r),u[d+1]=Math.abs(s-i),u[d+2]=Math.abs(a-n);break;case"subtract":u[d]=o-r,u[d+1]=s-i,u[d+2]=a-n;break;case"darken":u[d]=Math.min(o,r),u[d+1]=Math.min(s,i),u[d+2]=Math.min(a,n);break;case"lighten":u[d]=Math.max(o,r),u[d+1]=Math.max(s,i),u[d+2]=Math.max(a,n);break;case"overlay":u[d]=128>r?2*o*r/255:255-2*(255-o)*(255-r)/255,u[d+1]=128>i?2*s*i/255:255-2*(255-s)*(255-i)/255,u[d+2]=128>n?2*a*n/255:255-2*(255-a)*(255-n)/255;break;case"exclusion":u[d]=r+o-2*r*o/255,u[d+1]=i+s-2*i*s/255,u[d+2]=n+a-2*n*a/255;break;case"tint":u[d]=r+o*f,u[d+1]=i+s*f,u[d+2]=n+a*f}},getUniformLocations:function(t,e){return{uColor:t.getUniformLocation(e,"uColor")}},sendUniformData:function(t,r){var i=new e.Color(this.color).getSource();i[0]=this.alpha*i[0]/255,i[1]=this.alpha*i[1]/255,i[2]=this.alpha*i[2]/255,i[3]=this.alpha,t.uniform4fv(r.uColor,i)},toObject:function(){return{type:this.type,color:this.color,mode:this.mode,alpha:this.alpha}}}),e.Image.filters.BlendColor.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric,i=e.Image.filters,r=e.util.createClass;i.BlendImage=r(i.BaseFilter,{type:"BlendImage",image:null,mode:"multiply",alpha:1,vertexSource:"attribute vec2 aPosition;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nuniform mat3 uTransformMatrix;\nvoid main() {\nvTexCoord = aPosition;\nvTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;\ngl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n}",fragmentSource:{multiply:"precision highp float;\nuniform sampler2D uTexture;\nuniform sampler2D uImage;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec4 color2 = texture2D(uImage, vTexCoord2);\ncolor.rgba *= color2.rgba;\ngl_FragColor = color;\n}",mask:"precision highp float;\nuniform sampler2D uTexture;\nuniform sampler2D uImage;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec4 color2 = texture2D(uImage, vTexCoord2);\ncolor.a = color2.a;\ngl_FragColor = color;\n}"},retrieveShader:function(t){var e=this.type+"_"+this.mode,i=this.fragmentSource[this.mode];return t.programCache.hasOwnProperty(e)||(t.programCache[e]=this.createProgram(t.context,i)),t.programCache[e]},applyToWebGL:function(t){var e=t.context,i=this.createTexture(t.filterBackend,this.image);this.bindAdditionalTexture(e,i,e.TEXTURE1),this.callSuper("applyToWebGL",t),this.unbindAdditionalTexture(e,e.TEXTURE1)},createTexture:function(t,e){return t.getCachedTexture(e.cacheKey,e._element)},calculateMatrix:function(){var t=this.image,e=t._element.width,i=t._element.height;return[1/t.scaleX,0,0,0,1/t.scaleY,0,-t.left/e,-t.top/i,1]},applyTo2d:function(t){var i,r,n,s,o,a,c,l,h,u,f,d=t.imageData,g=t.filterBackend.resources,p=d.data,v=p.length,m=d.width,b=d.height,y=this.image;g.blendImage||(g.blendImage=e.util.createCanvasElement()),h=g.blendImage,u=h.getContext("2d"),h.width!==m||h.height!==b?(h.width=m,h.height=b):u.clearRect(0,0,m,b),u.setTransform(y.scaleX,0,0,y.scaleY,y.left,y.top),u.drawImage(y._element,0,0,m,b),f=u.getImageData(0,0,m,b).data;for(var x=0;v>x;x+=4)switch(o=p[x],a=p[x+1],c=p[x+2],l=p[x+3],i=f[x],r=f[x+1],n=f[x+2],s=f[x+3],this.mode){case"multiply":p[x]=o*i/255,p[x+1]=a*r/255,p[x+2]=c*n/255,p[x+3]=l*s/255;break;case"mask":p[x+3]=s}},getUniformLocations:function(t,e){return{uTransformMatrix:t.getUniformLocation(e,"uTransformMatrix"),uImage:t.getUniformLocation(e,"uImage")}},sendUniformData:function(t,e){var i=this.calculateMatrix();t.uniform1i(e.uImage,1),t.uniformMatrix3fv(e.uTransformMatrix,!1,i)},toObject:function(){return{type:this.type,image:this.image&&this.image.toObject(),mode:this.mode,alpha:this.alpha}}}),e.Image.filters.BlendImage.fromObject=function(t,i){e.Image.fromObject(t.image,function(r){var n=e.util.object.clone(t);n.image=r,i(new e.Image.filters.BlendImage(n))})}}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=Math.pow,r=Math.floor,n=Math.sqrt,o=Math.abs,a=Math.round,s=Math.sin,c=Math.ceil,l=e.Image.filters,h=e.util.createClass;l.Resize=h(l.BaseFilter,{type:"Resize",resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3,getUniformLocations:function(t,e){return{uDelta:t.getUniformLocation(e,"uDelta"),uTaps:t.getUniformLocation(e,"uTaps")}},sendUniformData:function(t,e){t.uniform2fv(e.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),t.uniform1fv(e.uTaps,this.taps)},retrieveShader:function(t){var e=this.getFilterWindow(),i=this.type+"_"+e;if(!t.programCache.hasOwnProperty(i)){var r=this.generateShader(e);t.programCache[i]=this.createProgram(t.context,r)}return t.programCache[i]},getFilterWindow:function(){var t=this.tempScale;return Math.ceil(this.lanczosLobes/t)},getTaps:function(){for(var t=this.lanczosCreate(this.lanczosLobes),e=this.tempScale,i=this.getFilterWindow(),r=new Array(i),n=1;i>=n;n++)r[n-1]=t(n*e);return r},generateShader:function(t){for(var t,e=new Array(t),i=this.fragmentSourceTOP,r=1;t>=r;r++)e[r-1]=r+".0 * uDelta";return i+="uniform float uTaps["+t+"];\n",i+="void main() {\n",i+="  vec4 color = texture2D(uTexture, vTexCoord);\n",i+="  float sum = 1.0;\n",e.forEach(function(t,e){i+="  color += texture2D(uTexture, vTexCoord + "+t+") * uTaps["+e+"];\n",i+="  color += texture2D(uTexture, vTexCoord - "+t+") * uTaps["+e+"];\n",i+="  sum += 2.0 * uTaps["+e+"];\n"}),i+="  gl_FragColor = color / sum;\n",i+="}"},fragmentSourceTOP:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec2 uDelta;\nvarying vec2 vTexCoord;\n",applyTo:function(t){t.webgl?(t.passes++,this.width=t.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=t.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),t.destinationWidth=this.dW,this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t),t.sourceWidth=t.destinationWidth,this.height=t.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),t.destinationHeight=this.dH,this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t),t.sourceHeight=t.destinationHeight):this.applyTo2d(t)},isNeutralState:function(){return 1===this.scaleX&&1===this.scaleY},lanczosCreate:function(t){return function(e){if(e>=t||-t>=e)return 0;if(1.1920929e-7>e&&e>-1.1920929e-7)return 1;e*=Math.PI;var i=e/t;return s(e)/e*s(i)/i}},applyTo2d:function(t){var e=t.imageData,i=this.scaleX,r=this.scaleY;this.rcpScaleX=1/i,this.rcpScaleY=1/r;var n,o=e.width,s=e.height,c=a(o*i),l=a(s*r);"sliceHack"===this.resizeType?n=this.sliceByTwo(t,o,s,c,l):"hermite"===this.resizeType?n=this.hermiteFastResize(t,o,s,c,l):"bilinear"===this.resizeType?n=this.bilinearFiltering(t,o,s,c,l):"lanczos"===this.resizeType&&(n=this.lanczosResize(t,o,s,c,l)),t.imageData=n},sliceByTwo:function(t,i,n,o,a){var s,c,l=t.imageData,h=.5,u=!1,f=!1,d=i*h,g=n*h,p=e.filterBackend.resources,v=0,m=0,b=i,y=0;for(p.sliceByTwo||(p.sliceByTwo=document.createElement("canvas")),s=p.sliceByTwo,(s.width<1.5*i||s.height<n)&&(s.width=1.5*i,s.height=n),c=s.getContext("2d"),c.clearRect(0,0,1.5*i,n),c.putImageData(l,0,0),o=r(o),a=r(a);!u||!f;)i=d,n=g,o<r(d*h)?d=r(d*h):(d=o,u=!0),a<r(g*h)?g=r(g*h):(g=a,f=!0),c.drawImage(s,v,m,i,n,b,y,d,g),v=b,m=y,y+=g;return c.getImageData(v,m,o,a)},lanczosResize:function(t,e,a,s,l){function h(t){var c,S,T,O,j,P,E,k,F,D,A;for(_.x=(t+.5)*p,w.x=r(_.x),c=0;l>c;c++){for(_.y=(c+.5)*v,w.y=r(_.y),j=0,P=0,E=0,k=0,F=0,S=w.x-y;S<=w.x+y;S++)if(!(0>S||S>=e)){D=r(1e3*o(S-_.x)),C[D]||(C[D]={});for(var M=w.y-x;M<=w.y+x;M++)0>M||M>=a||(A=r(1e3*o(M-_.y)),C[D][A]||(C[D][A]=g(n(i(D*m,2)+i(A*b,2))/1e3)),T=C[D][A],T>0&&(O=4*(M*e+S),j+=T,P+=T*u[O],E+=T*u[O+1],k+=T*u[O+2],F+=T*u[O+3]))}O=4*(c*s+t),d[O]=P/j,d[O+1]=E/j,d[O+2]=k/j,d[O+3]=F/j}return++t<s?h(t):f}var u=t.imageData.data,f=t.ctx.createImageData(s,l),d=f.data,g=this.lanczosCreate(this.lanczosLobes),p=this.rcpScaleX,v=this.rcpScaleY,m=2/this.rcpScaleX,b=2/this.rcpScaleY,y=c(p*this.lanczosLobes/2),x=c(v*this.lanczosLobes/2),C={},_={},w={};return h(0)},bilinearFiltering:function(t,e,i,n,o){var a,s,c,l,h,u,f,d,g,p,v,m,b,y=0,x=this.rcpScaleX,C=this.rcpScaleY,_=4*(e-1),w=t.imageData,S=w.data,T=t.ctx.createImageData(n,o),O=T.data;for(f=0;o>f;f++)for(d=0;n>d;d++)for(h=r(x*d),u=r(C*f),g=x*d-h,p=C*f-u,b=4*(u*e+h),v=0;4>v;v++)a=S[b+v],s=S[b+4+v],c=S[b+_+v],l=S[b+_+4+v],m=a*(1-g)*(1-p)+s*g*(1-p)+c*p*(1-g)+l*g*p,O[y++]=m;return T},hermiteFastResize:function(t,e,i,a,s){for(var l=this.rcpScaleX,h=this.rcpScaleY,u=c(l/2),f=c(h/2),d=t.imageData,g=d.data,p=t.ctx.createImageData(a,s),v=p.data,m=0;s>m;m++)for(var b=0;a>b;b++){for(var y=4*(b+m*a),x=0,C=0,_=0,w=0,S=0,T=0,O=0,j=(m+.5)*h,P=r(m*h);(m+1)*h>P;P++)for(var E=o(j-(P+.5))/f,k=(b+.5)*l,F=E*E,D=r(b*l);(b+1)*l>D;D++){var A=o(k-(D+.5))/u,M=n(F+A*A);M>1&&-1>M||(x=2*M*M*M-3*M*M+1,x>0&&(A=4*(D+P*e),O+=x*g[A+3],_+=x,g[A+3]<255&&(x=x*g[A+3]/250),w+=x*g[A],S+=x*g[A+1],T+=x*g[A+2],C+=x))}v[y]=w/C,v[y+1]=S/C,v[y+2]=T/C,v[y+3]=O/_}return p},toObject:function(){return{type:this.type,scaleX:this.scaleX,scaleY:this.scaleY,resizeType:this.resizeType,lanczosLobes:this.lanczosLobes}}}),e.Image.filters.Resize.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.Contrast=r(i.BaseFilter,{type:"Contrast",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uContrast;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));\ncolor.rgb = contrastF * (color.rgb - 0.5) + 0.5;\ngl_FragColor = color;\n}",contrast:0,mainParameter:"contrast",applyTo2d:function(t){if(0!==this.contrast){var e,i,r=t.imageData,n=r.data,i=n.length,s=Math.floor(255*this.contrast),o=259*(s+255)/(255*(259-s));for(e=0;i>e;e+=4)n[e]=o*(n[e]-128)+128,n[e+1]=o*(n[e+1]-128)+128,n[e+2]=o*(n[e+2]-128)+128}},getUniformLocations:function(t,e){return{uContrast:t.getUniformLocation(e,"uContrast")}},sendUniformData:function(t,e){t.uniform1f(e.uContrast,this.contrast)}}),e.Image.filters.Contrast.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.Saturation=r(i.BaseFilter,{type:"Saturation",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uSaturation;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat rgMax = max(color.r, color.g);\nfloat rgbMax = max(rgMax, color.b);\ncolor.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;\ncolor.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;\ncolor.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;\ngl_FragColor = color;\n}",saturation:0,mainParameter:"saturation",applyTo2d:function(t){if(0!==this.saturation){var e,i,r=t.imageData,n=r.data,o=n.length,s=-this.saturation;for(e=0;o>e;e+=4)i=Math.max(n[e],n[e+1],n[e+2]),n[e]+=i!==n[e]?(i-n[e])*s:0,n[e+1]+=i!==n[e+1]?(i-n[e+1])*s:0,n[e+2]+=i!==n[e+2]?(i-n[e+2])*s:0}},getUniformLocations:function(t,e){return{uSaturation:t.getUniformLocation(e,"uSaturation")}},sendUniformData:function(t,e){t.uniform1f(e.uSaturation,-this.saturation)}}),e.Image.filters.Saturation.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),r=e.Image.filters,i=e.util.createClass;r.Blur=i(r.BaseFilter,{type:"Blur",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec2 uDelta;\nvarying vec2 vTexCoord;\nconst float nSamples = 15.0;\nvec3 v3offset = vec3(12.9898, 78.233, 151.7182);\nfloat random(vec3 scale) {\nreturn fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);\n}\nvoid main() {\nvec4 color = vec4(0.0);\nfloat total = 0.0;\nfloat offset = random(v3offset);\nfor (float t = -nSamples; t <= nSamples; t++) {\nfloat percent = (t + offset - 0.5) / nSamples;\nfloat weight = 1.0 - abs(percent);\ncolor += texture2D(uTexture, vTexCoord + uDelta * percent) * weight;\ntotal += weight;\n}\ngl_FragColor = color / total;\n}",blur:0,mainParameter:"blur",applyTo:function(t){t.webgl?(this.aspectRatio=t.sourceWidth/t.sourceHeight,t.passes++,this._setupFrameBuffer(t),this.horizontal=!0,this.applyToWebGL(t),this._swapTextures(t),this._setupFrameBuffer(t),this.horizontal=!1,this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)},applyTo2d:function(t){t.imageData=this.simpleBlur(t)},simpleBlur:function(t){var r,i,n=t.filterBackend.resources,o=t.imageData.width,s=t.imageData.height;n.blurLayer1||(n.blurLayer1=e.util.createCanvasElement(),n.blurLayer2=e.util.createCanvasElement()),r=n.blurLayer1,i=n.blurLayer2,(r.width!==o||r.height!==s)&&(i.width=r.width=o,i.height=r.height=s);var a,c,l,h,u=r.getContext("2d"),f=i.getContext("2d"),d=15,g=.06*this.blur*.5;for(u.putImageData(t.imageData,0,0),f.clearRect(0,0,o,s),h=-d;d>=h;h++)a=(Math.random()-.5)/4,c=h/d,l=g*c*o+a,f.globalAlpha=1-Math.abs(c),f.drawImage(r,l,a),u.drawImage(i,0,0),f.globalAlpha=1,f.clearRect(0,0,i.width,i.height);for(h=-d;d>=h;h++)a=(Math.random()-.5)/4,c=h/d,l=g*c*s+a,f.globalAlpha=1-Math.abs(c),f.drawImage(r,a,l),u.drawImage(i,0,0),f.globalAlpha=1,f.clearRect(0,0,i.width,i.height);t.ctx.drawImage(r,0,0);var p=t.ctx.getImageData(0,0,r.width,r.height);return u.globalAlpha=1,u.clearRect(0,0,r.width,r.height),p},getUniformLocations:function(t,e){return{delta:t.getUniformLocation(e,"uDelta")}},sendUniformData:function(t,e){var r=this.chooseRightDelta();t.uniform2fv(e.delta,r)},chooseRightDelta:function(){var t,e=1,r=[0,0];return this.horizontal?this.aspectRatio>1&&(e=1/this.aspectRatio):this.aspectRatio<1&&(e=this.aspectRatio),t=e*this.blur*.12,this.horizontal?r[0]=t:r[1]=t,r}}),r.Blur.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.Gamma=r(i.BaseFilter,{type:"Gamma",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec3 uGamma;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec3 correction = (1.0 / uGamma);\ncolor.r = pow(color.r, correction.r);\ncolor.g = pow(color.g, correction.g);\ncolor.b = pow(color.b, correction.b);\ngl_FragColor = color;\ngl_FragColor.rgb *= color.a;\n}",gamma:[1,1,1],mainParameter:"gamma",initialize:function(t){this.gamma=[1,1,1],i.BaseFilter.prototype.initialize.call(this,t)},applyTo2d:function(t){var e,i=t.imageData,r=i.data,n=this.gamma,o=r.length,s=1/n[0],a=1/n[1],c=1/n[2];for(this.rVals||(this.rVals=new Uint8Array(256),this.gVals=new Uint8Array(256),this.bVals=new Uint8Array(256)),e=0,o=256;o>e;e++)this.rVals[e]=255*Math.pow(e/255,s),this.gVals[e]=255*Math.pow(e/255,a),this.bVals[e]=255*Math.pow(e/255,c);for(e=0,o=r.length;o>e;e+=4)r[e]=this.rVals[r[e]],r[e+1]=this.gVals[r[e+1]],r[e+2]=this.bVals[r[e+2]]},getUniformLocations:function(t,e){return{uGamma:t.getUniformLocation(e,"uGamma")}},sendUniformData:function(t,e){t.uniform3fv(e.uGamma,this.gamma)}}),e.Image.filters.Gamma.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.Composed=r(i.BaseFilter,{type:"Composed",subFilters:[],initialize:function(t){this.callSuper("initialize",t),this.subFilters=this.subFilters.slice(0)},applyTo:function(t){t.passes+=this.subFilters.length-1,this.subFilters.forEach(function(e){e.applyTo(t)})},toObject:function(){return e.util.object.extend(this.callSuper("toObject"),{subFilters:this.subFilters.map(function(t){return t.toObject()})})},isNeutralState:function(){return!this.subFilters.some(function(t){return!t.isNeutralState()})}}),e.Image.filters.Composed.fromObject=function(t,i){var r=t.subFilters||[],n=r.map(function(t){return new e.Image.filters[t.type](t)}),o=new e.Image.filters.Composed({subFilters:n});return i&&i(o),o}}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,r=e.util.createClass;i.HueRotation=r(i.ColorMatrix,{type:"HueRotation",rotation:0,mainParameter:"rotation",calculateMatrix:function(){var t=this.rotation*Math.PI,i=e.util.cos(t),r=e.util.sin(t),n=1/3,o=Math.sqrt(n)*r,s=1-i;this.matrix=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],this.matrix[0]=i+s/3,this.matrix[1]=n*s-o,this.matrix[2]=n*s+o,this.matrix[5]=n*s+o,this.matrix[6]=i+n*s,this.matrix[7]=n*s-o,this.matrix[10]=n*s-o,this.matrix[11]=n*s+o,this.matrix[12]=i+n*s},isNeutralState:function(t){return this.calculateMatrix(),i.BaseFilter.prototype.isNeutralState.call(this,t)},applyTo:function(t){this.calculateMatrix(),i.BaseFilter.prototype.applyTo.call(this,t)}}),e.Image.filters.HueRotation.fromObject=e.Image.filters.BaseFilter.fromObject}("undefined"!=typeof exports?exports:this);!function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.clone;if(e.Text)return void e.warn("fabric.Text is already defined");var r="fontFamily fontWeight fontSize text underline overline linethrough textAlign fontStyle lineHeight textBackgroundColor charSpacing styles direction path pathStartOffset pathSide pathAlign".split(" ");e.Text=e.util.createClass(e.Object,{_dimensionAffectingProps:["fontSize","fontWeight","fontFamily","fontStyle","lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide","pathAlign"],_reNewline:/\r?\n/,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,type:"text",fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:"left",fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stateProperties:e.Object.prototype.stateProperties.concat(r),cacheProperties:e.Object.prototype.cacheProperties.concat(r),stroke:null,shadow:null,path:null,pathStartOffset:0,pathSide:"left",pathAlign:"baseline",_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,styles:null,_measuringContext:null,deltaY:0,direction:"ltr",_styleProperties:["stroke","strokeWidth","fill","fontFamily","fontSize","fontWeight","fontStyle","underline","overline","linethrough","deltaY","textBackgroundColor"],__charBounds:[],CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2,initialize:function(t,e){this.styles=e?e.styles||{}:{},this.text=t,this.__skipDimension=!0,this.callSuper("initialize",e),this.path&&this.setPathInfo(),this.__skipDimension=!1,this.initDimensions(),this.setCoords(),this.setupState({propertySet:"_dimensionAffectingProps"})},setPathInfo:function(){var t=this.path;t&&(t.segmentsInfo=e.util.getPathSegmentsInfo(t.path))},getMeasuringContext:function(){return e._measuringContext||(e._measuringContext=this.canvas&&this.canvas.contextCache||e.util.createCanvasElement().getContext("2d")),e._measuringContext},_splitText:function(){var t=this._splitTextIntoLines(this.text);return this.textLines=t.lines,this._textLines=t.graphemeLines,this._unwrappedTextLines=t._unwrappedLines,this._text=t.graphemeText,t},initDimensions:function(){this.__skipDimension||(this._splitText(),this._clearCache(),this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),-1!==this.textAlign.indexOf("justify")&&this.enlargeSpaces(),this.saveState({propertySet:"_dimensionAffectingProps"}))},enlargeSpaces:function(){for(var t,e,i,r,n,s,o,a=0,c=this._textLines.length;c>a;a++)if(("justify"===this.textAlign||a!==c-1&&!this.isEndOfWrapping(a))&&(r=0,n=this._textLines[a],e=this.getLineWidth(a),e<this.width&&(o=this.textLines[a].match(this._reSpacesAndTabs)))){i=o.length,t=(this.width-e)/i;for(var l=0,h=n.length;h>=l;l++)s=this.__charBounds[a][l],this._reSpaceAndTab.test(n[l])?(s.width+=t,s.kernedWidth+=t,s.left+=r,r+=t):s.left+=r}},isEndOfWrapping:function(t){return t===this._textLines.length-1},missingNewlineOffset:function(){return 1},toString:function(){return"#<fabric.Text ("+this.complexity()+'): { "text": "'+this.text+'", "fontFamily": "'+this.fontFamily+'" }>'},_getCacheCanvasDimensions:function(){var t=this.callSuper("_getCacheCanvasDimensions"),e=this.fontSize;return t.width+=e*t.zoomX,t.height+=e*t.zoomY,t},_render:function(t){var e=this.path;e&&!e.isNotVisible()&&e._render(t),this._setTextStyles(t),this._renderTextLinesBackground(t),this._renderTextDecoration(t,"underline"),this._renderText(t),this._renderTextDecoration(t,"overline"),this._renderTextDecoration(t,"linethrough")},_renderText:function(t){"stroke"===this.paintFirst?(this._renderTextStroke(t),this._renderTextFill(t)):(this._renderTextFill(t),this._renderTextStroke(t))},_setTextStyles:function(t,e,i){if(t.textBaseline="alphabetical",this.path)switch(this.pathAlign){case"center":t.textBaseline="middle";break;case"ascender":t.textBaseline="top";break;case"descender":t.textBaseline="bottom"}t.font=this._getFontDeclaration(e,i)},calcTextWidth:function(){for(var t=this.getLineWidth(0),e=1,i=this._textLines.length;i>e;e++){var r=this.getLineWidth(e);r>t&&(t=r)}return t},_renderTextLine:function(t,e,i,r,n,s){this._renderChars(t,e,i,r,n,s)},_renderTextLinesBackground:function(t){if(this.textBackgroundColor||this.styleHas("textBackgroundColor")){for(var e,i,r,n,s,o,a,c=t.fillStyle,l=this._getLeftOffset(),h=this._getTopOffset(),u=0,f=0,d=this.path,g=0,p=this._textLines.length;p>g;g++)if(e=this.getHeightOfLine(g),this.textBackgroundColor||this.styleHas("textBackgroundColor",g)){r=this._textLines[g],i=this._getLineLeftOffset(g),f=0,u=0,n=this.getValueOfPropertyAt(g,0,"textBackgroundColor");for(var v=0,m=r.length;m>v;v++)s=this.__charBounds[g][v],o=this.getValueOfPropertyAt(g,v,"textBackgroundColor"),d?(t.save(),t.translate(s.renderLeft,s.renderTop),t.rotate(s.angle),t.fillStyle=o,o&&t.fillRect(-s.width/2,-e/this.lineHeight*(1-this._fontSizeFraction),s.width,e/this.lineHeight),t.restore()):o!==n?(a=l+i+u,"rtl"===this.direction&&(a=this.width-a-f),t.fillStyle=n,n&&t.fillRect(a,h,f,e/this.lineHeight),u=s.left,f=s.width,n=o):f+=s.kernedWidth;o&&!d&&(a=l+i+u,"rtl"===this.direction&&(a=this.width-a-f),t.fillStyle=o,t.fillRect(a,h,f,e/this.lineHeight)),h+=e}else h+=e;t.fillStyle=c,this._removeShadow(t)}},getFontCache:function(t){var i=t.fontFamily.toLowerCase();e.charWidthsCache[i]||(e.charWidthsCache[i]={});var r=e.charWidthsCache[i],n=t.fontStyle.toLowerCase()+"_"+(t.fontWeight+"").toLowerCase();return r[n]||(r[n]={}),r[n]},_measureChar:function(t,e,i,r){var n,s,o,a,c=this.getFontCache(e),l=this._getFontDeclaration(e),h=this._getFontDeclaration(r),u=i+t,f=l===h,d=e.fontSize/this.CACHE_FONT_SIZE;if(i&&void 0!==c[i]&&(o=c[i]),void 0!==c[t]&&(a=n=c[t]),f&&void 0!==c[u]&&(s=c[u],a=s-o),void 0===n||void 0===o||void 0===s){var g=this.getMeasuringContext();this._setTextStyles(g,e,!0)}return void 0===n&&(a=n=g.measureText(t).width,c[t]=n),void 0===o&&f&&i&&(o=g.measureText(i).width,c[i]=o),f&&void 0===s&&(s=g.measureText(u).width,c[u]=s,a=s-o),{width:n*d,kernedWidth:a*d}},getHeightOfChar:function(t,e){return this.getValueOfPropertyAt(t,e,"fontSize")},measureLine:function(t){var e=this._measureLine(t);return 0!==this.charSpacing&&(e.width-=this._getWidthOfCharSpacing()),e.width<0&&(e.width=0),e},_measureLine:function(t){var i,r,n,s,o,a,c=0,l=this._textLines[t],h=0,u=new Array(l.length),f=0,d=this.path,g="right"===this.pathSide;for(this.__charBounds[t]=u,i=0;i<l.length;i++)r=l[i],s=this._getGraphemeBox(r,t,i,n),u[i]=s,c+=s.kernedWidth,n=r;if(u[i]={left:s?s.left+s.width:0,width:0,kernedWidth:0,height:this.fontSize},d){switch(a=d.segmentsInfo[d.segmentsInfo.length-1].length,o=e.util.getPointOnPath(d.path,0,d.segmentsInfo),o.x+=d.pathOffset.x,o.y+=d.pathOffset.y,this.textAlign){case"left":f=g?a-c:0;break;case"center":f=(a-c)/2;break;case"right":f=g?0:a-c}for(f+=this.pathStartOffset*(g?-1:1),i=g?l.length-1:0;g?i>=0:i<l.length;g?i--:i++)s=u[i],f>a?f%=a:0>f&&(f+=a),this._setGraphemeOnPath(f,s,o),f+=s.kernedWidth}return{width:c,numOfSpaces:h}},_setGraphemeOnPath:function(t,i,r){var n=t+i.kernedWidth/2,s=this.path,o=e.util.getPointOnPath(s.path,n,s.segmentsInfo);i.renderLeft=o.x-r.x,i.renderTop=o.y-r.y,i.angle=o.angle+("right"===this.pathSide?Math.PI:0)},_getGraphemeBox:function(t,e,i,r,n){var s,o=this.getCompleteStyleDeclaration(e,i),a=r?this.getCompleteStyleDeclaration(e,i-1):{},c=this._measureChar(t,o,r,a),l=c.kernedWidth,h=c.width;0!==this.charSpacing&&(s=this._getWidthOfCharSpacing(),h+=s,l+=s);var u={width:h,left:0,height:o.fontSize,kernedWidth:l,deltaY:o.deltaY};if(i>0&&!n){var f=this.__charBounds[e][i-1];u.left=f.left+f.width+c.kernedWidth-c.width}return u},getHeightOfLine:function(t){if(this.__lineHeights[t])return this.__lineHeights[t];for(var e=this._textLines[t],i=this.getHeightOfChar(t,0),r=1,n=e.length;n>r;r++)i=Math.max(this.getHeightOfChar(t,r),i);return this.__lineHeights[t]=i*this.lineHeight*this._fontSizeMult},calcTextHeight:function(){for(var t,e=0,i=0,r=this._textLines.length;r>i;i++)t=this.getHeightOfLine(i),e+=i===r-1?t/this.lineHeight:t;return e},_getLeftOffset:function(){return"ltr"===this.direction?-this.width/2:this.width/2},_getTopOffset:function(){return-this.height/2},_renderTextCommon:function(t,e){t.save();for(var i=0,r=this._getLeftOffset(),n=this._getTopOffset(),s=0,o=this._textLines.length;o>s;s++){var a=this.getHeightOfLine(s),c=a/this.lineHeight,l=this._getLineLeftOffset(s);this._renderTextLine(e,t,this._textLines[s],r+l,n+i+c,s),i+=a}t.restore()},_renderTextFill:function(t){(this.fill||this.styleHas("fill"))&&this._renderTextCommon(t,"fillText")},_renderTextStroke:function(t){(this.stroke&&0!==this.strokeWidth||!this.isEmptyStyles())&&(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this._setLineDash(t,this.strokeDashArray),t.beginPath(),this._renderTextCommon(t,"strokeText"),t.closePath(),t.restore())},_renderChars:function(t,i,r,n,s,o){var a,c,l,h,u,f=this.getHeightOfLine(o),d=-1!==this.textAlign.indexOf("justify"),g="",p=0,v=this.path,m=!d&&0===this.charSpacing&&this.isEmptyStyles(o)&&!v,b="ltr"===this.direction,y="ltr"===this.direction?1:-1,x=i.canvas.getAttribute("dir");if(i.save(),x!==this.direction&&(i.canvas.setAttribute("dir",b?"ltr":"rtl"),i.direction=b?"ltr":"rtl",i.textAlign=b?"left":"right"),s-=f*this._fontSizeFraction/this.lineHeight,m)return this._renderChar(t,i,o,0,r.join(""),n,s,f),void i.restore();for(var _=0,C=r.length-1;C>=_;_++)h=_===C||this.charSpacing||v,g+=r[_],l=this.__charBounds[o][_],0===p?(n+=y*(l.kernedWidth-l.width),p+=l.width):p+=l.kernedWidth,d&&!h&&this._reSpaceAndTab.test(r[_])&&(h=!0),h||(a=a||this.getCompleteStyleDeclaration(o,_),c=this.getCompleteStyleDeclaration(o,_+1),h=e.util.hasStyleChanged(a,c,!1)),h&&(v?(i.save(),i.translate(l.renderLeft,l.renderTop),i.rotate(l.angle),this._renderChar(t,i,o,_,g,-p/2,0,f),i.restore()):(u=n,this._renderChar(t,i,o,_,g,u,s,f)),g="",a=c,n+=y*p,p=0);i.restore()},_applyPatternGradientTransformText:function(t){var i,r=e.util.createCanvasElement(),n=this.width+this.strokeWidth,s=this.height+this.strokeWidth;return r.width=n,r.height=s,i=r.getContext("2d"),i.beginPath(),i.moveTo(0,0),i.lineTo(n,0),i.lineTo(n,s),i.lineTo(0,s),i.closePath(),i.translate(n/2,s/2),i.fillStyle=t.toLive(i),this._applyPatternGradientTransform(i,t),i.fill(),i.createPattern(r,"no-repeat")},handleFiller:function(t,e,i){var r,n;return i.toLive?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?(r=-this.width/2,n=-this.height/2,t.translate(r,n),t[e]=this._applyPatternGradientTransformText(i),{offsetX:r,offsetY:n}):(t[e]=i.toLive(t,this),this._applyPatternGradientTransform(t,i)):(t[e]=i,{offsetX:0,offsetY:0})},_setStrokeStyles:function(t,e){return t.lineWidth=e.strokeWidth,t.lineCap=this.strokeLineCap,t.lineDashOffset=this.strokeDashOffset,t.lineJoin=this.strokeLineJoin,t.miterLimit=this.strokeMiterLimit,this.handleFiller(t,"strokeStyle",e.stroke)},_setFillStyles:function(t,e){return this.handleFiller(t,"fillStyle",e.fill)},_renderChar:function(t,e,i,r,n,s,o){var a,c,l=this._getStyleDeclaration(i,r),h=this.getCompleteStyleDeclaration(i,r),u="fillText"===t&&h.fill,f="strokeText"===t&&h.stroke&&h.strokeWidth;(f||u)&&(e.save(),u&&(a=this._setFillStyles(e,h)),f&&(c=this._setStrokeStyles(e,h)),e.font=this._getFontDeclaration(h),l&&l.textBackgroundColor&&this._removeShadow(e),l&&l.deltaY&&(o+=l.deltaY),u&&e.fillText(n,s-a.offsetX,o-a.offsetY),f&&e.strokeText(n,s-c.offsetX,o-c.offsetY),e.restore())},setSuperscript:function(t,e){return this._setScript(t,e,this.superscript)},setSubscript:function(t,e){return this._setScript(t,e,this.subscript)},_setScript:function(t,e,i){var r=this.get2DCursorLocation(t,!0),n=this.getValueOfPropertyAt(r.lineIndex,r.charIndex,"fontSize"),s=this.getValueOfPropertyAt(r.lineIndex,r.charIndex,"deltaY"),o={fontSize:n*i.size,deltaY:s+n*i.baseline};return this.setSelectionStyles(o,t,e),this},_getLineLeftOffset:function(t){var e,i=this.getLineWidth(t),r=this.width-i,n=this.textAlign,s=this.direction,o=0,e=this.isEndOfWrapping(t);return"justify"===n||"justify-center"===n&&!e||"justify-right"===n&&!e||"justify-left"===n&&!e?0:("center"===n&&(o=r/2),"right"===n&&(o=r),"justify-center"===n&&(o=r/2),"justify-right"===n&&(o=r),"rtl"===s&&(o-=r),o)},_clearCache:function(){this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]},_shouldClearDimensionCache:function(){var t=this._forceClearCache;return t||(t=this.hasStateChanged("_dimensionAffectingProps")),t&&(this.dirty=!0,this._forceClearCache=!1),t},getLineWidth:function(t){if(void 0!==this.__lineWidths[t])return this.__lineWidths[t];var e=this.measureLine(t),i=e.width;return this.__lineWidths[t]=i,i},_getWidthOfCharSpacing:function(){return 0!==this.charSpacing?this.fontSize*this.charSpacing/1e3:0},getValueOfPropertyAt:function(t,e,i){var r=this._getStyleDeclaration(t,e);return r&&"undefined"!=typeof r[i]?r[i]:this[i]},_renderTextDecoration:function(t,e){if(this[e]||this.styleHas(e)){for(var i,r,n,s,o,a,c,l,h,u,f,d,g,p,v,m,b=this._getLeftOffset(),y=this._getTopOffset(),x=this.path,_=this._getWidthOfCharSpacing(),C=this.offsets[e],w=0,S=this._textLines.length;S>w;w++)if(i=this.getHeightOfLine(w),this[e]||this.styleHas(e,w)){c=this._textLines[w],p=i/this.lineHeight,s=this._getLineLeftOffset(w),u=0,f=0,l=this.getValueOfPropertyAt(w,0,e),m=this.getValueOfPropertyAt(w,0,"fill"),h=y+p*(1-this._fontSizeFraction),r=this.getHeightOfChar(w,0),o=this.getValueOfPropertyAt(w,0,"deltaY");for(var T=0,O=c.length;O>T;T++)if(d=this.__charBounds[w][T],g=this.getValueOfPropertyAt(w,T,e),v=this.getValueOfPropertyAt(w,T,"fill"),n=this.getHeightOfChar(w,T),a=this.getValueOfPropertyAt(w,T,"deltaY"),x&&g&&v)t.save(),t.fillStyle=m,t.translate(d.renderLeft,d.renderTop),t.rotate(d.angle),t.fillRect(-d.kernedWidth/2,C*n+a,d.kernedWidth,this.fontSize/15),t.restore();else if((g!==l||v!==m||n!==r||a!==o)&&f>0){var j=b+s+u;"rtl"===this.direction&&(j=this.width-j-f),l&&m&&(t.fillStyle=m,t.fillRect(j,h+C*r+o,f,this.fontSize/15)),u=d.left,f=d.width,l=g,m=v,r=n,o=a}else f+=d.kernedWidth;var j=b+s+u;"rtl"===this.direction&&(j=this.width-j-f),t.fillStyle=v,g&&v&&t.fillRect(j,h+C*r+o,f-_,this.fontSize/15),y+=i}else y+=i;this._removeShadow(t)}},_getFontDeclaration:function(t,i){var r=t||this,n=this.fontFamily,s=e.Text.genericFonts.indexOf(n.toLowerCase())>-1,o=void 0===n||n.indexOf("'")>-1||n.indexOf(",")>-1||n.indexOf('"')>-1||s?r.fontFamily:'"'+r.fontFamily+'"';return[e.isLikelyNode?r.fontWeight:r.fontStyle,e.isLikelyNode?r.fontStyle:r.fontWeight,i?this.CACHE_FONT_SIZE+"px":r.fontSize+"px",o].join(" ")},render:function(t){this.visible&&(!this.canvas||!this.canvas.skipOffscreen||this.group||this.isOnScreen())&&(this._shouldClearDimensionCache()&&this.initDimensions(),this.callSuper("render",t))},_splitTextIntoLines:function(t){for(var i=t.split(this._reNewline),r=new Array(i.length),n=["\n"],s=[],o=0;o<i.length;o++)r[o]=e.util.string.graphemeSplit(i[o]),s=s.concat(r[o],n);return s.pop(),{_unwrappedLines:r,lines:i,graphemeText:s,graphemeLines:r}},toObject:function(t){var i=r.concat(t),n=this.callSuper("toObject",i);return n.styles=e.util.stylesToArray(this.styles,this.text),n.path&&(n.path=this.path.toObject()),n},set:function(t,e){this.callSuper("set",t,e);var i=!1,r=!1;if("object"==typeof t)for(var n in t)"path"===n&&this.setPathInfo(),i=i||-1!==this._dimensionAffectingProps.indexOf(n),r=r||"path"===n;else i=-1!==this._dimensionAffectingProps.indexOf(t),r="path"===t;return r&&this.setPathInfo(),i&&(this.initDimensions(),this.setCoords()),this},complexity:function(){return 1}}),e.Text.fromObject=function(t,r){var n=i(t),s=t.path;return delete n.path,e.Object._fromObject("Text",n,function(i){i.styles=e.util.stylesFromArray(t.styles,t.text),s?e.Object._fromObject("Path",s,function(t){i.set("path",t),r(i)},"path"):r(i)},"text")},e.Text.genericFonts=["sans-serif","serif","cursive","fantasy","monospace"],e.util.createAccessors&&e.util.createAccessors(e.Text)}("undefined"!=typeof exports?exports:this);!function(){fabric.util.object.extend(fabric.Text.prototype,{isEmptyStyles:function(t){if(!this.styles)return!0;if("undefined"!=typeof t&&!this.styles[t])return!0;var e="undefined"==typeof t?this.styles:{line:this.styles[t]};for(var i in e)for(var r in e[i])for(var n in e[i][r])return!1;return!0},styleHas:function(t,e){if(!this.styles||!t||""===t)return!1;if("undefined"!=typeof e&&!this.styles[e])return!1;var i="undefined"==typeof e?this.styles:{0:this.styles[e]};for(var r in i)for(var n in i[r])if("undefined"!=typeof i[r][n][t])return!0;return!1},cleanStyle:function(t){if(!this.styles||!t||""===t)return!1;var e,i,r,n=this.styles,s=0,o=!0,a=0;for(var c in n){e=0;for(var l in n[c]){var r=n[c][l],h=r.hasOwnProperty(t);s++,h?(i?r[t]!==i&&(o=!1):i=r[t],r[t]===this[t]&&delete r[t]):o=!1,0!==Object.keys(r).length?e++:delete n[c][l]}0===e&&delete n[c]}for(var u=0;u<this._textLines.length;u++)a+=this._textLines[u].length;o&&s===a&&(this[t]=i,this.removeStyle(t))},removeStyle:function(t){if(this.styles&&t&&""!==t){var e,i,r,n=this.styles;for(i in n){e=n[i];for(r in e)delete e[r][t],0===Object.keys(e[r]).length&&delete e[r];0===Object.keys(e).length&&delete n[i]}}},_extendStyles:function(t,e){var i=this.get2DCursorLocation(t);this._getLineStyle(i.lineIndex)||this._setLineStyle(i.lineIndex),this._getStyleDeclaration(i.lineIndex,i.charIndex)||this._setStyleDeclaration(i.lineIndex,i.charIndex,{}),fabric.util.object.extend(this._getStyleDeclaration(i.lineIndex,i.charIndex),e)},get2DCursorLocation:function(t,e){"undefined"==typeof t&&(t=this.selectionStart);for(var i=e?this._unwrappedTextLines:this._textLines,r=i.length,n=0;r>n;n++){if(t<=i[n].length)return{lineIndex:n,charIndex:t};t-=i[n].length+this.missingNewlineOffset(n)}return{lineIndex:n-1,charIndex:i[n-1].length<t?i[n-1].length:t}},getSelectionStyles:function(t,e,i){"undefined"==typeof t&&(t=this.selectionStart||0),"undefined"==typeof e&&(e=this.selectionEnd||t);for(var r=[],n=t;e>n;n++)r.push(this.getStyleAtPosition(n,i));return r},getStyleAtPosition:function(t,e){var i=this.get2DCursorLocation(t),r=e?this.getCompleteStyleDeclaration(i.lineIndex,i.charIndex):this._getStyleDeclaration(i.lineIndex,i.charIndex);return r||{}},setSelectionStyles:function(t,e,i){"undefined"==typeof e&&(e=this.selectionStart||0),"undefined"==typeof i&&(i=this.selectionEnd||e);for(var r=e;i>r;r++)this._extendStyles(r,t);return this._forceClearCache=!0,this},_getStyleDeclaration:function(t,e){var i=this.styles&&this.styles[t];return i?i[e]:null},getCompleteStyleDeclaration:function(t,e){for(var i,r=this._getStyleDeclaration(t,e)||{},n={},s=0;s<this._styleProperties.length;s++)i=this._styleProperties[s],n[i]="undefined"==typeof r[i]?this[i]:r[i];return n},_setStyleDeclaration:function(t,e,i){this.styles[t][e]=i},_deleteStyleDeclaration:function(t,e){delete this.styles[t][e]},_getLineStyle:function(t){return!!this.styles[t]},_setLineStyle:function(t){this.styles[t]={}},_deleteLineStyle:function(t){delete this.styles[t]}})}();!function(){function t(t){t.textDecoration&&(t.textDecoration.indexOf("underline")>-1&&(t.underline=!0),t.textDecoration.indexOf("line-through")>-1&&(t.linethrough=!0),t.textDecoration.indexOf("overline")>-1&&(t.overline=!0),delete t.textDecoration)}fabric.IText=fabric.util.createClass(fabric.Text,fabric.Observable,{type:"i-text",selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,_reSpace:/\s|\n/,_currentCursorOpacity:0,_selectionDirection:null,_abortCursorAnimation:!1,__widthOfSpace:[],inCompositionMode:!1,initialize:function(t,e){this.callSuper("initialize",t,e),this.initBehavior()},setSelectionStart:function(t){t=Math.max(t,0),this._updateAndFire("selectionStart",t)},setSelectionEnd:function(t){t=Math.min(t,this.text.length),this._updateAndFire("selectionEnd",t)},_updateAndFire:function(t,e){this[t]!==e&&(this._fireSelectionChanged(),this[t]=e),this._updateTextarea()},_fireSelectionChanged:function(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})},initDimensions:function(){this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this.callSuper("initDimensions")},render:function(t){this.clearContextTop(),this.callSuper("render",t),this.cursorOffsetCache={},this.renderCursorOrSelection()},_render:function(t){this.callSuper("_render",t)},clearContextTop:function(t){if(this.isEditing&&this.canvas&&this.canvas.contextTop){var e=this.canvas.contextTop,r=this.canvas.viewportTransform;e.save(),e.transform(r[0],r[1],r[2],r[3],r[4],r[5]),this.transform(e),this._clearTextArea(e),t||e.restore()}},renderCursorOrSelection:function(){if(this.isEditing&&this.canvas&&this.canvas.contextTop){var t=this._getCursorBoundaries(),e=this.canvas.contextTop;this.clearContextTop(!0),this.selectionStart===this.selectionEnd?this.renderCursor(t,e):this.renderSelection(t,e),e.restore()}},_clearTextArea:function(t){var e=this.width+4,r=this.height+4;t.clearRect(-e/2,-r/2,e,r)},_getCursorBoundaries:function(t){"undefined"==typeof t&&(t=this.selectionStart);var e=this._getLeftOffset(),r=this._getTopOffset(),i=this._getCursorBoundariesOffsets(t);return{left:e,top:r,leftOffset:i.left,topOffset:i.top}},_getCursorBoundariesOffsets:function(t){if(this.cursorOffsetCache&&"top"in this.cursorOffsetCache)return this.cursorOffsetCache;var e,r,i,n,o=0,s=0,a=this.get2DCursorLocation(t);i=a.charIndex,r=a.lineIndex;for(var c=0;r>c;c++)o+=this.getHeightOfLine(c);e=this._getLineLeftOffset(r);var l=this.__charBounds[r][i];return l&&(s=l.left),0!==this.charSpacing&&i===this._textLines[r].length&&(s-=this._getWidthOfCharSpacing()),n={top:o,left:e+(s>0?s:0)},"rtl"===this.direction&&(n.left*=-1),this.cursorOffsetCache=n,this.cursorOffsetCache},renderCursor:function(t,e){var r=this.get2DCursorLocation(),i=r.lineIndex,n=r.charIndex>0?r.charIndex-1:0,o=this.getValueOfPropertyAt(i,n,"fontSize"),s=this.scaleX*this.canvas.getZoom(),a=this.cursorWidth/s,c=t.topOffset,l=this.getValueOfPropertyAt(i,n,"deltaY");c+=(1-this._fontSizeFraction)*this.getHeightOfLine(i)/this.lineHeight-o*(1-this._fontSizeFraction),this.inCompositionMode&&this.renderSelection(t,e),e.fillStyle=this.cursorColor||this.getValueOfPropertyAt(i,n,"fill"),e.globalAlpha=this.__isMousedown?1:this._currentCursorOpacity,e.fillRect(t.left+t.leftOffset-a/2,c+t.top+l,a,o)},renderSelection:function(t,e){for(var r=this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,i=this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd,n=-1!==this.textAlign.indexOf("justify"),o=this.get2DCursorLocation(r),s=this.get2DCursorLocation(i),a=o.lineIndex,c=s.lineIndex,l=o.charIndex<0?0:o.charIndex,h=s.charIndex<0?0:s.charIndex,u=a;c>=u;u++){var f=this._getLineLeftOffset(u)||0,d=this.getHeightOfLine(u),g=0,p=0,v=0;if(u===a&&(p=this.__charBounds[a][l].left),u>=a&&c>u)v=n&&!this.isEndOfWrapping(u)?this.width:this.getLineWidth(u)||5;else if(u===c)if(0===h)v=this.__charBounds[c][h].left;else{var m=this._getWidthOfCharSpacing();v=this.__charBounds[c][h-1].left+this.__charBounds[c][h-1].width-m}g=d,(this.lineHeight<1||u===c&&this.lineHeight>1)&&(d/=this.lineHeight);var b=t.left+f+p,y=v-p,x=d,C=0;this.inCompositionMode?(e.fillStyle=this.compositionColor||"black",x=1,C=d):e.fillStyle=this.selectionColor,"rtl"===this.direction&&(b=this.width-b-y),e.fillRect(b,t.top+t.topOffset+C,y,x),t.topOffset+=g}},getCurrentCharFontSize:function(){var t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fontSize")},getCurrentCharColor:function(){var t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fill")},_getCurrentCharIndex:function(){var t=this.get2DCursorLocation(this.selectionStart,!0),e=t.charIndex>0?t.charIndex-1:0;return{l:t.lineIndex,c:e}}}),fabric.IText.fromObject=function(e,r){if(e.styles=fabric.util.stylesFromArray(e.styles,e.text),t(e),e.styles)for(var i in e.styles)for(var n in e.styles[i])t(e.styles[i][n]);fabric.Object._fromObject("IText",e,r,"text")}}();!function(){var t=fabric.util.object.clone;fabric.util.object.extend(fabric.IText.prototype,{initBehavior:function(){this.initAddedHandler(),this.initRemovedHandler(),this.initCursorSelectionHandlers(),this.initDoubleClickSimulation(),this.mouseMoveHandler=this.mouseMoveHandler.bind(this)},onDeselect:function(){this.isEditing&&this.exitEditing(),this.selected=!1},initAddedHandler:function(){var t=this;this.on("added",function(){var e=t.canvas;e&&(e._hasITextHandlers||(e._hasITextHandlers=!0,t._initCanvasHandlers(e)),e._iTextInstances=e._iTextInstances||[],e._iTextInstances.push(t))})},initRemovedHandler:function(){var t=this;this.on("removed",function(){var e=t.canvas;e&&(e._iTextInstances=e._iTextInstances||[],fabric.util.removeFromArray(e._iTextInstances,t),0===e._iTextInstances.length&&(e._hasITextHandlers=!1,t._removeCanvasHandlers(e)))})},_initCanvasHandlers:function(t){t._mouseUpITextHandler=function(){t._iTextInstances&&t._iTextInstances.forEach(function(t){t.__isMousedown=!1})},t.on("mouse:up",t._mouseUpITextHandler)},_removeCanvasHandlers:function(t){t.off("mouse:up",t._mouseUpITextHandler)},_tick:function(){this._currentTickState=this._animateCursor(this,1,this.cursorDuration,"_onTickComplete")},_animateCursor:function(t,e,i,r){var n;return n={isAborted:!1,abort:function(){this.isAborted=!0}},t.animate("_currentCursorOpacity",e,{duration:i,onComplete:function(){n.isAborted||t[r]()},onChange:function(){t.canvas&&t.selectionStart===t.selectionEnd&&t.renderCursorOrSelection()},abort:function(){return n.isAborted}}),n},_onTickComplete:function(){var t=this;this._cursorTimeout1&&clearTimeout(this._cursorTimeout1),this._cursorTimeout1=setTimeout(function(){t._currentTickCompleteState=t._animateCursor(t,0,this.cursorDuration/2,"_tick")},100)},initDelayedCursor:function(t){var e=this,i=t?0:this.cursorDelay;this.abortCursorAnimation(),this._currentCursorOpacity=1,this._cursorTimeout2=setTimeout(function(){e._tick()},i)},abortCursorAnimation:function(){var t=this._currentTickState||this._currentTickCompleteState,e=this.canvas;this._currentTickState&&this._currentTickState.abort(),this._currentTickCompleteState&&this._currentTickCompleteState.abort(),clearTimeout(this._cursorTimeout1),clearTimeout(this._cursorTimeout2),this._currentCursorOpacity=0,t&&e&&e.clearContext(e.contextTop||e.contextContainer)},selectAll:function(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this},getSelectedText:function(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")},findWordBoundaryLeft:function(t){var e=0,i=t-1;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i--;for(;/\S/.test(this._text[i])&&i>-1;)e++,i--;return t-e},findWordBoundaryRight:function(t){var e=0,i=t;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i++;for(;/\S/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e},findLineBoundaryLeft:function(t){for(var e=0,i=t-1;!/\n/.test(this._text[i])&&i>-1;)e++,i--;return t-e},findLineBoundaryRight:function(t){for(var e=0,i=t;!/\n/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e},searchWordBoundary:function(t,e){for(var i=this._text,r=this._reSpace.test(i[t])?t-1:t,n=i[r],s=fabric.reNonWord;!s.test(n)&&r>0&&r<i.length;)r+=e,n=i[r];return s.test(n)&&(r+=1===e?0:1),r},selectWord:function(t){t=t||this.selectionStart;var e=this.searchWordBoundary(t,-1),i=this.searchWordBoundary(t,1);this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()},selectLine:function(t){t=t||this.selectionStart;var e=this.findLineBoundaryLeft(t),i=this.findLineBoundaryRight(t);return this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this},enterEditing:function(t){return!this.isEditing&&this.editable?(this.canvas&&(this.canvas.calcOffset(),this.exitEditingOnOthers(this.canvas)),this.isEditing=!0,this.initHiddenTextarea(t),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick(),this.fire("editing:entered"),this._fireSelectionChanged(),this.canvas?(this.canvas.fire("text:editing:entered",{target:this}),this.initMouseMoveHandler(),this.canvas.requestRenderAll(),this):this):void 0},exitEditingOnOthers:function(t){t._iTextInstances&&t._iTextInstances.forEach(function(t){t.selected=!1,t.isEditing&&t.exitEditing()})},initMouseMoveHandler:function(){this.canvas.on("mouse:move",this.mouseMoveHandler)},mouseMoveHandler:function(t){if(this.__isMousedown&&this.isEditing){var e=this.getSelectionStartFromPointer(t.e),i=this.selectionStart,r=this.selectionEnd;(e===this.__selectionStartOnMouseDown&&i!==r||i!==e&&r!==e)&&(e>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=e):(this.selectionStart=e,this.selectionEnd=this.__selectionStartOnMouseDown),(this.selectionStart!==i||this.selectionEnd!==r)&&(this.restartCursorIfNeeded(),this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}},_setEditingProps:function(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0},fromStringToGraphemeSelection:function(t,e,i){var r=i.slice(0,t),n=fabric.util.string.graphemeSplit(r).length;if(t===e)return{selectionStart:n,selectionEnd:n};var s=i.slice(t,e),o=fabric.util.string.graphemeSplit(s).length;return{selectionStart:n,selectionEnd:n+o}},fromGraphemeToStringSelection:function(t,e,i){var r=i.slice(0,t),n=r.join("").length;if(t===e)return{selectionStart:n,selectionEnd:n};var s=i.slice(t,e),o=s.join("").length;return{selectionStart:n,selectionEnd:n+o}},_updateTextarea:function(){if(this.cursorOffsetCache={},this.hiddenTextarea){if(!this.inCompositionMode){var t=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=t.selectionStart,this.hiddenTextarea.selectionEnd=t.selectionEnd}this.updateTextareaPosition()}},updateFromTextArea:function(){if(this.hiddenTextarea){this.cursorOffsetCache={},this.text=this.hiddenTextarea.value,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords());var t=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value);this.selectionEnd=this.selectionStart=t.selectionEnd,this.inCompositionMode||(this.selectionStart=t.selectionStart),this.updateTextareaPosition()}},updateTextareaPosition:function(){if(this.selectionStart===this.selectionEnd){var t=this._calcTextareaPosition();this.hiddenTextarea.style.left=t.left,this.hiddenTextarea.style.top=t.top}},_calcTextareaPosition:function(){if(!this.canvas)return{x:1,y:1};var t=this.inCompositionMode?this.compositionStart:this.selectionStart,e=this._getCursorBoundaries(t),i=this.get2DCursorLocation(t),r=i.lineIndex,n=i.charIndex,s=this.getValueOfPropertyAt(r,n,"fontSize")*this.lineHeight,o=e.leftOffset,a=this.calcTransformMatrix(),c={x:e.left+o,y:e.top+e.topOffset+s},l=this.canvas.getRetinaScaling(),h=this.canvas.upperCanvasEl,u=h.width/l,f=h.height/l,d=u-s,g=f-s,p=h.clientWidth/u,v=h.clientHeight/f;return c=fabric.util.transformPoint(c,a),c=fabric.util.transformPoint(c,this.canvas.viewportTransform),c.x*=p,c.y*=v,c.x<0&&(c.x=0),c.x>d&&(c.x=d),c.y<0&&(c.y=0),c.y>g&&(c.y=g),c.x+=this.canvas._offset.left,c.y+=this.canvas._offset.top,{left:c.x+"px",top:c.y+"px",fontSize:s+"px",charHeight:s}},_saveEditingProps:function(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}},_restoreEditingProps:function(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor))},exitEditing:function(){var t=this._textBeforeEdit!==this.text,e=this.hiddenTextarea;return this.selected=!1,this.isEditing=!1,this.selectionEnd=this.selectionStart,e&&(e.blur&&e.blur(),e.parentNode&&e.parentNode.removeChild(e)),this.hiddenTextarea=null,this.abortCursorAnimation(),this._restoreEditingProps(),this._currentCursorOpacity=0,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this.fire("editing:exited"),t&&this.fire("modified"),this.canvas&&(this.canvas.off("mouse:move",this.mouseMoveHandler),this.canvas.fire("text:editing:exited",{target:this}),t&&this.canvas.fire("object:modified",{target:this})),this},_removeExtraneousStyles:function(){for(var t in this.styles)this._textLines[t]||delete this.styles[t]},removeStyleFromTo:function(t,e){var i,r,n=this.get2DCursorLocation(t,!0),s=this.get2DCursorLocation(e,!0),o=n.lineIndex,a=n.charIndex,c=s.lineIndex,l=s.charIndex;if(o!==c){if(this.styles[o])for(i=a;i<this._unwrappedTextLines[o].length;i++)delete this.styles[o][i];if(this.styles[c])for(i=l;i<this._unwrappedTextLines[c].length;i++)r=this.styles[c][i],r&&(this.styles[o]||(this.styles[o]={}),this.styles[o][a+i-l]=r);for(i=o+1;c>=i;i++)delete this.styles[i];this.shiftLineStyles(c,o-c)}else if(this.styles[o]){r=this.styles[o];var h,u,f=l-a;for(i=a;l>i;i++)delete r[i];for(u in this.styles[o])h=parseInt(u,10),h>=l&&(r[h-f]=r[u],delete r[u])}},shiftLineStyles:function(e,i){var r=t(this.styles);for(var n in this.styles){var s=parseInt(n,10);s>e&&(this.styles[s+i]=r[s],r[s-i]||delete this.styles[s])}},restartCursorIfNeeded:function(){(!this._currentTickState||this._currentTickState.isAborted||!this._currentTickCompleteState||this._currentTickCompleteState.isAborted)&&this.initDelayedCursor()},insertNewlineStyleObject:function(e,i,r,n){var s,o={},a=!1,c=this._unwrappedTextLines[e].length===i;r||(r=1),this.shiftLineStyles(e,r),this.styles[e]&&(s=this.styles[e][0===i?i:i-1]);for(var l in this.styles[e]){var h=parseInt(l,10);h>=i&&(a=!0,o[h-i]=this.styles[e][l],c&&0===i||delete this.styles[e][l])}var u=!1;for(a&&!c&&(this.styles[e+r]=o,u=!0),u&&r--;r>0;)n&&n[r-1]?this.styles[e+r]={0:t(n[r-1])}:s?this.styles[e+r]={0:t(s)}:delete this.styles[e+r],r--;this._forceClearCache=!0},insertCharStyleObject:function(e,i,r,n){this.styles||(this.styles={});var s=this.styles[e],o=s?t(s):{};r||(r=1);for(var a in o){var c=parseInt(a,10);c>=i&&(s[c+r]=o[c],o[c-r]||delete s[c])}if(this._forceClearCache=!0,n)for(;r--;)Object.keys(n[r]).length&&(this.styles[e]||(this.styles[e]={}),this.styles[e][i+r]=t(n[r]));else if(s)for(var l=s[i?i-1:1];l&&r--;)this.styles[e][i+r]=t(l)},insertNewStyleBlock:function(t,e,i){for(var r=this.get2DCursorLocation(e,!0),n=[0],s=0,o=0;o<t.length;o++)"\n"===t[o]?(s++,n[s]=0):n[s]++;n[0]>0&&(this.insertCharStyleObject(r.lineIndex,r.charIndex,n[0],i),i=i&&i.slice(n[0]+1)),s&&this.insertNewlineStyleObject(r.lineIndex,r.charIndex+n[0],s);for(var o=1;s>o;o++)n[o]>0?this.insertCharStyleObject(r.lineIndex+o,0,n[o],i):i&&this.styles[r.lineIndex+o]&&i[0]&&(this.styles[r.lineIndex+o][0]=i[0]),i=i&&i.slice(n[o]+1);n[o]>0&&this.insertCharStyleObject(r.lineIndex+o,0,n[o],i)},setSelectionStartEndWithShift:function(t,e,i){t>=i?(e===t?this._selectionDirection="left":"right"===this._selectionDirection&&(this._selectionDirection="left",this.selectionEnd=t),this.selectionStart=i):i>t&&e>i?"right"===this._selectionDirection?this.selectionEnd=i:this.selectionStart=i:(e===t?this._selectionDirection="right":"left"===this._selectionDirection&&(this._selectionDirection="right",this.selectionStart=e),this.selectionEnd=i)},setSelectionInBoundaries:function(){var t=this.text.length;this.selectionStart>t?this.selectionStart=t:this.selectionStart<0&&(this.selectionStart=0),this.selectionEnd>t?this.selectionEnd=t:this.selectionEnd<0&&(this.selectionEnd=0)}})}();fabric.util.object.extend(fabric.IText.prototype,{initDoubleClickSimulation:function(){this.__lastClickTime=+new Date,this.__lastLastClickTime=+new Date,this.__lastPointer={},this.on("mousedown",this.onMouseDown)},onMouseDown:function(t){if(this.canvas){this.__newClickTime=+new Date;var e=t.pointer;this.isTripleClick(e)&&(this.fire("tripleclick",t),this._stopEvent(t.e)),this.__lastLastClickTime=this.__lastClickTime,this.__lastClickTime=this.__newClickTime,this.__lastPointer=e,this.__lastIsEditing=this.isEditing,this.__lastSelected=this.selected}},isTripleClick:function(t){return this.__newClickTime-this.__lastClickTime<500&&this.__lastClickTime-this.__lastLastClickTime<500&&this.__lastPointer.x===t.x&&this.__lastPointer.y===t.y},_stopEvent:function(t){t.preventDefault&&t.preventDefault(),t.stopPropagation&&t.stopPropagation()},initCursorSelectionHandlers:function(){this.initMousedownHandler(),this.initMouseupHandler(),this.initClicks()},doubleClickHandler:function(t){this.isEditing&&this.selectWord(this.getSelectionStartFromPointer(t.e))},tripleClickHandler:function(t){this.isEditing&&this.selectLine(this.getSelectionStartFromPointer(t.e))},initClicks:function(){this.on("mousedblclick",this.doubleClickHandler),this.on("tripleclick",this.tripleClickHandler)},_mouseDownHandler:function(t){!this.canvas||!this.editable||t.e.button&&1!==t.e.button||(this.__isMousedown=!0,this.selected&&(this.inCompositionMode=!1,this.setCursorByClick(t.e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection()))},_mouseDownHandlerBefore:function(t){!this.canvas||!this.editable||t.e.button&&1!==t.e.button||(this.selected=this===this.canvas._activeObject)},initMousedownHandler:function(){this.on("mousedown",this._mouseDownHandler),this.on("mousedown:before",this._mouseDownHandlerBefore)},initMouseupHandler:function(){this.on("mouseup",this.mouseUpHandler)},mouseUpHandler:function(t){if(this.__isMousedown=!1,!(!this.editable||this.group||t.transform&&t.transform.actionPerformed||t.e.button&&1!==t.e.button)){if(this.canvas){var e=this.canvas._activeObject;if(e&&e!==this)return}this.__lastSelected&&!this.__corner?(this.selected=!1,this.__lastSelected=!1,this.enterEditing(t.e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection()):this.selected=!0}},setCursorByClick:function(t){var e=this.getSelectionStartFromPointer(t),i=this.selectionStart,r=this.selectionEnd;t.shiftKey?this.setSelectionStartEndWithShift(i,r,e):(this.selectionStart=e,this.selectionEnd=e),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())},getSelectionStartFromPointer:function(t){for(var e,i,r=this.getLocalPointer(t),n=0,s=0,o=0,a=0,c=0,l=0,h=this._textLines.length;h>l&&o<=r.y;l++)o+=this.getHeightOfLine(l)*this.scaleY,c=l,l>0&&(a+=this._textLines[l-1].length+this.missingNewlineOffset(l-1));e=this._getLineLeftOffset(c),s=e*this.scaleX,i=this._textLines[c],"rtl"===this.direction&&(r.x=this.width*this.scaleX-r.x+s);for(var u=0,f=i.length;f>u&&(n=s,s+=this.__charBounds[c][u].kernedWidth*this.scaleX,s<=r.x);u++)a++;return this._getNewSelectionStartFromOffset(r,n,s,a,f)},_getNewSelectionStartFromOffset:function(t,e,i,r,n){var s=t.x-e,o=i-t.x,a=o>s||0>o?0:1,c=r+a;return this.flipX&&(c=n-c),c>this._text.length&&(c=this._text.length),c}});fabric.util.object.extend(fabric.IText.prototype,{initHiddenTextarea:function(){this.hiddenTextarea=fabric.document.createElement("textarea"),this.hiddenTextarea.setAttribute("autocapitalize","off"),this.hiddenTextarea.setAttribute("autocorrect","off"),this.hiddenTextarea.setAttribute("autocomplete","off"),this.hiddenTextarea.setAttribute("spellcheck","false"),this.hiddenTextarea.setAttribute("data-fabric-hiddentextarea",""),this.hiddenTextarea.setAttribute("wrap","off");var t=this._calcTextareaPosition();this.hiddenTextarea.style.cssText="position: absolute; top: "+t.top+"; left: "+t.left+"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; paddingｰtop: "+t.fontSize+";",this.hiddenTextareaContainer?this.hiddenTextareaContainer.appendChild(this.hiddenTextarea):fabric.document.body.appendChild(this.hiddenTextarea),fabric.util.addListener(this.hiddenTextarea,"keydown",this.onKeyDown.bind(this)),fabric.util.addListener(this.hiddenTextarea,"keyup",this.onKeyUp.bind(this)),fabric.util.addListener(this.hiddenTextarea,"input",this.onInput.bind(this)),fabric.util.addListener(this.hiddenTextarea,"copy",this.copy.bind(this)),fabric.util.addListener(this.hiddenTextarea,"cut",this.copy.bind(this)),fabric.util.addListener(this.hiddenTextarea,"paste",this.paste.bind(this)),fabric.util.addListener(this.hiddenTextarea,"compositionstart",this.onCompositionStart.bind(this)),fabric.util.addListener(this.hiddenTextarea,"compositionupdate",this.onCompositionUpdate.bind(this)),fabric.util.addListener(this.hiddenTextarea,"compositionend",this.onCompositionEnd.bind(this)),!this._clickHandlerInitialized&&this.canvas&&(fabric.util.addListener(this.canvas.upperCanvasEl,"click",this.onClick.bind(this)),this._clickHandlerInitialized=!0)},keysMap:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorRight",36:"moveCursorLeft",37:"moveCursorLeft",38:"moveCursorUp",39:"moveCursorRight",40:"moveCursorDown"},keysMapRtl:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorLeft",36:"moveCursorRight",37:"moveCursorRight",38:"moveCursorUp",39:"moveCursorLeft",40:"moveCursorDown"},ctrlKeysMapUp:{67:"copy",88:"cut"},ctrlKeysMapDown:{65:"selectAll"},onClick:function(){this.hiddenTextarea&&this.hiddenTextarea.focus()},onKeyDown:function(t){if(this.isEditing){var e="rtl"===this.direction?this.keysMapRtl:this.keysMap;if(t.keyCode in e)this[e[t.keyCode]](t);else{if(!(t.keyCode in this.ctrlKeysMapDown&&(t.ctrlKey||t.metaKey)))return;this[this.ctrlKeysMapDown[t.keyCode]](t)}t.stopImmediatePropagation(),t.preventDefault(),t.keyCode>=33&&t.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}},onKeyUp:function(t){return!this.isEditing||this._copyDone||this.inCompositionMode?void(this._copyDone=!1):void(t.keyCode in this.ctrlKeysMapUp&&(t.ctrlKey||t.metaKey)&&(this[this.ctrlKeysMapUp[t.keyCode]](t),t.stopImmediatePropagation(),t.preventDefault(),this.canvas&&this.canvas.requestRenderAll()))},onInput:function(t){var e=this.fromPaste;if(this.fromPaste=!1,t&&t.stopPropagation(),this.isEditing){var i,r,n,s,o,a=this._splitTextIntoLines(this.hiddenTextarea.value).graphemeText,c=this._text.length,l=a.length,h=l-c,u=this.selectionStart,f=this.selectionEnd,d=u!==f;if(""===this.hiddenTextarea.value)return this.styles={},this.updateFromTextArea(),this.fire("changed"),void(this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll()));var p=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value),g=u>p.selectionStart;d?(i=this._text.slice(u,f),h+=f-u):c>l&&(i=g?this._text.slice(f+h,f):this._text.slice(u,u-h)),r=a.slice(p.selectionEnd-h,p.selectionEnd),i&&i.length&&(r.length&&(n=this.getSelectionStyles(u,u+1,!1),n=r.map(function(){return n[0]})),d?(s=u,o=f):g?(s=f-i.length,o=f):(s=f,o=f+i.length),this.removeStyleFromTo(s,o)),r.length&&(e&&r.join("")===fabric.copiedText&&!fabric.disableStyleCopyPaste&&(n=fabric.copiedTextStyle),this.insertNewStyleBlock(r,u,n)),this.updateFromTextArea(),this.fire("changed"),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())}},onCompositionStart:function(){this.inCompositionMode=!0},onCompositionEnd:function(){this.inCompositionMode=!1},onCompositionUpdate:function(t){this.compositionStart=t.target.selectionStart,this.compositionEnd=t.target.selectionEnd,this.updateTextareaPosition()},copy:function(){this.selectionStart!==this.selectionEnd&&(fabric.copiedText=this.getSelectedText(),fabric.copiedTextStyle=fabric.disableStyleCopyPaste?null:this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0)},paste:function(){this.fromPaste=!0},_getClipboardData:function(t){return t&&t.clipboardData||fabric.window.clipboardData},_getWidthBeforeCursor:function(t,e){var i,r=this._getLineLeftOffset(t);return e>0&&(i=this.__charBounds[t][e-1],r+=i.left+i.width),r},getDownCursorOffset:function(t,e){var i=this._getSelectionForOffset(t,e),r=this.get2DCursorLocation(i),n=r.lineIndex;if(n===this._textLines.length-1||t.metaKey||34===t.keyCode)return this._text.length-i;var s=r.charIndex,o=this._getWidthBeforeCursor(n,s),a=this._getIndexOnLine(n+1,o),c=this._textLines[n].slice(s);return c.length+a+1+this.missingNewlineOffset(n)},_getSelectionForOffset:function(t,e){return t.shiftKey&&this.selectionStart!==this.selectionEnd&&e?this.selectionEnd:this.selectionStart},getUpCursorOffset:function(t,e){var i=this._getSelectionForOffset(t,e),r=this.get2DCursorLocation(i),n=r.lineIndex;if(0===n||t.metaKey||33===t.keyCode)return-i;var s=r.charIndex,o=this._getWidthBeforeCursor(n,s),a=this._getIndexOnLine(n-1,o),c=this._textLines[n].slice(0,s),l=this.missingNewlineOffset(n-1);return-this._textLines[n-1].length+a-c.length+(1-l)},_getIndexOnLine:function(t,e){for(var i,r,n=this._textLines[t],s=this._getLineLeftOffset(t),o=s,a=0,c=0,l=n.length;l>c;c++)if(i=this.__charBounds[t][c].width,o+=i,o>e){r=!0;var h=o-i,u=o,f=Math.abs(h-e),d=Math.abs(u-e);a=f>d?c:c-1;break}return r||(a=n.length-1),a},moveCursorDown:function(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",t)},moveCursorUp:function(t){(0!==this.selectionStart||0!==this.selectionEnd)&&this._moveCursorUpOrDown("Up",t)},_moveCursorUpOrDown:function(t,e){var i="get"+t+"CursorOffset",r=this[i](e,"right"===this._selectionDirection);e.shiftKey?this.moveCursorWithShift(r):this.moveCursorWithoutShift(r),0!==r&&(this.setSelectionInBoundaries(),this.abortCursorAnimation(),this._currentCursorOpacity=1,this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorWithShift:function(t){var e="left"===this._selectionDirection?this.selectionStart+t:this.selectionEnd+t;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,e),0!==t},moveCursorWithoutShift:function(t){return 0>t?(this.selectionStart+=t,this.selectionEnd=this.selectionStart):(this.selectionEnd+=t,this.selectionStart=this.selectionEnd),0!==t},moveCursorLeft:function(t){(0!==this.selectionStart||0!==this.selectionEnd)&&this._moveCursorLeftOrRight("Left",t)},_move:function(t,e,i){var r;if(t.altKey)r=this["findWordBoundary"+i](this[e]);else{if(!t.metaKey&&35!==t.keyCode&&36!==t.keyCode)return this[e]+="Left"===i?-1:1,!0;r=this["findLineBoundary"+i](this[e])}return void 0!==typeof r&&this[e]!==r?(this[e]=r,!0):void 0},_moveLeft:function(t,e){return this._move(t,e,"Left")},_moveRight:function(t,e){return this._move(t,e,"Right")},moveCursorLeftWithoutShift:function(t){var e=!0;return this._selectionDirection="left",this.selectionEnd===this.selectionStart&&0!==this.selectionStart&&(e=this._moveLeft(t,"selectionStart")),this.selectionEnd=this.selectionStart,e},moveCursorLeftWithShift:function(t){return"right"===this._selectionDirection&&this.selectionStart!==this.selectionEnd?this._moveLeft(t,"selectionEnd"):0!==this.selectionStart?(this._selectionDirection="left",this._moveLeft(t,"selectionStart")):void 0},moveCursorRight:function(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",t)},_moveCursorLeftOrRight:function(t,e){var i="moveCursor"+t+"With";this._currentCursorOpacity=1,i+=e.shiftKey?"Shift":"outShift",this[i](e)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorRightWithShift:function(t){return"left"===this._selectionDirection&&this.selectionStart!==this.selectionEnd?this._moveRight(t,"selectionStart"):this.selectionEnd!==this._text.length?(this._selectionDirection="right",this._moveRight(t,"selectionEnd")):void 0},moveCursorRightWithoutShift:function(t){var e=!0;return this._selectionDirection="right",this.selectionStart===this.selectionEnd?(e=this._moveRight(t,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,e},removeChars:function(t,e){"undefined"==typeof e&&(e=t+1),this.removeStyleFromTo(t,e),this._text.splice(t,e-t),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()},insertChars:function(t,e,i,r){"undefined"==typeof r&&(r=i),r>i&&this.removeStyleFromTo(i,r);var n=fabric.util.string.graphemeSplit(t);this.insertNewStyleBlock(n,i,e),this._text=[].concat(this._text.slice(0,i),n,this._text.slice(r)),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()}});!function(t){"use strict";var e=t.fabric||(t.fabric={});e.Textbox=e.util.createClass(e.IText,e.Observable,{type:"textbox",minWidth:20,dynamicMinWidth:2,__cachedLines:null,lockScalingFlip:!0,noScaleCache:!1,_dimensionAffectingProps:e.Text.prototype._dimensionAffectingProps.concat("width"),_wordJoiners:/[ \t\r]/,splitByGrapheme:!1,initDimensions:function(){this.__skipDimension||(this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),-1!==this.textAlign.indexOf("justify")&&this.enlargeSpaces(),this.height=this.calcTextHeight(),this.saveState({propertySet:"_dimensionAffectingProps"}))},_generateStyleMap:function(t){for(var e=0,r=0,i=0,n={},o=0;o<t.graphemeLines.length;o++)"\n"===t.graphemeText[i]&&o>0?(r=0,i++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[i])&&o>0&&(r++,i++),n[o]={line:e,offset:r},i+=t.graphemeLines[o].length,r+=t.graphemeLines[o].length;return n},styleHas:function(t,r){if(this._styleMap&&!this.isWrapping){var i=this._styleMap[r];i&&(r=i.line)}return e.Text.prototype.styleHas.call(this,t,r)},isEmptyStyles:function(t){if(!this.styles)return!0;var e,r,i=0,n=t+1,o=!1,s=this._styleMap[t],a=this._styleMap[t+1];s&&(t=s.line,i=s.offset),a&&(n=a.line,o=n===t,e=a.offset),r="undefined"==typeof t?this.styles:{line:this.styles[t]};for(var c in r)for(var l in r[c])if(l>=i&&(!o||e>l))for(var u in r[c][l])return!1;return!0},_getStyleDeclaration:function(t,e){if(this._styleMap&&!this.isWrapping){var r=this._styleMap[t];if(!r)return null;t=r.line,e=r.offset+e}return this.callSuper("_getStyleDeclaration",t,e)},_setStyleDeclaration:function(t,e,r){var i=this._styleMap[t];t=i.line,e=i.offset+e,this.styles[t][e]=r},_deleteStyleDeclaration:function(t,e){var r=this._styleMap[t];t=r.line,e=r.offset+e,delete this.styles[t][e]},_getLineStyle:function(t){var e=this._styleMap[t];return!!this.styles[e.line]},_setLineStyle:function(t){var e=this._styleMap[t];this.styles[e.line]={}},_wrapText:function(t,e){var r,i=[];for(this.isWrapping=!0,r=0;r<t.length;r++)i=i.concat(this._wrapLine(t[r],r,e));return this.isWrapping=!1,i},_measureWord:function(t,e,r){var i,n=0,o=!0;r=r||0;for(var s=0,a=t.length;a>s;s++){var c=this._getGraphemeBox(t[s],e,s+r,i,o);n+=c.kernedWidth,i=t[s]}return n},_wrapLine:function(t,r,i,n){var o=0,s=this.splitByGrapheme,a=[],c=[],l=s?e.util.string.graphemeSplit(t):t.split(this._wordJoiners),u="",h=0,f=s?"":" ",d=0,g=0,p=0,v=!0,m=this._getWidthOfCharSpacing(),n=n||0;0===l.length&&l.push([]),i-=n;for(var b=0;b<l.length;b++)u=s?l[b]:e.util.string.graphemeSplit(l[b]),d=this._measureWord(u,r,h),h+=u.length,o+=g+d-m,o>i&&!v?(a.push(c),c=[],o=d,v=!0):o+=m,v||s||c.push(f),c=c.concat(u),g=s?0:this._measureWord([f],r,h),h++,v=!1,d>p&&(p=d);return b&&a.push(c),p+n>this.dynamicMinWidth&&(this.dynamicMinWidth=p-m+n),a},isEndOfWrapping:function(t){return this._styleMap[t+1]?this._styleMap[t+1].line!==this._styleMap[t].line?!0:!1:!0},missingNewlineOffset:function(t){return this.splitByGrapheme?this.isEndOfWrapping(t)?1:0:1},_splitTextIntoLines:function(t){for(var r=e.Text.prototype._splitTextIntoLines.call(this,t),i=this._wrapText(r.lines,this.width),n=new Array(i.length),o=0;o<i.length;o++)n[o]=i[o].join("");return r.lines=n,r.graphemeLines=i,r},getMinWidth:function(){return Math.max(this.minWidth,this.dynamicMinWidth)},_removeExtraneousStyles:function(){var t={};for(var e in this._styleMap)this._textLines[e]&&(t[this._styleMap[e].line]=1);for(var e in this.styles)t[e]||delete this.styles[e]},toObject:function(t){return this.callSuper("toObject",["minWidth","splitByGrapheme"].concat(t))}}),e.Textbox.fromObject=function(t,r){return t.styles=e.util.stylesFromArray(t.styles,t.text),e.Object._fromObject("Textbox",t,r,"text")}}("undefined"!=typeof exports?exports:this);!function(){var t=fabric.controlsUtils,e=t.scaleSkewCursorStyleHandler,i=t.scaleCursorStyleHandler,r=t.scalingEqually,n=t.scalingYOrSkewingX,s=t.scalingXOrSkewingY,o=t.scaleOrSkewActionName,a=fabric.Object.prototype.controls;if(a.ml=new fabric.Control({x:-.5,y:0,cursorStyleHandler:e,actionHandler:s,getActionName:o}),a.mr=new fabric.Control({x:.5,y:0,cursorStyleHandler:e,actionHandler:s,getActionName:o}),a.mb=new fabric.Control({x:0,y:.5,cursorStyleHandler:e,actionHandler:n,getActionName:o}),a.mt=new fabric.Control({x:0,y:-.5,cursorStyleHandler:e,actionHandler:n,getActionName:o}),a.tl=new fabric.Control({x:-.5,y:-.5,cursorStyleHandler:i,actionHandler:r}),a.tr=new fabric.Control({x:.5,y:-.5,cursorStyleHandler:i,actionHandler:r}),a.bl=new fabric.Control({x:-.5,y:.5,cursorStyleHandler:i,actionHandler:r}),a.br=new fabric.Control({x:.5,y:.5,cursorStyleHandler:i,actionHandler:r}),a.mtr=new fabric.Control({x:0,y:-.5,actionHandler:t.rotationWithSnapping,cursorStyleHandler:t.rotationStyleHandler,offsetY:-40,withConnection:!0,actionName:"rotate"}),fabric.Textbox){var c=fabric.Textbox.prototype.controls={};c.mtr=a.mtr,c.tr=a.tr,c.br=a.br,c.tl=a.tl,c.bl=a.bl,c.mt=a.mt,c.mb=a.mb,c.mr=new fabric.Control({x:.5,y:0,actionHandler:t.changeWidth,cursorStyleHandler:e,actionName:"resizing"}),c.ml=new fabric.Control({x:-.5,y:0,actionHandler:t.changeWidth,cursorStyleHandler:e,actionName:"resizing"})}}();!function(){fabric.Object.ENLIVEN_PROPS.push("eraser");{var t=fabric.Object.prototype._drawClipPath,e=fabric.Object.prototype.needsItsOwnCache,i=fabric.Object.prototype.toObject;fabric.Object.prototype.getSvgCommons,fabric.Object.prototype._createBaseClipPathSVGMarkup,fabric.Object.prototype._createBaseSVGMarkup}fabric.Object.prototype.cacheProperties.push("eraser"),fabric.Object.prototype.stateProperties.push("eraser"),fabric.util.object.extend(fabric.Object.prototype,{erasable:!0,eraser:void 0,needsItsOwnCache:function(){return e.call(this)||!!this.eraser},_drawClipPath:function(e,i){if(t.call(this,e,i),this.eraser){var r=this._getNonTransformedDimensions();this.eraser.isType("eraser")&&this.eraser.set({width:r.x,height:r.y}),t.call(this,e,this.eraser)}},toObject:function(t){var e=i.call(this,["erasable"].concat(t));return this.eraser&&!this.eraser.excludeFromExport&&(e.eraser=this.eraser.toObject(t)),e}});var r=fabric.Group.prototype._restoreObjectsState;fabric.util.object.extend(fabric.Group.prototype,{_addEraserPathToObjects:function(t){this._objects.forEach(function(e){fabric.EraserBrush.prototype._addPathToObjectEraser.call(fabric.EraserBrush.prototype,e,t)})},applyEraserToObjects:function(){var t=this,e=this.eraser;if(e){delete this.eraser;var i=t.calcTransformMatrix();e.clone(function(e){var r=t.clipPath;e.getObjects("path").forEach(function(e){var n=fabric.util.multiplyTransformMatrices(i,e.calcTransformMatrix());fabric.util.applyTransformToObject(e,n),r?r.clone(function(r){var n=fabric.EraserBrush.prototype.applyClipPathToPath.call(fabric.EraserBrush.prototype,e,r,i);t._addEraserPathToObjects(n)},["absolutePositioned","inverted"]):t._addEraserPathToObjects(e)})})}},_restoreObjectsState:function(){return this.erasable===!0&&this.applyEraserToObjects(),r.call(this)}}),fabric.Eraser=fabric.util.createClass(fabric.Group,{type:"eraser",originX:"center",originY:"center",drawObject:function(t){t.save(),t.fillStyle="black",t.fillRect(-this.width/2,-this.height/2,this.width,this.height),t.restore(),this.callSuper("drawObject",t)},_getBounds:function(){}}),fabric.Eraser.fromObject=function(t,e){var i=t.objects;fabric.util.enlivenObjects(i,function(i){var r=fabric.util.object.clone(t,!0);delete r.objects,fabric.util.enlivenObjectEnlivables(t,r,function(){e&&e(new fabric.Eraser(i,r,!0))})})};var n=fabric.Canvas.prototype._renderOverlay;fabric.util.object.extend(fabric.Canvas.prototype,{isErasing:function(){return this.isDrawingMode&&this.freeDrawingBrush&&"eraser"===this.freeDrawingBrush.type&&this.freeDrawingBrush._isErasing},_renderOverlay:function(t){n.call(this,t),this.isErasing()&&!this.freeDrawingBrush.inverted&&this.freeDrawingBrush._render()}}),fabric.EraserBrush=fabric.util.createClass(fabric.PencilBrush,{type:"eraser",inverted:!1,_isErasing:!1,_isErasable:function(t){return t.erasable!==!1},_prepareCollectionTraversal:function(t,e,i){t.forEachObject(function(r){r.forEachObject&&"deep"===r.erasable?this._prepareCollectionTraversal(r,e,i):!this.inverted&&r.erasable&&r.visible?(r.visible=!1,t.dirty=!0,i.visibility.push(r),i.collection.push(t)):this.inverted&&r.visible&&(r.erasable&&r.eraser?(r.eraser.inverted=!0,r.dirty=!0,t.dirty=!0,i.eraser.push(r),i.collection.push(t)):(r.visible=!1,t.dirty=!0,i.visibility.push(r),i.collection.push(t)))},this)},preparePattern:function(){this._patternCanvas||(this._patternCanvas=fabric.util.createCanvasElement());var t=this._patternCanvas;t.width=this.canvas.width,t.height=this.canvas.height;var e=t.getContext("2d");if(this.canvas._isRetinaScaling()){var i=this.canvas.getRetinaScaling();this.canvas.__initRetinaScaling(i,t,e)}var r=this.canvas.backgroundImage,s=r&&this._isErasable(r),o=this.canvas.overlayImage,a=o&&this._isErasable(o);if(!this.inverted&&(r&&!s||this.canvas.backgroundColor))s&&(this.canvas.backgroundImage=void 0),this.canvas._renderBackground(e),s&&(this.canvas.backgroundImage=r);else if(this.inverted&&r&&s){var c=this.canvas.backgroundColor;this.canvas.backgroundColor=void 0,this.canvas._renderBackground(e),this.canvas.backgroundColor=c}e.save(),e.transform.apply(e,this.canvas.viewportTransform);var l={visibility:[],eraser:[],collection:[]};if(this._prepareCollectionTraversal(this.canvas,e,l),this.canvas._renderObjects(e,this.canvas._objects),l.visibility.forEach(function(t){t.visible=!0}),l.eraser.forEach(function(t){t.eraser.inverted=!1,t.dirty=!0}),l.collection.forEach(function(t){t.dirty=!0}),e.restore(),!this.inverted&&(o&&!a||this.canvas.overlayColor))a&&(this.canvas.overlayImage=void 0),n.call(this.canvas,e),a&&(this.canvas.overlayImage=o);else if(this.inverted&&o&&a){var c=this.canvas.overlayColor;this.canvas.overlayColor=void 0,n.call(this.canvas,e),this.canvas.overlayColor=c}},_setBrushStyles:function(t){this.callSuper("_setBrushStyles",t),t.strokeStyle="black"},_saveAndTransform:function(t){this.callSuper("_saveAndTransform",t),this._setBrushStyles(t),t.globalCompositeOperation=t===this.canvas.getContext()?"destination-out":"source-over"},needsFullRender:function(){return!0},onMouseDown:function(t,e){this.canvas._isMainEvent(e.e)&&(this._prepareForDrawing(t),this._captureDrawingPath(t),this.preparePattern(),this._isErasing=!0,this.canvas.fire("erasing:start"),this._render())},_render:function(){var t;this.inverted||(t=this.canvas.getContext(),this.callSuper("_render",t)),t=this.canvas.contextTop,this.canvas.clearContext(t),this.callSuper("_render",t),t.save();var e=this.canvas.getRetinaScaling(),i=1/e;t.scale(i,i),t.globalCompositeOperation="source-in",t.drawImage(this._patternCanvas,0,0),t.restore()},createPath:function(t){var e=this.callSuper("createPath",t);return e.globalCompositeOperation=this.inverted?"source-over":"destination-out",e.stroke=this.inverted?"white":"black",e},applyClipPathToPath:function(t,e,i){var r=fabric.util.invertTransform(t.calcTransformMatrix()),n=e.calcTransformMatrix(),s=e.absolutePositioned?r:fabric.util.multiplyTransformMatrices(r,i);return e.absolutePositioned=!1,fabric.util.applyTransformToObject(e,fabric.util.multiplyTransformMatrices(s,n)),t.clipPath=t.clipPath?fabric.util.mergeClipPaths(e,t.clipPath):e,t},clonePathWithClipPath:function(t,e,i){var r=e.calcTransformMatrix(),n=e.clipPath,s=this;t.clone(function(t){n.clone(function(e){i(s.applyClipPathToPath(t,e,r))},["absolutePositioned","inverted"])})},_addPathToObjectEraser:function(t,e){var i=this;if(t.forEachObject&&"deep"===t.erasable){var r=t._objects.filter(function(t){return t.erasable});return void(r.length>0&&t.clipPath?this.clonePathWithClipPath(e,t,function(t){r.forEach(function(e){i._addPathToObjectEraser(e,t)})}):r.length>0&&r.forEach(function(t){i._addPathToObjectEraser(t,e)}))}var n=t.eraser;n||(n=new fabric.Eraser,t.eraser=n),e.clone(function(e){var r=fabric.util.multiplyTransformMatrices(fabric.util.invertTransform(t.calcTransformMatrix()),e.calcTransformMatrix());fabric.util.applyTransformToObject(e,r),n.addWithUpdate(e),t.set("dirty",!0),t.fire("erasing:end",{path:e}),t.group&&Array.isArray(i.__subTargets)&&i.__subTargets.push(t)})},applyEraserToCanvas:function(t){var e=this.canvas,i={};return["backgroundImage","overlayImage"].forEach(function(r){var n=e[r];n&&n.erasable&&(this._addPathToObjectEraser(n,t),i[r]=n)},this),i},_finalizeAndAddPath:function(){var t=this.canvas.contextTop,e=this.canvas;t.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate)),e.clearContext(e.contextTop),this._isErasing=!1;var i=this._points&&this._points.length>1?this.convertPointsToSVGPath(this._points):null;if(!i||this._isEmptySVGPath(i))return e.fire("erasing:end"),void e.requestRenderAll();var r=this.createPath(i);r.setCoords(),e.fire("before:path:created",{path:r});var n=this.applyEraserToCanvas(r),s=this;this.__subTargets=[];var o=[];e.forEachObject(function(t){t.erasable&&t.intersectsWithObject(r,!0,!0)&&(s._addPathToObjectEraser(t,r),o.push(t))}),e.fire("erasing:end",{path:r,targets:o,subTargets:this.__subTargets,drawables:n}),delete this.__subTargets,e.requestRenderAll(),this._resetShadow(),e.fire("path:created",{path:r})}})}();