/*! jQuery UI - v1.13.2 - 2023-05-18
* http://jqueryui.com
* Includes: effect.js, effects/effect-blind.js, effects/effect-bounce.js, effects/effect-clip.js, effects/effect-drop.js, effects/effect-explode.js, effects/effect-fade.js, effects/effect-fold.js, effects/effect-highlight.js, effects/effect-puff.js, effects/effect-pulsate.js, effects/effect-scale.js, effects/effect-shake.js, effects/effect-size.js, effects/effect-slide.js, effects/effect-transfer.js
* Copyright jQuery Foundation and other contributors; Licensed MIT */

!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(function(b){"use strict";b.ui=b.ui||{};b.ui.version="1.13.2";var a=b,i={},e=i.toString,c=/^([\-+])=\s*(\d+\.?\d*)/,t=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[t[1],t[2],t[3],t[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[2.55*t[1],2.55*t[2],2.55*t[3],t[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?/,parse:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16),t[4]?(parseInt(t[4],16)/255).toFixed(2):1]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?/,parse:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16),t[4]?(parseInt(t[4]+t[4],16)/255).toFixed(2):1]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(t){return[t[1],t[2]/100,t[3]/100,t[4]]}}],l=a.Color=function(t,e,i,o){return new a.Color.fn.parse(t,e,i,o)},u={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},d={byte:{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},s=l.support={},o=a("<p>")[0],h=a.each;function p(t){return null==t?t+"":"object"==typeof t?i[e.call(t)]||"object":typeof t}function g(t,e,i){var o=d[e.type]||{};return null==t?i||!e.def?null:e.def:(t=o.floor?~~t:parseFloat(t),isNaN(t)?e.def:o.mod?(t+o.mod)%o.mod:Math.min(o.max,Math.max(0,t)))}function f(o){var n=l(),r=n._rgba=[];return o=o.toLowerCase(),h(t,function(t,e){var i=e.re.exec(o),i=i&&e.parse(i),e=e.space||"rgba";if(i)return i=n[e](i),n[u[e].cache]=i[u[e].cache],r=n._rgba=i._rgba,!1}),r.length?("0,0,0,0"===r.join()&&a.extend(r,q.transparent),n):q[o]}function n(t,e,i){return 6*(i=(i+1)%1)<1?t+(e-t)*i*6:2*i<1?e:3*i<2?t+(e-t)*(2/3-i)*6:t}o.style.cssText="background-color:rgba(1,1,1,.5)",s.rgba=-1<o.style.backgroundColor.indexOf("rgba"),h(u,function(t,e){e.cache="_"+t,e.props.alpha={idx:3,type:"percent",def:1}}),a.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){i["[object "+e+"]"]=e.toLowerCase()}),(l.fn=a.extend(l.prototype,{parse:function(n,t,e,i){if(void 0===n)return this._rgba=[null,null,null,null],this;(n.jquery||n.nodeType)&&(n=a(n).css(t),t=void 0);var r=this,o=p(n),s=this._rgba=[];return void 0!==t&&(n=[n,t,e,i],o="array"),"string"===o?this.parse(f(n)||q._default):"array"===o?(h(u.rgba.props,function(t,e){s[e.idx]=g(n[e.idx],e)}),this):"object"===o?(h(u,n instanceof l?function(t,e){n[e.cache]&&(r[e.cache]=n[e.cache].slice())}:function(t,i){var o=i.cache;h(i.props,function(t,e){if(!r[o]&&i.to){if("alpha"===t||null==n[t])return;r[o]=i.to(r._rgba)}r[o][e.idx]=g(n[t],e,!0)}),r[o]&&a.inArray(null,r[o].slice(0,3))<0&&(null==r[o][3]&&(r[o][3]=1),i.from&&(r._rgba=i.from(r[o])))}),this):void 0},is:function(t){var n=l(t),r=!0,s=this;return h(u,function(t,e){var i,o=n[e.cache];return o&&(i=s[e.cache]||e.to&&e.to(s._rgba)||[],h(e.props,function(t,e){if(null!=o[e.idx])return r=o[e.idx]===i[e.idx]})),r}),r},_space:function(){var i=[],o=this;return h(u,function(t,e){o[e.cache]&&i.push(t)}),i.pop()},transition:function(t,s){var e=(c=l(t))._space(),i=u[e],t=0===this.alpha()?l("transparent"):this,a=t[i.cache]||i.to(t._rgba),f=a.slice(),c=c[i.cache];return h(i.props,function(t,e){var i=e.idx,o=a[i],n=c[i],r=d[e.type]||{};null!==n&&(null===o?f[i]=n:(r.mod&&(n-o>r.mod/2?o+=r.mod:o-n>r.mod/2&&(o-=r.mod)),f[i]=g((n-o)*s+o,e)))}),this[e](f)},blend:function(t){if(1===this._rgba[3])return this;var e=this._rgba.slice(),i=e.pop(),o=l(t)._rgba;return l(a.map(e,function(t,e){return(1-i)*o[e]+i*t}))},toRgbaString:function(){var t="rgba(",e=a.map(this._rgba,function(t,e){return null!=t?t:2<e?1:0});return 1===e[3]&&(e.pop(),t="rgb("),t+e.join()+")"},toHslaString:function(){var t="hsla(",e=a.map(this.hsla(),function(t,e){return null==t&&(t=2<e?1:0),t=e&&e<3?Math.round(100*t)+"%":t});return 1===e[3]&&(e.pop(),t="hsl("),t+e.join()+")"},toHexString:function(t){var e=this._rgba.slice(),i=e.pop();return t&&e.push(~~(255*i)),"#"+a.map(e,function(t){return 1===(t=(t||0).toString(16)).length?"0"+t:t}).join("")},toString:function(){return 0===this._rgba[3]?"transparent":this.toRgbaString()}})).parse.prototype=l.fn,u.hsla.to=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e=t[0]/255,i=t[1]/255,o=t[2]/255,n=t[3],r=Math.max(e,i,o),s=Math.min(e,i,o),a=r-s,f=r+s,t=.5*f,i=s===r?0:e===r?60*(i-o)/a+360:i===r?60*(o-e)/a+120:60*(e-i)/a+240,f=0==a?0:t<=.5?a/f:a/(2-f);return[Math.round(i)%360,f,t,null==n?1:n]},u.hsla.from=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e=t[0]/360,i=t[1],o=t[2],t=t[3],i=o<=.5?o*(1+i):o+i-o*i,o=2*o-i;return[Math.round(255*n(o,i,e+1/3)),Math.round(255*n(o,i,e)),Math.round(255*n(o,i,e-1/3)),t]},h(u,function(f,t){var e=t.props,r=t.cache,s=t.to,a=t.from;l.fn[f]=function(t){if(s&&!this[r]&&(this[r]=s(this._rgba)),void 0===t)return this[r].slice();var i=p(t),o="array"===i||"object"===i?t:arguments,n=this[r].slice();return h(e,function(t,e){t=o["object"===i?t:e.idx];null==t&&(t=n[e.idx]),n[e.idx]=g(t,e)}),a?((t=l(a(n)))[r]=n,t):l(n)},h(e,function(s,a){l.fn[s]||(l.fn[s]=function(t){var e,i=p(t),o="alpha"===s?this._hsla?"hsla":"rgba":f,n=this[o](),r=n[a.idx];return"undefined"===i?r:("function"===i&&(i=p(t=t.call(this,r))),null==t&&a.empty?this:("string"===i&&(e=c.exec(t))&&(t=r+parseFloat(e[2])*("+"===e[1]?1:-1)),n[a.idx]=t,this[o](n)))})})}),(l.hook=function(t){t=t.split(" ");h(t,function(t,r){a.cssHooks[r]={set:function(t,e){var i,o,n="";if("transparent"!==e&&("string"!==p(e)||(i=f(e)))){if(e=l(i||e),!s.rgba&&1!==e._rgba[3]){for(o="backgroundColor"===r?t.parentNode:t;(""===n||"transparent"===n)&&o&&o.style;)try{n=a.css(o,"backgroundColor"),o=o.parentNode}catch(t){}e=e.blend(n&&"transparent"!==n?n:"_default")}e=e.toRgbaString()}try{t.style[r]=e}catch(t){}}},a.fx.step[r]=function(t){t.colorInit||(t.start=l(t.elem,r),t.end=l(t.end),t.colorInit=!0),a.cssHooks[r].set(t.elem,t.start.transition(t.end,t.pos))}})})("backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor"),a.cssHooks.borderColor={expand:function(i){var o={};return h(["Top","Right","Bottom","Left"],function(t,e){o["border"+e+"Color"]=i}),o}};var r,m,y,v,x,w,C,T,k,W,q=a.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"},B="ui-effects-",H="ui-effects-style",S="ui-effects-animated";function M(t){var e,i,o=t.ownerDocument.defaultView?t.ownerDocument.defaultView.getComputedStyle(t,null):t.currentStyle,n={};if(o&&o.length&&o[0]&&o[o[0]])for(i=o.length;i--;)"string"==typeof o[e=o[i]]&&(n[e.replace(/-([\da-z])/gi,function(t,e){return e.toUpperCase()})]=o[e]);else for(e in o)"string"==typeof o[e]&&(n[e]=o[e]);return n}function _(t,e,i,o){return t={effect:t=b.isPlainObject(t)?(e=t).effect:t},"function"==typeof(e=null==e?{}:e)&&(o=e,i=null,e={}),"number"!=typeof e&&!b.fx.speeds[e]||(o=i,i=e,e={}),"function"==typeof i&&(o=i,i=null),e&&b.extend(t,e),i=i||e.duration,t.duration=b.fx.off?0:"number"==typeof i?i:i in b.fx.speeds?b.fx.speeds[i]:b.fx.speeds._default,t.complete=o||e.complete,t}function I(t){return!t||"number"==typeof t||b.fx.speeds[t]||("string"==typeof t&&!b.effects.effect[t]||("function"==typeof t||"object"==typeof t&&!t.effect))}function j(t,e){var i=e.outerWidth(),e=e.outerHeight(),t=/^rect\((-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto)\)$/.exec(t)||["",0,i,e,0];return{top:parseFloat(t[1])||0,right:"auto"===t[2]?i:parseFloat(t[2]),bottom:"auto"===t[3]?e:parseFloat(t[3]),left:parseFloat(t[4])||0}}b.effects={effect:{}},v=["add","remove","toggle"],x={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1},b.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],function(t,e){b.fx.step[e]=function(t){("none"!==t.end&&!t.setAttr||1===t.pos&&!t.setAttr)&&(a.style(t.elem,e,t.end),t.setAttr=!0)}}),b.fn.addBack||(b.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),b.effects.animateClass=function(n,t,e,i){var r=b.speed(t,e,i);return this.queue(function(){var i=b(this),t=i.attr("class")||"",e=(e=r.children?i.find("*").addBack():i).map(function(){return{el:b(this),start:M(this)}}),o=function(){b.each(v,function(t,e){n[e]&&i[e+"Class"](n[e])})};o(),e=e.map(function(){return this.end=M(this.el[0]),this.diff=function(t,e){var i,o,n={};for(i in e)o=e[i],t[i]!==o&&(x[i]||!b.fx.step[i]&&isNaN(parseFloat(o))||(n[i]=o));return n}(this.start,this.end),this}),i.attr("class",t),e=e.map(function(){var t=this,e=b.Deferred(),i=b.extend({},r,{queue:!1,complete:function(){e.resolve(t)}});return this.el.animate(this.diff,i),e.promise()}),b.when.apply(b,e.get()).done(function(){o(),b.each(arguments,function(){var e=this.el;b.each(this.diff,function(t){e.css(t,"")})}),r.complete.call(i[0])})})},b.fn.extend({addClass:(y=b.fn.addClass,function(t,e,i,o){return e?b.effects.animateClass.call(this,{add:t},e,i,o):y.apply(this,arguments)}),removeClass:(m=b.fn.removeClass,function(t,e,i,o){return 1<arguments.length?b.effects.animateClass.call(this,{remove:t},e,i,o):m.apply(this,arguments)}),toggleClass:(r=b.fn.toggleClass,function(t,e,i,o,n){return"boolean"==typeof e||void 0===e?i?b.effects.animateClass.call(this,e?{add:t}:{remove:t},i,o,n):r.apply(this,arguments):b.effects.animateClass.call(this,{toggle:t},e,i,o)}),switchClass:function(t,e,i,o,n){return b.effects.animateClass.call(this,{add:e,remove:t},i,o,n)}}),b.expr&&b.expr.pseudos&&b.expr.pseudos.animated&&(b.expr.pseudos.animated=(w=b.expr.pseudos.animated,function(t){return!!b(t).data(S)||w(t)})),!1!==b.uiBackCompat&&b.extend(b.effects,{save:function(t,e){for(var i=0,o=e.length;i<o;i++)null!==e[i]&&t.data(B+e[i],t[0].style[e[i]])},restore:function(t,e){for(var i,o=0,n=e.length;o<n;o++)null!==e[o]&&(i=t.data(B+e[o]),t.css(e[o],i))},setMode:function(t,e){return e="toggle"===e?t.is(":hidden")?"show":"hide":e},createWrapper:function(i){if(i.parent().is(".ui-effects-wrapper"))return i.parent();var o={width:i.outerWidth(!0),height:i.outerHeight(!0),float:i.css("float")},t=b("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),e={width:i.width(),height:i.height()},n=document.activeElement;try{n.id}catch(t){n=document.body}return i.wrap(t),i[0]!==n&&!b.contains(i[0],n)||b(n).trigger("focus"),t=i.parent(),"static"===i.css("position")?(t.css({position:"relative"}),i.css({position:"relative"})):(b.extend(o,{position:i.css("position"),zIndex:i.css("z-index")}),b.each(["top","left","bottom","right"],function(t,e){o[e]=i.css(e),isNaN(parseInt(o[e],10))&&(o[e]="auto")}),i.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),i.css(e),t.css(o).show()},removeWrapper:function(t){var e=document.activeElement;return t.parent().is(".ui-effects-wrapper")&&(t.parent().replaceWith(t),t[0]!==e&&!b.contains(t[0],e)||b(e).trigger("focus")),t}}),b.extend(b.effects,{version:"1.13.2",define:function(t,e,i){return i||(i=e,e="effect"),b.effects.effect[t]=i,b.effects.effect[t].mode=e,i},scaledDimensions:function(t,e,i){if(0===e)return{height:0,width:0,outerHeight:0,outerWidth:0};var o="horizontal"!==i?(e||100)/100:1,e="vertical"!==i?(e||100)/100:1;return{height:t.height()*e,width:t.width()*o,outerHeight:t.outerHeight()*e,outerWidth:t.outerWidth()*o}},clipToBox:function(t){return{width:t.clip.right-t.clip.left,height:t.clip.bottom-t.clip.top,left:t.clip.left,top:t.clip.top}},unshift:function(t,e,i){var o=t.queue();1<e&&o.splice.apply(o,[1,0].concat(o.splice(e,i))),t.dequeue()},saveStyle:function(t){t.data(H,t[0].style.cssText)},restoreStyle:function(t){t[0].style.cssText=t.data(H)||"",t.removeData(H)},mode:function(t,e){t=t.is(":hidden");return"toggle"===e&&(e=t?"show":"hide"),e=(t?"hide"===e:"show"===e)?"none":e},getBaseline:function(t,e){var i,o;switch(t[0]){case"top":i=0;break;case"middle":i=.5;break;case"bottom":i=1;break;default:i=t[0]/e.height}switch(t[1]){case"left":o=0;break;case"center":o=.5;break;case"right":o=1;break;default:o=t[1]/e.width}return{x:o,y:i}},createPlaceholder:function(t){var e,i=t.css("position"),o=t.position();return t.css({marginTop:t.css("marginTop"),marginBottom:t.css("marginBottom"),marginLeft:t.css("marginLeft"),marginRight:t.css("marginRight")}).outerWidth(t.outerWidth()).outerHeight(t.outerHeight()),/^(static|relative)/.test(i)&&(i="absolute",e=b("<"+t[0].nodeName+">").insertAfter(t).css({display:/^(inline|ruby)/.test(t.css("display"))?"inline-block":"block",visibility:"hidden",marginTop:t.css("marginTop"),marginBottom:t.css("marginBottom"),marginLeft:t.css("marginLeft"),marginRight:t.css("marginRight"),float:t.css("float")}).outerWidth(t.outerWidth()).outerHeight(t.outerHeight()).addClass("ui-effects-placeholder"),t.data(B+"placeholder",e)),t.css({position:i,left:o.left,top:o.top}),e},removePlaceholder:function(t){var e=B+"placeholder",i=t.data(e);i&&(i.remove(),t.removeData(e))},cleanUp:function(t){b.effects.restoreStyle(t),b.effects.removePlaceholder(t)},setTransition:function(o,t,n,r){return r=r||{},b.each(t,function(t,e){var i=o.cssUnit(e);0<i[0]&&(r[e]=i[0]*n+i[1])}),r}}),b.fn.extend({effect:function(){function t(t){var e=b(this),i=b.effects.mode(e,a)||r;e.data(S,!0),f.push(i),r&&("show"===i||i===r&&"hide"===i)&&e.show(),r&&"none"===i||b.effects.saveStyle(e),"function"==typeof t&&t()}var o=_.apply(this,arguments),n=b.effects.effect[o.effect],r=n.mode,e=o.queue,i=e||"fx",s=o.complete,a=o.mode,f=[];return b.fx.off||!n?a?this[a](o.duration,s):this.each(function(){s&&s.call(this)}):!1===e?this.each(t).each(c):this.queue(i,t).queue(i,c);function c(t){var e=b(this);function i(){"function"==typeof s&&s.call(e[0]),"function"==typeof t&&t()}o.mode=f.shift(),!1===b.uiBackCompat||r?"none"===o.mode?(e[a](),i()):n.call(e[0],o,function(){e.removeData(S),b.effects.cleanUp(e),"hide"===o.mode&&e.hide(),i()}):(e.is(":hidden")?"hide"===a:"show"===a)?(e[a](),i()):n.call(e[0],o,i)}},show:(k=b.fn.show,function(t){if(I(t))return k.apply(this,arguments);t=_.apply(this,arguments);return t.mode="show",this.effect.call(this,t)}),hide:(T=b.fn.hide,function(t){if(I(t))return T.apply(this,arguments);t=_.apply(this,arguments);return t.mode="hide",this.effect.call(this,t)}),toggle:(C=b.fn.toggle,function(t){if(I(t)||"boolean"==typeof t)return C.apply(this,arguments);t=_.apply(this,arguments);return t.mode="toggle",this.effect.call(this,t)}),cssUnit:function(t){var i=this.css(t),o=[];return b.each(["em","px","%","pt"],function(t,e){0<i.indexOf(e)&&(o=[parseFloat(i),e])}),o},cssClip:function(t){return t?this.css("clip","rect("+t.top+"px "+t.right+"px "+t.bottom+"px "+t.left+"px)"):j(this.css("clip"),this)},transfer:function(t,e){var i=b(this),o=b(t.to),n="fixed"===o.css("position"),r=b("body"),s=n?r.scrollTop():0,a=n?r.scrollLeft():0,r=o.offset(),r={top:r.top-s,left:r.left-a,height:o.innerHeight(),width:o.innerWidth()},o=i.offset(),f=b("<div class='ui-effects-transfer'></div>");f.appendTo("body").addClass(t.className).css({top:o.top-s,left:o.left-a,height:i.innerHeight(),width:i.innerWidth(),position:n?"fixed":"absolute"}).animate(r,t.duration,t.easing,function(){f.remove(),"function"==typeof e&&e()})}}),b.fx.step.clip=function(t){t.clipInit||(t.start=b(t.elem).cssClip(),"string"==typeof t.end&&(t.end=j(t.end,t.elem)),t.clipInit=!0),b(t.elem).cssClip({top:t.pos*(t.end.top-t.start.top)+t.start.top,right:t.pos*(t.end.right-t.start.right)+t.start.right,bottom:t.pos*(t.end.bottom-t.start.bottom)+t.start.bottom,left:t.pos*(t.end.left-t.start.left)+t.start.left})},W={},b.each(["Quad","Cubic","Quart","Quint","Expo"],function(e,t){W[t]=function(t){return Math.pow(t,e+2)}}),b.extend(W,{Sine:function(t){return 1-Math.cos(t*Math.PI/2)},Circ:function(t){return 1-Math.sqrt(1-t*t)},Elastic:function(t){return 0===t||1===t?t:-Math.pow(2,8*(t-1))*Math.sin((80*(t-1)-7.5)*Math.PI/15)},Back:function(t){return t*t*(3*t-2)},Bounce:function(t){for(var e,i=4;t<((e=Math.pow(2,--i))-1)/11;);return 1/Math.pow(4,3-i)-7.5625*Math.pow((3*e-2)/22-t,2)}}),b.each(W,function(t,e){b.easing["easeIn"+t]=e,b.easing["easeOut"+t]=function(t){return 1-e(1-t)},b.easing["easeInOut"+t]=function(t){return t<.5?e(2*t)/2:1-e(-2*t+2)/2}});o=b.effects,b.effects.define("blind","hide",function(t,e){var i={up:["bottom","top"],vertical:["bottom","top"],down:["top","bottom"],left:["right","left"],horizontal:["right","left"],right:["left","right"]},o=b(this),n=t.direction||"up",r=o.cssClip(),s={clip:b.extend({},r)},a=b.effects.createPlaceholder(o);s.clip[i[n][0]]=s.clip[i[n][1]],"show"===t.mode&&(o.cssClip(s.clip),a&&a.css(b.effects.clipToBox(s)),s.clip=r),a&&a.animate(b.effects.clipToBox(s),t.duration,t.easing),o.animate(s,{queue:!1,duration:t.duration,easing:t.easing,complete:e})}),b.effects.define("bounce",function(t,e){var i,o,n=b(this),r=t.mode,s="hide"===r,a="show"===r,f=t.direction||"up",c=t.distance,l=t.times||5,r=2*l+(a||s?1:0),u=t.duration/r,d=t.easing,h="up"===f||"down"===f?"top":"left",p="up"===f||"left"===f,g=0,t=n.queue().length;for(b.effects.createPlaceholder(n),f=n.css(h),c=c||n["top"==h?"outerHeight":"outerWidth"]()/3,a&&((o={opacity:1})[h]=f,n.css("opacity",0).css(h,p?2*-c:2*c).animate(o,u,d)),s&&(c/=Math.pow(2,l-1)),(o={})[h]=f;g<l;g++)(i={})[h]=(p?"-=":"+=")+c,n.animate(i,u,d).animate(o,u,d),c=s?2*c:c/2;s&&((i={opacity:0})[h]=(p?"-=":"+=")+c,n.animate(i,u,d)),n.queue(e),b.effects.unshift(n,t,1+r)}),b.effects.define("clip","hide",function(t,e){var i={},o=b(this),n=t.direction||"vertical",r="both"===n,s=r||"horizontal"===n,r=r||"vertical"===n,n=o.cssClip();i.clip={top:r?(n.bottom-n.top)/2:n.top,right:s?(n.right-n.left)/2:n.right,bottom:r?(n.bottom-n.top)/2:n.bottom,left:s?(n.right-n.left)/2:n.left},b.effects.createPlaceholder(o),"show"===t.mode&&(o.cssClip(i.clip),i.clip=n),o.animate(i,{queue:!1,duration:t.duration,easing:t.easing,complete:e})}),b.effects.define("drop","hide",function(t,e){var i=b(this),o="show"===t.mode,n=t.direction||"left",r="up"===n||"down"===n?"top":"left",s="up"===n||"left"===n?"-=":"+=",a="+="==s?"-=":"+=",f={opacity:0};b.effects.createPlaceholder(i),n=t.distance||i["top"==r?"outerHeight":"outerWidth"](!0)/2,f[r]=s+n,o&&(i.css(f),f[r]=a+n,f.opacity=1),i.animate(f,{queue:!1,duration:t.duration,easing:t.easing,complete:e})}),b.effects.define("explode","hide",function(t,e){var i,o,n,r,s,a,f=t.pieces?Math.round(Math.sqrt(t.pieces)):3,c=f,l=b(this),u="show"===t.mode,d=l.show().css("visibility","hidden").offset(),h=Math.ceil(l.outerWidth()/c),p=Math.ceil(l.outerHeight()/f),g=[];function m(){g.push(this),g.length===f*c&&(l.css({visibility:"visible"}),b(g).remove(),e())}for(i=0;i<f;i++)for(r=d.top+i*p,a=i-(f-1)/2,o=0;o<c;o++)n=d.left+o*h,s=o-(c-1)/2,l.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:-o*h,top:-i*p}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:h,height:p,left:n+(u?s*h:0),top:r+(u?a*p:0),opacity:u?0:1}).animate({left:n+(u?0:s*h),top:r+(u?0:a*p),opacity:u?1:0},t.duration||500,t.easing,m)}),b.effects.define("fade","toggle",function(t,e){var i="show"===t.mode;b(this).css("opacity",i?0:1).animate({opacity:i?1:0},{queue:!1,duration:t.duration,easing:t.easing,complete:e})}),b.effects.define("fold","hide",function(e,t){var i=b(this),o=e.mode,n="show"===o,r="hide"===o,s=e.size||15,a=/([0-9]+)%/.exec(s),f=!!e.horizFirst?["right","bottom"]:["bottom","right"],c=e.duration/2,l=b.effects.createPlaceholder(i),u=i.cssClip(),d={clip:b.extend({},u)},h={clip:b.extend({},u)},p=[u[f[0]],u[f[1]]],o=i.queue().length;a&&(s=parseInt(a[1],10)/100*p[r?0:1]),d.clip[f[0]]=s,h.clip[f[0]]=s,h.clip[f[1]]=0,n&&(i.cssClip(h.clip),l&&l.css(b.effects.clipToBox(h)),h.clip=u),i.queue(function(t){l&&l.animate(b.effects.clipToBox(d),c,e.easing).animate(b.effects.clipToBox(h),c,e.easing),t()}).animate(d,c,e.easing).animate(h,c,e.easing).queue(t),b.effects.unshift(i,o,4)}),b.effects.define("highlight","show",function(t,e){var i=b(this),o={backgroundColor:i.css("backgroundColor")};"hide"===t.mode&&(o.opacity=0),b.effects.saveStyle(i),i.css({backgroundImage:"none",backgroundColor:t.color||"#ffff99"}).animate(o,{queue:!1,duration:t.duration,easing:t.easing,complete:e})}),b.effects.define("size",function(o,e){var n,i=b(this),t=["fontSize"],r=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],s=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],a=o.mode,f="effect"!==a,c=o.scale||"both",l=o.origin||["middle","center"],u=i.css("position"),d=i.position(),h=b.effects.scaledDimensions(i),p=o.from||h,g=o.to||b.effects.scaledDimensions(i,0);b.effects.createPlaceholder(i),"show"===a&&(a=p,p=g,g=a),n={from:{y:p.height/h.height,x:p.width/h.width},to:{y:g.height/h.height,x:g.width/h.width}},"box"!==c&&"both"!==c||(n.from.y!==n.to.y&&(p=b.effects.setTransition(i,r,n.from.y,p),g=b.effects.setTransition(i,r,n.to.y,g)),n.from.x!==n.to.x&&(p=b.effects.setTransition(i,s,n.from.x,p),g=b.effects.setTransition(i,s,n.to.x,g))),"content"!==c&&"both"!==c||n.from.y!==n.to.y&&(p=b.effects.setTransition(i,t,n.from.y,p),g=b.effects.setTransition(i,t,n.to.y,g)),l&&(l=b.effects.getBaseline(l,h),p.top=(h.outerHeight-p.outerHeight)*l.y+d.top,p.left=(h.outerWidth-p.outerWidth)*l.x+d.left,g.top=(h.outerHeight-g.outerHeight)*l.y+d.top,g.left=(h.outerWidth-g.outerWidth)*l.x+d.left),delete p.outerHeight,delete p.outerWidth,i.css(p),"content"!==c&&"both"!==c||(r=r.concat(["marginTop","marginBottom"]).concat(t),s=s.concat(["marginLeft","marginRight"]),i.find("*[width]").each(function(){var t=b(this),e=b.effects.scaledDimensions(t),i={height:e.height*n.from.y,width:e.width*n.from.x,outerHeight:e.outerHeight*n.from.y,outerWidth:e.outerWidth*n.from.x},e={height:e.height*n.to.y,width:e.width*n.to.x,outerHeight:e.height*n.to.y,outerWidth:e.width*n.to.x};n.from.y!==n.to.y&&(i=b.effects.setTransition(t,r,n.from.y,i),e=b.effects.setTransition(t,r,n.to.y,e)),n.from.x!==n.to.x&&(i=b.effects.setTransition(t,s,n.from.x,i),e=b.effects.setTransition(t,s,n.to.x,e)),f&&b.effects.saveStyle(t),t.css(i),t.animate(e,o.duration,o.easing,function(){f&&b.effects.restoreStyle(t)})})),i.animate(g,{queue:!1,duration:o.duration,easing:o.easing,complete:function(){var t=i.offset();0===g.opacity&&i.css("opacity",p.opacity),f||(i.css("position","static"===u?"relative":u).offset(t),b.effects.saveStyle(i)),e()}})}),b.effects.define("scale",function(t,e){var i=b(this),o=t.mode,o=parseInt(t.percent,10)||(0===parseInt(t.percent,10)||"effect"!==o?0:100),o=b.extend(!0,{from:b.effects.scaledDimensions(i),to:b.effects.scaledDimensions(i,o,t.direction||"both"),origin:t.origin||["middle","center"]},t);t.fade&&(o.from.opacity=1,o.to.opacity=0),b.effects.effect.size.call(this,o,e)}),b.effects.define("puff","hide",function(t,e){t=b.extend(!0,{},t,{fade:!0,percent:parseInt(t.percent,10)||150});b.effects.effect.scale.call(this,t,e)}),b.effects.define("pulsate","show",function(t,e){var i=b(this),o=t.mode,n="show"===o,r=2*(t.times||5)+(n||"hide"===o?1:0),s=t.duration/r,a=0,f=1,o=i.queue().length;for(!n&&i.is(":visible")||(i.css("opacity",0).show(),a=1);f<r;f++)i.animate({opacity:a},s,t.easing),a=1-a;i.animate({opacity:a},s,t.easing),i.queue(e),b.effects.unshift(i,o,1+r)}),b.effects.define("shake",function(t,e){var i=1,o=b(this),n=t.direction||"left",r=t.distance||20,s=t.times||3,a=2*s+1,f=Math.round(t.duration/a),c="up"===n||"down"===n?"top":"left",l="up"===n||"left"===n,u={},d={},h={},n=o.queue().length;for(b.effects.createPlaceholder(o),u[c]=(l?"-=":"+=")+r,d[c]=(l?"+=":"-=")+2*r,h[c]=(l?"-=":"+=")+2*r,o.animate(u,f,t.easing);i<s;i++)o.animate(d,f,t.easing).animate(h,f,t.easing);o.animate(d,f,t.easing).animate(u,f/2,t.easing).queue(e),b.effects.unshift(o,n,1+a)}),b.effects.define("slide","show",function(t,e){var i,o,n=b(this),r={up:["bottom","top"],down:["top","bottom"],left:["right","left"],right:["left","right"]},s=t.mode,a=t.direction||"left",f="up"===a||"down"===a?"top":"left",c="up"===a||"left"===a,l=t.distance||n["top"==f?"outerHeight":"outerWidth"](!0),u={};b.effects.createPlaceholder(n),i=n.cssClip(),o=n.position()[f],u[f]=(c?-1:1)*l+o,u.clip=n.cssClip(),u.clip[r[a][1]]=u.clip[r[a][0]],"show"===s&&(n.cssClip(u.clip),n.css(f,u[f]),u.clip=i,u[f]=o),n.animate(u,{queue:!1,duration:t.duration,easing:t.easing,complete:e})}),!1!==b.uiBackCompat&&b.effects.define("transfer",function(t,e){b(this).transfer(t,e)})});