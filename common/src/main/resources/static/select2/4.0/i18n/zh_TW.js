(function() {
	if (jQuery && jQuery.fn && jQuery.fn.select2 && jQuery.fn.select2.amd)
		var e = jQuery.fn.select2.amd;
	return e.define("select2/i18n/zh_TW", [], function() {
		return { inputTooLong : function(e) {
			var t = e.input.length - e.maximum, n = "請刪掉" + t + "個字元";
			return n
		}, inputTooShort : function(e) {
			var t = e.minimum - e.input.length, n = "請再輸入" + t + "個字元";
			return n
		}, loadingMore : function() {
			return "載入中…"
		}, maximumSelected : function(e) {
			var t = "你只能選擇最多" + e.maximum + "項";
			return t
		}, noResults : function() {
			return "沒有找到相符的項目"
		}, searching : function() {
			return "搜尋中…"
		} }
	}), { define : e.define, require : e.require }
})();