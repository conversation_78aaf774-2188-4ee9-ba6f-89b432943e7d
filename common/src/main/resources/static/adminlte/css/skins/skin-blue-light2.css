/*
 * http://jeesite.com
 */
a, a:hover, a:active, a:focus, .form-unit, th[aria-selected=true] .ui-jqgrid-sortable {
  color:#1890ff;
}
.main-header .navbar {
  background: #1890ff;
  background: -webkit-gradient(linear, left bottom, right top, color-stop(0, #2684f5), color-stop(1, #1890ff));
  background: -ms-linear-gradient(left, #2684f5, #1890ff);
  background: -moz-linear-gradient(left top, #2684f5 0%, #1890ff 100%);
  background: -o-linear-gradient(#1890ff, #2684f5);
}
.main-header .navbar .nav > li > a {
  color: #ffffff;
}
.main-header .navbar .nav > li > a:hover,
.main-header .navbar .nav > li > a:active,
.main-header .navbar .nav > li > a:focus,
.main-header .navbar .nav .open > a,
.main-header .navbar .nav .open > a:hover,
.main-header .navbar .nav .open > a:focus,
.main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.1);
  color: #f6f6f6;
}
.main-header .navbar .sidebar-toggle {
  color: #ffffff;
}
.main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.1);
}
.main-header .navbar .sidebar-toggle {
  color: #fff;
}
.main-header .navbar .sidebar-toggle:hover {
  background-color: #367fa9;
}
@media (max-width: 767px) {
  .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .main-header .navbar .dropdown-menu li a:hover {
    background: #367fa9;
  }
}
.main-header .logo {
  background-color: transparent;
  color: #ffffff;
  border-bottom: 0 solid transparent;
}
.main-header .logo:hover {
  background-color: #1890ff;
}
.main-header li.user-header {
  background-color: #1890ff;
}
.content-header {
  background: transparent;
}
.sidebar,
.left-side {
  background-color: #ffffff;
}
.content-wrapper,
.main-footer {
  border-left: 1px solid #eeeeee;
}
.user-panel > .info,
.user-panel > .info > a {
  color: #555;
}
.sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.sidebar-menu > li.header {
  color: #848484;
  background: #f8f8f8;
}
.sidebar-menu > li > a {
  border-left: 3px solid transparent;
  font-weight: 600;
}
.sidebar-menu > li:hover > a,
.sidebar-menu > li.active > a {
  color: #000;
  background: #ffffff;
}
.sidebar-menu > li.active {
  border-left-color: #1890ff;
}
.sidebar-menu > li.active > a {
  font-weight: 600;
}
.sidebar-menu > li.menu-open > a,
.sidebar-menu > li > .treeview-menu {
  background: #ffffff;
}
.sidebar a {
  color: #555;
}
.sidebar a:hover {
  text-decoration: none;
}
.treeview-menu > li > a {
  color: #555;
}
.treeview-menu > li.active > a,
.treeview-menu > li > a:hover {
  color: #000;
}
.sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.sidebar-form input[type="text"],
.sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.sidebar-form input[type="text"]:focus,
.sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.main-footer {
  border-top-color: #e5e5e5;
  border-radius: 4px;
}
.skin-blue.layout-top-nav .main-header > .logo {
  background-color: #1890ff;
  color: #ffffff;
  border-bottom: 0 solid transparent;
}
.skin-blue.layout-top-nav .main-header > .logo:hover {
  background-color: #3b8ab8;
}

.sidebar-menu {padding:0 8px 0 7px;}
.sidebar-menu li>a>.pull-right-container {left:0;}
.sidebar-menu .treeview-item.active>a {color:#1890ff;background-color:#e7f4ff;border-right:0;border-radius:6px;}

.content-wrapper, .right-side, body {background-color:#f0f2f5;}

/* 页签-经典风格 */
.tabpanel_mover li {height:26px;padding:1px 16px 2px 3px;margin:6px 0 6px 6px;border:1px solid #e4e4e4;border-radius:3px;background:#fff;}
.tabpanel_mover li.active {background-color:#3aa0ff;box-shadow:0 0 5px #e6e6e6;}
.tabpanel_mover li.active div {color:#fff;}
.tabpanel_mover li .closer {font:11px/1 FontAwesome;top:6px;right:2px;background:none;opacity:0.7;}
.tabpanel_mover li .closer:before {content:"\f00d";}
.tabpanel_mover li .closer:hover {background:none;-moz-transform:scale(1.2);-webkit-transform:scale(1.2); 
     -o-transform:scale(1.2);-ms-transform:scale(1.2);transform:scale(1.2);color:#d30606;}
.tabpanel_mover li.active .closer:hover {color:#fff;opacity:0.9;}
.tabpanel_tab_content {background-color:#fafafa;border-bottom-color:#eeeeee;overflow:visible;}

.btn-primary, .btn-primary:hover, .btn-primary:active,
.btn-primary.hover, .btn-primary.focus, .btn-primary:focus,
.btn-primary.active.focus, .btn-primary.active:focus, .btn-primary.active:hover,
.btn-primary:active.focus, .btn-primary:active:focus, .btn-primary:active:hover,
.open>.dropdown-toggle.btn-primary.focus, .open>.dropdown-toggle.btn-primary:focus,
.open>.dropdown-toggle.btn-primary:hover, .layui-layer-btn .layui-layer-btn0,
.select2-container--default .select2-selection--multiple .select2-selection__choice,
.select2-container--default .select2-results__option--highlighted[aria-selected],
.pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover,
.pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover,
.wup_container .placeholder .webuploader-pick {background-color:#1890ff!important;border-color:#1890ff;}
.form-unit, th[aria-selected=true] .ui-jqgrid-sortable {color:#1890ff;}
.form-unit {border-bottom:1px solid #eee;}

.box-main>.box-header {border-bottom-color:#eeeeee;}
.box-main>.box-header .box-title .fa {color:#1890ff;}
.nav-tabs-custom>.nav-tabs>li.active {border-top-color:#3aa0ff;}
.form-control:focus,.select2-container--default.select2-container--focus .select2-selection--multiple,
.select2-container--default .select2-search--dropdown .select2-search__field,
.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--default.select2-container--focus .select2-selection--multiple {border-color:#40a9ff!important;box-shadow:0 0 0 2px rgba(24,144,255,.2);}
.table thead tr, .ui-jqgrid-htable thead tr, .ui-jqgrid-hdiv, .ui-jqgrid-hbox {background-color:#f6f6f6;}
.ui-jqgrid .ui-jqgrid-labels th, .ui-jqgrid tr.ui-row-ltr td, .ui-jqgrid tr.ui-row-rtl td, .ui-jqgrid tr.ui-row-ltr td:last-child,
.ui-jqgrid .ui-jqgrid-htable th.ui-th-column-header, .ui-jqgrid .ui-jqgrid-htable th.ui-th-column,
.ui-jqgrid .frozen-right th.ui-th-ltr, .ui-jqgrid .frozen-right tr.ui-row-ltr td,
.ui-jqgrid .frozen-left th.ui-th-ltr, .ui-jqgrid .frozen-left tr.ui-row-ltr td,
.ui-jqgrid.ui-widget-content, .ui-jqgrid .ui-widget-content {border-color:#eaeaea;}
.ui-state-hover td, .ui-widget-content .ui-state-hover td, .ui-widget-header .ui-state-hover td,
.ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus {background:#f5f5f5;}
.ui-jqgrid tr.ui-state-highlight.ui-row-ltr td {background-color:#ecf9ff;}
/* .ui-jqgrid tr.ui-row-ltr td {border-right:0!important;} 解开注释，可去除表格单元格的竖边框线 */
