/*
 * Skin: Green
 * -----------
 */
.main-header .navbar {
  background-color: #00a65a;
}
.main-header .navbar .nav > li > a {
  color: #ffffff;
}
.main-header .navbar .nav > li > a:hover,
.main-header .navbar .nav > li > a:active,
.main-header .navbar .nav > li > a:focus,
.main-header .navbar .nav .open > a,
.main-header .navbar .nav .open > a:hover,
.main-header .navbar .nav .open > a:focus,
.main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.1);
  color: #f6f6f6;
}
.main-header .navbar .sidebar-toggle {
  color: #ffffff;
}
.main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.1);
}
.main-header .navbar .sidebar-toggle {
  color: #fff;
}
.main-header .navbar .sidebar-toggle:hover {
  background-color: #008d4c;
}
@media (max-width: 767px) {
  .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .main-header .navbar .dropdown-menu li a:hover {
    background: #008d4c;
  }
}
.main-header .logo {
  background-color: #00a65a;
  color: #ffffff;
  border-bottom: 0 solid transparent;
}
.main-header .logo:hover {
  background-color: #00a157;
}
.main-header li.user-header {
  background-color: #00a65a;
}
.content-header {
  background: transparent;
}
.sidebar,
.left-side {
  background-color: #eaedf1;
}
.content-wrapper,
.main-footer {
  border-left: 1px solid #d2d6de;
}
.user-panel > .info,
.user-panel > .info > a {
  color: #444444;
}
.sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.sidebar-menu > li.header {
  color: #848484;
  background: #eaedf1;
}
.sidebar-menu > li > a {
  border-left: 3px solid transparent;
  font-weight: 600;
}
.sidebar-menu > li:hover > a,
.sidebar-menu > li.active > a {
  color: #000000;
  background: #f4f6f8;
}
.sidebar-menu > li.active {
  border-left-color: #00a65a;
}
.sidebar-menu > li.active > a {
  font-weight: 600;
}
.sidebar-menu > li.menu-open > a,
.sidebar-menu > li > .treeview-menu {
  background: #f4f6f8;
}
.sidebar a {
  color: #444444;
}
.sidebar a:hover {
  text-decoration: none;
}
.treeview-menu > li > a {
  color: #777777;
}
.treeview-menu > li.active > a,
.treeview-menu > li > a:hover {
  color: #000000;
}
.sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.sidebar-form input[type="text"],
.sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.sidebar-form input[type="text"]:focus,
.sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}

.sidebar-menu .treeview-item.active > a {color:#000;background-color:#fff;}

a, a:hover, a:active, a:focus, .form-unit,
th[aria-selected=true] .ui-jqgrid-sortable {color:#00a65a;}
.btn-primary, .btn-primary:hover, .btn-primary:active,
.btn-primary.hover, .btn-primary.focus, .btn-primary:focus,
.btn-primary.active.focus, .btn-primary.active:focus, .btn-primary.active:hover,
.btn-primary:active.focus, .btn-primary:active:focus, .btn-primary:active:hover,
.open>.dropdown-toggle.btn-primary.focus, .open>.dropdown-toggle.btn-primary:focus,
.open>.dropdown-toggle.btn-primary:hover, .layui-layer-btn .layui-layer-btn0,
.select2-container--default .select2-selection--multiple .select2-selection__choice,
.select2-container--default .select2-results__option--highlighted[aria-selected],
.pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover,
.pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover,
.wup_container .placeholder .webuploader-pick {background-color:#00a65a!important;border-color:#00a65a;}
