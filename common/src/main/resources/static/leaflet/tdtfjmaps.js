﻿var vec_fj_url = "service.fjmap.net/region/wmts";
var cva_fj_url = "service.fjmap.net/place/wmts";
var img_fj_url = "http://59.204.14.176:83/img_fj/wmts";
var cia_fj_url = "http://59.204.14.176:83/cia_fj/wmts";
var ter_fj_url = "http://59.204.14.176:83/ter_fj/wmts";
var cta_fj_url = "http://59.204.14.176:83/cta_fj/wmts";
var vec_fj = new L.TileLayer.WMTS(vec_fj_url, {
    layer: "region",
    style: "default",
    tilematrixSet: "Matrix_0",
    format: "image/png",
    minZoom: 7,
    maxZoom: 17,
    attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
});
var cva_fj = new L.TileLayer.WMTS(cva_fj_url, {
    layer: "place",
    style: "default",
    tilematrixSet: "Matrix_0",
    format: "image/png",
    minZoom: 7,
    maxZoom: 17,
    attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
});
var img_fj = new L.TileLayer.WMTS(img_fj_url, {
    layer: "img_fj",
    style: "default",
    tilematrixSet: "Matrix_0",
    format: "image/png",
    minZoom: 7,
    maxZoom: 17,
    attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
});
var cia_fj = new L.TileLayer.WMTS(cia_fj_url, {
    layer: "cia_fj",
    style: "default",
    tilematrixSet: "Matrix_0",
    format: "image/png",
    minZoom: 7,
    maxZoom: 17,
    attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
});
var ter_fj = new L.TileLayer.WMTS(ter_fj_url, {
    layer: "ter_fj",
    style: "default",
    tilematrixSet: "Matrix_0",
    format: "image/png",
    minZoom: 7,
    maxZoom: 17,
    attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
});
var cta_fj = new L.TileLayer.WMTS(cta_fj_url, {
    layer: "cta_fj",
    style: "default",
    tilematrixSet: "Matrix_0",
    format: "image/png",
    minZoom: 7,
    maxZoom: 17,
    attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
});
var TDTLayers = new L.Control.IconLayers([{
        title: '矢量', // use any string
        layer: L.layerGroup([vec_fj, cva_fj]), // any ILayer
        icon: 'leaflet/LayerIcons/Vectory.png' // 80x80 icon
    },
    {
        title: '影像',
        layer: L.layerGroup([img_fj, cia_fj]),
        icon: 'leaflet/LayerIcons/Satellite.png'
    },
    {
        title: '地形',
        layer: L.layerGroup([ter_fj, cta_fj]),
        icon: 'leaflet/LayerIcons/Terrain.png'
    }
], {
    position: 'topright',
    maxLayersInRow: 5
});