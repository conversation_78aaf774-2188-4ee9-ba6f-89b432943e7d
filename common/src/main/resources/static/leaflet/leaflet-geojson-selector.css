 
.leaflet-container .geojson-list {
	position:relative;
	float:left;
	color:#1978cf;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	border-radius: 0;
	background-color: rgba(255, 255, 255, 0.6);
	z-index:1000;	
	box-shadow: 0 1px 7px rgba(0,0,0,0.65);
	margin: 0;
	min-width: 50px;
	min-height:26px;
	overflow-y: scroll;
}

.leaflet-control.geojson-list .geojson-list-toggle {
	display: none;
}
.leaflet-control.geojson-list.geojson-list-collapsed .geojson-list-toggle {
	display: block;
}
.geojson-list-group {
	list-style: none;
	padding: 0;
	margin: 0;
}
.geojson-list-item {
	padding: 0;
	margin: 0;
	clear: both;
	cursor: pointer;
	display: block;
	overflow: hidden;
	line-height: 18px;
	vertical-align: middle;
	font-size: 14px;
	min-width: 120px;
	color: #666;	
	text-decoration: none;
	border-bottom: 1px solid rgba(0,0,0, 0.5);
	-moz-user-select: none;
	-khtml-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
	user-select: none;

	white-space: nowrap;
	text-transform: capitalize;
}
.geojson-list-item:hover,
.geojson-list-item.active {
	display: block;
	color: #666;
	text-decoration: none;
	background-color: rgba(255,204,0, 0.6);	/* #FFCC00 */
}
.geojson-list-item.selected {
	color: #666;
	background-color: rgba(255,136,0, 0.8);	/* #FF8800 */
}
.geojson-list-item input {
	line-height: 18px;
	margin: 4px;
}
.geojson-list-item label {
	display: block;
	cursor: pointer;
	vertical-align: middle;
}
.leaflet-control.geojson-list .geojson-list-group {
	display: block;
}
.leaflet-control.geojson-list.geojson-list-collapsed .geojson-list-ul {
	display: none;
}

.geojson-list .geojson-list-toggle {
	display:block;
	float:left;
	width:26px;
	height:26px;
	background: url('../images/list-icon.png') no-repeat 2px 2px;
	border-radius:4px;
}
.geojson-list .geojson-list-toggle:hover,
.geojson-list .geojson-list-toggle.active:hover {
	background: url('../images/list-icon.png') no-repeat 2px -24px #fff;
}
.geojson-list .geojson-list-toggle.active {
	background: url('../images/list-icon.png') no-repeat 2px -50px #fff;
}



