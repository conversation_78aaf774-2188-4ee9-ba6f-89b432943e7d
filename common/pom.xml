<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.hsobs</groupId>
		<artifactId>hsobs-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath>../parent/pom.xml</relativePath>
	</parent>
	
	<artifactId>hsobs-common</artifactId>
	<packaging>jar</packaging>

	
	<dependencies>

		<!-- Servlet Api -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		
		<!-- Apache Commons -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>${commons-text.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons-io.version}</version>
		</dependency>
		
		<!-- Java serialization -->
		<dependency>
			<groupId>de.ruedigermoeller</groupId>
			<artifactId>fst</artifactId>
			<version>${fst.version}</version>
		</dependency>
		
		<!-- Jackson json -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
		</dependency>
		
		<!-- Fast json -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		
		<!-- XPath xml -->
		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
		</dependency>
		
		<!-- Snake YAML -->
		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
			<version>${snakeyaml.version}</version>
		</dependency>
		
		<!-- Apache HTTP -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>${httpclient.version}</version>
		</dependency>
		
		<!-- Email -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-email</artifactId>
			<version>${commons-email.version}</version>
		</dependency>
		<!-- <dependency>
			<groupId>javax.activation</groupId>
			<artifactId>activation</artifactId>
			<version>${activation.version}</version>
		</dependency> -->
		
		<!-- User Agent -->
		<dependency>
			<groupId>eu.bitwalker</groupId>
			<artifactId>UserAgentUtils</artifactId>
			<version>${UserAgentUtils.version}</version>
		</dependency>
		
		<!-- 图片Meta获取
		<dependency>
			<groupId>com.drewnoakes</groupId>
			<artifactId>metadata-extractor</artifactId>
			<version>${metadata-extractor.version}</version>
		</dependency> -->
		<!-- 缩略图工具 -->
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>${thumbnailator.version}</version>
		</dependency>
		<!-- 支持CMYK图片 -->
		<dependency>
			<groupId>com.twelvemonkeys.imageio</groupId>
			<artifactId>imageio-jpeg</artifactId>
			<version>${twelvemonkeys.version}</version>
		</dependency>
		<!-- 图片验证码生成 -->
		<dependency>
			<groupId>com.bladejava</groupId>
			<artifactId>blade-patchca</artifactId>
			<version>${blade-patchca.version}</version>
		</dependency>
		<!-- File MimeType ContentType -->
		<dependency>
			<groupId>net.sf.jmimemagic</groupId>
			<artifactId>jmimemagic</artifactId>
			<version>${jmimemagic.version}</version>
			<exclusions>
				<exclusion>
					<groupId>xerces</groupId>
					<artifactId>xercesImpl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.jackson</groupId>
					<artifactId>jackson-xc</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.jackson</groupId>
					<artifactId>jackson-jaxrs</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<!-- 条形码、二维码生成 -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>${zxing.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>${zxing.version}</version>
		</dependency>
		
		<!-- Office Tools -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>${poi.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${poi.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-scratchpad</artifactId>
			<version>${poi.version}</version>
		</dependency>
		
		<!-- Pinyin4j -->
		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>${pinyin4j.version}</version>
		</dependency>

		<!-- BC Provider -->
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk18on</artifactId>
			<version>${bcprov.version}</version>
		</dependency>
		
		<!-- Logging begin -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<!-- common-logging 实际调用 slf4j -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>
		<!-- jdk logging 实际调用 slf4j -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jul-to-slf4j</artifactId>
		</dependency>
		<!-- log4j 实际调用 slf4j -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>log4j-over-slf4j</artifactId>
		</dependency>
		<!-- slf4j logback 实现 -->
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>
		<!-- Logging end -->
		
		<!-- Spring begin -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- Spring end -->
		
		<!-- JUnit Test -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
		</dependency>

	</dependencies>
	
	<developers>
		<developer>
			<id>thinkgem</id>
			<name>WangZhen</name>
			<email>thinkgem at 163.com</email>
			<roles><role>Project lead</role></roles>
			<timezone>+8</timezone>
		</developer>
	</developers>
	
	<organization>
		<name>JeeSite</name>
		<url>http://jeesite.com</url>
	</organization>
	
</project>
