<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.18</version>
		<relativePath />
	</parent>

	<groupId>com.hsobs</groupId>
	<artifactId>hsobs-parent</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<properties>

		<!-- common version setting -->
		<project.jeesite.version>5.9.1-SNAPSHOT</project.jeesite.version>
		<commons-io.version>2.13.0</commons-io.version>
		<commons-text.version>1.10.0</commons-text.version>
		<commons-email.version>1.5</commons-email.version>

		<jackson-bom2.version>2.17.1</jackson-bom2.version>
		<fastjson.version>2.0.51</fastjson.version>
		<fst.version>2.57</fst.version>
		<snakeyaml.version>1.33</snakeyaml.version>
		<activation.version>1.1.1</activation.version>
		<UserAgentUtils.version>1.21</UserAgentUtils.version>

		<metadata-extractor.version>2.11.0</metadata-extractor.version>
		<thumbnailator.version>0.4.19</thumbnailator.version>
		<twelvemonkeys.version>3.11.0</twelvemonkeys.version>
		<blade-patchca.version>1.1.2</blade-patchca.version>
		<jmimemagic.version>0.1.5</jmimemagic.version>
		<zxing.version>3.5.3</zxing.version>

		<poi.version>5.2.3</poi.version>
		<pinyin4j.version>2.5.1</pinyin4j.version>
		<groovy.version>3.0.17</groovy.version>
		<bcprov.version>1.78.1</bcprov.version>

		<joda-time.version>2.12.7</joda-time.version>
		<logstash-logback.version>7.3</logstash-logback.version>
		<elasticsearch.version>7.17.8</elasticsearch.version>
		<lucene.version>8.11.1</lucene.version>

		<!-- framework version setting -->
		<mybatis.version>3.5.15</mybatis.version>
		<mybatis-spring.version>2.0.7</mybatis-spring.version>
		<jsqlparser.version>4.7</jsqlparser.version>
		<druid.version>1.2.23</druid.version>
		<shiro.version>1.13.0</shiro.version>
		<j2cache.version>2.8.0-release</j2cache.version>
		<swagger.version>1.6.6</swagger.version>
		<liquibase.version>4.20.0</liquibase.version>
		<spring-framework.version>5.3.39</spring-framework.version>
		<logback.version>1.2.13</logback.version>

		<!-- jdbc setting -->
		<mysql.version>8.0.33</mysql.version>
		<h2.version>1.4.200</h2.version>

		<!-- environment setting -->
		<java.version>1.8</java.version>
		<!--<tomcat.version>9.0.75</tomcat.version>-->
		<maven.test.skip>true</maven.test.skip>
		<resource.delimiter>@</resource.delimiter>
		<maven.compiler.source>${java.version}</maven.compiler.source>
		<maven.compiler.target>${java.version}</maven.compiler.target>
		<maven-surefire-plugin.version>2.18.1</maven-surefire-plugin.version><!-- 其它版本可能会出现VM崩溃 -->
		<eclipse-plugin-download-sources>false</eclipse-plugin-download-sources>
		<eclipse-plugin-download-javadocs>false</eclipse-plugin-download-javadocs>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

		<!-- docker setting -->
		<docker.dockerHost>http://docker.local:2375</docker.dockerHost>
		<docker.imageName>thinkgem/${project.artifactId}:latest</docker.imageName>
		<docker.platform>linux/amd64</docker.platform>
		<docker.run.port>8980:8980</docker.run.port>

	</properties>

	<build>

		<plugins>

			<!-- compiler插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>

			<!-- jar插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
			</plugin>

			<!-- resource插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
			</plugin>

			<!-- timestamp插件 -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
			</plugin>

		</plugins>

		<pluginManagement>

			<plugins>

				<!-- compiler插件，设定JDK版本 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<!--<version>3.8.1</version>-->
					<configuration>
						<parameters>true</parameters>
						<showWarnings>true</showWarnings>
						<!-- <compilerArguments>
							<verbose /> 输出有关编译器正在执行的操作的消息
							<bootclasspath>${JAVA_HOME}\jre\lib\rt.jar;${JAVA_HOME}\jre\lib\jce.jar</bootclasspath>
						</compilerArguments> -->
					</configuration>
				</plugin>

				<!-- timestamp插件，生成编译时间 -->
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>build-helper-maven-plugin</artifactId>
					<executions>
						<execution>
							<id>timestamp-property</id>
							<goals>
								<goal>timestamp-property</goal>
							</goals>
							<configuration>
								<name>build.time</name>
								<pattern>yyyy-MM-dd HH:mm</pattern>
								<timeZone>GMT+8</timeZone>
							</configuration>
						</execution>
					</executions>
				</plugin>

				<!-- jar插件，配置manifest文件，加入lib包的jar依赖 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-jar-plugin</artifactId>
					<!--<version>3.2.2</version>-->
					<configuration>
						<archive>
							<manifest>
								<!--suppress UnresolvedMavenProperty -->
								<mainClass>${start-class}</mainClass>
								<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
							</manifest>
							<addMavenDescriptor>false</addMavenDescriptor>
							<manifestEntries>
								<!--suppress UnresolvedMavenProperty -->
								<Build-Time>${build.time}</Build-Time>
							</manifestEntries>
						</archive>
					</configuration>
					<executions>
						<execution>
							<configuration>
								<excludes>
									<exclude>userfiles/**</exclude>
								</excludes>
								<finalName>${project.artifactId}</finalName>
								<archive>
									<manifest>
										<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
										<addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
										<addClasspath>true</addClasspath>
									</manifest>
								</archive>
							</configuration>
						</execution>
					</executions>
				</plugin>

				<!-- war插件，war包名称不带版本号 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-war-plugin</artifactId>
					<!--<version>3.3.2</version>-->
					<configuration>
						<warSourceExcludes>
							userfiles/**
						</warSourceExcludes>
						<webappDirectory>${project.build.directory}/${project.artifactId}</webappDirectory>
						<warName>${project.artifactId}</warName>
						<failOnMissingWebXml>false</failOnMissingWebXml>
						<archive>
							<manifest>
								<!--suppress UnresolvedMavenProperty -->
								<mainClass>${start-class}</mainClass>
								<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
							</manifest>
							<addMavenDescriptor>false</addMavenDescriptor>
							<manifestEntries>
								<!--suppress UnresolvedMavenProperty -->
								<Build-Time>${build.time}</Build-Time>
							</manifestEntries>
						</archive>
					</configuration>
				</plugin>

				<!-- resource插件 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-resources-plugin</artifactId>
					<!--<version>3.2.0</version>-->
					<configuration>
						<propertiesEncoding>${project.build.sourceEncoding}</propertiesEncoding>
						<delimiters>
							<delimiter>${resource.delimiter}</delimiter>
						</delimiters>
						<useDefaultDelimiters>false</useDefaultDelimiters>
					</configuration>
				</plugin>

				<!-- exec插件 -->
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>exec-maven-plugin</artifactId>
					<!--<version>3.1.0</version>-->
				</plugin>

				<!-- springboot插件 -->
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
					<executions>
						<execution>
							<id>repackage</id>
							<goals>
								<goal>repackage</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
						<!--suppress UnresolvedMavenProperty -->
						<mainClass>${start-class}</mainClass>
					</configuration>
				</plugin>

				<!-- Eclipse 插件 -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-eclipse-plugin</artifactId>
					<version>2.10</version>
					<configuration>
						<downloadSources>${eclipse-plugin-download-sources}</downloadSources>
						<downloadJavadocs>${eclipse-plugin-download-javadocs}</downloadJavadocs>
						<wtpversion>2.0</wtpversion>
						<jeeversion>6.0</jeeversion>
						<additionalConfig>
							<file>
								<name>.settings/org.eclipse.core.resources.prefs</name>
								<content>
									<![CDATA[eclipse.preferences.version=1${line.separator}encoding/<project>=${project.build.sourceEncoding}${line.separator}]]>
								</content>
							</file>
						</additionalConfig>
						<additionalProjectnatures>
							<projectnature>org.springframework.ide.eclipse.core.springnature</projectnature>
						</additionalProjectnatures>
					</configuration>
				</plugin>

				<!-- Java Document Generate -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-javadoc-plugin</artifactId>
					<version>3.2.0</version>
					<executions>
						<execution>
							<phase>prepare-package</phase>
							<goals>
								<goal>jar</goal>
							</goals>
							<!-- <configuration>
								<additionalparam>-Xdoclint:none</additionalparam>
							</configuration> -->
						</execution>
					</executions>
				</plugin>

				<!-- JavaScript CSS Compress -->
				<plugin>
					<groupId>com.jeesite.maven</groupId>
					<artifactId>compressor-maven-plugin</artifactId>
					<version>1.0.0-SNAPSHOT</version>
					<executions>
						<execution>
							<phase>prepare-package</phase>
							<goals>
								<goal>compress</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
						<encoding>UTF-8</encoding>
						<jswarn>false</jswarn>
						<nosuffix>true</nosuffix>
						<linebreakpos>30000</linebreakpos>
						<force>true</force>
						<includes>
							<include>**/*.js</include>
							<include>**/*.css</include>
						</includes>
						<excludes>
							<exclude>**/WEB-INF/classes/**</exclude>
							<exclude>**/*.min.js</exclude>
							<exclude>**/*.min.css</exclude>
						</excludes>
					</configuration>
				</plugin>

				<!-- Eclipse m2e settings only -->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.apache.maven.plugins</groupId>
										<artifactId>maven-dependency-plugin</artifactId>
										<!--suppress UnresolvedMavenProperty -->
										<versionRange>${maven-dependency-plugin.version}</versionRange>
										<goals>
											<goal>copy-dependencies</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>

				<!-- Docker 插件 https://github.com/fabric8io/docker-maven-plugin - https://dmp.fabric8.io -->
				<plugin>
					<groupId>io.fabric8</groupId>
					<artifactId>docker-maven-plugin</artifactId>
					<version>0.43.4</version>
					<configuration>
						<dockerHost>${docker.dockerHost}</dockerHost>
						<verbose>true</verbose>
						<images>
							<image>
								<name>${docker.imageName}</name>
								<alias>${project.artifactId}</alias>
								<build>
									<dockerFile>${project.basedir}/bin/docker/Dockerfile</dockerFile>
									<buildOptions>
										<platform>${docker.platform}</platform>
									</buildOptions>
									<assembly>
										<descriptorRef>artifact</descriptorRef>
									</assembly>
								</build>
								<run>
									<ports>
										<port>${docker.run.port}</port>
									</ports>
									<network>
										<mode>host</mode>
									</network>
								</run>
							</image>
						</images>
					</configuration>
				</plugin>

			</plugins>

		</pluginManagement>

		<!-- 资源文件配置 -->
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>rebel.xml</exclude>
				</excludes>
			</resource>
		</resources>
		<testResources>
			<testResource>
				<directory>src/test/java</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</testResource>
			<testResource>
				<directory>src/test/resources</directory>
				<excludes>
					<exclude>rebel.xml</exclude>
				</excludes>
			</testResource>
		</testResources>

	</build>

	<developers>
		<developer>
			<id>thinkgem</id>
			<name>WangZhen</name>
			<email>thinkgem at 163.com</email>
			<roles><role>Project lead</role></roles>
			<timezone>+8</timezone>
		</developer>
	</developers>

	<organization>
		<name>JeeSite</name>
		<url>http://jeesite.com</url>
	</organization>

	<repositories>
		<repository>
			<id>aliyun-repos</id>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases><enabled>true</enabled></releases>
			<snapshots><enabled>false</enabled></snapshots>
		</repository>
		<repository>
			<id>jeesite-repos</id>
			<url>https://maven.jeesite.net/repository/maven-public</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>aliyun-repos</id>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases><enabled>true</enabled></releases>
			<snapshots><enabled>false</enabled></snapshots>
		</pluginRepository>
		<pluginRepository>
			<id>jeesite-repos</id>
			<url>https://maven.jeesite.net/repository/maven-public</url>
		</pluginRepository>
	</pluginRepositories>

	<profiles>

		<profile>
			<id>javadoc</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>package</id>
			<build>
				<plugins>
					<plugin>
						<groupId>com.jeesite.maven</groupId>
						<artifactId>compressor-maven-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>deploy</id>
			<build>
				<plugins>
					<plugin>
						<groupId>com.jeesite.maven</groupId>
						<artifactId>compressor-maven-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
			<distributionManagement>
				<snapshotRepository>
					<id>jeesite-repos-s</id>
					<url>https://maven.jeesite.net/repository/maven-snapshots</url>
				</snapshotRepository>
			</distributionManagement>
		</profile>
	</profiles>

</project>
