-- 人才住房补助申请增加区域信息
ALTER TABLE hs_talent_introduction_apply ADD CITY VARCHAR(64);
COMMENT ON COLUMN hs_talent_introduction_apply.CITY IS '城市';

ALTER TABLE hs_talent_introduction_apply ADD AREA VARCHAR(64);
COMMENT ON COLUMN hs_talent_introduction_apply.AREA IS '区域';

-- 追加数据 20250429   sys_role_data_scope   6-本部门数据不含下级单位

-- 维修资金增加下级菜单 单位申请  20250430


-- 维修资金申请表增加房屋坐落字段
ALTER TABLE hs_maintenance_apply ADD HOUSE_ADDRESS VARCHAR(512);
COMMENT ON COLUMN hs_maintenance_apply.HOUSE_ADDRESS IS '房屋坐落';

-- 数据管理 电子档案 合同管理 增加合同查询菜单

-- 数据填报增加上传状态
ALTER TABLE hs_data_form_delivery_record ADD UPLOAD_STATUS VARCHAR(1);
COMMENT ON COLUMN hs_data_form_delivery_record.UPLOAD_STATUS IS '上传状态';
ALTER TABLE hs_data_form_delivery_record ADD UPLOAD_DATE TIMESTAMP(6);
COMMENT ON COLUMN hs_data_form_delivery_record.UPLOAD_DATE IS '上传时间';

--
CREATE TABLE "HS_DATA_FORM_FILL_RECORD_FNL"
(
    "ID" VARCHAR(64) NOT NULL,
    "FILL_ID" VARCHAR(64),
    "TEMPLATE_ID" VARCHAR(64),
    "FIELD_ID" VARCHAR(32),
    "FIELD_TYPE" VARCHAR(32),
    "FIELD_NAME" VARCHAR(255),
    "FIELD_KEY" VARCHAR(255),
    "FIELD_VALUE" VARCHAR(800),
    "STATUS" VARCHAR(10),
    "CREATE_BY" VARCHAR(64),
    "CREATE_DATE" DATETIME(6),
    "UPDATE_BY" VARCHAR(64),
    "UPDATE_DATE" DATETIME(6),
    "VALID_TAG" CHAR(1) DEFAULT 1,
    CONSTRAINT "pk_hs_data_form_fill_record_fnl" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "HS_DATA_FORM_FILL_RECORD_FNL" IS '数据表单填写详情记录表';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."CREATE_DATE" IS '创建时间';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."FIELD_ID" IS '字段ID;1-多选框 2-单选框 3-文本框 4-文本域 5-下拉框';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."FIELD_KEY" IS '字段标识';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."FIELD_NAME" IS '字段名称';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."FIELD_TYPE" IS '字段类型;1-多选框 2-单选框 3-文本框 4-文本域 5-下拉框';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."FIELD_VALUE" IS '字段填写值';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."FILL_ID" IS '填写记录ID';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."ID" IS '主识别ID';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."STATUS" IS '状态';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."TEMPLATE_ID" IS '表单模板ID';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."UPDATE_DATE" IS '更新时间';
COMMENT ON COLUMN "HS_DATA_FORM_FILL_RECORD_FNL"."VALID_TAG" IS '是否有效;1-有效 0-无效';


-- 加装电梯补助申请增加区域信息
ALTER TABLE HS_ELEVATOR_APPLY ADD CITY VARCHAR(64);
COMMENT ON COLUMN HS_ELEVATOR_APPLY.CITY IS '城市';

ALTER TABLE HS_ELEVATOR_APPLY ADD AREA VARCHAR(64);
COMMENT ON COLUMN HS_ELEVATOR_APPLY.AREA IS '区域';

