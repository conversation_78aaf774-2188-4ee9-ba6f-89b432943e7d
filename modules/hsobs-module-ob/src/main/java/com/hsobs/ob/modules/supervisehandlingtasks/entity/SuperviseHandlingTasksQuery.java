package com.hsobs.ob.modules.supervisehandlingtasks.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 监督督办任务Entity
 * <AUTHOR>
 * @version 2024-12-10
 */
public class SuperviseHandlingTasksQuery extends BpmEntity<SuperviseHandlingTasksQuery> {

	private static final long serialVersionUID = 1L;

	private String officeName;		//被检查单位
	private String officeCode;		//被检查单位
	private Date checkDate;		// 检查日期
	private String verifyTheSituation;		// 核实情况
	private String rectificationMatters;		// 督办整改事项
	private String rectificationResultFeedback;		// 整改结果反馈
	private String status;		// 审批状态
	private String taskStatus;		// 整改状态
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}

	@ExcelFields({
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="被检查单位", attrName="officeName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=51),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="检查日期", attrName="checkDate", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=70, dataFormat="yyyy-MM-dd hh:mm"),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="核实情况", attrName="verifyTheSituation", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=90),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="督办整改事项", attrName="rectificationMatters", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=100),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="整改结果反馈", attrName="rectificationResultFeedback", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=110),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="审批状态", attrName="status", dictType="sys_office_type", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=120),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="整改状态", attrName="taskStatus", dictType="supervise_handling_task_status", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=120),
	})

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public String getVerifyTheSituation() {
		return verifyTheSituation;
	}

	public void setVerifyTheSituation(String verifyTheSituation) {
		this.verifyTheSituation = verifyTheSituation;
	}

	public String getRectificationMatters() {
		return rectificationMatters;
	}

	public void setRectificationMatters(String rectificationMatters) {
		this.rectificationMatters = rectificationMatters;
	}

	public String getRectificationResultFeedback() {
		return rectificationResultFeedback;
	}

	public void setRectificationResultFeedback(String rectificationResultFeedback) {
		this.rectificationResultFeedback = rectificationResultFeedback;
	}

	@Override
	public String getStatus() {
		return status;
	}

	@Override
	public void setStatus(String status) {
		this.status = status;
	}

	public String getTaskStatus() {
		return taskStatus;
	}

	public void setTaskStatus(String taskStatus) {
		this.taskStatus = taskStatus;
	}

}