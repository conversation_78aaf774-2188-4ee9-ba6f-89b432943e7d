package com.hsobs.ob.modules.repair.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.repair.entity.RepairRecord;
import com.hsobs.ob.modules.repair.service.RepairRecordService;

/**
 * 维修组织记录表Controller
 * <AUTHOR>
 * @version 2025-03-16
 */
@Controller
@RequestMapping(value = "${adminPath}/repair/record")
public class RepairRecordController extends BaseController {

	@Autowired
	private RepairRecordService repairRecordService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public RepairRecord get(String id, boolean isNewRecord) {
		return repairRecordService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("repair:record:view")
	@RequestMapping(value = {"list", ""})
	public String list(RepairRecord repairRecord, Model model) {
		model.addAttribute("repairRecord", repairRecord);
		return "modules/repair/repairRecordList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("repair:record:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<RepairRecord> listData(RepairRecord repairRecord, HttpServletRequest request, HttpServletResponse response) {
		repairRecord.setPage(new Page<>(request, response));
		Page<RepairRecord> page = repairRecordService.findPage(repairRecord);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("repair:record:view")
	@RequestMapping(value = "form")
	public String form(RepairRecord repairRecord, Model model) {
		model.addAttribute("repairRecord", repairRecord);
		return "modules/repair/repairRecordForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("repair:record:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated RepairRecord repairRecord) {
		repairRecordService.save(repairRecord);
		return renderResult(Global.TRUE, text("保存维修组织记录表成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("repair:record:view")
	@RequestMapping(value = "exportData")
	public void exportData(RepairRecord repairRecord, HttpServletResponse response) {
		List<RepairRecord> list = repairRecordService.findList(repairRecord);
		String fileName = "维修组织记录表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("维修组织记录表", RepairRecord.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("repair:record:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		RepairRecord repairRecord = new RepairRecord();
		List<RepairRecord> list = ListUtils.newArrayList(repairRecord);
		String fileName = "维修组织记录表模板.xlsx";
		try(ExcelExport ee = new ExcelExport("维修组织记录表", RepairRecord.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("repair:record:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = repairRecordService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("repair:record:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(RepairRecord repairRecord) {
		repairRecordService.delete(repairRecord);
		return renderResult(Global.TRUE, text("删除维修组织记录表成功！"));
	}
	
}