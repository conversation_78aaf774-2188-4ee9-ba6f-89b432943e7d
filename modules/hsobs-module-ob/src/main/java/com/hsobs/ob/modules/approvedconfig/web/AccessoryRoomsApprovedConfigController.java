package com.hsobs.ob.modules.approvedconfig.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.approvedconfig.entity.AccessoryRoomsApprovedConfig;
import com.hsobs.ob.modules.approvedconfig.service.AccessoryRoomsApprovedConfigService;

/**
 * 附属用房面积核定配置Controller
 * <AUTHOR>
 * @version 2025-03-09
 */
@Controller
@RequestMapping(value = "${adminPath}/approvedconfig/accessoryRoomsApprovedConfig")
public class AccessoryRoomsApprovedConfigController extends BaseController {

	@Autowired
	private AccessoryRoomsApprovedConfigService accessoryRoomsApprovedConfigService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public AccessoryRoomsApprovedConfig get(String id, boolean isNewRecord) {
		return accessoryRoomsApprovedConfigService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("approvedconfig:accessoryRoomsApprovedConfig:view")
	@RequestMapping(value = {"list", ""})
	public String list(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig, Model model) {
		model.addAttribute("accessoryRoomsApprovedConfig", accessoryRoomsApprovedConfig);
		return "modules/approvedconfig/accessoryRoomsApprovedConfigList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("approvedconfig:accessoryRoomsApprovedConfig:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<AccessoryRoomsApprovedConfig> listData(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig, HttpServletRequest request, HttpServletResponse response) {
		accessoryRoomsApprovedConfig.setPage(new Page<>(request, response));
		Page<AccessoryRoomsApprovedConfig> page = accessoryRoomsApprovedConfigService.findPage(accessoryRoomsApprovedConfig);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("approvedconfig:accessoryRoomsApprovedConfig:view")
	@RequestMapping(value = "form")
	public String form(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig, Model model) {
		long count = accessoryRoomsApprovedConfigService.findCount(accessoryRoomsApprovedConfig);
		if (count == 0) {
			accessoryRoomsApprovedConfigService.initSave();
		}
		accessoryRoomsApprovedConfig = accessoryRoomsApprovedConfigService.get(accessoryRoomsApprovedConfig.getId());
		model.addAttribute("accessoryRoomsApprovedConfig", accessoryRoomsApprovedConfig);
		return "modules/approvedconfig/accessoryRoomsApprovedConfigForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("approvedconfig:accessoryRoomsApprovedConfig:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig) {
		accessoryRoomsApprovedConfigService.save(accessoryRoomsApprovedConfig);
		return renderResult(Global.TRUE, text("保存附属用房面积核定配置成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("approvedconfig:accessoryRoomsApprovedConfig:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig) {
		accessoryRoomsApprovedConfigService.delete(accessoryRoomsApprovedConfig);
		return renderResult(Global.TRUE, text("删除附属用房面积核定配置成功！"));
	}
	
}