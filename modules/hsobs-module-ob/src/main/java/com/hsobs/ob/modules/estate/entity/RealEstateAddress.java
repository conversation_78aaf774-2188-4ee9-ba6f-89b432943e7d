package com.hsobs.ob.modules.estate.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.Area;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

/**
 * 不动产地址表Entity
 * <AUTHOR>
 * @version 2025-01-05
 */
@Table(name="ob_real_estate_address", alias="a", label="不动产地址表信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="name", attrName="name", label="地址名称", queryType=QueryType.LIKE),
		@Column(name="region", attrName="region", label="行政区划"),
		@Column(name="address", attrName="address", label="详细地址"),
		@Column(name="owner_office_code", attrName="ownerOfficeCode", label="产权机构ID"),
		@Column(name="owner_user_code", attrName="ownerUserCode", label="产权人ID"),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用机构ID"),
		@Column(name="used_user_code", attrName="usedUserCode", label="使用人ID"),
		@Column(name="latitude", attrName="latitude", label="纬度", isUpdateForce=true),
		@Column(name="longitude", attrName="longitude", label="经度", isUpdateForce=true),
		@Column(name="floor_count", attrName="floorCount", label="层数", isUpdateForce=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="修改人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="修改时间", isQuery=false),
		@Column(name="land_area", attrName="landArea", label="用地面积"),
		@Column(name="building_occupation_area", attrName="buildingOccupationArea", label="建筑占地面积"),
		@Column(name="building_area", attrName="buildingArea", label="建筑面积"),
		@Column(name="structure", attrName="structure", label="结构"),
		@Column(name="completion_time", attrName="completionTime", label="竣工时间", isUpdateForce=true),
		@Column(name="commencement_date", attrName="commencementDate", label="开工时间", isUpdateForce=true),
		@Column(name="final_settlement_date", attrName="finalSettlementDate", label="决算时间", isUpdateForce=true),
		@Column(name="design_unit", attrName="designUnit", label="设计单位"),
		@Column(name="construction_unit", attrName="constructionUnit", label="施工单位"),
		@Column(name="land_certificate_number", attrName="landCertificateNumber", label="土地证号"),
		@Column(name="property_ownership_certificate_number", attrName="propertyOwnershipCertificateNumber", label="房屋所有权证号"),
		@Column(name="real_estate_certificate_number", attrName="realEstateCertificateNumber", label="不动产权证号"),
		@Column(name="original_value", attrName="originalValue", label="原值"),
		@Column(name="not_obtaining_certificate_reason", attrName="notObtainingCertificateReason", label="未获得证书原因"),
}, joinTable = {
		@JoinTable(
				type = JoinTable.Type.LEFT_JOIN,
				entity = Area.class,
				attrName = "this",
				alias = "o",
				on = "a.region = o.area_code",
				columns = {
						@Column(name = "tree_names", attrName = "areaTreeNames", label = "全节点名")
				}
		),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o1",
				attrName = "ownerOffice",
				on = "a.owner_office_code = o1.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "usedOffice",
				on = "a.used_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u1",
				attrName = "ownerUser",
				on = "a.owner_user_code = u1.user_code",
				columns = {@Column(includeEntity = User.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u2",
				attrName = "usedUser",
				on = "a.used_user_code = u2.user_code",
				columns = {@Column(includeEntity = User.class)})
		},
		extWhereKeys="dsf",
		orderBy="a.update_date DESC"
)
public class RealEstateAddress extends DataEntity<RealEstateAddress> {

	private static final long serialVersionUID = 1L;
	private String name;		// 地址名称
	private String region;		// 行政区划
	private String areaTreeNames;		// 区县
	private String address;		// 详细地址
	private String ownerOfficeCode;
	private String ownerUserCode;
	private String usedOfficeCode;
	private String usedUserCode;
	private Double latitude;		// 纬度
	private Double longitude;		// 经度
	private Integer floorCount;		// 层数
	private String landArea;		// 用地面积
	private String buildingOccupationArea;		// 建筑占地面积
	private String buildingArea;		// 建筑面积
	private String structure;		// 结构
	private Date completionTime;		// 竣工时间
	private Date commencementDate;		// 竣工时间
	private Date finalSettlementDate;		// 竣工时间
	private String designUnit;		// 设计单位
	private String constructionUnit;		// 施工单位
	private String landCertificateNumber;		// 土地证号
	private String propertyOwnershipCertificateNumber;		// 房屋所有权证号
	private String realEstateCertificateNumber;		// 不动产权证号
	private String originalValue;		// 原值
	private String notObtainingCertificateReason;		// 未获得证书原因

	private List<RealEstateAddressFloor> realEstateAddressFloorList = ListUtils.newArrayList();

	private Office ownerOffice;
	private User ownerUser;
	private Office usedOffice;
	private User usedUser;

	public RealEstateAddress() {
		this(null);
	}

	public RealEstateAddress(String id){
		super(id);
	}

	@NotBlank(message="地址名称不能为空")
	@Size(min=0, max=1000, message="地址名称长度不能超过 1000 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@NotBlank(message="详细地址不能为空")
	@Size(min=0, max=1000, message="详细地址长度不能超过 1000 个字符")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}

	public Integer getFloorCount() {
		return floorCount;
	}

	public void setFloorCount(Integer floorCount) {
		this.floorCount = floorCount;
	}

	@Size(min=0, max=100, message="用地面积长度不能超过 100 个字符")
	public String getLandArea() {
		return landArea;
	}

	public void setLandArea(String landArea) {
		this.landArea = landArea;
	}

	@Size(min=0, max=100, message="建筑占地面积长度不能超过 100 个字符")
	public String getBuildingOccupationArea() {
		return buildingOccupationArea;
	}

	public void setBuildingOccupationArea(String buildingOccupationArea) {
		this.buildingOccupationArea = buildingOccupationArea;
	}

	@Size(min=0, max=100, message="建筑面积长度不能超过 100 个字符")
	public String getBuildingArea() {
		return buildingArea;
	}

	public void setBuildingArea(String buildingArea) {
		this.buildingArea = buildingArea;
	}

	@Size(min=0, max=100, message="结构长度不能超过 100 个字符")
	public String getStructure() {
		return structure;
	}

	public void setStructure(String structure) {
		this.structure = structure;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getCompletionTime() {
		return completionTime;
	}

	public void setCompletionTime(Date completionTime) {
		this.completionTime = completionTime;
	}

	@Size(min=0, max=1000, message="设计单位长度不能超过 1000 个字符")
	public String getDesignUnit() {
		return designUnit;
	}

	public void setDesignUnit(String designUnit) {
		this.designUnit = designUnit;
	}

	@Size(min=0, max=1000, message="施工单位长度不能超过 1000 个字符")
	public String getConstructionUnit() {
		return constructionUnit;
	}

	public void setConstructionUnit(String constructionUnit) {
		this.constructionUnit = constructionUnit;
	}

	@Size(min=0, max=100, message="土地证号长度不能超过 100 个字符")
	public String getLandCertificateNumber() {
		return landCertificateNumber;
	}

	public void setLandCertificateNumber(String landCertificateNumber) {
		this.landCertificateNumber = landCertificateNumber;
	}

	@Size(min=0, max=100, message="房屋所有权证号长度不能超过 100 个字符")
	public String getPropertyOwnershipCertificateNumber() {
		return propertyOwnershipCertificateNumber;
	}

	public void setPropertyOwnershipCertificateNumber(String propertyOwnershipCertificateNumber) {
		this.propertyOwnershipCertificateNumber = propertyOwnershipCertificateNumber;
	}

	@Size(min=0, max=100, message="不动产权证号长度不能超过 100 个字符")
	public String getRealEstateCertificateNumber() {
		return realEstateCertificateNumber;
	}

	public void setRealEstateCertificateNumber(String realEstateCertificateNumber) {
		this.realEstateCertificateNumber = realEstateCertificateNumber;
	}

	@Size(min=0, max=100, message="原值长度不能超过 100 个字符")
	public String getOriginalValue() {
		return originalValue;
	}

	public void setOriginalValue(String originalValue) {
		this.originalValue = originalValue;
	}

	@Size(min=0, max=2000, message="未获得证书原因长度不能超过 2000 个字符")
	public String getNotObtainingCertificateReason() {
		return notObtainingCertificateReason;
	}

	public void setNotObtainingCertificateReason(String notObtainingCertificateReason) {
		this.notObtainingCertificateReason = notObtainingCertificateReason;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getAreaTreeNames() {
		return areaTreeNames;
	}

	public void setAreaTreeNames(String areaTreeNames) {
		this.areaTreeNames = areaTreeNames;
	}

	public String getOwnerOfficeCode() {
		return ownerOfficeCode;
	}

	public void setOwnerOfficeCode(String ownerOfficeCode) {
		this.ownerOfficeCode = ownerOfficeCode;
	}

	public String getOwnerUserCode() {
		return ownerUserCode;
	}

	public void setOwnerUserCode(String ownerUserCode) {
		this.ownerUserCode = ownerUserCode;
	}

	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}

	public String getUsedUserCode() {
		return usedUserCode;
	}

	public void setUsedUserCode(String usedUserCode) {
		this.usedUserCode = usedUserCode;
	}

	public Office getOwnerOffice() {
		return ownerOffice;
	}

	public void setOwnerOffice(Office ownerOffice) {
		this.ownerOffice = ownerOffice;
	}

	public User getOwnerUser() {
		return ownerUser;
	}

	public void setOwnerUser(User ownerUser) {
		this.ownerUser = ownerUser;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}

	public User getUsedUser() {
		return usedUser;
	}

	public void setUsedUser(User usedUser) {
		this.usedUser = usedUser;
	}

	public List<RealEstateAddressFloor> getRealEstateAddressFloorList() {
		return realEstateAddressFloorList;
	}

	public void setRealEstateAddressFloorList(List<RealEstateAddressFloor> realEstateAddressFloorList) {
		this.realEstateAddressFloorList = realEstateAddressFloorList;
	}

	public Date getCommencementDate() {
		return commencementDate;
	}

	public void setCommencementDate(Date commencementDate) {
		this.commencementDate = commencementDate;
	}

	public Date getFinalSettlementDate() {
		return finalSettlementDate;
	}

	public void setFinalSettlementDate(Date finalSettlementDate) {
		this.finalSettlementDate = finalSettlementDate;
	}
}