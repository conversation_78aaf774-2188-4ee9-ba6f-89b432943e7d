package com.hsobs.ob.modules.vacated.dao;

import com.hsobs.ob.modules.vacated.entity.VacatedLedger;
import com.hsobs.ob.modules.vacated.entity.VacatedQuery;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.ob.modules.vacated.entity.Vacated;

import java.util.List;

/**
 * 清理腾退DAO接口
 * <AUTHOR>
 * @version 2025-02-03
 */
@MyBatisDao
public interface VacatedDao extends CrudDao<Vacated> {

    List<VacatedLedger> listLedger(VacatedLedger vacatedLedger);

    List<VacatedQuery> listQueryData(VacatedQuery vacatedQuery);
}