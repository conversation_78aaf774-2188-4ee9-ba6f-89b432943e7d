package com.hsobs.ob.modules.earlywarnmessage.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.hsobs.ob.modules.earlywarnmessage.entity.RoomAreaExcessRecord;
import com.hsobs.ob.modules.earlywarnmessage.entity.UnitAreaExcessRecord;
import com.hsobs.ob.modules.earlywarnset.entity.EarlyWarnSet;
import com.hsobs.ob.modules.earlywarnset.service.EarlyWarnSetService;
import com.hsobs.ob.modules.estate.dao.RealEstateDao;
import com.hsobs.ob.modules.estate.entity.RealEstateQuery;
import com.hsobs.ob.modules.ownership.entity.OwnershipRegistration;
import com.hsobs.ob.modules.ownership.entity.OwnershipRegistrationQuery;
import com.hsobs.ob.modules.ownership.service.OwnershipRegistrationService;
import com.jeesite.common.beetl.ext.fn.DictUtil;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.modules.sys.entity.DictData;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.DictUtils;
import com.jeesite.modules.sys.utils.EmpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.earlywarnmessage.entity.EarlyWarnMessage;
import com.hsobs.ob.modules.earlywarnmessage.dao.EarlyWarnMessageDao;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 预警消息Service
 * <AUTHOR>
 * @version 2024-12-02
 */
@Service
public class EarlyWarnMessageService extends CrudService<EarlyWarnMessageDao, EarlyWarnMessage> {

	@Autowired
	private EarlyWarnSetService earlyWarnSetService;

	@Autowired
	private OwnershipRegistrationService ownershipRegistrationService;

	@Autowired
	EarlyWarnMessageDao earlyWarnMessageDao;
	@Autowired
	private EmpUserService empUserService;
	@Autowired
	private RealEstateDao realEstateDao;
    @Autowired
    private ApiSzkjService apiSzkjService;

	/**
	 * 获取单条数据
	 * @param earlyWarnMessage
	 * @return
	 */
	@Override
	public EarlyWarnMessage get(EarlyWarnMessage earlyWarnMessage) {
		return super.get(earlyWarnMessage);
	}
	
	/**
	 * 查询分页数据
	 * @param earlyWarnMessage 查询条件
	 * @param earlyWarnMessage page 分页对象
	 * @return
	 */
	@Override
	public Page<EarlyWarnMessage> findPage(EarlyWarnMessage earlyWarnMessage) {
		return super.findPage(earlyWarnMessage);
	}
	
	/**
	 * 查询列表数据
	 * @param earlyWarnMessage
	 * @return
	 */
	@Override
	public List<EarlyWarnMessage> findList(EarlyWarnMessage earlyWarnMessage) {
		return super.findList(earlyWarnMessage);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param earlyWarnMessage
	 */
	@Override
	@Transactional
	public void save(EarlyWarnMessage earlyWarnMessage) {
		super.save(earlyWarnMessage);
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<EarlyWarnMessage> list = ei.getDataList(EarlyWarnMessage.class);
			for (EarlyWarnMessage earlyWarnMessage : list) {
				try{
					ValidatorUtils.validateWithException(earlyWarnMessage);
					this.save(earlyWarnMessage);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + earlyWarnMessage.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + earlyWarnMessage.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
	/**
	 * 更新状态
	 * @param earlyWarnMessage
	 */
	@Override
	@Transactional
	public void updateStatus(EarlyWarnMessage earlyWarnMessage) {
		super.updateStatus(earlyWarnMessage);
	}
	
	/**
	 * 删除数据
	 * @param earlyWarnMessage
	 */
	@Override
	@Transactional
	public void delete(EarlyWarnMessage earlyWarnMessage) {
		super.delete(earlyWarnMessage);
	}

	/**
	 * 添加数据权限过滤条件
	 */
	@Override
	public void addDataScopeFilter(EarlyWarnMessage earlyWarnMessage){
		SqlMap sqlMap = earlyWarnMessage.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		// 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"a.office_occupancy_unit_id",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}

	/**
	 * 单位面积超标预警定时器（对象为单位）
	 */
	public void executeUnitExcessiveAreaTask(){
		EarlyWarnSet earlyWarnSet = new EarlyWarnSet();
		earlyWarnSet.setEarlyWarnType("0");
		List<EarlyWarnSet> earlyWarnSetlist = earlyWarnSetService.findList(earlyWarnSet);
		EarlyWarnSet earlyWarnSet1 = earlyWarnSetlist.get(0);
		if(earlyWarnSet1.getStatus().equals("1")){
			return;
		}
		if(earlyWarnSet1.getEarlyTarget().equals("0")){
			return;
		}
		UnitAreaExcessRecord unitAreaExcessRecord = new UnitAreaExcessRecord();
		List<UnitAreaExcessRecord> list = earlyWarnMessageDao.unitAreaExcessRecordListData(unitAreaExcessRecord);

		for (int j = 0; j < list.size(); j++) {
			UnitAreaExcessRecord unitAreaExcessRecordMap = list.get(j);
			//判断使用人面积是否大于标准面积
			if (unitAreaExcessRecordMap.getExcessArea() > 0) {
				EarlyWarnMessage earlyWarnMessage = new EarlyWarnMessage();
				earlyWarnMessage.setEarlyWarnType("0");
				earlyWarnMessage.setOccupancyClassification(unitAreaExcessRecordMap.getOfficeRoomType());
				earlyWarnMessage.setOfficeOccupancyUnitId(unitAreaExcessRecordMap.getOfficeCode());
				earlyWarnMessage.setAllocationArea(unitAreaExcessRecordMap.getActualArea());
				earlyWarnMessage.setWarningDate(new Date());
				earlyWarnMessage.setEarlyWarnState("0");
				earlyWarnMessage.setPushTag("0");
				earlyWarnMessage.setEarlyWarnMessageInfo("单位面积超标，超出面积：" + unitAreaExcessRecordMap.getExcessArea()+"m²");
				earlyWarnMessage.setSuperstandardArea(unitAreaExcessRecordMap.getExcessArea());
				this.save(earlyWarnMessage);
			}
		}

	}

	/**
	 * 房屋面积超标预警定时器（对象为单位）
	 */
	public void executeHouseExcessiveAreaTask(){
		EarlyWarnSet earlyWarnSet = new EarlyWarnSet();
		earlyWarnSet.setEarlyWarnType("1");
		List<EarlyWarnSet> earlyWarnSetlist = earlyWarnSetService.findList(earlyWarnSet);
		EarlyWarnSet earlyWarnSet1 = earlyWarnSetlist.get(0);
		if(earlyWarnSet1.getStatus().equals("1")){
			return;
		}
		if(earlyWarnSet1.getEarlyTarget().equals("0")){
			return;
		}
		RoomAreaExcessRecord roomAreaExcessRecord = new RoomAreaExcessRecord();
		List<RoomAreaExcessRecord> list = earlyWarnMessageDao.roomAreaExcessRecordListData(roomAreaExcessRecord);

		for (int i = 0; i < list.size(); i++) {
			RoomAreaExcessRecord roomAreaExcessRecordMap = list.get(i);

			//判断使用人面积是否大于标准面积
			if (roomAreaExcessRecordMap.getExcessArea() > 0) {
				EarlyWarnMessage earlyWarnMessage = new EarlyWarnMessage();
				earlyWarnMessage.setEarlyWarnType("1");
				earlyWarnMessage.setOccupancyClassification("0");
				earlyWarnMessage.setOfficeOccupancyUnitId(roomAreaExcessRecordMap.getOfficeCode());
				earlyWarnMessage.setAllocationArea(roomAreaExcessRecordMap.getApprovedArea());
				earlyWarnMessage.setWarningDate(new Date());
				earlyWarnMessage.setEarlyWarnState("0");
				earlyWarnMessage.setPushTag("0");
				earlyWarnMessage.setUserId(roomAreaExcessRecordMap.getUserCode());
				earlyWarnMessage.setPositionId(roomAreaExcessRecordMap.getRank());
				earlyWarnMessage.setEarlyWarnMessageInfo("房屋面积超标，超出面积：" + roomAreaExcessRecordMap.getExcessArea()+"m²");
				earlyWarnMessage.setSuperstandardArea(roomAreaExcessRecordMap.getExcessArea());
				this.save(earlyWarnMessage);
			}
		}
	}

	/**
	 * 闲置预警定时器（对象为房间）
	 */
	public void executeLeaveTask(){
		EarlyWarnSet earlyWarnSet = new EarlyWarnSet();
		earlyWarnSet.setEarlyWarnType("2");
		List<EarlyWarnSet> earlyWarnSetlist = earlyWarnSetService.findList(earlyWarnSet);
		EarlyWarnSet earlyWarnSet1 = earlyWarnSetlist.get(0);
		if(earlyWarnSet1.getStatus().equals("1")){
			return;
		}
		if(earlyWarnSet1.getEarlyTarget().equals("0")){
			return;
		}
		RealEstateQuery realEstateQuery = new RealEstateQuery();
		realEstateQuery.setUseTag("1");
		List<RealEstateQuery> list = realEstateDao.listQueryData(realEstateQuery);
		for (int i = 0; i < list.size(); i++) {
			RealEstateQuery realEstateQueryMap = list.get(i);
			EarlyWarnMessage earlyWarnMessage = new EarlyWarnMessage();
			earlyWarnMessage.setEarlyWarnType("2");
			earlyWarnMessage.setOccupancyClassification(realEstateQueryMap.getOfficeRoomCategory());
			earlyWarnMessage.setOfficeOccupancyUnitId(realEstateQueryMap.getOfficeCode());
			earlyWarnMessage.setWarningDate(new Date());
			earlyWarnMessage.setEarlyWarnState("0");
			earlyWarnMessage.setPushTag("0");
			earlyWarnMessage.setEarlyWarnMessageInfo("房间闲置，闲置面积：" + realEstateQueryMap.getArea()+"m²");
			this.save(earlyWarnMessage);
		}
	}

	public Page<RoomAreaExcessRecord> roomAreaExcessRecordListData(RoomAreaExcessRecord roomAreaExcessRecord) {
		Page<RoomAreaExcessRecord> page = (Page<RoomAreaExcessRecord>) roomAreaExcessRecord.getPage();
		List<RoomAreaExcessRecord> list = earlyWarnMessageDao.roomAreaExcessRecordListData(roomAreaExcessRecord);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<RoomAreaExcessRecord> exportRoomAreaExcessRecordData(RoomAreaExcessRecord roomAreaExcessRecord) {
		return earlyWarnMessageDao.roomAreaExcessRecordListData(roomAreaExcessRecord);
	}

	public Page<UnitAreaExcessRecord> unitAreaExcessRecordListData(UnitAreaExcessRecord unitAreaExcessRecord) {
		Page<UnitAreaExcessRecord> page = (Page<UnitAreaExcessRecord>) unitAreaExcessRecord.getPage();
		List<UnitAreaExcessRecord> list = earlyWarnMessageDao.unitAreaExcessRecordListData(unitAreaExcessRecord);
		page.setList(list);
		return page;
	}

	public List<UnitAreaExcessRecord> exportUnitAreaExcessRecordData(UnitAreaExcessRecord unitAreaExcessRecord) {
		return earlyWarnMessageDao.unitAreaExcessRecordListData(unitAreaExcessRecord);
	}

	public void sendEarlyWarnMessageToOA(EarlyWarnMessage earlyWarnMessage) {

		if(earlyWarnMessage.getPushTag() != null && earlyWarnMessage.getPushTag().equals("1")) {
			throw new ServiceException("已经发布过预警信息，不能重复发布");
		}

		String dictLabel = DictUtils.getDictLabel("early_warn_type", earlyWarnMessage.getEarlyWarnType(), "未知");

		Office office = EmpUtils.getOffice();
		String officeCode = office.getExtAspId();
		if(officeCode == null || "".equals(officeCode)) {
			officeCode = office.getOfficeCode();
		}

		Api2NoticeBody body = new Api2NoticeBody();
		body.setMsgId(earlyWarnMessage.getId());
		body.setMsgType("3");		// 公告
		body.setMsgSource("智能预警");
		body.setTopic(dictLabel);
		body.setContent(earlyWarnMessage.getEarlyWarnMessageInfo());
		body.setFileIds("");
		body.setPublishUnit(office.getOfficeName());
		body.setPublishUnitId(officeCode);
		body.setPublishTime(earlyWarnMessage.getCreateDate());
		if(earlyWarnMessage.getUserId() != null) {

			EmpUser empUser = new EmpUser();
			empUser.setUserCode(earlyWarnMessage.getUserId());
			empUser = empUserService.get(empUser);
			String receiveUserIds = empUser.getExtAspId();
			if(receiveUserIds == null || "".equals(receiveUserIds)) {
				receiveUserIds = empUser.getLoginCode();
			}

			body.setReceiveUserIds(receiveUserIds);
		}else{
			office = earlyWarnMessage.getOffice();
			officeCode = office.getExtAspId();
			if(officeCode == null || "".equals(officeCode)) {
				officeCode = office.getOfficeCode();
			}

			body.setReceiveOrgIds(officeCode);
		}

		Api2ResponseBody responseBody = apiSzkjService.uploadNotice(body);
		if(responseBody.getCode() != 1) {
			throw new ServiceException(responseBody.getMessage());
		}
		earlyWarnMessage.setPushTag("1");
		super.save(earlyWarnMessage);
	}
}