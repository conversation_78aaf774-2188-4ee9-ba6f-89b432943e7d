package com.hsobs.ob.modules.vacated.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.vacated.entity.VacatedAsset;
import com.hsobs.ob.modules.vacated.dao.VacatedAssetDao;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * ob_vacated_assetService
 * <AUTHOR>
 * @version 2025-04-08
 */
@Service
public class VacatedAssetService extends CrudService<VacatedAssetDao, VacatedAsset> {

    /**
     * 获取单条数据
     * @param vacatedAsset
     * @return
     */
    @Override
    public VacatedAsset get(VacatedAsset vacatedAsset) {
        return super.get(vacatedAsset);
    }

    /**
     * 查询分页数据
     * @param vacatedAsset 查询条件
     * @param vacatedAsset page 分页对象
     * @return
     */
    @Override
    public Page<VacatedAsset> findPage(VacatedAsset vacatedAsset) {
        return super.findPage(vacatedAsset);
    }

    /**
     * 查询列表数据
     * @param vacatedAsset
     * @return
     */
    @Override
    public List<VacatedAsset> findList(VacatedAsset vacatedAsset) {
        return super.findList(vacatedAsset);
    }

    /**
     * 保存数据（插入或更新）
     * @param vacatedAsset
     */
    @Override
    @Transactional
    public void save(VacatedAsset vacatedAsset) {
        super.save(vacatedAsset);
    }

    /**
     * 导入数据
     * @param file 导入的数据文件
     */
    @Transactional
    public String importData(MultipartFile file, String vacatedId) {
        if (file == null){
            throw new ServiceException(text("请选择导入的数据文件！"));
        }
        int successNum = 0; int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        try(ExcelImport ei = new ExcelImport(file, 2, 0)){
            List<VacatedAsset> list = ei.getDataList(VacatedAsset.class);
            for (VacatedAsset vacatedAsset : list) {
                try{
                    ValidatorUtils.validateWithException(vacatedAsset);
                    vacatedAsset.setVacatedId(vacatedId);
                    this.save(vacatedAsset);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、编号 " + vacatedAsset.getId() + " 导入成功");
                } catch (Exception e) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、编号 " + vacatedAsset.getId() + " 导入失败：";
                    if (e instanceof ConstraintViolationException){
                        ConstraintViolationException cve = (ConstraintViolationException)e;
                        for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
                            msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
                        }
                    }else{
                        msg += e.getMessage();
                    }
                    failureMsg.append(msg);
                    logger.error(msg, e);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            failureMsg.append(e.getMessage());
            return failureMsg.toString();
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else{
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 更新状态
     * @param vacatedAsset
     */
    @Override
    @Transactional
    public void updateStatus(VacatedAsset vacatedAsset) {
        super.updateStatus(vacatedAsset);
    }

    /**
     * 删除数据
     * @param vacatedAsset
     */
    @Override
    @Transactional
    public void delete(VacatedAsset vacatedAsset) {
        super.delete(vacatedAsset);
    }

}