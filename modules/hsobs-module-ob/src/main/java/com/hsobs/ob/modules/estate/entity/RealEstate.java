package com.hsobs.ob.modules.estate.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

import java.util.Date;
import java.util.List;

/**
 * 不动产信息表Entity
 * <AUTHOR>
 * @version 2024-12-08
 */
@Table(name="ob_real_estate", alias="a", label="不动产信息表信息",
		columns={
			@Column(name="id", attrName="id", label="主键", isPK=true),
			@Column(name="name", attrName="name", label="房间名称", queryType=QueryType.LIKE),
			@Column(name="type", attrName="type", label="房间类型", isUpdateForce=true),
			@Column(name="puroise", attrName="puroise", label="房间用途", isUpdateForce=true),
			@Column(name="real_estate_address_id", attrName="realEstateAddressId", label="地址ID"),
			@Column(name="owner_office_code", attrName="ownerOfficeCode", label="产权机构ID"),
			@Column(name="owner_user_code", attrName="ownerUserCode", label="产权人ID"),
			@Column(name="used_office_code", attrName="usedOfficeCode", label="使用机构ID"),
			@Column(name="used_user_code", attrName="usedUserCode", label="使用人ID"),
			@Column(name="area", attrName="area", label="面积", isUpdateForce=true),
			@Column(name="cad_file_id", attrName="cadFileId", label="CAD 文件ID", isUpdateForce=true),
			@Column(name="lease", attrName="lease", label="是否租借", isUpdateForce=true),
			@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
			@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
			@Column(name="update_by", attrName="updateBy", label="修改人", isQuery=false),
			@Column(name="update_date", attrName="updateDate", label="修改时间", isQuery=false),
			@Column(name="remarks", attrName="remarks", label="备注", isQuery=false),
			@Column(name="office_type", attrName="officeType", label="办公室类型", isQuery=false),
			@Column(name="serial_number", attrName="serialNumber", label="房间编号", queryType=QueryType.LIKE),
			@Column(name="real_estate_address_floor_id", attrName="realEstateAddressFloorId", label="楼层ID", isQuery=true),
			@Column(name="apply_area", attrName="applyArea", label="使用面积"),
			@Column(name="apply_date", attrName="applyDate", label="使用时间"),
			@Column(name="room_number", attrName="roomNumber", label="房间号"),
			@Column(name="idleness_reason", attrName="idlenessReason", label="闲置原因"),
		},
		joinTable = {
			@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddress.class, alias="rea",
					on="rea.id = a.real_estate_address_id",
					columns={@Column(includeEntity=RealEstateAddress.class)}),
			@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o1",
					attrName = "ownerOffice",
					on = "a.owner_office_code = o1.office_code",
					columns = {@Column(includeEntity = Office.class)}),
			@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
					attrName = "usedOffice",
					on = "a.used_office_code = o2.office_code",
					columns = {@Column(includeEntity = Office.class)}),
			@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u1",
					attrName = "ownerUser",
					on = "a.owner_user_code = u1.user_code",
					columns = {@Column(includeEntity = User.class)}),
			@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u2",
					attrName = "usedUser",
					on = "a.used_user_code = u2.user_code",
					columns = {@Column(includeEntity = User.class)}),
			@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = RealEstateAddressFloor.class, alias = "reaf",
					attrName = "realEstateAddressFloor",
					on = "a.real_estate_address_floor_id = reaf.id",
					columns = {@Column(includeEntity = RealEstateAddressFloor.class)})
		},
		extWhereKeys="dsf",
		orderBy="a.update_date DESC"
)
public class RealEstate extends DataEntity<RealEstate> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 不动产名称
	private Integer type;		// 不动产类型
	private Integer puroise;		// 不动产用途
	private String realEstateAddressId;		// 地址ID
	private Integer floorNumber;		// 所属楼层
	private Double area;		// 面积
	private String ownerOfficeCode;
	private String ownerUserCode;
	private String usedOfficeCode;
	private String usedUserCode;
	private String cadFileId;
	private Boolean lease;
	private String remarks;
	private String officeType; // 办公室类型
	private String serialNumber; // 房间编号
	private String realEstateAddressFloorId; // 楼层ID
	private Double applyArea; // 使用面积
	private Date applyDate; // 使用时间
	private String roomNumber; // 房间号
	private String idlenessReason; // 闲置原因


	private RealEstateAddressFloor realEstateAddressFloor;
	private RealEstateAddress realEstateAddress;
	private Office ownerOffice;
	private User ownerUser;
	private Office usedOffice;
	private User usedUser;

	private List<EmpUser> usedUserList;

	public RealEstate() {
		this(null);
	}
	
	public RealEstate(String id){
		super(id);
	}
	
	@Size(min=0, max=1000, message="不动产名称长度不能超过 1000 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getPuroise() {
		return puroise;
	}

	public void setPuroise(Integer puroise) {
		this.puroise = puroise;
	}
	
	@Size(min=0, max=64, message="地址ID长度不能超过 64 个字符")
	public String getRealEstateAddressId() {
		return realEstateAddressId;
	}

	public void setRealEstateAddressId(String realEstateAddressId) {
		this.realEstateAddressId = realEstateAddressId;
	}
	
	public Integer getFloorNumber() {
		return floorNumber;
	}

	public void setFloorNumber(Integer floorNumber) {
		this.floorNumber = floorNumber;
	}
	
	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}

	public String getCadFileId() {
		return cadFileId;
	}

	public void setCadFileId(String cadFileId) {
		this.cadFileId = cadFileId;
	}

	public Boolean getLease() {
		return lease;
	}

	public void setLease(Boolean lease) {
		this.lease = lease;
	}

	public RealEstateAddress getRealEstateAddress() {
		return realEstateAddress;
	}

	public void setRealEstateAddress(RealEstateAddress realEstateAddress) {
		this.realEstateAddress = realEstateAddress;
	}

	public String getOwnerOfficeCode() {
		return ownerOfficeCode;
	}

	public void setOwnerOfficeCode(String ownerOfficeCode) {
		this.ownerOfficeCode = ownerOfficeCode;
	}

	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}

	public String getOwnerUserCode() {
		return ownerUserCode;
	}

	public void setOwnerUserCode(String ownerUserCode) {
		this.ownerUserCode = ownerUserCode;
	}

	public String getUsedUserCode() {
		return usedUserCode;
	}

	public void setUsedUserCode(String usedUserCode) {
		this.usedUserCode = usedUserCode;
	}

	public Office getOwnerOffice() {
		return ownerOffice;
	}

	public void setOwnerOffice(Office ownerOffice) {
		this.ownerOffice = ownerOffice;
	}

	public User getOwnerUser() {
		return ownerUser;
	}

	public void setOwnerUser(User ownerUser) {
		this.ownerUser = ownerUser;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}

	public User getUsedUser() {
		return usedUser;
	}

	public void setUsedUser(User usedUser) {
		this.usedUser = usedUser;
	}

	@Override
	public String getRemarks() {
		return remarks;
	}

	@Override
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getOfficeType() {
		return officeType;
	}

	public void setOfficeType(String officeType) {
		this.officeType = officeType;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getRealEstateAddressFloorId() {
		return realEstateAddressFloorId;
	}

	public void setRealEstateAddressFloorId(String realEstateAddressFloorId) {
		this.realEstateAddressFloorId = realEstateAddressFloorId;
	}

	public List<EmpUser> getUsedUserList() {
		return usedUserList;
	}

	public void setUsedUserList(List<EmpUser> usedUserList) {
		this.usedUserList = usedUserList;
	}

	public RealEstateAddressFloor getRealEstateAddressFloor() {
		return realEstateAddressFloor;
	}

	public void setRealEstateAddressFloor(RealEstateAddressFloor realEstateAddressFloor) {
		this.realEstateAddressFloor = realEstateAddressFloor;
	}

	public Double getApplyArea() {
		return applyArea;
	}

	public void setApplyArea(Double applyArea) {
		this.applyArea = applyArea;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public String getRoomNumber() {
		return roomNumber;
	}

	public void setRoomNumber(String roomNumber) {
		this.roomNumber = roomNumber;
	}

	public String getIdlenessReason() {
		return idlenessReason;
	}

	public void setIdlenessReason(String idlenessReason) {
		this.idlenessReason = idlenessReason;
	}
}

