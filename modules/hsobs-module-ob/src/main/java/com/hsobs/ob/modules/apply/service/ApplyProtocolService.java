package com.hsobs.ob.modules.apply.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.apply.entity.ApplyProtocol;
import com.hsobs.ob.modules.apply.dao.ApplyProtocolDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 有偿使用协议表Service
 * <AUTHOR>
 * @version 2025-03-13
 */
@Service
public class ApplyProtocolService extends CrudService<ApplyProtocolDao, ApplyProtocol> {
	
	/**
	 * 获取单条数据
	 * @param applyProtocol
	 * @return
	 */
	@Override
	public ApplyProtocol get(ApplyProtocol applyProtocol) {
		return super.get(applyProtocol);
	}
	
	/**
	 * 查询分页数据
	 * @param applyProtocol 查询条件
	 * @param applyProtocol page 分页对象
	 * @return
	 */
	@Override
	public Page<ApplyProtocol> findPage(ApplyProtocol applyProtocol) {
		return super.findPage(applyProtocol);
	}
	
	/**
	 * 查询列表数据
	 * @param applyProtocol
	 * @return
	 */
	@Override
	public List<ApplyProtocol> findList(ApplyProtocol applyProtocol) {
		return super.findList(applyProtocol);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param applyProtocol
	 */
	@Override
	@Transactional
	public void save(ApplyProtocol applyProtocol) {
		super.save(applyProtocol);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(applyProtocol, applyProtocol.getId(), "applyProtocol_file");
	}
	
	/**
	 * 更新状态
	 * @param applyProtocol
	 */
	@Override
	@Transactional
	public void updateStatus(ApplyProtocol applyProtocol) {
		super.updateStatus(applyProtocol);
	}
	
	/**
	 * 删除数据
	 * @param applyProtocol
	 */
	@Override
	@Transactional
	public void delete(ApplyProtocol applyProtocol) {
		super.delete(applyProtocol);
	}
	
}