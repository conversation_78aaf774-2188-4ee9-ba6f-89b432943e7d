package com.hsobs.ob.modules.disposalutilizationmanagement.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 处置利用管理Entity
 * <AUTHOR>
 * @version 2024-11-26
 */
public class DisposalUtilizationManagementLedger extends BpmEntity<DisposalUtilizationManagementLedger> {

	private static final long serialVersionUID = 1L;
	private String applicantUnitId;		// 处置单位ID
	private String disposalType;		// 处置业务类型
	private String applicantUnitName;		// 处置单位
	private String disposalAddress;		// 处置建筑地点
	private String disposalSpace;		// 处置总建筑面积
	@ExcelFields({
			@ExcelField(title="处置单位", attrName="applicantUnitName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="处置业务类型", attrName="disposalType", dictType="disposal_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="处置建筑地点", attrName="disposalAddress", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="处置总建筑面积", attrName="disposalSpace", align= ExcelField.Align.CENTER, sort=50),
	})
	public String getApplicantUnitId() {
		return applicantUnitId;
	}

	public void setApplicantUnitId(String applicantUnitId) {
		this.applicantUnitId = applicantUnitId;
	}

	public String getDisposalType() {
		return disposalType;
	}

	public void setDisposalType(String disposalType) {
		this.disposalType = disposalType;
	}

	public String getApplicantUnitName() {
		return applicantUnitName;
	}

	public void setApplicantUnitName(String applicantUnitName) {
		this.applicantUnitName = applicantUnitName;
	}

	public String getDisposalAddress() {
		return disposalAddress;
	}

	public void setDisposalAddress(String disposalAddress) {
		this.disposalAddress = disposalAddress;
	}

	public String getDisposalSpace() {
		return disposalSpace;
	}

	public void setDisposalSpace(String disposalSpace) {
		this.disposalSpace = disposalSpace;
	}
}