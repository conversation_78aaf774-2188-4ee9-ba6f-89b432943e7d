package com.hsobs.ob.modules.datastatistics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: DisposeTable
 * @projectName base
 * @description: 全省办公用房处置利用情况表
 * @date 2024/12/249:39
 */
public class DisposeTable extends DataEntity<DisposeTable> {
    private String region;
    private String year;
    private String office;
    private String yearCount;    //统计年度
    private String areaName;    //区域
    private String officeName;    //处置单位
    private String disposeType;    //处置类型
    private Integer disposeNumber;    //处置数量
    private float disposeArea;    //处置面积

    @ExcelFields({
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="统计年度", attrName="yearCount", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=10),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="区域", attrName="areaName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=20),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="处置单位", attrName="officeName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=30),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="处置类型", attrName="disposeType", dictType="disposal_type", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=40),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="处置数量", attrName="disposeNumber", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=50),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="处置面积", attrName="disposeArea", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=60),
    })
    public String getDisposeType() {
        return disposeType;
    }

    public void setDisposeType(String disposeType) {
        this.disposeType = disposeType;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getOffice() {
        return office;
    }

    public void setOffice(String office) {
        this.office = office;
    }

    public String getYearCount() {
        return yearCount;
    }

    public void setYearCount(String yearCount) {
        this.yearCount = yearCount;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public Integer getDisposeNumber() {
        return disposeNumber;
    }

    public void setDisposeNumber(Integer disposeNumber) {
        this.disposeNumber = disposeNumber;
    }

    public float getDisposeArea() {
        return disposeArea;
    }

    public void setDisposeArea(float disposeArea) {
        this.disposeArea = disposeArea;
    }
}
