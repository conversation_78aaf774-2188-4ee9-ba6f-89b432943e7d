package com.hsobs.ob.modules.supervisehandlingtasks.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 监督督办任务Entity
 * <AUTHOR>
 * @version 2024-12-10
 */
public class SuperviseHandlingTasksReq extends BpmEntity<SuperviseHandlingTasksReq> {

	String sjhm;
	String dcr;
	String id;
	String dcjy;
	String dcqkms;
	List<SurveyImageData> dcqktpList;

	public String getSjhm() {
		return sjhm;
	}

	public void setSjhm(String sjhm) {
		this.sjhm = sjhm;
	}

	public String getDcr() {
		return dcr;
	}

	public void setDcr(String dcr) {
		this.dcr = dcr;
	}

	@Override
	public String getId() {
		return id;
	}

	@Override
	public void setId(String id) {
		this.id = id;
	}

	public String getDcjy() {
		return dcjy;
	}

	public void setDcjy(String dcjy) {
		this.dcjy = dcjy;
	}

	public String getDcqkms() {
		return dcqkms;
	}

	public void setDcqkms(String dcqkms) {
		this.dcqkms = dcqkms;
	}

	public List<SurveyImageData> getDcqktpList() {
		return dcqktpList;
	}

	public void setDcqktpList(List<SurveyImageData> dcqktpList) {
		this.dcqktpList = dcqktpList;
	}
}