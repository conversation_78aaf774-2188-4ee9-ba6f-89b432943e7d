package com.hsobs.ob.modules.apply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.apply.entity.ApplyBusinessHouse;
import com.hsobs.ob.modules.apply.service.ApplyBusinessHouseService;

/**
 * 技术业务用房申请表Controller
 * <AUTHOR>
 * @version 2025-03-13
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/businessHouse")
public class ApplyBusinessHouseController extends BaseController {

	@Autowired
	private ApplyBusinessHouseService applyBusinessHouseService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ApplyBusinessHouse get(String id, boolean isNewRecord) {
		return applyBusinessHouseService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("apply:businessHouse:view")
	@RequestMapping(value = {"list", ""})
	public String list(ApplyBusinessHouse applyBusinessHouse, Model model) {
		model.addAttribute("applyBusinessHouse", applyBusinessHouse);
		return "modules/apply/applyBusinessHouseList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("apply:businessHouse:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ApplyBusinessHouse> listData(ApplyBusinessHouse applyBusinessHouse, HttpServletRequest request, HttpServletResponse response) {
		applyBusinessHouse.setPage(new Page<>(request, response));
		Page<ApplyBusinessHouse> page = applyBusinessHouseService.findPage(applyBusinessHouse);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("apply:businessHouse:view")
	@RequestMapping(value = "form")
	public String form(ApplyBusinessHouse applyBusinessHouse, Model model) {
		boolean commonReadonly= null != applyBusinessHouse.getStatus() && !applyBusinessHouse.getStatus().equals("9");
		model.addAttribute("commonReadonly", commonReadonly);
		model.addAttribute("applyBusinessHouse", applyBusinessHouse);
		return "modules/apply/applyBusinessHouseForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("apply:businessHouse:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ApplyBusinessHouse applyBusinessHouse) {
		applyBusinessHouseService.save(applyBusinessHouse);
		return renderResult(Global.TRUE, text("保存技术业务用房申请成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("apply:businessHouse:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ApplyBusinessHouse applyBusinessHouse) {
		if (!ApplyBusinessHouse.STATUS_DRAFT.equals(applyBusinessHouse.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		applyBusinessHouseService.delete(applyBusinessHouse);
		return renderResult(Global.TRUE, text("删除技术业务用房申请成功！"));
	}
	
}