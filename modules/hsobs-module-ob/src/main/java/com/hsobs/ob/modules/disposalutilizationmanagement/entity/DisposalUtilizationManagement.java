package com.hsobs.ob.modules.disposalutilizationmanagement.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.modules.bpm.entity.BpmEntity;

/**
 * 处置利用管理Entity
 * <AUTHOR>
 * @version 2024-11-26
 */
@Table(name="ob_disposal_utilization_management", alias="a", label="处置利用管理信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="room_id", attrName="roomId", label="房间ID"),
		@Column(name="applicant_id", attrName="applicantId", label="申请人ID"),
		@Column(name="applicant_unit_id", attrName="applicantUnitId", label="申请单位ID"),
		@Column(name="reason_for_application", attrName="reasonForApplication", label="申请理由"),
		@Column(name="application_for_use", attrName="applicationForUse", label="申请用途"),
		@Column(name="application_date", attrName="applicationDate", label="申请日期"),
		@Column(name="disposal_type", attrName="disposalType", label="处置类型"),
		@Column(name="disposal_opinion", attrName="disposalOpinion", label="处置意见"),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Employee.class, alias = "o",
				on = "o.emp_code = a.applicant_id", attrName="employee",
				columns = {@Column(includeEntity = Employee.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = RealEstate.class, alias = "re",
				on="re.id = a.room_id", attrName="realEstate",
				columns = {@Column(includeEntity = RealEstate.class)}),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddress.class, alias="rea",
				on="rea.id = re.real_estate_address_id",
				columns={@Column(includeEntity=RealEstateAddress.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "oi",
				on="oi.office_code = a.applicant_unit_id", attrName="office",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = AuctionTransferDisposalBusiness.class, alias = "at",
				on="at.disposal_id = a.id", attrName="auctionTransferDisposalBusiness",
				columns = {@Column(includeEntity = AuctionTransferDisposalBusiness.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = DemolitionDisposalService.class, alias = "dd",
				on="dd.disposal_id = a.id", attrName="demolitionDisposalService",
				columns = {@Column(includeEntity = DemolitionDisposalService.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = LeaseDisposalBusiness.class, alias = "ld",
				on="ld.disposal_id = a.id", attrName="leaseDisposalBusiness",
				columns = {@Column(includeEntity = LeaseDisposalBusiness.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = UsageConversion.class, alias = "uc",
				on="uc.disposal_id = a.id", attrName="usageConversion",
				columns = {@Column(includeEntity = UsageConversion.class)})
	},
	extWhereKeys="dsfOffice",
	orderBy="a.update_date DESC"
)
public class DisposalUtilizationManagement extends BpmEntity<DisposalUtilizationManagement> {
	
	private static final long serialVersionUID = 1L;
	private String roomId;		// 房间ID
	private String applicantId;		// 申请人ID
	private String applicantName;		// 申请人
	private String applicantUnitId;		// 申请单位ID
	private String applicantUnitName;		// 申请单位
	private String reasonForApplication;		// 申请理由
	private String applicationForUse;		// 申请用途
	private Date applicationDate;		// 申请日期
	private String disposalType;		// 处置类型
	private String disposalOpinion;		// 处置意见

	private Employee employee; // 人员信息
	private Office office; // 机构信息
	private RealEstate realEstate; // 房间信息
	private RealEstateAddress realEstateAddress;
	private AuctionTransferDisposalBusiness auctionTransferDisposalBusiness; // 拍卖转让处置业务
	private DemolitionDisposalService demolitionDisposalService; // 拆除处置业务
	private LeaseDisposalBusiness leaseDisposalBusiness; // 出租处置业务
	private UsageConversion usageConversion; // 转换用途业务


	@ExcelFields({
			@ExcelField(title="编号", attrName="id", align=Align.CENTER, sort=10),
			@ExcelField(title="房间信息", attrName="realEstate.name", align=Align.CENTER, sort=30),
			@ExcelField(title="申请人", attrName="employee.empName", align=Align.CENTER, sort=40),
			@ExcelField(title="申请单位", attrName="office.officeName", align=Align.CENTER, sort=50),
			@ExcelField(title="申请理由", attrName="reasonForApplication", align=Align.CENTER, sort=70),
			@ExcelField(title="申请用途", attrName="applicationForUse", align=Align.CENTER, sort=80),
			@ExcelField(title="申请日期", attrName="applicationDate", align=Align.CENTER, sort=90, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="处置类型", attrName="disposalType", dictType="disposal_type", align=Align.CENTER, sort=100),
			@ExcelField(title="处置意见", attrName="disposalOpinion", align=Align.CENTER, sort=110),
			@ExcelField(title="资产移交类别", attrName="demolitionDisposalService.assetTransferClass", align=Align.CENTER, sort=110),
			@ExcelField(title="资产移交日期", attrName="demolitionDisposalService.collectionTime", align=Align.CENTER, sort=110),
			@ExcelField(title="状态", attrName="status", dictType="sys_search_status", align=Align.CENTER, sort=120),
			@ExcelField(title="创建者", attrName="createBy", align=Align.CENTER, sort=130),
			@ExcelField(title="创建时间", attrName="createDate", align=Align.CENTER, sort=140),
			@ExcelField(title="更新者", attrName="updateBy", align=Align.CENTER, sort=150),
			@ExcelField(title="更新时间", attrName="updateDate", align=Align.CENTER, sort=160),
			@ExcelField(title="备注", attrName="remarks", align=Align.CENTER, sort=170),
	})

	public DisposalUtilizationManagement() {
		this(null);
	}
	
	public DisposalUtilizationManagement(String id){
		super(id);
	}
	
	@NotBlank(message="房间ID不能为空")
	@Size(min=0, max=64, message="房间ID长度不能超过 64 个字符")
	public String getRoomId() {
		return roomId;
	}

	public void setRoomId(String roomId) {
		this.roomId = roomId;
	}
	
	@NotBlank(message="申请人ID不能为空")
	@Size(min=0, max=64, message="申请人ID长度不能超过 64 个字符")
	public String getApplicantId() {
		return applicantId;
	}

	public void setApplicantId(String applicantId) {
		this.applicantId = applicantId;
	}
	
	@NotBlank(message="申请单位ID不能为空")
	@Size(min=0, max=64, message="申请单位ID长度不能超过 64 个字符")
	public String getApplicantUnitId() {
		return applicantUnitId;
	}

	public void setApplicantUnitId(String applicantUnitId) {
		this.applicantUnitId = applicantUnitId;
	}
	
	@Size(min=0, max=256, message="申请理由长度不能超过 256 个字符")
	public String getReasonForApplication() {
		return reasonForApplication;
	}

	public void setReasonForApplication(String reasonForApplication) {
		this.reasonForApplication = reasonForApplication;
	}
	
	@Size(min=0, max=256, message="申请用途长度不能超过 256 个字符")
	public String getApplicationForUse() {
		return applicationForUse;
	}

	public void setApplicationForUse(String applicationForUse) {
		this.applicationForUse = applicationForUse;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotNull(message="申请日期不能为空")
	public Date getApplicationDate() {
		return applicationDate;
	}

	public void setApplicationDate(Date applicationDate) {
		this.applicationDate = applicationDate;
	}
	
	@Size(min=0, max=64, message="处置类型长度不能超过 64 个字符")
	public String getDisposalType() {
		return disposalType;
	}

	public void setDisposalType(String disposalType) {
		this.disposalType = disposalType;
	}
	
	@Size(min=0, max=255, message="处置意见长度不能超过 255 个字符")
	public String getDisposalOpinion() {
		return disposalOpinion;
	}

	public void setDisposalOpinion(String disposalOpinion) {
		this.disposalOpinion = disposalOpinion;
	}

	public String getApplicantName() {
		return applicantName;
	}

	public void setApplicantName(String applicantName) {
		this.applicantName = applicantName;
	}

	public String getApplicantUnitName() {
		return applicantUnitName;
	}

	public void setApplicantUnitName(String applicantUnitName) {
		this.applicantUnitName = applicantUnitName;
	}

	public Employee getEmployee() {
		return employee;
	}

	public void setEmployee(Employee employee) {
		this.employee = employee;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	public RealEstate getRealEstate() {
		return realEstate;
	}

	public void setRealEstate(RealEstate realEstate) {
		this.realEstate = realEstate;
	}

	public AuctionTransferDisposalBusiness getAuctionTransferDisposalBusiness() {
		return auctionTransferDisposalBusiness;
	}

	public void setAuctionTransferDisposalBusiness(AuctionTransferDisposalBusiness auctionTransferDisposalBusiness) {
		this.auctionTransferDisposalBusiness = auctionTransferDisposalBusiness;
	}

	public DemolitionDisposalService getDemolitionDisposalService() {
		return demolitionDisposalService;
	}

	public void setDemolitionDisposalService(DemolitionDisposalService demolitionDisposalService) {
		this.demolitionDisposalService = demolitionDisposalService;
	}

	public LeaseDisposalBusiness getLeaseDisposalBusiness() {
		return leaseDisposalBusiness;
	}

	public void setLeaseDisposalBusiness(LeaseDisposalBusiness leaseDisposalBusiness) {
		this.leaseDisposalBusiness = leaseDisposalBusiness;
	}

	public RealEstateAddress getRealEstateAddress() {
		return realEstateAddress;
	}

	public void setRealEstateAddress(RealEstateAddress realEstateAddress) {
		this.realEstateAddress = realEstateAddress;
	}

	public UsageConversion getUsageConversion() {
		return usageConversion;
	}

	public void setUsageConversion(UsageConversion usageConversion) {
		this.usageConversion = usageConversion;
	}
}