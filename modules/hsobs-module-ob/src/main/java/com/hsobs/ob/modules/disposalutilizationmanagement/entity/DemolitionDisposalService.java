package com.hsobs.ob.modules.disposalutilizationmanagement.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 拆除处置业务Entity
 * <AUTHOR>
 * @version 2025-03-08
 */
@Table(name="ob_demolition_disposal_service", alias="a", label="拆除处置业务信息", columns={
        @Column(name="id", attrName="id", label="编号", isPK=true),
        @Column(name="geographic_information_id", attrName="geographicInformationId", label="地理信息编码"),
        @Column(name="asset_transfer_class", attrName="assetTransferClass", label="资产移交类别"),
        @Column(name="collection_time", attrName="collectionTime", label="征收时间", isUpdateForce=true),
        @Column(includeEntity=DataEntity.class),
        @Column(name="disposal_id", attrName="disposalId", label="处置编号"),
}, orderBy="a.update_date DESC"
)
public class DemolitionDisposalService extends DataEntity<DemolitionDisposalService> {

    private static final long serialVersionUID = 1L;
    private String geographicInformationId;		// 地理信息编码
    private String assetTransferClass;		// 资产移交类别
    private Date collectionTime;		// 征收时间
    private String disposalId;		// 处置编号

    public DemolitionDisposalService() {
        this(null);
    }

    public DemolitionDisposalService(String id){
        super(id);
    }

    @Size(min=0, max=64, message="地理信息编码长度不能超过 64 个字符")
    public String getGeographicInformationId() {
        return geographicInformationId;
    }

    public void setGeographicInformationId(String geographicInformationId) {
        this.geographicInformationId = geographicInformationId;
    }

    @Size(min=0, max=1, message="资产移交类别长度不能超过 1 个字符")
    public String getAssetTransferClass() {
        return assetTransferClass;
    }

    public void setAssetTransferClass(String assetTransferClass) {
        this.assetTransferClass = assetTransferClass;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getCollectionTime() {
        return collectionTime;
    }

    public void setCollectionTime(Date collectionTime) {
        this.collectionTime = collectionTime;
    }

    @Size(min=0, max=64, message="处置编号长度不能超过 64 个字符")
    public String getDisposalId() {
        return disposalId;
    }

    public void setDisposalId(String disposalId) {
        this.disposalId = disposalId;
    }

}