package com.hsobs.ob.modules.arrange.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.arrange.entity.RentalContractTemplate;
import com.hsobs.ob.modules.arrange.service.RentalContractTemplateService;

/**
 * 租用合同模版表Controller
 * <AUTHOR>
 * @version 2025-03-09
 */
@Controller
@RequestMapping(value = "${adminPath}/arrange/rentalContractTemplate")
public class RentalContractTemplateController extends BaseController {

	@Autowired
	private RentalContractTemplateService rentalContractTemplateService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public RentalContractTemplate get(String id, boolean isNewRecord) {
		return rentalContractTemplateService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("arrange:rentalContractTemplate:view")
	@RequestMapping(value = {"list", ""})
	public String list(RentalContractTemplate rentalContractTemplate, Model model) {
		model.addAttribute("rentalContractTemplate", rentalContractTemplate);
		return "modules/arrange/rentalContractTemplateList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("arrange:rentalContractTemplate:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<RentalContractTemplate> listData(RentalContractTemplate rentalContractTemplate, HttpServletRequest request, HttpServletResponse response) {
		rentalContractTemplate.setPage(new Page<>(request, response));
		Page<RentalContractTemplate> page = rentalContractTemplateService.findPage(rentalContractTemplate);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("arrange:rentalContractTemplate:view")
	@RequestMapping(value = "form")
	public String form(RentalContractTemplate rentalContractTemplate, Model model) {
		if (null == rentalContractTemplate.getStatus()) {
			rentalContractTemplate.setEnable("0");
		}
		model.addAttribute("rentalContractTemplate", rentalContractTemplate);
		return "modules/arrange/rentalContractTemplateForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("arrange:rentalContractTemplate:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated RentalContractTemplate rentalContractTemplate) {
		rentalContractTemplateService.save(rentalContractTemplate);
		return renderResult(Global.TRUE, text("保存租用合同模版成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("arrange:rentalContractTemplate:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(RentalContractTemplate rentalContractTemplate) {
		rentalContractTemplate.setStatus(RentalContractTemplate.STATUS_DISABLE);
		rentalContractTemplateService.updateStatus(rentalContractTemplate);
		return renderResult(Global.TRUE, text("停用租用合同模版成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("arrange:rentalContractTemplate:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(RentalContractTemplate rentalContractTemplate) {
		rentalContractTemplate.setEnable("1");
		rentalContractTemplateService.enable(rentalContractTemplate);
		return renderResult(Global.TRUE, text("启用租用合同模版成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("arrange:rentalContractTemplate:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(RentalContractTemplate rentalContractTemplate) {
		rentalContractTemplateService.delete(rentalContractTemplate);
		return renderResult(Global.TRUE, text("删除租用合同模版成功！"));
	}
	
}