package com.hsobs.ob.modules.repair.service;

import java.util.List;

import com.jeesite.common.entity.DataScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.repair.entity.RepairReport;
import com.hsobs.ob.modules.repair.dao.RepairReportDao;

/**
 * 维修规划报备Service
 * <AUTHOR>
 * @version 2025-03-15
 */
@Service
public class RepairReportService extends CrudService<RepairReportDao, RepairReport> {
	
	/**
	 * 获取单条数据
	 * @param repairReport
	 * @return
	 */
	@Override
	public RepairReport get(RepairReport repairReport) {
		return super.get(repairReport);
	}
	
	/**
	 * 查询分页数据
	 * @param repairReport 查询条件
	 * @param repairReport page 分页对象
	 * @return
	 */
	@Override
	public Page<RepairReport> findPage(RepairReport repairReport) {
		return super.findPage(repairReport);
	}
	
	/**
	 * 查询列表数据
	 * @param repairReport
	 * @return
	 */
	@Override
	public List<RepairReport> findList(RepairReport repairReport) {
		return super.findList(repairReport);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param repairReport
	 */
	@Override
	@Transactional
	public void save(RepairReport repairReport) {
		super.save(repairReport);
	}
	
	/**
	 * 更新状态
	 * @param repairReport
	 */
	@Override
	@Transactional
	public void updateStatus(RepairReport repairReport) {
		super.updateStatus(repairReport);
	}
	
	/**
	 * 删除数据
	 * @param repairReport
	 */
	@Override
	@Transactional
	public void delete(RepairReport repairReport) {
		super.delete(repairReport);
	}

	@Override
	public void addDataScopeFilter(RepairReport repairReport) {
		repairReport.sqlMap().getDataScope().addFilter("dsf", "Office", "a.office_code", DataScope.CTRL_PERMI_HAVE);
	}
}