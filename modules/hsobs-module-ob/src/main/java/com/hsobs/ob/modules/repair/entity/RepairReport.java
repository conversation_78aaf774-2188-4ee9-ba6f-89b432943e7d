package com.hsobs.ob.modules.repair.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;
import java.util.Date;

import com.jeesite.common.mybatis.annotation.JoinTable;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 维修规划报备Entity
 * <AUTHOR>
 * @version 2025-03-15
 */
@Table(name="ob_repair_report", alias="a", label="维修规划报备信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="project_no", attrName="projectNo", label="工程编号"),
		@Column(name="office_code", attrName="officeCode", label="单位编号"),
		@Column(name="project_name", attrName="projectName", label="项目名称", queryType=QueryType.LIKE),
		@Column(name="renovation_plan", attrName="renovationPlan", label="维修功能方案"),
		@Column(name="budget", attrName="budget", label="预算"),
		@Column(name="fund_source", attrName="fundSource", label="资金来源"),
		@Column(name="maint_type", attrName="maintType", label="维修性质"),
		@Column(name="repair_category", attrName="repairCategory", label="维修类型"),
		@Column(name="work_content", attrName="workContent", label="维修内容"),
		@Column(name="start_date", attrName="startDate", label="维修预计开始时间", queryType=QueryType.GTE),
		@Column(name="end_date", attrName="endDate", label="维修预计结束时间", queryType=QueryType.LTE),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, joinTable={
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "office",
				on = "a.office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
	}
	, extWhereKeys="dsf"
	, orderBy="a.update_date DESC"
)
public class RepairReport extends DataEntity<RepairReport> {
	
	private static final long serialVersionUID = 1L;
	private String projectNo;		// 工程编号
	private String officeCode;		// 单位编号
	private String projectName;		// 项目名称
	private String renovationPlan;		// 维修功能方案
	private Double budget;		// 预算
	private String fundSource;		// 资金来源
	private String maintType;		// 维修性质
	private String repairCategory;		// 维修类型
	private String workContent;		// 维修内容
	private Date startDate;		// 维修预计开始时间
	private Date endDate;		// 维修预计结束时间

	private Office office;

	@ExcelFields({
			@ExcelField(title="报备编号", attrName="id", align=Align.CENTER, sort=10),
			@ExcelField(title="工程编号", attrName="projectNo", align=Align.CENTER, sort=20),
			@ExcelField(title="单位名称", attrName="office.officeName", align=Align.CENTER, sort=30),
			@ExcelField(title="维修工程名称", attrName="projectName", align=Align.CENTER, sort=40),
			@ExcelField(title="维修改造方案", attrName="renovationPlan", align=Align.CENTER, sort=50),
			@ExcelField(title="工程预算", attrName="budget", align=Align.CENTER, sort=60),
			@ExcelField(title="资金来源", attrName="fundSource", dictType="ob_funding", align=Align.CENTER, sort=70),
			@ExcelField(title="维修性质", attrName="maintType", dictType="ob_repair_report_maint", align=Align.CENTER, sort=80),
			@ExcelField(title="维修类型", attrName="repairCategory", dictType="ob_repair_report_category", align=Align.CENTER, sort=90),
			@ExcelField(title="维修内容", attrName="workContent", align=Align.CENTER, sort=100),
			@ExcelField(title="维修预计开始时间", attrName="startDate", align=Align.CENTER, sort=110, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="维修预计结束时间", attrName="endDate", align=Align.CENTER, sort=120, dataFormat="yyyy-MM-dd hh:mm"),
	})
	public RepairReport() {
		this(null);
	}
	
	public RepairReport(String id){
		super(id);
	}
	
	@NotBlank(message="工程编号不能为空")
	@Size(min=0, max=512, message="工程编号长度不能超过 512 个字符")
	public String getProjectNo() {
		return projectNo;
	}

	public void setProjectNo(String projectNo) {
		this.projectNo = projectNo;
	}
	
	@NotBlank(message="单位编号不能为空")
	@Size(min=0, max=64, message="单位编号长度不能超过 64 个字符")
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
	
	@NotBlank(message="项目名称不能为空")
	@Size(min=0, max=512, message="项目名称长度不能超过 512 个字符")
	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	
	@NotBlank(message="维修功能方案不能为空")
	public String getRenovationPlan() {
		return renovationPlan;
	}

	public void setRenovationPlan(String renovationPlan) {
		this.renovationPlan = renovationPlan;
	}
	
	@NotNull(message="预算不能为空")
	public Double getBudget() {
		return budget;
	}

	public void setBudget(Double budget) {
		this.budget = budget;
	}
	
	@NotBlank(message="资金来源不能为空")
	@Size(min=0, max=20, message="资金来源长度不能超过 20 个字符")
	public String getFundSource() {
		return fundSource;
	}

	public void setFundSource(String fundSource) {
		this.fundSource = fundSource;
	}
	
	@NotBlank(message="维修性质不能为空")
	@Size(min=0, max=20, message="维修性质长度不能超过 20 个字符")
	public String getMaintType() {
		return maintType;
	}

	public void setMaintType(String maintType) {
		this.maintType = maintType;
	}
	
	@NotBlank(message="维修类型不能为空")
	@Size(min=0, max=30, message="维修类型长度不能超过 30 个字符")
	public String getRepairCategory() {
		return repairCategory;
	}

	public void setRepairCategory(String repairCategory) {
		this.repairCategory = repairCategory;
	}
	
	@NotBlank(message="维修内容不能为空")
	public String getWorkContent() {
		return workContent;
	}

	public void setWorkContent(String workContent) {
		this.workContent = workContent;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="维修预计开始时间不能为空")
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="维修预计结束时间不能为空")
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}
}