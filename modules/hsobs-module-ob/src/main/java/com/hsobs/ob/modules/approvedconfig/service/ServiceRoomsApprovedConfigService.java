package com.hsobs.ob.modules.approvedconfig.service;

import java.util.ArrayList;
import java.util.List;

import com.jeesite.common.config.Global;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.validator.ValidatorUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.approvedconfig.entity.ServiceRoomsApprovedConfig;
import com.hsobs.ob.modules.approvedconfig.dao.ServiceRoomsApprovedConfigDao;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 服务用房使用面积核定Service
 * <AUTHOR>
 * @version 2025-03-09
 */
@Service
public class ServiceRoomsApprovedConfigService extends CrudService<ServiceRoomsApprovedConfigDao, ServiceRoomsApprovedConfig> {
	
	/**
	 * 获取单条数据
	 * @param serviceRoomsApprovedConfig
	 * @return
	 */
	@Override
	public ServiceRoomsApprovedConfig get(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig) {
		return super.get(serviceRoomsApprovedConfig);
	}
	
	/**
	 * 查询分页数据
	 * @param serviceRoomsApprovedConfig 查询条件
	 * @param serviceRoomsApprovedConfig page 分页对象
	 * @return
	 */
	@Override
	public Page<ServiceRoomsApprovedConfig> findPage(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig) {
		return super.findPage(serviceRoomsApprovedConfig);
	}
	
	/**
	 * 查询列表数据
	 * @param serviceRoomsApprovedConfig
	 * @return
	 */
	@Override
	public List<ServiceRoomsApprovedConfig> findList(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig) {
		return super.findList(serviceRoomsApprovedConfig);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param serviceRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void save(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig) {
		super.save(serviceRoomsApprovedConfig);
	}

	public void saveAll(List<ServiceRoomsApprovedConfig> serviceRoomsApprovedConfigs) {
		for (ServiceRoomsApprovedConfig config : serviceRoomsApprovedConfigs) {
			super.save(config);
		}
	}

	@Transactional
	public void initSave() {
		List<ServiceRoomsApprovedConfig> serviceRoomsApprovedConfigList = new ArrayList<>();
		serviceRoomsApprovedConfigList.add(buildServiceRoomsApprovedConfig("1", 7., 9.));
		serviceRoomsApprovedConfigList.add(buildServiceRoomsApprovedConfig("2", 7., 9.));
		serviceRoomsApprovedConfigList.add(buildServiceRoomsApprovedConfig("3", 6., 8.));
		serviceRoomsApprovedConfigList.add(buildServiceRoomsApprovedConfig("4", 6., 8.));
		serviceRoomsApprovedConfigList.add(buildServiceRoomsApprovedConfig("5", 6., 8.));
		serviceRoomsApprovedConfigList.forEach(super::insert);
	}

	private ServiceRoomsApprovedConfig buildServiceRoomsApprovedConfig(String id, Double minArea, Double maxArea) {
		ServiceRoomsApprovedConfig serviceRoomsApprovedConfig = new ServiceRoomsApprovedConfig();
		serviceRoomsApprovedConfig.setId(id);
		serviceRoomsApprovedConfig.setMinArea(minArea);
		serviceRoomsApprovedConfig.setMaxArea(maxArea);
		return serviceRoomsApprovedConfig;
	}

	/**
	 * 更新状态
	 * @param serviceRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void updateStatus(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig) {
		super.updateStatus(serviceRoomsApprovedConfig);
	}
	
	/**
	 * 删除数据
	 * @param serviceRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void delete(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig) {
		super.delete(serviceRoomsApprovedConfig);
	}


	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<ServiceRoomsApprovedConfig> list = ei.getDataList(ServiceRoomsApprovedConfig.class);
			for (ServiceRoomsApprovedConfig serviceRoomsApprovedConfig : list) {
				try{
					ValidatorUtils.validateWithException(serviceRoomsApprovedConfig);
					this.save(serviceRoomsApprovedConfig);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + serviceRoomsApprovedConfig.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + serviceRoomsApprovedConfig.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
}