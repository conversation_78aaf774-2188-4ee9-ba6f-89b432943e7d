package com.hsobs.ob.modules.earlywarnmessage.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.ob.modules.earlywarnmessage.entity.RoomAreaExcessRecord;
import com.hsobs.ob.modules.earlywarnmessage.entity.UnitAreaExcessRecord;
import com.hsobs.ob.modules.earlywarnmessage.service.EarlyWarnMessageService;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.earlywarnmessage.entity.EarlyWarnMessage;
import com.hsobs.ob.modules.earlywarnmessage.service.EarlyWarnMessageService;

/**
 * 预警消息Controller
 * <AUTHOR>
 * @version 2024-12-02
 */
@Controller
@RequestMapping(value = "${adminPath}/earlywarnmessage/")
public class EarlyWarnMessageController extends BaseController {

	@Autowired
	private EarlyWarnMessageService earlyWarnMessageService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public EarlyWarnMessage get(String id, boolean isNewRecord) {
		return earlyWarnMessageService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = {"list", ""})
	public String list(EarlyWarnMessage earlyWarnMessage, Model model) {
		model.addAttribute("earlyWarnMessage", earlyWarnMessage);
		return "modules/earlywarnmessage/earlyWarnMessageList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = {"roomAreaExcessRecordList", ""})
	public String roomAreaExcessRecordList(RoomAreaExcessRecord roomAreaExcessRecord, Model model) {
		model.addAttribute("roomAreaExcessRecord", roomAreaExcessRecord);
		return "modules/earlywarnmessage/roomAreaExcessRecordList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = {"unitAreaExcessRecordList", ""})
	public String unitAreaExcessRecordList(UnitAreaExcessRecord unitAreaExcessRecord, Model model) {
		model.addAttribute("unitAreaExcessRecord", unitAreaExcessRecord);
		return "modules/earlywarnmessage/unitAreaExcessRecordList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "unitAreaExcessRecordListData")
	@ResponseBody
	public Page<UnitAreaExcessRecord> unitAreaExcessRecordListData(UnitAreaExcessRecord unitAreaExcessRecord, HttpServletRequest request, HttpServletResponse response) {
		unitAreaExcessRecord.setPage(new Page<>(request, response));
		Page<UnitAreaExcessRecord> page = earlyWarnMessageService.unitAreaExcessRecordListData(unitAreaExcessRecord);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "roomAreaExcessRecordListData")
	@ResponseBody
	public Page<RoomAreaExcessRecord> roomAreaExcessRecordListData(RoomAreaExcessRecord roomAreaExcessRecord, HttpServletRequest request, HttpServletResponse response) {
		roomAreaExcessRecord.setPage(new Page<>(request, response));
		Page<RoomAreaExcessRecord> page = earlyWarnMessageService.roomAreaExcessRecordListData(roomAreaExcessRecord);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<EarlyWarnMessage> listData(EarlyWarnMessage earlyWarnMessage, HttpServletRequest request, HttpServletResponse response) {
		earlyWarnMessage.setPage(new Page<>(request, response));
		earlyWarnMessageService.addDataScopeFilter(earlyWarnMessage);
		Page<EarlyWarnMessage> page = earlyWarnMessageService.findPage(earlyWarnMessage);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "form")
	public String form(EarlyWarnMessage earlyWarnMessage, Model model) {
		model.addAttribute("earlyWarnMessage", earlyWarnMessage);
		return "modules/earlywarnmessage/earlyWarnMessageForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("earlywarnmessage::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated EarlyWarnMessage earlyWarnMessage) {
		earlyWarnMessageService.save(earlyWarnMessage);
		return renderResult(Global.TRUE, text("保存预警消息成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "exportData")
	public void exportData(EarlyWarnMessage earlyWarnMessage, HttpServletResponse response) {
		List<EarlyWarnMessage> list = earlyWarnMessageService.findList(earlyWarnMessage);
		String fileName = "预警消息" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("预警消息", EarlyWarnMessage.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "sendEarlyWarnMessageToOA")
	public void sendEarlyWarnMessageToOA(EarlyWarnMessage earlyWarnMessage, HttpServletResponse response) {
		earlyWarnMessageService.sendEarlyWarnMessageToOA(earlyWarnMessage);
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "exportRoomAreaExcessRecordData")
	public void exportRoomAreaExcessRecordData(RoomAreaExcessRecord roomAreaExcessRecord, HttpServletResponse response) {
		List<RoomAreaExcessRecord> list = earlyWarnMessageService.exportRoomAreaExcessRecordData(roomAreaExcessRecord);
		String fileName = "房间面积超标查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("房间面积超标查询", RoomAreaExcessRecord.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "exportUnitAreaExcessRecordData")
	public void exportUnitAreaExcessRecordData(UnitAreaExcessRecord unitAreaExcessRecord, HttpServletResponse response) {
		List<UnitAreaExcessRecord> list = earlyWarnMessageService.exportUnitAreaExcessRecordData(unitAreaExcessRecord);
		String fileName = "单位面积超标查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("单位面积超标查询", UnitAreaExcessRecord.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("earlywarnmessage::view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		EarlyWarnMessage earlyWarnMessage = new EarlyWarnMessage();
		List<EarlyWarnMessage> list = ListUtils.newArrayList(earlyWarnMessage);
		String fileName = "预警消息模板.xlsx";
		try(ExcelExport ee = new ExcelExport("预警消息", EarlyWarnMessage.class, ExcelField.Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("earlywarnmessage::edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = earlyWarnMessageService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("earlywarnmessage::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(EarlyWarnMessage earlyWarnMessage) {
		earlyWarnMessageService.delete(earlyWarnMessage);
		return renderResult(Global.TRUE, text("删除预警消息成功！"));
	}
	
}