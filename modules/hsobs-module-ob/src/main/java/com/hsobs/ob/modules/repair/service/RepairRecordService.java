package com.hsobs.ob.modules.repair.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.repair.entity.RepairRecord;
import com.hsobs.ob.modules.repair.dao.RepairRecordDao;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 维修组织记录表Service
 * <AUTHOR>
 * @version 2025-03-16
 */
@Service
public class RepairRecordService extends CrudService<RepairRecordDao, RepairRecord> {
	
	/**
	 * 获取单条数据
	 * @param repairRecord
	 * @return
	 */
	@Override
	public RepairRecord get(RepairRecord repairRecord) {
		return super.get(repairRecord);
	}
	
	/**
	 * 查询分页数据
	 * @param repairRecord 查询条件
	 * @param repairRecord page 分页对象
	 * @return
	 */
	@Override
	public Page<RepairRecord> findPage(RepairRecord repairRecord) {
		return super.findPage(repairRecord);
	}
	
	/**
	 * 查询列表数据
	 * @param repairRecord
	 * @return
	 */
	@Override
	public List<RepairRecord> findList(RepairRecord repairRecord) {
		return super.findList(repairRecord);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param repairRecord
	 */
	@Override
	@Transactional
	public void save(RepairRecord repairRecord) {
		super.save(repairRecord);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(repairRecord, repairRecord.getId(), "repairRecord_file");
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<RepairRecord> list = ei.getDataList(RepairRecord.class);
			for (RepairRecord repairRecord : list) {
				try{
					ValidatorUtils.validateWithException(repairRecord);
					this.save(repairRecord);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + repairRecord.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + repairRecord.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
	/**
	 * 更新状态
	 * @param repairRecord
	 */
	@Override
	@Transactional
	public void updateStatus(RepairRecord repairRecord) {
		super.updateStatus(repairRecord);
	}
	
	/**
	 * 删除数据
	 * @param repairRecord
	 */
	@Override
	@Transactional
	public void delete(RepairRecord repairRecord) {
		super.delete(repairRecord);
	}
	
}