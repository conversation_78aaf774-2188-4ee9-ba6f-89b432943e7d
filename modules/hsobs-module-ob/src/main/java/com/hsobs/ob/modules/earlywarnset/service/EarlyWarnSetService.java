package com.hsobs.ob.modules.earlywarnset.service;

import java.util.List;

import com.hsobs.ob.modules.earlywarnset.dao.EarlyWarnSetDao;
import com.hsobs.ob.modules.earlywarnset.entity.EarlyWarnSet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.earlywarnset.entity.EarlyWarnSet;
import com.hsobs.ob.modules.earlywarnset.dao.EarlyWarnSetDao;

/**
 * 预警设置Service
 * <AUTHOR>
 * @version 2024-12-01
 */
@Service
public class EarlyWarnSetService extends CrudService<EarlyWarnSetDao, EarlyWarnSet> {
	
	/**
	 * 获取单条数据
	 * @param earlyWarnSet
	 * @return
	 */
	@Override
	public EarlyWarnSet get(EarlyWarnSet earlyWarnSet) {
		return super.get(earlyWarnSet);
	}
	
	/**
	 * 查询分页数据
	 * @param earlyWarnSet 查询条件
	 * @param earlyWarnSet page 分页对象
	 * @return
	 */
	@Override
	public Page<EarlyWarnSet> findPage(EarlyWarnSet earlyWarnSet) {
		return super.findPage(earlyWarnSet);
	}
	
	/**
	 * 查询列表数据
	 * @param earlyWarnSet
	 * @return
	 */
	@Override
	public List<EarlyWarnSet> findList(EarlyWarnSet earlyWarnSet) {
		return super.findList(earlyWarnSet);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param earlyWarnSet
	 */
	@Override
	@Transactional
	public void save(EarlyWarnSet earlyWarnSet) {
		super.save(earlyWarnSet);
	}
	
	/**
	 * 更新状态
	 * @param earlyWarnSet
	 */
	@Override
	@Transactional
	public void updateStatus(EarlyWarnSet earlyWarnSet) {
		super.updateStatus(earlyWarnSet);
	}
	
	/**
	 * 删除数据
	 * @param earlyWarnSet
	 */
	@Override
	@Transactional
	public void delete(EarlyWarnSet earlyWarnSet) {
		super.delete(earlyWarnSet);
	}
	
}