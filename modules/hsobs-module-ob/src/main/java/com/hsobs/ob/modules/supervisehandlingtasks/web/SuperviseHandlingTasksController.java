package com.hsobs.ob.modules.supervisehandlingtasks.web;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSON;
import com.hsobs.ob.modules.earlywarnmessage.entity.EarlyWarnMessage;
import com.hsobs.ob.modules.earlywarnmessage.service.EarlyWarnMessageService;
import com.hsobs.ob.modules.supervisehandlingtasks.entity.*;
import com.hsobs.ob.modules.supervisehandlingtasks.service.SuperviseHandlingTasksService;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasks;
import com.hsobs.ob.modules.supervisehandlingtasks.service.SuperviseHandlingTasksService;

/**
 * 监督督办任务Controller
 * <AUTHOR>
 * @version 2024-12-10
 */
@Controller
@RequestMapping(value = "${adminPath}/supervisehandlingtasks/")
public class SuperviseHandlingTasksController extends BaseController {

	@Autowired
	private SuperviseHandlingTasksService superviseHandlingTasksService;

	@Autowired
	private FileUploadService fileUploadService;
    @Autowired
    private EarlyWarnMessageService earlyWarnMessageService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public SuperviseHandlingTasks get(String id, boolean isNewRecord) {
		return superviseHandlingTasksService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = {"list", ""})
	public String list(SuperviseHandlingTasks superviseHandlingTasks, Model model) {
		model.addAttribute("superviseHandlingTasks", superviseHandlingTasks);
		return "modules/supervisehandlingtasks/superviseHandlingTasksList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = {"listQuery", ""})
	public String listQuery(SuperviseHandlingTasksQuery superviseHandlingTasksQuery, Model model) {
		model.addAttribute("superviseHandlingTasksQuery", superviseHandlingTasksQuery);
		return "modules/supervisehandlingtasks/superviseHandlingTasksListQuery";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = {"listLedger", ""})
	public String listLedger(SuperviseHandlingTasksLedger superviseHandlingTasksLedger, Model model) {
		model.addAttribute("superviseHandlingTasksLedger", superviseHandlingTasksLedger);
		return "modules/supervisehandlingtasks/superviseHandlingTasksListLedger";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<SuperviseHandlingTasks> listData(SuperviseHandlingTasks superviseHandlingTasks, HttpServletRequest request, HttpServletResponse response) {
		superviseHandlingTasks.setPage(new Page<>(request, response));
		superviseHandlingTasksService.addDataScopeFilter(superviseHandlingTasks);
		Page<SuperviseHandlingTasks> page = superviseHandlingTasksService.findPage(superviseHandlingTasks);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "listLedgerData")
	@ResponseBody
	public Page<SuperviseHandlingTasksLedger> listLedgerData(SuperviseHandlingTasksLedger superviseHandlingTasksLedger, HttpServletRequest request, HttpServletResponse response) {
		superviseHandlingTasksLedger.setPage(new Page<>(request, response));
		Page<SuperviseHandlingTasksLedger> page = superviseHandlingTasksService.listLedgerData(superviseHandlingTasksLedger);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "listQueryData")
	@ResponseBody
	public Page<SuperviseHandlingTasksQuery> listQueryData(SuperviseHandlingTasksQuery superviseHandlingTasksQuery, HttpServletRequest request, HttpServletResponse response) {
		superviseHandlingTasksQuery.setPage(new Page<>(request, response));
		Page<SuperviseHandlingTasksQuery> page = superviseHandlingTasksService.listQueryData(superviseHandlingTasksQuery);
		return page;
	}

	/**
	 * 查询列表数据-外部接口
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "officeList")
	@ResponseBody
	public Page<Map<String,Object>> officeList(SuperviseHandlingTasks superviseHandlingTasks, HttpServletRequest request, HttpServletResponse response) {
		superviseHandlingTasks.setPage(new Page<>(request, response));
		superviseHandlingTasks.setStatus("");
		List<Map<String,Object>> list = superviseHandlingTasksService.findPage(superviseHandlingTasks).getList().stream().map(item ->{
			Map<String,Object> map = new HashMap<>();
			map.put("id",item.getId());
			map.put("dcdh",item.getId());
			map.put("dcdw",item.getOffice().getOfficeName());
			if(item.getTaskStatus().equals("1")||item.getTaskStatus().equals("2")||item.getTaskStatus().equals("3")){
				map.put("dczt","1");
			}else{
				map.put("dczt","0");
			}

			return map;
		}).collect(Collectors.toList());
		Page<Map<String,Object>> page = new Page<>(request, response);
		page.setList(list);
		return page;
	}

	/**
	 * 查询数据详情-外部接口
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "officeDetail")
	@ResponseBody
	public Map<String,Object> officeDetail(SuperviseHandlingTasks superviseHandlingTasksReq, HttpServletRequest request, HttpServletResponse response) {
		System.out.println(superviseHandlingTasksReq.getId());
		SuperviseHandlingTasks superviseHandlingTasks = superviseHandlingTasksService.get(superviseHandlingTasksReq.getId());
		Map<String,Object> map = new HashMap<>();
		map.put("id",superviseHandlingTasks.getId());
		map.put("dcdh",superviseHandlingTasks.getId());
		map.put("dcdw",superviseHandlingTasks.getOffice().getOfficeName());
		map.put("dcr", superviseHandlingTasks.getInquirer());
		if(superviseHandlingTasks.getTaskStatus().equals("1")||superviseHandlingTasks.getTaskStatus().equals("2")||superviseHandlingTasks.getTaskStatus().equals("3")){
			map.put("dczt","1");
		}else{
			map.put("dczt","0");
		}
		if(superviseHandlingTasks.getTaskStatus().equals("2")||superviseHandlingTasks.getTaskStatus().equals("3")){
			map.put("dcjg","1");
		}else{
			map.put("dcjg","0");
		}
		map.put("dcqkms",superviseHandlingTasks.getVerifyTheSituation());
		FileUpload fileUpload = new FileUpload();
		fileUpload.setBizKey(superviseHandlingTasks.getId());
		fileUpload.setBizType("obQwOffice_check");
		fileUpload.setPage(new Page<>(request, response));
		List<FileUpload> list = fileUploadService.findPage(fileUpload).getList();
		List<SurveyImageData> surveyImageDataList = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			SurveyImageData surveyImageData = new SurveyImageData();
			surveyImageData.setFileType("obQwOffice_check");
			surveyImageData.setFileId(list.get(i).getId());
			surveyImageDataList.add(surveyImageData);
		}
		map.put("dcqktpList",surveyImageDataList);
		return map;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "form")
	public String form(SuperviseHandlingTasks superviseHandlingTasks, Model model) {
		model.addAttribute("superviseHandlingTasks", superviseHandlingTasks);
		return "modules/supervisehandlingtasks/superviseHandlingTasksForm";
	}

	/**
	 * 查看审批表单
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "auditForm")
	public String auditForm(SuperviseHandlingTasks superviseHandlingTasks, Model model) {
		model.addAttribute("superviseHandlingTasks", superviseHandlingTasks);
		return "modules/supervisehandlingtasks/superviseHandlingTasksAuditForm";
	}

	/**
	 * 查看监督检查表单
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "superviseCheckForm")
	public String superviseCheckForm(SuperviseHandlingTasks superviseHandlingTasks, Model model) {
		model.addAttribute("superviseHandlingTasks", superviseHandlingTasks);
		return "modules/supervisehandlingtasks/superviseHandlingTasksSuperviseCheckForm";
	}

	/**
	 * 查看整改反馈表单
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "correctiveFeedbackForm")
	public String correctiveFeedbackForm(SuperviseHandlingTasks superviseHandlingTasks, Model model) {
		model.addAttribute("superviseHandlingTasks", superviseHandlingTasks);
		return "modules/supervisehandlingtasks/superviseHandlingTasksCorrectiveFeedbackForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("supervisehandlingtasks::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated SuperviseHandlingTasks superviseHandlingTasks) {
		superviseHandlingTasksService.save(superviseHandlingTasks);
		return renderResult(Global.TRUE, text("保存监督督办任务成功！"));
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("supervisehandlingtasks::edit")
	@RequestMapping(value = "officeSave")
	@ResponseBody
	public String officeSave(SuperviseHandlingTasksReq superviseHandlingTasksReq,HttpServletRequest request, HttpServletResponse response) {
		SuperviseHandlingTasks superviseHandlingTasks = new SuperviseHandlingTasks();
		superviseHandlingTasks.setId(superviseHandlingTasksReq.getId());
		superviseHandlingTasks.setInquirer(superviseHandlingTasksReq.getDcr());
		superviseHandlingTasks.setInquirerPhone(superviseHandlingTasksReq.getSjhm());
		if(superviseHandlingTasksReq.getDcjy().equals("0")){
			superviseHandlingTasks.setTaskStatus("2");
		}else{
			superviseHandlingTasks.setTaskStatus("3");
		}
		List<SurveyImageData> surveyImageDataList = superviseHandlingTasksReq.getDcqktpList();
		Map<String,Object> dataMap = new HashMap<>();
		for (int i = 0; i < surveyImageDataList.size(); i++) {
			SurveyImageData surveyImageData = surveyImageDataList.get(i);
			dataMap.put(surveyImageData.getFileType(),surveyImageData.getFileId());
		}

		superviseHandlingTasks.setDataMap(dataMap);
		superviseHandlingTasks.setVerifyTheSituation(superviseHandlingTasksReq.getDcqkms());
		superviseHandlingTasksService.officeSave(superviseHandlingTasks);
		// 保存上传附件（督办任务材料）
		FileUploadUtils.saveFileUpload(superviseHandlingTasks, superviseHandlingTasks.getId(), "obQwOffice_check");

		return renderResult(Global.TRUE, text("提交办公用房调查核实信息成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "exportData")
	public void exportData(SuperviseHandlingTasks superviseHandlingTasks, HttpServletResponse response) {
		// 根据部门类型过滤数据
		if (StringUtils.isNotBlank(superviseHandlingTasks.getCodesString())){
			superviseHandlingTasks.setId_in(superviseHandlingTasks.getCodesString().split(","));
		}
		List<SuperviseHandlingTasks> list = superviseHandlingTasksService.findList(superviseHandlingTasks);
		String fileName = "监督督办任务" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("监督督办任务", SuperviseHandlingTasks.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "batchDistribution")
	@ResponseBody
	public String batchDistribution(SuperviseHandlingTasks superviseHandlingTasks) {
		if(superviseHandlingTasks.getStatus()==null){
			superviseHandlingTasks.setStatus("");
		}
		// 根据部门类型过滤数据
		if (StringUtils.isNotBlank(superviseHandlingTasks.getCodesString())){
			superviseHandlingTasks.setId_in(superviseHandlingTasks.getCodesString().split(","));
		}
		List<SuperviseHandlingTasks> list = superviseHandlingTasksService.findList(superviseHandlingTasks);
		int n = 0;
		for (int i = 0; i < list.size(); i++) {
			SuperviseHandlingTasks superviseHandlingTasksQuery = list.get(i);
			if(superviseHandlingTasksQuery.getTaskStatus().equals("2")){
				EarlyWarnMessage earlyWarnMessage = new EarlyWarnMessage();
				earlyWarnMessage.setEarlyWarnType("3");
				earlyWarnMessage.setOccupancyClassification(superviseHandlingTasksQuery.getOccupancyClassification());
				earlyWarnMessage.setOfficeOccupancyUnitId(superviseHandlingTasksQuery.getOfficeCode());
				earlyWarnMessage.setWarningDate(new Date());
				earlyWarnMessage.setEarlyWarnState("0");
				earlyWarnMessage.setPushTag("0");
				earlyWarnMessage.setEarlyWarnMessageInfo("监督督办预警：" + superviseHandlingTasksQuery.getRectificationMatters());
				earlyWarnMessageService.save(earlyWarnMessage);
				n++;
			}
		}
		return renderResult(Global.TRUE, text("批量下发成功，共下发"+n+"条监督督办预警！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "exportLedgerData")
	public void exportLedgerData(SuperviseHandlingTasksLedger superviseHandlingTasksLedger, HttpServletResponse response) {
		List<SuperviseHandlingTasksLedger> list = superviseHandlingTasksService.listLedger(superviseHandlingTasksLedger);
		String fileName = "监督督办任务" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("监督督办任务", SuperviseHandlingTasksLedger.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据-监督检查信息查询
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "exportQueryData")
	public void exportQueryData(SuperviseHandlingTasksQuery superviseHandlingTasksQuery, HttpServletResponse response) {
		List<SuperviseHandlingTasksQuery> list = superviseHandlingTasksService.exportQueryData(superviseHandlingTasksQuery);
		String fileName = "监督检查信息查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("监督检查信息查询", SuperviseHandlingTasksQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("supervisehandlingtasks::view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		SuperviseHandlingTasks superviseHandlingTasks = new SuperviseHandlingTasks();
		List<SuperviseHandlingTasks> list = ListUtils.newArrayList(superviseHandlingTasks);
		String fileName = "监督督办任务模板.xlsx";
		try(ExcelExport ee = new ExcelExport("监督督办任务", SuperviseHandlingTasks.class, ExcelField.Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("supervisehandlingtasks::edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = superviseHandlingTasksService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("supervisehandlingtasks::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(SuperviseHandlingTasks superviseHandlingTasks) {
		if (!SuperviseHandlingTasks.STATUS_DRAFT.equals(superviseHandlingTasks.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		superviseHandlingTasksService.delete(superviseHandlingTasks);
		return renderResult(Global.TRUE, text("删除监督督办任务成功！"));
	}
	
}