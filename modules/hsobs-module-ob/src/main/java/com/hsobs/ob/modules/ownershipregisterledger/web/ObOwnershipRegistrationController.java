package com.hsobs.ob.modules.ownershipregisterledger.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.ownershipregisterledger.entity.ObOwnershipRegistration;
import com.hsobs.ob.modules.ownershipregisterledger.service.ObOwnershipRegistrationService;

/**
 * 权属登记台账Controller
 * <AUTHOR>
 * @version 2025-03-01
 */
@Controller
@RequestMapping(value = "${adminPath}/ownershipregisterledger/obOwnershipRegistration")
public class ObOwnershipRegistrationController extends BaseController {

	@Autowired
	private ObOwnershipRegistrationService obOwnershipRegistrationService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ObOwnershipRegistration get(String id, boolean isNewRecord) {
		return obOwnershipRegistrationService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("ownershipregisterledger:obOwnershipRegistration:view")
	@RequestMapping(value = {"list", ""})
	public String list(ObOwnershipRegistration obOwnershipRegistration, Model model) {
		model.addAttribute("obOwnershipRegistration", obOwnershipRegistration);
		return "modules/ownershipregisterledger/obOwnershipRegistrationList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("ownershipregisterledger:obOwnershipRegistration:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ObOwnershipRegistration> listData(ObOwnershipRegistration obOwnershipRegistration, HttpServletRequest request, HttpServletResponse response) {
		obOwnershipRegistration.setPage(new Page<>(request, response));
		Page<ObOwnershipRegistration> page = obOwnershipRegistrationService.findPage(obOwnershipRegistration);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("ownershipregisterledger:obOwnershipRegistration:view")
	@RequestMapping(value = "form")
	public String form(ObOwnershipRegistration obOwnershipRegistration, Model model) {
		model.addAttribute("obOwnershipRegistration", obOwnershipRegistration);
		return "modules/ownershipregisterledger/obOwnershipRegistrationForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("ownershipregisterledger:obOwnershipRegistration:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ObOwnershipRegistration obOwnershipRegistration) {
		obOwnershipRegistrationService.save(obOwnershipRegistration);
		return renderResult(Global.TRUE, text("保存权属登记台账成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("ownershipregisterledger:obOwnershipRegistration:view")
	@RequestMapping(value = "exportData")
	public void exportData(ObOwnershipRegistration obOwnershipRegistration, HttpServletResponse response) {
		List<ObOwnershipRegistration> list = obOwnershipRegistrationService.findList(obOwnershipRegistration);
		String fileName = "权属登记台账" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("权属登记台账", ObOwnershipRegistration.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("ownershipregisterledger:obOwnershipRegistration:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		ObOwnershipRegistration obOwnershipRegistration = new ObOwnershipRegistration();
		List<ObOwnershipRegistration> list = ListUtils.newArrayList(obOwnershipRegistration);
		String fileName = "权属登记台账模板.xlsx";
		try(ExcelExport ee = new ExcelExport("权属登记台账", ObOwnershipRegistration.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("ownershipregisterledger:obOwnershipRegistration:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = obOwnershipRegistrationService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
}