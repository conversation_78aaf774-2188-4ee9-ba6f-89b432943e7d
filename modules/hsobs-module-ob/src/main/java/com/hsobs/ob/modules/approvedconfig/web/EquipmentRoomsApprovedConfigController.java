package com.hsobs.ob.modules.approvedconfig.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.approvedconfig.entity.EquipmentRoomsApprovedConfig;
import com.hsobs.ob.modules.approvedconfig.service.EquipmentRoomsApprovedConfigService;

/**
 * 设备用房面积核定Controller
 * <AUTHOR>
 * @version 2025-03-09
 */
@Controller
@RequestMapping(value = "${adminPath}/approvedconfig/equipmentRoomsApprovedConfig")
public class EquipmentRoomsApprovedConfigController extends BaseController {

	@Autowired
	private EquipmentRoomsApprovedConfigService equipmentRoomsApprovedConfigService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public EquipmentRoomsApprovedConfig get(String id, boolean isNewRecord) {
		return equipmentRoomsApprovedConfigService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("approvedconfig:equipmentRoomsApprovedConfig:view")
	@RequestMapping(value = {"list", ""})
	public String list(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig, Model model) {
		model.addAttribute("equipmentRoomsApprovedConfig", equipmentRoomsApprovedConfig);
		return "modules/approvedconfig/equipmentRoomsApprovedConfigList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("approvedconfig:equipmentRoomsApprovedConfig:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<EquipmentRoomsApprovedConfig> listData(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig, HttpServletRequest request, HttpServletResponse response) {
		equipmentRoomsApprovedConfig.setPage(new Page<>(request, response));
		Page<EquipmentRoomsApprovedConfig> page = equipmentRoomsApprovedConfigService.findPage(equipmentRoomsApprovedConfig);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("approvedconfig:equipmentRoomsApprovedConfig:view")
	@RequestMapping(value = "form")
	public String form(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig, Model model) {
		long count = equipmentRoomsApprovedConfigService.findCount(equipmentRoomsApprovedConfig);
		if (count == 0) {
			equipmentRoomsApprovedConfigService.initSave();

		}
		equipmentRoomsApprovedConfig = equipmentRoomsApprovedConfigService.get(equipmentRoomsApprovedConfig.getId());
		model.addAttribute("equipmentRoomsApprovedConfig", equipmentRoomsApprovedConfig);
		return "modules/approvedconfig/equipmentRoomsApprovedConfigForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("approvedconfig:equipmentRoomsApprovedConfig:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig) {
		equipmentRoomsApprovedConfigService.save(equipmentRoomsApprovedConfig);
		return renderResult(Global.TRUE, text("保存设备用房面积核定成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("approvedconfig:equipmentRoomsApprovedConfig:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig) {
		equipmentRoomsApprovedConfigService.delete(equipmentRoomsApprovedConfig);
		return renderResult(Global.TRUE, text("删除设备用房面积核定成功！"));
	}
	
}