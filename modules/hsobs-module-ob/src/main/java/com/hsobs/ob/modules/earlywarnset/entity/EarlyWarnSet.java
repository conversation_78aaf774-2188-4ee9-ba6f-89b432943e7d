package com.hsobs.ob.modules.earlywarnset.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;

import java.math.BigDecimal;

/**
 * 预警设置Entity
 * <AUTHOR>
 * @version 2024-12-01
 */
@Table(name="ob_early_warn_set", alias="a", label="预警设置信息", columns={
		@Column(name="id", attrName="id", label="预警编号", isPK=true),
		@Column(name="early_warn_type", attrName="earlyWarnType", label="预警类型"),
		@Column(name="occupancy_classification", attrName="occupancyClassification", label="用房分类"),
		@Column(name="early_warn_index", attrName="earlyWarnIndex", label="预警指标"),
		@Column(name="early_target", attrName="earlyTarget", label="预警对象"),
		@Column(name="early_cycle", attrName="earlyCycle", label="预警周期"),
		@Column(name="standard_area", attrName="standardArea", label="标准面积", isUpdateForce=true),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class EarlyWarnSet extends DataEntity<EarlyWarnSet> {
	
	private static final long serialVersionUID = 1L;
	private String earlyWarnType;		// 预警类型
	private String occupancyClassification;		// 用房分类
	private String earlyWarnIndex;		// 预警指标
	private String earlyTarget;		// 预警对象
	private String earlyCycle;		// 预警周期
	private BigDecimal standardArea;		// 标准面积

	public String getEarlyTarget() {
		return earlyTarget;
	}

	public void setEarlyTarget(String earlyTarget) {
		this.earlyTarget = earlyTarget;
	}

	public String getEarlyCycle() {
		return earlyCycle;
	}

	public void setEarlyCycle(String earlyCycle) {
		this.earlyCycle = earlyCycle;
	}

	public EarlyWarnSet() {
		this(null);
	}
	
	public EarlyWarnSet(String id){
		super(id);
	}
	
	@Size(min=0, max=1, message="预警类型长度不能超过 1 个字符")
	public String getEarlyWarnType() {
		return earlyWarnType;
	}

	public void setEarlyWarnType(String earlyWarnType) {
		this.earlyWarnType = earlyWarnType;
	}
	
	@Size(min=0, max=1, message="用房分类长度不能超过 1 个字符")
	public String getOccupancyClassification() {
		return occupancyClassification;
	}

	public void setOccupancyClassification(String occupancyClassification) {
		this.occupancyClassification = occupancyClassification;
	}
	
	@Size(min=0, max=64, message="预警指标长度不能超过 64 个字符")
	public String getEarlyWarnIndex() {
		return earlyWarnIndex;
	}

	public void setEarlyWarnIndex(String earlyWarnIndex) {
		this.earlyWarnIndex = earlyWarnIndex;
	}
	
	public BigDecimal getStandardArea() {
		return standardArea;
	}

	public void setStandardArea(BigDecimal standardArea) {
		this.standardArea = standardArea;
	}
	
}