package com.hsobs.ob.modules.repair.web;

import java.util.List;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.hsobs.ob.modules.estate.service.RealEstateAddressService;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.repair.entity.RepairReport;
import com.hsobs.ob.modules.repair.service.RepairReportService;

/**
 * 维修规划报备Controller
 * <AUTHOR>
 * @version 2025-03-15
 */
@Controller
@RequestMapping(value = "${adminPath}/repair/report")
public class RepairReportController extends BaseController {

	@Autowired
	private RepairReportService repairReportService;

	@Autowired
	RealEstateAddressService realEstateAddressService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public RepairReport get(String id, boolean isNewRecord) {
		return repairReportService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("repair:report:view")
	@RequestMapping(value = {"list", ""})
	public String list(RepairReport repairReport, Model model) {
		model.addAttribute("repairReport", repairReport);
		return "modules/repair/repairReportList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("repair:report:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<RepairReport> listData(RepairReport repairReport, HttpServletRequest request, HttpServletResponse response) {
		repairReportService.addDataScopeFilter(repairReport);
		repairReport.setPage(new Page<>(request, response));
		Page<RepairReport> page = repairReportService.findPage(repairReport);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("repair:report:view")
	@RequestMapping(value = "form")
	public String form(RepairReport repairReport, Model model) {
		if (null == repairReport.getProjectNo() || repairReport.getProjectNo().isEmpty()) {
			Snowflake snowflake = new Snowflake(1, 1);
			repairReport.setProjectNo(String.valueOf(snowflake.nextId()));
		}
		model.addAttribute("repairReport", repairReport);
		return "modules/repair/repairReportForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("repair:report:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated RepairReport repairReport) {
		repairReportService.save(repairReport);
		return renderResult(Global.TRUE, text("保存维修规划报备成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("repair:report:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(RepairReport repairReport) {
		repairReportService.delete(repairReport);
		return renderResult(Global.TRUE, text("删除维修规划报备成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("repair:report:view")
	@RequestMapping(value = "repairReportSelect")
	public String repairReportSelect(RepairReport repairReport, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("repairReport", repairReport);
		return "modules/repair/repairReportSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("repair:report:view")
	@RequestMapping(value = "exportData")
	public void exportData(RepairReport repairReport, HttpServletResponse response) {
		List<RepairReport> list = repairReportService.findList(repairReport);
		String fileName = "维修规划报备" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("维修规划报备", RepairReport.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}