package com.hsobs.ob.modules.usevouchers.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.usevouchers.entity.UseVouchers;
import com.hsobs.ob.modules.usevouchers.dao.UseVouchersDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 使用凭证Service
 * <AUTHOR>
 * @version 2025-02-07
 */
@Service
public class UseVouchersService extends CrudService<UseVouchersDao, UseVouchers> {
	
	/**
	 * 获取单条数据
	 * @param useVouchers
	 * @return
	 */
	@Override
	public UseVouchers get(UseVouchers useVouchers) {
		return super.get(useVouchers);
	}
	
	/**
	 * 查询分页数据
	 * @param useVouchers 查询条件
	 * @param useVouchers page 分页对象
	 * @return
	 */
	@Override
	public Page<UseVouchers> findPage(UseVouchers useVouchers) {
		return super.findPage(useVouchers);
	}
	
	/**
	 * 查询列表数据
	 * @param useVouchers
	 * @return
	 */
	@Override
	public List<UseVouchers> findList(UseVouchers useVouchers) {
		return super.findList(useVouchers);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param useVouchers
	 */
	@Override
	@Transactional
	public void save(UseVouchers useVouchers) {
		super.save(useVouchers);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(useVouchers, useVouchers.getId(), "useVouchers_file");
	}
	
	/**
	 * 更新状态
	 * @param useVouchers
	 */
	@Override
	@Transactional
	public void updateStatus(UseVouchers useVouchers) {
		super.updateStatus(useVouchers);
	}
	
	/**
	 * 删除数据
	 * @param useVouchers
	 */
	@Override
	@Transactional
	public void delete(UseVouchers useVouchers) {
		super.delete(useVouchers);
	}
	
}