package com.hsobs.ob.modules.datastatistics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: OwnershipView
 * @projectName base
 * @description: 全省办公用房权属登记总数
 * @date 2024/12/249:39
 */
public class OwnershipTable  extends DataEntity<OwnershipTable> {
    private String roomInfo;    //办公用房信息
    private Integer roomNumber;    //房号及名称
    private String officeName;    //使用单位
    private String roomName;    //办公用房名称
    private Date registerTime;    //登记时间
    private Date auditTime;    //审批时间
    private String ownershipStatus;    //状态
    private String areaCode;
    private String year;

    public String getRoomInfo() {
        return roomInfo;
    }

    public void setRoomInfo(String roomInfo) {
        this.roomInfo = roomInfo;
    }

    public Integer getRoomNumber() {
        return roomNumber;
    }

    public void setRoomNumber(Integer roomNumber) {
        this.roomNumber = roomNumber;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getOwnershipStatus() {
        return ownershipStatus;
    }

    public void setOwnershipStatus(String ownershipStatus) {
        this.ownershipStatus = ownershipStatus;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }
}
