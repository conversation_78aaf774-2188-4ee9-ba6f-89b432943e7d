package com.hsobs.ob.modules.approvedconfig.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.approvedconfig.entity.AccessoryRoomsApprovedConfig;
import com.hsobs.ob.modules.approvedconfig.dao.AccessoryRoomsApprovedConfigDao;

/**
 * 附属用房面积核定配置Service
 * <AUTHOR>
 * @version 2025-03-09
 */
@Service
public class AccessoryRoomsApprovedConfigService extends CrudService<AccessoryRoomsApprovedConfigDao, AccessoryRoomsApprovedConfig> {
	
	/**
	 * 获取单条数据
	 * @param accessoryRoomsApprovedConfig
	 * @return
	 */
	@Override
	public AccessoryRoomsApprovedConfig get(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig) {
		return super.get(accessoryRoomsApprovedConfig);
	}
	
	/**
	 * 查询分页数据
	 * @param accessoryRoomsApprovedConfig 查询条件
	 * @param accessoryRoomsApprovedConfig page 分页对象
	 * @return
	 */
	@Override
	public Page<AccessoryRoomsApprovedConfig> findPage(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig) {
		return super.findPage(accessoryRoomsApprovedConfig);
	}
	
	/**
	 * 查询列表数据
	 * @param accessoryRoomsApprovedConfig
	 * @return
	 */
	@Override
	public List<AccessoryRoomsApprovedConfig> findList(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig) {
		return super.findList(accessoryRoomsApprovedConfig);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param accessoryRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void save(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig) {
		super.save(accessoryRoomsApprovedConfig);
	}

	public void initSave() {

		AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig = new AccessoryRoomsApprovedConfig();
		accessoryRoomsApprovedConfig.setId("3");
		accessoryRoomsApprovedConfig.setDiningStaffThreshold(100);
		accessoryRoomsApprovedConfig.setDiningAreaPerCapitaBase(3.7);
		accessoryRoomsApprovedConfig.setDiningExtraThreshold(100);
		accessoryRoomsApprovedConfig.setDiningAreaPerCapitaExtra(2.6);

		accessoryRoomsApprovedConfig.setCarParkBaseArea(40.);
		accessoryRoomsApprovedConfig.setCarParkExtraThreshold(200);
		accessoryRoomsApprovedConfig.setCarParkExtraArea(38.);
		accessoryRoomsApprovedConfig.setBicycleParkArea(1.8);
		accessoryRoomsApprovedConfig.setEmotorcycleParkArea(2.5);

		accessoryRoomsApprovedConfig.setSecurityRoomAreaPerCapita(25.);

		super.insert(accessoryRoomsApprovedConfig);
	}
	
	/**
	 * 更新状态
	 * @param accessoryRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void updateStatus(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig) {
		super.updateStatus(accessoryRoomsApprovedConfig);
	}
	
	/**
	 * 删除数据
	 * @param accessoryRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void delete(AccessoryRoomsApprovedConfig accessoryRoomsApprovedConfig) {
		super.delete(accessoryRoomsApprovedConfig);
	}
	
}