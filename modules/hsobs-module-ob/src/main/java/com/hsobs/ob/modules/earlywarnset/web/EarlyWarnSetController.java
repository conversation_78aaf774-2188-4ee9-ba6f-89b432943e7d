package com.hsobs.ob.modules.earlywarnset.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.earlywarnset.entity.EarlyWarnSet;
import com.hsobs.ob.modules.earlywarnset.service.EarlyWarnSetService;

/**
 * 预警设置Controller
 * <AUTHOR>
 * @version 2024-12-01
 */
@Controller
@RequestMapping(value = "${adminPath}/earlywarnset/")
public class EarlyWarnSetController extends BaseController {

	@Autowired
	private EarlyWarnSetService earlyWarnSetService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public EarlyWarnSet get(String id, boolean isNewRecord) {
		return earlyWarnSetService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("earlywarnset::view")
	@RequestMapping(value = {"list", ""})
	public String list(EarlyWarnSet earlyWarnSet, Model model) {
		model.addAttribute("earlyWarnSet", earlyWarnSet);
		return "modules/earlywarnset/earlyWarnSetList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("earlywarnset::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<EarlyWarnSet> listData(EarlyWarnSet earlyWarnSet, HttpServletRequest request, HttpServletResponse response) {
		earlyWarnSet.setPage(new Page<>(request, response));
		Page<EarlyWarnSet> page = earlyWarnSetService.findPage(earlyWarnSet);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("earlywarnset::view")
	@RequestMapping(value = "form")
	public String form(EarlyWarnSet earlyWarnSet, Model model) {
		model.addAttribute("earlyWarnSet", earlyWarnSet);
		return "modules/earlywarnset/earlyWarnSetForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("earlywarnset::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated EarlyWarnSet earlyWarnSet) {
		earlyWarnSetService.save(earlyWarnSet);
		return renderResult(Global.TRUE, text("保存预警设置成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("earlywarnset::edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(EarlyWarnSet earlyWarnSet) {
		earlyWarnSet.setStatus(EarlyWarnSet.STATUS_DISABLE);
		earlyWarnSetService.updateStatus(earlyWarnSet);
		return renderResult(Global.TRUE, text("停用预警设置成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("earlywarnset::edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(EarlyWarnSet earlyWarnSet) {
		earlyWarnSet.setStatus(EarlyWarnSet.STATUS_NORMAL);
		earlyWarnSetService.updateStatus(earlyWarnSet);
		return renderResult(Global.TRUE, text("启用预警设置成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("earlywarnset::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(EarlyWarnSet earlyWarnSet) {
		earlyWarnSetService.delete(earlyWarnSet);
		return renderResult(Global.TRUE, text("删除预警设置成功！"));
	}
	
}