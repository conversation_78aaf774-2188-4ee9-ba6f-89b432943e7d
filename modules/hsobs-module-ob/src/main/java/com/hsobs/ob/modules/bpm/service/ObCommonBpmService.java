package com.hsobs.ob.modules.bpm.service;

import com.hsobs.ob.modules.bpm.builder.ColumnMappingBuilder;
import com.hsobs.ob.modules.bpm.dao.ObCommonBpmDao;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.reflect.ReflectUtils;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Role;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ObCommonBpmService extends BaseService {

    @Autowired
    private ObCommonBpmDao commonBpmDao;

    /**
     * 查找业务列表-业务对象不重复
     */
    public <T extends BpmEntity<?>> Page<T> findObjectList(
            String[] taskNames,
            String businessKeyLike,
            T bpmEntity,
            String bpmStatus) {
        validateParams(businessKeyLike);
        Map<String, Object> params = prepareQueryParams(taskNames, businessKeyLike, bpmEntity, bpmStatus);
        List<Map<String, Object>> mapList = commonBpmDao.findAllObjecList(params);
        return convertToPage(mapList, bpmEntity, params);
    }

    /**
     * 查找任务列表
     */
    public <T extends BpmEntity<?>> Page<T> findTaskList(
            String[] taskNames,
            String businessKeyLike,
            T bpmEntity,
            String bpmStatus) {
        validateParams(businessKeyLike);
        Map<String, Object> params = prepareQueryParams(taskNames, businessKeyLike, bpmEntity, bpmStatus);
        List<Map<String, Object>> mapList = commonBpmDao.findAllTaskList(params);
        return convertToPage(mapList, bpmEntity, params);
    }

    public <T extends BpmEntity<?>> List<T> findTaskListNoPage(
            String[] taskNames,
            String businessKeyLike,
            T bpmEntity,
            String bpmStatus) {
        validateParams(businessKeyLike);
        Map<String, Object> params = prepareQueryParams(taskNames, businessKeyLike, bpmEntity, bpmStatus);
        List<Map<String, Object>> mapList = commonBpmDao.findAllTaskList(params);
        return convertToEntityList(mapList, bpmEntity);
    }

    private void validateParams(String businessKeyLike) {
        if (businessKeyLike == null) {
            throw new ServiceException("formKey不能为空");
        }
    }

    /**
     * 准备查询参数
     */
    private <T extends BpmEntity<?>> Map<String, Object> prepareQueryParams(
            String[] taskNames, String businessKeyLike, T bpmEntity, String bpmStatus) {
        Map<String, Object> params = new HashMap<>();

        initializeEntity(bpmEntity);
        handleEntityProperties(bpmEntity, params, null);
        addWorkflowParams(params, taskNames, businessKeyLike, bpmStatus, bpmEntity);
        handleBusinessTable(bpmEntity, params);

        return params;
    }

    private void initializeEntity(BpmEntity<?> bpmEntity) {
        if (bpmEntity.sqlMap() != null) {
            bpmEntity.sqlMap().getWhere().disableAutoAddStatusWhere();
        }
        if (StringUtils.isNotEmpty(bpmEntity.getIdq())){
            bpmEntity.setId(bpmEntity.getIdq());
        }
    }

    /**
     * 添加工作流相关参数
     */
    private void addWorkflowParams(Map<String, Object> params, String[] taskNames,
            String businessKeyLike, String bpmStatus, BpmEntity<?> bpmEntity) {
        if (StringUtils.isBlank(bpmEntity.getProcessName())) {
            params.put("taskNames", taskNames);
        } else {
            params.put("processName", bpmEntity.getProcessName());
        }
        params.put("businessKeyLike", businessKeyLike + ":%");

        // 用户相关参数
        String userCode = UserUtils.getUser().getUserCode();
        params.put("assignee", userCode);
        params.put("candidateUser", userCode);
        params.put("candidateGroups", getUserRoleCodes());

        // 其他参数
        params.put("tenantId", "0");
        params.put("bpmStatus", StringUtils.isBlank(bpmStatus) ? "3" : bpmStatus);
    }

    private String[] getUserRoleCodes() {
        return UserUtils.getUser().getRoleList().stream()
                .map(Role::getRoleCode)
                .filter(StringUtils::isNotBlank)
                .toArray(String[]::new);
    }

    /**
     * 处理业务表关联
     */
    private void handleBusinessTable(BpmEntity<?> bpmEntity, Map<String, Object> params) {
        Table tableAnn = bpmEntity.getClass().getAnnotation(Table.class);
        if (tableAnn != null) {
            params.put("businessTable", generateTableJoins(tableAnn));
            params.put("columns", ColumnMappingBuilder.generate(tableAnn));
        }

        handleWhereCondition(bpmEntity, params);
    }

    private void handleWhereCondition(BpmEntity<?> bpmEntity, Map<String, Object> params) {
//        if (bpmEntity.sqlMap() != null && bpmEntity.sqlMap().getWhere() != null) {
//            QueryWhere where = bpmEntity.sqlMap().getWhere();
//
//            // 将 sqlMap 添加到参数中
//            params.put("sqlMap", bpmEntity.sqlMap());
//
//            // 手动构建 SQL 条件
//            StringBuilder whereSql = new StringBuilder();
//            for (Map.Entry<String, Object> entry : where.entrySet()) {
//                String key = entry.getKey();
//                Object value = entry.getValue();
//
//                if (value instanceof QueryWhereEntity) {
//                    QueryWhereEntity whereEntity = (QueryWhereEntity) value;
//
//                    // 从 key 中提取字段名和操作符
//                    String[] parts = key.split("#");
//                    if (parts.length >= 3) {
//                        String fieldName = parts[0] + "." + parts[1];
//                        String operator = parts[2].substring(0, 2); // EQ, GT, LT 等
//
//                        // 构建条件
//                        if (whereSql.length() > 0) {
//                            whereSql.append(" AND ");
//                        }
//
//                        // 根据操作符类型生成对应的 SQL
//                        String operatorSql = getOperatorSql(operator);
//                        whereSql.append(fieldName)
//                                .append(" ")
//                                .append(operatorSql)
//                                .append(" #{sqlMap.where.")
//                                .append(key)
//                                .append(".val}");
//
//                        // 添加参数值
//                        String paramKey = "sqlMap.where." + key + ".val";
//                        params.put(paramKey, whereEntity.getValue());
//                    }
//                }
//            }
//
//            // 将 SQL 条件添加到参数中
//            if (StringUtils.isNotBlank(whereSql.toString())) {
//                params.put("bizWhere", whereSql.toString());
//            }
//
//        }

//         将 SQL 条件添加到参数中
            if (StringUtils.isNotBlank(bpmEntity.sqlMap().getWhere().toSql())) {
                params.put("bizWhere", bpmEntity.sqlMap().getWhere().toSql());
            }
        if (bpmEntity.getPage()!=null && bpmEntity.getPage().getOrderBy() != null) {
            params.put("bizOrder", bpmEntity.getPage().getOrderBy());
        }
    }

    /**
     * 根据操作符类型返回对应的 SQL 操作符
     */
    private String getOperatorSql(String operator) {
        switch (operator) {
            case "EQ":
                return "=";
            case "NE":
                return "!=";
            case "GT":
                return ">";
            case "GE":
                return ">=";
            case "LT":
                return "<";
            case "LE":
                return "<=";
            case "LK":
                return "LIKE";
            case "NL":
                return "NOT LIKE";
            case "IN":
                return "IN";
            case "NI":
                return "NOT IN";
            case "LI":
                return "LIKE";
            default:
                return "=";
        }
    }

    /**
     * 生成表连接SQL
     */
    private String generateTableJoins(Table tableAnn) {
        StringBuilder tableJoin = new StringBuilder();

        // 添加主表
        appendMainTable(tableJoin, tableAnn);

        // 添加关联表
        for (JoinTable joinTable : tableAnn.joinTable()) {
            appendJoinTable(tableJoin, joinTable);
        }

        return tableJoin.toString().replace("${_prefix}", "js_");
    }

    private void appendMainTable(StringBuilder tableJoin, Table tableAnn) {
        tableJoin.append(tableAnn.name())
                .append(" ")
                .append(tableAnn.alias())
                .append(" ON ")
                .append(tableAnn.alias())
                .append(".id = bpmt.BUSINESS_KEY_");
    }

    private void appendJoinTable(StringBuilder tableJoin, JoinTable joinTable) {
        tableJoin.append(" ")
                .append(joinTable.type().name().replace("_", " "))
                .append(" ")
                .append(joinTable.entity().getAnnotation(Table.class).name())
                .append(" ")
                .append(joinTable.alias())
                .append(" ON ")
                .append(joinTable.on());
    }

    /**
     * 将Map列表转换为实体对象列表
     */
    @SuppressWarnings("unchecked")
    private <T extends BpmEntity<?>> List<T> convertToEntityList(List<Map<String, Object>> mapList, T template) {
        if (mapList == null || mapList.isEmpty()) {
            return new ArrayList<>();
        }

        List<T> resultList = new ArrayList<>(mapList.size());
        Class<T> entityClass = (Class<T>) template.getClass();

        for (Map<String, Object> map : mapList) {
            try {
                T entity = entityClass.getDeclaredConstructor().newInstance();
                populateEntity(entity, map);
                resultList.add(entity);
            } catch (Exception e) {
                logger.error("Error converting map to entity", e);
            }
        }

        return resultList;
    }

    @SuppressWarnings("unchecked")
    private <T extends BpmEntity<?>> Page<T> convertToPage(
            List<Map<String, Object>> mapList, T bpmEntity, Map<String, Object> params) {
        Page<T> page = (Page<T>) params.get("page");
        page.setList(convertToEntityList(mapList, bpmEntity));
        return page;
    }

    private void populateEntity(Object entity, Map<String, Object> map) {
        setWorkflowProperties(entity, map);
        setEntityProperties(entity, map);
    }

    /**
     * 设置工作流相关属性
     */
    private void setWorkflowProperties(Object bean, Map<String, Object> map) {
        try {
            setBasicWorkflowInfo(bean, map);
            setWorkflowTimeInfo(bean, map);
            setFormKey(bean, map);
        } catch (Exception e) {
            logger.error("Error setting workflow properties", e);
        }
    }

    private void setBasicWorkflowInfo(Object bean, Map<String, Object> map) {
        setProperty(bean, "taskId", map.get("taskId"));
        setProperty(bean, "taskName", map.get("taskName"));
        setProperty(bean, "processName", map.get("taskName"));
        setProperty(bean, "flowStatus", map.get("taskName"));
        setProperty(bean, "applyTitle", map.get("procInsName"));
        setProperty(bean, "bpmStatus", map.get("taskStatus"));
    }

    private void setWorkflowTimeInfo(Object bean, Map<String, Object> map) {
        setDueDate(bean, map);
        setStartDate(bean, map);
    }

    private void setDueDate(Object bean, Map<String, Object> map) {
        String taskDueDate = MapUtils.getString(map, "taskDueDate");
        if (taskDueDate != null) {
            setFormattedDate(bean, "taskDueDate", taskDueDate);
        }
    }

    private void setStartDate(Object bean, Map<String, Object> map) {
        String claimTime = MapUtils.getString(map, "taskClaimTime");
        String createTime = MapUtils.getString(map, "taskCreateTime");
        String startTime = claimTime != null ? claimTime : createTime;
        if (startTime != null) {
            setFormattedDate(bean, "taskStartDate", startTime);
        }
    }

    private void setFormKey(Object bean, Map<String, Object> map) {
        setProperty(bean, "formKey", map.get("formKey"));
    }

    /**
     * 设置实体属性
     */
    private void setEntityProperties(Object entity, Map<String, Object> map) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value != null) {
                if (key.contains("_")) {
                    setNestedProperty(entity, key, value);
                } else {
                    setProperty(entity, key, value);
                }
            }
        }
    }

    /**
     * 设置嵌套属性值
     */
    private void setNestedProperty(Object bean, String propertyPath, Object value) {
        try {
            String[] properties = propertyPath.split("_");
            Object current = bean;

            // 遍历属性路径，直到最后一个属性
            for (int i = 0; i < properties.length - 1; i++) {
                String propertyName = properties[i];
                Object nestedObject = ReflectUtils.invokeGetter(current, propertyName);

                // 如果嵌套对象为空，创建一个新实例
                if (nestedObject == null) {
                    Field field = getField(current.getClass(), propertyName);
                    if (field != null) {
                        Class<?> propertyType = field.getType();
                        nestedObject = propertyType.getDeclaredConstructor().newInstance();
                        ReflectUtils.invokeSetter(current, propertyName, nestedObject);
                    }
                }

                current = nestedObject;
            }

            // 设置最后一个属性的值
            if (current != null) {
                String lastProperty = properties[properties.length - 1];
                ReflectUtils.invokeSetter(current, lastProperty, value);
            }
        } catch (Exception e) {
            logger.debug("Error setting nested property: " + propertyPath, e);
        }
    }

    /**
     * 递归处理实体属性到Map
     */
    private void handleEntityProperties(Object entity, Map<String, Object> params, String prefix) {
        if (entity == null)
            return;

        List<Field> fields = getAllFields(entity.getClass());
        for (Field field : fields) {
            processEntityField(entity, field, params, prefix);
        }
    }

    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            fields.addAll(Arrays.asList(currentClass.getDeclaredFields()));
            currentClass = currentClass.getSuperclass();
        }
        return fields;
    }

    private void processEntityField(Object entity, Field field, Map<String, Object> params, String prefix) {
        field.setAccessible(true);
        try {
            Object value = field.get(entity);
            if (value != null) {
                String key = prefix != null ? prefix + "." + field.getName() : field.getName();
                if (value instanceof Map || !isSimpleType(value.getClass())) {
                    params.put(key, value);
                }
            }
        } catch (Exception e) {
            logger.debug("Error handling property: " + field.getName(), e);
        }
    }

    /**
     * 获取字段（包括父类字段）
     */
    private Field getField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                Field field = currentClass.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field;
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    private void setProperty(Object bean, String propertyName, Object value) {
        try {
            ReflectUtils.invokeSetter(bean, propertyName, value);
        } catch (Exception e) {
            logger.debug("Error setting property: " + propertyName);
        }
    }

    private void setFormattedDate(Object bean, String propertyName, String dateStr) {
        try {
            String formattedDate = DateUtils.formatDate(
                    DateUtils.parseDate(dateStr), "yyyy-MM-dd HH:mm:ss");
            setProperty(bean, propertyName, formattedDate);
        } catch (Exception e) {
            logger.error("Error formatting date: " + dateStr, e);
        }
    }

    /**
     * 判断是否为简单类型
     */
    private boolean isSimpleType(Class<?> clazz) {
        return clazz.isPrimitive()
                || clazz == String.class
                || Number.class.isAssignableFrom(clazz)
                || Date.class.isAssignableFrom(clazz)
                || Boolean.class == clazz;
    }
}
