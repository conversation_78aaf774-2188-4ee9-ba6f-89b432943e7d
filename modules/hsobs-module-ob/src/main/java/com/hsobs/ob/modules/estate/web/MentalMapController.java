package com.hsobs.ob.modules.estate.web;

import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.jeesite.common.config.Global;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping(value = "${adminPath}/estate/MentalMap")
public class MentalMapController extends BaseController {

    /**
     * 查询列表
     */
    @RequiresPermissions("estate:mentalMap:view")
    @RequestMapping(value = {"index", ""})
    public String list(RealEstateAddress realEstateAddress, Model model) {
        model.addAttribute("realEstateAddress", realEstateAddress);
        return "modules/estate/mentalMap";
    }
}
