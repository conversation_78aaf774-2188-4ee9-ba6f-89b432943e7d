package com.hsobs.ob.modules.arrange.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

/**
 * 配置管理Entity
 * <AUTHOR>
 * @version 2025-01-05
 */
public class ArrangeLedger extends BpmEntity<ArrangeLedger> {

	private static final long serialVersionUID = 1L;
	private String officeName;		// 配置单位
	private String officeCode;		// 配置单位
	private String arrangeType;		// 配置类型
	private String arrangeNumber;		// 配置数量
	private String arrangeTotalArea;		// 配置总面积
	@ExcelFields({
			@ExcelField(title="配置单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="配置类型", attrName="arrangeType", dictType="ob_repair_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="配置数量", attrName="arrangeNumber", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="配置总面积", attrName="arrangeTotalArea", align= ExcelField.Align.CENTER, sort=50),
	})
	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getArrangeType() {
		return arrangeType;
	}

	public void setArrangeType(String arrangeType) {
		this.arrangeType = arrangeType;
	}

	public String getArrangeNumber() {
		return arrangeNumber;
	}

	public void setArrangeNumber(String arrangeNumber) {
		this.arrangeNumber = arrangeNumber;
	}

	public String getArrangeTotalArea() {
		return arrangeTotalArea;
	}

	public void setArrangeTotalArea(String arrangeTotalArea) {
		this.arrangeTotalArea = arrangeTotalArea;
	}
}