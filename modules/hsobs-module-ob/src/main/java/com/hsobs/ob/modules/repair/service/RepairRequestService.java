package com.hsobs.ob.modules.repair.service;

import java.util.List;

import com.hsobs.ob.modules.repair.entity.RepairRequestLedger;
import com.hsobs.ob.modules.repair.entity.RepairRequestQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.repair.entity.RepairRequest;
import com.hsobs.ob.modules.repair.dao.RepairRequestDao;
import java.util.Map;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 维修申请Service
 * <AUTHOR>
 * @version 2025-02-06
 */
@Service
public class RepairRequestService extends CrudService<RepairRequestDao, RepairRequest> {

	@Autowired
	private RepairRequestDao repairRequestDao;
	/**
	 * 获取单条数据
	 * @param repairRequest
	 * @return
	 */
	@Override
	public RepairRequest get(RepairRequest repairRequest) {
		return super.get(repairRequest);
	}

	/**
	 * 查询分页数据
	 * @param repairRequest 查询条件
	 * @param repairRequest page 分页对象
	 * @return
	 */
	@Override
	public Page<RepairRequest> findPage(RepairRequest repairRequest) {
		return super.findPage(repairRequest);
	}

	/**
	 * 查询列表数据
	 * @param repairRequest
	 * @return
	 */
	@Override
	public List<RepairRequest> findList(RepairRequest repairRequest) {
		return super.findList(repairRequest);
	}

	/**
	 * 保存数据（插入或更新）
	 * @param repairRequest
	 */
	@Override
	@Transactional
	public void save(RepairRequest repairRequest) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(repairRequest.getStatus())){
			repairRequest.setStatus(RepairRequest.STATUS_AUDIT);
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (RepairRequest.STATUS_NORMAL.equals(repairRequest.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (RepairRequest.STATUS_DRAFT.equals(repairRequest.getStatus())
				|| RepairRequest.STATUS_AUDIT.equals(repairRequest.getStatus())){
			super.save(repairRequest);
		}

		// 如果为审核状态，则进行审批流操作
		if (RepairRequest.STATUS_AUDIT.equals(repairRequest.getStatus())){

			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			variables.put("fundsSource", repairRequest.getFundsSource());
			variables.put("budget", repairRequest.getBudget());

			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(repairRequest.getBpm().getProcInsId())
					&& StringUtils.isBlank(repairRequest.getBpm().getTaskId())){
				BpmUtils.start(repairRequest, "ob_repair", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(repairRequest, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(repairRequest, repairRequest.getId(), "repairRequest_file");
		FileUploadUtils.saveFileUpload(repairRequest, repairRequest.getId(), "repairRequest_application_letter_file");
		FileUploadUtils.saveFileUpload(repairRequest, repairRequest.getId(), "repairRequest_plan_file");
	}

	/**
	 * 更新状态
	 * @param repairRequest
	 */
	@Override
	@Transactional
	public void updateStatus(RepairRequest repairRequest) {
		super.updateStatus(repairRequest);
	}

	/**
	 * 删除数据
	 * @param repairRequest
	 */
	@Override
	@Transactional
	public void delete(RepairRequest repairRequest) {
		repairRequestDao.phyDelete(repairRequest);
	}

	public Page<RepairRequestLedger> listLedgerData(RepairRequestLedger repairRequestLedger) {
		Page<RepairRequestLedger> page = (Page<RepairRequestLedger>) repairRequestLedger.getPage();
		List<RepairRequestLedger> list = repairRequestDao.listLedger(repairRequestLedger);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<RepairRequestLedger> listLedger(RepairRequestLedger repairRequestLedger) {
		return repairRequestDao.listLedger(repairRequestLedger);
	}

	public Page<RepairRequestQuery> listQueryData(RepairRequestQuery repairRequestQuery) {
		Page<RepairRequestQuery> page = (Page<RepairRequestQuery>) repairRequestQuery.getPage();
		List<RepairRequestQuery> list = repairRequestDao.listQueryData(repairRequestQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<RepairRequestQuery> listQuery(RepairRequestQuery repairRequestQuery) {
		return repairRequestDao.listQueryData(repairRequestQuery);
	}
}