package com.hsobs.ob.modules.apply.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

import java.util.Date;
import java.util.List;

/**
 * 使用管理Entity
 * <AUTHOR>
 * @version 2025-02-08
 */
@Table(name="ob_apply", alias="a", label="ob_apply信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="name", attrName="name", label="申请名称"),
		@Column(name="describe", attrName="describe", label="说明"),
		@Column(name="paid", attrName="paid", label="是否有偿"),
		@Column(name="rent", attrName="rent", label="租金"),
		@Column(name="real_estate_type", attrName="realEstateType", label="不动产类型"),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用机构ID"),
		@Column(name="used_user_code", attrName="usedUserCode", label="使用人ID"),
		@Column(name="area", attrName="area", label="面积"),
		@Column(name="apply_date", attrName="applyDate", label="使用时间"),
		@Column(name="purpose", attrName="purpose", label="用途"),
		@Column(name="position", attrName="position", label="位置"),
		@Column(name="room_num", attrName="roomNum", label="房间数"),
		@Column(
				name = "status",
				attrName = "status",
				label = "状态",
				isUpdate = true,
				isQuery = false,
				comment = "（推荐状态：0：正常；1：删除；2：停用；3：冻结；4：审核、待审核；5：审核驳回；9：草稿）"
		),
		@Column(includeEntity=DataEntity.class),
}, joinTable={
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "usedOffice",
				on = "a.used_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u2",
				attrName = "usedUser",
				on = "a.used_user_code = u2.user_code",
				columns = {@Column(includeEntity = User.class)})
}, orderBy="a.update_date DESC"
)
public class Apply extends BpmEntity<Apply> {

	private static final long serialVersionUID = 1L;
	private String name;		// 说明
	private String describe;		// 说明
	private String paid;		// 是否有偿
	private String rent;		// 租金
	private String usedOfficeCode;
	private String usedUserCode;
	private String realEstateType;		// 不动产类型
	private Double area; // 面积
	private Date applyDate; // 面积
	private String purpose; // 用途

	// modify by 20250512
	private String position;
	private Integer roomNum;

	private Office usedOffice;
	private User usedUser;

	private List<ApplyRealEstate> applyRealEstateList = ListUtils.newArrayList();

	public Apply() {
		this(null);
	}

	public Apply(String id){
		super(id);
	}

	@Size(min=0, max=512, message="名称长度不能超过 512 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Size(min=0, max=1000, message="说明长度不能超过 1000 个字符")
	public String getDescribe() {
		return describe;
	}

	public void setDescribe(String describe) {
		this.describe = describe;
	}

	@Size(min=0, max=1, message="是否有偿长度不能超过 1 个字符")
	public String getPaid() {
		return paid;
	}

	public void setPaid(String paid) {
		this.paid = paid;
	}

	@Size(min=0, max=100, message="租金长度不能超过 100 个字符")
	public String getRent() {
		return rent;
	}

	public void setRent(String rent) {
		this.rent = rent;
	}

	public String getRealEstateType() {
		return realEstateType;
	}

	public void setRealEstateType(String realEstateType) {
		this.realEstateType = realEstateType;
	}

	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}

	public String getUsedUserCode() {
		return usedUserCode;
	}

	public void setUsedUserCode(String usedUserCode) {
		this.usedUserCode = usedUserCode;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}

	public User getUsedUser() {
		return usedUser;
	}

	public void setUsedUser(User usedUser) {
		this.usedUser = usedUser;
	}

	public List<ApplyRealEstate> getApplyRealEstateList() {
		return applyRealEstateList;
	}

	public void setApplyRealEstateList(List<ApplyRealEstate> applyRealEstateList) {
		this.applyRealEstateList = applyRealEstateList;
	}

	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	@Size(min=0, max=256, message="位置长度不能超过 256 个字符")
	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public Integer getRoomNum() {
		return roomNum;
	}

	public void setRoomNum(Integer roomNum) {
		this.roomNum = roomNum;
	}
}