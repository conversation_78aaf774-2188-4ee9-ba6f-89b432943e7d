package com.hsobs.ob.modules.ownership.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.ob.modules.ownership.entity.OwnershipRealEstate;
import com.hsobs.ob.modules.ownership.entity.OwnershipRegistrationQuery;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.ownership.entity.OwnershipRegistration;
import com.hsobs.ob.modules.ownership.service.OwnershipRegistrationService;

/**
 * 权属登记Controller
 * <AUTHOR>
 * @version 2025-02-05
 */
@Controller
@RequestMapping(value = "${adminPath}/ownership/registration")
public class OwnershipRegistrationController extends BaseController {

	@Autowired
	private OwnershipRegistrationService ownershipRegistrationService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public OwnershipRegistration get(String id, boolean isNewRecord) {
		return ownershipRegistrationService.get(id, isNewRecord);
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("ownership:registration:view")
	@RequestMapping(value = {"list", ""})
	public String list(OwnershipRegistration ownershipRegistration, Model model) {
		model.addAttribute("ownershipRegistration", ownershipRegistration);
		return "modules/ownership/ownershipRegistrationList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("ownership:registration:view")
	@RequestMapping(value = {"listQuery", ""})
	public String listQuery(OwnershipRegistrationQuery ownershipRegistrationQuery, Model model) {
		model.addAttribute("ownershipRegistrationQuery", ownershipRegistrationQuery);
		return "modules/ownership/ownershipRegistrationListQuery";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("ownership:registration:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<OwnershipRegistration> listData(OwnershipRegistration ownershipRegistration, HttpServletRequest request, HttpServletResponse response) {
		ownershipRegistration.setPage(new Page<>(request, response));
		Page<OwnershipRegistration> page = ownershipRegistrationService.findPage(ownershipRegistration);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("ownership:registration:view")
	@RequestMapping(value = "listQueryData")
	@ResponseBody
	public Page<OwnershipRegistrationQuery> listQueryData(OwnershipRegistrationQuery ownershipRegistrationQuery, HttpServletRequest request, HttpServletResponse response) {
		ownershipRegistrationQuery.setPage(new Page<>(request, response));
		ownershipRegistrationService.addDataScopeFilter(ownershipRegistrationQuery);
		Page<OwnershipRegistrationQuery> page = ownershipRegistrationService.listQueryData(ownershipRegistrationQuery);
		return page;
	}

	/**
	 * 查询子表数据
	 */
	@RequiresPermissions("ownership:registration:view")
	@RequestMapping(value = "realEstateListData")
	@ResponseBody
	public Page<OwnershipRealEstate> subListData(OwnershipRealEstate ownershipRealEstate, HttpServletRequest request, HttpServletResponse response) {
		ownershipRealEstate.setPage(new Page<>(request, response));
		Page<OwnershipRealEstate> page = ownershipRegistrationService.findSubPage(ownershipRealEstate);
		return page;
	}
	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("ownership:registration:view")
	@RequestMapping(value = "form")
	public String form(OwnershipRegistration ownershipRegistration, Model model) {
		ownershipRegistrationService.loadChildData(ownershipRegistration);
		boolean commonReadonly= null != ownershipRegistration.getStatus() && !ownershipRegistration.getStatus().equals("9");
        model.addAttribute("commonReadonly", commonReadonly);
		model.addAttribute("ownershipRegistration", ownershipRegistration);
		return "modules/ownership/ownershipRegistrationForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("ownership:registration:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated OwnershipRegistration ownershipRegistration) {
		ownershipRegistrationService.save(ownershipRegistration);
		return renderResult(Global.TRUE, text("保存权属登记成功！"));
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("ownership:registration:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(OwnershipRegistration ownershipRegistration) {
		if (!OwnershipRegistration.STATUS_DRAFT.equals(ownershipRegistration.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		ownershipRegistrationService.delete(ownershipRegistration);
		return renderResult(Global.TRUE, text("删除权属登记成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportData")
	public void exportData(OwnershipRegistrationQuery ownershipRegistrationQuery, HttpServletResponse response) {
		List<OwnershipRegistrationQuery> list = ownershipRegistrationService.listQuery(ownershipRegistrationQuery);
		String fileName = "权属信息查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("权属信息查询", OwnershipRegistrationQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("ownership:registration:view")
	@RequestMapping(value = "exportListData")
	public void exportListData(OwnershipRegistration ownershipRegistration, HttpServletResponse response) {
		List<OwnershipRegistration> list = ownershipRegistrationService.findList(ownershipRegistration);
		String fileName = "权属登记台账" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("权属登记台账", OwnershipRegistration.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

}