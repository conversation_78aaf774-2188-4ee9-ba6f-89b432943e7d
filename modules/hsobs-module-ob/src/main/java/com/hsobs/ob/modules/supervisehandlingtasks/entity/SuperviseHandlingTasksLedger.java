package com.hsobs.ob.modules.supervisehandlingtasks.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 监督督办任务Entity
 * <AUTHOR>
 * @version 2024-12-10
 */
public class SuperviseHandlingTasksLedger extends BpmEntity<SuperviseHandlingTasksLedger> {

	private static final long serialVersionUID = 1L;
	private String officeCode;		//抽查单位
	private String officeName;		//抽查单位
	private String businessType;		// 业务类型
	private String officeNumber;		// 使用办公用房数量
	private String SpotNumber;		// 抽查数
	private String questionNumber;		// 问题数
	private String correctedNumber;		// 已整改数
	private String uncorrectedNumber;		// 未整改数

	@ExcelFields({
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="抽查单位", attrName="officeName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=51),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="业务类型", attrName="businessType", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=60, dataFormat="yyyy-MM-dd hh:mm"),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="使用办公用房数量", attrName="officeNumber", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=70, dataFormat="yyyy-MM-dd hh:mm"),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="抽查数", attrName="SpotNumber", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=80),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="问题数", attrName="questionNumber", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=90),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="已整改数", attrName="correctedNumber", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=100),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="未整改数", attrName="uncorrectedNumber", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=110),
	})

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getOfficeNumber() {
		return officeNumber;
	}

	public void setOfficeNumber(String officeNumber) {
		this.officeNumber = officeNumber;
	}

	public String getSpotNumber() {
		return SpotNumber;
	}

	public void setSpotNumber(String spotNumber) {
		SpotNumber = spotNumber;
	}

	public String getQuestionNumber() {
		return questionNumber;
	}

	public void setQuestionNumber(String questionNumber) {
		this.questionNumber = questionNumber;
	}

	public String getCorrectedNumber() {
		return correctedNumber;
	}

	public void setCorrectedNumber(String correctedNumber) {
		this.correctedNumber = correctedNumber;
	}

	public String getUncorrectedNumber() {
		return uncorrectedNumber;
	}

	public void setUncorrectedNumber(String uncorrectedNumber) {
		this.uncorrectedNumber = uncorrectedNumber;
	}
}