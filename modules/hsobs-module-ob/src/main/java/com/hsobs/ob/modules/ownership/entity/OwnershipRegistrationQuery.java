package com.hsobs.ob.modules.ownership.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

/**
 * 权属登记Entity
 * <AUTHOR>
 * @version 2025-02-05
 */
public class OwnershipRegistrationQuery extends BpmEntity<OwnershipRegistrationQuery> {

	private static final long serialVersionUID = 1L;
	private String applyDate;		// 申请日期
	private String officeName;		// 权属单位
	private String officeCode;		// 权属单位
	private String officeRoomName;		// 办公用房名称
	private String title;		// 标题
	private String purpose;		// 用途
	private String ownershipStatus;		// 权属状态
	private String approvalStatus;		// 审批状态
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}

	@ExcelFields({
			@ExcelField(title="申请日期", attrName="applyDate", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="权属单位", attrName="officeName", dictType="ob_ownership_registration_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="办公用房名称", attrName="officeRoomName", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="标题", attrName="title", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="用途", attrName="purpose", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="权属状态", attrName="ownershipStatus", dictType="ob_ownership_registration_type", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="审批状态", attrName="approvalStatus", dictType="sys_office_type", align= ExcelField.Align.CENTER, sort=50),
	})
	public String getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getOfficeRoomName() {
		return officeRoomName;
	}

	public void setOfficeRoomName(String officeRoomName) {
		this.officeRoomName = officeRoomName;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	public String getOwnershipStatus() {
		return ownershipStatus;
	}

	public void setOwnershipStatus(String ownershipStatus) {
		this.ownershipStatus = ownershipStatus;
	}

	public String getApprovalStatus() {
		return approvalStatus;
	}

	public void setApprovalStatus(String approvalStatus) {
		this.approvalStatus = approvalStatus;
	}
}