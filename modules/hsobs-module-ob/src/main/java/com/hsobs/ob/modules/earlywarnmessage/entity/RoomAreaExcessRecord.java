package com.hsobs.ob.modules.earlywarnmessage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 房间面积超标查询Entity
 * <AUTHOR>
 * @version 2024-12-02
 */
public class RoomAreaExcessRecord extends DataEntity<RoomAreaExcessRecord> {

	private String userName;          // 人员名称
	private String userCode;          // 人员名称
	private String officeName;           // 单位名称
	private String officeCode;           // 单位名称
	private String rank;               // 职级
	private String officeType;         // 办公用房类型
	private Double approvedArea;       // 核定面积
	private Double sharedArea;         // 分摊使用面积
	private Double excessArea;         // 超标面积
	private Double excessRatio;        // 超标比例
	private String needRectification; // 是否需要整改

	@ExcelFields({
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="人员名称", attrName="userName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=10),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="单位名称", attrName="officeName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=20),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="职级", attrName="rank", dictType="ob_establishment_type", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=30),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="办公用房类型", attrName="officeType", dictType="occupancy_classification", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=40),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="核定面积", attrName="approvedArea", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=50),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="分摊使用面积", attrName="sharedArea", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=60),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="超标面积", attrName="excessArea", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=70),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="超标比例", attrName="excessRatio", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=80, dataFormat="yyyy-MM-dd hh:mm"),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="是否需要整改", attrName="needRectification", dictType="sys_yes_no", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=90),
	})

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getRank() {
		return rank;
	}

	public void setRank(String rank) {
		this.rank = rank;
	}

	public String getOfficeType() {
		return officeType;
	}

	public void setOfficeType(String officeType) {
		this.officeType = officeType;
	}

	public Double getApprovedArea() {
		return approvedArea;
	}

	public void setApprovedArea(Double approvedArea) {
		this.approvedArea = approvedArea;
	}

	public Double getSharedArea() {
		return sharedArea;
	}

	public void setSharedArea(Double sharedArea) {
		this.sharedArea = sharedArea;
	}

	public Double getExcessArea() {
		if((this.sharedArea-this.approvedArea)>0){
			return this.sharedArea-this.approvedArea;
		}else{
			return new Double(0);
		}
	}

	public void setExcessArea(Double excessArea) {
		this.excessArea = excessArea;
	}

	public Double getExcessRatio() {
		if((this.sharedArea - this.approvedArea)<=0){
			return new Double(0);
		}else {
			return (this.sharedArea - this.approvedArea) / this.approvedArea;
		}
	}

	public void setExcessRatio(Double excessRatio) {
		this.excessRatio = excessRatio;
	}

	public String getNeedRectification() {
		if ((this.sharedArea-this.approvedArea)>0){
			return "1";
		}else{
			return "0";
		}
	}

	public void setNeedRectification(String needRectification) {
		this.needRectification = needRectification;
	}
}