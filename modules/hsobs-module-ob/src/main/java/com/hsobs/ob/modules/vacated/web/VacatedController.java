package com.hsobs.ob.modules.vacated.web;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSON;
import com.hsobs.ob.modules.vacated.entity.*;
import com.hsobs.ob.modules.vacated.service.VacatedAssetService;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.entity.api.ApiRequestBody;
import com.jeesite.modules.sys.service.OfficeService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.vacated.service.VacatedService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 清理腾退Controller
 * 
 * <AUTHOR>
 * @version 2025-02-03
 */
@Controller
@RequestMapping(value = "${adminPath}/vacated/")
public class VacatedController extends BaseController {

	@Autowired
	private VacatedService vacatedService;

	@Autowired
	private OfficeService officeService;

	@Autowired
	private VacatedAssetService vacatedAssetService;

	@Autowired
	TaskService taskService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public Vacated get(String id, boolean isNewRecord) {
		return vacatedService.get(id, isNewRecord);
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = { "list", "" })
	public String list(Vacated vacated, Model model) {
		model.addAttribute("vacated", vacated);
		return "modules/vacated/vacatedList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = { "listLedger", "" })
	public String listLedger(VacatedLedger vacatedLedger, Model model) {
		model.addAttribute("vacatedLedger", vacatedLedger);
		return "modules/vacated/vacatedListLedger";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = { "listQuery", "" })
	public String listQuery(VacatedQuery vacatedQuery, Model model) {
		model.addAttribute("vacatedQuery", vacatedQuery);
		return "modules/vacated/vacatedListQuery";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "listLedgerData")
	@ResponseBody
	public Page<VacatedLedger> listLedgerData(VacatedLedger vacatedLedger, HttpServletRequest request,
			HttpServletResponse response) {
		vacatedLedger.setPage(new Page<>(request, response));
		vacatedService.addDataScopeFilter(vacatedLedger);
		Page<VacatedLedger> page = vacatedService.listLedgerData(vacatedLedger);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "listQueryData")
	@ResponseBody
	public Page<VacatedQuery> listQueryData(VacatedQuery vacatedQuery, HttpServletRequest request,
			HttpServletResponse response) {
		vacatedQuery.setPage(new Page<>(request, response));
		vacatedService.addDataScopeFilter(vacatedQuery);
		Page<VacatedQuery> page = vacatedService.listQueryData(vacatedQuery);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<Vacated> listData(Vacated vacated, HttpServletRequest request, HttpServletResponse response) {
		vacated.setPage(new Page<>(request, response));
		Page<Vacated> page = vacatedService.findPage(vacated);
		return page;
	}

	/**
	 * 查询核验列表数据
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "verifyListData")
	@ResponseBody
	public Page<VerifyListResponse> verifyListData(VerifyListRequest verifyListRequest, HttpServletRequest request,
			HttpServletResponse response) {
		Vacated vacated = new Vacated();
		vacated.setPage(new Page<>(request, response));
		Page<Vacated> page = vacatedService.findMztPage(vacated);
		Page<VerifyListResponse> result = new Page<>();
		result.setCount(page.getCount());
		result.setList(page.getList().stream().map(item -> {
			VerifyListResponse verifyListResponse = new VerifyListResponse();
			verifyListResponse.setId(item.getId());
			verifyListResponse.setHydh(item.getId());
			verifyListResponse.setTtmc(item.getVacatedName());
			verifyListResponse.setTtsm(item.getVacatedDescribe());
			if (null != item.getRealEstateAddress() && null != item.getRealEstateAddress().getUsedOfficeCode()
					&& !item.getRealEstateAddress().getUsedOfficeCode().isEmpty()) {
				verifyListResponse
						.setHydw(officeService.get(item.getRealEstateAddress().getUsedOfficeCode()).getOfficeName());
			}
			String currentTaskName = "";
			BpmProcIns bpmProcIns = BpmUtils.getProcIns(item, "ob_vacated");
			if (null != bpmProcIns) {
				Task task = taskService.createTaskQuery().processInstanceId(bpmProcIns.getId()).singleResult();
				if (null != task) {
					currentTaskName = task.getName();
				}
			}
			if (null != currentTaskName && currentTaskName.equals("现场核验")) {
				verifyListResponse.setHyzt("0");
			} else {
				verifyListResponse.setHyzt("1");
			}
			verifyListResponse
					.setFwmc(item.getRealEstateAddress() == null ? "" : item.getRealEstateAddress().getName());
			return verifyListResponse;
		}).collect(Collectors.toList()));
		return result;
	}

	/**
	 * 查询核验详情
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "verifyDataInfo")
	@ResponseBody
	public VerifyInfoResponse verifyDataInfo(VerifyInfoRequest verifyInfoRequest, HttpServletRequest request,
			HttpServletResponse response) {
		Vacated vacated = vacatedService.get(verifyInfoRequest.getId());
		VerifyInfoResponse verifyInfoResponse = new VerifyInfoResponse();
		verifyInfoResponse.setId(vacated.getId());
		verifyInfoResponse.setHydh(vacated.getId());
		if (null != vacated.getRealEstateAddress() && null != vacated.getRealEstateAddress().getUsedOfficeCode()
				&& !vacated.getRealEstateAddress().getUsedOfficeCode().isEmpty()) {
			verifyInfoResponse
					.setHydw(officeService.get(vacated.getRealEstateAddress().getUsedOfficeCode()).getOfficeName());
		}
		verifyInfoResponse.setHyr(vacated.getCreateBy());
		verifyInfoResponse.setHyqkms(vacated.getVacatedDescribe());
		List<FileUpload> obQwEstateCheck = FileUploadUtils.findFileUpload(vacated.getId(), "obQwEstate_check");
		verifyInfoResponse.setHyqktpList(
				obQwEstateCheck.stream().map(fileUpload -> new VerifyImageData(fileUpload.getId(), "obQwEstateCheck"))
						.collect(Collectors.toList()));
		verifyInfoResponse.setTtmc(vacated.getVacatedName());
		verifyInfoResponse.setTtsm(vacated.getVacatedDescribe());

		String currentTaskName = "";
		BpmProcIns bpmProcIns = BpmUtils.getProcIns(vacated, "ob_vacated");
		if (null != bpmProcIns) {
			Task task = taskService.createTaskQuery().processInstanceId(bpmProcIns.getId()).singleResult();
			if (null != task) {
				currentTaskName = task.getName();
			}
		}
		verifyInfoResponse.setHyzt(currentTaskName.equals("现场核验") ? "0" : "1");
		verifyInfoResponse.setHdyj("");
		verifyInfoResponse
				.setFwmc(vacated.getRealEstateAddress() == null ? "" : vacated.getRealEstateAddress().getName());
		verifyInfoResponse.setFjmc(vacated.getRealEstate() == null ? "" : vacated.getRealEstate().getName());
		verifyInfoResponse.setTtyy(vacated.getType());
		verifyInfoResponse.setZcsm(vacated.getAssetDescribe() == null ? "" : vacated.getAssetDescribe());
		return verifyInfoResponse;
	}

	/**
	 * 提交房产核验信息
	 */
	@RequiresPermissions("vacated::edit")
	@RequestMapping(value = "saveVerifyData")
	@ResponseBody
	public SaveVerifyDataRequest saveVerifyData(SaveVerifyDataRequest saveVerifyDataRequest, HttpServletRequest request,
			HttpServletResponse response) {
		// SaveVerifyDataRequest saveVerifyDataRequest = new SaveVerifyDataRequest();
		// saveVerifyDataRequest.setId(request.getParameter("id"));
		// saveVerifyDataRequest.setHyqkms(request.getParameter("hyqkms"));
		// saveVerifyDataRequest.setHyr(request.getParameter("hyr"));
		// saveVerifyDataRequest.setSjh(request.getParameter("sjh"));
		// if (null != request.getParameter("hyqktpList")) {
		// String hyqktpList = request.getParameter("hyqktpList").replace('=', ':') //
		// 替换 = 为 :
		// .replaceAll("(\\w+):", "\"$1\":").replaceAll(":([^,}\\]]+)", ":\"$1\"");
		// List<VerifyImageData> imageList = JSON.parseArray(hyqktpList,
		// VerifyImageData.class);
		// saveVerifyDataRequest.setHyqktpList(imageList);
		// } else {
		// saveVerifyDataRequest.setHyqktpList(new ArrayList<>());
		// }
		vacatedService.saveMZTApiData(saveVerifyDataRequest);
		return saveVerifyDataRequest;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "form")
	public String form(Vacated vacated, Model model) {
		vacatedService.loadChildData(vacated);
		VacatedAsset vacatedAsset = new VacatedAsset();
		if (!vacated.getIsNewRecord() && null != vacated && null != vacated.getId()) {
			vacatedAsset.setVacatedId(vacated.getId());
		}
		String currentTaskName = "";
		BpmProcIns bpmProcIns = BpmUtils.getProcIns(vacated, "ob_vacated");
		if (null != bpmProcIns) {
			Task task = taskService.createTaskQuery().processInstanceId(bpmProcIns.getId()).singleResult();
			if (null != task) {
				currentTaskName = task.getName();
			}
		}
		boolean commonReadonly = null != vacated.getStatus() && !vacated.getStatus().equals("9");
		model.addAttribute("commonReadonly", commonReadonly);
		model.addAttribute("currentTaskName", currentTaskName);
		model.addAttribute("vacatedAsset", vacatedAsset);
		model.addAttribute("vacated", vacated);
		return "modules/vacated/vacatedForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("vacated::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated Vacated vacated) {
		try {
			vacatedService.save(vacated);
		} catch (RuntimeException e) {
			return renderResult(Global.FALSE, text(e.getMessage()));
		}
		return renderResult(Global.TRUE, text("保存清理腾退成功！"));
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("vacated::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(Vacated vacated) {
		if (!Vacated.STATUS_DRAFT.equals(vacated.getStatus())) {
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		vacatedService.delete(vacated);
		return renderResult(Global.TRUE, text("删除清理腾退成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "exportData")
	public void exportData(VacatedLedger vacatedLedger, HttpServletResponse response) {
		List<VacatedLedger> list = vacatedService.listLedger(vacatedLedger);
		String fileName = "腾退明细台账" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try (ExcelExport ee = new ExcelExport("腾退明细台账", VacatedLedger.class)) {
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "exportVacatedData")
	public void exportVacatedData(Vacated vacated, HttpServletResponse response) {
		List<Vacated> list = vacatedService.findList(vacated);
		String fileName = "清理腾退" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try (ExcelExport ee = new ExcelExport("清理腾退", Vacated.class)) {
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "/asset/exportData")
	public void exportData(VacatedAsset vacatedAsset, HttpServletResponse response) {
		if (null == vacatedAsset.getVacatedId()) {
			return;
		}
		List<VacatedAsset> list = vacatedAssetService.findList(vacatedAsset);
		String fileName = "腾退资产" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try (ExcelExport ee = new ExcelExport("腾退资产", VacatedAsset.class)) {
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("vacated::view")
	@RequestMapping(value = "/asset/importTemplate")
	public void importTemplate(HttpServletResponse response) {
		VacatedAsset vacatedAsset = new VacatedAsset();
		List<VacatedAsset> list = ListUtils.newArrayList(vacatedAsset);
		String fileName = "腾退资产模板.xlsx";
		try (ExcelExport ee = new ExcelExport("腾退资产", VacatedAsset.class, ExcelField.Type.IMPORT)) {
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("vacated::edit")
	@PostMapping(value = "/asset/importData")
	public String importData(MultipartFile file, String vacatedId) {
		if (null == vacatedId || vacatedId.isEmpty()) {
			return renderResult(Global.FALSE, "posfull:请保存数据后导入");
		}
		try {
			String message = vacatedAssetService.importData(file, vacatedId);
			return renderResult(Global.TRUE, "posfull:" + message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:" + ex.getMessage());
		}
	}

}