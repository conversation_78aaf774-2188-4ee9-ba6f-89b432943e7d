package com.hsobs.ob.modules.usevouchers.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotBlank;
import java.util.Date;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

/**
 * 使用凭证Entity
 * <AUTHOR>
 * @version 2025-02-07
 */
@Table(name="ob_use_vouchers", alias="a", label="使用凭证信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="name", attrName="name", label="名称", queryType=QueryType.LIKE),
		@Column(name="organization_id", attrName="organizationId", label="组织机构ID"),
		@Column(name="real_estate_address_id", attrName="realEstateAddressId", label="不动产地址ID"),
		@Column(name="real_estate_id", attrName="realEstateId", label="房间ID"),
		@Column(name="registration_manager_by", attrName="registrationManagerBy", label="登记负责人"),
		@Column(name="handled_by", attrName="handledBy", label="经办人"),
		@Column(name="signing_date", attrName="signingDate", label="签订日期"),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, joinTable={
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstate.class, alias="re",
				on="re.id = a.real_estate_id",
				columns={@Column(includeEntity=RealEstate.class)}),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddress.class, alias="rea",
				on="rea.id = a.real_estate_address_id",
				columns={@Column(includeEntity=RealEstateAddress.class)}),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= Office.class, alias="o",
				on="o.office_code = a.organization_id",
				columns={@Column(includeEntity=Office.class)}),
		@JoinTable(type=Type.LEFT_JOIN, entity= User.class, attrName="registrationManagerByUser", alias="rmbu",
				on="rmbu.user_code = a.registration_manager_by", columns={
				@Column(name="user_code", label="用户编码", isPK=true),
				@Column(name="user_name", label="用户名称", isQuery=false),
		}),
		@JoinTable(type=Type.LEFT_JOIN, entity= User.class, attrName="handledByUser", alias="hbu",
				on="hbu.user_code = a.handled_by", columns={
				@Column(name="user_code", label="用户编码", isPK=true),
				@Column(name="user_name", label="用户名称", isQuery=false),
		}),
	}, orderBy="a.update_date DESC"
)
public class UseVouchers extends DataEntity<UseVouchers> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 名称
	private String organizationId;		// 组织机构ID
	private String realEstateAddressId;		// 不动产地址ID
	private String realEstateId;		// 房间ID
	private String registrationManagerBy;		// 登记负责人
	private String handledBy;		// 经办人
	private Date signingDate;		// 签订日期


	private RealEstate realEstate;
	private RealEstateAddress realEstateAddress;
	private Office office;
	private User registrationManagerByUser;
	private User handledByUser;

	public UseVouchers() {
		this(null);
	}
	
	public UseVouchers(String id){
		super(id);
	}
	
	@Size(min=0, max=512, message="名称长度不能超过 512 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@Size(min=0, max=64, message="组织机构ID长度不能超过 64 个字符")
	public String getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId;
	}
	
	@Size(min=0, max=64, message="不动产地址ID长度不能超过 64 个字符")
	public String getRealEstateAddressId() {
		return realEstateAddressId;
	}

	public void setRealEstateAddressId(String realEstateAddressId) {
		this.realEstateAddressId = realEstateAddressId;
	}
	
	@Size(min=0, max=64, message="房间ID长度不能超过 64 个字符")
	public String getRealEstateId() {
		return realEstateId;
	}

	public void setRealEstateId(String realEstateId) {
		this.realEstateId = realEstateId;
	}
	
	@NotBlank(message="登记负责人不能为空")
	@Size(min=0, max=64, message="登记负责人长度不能超过 64 个字符")
	public String getRegistrationManagerBy() {
		return registrationManagerBy;
	}

	public void setRegistrationManagerBy(String registrationManagerBy) {
		this.registrationManagerBy = registrationManagerBy;
	}
	
	@NotBlank(message="经办人不能为空")
	@Size(min=0, max=64, message="经办人长度不能超过 64 个字符")
	public String getHandledBy() {
		return handledBy;
	}

	public void setHandledBy(String handledBy) {
		this.handledBy = handledBy;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="签订日期不能为空")
	public Date getSigningDate() {
		return signingDate;
	}

	public void setSigningDate(Date signingDate) {
		this.signingDate = signingDate;
	}


	public RealEstate getRealEstate() {
		return realEstate;
	}

	public void setRealEstate(RealEstate realEstate) {
		this.realEstate = realEstate;
	}

	public RealEstateAddress getRealEstateAddress() {
		return realEstateAddress;
	}

	public void setRealEstateAddress(RealEstateAddress realEstateAddress) {
		this.realEstateAddress = realEstateAddress;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	public User getRegistrationManagerByUser() {
		return registrationManagerByUser;
	}

	public void setRegistrationManagerByUser(User registrationManagerByUser) {
		this.registrationManagerByUser = registrationManagerByUser;
	}

	public User getHandledByUser() {
		return handledByUser;
	}

	public void setHandledByUser(User handledByUser) {
		this.handledByUser = handledByUser;
	}
}