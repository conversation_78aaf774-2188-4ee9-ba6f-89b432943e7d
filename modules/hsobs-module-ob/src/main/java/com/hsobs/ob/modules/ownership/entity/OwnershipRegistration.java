package com.hsobs.ob.modules.ownership.entity;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 权属登记Entity
 * <AUTHOR>
 * @version 2025-02-05
 */
@Table(name="ob_ownership_registration", alias="a", label="权属登记信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="name", attrName="name", label="登记名称"),
		@Column(name="work_order_code", attrName="workOrderCode", label="登记编号"),
		@Column(name="type", attrName="type", label="登记类型"),
		@Column(name="content", attrName="content", label="登记内容"),
		@Column(name="pre_owner_office_code", attrName="preOwnerOfficeCode", label="原产权机构ID"),
		@Column(name="owner_office_code", attrName="ownerOfficeCode", label="产权机构ID"),
		@Column(name="owner_user_code", attrName="ownerUserCode", label="产权人ID"),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用机构ID"),
		@Column(name="used_user_code", attrName="usedUserCode", label="使用人ID"),
		@Column(name="purpose", attrName="purpose", label="用途"),
		@Column(name="coordinate_date", attrName="coordinateDate", label="协调日期", isUpdateForce=true),
		@Column(name="classified", attrName="classified", label="是否涉密"),
		@Column(name="real_estate_type", attrName="realEstateType", label="不动产类型", isUpdate = true),
		@Column(name="registration_date", attrName="registrationDate", label="登记日期", isUpdateForce=true),
		@Column(name="certificate_complete", attrName="certificateComplete", label="证件是否齐全", isUpdateForce=true),
		@Column(name="hypothecate_type", attrName="hypothecateType", label="抵押类型", isUpdateForce=true),
		@Column(name="hypothecate_office_code", attrName="hypothecateOfficeCode", label="抵押单位", isUpdateForce=true),
		@Column(name="hypothecate_date", attrName="hypothecateDate", label="抵押日期", isUpdateForce=true),
		@Column(name="cancel_office_code", attrName="cancelOfficeCode", label="注销单位", isUpdateForce=true),
		@Column(name="cancel_date", attrName="cancelDate", label="注销日期", isUpdateForce=true),
		@Column(name="cancel_reason", attrName="cancelReason", label="注销原因", isUpdateForce=true),
		@Column(name="change_type", attrName="changeType", label="变更原因", isUpdateForce=true),
		@Column(name="change_date", attrName="changeDate", label="变更日期", isUpdateForce=true),
		@Column(
				name = "status",
				attrName = "status",
				label = "状态",
				isUpdate = true,
				isQuery = true,
				comment = "（推荐状态：0：正常；1：删除；2：停用；3：冻结；4：审核、待审核；5：审核驳回；9：草稿）"
		),
		@Column(includeEntity=DataEntity.class),
}, joinTable={
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o3",
				attrName = "preOwnerOffice",
				on = "a.pre_owner_office_code = o3.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o4",
				attrName = "hypothecateOffice",
				on = "a.hypothecate_office_code = o4.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o5",
				attrName = "cancelOffice",
				on = "a.cancel_office_code = o5.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o1",
				attrName = "ownerOffice",
				on = "a.owner_office_code = o1.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "usedOffice",
				on = "a.used_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u1",
				attrName = "ownerUser",
				on = "a.owner_user_code = u1.user_code",
				columns = {@Column(includeEntity = User.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u2",
				attrName = "usedUser",
				on = "a.used_user_code = u2.user_code",
				columns = {@Column(includeEntity = User.class)})
}, orderBy="a.update_date DESC"
)
public class OwnershipRegistration extends BpmEntity<OwnershipRegistration> {

	private static final long serialVersionUID = 1L;
	private String name;
	private String workOrderCode;		// 登记编号
	private String type;		// 登记类型
	private String content;		// 登记内容
	private String purpose;		// 用途
	private Date coordinateDate;		// 协调日期
	private String classified;		// 是否涉密
	private String realEstateType;	// 不动产类型
	private String preOwnerOfficeCode;
	private String ownerOfficeCode;
	private String ownerUserCode;
	private String usedOfficeCode;
	private String usedUserCode;

	private Date registrationDate; // 登记日期
	private String certificateComplete; // 证件是否齐全

	private String hypothecateType; // 抵押类型
	private String hypothecateOfficeCode; // 抵押单位
	private Date hypothecateDate; // 抵押日期

	private String cancelOfficeCode; // 注销单位
	private Date cancelDate; // 注销日期
	private String cancelReason; // 注销原因

	private String changeType; // 变更原因
	private Date changeDate; // 变更日期

	private Office ownerOffice;
	private Office preOwnerOffice;
	private User ownerUser;
	private Office usedOffice;
	private User usedUser;
	private Office hypothecateOffice;
	private Office cancelOffice;


	private List<OwnershipRealEstate> ownershipRealEstateList = ListUtils.newArrayList();

	@ExcelFields({
			@ExcelField(title="登记编号", attrName="id", align=Align.CENTER, sort=10),
			@ExcelField(title="登记名称", attrName="name", align=Align.CENTER, sort=20),
			@ExcelField(title="登记类型", attrName="type", dictType="ob_ownership_registration_type", align=Align.CENTER, sort=30),
			@ExcelField(title="权属单位名称", attrName="ownerOffice.officeName", align=Align.CENTER, sort=40),
			@ExcelField(title="产权人", attrName="ownerUser.userName", align=Align.CENTER, sort=170),
			@ExcelField(title="使用单位", attrName="usedOffice.officeName", align=Align.CENTER, sort=180),
			@ExcelField(title="使用人", attrName="usedUser.userName", align=Align.CENTER, sort=190),
			@ExcelField(title="登记日期", attrName="registrationDate", align=Align.CENTER, sort=200, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="抵押类型", attrName="hypothecateType", dictType="ob_hypothecate_type", align=Align.CENTER, sort=30),
			@ExcelField(title="抵押日期", attrName="hypothecateDate", align=Align.CENTER, sort=200, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="抵押单位", attrName="hypothecateOffice.officeName", align=Align.CENTER, sort=200),
			@ExcelField(title="注销单位", attrName="cancelOffice.officeName", align=Align.CENTER, sort=200),
			@ExcelField(title="注销日期", attrName="cancelDate", align=Align.CENTER, sort=200, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="变更原因", attrName="changeType", dictType="ob_change_type", align=Align.CENTER, sort=50),
			@ExcelField(title="变更日期", attrName="changeDate", align=Align.CENTER, sort=200, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="审批状态", attrName="status", dictType="bpm_biz_status", align=Align.CENTER, sort=50),
	})
	public OwnershipRegistration() {
		this(null);
	}

	public OwnershipRegistration(String id){
		super(id);
	}

	@NotBlank(message = "登记名称不能为空")
	@Size(min=0, max=256, message="登记名称长度不能超过 256 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Size(min=0, max=100, message="登记编号长度不能超过 100 个字符")
	public String getWorkOrderCode() {
		return workOrderCode;
	}

	public void setWorkOrderCode(String workOrderCode) {
		this.workOrderCode = workOrderCode;
	}

	@Size(min=0, max=100, message="登记类型长度不能超过 100 个字符")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Size(min=0, max=1000, message="登记内容长度不能超过 1000 个字符")
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Size(min=0, max=1000, message="用途长度不能超过 1000 个字符")
	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getCoordinateDate() {
		return coordinateDate;
	}

	public void setCoordinateDate(Date coordinateDate) {
		this.coordinateDate = coordinateDate;
	}

	@Size(min=0, max=1, message="是否涉密长度不能超过 1 个字符")
	public String getClassified() {
		return classified;
	}

	public void setClassified(String classified) {
		this.classified = classified;
	}

	public String getRealEstateType() {
		return realEstateType;
	}

	public void setRealEstateType(String realEstateType) {
		this.realEstateType = realEstateType;
	}

	@Valid
	public List<OwnershipRealEstate> getOwnershipRealEstateList() {
		return ownershipRealEstateList;
	}

	public void setOwnershipRealEstateList(List<OwnershipRealEstate> ownershipRealEstateList) {
		this.ownershipRealEstateList = ownershipRealEstateList;
	}

	public String getOwnerOfficeCode() {
		return ownerOfficeCode;
	}

	public void setOwnerOfficeCode(String ownerOfficeCode) {
		this.ownerOfficeCode = ownerOfficeCode;
	}

	public String getOwnerUserCode() {
		return ownerUserCode;
	}

	public void setOwnerUserCode(String ownerUserCode) {
		this.ownerUserCode = ownerUserCode;
	}

	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}

	public String getUsedUserCode() {
		return usedUserCode;
	}

	public void setUsedUserCode(String usedUserCode) {
		this.usedUserCode = usedUserCode;
	}

	public Office getOwnerOffice() {
		return ownerOffice;
	}

	public void setOwnerOffice(Office ownerOffice) {
		this.ownerOffice = ownerOffice;
	}

	public User getOwnerUser() {
		return ownerUser;
	}

	public void setOwnerUser(User ownerUser) {
		this.ownerUser = ownerUser;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}

	public User getUsedUser() {
		return usedUser;
	}

	public void setUsedUser(User usedUser) {
		this.usedUser = usedUser;
	}

	public Date getRegistrationDate() {
		return registrationDate;
	}

	public void setRegistrationDate(Date registrationDate) {
		this.registrationDate = registrationDate;
	}


	public Date getRegistrationDate_gte() {
		return sqlMap.getWhere().getValue("registration_date", QueryType.GTE);
	}

	public void setRegistrationDate_gte(Date registrationDate) {
		sqlMap.getWhere().and("registration_date", QueryType.GTE, registrationDate);
	}

	public Date getRegistrationDate_lte() {
		return sqlMap.getWhere().getValue("registration_date", QueryType.LTE);
	}

	public void setRegistrationDate_lte(Date registrationDate) {
		sqlMap.getWhere().and("registration_date", QueryType.LTE, registrationDate);
	}

	public String getCertificateComplete() {
		return certificateComplete;
	}

	public void setCertificateComplete(String certificateComplete) {
		this.certificateComplete = certificateComplete;
	}

	public String getPreOwnerOfficeCode() {
		return preOwnerOfficeCode;
	}

	public void setPreOwnerOfficeCode(String preOwnerOfficeCode) {
		this.preOwnerOfficeCode = preOwnerOfficeCode;
	}

	public Office getPreOwnerOffice() {
		return preOwnerOffice;
	}

	public void setPreOwnerOffice(Office preOwnerOffice) {
		this.preOwnerOffice = preOwnerOffice;
	}

	public String getHypothecateType() {
		return hypothecateType;
	}

	public void setHypothecateType(String hypothecateType) {
		this.hypothecateType = hypothecateType;
	}

	public String getHypothecateOfficeCode() {
		return hypothecateOfficeCode;
	}

	public void setHypothecateOfficeCode(String hypothecateOfficeCode) {
		this.hypothecateOfficeCode = hypothecateOfficeCode;
	}

	public Date getHypothecateDate() {
		return hypothecateDate;
	}

	public void setHypothecateDate(Date hypothecateDate) {
		this.hypothecateDate = hypothecateDate;
	}

	public Office getHypothecateOffice() {
		return hypothecateOffice;
	}

	public void setHypothecateOffice(Office hypothecateOffice) {
		this.hypothecateOffice = hypothecateOffice;
	}

	public Date getHypothecateDate_gte() {
		return sqlMap.getWhere().getValue("hypothecate_date", QueryType.GTE);
	}

	public void setHypothecateDate_gte(Date hypothecateDate) {
		sqlMap.getWhere().and("hypothecate_date", QueryType.GTE, hypothecateDate);
	}

	public Date getHypothecateDate_lte() {
		return sqlMap.getWhere().getValue("hypothecate_date", QueryType.LTE);
	}

	public void setHypothecateDate_lte(Date hypothecateDate) {
		sqlMap.getWhere().and("hypothecate_date", QueryType.LTE, hypothecateDate);
	}

	public String getCancelOfficeCode() {
		return cancelOfficeCode;
	}

	public void setCancelOfficeCode(String cancelOfficeCode) {
		this.cancelOfficeCode = cancelOfficeCode;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public String getCancelReason() {
		return cancelReason;
	}

	public void setCancelReason(String cancelReason) {
		this.cancelReason = cancelReason;
	}

	public Office getCancelOffice() {
		return cancelOffice;
	}

	public void setCancelOffice(Office cancelOffice) {
		this.cancelOffice = cancelOffice;
	}

	public String getChangeType() {
		return changeType;
	}

	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}

	public Date getChangeDate() {
		return changeDate;
	}

	public void setChangeDate(Date changeDate) {
		this.changeDate = changeDate;
	}

	public Date getCancelDate_gte() {
		return sqlMap.getWhere().getValue("cancel_date", QueryType.GTE);
	}

	public void setCancelDate_gte(Date cancelDate) {
		sqlMap.getWhere().and("cancel_date", QueryType.GTE, cancelDate);
	}

	public Date getCancelDate_lte() {
		return sqlMap.getWhere().getValue("cancel_date", QueryType.LTE);
	}

	public void setCancelDate_lte(Date cancelDate) {
		sqlMap.getWhere().and("cancel_date", QueryType.LTE, cancelDate);
	}

	public Date getChangeDate_gte() {
		return sqlMap.getWhere().getValue("change_date", QueryType.GTE);
	}

	public void setChangeDate_gte(Date changeDate) {
		sqlMap.getWhere().and("change_date", QueryType.GTE, changeDate);
	}

	public Date getChangeDate_lte() {
		return sqlMap.getWhere().getValue("change_date", QueryType.LTE);
	}

	public void setChangeDate_lte(Date changeDate) {
		sqlMap.getWhere().and("change_date", QueryType.LTE, changeDate);
	}
}