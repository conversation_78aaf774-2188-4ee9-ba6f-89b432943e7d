package com.hsobs.ob.modules.vacated.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * ob_vacated_assetEntity
 * <AUTHOR>
 * @version 2025-04-08
 */
@Table(name="ob_vacated_asset", alias="a", label="ob_vacated_asset信息", columns={
        @Column(name="id", attrName="id", label="id", isPK=true),
        @Column(name="vacated_id", attrName="vacatedId", label="vacated_id"),
        @Column(name="name", attrName="name", label="name", queryType=QueryType.LIKE),
        @Column(name="count", attrName="count", label="count", isUpdateForce=true),
        @Column(name="create_by", attrName="createBy", label="create_by", isUpdate=false, isQuery=false),
        @Column(name="create_date", attrName="createDate", label="create_date", isUpdate=false, isQuery=false),
        @Column(name="update_by", attrName="updateBy", label="update_by", isQuery=false),
        @Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
}, orderBy="a.update_date DESC"
)
public class VacatedAsset extends DataEntity<VacatedAsset> {

    private static final long serialVersionUID = 1L;
    private String vacatedId;		// vacated_id
    private String name;		// name
    private Integer count;		// count

    @ExcelFields({
            @ExcelField(title="名称", attrName="name", align=Align.CENTER, sort=30),
            @ExcelField(title="数量", attrName="count", align=Align.CENTER, sort=40),
    })
    public VacatedAsset() {
        this(null);
    }

    public VacatedAsset(String id){
        super(id);
    }

    @Size(min=0, max=64, message="vacated_id长度不能超过 64 个字符")
    public String getVacatedId() {
        return vacatedId;
    }

    public void setVacatedId(String vacatedId) {
        this.vacatedId = vacatedId;
    }

    @Size(min=0, max=512, message="name长度不能超过 512 个字符")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

}
