package com.hsobs.ob.modules.ownership.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

/**
 * 权属备案Entity
 * <AUTHOR>
 * @version 2025-03-09
 */
@Table(name="ob_ownership_record", alias="a", label="权属备案信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="office_code", attrName="officeCode", label="备案单位"),
		@Column(name="owner_office_code", attrName="ownerOfficeCode", label="权属单位"),
		@Column(name="description", attrName="description", label="备案原因"),
		@Column(name="ownership_date", attrName="ownershipDate", label="备案日期"),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
		@Column(name = "record_status", attrName = "recordStatus", label = "状态", isUpdate = true, isQuery = true),
	}, joinTable={
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o1",
				attrName = "office",
				on = "a.office_code = o1.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "ownerOffice",
				on = "a.owner_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
	}, orderBy="a.update_date DESC"
)
public class OwnershipRecord extends DataEntity<OwnershipRecord> {
	
	private static final long serialVersionUID = 1L;
	private String officeCode;		// 备案单位
	private String ownerOfficeCode;		// 权属单位
	private String description;		// 备案原因
	private Date ownershipDate;		// 备案日期
	private String recordStatus;

	private boolean report;

	private Office office;
	private Office ownerOffice;

	@ExcelFields({
		@ExcelField(title="备案单位", attrName="officeCode", align=Align.CENTER, sort=20),
		@ExcelField(title="备案原因", attrName="description", align=Align.CENTER, sort=30),
		@ExcelField(title="备案日期", attrName="ownershipDate", align=Align.CENTER, sort=40, dataFormat="yyyy-MM-dd hh:mm"),
		@ExcelField(title="状态", attrName="status", dictType="ob_ownership_record_status", align=Align.CENTER, sort=50),
	})
	public OwnershipRecord() {
		this(null);
	}
	
	public OwnershipRecord(String id){
		super(id);
	}
	
	@NotBlank(message="备案单位不能为空")
	@Size(min=0, max=64, message="备案单位长度不能超过 64 个字符")
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
	
	@NotBlank(message="备案原因不能为空")
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="备案日期不能为空")
	public Date getOwnershipDate() {
		return ownershipDate;
	}

	public void setOwnershipDate(Date ownershipDate) {
		this.ownershipDate = ownershipDate;
	}

	public boolean isReport() {
		return report;
	}

	public void setReport(boolean report) {
		this.report = report;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	public String getOwnerOfficeCode() {
		return ownerOfficeCode;
	}

	public void setOwnerOfficeCode(String ownerOfficeCode) {
		this.ownerOfficeCode = ownerOfficeCode;
	}

	public Office getOwnerOffice() {
		return ownerOffice;
	}

	public void setOwnerOffice(Office ownerOffice) {
		this.ownerOffice = ownerOffice;
	}

	public String getRecordStatus() {
		return recordStatus;
	}

	public void setRecordStatus(String recordStatus) {
		this.recordStatus = recordStatus;
	}

	public Date getOwnershipDate_gte() {
		return sqlMap.getWhere().getValue("ownership_date", QueryType.GTE);
	}

	public void setOwnershipDate_gte(Date ownershipDate) {
		sqlMap.getWhere().and("ownership_date", QueryType.GTE, ownershipDate);
	}

	public Date getOwnershipDate_lte() {
		return sqlMap.getWhere().getValue("ownership_date", QueryType.LTE);
	}

	public void setOwnershipDate_lte(Date ownershipDate) {
		sqlMap.getWhere().and("ownership_date", QueryType.LTE, ownershipDate);
	}
}