package com.hsobs.ob.modules.apply.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

/**
 * 技术业务用房申请表Entity
 * <AUTHOR>
 * @version 2025-03-13
 */
@Table(name="ob_apply_business_house", alias="a", label="技术业务用房申请表信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用单位"),
		@Column(name="apply_date", attrName="applyDate", label="使用日期"),
		@Column(name="reason", attrName="reason", label="理由"),
		@Column(name="ability", attrName="ability", label="功能"),
		@Column(name="remark", attrName="remark", label="备注"),
		@Column(name="area", attrName="area", label="需求面积"),
		@Column(
				name = "status",
				attrName = "status",
				label = "状态",
				isUpdate = true,
				isQuery = false,
				comment = "（推荐状态：0：正常；1：删除；2：停用；3：冻结；4：审核、待审核；5：审核驳回；9：草稿）"
		),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "usedOffice",
				on = "a.used_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
	}, orderBy="a.update_date DESC"
)
public class ApplyBusinessHouse extends BpmEntity<ApplyBusinessHouse> {
	
	private static final long serialVersionUID = 1L;
	private String usedOfficeCode;		// 使用单位
	private Date applyDate;		// 使用日期
	private String reason;		// 理由
	private String ability;		// 功能
	private String remark;		// 备注
	private Double area;		// 需求面积

	private Office usedOffice;

	public ApplyBusinessHouse() {
		this(null);
	}
	
	public ApplyBusinessHouse(String id){
		super(id);
	}
	
	@NotBlank(message="使用单位不能为空")
	@Size(min=0, max=64, message="使用单位长度不能超过 64 个字符")
	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="使用日期不能为空")
	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}
	
	@NotBlank(message="理由不能为空")
	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}
	
	@NotBlank(message="功能不能为空")
	@Size(min=0, max=512, message="功能长度不能超过 512 个字符")
	public String getAbility() {
		return ability;
	}

	public void setAbility(String ability) {
		this.ability = ability;
	}
	
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	@NotNull(message="需求面积不能为空")
	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}
}