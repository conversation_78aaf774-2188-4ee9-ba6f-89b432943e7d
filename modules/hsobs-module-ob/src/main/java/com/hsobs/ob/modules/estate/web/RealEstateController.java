package com.hsobs.ob.modules.estate.web;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.lang.Snowflake;
import com.hsobs.ob.modules.estate.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.sys.entity.DictData;
import com.jeesite.modules.sys.utils.DictUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.estate.service.RealEstateService;

/**
 * 不动产信息表Controller
 * <AUTHOR>
 * @version 2024-12-08
 */
@Controller
@RequestMapping(value = "${adminPath}/estate/realEstate")
public class RealEstateController extends BaseController {

	@Autowired
	private RealEstateService realEstateService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public RealEstate get(String id, boolean isNewRecord) {
		return realEstateService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = {"list", ""})
	public String list(RealEstate realEstate, Model model) {
		model.addAttribute("realEstate", realEstate);
		return "modules/estate/realEstateList";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = {"listQuery", ""})
	public String listQuery(RealEstateQuery realEstateQuery, Model model) {
		model.addAttribute("realEstateQuery", realEstateQuery);
		return "modules/estate/realEstateListQuery";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = {"listUsageQuery", ""})
	public String listUsageQuery(RealEstateQuery realEstateQuery, Model model) {
		model.addAttribute("realEstateQuery", realEstateQuery);
		return "modules/estate/realEstateListUsageQuery";
	}

	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = "realEstateType")
	@ResponseBody
	public ApiRealEstateTypeResponse realEstateType() {
		ApiRealEstateTypeResponse result = new ApiRealEstateTypeResponse();
		result.setTotal(0);
		result.setDataList(new ArrayList<>());

		List<DictData> occupancyClassification = DictUtils.getDictList("occupancy_classification");

		if (occupancyClassification.isEmpty()) {
			return result;
		}

		List<RealEstateType> realEstateTypeList = occupancyClassification.stream().map(item -> {
			RealEstateType realEstateType = new RealEstateType();
			realEstateType.setCode(item.getDictValue());
			realEstateType.setName(item.getDictLabelRaw());
			return realEstateType;
		}).collect(Collectors.toList());


		result.setTotal(realEstateTypeList.size());
		result.setDataList(realEstateTypeList);

		return result;
	}


	/**
	 * 获取单位房地产信息
	 */
	@RequestMapping(value = "apiListData")
	@ResponseBody
	public Page<ApiRealEstateListResponse> apiListData(ApiRealEstateListRequest apiRealEstateListRequest, HttpServletRequest request, HttpServletResponse response) {
		RealEstate realEstate = new RealEstate();
		realEstate.setPageNo(apiRealEstateListRequest.getPageIndex());
		realEstate.setType(Integer.valueOf(apiRealEstateListRequest.getFclx()));
		realEstate.setPageSize(apiRealEstateListRequest.getPageSize());
		realEstate.setPage(new Page<>(request, response));
		realEstateService.addDataScopeFilter(realEstate);
		Page<RealEstate> page = realEstateService.findPage(realEstate);

		List<ApiRealEstateListResponse> pageList = page.getList().stream().map(item -> {
			ApiRealEstateListResponse apiRealEstateListResponse = new ApiRealEstateListResponse();
			apiRealEstateListResponse.setYfmc(item.getName());
			apiRealEstateListResponse.setSymj(item.getArea());
			apiRealEstateListResponse.setFclx(DictUtils.getDictLabel("occupancy_classification", item.getType().toString(), "未知"));
			apiRealEstateListResponse.setFczt("0");
			if (item.getUsedUserCode() != null) {
				apiRealEstateListResponse.setFczt("1");
			}
			return apiRealEstateListResponse;
		}).collect(Collectors.toList());
		Page<ApiRealEstateListResponse> result = new Page<>();
		result.setCount(page.getCount());
		result.setList(pageList);
		return result;
	}


	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<RealEstate> listData(RealEstate realEstate, HttpServletRequest request, HttpServletResponse response) {
		realEstateService.addDataScopeFilter(realEstate);
		realEstate.setPage(new Page<>(request, response));
		Page<RealEstate> page = realEstateService.findPage(realEstate);
		return page;
	}


	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = "listQueryData")
	@ResponseBody
	public Page<RealEstateQuery> listQueryData(RealEstateQuery realEstateQuery, HttpServletRequest request, HttpServletResponse response) {
		realEstateQuery.setPage(new Page<>(request, response));

		realEstateService.addDataScopeFilter(realEstateQuery);
		Page<RealEstateQuery> page = realEstateService.listQueryData(realEstateQuery);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = "form")
	public String form(RealEstate realEstate, Model model) {
		if (null == realEstate.getSerialNumber() || realEstate.getSerialNumber().isEmpty()) {
			Snowflake snowflake = new Snowflake(1, 1);
			snowflake.nextId();
			realEstate.setSerialNumber(String.valueOf(snowflake.nextId()));
		}
		realEstateService.loadUsedUserList(realEstate);
		model.addAttribute("realEstate", realEstate);
		return "modules/estate/realEstateForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("estate:realEstate:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated RealEstate realEstate) {
		realEstateService.save(realEstate);
		return renderResult(Global.TRUE, text("保存房间成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("estate:realEstate:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(RealEstate realEstate) {
		realEstateService.delete(realEstate);
		return renderResult(Global.TRUE, text("删除不动产信息表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = "realEstateSelect")
	public String realEstateSelect(RealEstate realEstate, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("realEstate", realEstate);
		return "modules/estate/realEstateSelect";
	}

	/**
	 * 列表选择对话框-处置利用
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = "realEstateSelectDisposal")
	public String realEstateSelectDisposal(RealEstate realEstate, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("realEstate", realEstate);
		return "modules/estate/realEstateSelectDisposal";
	}

	/**
	 * 获取单位技术业务用房面积数据
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = "getOfficeTechnicalBusinessArea")
	@ResponseBody
	public List<OfficeTechnicalBusinessAreaDto> getOfficeTechnicalBusinessArea(RealEstate realEstate, Model model) {
		String officeName = realEstate.getName();
		return realEstateService.getOfficeTechnicalBusinessArea(officeName);
	}


	/**
	 * 导出数据
	 */
	@RequiresPermissions("estate:realEstate:view")
	@RequestMapping(value = "exportOfficeTechnicalBusinessAreaData")
	public void exportOfficeTechnicalBusinessAreaData(RealEstate realEstate, HttpServletResponse response) {
		String officeName = realEstate.getName();
		List<OfficeTechnicalBusinessAreaDto> list = realEstateService.getOfficeTechnicalBusinessArea(officeName);
		String fileName = "技术业务用房核定" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("技术业务用房核定", OfficeTechnicalBusinessAreaDto.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}