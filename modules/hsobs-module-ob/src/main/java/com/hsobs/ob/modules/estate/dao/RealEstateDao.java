package com.hsobs.ob.modules.estate.dao;

import com.hsobs.ob.modules.estate.entity.OfficeTechnicalBusinessAreaDto;
import com.hsobs.ob.modules.estate.entity.RealEstateQuery;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.ob.modules.estate.entity.RealEstate;

import java.util.List;

/**
 * 不动产信息表DAO接口
 * <AUTHOR>
 * @version 2024-12-08
 */
@MyBatisDao
public interface RealEstateDao extends CrudDao<RealEstate> {

    List<RealEstateQuery> listQueryData(RealEstateQuery realEstateQuery);

    List<OfficeTechnicalBusinessAreaDto> getOfficeTechnicalBusinessArea(String officeName);
}