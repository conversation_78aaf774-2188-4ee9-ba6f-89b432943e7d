package com.hsobs.ob.modules.arrange.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.arrange.entity.RentalRemind;
import com.hsobs.ob.modules.arrange.service.RentalRemindService;

/**
 * 租借提醒Controller
 * <AUTHOR>
 * @version 2025-03-27
 */
@Controller
@RequestMapping(value = "${adminPath}/arrange/rentalRemind")
public class RentalRemindController extends BaseController {

	@Autowired
	private RentalRemindService rentalRemindService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public RentalRemind get(String id, boolean isNewRecord) {
		return rentalRemindService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("arrange:rentalRemind:view")
	@RequestMapping(value = {"list", ""})
	public String list(RentalRemind rentalRemind, Model model) {
		model.addAttribute("rentalRemind", rentalRemind);
		return "modules/arrange/rentalRemindList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("arrange:rentalRemind:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<RentalRemind> listData(RentalRemind rentalRemind, HttpServletRequest request, HttpServletResponse response) {
		rentalRemind.setPage(new Page<>(request, response));
		Page<RentalRemind> page = rentalRemindService.findPage(rentalRemind);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("arrange:rentalRemind:view")
	@RequestMapping(value = "form")
	public String form(RentalRemind rentalRemind, Model model) {
		if (null == rentalRemind.getContractStatus() || rentalRemind.getContractStatus().isEmpty()) {
			rentalRemind.setContractStatus("1");
		}
		model.addAttribute("rentalRemind", rentalRemind);
		return "modules/arrange/rentalRemindForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("arrange:rentalRemind:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated RentalRemind rentalRemind) {
		rentalRemindService.save(rentalRemind);
		return renderResult(Global.TRUE, text("保存租借提醒成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("arrange:rentalRemind:view")
	@RequestMapping(value = "exportData")
	public void exportData(RentalRemind rentalRemind, HttpServletResponse response) {
		List<RentalRemind> list = rentalRemindService.findList(rentalRemind);
		String fileName = "租借提醒" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("租借提醒", RentalRemind.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("arrange:rentalRemind:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		RentalRemind rentalRemind = new RentalRemind();
		List<RentalRemind> list = ListUtils.newArrayList(rentalRemind);
		String fileName = "租借提醒模板.xlsx";
		try(ExcelExport ee = new ExcelExport("租借提醒", RentalRemind.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("arrange:rentalRemind:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = rentalRemindService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("arrange:rentalRemind:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(RentalRemind rentalRemind) {
		rentalRemindService.delete(rentalRemind);
		return renderResult(Global.TRUE, text("删除租借提醒成功！"));
	}
	
}