package com.hsobs.ob.modules.ownershipregisterledger.entity;

import javax.validation.constraints.Size;
import java.util.Date;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;

/**
 * 权属登记台账Entity
 * <AUTHOR>
 * @version 2025-03-01
 */
@Table(name="ob_ownership_registration", alias="a", label="权属登记台账信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="name", attrName="name", label="办公用房名称", isInsert=false, isUpdate=false, queryType=QueryType.LIKE),
		@Column(name="type", attrName="type", label="权属状态", isInsert=false, isUpdate=false),
		@Column(name="owner_office_code", attrName="ownerOfficeCode", label="权属单位名称", isInsert=false, isUpdate=false),
		@Column(includeEntity=DataEntity.class),
		@Column(name="work_order_code", attrName="workOrderCode", label="登记编号", isInsert=false, isUpdate=false, isQuery=false),
		@Column(name="content", attrName="content", label="登记内容", isInsert=false, isUpdate=false, isQuery=false),
		@Column(name="purpose", attrName="purpose", label="用途", isInsert=false, isUpdate=false, isQuery=false),
		@Column(name="coordinate_date", attrName="coordinateDate", label="协调日期", isInsert=false, isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="classified", attrName="classified", label="是否涉密", isInsert=false, isUpdate=false, isQuery=false),
		@Column(name="real_estate_type", attrName="realEstateType", label="不动产类型", isInsert=false, isUpdate=false, isQuery=false),
		@Column(name="owner_user_code", attrName="ownerUserCode", label="产权人", isInsert=false, isUpdate=false, isQuery=false),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用单位", isInsert=false, isUpdate=false, isQuery=false),
		@Column(name="used_user_code", attrName="usedUserCode", label="使用人", isInsert=false, isUpdate=false, isQuery=false),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "oi",
				on="oi.office_code = a.owner_office_code", attrName="office",
				columns = {@Column(includeEntity = Office.class)})
	},
	// 扩展Where里指定一个Key名字（与 sqlMap().add() 函数的第一个参数一致）
	extWhereKeys="extWhere", orderBy="a.update_date DESC"
)
public class ObOwnershipRegistration extends DataEntity<ObOwnershipRegistration> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 办公用房名称
	private String type;		// 权属状态
	private String ownerOfficeCode;		// 权属单位名称
	private String workOrderCode;		// 登记编号
	private String content;		// 登记内容
	private String purpose;		// 用途
	private Date coordinateDate;		// 协调日期
	private String classified;		// 是否涉密
	private String realEstateType;		// 不动产类型
	private String ownerUserCode;		// 产权人
	private String usedOfficeCode;		// 使用单位
	private String usedUserCode;		// 使用人
	private Office office; // 机构信息
	@ExcelFields({
		@ExcelField(title="主键ID", attrName="id", align=Align.CENTER, sort=10),
		@ExcelField(title="办公用房名称", attrName="name", align=Align.CENTER, sort=20),
		@ExcelField(title="权属状态", attrName="type", dictType="ob_ownership_registration_type", align=Align.CENTER, sort=30),
		@ExcelField(title="权属单位名称", attrName="office.officeName", align=Align.CENTER, sort=40),
		@ExcelField(title="审批状态", attrName="status", dictType="bpm_biz_status", align=Align.CENTER, sort=50),
	})
	public ObOwnershipRegistration() {
		this(null);
	}
	
	public ObOwnershipRegistration(String id){
		super(id);
	}
	
	@Size(min=0, max=512, message="办公用房名称长度不能超过 512 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@Size(min=0, max=100, message="权属状态长度不能超过 100 个字符")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
	
	@Size(min=0, max=64, message="权属单位名称长度不能超过 64 个字符")
	public String getOwnerOfficeCode() {
		return ownerOfficeCode;
	}

	public void setOwnerOfficeCode(String ownerOfficeCode) {
		this.ownerOfficeCode = ownerOfficeCode;
	}
	
	@Size(min=0, max=100, message="登记编号长度不能超过 100 个字符")
	public String getWorkOrderCode() {
		return workOrderCode;
	}

	public void setWorkOrderCode(String workOrderCode) {
		this.workOrderCode = workOrderCode;
	}
	
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	@Size(min=0, max=1000, message="用途长度不能超过 1000 个字符")
	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getCoordinateDate() {
		return coordinateDate;
	}

	public void setCoordinateDate(Date coordinateDate) {
		this.coordinateDate = coordinateDate;
	}
	
	@Size(min=0, max=1, message="是否涉密长度不能超过 1 个字符")
	public String getClassified() {
		return classified;
	}

	public void setClassified(String classified) {
		this.classified = classified;
	}
	
	@Size(min=0, max=100, message="不动产类型长度不能超过 100 个字符")
	public String getRealEstateType() {
		return realEstateType;
	}

	public void setRealEstateType(String realEstateType) {
		this.realEstateType = realEstateType;
	}
	
	@Size(min=0, max=64, message="产权人长度不能超过 64 个字符")
	public String getOwnerUserCode() {
		return ownerUserCode;
	}

	public void setOwnerUserCode(String ownerUserCode) {
		this.ownerUserCode = ownerUserCode;
	}
	
	@Size(min=0, max=64, message="使用单位长度不能超过 64 个字符")
	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}
	
	@Size(min=0, max=64, message="使用人长度不能超过 64 个字符")
	public String getUsedUserCode() {
		return usedUserCode;
	}

	public void setUsedUserCode(String usedUserCode) {
		this.usedUserCode = usedUserCode;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}
	
}