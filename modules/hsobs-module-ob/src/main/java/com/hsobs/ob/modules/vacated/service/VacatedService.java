package com.hsobs.ob.modules.vacated.service;

import java.util.List;

import com.hsobs.ob.modules.arrange.entity.Arrange;
import com.hsobs.ob.modules.bpm.service.ObCommonBpmService;
import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.hsobs.ob.modules.estate.service.RealEstateAddressService;
import com.hsobs.ob.modules.estate.service.RealEstateService;
import com.hsobs.ob.modules.vacated.dao.VacatedAssetDao;
import com.hsobs.ob.modules.vacated.entity.SaveVerifyDataRequest;
import com.hsobs.ob.modules.vacated.entity.VacatedAsset;
import com.hsobs.ob.modules.vacated.entity.VacatedLedger;
import com.hsobs.ob.modules.vacated.entity.VacatedQuery;
import com.jeesite.common.config.Global;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.vacated.entity.Vacated;
import com.hsobs.ob.modules.vacated.dao.VacatedDao;
import java.util.Map;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 清理腾退Service
 * <AUTHOR>
 * @version 2025-02-03
 */
@Service
public class VacatedService extends CrudService<VacatedDao, Vacated> {

	@Autowired
	private VacatedDao vacatedDao;

	@Autowired
	private RealEstateService realEstateService;

	@Autowired
	private RealEstateAddressService realEstateAddressService;

	@Autowired
	VacatedAssetDao vacatedAssetDao;

	@Autowired
	private ObCommonBpmService commonBpmService;

	@Autowired
	TaskService taskService;

	/**
	 * 获取单条数据
	 * @param vacated
	 * @return
	 */
	@Override
	public Vacated get(Vacated vacated) {
		return super.get(vacated);
	}

	/**
	 * 查询分页数据
	 * @param vacated 查询条件
	 * @param vacated page 分页对象
	 * @return
	 */
	@Override
	public Page<Vacated> findPage(Vacated vacated) {
		return super.findPage(vacated);
	}

	public Page<Vacated> findMztPage(Vacated vacated) {
//		vacated.setProcessName("现场核验");
//		return commonBpmService.findObjectList(null, "ob_vacated%", vacated, null);
		return super.findPage(vacated);
	}

	/**
	 * 加载子表数据
	 */
	public Vacated loadChildData(Vacated vacated) {
		if (vacated != null && !vacated.getIsNewRecord()){
			VacatedAsset where = new VacatedAsset();
			where.setVacatedId(vacated.getId());
			vacated.setVacatedAssetList(vacatedAssetDao.findList(where));
		}
		return vacated;
	}
	/**
	 * 查询列表数据
	 * @param vacated
	 * @return
	 */
	@Override
	public List<Vacated> findList(Vacated vacated) {
		return super.findList(vacated);
	}

	public void saveMZTApiData(SaveVerifyDataRequest request) {
		String vacatedId = request.getId();
		if (null == vacatedId || StringUtils.isBlank(vacatedId)) {
			return;
		}
		Vacated vacated = super.get(vacatedId);
		if (null == vacated || null == vacated.getId()) {
			return;
		}

		vacated.setVerificationDescribe(request.getHyqkms());

		super.save(vacated);
		request.getHyqktpList().forEach(hyqktp -> {
			FileUploadUtils.saveFileUpload(vacated.getId(), "obQwEstate_check", hyqktp.getFileId(), null);
		});
		Map<String, Object> variables = MapUtils.newHashMap();
		BpmProcIns bpmProcIns = BpmUtils.getProcIns(vacated, "ob_vacated");
		if (null != bpmProcIns) {
			vacated.getBpm().setProcInsId(bpmProcIns.getId());
			Task task = taskService.createTaskQuery().processInstanceId(bpmProcIns.getId()).singleResult();
			if (null != task) {
				vacated.getBpm().setTaskId(task.getId());
			}
		}
		vacated.setStatus(Vacated.STATUS_AUDIT);
		BpmUtils.complete(vacated, variables, null);
	}

	/**
	 * 保存数据（插入或更新）
	 * @param vacated
	 */
	@Override
	@Transactional
	public void save(Vacated vacated) {
		if (vacated.getIsNewRecord() || vacated.getStatus().equals(Vacated.STATUS_DRAFT)) {
			if (null != vacated.getRealEstateId() && !vacated.getRealEstateId().isEmpty()) {
				RealEstate realEstate = realEstateService.get(vacated.getRealEstateId());
				if (null == realEstate.getId() || null == realEstate.getUsedOfficeCode()) {
					throw new RuntimeException("资产无需腾退");
				}
				vacated.setApplyOfficeCode(realEstate.getUsedOfficeCode());
			} else {
				if (null == vacated.getRealEstateAddressId() && vacated.getRealEstateAddressId().isEmpty()) {
					throw new RuntimeException("请选择腾退资产");
				}
				RealEstateAddress realEstateAddress = realEstateAddressService.get(vacated.getRealEstateAddressId());
				if (null == realEstateAddress.getId() || null == realEstateAddress.getUsedOfficeCode()) {
					throw new RuntimeException("资产无需腾退");
				}
				vacated.setApplyOfficeCode(realEstateAddress.getUsedOfficeCode());
			}
		}
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(vacated.getStatus())){
			vacated.setStatus(Vacated.STATUS_AUDIT);
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (Vacated.STATUS_NORMAL.equals(vacated.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (Vacated.STATUS_DRAFT.equals(vacated.getStatus())
				|| Vacated.STATUS_AUDIT.equals(vacated.getStatus())){
			super.save(vacated);
		}

		// 如果为审核状态，则进行审批流操作
		if (Vacated.STATUS_AUDIT.equals(vacated.getStatus())){

			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", vacated.getLeaveDays());

			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(vacated.getBpm().getProcInsId())
					&& StringUtils.isBlank(vacated.getBpm().getTaskId())){
				BpmUtils.start(vacated, "ob_vacated", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(vacated, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(vacated, vacated.getId(), "vacated_file");
		FileUploadUtils.saveFileUpload(vacated, vacated.getId(), "obQwEstate_check");
		FileUploadUtils.saveFileUpload(vacated, vacated.getId(), "vacated_transfer_file");

		VacatedAsset clearVacatedAsset = new VacatedAsset();
		clearVacatedAsset.setVacatedId(vacated.getId());
		vacatedAssetDao.phyDeleteByEntity(clearVacatedAsset);

		for (VacatedAsset vacatedAsset : vacated.getVacatedAssetList()) {
			vacatedAsset.setVacatedId(vacated.getId());
			vacatedAssetDao.insert(vacatedAsset);
		}
	}

	/**
	 * 更新状态
	 * @param vacated
	 */
	@Override
	@Transactional
	public void updateStatus(Vacated vacated) {
		super.updateStatus(vacated);

		if (null!= vacated && null != vacated.getStatus() && vacated.getStatus().equals(Arrange.STATUS_NORMAL)){
			if (null == vacated.getRealEstateId()){
				if (null == vacated.getRealEstateAddressId()) {
					return;
				}
				String realEstateAddressId = vacated.getRealEstateAddressId();
				RealEstateAddress saveRealEstateAddress = new RealEstateAddress();
				saveRealEstateAddress.setId(realEstateAddressId);
				saveRealEstateAddress.setUsedOfficeCode(null);
				saveRealEstateAddress.setUsedUserCode(null);
				realEstateAddressService.save(saveRealEstateAddress);
			} else {
				String realEstateId = vacated.getRealEstateId();
				RealEstate saveRealEstate = new RealEstate();
				saveRealEstate.setId(realEstateId);
				saveRealEstate.setUsedOfficeCode(null);
				saveRealEstate.setUsedUserCode(null);
				realEstateService.save(saveRealEstate);
			}

		}
	}

	/**
	 * 删除数据
	 * @param vacated
	 */
	@Override
	@Transactional
	public void delete(Vacated vacated) {
		vacatedDao.phyDelete(vacated);
	}

	public List<VacatedLedger> listLedger(VacatedLedger vacatedLedger) {
		return vacatedDao.listLedger(vacatedLedger);
	}

	public Page<VacatedLedger> listLedgerData(VacatedLedger vacatedLedger) {
		Page<VacatedLedger> page = (Page<VacatedLedger>) vacatedLedger.getPage();
		List<VacatedLedger> list = dao.listLedger(vacatedLedger);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public Page<VacatedQuery> listQueryData(VacatedQuery vacatedQuery) {
		Page<VacatedQuery> page = (Page<VacatedQuery>) vacatedQuery.getPage();
		List<VacatedQuery> list = dao.listQueryData(vacatedQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public void addDataScopeFilter(VacatedQuery vacatedQuery) {
		SqlMap sqlMap = vacatedQuery.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"ov.APPLY_OFFICE_CODE",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}

	public void addDataScopeFilter(VacatedLedger vacatedLedger) {
		SqlMap sqlMap = vacatedLedger.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"ov.APPLY_OFFICE_CODE",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}

	public void taskAssigner(DelegateExecution execution) {
		System.out.println(execution.getEventName());
	}
}