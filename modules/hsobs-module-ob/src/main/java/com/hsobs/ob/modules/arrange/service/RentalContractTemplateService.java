package com.hsobs.ob.modules.arrange.service;

import java.util.List;

import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.entity.FileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.arrange.entity.RentalContractTemplate;
import com.hsobs.ob.modules.arrange.dao.RentalContractTemplateDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 租用合同模版表Service
 * <AUTHOR>
 * @version 2025-03-09
 */
@Service
public class RentalContractTemplateService extends CrudService<RentalContractTemplateDao, RentalContractTemplate> {

	@Autowired
	RentalContractTemplateDao rentalContractTemplateDao;

	/**
	 * 获取单条数据
	 * @param rentalContractTemplate
	 * @return
	 */
	@Override
	public RentalContractTemplate get(RentalContractTemplate rentalContractTemplate) {
		return super.get(rentalContractTemplate);
	}
	
	/**
	 * 查询分页数据
	 * @param rentalContractTemplate 查询条件
	 * @param rentalContractTemplate page 分页对象
	 * @return
	 */
	@Override
	public Page<RentalContractTemplate> findPage(RentalContractTemplate rentalContractTemplate) {
		return super.findPage(rentalContractTemplate);
	}
	
	/**
	 * 查询列表数据
	 * @param rentalContractTemplate
	 * @return
	 */
	@Override
	public List<RentalContractTemplate> findList(RentalContractTemplate rentalContractTemplate) {
		return super.findList(rentalContractTemplate);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param rentalContractTemplate
	 */
	@Override
	@Transactional
	public void save(RentalContractTemplate rentalContractTemplate) {
		long count = super.findCount(new RentalContractTemplate());
		if (count == 0) {
			rentalContractTemplate.setEnable("1");
		}
		super.save(rentalContractTemplate);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(rentalContractTemplate, rentalContractTemplate.getId(), "rentalContractTemplate_file");
		List<FileUpload> rentalContractTemplateFile = FileUploadUtils.findFileUpload(rentalContractTemplate.getId(), "rentalContractTemplate_file");
		if (null == rentalContractTemplateFile || rentalContractTemplateFile.isEmpty()) {
			throw new ServiceException(text("文件不符合要求"));
		}
	}

	public void enable(RentalContractTemplate rentalContractTemplate) {
		RentalContractTemplate newRentalContractTemplate = new RentalContractTemplate();
		newRentalContractTemplate.setEnable("0");
		RentalContractTemplate whereEntity = new RentalContractTemplate();
		whereEntity.setEnable("1");
		whereEntity.sqlMap().getWhere().and("enable", QueryType.EQ, "1");
		rentalContractTemplateDao.updateByEntity(newRentalContractTemplate, whereEntity);
		super.save(rentalContractTemplate);
	}
	
	/**
	 * 更新状态
	 * @param rentalContractTemplate
	 */
	@Override
	@Transactional
	public void updateStatus(RentalContractTemplate rentalContractTemplate) {
		super.updateStatus(rentalContractTemplate);
	}
	
	/**
	 * 删除数据
	 * @param rentalContractTemplate
	 */
	@Override
	@Transactional
	public void delete(RentalContractTemplate rentalContractTemplate) {
		rentalContractTemplateDao.delete(rentalContractTemplate);
	}
	
}