package com.hsobs.ob.modules.apply.entity;


import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 使用管理Entity
 * <AUTHOR>
 * @version 2025-02-08
 */
public class ApplyDetailQuery extends BpmEntity<ApplyDetailQuery> {

	private static final long serialVersionUID = 1L;
	private String officeName;		//
	private String officeCode;		// 使用单位
	private String userName;		// 使用人
	private String userCode;		// 使用人
	private String useType;		// 功能分类
	private String rank;		// 职级
	private String usageArea;		// 使用面积
	private String paId;		// 是否有偿使用
	private String status;		// 审批状态
	private Date usageDate;		// 使用日期
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	private String describe;   // 申请原由
    private String purpose;  // 用途
    private String position; // 位置
    private Integer roomNum;  // 房间数

	@Override
	public String getStatus() {
		return status;
	}

	@Override
	public void setStatus(String status) {
		this.status = status;
	}

	public String getPaId() {
		return paId;
	}

	public void setPaId(String paId) {
		this.paId = paId;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getUseType() {
		return useType;
	}

	public void setUseType(String useType) {
		this.useType = useType;
	}

	@ExcelFields({
			@ExcelField(title="使用单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=10),
			@ExcelField(title="功能分类", attrName="useType", dictType="occupancy_classification", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="使用人", attrName="userName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="职级", attrName="rank", dictType="ob_establishment_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="使用面积", attrName="usageArea", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="使用日期", attrName="usageDate", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="用途", attrName="purpose", align= ExcelField.Align.CENTER, sort=60),
			@ExcelField(title="位置", attrName="position", align= ExcelField.Align.CENTER, sort=70),
			@ExcelField(title="房间数", attrName="roomNum", align= ExcelField.Align.CENTER, sort=80),
			@ExcelField(title="申请原由", attrName="describe", align= ExcelField.Align.CENTER, sort=90),
			@ExcelField(title="审批状态", attrName="status", align= ExcelField.Align.CENTER, sort=110),
	})

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getRank() {
		return rank;
	}

	public void setRank(String rank) {
		this.rank = rank;
	}

	public String getUsageArea() {
		return usageArea;
	}

	public void setUsageArea(String usageArea) {
		this.usageArea = usageArea;
	}

	public Date getUsageDate() {
		return usageDate;
	}

	public void setUsageDate(Date usageDate) {
		this.usageDate = usageDate;
	}

	public String getDescribe() {
		return describe;
	}

	public void setDescribe(String describe) {
		this.describe = describe;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public Integer getRoomNum() {
		return roomNum;
	}

	public void setRoomNum(Integer roomNum) {
		this.roomNum = roomNum;
	}
}