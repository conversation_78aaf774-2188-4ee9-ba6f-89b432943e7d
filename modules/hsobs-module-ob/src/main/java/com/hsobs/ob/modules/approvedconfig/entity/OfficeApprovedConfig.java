package com.hsobs.ob.modules.approvedconfig.entity;


import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 办公室使用面积核定配置Entity
 * <AUTHOR>
 * @version 2025-03-09
 */
@Table(name="ob_office_approved_config", alias="a", label="办公室使用面积核定配置信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="area", attrName="area", label="面积", isUpdateForce=true),
		@Column(name="office_type", attrName="officeType", label="单位类别", isUpdateForce=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="修改人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, orderBy="CAST(a.id AS INT) ASC"
)
public class OfficeApprovedConfig extends DataEntity<OfficeApprovedConfig> {
	
	private static final long serialVersionUID = 1L;
	private Double area;		// 面积
	private String officeType;

	@ExcelFields({
		@ExcelField(title="单位类别", attrName="officeType", dictType="sys_office_type", align=Align.CENTER, sort=10),
		@ExcelField(title="适用对象", attrName="id", dictType="ob_establishment_type", align=Align.CENTER, sort=10),
		@ExcelField(title="面积", attrName="area", align=Align.CENTER, sort=20),
	})
	public OfficeApprovedConfig() {
		this(null);
	}
	
	public OfficeApprovedConfig(String id){
		super(id);
	}
	
	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}

	public String getOfficeType() {
		return officeType;
	}

	public void setOfficeType(String officeType) {
		this.officeType = officeType;
	}
}