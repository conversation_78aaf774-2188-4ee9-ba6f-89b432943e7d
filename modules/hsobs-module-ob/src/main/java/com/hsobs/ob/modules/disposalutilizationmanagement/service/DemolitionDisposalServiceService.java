package com.hsobs.ob.modules.disposalutilizationmanagement.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DemolitionDisposalService;
import com.hsobs.ob.modules.disposalutilizationmanagement.dao.DemolitionDisposalServiceDao;

/**
 * 拆除处置业务Service
 * <AUTHOR>
 * @version 2025-03-08
 */
@Service
public class DemolitionDisposalServiceService extends CrudService<DemolitionDisposalServiceDao, DemolitionDisposalService> {

    /**
     * 获取单条数据
     * @param demolitionDisposalService
     * @return
     */
    @Override
    public DemolitionDisposalService get(DemolitionDisposalService demolitionDisposalService) {
        return super.get(demolitionDisposalService);
    }

    /**
     * 查询分页数据
     * @param demolitionDisposalService 查询条件
     * @param demolitionDisposalService page 分页对象
     * @return
     */
    @Override
    public Page<DemolitionDisposalService> findPage(DemolitionDisposalService demolitionDisposalService) {
        return super.findPage(demolitionDisposalService);
    }

    /**
     * 查询列表数据
     * @param demolitionDisposalService
     * @return
     */
    @Override
    public List<DemolitionDisposalService> findList(DemolitionDisposalService demolitionDisposalService) {
        return super.findList(demolitionDisposalService);
    }

    /**
     * 保存数据（插入或更新）
     * @param demolitionDisposalService
     */
    @Override
    @Transactional
    public void save(DemolitionDisposalService demolitionDisposalService) {
        super.save(demolitionDisposalService);
    }

    /**
     * 更新状态
     * @param demolitionDisposalService
     */
    @Override
    @Transactional
    public void updateStatus(DemolitionDisposalService demolitionDisposalService) {
        super.updateStatus(demolitionDisposalService);
    }

    /**
     * 删除数据
     * @param demolitionDisposalService
     */
    @Override
    @Transactional
    public void delete(DemolitionDisposalService demolitionDisposalService) {
        super.delete(demolitionDisposalService);
    }

}