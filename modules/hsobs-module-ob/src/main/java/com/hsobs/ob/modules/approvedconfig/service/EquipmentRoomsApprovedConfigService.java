package com.hsobs.ob.modules.approvedconfig.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.approvedconfig.entity.EquipmentRoomsApprovedConfig;
import com.hsobs.ob.modules.approvedconfig.dao.EquipmentRoomsApprovedConfigDao;

/**
 * 设备用房面积核定Service
 * <AUTHOR>
 * @version 2025-03-09
 */
@Service
public class EquipmentRoomsApprovedConfigService extends CrudService<EquipmentRoomsApprovedConfigDao, EquipmentRoomsApprovedConfig> {
	
	/**
	 * 获取单条数据
	 * @param equipmentRoomsApprovedConfig
	 * @return
	 */
	@Override
	public EquipmentRoomsApprovedConfig get(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig) {
		return super.get(equipmentRoomsApprovedConfig);
	}
	
	/**
	 * 查询分页数据
	 * @param equipmentRoomsApprovedConfig 查询条件
	 * @param equipmentRoomsApprovedConfig page 分页对象
	 * @return
	 */
	@Override
	public Page<EquipmentRoomsApprovedConfig> findPage(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig) {
		return super.findPage(equipmentRoomsApprovedConfig);
	}
	
	/**
	 * 查询列表数据
	 * @param equipmentRoomsApprovedConfig
	 * @return
	 */
	@Override
	public List<EquipmentRoomsApprovedConfig> findList(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig) {
		return super.findList(equipmentRoomsApprovedConfig);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param equipmentRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void save(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig) {
		super.save(equipmentRoomsApprovedConfig);
	}

	public void initSave() {
		EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig = new EquipmentRoomsApprovedConfig();
		equipmentRoomsApprovedConfig.setId("2");
		equipmentRoomsApprovedConfig.setEquipmentRoomRatio(9.);
		equipmentRoomsApprovedConfig.setMultistoryUsageCoefficientMin(65.);
		equipmentRoomsApprovedConfig.setHighriseUsageCoefficientMin(60.);
		super.insert(equipmentRoomsApprovedConfig);
	}
	
	/**
	 * 更新状态
	 * @param equipmentRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void updateStatus(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig) {
		super.updateStatus(equipmentRoomsApprovedConfig);
	}
	
	/**
	 * 删除数据
	 * @param equipmentRoomsApprovedConfig
	 */
	@Override
	@Transactional
	public void delete(EquipmentRoomsApprovedConfig equipmentRoomsApprovedConfig) {
		super.delete(equipmentRoomsApprovedConfig);
	}
	
}