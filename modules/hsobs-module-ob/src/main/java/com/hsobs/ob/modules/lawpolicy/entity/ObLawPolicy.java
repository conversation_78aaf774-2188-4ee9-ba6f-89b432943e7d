package com.hsobs.ob.modules.lawpolicy.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 政策法规Entity
 * <AUTHOR>
 * @version 2025-02-15
 */
@Table(name="ob_law_policy", alias="a", label="政策法规信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="title", attrName="title", label="法规政策标题", queryType=QueryType.LIKE),
		@Column(name="content", attrName="content", label="法规政策说明"),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class ObLawPolicy extends DataEntity<ObLawPolicy> {
	
	private static final long serialVersionUID = 1L;
	private String title;		// 法规政策标题
	private String content;		// 法规政策说明

	public ObLawPolicy() {
		this(null);
	}
	
	public ObLawPolicy(String id){
		super(id);
	}
	
	@Size(min=0, max=255, message="法规政策标题长度不能超过 255 个字符")
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}
	
	@Size(min=0, max=255, message="法规政策说明长度不能超过 255 个字符")
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
}