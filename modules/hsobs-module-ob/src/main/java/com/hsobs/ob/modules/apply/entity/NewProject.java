package com.hsobs.ob.modules.apply.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 新建项目管理Entity
 * <AUTHOR>
 * @version 2025-03-16
 */
@Table(name="ob_new_project", alias="a", label="新建项目管理信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="name", attrName="name", label="办公用房名称", queryType=QueryType.LIKE),
		@Column(name="address", attrName="address", label="地址", queryType=QueryType.LIKE),
		@Column(name="classification", attrName="classification", label="办公用房分类", queryType=QueryType.LIKE),
		@Column(name="area", attrName="area", label="面积", queryType=QueryType.LTE),
		@Column(name="ability", attrName="ability", label="使用功能", queryType=QueryType.LIKE),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, orderBy="a.update_date DESC"
)
public class NewProject extends DataEntity<NewProject> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 办公用房名称
	private String address;		// 地址
	private String classification;		// 办公用房分类
	private Double area;		// 面积
	private String ability;		// 使用功能

	@ExcelFields({
		@ExcelField(title="办公用房名称", attrName="name", align=Align.CENTER, sort=20),
		@ExcelField(title="地址", attrName="address", align=Align.CENTER, sort=30),
		@ExcelField(title="办公用房分类", attrName="classification", align=Align.CENTER, sort=40),
		@ExcelField(title="面积", attrName="area", align=Align.CENTER, sort=50),
		@ExcelField(title="使用功能", attrName="ability", align=Align.CENTER, sort=60),
	})
	public NewProject() {
		this(null);
	}
	
	public NewProject(String id){
		super(id);
	}
	
	@NotBlank(message="办公用房名称不能为空")
	@Size(min=0, max=512, message="办公用房名称长度不能超过 512 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@NotBlank(message="地址不能为空")
	@Size(min=0, max=512, message="地址长度不能超过 512 个字符")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	
	@NotBlank(message="办公用房分类不能为空")
	@Size(min=0, max=512, message="办公用房分类长度不能超过 512 个字符")
	public String getClassification() {
		return classification;
	}

	public void setClassification(String classification) {
		this.classification = classification;
	}
	
	@NotNull(message="面积不能为空")
	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}
	
	@NotBlank(message="使用功能不能为空")
	public String getAbility() {
		return ability;
	}

	public void setAbility(String ability) {
		this.ability = ability;
	}
	
}