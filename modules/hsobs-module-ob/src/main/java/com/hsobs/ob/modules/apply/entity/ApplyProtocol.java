package com.hsobs.ob.modules.apply.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

/**
 * 有偿使用协议表Entity
 * <AUTHOR>
 * @version 2025-03-13
 */
@Table(name="ob_apply_protocol", alias="a", label="有偿使用协议表信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用单位"),
		@Column(name="personnel_number", attrName="personnelNumber", label="定编人数"),
		@Column(name="address", attrName="address", label="地址"),
		@Column(name="used_area", attrName="usedArea", label="使用面积"),
		@Column(name="remarks", attrName="remarks", label="备注", queryType=QueryType.LIKE),
		@Column(name="lease_begin_date", attrName="leaseBeginDate", label="租用开始时间"),
		@Column(name="lease_end_date", attrName="leaseEndDate", label="租用结束时间"),
		@Column(name="rent", attrName="rent", label="租金"),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false),
		@Column(name="registration_user_code", attrName="registrationUserCode", label="登记负责人", isQuery=false),
		@Column(name="handled_user_code", attrName="handledUserCode", label="经办人", isQuery=false),
		@Column(name="signing_date", attrName="signingDate", label="签订日期", isQuery=false),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "usedOffice",
				on = "a.used_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u1",
				attrName = "registrationUser",
				on = "a.registration_user_code = u1.user_code",
				columns = {@Column(includeEntity = User.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u2",
				attrName = "handledUser",
				on = "a.handled_user_code = u2.user_code",
				columns = {@Column(includeEntity = User.class)})
	}, orderBy="a.update_date DESC"
)
public class ApplyProtocol extends DataEntity<ApplyProtocol> {
	
	private static final long serialVersionUID = 1L;
	private String usedOfficeCode;		// 使用单位
	private Integer personnelNumber;		// 定编人数
	private String address;		// 地址
	private Double usedArea;		// 使用面积
	private Date leaseBeginDate;		// 租用开始时间
	private Date leaseEndDate;		// 租用结束时间
	private Double rent;		// 租金

	private String registrationUserCode;
	private String handledUserCode;
	private Date signingDate;

	private Office usedOffice;
	private User registrationUser;
	private User handledUser;

	public ApplyProtocol() {
		this(null);
	}
	
	public ApplyProtocol(String id){
		super(id);
	}
	
	@NotBlank(message="使用单位不能为空")
	@Size(min=0, max=64, message="使用单位长度不能超过 64 个字符")
	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}
	
	@NotNull(message="定编人数不能为空")
	public Integer getPersonnelNumber() {
		return personnelNumber;
	}

	public void setPersonnelNumber(Integer personnelNumber) {
		this.personnelNumber = personnelNumber;
	}
	
	@NotBlank(message="地址不能为空")
	@Size(min=0, max=512, message="地址长度不能超过 512 个字符")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	
	@NotNull(message="使用面积不能为空")
	public Double getUsedArea() {
		return usedArea;
	}

	public void setUsedArea(Double usedArea) {
		this.usedArea = usedArea;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="租用开始时间不能为空")
	public Date getLeaseBeginDate() {
		return leaseBeginDate;
	}

	public void setLeaseBeginDate(Date leaseBeginDate) {
		this.leaseBeginDate = leaseBeginDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="租用结束时间不能为空")
	public Date getLeaseEndDate() {
		return leaseEndDate;
	}

	public void setLeaseEndDate(Date leaseEndDate) {
		this.leaseEndDate = leaseEndDate;
	}
	
	@NotNull(message="租金不能为空")
	public Double getRent() {
		return rent;
	}

	public void setRent(Double rent) {
		this.rent = rent;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}

	public String getRegistrationUserCode() {
		return registrationUserCode;
	}

	public void setRegistrationUserCode(String registrationUserCode) {
		this.registrationUserCode = registrationUserCode;
	}

	public String getHandledUserCode() {
		return handledUserCode;
	}

	public void setHandledUserCode(String handledUserCode) {
		this.handledUserCode = handledUserCode;
	}

	public Date getSigningDate() {
		return signingDate;
	}

	public void setSigningDate(Date signingDate) {
		this.signingDate = signingDate;
	}

	public User getRegistrationUser() {
		return registrationUser;
	}

	public void setRegistrationUser(User registrationUser) {
		this.registrationUser = registrationUser;
	}

	public User getHandledUser() {
		return handledUser;
	}

	public void setHandledUser(User handledUser) {
		this.handledUser = handledUser;
	}
}