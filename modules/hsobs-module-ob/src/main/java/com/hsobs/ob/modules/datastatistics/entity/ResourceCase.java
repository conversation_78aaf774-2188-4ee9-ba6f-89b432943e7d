package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;

/**
 * <AUTHOR>
 * @title: ResourceCase
 * @projectName base
 * @description: 省直单位办公业务用房房产资源情况表(表一)
 * @date 2024/12/1919:15
 */
public class ResourceCase extends DataEntity<ResourceCase> {
    private String officeName;
    private String officeCode;

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    private String officeType;
    private String address;
    private Integer floorNumber;
    private Integer principalNumber;
    private Integer deputyNumber;
    private Integer bureauNumber;
    private Integer deputyBureauNumber;
    private Integer divisionNumber;
    private Integer deputyDivisionNumber;
    private Integer underDivisionNumber;
    private Integer staffingTotalNumber;
    private float officeSpace;
    private float serviceSpace;
    private float deviceSpace;
    private float baseTotalSpace;
    private float structureTotalSpace;
    private float auxiliarySpace;
    private float officeNowSpace;
    private float technologySpace;
    private float totalSpace;
    private float siteSpace;
    private String propertyUnit;
    private String constructionAge;
    private String rentTag;
    private String remark;

    public ResourceCase() {

    }
    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getOfficeType() {
        return officeType;
    }

    public void setOfficeType(String officeType) {
        this.officeType = officeType;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getFloorNumber() {
        return floorNumber;
    }

    public void setFloorNumber(Integer floorNumber) {
        this.floorNumber = floorNumber;
    }

    public Integer getPrincipalNumber() {
        return principalNumber;
    }

    public void setPrincipalNumber(Integer principalNumber) {
        this.principalNumber = principalNumber;
    }

    public Integer getDeputyNumber() {
        return deputyNumber;
    }

    public void setDeputyNumber(Integer deputyNumber) {
        this.deputyNumber = deputyNumber;
    }

    public float getOfficeSpace() {
        return officeSpace;
    }

    public void setOfficeSpace(float officeSpace) {
        this.officeSpace = officeSpace;
    }

    public float getServiceSpace() {
        return serviceSpace;
    }

    public void setServiceSpace(float serviceSpace) {
        this.serviceSpace = serviceSpace;
    }

    public float getAuxiliarySpace() {
        return auxiliarySpace;
    }

    public void setAuxiliarySpace(float auxiliarySpace) {
        this.auxiliarySpace = auxiliarySpace;
    }

    public float getTechnologySpace() {
        return technologySpace;
    }

    public void setTechnologySpace(float technologySpace) {
        this.technologySpace = technologySpace;
    }

    public float getTotalSpace() {
        return totalSpace;
    }

    public void setTotalSpace(float totalSpace) {
        this.totalSpace = totalSpace;
    }

    public float getSiteSpace() {
        return siteSpace;
    }

    public Integer getBureauNumber() {
        return bureauNumber;
    }

    public void setBureauNumber(Integer bureauNumber) {
        this.bureauNumber = bureauNumber;
    }

    public Integer getDeputyBureauNumber() {
        return deputyBureauNumber;
    }

    public void setDeputyBureauNumber(Integer deputyBureauNumber) {
        this.deputyBureauNumber = deputyBureauNumber;
    }

    public Integer getDivisionNumber() {
        return divisionNumber;
    }

    public void setDivisionNumber(Integer divisionNumber) {
        this.divisionNumber = divisionNumber;
    }

    public Integer getDeputyDivisionNumber() {
        return deputyDivisionNumber;
    }

    public void setDeputyDivisionNumber(Integer deputyDivisionNumber) {
        this.deputyDivisionNumber = deputyDivisionNumber;
    }

    public Integer getStaffingTotalNumber() {
        return staffingTotalNumber;
    }

    public void setStaffingTotalNumber(Integer staffingTotalNumber) {
        this.staffingTotalNumber = staffingTotalNumber;
    }

    public float getDeviceSpace() {
        return deviceSpace;
    }

    public void setDeviceSpace(float deviceSpace) {
        this.deviceSpace = deviceSpace;
    }

    public float getBaseTotalSpace() {
        return baseTotalSpace;
    }

    public void setBaseTotalSpace(float baseTotalSpace) {
        this.baseTotalSpace = baseTotalSpace;
    }

    public float getOfficeNowSpace() {
        return officeNowSpace;
    }

    public void setOfficeNowSpace(float officeNowSpace) {
        this.officeNowSpace = officeNowSpace;
    }

    public String getPropertyUnit() {
        return propertyUnit;
    }

    public void setPropertyUnit(String propertyUnit) {
        this.propertyUnit = propertyUnit;
    }

    public String getConstructionAge() {
        return constructionAge;
    }

    public void setConstructionAge(String constructionAge) {
        this.constructionAge = constructionAge;
    }

    public String getRentTag() {
        return rentTag;
    }

    public void setRentTag(String rentTag) {
        this.rentTag = rentTag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setSiteSpace(float siteSpace) {
        this.siteSpace = siteSpace;
    }

    public Integer getUnderDivisionNumber() {
        return underDivisionNumber;
    }

    public void setUnderDivisionNumber(Integer underDivisionNumber) {
        this.underDivisionNumber = underDivisionNumber;
    }

    public float getStructureTotalSpace() {
        return structureTotalSpace;
    }

    public void setStructureTotalSpace(float structureTotalSpace) {
        this.structureTotalSpace = structureTotalSpace;
    }
}
