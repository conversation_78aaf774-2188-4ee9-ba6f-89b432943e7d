package com.hsobs.ob.modules.approvedconfig.service;

import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.approvedconfig.entity.OfficeApprovedConfig;
import com.hsobs.ob.modules.approvedconfig.dao.OfficeApprovedConfigDao;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 办公室使用面积核定配置Service
 * <AUTHOR>
 * @version 2025-03-09
 */
@Service
public class OfficeApprovedConfigService extends CrudService<OfficeApprovedConfigDao, OfficeApprovedConfig> {
	
	/**
	 * 获取单条数据
	 * @param officeApprovedConfig
	 * @return
	 */
	@Override
	public OfficeApprovedConfig get(OfficeApprovedConfig officeApprovedConfig) {
		return super.get(officeApprovedConfig);
	}

	public List<OfficeApprovedConfig> findAll(OfficeApprovedConfig officeApprovedConfig) {
		return super.findList(officeApprovedConfig);
	}

	/**
	 * 查询分页数据
	 * @param officeApprovedConfig 查询条件
	 * @param officeApprovedConfig page 分页对象
	 * @return
	 */
	@Override
	public Page<OfficeApprovedConfig> findPage(OfficeApprovedConfig officeApprovedConfig) {
		return super.findPage(officeApprovedConfig);
	}
	
	/**
	 * 查询列表数据
	 * @param officeApprovedConfig
	 * @return
	 */
	@Override
	public List<OfficeApprovedConfig> findList(OfficeApprovedConfig officeApprovedConfig) {
		return super.findList(officeApprovedConfig);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param officeApprovedConfig
	 */
	@Override
	@Transactional
	public void save(OfficeApprovedConfig officeApprovedConfig) {
		super.save(officeApprovedConfig);
	}

	public void saveAll(List<OfficeApprovedConfig> officeApprovedConfigs) {
        for (OfficeApprovedConfig officeApprovedConfig : officeApprovedConfigs) {
            super.save(officeApprovedConfig);
        }
    }

	@Transactional
	public void initSave() {
		List<OfficeApprovedConfig> officeApprovedConfigList = new ArrayList<>();
		officeApprovedConfigList.add(buildOfficeApprovedConfig("30", 54., "2"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("40", 42., "2"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("50", 30., "2"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("60", 24., "2"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("70", 18., "2"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("80", 12., "2"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("90", 9., "2"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("91", 42., "3"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("92", 30., "3"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("93", 24., "3"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("94", 18., "3"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("95", 9., "3"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("96", 30., "4"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("97", 24., "4"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("98", 18., "4"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("99", 12., "4"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("100", 9., "4"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("101", 24., "5"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("102", 18., "5"));
		officeApprovedConfigList.add(buildOfficeApprovedConfig("103", 9., "5"));
		officeApprovedConfigList.forEach(super::insert);
	}

	private OfficeApprovedConfig buildOfficeApprovedConfig(String id, Double area, String officeType) {
		OfficeApprovedConfig officeApprovedConfig = new OfficeApprovedConfig();
		officeApprovedConfig.setId(id);
		officeApprovedConfig.setArea(area);
		officeApprovedConfig.setOfficeType(officeType);
		return officeApprovedConfig;
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<OfficeApprovedConfig> list = ei.getDataList(OfficeApprovedConfig.class);
			for (OfficeApprovedConfig officeApprovedConfig : list) {
				try{
					ValidatorUtils.validateWithException(officeApprovedConfig);
					this.save(officeApprovedConfig);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + officeApprovedConfig.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + officeApprovedConfig.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
	/**
	 * 更新状态
	 * @param officeApprovedConfig
	 */
	@Override
	@Transactional
	public void updateStatus(OfficeApprovedConfig officeApprovedConfig) {
		super.updateStatus(officeApprovedConfig);
	}
	
	/**
	 * 删除数据
	 * @param officeApprovedConfig
	 */
	@Override
	@Transactional
	public void delete(OfficeApprovedConfig officeApprovedConfig) {
		super.delete(officeApprovedConfig);
	}
	
}