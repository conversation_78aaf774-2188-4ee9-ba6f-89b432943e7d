package com.hsobs.ob.modules.housingverification.web;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 用房核定Controller
 * <AUTHOR>
 * @version 2025-03-02
 */
@Controller
@RequestMapping(value = "${adminPath}/housingVerification")
public class HousingVerificationController {

    @RequiresPermissions("housingVerification:leadingCadres:view")
    @RequestMapping(value = {"leadingCadres", ""})
    public String leadingCadres() {
        return "modules/housingverification/leadingCadresList";
    }

    @RequiresPermissions("housingVerification:businessPremises:view")
    @RequestMapping(value = {"businessPremises", ""})
    public String businessPremises(RealEstate realEstate, Model model) {
        model.addAttribute("realEstate", realEstate);
        return "modules/housingverification/businessPremisesList";
    }
}
