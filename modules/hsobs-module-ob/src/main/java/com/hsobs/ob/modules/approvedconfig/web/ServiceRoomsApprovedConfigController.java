package com.hsobs.ob.modules.approvedconfig.web;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.ob.modules.approvedconfig.entity.ServiceRoomsApprovedConfigForm;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.approvedconfig.entity.ServiceRoomsApprovedConfig;
import com.hsobs.ob.modules.approvedconfig.service.ServiceRoomsApprovedConfigService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 服务用房使用面积核定Controller
 * <AUTHOR>
 * @version 2025-03-09
 */
@Controller
@RequestMapping(value = "${adminPath}/approvedconfig/serviceRoomsApprovedConfig")
public class ServiceRoomsApprovedConfigController extends BaseController {

	@Autowired
	private ServiceRoomsApprovedConfigService serviceRoomsApprovedConfigService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ServiceRoomsApprovedConfig get(String id, boolean isNewRecord) {
		return serviceRoomsApprovedConfigService.get(id, isNewRecord);
	}



	/**
	 * 查询列表
	 */
	@RequiresPermissions("approvedconfig:serviceRoomsApprovedConfig:view")
	@RequestMapping(value = {"list", ""})
	public String list(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig, Model model) {
		model.addAttribute("serviceRoomsApprovedConfig", serviceRoomsApprovedConfig);
		return "modules/approvedconfig/serviceRoomsApprovedConfigList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("approvedconfig:serviceRoomsApprovedConfig:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public List<ServiceRoomsApprovedConfig> listData(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig, HttpServletRequest request, HttpServletResponse response) {
		List<ServiceRoomsApprovedConfig> serviceRoomsApprovedConfigList;
		serviceRoomsApprovedConfigList = serviceRoomsApprovedConfigService.findList(serviceRoomsApprovedConfig);
		if (serviceRoomsApprovedConfigList.isEmpty()) {
			serviceRoomsApprovedConfigService.initSave();
		}
		serviceRoomsApprovedConfigList = serviceRoomsApprovedConfigService.findList(serviceRoomsApprovedConfig);
		return serviceRoomsApprovedConfigList;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("approvedconfig:serviceRoomsApprovedConfig:view")
	@RequestMapping(value = "form")
	public String form(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig, Model model) {
		model.addAttribute("serviceRoomsApprovedConfig", serviceRoomsApprovedConfig);
		List<ServiceRoomsApprovedConfig> serviceRoomsApprovedConfigList;
		serviceRoomsApprovedConfig.sqlMap().getOrder().setOrderBy("a.update_date asc");
		serviceRoomsApprovedConfigList = serviceRoomsApprovedConfigService.findList(serviceRoomsApprovedConfig);
		if (serviceRoomsApprovedConfigList.isEmpty()) {
			serviceRoomsApprovedConfigService.initSave();
		}
		serviceRoomsApprovedConfigList = serviceRoomsApprovedConfigService.findList(serviceRoomsApprovedConfig);
		model.addAttribute("serviceRoomsApprovedConfigList", serviceRoomsApprovedConfigList);
		return "modules/approvedconfig/serviceRoomsApprovedConfigForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("approvedconfig:serviceRoomsApprovedConfig:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ServiceRoomsApprovedConfigForm form) {
		serviceRoomsApprovedConfigService.saveAll(form.getServiceRoomsApprovedConfigList());
		return renderResult(Global.TRUE, text("保存服务用房使用面积核定成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("approvedconfig:serviceRoomsApprovedConfig:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig) {
		serviceRoomsApprovedConfigService.delete(serviceRoomsApprovedConfig);
		return renderResult(Global.TRUE, text("删除服务用房使用面积核定成功！"));
	}
	/**
	 * 导出数据
	 */
	@RequiresPermissions("approvedconfig:serviceRoomsApprovedConfig:view")
	@RequestMapping(value = "exportData")
	public void exportData(ServiceRoomsApprovedConfig serviceRoomsApprovedConfig, HttpServletResponse response) {
		List<ServiceRoomsApprovedConfig> list = serviceRoomsApprovedConfigService.findList(serviceRoomsApprovedConfig);
		String fileName = "服务用房使用面积核定" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("服务用房使用面积核定", ServiceRoomsApprovedConfig.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("approvedconfig:serviceRoomsApprovedConfig:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		ServiceRoomsApprovedConfig serviceRoomsApprovedConfig = new ServiceRoomsApprovedConfig();
		List<ServiceRoomsApprovedConfig> list = ListUtils.newArrayList(serviceRoomsApprovedConfig);
		String fileName = "服务用房使用面积核定模板.xlsx";
		try(ExcelExport ee = new ExcelExport("服务用房使用面积核定", ServiceRoomsApprovedConfig.class, ExcelField.Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("approvedconfig:serviceRoomsApprovedConfig:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = serviceRoomsApprovedConfigService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
}