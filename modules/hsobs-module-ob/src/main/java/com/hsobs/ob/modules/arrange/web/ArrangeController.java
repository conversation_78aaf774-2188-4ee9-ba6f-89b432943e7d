package com.hsobs.ob.modules.arrange.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.ob.modules.arrange.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.utils.DictUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.arrange.service.ArrangeService;

import java.util.List;

/**
 * 配置管理Controller
 * <AUTHOR>
 * @version 2025-01-05
 */
@Controller
@RequestMapping(value = "${adminPath}/arrange/")
public class ArrangeController extends BaseController {

	@Autowired
	private ArrangeService arrangeService;

	@Autowired
	TaskService taskService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public Arrange get(String id, boolean isNewRecord) {
		return arrangeService.get(id, isNewRecord);
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = {"list", ""})
	public String list(Arrange arrange, Model model) {
		arrange.setArrangeTypeName(DictUtils.getDictLabel("ob_arrange_type", arrange.getArrangeType(), "配置管理"));
		model.addAttribute("arrange", arrange);
		return "modules/arrange/arrangeList";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = {"listLedger", ""})
	public String listLedger(ArrangeLedger arrangeLedger, Model model) {
		model.addAttribute("arrangeLedger", arrangeLedger);
		return "modules/arrange/arrangeListLedger";
	}
	/**
	 * 查询列表-调剂查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = {"transferQuery", ""})
	public String transferQuery(TransferQuery transferQuery, Model model) {
		model.addAttribute("transferQuery", transferQuery);
		return "modules/arrange/transferQuery";
	}

	/**
	 * 查询列表-置换查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = {"exchangeQuery", ""})
	public String exchangeQuery(ExchangeQuery exchangeQuery, Model model) {
		model.addAttribute("exchangeQuery", exchangeQuery);
		return "modules/arrange/exchangeQuery";
	}

	/**
	 * 查询列表-租用信息查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = {"rentInfoQuery", ""})
	public String rentInfoQuery(RentInfoQuery rentInfoQuery, Model model) {
		model.addAttribute("rentInfoQuery", rentInfoQuery);
		return "modules/arrange/rentInfoQuery";
	}

	/**
	 * 查询列表-租用信息查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = {"arrangeFormContract"})
	public String arrangeFormContract(RentInfoQuery rentInfoQuery, Model model) {
		model.addAttribute("rentInfoQuery", rentInfoQuery);
		return "modules/arrange/arrangeFormContract";
	}

	/**
	 * 查询列表-建设项目查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = {"projectQuery", ""})
	public String projectQuery(ProjectQuery projectQuery, Model model) {
		model.addAttribute("projectQuery", projectQuery);
		return "modules/arrange/projectQuery";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<Arrange> listData(Arrange arrange, HttpServletRequest request, HttpServletResponse response) {
		arrangeService.addDataScopeFilter(arrange);
		arrange.setPage(new Page<>(request, response));
		Page<Arrange> page = arrangeService.findPage(arrange);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = "listLedgerData")
	@ResponseBody
	public Page<ArrangeLedger> listLedgerData(ArrangeLedger arrangeLedger, HttpServletRequest request, HttpServletResponse response) {
		arrangeLedger.setPage(new Page<>(request, response));
		arrangeService.addDataScopeFilter(arrangeLedger);
		Page<ArrangeLedger> page = arrangeService.listLedgerData(arrangeLedger);
		return page;
	}

	/**
	 * 查询列表数据-调剂查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = "listDataTransferQuery")
	@ResponseBody
	public Page<TransferQuery> listDataTransferQuery(TransferQuery transferQuery, HttpServletRequest request, HttpServletResponse response) {
		transferQuery.setPage(new Page<>(request, response));
		arrangeService.addDataScopeFilterByTransferQuery(transferQuery);
		Page<TransferQuery> page = arrangeService.listDataTransferQuery(transferQuery);
		return page;
	}

	/**
	 * 查询列表数据-置换查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = "listDataExchangeQuery")
	@ResponseBody
	public Page<ExchangeQuery> listDataExchangeQuery(ExchangeQuery exchangeQuery, HttpServletRequest request, HttpServletResponse response) {
		exchangeQuery.setPage(new Page<>(request, response));
		Page<ExchangeQuery> page = arrangeService.listDataExchangeQuery(exchangeQuery);
		return page;
	}

	/**
	 * 查询列表数据-租用查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = "listDataRentInfoQuery")
	@ResponseBody
	public Page<RentInfoQuery> listDataRentInfoQuery(RentInfoQuery rentInfoQuery, HttpServletRequest request, HttpServletResponse response) {
		rentInfoQuery.setPage(new Page<>(request, response));

		arrangeService.addDataScopeFilterByRentInfoQuery(rentInfoQuery);
		Page<RentInfoQuery> page = arrangeService.listDataRentInfoQuery(rentInfoQuery);
		return page;
	}

	/**
	 * 查询列表数据-建设项目查询
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = "listDataProjectQuery")
	@ResponseBody
	public Page<ProjectQuery> listDataProjectQuery(ProjectQuery projectQuery, HttpServletRequest request, HttpServletResponse response) {
		projectQuery.setPage(new Page<>(request, response));
		Page<ProjectQuery> page = arrangeService.listDataProjectQuery(projectQuery);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = "form")
	public String form(Arrange arrange, Model model) {
		if (arrange.getIsNewRecord()) {
			User currentUser = UserUtils.getUser();
			arrange.setUsedUserCode(currentUser.getUserCode());
			arrange.setUsedUser(currentUser);
			if (null != currentUser.getRefObj()) {
				Employee employee = currentUser.getRefObj();
				if (null != employee) {
					if (null != employee.getOffice()) {
						arrange.setUsedOfficeCode(employee.getOffice().getOfficeCode());
					}
					arrange.setUsedOffice(employee.getOffice());
				}
			}
		}
		arrangeService.loadChildData(arrange);
		model.addAttribute("arrange", arrange);
		boolean commonReadonly= null != arrange.getStatus() && !arrange.getStatus().equals("9");
		model.addAttribute("commonReadonly", commonReadonly);
		String currentTaskName = "";
		BpmProcIns bpmProcIns = BpmUtils.getProcIns(arrange, "adjustment_application");
		if (null != bpmProcIns) {
			Task task = taskService.createTaskQuery().processInstanceId(bpmProcIns.getId()).singleResult();
			if (null != task) {
				currentTaskName = task.getName();
			}
		}
		model.addAttribute("currentTaskName", currentTaskName);
		return "modules/arrange/arrangeForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("arrange::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated Arrange arrange) {
		arrangeService.save(arrange);
		return renderResult(Global.TRUE, text("保存成功！"));
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("arrange::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(Arrange arrange) {
		if (!Arrange.STATUS_DRAFT.equals(arrange.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		arrangeService.delete(arrange);
		return renderResult(Global.TRUE, text("删除成功！"));
	}

	/**
	 * 生成租用合同
	 */
	@RequiresPermissions("arrange::edit")
	@RequestMapping(value = "genContract")
	@ResponseBody
	public String genContract(Arrange arrange) {
		arrangeService.genContractById(arrange.getId());
		return renderResult(Global.TRUE, text("合同生成成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("arrange::view")
	@RequestMapping(value = "exportListData")
	public void exportListData(Arrange arrange, HttpServletResponse response) {
		arrangeService.addDataScopeFilter(arrange);
		List<Arrange> list = arrangeService.findList(arrange);
		String fileName = "配置信息" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("配置信息台账", Arrange.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportData")
	public void exportData(ArrangeLedger arrangeLedger, HttpServletResponse response) {
		List<ArrangeLedger> list = arrangeService.listLedger(arrangeLedger);
		String fileName = "配置明细台账" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("配置明细台账", ArrangeLedger.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据-调剂查询
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportDataTransferQuery")
	public void exportDataTransferQuery(TransferQuery transferQuery, HttpServletResponse response) {
		List<TransferQuery> list = arrangeService.exportDataTransferQuery(transferQuery);
		String fileName = "调剂查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("调剂查询", TransferQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据-置换查询
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportDataExchangeQuery")
	public void exportDataExchangeQuery(ExchangeQuery exchangeQuery, HttpServletResponse response) {
		List<ExchangeQuery> list = arrangeService.exportDataExchangeQuery(exchangeQuery);
		String fileName = "置换查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("置换查询", ExchangeQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据-租用信息查询
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportDataRentInfoQuery")
	public void exportDataRentInfoQuery(RentInfoQuery rentInfoQuery, HttpServletResponse response) {
		List<RentInfoQuery> list = arrangeService.exportDataRentInfoQuery(rentInfoQuery);
		String fileName = "租用信息查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("租用信息查询", RentInfoQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据-建设项目查询
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportDataProjectQuery")
	public void exportDataProjectQuery(ProjectQuery projectQuery, HttpServletResponse response) {
		List<ProjectQuery> list = arrangeService.exportDataProjectQuery(projectQuery);
		String fileName = "建设项目查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("建设项目查询", ProjectQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}