package com.hsobs.ob.modules.disposalutilizationmanagement.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.AuctionTransferDisposalBusiness;
import com.hsobs.ob.modules.disposalutilizationmanagement.dao.AuctionTransferDisposalBusinessDao;

/**
 * 拍卖转让处置业务Service
 * <AUTHOR>
 * @version 2025-03-08
 */
@Service
public class AuctionTransferDisposalBusinessService extends CrudService<AuctionTransferDisposalBusinessDao, AuctionTransferDisposalBusiness> {

    /**
     * 获取单条数据
     * @param auctionTransferDisposalBusiness
     * @return
     */
    @Override
    public AuctionTransferDisposalBusiness get(AuctionTransferDisposalBusiness auctionTransferDisposalBusiness) {
        return super.get(auctionTransferDisposalBusiness);
    }

    /**
     * 查询分页数据
     * @param auctionTransferDisposalBusiness 查询条件
     * @param auctionTransferDisposalBusiness page 分页对象
     * @return
     */
    @Override
    public Page<AuctionTransferDisposalBusiness> findPage(AuctionTransferDisposalBusiness auctionTransferDisposalBusiness) {
        return super.findPage(auctionTransferDisposalBusiness);
    }

    /**
     * 查询列表数据
     * @param auctionTransferDisposalBusiness
     * @return
     */
    @Override
    public List<AuctionTransferDisposalBusiness> findList(AuctionTransferDisposalBusiness auctionTransferDisposalBusiness) {
        return super.findList(auctionTransferDisposalBusiness);
    }

    /**
     * 保存数据（插入或更新）
     * @param auctionTransferDisposalBusiness
     */
    @Override
    @Transactional
    public void save(AuctionTransferDisposalBusiness auctionTransferDisposalBusiness) {
        super.save(auctionTransferDisposalBusiness);
    }

    /**
     * 更新状态
     * @param auctionTransferDisposalBusiness
     */
    @Override
    @Transactional
    public void updateStatus(AuctionTransferDisposalBusiness auctionTransferDisposalBusiness) {
        super.updateStatus(auctionTransferDisposalBusiness);
    }

    /**
     * 删除数据
     * @param auctionTransferDisposalBusiness
     */
    @Override
    @Transactional
    public void delete(AuctionTransferDisposalBusiness auctionTransferDisposalBusiness) {
        super.delete(auctionTransferDisposalBusiness);
    }

}