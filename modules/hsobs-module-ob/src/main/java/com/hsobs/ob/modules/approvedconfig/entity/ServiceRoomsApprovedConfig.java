package com.hsobs.ob.modules.approvedconfig.entity;


import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;

/**
 * 服务用房使用面积核定Entity
 * <AUTHOR>
 * @version 2025-03-09
 */
@Table(name="ob_service_rooms_approved_config", alias="a", label="服务用房使用面积核定信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="min_area", attrName="minArea", label="最小面积", isUpdateForce=true),
		@Column(name="max_area", attrName="maxArea", label="最大面积", isUpdateForce=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="修改人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, orderBy="CAST(a.id AS INT) ASC"
)
public class ServiceRoomsApprovedConfig extends DataEntity<ServiceRoomsApprovedConfig> {
	
	private static final long serialVersionUID = 1L;
	private Double minArea;		// 最小面积
	private Double maxArea;		// 最大面积

	@ExcelFields({
			@ExcelField(title="适用对象", attrName="id", dictType="sys_office_type" , align=Align.CENTER, sort=10),
			@ExcelField(title="最小面积", attrName="minArea", align=Align.CENTER, sort=20),
			@ExcelField(title="最大面积", attrName="maxArea", align=Align.CENTER, sort=30),
	})
	public ServiceRoomsApprovedConfig() {
		this(null);
	}
	
	public ServiceRoomsApprovedConfig(String id){
		super(id);
	}
	
	public Double getMinArea() {
		return minArea;
	}

	public void setMinArea(Double minArea) {
		this.minArea = minArea;
	}
	
	public Double getMaxArea() {
		return maxArea;
	}

	public void setMaxArea(Double maxArea) {
		this.maxArea = maxArea;
	}
	
}