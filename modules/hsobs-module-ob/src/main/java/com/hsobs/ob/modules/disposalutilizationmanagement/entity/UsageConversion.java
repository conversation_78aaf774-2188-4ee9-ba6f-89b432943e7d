package com.hsobs.ob.modules.disposalutilizationmanagement.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 转换用途Entity
 * <AUTHOR>
 * @version 2025-03-08
 */
@Table(name="ob_usage_conversion", alias="a", label="转换用途信息", columns={
        @Column(name="id", attrName="id", label="编号", isPK=true),
        @Column(name="disposal_id", attrName="disposalId", label="处置编号"),
        @Column(name="converted_usage", attrName="convertedUsage", label="转换后用途"),
        @Column(name="approval_unit", attrName="approvalUnit", label="审批单位"),
        @Column(name="approval_time", attrName="approvalTime", label="审批时间", isUpdateForce=true),
        @Column(includeEntity=DataEntity.class),
}, orderBy="a.update_date DESC"
)
public class UsageConversion extends DataEntity<UsageConversion> {

    private static final long serialVersionUID = 1L;
    private String disposalId;		// 处置编号
    private String convertedUsage;		// 转换后用途
    private String approvalUnit;		// 审批单位
    private Date approvalTime;		// 审批时间

    public UsageConversion() {
        this(null);
    }

    public UsageConversion(String id){
        super(id);
    }

    @Size(min=0, max=64, message="处置编号长度不能超过 64 个字符")
    public String getDisposalId() {
        return disposalId;
    }

    public void setDisposalId(String disposalId) {
        this.disposalId = disposalId;
    }

    @Size(min=0, max=255, message="转换后用途长度不能超过 255 个字符")
    public String getConvertedUsage() {
        return convertedUsage;
    }

    public void setConvertedUsage(String convertedUsage) {
        this.convertedUsage = convertedUsage;
    }

    @Size(min=0, max=255, message="审批单位长度不能超过 255 个字符")
    public String getApprovalUnit() {
        return approvalUnit;
    }

    public void setApprovalUnit(String approvalUnit) {
        this.approvalUnit = approvalUnit;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    public Date getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(Date approvalTime) {
        this.approvalTime = approvalTime;
    }

}