package com.hsobs.ob.modules.disposalutilizationmanagement.service;

import java.util.List;

import com.hsobs.ob.modules.apply.entity.ApplyLedger;
import com.hsobs.ob.modules.disposalutilizationmanagement.dao.DisposalUtilizationManagementDao;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.*;
import com.hsobs.ob.modules.earlywarnmessage.entity.EarlyWarnMessage;
import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.service.RealEstateService;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.support.EmployeeServiceSupport;
import com.jeesite.modules.sys.service.support.OfficeServiceSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagement;
import com.hsobs.ob.modules.disposalutilizationmanagement.dao.DisposalUtilizationManagementDao;
import java.util.Map;

import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 处置利用管理Service
 * <AUTHOR>
 * @version 2024-11-26
 */
@Service
public class DisposalUtilizationManagementService extends CrudService<DisposalUtilizationManagementDao, DisposalUtilizationManagement> {

	@Autowired
	DisposalUtilizationManagementDao dao;
	@Autowired
	DemolitionDisposalServiceService demolitionDisposalServiceService;
	@Autowired
	AuctionTransferDisposalBusinessService auctionTransferDisposalBusinessService;
	@Autowired
	UsageConversionService usageConversionService;
	@Autowired
	RealEstateService realEstateService;
	@Autowired
	EmployeeServiceSupport employeeServiceSupport;
	@Autowired
	OfficeServiceSupport officeServiceSupport;
	/**
	 * 获取单条数据
	 * @param disposalUtilizationManagement
	 * @return
	 */
	@Override
	public DisposalUtilizationManagement get(DisposalUtilizationManagement disposalUtilizationManagement) {
		return super.get(disposalUtilizationManagement);
	}
	
	/**
	 * 查询分页数据
	 * @param disposalUtilizationManagement 查询条件
	 * @param disposalUtilizationManagement page 分页对象
	 * @return
	 */
	@Override
	public Page<DisposalUtilizationManagement> findPage(DisposalUtilizationManagement disposalUtilizationManagement) {
		return super.findPage(disposalUtilizationManagement);
	}

	/**
	 * 添加数据权限过滤条件
	 */
	@Override
	public void addDataScopeFilter(DisposalUtilizationManagement disposalUtilizationManagement){
		SqlMap sqlMap = disposalUtilizationManagement.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"a.applicant_unit_id",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}
	
	/**
	 * 查询列表数据
	 * @param disposalUtilizationManagement
	 * @return
	 */
	@Override
	public List<DisposalUtilizationManagement> findList(DisposalUtilizationManagement disposalUtilizationManagement) {
		return super.findList(disposalUtilizationManagement);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param disposalUtilizationManagement
	 */
	@Override
	@Transactional
	public void save(DisposalUtilizationManagement disposalUtilizationManagement) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(disposalUtilizationManagement.getStatus())){
			disposalUtilizationManagement.setStatus(DisposalUtilizationManagement.STATUS_AUDIT);
		}
		
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (DisposalUtilizationManagement.STATUS_NORMAL.equals(disposalUtilizationManagement.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		
		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (DisposalUtilizationManagement.STATUS_DRAFT.equals(disposalUtilizationManagement.getStatus())
				|| DisposalUtilizationManagement.STATUS_AUDIT.equals(disposalUtilizationManagement.getStatus())){
			super.save(disposalUtilizationManagement);
			if(disposalUtilizationManagement.getDisposalType() !=null &&disposalUtilizationManagement.getDisposalType().equals("5")){
				DemolitionDisposalService demolitionDisposalService = disposalUtilizationManagement.getDemolitionDisposalService();
				demolitionDisposalService.setDisposalId(disposalUtilizationManagement.getId());
				demolitionDisposalServiceService.save(demolitionDisposalService);
				// 保存上传附件
				FileUploadUtils.saveFileUpload(disposalUtilizationManagement, disposalUtilizationManagement.getId(), "disposalUtilizationManagement_demolitionDisposalService_file");
			}else if(disposalUtilizationManagement.getDisposalType() !=null &&disposalUtilizationManagement.getDisposalType().equals("4")){
				AuctionTransferDisposalBusiness auctionTransferDisposalBusiness = disposalUtilizationManagement.getAuctionTransferDisposalBusiness();
				auctionTransferDisposalBusiness.setDisposalId(disposalUtilizationManagement.getId());
				auctionTransferDisposalBusinessService.save(auctionTransferDisposalBusiness);
				// 保存上传附件
				FileUploadUtils.saveFileUpload(disposalUtilizationManagement, disposalUtilizationManagement.getId(), "disposalUtilizationManagement_auctionTransferDisposalBusiness_file");
			}else if(disposalUtilizationManagement.getDisposalType() !=null &&disposalUtilizationManagement.getDisposalType().equals("1")){
				UsageConversion usageConversion = disposalUtilizationManagement.getUsageConversion();
				usageConversion.setDisposalId(disposalUtilizationManagement.getId());
				usageConversionService.save(usageConversion);
				// 保存上传附件
				FileUploadUtils.saveFileUpload(disposalUtilizationManagement, disposalUtilizationManagement.getId(), "disposalUtilizationManagement_usageConversion_file");
			}
		}

		// 如果为审核状态，则进行审批流操作
		if (DisposalUtilizationManagement.STATUS_AUDIT.equals(disposalUtilizationManagement.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", disposalUtilizationManagement.getLeaveDays());
			
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (com.jeesite.common.lang.StringUtils.isBlank(disposalUtilizationManagement.getBpm().getProcInsId())
					&& StringUtils.isBlank(disposalUtilizationManagement.getBpm().getTaskId())){
				BpmUtils.start(disposalUtilizationManagement, "disposal_utilization_management", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(disposalUtilizationManagement, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(disposalUtilizationManagement, disposalUtilizationManagement.getId(), "disposalUtilizationManagement_file");
	}
	
	/**
	 * 更新状态
	 * @param disposalUtilizationManagement
	 */
	@Override
	@Transactional
	public void updateStatus(DisposalUtilizationManagement disposalUtilizationManagement) {
		super.updateStatus(disposalUtilizationManagement);
	}
	
	/**
	 * 删除数据
	 * @param disposalUtilizationManagement
	 */
	@Override
	@Transactional
	public void delete(DisposalUtilizationManagement disposalUtilizationManagement) {
		super.delete(disposalUtilizationManagement);
	}

	public Page<DisposalUtilizationManagementLedger> listLedger(DisposalUtilizationManagementLedger disposalUtilizationManagementLedger) {
		Page<DisposalUtilizationManagementLedger> page = (Page<DisposalUtilizationManagementLedger>) disposalUtilizationManagementLedger.getPage();
		List<DisposalUtilizationManagementLedger> list = dao.listLedgerData(disposalUtilizationManagementLedger);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<DisposalUtilizationManagementLedger> listLedgerData(DisposalUtilizationManagementLedger disposalUtilizationManagementLedger) {
		return dao.listLedgerData(disposalUtilizationManagementLedger);
	}

	public Page<DisposalUtilizationManagementQuery> listQueryData(DisposalUtilizationManagementQuery disposalUtilizationManagementQuery) {
		Page<DisposalUtilizationManagementQuery> page = (Page<DisposalUtilizationManagementQuery>) disposalUtilizationManagementQuery.getPage();
		List<DisposalUtilizationManagementQuery> list = dao.listQueryData(disposalUtilizationManagementQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<DisposalUtilizationManagementQuery> exportQueryData(DisposalUtilizationManagementQuery disposalUtilizationManagementQuery) {
		return dao.listQueryData(disposalUtilizationManagementQuery);
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<DisposalUtilizationManagementImport> list = ei.getDataList(DisposalUtilizationManagementImport.class);
			for (DisposalUtilizationManagementImport disposalUtilizationManagementImport : list) {
				try{
					DisposalUtilizationManagement disposalUtilizationManagement = new DisposalUtilizationManagement();
					List<RealEstate> realEstateList = realEstateService.findList(disposalUtilizationManagementImport.getRealEstate());
					if(realEstateList.size()>0){
						disposalUtilizationManagement.setRoomId(realEstateList.get(0).getId());
					}
					List<Employee> employeeList = employeeServiceSupport.findList(disposalUtilizationManagementImport.getEmployee());
					if(employeeList.size()>0){
						disposalUtilizationManagement.setApplicantId(employeeList.get(0).getId());
					}
					List<Office> officeList = officeServiceSupport.findList(disposalUtilizationManagementImport.getOffice());
					if(officeList.size()>0){
						disposalUtilizationManagement.setApplicantUnitId(officeList.get(0).getId());
					}
					disposalUtilizationManagement.setReasonForApplication(disposalUtilizationManagementImport.getReasonForApplication());
					disposalUtilizationManagement.setApplicationForUse(disposalUtilizationManagementImport.getApplicationForUse());
					disposalUtilizationManagement.setApplicationDate(disposalUtilizationManagementImport.getApplicationDate());
					ValidatorUtils.validateWithException(disposalUtilizationManagement);
					this.save(disposalUtilizationManagement);
					disposalUtilizationManagementImport.setId(disposalUtilizationManagement.getId());
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + disposalUtilizationManagement.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + disposalUtilizationManagementImport.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException) e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}

}