package com.hsobs.ob.modules.apply.entity;


import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

/**
 * 使用管理Entity
 * <AUTHOR>
 * @version 2025-02-08
 */
public class ApplyLedger extends BpmEntity<ApplyLedger> {

	private static final long serialVersionUID = 1L;
	private String name;		// 说明
	private String address;
	private String ownerOfficeName;
	private String usedOfficeName;
	private String employeeCount;
	private String officeType;
	private String floorCount;
	private String usedOfficeCode;
	@ExcelFields({
			@ExcelField(title="主键ID", attrName="id", align= ExcelField.Align.CENTER, sort=10),
			@ExcelField(title="建筑名称", attrName="name", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="坐落地址", attrName="address", dictType="ob_ownership_registration_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="房屋所有权", attrName="ownerOfficeName", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="使用单位", attrName="usedOfficeName", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="编制人数", attrName="employeeCount", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="使用单位性质", attrName="officeType", dictType="sys_office_type", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="具体用房数", attrName="number", align= ExcelField.Align.CENTER, sort=50),
	})
	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}

	public String getEmployeeCount() {
		return employeeCount;
	}

	public void setEmployeeCount(String employeeCount) {
		this.employeeCount = employeeCount;
	}

	public String getOfficeType() {
		return officeType;
	}

	public void setOfficeType(String officeType) {
		this.officeType = officeType;
	}

	private String number;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getOwnerOfficeName() {
		return ownerOfficeName;
	}

	public void setOwnerOfficeName(String ownerOfficeName) {
		this.ownerOfficeName = ownerOfficeName;
	}

	public String getUsedOfficeName() {
		return usedOfficeName;
	}

	public void setUsedOfficeName(String usedOfficeName) {
		this.usedOfficeName = usedOfficeName;
	}

	public String getFloorCount() {
		return floorCount;
	}

	public void setFloorCount(String floorCount) {
		this.floorCount = floorCount;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}
}