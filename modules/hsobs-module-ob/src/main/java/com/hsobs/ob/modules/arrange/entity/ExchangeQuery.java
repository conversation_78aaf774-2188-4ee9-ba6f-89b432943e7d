package com.hsobs.ob.modules.arrange.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 配置管理Entity
 * <AUTHOR>
 * @version 2025-01-05
 */
public class ExchangeQuery extends BpmEntity<ExchangeQuery> {

	private static final long serialVersionUID = 1L;
	private String officeName;          // 申请单位
	private String officeCode;          // 申请单位
	private Date exchangeApplyDate;            // 申请日期
	private String exchangeConfigMethod;       // 配置方式
	private String exchangeSurveyResult;       // 现场调研结果
	private String exchangeApprovalStatus;     // 审批状态
	private String exchangeApprovalComment;    // 审批意见

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	@ExcelFields({
			@ExcelField(title="申请单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="申请日期", attrName="transferApplyDate", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="配置方式", attrName="exchangeConfigMethod", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="现场调研结果", attrName="exchangeSurveyResult", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="审批状态", attrName="transferApprovalStatus", dictType="bpm_biz_status", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="审批意见", attrName="exchangeApprovalComment",align= ExcelField.Align.CENTER, sort=50),
	})

	public Date getExchangeApplyDate() {
		return exchangeApplyDate;
	}

	public void setExchangeApplyDate(Date exchangeApplyDate) {
		this.exchangeApplyDate = exchangeApplyDate;
	}

	public String getExchangeConfigMethod() {
		return exchangeConfigMethod;
	}

	public void setExchangeConfigMethod(String exchangeConfigMethod) {
		this.exchangeConfigMethod = exchangeConfigMethod;
	}

	public String getExchangeSurveyResult() {
		return exchangeSurveyResult;
	}

	public void setExchangeSurveyResult(String exchangeSurveyResult) {
		this.exchangeSurveyResult = exchangeSurveyResult;
	}

	public String getExchangeApprovalStatus() {
		return exchangeApprovalStatus;
	}

	public void setExchangeApprovalStatus(String exchangeApprovalStatus) {
		this.exchangeApprovalStatus = exchangeApprovalStatus;
	}

	public String getExchangeApprovalComment() {
		return exchangeApprovalComment;
	}

	public void setExchangeApprovalComment(String exchangeApprovalComment) {
		this.exchangeApprovalComment = exchangeApprovalComment;
	}
}