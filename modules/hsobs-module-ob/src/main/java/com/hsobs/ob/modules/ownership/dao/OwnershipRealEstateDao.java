package com.hsobs.ob.modules.ownership.dao;

import com.hsobs.ob.modules.ownership.entity.OwnershipRegistrationQuery;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.ob.modules.ownership.entity.OwnershipRealEstate;

import java.util.List;

/**
 * 权属登记DAO接口
 * <AUTHOR>
 * @version 2025-02-16
 */
@MyBatisDao
public interface OwnershipRealEstateDao extends CrudDao<OwnershipRealEstate> {

    List<OwnershipRegistrationQuery> listQueryData(OwnershipRegistrationQuery ownershipRegistrationQuery);
}