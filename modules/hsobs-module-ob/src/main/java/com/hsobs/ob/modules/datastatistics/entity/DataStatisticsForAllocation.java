package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;

/**
 * 办公用房配置情况数据统计Entity
 * <AUTHOR>
 * @version 2024-12-24
 */
public class DataStatisticsForAllocation extends DataEntity<DataStatisticsForAllocation> {

	private static final long serialVersionUID = 1L;
	private String sysCode;
	private AllocationView allocationView;
	private AllocationTable allocationTable;

	public DataStatisticsForAllocation() {
		this(null);
	}

	public DataStatisticsForAllocation(String id){
		super(id);
	}

	public String getSysCode() {
		return sysCode;
	}

	public void setSysCode(String sysCode) {
		this.sysCode = sysCode;
	}

	public AllocationView getAllocationView() {
		return allocationView;
	}

	public void setAllocationView(AllocationView allocationView) {
		this.allocationView = allocationView;
	}

	public AllocationTable getAllocationTable() {
		return allocationTable;
	}

	public void setAllocationTable(AllocationTable allocationTable) {
		this.allocationTable = allocationTable;
	}
}