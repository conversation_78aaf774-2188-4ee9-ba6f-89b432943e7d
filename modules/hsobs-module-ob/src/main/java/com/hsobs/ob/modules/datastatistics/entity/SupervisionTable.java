package com.hsobs.ob.modules.datastatistics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: SupervisionTable
 * @projectName base
 * @description: 办公用房监督整改情况
 * @date 2024/12/249:39
 */
public class SupervisionTable  extends DataEntity<SupervisionTable> {
    private String region;
    private String office;
    private String areaName;    //区域
    private String officeName;    //单位信息
    private String businessType;    //业务类型
    private Integer spotNumber;    //抽查数
    private String occupancyClassification;
    private String dateGte;	//使用日期上限
    private String dateLte;	//使用日期下限
    @ExcelFields({
            @ExcelField(title="区域", attrName="areaName", align= ExcelField.Align.CENTER, sort=20),
            @ExcelField(title="单位信息", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
            @ExcelField(title="业务类型", attrName="businessType", align= ExcelField.Align.CENTER, sort=20),
            @ExcelField(title="抽查数", attrName="spotNumber", dictType="disposal_type", align= ExcelField.Align.CENTER, sort=30),
            @ExcelField(title="问题数", attrName="questionNumber", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="已整改数", attrName="correctedNumber", align= ExcelField.Align.CENTER, sort=50),
            @ExcelField(title="未整改数", attrName="uncorrectedNumber", align= ExcelField.Align.CENTER, sort=50),
    })
    public String getDateGte() {
        return dateGte;
    }

    public void setDateGte(String dateGte) {
        this.dateGte = dateGte;
    }

    public String getDateLte() {
        return dateLte;
    }

    public void setDateLte(String dateLte) {
        this.dateLte = dateLte;
    }

    public String getOccupancyClassification() {
        return occupancyClassification;
    }

    public void setOccupancyClassification(String occupancyClassification) {
        this.occupancyClassification = occupancyClassification;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getOffice() {
        return office;
    }

    public void setOffice(String office) {
        this.office = office;
    }

    private Integer questionNumber;    //问题数
    private float correctedNumber;    //已整改数
    private float uncorrectedNumber;    //未整改数

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getSpotNumber() {
        return spotNumber;
    }

    public void setSpotNumber(Integer spotNumber) {
        this.spotNumber = spotNumber;
    }

    public Integer getQuestionNumber() {
        return questionNumber;
    }

    public void setQuestionNumber(Integer questionNumber) {
        this.questionNumber = questionNumber;
    }

    public float getCorrectedNumber() {
        return correctedNumber;
    }

    public void setCorrectedNumber(float correctedNumber) {
        this.correctedNumber = correctedNumber;
    }

    public float getUncorrectedNumber() {
        return uncorrectedNumber;
    }

    public void setUncorrectedNumber(float uncorrectedNumber) {
        this.uncorrectedNumber = uncorrectedNumber;
    }
}
