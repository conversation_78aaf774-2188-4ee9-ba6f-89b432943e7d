package com.hsobs.ob.modules.approvedconfig.entity;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;

import java.util.List;

public class OfficeApprovedConfigForm extends DataEntity<OfficeApprovedConfigForm> {

    private static final long serialVersionUID = 1L;

    private String id;

    private List<OfficeApprovedConfig> officeApprovedConfigList = ListUtils.newArrayList();

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public List<OfficeApprovedConfig> getOfficeApprovedConfigList() {
        return officeApprovedConfigList;
    }

    public void setOfficeApprovedConfigList(List<OfficeApprovedConfig> officeApprovedConfigList) {
        this.officeApprovedConfigList = officeApprovedConfigList;
    }
}
