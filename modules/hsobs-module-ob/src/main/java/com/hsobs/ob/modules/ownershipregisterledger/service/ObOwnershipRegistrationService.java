package com.hsobs.ob.modules.ownershipregisterledger.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.ownershipregisterledger.entity.ObOwnershipRegistration;
import com.hsobs.ob.modules.ownershipregisterledger.dao.ObOwnershipRegistrationDao;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 权属登记台账Service
 * <AUTHOR>
 * @version 2025-03-01
 */
@Service
public class ObOwnershipRegistrationService extends CrudService<ObOwnershipRegistrationDao, ObOwnershipRegistration> {
	
	/**
	 * 获取单条数据
	 * @param obOwnershipRegistration
	 * @return
	 */
	@Override
	public ObOwnershipRegistration get(ObOwnershipRegistration obOwnershipRegistration) {
		return super.get(obOwnershipRegistration);
	}
	
	/**
	 * 查询分页数据
	 * @param obOwnershipRegistration 查询条件
	 * @param obOwnershipRegistration page 分页对象
	 * @return
	 */
	@Override
	public Page<ObOwnershipRegistration> findPage(ObOwnershipRegistration obOwnershipRegistration) {
		String extWhere = "AND a.\"TYPE\" !=1";
		obOwnershipRegistration.sqlMap().add("extWhere", extWhere); // 旧版将 sqlMap() 替换为 getSqlMap()
		return super.findPage(obOwnershipRegistration);
	}
	
	/**
	 * 查询列表数据
	 * @param obOwnershipRegistration
	 * @return
	 */
	@Override
	public List<ObOwnershipRegistration> findList(ObOwnershipRegistration obOwnershipRegistration) {
		String extWhere = "AND a.\"TYPE\" !=1";
		obOwnershipRegistration.sqlMap().add("extWhere", extWhere); // 旧版将 sqlMap() 替换为 getSqlMap()
		return super.findList(obOwnershipRegistration);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param obOwnershipRegistration
	 */
	@Override
	@Transactional
	public void save(ObOwnershipRegistration obOwnershipRegistration) {
		super.save(obOwnershipRegistration);
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<ObOwnershipRegistration> list = ei.getDataList(ObOwnershipRegistration.class);
			for (ObOwnershipRegistration obOwnershipRegistration : list) {
				try{
					ValidatorUtils.validateWithException(obOwnershipRegistration);
					this.save(obOwnershipRegistration);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + obOwnershipRegistration.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + obOwnershipRegistration.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
	/**
	 * 更新状态
	 * @param obOwnershipRegistration
	 */
	@Override
	@Transactional
	public void updateStatus(ObOwnershipRegistration obOwnershipRegistration) {
		super.updateStatus(obOwnershipRegistration);
	}
	
	/**
	 * 删除数据
	 * @param obOwnershipRegistration
	 */
	@Override
	@Transactional
	public void delete(ObOwnershipRegistration obOwnershipRegistration) {
		super.delete(obOwnershipRegistration);
	}
	
}