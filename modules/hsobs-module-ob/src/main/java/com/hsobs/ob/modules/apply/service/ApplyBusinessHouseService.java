package com.hsobs.ob.modules.apply.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.apply.entity.ApplyBusinessHouse;
import com.hsobs.ob.modules.apply.dao.ApplyBusinessHouseDao;
import java.util.Map;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 技术业务用房申请表Service
 * <AUTHOR>
 * @version 2025-03-13
 */
@Service
public class ApplyBusinessHouseService extends CrudService<ApplyBusinessHouseDao, ApplyBusinessHouse> {
	
	/**
	 * 获取单条数据
	 * @param applyBusinessHouse
	 * @return
	 */
	@Override
	public ApplyBusinessHouse get(ApplyBusinessHouse applyBusinessHouse) {
		return super.get(applyBusinessHouse);
	}
	
	/**
	 * 查询分页数据
	 * @param applyBusinessHouse 查询条件
	 * @param applyBusinessHouse page 分页对象
	 * @return
	 */
	@Override
	public Page<ApplyBusinessHouse> findPage(ApplyBusinessHouse applyBusinessHouse) {
		return super.findPage(applyBusinessHouse);
	}
	
	/**
	 * 查询列表数据
	 * @param applyBusinessHouse
	 * @return
	 */
	@Override
	public List<ApplyBusinessHouse> findList(ApplyBusinessHouse applyBusinessHouse) {
		return super.findList(applyBusinessHouse);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param applyBusinessHouse
	 */
	@Override
	@Transactional
	public void save(ApplyBusinessHouse applyBusinessHouse) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(applyBusinessHouse.getStatus())){
			applyBusinessHouse.setStatus(ApplyBusinessHouse.STATUS_AUDIT);
		}
		
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (ApplyBusinessHouse.STATUS_NORMAL.equals(applyBusinessHouse.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		
		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (ApplyBusinessHouse.STATUS_DRAFT.equals(applyBusinessHouse.getStatus())
				|| ApplyBusinessHouse.STATUS_AUDIT.equals(applyBusinessHouse.getStatus())){
			super.save(applyBusinessHouse);
		}

		// 如果为审核状态，则进行审批流操作
		if (ApplyBusinessHouse.STATUS_AUDIT.equals(applyBusinessHouse.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", applyBusinessHouse.getLeaveDays());
			
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(applyBusinessHouse.getBpm().getProcInsId())
					&& StringUtils.isBlank(applyBusinessHouse.getBpm().getTaskId())){
				BpmUtils.start(applyBusinessHouse, "obApplyBusinessHouse", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(applyBusinessHouse, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(applyBusinessHouse, applyBusinessHouse.getId(), "applyBusinessHouse_file");
	}
	
	/**
	 * 更新状态
	 * @param applyBusinessHouse
	 */
	@Override
	@Transactional
	public void updateStatus(ApplyBusinessHouse applyBusinessHouse) {
		super.updateStatus(applyBusinessHouse);
	}
	
	/**
	 * 删除数据
	 * @param applyBusinessHouse
	 */
	@Override
	@Transactional
	public void delete(ApplyBusinessHouse applyBusinessHouse) {
		super.delete(applyBusinessHouse);
	}
	
}