package com.hsobs.ob.modules.arrange.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 配置管理Entity
 * <AUTHOR>
 * @version 2025-01-05
 */
public class TransferQuery extends BpmEntity<TransferQuery> {

	private static final long serialVersionUID = 1L;
	private String officeName;          // 申请单位
	private String officeCode;          // 申请单位
	private Date transferApplyDate;            // 申请日期
	private Double transferTotalArea;          // 办公用房总建筑面积
	private Double transferBasicArea;          // 基本办公用房面积
	private Double transferAuxiliaryArea;      // 附属办公用房面积
	private String transferReason;             // 调剂原由
	private Date transferDate;                 // 调剂日期
	private String transferApprovalStatus;     // 审批状态

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	@ExcelFields({
			@ExcelField(title="申请单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="申请日期", attrName="transferApplyDate", dictType="ob_ownership_registration_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="办公用房总建筑面积", attrName="transferTotalArea", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="基本办公用房面积", attrName="transferBasicArea", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="基本办公用房面积", attrName="transferAuxiliaryArea", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="调剂原由", attrName="transferReason", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="调剂日期", attrName="transferDate",align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="审批状态", attrName="transferApprovalStatus", dictType="sys_office_type", align= ExcelField.Align.CENTER, sort=50),
	})

	public Date getTransferApplyDate() {
		return transferApplyDate;
	}

	public void setTransferApplyDate(Date transferApplyDate) {
		this.transferApplyDate = transferApplyDate;
	}

	public Double getTransferTotalArea() {
		return transferTotalArea;
	}

	public void setTransferTotalArea(Double transferTotalArea) {
		this.transferTotalArea = transferTotalArea;
	}

	public Double getTransferBasicArea() {
		return transferBasicArea;
	}

	public void setTransferBasicArea(Double transferBasicArea) {
		this.transferBasicArea = transferBasicArea;
	}

	public Double getTransferAuxiliaryArea() {
		return transferAuxiliaryArea;
	}

	public void setTransferAuxiliaryArea(Double transferAuxiliaryArea) {
		this.transferAuxiliaryArea = transferAuxiliaryArea;
	}

	public String getTransferReason() {
		return transferReason;
	}

	public void setTransferReason(String transferReason) {
		this.transferReason = transferReason;
	}

	public Date getTransferDate() {
		return transferDate;
	}

	public void setTransferDate(Date transferDate) {
		this.transferDate = transferDate;
	}

	public String getTransferApprovalStatus() {
		return transferApprovalStatus;
	}

	public void setTransferApprovalStatus(String transferApprovalStatus) {
		this.transferApprovalStatus = transferApprovalStatus;
	}
}