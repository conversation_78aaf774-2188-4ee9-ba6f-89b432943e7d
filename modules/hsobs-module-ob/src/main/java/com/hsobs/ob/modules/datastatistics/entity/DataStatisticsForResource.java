package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;

/**
 * 办公用房资源情况数据统计Entity
 * <AUTHOR>
 * @version 2024-12-16
 */
@Table(name="office_type_count", alias="a", label="办公用房数据统计信息", columns={
		@Column(name="type", attrName="type", label="type", isPK=true),
		@Column(name="count(*)", attrName="count(*)", label="count", comment="count(*)", isInsert=false, isUpdate=false, isUpdateForce=true),
	}, orderBy="a.type DESC"
)
public class DataStatisticsForResource extends DataEntity<DataStatisticsForResource> {
	
	private static final long serialVersionUID = 1L;
	private String sysCode;
	private ResourceCase resourceCase;
	private SpaceVerification spaceVerification;

	public DataStatisticsForResource() {
		this(null);
	}
	
	public DataStatisticsForResource(String id){
		super(id);
	}

	public String getSysCode() {
		return sysCode;
	}

	public void setSysCode(String sysCode) {
		this.sysCode = sysCode;
	}

	public ResourceCase getResourceCase() {
		return resourceCase;
	}

	public void setResourceCase(ResourceCase resourceCase) {
		this.resourceCase = resourceCase;
	}

	public SpaceVerification getSpaceVerification() {
		return spaceVerification;
	}

	public void setSpaceVerification(SpaceVerification spaceVerification) {
		this.spaceVerification = spaceVerification;
	}
}