package com.hsobs.ob.modules.usevouchers.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.usevouchers.entity.UseVouchers;
import com.hsobs.ob.modules.usevouchers.service.UseVouchersService;

/**
 * 使用凭证Controller
 * <AUTHOR>
 * @version 2025-02-07
 */
@Controller
@RequestMapping(value = "${adminPath}/usevouchers/")
public class UseVouchersController extends BaseController {

	@Autowired
	private UseVouchersService useVouchersService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public UseVouchers get(String id, boolean isNewRecord) {
		return useVouchersService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("usevouchers::view")
	@RequestMapping(value = {"list", ""})
	public String list(UseVouchers useVouchers, Model model) {
		model.addAttribute("useVouchers", useVouchers);
		return "modules/usevouchers/useVouchersList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("usevouchers::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<UseVouchers> listData(UseVouchers useVouchers, HttpServletRequest request, HttpServletResponse response) {
		useVouchers.setPage(new Page<>(request, response));
		Page<UseVouchers> page = useVouchersService.findPage(useVouchers);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("usevouchers::view")
	@RequestMapping(value = "form")
	public String form(UseVouchers useVouchers, Model model) {
		model.addAttribute("useVouchers", useVouchers);
		return "modules/usevouchers/useVouchersForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("usevouchers::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated UseVouchers useVouchers) {
		useVouchersService.save(useVouchers);
		return renderResult(Global.TRUE, text("保存使用凭证成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("usevouchers::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(UseVouchers useVouchers) {
		useVouchersService.delete(useVouchers);
		return renderResult(Global.TRUE, text("删除使用凭证成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("usevouchers::view")
	@RequestMapping(value = "useVouchersSelect")
	public String useVouchersSelect(UseVouchers useVouchers, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("useVouchers", useVouchers);
		return "modules/usevouchers/useVouchersSelect";
	}
	
}