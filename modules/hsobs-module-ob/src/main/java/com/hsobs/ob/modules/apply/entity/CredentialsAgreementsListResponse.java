package com.hsobs.ob.modules.apply.entity;

import com.jeesite.common.entity.BaseEntity;

public class CredentialsAgreementsListResponse extends BaseEntity<CredentialsAgreementsListResponse> {
    private String id;
    private String ldmc;
    private String pzbh;
    private String gxsj;
    private String type;
    private String fileId;
    private String fileType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLdmc() {
        return ldmc;
    }

    public void setLdmc(String ldmc) {
        this.ldmc = ldmc;
    }

    public String getPzbh() {
        return pzbh;
    }

    public void setPzbh(String pzbh) {
        this.pzbh = pzbh;
    }

    public String getGxsj() {
        return gxsj;
    }

    public void setGxsj(String gxsj) {
        this.gxsj = gxsj;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
}
