package com.hsobs.ob.modules.arrange.service;

import java.util.Date;
import java.util.List;

import com.jeesite.modules.msg.entity.content.PcMsgContent;
import com.jeesite.modules.msg.utils.MsgPushUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.arrange.entity.RentalRemind;
import com.hsobs.ob.modules.arrange.dao.RentalRemindDao;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 租借提醒Service
 * <AUTHOR>
 * @version 2025-03-27
 */
@Service
public class RentalRemindService extends CrudService<RentalRemindDao, RentalRemind> {

	/**
	 * 获取单条数据
	 * @param rentalRemind
	 * @return
	 */
	@Override
	public RentalRemind get(RentalRemind rentalRemind) {
		return super.get(rentalRemind);
	}
	
	/**
	 * 查询分页数据
	 * @param rentalRemind 查询条件
	 * @param rentalRemind page 分页对象
	 * @return
	 */
	@Override
	public Page<RentalRemind> findPage(RentalRemind rentalRemind) {
		return super.findPage(rentalRemind);
	}
	
	/**
	 * 查询列表数据
	 * @param rentalRemind
	 * @return
	 */
	@Override
	public List<RentalRemind> findList(RentalRemind rentalRemind) {
		return super.findList(rentalRemind);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param rentalRemind
	 */
	@Override
	@Transactional
	public void save(RentalRemind rentalRemind) {
		Date currentDate = new Date();
		Date rentStartDate = rentalRemind.getRentStartDate();
		Date rentEndDate = rentalRemind.getRentEndDate();

		if (rentStartDate == null || rentEndDate == null) {
			throw new ServiceException("租借开始时间和结束时间不能为空");
		}
		if (rentStartDate.after(rentEndDate)) {
			throw new ServiceException("租借开始时间不能晚于结束时间");
		}
		if (currentDate.after(rentEndDate)) {
			rentalRemind.setContractStatus("2");
			PcMsgContent msgContent = new PcMsgContent();
			msgContent.setTitle("【合同到期】租用合同到期提醒");
			msgContent.setContent(rentalRemind.getRealEstateAddress() + " 的房屋租用合同已到期，请尽快处理，如已处理请忽略此提醒。");
			MsgPushUtils.push(msgContent, "", "", rentalRemind.getRemindUserCode());
		} else if (currentDate.before(rentStartDate)) {
			rentalRemind.setContractStatus("1");
		} else {
			rentalRemind.setContractStatus("1");
		}
		super.save(rentalRemind);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(rentalRemind, rentalRemind.getId(), "rentalRemind_file");
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<RentalRemind> list = ei.getDataList(RentalRemind.class);
			for (RentalRemind rentalRemind : list) {
				try{
					ValidatorUtils.validateWithException(rentalRemind);
					this.save(rentalRemind);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + rentalRemind.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + rentalRemind.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
	/**
	 * 更新状态
	 * @param rentalRemind
	 */
	@Override
	@Transactional
	public void updateStatus(RentalRemind rentalRemind) {
		super.updateStatus(rentalRemind);
	}
	
	/**
	 * 删除数据
	 * @param rentalRemind
	 */
	@Override
	@Transactional
	public void delete(RentalRemind rentalRemind) {
		super.delete(rentalRemind);
	}


	public void executeRentalRemind(){
		RentalRemind where = new RentalRemind();
		where.setRentEndDate_lte(new Date());
		where.setContractStatus("1");
		List<RentalRemind> remindList = super.findList(where);
		if (null == remindList || remindList.isEmpty()) {
			return;
		}
		remindList.forEach(item -> {
			item.setContractStatus("2");
			super.update(item);
			PcMsgContent msgContent = new PcMsgContent();
			msgContent.setTitle("【合同到期】租用合同到期提醒");
			msgContent.setContent(item.getRealEstateAddress() + " 的房屋租用合同已到期，请尽快处理，如已处理请忽略此提醒。");
			MsgPushUtils.push(msgContent, "", "", item.getRemindUserCode());
		});
	}
	
}