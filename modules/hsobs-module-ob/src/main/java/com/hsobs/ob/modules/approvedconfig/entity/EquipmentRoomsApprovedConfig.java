package com.hsobs.ob.modules.approvedconfig.entity;


import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 设备用房面积核定Entity
 * <AUTHOR>
 * @version 2025-03-09
 */
@Table(name="ob_equipment_rooms_approved_config", alias="a", label="设备用房面积核定信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="equipment_room_ratio", attrName="equipmentRoomRatio", label="用房面积指标", isUpdateForce=true),
		@Column(name="multistory_usage_coefficient_min", attrName="multistoryUsageCoefficientMin", label="多层建筑指标", isUpdateForce=true),
		@Column(name="highrise_usage_coefficient_min", attrName="highriseUsageCoefficientMin", label="highrise_usage_coefficient_min", isUpdateForce=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, orderBy="a.update_date DESC"
)
public class EquipmentRoomsApprovedConfig extends DataEntity<EquipmentRoomsApprovedConfig> {
	
	private static final long serialVersionUID = 1L;
	private Double equipmentRoomRatio;		// 用房面积指标
	private Double multistoryUsageCoefficientMin;		// 多层建筑指标
	private Double highriseUsageCoefficientMin;		// highrise_usage_coefficient_min

	public EquipmentRoomsApprovedConfig() {
		this(null);
	}
	
	public EquipmentRoomsApprovedConfig(String id){
		super(id);
	}
	
	public Double getEquipmentRoomRatio() {
		return equipmentRoomRatio;
	}

	public void setEquipmentRoomRatio(Double equipmentRoomRatio) {
		this.equipmentRoomRatio = equipmentRoomRatio;
	}
	
	public Double getMultistoryUsageCoefficientMin() {
		return multistoryUsageCoefficientMin;
	}

	public void setMultistoryUsageCoefficientMin(Double multistoryUsageCoefficientMin) {
		this.multistoryUsageCoefficientMin = multistoryUsageCoefficientMin;
	}
	
	public Double getHighriseUsageCoefficientMin() {
		return highriseUsageCoefficientMin;
	}

	public void setHighriseUsageCoefficientMin(Double highriseUsageCoefficientMin) {
		this.highriseUsageCoefficientMin = highriseUsageCoefficientMin;
	}
	
}