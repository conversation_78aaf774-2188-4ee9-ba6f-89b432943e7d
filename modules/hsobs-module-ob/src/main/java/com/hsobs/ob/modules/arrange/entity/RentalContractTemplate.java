package com.hsobs.ob.modules.arrange.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租用合同模版表Entity
 * <AUTHOR>
 * @version 2025-03-09
 */
@Table(name="ob_rental_contract_template", alias="a", label="租用合同模版信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="name", attrName="name", label="合同名称", queryType=QueryType.LIKE),
		@Column(name="number", attrName="number", label="合同编号", queryType=QueryType.LIKE),
		@Column(name="version", attrName="version", label="版本"),
		@Column(name="remark", attrName="remark", label="备注"),
		@Column(name="enable", attrName="enable", label="是否启用", isUpdate=true, isQuery = false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, orderBy="a.update_date DESC"
)
public class RentalContractTemplate extends DataEntity<RentalContractTemplate> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 合同名称
	private String number;		// 合同编号
	private String version;		// 版本
	private String remark;		// 备注
	private String enable;

	public RentalContractTemplate() {
		this(null);
	}
	
	public RentalContractTemplate(String id){
		super(id);
	}
	
	@NotBlank(message="合同名称不能为空")
	@Size(min=0, max=512, message="合同名称长度不能超过 512 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@NotBlank(message="合同编号不能为空")
	@Size(min=0, max=512, message="合同编号长度不能超过 512 个字符")
	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}
	
	@NotBlank(message="版本不能为空")
	@Size(min=0, max=512, message="版本长度不能超过 512 个字符")
	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}
	
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getEnable() {
		return enable;
	}

	public void setEnable(String enable) {
		this.enable = enable;
	}
}