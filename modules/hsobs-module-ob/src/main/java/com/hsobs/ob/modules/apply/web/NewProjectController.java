package com.hsobs.ob.modules.apply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.apply.entity.NewProject;
import com.hsobs.ob.modules.apply.service.NewProjectService;

/**
 * 新建项目管理Controller
 * <AUTHOR>
 * @version 2025-03-16
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/newProject")
public class NewProjectController extends BaseController {

	@Autowired
	private NewProjectService newProjectService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public NewProject get(String id, boolean isNewRecord) {
		return newProjectService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("apply:newProject:view")
	@RequestMapping(value = {"list", ""})
	public String list(NewProject newProject, Model model) {
		model.addAttribute("newProject", newProject);
		return "modules/apply/newProjectList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("apply:newProject:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<NewProject> listData(NewProject newProject, HttpServletRequest request, HttpServletResponse response) {
		newProject.setPage(new Page<>(request, response));
		Page<NewProject> page = newProjectService.findPage(newProject);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("apply:newProject:view")
	@RequestMapping(value = "form")
	public String form(NewProject newProject, Model model) {
		model.addAttribute("newProject", newProject);
		return "modules/apply/newProjectForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("apply:newProject:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated NewProject newProject) {
		newProjectService.save(newProject);
		return renderResult(Global.TRUE, text("保存新建项目管理成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("apply:newProject:view")
	@RequestMapping(value = "exportData")
	public void exportData(NewProject newProject, HttpServletResponse response) {
		List<NewProject> list = newProjectService.findList(newProject);
		String fileName = "新建项目管理" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("新建项目管理", NewProject.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("apply:newProject:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		NewProject newProject = new NewProject();
		List<NewProject> list = ListUtils.newArrayList(newProject);
		String fileName = "新建项目管理模板.xlsx";
		try(ExcelExport ee = new ExcelExport("新建项目管理", NewProject.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("apply:newProject:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = newProjectService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("apply:newProject:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(NewProject newProject) {
		newProjectService.delete(newProject);
		return renderResult(Global.TRUE, text("删除新建项目管理成功！"));
	}
	
}