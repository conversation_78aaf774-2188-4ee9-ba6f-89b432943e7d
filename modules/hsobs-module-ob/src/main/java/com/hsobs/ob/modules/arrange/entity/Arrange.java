package com.hsobs.ob.modules.arrange.entity;

import javax.validation.constraints.Size;

import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 配置管理Entity
 * <AUTHOR>
 * @version 2025-01-05
 */
@Setter
@Table(name="ob_arrange", alias="a", label="ob_arrange信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="name", attrName="name", label="配置名称", queryType=QueryType.LIKE),
		@Column(name="arrange_type", attrName="arrangeType", label="配置类型"),
		@Column(name="describe", attrName="describe", label="说明"),
		@Column(name="area", attrName="area", label="面积"),
		@Column(name="purpose", attrName="purpose", label="用途", queryType = QueryType.LIKE),
		@Column(name="real_estate_type", attrName="realEstateType", label="不动产类型", isUpdate = true),
		@Column(name="application_id", attrName="applicationId", label="申请ID"),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用机构ID"),
		@Column(name="used_user_code", attrName="usedUserCode", label="使用人ID"),
		@Column(name="project_name", attrName="projectName", label="项目名称"),
		@Column(name="budget", attrName="budget", label="预算"),
		@Column(name="overall_relocation", attrName="overallRelocation", label="是否整体搬迁", isUpdate = true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="修改时间", isQuery=false),
		@Column(name="construction_type", attrName="constructionType", label="建设类型", isQuery=true),
		@Column(name="regulate_date", attrName="regulateDate", label="调剂时间", isQuery=true),
		@Column(name="rental_start_date", attrName="rentalStartDate", label="租借开始时间", isQuery=true),
		@Column(name="rental_end_date", attrName="rentalEndDate", label="租借结束时间", isQuery=true),
		@Column(name="findings", attrName="findings", label="调研说明", isQuery=true),
		@Column(name="construction_date", attrName="constructionDate", label="建设时间", isQuery=true),
		@Column(name="province_opinion", attrName="provinceOpinion", label="省委省政府意见", isQuery=true),
		@Column(name="reason", attrName="reason", label="理由", queryType = QueryType.LIKE, isQuery=true),
		@Column(name="way", attrName="way", label="方式", queryType = QueryType.LIKE, isQuery=true),
		@Column(name="research_results", attrName="researchResults", label="调研结果", queryType = QueryType.LIKE, isQuery=true),
		@Column(name="entry_name", attrName="entryName", label="工程名称", isQuery=true),
		@Column(name="entry_budget", attrName="entryBudget", label="工程预算", isQuery=true),
		@Column(name="main_used_office_code", attrName="mainUsedOfficeCode", label="主要使用单位", isQuery=true),
		@Column(name="construction_permit_number", attrName="constructionPermitNumber", label="施工许可证号", isQuery=true),
		@Column(name="commencement_date", attrName="commencementDate", label="开工日期", isQuery=true),
		@Column(name="completion_date", attrName="completionDate", label="完工日期", isQuery=true),
		@Column(name="final_settlement_date", attrName="finalSettlementDate", label="决算日期", isQuery=true),
		@Column(name="final_settlement_budget", attrName="finalSettlementBudget", label="决算金额", isQuery=true),
		@Column(name="construction_unit_name", attrName="constructionUnitName", label="施工单位名称", isQuery=true),
		@Column(name="supervision_unit_name", attrName="supervisionUnitName", label="监理单位名称", isQuery=true),
		@Column(name="housing_information", attrName="housingInformation", label="房屋信息", isQuery=true),
		@Column(name="real_estate_address_id", attrName="realEstateAddressId", label="地址ID"),
		@Column(name="approval_date", attrName="approvalDate", label="审批通过日期"),
		@Column(name="approval_code", attrName="approvalCode", label="申请单号", queryType = QueryType.EQ),
		@Column(name="application_date", attrName="applicationDate", label="申请日期"),
		@Column(name="last_approval_date", attrName="lastApprovalDate", label="最近一次审批日期"),
		@Column(name="last_approval_common", attrName="lastApprovalCommon", label="最近一次审批意见", queryType = QueryType.LIKE, isUpdate = true),
		@Column(name="lessor", attrName="lessor", label="出租人", queryType = QueryType.LIKE, isUpdate = true),
		@Column(name="rental_address", attrName="rentalAddress", label="出租地址", queryType = QueryType.LIKE, isUpdate = true),
		@Column(name="current_office_situation", attrName="currentOfficeSituation", label="办公现状", queryType = QueryType.LIKE, isUpdate = true),
		@Column(
				name = "status",
				attrName = "status",
				label = "状态",
				isUpdate = true,
				isQuery = true,
				comment = "（推荐状态：0：正常；1：删除；2：停用；3：冻结；4：审核、待审核；5：审核驳回；9：草稿）"
		),
		@Column(includeEntity=DataEntity.class),
	}, joinTable={
			@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
					attrName = "usedOffice",
					on = "a.used_office_code = o2.office_code",
					columns = {@Column(includeEntity = Office.class)}),
			@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o3",
					attrName = "mainUsedOffice",
					on = "a.main_used_office_code = o3.office_code",
					columns = {@Column(includeEntity = Office.class)}),
			@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u2",
					attrName = "usedUser",
					on = "a.used_user_code = u2.user_code",
					columns = {@Column(includeEntity = User.class)}),
			@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddress.class, alias="rea",
					on="rea.id = a.real_estate_address_id",
					columns={@Column(includeEntity=RealEstateAddress.class)}),
	},
		extWhereKeys="dsf"
		, orderBy="a.update_date DESC"
)
public class Arrange extends BpmEntity<Arrange> {

	private static final long serialVersionUID = 1L;
	private String name;		// 配置名称
	private String arrangeType;		// 配置类型
	private String arrangeTypeName;
	private String describe;		// 说明
	private Double area;		// 面积
	private String realEstateType;	// 不动产类型
	private String purpose;		// 用途
	private String applicationId;		// 申请ID
	private String usedOfficeCode;
	private String usedUserCode;
	private String projectName; // 项目名称
	private Double budget; // 预算
	private String overallRelocation; // 是否整体搬迁
	private String constructionType; // 建设类型
	private Date regulateDate; //调剂时间
	private Date rentalStartDate; // 租借开始时间
	private Date rentalEndDate; // 租借结束时间
	private String findings; // 租借结束时间
	private Date constructionDate; // 建设时间

	private String provinceOpinion; // 省委省政府意见

	private String reason;
	private String way;
	private String researchResults;

	private String entryName;
	private Double entryBudget;
	private String mainUsedOfficeCode;
	private String constructionPermitNumber;
	private Date commencementDate;
	private Date completionDate;
	private Date finalSettlementDate;
	private Double finalSettlementBudget;
	private String constructionUnitName;
	private String supervisionUnitName;
	private String housingInformation;
	private String realEstateAddressId;		// 地址ID

	private Date approvalDate;
	private Date applicationDate;
	private Date lastApprovalDate;
	private String lastApprovalCommon;
	private String approvalCode;

	private String lessor;
	private String rentalAddress;

	private String currentOfficeSituation;


	private Office usedOffice;
	private Office mainUsedOffice;
	private User usedUser;
	private RealEstateAddress realEstateAddress;


	private List<ArrangeRealEstate> arrangeRealEstateList = ListUtils.newArrayList();

	@ExcelFields({
			@ExcelField(title="配置名称", attrName="name", align= ExcelField.Align.CENTER, sort=10),
			@ExcelField(title="申请单位", attrName="usedOffice.officeName", align= ExcelField.Align.CENTER, sort=180),
			@ExcelField(title="说明", attrName="describe", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="用途", attrName="purpose", dictType="ob_ownership_registration_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="方式", attrName="way", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="调研结果", attrName="researchResults", align= ExcelField.Align.CENTER, sort=170),
			@ExcelField(title="租用开始时间", attrName="rentalStartDate", align= ExcelField.Align.CENTER, sort=190, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="租用结束时间", attrName="rentalEndDate", align= ExcelField.Align.CENTER, sort=200, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="建设类型", attrName="constructionType", dictType="ob_construction_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="主要使用单位", attrName="mainUsedOffice.officeName", align= ExcelField.Align.CENTER, sort=200),
			@ExcelField(title="申请日期", attrName="applicationDate", align= ExcelField.Align.CENTER, sort=200, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="通过日期", attrName="approvalDate", align= ExcelField.Align.CENTER, sort=200, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="审批状态", attrName="status", dictType="bpm_biz_status", align= ExcelField.Align.CENTER, sort=50),
	})
	public Arrange() {
		this(null);
	}

	public Arrange(String id){
		super(id);
	}

	@Size(min=0, max=1000, message="配置名称长度不能超过 1000 个字符")
	public String getName() {
		return name;
	}

    @Size(min=0, max=100, message="配置类型长度不能超过 100 个字符")
	public String getArrangeType() {
		return arrangeType;
	}

	public String getArrangeTypeName() {
		return arrangeTypeName;
	}

	@Size(min=0, max=1000, message="说明长度不能超过 1000 个字符")
	public String getDescribe() {
		return describe;
	}

	public Double getArea() {
		return area;
	}

	@Size(min=0, max=1000, message="用途长度不能超过 1000 个字符")
	public String getPurpose() {
		return purpose;
	}

    @Size(min=0, max=64, message="申请ID长度不能超过 64 个字符")
	public String getApplicationId() {
		return applicationId;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setArrangeType(String arrangeType) {
		this.arrangeType = arrangeType;
	}

	public void setArrangeTypeName(String arrangeTypeName) {
		this.arrangeTypeName = arrangeTypeName;
	}

	public void setDescribe(String describe) {
		this.describe = describe;
	}

	public void setArea(Double area) {
		this.area = area;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	public void setApplicationId(String applicationId) {
		this.applicationId = applicationId;
	}

	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}

	public String getUsedUserCode() {
		return usedUserCode;
	}

	public void setUsedUserCode(String usedUserCode) {
		this.usedUserCode = usedUserCode;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}

	public User getUsedUser() {
		return usedUser;
	}

	public void setUsedUser(User usedUser) {
		this.usedUser = usedUser;
	}

	public String getRealEstateType() {
		return realEstateType;
	}

	public void setRealEstateType(String realEstateType) {
		this.realEstateType = realEstateType;
	}

	public List<ArrangeRealEstate> getArrangeRealEstateList() {
		return arrangeRealEstateList;
	}

	public void setArrangeRealEstateList(List<ArrangeRealEstate> arrangeRealEstateList) {
		this.arrangeRealEstateList = arrangeRealEstateList;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public Double getBudget() {
		return budget;
	}

	public void setBudget(Double budget) {
		this.budget = budget;
	}

	public String getOverallRelocation() {
		return overallRelocation;
	}

	public void setOverallRelocation(String overallRelocation) {
		this.overallRelocation = overallRelocation;
	}

	public String getConstructionType() {
		return constructionType;
	}

	public void setConstructionType(String constructionType) {
		this.constructionType = constructionType;
	}

	public Date getRegulateDate() {
		return regulateDate;
	}

	public void setRegulateDate(Date regulateDate) {
		this.regulateDate = regulateDate;
	}

	public Date getRentalStartDate() {
		return rentalStartDate;
	}

	public void setRentalStartDate(Date rentalStartDate) {
		this.rentalStartDate = rentalStartDate;
	}

	public Date getRentalEndDate() {
		return rentalEndDate;
	}

	public void setRentalEndDate(Date rentalEndDate) {
		this.rentalEndDate = rentalEndDate;
	}

	public String getFindings() {
		return findings;
	}

	public void setFindings(String findings) {
		this.findings = findings;
	}

	public Date getConstructionDate() {
		return constructionDate;
	}

	public void setConstructionDate(Date constructionDate) {
		this.constructionDate = constructionDate;
	}

	public String getProvinceOpinion() {
		return provinceOpinion;
	}

	public void setProvinceOpinion(String provinceOpinion) {
		this.provinceOpinion = provinceOpinion;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getWay() {
		return way;
	}

	public void setWay(String way) {
		this.way = way;
	}

	public String getResearchResults() {
		return researchResults;
	}

	public void setResearchResults(String researchResults) {
		this.researchResults = researchResults;
	}

	public String getEntryName() {
		return entryName;
	}

	public void setEntryName(String entryName) {
		this.entryName = entryName;
	}

	public Double getEntryBudget() {
		return entryBudget;
	}

	public void setEntryBudget(Double entryBudget) {
		this.entryBudget = entryBudget;
	}

	public String getMainUsedOfficeCode() {
		return mainUsedOfficeCode;
	}

	public void setMainUsedOfficeCode(String mainUsedOfficeCode) {
		this.mainUsedOfficeCode = mainUsedOfficeCode;
	}

	public String getConstructionPermitNumber() {
		return constructionPermitNumber;
	}

	public void setConstructionPermitNumber(String constructionPermitNumber) {
		this.constructionPermitNumber = constructionPermitNumber;
	}

	public Date getCommencementDate() {
		return commencementDate;
	}

	public void setCommencementDate(Date commencementDate) {
		this.commencementDate = commencementDate;
	}

	public Date getCompletionDate() {
		return completionDate;
	}

	public void setCompletionDate(Date completionDate) {
		this.completionDate = completionDate;
	}

	public Date getFinalSettlementDate() {
		return finalSettlementDate;
	}

	public void setFinalSettlementDate(Date finalSettlementDate) {
		this.finalSettlementDate = finalSettlementDate;
	}

	public Double getFinalSettlementBudget() {
		return finalSettlementBudget;
	}

	public void setFinalSettlementBudget(Double finalSettlementBudget) {
		this.finalSettlementBudget = finalSettlementBudget;
	}

	public String getConstructionUnitName() {
		return constructionUnitName;
	}

	public void setConstructionUnitName(String constructionUnitName) {
		this.constructionUnitName = constructionUnitName;
	}

	public String getSupervisionUnitName() {
		return supervisionUnitName;
	}

	public void setSupervisionUnitName(String supervisionUnitName) {
		this.supervisionUnitName = supervisionUnitName;
	}

	public String getHousingInformation() {
		return housingInformation;
	}

	public void setHousingInformation(String housingInformation) {
		this.housingInformation = housingInformation;
	}

	public Office getMainUsedOffice() {
		return mainUsedOffice;
	}

	public void setMainUsedOffice(Office mainUsedOffice) {
		this.mainUsedOffice = mainUsedOffice;
	}

	public String getRealEstateAddressId() {
		return realEstateAddressId;
	}

	public void setRealEstateAddressId(String realEstateAddressId) {
		this.realEstateAddressId = realEstateAddressId;
	}

	public RealEstateAddress getRealEstateAddress() {
		return realEstateAddress;
	}

	public void setRealEstateAddress(RealEstateAddress realEstateAddress) {
		this.realEstateAddress = realEstateAddress;
	}

	public Date getApprovalDate() {
		return approvalDate;
	}

	public void setApprovalDate(Date approvalDate) {
		this.approvalDate = approvalDate;
	}


	public Date getRentalStartDate_gte() {
		return sqlMap.getWhere().getValue("rental_start_date", QueryType.GTE);
	}

	public void setRentalStartDate_gte(Date rentalStartDate) {
		sqlMap.getWhere().and("rental_start_date", QueryType.GTE, rentalStartDate);
	}

	public Date getRentalStartDate_lte() {
		return sqlMap.getWhere().getValue("rental_start_date", QueryType.LTE);
	}

	public void setRentalStartDate_lte(Date rentalStartDate) {
		sqlMap.getWhere().and("rental_start_date", QueryType.LTE, rentalStartDate);
	}

	public Date getRentalEndDate_gte() {
		return sqlMap.getWhere().getValue("rental_end_date", QueryType.GTE);
	}

	public void setRentalEndDate_gte(Date rentalEndDate) {
		sqlMap.getWhere().and("rental_end_date", QueryType.GTE, rentalEndDate);
	}

	public Date getRentalEndDate_lte() {
		return sqlMap.getWhere().getValue("rental_end_date", QueryType.LTE);
	}

	public void setRentalEndDate_lte(Date rentalEndDate) {
		sqlMap.getWhere().and("rental_end_date", QueryType.LTE, rentalEndDate);
	}
	public Date getApplicationDate_gte() {
		return sqlMap.getWhere().getValue("application_date", QueryType.GTE);
	}

	public void setApplicationDate_gte(Date applicationDate) {
		sqlMap.getWhere().and("application_date", QueryType.GTE, applicationDate);
	}

	public Date getApplicationDate_lte() {
		return sqlMap.getWhere().getValue("application_date", QueryType.LTE);
	}

	public void setApplicationDate_lte(Date applicationDate) {
		sqlMap.getWhere().and("application_date", QueryType.LTE, applicationDate);
	}

	public Date getApplicationDate() {
		return applicationDate;
	}

	public void setApplicationDate(Date applicationDate) {
		this.applicationDate = applicationDate;
	}

	public Date getLastApprovalDate() {
		return lastApprovalDate;
	}

	public void setLastApprovalDate(Date lastApprovalDate) {
		this.lastApprovalDate = lastApprovalDate;
	}

	public String getLastApprovalCommon() {
		return lastApprovalCommon;
	}

	public void setLastApprovalCommon(String lastApprovalCommon) {
		this.lastApprovalCommon = lastApprovalCommon;
	}

	public Date getLastApprovalDate_gte() {
		return sqlMap.getWhere().getValue("last_approval_date", QueryType.GTE);
	}

	public void setLastApprovalDate_gte(Date lastApprovalDate) {
		sqlMap.getWhere().and("last_approval_date", QueryType.GTE, lastApprovalDate);
	}

	public Date getLastApprovalDate_lte() {
		return sqlMap.getWhere().getValue("last_approval_date", QueryType.LTE);
	}

	public void setLastApprovalDate_lte(Date lastApprovalDate) {
		sqlMap.getWhere().and("last_approval_date", QueryType.LTE, lastApprovalDate);
	}

	public String getApprovalCode() {
		return approvalCode;
	}

	public void setApprovalCode(String approvalCode) {
		this.approvalCode = approvalCode;
	}

	public String getLessor() {
		return lessor;
	}

	public void setLessor(String lessor) {
		this.lessor = lessor;
	}

	public String getRentalAddress() {
		return rentalAddress;
	}

	public void setRentalAddress(String rentalAddress) {
		this.rentalAddress = rentalAddress;
	}

	public String getCurrentOfficeSituation() {
		return currentOfficeSituation;
	}

	public void setCurrentOfficeSituation(String currentOfficeSituation) {
		this.currentOfficeSituation = currentOfficeSituation;
	}

	public void setLikeEntryNameOrProjectName(String likeEntryNameOrProjectName) {
		sqlMap.getWhere()
				.and("project_name", QueryType.LIKE, likeEntryNameOrProjectName)
				.or("entry_name", QueryType.LIKE, likeEntryNameOrProjectName);
	}
	public String getLikeEntryNameOrProjectName() {
		return sqlMap.getWhere().getValue("project_name", QueryType.LIKE);
	}
}
