package com.hsobs.ob.modules.approvedconfig.entity;


import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 附属用房面积核定配置Entity
 * <AUTHOR>
 * @version 2025-03-09
 */
@Table(name="ob_accessory_rooms_approved_config", alias="a", label="附属用房面积核定配置信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="dining_staff_threshold", attrName="diningStaffThreshold", label="食堂编制定员", isUpdateForce=true),
		@Column(name="dining_extra_threshold", attrName="diningExtraThreshold", label="食堂编制超员", isUpdateForce=true),
		@Column(name="dining_area_per_capita_base", attrName="diningAreaPerCapitaBase", label="食堂人均面积", isUpdateForce=true),
		@Column(name="dining_area_per_capita_extra", attrName="diningAreaPerCapitaExtra", label="食堂超编人均面积", isUpdateForce=true),
		@Column(name="car_park_base_area", attrName="carParkBaseArea", label="停车库基础车位面积", isUpdateForce=true),
		@Column(name="car_park_extra_threshold", attrName="carParkExtraThreshold", label="停车库超额车位阈值", isUpdateForce=true),
		@Column(name="car_park_extra_area", attrName="carParkExtraArea", label="停车库超额车位面积", isUpdateForce=true),
		@Column(name="bicycle_park_area", attrName="bicycleParkArea", label="自行车面积", isUpdateForce=true),
		@Column(name="emotorcycle_park_area", attrName="emotorcycleParkArea", label="电动车面积", isUpdateForce=true),
		@Column(name="security_room_area_per_capita", attrName="securityRoomAreaPerCapita", label="security_room_area_per_capita", isUpdateForce=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="修改人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, orderBy="a.update_date DESC"
)
public class AccessoryRoomsApprovedConfig extends DataEntity<AccessoryRoomsApprovedConfig> {
	
	private static final long serialVersionUID = 1L;
	private Integer diningStaffThreshold;		// 食堂编制定员
	private Integer diningExtraThreshold;		// 食堂编制超员
	private Double diningAreaPerCapitaBase;		// 食堂人均面积
	private Double diningAreaPerCapitaExtra;		// 食堂超编人均面积
	private Double carParkBaseArea;		// 停车库基础车位面积
	private Integer carParkExtraThreshold;		// 停车库超额车位阈值
	private Double carParkExtraArea;		// 停车库超额车位面积
	private Double bicycleParkArea;		// 自行车面积
	private Double emotorcycleParkArea;		// 电动车面积
	private Double securityRoomAreaPerCapita;		// security_room_area_per_capita

	public AccessoryRoomsApprovedConfig() {
		this(null);
	}
	
	public AccessoryRoomsApprovedConfig(String id){
		super(id);
	}
	
	public Integer getDiningStaffThreshold() {
		return diningStaffThreshold;
	}

	public void setDiningStaffThreshold(Integer diningStaffThreshold) {
		this.diningStaffThreshold = diningStaffThreshold;
	}
	
	public Double getDiningAreaPerCapitaBase() {
		return diningAreaPerCapitaBase;
	}

	public void setDiningAreaPerCapitaBase(Double diningAreaPerCapitaBase) {
		this.diningAreaPerCapitaBase = diningAreaPerCapitaBase;
	}
	
	public Double getDiningAreaPerCapitaExtra() {
		return diningAreaPerCapitaExtra;
	}

	public void setDiningAreaPerCapitaExtra(Double diningAreaPerCapitaExtra) {
		this.diningAreaPerCapitaExtra = diningAreaPerCapitaExtra;
	}
	
	public Double getCarParkBaseArea() {
		return carParkBaseArea;
	}

	public void setCarParkBaseArea(Double carParkBaseArea) {
		this.carParkBaseArea = carParkBaseArea;
	}
	
	public Integer getCarParkExtraThreshold() {
		return carParkExtraThreshold;
	}

	public void setCarParkExtraThreshold(Integer carParkExtraThreshold) {
		this.carParkExtraThreshold = carParkExtraThreshold;
	}
	
	public Double getCarParkExtraArea() {
		return carParkExtraArea;
	}

	public void setCarParkExtraArea(Double carParkExtraArea) {
		this.carParkExtraArea = carParkExtraArea;
	}
	
	public Double getBicycleParkArea() {
		return bicycleParkArea;
	}

	public void setBicycleParkArea(Double bicycleParkArea) {
		this.bicycleParkArea = bicycleParkArea;
	}
	
	public Double getEmotorcycleParkArea() {
		return emotorcycleParkArea;
	}

	public void setEmotorcycleParkArea(Double emotorcycleParkArea) {
		this.emotorcycleParkArea = emotorcycleParkArea;
	}
	
	public Double getSecurityRoomAreaPerCapita() {
		return securityRoomAreaPerCapita;
	}

	public void setSecurityRoomAreaPerCapita(Double securityRoomAreaPerCapita) {
		this.securityRoomAreaPerCapita = securityRoomAreaPerCapita;
	}

	public Integer getDiningExtraThreshold() {
		return diningExtraThreshold;
	}

	public void setDiningExtraThreshold(Integer diningExtraThreshold) {
		this.diningExtraThreshold = diningExtraThreshold;
	}
}