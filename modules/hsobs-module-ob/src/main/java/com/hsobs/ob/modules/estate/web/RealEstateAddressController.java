package com.hsobs.ob.modules.estate.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddressFloor;
import com.hsobs.ob.modules.estate.entity.RealEstateFloorAndRoomDto;
import com.hsobs.ob.modules.estate.entity.UploadMarkImageRequest;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.hsobs.ob.modules.estate.service.RealEstateAddressService;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * 不动产地址表Controller
 * <AUTHOR>
 * @version 2024-12-05
 */
@Controller
@RequestMapping(value = "${adminPath}/estate/realEstateAddress")
public class RealEstateAddressController extends BaseController {

	@Autowired
	private RealEstateAddressService realEstateAddressService;

	@Autowired
	private FileUploadService fileUploadService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public RealEstateAddress get(String id, boolean isNewRecord) {
		return realEstateAddressService.get(id, isNewRecord);
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("estate:realEstateAddress:view")
	@RequestMapping(value = {"list", ""})
	public String list(RealEstateAddress realEstateAddress, Model model) {
		model.addAttribute("realEstateAddress", realEstateAddress);
		model.addAttribute("realEstate", new RealEstate());
		return "modules/estate/realEstateAddressList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("estate:realEstateAddress:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<RealEstateAddress> listData(RealEstateAddress realEstateAddress, HttpServletRequest request, HttpServletResponse response) {
//		realEstateAddressService.addDataScopeFilter(realEstateAddress);
		realEstateAddress.setPage(new Page<>(request, response));
        return realEstateAddressService.findPage(realEstateAddress);
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("estate:realEstateAddress:view")
	@RequestMapping(value = "form")
	public String form(RealEstateAddress realEstateAddress, Model model) {
		realEstateAddressService.loadChildData(realEstateAddress);

		Long roomCountByAddress = realEstateAddressService.getRoomCountByAddress(realEstateAddress.getId());
		model.addAttribute("realEstateAddress", realEstateAddress);
		model.addAttribute("roomCount", roomCountByAddress);
		return "modules/estate/realEstateAddressForm";
	}

	@RequiresPermissions("estate:realEstateAddress:view")
	@RequestMapping(value = "findRealEstateAddressFloorList")
	@ResponseBody
	public List<RealEstateAddressFloor> findRealEstateAddressFloorList(RealEstateAddress realEstateAddress) {
		return realEstateAddressService.findRealEstateAddressFloorList(realEstateAddress);
	}

	@RequiresPermissions("estate:realEstateAddress:view")
	@RequestMapping(value = "findFloorAndRoomDto")
	@ResponseBody
	public List<RealEstateFloorAndRoomDto> findFloorAndRoomDto(RealEstateAddress realEstateAddress) {
		return realEstateAddressService.getFloorAndRoomDto(realEstateAddress);
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("estate:realEstateAddress:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated RealEstateAddress realEstateAddress) {
		realEstateAddressService.save(realEstateAddress);
		return renderResult(Global.TRUE, text("保存房屋成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("estate:realEstateAddress:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(RealEstateAddress realEstateAddress) {
		realEstateAddressService.delete(realEstateAddress);
		return renderResult(Global.TRUE, text("删除不动产地址表成功！"));
	}

	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("estate:realEstateAddress:view")
	@RequestMapping(value = "realEstateAddressSelect")
	public String realEstateAddressSelect(RealEstateAddress realEstateAddress, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("realEstateAddress", realEstateAddress);
		return "modules/estate/realEstateAddressSelect";
	}


	@RequiresPermissions("estate:realEstateAddress:edit")
	@RequestMapping(value = "uploadMarkImage")
	@ResponseBody
	public String uploadMarkImage(UploadMarkImageRequest uploadMarkImageRequest) throws NoSuchAlgorithmException, IOException {

		// 1. 参数校验
		if (StringUtils.isAnyBlank(uploadMarkImageRequest.getBizKey(),
				uploadMarkImageRequest.getBizType(),
				uploadMarkImageRequest.getImageBase64())) {
			return renderResult(Global.FALSE, text("参数[bizKey、bizType、imageBase64]不能为空"));
		}

		// 2. 解析Base64数据
		String[] base64Parts = uploadMarkImageRequest.getImageBase64().split(",");
		String contentType = "image/jpeg";
		String base64Data;

		if (base64Parts.length > 1) {
			contentType = base64Parts[0].split(":")[1].split(";")[0];
			base64Data = base64Parts[1];
		} else {
			base64Data = base64Parts[0];
		}

		// 3. Base64解码
		byte[] imageBytes = Base64.getDecoder().decode(base64Data.trim());


		// 4. 计算MD5
		MessageDigest md5Digest = MessageDigest.getInstance("MD5");
		md5Digest.update(imageBytes);
		String md5Hash = bytesToHex(md5Digest.digest());

		List<FileUpload> fileUploadList = FileUploadUtils.findFileUpload(uploadMarkImageRequest.getBizKey(), uploadMarkImageRequest.getBizType());
		FileUpload fileUpload = fileUploadList.get(0);
		// 6. 创建MultipartFile
		MultipartFile multipartFile = new MockMultipartFile(
				"file",
				fileUpload.getFileName(),
				contentType,
				new ByteArrayInputStream(imageBytes)
		);

		FileUploadParams fileUploadParams = new FileUploadParams();
		fileUploadParams.setFile(multipartFile);
		fileUploadParams.setFileName(fileUpload.getFileName());
		fileUploadParams.setFileMd5(md5Hash);
		fileUploadParams.setBizKey(uploadMarkImageRequest.getBizKey());
		fileUploadParams.setBizType(uploadMarkImageRequest.getBizType());

//		System.out.println(uploadMarkImageRequest);
//		FileUploadParams fileUploadParams = new FileUploadParams();
//		fileUploadParams.setFileName(fileUpload.getFileName());
//		FileUploadUtils.saveFileUpload(fileUpload, fileUploadParams);
//		FileUploadUtils.saveFileUpload(fileUpload, fileUploadParams);
//		fileUploadService.delete(fileUpload);
		Map<String, Object> stringObjectMap = fileUploadService.uploadFile(new FileUpload(), fileUploadParams);
		FileUploadUtils.saveFileUpload(uploadMarkImageRequest.getBizKey(), uploadMarkImageRequest.getBizType(), stringObjectMap.get("fileUpload").toString(), fileUpload.getId());
//		FileUploadUtils.saveFileUpload(uploadMarkImageRequest.getBizKey(), uploadMarkImageRequest.getBizType());
//		fileUploadService.delete(fileUpload);
		return renderResult(Global.TRUE, text("保存标注图片成功！"));
	}

	// 字节数组转十六进制字符串
	private static String bytesToHex(byte[] bytes) {
		StringBuilder hexString = new StringBuilder();
		for (byte b : bytes) {
			String hex = Integer.toHexString(0xff & b);
			if (hex.length() == 1) hexString.append('0');
			hexString.append(hex);
		}
		return hexString.toString();
	}

	// 根据ContentType获取文件扩展名
	private String getFileExtension(String contentType) {
		switch (contentType.toLowerCase()) {
			case "image/png": return "png";
			case "image/gif": return "gif";
			case "image/webp": return "webp";
			default: return "jpg";
		}
	}
}