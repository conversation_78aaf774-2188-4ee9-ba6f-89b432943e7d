package com.hsobs.ob.modules.datastatistics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: UnuseTable
 * @projectName base
 * @description: 全省办公用房闲置情况
 * @date 2024/12/249:39
 */
public class UnuseTable extends DataEntity<UnuseTable> {
    private String areaName;    //区域
    private String officeName;    //单位信息
    private Integer officeNumber;    //办公用房数量
    private Integer unuseNumber;    //闲置数量
    private float unuseSpace;    //闲置面积
    private float unuseRate;    //闲置率
    private String unuseDate; //闲置日期
    private String areaCode;
    private String officeCode;
    private String dateGte;	//使用日期上限
    private String dateLte;	//使用日期下限

    @ExcelFields({
            @ExcelField(title="区域", attrName="areaName", align= ExcelField.Align.CENTER, sort=20),
            @ExcelField(title="单位信息", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
            @ExcelField(title="办公用房数量", attrName="unuseNumber", align= ExcelField.Align.CENTER, sort=20),
            @ExcelField(title="闲置日期", attrName="unuseDate", align= ExcelField.Align.CENTER, sort=21),
            @ExcelField(title="闲置数量", attrName="unuseNumber", dictType="disposal_type", align= ExcelField.Align.CENTER, sort=30),
            @ExcelField(title="闲置面积", attrName="unuseSpace", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="闲置率", attrName="unuseRate", align= ExcelField.Align.CENTER, sort=50),
    })
    public String getDateGte() {
        return dateGte;
    }

    public String getUnuseDate() {
        return unuseDate;
    }

    public void setUnuseDate(String unuseDate) {
        this.unuseDate = unuseDate;
    }

    public void setDateGte(String dateGte) {
        this.dateGte = dateGte;
    }

    public String getDateLte() {
        return dateLte;
    }

    public void setDateLte(String dateLte) {
        this.dateLte = dateLte;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public Integer getOfficeNumber() {
        return officeNumber;
    }

    public void setOfficeNumber(Integer officeNumber) {
        this.officeNumber = officeNumber;
    }

    public Integer getUnuseNumber() {
        return unuseNumber;
    }

    public void setUnuseNumber(Integer unuseNumber) {
        this.unuseNumber = unuseNumber;
    }

    public float getUnuseSpace() {
        return unuseSpace;
    }

    public void setUnuseSpace(float unuseSpace) {
        this.unuseSpace = unuseSpace;
    }

    public float getUnuseRate() {
        return unuseRate;
    }

    public void setUnuseRate(float unuseRate) {
        this.unuseRate = unuseRate;
    }
}
