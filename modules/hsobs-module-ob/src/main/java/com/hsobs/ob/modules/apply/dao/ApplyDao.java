package com.hsobs.ob.modules.apply.dao;

import com.hsobs.ob.modules.apply.entity.*;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;

import java.util.List;

/**
 * 使用管理DAO接口
 * <AUTHOR>
 * @version 2025-02-08
 */
@MyBatisDao
public interface ApplyDao extends CrudDao<Apply> {

    List<ApplyLedger> listLedgerData(ApplyLedger applyLedger);

    List<CredentialsAgreementsListResponse> findCredentialsAgreementsList(CredentialsAgreementsListResponse response);
    Long findCredentialsAgreementsListCount(CredentialsAgreementsListResponse response);

    List<ApplyDetailQuery> listApplyDetailQueryData(ApplyDetailQuery applyDetailQuery);
    List<ApplyInfoQuery> listApplyInfoQueryData(ApplyInfoQuery applyInfoQuery);
}