package com.hsobs.ob.modules.bpm.builder;

import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

/**
 * SQL列映射构建器
 * 用于构建SQL查询中的列映射部分
 */
public class ColumnMappingBuilder {
    
    private static final Logger logger = LoggerFactory.getLogger(ColumnMappingBuilder.class);
    private final StringBuilder columns;
    private final Set<String> processedColumns;

    public ColumnMappingBuilder() {
        this.columns = new StringBuilder();
        this.processedColumns = new HashSet<>();
    }

    /**
     * 生成列映射
     */
    public static String generate(Table tableAnn) {
        ColumnMappingBuilder builder = new ColumnMappingBuilder();
        return builder.generateColumnMappings(tableAnn);
    }

    private String generateColumnMappings(Table tableAnn) {
        try {
            // 添加主键
            addPrimaryKey(tableAnn);
            
            // 处理当前表的列
            processTableColumns(tableAnn);
            
            // 处理关联表
            processJoinTableColumns(tableAnn);

            return buildResult();
        } catch (Exception e) {
            logger.error("Error generating column mappings", e);
            return "";
        }
    }

    private void addPrimaryKey(Table tableAnn) {
        appendColumn(tableAnn.alias(), "id", "id");
    }

    private void processTableColumns(Table tableAnn) {
        for (Column column : tableAnn.columns()) {
            // 处理包含的实体
            if (column.includeEntity() != null) {
                processIncludedEntity(column.includeEntity(), tableAnn.alias());
            }
            // 处理当前列
            addColumnIfValid(column, tableAnn.alias(), "");
        }
    }

    private void processJoinTableColumns(Table tableAnn) {
        for (JoinTable joinTable : tableAnn.joinTable()) {
            String prefix = joinTable.attrName().replace(".", "_");
            
            // 处理关联表的列
            for (Column column : joinTable.columns()) {
                if (column.includeEntity() != null) {
                    processIncludedEntityForJoinTable(column.includeEntity(), joinTable, prefix);
                } else {
                    addColumnIfValid(column, joinTable.alias(), prefix + "_");
                }
            }
        }
    }

    private void processIncludedEntity(Class<?> entityClass, String alias) {
        Table includedTable = entityClass.getAnnotation(Table.class);
        if (includedTable != null) {
            for (Column column : includedTable.columns()) {
                // 递归处理嵌套的包含实体
                if (column.includeEntity() != null) {
                    processIncludedEntity(column.includeEntity(), alias);
                }
                addColumnIfValid(column, alias, "");
            }
        }
    }

    private void processIncludedEntityForJoinTable(Class<?> entityClass, JoinTable joinTable, String prefix) {
        Table includedTable = entityClass.getAnnotation(Table.class);
        if (includedTable != null) {
            for (Column column : includedTable.columns()) {
                // 处理嵌套的包含实体
                if (column.includeEntity() != null) {
                    processIncludedTable(column.includeEntity().getAnnotation(Table.class),
                            prefix + "_" + column.attrName(),
                            joinTable);
                }
                addColumnIfValid(column, joinTable.alias(), prefix + "_");
            }
        }
    }

    private void processIncludedTable(Table includedTable, String prefix, JoinTable joinTable) {
        if (includedTable != null) {
            for (Column column : includedTable.columns()) {
                addColumnIfValid(column, joinTable.alias(), prefix + "_");
            }
        }
    }

    private void addColumnIfValid(Column column, String alias, String prefix) {
        if (isValidColumn(column, prefix)) {
            appendColumn(alias, column.name(), prefix + column.attrName());
        }
    }

    private boolean isValidColumn(Column column, String prefix) {
        return StringUtils.isNotBlank(column.attrName()) 
            && !processedColumns.contains(prefix + column.attrName())
            && !StringUtils.isBlank(column.name());
    }

    private void appendColumn(String alias, String columnName, String asName) {
        columns.append(alias)
              .append(".")
              .append(columnName)
              .append(" AS \"")
              .append(asName)
              .append("\",");
        processedColumns.add(asName);
    }

    private String buildResult() {
        if (columns.length() > 0 && columns.charAt(columns.length() - 1) == ',') {
            columns.setLength(columns.length() - 1);
        }
        return columns.toString();
    }
} 