package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;

/**
 * <AUTHOR>
 * @title: UnuseTable
 * @projectName base
 * @description: 全省办公用房闲置情况
 * @date 2024/12/249:39
 */
public class UseTable extends DataEntity<UseTable> {
    private String areaName;    //区域
    private String officeName;    //单位信息
    private Integer officeNumber;    //办公用房数量
    private Integer unuseNumber;    //闲置数量
    private float unuseSpace;    //闲置面积
    private float unuseRate;    //闲置率
    private String areaCode;
    private String region;
    private String year;
    private String officeCode;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public Integer getOfficeNumber() {
        return officeNumber;
    }

    public void setOfficeNumber(Integer officeNumber) {
        this.officeNumber = officeNumber;
    }

    public Integer getUnuseNumber() {
        return unuseNumber;
    }

    public void setUnuseNumber(Integer unuseNumber) {
        this.unuseNumber = unuseNumber;
    }

    public float getUnuseSpace() {
        return unuseSpace;
    }

    public void setUnuseSpace(float unuseSpace) {
        this.unuseSpace = unuseSpace;
    }

    public float getUnuseRate() {
        return unuseRate;
    }

    public void setUnuseRate(float unuseRate) {
        this.unuseRate = unuseRate;
    }
}
