package com.hsobs.ob.modules.datastatistics.dao;

import com.hsobs.ob.modules.datastatistics.entity.*;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;

import java.util.List;
import java.util.Map;

/**
 * 办公用房数据统计DAO接口
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataStatisticsDao extends CrudDao<DataStatisticsForResource> {

    List<DisposeTable> countDisposalNumber(DisposeTable disposeTableQuery);

    List<SupervisionTable> countSupervisionNumber(SupervisionTable supervisionTable);

    List<Map<String, Object>> officeOccupancy(SupervisionTable supervisionTable);

    List<ResourceCase> findResourceCaseData(ResourceCase resourceCase);

    List<SpaceVerification> findSpaceVerificationData(SpaceVerification spaceVerificationQuery);

    List<Map<String, Object>> officeResourceCout(ResourceCase resourceCase);

    List<Map<String, Object>> officeResourceNumberCout(String officeType, String officeCode);

    List<OwnershipTable> findOwnershipDataPage(OwnershipTable ownershipTableQuery);

    List<Map<String, Object>> ownershipCount(String areaCode, String year);

    List<AllocationTable> findAllocationDataPage(AllocationTable allocationTableQuery);

    List<Map<String, Object>> allocationCountData(String areaCode, String year);

    List<Map<String, Object>> allocationAreaCountData(String areaCode, String year);

    List<MaintainTable> findMaintainPage(MaintainTable maintainTableQuery);

    List<Map<String, Object>> findMaintainCount(String areaCode, String year);

    List<Map<String, Object>> maintainNumberCountData(String areaCode, String year);

    List<UnuseTable> findUnusePage(UnuseTable unuseTableQuery);

    List<Map<String, Object>> calculateUsageAreaRatioByUnit(UseTable useTable);

    List<Map<String, Object>> calculateUsageAreaRatioByRank(UseTable useTable);

    List<Map<String, Object>> analyzeOfficeSpaceUsageStatus(UseTable useTable);

    List<Map<String, Object>> analyzeOfficeSpaceAnalysis(UseTable useTable);

    List<Map<String, Object>> officeUsageStats(UseTable useTable);

    List<Map<String, Object>> allocationAreaCountBarData(String areaCode, String year);
}