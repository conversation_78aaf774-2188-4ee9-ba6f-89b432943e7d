package com.hsobs.ob.modules.datastatistics.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.hsobs.ob.modules.datastatistics.entity.*;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagementQuery;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.datastatistics.service.DataStatisticsService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 办公用房数据统计Controller
 * <AUTHOR>
 * @version 2024-12-16
 */
@Controller
@RequestMapping(value = "${adminPath}/datastatistics/")
public class DataStatisticsController extends BaseController {

	@Autowired
	private DataStatisticsService dataStatisticsService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataStatisticsForResource get(Integer type, boolean isNewRecord) {
		return dataStatisticsService.get(type, isNewRecord);
	}

	/**
	 * 办公用房资源情况统计视图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsResource", ""})
	public String dataStatisticsResource(DataStatisticsForResource dataStatisticsForResource, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsForResource);
		return "modules/datastatistics/dataStatisticsResource";
	}

	/**
	 * 地图查询
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsMap", ""})
	public String dataStatisticsMap(Map<String,Object> dataStatisticsMap, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsMap);
		return "modules/datastatistics/dataStatisticsMap";
	}

	/**
	 * 办公用房权属情况统计视图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsOwnership", ""})
	public String dataStatisticsOwnership(DataStatisticsForResource dataStatisticsForResource, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsForResource);
		return "modules/datastatistics/dataStatisticsOwnership";
	}

	/**
	 * 办公用房配置情况统计视图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsAllocation", ""})
	public String dataStatisticsAllocation(DataStatisticsForAllocation dataStatisticsForAllocation, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsForAllocation);
		return "modules/datastatistics/dataStatisticsAllocation";
	}

	/**
	 * 办公用房处置利用情况统计视图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsDispose", ""})
	public String dataStatisticsDispose(DisposeTable disposeTable, Model model) {
		model.addAttribute("dataStatistics", disposeTable);
		return "modules/datastatistics/dataStatisticsDispose";
	}

	/**
	 * 办公用房使用情况统计视图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsUse", ""})
	public String dataStatisticsUse(DataStatisticsForUse dataStatisticsForUse, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsForUse);
		return "modules/datastatistics/dataStatisticsUse";
	}

	/**
	 * 办公用房维修情况统计视图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsMaintain", ""})
	public String dataStatisticsMaintain(DataStatisticsForMaintain dataStatisticsForMaintain, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsForMaintain);
		return "modules/datastatistics/dataStatisticsMaintain";
	}

	/**
	 * 办公用房闲置情况统计视图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsUnuse", ""})
	public String dataStatisticsUnuse(DataStatisticsForUnuse dataStatisticsForUnuse, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsForUnuse);
		return "modules/datastatistics/dataStatisticsUnuse";
	}

	/**
	 * 办公用房监督整改情况统计视图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = {"dataStatisticsSupervision", ""})
	public String dataStatisticsSupervision(DataStatisticsForSupervision dataStatisticsForSupervision,SupervisionTable supervisionTable, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsForSupervision);
		model.addAttribute("supervisionTable", supervisionTable);
		return "modules/datastatistics/dataStatisticsSupervision";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<DataStatisticsForResource> listData(DataStatisticsForResource dataStatisticsForResource, HttpServletRequest request, HttpServletResponse response) {
		dataStatisticsForResource.setPage(new Page<>(request, response));
		Page<DataStatisticsForResource> page = dataStatisticsService.findPage(dataStatisticsForResource);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "resourceData")
	@ResponseBody
	public Page<DataStatisticsForResource> resourceData(DataStatisticsForResource dataStatisticsForResource, HttpServletRequest request, HttpServletResponse response) {
		dataStatisticsForResource.setPage(new Page<>(request, response));
		Page<DataStatisticsForResource> page = dataStatisticsService.findResourceDataPage(dataStatisticsForResource);
		return page;
	}

	/**
	 * 办公用房功能统计
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "officeResourceCout")
	@ResponseBody
	public String officeResourceCout(ResourceCase resourceCase, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.officeResourceCout(resourceCase,"-1");
	}

	/**
	 * 办公用房功能统计
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "officeResourceNumberCout")
	@ResponseBody
	public String officeResourceNumberCout(ResourceCase resourceCase, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.officeResourceNumberCout(resourceCase);
	}


	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "resourceCaseData")
	@ResponseBody
	public Page<ResourceCase> resourceCaseData(ResourceCase resourceCase, HttpServletRequest request, HttpServletResponse response) {
		resourceCase.setPage(new Page<>(request, response));
		Page<ResourceCase> page = dataStatisticsService.findResourceCaseData(resourceCase);
		return page;
	}
	/**
	 * 查询办公用房资源情况面积核定表数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "spaceVerificationData")
	@ResponseBody
	public Page<SpaceVerification> spaceVerificationData(SpaceVerification spaceVerification, HttpServletRequest request, HttpServletResponse response) {
		spaceVerification.setPage(new Page<>(request, response));
		Page<SpaceVerification> page = dataStatisticsService.spaceVerificationData(spaceVerification);
		return page;
	}
	/**
	 * 导出办公用房资源情况面积核定表数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "exportSpaceVerificationData")
	@ResponseBody
	public void exportSpaceVerificationData(SpaceVerification spaceVerification, HttpServletRequest request, HttpServletResponse response) {
		List<SpaceVerification> list = dataStatisticsService.exportSpaceVerificationData(spaceVerification);
		String fileName = "省直单位办公业务用房面积核定表(表二)" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("省直单位办公业务用房面积核定表(表二)", SpaceVerification.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 查询办公用房权属情况列表数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "ownershipData")
	@ResponseBody
	public Page<OwnershipTable> ownershipData(OwnershipTable ownershipTable, HttpServletRequest request, HttpServletResponse response) {
		ownershipTable.setPage(new Page<>(request, response));
		Page<OwnershipTable> page = dataStatisticsService.findOwnershipDataPage(ownershipTable);
		return page;
	}

	/**
	 * 查询办公用房权属情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "ownershipCount")
	@ResponseBody
	public String ownershipCount(OwnershipTable ownershipTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.ownershipCount(ownershipTable);
	}

	/**
	 * 查询办公用房配置情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "allocationData")
	@ResponseBody
	public Page<AllocationTable> allocationData(AllocationTable allocationTable, HttpServletRequest request, HttpServletResponse response) {
		allocationTable.setPage(new Page<>(request, response));
		Page<AllocationTable> page = dataStatisticsService.findAllocationDataPage(allocationTable);
		return page;
	}
	/**
	 * 全省办公用房配置总数-饼图
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "allocationCountData")
	@ResponseBody
	public String allocationCountData(AllocationTable allocationTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.allocationCountData(allocationTable);
	}
	/**
	 * 全省办公用房配置总数-3个仪表盘
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "allocationNumberCountData")
	@ResponseBody
	public String allocationNumberCountData(AllocationTable allocationTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.allocationNumberCountData(allocationTable);
	}
	/**
	 * 查询办公用房配置情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "allocationAreaCountData")
	@ResponseBody
	public String allocationAreaCountData(AllocationTable allocationTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.allocationAreaCountData(allocationTable);
	}
	/**
	 * 查询办公用房配置情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "allocationAreaCountBarData")
	@ResponseBody
	public String allocationAreaCountBarData(AllocationTable allocationTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.allocationAreaCountBarData(allocationTable);
	}

	/**
	 * 查询办公用房处置利用情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "disposeData")
	@ResponseBody
	public Page<DisposeTable> disposeData(DisposeTable disposeTable, HttpServletRequest request, HttpServletResponse response) {
		disposeTable.setPage(new Page<>(request, response));
		dataStatisticsService.addDataScopeFilter(disposeTable);
		Page<DisposeTable> page = dataStatisticsService.findDisposeDataPage(disposeTable);
		return page;
	}

	/**
	 * 查询办公用房维修情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "maintainData")
	@ResponseBody
	public Page<MaintainTable> maintainData(MaintainTable maintainTable, HttpServletRequest request, HttpServletResponse response) {
		maintainTable.setPage(new Page<>(request, response));
		Page<MaintainTable> page = dataStatisticsService.findMaintainPage(maintainTable);
		return page;
	}

	/**
	 * 查询办公用房维修情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "maintainCount")
	@ResponseBody
	public String maintainCount(MaintainTable maintainTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.findMaintainCount(maintainTable);
	}

	/**
	 * 查询办公用房维修情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "maintainCountData")
	@ResponseBody
	public String maintainCountData(MaintainTable maintainTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.maintainCountData(maintainTable);
	}

	/**
	 * 查询办公用房维修情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "maintainNumberCountData")
	@ResponseBody
	public String maintainNumberCountData(MaintainTable maintainTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.maintainNumberCountData(maintainTable);
	}

	/**
	 * 查询办公用房维修情况统计视图数据-维修费用分析
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "maintainCosAanalysis")
	@ResponseBody
	public String maintainCosAanalysis(MaintainTable maintainTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.maintainCosAanalysis(maintainTable);
	}

	/**
	 * 查询办公用房闲置情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "unuseData")
	@ResponseBody
	public Page<UnuseTable> unuseData(UnuseTable unuseTable, HttpServletRequest request, HttpServletResponse response) {
		unuseTable.setPage(new Page<>(request, response));
		dataStatisticsService.addDataScopeFilter(unuseTable);
		Page<UnuseTable> page = dataStatisticsService.findUnusePage(unuseTable);
		return page;
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "exportUnuseData")
	public void exportUnuseData(UnuseTable unuseTable, HttpServletResponse response) {
		List<UnuseTable> list = dataStatisticsService.exportUnuseData(unuseTable);
		String fileName = "办公用房闲置情况" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("办公用房闲置情况", UnuseTable.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "exportDisposeData")
	public void exportDisposeData(DisposeTable disposeTable, HttpServletResponse response) {
		List<DisposeTable> list = dataStatisticsService.exportDisposeData(disposeTable);
		String fileName = "办公用房处置利用情况" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("办公用房处置利用情况", UnuseTable.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	/**
	 * 查询办公用房监督整改情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "supervisionData")
	@ResponseBody
	public Page<DataStatisticsForSupervision> supervisionData(DataStatisticsForSupervision dataStatisticsForSupervision, HttpServletRequest request, HttpServletResponse response) {
		dataStatisticsForSupervision.setPage(new Page<>(request, response));
		Page<DataStatisticsForSupervision> page = dataStatisticsService.getDataStatisticsForSupervision(dataStatisticsForSupervision);
		return page;
	}

	/**
	 * 查询办公用房监督整改情况统计视图数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "supervisionListData")
	@ResponseBody
	public Page<SupervisionTable> supervisionListData(SupervisionTable supervisionTable, HttpServletRequest request, HttpServletResponse response) {
		supervisionTable.setPage(new Page<>(request, response));
		dataStatisticsService.addDataScopeFilter(supervisionTable);
		Page<SupervisionTable> page = dataStatisticsService.findSupervisionPage(supervisionTable);
		return page;
	}


	/**
	 * 导出数据
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "exportSupervisionListData")
	public void exportSupervisionListData(SupervisionTable supervisionTable, HttpServletResponse response) {
		dataStatisticsService.addDataScopeFilter(supervisionTable);
		List<SupervisionTable> list = dataStatisticsService.exportSupervisionListData(supervisionTable);
		String fileName = "办公用房监督整改情况表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("办公用房监督整改情况表", SupervisionTable.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	/**
	 * 查询办公室用房情况统计视图数据-办公室用房
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "officeOccupancy")
	@ResponseBody
	public String officeOccupancy(SupervisionTable supervisionTable, HttpServletRequest request, HttpServletResponse response) {
		dataStatisticsService.addDataScopeFilter(supervisionTable);
		return dataStatisticsService.officeOccupancy(supervisionTable,"0");
	}
	/**
	 * 查询办公室用房情况统计视图数据-服务用房
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "serviceOccupancy")
	@ResponseBody
	public String serviceOccupancy(SupervisionTable supervisionTable, HttpServletRequest request, HttpServletResponse response) {
		dataStatisticsService.addDataScopeFilter(supervisionTable);
		return dataStatisticsService.officeOccupancy(supervisionTable,"1");
	}
	/**
	 * 查询办公室用房情况统计视图数据-设备用房
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "deviceOccupancy")
	@ResponseBody
	public String deviceOccupancy(SupervisionTable supervisionTable, HttpServletRequest request, HttpServletResponse response) {
		dataStatisticsService.addDataScopeFilter(supervisionTable);
		return dataStatisticsService.officeOccupancy(supervisionTable,"2");
	}
	/**
	 * 查询办公室用房情况统计视图数据-附属用房
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "accessoryOccupancy")
	@ResponseBody
	public String accessoryOccupancy(SupervisionTable supervisionTable, HttpServletRequest request, HttpServletResponse response) {
		dataStatisticsService.addDataScopeFilter(supervisionTable);
		return dataStatisticsService.officeOccupancy(supervisionTable,"3");
	}
	/**
	 * 查询办公室用房情况统计视图数据-技术用房
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "technologyOccupancy")
	@ResponseBody
	public String technologyOccupancy(SupervisionTable supervisionTable, HttpServletRequest request, HttpServletResponse response) {
		dataStatisticsService.addDataScopeFilter(supervisionTable);
		return dataStatisticsService.officeOccupancy(supervisionTable,"4");
	}
	/**
	 * 查询办公室用房使用情况统计视图数据-各单位使用面积占比
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "calculateUsageAreaRatioByUnit")
	@ResponseBody
	public String calculateUsageAreaRatioByUnit(UseTable useTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.calculateUsageAreaRatioByUnit(useTable);
	}
	/**
	 * 查询办公室用房使用情况统计视图数据-各职位使用面积占比
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "calculateUsageAreaRatioByRank")
	@ResponseBody
	public String calculateUsageAreaRatioByRank(UseTable useTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.calculateUsageAreaRatioByRank(useTable);
	}
	/**
	 * 查询办公室用房使用情况统计视图数据-办公用房使用状态分析
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "analyzeOfficeSpaceUsageStatus")
	@ResponseBody
	public String analyzeOfficeSpaceUsageStatus( UseTable useTable,HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.analyzeOfficeSpaceUsageStatus(useTable);
	}
	/**
	 * 查询办公室用房使用情况统计视图数据-办公用房使用超标分析
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "analyzeOfficeSpaceAnalysis")
	@ResponseBody
	public String analyzeOfficeSpaceAnalysis(UseTable useTable, HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.analyzeOfficeSpaceAnalysis(useTable);
	}
	/**
	 * 查询办公室用房使用情况统计视图数据-办公用房使用状态分析
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "officeUsageStats")
	@ResponseBody
	public String officeUsageStats(UseTable useTable,  HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.officeUsageStats(useTable);
	}
	/**
	 * 查询办公室用房使用情况统计视图数据-办公用房使用状态分析
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "officeUsageStatsCount")
	@ResponseBody
	public String officeUsageStatsCount(UseTable useTable,  HttpServletRequest request, HttpServletResponse response) {
		return dataStatisticsService.officeUsageStatsCount(useTable);
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("datastatistics::view")
	@RequestMapping(value = "form")
	public String form(DataStatisticsForResource dataStatisticsForResource, Model model) {
		model.addAttribute("dataStatistics", dataStatisticsForResource);
		return "modules/datastatistics/dataStatisticsForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("datastatistics::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated DataStatisticsForResource dataStatisticsForResource) {
		dataStatisticsService.save(dataStatisticsForResource);
		return renderResult(Global.TRUE, text("保存办公用房数据统计成功！"));
	}
	
}