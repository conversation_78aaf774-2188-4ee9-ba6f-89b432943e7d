package com.hsobs.ob.modules.repair.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;

/**
 * 维修组织记录表Entity
 * <AUTHOR>
 * @version 2025-03-16
 */
@Table(name="ob_repair_record", alias="a", label="维修组织记录表信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="office_code", attrName="officeCode", label="单位编码"),
		@Column(name="repair_request_id", attrName="repairRequestId", label="维修申请表ID"),
		@Column(name="price", attrName="price", label="金额", queryType=QueryType.LTE),
		@Column(name="repair_status", attrName="repairStatus", label="维修状态"),
		@Column(name="start_date", attrName="startDate", label="维修开始时间", queryType=QueryType.GTE),
		@Column(name="end_date", attrName="endDate", label="维修结束时间", queryType=QueryType.LTE),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "office",
				on = "a.office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = RepairRequest.class, alias = "rr",
				attrName = "repairRequest",
				on = "a.repair_request_id = rr.id",
				columns = {@Column(includeEntity = RepairRequest.class)}),
	}, orderBy="a.update_date DESC"
)
public class RepairRecord extends DataEntity<RepairRecord> {
	
	private static final long serialVersionUID = 1L;
	private String officeCode;		// 单位编码
	private String repairRequestId;		// 维修申请表ID
	private Double price;		// 金额
	private String repairStatus;		// 维修状态
	private Date startDate;		// 维修开始时间
	private Date endDate;		// 维修结束时间

	private Office office;
	private RepairRequest repairRequest;

	@ExcelFields({
		@ExcelField(title="单位编码", attrName="officeCode", align=Align.CENTER, sort=20),
		@ExcelField(title="维修申请表ID", attrName="repairRequestId", align=Align.CENTER, sort=30),
		@ExcelField(title="金额", attrName="price", align=Align.CENTER, sort=40),
		@ExcelField(title="维修状态", attrName="repairStatus", align=Align.CENTER, sort=50),
		@ExcelField(title="维修开始时间", attrName="startDate", align=Align.CENTER, sort=60, dataFormat="yyyy-MM-dd hh:mm"),
		@ExcelField(title="维修结束时间", attrName="endDate", align=Align.CENTER, sort=70, dataFormat="yyyy-MM-dd hh:mm"),
	})
	public RepairRecord() {
		this(null);
	}
	
	public RepairRecord(String id){
		super(id);
	}
	
	@NotBlank(message="单位编码不能为空")
	@Size(min=0, max=64, message="单位编码长度不能超过 64 个字符")
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
	
	@NotBlank(message="维修申请表ID不能为空")
	@Size(min=0, max=64, message="维修申请表ID长度不能超过 64 个字符")
	public String getRepairRequestId() {
		return repairRequestId;
	}

	public void setRepairRequestId(String repairRequestId) {
		this.repairRequestId = repairRequestId;
	}
	
	@NotNull(message="金额不能为空")
	public Double getPrice() {
		return price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}
	
	@NotBlank(message="维修状态不能为空")
	@Size(min=0, max=2, message="维修状态长度不能超过 2 个字符")
	public String getRepairStatus() {
		return repairStatus;
	}

	public void setRepairStatus(String repairStatus) {
		this.repairStatus = repairStatus;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="维修开始时间不能为空")
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="维修结束时间不能为空")
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	public RepairRequest getRepairRequest() {
		return repairRequest;
	}

	public void setRepairRequest(RepairRequest repairRequest) {
		this.repairRequest = repairRequest;
	}
}