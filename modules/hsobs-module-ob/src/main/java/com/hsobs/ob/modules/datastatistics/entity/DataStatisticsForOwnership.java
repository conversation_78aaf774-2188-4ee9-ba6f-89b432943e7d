package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;

/**
 * 办公用房权属情况数据统计Entity
 * <AUTHOR>
 * @version 2024-12-24
 */
public class DataStatisticsForOwnership extends DataEntity<DataStatisticsForOwnership> {
	
	private static final long serialVersionUID = 1L;
	private String sysCode;
	private OwnershipView OwnershipView;
	private OwnershipTable ownershipTable;

	public DataStatisticsForOwnership() {
		this(null);
	}
	
	public DataStatisticsForOwnership(String id){
		super(id);
	}

	public String getSysCode() {
		return sysCode;
	}

	public void setSysCode(String sysCode) {
		this.sysCode = sysCode;
	}

	public com.hsobs.ob.modules.datastatistics.entity.OwnershipView getOwnershipView() {
		return OwnershipView;
	}

	public void setOwnershipView(com.hsobs.ob.modules.datastatistics.entity.OwnershipView ownershipView) {
		OwnershipView = ownershipView;
	}

	public OwnershipTable getOwnershipTable() {
		return ownershipTable;
	}

	public void setOwnershipTable(OwnershipTable ownershipTable) {
		this.ownershipTable = ownershipTable;
	}
}