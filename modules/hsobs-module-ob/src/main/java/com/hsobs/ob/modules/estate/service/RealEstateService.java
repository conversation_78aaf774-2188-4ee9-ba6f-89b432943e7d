package com.hsobs.ob.modules.estate.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.hsobs.ob.modules.estate.dao.RealEstateUsedUserDao;
import com.hsobs.ob.modules.estate.entity.OfficeTechnicalBusinessAreaDto;
import com.hsobs.ob.modules.estate.entity.RealEstateQuery;
import com.hsobs.ob.modules.estate.entity.RealEstateUsedUser;
import com.jeesite.common.entity.DataScope;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.service.EmpUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.dao.RealEstateDao;

/**
 * 不动产信息表Service
 * <AUTHOR>
 * @version 2024-12-08
 */
@Service
public class RealEstateService extends CrudService<RealEstateDao, RealEstate> {

	@Autowired
	private RealEstateDao realEstateDao;

	@Autowired
	RealEstateUsedUserDao realEstateUsedUserDao;

	@Autowired
	EmpUserService empUserService;
	
	/**
	 * 获取单条数据
	 * @param realEstate
	 * @return
	 */
	@Override
	public RealEstate get(RealEstate realEstate) {
		RealEstate entity = super.get(realEstate);
		if (null != entity) {
			RealEstateUsedUser whereRealEstateUsedUser = new RealEstateUsedUser();
			whereRealEstateUsedUser.setRealEstateId(realEstate);
			List<RealEstateUsedUser> list = realEstateUsedUserDao.findList(whereRealEstateUsedUser);
			List<String> userCodeList = list.stream().map(RealEstateUsedUser::getUserCode).collect(Collectors.toList());
			EmpUser whereUser = new EmpUser();
			whereUser.setId_in(userCodeList.toArray(new String[0]));
			List<EmpUser> userList = empUserService.findList(whereUser);
			realEstate.setUsedUserList(userList);
		}
		return entity;
	}

	public RealEstate loadUsedUserList(RealEstate realEstate) {
		realEstate.setUsedUserList(new ArrayList<>());
		if (null != realEstate.getId() && !realEstate.getId().isEmpty()) {
			RealEstateUsedUser whereRealEstateUsedUser = new RealEstateUsedUser();
			whereRealEstateUsedUser.setRealEstateId(realEstate);
			List<RealEstateUsedUser> list = realEstateUsedUserDao.findList(whereRealEstateUsedUser);
			if (null == list || list.isEmpty()) {
				realEstate.setUsedUserList(new ArrayList<>());
				return realEstate;
			}
            EmpUser whereUser = new EmpUser();
			whereUser.setId_in(list.stream().map(RealEstateUsedUser::getUserCode).toArray(String[]::new));
			List<EmpUser> userList = empUserService.findList(whereUser);
			realEstate.setUsedUserList(userList);
		}
		return realEstate;
	}
	
	/**
	 * 查询分页数据
	 * @param realEstate 查询条件
	 * @param realEstate page 分页对象
	 * @return
	 */
	@Override
	public Page<RealEstate> findPage(RealEstate realEstate) {
		return super.findPage(realEstate);
	}
	
	/**
	 * 查询列表数据
	 * @param realEstate
	 * @return
	 */
	@Override
	public List<RealEstate> findList(RealEstate realEstate) {
		return super.findList(realEstate);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param realEstate
	 */
	@Override
	@Transactional
	public void save(RealEstate realEstate) {
		super.save(realEstate);
		FileUploadUtils.saveFileUpload(realEstate, realEstate.getId(), "realEstate_cad_file");
		FileUploadUtils.saveFileUpload(realEstate, realEstate.getId(), "realEstate_asset_file");

		RealEstateUsedUser clearRealEstateUsedUser = new RealEstateUsedUser();
		clearRealEstateUsedUser.setRealEstateId(realEstate);
		realEstateUsedUserDao.deleteByEntity(clearRealEstateUsedUser);

		if (null == realEstate.getUsedUserList() || realEstate.getUsedUserList().isEmpty()) {
			return;
		}
		for (User user: realEstate.getUsedUserList()) {
			RealEstateUsedUser realEstateUsedUser = new RealEstateUsedUser();
			realEstateUsedUser.setRealEstateId(realEstate);
			realEstateUsedUser.setUserCode(user.getUserCode());
			realEstateUsedUserDao.insert(realEstateUsedUser);
		}
	}
	
	/**
	 * 更新状态
	 * @param realEstate
	 */
	@Override
	@Transactional
	public void updateStatus(RealEstate realEstate) {
		super.updateStatus(realEstate);
	}
	
	/**
	 * 删除数据
	 * @param realEstate
	 */
	@Override
	@Transactional
	public void delete(RealEstate realEstate) {
		super.delete(realEstate);
	}

	public void updateRealEstate(RealEstate realEstate) {
		super.update(realEstate);
	}

	public Page<RealEstateQuery> listQueryData(RealEstateQuery realEstateQuery) {
		Page<RealEstateQuery> page = (Page<RealEstateQuery>) realEstateQuery.getPage();
		List<RealEstateQuery> list = realEstateDao.listQueryData(realEstateQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<OfficeTechnicalBusinessAreaDto> getOfficeTechnicalBusinessArea(String officeName) {
		return realEstateDao.getOfficeTechnicalBusinessArea(officeName);
	}

	@Override
	public void addDataScopeFilter(RealEstate realEstate) {
//		realEstate.sqlMap().getDataScope().addFilter("sqlMap.dsfOffice", "Office", "ore.used_office_code", DataScope.CTRL_PERMI_HAVE);
		realEstate.sqlMap().getDataScope()
				.addFilter("dsf", "Office", "a.used_office_code", DataScope.CTRL_PERMI_HAVE)
				.addFilter("dsf", "Office", "a.owner_office_code", DataScope.CTRL_PERMI_HAVE);
	}

	public void addDataScopeFilter(RealEstateQuery realEstateQuery) {
		realEstateQuery.sqlMap().getDataScope().addFilter("dsfOffice", "Office", "ore.used_office_code", DataScope.CTRL_PERMI_HAVE);

	}
}