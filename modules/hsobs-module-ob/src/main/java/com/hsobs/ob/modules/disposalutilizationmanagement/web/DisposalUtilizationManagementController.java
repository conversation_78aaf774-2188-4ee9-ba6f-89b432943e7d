package com.hsobs.ob.modules.disposalutilizationmanagement.web;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.ob.modules.apply.entity.ApplyLedger;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagementImport;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagementLedger;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagementQuery;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagement;
import com.hsobs.ob.modules.disposalutilizationmanagement.service.DisposalUtilizationManagementService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 处置利用管理Controller
 * <AUTHOR>
 * @version 2024-11-26
 */
@Controller
@RequestMapping(value = "${adminPath}/disposalutilizationmanagement/")
public class DisposalUtilizationManagementController extends BaseController {

	@Autowired
	private DisposalUtilizationManagementService disposalUtilizationManagementService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DisposalUtilizationManagement get(String id, boolean isNewRecord) {
		return disposalUtilizationManagementService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = {"list", ""})
	public String list(DisposalUtilizationManagement disposalUtilizationManagement, Model model) {
		model.addAttribute("disposalUtilizationManagement", disposalUtilizationManagement);
		return "modules/disposalutilizationmanagement/disposalUtilizationManagementList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = {"listLedger", ""})
	public String listLedger(DisposalUtilizationManagementLedger disposalUtilizationManagementLedger, Model model) {
		model.addAttribute("disposalUtilizationManagementLedger", disposalUtilizationManagementLedger);
		return "modules/disposalutilizationmanagement/disposalUtilizationManagementListLedger";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = {"listQuery", ""})
	public String listQuery(DisposalUtilizationManagementQuery disposalUtilizationManagementQuery, Model model) {
		model.addAttribute("disposalUtilizationManagementQuery", disposalUtilizationManagementQuery);
		return "modules/disposalutilizationmanagement/disposalUtilizationManagementListQuery";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<DisposalUtilizationManagement> listData(DisposalUtilizationManagement disposalUtilizationManagement, HttpServletRequest request, HttpServletResponse response) {
		disposalUtilizationManagement.setPage(new Page<>(request, response));
		disposalUtilizationManagementService.addDataScopeFilter(disposalUtilizationManagement);
		Page<DisposalUtilizationManagement> page = disposalUtilizationManagementService.findPage(disposalUtilizationManagement);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "listLedgerData")
	@ResponseBody
	public Page<DisposalUtilizationManagementLedger> listLedgerData(DisposalUtilizationManagementLedger disposalUtilizationManagementLedger, HttpServletRequest request, HttpServletResponse response) {
		disposalUtilizationManagementLedger.setPage(new Page<>(request, response));
		Page<DisposalUtilizationManagementLedger> page = disposalUtilizationManagementService.listLedger(disposalUtilizationManagementLedger);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "listQueryData")
	@ResponseBody
	public Page<DisposalUtilizationManagementQuery> listQueryData(DisposalUtilizationManagementQuery disposalUtilizationManagementQuery, HttpServletRequest request, HttpServletResponse response) {
		disposalUtilizationManagementQuery.setPage(new Page<>(request, response));
		Page<DisposalUtilizationManagementQuery> page = disposalUtilizationManagementService.listQueryData(disposalUtilizationManagementQuery);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "form")
	public String form(DisposalUtilizationManagement disposalUtilizationManagement, Model model) {
		if (disposalUtilizationManagement.getIsNewRecord()) {
			User currentUser = UserUtils.getUser();
			disposalUtilizationManagement.setApplicantId(currentUser.getUserCode());
			disposalUtilizationManagement.setEmployee(EmpUtils.getEmployee());
			if (null != currentUser.getRefObj()) {
				Employee employee = currentUser.getRefObj();
				if (null != employee) {
					if (null != employee.getOffice()) {
						disposalUtilizationManagement.setApplicantId(currentUser.getUserCode());
						disposalUtilizationManagement.setApplicantName(currentUser.getUserName());
					}
				}
			}
		}
		model.addAttribute("disposalUtilizationManagement", disposalUtilizationManagement);
		return "modules/disposalutilizationmanagement/disposalUtilizationManagementForm";
	}

	/**
	 * 查看审核表单
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "auditForm")
	public String auditForm(DisposalUtilizationManagement disposalUtilizationManagement, Model model) {
		model.addAttribute("disposalUtilizationManagement", disposalUtilizationManagement);
		return "modules/disposalutilizationmanagement/disposalUtilizationManagementAuditForm";
	}

	/**
	 * 查看处置表单
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "disposalForm")
	public String disposalForm(DisposalUtilizationManagement disposalUtilizationManagement, Model model) {
		model.addAttribute("disposalUtilizationManagement", disposalUtilizationManagement);
		return "modules/disposalutilizationmanagement/disposalUtilizationManagementDisposalForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("disposalutilizationmanagement::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated DisposalUtilizationManagement disposalUtilizationManagement, HttpServletRequest request) {
		Map<String, String[]> parameterMap = request.getParameterMap();
		disposalUtilizationManagementService.save(disposalUtilizationManagement);
		return renderResult(Global.TRUE, text("保存处置利用管理成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("disposalutilizationmanagement::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(DisposalUtilizationManagement disposalUtilizationManagement) {
		if (!DisposalUtilizationManagement.STATUS_DRAFT.equals(disposalUtilizationManagement.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		disposalUtilizationManagementService.delete(disposalUtilizationManagement);
		return renderResult(Global.TRUE, text("删除处置利用管理成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "exportData")
	public void exportData(DisposalUtilizationManagement disposalUtilizationManagement, HttpServletResponse response) {
		List<DisposalUtilizationManagement> list = disposalUtilizationManagementService.findList(disposalUtilizationManagement);
		String fileName = "处置利用管理" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("处置利用管理", DisposalUtilizationManagement.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		DisposalUtilizationManagementImport disposalUtilizationManagement = new DisposalUtilizationManagementImport();
		List<DisposalUtilizationManagementImport> list = ListUtils.newArrayList(disposalUtilizationManagement);
		String fileName = "处置利用管理模板.xlsx";
		try(ExcelExport ee = new ExcelExport("处置利用管理", DisposalUtilizationManagementImport.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("disposalutilizationmanagement::edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = disposalUtilizationManagementService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "exportLedgerData")
	public void exportLedgerData(DisposalUtilizationManagementLedger disposalUtilizationManagementLedger, HttpServletResponse response) {
		List<DisposalUtilizationManagementLedger> list = disposalUtilizationManagementService.listLedgerData(disposalUtilizationManagementLedger);
		String fileName = "处置利用台账" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("处置利用台账", DisposalUtilizationManagementLedger.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("disposalutilizationmanagement::view")
	@RequestMapping(value = "exportQueryData")
	public void exportQueryData(DisposalUtilizationManagementQuery disposalUtilizationManagementQuery, HttpServletResponse response) {
		List<DisposalUtilizationManagementQuery> list = disposalUtilizationManagementService.exportQueryData(disposalUtilizationManagementQuery);
		String fileName = "处置利用查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("处置利用查询", DisposalUtilizationManagementQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}