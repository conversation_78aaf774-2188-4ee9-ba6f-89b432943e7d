package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * <AUTHOR>
 * @title: ResourceCase
 * @projectName base
 * @description: 省直单位办公业务用房面积核定表(表二)
 * @date 2024/12/1919:15
 */
public class SpaceVerification extends DataEntity<SpaceVerification> {
    private String officeName;
    private String officeType;
    private Integer staffingTotalNumber;
    private float officeSpace;
    private float serviceSpace;
    private float deviceSpace;
    private float baseTotalSpace;
    private float structureTotalSpace;
    private float auxiliarySpace;
    private float officeNowSpace;
    private float technologySpace;
    private float businessSpace;

    public float getBusinessSpace() {
        return businessSpace;
    }

    public void setBusinessSpace(float businessSpace) {
        this.businessSpace = businessSpace;
    }

    private float totalSpace;
    private float officeSiteSpace;
    private float businessSiteSpace;
    private float totalSiteSpace;
    private float overproofOfficeSpace;
    private float overproofBusinessSpace;
    private float overproofTotalSpace;
    private String remark;
    private String officeCode;

    @ExcelFields({
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="单位名称", attrName="officeName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=10),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="单位性质", attrName="officeType", dictType="sys_office_type", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=20),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="编制人数", attrName="staffingTotalNumber", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=30),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="办公室", attrName="officeSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=40),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="服务用房", attrName="serviceSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=50),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="设备用房", attrName="deviceSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=60),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="小计", attrName="baseTotalSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=70),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="建筑面积", attrName="structureTotalSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=80),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="业务用房", attrName="businessSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=90),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="合计", attrName="totalSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=100),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="基本办公用房", attrName="officeSiteSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=150),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="业务用房", attrName="businessSiteSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=160),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="合计", attrName="totalSiteSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=160),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="超标面积", attrName="overproofTotalSpace", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=160),
            @com.jeesite.common.utils.excel.annotation.ExcelField(title="备注", attrName="remarks", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=160),
    })
    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    public SpaceVerification() {

    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getOfficeType() {
        return officeType;
    }

    public void setOfficeType(String officeType) {
        this.officeType = officeType;
    }

    public Integer getStaffingTotalNumber() {
        return staffingTotalNumber;
    }

    public void setStaffingTotalNumber(Integer staffingTotalNumber) {
        this.staffingTotalNumber = staffingTotalNumber;
    }

    public float getOfficeSpace() {
        return officeSpace;
    }

    public void setOfficeSpace(float officeSpace) {
        this.officeSpace = officeSpace;
    }

    public float getServiceSpace() {
        return serviceSpace;
    }

    public void setServiceSpace(float serviceSpace) {
        this.serviceSpace = serviceSpace;
    }

    public float getDeviceSpace() {
        return deviceSpace;
    }

    public void setDeviceSpace(float deviceSpace) {
        this.deviceSpace = deviceSpace;
    }

    public float getBaseTotalSpace() {
        return baseTotalSpace;
    }

    public void setBaseTotalSpace(float baseTotalSpace) {
        this.baseTotalSpace = baseTotalSpace;
    }

    public float getStructureTotalSpace() {
        return structureTotalSpace;
    }

    public void setStructureTotalSpace(float structureTotalSpace) {
        this.structureTotalSpace = structureTotalSpace;
    }

    public float getAuxiliarySpace() {
        return auxiliarySpace;
    }

    public void setAuxiliarySpace(float auxiliarySpace) {
        this.auxiliarySpace = auxiliarySpace;
    }

    public float getOfficeNowSpace() {
        return officeNowSpace;
    }

    public void setOfficeNowSpace(float officeNowSpace) {
        this.officeNowSpace = officeNowSpace;
    }

    public float getTechnologySpace() {
        return technologySpace;
    }

    public void setTechnologySpace(float technologySpace) {
        this.technologySpace = technologySpace;
    }

    public float getTotalSpace() {
        return totalSpace;
    }

    public void setTotalSpace(float totalSpace) {
        this.totalSpace = totalSpace;
    }

    public float getOfficeSiteSpace() {
        return officeSiteSpace;
    }

    public void setOfficeSiteSpace(float officeSiteSpace) {
        this.officeSiteSpace = officeSiteSpace;
    }

    public float getBusinessSiteSpace() {
        return businessSiteSpace;
    }

    public void setBusinessSiteSpace(float businessSiteSpace) {
        this.businessSiteSpace = businessSiteSpace;
    }

    public float getTotalSiteSpace() {
        return totalSiteSpace;
    }

    public void setTotalSiteSpace(float totalSiteSpace) {
        this.totalSiteSpace = totalSiteSpace;
    }

    public float getOverproofOfficeSpace() {
        return overproofOfficeSpace;
    }

    public void setOverproofOfficeSpace(float overproofOfficeSpace) {
        this.overproofOfficeSpace = overproofOfficeSpace;
    }

    public float getOverproofBusinessSpace() {
        return overproofBusinessSpace;
    }

    public void setOverproofBusinessSpace(float overproofBusinessSpace) {
        this.overproofBusinessSpace = overproofBusinessSpace;
    }

    public float getOverproofTotalSpace() {
        return overproofTotalSpace;
    }

    public void setOverproofTotalSpace(float overproofTotalSpace) {
        this.overproofTotalSpace = overproofTotalSpace;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
