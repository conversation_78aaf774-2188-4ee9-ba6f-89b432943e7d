package com.hsobs.ob.modules.apply.entity;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.hsobs.ob.modules.estate.entity.RealEstateAddressFloor;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;

import javax.validation.constraints.Size;

/**
 * 配置管理管理不动产Entity
 * <AUTHOR>
 * @version 2025-02-16
 */
@Table(name="ob_apply_real_estate", alias="a", label="配置管理管理不动产信息", columns={
        @Column(name="id", attrName="id", label="id", isPK=true),
        @Column(name="apply_id", attrName="apply.id", label="apply_id"),
        @Column(name="real_estate_id", attrName="realEstateId", label="real_estate_id"),
        @Column(name="create_by", attrName="createBy", label="create_by", isUpdate=false, isQuery=false),
        @Column(name="create_date", attrName="createDate", label="create_date", isUpdate=false, isQuery=false),
        @Column(name="update_by", attrName="updateBy", label="update_by", isQuery=false),
        @Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
}, joinTable={
        @JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstate.class, alias="re",
                on="re.id = a.real_estate_id",
                columns={@Column(includeEntity = RealEstate.class)}),
        @JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddress.class, alias="rea1",
                on="rea1.id = a.real_estate_id",
                columns={@Column(includeEntity = RealEstateAddress.class)}),
        @JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddress.class, alias="rea",
                on="rea.id = re.real_estate_address_id",
                columns={@Column(includeEntity = RealEstateAddress.class)}),
        @JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddressFloor.class, alias="reaf",
                on="reaf.id = re.real_estate_address_floor_id",
                columns={@Column(includeEntity = RealEstateAddressFloor.class)}),
}, orderBy="a.create_date ASC"
)
public class ApplyRealEstate extends DataEntity<ApplyRealEstate> {

    private static final long serialVersionUID = 1L;
    private Apply apply;		// ownership_id 父类
    private String realEstateId;		// real_estate_id

    private RealEstate realEstate;
    private RealEstateAddress realEstateAddress;
    private RealEstateAddressFloor realEstateAddressFloor;


    public ApplyRealEstate() {
        this(null);
    }

    public ApplyRealEstate(Apply apply){
        this.apply = apply;
    }

    public Apply getApply() {
        return apply;
    }

    public void setApply(Apply apply) {
        this.apply = apply;
    }

    @Size(min=0, max=64, message="real_estate_id长度不能超过 64 个字符")
    public String getRealEstateId() {
        return realEstateId;
    }

    public void setRealEstateId(String realEstateId) {
        this.realEstateId = realEstateId;
    }

    public RealEstate getRealEstate() {
        return realEstate;
    }

    public void setRealEstate(RealEstate realEstate) {
        this.realEstate = realEstate;
    }

    public RealEstateAddress getRealEstateAddress() {
        return realEstateAddress;
    }

    public void setRealEstateAddress(RealEstateAddress realEstateAddress) {
        this.realEstateAddress = realEstateAddress;
    }

    public RealEstateAddressFloor getRealEstateAddressFloor() {
        return realEstateAddressFloor;
    }

    public void setRealEstateAddressFloor(RealEstateAddressFloor realEstateAddressFloor) {
        this.realEstateAddressFloor = realEstateAddressFloor;
    }
}
