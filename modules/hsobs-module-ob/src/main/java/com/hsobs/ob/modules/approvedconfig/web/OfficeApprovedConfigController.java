package com.hsobs.ob.modules.approvedconfig.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.ob.modules.approvedconfig.entity.OfficeApprovedConfigForm;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.approvedconfig.entity.OfficeApprovedConfig;
import com.hsobs.ob.modules.approvedconfig.service.OfficeApprovedConfigService;

/**
 * 办公室使用面积核定配置Controller
 * <AUTHOR>
 * @version 2025-03-09
 */
@Controller
@RequestMapping(value = "${adminPath}/approvedconfig/officeApprovedConfig")
public class OfficeApprovedConfigController extends BaseController {

	@Autowired
	private OfficeApprovedConfigService officeApprovedConfigService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public OfficeApprovedConfig get(String id, boolean isNewRecord) {
		return officeApprovedConfigService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("approvedconfig:officeApprovedConfig:view")
	@RequestMapping(value = {"list", ""})
	public String list(OfficeApprovedConfig officeApprovedConfig, Model model) {
		model.addAttribute("officeApprovedConfig", officeApprovedConfig);
		return "modules/approvedconfig/officeApprovedConfigList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("approvedconfig:officeApprovedConfig:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public List<OfficeApprovedConfig> listData(OfficeApprovedConfig officeApprovedConfig, HttpServletRequest request, HttpServletResponse response) {
		officeApprovedConfig.sqlMap().getOrder().setOrderBy("CAST(a.id AS INT) asc");
		List<OfficeApprovedConfig> officeApprovedConfigList = officeApprovedConfigService.findList(officeApprovedConfig);
		if (null == officeApprovedConfigList || officeApprovedConfigList.isEmpty()) {
			officeApprovedConfigService.initSave();
		}
		officeApprovedConfigList = officeApprovedConfigService.findList(officeApprovedConfig);
		return officeApprovedConfigList;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("approvedconfig:officeApprovedConfig:view")
	@RequestMapping(value = "form")
	public String form(OfficeApprovedConfig officeApprovedConfig, Model model) {
		model.addAttribute("officeApprovedConfig", officeApprovedConfig);
		officeApprovedConfig.sqlMap().getOrder().setOrderBy("a.update_date asc");
		List<OfficeApprovedConfig> officeApprovedConfigList = officeApprovedConfigService.findList(officeApprovedConfig);
		if (null == officeApprovedConfigList || officeApprovedConfigList.isEmpty()) {
			officeApprovedConfigService.initSave();
		}
		officeApprovedConfigList = officeApprovedConfigService.findList(officeApprovedConfig);
		model.addAttribute("officeApprovedConfigList", officeApprovedConfigList);
		return "modules/approvedconfig/officeApprovedConfigForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("approvedconfig:officeApprovedConfig:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated @ModelAttribute("form") OfficeApprovedConfigForm form) {
		officeApprovedConfigService.saveAll(form.getOfficeApprovedConfigList());
		return renderResult(Global.TRUE, text("保存办公室使用面积核定配置成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("approvedconfig:officeApprovedConfig:view")
	@RequestMapping(value = "exportData")
	public void exportData(OfficeApprovedConfig officeApprovedConfig, HttpServletResponse response) {
		List<OfficeApprovedConfig> list = officeApprovedConfigService.findList(officeApprovedConfig);
		String fileName = "办公室使用面积核定配置" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("办公室使用面积核定配置", OfficeApprovedConfig.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("approvedconfig:officeApprovedConfig:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		OfficeApprovedConfig officeApprovedConfig = new OfficeApprovedConfig();
		List<OfficeApprovedConfig> list = ListUtils.newArrayList(officeApprovedConfig);
		String fileName = "办公室使用面积核定配置模板.xlsx";
		try(ExcelExport ee = new ExcelExport("办公室使用面积核定配置", OfficeApprovedConfig.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("approvedconfig:officeApprovedConfig:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = officeApprovedConfigService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("approvedconfig:officeApprovedConfig:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(OfficeApprovedConfig officeApprovedConfig) {
		officeApprovedConfigService.delete(officeApprovedConfig);
		return renderResult(Global.TRUE, text("删除办公室使用面积核定配置成功！"));
	}
	
}