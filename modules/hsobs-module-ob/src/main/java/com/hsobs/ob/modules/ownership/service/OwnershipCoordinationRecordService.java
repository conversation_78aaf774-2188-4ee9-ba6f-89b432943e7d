package com.hsobs.ob.modules.ownership.service;

import java.util.List;

import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.ownership.entity.OwnershipCoordinationRecord;
import com.hsobs.ob.modules.ownership.dao.OwnershipCoordinationRecordDao;

/**
 * 权属登记协调记录Service
 * <AUTHOR>
 * @version 2025-03-09
 */
@Service
public class OwnershipCoordinationRecordService extends CrudService<OwnershipCoordinationRecordDao, OwnershipCoordinationRecord> {
	
	/**
	 * 获取单条数据
	 * @param ownershipCoordinationRecord
	 * @return
	 */
	@Override
	public OwnershipCoordinationRecord get(OwnershipCoordinationRecord ownershipCoordinationRecord) {
		return super.get(ownershipCoordinationRecord);
	}
	
	/**
	 * 查询分页数据
	 * @param ownershipCoordinationRecord 查询条件
	 * @param ownershipCoordinationRecord page 分页对象
	 * @return
	 */
	@Override
	public Page<OwnershipCoordinationRecord> findPage(OwnershipCoordinationRecord ownershipCoordinationRecord) {
		return super.findPage(ownershipCoordinationRecord);
	}
	
	/**
	 * 查询列表数据
	 * @param ownershipCoordinationRecord
	 * @return
	 */
	@Override
	public List<OwnershipCoordinationRecord> findList(OwnershipCoordinationRecord ownershipCoordinationRecord) {
		return super.findList(ownershipCoordinationRecord);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param ownershipCoordinationRecord
	 */
	@Override
	@Transactional
	public void save(OwnershipCoordinationRecord ownershipCoordinationRecord) {
		super.save(ownershipCoordinationRecord);

		// 保存上传附件
		FileUploadUtils.saveFileUpload(ownershipCoordinationRecord, ownershipCoordinationRecord.getId(), "ownershipCoordinationRecord_file");
	}
	
	/**
	 * 更新状态
	 * @param ownershipCoordinationRecord
	 */
	@Override
	@Transactional
	public void updateStatus(OwnershipCoordinationRecord ownershipCoordinationRecord) {
		super.updateStatus(ownershipCoordinationRecord);
	}
	
	/**
	 * 删除数据
	 * @param ownershipCoordinationRecord
	 */
	@Override
	@Transactional
	public void delete(OwnershipCoordinationRecord ownershipCoordinationRecord) {
		super.delete(ownershipCoordinationRecord);
	}
	
}