package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;

/**
 * 办公用房权属情况数据统计Entity
 * <AUTHOR>
 * @version 2024-12-24
 */
public class DataStatisticsForMaintain extends DataEntity<DataStatisticsForMaintain> {

	private static final long serialVersionUID = 1L;
	private String sysCode;
	private MaintainTable maintainTable;

	public DataStatisticsForMaintain() {
		this(null);
	}

	public DataStatisticsForMaintain(String id){
		super(id);
	}

	public String getSysCode() {
		return sysCode;
	}

	public void setSysCode(String sysCode) {
		this.sysCode = sysCode;
	}

	public MaintainTable getMaintainTable() {
		return maintainTable;
	}

	public void setMaintainTable(MaintainTable maintainTable) {
		this.maintainTable = maintainTable;
	}
}