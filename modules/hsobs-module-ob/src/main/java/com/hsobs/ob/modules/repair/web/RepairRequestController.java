package com.hsobs.ob.modules.repair.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.ob.modules.repair.entity.RepairRequestLedger;
import com.hsobs.ob.modules.repair.entity.RepairRequestQuery;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.repair.entity.RepairRequest;
import com.hsobs.ob.modules.repair.service.RepairRequestService;

/**
 * 维修申请Controller
 * <AUTHOR>
 * @version 2025-02-06
 */
@Controller
@RequestMapping(value = "${adminPath}/repair/request")
public class RepairRequestController extends BaseController {

	@Autowired
	private RepairRequestService repairRequestService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public RepairRequest get(String id, boolean isNewRecord) {
		return repairRequestService.get(id, isNewRecord);
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = {"list", ""})
	public String list(RepairRequest repairRequest, Model model) {
		model.addAttribute("repairRequest", repairRequest);
		return "modules/repair/repairRequestList";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = {"listLedger", ""})
	public String listLedger(RepairRequestLedger repairRequestLedger, Model model) {
		model.addAttribute("repairRequestLedger", repairRequestLedger);
		return "modules/repair/repairRequestListLedger";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = {"repairRequestQuery", ""})
	public String repairRequestQuery(RepairRequestQuery repairRequestQuery, Model model) {
		model.addAttribute("repairRequestQuery", repairRequestQuery);
		return "modules/repair/repairRequestListQuery";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "listLedgerData")
	@ResponseBody
	public Page<RepairRequestLedger> listLedgerData(RepairRequestLedger repairRequestLedger, HttpServletRequest request, HttpServletResponse response) {
		repairRequestLedger.setPage(new Page<>(request, response));
		Page<RepairRequestLedger> page = repairRequestService.listLedgerData(repairRequestLedger);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "listQueryData")
	@ResponseBody
	public Page<RepairRequestQuery> listQueryData(RepairRequestQuery repairRequestQuery, HttpServletRequest request, HttpServletResponse response) {
		repairRequestQuery.setPage(new Page<>(request, response));
		Page<RepairRequestQuery> page = repairRequestService.listQueryData(repairRequestQuery);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<RepairRequest> listData(RepairRequest repairRequest, HttpServletRequest request, HttpServletResponse response) {
		repairRequest.setPage(new Page<>(request, response));
		Page<RepairRequest> page = repairRequestService.findPage(repairRequest);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "form")
	public String form(RepairRequest repairRequest, Model model) {
		model.addAttribute("repairRequest", repairRequest);
		boolean commonReadonly= null != repairRequest.getStatus() && !repairRequest.getStatus().equals("9");
		model.addAttribute("commonReadonly", commonReadonly);
		return "modules/repair/repairRequestForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("repair:request:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated RepairRequest repairRequest) {
		repairRequestService.save(repairRequest);
		return renderResult(Global.TRUE, text("保存维修申请成功！"));
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("repair:request:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(RepairRequest repairRequest) {
		if (!RepairRequest.STATUS_DRAFT.equals(repairRequest.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		repairRequestService.delete(repairRequest);
		return renderResult(Global.TRUE, text("删除维修申请成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportData")
	public void exportData(RepairRequestLedger repairRequestLedger, HttpServletResponse response) {
		List<RepairRequestLedger> list = repairRequestService.listLedger(repairRequestLedger);
		String fileName = "维修管理台账" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("维修管理台账", RepairRequestLedger.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportQueryData")
	public void exportQueryData(RepairRequestQuery repairRequestQuery, HttpServletResponse response) {
		List<RepairRequestQuery> list = repairRequestService.listQuery(repairRequestQuery);
		String fileName = "维修项目查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("维修项目查询", RepairRequestQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}


	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "repairRequestSelect")
	public String repairRequestSelect(RepairRequest repairRequest, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("repairRequest", repairRequest);
		return "modules/repair/repairRequestSelect";
	}


	/**
	 * 导出数据
	 */
	@RequiresPermissions("repair:request:view")
	@RequestMapping(value = "exportListData")
	public void exportData(RepairRequest repairRequest, HttpServletResponse response) {
		List<RepairRequest> list = repairRequestService.findList(repairRequest);
		String fileName = "维修申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("维修申请", RepairRequest.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

}