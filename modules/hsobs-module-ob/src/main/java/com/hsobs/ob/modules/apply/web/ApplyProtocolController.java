package com.hsobs.ob.modules.apply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.apply.entity.ApplyProtocol;
import com.hsobs.ob.modules.apply.service.ApplyProtocolService;

/**
 * 有偿使用协议表Controller
 * <AUTHOR>
 * @version 2025-03-13
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/protocol")
public class ApplyProtocolController extends BaseController {

	@Autowired
	private ApplyProtocolService applyProtocolService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ApplyProtocol get(String id, boolean isNewRecord) {
		return applyProtocolService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("apply:protocol:view")
	@RequestMapping(value = {"list", ""})
	public String list(ApplyProtocol applyProtocol, Model model) {
		model.addAttribute("applyProtocol", applyProtocol);
		return "modules/apply/applyProtocolList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("apply:protocol:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ApplyProtocol> listData(ApplyProtocol applyProtocol, HttpServletRequest request, HttpServletResponse response) {
		applyProtocol.setPage(new Page<>(request, response));
		if (null != applyProtocol.getLeaseBeginDate()) {
			applyProtocol.sqlMap().getWhere().and("lease_begin_date", QueryType.GTE, applyProtocol.getLeaseBeginDate());
		}
		if (null != applyProtocol.getLeaseEndDate()) {
			applyProtocol.sqlMap().getWhere().and("lease_end_date", QueryType.LTE, applyProtocol.getLeaseEndDate());
		}
		Page<ApplyProtocol> page = applyProtocolService.findPage(applyProtocol);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("apply:protocol:view")
	@RequestMapping(value = "form")
	public String form(ApplyProtocol applyProtocol, Model model) {
		model.addAttribute("applyProtocol", applyProtocol);
		return "modules/apply/applyProtocolForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("apply:protocol:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ApplyProtocol applyProtocol) {
		applyProtocolService.save(applyProtocol);
		return renderResult(Global.TRUE, text("保存有偿使用协议成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("apply:protocol:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ApplyProtocol applyProtocol) {
		applyProtocolService.delete(applyProtocol);
		return renderResult(Global.TRUE, text("删除有偿使用协议成功！"));
	}
	
}