package com.hsobs.ob.modules.supervisehandlingtasks.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.UserRole;
import io.swagger.annotations.ApiModelProperty;

/**
 * 监督督办任务Entity
 * <AUTHOR>
 * @version 2024-12-10
 */
@Table(name="ob_supervise_handling_tasks", alias="a", label="监督督办任务信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="task_source", attrName="taskSource", label="任务来源"),
		@Column(name="task_name", attrName="taskName", label="任务名称", queryType=QueryType.LIKE),
		@Column(name="task_status", attrName="taskStatus", label="任务状态"),
		@Column(name="occupancy_classification", attrName="occupancyClassification", label="用房分类"),
		@Column(name="task_details", attrName="taskDetails", label="任务详情"),
		@Column(name="office_code", attrName="officeCode", label="单位"),
		@Column(name="task_date", attrName="taskDate", label="填报日期"),
		@Column(name="check_date", attrName="checkDate", label="检查日期", isUpdateForce=true),
		@Column(name="inspection_record", attrName="inspectionRecord", label="巡检记录"),
		@Column(name="verify_the_situation", attrName="verifyTheSituation", label="核实情况"),
		@Column(name="fix_deadline_days", attrName="fixDeadlineDays", label="整改期限"),
		@Column(name="fix_date", attrName="fixDate", label="整改日期"),
		@Column(name="rectification_matters", attrName="rectificationMatters", label="督办整改事项"),
		@Column(name="rectification_result_feedback", attrName="rectificationResultFeedback", label="整改结果反馈"),
		@Column(name="inquirer", attrName="inquirer", label="调查人"),
		@Column(name="inquirer_phone", attrName="inquirerPhone", label="调查人手机号"),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "oi",
				on="oi.office_code = a.office_code", attrName="office",
				columns = {@Column(includeEntity = Office.class)})
	},
	extWhereKeys="dsfOffice",
	orderBy="a.update_date DESC"
)
public class SuperviseHandlingTasks extends BpmEntity<SuperviseHandlingTasks> {
	
	private static final long serialVersionUID = 1L;
	private String taskSource;		// 任务来源
	private String taskName;		// 任务名称
	private String taskStatus;		// 任务状态
	private String taskDetails;		// 任务详情
	private String officeCode;		//单位
	private Date taskDate;		// 填报日期
	private Date checkDate;		// 检查日期
	private Date fixDeadlineDays;		// 整改期限
	private Date fixDate;		// 整改日期
	private String inspectionRecord;		// 巡检记录
	private String verifyTheSituation;		// 核实情况
	private String rectificationMatters;		// 督办整改事项
	private String rectificationResultFeedback;		// 整改结果反馈

	private Office office; // 机构信息
	private String occupancyClassification;		// 用房分类
	private String inquirer;		// 调查人
	private String inquirerPhone;		// 调查电话
	private String codesString;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getFixDeadlineDays() {
		return fixDeadlineDays;
	}

	public void setFixDeadlineDays(Date fixDeadlineDays) {
		this.fixDeadlineDays = fixDeadlineDays;
	}
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getFixDate() {
		return fixDate;
	}

	public void setFixDate(Date fixDate) {
		this.fixDate = fixDate;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	@NotBlank(message="用房分类不能为空")
	@Size(min=0, max=1, message="用房分类长度不能超过 1 个字符")
	public String getOccupancyClassification() {
		return occupancyClassification;
	}

	public void setOccupancyClassification(String occupancyClassification) {
		this.occupancyClassification = occupancyClassification;
	}


	@ExcelFields({
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="编号", attrName="id", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=10),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="任务来源", attrName="taskSource", dictType="supervise_handling_task_source", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=20),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="任务名称", attrName="taskName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=30),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="任务状态", attrName="taskStatus", dictType="supervise_handling_task_status", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=40),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="任务详情", attrName="taskDetails", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=50),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="单位", attrName="officeCode", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=51),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="填报日期", attrName="taskDate", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=60, dataFormat="yyyy-MM-dd hh:mm"),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="检查日期", attrName="checkDate", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=70, dataFormat="yyyy-MM-dd hh:mm"),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="巡检记录", attrName="inspectionRecord", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=80),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="核实情况", attrName="verifyTheSituation", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=90),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="督办整改事项", attrName="rectificationMatters", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=100),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="整改结果反馈", attrName="rectificationResultFeedback", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=110),
	})
	public SuperviseHandlingTasks() {
		this(null);
	}
	
	public SuperviseHandlingTasks(String id){
		super(id);
	}
	
	@NotBlank(message="任务来源不能为空")
	@Size(min=0, max=1, message="任务来源长度不能超过 1 个字符")
	public String getTaskSource() {
		return taskSource;
	}

	public void setTaskSource(String taskSource) {
		this.taskSource = taskSource;
	}
	
	@NotBlank(message="任务名称不能为空")
	@Size(min=0, max=256, message="任务名称长度不能超过 256 个字符")
	public String getTaskName() {
		return taskName;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}
	
	@NotBlank(message="任务状态不能为空")
	@Size(min=0, max=1, message="任务状态长度不能超过 1 个字符")
	public String getTaskStatus() {
		return taskStatus;
	}

	public void setTaskStatus(String taskStatus) {
		this.taskStatus = taskStatus;
	}
	
	@NotBlank(message="任务详情不能为空")
	@Size(min=0, max=256, message="任务详情长度不能超过 256 个字符")
	public String getTaskDetails() {
		return taskDetails;
	}

	public void setTaskDetails(String taskDetails) {
		this.taskDetails = taskDetails;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@NotNull(message="填报日期不能为空")
	public Date getTaskDate() {
		return taskDate;
	}

	public void setTaskDate(Date taskDate) {
		this.taskDate = taskDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}
	
	@Size(min=0, max=256, message="巡检记录长度不能超过 256 个字符")
	public String getInspectionRecord() {
		return inspectionRecord;
	}

	public void setInspectionRecord(String inspectionRecord) {
		this.inspectionRecord = inspectionRecord;
	}
	
	@Size(min=0, max=256, message="核实情况长度不能超过 256 个字符")
	public String getVerifyTheSituation() {
		return verifyTheSituation;
	}

	public void setVerifyTheSituation(String verifyTheSituation) {
		this.verifyTheSituation = verifyTheSituation;
	}
	
	@Size(min=0, max=256, message="督办整改事项长度不能超过 256 个字符")
	public String getRectificationMatters() {
		return rectificationMatters;
	}

	public void setRectificationMatters(String rectificationMatters) {
		this.rectificationMatters = rectificationMatters;
	}
	
	@Size(min=0, max=256, message="整改结果反馈长度不能超过 256 个字符")
	public String getRectificationResultFeedback() {
		return rectificationResultFeedback;
	}

	@NotBlank(message="机构编码不能为空")
	@Size(min=0, max=64, message="机构编码长度不能超过 64 个字符")
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public void setRectificationResultFeedback(String rectificationResultFeedback) {
		this.rectificationResultFeedback = rectificationResultFeedback;
	}
	@Size(min=0, max=64, message="调查人长度不能超过 64 个字符")
	public String getInquirer() {
		return inquirer;
	}

	public void setInquirer(String inquirer) {
		this.inquirer = inquirer;
	}

	@Size(min=0, max=64, message="调查人手机号长度不能超过 64 个字符")
	public String getInquirerPhone() {
		return inquirerPhone;
	}

	public void setInquirerPhone(String inquirerPhone) {
		this.inquirerPhone = inquirerPhone;
	}

	public String getCodesString() {
		return codesString;
	}

	public void setCodesString(String codesString) {
		this.codesString = codesString;
	}

	public void setId_in(String[] ids){
		sqlMap.getWhere().and("id", QueryType.IN, ids);
	}
}