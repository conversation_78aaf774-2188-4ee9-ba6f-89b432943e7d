package com.hsobs.ob.modules.disposalutilizationmanagement.dao;

import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagementLedger;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagementQuery;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagement;

import java.util.List;

/**
 * 处置利用管理DAO接口
 * <AUTHOR>
 * @version 2024-11-26
 */
@MyBatisDao
public interface DisposalUtilizationManagementDao extends CrudDao<DisposalUtilizationManagement> {

    List<DisposalUtilizationManagementLedger> listLedgerData(DisposalUtilizationManagementLedger disposalUtilizationManagementLedger);

    List<DisposalUtilizationManagementQuery> listQueryData(DisposalUtilizationManagementQuery disposalUtilizationManagementQuery);
}