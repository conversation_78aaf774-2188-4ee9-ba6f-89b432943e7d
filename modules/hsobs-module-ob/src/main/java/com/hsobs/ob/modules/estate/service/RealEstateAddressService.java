package com.hsobs.ob.modules.estate.service;

import java.util.ArrayList;
import java.util.List;

import com.hsobs.ob.modules.estate.dao.RealEstateAddressFloorDao;
import com.hsobs.ob.modules.estate.dao.RealEstateDao;
import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddressFloor;
import com.hsobs.ob.modules.estate.entity.RealEstateFloorAndRoomDto;
import com.jeesite.common.entity.DataScope;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.hsobs.ob.modules.estate.dao.RealEstateAddressDao;

/**
 * 不动产地址表Service
 * <AUTHOR>
 * @version 2024-12-05
 */
@Service
public class RealEstateAddressService extends CrudService<RealEstateAddressDao, RealEstateAddress> {

	@Autowired
	RealEstateAddressFloorDao realEstateAddressFloorDao;

	@Autowired
	RealEstateDao realEstateDao;

	/**
	 * 获取单条数据
	 * @param realEstateAddress
	 * @return
	 */
	@Override
	public RealEstateAddress get(RealEstateAddress realEstateAddress) {
		RealEstateAddress entity = super.get(realEstateAddress);
		if (null != entity){
			RealEstateAddressFloor realEstateAddressFloor = new RealEstateAddressFloor(entity);
			entity.setRealEstateAddressFloorList(realEstateAddressFloorDao.findList(realEstateAddressFloor));
		}
		return entity;
	}
	
	/**
	 * 查询分页数据
	 * @param realEstateAddress 查询条件
	 * @param realEstateAddress page 分页对象
	 * @return
	 */
	@Override
	public Page<RealEstateAddress> findPage(RealEstateAddress realEstateAddress) {
		return super.findPage(realEstateAddress);
	}
	
	/**
	 * 查询列表数据
	 * @param realEstateAddress
	 * @return
	 */
	@Override
	public List<RealEstateAddress> findList(RealEstateAddress realEstateAddress) {
		return super.findList(realEstateAddress);
	}

	public List<RealEstateFloorAndRoomDto> getFloorAndRoomDto(RealEstateAddress realEstateAddress) {
		List<RealEstateFloorAndRoomDto> result = new ArrayList<>();
		RealEstateAddressFloor whereRealEstateAddressFloor = new RealEstateAddressFloor();
		whereRealEstateAddressFloor.setRealEstateAddressId(realEstateAddress);
		List<RealEstateAddressFloor> realEstateAddressFloorDaoList = realEstateAddressFloorDao.findList(whereRealEstateAddressFloor);
		if (realEstateAddressFloorDaoList.isEmpty()) {
			return result;
		}
		realEstateAddressFloorDaoList.forEach(realEstateAddressFloorDao -> {
			RealEstateFloorAndRoomDto realEstateFloorAndRoomDto = new RealEstateFloorAndRoomDto();
			realEstateFloorAndRoomDto.setRealEstateAddressFloor(realEstateAddressFloorDao);
			RealEstate whereRealEstate = new RealEstate();
			whereRealEstate.setRealEstateAddressFloorId(realEstateAddressFloorDao.getId());
//			whereRealEstate.sqlMap().getWhere().and("real_estate_address_floor_id", QueryType.EQ, whereRealEstateAddressFloor.getId());
			List<RealEstate> list = realEstateDao.findList(whereRealEstate);
			realEstateFloorAndRoomDto.setRealEstateList(list);
			result.add(realEstateFloorAndRoomDto);
		});

		return result;
	}

	/**
	 * 加载子表数据
	 */
	public RealEstateAddress loadChildData(RealEstateAddress realEstateAddress) {
		if (realEstateAddress != null && !realEstateAddress.getIsNewRecord()){
			RealEstateAddressFloor realEstateAddressFloor = new RealEstateAddressFloor(realEstateAddress);
			realEstateAddress.setRealEstateAddressFloorList(realEstateAddressFloorDao.findList(realEstateAddressFloor));
		}
		return realEstateAddress;
	}

	public List<RealEstateAddressFloor> findRealEstateAddressFloorList(RealEstateAddress realEstateAddress) {
		RealEstateAddressFloor realEstateAddressFloor = new RealEstateAddressFloor();
		realEstateAddressFloor.setRealEstateAddressId(realEstateAddress);
		return realEstateAddressFloorDao.findList(realEstateAddressFloor);
	}

	public Long getRoomCountByAddress(String addressId){
		if (null == addressId || addressId.isEmpty()){
			return 0L;
		}
		RealEstate realEstate = new RealEstate();
		realEstate.setRealEstateAddressId(addressId);

		return realEstateDao.findCount(realEstate);
	}

	/**
	 * 保存数据（插入或更新）
	 * @param realEstateAddress
	 */
	@Override
	@Transactional
	public void save(RealEstateAddress realEstateAddress) {
		super.save(realEstateAddress);
		FileUploadUtils.saveFileUpload(realEstateAddress, realEstateAddress.getId(), "realEstateAddress_file");
		FileUploadUtils.saveFileUpload(realEstateAddress, realEstateAddress.getId(), "realEstateAddress_exterior_file");

		RealEstateAddressFloor clearRealEstateAddressFloor = new RealEstateAddressFloor();
		clearRealEstateAddressFloor.setRealEstateAddressId(realEstateAddress);
		realEstateAddressFloorDao.deleteByEntity(clearRealEstateAddressFloor);

		int index = 0;
		for (RealEstateAddressFloor realEstateAddressFloor: realEstateAddress.getRealEstateAddressFloorList()){
			realEstateAddressFloor.setRealEstateAddressId(realEstateAddress);
			realEstateAddressFloorDao.insert(realEstateAddressFloor);
			// 保存上传附件
			FileUploadUtils.saveFileUpload(realEstateAddressFloor, realEstateAddressFloor.getId(),
					"realEstateAddressFloorList["+index+"].realEstateAddressFloor_file");
			index++;
		}
	}
	
	/**
	 * 更新状态
	 * @param realEstateAddress
	 */
	@Override
	@Transactional
	public void updateStatus(RealEstateAddress realEstateAddress) {
		super.updateStatus(realEstateAddress);
	}
	
	/**
	 * 删除数据
	 * @param realEstateAddress
	 */
	@Override
	@Transactional
	public void delete(RealEstateAddress realEstateAddress) {
		super.delete(realEstateAddress);
		RealEstateAddressFloor realEstateAddressFloor = new RealEstateAddressFloor();
		realEstateAddressFloor.setRealEstateAddressId(realEstateAddress);
		realEstateAddressFloorDao.deleteByEntity(realEstateAddressFloor);
	}


	@Override
	public void addDataScopeFilter(RealEstateAddress realEstateAddress) {
		realEstateAddress
				.sqlMap()
				.getDataScope()
				.addFilter("dsf", "Office", "a.used_office_code", DataScope.CTRL_PERMI_HAVE)
				.addFilter("dsf", "Office", "a.owner_office_code", DataScope.CTRL_PERMI_HAVE);
	}
	
}