package com.hsobs.ob.modules.earlywarnmessage.dao;

import com.hsobs.ob.modules.earlywarnmessage.entity.EarlyWarnMessage;
import com.hsobs.ob.modules.earlywarnmessage.entity.RoomAreaExcessRecord;
import com.hsobs.ob.modules.earlywarnmessage.entity.UnitAreaExcessRecord;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.ob.modules.earlywarnmessage.entity.EarlyWarnMessage;

import java.util.List;
import java.util.Map;

/**
 * 预警消息DAO接口
 * <AUTHOR>
 * @version 2024-12-02
 */
@MyBatisDao
public interface EarlyWarnMessageDao extends CrudDao<EarlyWarnMessage> {
    /**
     * 获取个人办公室使用面积情况
     * @param realEstateType
     * @param establishmentType
     * @return
     */
    List<Map<String, Object>> countOwnership(String realEstateType, String establishmentType);

    /**
     * 获取机构服务用房人数和面积情况
     * @param realEstateType
     * @param establishmentType
     * @return
     */
    List<Map<String, Object>> countOfficeUserNumber(String realEstateType, String earlyWarnIndex);

    /**
     * 获取机构办公室和服务用房面积情况
     * @return
     */
    List<Map<String, Object>> countOfficeAndServiceUseSituation();

    List<RoomAreaExcessRecord> roomAreaExcessRecordListData(RoomAreaExcessRecord roomAreaExcessRecord);

    List<UnitAreaExcessRecord> unitAreaExcessRecordListData(UnitAreaExcessRecord unitAreaExcessRecord);
}