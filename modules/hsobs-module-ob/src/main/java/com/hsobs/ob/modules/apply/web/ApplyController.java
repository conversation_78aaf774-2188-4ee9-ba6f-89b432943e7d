package com.hsobs.ob.modules.apply.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.ob.modules.apply.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.apply.service.ApplyService;

import java.util.List;

/**
 * 使用管理Controller
 * <AUTHOR>
 * @version 2025-02-08
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/")
public class ApplyController extends BaseController {

	@Autowired
	private ApplyService applyService;

	@Autowired
	TaskService taskService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public Apply get(String id, boolean isNewRecord) {
		return applyService.get(id, isNewRecord);
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = {"list", ""})
	public String list(Apply apply, Model model) {
		model.addAttribute("apply", apply);
		return "modules/apply/applyList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = {"listLedger", ""})
	public String listLedger(Apply apply, Model model) {
		model.addAttribute("apply", apply);
		return "modules/apply/applyLedgerList";
	}

	/**
	 * 查询列表-使用明细查询
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = {"applyDetailQuery", ""})
	public String applyDetailQuery(ApplyDetailQuery applyDetailQuery, Model model) {
		model.addAttribute("applyDetailQuery", applyDetailQuery);
		return "modules/apply/applyDetailQuery";
	}

	/**
	 * 查询列表-使用明细查询
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = {"applyInfoQuery", ""})
	public String applyInfoQuery(ApplyInfoQuery applyInfoQuery, Model model) {
		model.addAttribute("applyInfoQuery", applyInfoQuery);
		return "modules/apply/applyInfoQuery";
	}

	/**
	 * 查询列表-有偿使用查询
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = {"applyCompensationQuery", ""})
	public String applyCompensationQuery(ApplyDetailQuery applyDetailQuery, Model model) {
		model.addAttribute("applyDetailQuery", applyDetailQuery);
		return "modules/apply/applyCompensationQuery";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = "listApplyDetailQueryData")
	@ResponseBody
	public Page<ApplyDetailQuery> listApplyDetailQueryData(ApplyDetailQuery applyDetailQuery, HttpServletRequest request, HttpServletResponse response) {
		applyDetailQuery.setPage(new Page<>(request, response));
		Page<ApplyDetailQuery> page = applyService.listApplyDetailQueryData(applyDetailQuery);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = "listApplyInfoQueryData")
	@ResponseBody
	public Page<ApplyInfoQuery> listApplyInfoQueryData(ApplyInfoQuery applyInfoQuery, HttpServletRequest request, HttpServletResponse response) {
		applyInfoQuery.setPage(new Page<>(request, response));
		Page<ApplyInfoQuery> page = applyService.listApplyInfoQueryData(applyInfoQuery);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<Apply> listData(Apply apply, HttpServletRequest request, HttpServletResponse response) {
		apply.setPage(new Page<>(request, response));
		Page<Apply> page = applyService.findPage(apply);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = "listLedgerData")
	@ResponseBody
	public Page<ApplyLedger> listLedgerData(ApplyLedger applyLedger, HttpServletRequest request, HttpServletResponse response) {
		applyLedger.setPage(new Page<>(request, response));
		Page<ApplyLedger> page = applyService.listLedgerData(applyLedger);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = "form")
	public String form(Apply apply, Model model) {
		if (apply.getIsNewRecord()) {
			User currentUser = UserUtils.getUser();
			apply.setUsedUserCode(currentUser.getUserCode());
			apply.setUsedUser(currentUser);
			if (null != currentUser.getRefObj()) {
				Employee employee = currentUser.getRefObj();
				if (null != employee) {
					if (null != employee.getOffice()) {
						apply.setUsedOfficeCode(employee.getOffice().getOfficeCode());
					}
					apply.setUsedOffice(employee.getOffice());
				}
			}
		}
		applyService.loadChildData(apply);
		model.addAttribute("apply", apply);
		boolean commonReadonly= null != apply.getStatus() && !apply.getStatus().equals("9");
		model.addAttribute("commonReadonly", commonReadonly);
		String currentTaskName = "";
		BpmProcIns bpmProcIns = BpmUtils.getProcIns(apply, "ob_apply");
		if (null != bpmProcIns) {
			Task task = taskService.createTaskQuery().processInstanceId(bpmProcIns.getId()).singleResult();
			if (null != task) {
				currentTaskName = task.getName();
			}
		}
		model.addAttribute("currentTaskName", currentTaskName);
		return "modules/apply/applyForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("apply::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated Apply apply) {
		applyService.save(apply);
		return renderResult(Global.TRUE, text("保存成功！"));
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("apply::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(Apply apply) {
		if (!Apply.STATUS_DRAFT.equals(apply.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		applyService.delete(apply);
		return renderResult(Global.TRUE, text("删除成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = "exportData")
	public void exportData(ApplyLedger applyLedger, HttpServletResponse response) {
		List<ApplyLedger> list = applyService.listLedger(applyLedger);
		String fileName = "使用明细台账" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("使用明细台账", ApplyLedger.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据-使用明细查询
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = "exportDetailData")
	public void exportDetailData(ApplyDetailQuery applyDetailQuery, HttpServletResponse response) {
		List<ApplyDetailQuery> list = applyService.exportDetailData(applyDetailQuery);
		String fileName = "使用明细查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("使用明细查询", ApplyDetailQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据-使用查询查询
	 */
	@RequiresPermissions("apply::view")
	@RequestMapping(value = "exportInfoData")
	public void exportInfoData(ApplyInfoQuery applyInfoQuery, HttpServletResponse response) {
		List<ApplyInfoQuery> list = applyService.exportInfoData(applyInfoQuery);
		String fileName = "使用查询查询" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("使用查询查询", ApplyInfoQuery.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	@RequiresPermissions("apply::view")
	@RequestMapping(value = "credentialsOrAgreementsList")
	@ResponseBody
	public Page<CredentialsAgreementsListResponse> credentialsOrAgreementsList(CredentialsAgreementsListRequest request) {
		CredentialsAgreementsListResponse response = new CredentialsAgreementsListResponse();
		response.setPage(new Page<>(request.getPageIndex(), request.getPageSize()));
		response.setFileType(request.getType());
		return applyService.findCredentialsAgreementsListByFileInfo(response);
	}
}