package com.hsobs.ob.modules.vacated.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 清理腾退Entity
 * <AUTHOR>
 * @version 2025-02-03
 */
public class VacatedQuery extends BpmEntity<VacatedQuery> {

	private String officeName;    // 腾退单位
	private String officeCode;    // 腾退单位
	private String vacateName;    // 腾退名称
	private String vacateType;    // 腾退类型
	private Date vacateDate;      // 腾退时间
	private String vacateStatus;  // 腾退状态
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	@ExcelFields({
			@ExcelField(title="腾退单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="腾退名称", attrName="vacateName", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="腾退类型", attrName="vacateType", dictType="ob_vacated_type", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="腾退时间", attrName="vacateDate", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="腾退状态", attrName="vacateStatus", dictType="bpm_biz_status", align= ExcelField.Align.CENTER, sort=60),
	})

	public String getVacateName() {
		return vacateName;
	}

	public void setVacateName(String vacateName) {
		this.vacateName = vacateName;
	}

	public String getVacateType() {
		return vacateType;
	}

	public void setVacateType(String vacateType) {
		this.vacateType = vacateType;
	}

	public Date getVacateDate() {
		return vacateDate;
	}

	public void setVacateDate(Date vacateDate) {
		this.vacateDate = vacateDate;
	}

	public String getVacateStatus() {
		return vacateStatus;
	}

	public void setVacateStatus(String vacateStatus) {
		this.vacateStatus = vacateStatus;
	}
}