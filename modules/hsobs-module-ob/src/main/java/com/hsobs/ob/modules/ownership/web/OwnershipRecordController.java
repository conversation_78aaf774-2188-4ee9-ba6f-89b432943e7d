package com.hsobs.ob.modules.ownership.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.ownership.entity.OwnershipRecord;
import com.hsobs.ob.modules.ownership.service.OwnershipRecordService;

/**
 * 权属备案Controller
 * <AUTHOR>
 * @version 2025-03-09
 */
@Controller
@RequestMapping(value = "${adminPath}/ownership/record")
public class OwnershipRecordController extends BaseController {

	@Autowired
	private OwnershipRecordService ownershipRecordService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public OwnershipRecord get(String id, boolean isNewRecord) {
		return ownershipRecordService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("ownership:record:view")
	@RequestMapping(value = {"list", ""})
	public String list(OwnershipRecord ownershipRecord, Model model) {
		model.addAttribute("ownershipRecord", ownershipRecord);
		return "modules/ownership/ownershipRecordList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("ownership:record:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<OwnershipRecord> listData(OwnershipRecord ownershipRecord, HttpServletRequest request, HttpServletResponse response) {
 		ownershipRecord.setPage(new Page<>(request, response));
		Page<OwnershipRecord> page = ownershipRecordService.findPage(ownershipRecord);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("ownership:record:view")
	@RequestMapping(value = "form")
	public String form(OwnershipRecord ownershipRecord, Model model) {
		ownershipRecord.setReport(false);
		if (ownershipRecord.getIsNewRecord()) {
			ownershipRecord.setRecordStatus("1");
		}
		model.addAttribute("ownershipRecord", ownershipRecord);
		return "modules/ownership/ownershipRecordForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("ownership:record:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated OwnershipRecord ownershipRecord) {
		ownershipRecordService.save(ownershipRecord);
		if (ownershipRecord.isReport()) {
			ownershipRecordService.asyncUpdateStatus(ownershipRecord);
			return renderResult(Global.TRUE, text("权属备案报送中，报送结果请稍后刷新列表查看"));
		}
		return renderResult(Global.TRUE, text("保存权属备案成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("ownership:record:view")
	@RequestMapping(value = "exportData")
	public void exportData(OwnershipRecord ownershipRecord, HttpServletResponse response) {
		List<OwnershipRecord> list = ownershipRecordService.findList(ownershipRecord);
		String fileName = "权属备案" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("权属备案", OwnershipRecord.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("ownership:record:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		OwnershipRecord ownershipRecord = new OwnershipRecord();
		List<OwnershipRecord> list = ListUtils.newArrayList(ownershipRecord);
		String fileName = "权属备案模板.xlsx";
		try(ExcelExport ee = new ExcelExport("权属备案", OwnershipRecord.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("ownership:record:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = ownershipRecordService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("ownership:record:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(OwnershipRecord ownershipRecord) {
		ownershipRecordService.delete(ownershipRecord);
		return renderResult(Global.TRUE, text("删除权属备案成功！"));
	}
	
}