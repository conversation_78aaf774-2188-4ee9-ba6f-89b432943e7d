package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;

/**
 * 办公用房监督整改情况数据统计Entity
 * <AUTHOR>
 * @version 2024-12-24
 */
public class DataStatisticsForSupervision extends DataEntity<DataStatisticsForSupervision> {

	private static final long serialVersionUID = 1L;
	private String sysCode;
	private SupervisionTable supervisionTable;

	public DataStatisticsForSupervision() {
		this(null);
	}

	public DataStatisticsForSupervision(String id){
		super(id);
	}

	public String getSysCode() {
		return sysCode;
	}

	public void setSysCode(String sysCode) {
		this.sysCode = sysCode;
	}

	public SupervisionTable getSupervisionTable() {
		return supervisionTable;
	}

	public void setSupervisionTable(SupervisionTable supervisionTable) {
		this.supervisionTable = supervisionTable;
	}
}