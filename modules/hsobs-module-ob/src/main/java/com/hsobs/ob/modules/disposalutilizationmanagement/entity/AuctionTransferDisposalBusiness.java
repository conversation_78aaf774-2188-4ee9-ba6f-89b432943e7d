package com.hsobs.ob.modules.disposalutilizationmanagement.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 拍卖转让处置业务Entity
 * <AUTHOR>
 * @version 2025-03-08
 */
@Table(name="ob_auction_transfer_disposal_business", alias="a", label="拍卖转让处置业务信息", columns={
        @Column(name="id", attrName="id", label="编号", isPK=true),
        @Column(name="disposal_id", attrName="disposalId", label="处置编号"),
        @Column(name="purchasing_unit", attrName="purchasingUnit", label="购买单位"),
        @Column(name="auction_time", attrName="auctionTime", label="拍卖时间", isUpdateForce=true),
        @Column(name="sale_price", attrName="salePrice", label="拍卖成交价格", isUpdateForce=true),
        @Column(includeEntity=DataEntity.class),
}, orderBy="a.update_date DESC"
)
public class AuctionTransferDisposalBusiness extends DataEntity<AuctionTransferDisposalBusiness> {

    private static final long serialVersionUID = 1L;
    private String disposalId;		// 处置编号
    private String purchasingUnit;		// 购买单位
    private Date auctionTime;		// 拍卖时间
    private Long salePrice;		// 拍卖成交价格

    public AuctionTransferDisposalBusiness() {
        this(null);
    }

    public AuctionTransferDisposalBusiness(String id){
        super(id);
    }

    @Size(min=0, max=64, message="处置编号长度不能超过 64 个字符")
    public String getDisposalId() {
        return disposalId;
    }

    public void setDisposalId(String disposalId) {
        this.disposalId = disposalId;
    }

    @Size(min=0, max=256, message="购买单位长度不能超过 256 个字符")
    public String getPurchasingUnit() {
        return purchasingUnit;
    }

    public void setPurchasingUnit(String purchasingUnit) {
        this.purchasingUnit = purchasingUnit;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    public Date getAuctionTime() {
        return auctionTime;
    }

    public void setAuctionTime(Date auctionTime) {
        this.auctionTime = auctionTime;
    }

    public Long getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(Long salePrice) {
        this.salePrice = salePrice;
    }

}