package com.hsobs.ob.modules.arrange.dao;

import com.hsobs.ob.modules.arrange.entity.*;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;

import java.util.List;

/**
 * 配置管理DAO接口
 * <AUTHOR>
 * @version 2025-01-05
 */
@MyBatisDao
public interface ArrangeDao extends CrudDao<Arrange> {

    List<ArrangeLedger> listLedger(ArrangeLedger arrangeLedger);

    List<TransferQuery> listDataTransferQuery(TransferQuery transferQuery);

    List<ExchangeQuery> listDataExchangeQuery(ExchangeQuery exchangeQuery);

    List<RentInfoQuery> listDataRentInfoQuery(RentInfoQuery rentInfoQuery);

    List<ProjectQuery> listDataProjectQuery(ProjectQuery projectQuery);
}