package com.hsobs.ob.modules.vacated.entity;

import javax.validation.constraints.Size;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 清理腾退Entity
 * <AUTHOR>
 * @version 2025-02-03
 */
@Table(name="ob_vacated", alias="a", label="清理腾退信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="type", attrName="type", label="腾退类型"),
		@Column(name="vacated_describe", attrName="vacatedDescribe", label="腾退说明"),
		@Column(name="real_estate_id", attrName="realEstateId", label="不动产ID"),
		@Column(name="real_estate_address_id", attrName="realEstateAddressId", label="不动产地址ID"),
		@Column(name="verification_describe", attrName="verificationDescribe", label="核验说明"),
		@Column(name="apply_office_code", attrName="applyOfficeCode", label="申请单位"),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新日期", isQuery=false),
		@Column(name="vacated_name", attrName="vacatedName", label="腾退名称", queryType=QueryType.LIKE),
		@Column(name="asset_describe", attrName="assetDescribe", label="资产说明"),

		@Column(name="transfer_office_code", attrName="transferOfficeCode", label="移交单位"),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用单位"),
		@Column(name="transfer_type", attrName="transferType", label="移交类型"),
		@Column(name="transfer_date", attrName="transferDate", label="移交日期"),
		@Column(name="verification_date", attrName="verificationDate", label="核验日期"),
		@Column(
				name = "status",
				attrName = "status",
				label = "状态",
				isUpdate = true,
				isQuery = false,
				comment = "（推荐状态：0：正常；1：删除；2：停用；3：冻结；4：审核、待审核；5：审核驳回；9：草稿）"
		),
		@Column(includeEntity=DataEntity.class),
}, joinTable={
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= Office.class, alias="o",
				attrName = "transferOffice",
				on="o.office_code = a.transfer_office_code",
				columns={@Column(includeEntity=Office.class)}),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= Office.class, alias="o1",
				attrName = "usedOffice",
				on="o1.office_code = a.used_office_code",
				columns={@Column(includeEntity=Office.class)}),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddress.class, alias="b",
				on="b.id = a.real_estate_address_id",
				columns={@Column(includeEntity=RealEstateAddress.class)}),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstate.class, alias="c",
				on="c.id = a.real_estate_id",
				columns={@Column(includeEntity=RealEstate.class)}),
}, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere", orderBy="a.update_date DESC"
)
public class Vacated extends BpmEntity<Vacated> {

	private static final long serialVersionUID = 1L;
	private String type;	// 腾退类型
	private String vacatedDescribe;		// 腾退说明
	private String realEstateId;		// 不动产ID
	private String realEstateAddressId;		// 不动产地址ID
	private String verificationDescribe;		// 核验说明
	private String vacatedName;		// 腾退名称
	private String assetDescribe;		// 资产说明
	private String applyOfficeCode;		// 申请单位

	private String usedOfficeCode; // 使用单位
	private String transferOfficeCode;		// 移交单位
	private String transferType;		// 移交类型
	private Date transferDate;		// 移交日期
	private Date verificationDate;

	private RealEstateAddress realEstateAddress;
	private RealEstate realEstate;
	private Office transferOffice;
	private Office usedOffice;

	private List<VacatedAsset> vacatedAssetList = ListUtils.newArrayList();

	@ExcelFields({
			@ExcelField(title="腾退原因", attrName="type", dictType="ob_vacated_type", align=Align.CENTER, sort=20),
			@ExcelField(title="核验日期", attrName="vacatedDescribe", align=Align.CENTER, sort=20),
			@ExcelField(title="房屋", attrName="realEstateAddress.name", align=Align.CENTER, sort=30),
			@ExcelField(title="房间", attrName="realEstate.name", align=Align.CENTER, sort=40),
			@ExcelField(title="移交单位", attrName="transferOffice.officeName", align=Align.CENTER, sort=140),
			@ExcelField(title="移交类别", attrName="transferType", dictType="ob_transfer_type", align=Align.CENTER, sort=150),
			@ExcelField(title="移交日期", attrName="transferDate", align=Align.CENTER, sort=180, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="状态", attrName="status", dictType="bpm_biz_status", align=Align.CENTER, sort=150),
	})
	public Vacated() {
		this(null);
	}

	public Vacated(String id){
		super(id);
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@NotBlank(message="腾退说明不能为空")
	@Size(min=0, max=1000, message="腾退说明长度不能超过 1000 个字符")
	public String getVacatedDescribe() {
		return vacatedDescribe;
	}

	public void setVacatedDescribe(String vacatedDescribe) {
		this.vacatedDescribe = vacatedDescribe;
	}

	@Size(min=0, max=64, message="不动产ID长度不能超过 64 个字符")
	public String getRealEstateId() {
		return realEstateId;
	}

	public void setRealEstateId(String realEstateId) {
		this.realEstateId = realEstateId;
	}

	@NotBlank(message="不动产地址ID不能为空")
	@Size(min=0, max=64, message="不动产地址ID长度不能超过 64 个字符")
	public String getRealEstateAddressId() {
		return realEstateAddressId;
	}

	public void setRealEstateAddressId(String realEstateAddressId) {
		this.realEstateAddressId = realEstateAddressId;
	}

	@Size(min=0, max=1000, message="核验说明长度不能超过 1000 个字符")
	public String getVerificationDescribe() {
		return verificationDescribe;
	}

	public void setVerificationDescribe(String verificationDescribe) {
		this.verificationDescribe = verificationDescribe;
	}

	@NotBlank(message="腾退名称不能为空")
	@Size(min=0, max=100, message="腾退名称长度不能超过 100 个字符")
	public String getVacatedName() {
		return vacatedName;
	}

	public void setVacatedName(String vacatedName) {
		this.vacatedName = vacatedName;
	}

	public String getAssetDescribe() {
		return assetDescribe;
	}

	public void setAssetDescribe(String assetDescribe) {
		this.assetDescribe = assetDescribe;
	}

	public RealEstateAddress getRealEstateAddress() {
		return realEstateAddress;
	}

	public void setRealEstateAddress(RealEstateAddress realEstateAddress) {
		this.realEstateAddress = realEstateAddress;
	}

	public RealEstate getRealEstate() {
		return realEstate;
	}

	public void setRealEstate(RealEstate realEstate) {
		this.realEstate = realEstate;
	}

	public String getApplyOfficeCode() {
		return applyOfficeCode;
	}

	public void setApplyOfficeCode(String applyOfficeCode) {
		this.applyOfficeCode = applyOfficeCode;
	}

	public String getTransferOfficeCode() {
		return transferOfficeCode;
	}

	public void setTransferOfficeCode(String transferOfficeCode) {
		this.transferOfficeCode = transferOfficeCode;
	}

	public String getTransferType() {
		return transferType;
	}

	public void setTransferType(String transferType) {
		this.transferType = transferType;
	}

	public Date getTransferDate() {
		return transferDate;
	}

	public void setTransferDate(Date transferDate) {
		this.transferDate = transferDate;
	}

	public Date getTransferDate_gte() {
		return sqlMap.getWhere().getValue("transfer_date", QueryType.GTE);
	}

	public void setTransferDate_gte(Date transferDate) {
		sqlMap.getWhere().and("transfer_date", QueryType.GTE, transferDate);
	}

	public Date getTransferDate_lte() {
		return sqlMap.getWhere().getValue("transfer_date", QueryType.LTE);
	}

	public void setTransferDate_lte(Date transferDate) {
		sqlMap.getWhere().and("transfer_date", QueryType.LTE, transferDate);
	}

	public Office getTransferOffice() {
		return transferOffice;
	}

	public void setTransferOffice(Office transferOffice) {
		this.transferOffice = transferOffice;
	}

	public Date getVerificationDate() {
		return verificationDate;
	}

	public void setVerificationDate(Date verificationDate) {
		this.verificationDate = verificationDate;
	}

	public List<VacatedAsset> getVacatedAssetList() {
		return vacatedAssetList;
	}

	public void setVacatedAssetList(List<VacatedAsset> vacatedAssetList) {
		this.vacatedAssetList = vacatedAssetList;
	}

	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}
}