package com.hsobs.ob.modules.supervisehandlingtasks.dao;

import com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasksLedger;
import com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasksQuery;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasks;

import java.util.List;

/**
 * 监督督办任务DAO接口
 * <AUTHOR>
 * @version 2024-12-10
 */
@MyBatisDao
public interface SuperviseHandlingTasksDao extends CrudDao<SuperviseHandlingTasks> {

    List<SuperviseHandlingTasksLedger> listLedger(SuperviseHandlingTasksLedger superviseHandlingTasksLedger);

    List<SuperviseHandlingTasksQuery> listQueryData(SuperviseHandlingTasksQuery superviseHandlingTasksQuery);
}