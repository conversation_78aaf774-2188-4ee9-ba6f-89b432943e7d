package com.hsobs.ob.modules.disposalutilizationmanagement.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 出租处置业务Entity
 * <AUTHOR>
 * @version 2025-03-08
 */
@Table(name="ob_lease_disposal_business", alias="a", label="出租处置业务信息", columns={
        @Column(name="id", attrName="id", label="编号", isPK=true),
        @Column(name="disposal_id", attrName="disposalId", label="处置编号"),
        @Column(name="leasee", attrName="leasee", label="承租方"),
        @Column(name="monthly_rent", attrName="monthlyRent", label="月租金", isUpdateForce=true),
        @Column(name="rental_start_time", attrName="rentalStartTime", label="出租开始时间", isUpdateForce=true),
        @Column(name="rental_end_time", attrName="rentalEndTime", label="出租结束时间", isUpdateForce=true),
        @Column(name="lease_term_month", attrName="leaseTermMonth", label="租期", comment="租期（月）", isUpdateForce=true),
        @Column(includeEntity=DataEntity.class),
}, orderBy="a.update_date DESC"
)
public class LeaseDisposalBusiness extends DataEntity<LeaseDisposalBusiness> {

    private static final long serialVersionUID = 1L;
    private String disposalId;		// 处置编号
    private String leasee;		// 承租方
    private Long monthlyRent;		// 月租金
    private Date rentalStartTime;		// 出租开始时间
    private Date rentalEndTime;		// 出租结束时间
    private Integer leaseTermMonth;		// 租期（月）

    public LeaseDisposalBusiness() {
        this(null);
    }

    public LeaseDisposalBusiness(String id){
        super(id);
    }

    @Size(min=0, max=64, message="处置编号长度不能超过 64 个字符")
    public String getDisposalId() {
        return disposalId;
    }

    public void setDisposalId(String disposalId) {
        this.disposalId = disposalId;
    }

    @Size(min=0, max=64, message="承租方长度不能超过 64 个字符")
    public String getLeasee() {
        return leasee;
    }

    public void setLeasee(String leasee) {
        this.leasee = leasee;
    }

    public Long getMonthlyRent() {
        return monthlyRent;
    }

    public void setMonthlyRent(Long monthlyRent) {
        this.monthlyRent = monthlyRent;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    public Date getRentalStartTime() {
        return rentalStartTime;
    }

    public void setRentalStartTime(Date rentalStartTime) {
        this.rentalStartTime = rentalStartTime;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    public Date getRentalEndTime() {
        return rentalEndTime;
    }

    public void setRentalEndTime(Date rentalEndTime) {
        this.rentalEndTime = rentalEndTime;
    }

    public Integer getLeaseTermMonth() {
        return leaseTermMonth;
    }

    public void setLeaseTermMonth(Integer leaseTermMonth) {
        this.leaseTermMonth = leaseTermMonth;
    }

}