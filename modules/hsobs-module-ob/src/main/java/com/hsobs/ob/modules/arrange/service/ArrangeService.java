package com.hsobs.ob.modules.arrange.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.hsobs.ob.modules.arrange.dao.ArrangeRealEstateDao;
import com.hsobs.ob.modules.arrange.entity.*;
import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.hsobs.ob.modules.estate.service.RealEstateAddressService;
import com.hsobs.ob.modules.estate.service.RealEstateService;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.file.entity.FileEntity;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.arrange.dao.ArrangeDao;
import java.util.Map;

import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 配置管理Service
 * <AUTHOR>
 * @version 2025-01-05
 */
@Service
public class ArrangeService extends CrudService<ArrangeDao, Arrange> {

	@Autowired
	private ArrangeDao arrangeDao;
	@Autowired
	private ArrangeRealEstateDao arrangeRealEstateDao;

	@Autowired
	private RealEstateService realEstateService;

	@Autowired
	private RealEstateAddressService realEstateAddressService;

	@Autowired
	private RentalContractTemplateService rentalContractTemplateService;

	@Autowired
	FileUploadService fileUploadService;
	/**
	 * 获取单条数据
	 * @param arrange
	 * @return
	 */
	@Override
	public Arrange get(Arrange arrange) {
		return super.get(arrange);
	}

	/**
	 * 查询分页数据
	 * @param arrange 查询条件
	 * @param arrange page 分页对象
	 * @return
	 */
	@Override
	public Page<Arrange> findPage(Arrange arrange) {
		return super.findPage(arrange);
	}

	/**
	 * 查询列表数据
	 * @param arrange
	 * @return
	 */
	@Override
	public List<Arrange> findList(Arrange arrange) {
		return super.findList(arrange);
	}

	/**
	 * 加载子表数据
	 */
	public Arrange loadChildData(Arrange arrange) {
		if (arrange != null && !arrange.getIsNewRecord()){
			ArrangeRealEstate arrangeRealEstate = new ArrangeRealEstate(arrange);
			arrange.setArrangeRealEstateList(arrangeRealEstateDao.findList(arrangeRealEstate));
		}
		return arrange;
	}

	/**
	 * 保存数据（插入或更新）
	 * @param arrange
	 */
	@Override
	@Transactional
	public void save(Arrange arrange) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(arrange.getStatus())){
			arrange.setApplicationDate(new Date());
			arrange.setStatus(Arrange.STATUS_AUDIT);
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (Arrange.STATUS_NORMAL.equals(arrange.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (Arrange.STATUS_DRAFT.equals(arrange.getStatus())
				|| Arrange.STATUS_AUDIT.equals(arrange.getStatus())){
			super.save(arrange);
		}

		if (null == arrange.getApprovalCode()) {
			arrange.setApprovalCode(arrange.getId());
			super.update(arrange);
		}
		// 如果为审核状态，则进行审批流操作
		if (Arrange.STATUS_AUDIT.equals(arrange.getStatus())){
			if (null == arrange.getApplicationDate()) {
				arrange.setApplicationDate(new Date());
			}
			arrange.setLastApprovalDate(new Date());
			arrange.setLastApprovalCommon(arrange.getBpm().getComment());
			arrange.setApprovalCode(arrange.getId());
			super.update(arrange);
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			variables.put("overallRelocation", arrange.getOverallRelocation());
			//variables.put("leaveDays", arrange.getLeaveDays());

			variables.put("area", arrange.getArea());
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(arrange.getBpm().getProcInsId())
					&& StringUtils.isBlank(arrange.getBpm().getTaskId())){
				BpmUtils.start(arrange, "adjustment_application", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(arrange, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(arrange, arrange.getId(), "arrange_file");
		// 调剂管理相关文件
		FileUploadUtils.saveFileUpload(arrange, arrange.getId(), "arrange_certificate_file");
		FileUploadUtils.saveFileUpload(arrange, arrange.getId(), "arrange_floor_file");
		FileUploadUtils.saveFileUpload(arrange, arrange.getId(), "arrange_person_file");
		FileUploadUtils.saveFileUpload(arrange, arrange.getId(), "arrange_allocation_file");
		FileUploadUtils.saveFileUpload(arrange, arrange.getId(), "arrange_provincial_reason_file");

		// 租用相关文件
		FileUploadUtils.saveFileUpload(arrange, arrange.getId(), "arrange_contract_file");


		ArrangeRealEstate clearArrangeRealEstate = new ArrangeRealEstate();
		clearArrangeRealEstate.setArrange(arrange);
		arrangeRealEstateDao.deleteByEntity(clearArrangeRealEstate);

		for (ArrangeRealEstate saveArrangeRealEstate : arrange.getArrangeRealEstateList()){
			saveArrangeRealEstate.setArrange(arrange);
			arrangeRealEstateDao.insert(saveArrangeRealEstate);
		}
	}

	/**
	 * 更新状态
	 * @param arrange
	 */
	@Override
	@Transactional
	public void updateStatus(Arrange arrange) {
		super.updateStatus(arrange);

		if (null!= arrange && null != arrange.getStatus() && arrange.getStatus().equals(Arrange.STATUS_NORMAL)){
			arrange.setApprovalDate(new Date());
			super.save(arrange);

			Arrange arrangeInfo = super.get(arrange.getId(), false);
			if (null == arrangeInfo) {
				return;
			}
			List<ArrangeRealEstate> arrangeRealEstateList = arrangeRealEstateDao.findList(new ArrangeRealEstate(arrange));
			if (null != arrangeInfo.getRealEstateType() && !arrangeRealEstateList.isEmpty()) {
				arrangeRealEstateList.forEach(arrangeRealEstate->{
					if (arrangeInfo.getRealEstateType().equals("1")) {
						RealEstateAddress saveRealEstateAddress = new RealEstateAddress();
						saveRealEstateAddress.setId(arrangeRealEstate.getRealEstateId());
						saveRealEstateAddress.setUsedOfficeCode(arrangeInfo.getUsedOfficeCode());
						saveRealEstateAddress.setUsedUserCode(arrangeInfo.getUsedUserCode());
						realEstateAddressService.save(saveRealEstateAddress);
					} else {
						RealEstate saveRealEstate = new RealEstate();
						saveRealEstate.setId(arrangeRealEstate.getRealEstateId());
						saveRealEstate.setUsedOfficeCode(arrangeInfo.getUsedOfficeCode());
						saveRealEstate.setUsedUserCode(arrangeInfo.getUsedUserCode());
						realEstateService.save(saveRealEstate);
					}
				});
			}

			if (arrangeInfo.getArrangeType().equals("3")) {
				genContractFile(arrangeInfo);
            }
		}

	}

	public void genContractById(String arrangeId) {
		Arrange arrange = super.get(arrangeId, false);
		if (null == arrange) {
			return;
		}
		if (!arrange.getStatus().equals(Arrange.STATUS_NORMAL)) {
			return;
		}

		if (!arrange.getArrangeType().equals("3")) {
			return;
		}
		genContractFile(arrange);
	}

	private void genContractFile(Arrange arrangeInfo) {
		RentalContractTemplate where = new RentalContractTemplate();
		where.sqlMap().getWhere().and("enable", QueryType.EQ, "1");
		List<RentalContractTemplate> rentalContractTemplateList = rentalContractTemplateService.findList(where);
		if (null == rentalContractTemplateList || rentalContractTemplateList.isEmpty()) {
			return;
		}
		RentalContractTemplate rentalContractTemplate = rentalContractTemplateList.get(0);

		List<FileUpload> rentalContractTemplateFileList = FileUploadUtils.findFileUpload(rentalContractTemplate.getId(), "rentalContractTemplate_file");
		if (null == rentalContractTemplateFileList || rentalContractTemplateFileList.isEmpty()) {
			return;
		}
		FileUpload fileUpload = rentalContractTemplateFileList.get(0);
		FileEntity fileEntity = fileUpload.getFileEntity();
		ConfigureBuilder configureBuilder = Configure.builder();
		Configure config = configureBuilder.build();
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		try (InputStream templateStream = Files.newInputStream(Paths.get(fileEntity.getFileRealPath()));
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {

			template.render(new HashMap<String, Object>() {{
				put("lessor", arrangeInfo.getLessor());
				put("usedUserName", arrangeInfo.getUsedUser().getUserName());
				put("address", arrangeInfo.getRentalAddress());
				put("budget", arrangeInfo.getBudget());
				put("startDate", formatterDateString(arrangeInfo.getRentalStartDate()));
				put("endDate", formatterDateString(arrangeInfo.getRentalEndDate()));
			}});
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

		String md5 = DigestUtils.md5Hex(outputStream.toByteArray());

		FileUploadParams fileParams = new FileUploadParams();
		MultipartFile multipartFile = new MockMultipartFile(
				"租用合同.docx",
				"租用合同.docx",
				"application/octet-stream",
				outputStream.toByteArray()
		);
		fileParams.setBizKey(arrangeInfo.getId());
		fileParams.setBizType("arrange_contract_file");
		fileParams.setFile(multipartFile);
		fileParams.setFileMd5(md5);
		fileParams.setFileName("租用合同.docx");
		FileUpload contractFileUpload = new FileUpload();
		contractFileUpload.setBizKey(arrangeInfo.getId());
		contractFileUpload.setBizType("arrange_contract_file");
		Map<String, Object> uploadedFileMap = fileUploadService.uploadFile(contractFileUpload, fileParams);
		arrangeInfo.setDataMap(uploadedFileMap);
		FileUploadUtils.saveFileUpload(arrangeInfo, arrangeInfo.getId(), "arrange_contract_file");
	}

	private String formatterDateString(Date date) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().format(formatter);
	}

	/**
	 * 删除数据
	 * @param arrange
	 */
	@Override
	@Transactional
	public void delete(Arrange arrange) {
		super.delete(arrange);
		ArrangeRealEstate arrangeRealEstate = new ArrangeRealEstate();
		arrangeRealEstate.setArrange(arrange);
		arrangeRealEstateDao.deleteByEntity(arrangeRealEstate);
	}

	public Page<ArrangeLedger> listLedgerData(ArrangeLedger arrangeLedger) {
		Page<ArrangeLedger> page = (Page<ArrangeLedger>) arrangeLedger.getPage();
		List<ArrangeLedger> list = arrangeDao.listLedger(arrangeLedger);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<ArrangeLedger> listLedger(ArrangeLedger arrangeLedger) {
		return arrangeDao.listLedger(arrangeLedger);
	}

	public Page<TransferQuery> listDataTransferQuery(TransferQuery transferQuery) {
		Page<TransferQuery> page = (Page<TransferQuery>) transferQuery.getPage();
		List<TransferQuery> list = arrangeDao.listDataTransferQuery(transferQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public Page<ExchangeQuery> listDataExchangeQuery(ExchangeQuery exchangeQuery) {
		Page<ExchangeQuery> page = (Page<ExchangeQuery>) exchangeQuery.getPage();
		List<ExchangeQuery> list = arrangeDao.listDataExchangeQuery(exchangeQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public Page<RentInfoQuery> listDataRentInfoQuery(RentInfoQuery rentInfoQuery) {
		Page<RentInfoQuery> page = (Page<RentInfoQuery>) rentInfoQuery.getPage();
		List<RentInfoQuery> list = arrangeDao.listDataRentInfoQuery(rentInfoQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public Page<ProjectQuery> listDataProjectQuery(ProjectQuery projectQuery) {
		Page<ProjectQuery> page = (Page<ProjectQuery>) projectQuery.getPage();
		List<ProjectQuery> list = arrangeDao.listDataProjectQuery(projectQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<TransferQuery> exportDataTransferQuery(TransferQuery transferQuery) {
		return arrangeDao.listDataTransferQuery(transferQuery);
	}

	public List<ExchangeQuery> exportDataExchangeQuery(ExchangeQuery exchangeQuery) {
		return arrangeDao.listDataExchangeQuery(exchangeQuery);
	}

	public List<RentInfoQuery> exportDataRentInfoQuery(RentInfoQuery rentInfoQuery) {
		return arrangeDao.listDataRentInfoQuery(rentInfoQuery);
	}

	public List<ProjectQuery> exportDataProjectQuery(ProjectQuery projectQuery) {
		return arrangeDao.listDataProjectQuery(projectQuery);
	}

	@Override
	public void addDataScopeFilter(Arrange arrange) {
		arrange.sqlMap().getDataScope().addFilter("dsf", "Office", "a.used_office_code", DataScope.CTRL_PERMI_HAVE);
	}

	public void addDataScopeFilterByTransferQuery(TransferQuery transferQuery) {
		SqlMap sqlMap = transferQuery.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"oa.USED_OFFICE_CODE",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}

	public void addDataScopeFilterByRentInfoQuery(RentInfoQuery rentInfoQuery) {
		SqlMap sqlMap = rentInfoQuery.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"oa.USED_OFFICE_CODE",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}

	public void addDataScopeFilter(ArrangeLedger arrangeLedger) {
		SqlMap sqlMap = arrangeLedger.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"oa.USED_OFFICE_CODE",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}
}