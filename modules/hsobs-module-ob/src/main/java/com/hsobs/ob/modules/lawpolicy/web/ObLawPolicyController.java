package com.hsobs.ob.modules.lawpolicy.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.lawpolicy.entity.ObLawPolicy;
import com.hsobs.ob.modules.lawpolicy.service.ObLawPolicyService;

/**
 * 政策法规Controller
 * <AUTHOR>
 * @version 2025-02-15
 */
@Controller
@RequestMapping(value = "${adminPath}/lawpolicy/obLawPolicy")
public class ObLawPolicyController extends BaseController {

	@Autowired
	private ObLawPolicyService obLawPolicyService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ObLawPolicy get(String id, boolean isNewRecord) {
		return obLawPolicyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("lawpolicy:obLawPolicy:view")
	@RequestMapping(value = {"list", ""})
	public String list(ObLawPolicy obLawPolicy, Model model) {
		model.addAttribute("obLawPolicy", obLawPolicy);
		return "modules/lawpolicy/obLawPolicyList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("lawpolicy:obLawPolicy:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ObLawPolicy> listData(ObLawPolicy obLawPolicy, HttpServletRequest request, HttpServletResponse response) {
		obLawPolicy.setPage(new Page<>(request, response));
		Page<ObLawPolicy> page = obLawPolicyService.findPage(obLawPolicy);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("lawpolicy:obLawPolicy:view")
	@RequestMapping(value = "form")
	public String form(ObLawPolicy obLawPolicy, Model model) {
		model.addAttribute("obLawPolicy", obLawPolicy);
		return "modules/lawpolicy/obLawPolicyForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("lawpolicy:obLawPolicy:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ObLawPolicy obLawPolicy) {
		obLawPolicyService.save(obLawPolicy);
		return renderResult(Global.TRUE, text("保存政策法规成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("lawpolicy:obLawPolicy:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ObLawPolicy obLawPolicy) {
		obLawPolicyService.delete(obLawPolicy);
		return renderResult(Global.TRUE, text("删除政策法规成功！"));
	}
	
}