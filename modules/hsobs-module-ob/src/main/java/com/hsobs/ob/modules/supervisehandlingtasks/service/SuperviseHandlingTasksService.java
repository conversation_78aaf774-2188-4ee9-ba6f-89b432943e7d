package com.hsobs.ob.modules.supervisehandlingtasks.service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagement;
import com.hsobs.ob.modules.repair.entity.RepairRequestLedger;
import com.hsobs.ob.modules.supervisehandlingtasks.dao.SuperviseHandlingTasksDao;
import com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasksLedger;
import com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasksQuery;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.utils.word.WordExport;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasks;
import com.hsobs.ob.modules.supervisehandlingtasks.dao.SuperviseHandlingTasksDao;
import java.util.Map;

import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 监督督办任务Service
 * <AUTHOR>
 * @version 2024-12-10
 */
@Service
public class SuperviseHandlingTasksService extends CrudService<SuperviseHandlingTasksDao, SuperviseHandlingTasks> {

	@Autowired
	FileUploadService fileUploadService;

	@Autowired
	private SuperviseHandlingTasksDao superviseHandlingTasksDao;
	/**
	 * 获取单条数据
	 * @param superviseHandlingTasks
	 * @return
	 */
	@Override
	public SuperviseHandlingTasks get(SuperviseHandlingTasks superviseHandlingTasks) {
		return super.get(superviseHandlingTasks);
	}
	
	/**
	 * 查询分页数据
	 * @param superviseHandlingTasks 查询条件
	 * @param superviseHandlingTasks page 分页对象
	 * @return
	 */
	@Override
	public Page<SuperviseHandlingTasks> findPage(SuperviseHandlingTasks superviseHandlingTasks) {
		return super.findPage(superviseHandlingTasks);
	}


	/**
	 * 添加数据权限过滤条件
	 */
	@Override
	public void addDataScopeFilter(SuperviseHandlingTasks superviseHandlingTasks){
		SqlMap sqlMap = superviseHandlingTasks.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"a.office_code",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}

	/**
	 * 查询列表数据
	 * @param superviseHandlingTasks
	 * @return
	 */
	@Override
	public List<SuperviseHandlingTasks> findList(SuperviseHandlingTasks superviseHandlingTasks) {
		return super.findList(superviseHandlingTasks);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param superviseHandlingTasks
	 */
	@Override
	@Transactional
	public void save(SuperviseHandlingTasks superviseHandlingTasks) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(superviseHandlingTasks.getStatus())){
			superviseHandlingTasks.setStatus(SuperviseHandlingTasks.STATUS_AUDIT);
		}
		//变更监督督办任务的任务状态
		if(superviseHandlingTasks.getBpm().getActivityId().equals("s2416b4c18"))
		{
			superviseHandlingTasks.setTaskStatus("4");
		}
		if(superviseHandlingTasks.getTaskStatus().equals("2")){
			WordExport export = new WordExport();
			String fileName = "classpath:" + org.apache.commons.lang3.StringUtils.substringBeforeLast(org.apache.commons.lang3.StringUtils.replace(
					WordExport.class.getName(), ".", "/"), "/") + "/督查建议书.docx";
			export.setTemplate(fileName);

			Map<String, String> content = new HashMap<String, String>();
			content.put("officeName", superviseHandlingTasks.getOffice().getOfficeName());
			content.put("behavior", superviseHandlingTasks.getVerifyTheSituation());
			content.put("stipulate", superviseHandlingTasks.getTaskDetails());
			content.put("sugges", superviseHandlingTasks.getRectificationMatters());
			// 获取当前日期
			LocalDate currentDate = LocalDate.now();
			// 获取当前的年、月、日
			int year = currentDate.getYear();
			int month = currentDate.getMonthValue();
			int day = currentDate.getDayOfMonth();
			content.put("year", year+"");
			content.put("month", month+"");
			content.put("day", day+"");
			export.replaceBookMark(content);

			FileUploadParams fileParams = new FileUploadParams();
			MultipartFile multipartFile = new MockMultipartFile(
					superviseHandlingTasks.getTaskName()+"-督查建议书.docx",
					superviseHandlingTasks.getTaskName()+"-督查建议书.docx",
					"application/octet-stream",
					export.getFileContent()
			);
			fileParams.setBizKey(superviseHandlingTasks.getId());
			fileParams.setBizType("superviseHandlingTasks_recommendation_file");
			fileParams.setFile(multipartFile);
			fileParams.setFileMd5(DigestUtils.md5Hex(export.getFileContent()));
			fileParams.setFileName(superviseHandlingTasks.getTaskName()+"-督查建议书.docx");
			FileUpload fileUpload = new FileUpload();
			fileUploadService.uploadFile(fileUpload, fileParams);

			Map<String,Object> dataMap = new HashMap<>();
			dataMap.put("superviseHandlingTasks_recommendation_file",fileUpload.getId());
			superviseHandlingTasks.setDataMap(dataMap);
			// 保存上传附件（督查建议书）
			FileUploadUtils.saveFileUpload(superviseHandlingTasks,superviseHandlingTasks.getId(), "superviseHandlingTasks_recommendation_file");

		}
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (SuperviseHandlingTasks.STATUS_NORMAL.equals(superviseHandlingTasks.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		
		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (SuperviseHandlingTasks.STATUS_DRAFT.equals(superviseHandlingTasks.getStatus())
				|| SuperviseHandlingTasks.STATUS_AUDIT.equals(superviseHandlingTasks.getStatus())){
			super.save(superviseHandlingTasks);
		}

		// 如果为审核状态，则进行审批流操作
		if (SuperviseHandlingTasks.STATUS_AUDIT.equals(superviseHandlingTasks.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			variables.put("taskStatus", superviseHandlingTasks.getTaskStatus());
			
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(superviseHandlingTasks.getBpm().getProcInsId())
					&& StringUtils.isBlank(superviseHandlingTasks.getBpm().getTaskId())){
				BpmUtils.start(superviseHandlingTasks, "supervise_handling_tasks", variables, null);
			}

			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(superviseHandlingTasks, variables, null);
			}
		}
		// 保存上传附件（督办任务材料）
		FileUploadUtils.saveFileUpload(superviseHandlingTasks, superviseHandlingTasks.getId(), "superviseHandlingTasks_supervise_file");
		// 保存上传附件（调查核实材料）
		FileUploadUtils.saveFileUpload(superviseHandlingTasks, superviseHandlingTasks.getId(), "superviseHandlingTasks_verify_file");
		// 保存上传附件（整改证明材料）
		FileUploadUtils.saveFileUpload(superviseHandlingTasks, superviseHandlingTasks.getId(), "superviseHandlingTasks_rectify_file");
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<SuperviseHandlingTasks> list = ei.getDataList(SuperviseHandlingTasks.class);
			for (SuperviseHandlingTasks superviseHandlingTasks : list) {
				try{
					ValidatorUtils.validateWithException(superviseHandlingTasks);
					this.save(superviseHandlingTasks);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + superviseHandlingTasks.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + superviseHandlingTasks.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
	/**
	 * 更新状态
	 * @param superviseHandlingTasks
	 */
	@Override
	@Transactional
	public void updateStatus(SuperviseHandlingTasks superviseHandlingTasks) {
		super.updateStatus(superviseHandlingTasks);
	}
	
	/**
	 * 删除数据
	 * @param superviseHandlingTasks
	 */
	@Override
	@Transactional
	public void delete(SuperviseHandlingTasks superviseHandlingTasks) {
		super.delete(superviseHandlingTasks);
	}

	public void officeSave(SuperviseHandlingTasks superviseHandlingTasks) {
		super.save(superviseHandlingTasks);
	}

	public Page<SuperviseHandlingTasksLedger> listLedgerData(SuperviseHandlingTasksLedger superviseHandlingTasksLedger) {
		Page<SuperviseHandlingTasksLedger> page = (Page<SuperviseHandlingTasksLedger>) superviseHandlingTasksLedger.getPage();
		List<SuperviseHandlingTasksLedger> list = superviseHandlingTasksDao.listLedger(superviseHandlingTasksLedger);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<SuperviseHandlingTasksLedger> listLedger(SuperviseHandlingTasksLedger superviseHandlingTasksLedger) {
		return superviseHandlingTasksDao.listLedger(superviseHandlingTasksLedger);
	}

	public Page<SuperviseHandlingTasksQuery> listQueryData(SuperviseHandlingTasksQuery superviseHandlingTasksQuery) {
		Page<SuperviseHandlingTasksQuery> page = (Page<SuperviseHandlingTasksQuery>) superviseHandlingTasksQuery.getPage();
		List<SuperviseHandlingTasksQuery> list = superviseHandlingTasksDao.listQueryData(superviseHandlingTasksQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<SuperviseHandlingTasksQuery> exportQueryData(SuperviseHandlingTasksQuery superviseHandlingTasksQuery) {
		return superviseHandlingTasksDao.listQueryData(superviseHandlingTasksQuery);
	}
}