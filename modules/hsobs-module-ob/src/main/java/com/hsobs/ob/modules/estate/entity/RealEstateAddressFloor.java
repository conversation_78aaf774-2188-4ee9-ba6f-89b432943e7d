package com.hsobs.ob.modules.estate.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 不动产地址表Entity
 * <AUTHOR>
 * @version 2025-03-10
 */
@Table(name="ob_real_estate_address_floor", alias="a", label="不动产地址表信息", columns={
        @Column(name="id", attrName="id", label="主键", isPK=true),
        @Column(name="floor_index", attrName="floorIndex", label="楼层编号", isUpdateForce=true),
        @Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
        @Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
        @Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
        @Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
        @Column(name="name", attrName="name", label="名称", queryType=QueryType.LIKE),
        @Column(name="real_estate_address_id", attrName="realEstateAddressId.id", label="房屋ID"),
}, orderBy="a.create_date ASC"
)
public class RealEstateAddressFloor extends DataEntity<RealEstateAddressFloor> {

    private static final long serialVersionUID = 1L;
    private Integer floorIndex;		// 楼层编号
    private String name;		// 名称
    private RealEstateAddress realEstateAddressId;		// 房屋ID 父类

    public RealEstateAddressFloor() {
        this(null);
    }

    public RealEstateAddressFloor(RealEstateAddress realEstateAddressId){
        this.realEstateAddressId = realEstateAddressId;
    }

    public Integer getFloorIndex() {
        return floorIndex;
    }

    public void setFloorIndex(Integer floorIndex) {
        this.floorIndex = floorIndex;
    }

    @Size(min=0, max=100, message="名称长度不能超过 100 个字符")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public RealEstateAddress getRealEstateAddressId() {
        return realEstateAddressId;
    }

    public void setRealEstateAddressId(RealEstateAddress realEstateAddressId) {
        this.realEstateAddressId = realEstateAddressId;
    }

}