package com.hsobs.ob.modules.arrange.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;
import java.util.Date;

import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

/**
 * 租借提醒Entity
 * <AUTHOR>
 * @version 2025-03-27
 */
@Table(name="ob_rental_remind", alias="a", label="租借提醒信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="contract_code", attrName="contractCode", label="合同编号"),
		@Column(name="rental_office_code", attrName="rentalOfficeCode", label="承租单位"),
		@Column(name="real_estate_address", attrName="realEstateAddress", label="房屋地址"),
		@Column(name="area", attrName="area", label="面积"),
		@Column(name="house_type", attrName="houseType", label="户型"),
		@Column(name="province_opinion", attrName="provinceOpinion", label="省委省政府意见"),
		@Column(name="rent", attrName="rent", label="租金"),
		@Column(name="rent_start_date", attrName="rentStartDate", label="租用开始时间", queryType=QueryType.GTE, isUpdateForce=true),
		@Column(name="rent_end_date", attrName="rentEndDate", label="租用结束时间", queryType=QueryType.LTE, isUpdateForce=true),
		@Column(name="contract_status", attrName="contractStatus", label="合同状态"),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false),
		@Column(name="remind_type", attrName="remindType", label="提醒方式"),
		@Column(name="remind_user_code", attrName="remindUserCode", label="提醒人"),
	}, joinTable={
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "rentalOffice",
				on = "a.rental_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u2",
				attrName = "remindUser",
				on = "a.remind_user_code = u2.user_code",
				columns = {@Column(includeEntity = User.class)})
	}, orderBy="a.update_date DESC"
)
public class RentalRemind extends DataEntity<RentalRemind> {
	
	private static final long serialVersionUID = 1L;
	private String contractCode;		// 合同编号
	private String rentalOfficeCode;		// 承租单位
	private String realEstateAddress;		// 房屋地址
	private Double area;		// 面积
	private String houseType;		// 户型
	private String provinceOpinion;		// 省委省政府意见
	private Double rent;		// 租金
	private Date rentStartDate;		// 租用开始时间
	private Date rentEndDate;		// 租用结束时间
	private String contractStatus;		// 合同状态
	private String remindType;		// 提醒方式
	private String remindUserCode;

	private Office rentalOffice;
	private User remindUser;

	@ExcelFields({
		@ExcelField(title="合同编号", attrName="contractCode", align=Align.CENTER, sort=20),
		@ExcelField(title="承租单位", attrName="rentalOffice.officeName", align=Align.CENTER, sort=30),
		@ExcelField(title="房屋地址", attrName="realEstateAddress", align=Align.CENTER, sort=40),
		@ExcelField(title="面积", attrName="area", align=Align.CENTER, sort=50),
		@ExcelField(title="户型", attrName="houseType", align=Align.CENTER, sort=60),
		@ExcelField(title="省委省政府意见", attrName="provinceOpinion", align=Align.CENTER, sort=70),
		@ExcelField(title="租金", attrName="rent", align=Align.CENTER, sort=80),
		@ExcelField(title="租用开始时间", attrName="rentStartDate", align=Align.CENTER, sort=90, dataFormat="yyyy-MM-dd hh:mm"),
		@ExcelField(title="租用结束时间", attrName="rentEndDate", align=Align.CENTER, sort=100, dataFormat="yyyy-MM-dd hh:mm"),
		@ExcelField(title="合同状态", attrName="contractStatus", dictType="ob_contract_status", align=Align.CENTER, sort=110),
	})
	public RentalRemind() {
		this(null);
	}
	
	public RentalRemind(String id){
		super(id);
	}
	
	@NotBlank(message="合同编号不能为空")
	@Size(min=0, max=512, message="合同编号长度不能超过 512 个字符")
	public String getContractCode() {
		return contractCode;
	}

	public void setContractCode(String contractCode) {
		this.contractCode = contractCode;
	}
	
	@NotBlank(message="承租单位不能为空")
	@Size(min=0, max=64, message="承租单位长度不能超过 64 个字符")
	public String getRentalOfficeCode() {
		return rentalOfficeCode;
	}

	public void setRentalOfficeCode(String rentalOfficeCode) {
		this.rentalOfficeCode = rentalOfficeCode;
	}
	
	@NotBlank(message="房屋地址不能为空")
	@Size(min=0, max=512, message="房屋地址长度不能超过 512 个字符")
	public String getRealEstateAddress() {
		return realEstateAddress;
	}

	public void setRealEstateAddress(String realEstateAddress) {
		this.realEstateAddress = realEstateAddress;
	}
	
	@NotNull(message="面积不能为空")
	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}
	
	@NotBlank(message="户型不能为空")
	@Size(min=0, max=512, message="户型长度不能超过 512 个字符")
	public String getHouseType() {
		return houseType;
	}

	public void setHouseType(String houseType) {
		this.houseType = houseType;
	}
	
	@NotBlank(message="省委省政府意见不能为空")
	public String getProvinceOpinion() {
		return provinceOpinion;
	}

	public void setProvinceOpinion(String provinceOpinion) {
		this.provinceOpinion = provinceOpinion;
	}
	
	@NotNull(message="租金不能为空")
	public Double getRent() {
		return rent;
	}

	public void setRent(Double rent) {
		this.rent = rent;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getRentStartDate() {
		return rentStartDate;
	}

	public void setRentStartDate(Date rentStartDate) {
		this.rentStartDate = rentStartDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getRentEndDate() {
		return rentEndDate;
	}

	public void setRentEndDate(Date rentEndDate) {
		this.rentEndDate = rentEndDate;
	}
	
	@Size(min=0, max=100, message="合同状态长度不能超过 100 个字符")
	public String getContractStatus() {
		return contractStatus;
	}

	public void setContractStatus(String contractStatus) {
		this.contractStatus = contractStatus;
	}
	
	@Size(min=0, max=100, message="提醒方式长度不能超过 100 个字符")
	public String getRemindType() {
		return remindType;
	}

	public void setRemindType(String remindType) {
		this.remindType = remindType;
	}

	public Office getRentalOffice() {
		return rentalOffice;
	}

	public void setRentalOffice(Office rentalOffice) {
		this.rentalOffice = rentalOffice;
	}

	public Date getRentEndDate_gte() {
		return sqlMap.getWhere().getValue("rent_end_date", QueryType.GTE);
	}

	public void setRentEndDate_gte(Date rentEndDate) {
		sqlMap.getWhere().and("rent_end_date", QueryType.GTE, rentEndDate);
	}

	public Date getRentEndDate_lte() {
		return sqlMap.getWhere().getValue("rent_end_date", QueryType.LTE);
	}

	public void setRentEndDate_lte(Date rentEndDate) {
		sqlMap.getWhere().and("rent_end_date", QueryType.LTE, rentEndDate);
	}

	public String getRemindUserCode() {
		return remindUserCode;
	}

	public void setRemindUserCode(String remindUserCode) {
		this.remindUserCode = remindUserCode;
	}

	public User getRemindUser() {
		return remindUser;
	}

	public void setRemindUser(User remindUser) {
		this.remindUser = remindUser;
	}
}