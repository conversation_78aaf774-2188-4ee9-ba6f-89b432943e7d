package com.hsobs.ob.modules.datastatistics.service;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.hsobs.ob.modules.datastatistics.entity.*;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagement;
import com.jeesite.common.config.Global;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.modules.sys.utils.DictUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.datastatistics.dao.DataStatisticsDao;

/**
 * 办公用房数据统计Service
 * <AUTHOR>
 * @version 2024-12-16
 */
@Service
public class DataStatisticsService extends CrudService<DataStatisticsDao, DataStatisticsForResource> {

	private final DataStatisticsDao dataStatisticsDao;

	public DataStatisticsService(DataStatisticsDao dataStatisticsDao) {
		this.dataStatisticsDao = dataStatisticsDao;
	}

	/**
	 * 获取单条数据
	 *
	 * @param dataStatisticsForResource
	 * @return
	 */
	@Override
	public DataStatisticsForResource get(DataStatisticsForResource dataStatisticsForResource) {
		return super.get(dataStatisticsForResource);
	}

	/**
	 * 查询分页数据
	 *
	 * @param dataStatisticsForResource 查询条件
	 * @param dataStatisticsForResource page 分页对象
	 * @return
	 */
	@Override
	public Page<DataStatisticsForResource> findPage(DataStatisticsForResource dataStatisticsForResource) {
		return super.findPage(dataStatisticsForResource);
	}

	/**
	 * 查询列表数据
	 *
	 * @param dataStatisticsForResource
	 * @return
	 */
	@Override
	public List<DataStatisticsForResource> findList(DataStatisticsForResource dataStatisticsForResource) {
		return super.findList(dataStatisticsForResource);
	}

	/**
	 * 保存数据（插入或更新）
	 *
	 * @param dataStatisticsForResource
	 */
	@Override
	@Transactional
	public void save(DataStatisticsForResource dataStatisticsForResource) {
		super.save(dataStatisticsForResource);
	}

	/**
	 * 更新状态
	 *
	 * @param dataStatisticsForResource
	 */
	@Override
	@Transactional
	public void updateStatus(DataStatisticsForResource dataStatisticsForResource) {
		super.updateStatus(dataStatisticsForResource);
	}

	/**
	 * 删除数据
	 *
	 * @param dataStatisticsForResource
	 */
	@Override
	@Transactional
	public void delete(DataStatisticsForResource dataStatisticsForResource) {
		dataStatisticsForResource.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataStatisticsForResource);
	}

	public Page<DataStatisticsForResource> findResourceDataPage(DataStatisticsForResource dataStatisticsForResource) {

		Page<DataStatisticsForResource> page = (Page<DataStatisticsForResource>) dataStatisticsForResource.getPage();
		ResourceCase resourceCase = new ResourceCase();
		resourceCase.setOfficeName("机关事务局");
		resourceCase.setOfficeType("国家机关");
		resourceCase.setAddress("福建省福州市xxx");
		resourceCase.setFloorNumber(4);
		resourceCase.setPrincipalNumber(4);
		resourceCase.setDeputyNumber(4);
		resourceCase.setBureauNumber(5);
		resourceCase.setOfficeSpace(Float.parseFloat("1.5"));
		resourceCase.setServiceSpace(Float.parseFloat("1.5"));
		resourceCase.setAuxiliarySpace(Float.parseFloat("1.5"));
		resourceCase.setTechnologySpace(Float.parseFloat("1.5"));
		resourceCase.setTotalSpace(Float.parseFloat("1.5"));
		resourceCase.setSiteSpace(Float.parseFloat("1.5"));
		resourceCase.setRentTag("0");
		DataStatisticsForResource dataStatisticsForResourceResult = new DataStatisticsForResource();
		dataStatisticsForResourceResult.setResourceCase(resourceCase);

		SpaceVerification spaceVerification = new SpaceVerification();
		spaceVerification.setOfficeName("机关事务局");
		spaceVerification.setOfficeType("国家机关");
		spaceVerification.setOfficeSpace(Float.parseFloat("1.5"));
		spaceVerification.setServiceSpace(Float.parseFloat("1.5"));
		spaceVerification.setAuxiliarySpace(Float.parseFloat("1.5"));
		spaceVerification.setTechnologySpace(Float.parseFloat("1.5"));
		spaceVerification.setTotalSpace(Float.parseFloat("1.5"));
		dataStatisticsForResourceResult.setSpaceVerification(spaceVerification);
		List<DataStatisticsForResource> dataStatisticsForResourceList = new ArrayList<>();
		dataStatisticsForResourceList.add(dataStatisticsForResourceResult);
		page.setList(dataStatisticsForResourceList);
		return page;
	}

	public Page<OwnershipTable> findOwnershipDataPage(OwnershipTable ownershipTableQuery) {

		Page<OwnershipTable> page = (Page<OwnershipTable>) ownershipTableQuery.getPage();
		List<OwnershipTable> list = dataStatisticsDao.findOwnershipDataPage(ownershipTableQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public Page<AllocationTable> findAllocationDataPage(AllocationTable allocationTableQuery) {

		Page<AllocationTable> page = (Page<AllocationTable>) allocationTableQuery.getPage();
		List<AllocationTable> list = dataStatisticsDao.findAllocationDataPage(allocationTableQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}
	public void addDataScopeFilter(DisposeTable disposeTable){
		SqlMap sqlMap = disposeTable.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"odu.applicant_unit_id",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}

	public Page<DisposeTable> findDisposeDataPage(DisposeTable disposeTableQuery) {

		Page<DisposeTable> page = (Page<DisposeTable>) disposeTableQuery.getPage();

		List<DisposeTable> list = dataStatisticsDao.countDisposalNumber(disposeTableQuery);
		page.setList(list);
		return page;
	}

	public List<DisposeTable> exportDisposeData(DisposeTable disposeTableQuery) {

		List<DisposeTable> list = dataStatisticsDao.countDisposalNumber(disposeTableQuery);
		return list;
	}

	public Page<MaintainTable> findMaintainPage(MaintainTable maintainTableQuery) {

		Page<MaintainTable> page = (Page<MaintainTable>) maintainTableQuery.getPage();
		List<MaintainTable> list = dataStatisticsDao.findMaintainPage(maintainTableQuery);
		page.setList(list);
//		page.setCount(list.size());

		return page;
	}
	public String findMaintainCount(MaintainTable maintainTable) {
		List<Map<String, Object>> list = dataStatisticsDao.findMaintainCount(maintainTable.getAreaCode(), maintainTable.getYear());

//		if (list.size() == 0) {
//			return JSON.toJSONString(list);
//		} else {
			Map<String, Object> map = new HashMap<>();
			List<String> data1 = new ArrayList<>();
			List<String> data2 = new ArrayList<>();
			Map<String, Object> data3 = new HashMap<>();
			List<String> data31 = new ArrayList<>();
			Map<String, Object> data4 = new HashMap<>();
			List<String> data41 = new ArrayList<>();
			Map<String, Object> data5 = new HashMap<>();
			List<String> data51 = new ArrayList<>();
			Map<String, Object> data6 = new HashMap<>();
			List<String> data61 = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				String areaName = stringObjectMap.get("areaName").toString();
				String number = stringObjectMap.get("number").toString();
				String awaitNumber = stringObjectMap.get("awaitNumber").toString();
				String approvedNumber = stringObjectMap.get("approvedNumber").toString();

				//区域
				data2.add(areaName);

				data31.add(number);
				data41.add(awaitNumber);
				data51.add(approvedNumber);
				data61.add(approvedNumber);
			}

			data3.put("name", "总数");
			data3.put("value", data31);

			data4.put("name", "待审批");
			data4.put("value", data41);

			data5.put("name", "已审批");
			data5.put("value", data51);

			data6.put("name", "已维修");
			data6.put("value", data61);

			map.put("data2", data2.stream()
					.distinct()
					.collect(Collectors.toList()));
			map.put("data3", data3);
			map.put("data4", data4);
			map.put("data5", data5);
			map.put("data6", data6);
			return JSON.toJSONString(map);
//		}
	}

	public Page<UnuseTable> findUnusePage(UnuseTable UnuseTableQuery) {

		Page<UnuseTable> page = (Page<UnuseTable>) UnuseTableQuery.getPage();
		List<UnuseTable> list = dataStatisticsDao.findUnusePage(UnuseTableQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public Page<SupervisionTable> findSupervisionPage(SupervisionTable supervisionTableQuery) {

		Page<SupervisionTable> page = (Page<SupervisionTable>) supervisionTableQuery.getPage();
		List<SupervisionTable> list = dataStatisticsDao.countSupervisionNumber(supervisionTableQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public Page<SpaceVerification> spaceVerificationData(SpaceVerification spaceVerificationQuery) {
		Page<SpaceVerification> page = (Page<SpaceVerification>) spaceVerificationQuery.getPage();

		List<SpaceVerification> list = dataStatisticsDao.findSpaceVerificationData(spaceVerificationQuery);
		page.setList(list);
//		page.setCount(list.size());

		return page;
	}

	public Page<DataStatisticsForSupervision> getDataStatisticsForSupervision(DataStatisticsForSupervision dataStatisticsForSupervision) {
		Page<DataStatisticsForSupervision> page = (Page<DataStatisticsForSupervision>) dataStatisticsForSupervision.getPage();
		SupervisionTable supervisionTable = new SupervisionTable();
		supervisionTable.setAreaName("福建");
		supervisionTable.setOfficeName("省直机关");
		supervisionTable.setBusinessType("领导交办");
		supervisionTable.setSpotNumber(200);
		supervisionTable.setQuestionNumber(20);
		supervisionTable.setCorrectedNumber(20);
		supervisionTable.setUncorrectedNumber(20);

		DataStatisticsForSupervision ddataStatisticsForSupervisionResult = new DataStatisticsForSupervision();
		ddataStatisticsForSupervisionResult.setSupervisionTable(supervisionTable);
		List<DataStatisticsForSupervision> dataStatisticsForSupervisions = new ArrayList<>();
		dataStatisticsForSupervisions.add(ddataStatisticsForSupervisionResult);
		page.setList(dataStatisticsForSupervisions);
		return page;
	}

	public String officeOccupancy(SupervisionTable supervisionTable, String occupancyClassification) {
		supervisionTable.setOccupancyClassification(occupancyClassification);
		List<Map<String, Object>> list = dataStatisticsDao.officeOccupancy(supervisionTable);

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			Map<String, Object> stringObjectMap = list.get(0);
			Map<String, Object> map1 = new HashMap<>();
			map1.put("name", "抽查数");
			map1.put("value", stringObjectMap.get("SpotNumber"));
			Map<String, Object> map2 = new HashMap<>();
			map2.put("name", "问题数");
			map2.put("value", stringObjectMap.get("questionNumber"));
			Map<String, Object> map3 = new HashMap<>();
			map3.put("name", "已整改数");
			map3.put("value", stringObjectMap.get("correctedNumber"));
			Map<String, Object> map4 = new HashMap<>();
			map4.put("name", "未整改数");
			map4.put("value", stringObjectMap.get("uncorrectedNumber"));

			List<Map<String, Object>> newList = new ArrayList<>();
			newList.add(map1);
			newList.add(map2);
			newList.add(map3);
			newList.add(map4);
			return JSON.toJSONString(newList);
		}
	}

	public Page<ResourceCase> findResourceCaseData(ResourceCase resourceCase) {
		Page<ResourceCase> page = (Page<ResourceCase>) resourceCase.getPage();
		List<ResourceCase> list = dataStatisticsDao.findResourceCaseData(resourceCase);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public String officeResourceCout(ResourceCase resourceCase, String officeType) {
		List<Map<String, Object>> list = dataStatisticsDao.officeResourceCout(resourceCase);

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			List<Map<String, Object>> newList = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				if (stringObjectMap.get("officeType") == null){
					Map<String, Object> map = new HashMap<>();
					map.put("name", "其他");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				}else if (stringObjectMap.get("officeType").toString().equals("0")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "办公室用房");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else if (stringObjectMap.get("officeType").toString().equals("1")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "服务用房");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else if (stringObjectMap.get("officeType").toString().equals("2")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "设备用房");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else if (stringObjectMap.get("officeType").toString().equals("3")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "附属用房");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else if (stringObjectMap.get("officeType").toString().equals("4")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "技术用房");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "其他");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				}
			}
			return JSON.toJSONString(newList);
		}
	}

	public String officeResourceNumberCout(ResourceCase resourceCase) {
		List<Map<String, Object>> list = dataStatisticsDao.officeResourceNumberCout(resourceCase.getOfficeType(), resourceCase.getOfficeCode());

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			Map<String, Object> map = new HashMap<>();
			Map<String, Object> stringObjectMap = list.get(0);

			List<Map<String, Object>> newList1 = new ArrayList<>();
			Map<String, Object> map1 = new HashMap<>();
			map1.put("value", stringObjectMap.get("number"));
			newList1.add(map1);

			List<Map<String, Object>> newList2 = new ArrayList<>();
			Map<String, Object> map2 = new HashMap<>();
			map2.put("value", stringObjectMap.get("area"));
			newList2.add(map2);

			List<Map<String, Object>> newList3 = new ArrayList<>();
			Map<String, Object> map3 = new HashMap<>();
			map3.put("value", stringObjectMap.get("officeNumber"));
			newList3.add(map3);

			map.put("number", newList1);
			map.put("area", newList2);
			map.put("officeNumber", newList3);
			return JSON.toJSONString(map);
		}
	}

	public String ownershipCount(OwnershipTable ownershipTable) {
		List<Map<String, Object>> list = dataStatisticsDao.ownershipCount(ownershipTable.getAreaCode(), ownershipTable.getYear());

//		if (list.size() == 0) {
//			return JSON.toJSONString(list);
//		} else {
			Map<String, Object> map = new HashMap<>();
			List<String> data1 = new ArrayList<>();
			List<String> data2 = new ArrayList<>();
			Map<String, Object> data3 = new HashMap<>();
			List<String> data31 = new ArrayList<>();
			Map<String, Object> data4 = new HashMap<>();
			List<String> data41 = new ArrayList<>();
			Map<String, Object> data5 = new HashMap<>();
			List<String> data51 = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				String areaName = stringObjectMap.get("areaName").toString();
				String number = stringObjectMap.get("number").toString();
				String awaitNumber = stringObjectMap.get("awaitNumber").toString();
				String approvedNumber = stringObjectMap.get("approvedNumber").toString();

				//区域
				data2.add(areaName);

				data31.add(number);
				data41.add(awaitNumber);
				data51.add(approvedNumber);
			}

			data3.put("name", "总数");
			data3.put("value", data31);

			data4.put("name", "待审批");
			data4.put("value", data41);

			data5.put("name", "已审批");
			data5.put("value", data51);

			map.put("data2", data2.stream()
					.distinct()
					.collect(Collectors.toList()));
			map.put("data3", data3);
			map.put("data4", data4);
			map.put("data5", data5);
			return JSON.toJSONString(map);
//		}
	}

	public String allocationCountData(AllocationTable allocationTable) {
		List<Map<String, Object>> list = dataStatisticsDao.allocationCountData(allocationTable.getAreaCode(), allocationTable.getYear());

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			List<Map<String, Object>> newList = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				if (stringObjectMap.get("arrangeType").equals("1")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "调剂数量");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else if (stringObjectMap.get("arrangeType").equals("2")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "置换数量");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else if (stringObjectMap.get("arrangeType").equals("3")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "租用数量");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else if (stringObjectMap.get("arrangeType").equals("4")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "建设数量");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				} else {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "其他");
					map.put("value", stringObjectMap.get("number"));
					newList.add(map);
				}
			}
			return JSON.toJSONString(newList);
		}
	}

	public String allocationNumberCountData(AllocationTable allocationTable) {
		List<Map<String, Object>> list = dataStatisticsDao.allocationCountData(allocationTable.getAreaCode(), allocationTable.getYear());

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			Map<String, Object> map = new HashMap<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				if (stringObjectMap.get("arrangeType").equals("1")) {
					List<Map<String, Object>> newList = new ArrayList<>();
					Map<String, Object> map1 = new HashMap<>();
					map1.put("value", stringObjectMap.get("number"));
					newList.add(map1);
					map.put("number1", newList);
				}else if (stringObjectMap.get("arrangeType").equals("2")) {
					List<Map<String, Object>> newList = new ArrayList<>();
					Map<String, Object> map1 = new HashMap<>();
					map1.put("value", stringObjectMap.get("number"));
					newList.add(map1);
					map.put("number2", newList);
				}else if (stringObjectMap.get("arrangeType").equals("3")) {
					List<Map<String, Object>> newList = new ArrayList<>();
					Map<String, Object> map1 = new HashMap<>();
					map1.put("value", stringObjectMap.get("number"));
					newList.add(map1);
					map.put("number3", newList);
				}else if (stringObjectMap.get("arrangeType").equals("4")) {
					List<Map<String, Object>> newList = new ArrayList<>();
					Map<String, Object> map1 = new HashMap<>();
					map1.put("value", stringObjectMap.get("number"));
					newList.add(map1);
					map.put("number4", newList);
				}
			}
			return JSON.toJSONString(map);
		}
	}

	public String allocationAreaCountData(AllocationTable allocationTable) {
		List<Map<String, Object>> list = dataStatisticsDao.allocationAreaCountData(allocationTable.getAreaCode(), allocationTable.getYear());

//		if (list.size() == 0) {
//			return JSON.toJSONString(list);
//		} else {
			Map<String, Object> map = new HashMap<>();
			List<String> data1 = new ArrayList<>();
			List<String> data2 = new ArrayList<>();
			Map<String, Object> data3 = new HashMap<>();
			List<String> data31 = new ArrayList<>();
			Map<String, Object> data4 = new HashMap<>();
			List<String> data41 = new ArrayList<>();
			Map<String, Object> data5 = new HashMap<>();
			List<String> data51 = new ArrayList<>();
			Map<String, Object> data6 = new HashMap<>();
			List<String> data61 = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				String areaName = stringObjectMap.get("areaName").toString();
				String number = stringObjectMap.get("number").toString();
				String awaitNumber = stringObjectMap.get("awaitNumber").toString();
				String approvedNumber = stringObjectMap.get("approvedNumber").toString();
				String preNumber = stringObjectMap.get("preNumber").toString();

				//区域
				data2.add(areaName);

				data31.add(number);
				data41.add(awaitNumber);
				data51.add(approvedNumber);
				data61.add(preNumber);
			}

			data3.put("name", "总数");
			data3.put("value", data31);

			data4.put("name", "待审批");
			data4.put("value", data41);

			data5.put("name", "已审批");
			data5.put("value", data51);

			data6.put("name", "同比变化率");
			data6.put("value", data61);

			map.put("data2", data2.stream()
					.distinct()
					.collect(Collectors.toList()));
			map.put("data3", data3);
			map.put("data4", data4);
			map.put("data5", data5);
			map.put("data6", data6);
			return JSON.toJSONString(map);
//		}
	}

	public String maintainNumberCountData(MaintainTable maintainTable) {
		List<Map<String, Object>> list = dataStatisticsDao.maintainNumberCountData(maintainTable.getAreaCode(), maintainTable.getYear());

//		if (list.size() == 0) {
//			return JSON.toJSONString(list);
//		} else {
			Map<String, Object> map = new HashMap<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				if (stringObjectMap.get("type").equals("2")) {
					List<Map<String, Object>> newList = new ArrayList<>();
					Map<String, Object> map1 = new HashMap<>();
					map1.put("value", stringObjectMap.get("number"));
					newList.add(map1);
					map.put("number1", newList);
				}else if (stringObjectMap.get("type").equals("3")) {
					List<Map<String, Object>> newList = new ArrayList<>();
					Map<String, Object> map1 = new HashMap<>();
					map1.put("value", stringObjectMap.get("number"));
					newList.add(map1);
					map.put("number2", newList);
				}else if (stringObjectMap.get("type").equals("8")) {
					List<Map<String, Object>> newList = new ArrayList<>();
					Map<String, Object> map1 = new HashMap<>();
					map1.put("value", stringObjectMap.get("number"));
					newList.add(map1);
					map.put("number3", newList);
				}else {
					List<Map<String, Object>> newList = new ArrayList<>();
					Map<String, Object> map1 = new HashMap<>();
					map1.put("value", stringObjectMap.get("number"));
					newList.add(map1);
					map.put("number4", newList);
				}
			}
			return JSON.toJSONString(map);
//		}
	}

	public String maintainCountData(MaintainTable maintainTable) {
		List<Map<String, Object>> list = dataStatisticsDao.maintainNumberCountData(maintainTable.getAreaCode(), maintainTable.getYear());

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			List<Map<String, Object>> newList = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				if (stringObjectMap.get("type").equals("2")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "水箱维修");
					map.put("value", stringObjectMap.get("total"));
					newList.add(map);
				} else if (stringObjectMap.get("type").equals("3")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "照明维修");
					map.put("value", stringObjectMap.get("total"));
					newList.add(map);
				} else if (stringObjectMap.get("type").equals("8")) {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "煤气管道维修");
					map.put("value", stringObjectMap.get("total"));
					newList.add(map);
				} else {
					Map<String, Object> map = new HashMap<>();
					map.put("name", "其他维修");
					map.put("value", stringObjectMap.get("total"));
					newList.add(map);
				}
			}
			return JSON.toJSONString(newList);
		}
	}

	public String calculateUsageAreaRatioByUnit(UseTable useTable) {
		List<Map<String, Object>> list = dataStatisticsDao.calculateUsageAreaRatioByUnit(useTable);

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			Map<String, Object> dataMap = new HashMap<>();
			List<String> categoryList = new ArrayList<>();
			List<String> dataList = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				if(stringObjectMap!=null){
					categoryList.add(stringObjectMap.get("areaName").toString());
					dataList.add(stringObjectMap.get("area").toString());
				}
			}
			dataMap.put("categoryList",categoryList);
			dataMap.put("dataList",dataList);
			return JSON.toJSONString(dataMap);
		}
	}

	public String calculateUsageAreaRatioByRank(UseTable useTable) {
		List<Map<String, Object>> list = dataStatisticsDao.calculateUsageAreaRatioByRank(useTable);
		List<Map<String, Object>> dataList = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> map = new HashMap<>();
			Map<String, Object> stringObjectMap = list.get(i);
			String area = stringObjectMap.get("area").toString();
			map.put("value", area);
			if(stringObjectMap.get("type")!=null) {
				String type = stringObjectMap.get("type").toString();
				map.put("name", type);
			}else{
				map.put("name", "未知");
			}
			dataList.add(map);
		}

		return JSON.toJSONString(dataList);
	}

	public String analyzeOfficeSpaceUsageStatus(UseTable useTable) {
		List<Map<String, Object>> list = dataStatisticsDao.analyzeOfficeSpaceUsageStatus(useTable);

//		if (list.size() == 0) {
//			return JSON.toJSONString(list);
//		} else {
			Map<String, Object> map = new HashMap<>();
			List<String> data1 = new ArrayList<>();
			List<String> data2 = new ArrayList<>();
			Map<String, Object> data3 = new HashMap<>();
			List<String> data31 = new ArrayList<>();
			Map<String, Object> data4 = new HashMap<>();
			List<String> data41 = new ArrayList<>();
			Map<String, Object> data5 = new HashMap<>();
			List<String> data51 = new ArrayList<>();
			Map<String, Object> data6 = new HashMap<>();
			List<String> data61 = new ArrayList<>();
			Map<String, Object> data7 = new HashMap<>();
			List<String> data71 = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				String areaName = stringObjectMap.get("areaName").toString();
				String officeArea = stringObjectMap.get("officeArea").toString();
				String serviceArea = stringObjectMap.get("serviceArea").toString();
				String equipmentArea = stringObjectMap.get("equipmentArea").toString();
				String techArea = stringObjectMap.get("techArea").toString();
				String auxiliaryArea = stringObjectMap.get("auxiliaryArea").toString();

				//区域
				data2.add(areaName);

				data31.add(officeArea);
				data41.add(serviceArea);
				data51.add(equipmentArea);
				data61.add(techArea);
				data71.add(auxiliaryArea);
			}

			data3.put("name", "办公室使用面积");
			data3.put("value", data31);

			data4.put("name", "设备用房使用面积");
			data4.put("value", data41);

			data5.put("name", "服务用房使用面积");
			data5.put("value", data51);

			data6.put("name", "技术用房使用面积");
			data6.put("value", data61);

			data7.put("name", "附属用房使用面积");
			data7.put("value", data71);

			map.put("data2", data2.stream()
					.distinct()
					.collect(Collectors.toList()));
			map.put("data3", data3);
			map.put("data4", data4);
			map.put("data5", data5);
			map.put("data6", data6);
			map.put("data7", data7);
			return JSON.toJSONString(map);
//		}
	}

	public String analyzeOfficeSpaceAnalysis(UseTable useTable) {
		List<Map<String, Object>> list = dataStatisticsDao.analyzeOfficeSpaceAnalysis(useTable);

		Map<String, Object> map = new HashMap<>();
		List<String> data1 = new ArrayList<>();
		List<String> data2 = new ArrayList<>();
		Map<String, Object> data3 = new HashMap<>();
		List<String> data31 = new ArrayList<>();
		Map<String, Object> data4 = new HashMap<>();
		List<String> data41 = new ArrayList<>();

		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> stringObjectMap = list.get(i);
			String areaName = stringObjectMap.get("areaName").toString();
			String employeeCount = stringObjectMap.get("employeeCount").toString();
			String area = stringObjectMap.get("area").toString();

			//区域
			data2.add(areaName);

			data31.add(employeeCount);
			data41.add(area);
		}

		data3.put("name", "超标人数");
		data3.put("value", data31);

		data4.put("name", "超标面积");
		data4.put("value", data41);

		map.put("data2", data2.stream()
				.distinct()
				.collect(Collectors.toList()));
		map.put("data3", data3);
		map.put("data4", data4);
		return JSON.toJSONString(map);
	}

	public String officeUsageStats(UseTable useTable) {
		List<Map<String, Object>> list = dataStatisticsDao.officeUsageStats(useTable);

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			List<Map<String, Object>> data = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				String number = stringObjectMap.get("number").toString();
				Map<String, Object> map1 = new HashMap<>();
				map1.put("name", "使用申请总数");
				map1.put("value", number);
				data.add(map1);
				if(stringObjectMap.get("awaitNumber")!=null) {
					String awaitNumber = stringObjectMap.get("awaitNumber").toString();
					Map<String, Object> map2 = new HashMap<>();
					map2.put("name", "待审批数");
					map2.put("value", awaitNumber);
					data.add(map2);
				}else{
					Map<String, Object> map2 = new HashMap<>();
					map2.put("name", "待审批数");
					map2.put("value", 0);
					data.add(map2);
				}
				if(stringObjectMap.get("issueNumber")!=null) {
					String issueNumber = stringObjectMap.get("issueNumber").toString();
					Map<String, Object> map3 = new HashMap<>();
					map3.put("name", "核发证件数");
					map3.put("value", issueNumber);
					data.add(map3);
				}else{
					Map<String, Object> map3 = new HashMap<>();
					map3.put("name", "核发证件数");
					map3.put("value", 0);
					data.add(map3);
				}
				if(stringObjectMap.get("availableNumber")!=null) {
					String availableNumber = stringObjectMap.get("availableNumber").toString();
					Map<String, Object> map4 = new HashMap<>();
					map4.put("name", "用房安排数");
					map4.put("value", availableNumber);
					data.add(map4);
				}else{
					Map<String, Object> map4 = new HashMap<>();
					map4.put("name", "用房安排数");
					map4.put("value", 0);
					data.add(map4);
				}
			}

			return JSON.toJSONString(data);
		}
	}
	public String officeUsageStatsCount(UseTable useTable) {
		List<Map<String, Object>> list = dataStatisticsDao.officeUsageStats(useTable);

//		if (list.size() == 0) {
//			return JSON.toJSONString(list);
//		} else {
			Map<String, Object> map = new HashMap<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				String number = stringObjectMap.get("number").toString();
				Map<String, Object> map1 = new HashMap<>();
				map.put("number", number);
				if(stringObjectMap.get("awaitNumber")!=null) {
					String awaitNumber = stringObjectMap.get("awaitNumber").toString();
					map.put("awaitNumber", awaitNumber);
				}else{
					map.put("awaitNumber", 0);
				}
				if(stringObjectMap.get("availableNumber")!=null) {
					String availableNumber = stringObjectMap.get("availableNumber").toString();
					map.put("availableNumber", availableNumber);
				}else{
					map.put("availableNumber", 0);
				}
				if(stringObjectMap.get("issueNumber")!=null) {
					String issueNumber = stringObjectMap.get("issueNumber").toString();
					map.put("issueNumber", issueNumber);
				}else{
					map.put("issueNumber", 0);
				}
			}

			return JSON.toJSONString(map);
//		}
	}

	public String allocationAreaCountBarData(AllocationTable allocationTable) {
		List<Map<String, Object>> list = dataStatisticsDao.allocationAreaCountBarData(allocationTable.getAreaCode(), allocationTable.getYear());

//		if (list.size() == 0) {
//			return JSON.toJSONString(list);
//		} else {
			Map<String, Object> map = new HashMap<>();
			List<String> data1 = new ArrayList<>();
			List<String> data2 = new ArrayList<>();
			Map<String, Object> data3 = new HashMap<>();
			List<String> data31 = new ArrayList<>();
			Map<String, Object> data4 = new HashMap<>();
			List<String> data41 = new ArrayList<>();
			Map<String, Object> data5 = new HashMap<>();
			List<String> data51 = new ArrayList<>();
			Map<String, Object> data6 = new HashMap<>();
			List<String> data61 = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				String areaName = stringObjectMap.get("areaName").toString();
				String adjustmentQuantity = stringObjectMap.get("adjustmentQuantity").toString();
				String replacementQuantity = stringObjectMap.get("replacementQuantity").toString();
				String rentalQuantity = stringObjectMap.get("rentalQuantity").toString();
				String constructionQuantity = stringObjectMap.get("constructionQuantity").toString();

				//区域
				data2.add(areaName);

				data31.add(adjustmentQuantity);
				data41.add(replacementQuantity);
				data51.add(rentalQuantity);
				data61.add(constructionQuantity);
			}

			data3.put("name", "调剂数量");
			data3.put("value", data31);

			data4.put("name", "置换数量");
			data4.put("value", data41);

			data5.put("name", "租用数量");
			data5.put("value", data51);

			data6.put("name", "建设数量");
			data6.put("value", data61);

			map.put("data2", data2.stream()
					.distinct()
					.collect(Collectors.toList()));
			map.put("data3", data3);
			map.put("data4", data4);
			map.put("data5", data5);
			map.put("data6", data6);
			return JSON.toJSONString(map);
//		}
	}

	public List<UnuseTable> exportUnuseData(UnuseTable unuseTable) {
		return dataStatisticsDao.findUnusePage(unuseTable);
	}

	public List<SupervisionTable> exportSupervisionListData(SupervisionTable supervisionTable) {
		return dataStatisticsDao.countSupervisionNumber(supervisionTable);
	}

	public List<SpaceVerification> exportSpaceVerificationData(SpaceVerification spaceVerification) {
		return dataStatisticsDao.findSpaceVerificationData(spaceVerification);
	}

	public String maintainCosAanalysis(MaintainTable maintainTable) {
		List<Map<String, Object>> list = dataStatisticsDao.maintainNumberCountData(maintainTable.getAreaCode(), maintainTable.getYear());

		if (list.size() == 0) {
			return JSON.toJSONString(list);
		} else {
			List<String> newList = new ArrayList<>();
			List<String> dataList = new ArrayList<>();
			Integer total = new Integer(0);
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> stringObjectMap = list.get(i);
				if (stringObjectMap.get("type").equals("2")) {
					newList.add("水箱维修费用");
					dataList.add(stringObjectMap.get("total").toString());
				} else if (stringObjectMap.get("type").equals("3")) {
					newList.add("照明维修费用");
					dataList.add(stringObjectMap.get("total").toString());
				} else if (stringObjectMap.get("type").equals("8")) {
					newList.add("煤气管道维修费用");
					dataList.add(stringObjectMap.get("total").toString());
				} else {
					total += Integer.parseInt(stringObjectMap.get("total").toString());
				}
			}
			newList.add("其他维修费用");
			dataList.add(total.toString());
			Map<String, Object> map = new HashMap<>();
			map.put("newList", newList);
			map.put("dataList", dataList);
			return JSON.toJSONString(map);
		}
	}

	public void addDataScopeFilter(UnuseTable unuseTable) {
		SqlMap sqlMap = unuseTable.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"o.OFFICE_CODE",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}

	public void addDataScopeFilter(SupervisionTable supervisionTable) {
		SqlMap sqlMap = supervisionTable.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"jso.OFFICE_CODE",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}
}