package com.hsobs.ob.modules.repair.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

/**
 * 维修申请Entity
 * <AUTHOR>
 * @version 2025-02-06
 */
public class RepairRequestLedger extends BpmEntity<RepairRequestLedger> {

	private static final long serialVersionUID = 1L;
	private String officeName;		// 维修申请单位
	private String officeCode;		// 维修申请单位
	private String maintenanceType;		// 维修类型
	private String maintenanceAmount;		// 维修金额
	private String maintenanceTotalArea;		// 维修总建筑面积

	@ExcelFields({
			@ExcelField(title="维修申请单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="维修类型", attrName="maintenanceType", dictType="ob_repair_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="维修金额", attrName="maintenanceAmount", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="维修总建筑面积", attrName="maintenanceTotalArea", align= ExcelField.Align.CENTER, sort=50),
	})
	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getMaintenanceType() {
		return maintenanceType;
	}

	public void setMaintenanceType(String maintenanceType) {
		this.maintenanceType = maintenanceType;
	}

	public String getMaintenanceAmount() {
		return maintenanceAmount;
	}

	public void setMaintenanceAmount(String maintenanceAmount) {
		this.maintenanceAmount = maintenanceAmount;
	}

	public String getMaintenanceTotalArea() {
		return maintenanceTotalArea;
	}

	public void setMaintenanceTotalArea(String maintenanceTotalArea) {
		this.maintenanceTotalArea = maintenanceTotalArea;
	}
}