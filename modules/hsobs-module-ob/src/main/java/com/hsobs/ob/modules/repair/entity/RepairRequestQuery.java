package com.hsobs.ob.modules.repair.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 维修申请Entity
 * <AUTHOR>
 * @version 2025-02-06
 */
public class RepairRequestQuery extends BpmEntity<RepairRequestQuery> {

	private static final long serialVersionUID = 1L;
	private String officeName;		// 申请单位
	private String officeCode;		// 申请单位
	private String maintenanceOrderNumber;  // 维修单据号
	private String maintenanceType;         // 维修类型
	private Date applyDate;                 // 申请日期
	private String maintenanceProjectName;  // 维修工程名称
	private String officeRoomName;          // 办公用房名称
	private Double maintenanceArea;         // 维修面积
	private Double budgetAmount;            // 预算金额
	private Date commenceDate;            // 开工日期
	private Date completionDate;            // 竣工日期
	private String approvalStatus;          // 审批状态
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	public Date getCommenceDate() {
		return commenceDate;
	}

	public void setCommenceDate(Date commenceDate) {
		this.commenceDate = commenceDate;
	}

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}

	@ExcelFields({
			@ExcelField(title="维修单据号", attrName="maintenanceOrderNumber", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="维修类型", attrName="maintenanceType", dictType="ob_repair_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="申请日期", attrName="applyDate", align= ExcelField.Align.CENTER, sort=31),
			@ExcelField(title="申请单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=32),
			@ExcelField(title="维修工程名称", attrName="maintenanceProjectName", align= ExcelField.Align.CENTER, sort=33),
			@ExcelField(title="办公用房名称", attrName="officeRoomName", align= ExcelField.Align.CENTER, sort=34),
			@ExcelField(title="维修面积", attrName="maintenanceArea", align= ExcelField.Align.CENTER, sort=35),
			@ExcelField(title="预算金额", attrName="budgetAmount", align= ExcelField.Align.CENTER, sort=36),
			@ExcelField(title="开工日期", attrName="commenceDate", align= ExcelField.Align.CENTER, sort=37),
			@ExcelField(title="竣工日期", attrName="completionDate", align= ExcelField.Align.CENTER, sort=37),
			@ExcelField(title="审批状态", attrName="approvalStatus", dictType="sys_office_type", align= ExcelField.Align.CENTER, sort=38),
	})

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getMaintenanceOrderNumber() {
		return maintenanceOrderNumber;
	}

	public void setMaintenanceOrderNumber(String maintenanceOrderNumber) {
		this.maintenanceOrderNumber = maintenanceOrderNumber;
	}

	public String getMaintenanceType() {
		return maintenanceType;
	}

	public void setMaintenanceType(String maintenanceType) {
		this.maintenanceType = maintenanceType;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public String getMaintenanceProjectName() {
		return maintenanceProjectName;
	}

	public void setMaintenanceProjectName(String maintenanceProjectName) {
		this.maintenanceProjectName = maintenanceProjectName;
	}

	public String getOfficeRoomName() {
		return officeRoomName;
	}

	public void setOfficeRoomName(String officeRoomName) {
		this.officeRoomName = officeRoomName;
	}

	public Double getMaintenanceArea() {
		return maintenanceArea;
	}

	public void setMaintenanceArea(Double maintenanceArea) {
		this.maintenanceArea = maintenanceArea;
	}

	public Double getBudgetAmount() {
		return budgetAmount;
	}

	public void setBudgetAmount(Double budgetAmount) {
		this.budgetAmount = budgetAmount;
	}

	public Date getCompletionDate() {
		return completionDate;
	}

	public void setCompletionDate(Date completionDate) {
		this.completionDate = completionDate;
	}

	public String getApprovalStatus() {
		return approvalStatus;
	}

	public void setApprovalStatus(String approvalStatus) {
		this.approvalStatus = approvalStatus;
	}
}