package com.hsobs.ob.modules.ownership.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

/**
 * 权属登记协调记录Entity
 * <AUTHOR>
 * @version 2025-03-09
 */
@Table(name="ob_ownership_coordination_record", alias="a", label="权属登记协调记录信息", columns={
		@Column(name="id", attrName="id", label="主键", isPK=true),
		@Column(name="ownership_number", attrName="ownershipNumber", label="权属登记单号", queryType=QueryType.LIKE),
		@Column(name="ownership_type", attrName="ownershipType", label="权属登记类型", queryType=QueryType.EQ),
		@Column(name="apply_office_code", attrName="applyOfficeCode", label="申请单位", queryType=QueryType.EQ),
		@Column(name="owner_user_name", attrName="ownerUserName", label="产权人名称", queryType=QueryType.LIKE),
		@Column(name="property_certificate_number", attrName="propertyCertificateNumber", label="房产证号"),
		@Column(name="land_certificate_number", attrName="landCertificateNumber", label="土地证号"),
		@Column(name="user_by", attrName="userBy", label="使用人"),
		@Column(name="coordination_date", attrName="coordinationDate", label="协调日期", isUpdateForce=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
		@Column(name="usage_type", attrName="usageType", label="房产用途", isQuery=false),
	}, joinTable={
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o1",
				attrName = "applyOffice",
				on = "a.apply_office_code = o1.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u1",
				attrName = "usedUser",
				on = "a.user_by = u1.user_code",
				columns = {@Column(includeEntity = User.class)}),
	}, orderBy="a.update_date DESC"
)
public class OwnershipCoordinationRecord extends DataEntity<OwnershipCoordinationRecord> {
	
	private static final long serialVersionUID = 1L;
	private String ownershipNumber;		// 权属登记单号
	private String ownershipType;		// 权属登记类型
	private String applyOfficeCode;		// 申请单位
	private String ownerUserName;		// 产权人名称
	private String propertyCertificateNumber;		// 房产证号
	private String landCertificateNumber;		// 土地证号
	private String userBy;		// 使用人
	private Date coordinationDate;		// 协调日期
	private String usageType; // 房产用途

	private Office applyOffice;
	private User usedUser;


    @ExcelFields({
            @ExcelField(title="权属登记单号", attrName="ownershipNumber", align= ExcelField.Align.CENTER, sort=20),
            @ExcelField(title="权属登记类型", attrName="ownershipType", dictType="ob_ownership_registration_type", align= ExcelField.Align.CENTER, sort=30),
            @ExcelField(title="申请单位", attrName="applyOffice.officeName", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="产权人名称", attrName="ownerUserName", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="房产证号", attrName="propertyCertificateNumber", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="土地证号", attrName="landCertificateNumber", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="使用人", attrName="usedUser.userName", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="协调日期", attrName="coordinationDate", align= ExcelField.Align.CENTER, sort=40, dataFormat="yyyy-MM-dd hh:mm"),
    })
	public OwnershipCoordinationRecord() {
		this(null);
	}
	
	public OwnershipCoordinationRecord(String id){
		super(id);
	}
	
	@NotBlank(message="权属登记单号不能为空")
	@Size(min=0, max=64, message="权属登记单号长度不能超过 64 个字符")
	public String getOwnershipNumber() {
		return ownershipNumber;
	}

	public void setOwnershipNumber(String ownershipNumber) {
		this.ownershipNumber = ownershipNumber;
	}
	
	@NotBlank(message="权属登记类型不能为空")
	@Size(min=0, max=64, message="权属登记类型长度不能超过 64 个字符")
	public String getOwnershipType() {
		return ownershipType;
	}

	public void setOwnershipType(String ownershipType) {
		this.ownershipType = ownershipType;
	}
	
	@NotBlank(message="申请单位不能为空")
	@Size(min=0, max=64, message="申请单位长度不能超过 64 个字符")
	public String getApplyOfficeCode() {
		return applyOfficeCode;
	}

	public void setApplyOfficeCode(String applyOfficeCode) {
		this.applyOfficeCode = applyOfficeCode;
	}
	
	@NotBlank(message="产权人名称不能为空")
	@Size(min=0, max=100, message="产权人名称长度不能超过 100 个字符")
	public String getOwnerUserName() {
		return ownerUserName;
	}

	public void setOwnerUserName(String ownerUserName) {
		this.ownerUserName = ownerUserName;
	}
	
	@Size(min=0, max=255, message="房产证号长度不能超过 255 个字符")
	public String getPropertyCertificateNumber() {
		return propertyCertificateNumber;
	}

	public void setPropertyCertificateNumber(String propertyCertificateNumber) {
		this.propertyCertificateNumber = propertyCertificateNumber;
	}
	
	@Size(min=0, max=255, message="土地证号长度不能超过 255 个字符")
	public String getLandCertificateNumber() {
		return landCertificateNumber;
	}

	public void setLandCertificateNumber(String landCertificateNumber) {
		this.landCertificateNumber = landCertificateNumber;
	}
	
	@Size(min=0, max=64, message="使用人长度不能超过 64 个字符")
	public String getUserBy() {
		return userBy;
	}

	public void setUserBy(String userBy) {
		this.userBy = userBy;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getCoordinationDate() {
		return coordinationDate;
	}

	public void setCoordinationDate(Date coordinationDate) {
		this.coordinationDate = coordinationDate;
	}

	public Office getApplyOffice() {
		return applyOffice;
	}

	public void setApplyOffice(Office applyOffice) {
		this.applyOffice = applyOffice;
	}

	public User getUsedUser() {
		return usedUser;
	}

	public void setUsedUser(User usedUser) {
		this.usedUser = usedUser;
	}

	public String getUsageType() {
		return usageType;
	}

	public void setUsageType(String usageType) {
		this.usageType = usageType;
	}
}