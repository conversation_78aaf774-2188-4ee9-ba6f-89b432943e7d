package com.hsobs.ob.modules.estate.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OfficeTechnicalBusinessAreaDto {
    private String officeCode; // 单位编码
    private String officeName; // 单位名称
    private Double technicalBusinessArea; // 技术业务用房核定面积
    private Double area; // 技术业务用房实际使用面积
    private Double overArea; // 超标面积


    @ExcelFields({
            @ExcelField(title="单位编码", attrName="officeCode", align= ExcelField.Align.CENTER, sort=10),
            @ExcelField(title="单位名称", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
            @ExcelField(title="技术业务用房核定面积（㎡）", attrName="technicalBusinessArea", align= ExcelField.Align.CENTER, sort=30),
            @ExcelField(title="实际使用面积（㎡）", attrName="area", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="超标面积（㎡）", attrName="overArea", align= ExcelField.Align.CENTER, sort=50),
    })
    public OfficeTechnicalBusinessAreaDto() {}
}
