package com.hsobs.ob.modules.ownership.service;

import java.util.List;

import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.ownership.entity.OwnershipRecord;
import com.hsobs.ob.modules.ownership.dao.OwnershipRecordDao;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 权属备案Service
 * <AUTHOR>
 * @version 2025-03-09
 */
@Service
public class OwnershipRecordService extends CrudService<OwnershipRecordDao, OwnershipRecord> {

	@Autowired
	OwnershipRecordDao ownershipRecordDao;

	/**
	 * 获取单条数据
	 * @param ownershipRecord
	 * @return
	 */
	@Override
	public OwnershipRecord get(OwnershipRecord ownershipRecord) {
		return super.get(ownershipRecord);
	}
	
	/**
	 * 查询分页数据
	 * @param ownershipRecord 查询条件
	 * @param ownershipRecord page 分页对象
	 * @return
	 */
	@Override
	public Page<OwnershipRecord> findPage(OwnershipRecord ownershipRecord) {
		return super.findPage(ownershipRecord);
	}
	
	/**
	 * 查询列表数据
	 * @param ownershipRecord
	 * @return
	 */
	@Override
	public List<OwnershipRecord> findList(OwnershipRecord ownershipRecord) {
		return super.findList(ownershipRecord);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param ownershipRecord
	 */
	@Override
	@Transactional
	public void save(OwnershipRecord ownershipRecord) {
		if (ownershipRecord.isReport()) {
			ownershipRecord.setRecordStatus("2");
		}
		super.save(ownershipRecord);

		FileUploadUtils.saveFileUpload(ownershipRecord, ownershipRecord.getId(), "ownershipRecord_file");
	}

	@Async
	@Transactional
	public void asyncUpdateStatus(OwnershipRecord record) {
		try {
			Thread.sleep(5000);
			record = super.get(record.getId());
			record.setRecordStatus("3");
			super.save(record);

		} catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			logger.error("状态更新任务被中断", e);
		} catch (Exception e) {
			logger.error("状态更新失败", e);
		}
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<OwnershipRecord> list = ei.getDataList(OwnershipRecord.class);
			for (OwnershipRecord ownershipRecord : list) {
				try{
					ValidatorUtils.validateWithException(ownershipRecord);
					this.save(ownershipRecord);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + ownershipRecord.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + ownershipRecord.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
	/**
	 * 更新状态
	 * @param ownershipRecord
	 */
	@Override
	@Transactional
	public void updateStatus(OwnershipRecord ownershipRecord) {
		super.updateStatus(ownershipRecord);
	}
	
	/**
	 * 删除数据
	 * @param ownershipRecord
	 */
	@Override
	@Transactional
	public void delete(OwnershipRecord ownershipRecord) {
		ownershipRecordDao.phyDelete(ownershipRecord);
	}
	
}