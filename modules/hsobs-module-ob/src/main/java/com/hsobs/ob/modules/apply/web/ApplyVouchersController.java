package com.hsobs.ob.modules.apply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.apply.entity.ApplyVouchers;
import com.hsobs.ob.modules.apply.service.ApplyVouchersService;

/**
 * 使用凭证表Controller
 * <AUTHOR>
 * @version 2025-03-13
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/vouchers")
public class ApplyVouchersController extends BaseController {

	@Autowired
	private ApplyVouchersService applyVouchersService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ApplyVouchers get(String id, boolean isNewRecord) {
		return applyVouchersService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("apply:vouchers:view")
	@RequestMapping(value = {"list", ""})
	public String list(ApplyVouchers applyVouchers, Model model) {
		model.addAttribute("applyVouchers", applyVouchers);
		return "modules/apply/applyVouchersList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("apply:vouchers:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<ApplyVouchers> listData(ApplyVouchers applyVouchers, HttpServletRequest request, HttpServletResponse response) {
		applyVouchers.setPage(new Page<>(request, response));
		if (null != applyVouchers.getBeginDate()) {
			applyVouchers.sqlMap().getWhere().and("signing_date", QueryType.GTE, applyVouchers.getBeginDate());
		}
		if (null != applyVouchers.getEndDate()) {
			applyVouchers.sqlMap().getWhere().and("signing_date", QueryType.LTE, applyVouchers.getEndDate());
		}
		Page<ApplyVouchers> page = applyVouchersService.findPage(applyVouchers);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("apply:vouchers:view")
	@RequestMapping(value = "form")
	public String form(ApplyVouchers applyVouchers, Model model) {
		model.addAttribute("applyVouchers", applyVouchers);
		return "modules/apply/applyVouchersForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("apply:vouchers:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated ApplyVouchers applyVouchers) {
		applyVouchersService.save(applyVouchers);
		return renderResult(Global.TRUE, text("保存使用凭证表成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("apply:vouchers:view")
	@RequestMapping(value = "exportData")
	public void exportData(ApplyVouchers applyVouchers, HttpServletResponse response) {
		List<ApplyVouchers> list = applyVouchersService.findList(applyVouchers);
		String fileName = "使用凭证" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("使用凭证", ApplyVouchers.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("apply:vouchers:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		ApplyVouchers applyVouchers = new ApplyVouchers();
		List<ApplyVouchers> list = ListUtils.newArrayList(applyVouchers);
		String fileName = "使用凭证模板.xlsx";
		try(ExcelExport ee = new ExcelExport("使用凭证", ApplyVouchers.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("apply:vouchers:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = applyVouchersService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("apply:vouchers:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(ApplyVouchers applyVouchers) {
		applyVouchersService.delete(applyVouchers);
		return renderResult(Global.TRUE, text("删除使用凭证成功！"));
	}
	
}