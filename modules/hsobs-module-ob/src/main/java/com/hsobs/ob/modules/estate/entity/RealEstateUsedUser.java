package com.hsobs.ob.modules.estate.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 不动产信息表Entity
 * <AUTHOR>
 * @version 2025-03-11
 */
@Table(name="ob_real_estate_used_user", alias="a", label="不动产信息表信息", columns={
        @Column(name="id", attrName="id", label="主键", isPK=true),
        @Column(name="real_estate_id", attrName="realEstateId.id", label="房间ID"),
        @Column(name="user_code", attrName="userCode", label="用户编码"),
        @Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
        @Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
        @Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
        @Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false),
}, orderBy="a.create_date ASC"
)
public class RealEstateUsedUser extends DataEntity<RealEstateUsedUser> {

    private static final long serialVersionUID = 1L;
    private RealEstate realEstateId;		// 房间ID 父类
    private String userCode;		// user_code

    public RealEstateUsedUser() {
        this(null);
    }

    public RealEstateUsedUser(RealEstate realEstateId){
        this.realEstateId = realEstateId;
    }

    public RealEstate getRealEstateId() {
        return realEstateId;
    }

    public void setRealEstateId(RealEstate realEstateId) {
        this.realEstateId = realEstateId;
    }

    @Size(min=0, max=64, message="user_code长度不能超过 64 个字符")
    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

}