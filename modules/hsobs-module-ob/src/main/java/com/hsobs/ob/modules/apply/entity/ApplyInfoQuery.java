package com.hsobs.ob.modules.apply.entity;


import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 使用管理Entity
 * <AUTHOR>
 * @version 2025-02-08
 */
public class ApplyInfoQuery extends BpmEntity<ApplyInfoQuery> {

	private static final long serialVersionUID = 1L;
	private String officeName;		//
	private String officeCode;		// 使用单位
	private String type;		// 房间类型
	private String describe;		// 申请原由
	private String purpose;		// 房间用途
	private String approvedArea;		// 核定面积
	private String actualArea;		// 实际面积
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	@ExcelFields({
			@ExcelField(title="使用单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=10),
			@ExcelField(title="房间类型", attrName="type", dictType="occupancy_classification", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="房间用途", attrName="purpose", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="申请原由", attrName="describe", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="核定面积", attrName="approvedArea",  align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="实际面积", attrName="actualArea", align= ExcelField.Align.CENTER, sort=40),
	})

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getDescribe() {
		return describe;
	}

	public void setDescribe(String describe) {
		this.describe = describe;
	}

	public String getApprovedArea() {
		return approvedArea;
	}

	public void setApprovedArea(String approvedArea) {
		this.approvedArea = approvedArea;
	}

	public String getActualArea() {
		return actualArea;
	}

	public void setActualArea(String actualArea) {
		this.actualArea = actualArea;
	}

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}
}