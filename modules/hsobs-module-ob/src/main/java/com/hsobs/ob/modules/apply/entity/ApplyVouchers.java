package com.hsobs.ob.modules.apply.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Area;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;

/**
 * 使用凭证表Entity
 * <AUTHOR>
 * @version 2025-03-13
 */
@Table(name="ob_apply_vouchers", alias="a", label="使用凭证表信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="used_office_code", attrName="usedOfficeCode", label="使用单位"),
		@Column(name="address", attrName="address", label="办公用房地址"),
		@Column(name="building_area", attrName="buildingArea", label="建筑面积"),
		@Column(name="registration_user_code", attrName="registrationUserCode", label="登记负责人"),
		@Column(name="handled_user_code", attrName="handledUserCode", label="经办人"),
		@Column(name="signing_date", attrName="signingDate", label="签订日期"),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
		@Column(name="protocol_status", attrName="protocolStatus", label="协议状态"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "usedOffice",
				on = "a.used_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u1",
				attrName = "registrationUser",
				on = "a.registration_user_code = u1.user_code",
				columns = {@Column(includeEntity = User.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = User.class, alias = "u2",
				attrName = "handledUser",
				on = "a.handled_user_code = u2.user_code",
				columns = {@Column(includeEntity = User.class)})
	}, orderBy="a.update_date DESC"
)
public class ApplyVouchers extends DataEntity<ApplyVouchers> {
	
	private static final long serialVersionUID = 1L;
	private String usedOfficeCode;		// 使用单位
	private String address;		// 办公用房地址
	private Double buildingArea;		// 建筑面积
	private String registrationUserCode;		// 登记负责人
	private String handledUserCode;		// 经办人
	private Date signingDate;		// 签订日期
	private String protocolStatus;		// 协议状态

	private Office usedOffice;
	private User registrationUser;
	private User handledUser;


	private Date beginDate;
	private Date endDate;
	@ExcelFields({
		@ExcelField(title="使用单位", attrName="usedOfficeCode", align=Align.CENTER, sort=20),
		@ExcelField(title="办公用房地址", attrName="address", align=Align.CENTER, sort=30),
//		@ExcelField(title="建筑面积", attrName="buildingArea", align=Align.CENTER, sort=40),
//		@ExcelField(title="登记负责人", attrName="registrationUserCode", align=Align.CENTER, sort=50),
//		@ExcelField(title="经办人", attrName="handledUserCode", align=Align.CENTER, sort=60),
		@ExcelField(title="签订日期", attrName="signingDate", align=Align.CENTER, sort=70, dataFormat="yyyy-MM-dd hh:mm"),
		@ExcelField(title="协议状态", attrName="protocolStatus", dictType="ob_apply_vouchers_status", align= ExcelField.Align.CENTER, sort=120),
	})
	public ApplyVouchers() {
		this(null);
	}
	
	public ApplyVouchers(String id){
		super(id);
	}
	
	@NotBlank(message="使用单位不能为空")
	@Size(min=0, max=64, message="使用单位长度不能超过 64 个字符")
	public String getUsedOfficeCode() {
		return usedOfficeCode;
	}

	public void setUsedOfficeCode(String usedOfficeCode) {
		this.usedOfficeCode = usedOfficeCode;
	}
	
	@NotBlank(message="办公用房地址不能为空")
	@Size(min=0, max=512, message="办公用房地址长度不能超过 512 个字符")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	
	@NotNull(message="建筑面积不能为空")
	public Double getBuildingArea() {
		return buildingArea;
	}

	public void setBuildingArea(Double buildingArea) {
		this.buildingArea = buildingArea;
	}
	
	@NotBlank(message="登记负责人不能为空")
	@Size(min=0, max=64, message="登记负责人长度不能超过 64 个字符")
	public String getRegistrationUserCode() {
		return registrationUserCode;
	}

	public void setRegistrationUserCode(String registrationUserCode) {
		this.registrationUserCode = registrationUserCode;
	}
	
	@NotBlank(message="经办人不能为空")
	@Size(min=0, max=64, message="经办人长度不能超过 64 个字符")
	public String getHandledUserCode() {
		return handledUserCode;
	}

	public void setHandledUserCode(String handledUserCode) {
		this.handledUserCode = handledUserCode;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="签订日期不能为空")
	public Date getSigningDate() {
		return signingDate;
	}

	public void setSigningDate(Date signingDate) {
		this.signingDate = signingDate;
	}
	
	@Size(min=0, max=1, message="协议状态长度不能超过 1 个字符")
	public String getProtocolStatus() {
		return protocolStatus;
	}

	public void setProtocolStatus(String protocolStatus) {
		this.protocolStatus = protocolStatus;
	}

	public Office getUsedOffice() {
		return usedOffice;
	}

	public void setUsedOffice(Office usedOffice) {
		this.usedOffice = usedOffice;
	}

	public User getRegistrationUser() {
		return registrationUser;
	}

	public void setRegistrationUser(User registrationUser) {
		this.registrationUser = registrationUser;
	}

	public User getHandledUser() {
		return handledUser;
	}

	public void setHandledUser(User handledUser) {
		this.handledUser = handledUser;
	}

	public Date getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(Date beginDate) {
		this.beginDate = beginDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
}