package com.hsobs.ob.modules.earlywarnmessage.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 房间面积超标查询Entity
 * <AUTHOR>
 * @version 2024-12-02
 */
public class UnitAreaExcessRecord extends DataEntity<UnitAreaExcessRecord> {

	private String officeName;           // 单位名称
	private String officeCode;           // 单位名称
	private String rank;               // 职级
	private String officeType;         // 机构类型
	private String officeRoomType;         // 办公用房类型
	private Double approvedArea;       // 核定面积
	private Double actualArea;         // 实际面积
	private Double excessArea;         // 超标面积

	public String getOfficeRoomType() {
		return officeRoomType;
	}

	public void setOfficeRoomType(String officeRoomType) {
		this.officeRoomType = officeRoomType;
	}

	@ExcelFields({
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="单位名称", attrName="officeName", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=20),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="机构类型", attrName="officeType", dictType="sys_office_type", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=40),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="办公用房类型", attrName="officeRoomType", dictType="occupancy_classification", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=40),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="核定面积", attrName="approvedArea", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=50),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="实际面积", attrName="actualArea", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=60),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="超标面积", attrName="excessArea", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=70),
		})

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getRank() {
		return rank;
	}

	public void setRank(String rank) {
		this.rank = rank;
	}

	public String getOfficeType() {
		return officeType;
	}

	public void setOfficeType(String officeType) {
		this.officeType = officeType;
	}

	public Double getApprovedArea() {
		return approvedArea;
	}

	public void setApprovedArea(Double approvedArea) {
		this.approvedArea = approvedArea;
	}

	public Double getActualArea() {
		return actualArea;
	}

	public void setActualArea(Double actualArea) {
		this.actualArea = actualArea;
	}

	public Double getExcessArea() {
		if((this.actualArea-this.approvedArea)>0){
			return this.actualArea-this.approvedArea;
		}else{
			return new Double(0);
		}
	}

	public void setExcessArea(Double excessArea) {
		this.excessArea = excessArea;
	}
}