package com.hsobs.ob.modules.arrange.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 配置管理Entity
 * <AUTHOR>
 * @version 2025-01-05
 */
public class RentInfoQuery extends BpmEntity<RentInfoQuery> {

	private static final long serialVersionUID = 1L;
	private String id;          // 申请id
	private String officeName;          // 申请单位
	private String roomInfo;          // 租借房屋档案
	private String officeCode;          // 申请单位
	private Date transferApplyDate;            // 申请日期
	private String rentHouseRecord;            // 租借房屋档案
	private Date rentStartDate;                // 租借开始时间
	private Date rentEndDate;                  // 租借结束时间
	private String transferApprovalStatus;     // 审批状态

	@Override
	public String getId() {
		return id;
	}

	@Override
	public void setId(String id) {
		this.id = id;
	}

	public String getRoomInfo() {
		return roomInfo;
	}

	public void setRoomInfo(String roomInfo) {
		this.roomInfo = roomInfo;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	@ExcelFields({
			@ExcelField(title="申请单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="租借房屋档案", attrName="roomInfo", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="申请日期", attrName="transferApplyDate", dictType="ob_ownership_registration_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="租借开始时间", attrName="rentStartDate", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="租借结束时间", attrName="rentEndDate", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="审批状态", attrName="transferApprovalStatus", dictType="sys_office_type", align= ExcelField.Align.CENTER, sort=50),
	})

	public Date getTransferApplyDate() {
		return transferApplyDate;
	}

	public void setTransferApplyDate(Date transferApplyDate) {
		this.transferApplyDate = transferApplyDate;
	}

	public String getRentHouseRecord() {
		return rentHouseRecord;
	}

	public void setRentHouseRecord(String rentHouseRecord) {
		this.rentHouseRecord = rentHouseRecord;
	}

	public Date getRentStartDate() {
		return rentStartDate;
	}

	public void setRentStartDate(Date rentStartDate) {
		this.rentStartDate = rentStartDate;
	}

	public Date getRentEndDate() {
		return rentEndDate;
	}

	public void setRentEndDate(Date rentEndDate) {
		this.rentEndDate = rentEndDate;
	}

	public String getTransferApprovalStatus() {
		return transferApprovalStatus;
	}

	public void setTransferApprovalStatus(String transferApprovalStatus) {
		this.transferApprovalStatus = transferApprovalStatus;
	}
}