package com.hsobs.ob.modules.estate.entity;

import com.jeesite.common.entity.DataEntity;
/**
 * 不动产信息表Entity
 * <AUTHOR>
 * @version 2024-12-08
 */
public class RealEstateQuery extends DataEntity<RealEstateQuery> {

	private static final long serialVersionUID = 1L;
	private String officeRoomName;		// 房间名称
	private String officeAddressName;		// 房屋名称
	private String officeType;		// 办公室分类
	private String officeRoomAddress;		// 办公用房所属位置
	private String officeRoomNo;		// 办公用房房号
	private String officeRoomCategory;		// 办公用房分类
	private String usageStatus;	//使用状态
	private String usageDate;	//使用日期
	private String officeName;	//使用单位
	private String officeCode;	//使用单位
	private String userName;	//使用人
	private String userCode;	//使用人
	private String area;	//使用面积
	private String useTag;	//是否闲置
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	public String getOfficeAddressName() {
		return officeAddressName;
	}

	public void setOfficeAddressName(String officeAddressName) {
		this.officeAddressName = officeAddressName;
	}

	public String getOfficeType() {
		return officeType;
	}

	public void setOfficeType(String officeType) {
		this.officeType = officeType;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getUseTag() {
		return useTag;
	}

	public void setUseTag(String useTag) {
		this.useTag = useTag;
	}

	public String getOfficeRoomAddress() {
		return officeRoomAddress;
	}

	public void setOfficeRoomAddress(String officeRoomAddress) {
		this.officeRoomAddress = officeRoomAddress;
	}

	public String getOfficeRoomNo() {
		return officeRoomNo;
	}

	public void setOfficeRoomNo(String officeRoomNo) {
		this.officeRoomNo = officeRoomNo;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}

	public String getOfficeRoomName() {
		return officeRoomName;
	}

	public void setOfficeRoomName(String officeRoomName) {
		this.officeRoomName = officeRoomName;
	}

	public String getOfficeRoomCategory() {
		return officeRoomCategory;
	}

	public void setOfficeRoomCategory(String officeRoomCategory) {
		this.officeRoomCategory = officeRoomCategory;
	}

	public String getUsageStatus() {
		return usageStatus;
	}

	public void setUsageStatus(String usageStatus) {
		this.usageStatus = usageStatus;
	}

	public String getUsageDate() {
		return usageDate;
	}

	public void setUsageDate(String usageDate) {
		this.usageDate = usageDate;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
}
