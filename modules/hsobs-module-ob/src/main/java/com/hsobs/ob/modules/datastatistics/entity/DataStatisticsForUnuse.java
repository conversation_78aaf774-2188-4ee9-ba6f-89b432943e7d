package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;

/**
 * 办公用房闲置情况数据统计Entity
 * <AUTHOR>
 * @version 2024-12-24
 */
public class DataStatisticsForUnuse extends DataEntity<DataStatisticsForUnuse> {

	private static final long serialVersionUID = 1L;
	private String sysCode;
	private UnuseTable unuseTable;

	public DataStatisticsForUnuse() {
		this(null);
	}

	public DataStatisticsForUnuse(String id){
		super(id);
	}

	public String getSysCode() {
		return sysCode;
	}

	public void setSysCode(String sysCode) {
		this.sysCode = sysCode;
	}

	public UnuseTable getUnuseTable() {
		return unuseTable;
	}

	public void setUnuseTable(UnuseTable unuseTable) {
		this.unuseTable = unuseTable;
	}
}