package com.hsobs.ob.modules.vacated.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

/**
 * 清理腾退Entity
 * <AUTHOR>
 * @version 2025-02-03
 */
public class VacatedLedger extends BpmEntity<VacatedLedger> {

	private static final long serialVersionUID = 1L;
	private String officeName;	// 登记单位
	private String officeCode;	// 登记单位
	private String overuseClearanceCount;		// 超标使用清退数量
	private String retireeClearanceCount;		// 离退休清退数量
	private String socialOrgClearanceCount;		// 社会组织占用清退数量
	private String enterpriseClearanceCount;		// 企事业清退数量
	private String otherClearanceCount;		// 其他情况清退数量
	private String overuseClearanceArea;		// 超标使用清退面积
	private String retireeClearanceArea;		// 离退休清退面积
	private String socialOrgClearanceArea;		// 社会组织占用清退面积
	private String enterpriseClearanceArea;		// 企事业清退面积
	private String otherClearanceArea;		// 其他情况清退面积
	@ExcelFields({
			@ExcelField(title="建筑名称", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="超标使用清退数量", attrName="overuseClearanceCount", dictType="ob_ownership_registration_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="离退休清退数量", attrName="retireeClearanceCount", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="社会组织占用清退数量", attrName="socialOrgClearanceCount", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="企事业清退数量", attrName="enterpriseClearanceCount", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="其他情况清退数量", attrName="otherClearanceCount", dictType="sys_office_type", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="超标使用清退面积", attrName="overuseClearanceArea", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="离退休清退面积", attrName="retireeClearanceArea", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="社会组织占用清退面积", attrName="socialOrgClearanceArea", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="企事业清退面积", attrName="enterpriseClearanceArea", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="其他情况清退面积", attrName="otherClearanceArea", align= ExcelField.Align.CENTER, sort=50),
	})
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOveruseClearanceCount() {
		return overuseClearanceCount;
	}

	public void setOveruseClearanceCount(String overuseClearanceCount) {
		this.overuseClearanceCount = overuseClearanceCount;
	}

	public String getRetireeClearanceCount() {
		return retireeClearanceCount;
	}

	public void setRetireeClearanceCount(String retireeClearanceCount) {
		this.retireeClearanceCount = retireeClearanceCount;
	}

	public String getSocialOrgClearanceCount() {
		return socialOrgClearanceCount;
	}

	public void setSocialOrgClearanceCount(String socialOrgClearanceCount) {
		this.socialOrgClearanceCount = socialOrgClearanceCount;
	}

	public String getEnterpriseClearanceCount() {
		return enterpriseClearanceCount;
	}

	public void setEnterpriseClearanceCount(String enterpriseClearanceCount) {
		this.enterpriseClearanceCount = enterpriseClearanceCount;
	}

	public String getOtherClearanceCount() {
		return otherClearanceCount;
	}

	public void setOtherClearanceCount(String otherClearanceCount) {
		this.otherClearanceCount = otherClearanceCount;
	}

	public String getOveruseClearanceArea() {
		return overuseClearanceArea;
	}

	public void setOveruseClearanceArea(String overuseClearanceArea) {
		this.overuseClearanceArea = overuseClearanceArea;
	}

	public String getRetireeClearanceArea() {
		return retireeClearanceArea;
	}

	public void setRetireeClearanceArea(String retireeClearanceArea) {
		this.retireeClearanceArea = retireeClearanceArea;
	}

	public String getSocialOrgClearanceArea() {
		return socialOrgClearanceArea;
	}

	public void setSocialOrgClearanceArea(String socialOrgClearanceArea) {
		this.socialOrgClearanceArea = socialOrgClearanceArea;
	}

	public String getEnterpriseClearanceArea() {
		return enterpriseClearanceArea;
	}

	public void setEnterpriseClearanceArea(String enterpriseClearanceArea) {
		this.enterpriseClearanceArea = enterpriseClearanceArea;
	}

	public String getOtherClearanceArea() {
		return otherClearanceArea;
	}

	public void setOtherClearanceArea(String otherClearanceArea) {
		this.otherClearanceArea = otherClearanceArea;
	}
}