package com.hsobs.ob.modules.apply.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.hsobs.ob.modules.apply.dao.ApplyRealEstateDao;
import com.hsobs.ob.modules.apply.entity.*;
import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.hsobs.ob.modules.estate.entity.RealEstateAddressFloor;
import com.hsobs.ob.modules.estate.service.RealEstateAddressService;
import com.hsobs.ob.modules.estate.service.RealEstateService;
import com.jeesite.common.io.FileUtils;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.tomcat.util.http.fileupload.FileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.apply.dao.ApplyDao;
import java.util.Map;
import java.util.Optional;

import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

/**
 * 使用管理Service
 * <AUTHOR>
 * @version 2025-02-08
 */
@Service
public class ApplyService extends CrudService<ApplyDao, Apply> {

	@Autowired
	private ApplyRealEstateDao applyRealEstateDao;

	@Autowired
	private RealEstateService realEstateService;

	@Autowired
	private RealEstateAddressService realEstateAddressService;
    @Autowired
    private ApplyDao applyDao;

	@Autowired
	ResourceLoader resourceLoader;

	@Autowired
	FileUploadService fileUploadService;

	/**
	 * 获取单条数据
	 * @param apply
	 * @return
	 */
	@Override
	public Apply get(Apply apply) {
		return super.get(apply);
	}

	/**
	 * 查询分页数据
	 * @param apply 查询条件
	 * @param apply page 分页对象
	 * @return
	 */
	@Override
	public Page<Apply> findPage(Apply apply) {
		return super.findPage(apply);
	}

	/**
	 * 查询列表数据
	 * @param apply
	 * @return
	 */
	@Override
	public List<Apply> findList(Apply apply) {
		return super.findList(apply);
	}

	/**
	 * 加载子表数据
	 */
	public Apply loadChildData(Apply apply) {
		if (apply != null && !apply.getIsNewRecord()){
			ApplyRealEstate applyRealEstate = new ApplyRealEstate(apply);
			apply.setApplyRealEstateList(applyRealEstateDao.findList(applyRealEstate));
		}
		return apply;
	}

	/**
	 * 保存数据（插入或更新）
	 * @param apply
	 */
	@Override
	@Transactional
	public void save(Apply apply) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(apply.getStatus())){
			apply.setStatus(Apply.STATUS_AUDIT);
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (Apply.STATUS_NORMAL.equals(apply.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (Apply.STATUS_DRAFT.equals(apply.getStatus())
				|| Apply.STATUS_AUDIT.equals(apply.getStatus())){
			super.save(apply);
		}

		// 如果为审核状态，则进行审批流操作
		if (Apply.STATUS_AUDIT.equals(apply.getStatus())){

			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			variables.put("area", apply.getArea());
			//variables.put("leaveDays", apply.getLeaveDays());

			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(apply.getBpm().getProcInsId())
					&& StringUtils.isBlank(apply.getBpm().getTaskId())){
				BpmUtils.start(apply, "ob_apply", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(apply, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(apply, apply.getId(), "apply_file");
		FileUploadUtils.saveFileUpload(apply, apply.getId(), "apply_vouchers_file");
		FileUploadUtils.saveFileUpload(apply, apply.getId(), "apply_agreement_file");


		ApplyRealEstate clearApplyRealEstate = new ApplyRealEstate();
		clearApplyRealEstate.setApply(apply);
		applyRealEstateDao.deleteByEntity(clearApplyRealEstate);

		for (ApplyRealEstate saveApplyRealEstate : apply.getApplyRealEstateList()){
			saveApplyRealEstate.setApply(apply);
			applyRealEstateDao.insert(saveApplyRealEstate);
		}
	}

	/**
	 * 更新状态
	 * @param apply
	 */
	@Override
	@Transactional
	public void updateStatus(Apply apply) {
		super.updateStatus(apply);

		if (null!= apply && null != apply.getStatus() && apply.getStatus().equals(Apply.STATUS_NORMAL)){
			Apply applyInfo = super.get(apply.getId(), false);
			List<ApplyRealEstate> applyRealEstateList = applyRealEstateDao.findList(new ApplyRealEstate(apply));
			if (applyInfo == null) {
				return;
			}
//			if (null != applyInfo.getRealEstateType() && !applyRealEstateList.isEmpty()) {
//				applyRealEstateList.forEach(applyRealEstate->{
//					if (applyInfo.getRealEstateType().equals("1")) {
//						RealEstateAddress saveRealEstateAddress = new RealEstateAddress();
//						saveRealEstateAddress.setId(applyRealEstate.getRealEstateId());
//						saveRealEstateAddress.setUsedOfficeCode(applyInfo.getUsedOfficeCode());
//						saveRealEstateAddress.setUsedUserCode(applyInfo.getUsedUserCode());
//						realEstateAddressService.save(saveRealEstateAddress);
//					} else {
//						RealEstate saveRealEstate = new RealEstate();
//						saveRealEstate.setId(applyRealEstate.getRealEstateId());
//						saveRealEstate.setUsedOfficeCode(applyInfo.getUsedOfficeCode());
//						saveRealEstate.setUsedUserCode(applyInfo.getUsedUserCode());
//						realEstateService.save(saveRealEstate);
//					}
//				});
//			}

			Resource usedVouchersFileResource = resourceLoader.getResource("classpath:file.template/ob_used_vouchers.docx");
			genApplyFile(applyInfo, usedVouchersFileResource, applyRealEstateList, "使用凭证", "apply_vouchers_file");
			Resource usedProtocolFileResource = resourceLoader.getResource("classpath:file.template/ob_used_protocol.docx");
			genApplyFile(applyInfo, usedProtocolFileResource, applyRealEstateList, "使用协议", "apply_agreement_file");

		}
	}

	private void genApplyFile(Apply applyInfo, Resource fileResource, List<ApplyRealEstate> applyRealEstateList, String fileName, String bizType) {
		if (!fileResource.exists()) {
			return;
		}
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();

		ConfigureBuilder configureBuilder = Configure.builder();
		if (null != applyInfo.getRealEstateType() && !applyRealEstateList.isEmpty() && !applyInfo.getRealEstateType().equals("1")) {
			configureBuilder.bind("applyRealEstateList", policy);
		}
		Configure config = configureBuilder.build();
		try (InputStream templateStream = fileResource.getInputStream();
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
			template.render(new HashMap<String, Object>() {{
				put("id", applyInfo.getId());
				put("reportOfficeName", "房管处");
				put("reportDate", getCurrentDateString());
				put("usedOfficeName", applyInfo.getUsedOffice().getOfficeName());

				List<Map<String, Object>> reportApplyRealEstateList = new ArrayList<>();
				if (null != applyInfo.getRealEstateType() && !applyRealEstateList.isEmpty()) {
					applyRealEstateList.forEach(item -> {
						Map<String, Object> param = new HashMap<>();
						param.put("address", Optional.ofNullable(item.getRealEstate())
								.map(RealEstate::getRealEstateAddress)
								.map(RealEstateAddress::getAddress)
								.orElseGet(() ->
										Optional.ofNullable(item.getRealEstateAddress())
												.map(RealEstateAddress::getAddress)
												.orElse("")
								));
						param.put("realEstateCertificateNumber",
								Optional.ofNullable(item.getRealEstate())
										.map(RealEstate::getRealEstateAddress)
										.map(RealEstateAddress::getRealEstateCertificateNumber)
										.orElseGet(() ->
												Optional.ofNullable(item.getRealEstateAddress())
														.map(RealEstateAddress::getRealEstateCertificateNumber)
														.orElse("")
										)
						);
						param.put("floorName",
								Optional.ofNullable(item.getRealEstate())
										.map(RealEstate::getRealEstateAddressFloor)
										.map(RealEstateAddressFloor::getName)
										.orElse("")

						);
						param.put("area",
								Optional.ofNullable(item.getRealEstate())
										.map(RealEstate::getArea)
										.orElse(0.)
						);
						reportApplyRealEstateList.add(param);
					});
					put("applyRealEstateList", reportApplyRealEstateList);
				}
			}});
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		String md5 = DigestUtils.md5Hex(outputStream.toByteArray());

		FileUploadParams fileParams = new FileUploadParams();
		MultipartFile multipartFile = new MockMultipartFile(
				fileName + ".docx",
				fileName + ".docx",
				"application/octet-stream",
				outputStream.toByteArray()
		);
		fileParams.setBizKey(applyInfo.getId());
		fileParams.setBizType("apply_vouchers_file");
		fileParams.setFile(multipartFile);
		fileParams.setFileMd5(md5);
		fileParams.setFileName(fileName + ".docx");
		FileUpload fileUpload = new FileUpload();
		fileUpload.setBizKey(applyInfo.getId());
		fileUpload.setBizType(bizType);
		Map<String, Object> uploadedFileMap = fileUploadService.uploadFile(fileUpload, fileParams);
		applyInfo.setDataMap(uploadedFileMap);
		FileUploadUtils.saveFileUpload(applyInfo, applyInfo.getId(), bizType);
	}

	private String getCurrentDateString() {
		LocalDateTime now = LocalDateTime.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		return now.format(formatter);
	}


	/**
	 * 删除数据
	 * @param apply
	 */
	@Override
	@Transactional
	public void delete(Apply apply) {
		applyDao.phyDelete(apply);
		ApplyRealEstate applyRealEstate = new ApplyRealEstate();
		applyRealEstate.setApply(apply);
		applyRealEstateDao.deleteByEntity(applyRealEstate);
	}

	public Page<ApplyLedger> listLedgerData(ApplyLedger applyLedger) {
		Page<ApplyLedger> page = (Page<ApplyLedger>) applyLedger.getPage();
		List<ApplyLedger> list = applyDao.listLedgerData(applyLedger);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<ApplyLedger> listLedger(ApplyLedger applyLedger) {
		List<ApplyLedger> list = applyDao.listLedgerData(applyLedger);
		return list;
	}


	public Page<CredentialsAgreementsListResponse> findCredentialsAgreementsListByFileInfo(CredentialsAgreementsListResponse response) {
		Page<CredentialsAgreementsListResponse> page = new Page<>();
		page.setList(applyDao.findCredentialsAgreementsList(response));
		page.setCount(dao.findCredentialsAgreementsListCount(response));
		return page;
	}

	public Page<ApplyDetailQuery> listApplyDetailQueryData(ApplyDetailQuery applyDetailQuery) {
		Page<ApplyDetailQuery> page = (Page<ApplyDetailQuery>) applyDetailQuery.getPage();
		List<ApplyDetailQuery> list = applyDao.listApplyDetailQueryData(applyDetailQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<ApplyDetailQuery> exportDetailData(ApplyDetailQuery applyDetailQuery) {
		return applyDao.listApplyDetailQueryData(applyDetailQuery);
	}

	public Page<ApplyInfoQuery> listApplyInfoQueryData(ApplyInfoQuery applyInfoQuery) {
		Page<ApplyInfoQuery> page = (Page<ApplyInfoQuery>) applyInfoQuery.getPage();
		List<ApplyInfoQuery> list = applyDao.listApplyInfoQueryData(applyInfoQuery);
		page.setList(list);
		return page;
	}

	public List<ApplyInfoQuery> exportInfoData(ApplyInfoQuery applyInfoQuery) {
		return applyDao.listApplyInfoQueryData(applyInfoQuery);
	}
}