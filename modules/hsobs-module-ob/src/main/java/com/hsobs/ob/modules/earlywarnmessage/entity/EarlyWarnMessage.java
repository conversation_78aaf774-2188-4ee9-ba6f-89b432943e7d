package com.hsobs.ob.modules.earlywarnmessage.entity;

import javax.validation.constraints.Size;
import java.lang.Double;
import java.util.Date;

import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Employee;

/**
 * 预警消息Entity
 * <AUTHOR>
 * @version 2024-12-02
 */
@Table(name="ob_early_warn_message", alias="a", label="预警消息信息",columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="early_warn_message_info", attrName="earlyWarnMessageInfo", label="预警消息"),
		@Column(name="office_occupancy_unit_id", attrName="officeOccupancyUnitId", label="办公用房单位"),
		@Column(name="occupancy_classification", attrName="occupancyClassification", label="用房分类"),
		@Column(name="user_id", attrName="userId", label="使用人"),
		@Column(name="position_id", attrName="positionId", label="职位"),
		@Column(name="superstandard_area", attrName="superstandardArea", label="超标面积", isUpdateForce=true),
		@Column(name="allocation_area", attrName="allocationArea", label="配置面积", isUpdateForce=true),
		@Column(name="warning_date", attrName="warningDate", label="预警日期", isUpdateForce=true),
		@Column(name="early_warn_type", attrName="earlyWarnType", label="预警类型"),
		@Column(name="early_warn_state", attrName="earlyWarnState", label="预警状态"),
		@Column(name="push_tag", attrName="pushTag", label="推送状态"),
		@Column(includeEntity=DataEntity.class),
	},joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Employee.class, alias = "o",
				on = "o.emp_code = a.user_id", attrName="employee",
				columns = {@Column(includeEntity = Employee.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "oi",
				on="oi.office_code = a.office_occupancy_unit_id", attrName="office",
				columns = {@Column(includeEntity = Office.class)})
	},
	extWhereKeys="dsfOffice",
	orderBy="a.update_date DESC"
)
public class EarlyWarnMessage extends DataEntity<EarlyWarnMessage> {
	
	private static final long serialVersionUID = 1L;
	private String earlyWarnMessageInfo;		// 预警消息
	private String officeOccupancyUnitId;		// 办公用房单位
	private String occupancyClassification;		// 用房分类
	private String userId;		// 使用人
	private String positionId;		// 职位
	private Double allocationArea;		// 配置面积
	private Double superstandardArea;		// 超标面积
	private Date warningDate;		// 预警日期
	private String earlyWarnType;		// 预警类型
	private String earlyWarnState;		// 预警状态
	private String pushTag;		// 推送状态

	private Employee employee; // 人员信息
	private Office office; // 机构信息

	@ExcelFields({
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="编号", attrName="id", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=10),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="预警消息", attrName="earlyWarnMessageInfo", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=20),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="办公用房单位", attrName="officeOccupancyUnitId", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=30),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="用房分类", attrName="occupancyClassification", dictType="occupancy_classification", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=40),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="使用人", attrName="userId", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=50),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="职位", attrName="positionId", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=60),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="配置面积", attrName="allocationArea", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=70),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="预警日期", attrName="warningDate", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=80, dataFormat="yyyy-MM-dd hh:mm"),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="预警类型", attrName="earlyWarnType", dictType="early_warn_type", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=90),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="预警状态", attrName="earlyWarnState", dictType="early_warn_state", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=100),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="更新时间", attrName="updateDate", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=150),
		@com.jeesite.common.utils.excel.annotation.ExcelField(title="备注", attrName="remarks", align= com.jeesite.common.utils.excel.annotation.ExcelField.Align.CENTER, sort=160),
	})
	public EarlyWarnMessage() {
		this(null);
	}
	
	public EarlyWarnMessage(String id){
		super(id);
	}

	public String getPushTag() {
		return pushTag;
	}

	public void setPushTag(String pushTag) {
		this.pushTag = pushTag;
	}

	@Size(min=0, max=256, message="预警消息长度不能超过 256 个字符")
	public String getEarlyWarnMessageInfo() {
		return earlyWarnMessageInfo;
	}

	public void setEarlyWarnMessageInfo(String earlyWarnMessageInfo) {
		this.earlyWarnMessageInfo = earlyWarnMessageInfo;
	}
	
	@Size(min=0, max=64, message="办公用房单位长度不能超过 64 个字符")
	public String getOfficeOccupancyUnitId() {
		return officeOccupancyUnitId;
	}

	public void setOfficeOccupancyUnitId(String officeOccupancyUnitId) {
		this.officeOccupancyUnitId = officeOccupancyUnitId;
	}
	
	@Size(min=0, max=1, message="用房分类长度不能超过 1 个字符")
	public String getOccupancyClassification() {
		return occupancyClassification;
	}

	public void setOccupancyClassification(String occupancyClassification) {
		this.occupancyClassification = occupancyClassification;
	}
	
	@Size(min=0, max=64, message="使用人长度不能超过 64 个字符")
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	@Size(min=0, max=64, message="职位长度不能超过 64 个字符")
	public String getPositionId() {
		return positionId;
	}

	public void setPositionId(String positionId) {
		this.positionId = positionId;
	}
	
	public Double getAllocationArea() {
		return allocationArea;
	}

	public void setAllocationArea(Double allocationArea) {
		this.allocationArea = allocationArea;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getWarningDate() {
		return warningDate;
	}

	public void setWarningDate(Date warningDate) {
		this.warningDate = warningDate;
	}
	
	@Size(min=0, max=1, message="预警类型长度不能超过 1 个字符")
	public String getEarlyWarnType() {
		return earlyWarnType;
	}

	public void setEarlyWarnType(String earlyWarnType) {
		this.earlyWarnType = earlyWarnType;
	}
	
	@Size(min=0, max=1, message="预警状态长度不能超过 1 个字符")
	public String getEarlyWarnState() {
		return earlyWarnState;
	}

	public void setEarlyWarnState(String earlyWarnState) {
		this.earlyWarnState = earlyWarnState;
	}

	public Employee getEmployee() {
		return employee;
	}

	public void setEmployee(Employee employee) {
		this.employee = employee;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	public Double getSuperstandardArea() {
		return superstandardArea;
	}

	public void setSuperstandardArea(Double superstandardArea) {
		this.superstandardArea = superstandardArea;
	}

	public String getDateGte() {
		return sqlMap.getWhere().getValue("CREATE_DATE", QueryType.GTE);
	}

	public void setDateGte(Date dateGte) {
		sqlMap.getWhere().and("CREATE_DATE", QueryType.GTE, dateGte);
	}

	public String getDateLte() {
		return sqlMap.getWhere().getValue("CREATE_DATE", QueryType.LTE);
	}

	public void setDateLte(Date dateLte) {
		sqlMap.getWhere().and("CREATE_DATE", QueryType.LTE, dateLte);
	}
}