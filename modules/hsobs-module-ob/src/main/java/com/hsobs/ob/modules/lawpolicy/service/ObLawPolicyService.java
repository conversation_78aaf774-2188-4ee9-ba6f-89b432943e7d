package com.hsobs.ob.modules.lawpolicy.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.lawpolicy.entity.ObLawPolicy;
import com.hsobs.ob.modules.lawpolicy.dao.ObLawPolicyDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 政策法规Service
 * <AUTHOR>
 * @version 2025-02-15
 */
@Service
public class ObLawPolicyService extends CrudService<ObLawPolicyDao, ObLawPolicy> {
	
	/**
	 * 获取单条数据
	 * @param obLawPolicy
	 * @return
	 */
	@Override
	public ObLawPolicy get(ObLawPolicy obLawPolicy) {
		return super.get(obLawPolicy);
	}
	
	/**
	 * 查询分页数据
	 * @param obLawPolicy 查询条件
	 * @param obLawPolicy page 分页对象
	 * @return
	 */
	@Override
	public Page<ObLawPolicy> findPage(ObLawPolicy obLawPolicy) {
		return super.findPage(obLawPolicy);
	}
	
	/**
	 * 查询列表数据
	 * @param obLawPolicy
	 * @return
	 */
	@Override
	public List<ObLawPolicy> findList(ObLawPolicy obLawPolicy) {
		return super.findList(obLawPolicy);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param obLawPolicy
	 */
	@Override
	@Transactional
	public void save(ObLawPolicy obLawPolicy) {
		super.save(obLawPolicy);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(obLawPolicy, obLawPolicy.getId(), "obLawPolicy_file");
	}
	
	/**
	 * 更新状态
	 * @param obLawPolicy
	 */
	@Override
	@Transactional
	public void updateStatus(ObLawPolicy obLawPolicy) {
		super.updateStatus(obLawPolicy);
	}
	
	/**
	 * 删除数据
	 * @param obLawPolicy
	 */
	@Override
	@Transactional
	public void delete(ObLawPolicy obLawPolicy) {
		super.delete(obLawPolicy);
	}
	
}