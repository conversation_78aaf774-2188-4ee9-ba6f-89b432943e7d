package com.hsobs.ob.modules.ownership.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.ob.modules.ownership.entity.OwnershipCoordinationRecord;
import com.hsobs.ob.modules.ownership.service.OwnershipCoordinationRecordService;

/**
 * 权属登记协调记录Controller
 * <AUTHOR>
 * @version 2025-03-09
 */
@Controller
@RequestMapping(value = "${adminPath}/ownership/coordinationRecord")
public class OwnershipCoordinationRecordController extends BaseController {

	@Autowired
	private OwnershipCoordinationRecordService ownershipCoordinationRecordService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public OwnershipCoordinationRecord get(String id, boolean isNewRecord) {
		return ownershipCoordinationRecordService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("ownership:coordinationRecord:view")
	@RequestMapping(value = {"list", ""})
	public String list(OwnershipCoordinationRecord ownershipCoordinationRecord, Model model) {
		model.addAttribute("ownershipCoordinationRecord", ownershipCoordinationRecord);
		return "modules/ownership/ownershipCoordinationRecordList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("ownership:coordinationRecord:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<OwnershipCoordinationRecord> listData(OwnershipCoordinationRecord ownershipCoordinationRecord, HttpServletRequest request, HttpServletResponse response) {
		ownershipCoordinationRecord.setPage(new Page<>(request, response));
		Page<OwnershipCoordinationRecord> page = ownershipCoordinationRecordService.findPage(ownershipCoordinationRecord);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("ownership:coordinationRecord:view")
	@RequestMapping(value = "form")
	public String form(OwnershipCoordinationRecord ownershipCoordinationRecord, Model model) {
		model.addAttribute("ownershipCoordinationRecord", ownershipCoordinationRecord);
		return "modules/ownership/ownershipCoordinationRecordForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("ownership:coordinationRecord:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated OwnershipCoordinationRecord ownershipCoordinationRecord) {
		ownershipCoordinationRecordService.save(ownershipCoordinationRecord);
		return renderResult(Global.TRUE, text("保存权属登记协调记录成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("ownership:coordinationRecord:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(OwnershipCoordinationRecord ownershipCoordinationRecord) {
		ownershipCoordinationRecordService.delete(ownershipCoordinationRecord);
		return renderResult(Global.TRUE, text("删除权属登记协调记录成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("ownership:coordinationRecord:view")
	@RequestMapping(value = "exportData")
	public void exportData(OwnershipCoordinationRecord ownershipCoordinationRecord, HttpServletResponse response) {
		List<OwnershipCoordinationRecord> list = ownershipCoordinationRecordService.findList(ownershipCoordinationRecord);
		String fileName = "权属登记协调记录" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("权属登记协调记录", OwnershipCoordinationRecord.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}