package com.hsobs.ob.modules.datastatistics.entity;

import com.jeesite.common.entity.DataEntity;

/**
 * 办公用房配置情况数据统计Entity
 * <AUTHOR>
 * @version 2024-12-24
 */
public class DataStatisticsForDispose extends DataEntity<DataStatisticsForDispose> {

	private static final long serialVersionUID = 1L;
	private String sysCode;
	private String region;
	private String year;
	private String office;
	private DisposeTable disposeTable;

	public DataStatisticsForDispose() {
		this(null);
	}

	public DataStatisticsForDispose(String id){
		super(id);
	}

	public String getSysCode() {
		return sysCode;
	}

	public void setSysCode(String sysCode) {
		this.sysCode = sysCode;
	}

	public DisposeTable getDisposeTable() {
		return disposeTable;
	}

	public void setDisposeTable(DisposeTable disposeTable) {
		this.disposeTable = disposeTable;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getOffice() {
		return office;
	}

	public void setOffice(String office) {
		this.office = office;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}
}