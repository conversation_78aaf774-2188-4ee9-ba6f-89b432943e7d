package com.hsobs.ob.modules.repair.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

/**
 * 维修申请Entity
 * <AUTHOR>
 * @version 2025-02-06
 */
@Table(name="ob_repair_request", alias="a", label="维修申请信息", columns={
		@Column(name="id", attrName="id", label="主键ID", isPK=true),
		@Column(name="project_name", attrName="projectName", label="项目名称", queryType=QueryType.LIKE),
		@Column(name="nature", attrName="nature", label="性质"),
		@Column(name="type", attrName="type", label="类型"),
		@Column(name="content", attrName="content", label="维修内容"),
		@Column(name="scheme", attrName="scheme", label="维修方案"),
		@Column(name="budget", attrName="budget", label="预算", isUpdateForce=true),
		@Column(name="funds_source", attrName="fundsSource", label="资金来源"),
		@Column(name="real_estate_id", attrName="realEstateId", label="房间ID"),
		@Column(name="real_estate_address_id", attrName="realEstateAddressId", label="房屋ID"),
		@Column(name="apply_office_code", attrName="applyOfficeCode", label="申请单位"),
		@Column(name="repair_date", attrName="repairDate", label="维修时间", isUpdateForce=true),
		@Column(name="complete", attrName="complete", label="维修是否完成", isUpdateForce=true),
		@Column(name="repair_report_id", attrName="repairReportId", label="维修是否完成", isUpdateForce=true),
		@Column(name="repair_no", attrName="repairNo", label="维修编号", isUpdateForce=true),
		@Column(name="completion_date", attrName="completionDate", label="竣工时间", isUpdateForce=true),
		@Column(name="priority", attrName="priority", label="优先级", isUpdateForce=true),
		@Column(
				name = "status",
				attrName = "status",
				label = "状态",
				isUpdate = true,
				isQuery = true,
				comment = "（推荐状态：0：正常；1：删除；2：停用；3：冻结；4：审核、待审核；5：审核驳回；9：草稿）"
		),
		@Column(includeEntity=DataEntity.class),
}, joinTable={
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstate.class, alias="c",
				on="c.id = a.real_estate_id",
				columns={@Column(includeEntity=RealEstate.class)}),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= RealEstateAddress.class, alias="rea",
				on="rea.id = a.real_estate_address_id",
				columns={@Column(includeEntity = RealEstateAddress.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o2",
				attrName = "applyOffice",
				on = "a.apply_office_code = o2.office_code",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = RepairReport.class, alias = "rr",
				attrName = "repairReport",
				on = "a.repair_report_id = rr.id",
				columns = {@Column(includeEntity = RepairReport.class)}),
}, orderBy="a.update_date DESC"
)
public class RepairRequest extends BpmEntity<RepairRequest> {

	private static final long serialVersionUID = 1L;
	private String projectName;		// 项目名称
	private String nature;		// 性质
	private String type;		// 类型
	private String content;		// 维修内容
	private String scheme;		// 维修方案
	private Double budget;		// 预算
	private String fundsSource;		// 资金来源
	private String realEstateId;		// 不动产ID
	private String realEstateAddressId;
	private String complete;
	private Date repairDate;		// 维修时间
	private Date completionDate;		// 竣工时间
	private String applyOfficeCode;		// 申请单位
	private String repairReportId;
	private String repairNo; // 维修编号

	private Date startDate;
	private Date endDate;

	private String priority; // 优先级

	private RealEstate realEstate;
	private RealEstateAddress realEstateAddress;
	private Office applyOffice;
	private RepairReport repairReport;


	@ExcelFields({
			@ExcelField(title="项目名称", attrName="projectName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="性质", attrName="nature", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="类型", attrName="type", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="维修内容", attrName="content", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="维修方案", attrName="scheme", align=Align.CENTER, sort=60),
			@ExcelField(title="预算", attrName="budget", align=Align.CENTER, sort=70),
			@ExcelField(title="资金来源", attrName="fundsSource", dictType="ob_funding", align=Align.CENTER, sort=80),
			@ExcelField(title="维修编号", attrName="repairNo", align=Align.CENTER, sort=200),
	})
	public RepairRequest() {
		this(null);
	}

	public RepairRequest(String id){
		super(id);
	}

	@Size(min=0, max=512, message="项目名称长度不能超过 512 个字符")
	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	@Size(min=0, max=1000, message="性质长度不能超过 1000 个字符")
	public String getNature() {
		return nature;
	}

	public void setNature(String nature) {
		this.nature = nature;
	}

	@NotBlank(message = "维修类型不能为空")
	@Size(min=0, max=100, message="类型长度不能超过 100 个字符")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Size(min=0, max=1000, message="维修内容长度不能超过 1000 个字符")
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Size(min=0, max=100, message="维修方案长度不能超过 100 个字符")
	public String getScheme() {
		return scheme;
	}

	public void setScheme(String scheme) {
		this.scheme = scheme;
	}

	public Double getBudget() {
		return budget;
	}

	public void setBudget(Double budget) {
		this.budget = budget;
	}

	@Size(min=0, max=1000, message="资金来源长度不能超过 1000 个字符")
	public String getFundsSource() {
		return fundsSource;
	}

	public void setFundsSource(String fundsSource) {
		this.fundsSource = fundsSource;
	}

	@Size(min=0, max=64, message="不动产ID长度不能超过 64 个字符")
	public String getRealEstateId() {
		return realEstateId;
	}

	public void setRealEstateId(String realEstateId) {
		this.realEstateId = realEstateId;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getRepairDate() {
		return repairDate;
	}

	public void setRepairDate(Date repairDate) {
		this.repairDate = repairDate;
	}

	public RealEstate getRealEstate() {
		return realEstate;
	}

	public void setRealEstate(RealEstate realEstate) {
		this.realEstate = realEstate;
	}

	public String getComplete() {
		return complete;
	}

	public void setComplete(String complete) {
		this.complete = complete;
	}

	@NotBlank(message = "申请单位不能为空")
	@NotNull(message = "申请单位不能为空")
	public String getApplyOfficeCode() {
		return applyOfficeCode;
	}

	public void setApplyOfficeCode(String applyOfficeCode) {
		this.applyOfficeCode = applyOfficeCode;
	}

	public Office getApplyOffice() {
		return applyOffice;
	}

	public void setApplyOffice(Office applyOffice) {
		this.applyOffice = applyOffice;
	}

	public RepairReport getRepairReport() {
		return repairReport;
	}

	public void setRepairReport(RepairReport repairReport) {
		this.repairReport = repairReport;
	}

	public String getRepairReportId() {
		return repairReportId;
	}

	public void setRepairReportId(String repairReportId) {
		this.repairReportId = repairReportId;
	}

	public String getRepairNo() {
		return repairNo;
	}

	public void setRepairNo(String repairNo) {
		this.repairNo = repairNo;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Date getCompletionDate() {
		return completionDate;
	}

	public void setCompletionDate(Date completionDate) {
		this.completionDate = completionDate;
	}

	public String getRealEstateAddressId() {
		return realEstateAddressId;
	}

	public void setRealEstateAddressId(String realEstateAddressId) {
		this.realEstateAddressId = realEstateAddressId;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public RealEstateAddress getRealEstateAddress() {
		return realEstateAddress;
	}

	public void setRealEstateAddress(RealEstateAddress realEstateAddress) {
		this.realEstateAddress = realEstateAddress;
	}
}