package com.hsobs.ob.modules.ownership.service;

import java.util.List;

import com.hsobs.ob.modules.estate.entity.RealEstate;
import com.hsobs.ob.modules.estate.entity.RealEstateAddress;
import com.hsobs.ob.modules.estate.service.RealEstateAddressService;
import com.hsobs.ob.modules.estate.service.RealEstateService;
import com.hsobs.ob.modules.ownership.dao.OwnershipRealEstateDao;
import com.hsobs.ob.modules.ownership.entity.OwnershipRealEstate;
import com.hsobs.ob.modules.ownership.entity.OwnershipRegistrationQuery;
import com.jeesite.common.config.Global;
import com.jeesite.common.mybatis.mapper.SqlMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.ownership.entity.OwnershipRegistration;
import com.hsobs.ob.modules.ownership.dao.OwnershipRegistrationDao;
import java.util.Map;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 权属登记Service
 * <AUTHOR>
 * @version 2025-02-05
 */
@Service
public class OwnershipRegistrationService extends CrudService<OwnershipRegistrationDao, OwnershipRegistration> {

	@Autowired
	private OwnershipRealEstateDao ownershipRealEstateDao;

	@Autowired
	private RealEstateService realEstateService;

	@Autowired
	private RealEstateAddressService realEstateAddressService;

	/**
	 * 获取单条数据
	 * @param ownershipRegistration
	 * @return
	 */
	@Override
	public OwnershipRegistration get(OwnershipRegistration ownershipRegistration) {
		return super.get(ownershipRegistration);
	}

	/**
	 * 查询分页数据
	 * @param ownershipRegistration 查询条件
	 * @param ownershipRegistration page 分页对象
	 * @return
	 */
	@Override
	public Page<OwnershipRegistration> findPage(OwnershipRegistration ownershipRegistration) {
		return super.findPage(ownershipRegistration);
	}

	/**
	 * 查询列表数据
	 * @param ownershipRegistration
	 * @return
	 */
	@Override
	public List<OwnershipRegistration> findList(OwnershipRegistration ownershipRegistration) {
		return super.findList(ownershipRegistration);
	}

	/**
	 * 加载子表数据
	 */
	public OwnershipRegistration loadChildData(OwnershipRegistration ownershipRegistration) {
		if (ownershipRegistration != null && !ownershipRegistration.getIsNewRecord()){
			OwnershipRealEstate ownershipRealEstate = new OwnershipRealEstate(ownershipRegistration);
			ownershipRegistration.setOwnershipRealEstateList(ownershipRealEstateDao.findList(ownershipRealEstate));
		}
		return ownershipRegistration;
	}

	/**
	 * 查询子表分页数据
	 * @param ownershipRealEstate
	 * @param ownershipRealEstate page 分页对象
	 * @return
	 */
	public Page<OwnershipRealEstate> findSubPage(OwnershipRealEstate ownershipRealEstate) {
		Page<OwnershipRealEstate> page = ownershipRealEstate.getPage();
		page.setList(ownershipRealEstateDao.findList(ownershipRealEstate));
		return page;
	}
	/**
	 * 保存数据（插入或更新）
	 * @param ownershipRegistration
	 */
	@Override
	@Transactional
	public void save(OwnershipRegistration ownershipRegistration) {
		System.out.println(ownershipRegistration.getBpm());
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(ownershipRegistration.getStatus())){
			ownershipRegistration.setStatus(OwnershipRegistration.STATUS_AUDIT);
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (OwnershipRegistration.STATUS_NORMAL.equals(ownershipRegistration.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (OwnershipRegistration.STATUS_DRAFT.equals(ownershipRegistration.getStatus())
				|| OwnershipRegistration.STATUS_AUDIT.equals(ownershipRegistration.getStatus())){
			super.save(ownershipRegistration);
		}

		// 如果为审核状态，则进行审批流操作
		if (OwnershipRegistration.STATUS_AUDIT.equals(ownershipRegistration.getStatus())){

			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			variables.put("classified", ownershipRegistration.getClassified());

			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(ownershipRegistration.getBpm().getProcInsId())
					&& StringUtils.isBlank(ownershipRegistration.getBpm().getTaskId())){
				BpmUtils.start(ownershipRegistration, "ob_ownership_registration", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(ownershipRegistration, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_identification_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_contract_assignment_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_contract_mortgage_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_contract_loan_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_land_use_certificate_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_management_certificate_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_ownership_certificate_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_property_ownership_certificate_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_cancel_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_change_report_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_change_approve_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_change_map_file");
		FileUploadUtils.saveFileUpload(ownershipRegistration, ownershipRegistration.getId(), "ownershipRegistration_change_owner_file");

		// 清空子表数据
		OwnershipRealEstate clearOwnershipRealEstate = new OwnershipRealEstate();
		clearOwnershipRealEstate.setOwnershipRegistration(ownershipRegistration);
		ownershipRealEstateDao.deleteByEntity(clearOwnershipRealEstate);
		// 保存 OwnershipRegistration子表
		for (OwnershipRealEstate saveOwnershipRealEstate : ownershipRegistration.getOwnershipRealEstateList()){
			saveOwnershipRealEstate.setOwnershipRegistration(ownershipRegistration);
			ownershipRealEstateDao.insert(saveOwnershipRealEstate);
		}
	}

	/**
	 * 更新状态
	 * @param ownershipRegistration
	 */
	@Override
	@Transactional
	public void updateStatus(OwnershipRegistration ownershipRegistration) {
		super.updateStatus(ownershipRegistration);

		if (null!= ownershipRegistration && null != ownershipRegistration.getStatus() && ownershipRegistration.getStatus().equals(OwnershipRegistration.STATUS_NORMAL)){
			OwnershipRegistration ownershipRegistrationInfo = super.get(ownershipRegistration.getId(), false);
			List<OwnershipRealEstate> ownershipRealEstateList = ownershipRealEstateDao.findList(new OwnershipRealEstate(ownershipRegistration));
			if (null == ownershipRegistrationInfo || null == ownershipRegistrationInfo.getRealEstateType() || ownershipRealEstateList.isEmpty()) {
				return;
			}
//			ownershipRealEstateList.forEach(ownershipRealEstate->{
//				if (ownershipRegistrationInfo.getRealEstateType().equals("1")) {
//					RealEstateAddress saveRealEstateAddress = new RealEstateAddress();
//					saveRealEstateAddress.setId(ownershipRealEstate.getRealEstateId());
//					saveRealEstateAddress.setOwnerOfficeCode(ownershipRegistrationInfo.getOwnerOfficeCode());
//					saveRealEstateAddress.setOwnerUserCode(ownershipRegistrationInfo.getOwnerUserCode());
//					saveRealEstateAddress.setUsedOfficeCode(ownershipRegistrationInfo.getUsedOfficeCode());
//					saveRealEstateAddress.setUsedUserCode(ownershipRegistrationInfo.getUsedUserCode());
//					realEstateAddressService.save(saveRealEstateAddress);
//				} else {
//					RealEstate saveRealEstate = new RealEstate();
//					saveRealEstate.setId(ownershipRealEstate.getRealEstateId());
//					saveRealEstate.setOwnerOfficeCode(ownershipRegistrationInfo.getOwnerOfficeCode());
//					saveRealEstate.setOwnerUserCode(ownershipRegistrationInfo.getOwnerUserCode());
//					saveRealEstate.setUsedOfficeCode(ownershipRegistrationInfo.getUsedOfficeCode());
//					saveRealEstate.setUsedUserCode(ownershipRegistrationInfo.getUsedUserCode());
//					realEstateService.save(saveRealEstate);
//				}
//			});
		}
	}

	/**
	 * 删除数据
	 * @param ownershipRegistration
	 */
	@Override
	@Transactional
	public void delete(OwnershipRegistration ownershipRegistration) {
		super.delete(ownershipRegistration);
		OwnershipRealEstate ownershipRealEstate = new OwnershipRealEstate();
		ownershipRealEstate.setOwnershipRegistration(ownershipRegistration);
		ownershipRealEstateDao.deleteByEntity(ownershipRealEstate);
	}

	public Page<OwnershipRegistrationQuery> listQueryData(OwnershipRegistrationQuery ownershipRegistrationQuery) {
		Page<OwnershipRegistrationQuery> page = (Page<OwnershipRegistrationQuery>) ownershipRegistrationQuery.getPage();
		List<OwnershipRegistrationQuery> list = ownershipRealEstateDao.listQueryData(ownershipRegistrationQuery);
		page.setList(list);
//		page.setCount(list.size());
		return page;
	}

	public List<OwnershipRegistrationQuery> listQuery(OwnershipRegistrationQuery ownershipRegistrationQuery) {
		return ownershipRealEstateDao.listQueryData(ownershipRegistrationQuery);
	}

	public void addDataScopeFilter(OwnershipRegistrationQuery ownershipRegistrationQuery) {
		SqlMap sqlMap = ownershipRegistrationQuery.sqlMap(); // v5.3.0+ 及之后版本
		final String config = Global.getConfig("user.adminCtrlPermi", "2");
		sqlMap.getDataScope().addFilter("dsfOffice", "Office",
				"jso.office_code",config);
		sqlMap.getWhere().disableAutoAddStatusWhere();
	}
}