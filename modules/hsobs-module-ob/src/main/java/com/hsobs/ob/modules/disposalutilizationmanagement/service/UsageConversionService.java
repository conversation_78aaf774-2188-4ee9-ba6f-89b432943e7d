package com.hsobs.ob.modules.disposalutilizationmanagement.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.ob.modules.disposalutilizationmanagement.entity.UsageConversion;
import com.hsobs.ob.modules.disposalutilizationmanagement.dao.UsageConversionDao;

/**
 * 转换用途Service
 * <AUTHOR>
 * @version 2025-03-08
 */
@Service
public class UsageConversionService extends CrudService<UsageConversionDao, UsageConversion> {

    /**
     * 获取单条数据
     * @param usageConversion
     * @return
     */
    @Override
    public UsageConversion get(UsageConversion usageConversion) {
        return super.get(usageConversion);
    }

    /**
     * 查询分页数据
     * @param usageConversion 查询条件
     * @param usageConversion page 分页对象
     * @return
     */
    @Override
    public Page<UsageConversion> findPage(UsageConversion usageConversion) {
        return super.findPage(usageConversion);
    }

    /**
     * 查询列表数据
     * @param usageConversion
     * @return
     */
    @Override
    public List<UsageConversion> findList(UsageConversion usageConversion) {
        return super.findList(usageConversion);
    }

    /**
     * 保存数据（插入或更新）
     * @param usageConversion
     */
    @Override
    @Transactional
    public void save(UsageConversion usageConversion) {
        super.save(usageConversion);
    }

    /**
     * 更新状态
     * @param usageConversion
     */
    @Override
    @Transactional
    public void updateStatus(UsageConversion usageConversion) {
        super.updateStatus(usageConversion);
    }

    /**
     * 删除数据
     * @param usageConversion
     */
    @Override
    @Transactional
    public void delete(UsageConversion usageConversion) {
        super.delete(usageConversion);
    }

}