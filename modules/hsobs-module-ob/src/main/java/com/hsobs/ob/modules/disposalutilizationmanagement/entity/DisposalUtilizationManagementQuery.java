package com.hsobs.ob.modules.disposalutilizationmanagement.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 处置利用管理Entity
 * <AUTHOR>
 * @version 2024-11-26
 */
public class DisposalUtilizationManagementQuery extends BpmEntity<DisposalUtilizationManagementQuery> {

	private static final long serialVersionUID = 1L;
	private Date applyDate;           // 申请日期
	private String orderNumber;       // 单据号
	private String applicantUnitId;		// 处置单位ID
	private String disposalType;		// 处置业务类型
	private String applicantUnitName;		// 处置单位
	private String disposalAddress;		// 处置建筑地点
	private String disposalSpace;		// 处置总建筑面积
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public String getOrderNumber() {
		return orderNumber;
	}

	public void setOrderNumber(String orderNumber) {
		this.orderNumber = orderNumber;
	}

	@ExcelFields({
			@ExcelField(title="申请日期", attrName="applyDate", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="单据号", attrName="orderNumber", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="处置单位", attrName="applicantUnitName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="处置业务类型", attrName="disposalType", dictType="disposal_type", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="处置建筑地点", attrName="disposalAddress", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="处置总建筑面积", attrName="disposalSpace", align= ExcelField.Align.CENTER, sort=50),
	})

	public String getApplicantUnitId() {
		return applicantUnitId;
	}

	public void setApplicantUnitId(String applicantUnitId) {
		this.applicantUnitId = applicantUnitId;
	}

	public String getDisposalType() {
		return disposalType;
	}

	public void setDisposalType(String disposalType) {
		this.disposalType = disposalType;
	}

	public String getApplicantUnitName() {
		return applicantUnitName;
	}

	public void setApplicantUnitName(String applicantUnitName) {
		this.applicantUnitName = applicantUnitName;
	}

	public String getDisposalAddress() {
		return disposalAddress;
	}

	public void setDisposalAddress(String disposalAddress) {
		this.disposalAddress = disposalAddress;
	}

	public String getDisposalSpace() {
		return disposalSpace;
	}

	public void setDisposalSpace(String disposalSpace) {
		this.disposalSpace = disposalSpace;
	}
}