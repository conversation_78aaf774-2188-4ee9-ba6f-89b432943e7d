package com.hsobs.ob.modules.repair.dao;

import com.hsobs.ob.modules.repair.entity.RepairRequestLedger;
import com.hsobs.ob.modules.repair.entity.RepairRequestQuery;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.ob.modules.repair.entity.RepairRequest;

import java.util.List;

/**
 * 维修申请DAO接口
 * <AUTHOR>
 * @version 2025-02-06
 */
@MyBatisDao
public interface RepairRequestDao extends CrudDao<RepairRequest> {

    List<RepairRequestLedger> listLedger(RepairRequestLedger repairRequestLedger);

    List<RepairRequestQuery> listQueryData(RepairRequestQuery repairRequestQuery);
}