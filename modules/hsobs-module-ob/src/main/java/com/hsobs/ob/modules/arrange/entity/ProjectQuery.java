package com.hsobs.ob.modules.arrange.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.util.Date;

/**
 * 配置管理Entity
 * <AUTHOR>
 * @version 2025-01-05
 */
public class ProjectQuery extends BpmEntity<ProjectQuery> {

	private static final long serialVersionUID = 1L;
	private String officeName;          // 申请单位
	private String officeCode;          // 申请单位
	private Date transferApplyDate;            // 申请日期
	private Date projectCategoryDate;            // 建设日期
	private String projectCategory;            // 建设项目类别
	private String transferApprovalStatus;     // 审批状态
	private String dateGte;	//使用日期上限
	private String dateLte;	//使用日期下限

	public String getDateGte() {
		return dateGte;
	}

	public void setDateGte(String dateGte) {
		this.dateGte = dateGte;
	}

	public String getDateLte() {
		return dateLte;
	}

	public void setDateLte(String dateLte) {
		this.dateLte = dateLte;
	}

	public Date getProjectCategoryDate() {
		return projectCategoryDate;
	}

	public void setProjectCategoryDate(Date projectCategoryDate) {
		this.projectCategoryDate = projectCategoryDate;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	@ExcelFields({
			@ExcelField(title="申请单位", attrName="officeName", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="申请日期", attrName="transferApplyDate", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="建设日期", attrName="projectCategoryDate", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="建设项目类别", attrName="projectCategory", dictType="ob_construction_type", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="审批状态", attrName="transferApprovalStatus", dictType="sys_office_type", align= ExcelField.Align.CENTER, sort=50),
	})

	public Date getTransferApplyDate() {
		return transferApplyDate;
	}

	public void setTransferApplyDate(Date transferApplyDate) {
		this.transferApplyDate = transferApplyDate;
	}

	public String getProjectCategory() {
		return projectCategory;
	}

	public void setProjectCategory(String projectCategory) {
		this.projectCategory = projectCategory;
	}

	public String getTransferApprovalStatus() {
		return transferApprovalStatus;
	}

	public void setTransferApprovalStatus(String transferApprovalStatus) {
		this.transferApprovalStatus = transferApprovalStatus;
	}
}