<% layout('/layouts/default.html', {title: '技术业务用房核定', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('技术业务用房核定')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
			</div>
		</div>

		<div class="box-body" style="background-color: #FFF">
		<#form:form id="searchForm" model="${realEstate}" action="${ctx}/estate/realEstate/getOfficeTechnicalBusinessArea" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('单位名称')}：</label>
				<div class="control-inline width-120" >
					<#form:input path="name" maxlength="64" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
	</div>
</div>
</div>
<% } %>
<script>
	// 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'单位名称', name:'officeName', index:'officeName', width:200, align:"left", frozen:true},
			{header:'技术业务用房核定面积（㎡）',
				name:'technicalBusinessArea',
				index:'technicalBusinessArea',
				width:120,
				align:"center",
				formatter: function(val) {
					return (val || 0).toFixed(2) + '㎡'; // 保留两位小数
				}},
			{header:'实际使用面积（㎡）',
				name:'area',
				index:'area',
				width:120,
				align:"center",
				formatter: function(val) {
					return (val || 0).toFixed(2) + '㎡';
				}},
			{header:'超标面积（㎡）',
				name:'overArea',
				index:'overArea',
				width:120,
				align:"center",
				formatter: function(val) {
					val = val || 0;
					var num = parseFloat(val).toFixed(2);
					if (val > 0) {
						return '<span class="label label-danger" style="padding:3px 6px">' + num + '㎡</span>';
					}
					return '<span class="text-muted">' + num + '㎡</span>';
				}}
		],
		shrinkToFit: false,
		frozenCols: true,
		showRownum: true,
		height: 'auto', // 自动高度
		autoScroll: true // 自动滚动条
	});

	// 添加全局样式
	$('<style>')
			.append('.label-danger { background: #ff4d4f!important; border-radius: 4px; color: white!important; }')
			.appendTo('head');
</script>
<script>
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/estate/realEstate/exportOfficeTechnicalBusinessAreaData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>