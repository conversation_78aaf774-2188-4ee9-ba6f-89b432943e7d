<% layout('/layouts/default.html', {title: '领导干部用房配备情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('领导干部用房配备情况')}
			</div>
		</div>

		<div class="box-body">
			<table id="dataGrid"></table>
<!--			<div id="dataGridPage"></div>-->
		</div>
	</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		data: [{name: '福建省人民代表大会常务委员会'}, {name: '闽侯县司法局上街司法所'}],
		datatype: 'local', // 设置本地数据
		columnModel: [
			{header:'单位名称', name:'name', index:'a.name', width:200, align:"left", frozen:true},
			{header:'单位级别', name:'level', index:'a.level', width:80, align:"center"},
			{header:'单位类别', name:'type', index:'a.type', width:80, align:"center"},
			{header:'地址坐落', name:'address', index:'a.address', width:80, align:"center"},
			{header:'行政区划', name:'region', index:'a.region', width:80, align:"center"},
			{header:'楼号及所在楼层', name:'floor', index:'a.floor', width:80, align:"center"},
			{header:'省级正职', name:'SJZZ', index:'a.region1', width:80, align:"center"},
			{header:'省级副职', name:'SJFZ', index:'a.region2', width:80, align:"center"},
			{header:'正厅(局)级', name:'ZTJJ', index:'a.region3', width:80, align:"center"},
			{header:'副厅(局)级', name:'FTJZ', index:'a.region4', width:80, align:"center"},
			{header:'正处级', name:'ZCJ', index:'a.region5', width:80, align:"center"},
			{header:'副处级', name:'FCJ', index:'a.region6', width:80, align:"center"},
			{header:'处级以下', name:'CHYX', index:'a.region7', width:80, align:"center"},
			{header:'编制总数', name:'BZZS', index:'a.region8', width:80, align:"center"},
			{header:'办公室', name:'XYBGS', index:'a.region9', width:80, align:"center"},
			{header:'服务用房', name:'XYFWYF', index:'a.region10', width:80, align:"center"},
			{header:'设备用房', name:'XYSBYF', index:'a.region11', width:80, align:"center"},
			{header:'合计', name:'XYHJ', index:'a.region12', width:80, align:"center"},
			{header:'建筑面积(仅供参考)', name:'XYJZMJ', index:'a.region13', width:80, align:"center"},
			{header:'附属用房建筑面积', name:'FSYFJZMJ', index:'a.region14', width:80, align:"center"},
			{header:'办公用房', name:'var15', index:'a.region15', width:80, align:"center"},
			{header:'技术业务用房', name:'var16', index:'a.region16', width:80, align:"center"},
			{header:'总建筑面积', name:'var17', index:'a.region17', width:80, align:"center"},
			{header:'办公业务用房核定总建筑面积', name:'var18', index:'a.region18', width:80, align:"center"},
			{header:'用地面积', name:'var19', index:'a.region19', width:80, align:"center"},
			{header:'产权单位', name:'var20', index:'a.region20', width:80, align:"center"},
			{header:'权属登记情况', name:'var21', index:'a.region21', width:80, align:"center"},
			{header:'建设年代', name:'var22', index:'a.region22', width:80, align:"center"},
			{header:'是否为租(借)用', name:'var23', index:'a.region23', width:80, align:"center"},
			{header:'备注', name:'var24', index:'a.region24', width:80, align:"center"},
			{header:'编报单位', name:'var25', index:'a.region25', width:80, align:"center"},
		],
		shrinkToFit: false, // 是否按百分比自动调整列宽，当列比较多时，开启水平滚动，可设置为false
		frozenCols: true, 	// 启用冻结列，并在colModel中设置frozen:true
		showRownum: true,	// 是否显示行号，默认true

		// ================ 设置多级表头 BEGIN ==============
		// 设置多级表头
		groupHeaders: {
			twoLevel:[
				{startColumnName: 'XYBGS', numberOfColumns: 4, titleText: '使用面积'}
			],
			threeLevel:[
				{startColumnName: 'SJZZ', numberOfColumns: 8, titleText: '单位及批复编制情况(人)'},
				{startColumnName: 'XYBGS', numberOfColumns: 5, titleText: '现有基本办公用房'},
				{startColumnName: 'var15', numberOfColumns: 3, titleText: '现有实际建筑面积'}
			]
		},
		// ================ 设置多级表头 END ==============

	});
</script>