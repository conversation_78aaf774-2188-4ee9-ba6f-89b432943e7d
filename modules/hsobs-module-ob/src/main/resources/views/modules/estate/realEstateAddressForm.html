<% layout('/layouts/default.html', {title: '房屋管理', libs: ['validate', 'fileupload', 'dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(realEstateAddress.isNewRecord ? '新增房屋' : '编辑房屋')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${realEstateAddress}" action="${ctx}/estate/realEstateAddress/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="row">
					<div class="col-xs-8">
						<div class="form-unit">${text('基本信息')}</div>
						<#form:hidden path="id"/>
						<div class="row">
							<div class="row">
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required ">*</span> ${text('名称')}：<i class="fa icon-question hide"></i>
										</label>
										<div class="col-sm-8">
											<#form:input id="name" path="name" maxlength="1000" class="form-control required"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required ">*</span> ${text('行政区划')}：<i class="fa icon-question hide"></i>
										</label>
										<div class="col-sm-8">
											<#form:treeselect id="region" title="行政区划"
											path="region" labelPath="areaTreeNames"
											url="${ctx}/sys/area/treeData?parentCode=0"
											class="form-control required" allowClear="true" returnFullName="true"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('资产编码')}：<i class="fa icon-question hide"></i>
										</label>
										<div class="col-sm-8">
											<#form:listselect id="assetSelect" title="资产信息查询" path="assetCode"
											url="${ctx}/szgz/assets/obAssetSelect" allowClear="false" class="form-control"
											checkbox="false" itemCode="assetCode" itemName="assetCode"
											callbackFuncName="assetListCheckCallback" />
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required ">*</span> ${text('地址坐落')}：<i class="fa icon-question hide"></i>
										</label>
										<div class="col-sm-8">

											<div class="input-group">
												<#form:textarea rows="1" id="address" path="address" maxlength="1000" class="form-control required"/>
												<span class="input-group-btn">
													<button type="button" class="btn btn-primary" id="btnShowMap" title="查看标注">
														<i class="fa fa-map-marker"></i>
													</button>
<!--													<button class="btn btn-default" type="button">Go!</button>-->
											  	</span>
											</div><!-- /input-group -->
										</div>
									</div>
								</div>

								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('层数')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input id="floorCount" path="floorCount" class="form-control digits required"/>
										</div>
									</div>
								</div>

								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('产权单位')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:treeselect id="ownerOfficeCode" title="${text('机构选择')}"
												path="ownerOfficeCode" labelPath="ownerOffice.officeName"
												url="${ctx}/sys/office/treeData"
												class="form-control required" allowClear="true"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('产权人')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:listselect id="ownerUserCode" path="ownerUserCode" labelPath="ownerUser.userName" title="产权人"
											url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
											checkbox="false" itemCode="userCode" itemName="userName"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('使用单位')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
											path="usedOfficeCode" labelPath="usedOffice.officeName"
											url="${ctx}/sys/office/treeData"
											class="" allowClear="true"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('使用人')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:listselect id="usedUserCode" path="usedUserCode" labelPath="usedUser.userName" title="产权人"
											url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
											checkbox="false" itemCode="userCode" itemName="userName"/>
										</div>
									</div>
								</div>
								<div class="col-xs-12">
									<div class="form-group">
										<label class="control-label col-sm-2" title="">
											<span class="required">*</span> ${text('经纬度')}：<i class="fa icon-question hide"></i>
										</label>
										<div class="col-sm-10">
											<div class="row">
												<div class="col-sm-6">
													<#form:input id="lat" path="latitude" class="form-control number required" readonly="true"/>
													<small class="help-block">${text('纬度')}</small>
												</div>
												<div class="col-sm-6">
													<#form:input id="lng" path="longitude" class="form-control number required" readonly="true"/>
													<small class="help-block">${text('经度')}</small>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-xs-12">
									<div class="form-group">
										<label class="control-label col-sm-2" title="">
											<span class="required hide">*</span> ${text('外观照片')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-10">

											<#form:fileupload id="uploadFile" bizKey="${realEstateAddress.id}" bizType="realEstateAddress_exterior_file"
											uploadType="image" class="" readonly="false" preview="true" maxUploadNum="10"
											serviceFileList="${ctxAdmin}/file/fileList?bizKeyIsLike=true"
											/>
										</div>
									</div>
								</div>
							</div>

							<!-- 地图弹窗 -->
							<div class="modal fade" id="mapModal" tabindex="-1" role="dialog">
								<div class="modal-dialog modal-lg" role="document">
									<div class="modal-content">
										<div class="modal-header">
											<button type="button" class="close" data-dismiss="modal">&times;</button>
											<h4 class="modal-title">${text('地图标注')}</h4>
										</div>
										<div class="modal-body">
											<div id="mapContainer" style="height: 500px; width: 100%;"></div>
										</div>
									</div>
								</div>
							</div>


							<div class="form-unit">${text('建筑信息')}</div>
							<div class="row">

								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('总建筑面积')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="landArea" maxlength="100" class="form-control"/>
										</div>
									</div>
								</div>



								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('地上建筑面积')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="buildingOccupationArea" maxlength="100" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('地下建面')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="buildingArea" maxlength="100" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('房屋结构')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:select path="structure" dictType="ob_structure_type" class="form-control" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('施工单位')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="constructionUnit" maxlength="1000" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('监理单位名称')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="designUnit" maxlength="1000" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-12">
									<div class="form-group">
										<label class="control-label col-sm-2" title="">
											<span class="required hide">*</span> ${text('施工许可')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-10">
											<#form:input path="realEstateCertificateNumber" maxlength="100" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('开工日期')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="commencementDate" readonly="true" maxlength="20" class="form-control laydate"
											dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('完工日期')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="completionTime" readonly="true" maxlength="20" class="form-control laydate"
											dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('决算日期')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="finalSettlementDate" readonly="true" maxlength="20" class="form-control laydate"
											dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('决算金额')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="originalValue" maxlength="100" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('土地证号')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="landCertificateNumber" maxlength="100" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('所有权证号')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="propertyOwnershipCertificateNumber" maxlength="100" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-12">
									<div class="form-group">
										<label class="control-label col-sm-2" title="">
											<span class="required hide">*</span> ${text('未获得证书原因')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-10">
											<#form:input path="notObtainingCertificateReason" maxlength="2000" class="form-control"/>
										</div>
									</div>
								</div>
								<div class="col-xs-12">
									<div class="form-group">
										<label class="control-label col-sm-2" title="">
											<span class="required hide">*</span> ${text('图纸附件')}：<i class="fa icon-question hide"></i></label>
										<div class="col-sm-10">

											<#form:fileupload id="realEstateAddress_file" bizKey="${realEstateAddress.id}" bizType="realEstateAddress_file"
											uploadType="all" class="" preview="true"
											serviceFileList="${ctxAdmin}/file/fileList?bizKeyIsLike=true"
											/>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="row">
							<div class="form-unit">${text('用房信息')}</div>
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required hide">*</span> ${text('房间数量')}：
										<i class="fa fa-question-circle" data-toggle="tooltip" title="自动统计"></i>
									</label>
									<div class="col-sm-8">
										<#form:input id="roomCount" maxlength="2000" class="form-control" readOnly="true" defaultValue="0" />
									</div>
								</div>
							</div>
							<div class="form-unit">${text('楼层信息')}</div>
							<div class="form-unit-wrap table-form">
								<table id="floorDataGrid"></table>
							</div>
						</div>
					</div>
				</div>

				<div class="box-footer">
					<div class="row">
						<div class="col-sm-offset-2 col-sm-10">
							<% if (hasPermi('estate:realEstateAddress:edit')){ %>
								<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
							<% } %>
							<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
						</div>
					</div>
				</div>
		</#form:form>
	</div>

	<!-- Modal -->
	<div class="modal fade" id="imageMarkModal" tabindex="-1" role="dialog">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">${text('楼层标注')}</h4>
				</div>
				<div class="modal-body">
					<div id="editor_container" style="height: 600px;"></div>
				</div>
				<div class="modal-footer">
<!--					<button type="button" class="btn btn-primary" id="btnSaveMark">${text('保存标注')}</button>-->
					<button type="button" class="btn btn-default" data-dismiss="modal">${text('关闭')}</button>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<style>
	#editor_container {
		border: 1px solid #ddd;
		border-radius: 4px;
		overflow: hidden;
	}

	#imageMarkModal .modal-dialog {
		width: 90%;
		max-width: 1200px;
	}

	#imageMarkModal .modal-body {
		padding: 0;
	}
</style>
<script>
	if (null !== `${realEstateAddress.assetCode }` && "" !== `${realEstateAddress.assetCode }`) {
		$('#assetSelectName').val(`${realEstateAddress.assetCode }`);

	}
	function assetListCheckCallback(id, act, index, layero, nodes) {
		if (id === "assetSelect" && act === "ok") {
			Object.keys(nodes).forEach(key => {
				let assetName = nodes[key].assetName;
				let assetCode = nodes[key].assetCode;
				let officeName = nodes[key].agencyName;
				let officeCode = nodes[key].agencyCode;
				$('#name').val(assetName)
				$('#ownerOfficeCodeCode').val(officeCode);
				$('#ownerOfficeCodeName').val(officeName);
			});
		}
	}
</script>
<!--<script src="${ctxStatic}/fabric/fabric.min.js"></script>-->
<script src="${ctxStatic}/filerobot/filerobot-image-editor.min.js"></script>

<script id="fileuploadTpl" type="text/template">//<!--<div>
<#form:fileupload id="{{d.id}}" bizKey="{{d.bizKey}}" bizType="{{d.bizType}}" uploadType="all" maxUploadNum="1"
	class="{{d.cssClass}}" isMini="false" preview="true" readonly="{{d.readonly}}"/>
</div>//--></script>
<script>
	$(function () {
		$('#roomCount').val(`${roomCount}`);
		$('[data-toggle="tooltip"]').tooltip();

		// 在floorDataGrid初始化后添加以下代码
		let initFloorGrid = function(floorCount) {

			var $grid = $('#floorDataGrid');
			var currentData = $grid.dataGrid('getGridParam','data') || [];

			// 自动生成默认楼层数据（仅当没有数据时）
			if (currentData.length === 0 && floorCount > 0) {
				for (var i = 1; i <= floorCount; i++) {
					var newRow = {
						id: '',
						floorIndex: i,
						name: i + 'F'
					};
					$grid.dataGrid('addRowData', i, newRow);
				}
				return;
			}
			var currentRowNum = currentData.length;

			// 删除多余行（从后往前删）
			if (currentRowNum > floorCount) {
				for (var i = currentRowNum; i > floorCount; i--) {
					$('#floorDataGrid').dataGrid('delRowData', currentData[i - 1].id);
				}
			} else if (currentRowNum < floorCount) {
				for (var i = currentRowNum + 1; i <= floorCount; i++) {
					$grid.jqGrid('addRow', { position: 'last',
						addRowParams: {keys: false, focusField: true},
						initdata: {
							id: '',
							floorIndex: i,
							name: i + 'F'
						}
					});
				}
			}
		};

		var initCount = parseInt($('#floorCount').val()) || 1;
		$('#floorCount').val(initCount);
		var floorData = $('#floorDataGrid').dataGrid('getGridParam','data') || []; // 确保后端返回数据

		// 当没有楼层数据但存在层数时自动生成
		if (floorData.length === 0 && initCount > 0) {
			initFloorGrid(initCount);
		}

		// 楼层数变化监听
		$('#floorCount').on('change', function() {
			var val = parseInt($(this).val()) || 1;
			$(this).val(Math.max(1, val)); // 确保最小值1
			initFloorGrid(val);
		});
	})
</script>
<script>
	let floorEditorInstance = null;

	// 修改后的imageMark函数
	function imageMark(id) {
		$.ajax({
			url: ctx +'/file/fileList?__t=' + new Date().getTime(),
			data: {
				"bizKey" : id,
				"bizType" : 'realEstateAddressFloor_file'
			},
			async: false,
			dataType : 'json',
			success : function(data){
				if (!(data.result == "false")) {
					if (data.length === 0) {
						js.showMessage('请上传楼层图并保存房屋后进行标注');
						return;
					}
					let fileUrl = data[0].fileUrl;
							// ` + ctxPath + fileUrl + `
					$('#imageMarkModal .modal-title').text(`楼层标注`);

					// 显示模态框
					$('#imageMarkModal').modal('show').on('shown.bs.modal', function() {
						const { TABS, TOOLS } = FilerobotImageEditor;
						const config = {
							source: ctxPath + fileUrl,
							onSave: (editedImageObject, designState) => {

								// 创建FormData对象
								const formData = new FormData();
								formData.append('bizKey', id);
								formData.append('bizType', 'realEstateAddressFloor_file');
								formData.append('imageBase64', editedImageObject.imageBase64);

								// 优化后的AJAX请求
								$.ajax({
									url: ctx + '/estate/realEstateAddress/uploadMarkImage',
									type: 'POST',
									data: formData,
									processData: false,  // 防止jQuery处理数据
									contentType: false,  // 不设置内容类型
									cache: false,        // 禁用缓存
									timeout: 60000       // 设置超时时间
								}).done(function(data) {
									console.log("Save mark image success", data);
									// 可以在这里添加成功后的处理逻辑
								}).fail(function(jqXHR, textStatus, errorThrown) {
									console.error("Save mark image failed:", textStatus, errorThrown);
									// 可以在这里添加错误处理逻辑
								});
								// $.ajax({
								// 	url: ctx + '/estate/realEstateAddress/uploadMarkImage?__t=' + new Date().getTime(),
								// 	data: {
								// 		"bizKey": id,
								// 		"bizType": 'realEstateAddressFloor_file',
								// 		"imageBase64": editedImageObject.imageBase64
								// 	},
								// 	async: false,
								// 	dataType: 'json',
								// 	success: function (data) {
								// 		console.log("save mark image result", data);
								// 	}
								// });
								// let tmpLink = document.createElement('a');
								// tmpLink.href = editedImageObject.imageBase64
								// tmpLink.download = 'mark-image.jpg'
								// tmpLink.style = 'position: absolute; z-index: -111; visibility: none;';
								// document.body.appendChild(tmpLink);
								// tmpLink.click();
								// document.body.removeChild(tmpLink);
								// tmpLink = null;

								// 模态框关闭时清理资源
								$('#imageMarkModal').on('hidden.bs.modal', function() {
									if (floorEditorInstance) {
										floorEditorInstance.terminate();
										floorEditorInstance = null;
									}
								});
							},
							annotationsCommon: {
								fill: '#ff0000',
							},
							Text: { text: '' },
							Rotate: { angle: 90, componentType: 'slider' },
							translations: {
								profile: 'Profile',
								coverPhoto: 'Cover photo',
								facebook: 'Facebook',
								socialMedia: 'Social Media',
								fbProfileSize: '180x180px',
								fbCoverPhotoSize: '820x312px',
							},
							Crop: {
								presetsItems: [
									{
										titleKey: 'classicTv',
										descriptionKey: '4:3',
										ratio: 4 / 3,
										// icon: CropClassicTv, // optional, CropClassicTv is a React Function component. Possible (React Function component, string or HTML Element)
									},
									{
										titleKey: 'cinemascope',
										descriptionKey: '21:9',
										ratio: 21 / 9,
										// icon: CropCinemaScope, // optional, CropCinemaScope is a React Function component.  Possible (React Function component, string or HTML Element)
									},
								],
								presetsFolders: [
									{
										titleKey: 'socialMedia', // will be translated into Social Media as backend contains this translation key
										// icon: Social, // optional, Social is a React Function component. Possible (React Function component, string or HTML Element)
										groups: [
											{
												titleKey: 'facebook',
												items: [
													{
														titleKey: 'profile',
														width: 180,
														height: 180,
														descriptionKey: 'fbProfileSize',
													},
													{
														titleKey: 'coverPhoto',
														width: 820,
														height: 312,
														descriptionKey: 'fbCoverPhotoSize',
													},
												],
											},
										],
									},
								],
							},
							tabsIds: [TABS.ADJUST, TABS.ANNOTATE, TABS.WATERMARK], // or ['Adjust', 'Annotate', 'Watermark']
							defaultTabId: TABS.ANNOTATE, // or 'Annotate'
							defaultToolId: TOOLS.TEXT, // or 'Text'
						};

						// Assuming we have a div with id="editor_container"
						const filerobotImageEditor = new FilerobotImageEditor(
								document.querySelector('#editor_container'),
								config,
						);

						filerobotImageEditor.render({
							onClose: (closingReason) => {
								console.log('Closing reason', closingReason);
								filerobotImageEditor.terminate();
							},
						});
					});
				} else {
					js.showMessage('请上传楼层图并保存房屋后进行标注');
				}
			}
		});
	}

	// 模态框关闭时清理资源
	$('#imageMarkModal').on('hidden.bs.modal', function() {
		if (floorEditorInstance) {
			floorEditorInstance.terminate();
			floorEditorInstance = null;
		}
	});
	//初始化测试数据子表DataGrid对象
	$('#floorDataGrid').dataGrid({

		data: "#{toJson(realEstateAddress.realEstateAddressFloorList)}",
		datatype: 'local', // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度

		// 设置数据表格列
		columnModel: [
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'楼层', name:'floorIndex', editable:true, hidden:true},
			{header:'${text("楼层")}', name:'name', editable:true, hidden:true, formatter: function (val, obj, row, act) {
				return val || (row.floorIndex) + "F";

			}},
			{header:'${text("楼层")}', name:'nickName', editable:false, width:40, align: "center", formatter: function (val, obj, row, act) {
				return row.name || (row.floorIndex) + "F";
			}},
			{header:'${text("楼层图")}', name: "id", width:200,
				editable: true, edittype: "custom",
				editoptions: {
					custom_element: function(val, editOptions) {
						return js.template('fileuploadTpl', {
							id: 'fileupload_'+ editOptions.id, title: '楼层图选择',
							bizKey: val, bizType: 'realEstateAddressFloor_file', cssClass: '', readonly: false
						});
					}
				}
			},
			{header: '${text("标注")}', editable: false, formatter: function (val, obj, row, act) {
				return `<a class="btn btn-xs btn-success" onclick="imageMark('`+row.id+`')"><i class="fa fa-map-marker"></i>标注</a>`;
			}}
		],
		shrinkToFit: false,	// 是否按百分比自动调整列宽
		showRownum: false,
		// 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上 v4.1.7
		editGridAddRowInitData: {id: ''},	// 新增行的时候初始化的数据

		// 编辑表格的提交数据参数
		editGridInputFormListName: 'realEstateAddressFloorList', // 提交的数据列表名
		editGridInputFormListAttrs: 'id,floorIndex,name,realEstateAddressFloor_file,realEstateAddressFloor_file__del', // 提交数据列表的属性字段

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>

<script type="text/javascript" src="${@Global.getConfig('tianditu.baseUrl')}"></script>
<style>
	.map-search-box {
		position: absolute;
		top: 10px;
		left: 50px;
		z-index: 1000;
		width: 300px;
		background: rgba(255,255,255,0.9);
		padding: 10px;
		border-radius: 4px;
		box-shadow: 0 2px 4px rgba(0,0,0,0.2);
	}
	.map-search-box input {
		width: 65% !important;
		display: inline-block !important;
	}
	.map-search-box button {
		width: 28%;
		margin-left: 2%;
	}
</style>
<script>
	$(function () {
		let map, geocoder, currentMarker, localsearch;
		function initMap() {
			// 清理之前的实例
			$('#mapContainer').empty();
			if(map) map = null;


			// 创建新地图实例
			map = new T.Map('mapContainer', {projection: 'EPSG:4326'});
			geocoder = new T.Geocoder();

			let lng = parseFloat($('#lng').val());
			let lat = parseFloat($('#lat').val());
			let hasValidCoords = !isNaN(lng) && !isNaN(lat);

			let centerLng, centerLat;
			if (hasValidCoords) {
				centerLng = lng;
				centerLat = lat;
			} else {
				// 默认中心点（例如福州）
				centerLng = 119.291320;
				centerLat = 26.077380;
			}
			map.centerAndZoom(new T.LngLat(centerLng, centerLat), hasValidCoords ? 15 : 15);
			if(hasValidCoords) addMarker(centerLng, centerLat);

			const config = {
				pageCapacity: 10,
				onSearchComplete: localSearchResult
			};
			localsearch = new T.LocalSearch(map, config);
			// 添加地图点击监听
			map.addEventListener('click', handleMapClick);

			// 初始化搜索功能
			initSearch();
		}
		function localSearchResult(result) {
			map.clearOverLays();
			if (parseInt(result.getResultType()) !== 1) {
				return;
			}
			let zoomArr = [];

			let obj = result.getPois();
			for (var i = 0; i < obj.length; i++) {
				//闭包
				(function (i) {
					var lnglatArr;
					const lonlat = obj[i].lonlat;
					const commaIndex = lonlat.indexOf(",");
					lnglatArr = commaIndex !== -1 ? lonlat.split(",") : lonlat.split(" ");
					var lnglat = new T.LngLat(lnglatArr[0], lnglatArr[1]);

					//创建标注对象
					var marker = new T.Marker(lnglat);
					//地图上添加标注点
					map.addOverLay(marker);
					zoomArr.push(lnglat);
				})(i);
			}
			//显示地图的最佳级别
			map.setViewport(zoomArr);
		}

		// 更新表单字段
		function updateFormFields({lng, lat, address, name}) {
			$('#lng').val(lng);
			$('#lat').val(lat);
			$('#address').val(address);
			// if (!name) return;
			// $('#name').val(name);
		}
		// 处理地图点击事件
		async function handleMapClick(e) {

			const lng = e.lnglat.getLng();
			const lat = e.lnglat.getLat();

			try {
				// 反向地理编码获取地址
				const result = await new Promise((resolve, reject) => {
					geocoder.getLocation(e.lnglat, resolve, reject);
				});

				updateFormFields({
					lng: lng.toFixed(6),
					lat: lat.toFixed(6),
					address: result.addressComponent.address || '',
					name: result.addressComponent?.poi || $('#name').val()
				});

				addMarker(lng, lat);
				map.panTo(new T.LngLat(lng, lat));
			} catch (error) {
				js.showMessage('无法获取该位置的地址信息');
			}
		}

		// 添加/更新标记
		function addMarker(lng, lat) {
			if(currentMarker) map.removeOverLay(currentMarker);
			currentMarker = new T.Marker(new T.LngLat(lng, lat), {draggable: false});
			map.addOverLay(currentMarker);
			bindMarkerClick(lng, lat);
		}

		// 绑定标记点击事件
		function bindMarkerClick(lng, lat) {
			currentMarker.addEventListener('click', () => {
				showInfoWindow(lng, lat);
			});
		}

		// 显示信息窗口
		function showInfoWindow(lng, lat) {
			const content = `【` + ($('#name').val() || '未命名') + `】<br>
                        ` + ($('#address').val() || '无地址信息') + `<br>
                        经度：` + lng.toFixed(6) + `<br>
                        纬度：` + lat.toFixed(6) + ``;
			const infoWindow = new T.InfoWindow(content, {offset: new T.Point(0, -30)});
			map.openInfoWindow(infoWindow, new T.LngLat(lng, lat));
		}

		// 显示地图弹窗
		$('#btnShowMap').click(function(){
			$('#mapModal').modal('show').on('shown.bs.modal', initMap);
		});

		// 清理地图资源
		$('#mapModal').on('hidden.bs.modal', () => {
			if(map) map = null;
			currentMarker = null;
		});

		// 初始化搜索功能
		function initSearch() {
			const $searchBox = $(`
				<div class="map-search-box">
					<input type="text" placeholder="输入地址进行搜索" class="form-control">
					<button type="button" class="btn btn-primary">搜索</button>
				</div>
			`).prependTo('#mapContainer');

			// 阻止搜索区域的点击事件冒泡
			$searchBox.find('input, button').on('click', function(event) {
				event.stopPropagation();
			});

			$searchBox.find('button').click(async (event) => {
				event.preventDefault();
				const address = $searchBox.find('input').val().trim();
				if (!address) return;
				localsearch.search(address);
			})
		}
	})
</script>
<script>
// 自定义验证规则（添加在validate配置之前）
$.validator.addMethod("validateTreeselect", function(value, element) {
	return $(element).siblings('.treeselect').find('input[name$="_name"]').val() !== '';
}, "此项为必填项");

// 在页面加载后为treeselect组件绑定验证
$(function() {
	$('#region, #ownerOfficeCode, #usedOfficeCode').each(function(){
		$(this).siblings('.treeselect').find('input[name$="_name"]').addClass('required');
	});
});
$('#inputForm').validate({
	rules: {
		name: "required",
		region: "required",
		address: "required",
		floorCount: {
			required: true,
			digits: true,
			min: 1 // 添加最小值验证
		},
		ownerOfficeCode: "required",
		latitude: {required: true, number: true},
		longitude: {required: true, number: true},
	},
	messages: {
		name: "请输入地址名称",
		region: "请选择行政区划",
		address: "请输入详细地址",
		floorCount: {
			required: "请输入层数",
			digits: "层数必须为整数",
			min: "层数不能小于1"
		},
		ownerOfficeCode: "请选择产权单位",
		latitude: "请在地图标注中点击选择经纬度",
		longitude: "请在地图标注中点击选择经纬度",
	},
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>
