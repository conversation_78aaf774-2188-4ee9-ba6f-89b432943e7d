<% layout('/layouts/default.html', {title: '房屋档案查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('房屋档案查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${realEstate}" action="${ctx}/estate/realEstate/listQueryData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('办公用房名称')}：</label>
					<div class="control-inline">
						<#form:input path="officeRoomName" maxlength="1000" class="form-control width-120"/>
					</div>
				</div>
			<div class="form-group">
					<label class="control-label">${text('使用人')}：</label>
					<div class="control-inline">
						<#form:input path="userName" maxlength="1000" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('办公用房分类')}：</label>
					<div class="control-inline width-120">
						<#form:select path="officeRoomCategory" dictType="occupancy_classification" blankOption="true" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('使用日期')}：</label>
					<div class="control-inline">
						<#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('使用单位')}：</label>
					<div class="control-inline">
						<#form:treeselect id="officeCode" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('使用状态')}：</label>
					<div class="control-inline width-120">
						<% var items = [{label:'使用中',value:'0'},{label:'闲置中',value:'1'}]; %>
						<#form:select path="usageStatus" items="${items}" itemLabel="label" itemValue="value" blankOption="true" class="form-control width-120" />
					</div>
				</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("办公用房分类")}', name:'officeRoomCategory', index:'a.officeRoomCategory', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('occupancy_classification')}", val, '未知', true);
		}},
		{header:'${text("办公室分类")}', name:'officeType', index:'a.officeType', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_office_work')}", val, '未知', true);
		}},
		{header:'${text("房屋名称")}', name:'officeAddressName', index:'a.officeAddressName', width:150, align:"left", frozen:true},
		{header:'${text("房间名称")}', name:'officeRoomName', index:'a.officeRoomName', width:150, align:"left", frozen:true},
		{header:'${text("使用状态")}', name:'usageStatus', index:'a.usageStatus', width:150, align:"left"},
		{header:'${text("使用日期")}', name:'usageDate', index:'a.usageDate', width:150, align:"center"},
		{header:'${text("使用单位")}', name:'officeName', index:'a.officeName', width:150, align:"center"},
		{header:'${text("使用人")}', name:'userName', index:'a.userName', width:150, align:"center"},
		{header:'${text("使用面积")}', name:'area', index:'a.area', width:150, align:"center"},
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>