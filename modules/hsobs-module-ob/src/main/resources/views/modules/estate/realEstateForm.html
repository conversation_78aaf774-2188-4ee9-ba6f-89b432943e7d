<% layout('/layouts/default.html', {title: '房间管理', libs: ['validate', 'fileupload', 'dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(realEstate.isNewRecord ? '新增房间' : '编辑房间')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${realEstate}" action="${ctx}/estate/realEstate/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="row">
					<div class="col-xs-7">
						<div class="form-unit">${text('基本信息')}</div>
						<#form:hidden path="id"/>
						<div class="row">
							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('房间名称')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:input path="name" maxlength="1000" class="form-control required"/>
									</div>
								</div>
							</div>
							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('房间编号')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:input path="serialNumber" maxlength="1000" class="form-control required" readOnly="true"/>
									</div>
								</div>
							</div>
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-2" title="">
										<span class="required">*</span> ${text('房间号')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-10">
										<#form:input path="roomNumber" maxlength="1000" class="form-control required"/>
									</div>
								</div>
							</div>
						</div>
						<div class="row">

							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('房间类型')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:select id="type" path="type" dictType="occupancy_classification" class="form-control required" />
									</div>
								</div>
							</div>
							<div class="col-xs-6" id="officeTypeGroup">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('办公室类型')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:select id="officeType" path="officeType" dictType="ob_office_work" class="form-control required" />
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-2" title="">
										<span class="required">*</span> ${text('使用单位')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-10">
										<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
										path="usedOfficeCode" labelPath="usedOffice.officeName"
										url="${ctx}/sys/office/treeData"
										class="required" allowClear="true"/>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('房间用途')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:select path="puroise" dictType="ob_house_purpose" class="form-control required" />
									</div>
								</div>
							</div>
							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required hide">*</span> ${text('是否租借')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:select path="lease" dictType="ob_lease" class="form-control required" />
									</div>
								</div>
							</div>
							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('面积')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:input path="area" class="form-control number required minValue"
										min="0.01" defaultValue="0"/>
									</div>
								</div>
							</div>
							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('核定面积')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:input id="approvedArea" class="form-control number required" value="0" readonly="true"/>
									</div>
								</div>
							</div>
							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('房屋')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:listselect id="realEstateAddressId" title="地址选择"
										class="form-control required"
										path="realEstateAddressId"
										labelPath = "realEstateAddress.name"
										url="${ctx}/estate/realEstateAddress/realEstateAddressSelect" allowClear="false"
										checkbox="false" itemCode="id" itemName="name"/>
									</div>
								</div>
							</div>
							<div class="col-xs-6">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required">*</span> ${text('所属楼层')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:select id="floor" path="realEstateAddressFloorId" name="realEstateAddressFloorId" blankOption="false" class="form-control required" />
									</div>
								</div>
							</div>
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-2" title="">
										<span class="required">*</span> ${text('闲置原因')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-10">
										<#form:select path="idlenessReason" dictType="ob_idleness_reason" class="form-control required" />
									</div>
								</div>
							</div>
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-2">室内资产材料：</label>
									<div class="col-sm-10">
										<#form:fileupload id="realEstate_asset_file" bizKey="${realEstate.id}" bizType="realEstate_asset_file"
										uploadType="all" class="" readonly="false" preview="true" maxUploadNum = "10"
										serviceFileList="${ctxAdmin}/file/fileList?bizKeyIsLike=true"
										/>
									</div>
								</div>
							</div>
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-2">CAD 图纸：</label>
									<div class="col-sm-10">
										<#form:fileupload id="realEstate_cad_file" bizKey="${realEstate.id}" bizType="realEstate_cad_file"
										class="" readonly="false" preview="true" maxUploadNum = "1"
										serviceFileList="${ctxAdmin}/file/fileList?bizKeyIsLike=true"
										/>
									</div>
								</div>
							</div>
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-2" title="">
										<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-10">
										<#form:textarea path="remarks" rows="4" maxlength="1000" class="form-control"/>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="col-xs-5">
						<div class="form-unit">${text('使用信息')}</div>
						<div class="row">
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required hide">*</span> ${text('使用面积')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:input path="area" class="form-control number minValue"
											min="0.01" defaultValue="0"/>
									</div>
								</div>
							</div>
							<div class="col-xs-12">
								<div class="form-group">
									<label class="control-label col-sm-4" title="">
										<span class="required hide">*</span> ${text('使用日期')}：<i class="fa icon-question hide"></i></label>
									<div class="col-sm-8">
										<#form:input path="applyDate" maxlength="20" class="form-control laydate required"
											dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
									</div>
								</div>
							</div>
						</div>
						<div class="form-unit">${text('使用人')}</div>

						<div class="row" style="text-align: right; padding: 5px 0;">
							<#form:btnlistselect id="userListselectBtn" title="智能分配房间"
								allowClear="true"
								inputStyle="display: none;"
								btnLabel="智能分配房间"
								callbackFuncName="listselectCallback"
								url="${ctx}/sys/empUser/empUserSelect?hasUsedRoom=true"
								allowClear="false"
								checkbox="true" itemCode="id" itemName="name" />
						</div>
						<table id="userDataGrid"></table>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('estate:realEstate:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
	$(function () {
	});
</script>
<script>

	function setOfficeApprovedArea() {
		let data = $('#userDataGrid').dataGrid('getRowData');
		let approvedArea = 0;
		if (data.length === 0) {
			return;
		} else {
			data.forEach(item => {
				approvedArea += parseFloat(item['employee.officeApprovedConfig.area']) || 0;
			})
		}
		$('#approvedArea').val(approvedArea);
	}
	function listselectCallback(id, act, index, layero, selectData){
		if (id !== 'userListselectBtn') {
			return;
		}
		if (act === 'cancel') {
			return;
		}
		$.each(selectData, (key, value) => {
			let gridIds = $('#userDataGrid').jqGrid("getDataIDs");
			if ($.inArray(key,gridIds) >= 0) {
				return;
			}
			if (!value.userName) {
				return;
			}
			$('#userDataGrid').jqGrid('addRow', { position: 'last',
				addRowParams: {keys: false, focusField: true},
				initdata: value
			});
		});
		setOfficeApprovedArea();
	}
	$('#userDataGrid').dataGrid({
		data: "#{toJson(realEstate.usedUserList)}",
		datatype: 'local', // 设置本地数据
		columnModel: [
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'userCode', name:'userCode', editable:true, hidden:true},
			{header:'姓名', name:'userName', editable:false, width:80, align:'center'},
			{header:'部门', name:'officeName', editable:false, width:80, align:'center', formatter: function (val, obj, row, act) {
				return row.employee?.office?.officeName || '';
			}},
			{header:'职级', name:'establishmentType', editable:false, width:80, align:'center', formatter: function (val, obj, row, act) {
				return js.getDictLabel("#{@DictUtils.getDictListJson('ob_establishment_type')}", row.employee?.establishmentType || '', '${text("未知")}', true);
			}},
			{header:'办公室核定面积', name:'employee.officeApprovedConfig.area', editable:false, width:80, align:'center'},
			{header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
					var actions = [];
					actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#userDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
					// actions.push('<a href="#" onclick="$(\'#userDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\');">删除</a>&nbsp;');
					return actions.join('');
				}, editoptions: {defaultValue: 'new'}}
		],

		//# // 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
		editGridInitAllRowEdit: true,  // 是否初始化就编辑所有行（*** 重点 ***）
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
		editGridAddRowInitData: {id: '',arrangeId: {id: ''},realEstateId: ''},	// 新增行的时候初始化的数据

		//# // 编辑表格的提交数据参数
		editGridInputFormListName: 'usedUserList', // 提交的数据列表名
		editGridInputFormListAttrs: 'id,userCode', // 提交数据列表的属性字段

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){
			let userNameList = data.rows.map(item => item.userName);
			$('#userListselectBtnName').val(userNameList.join(','))
			let userCodeList = data.rows.map(item => item.userCode);
			$('#userListselectBtnCode').val(userCodeList.join(','))
			setOfficeApprovedArea();
		}

	});

</script>
<script>
	$(function () {
		$('#realEstateAddressIdCode').on('change', function() {
			changeFloorSelectOptions();
		});

		function changeFloorSelectOptions() {
			$('#floor').empty().trigger('change');
			let realEstateAddressId = $('#realEstateAddressIdCode').val();
			if (realEstateAddressId) {
				js.ajaxSubmit(ctx + '/estate/realEstateAddress/findRealEstateAddressFloorList', {
					id: $('#realEstateAddressIdCode').val(),
				}, function(data){
					$('#floor').select2({
						data: data.map(item => {
							return {id: item.id, text: item.name};
						})
					});
					$('#floor').val(`${realEstate.realEstateAddressFloorId}`).trigger('change');
				});
			}
		}
		changeFloorSelectOptions();
	});
</script>
<script>
	// 初始化显示状态和动态切换
	function toggleOfficeTypeVisibility() {
		var typeValue = $('#type').val();
		if (typeValue === '0') {
			$('#officeTypeGroup').show();
		} else {
			$('#officeTypeGroup').hide();
		}
	}

	$(document).ready(function() {
		toggleOfficeTypeVisibility(); // 初始状态
		$('#type').on('change', toggleOfficeTypeVisibility); // 监听变化
	});

</script>
<script>
if (typeof window.webuploaderRefresh == 'function'){
	window.webuploaderRefresh();
}
$(function(){
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		if(typeof window.webuploaderRefresh == 'function'){
			window.webuploaderRefresh();
		}
	});
});
$('#inputForm').validate({
	submitHandler: function(form){
		let area = parseFloat($('#area').val()) || 0;
		let approvedArea = parseFloat($('#approvedArea').val()) || 0;
		if (approvedArea < area) {
			js.confirm('实际使用面积超过核定面积，是否继续提交？', function() {
				js.ajaxSubmitForm($(form), function(data) {
					js.showMessage(data.message);
					if (data.result == Global.TRUE) {
						js.closeCurrentTabPage(function(contentWindow) {
							// contentWindow.page();
							contentWindow.$('#roomDataGrid').dataGrid('refresh');
							contentWindow.$('#dataGrid').dataGrid('refresh');
						});
					}
				}, "json");
			});
		} else {
			// 正常提交
			js.ajaxSubmitForm($(form), function(data) {
				js.showMessage(data.message);
				if (data.result == Global.TRUE) {
					js.closeCurrentTabPage(function(contentWindow) {
						console.log(contentWindow);
						// contentWindow.page();
						contentWindow.$('#roomDataGrid').dataGrid('refresh');
						contentWindow.$('#dataGrid').dataGrid('refresh');
					});
				}
			}, "json");
		}
    }
});
</script>