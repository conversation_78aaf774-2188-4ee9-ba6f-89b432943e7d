<% layout('/layouts/default.html', {title: '房地产信息', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="row">
			<div class="col-xs-6">
				<div class="box-header">
					<div class="box-title">
						<i class="fa icon-notebook"></i> ${text('房屋管理')}
					</div>
					<div class="box-tools pull-right">
						<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
						<% if(hasPermi('estate:realEstateAddress:edit')){ %>
						<a href="${ctx}/estate/realEstateAddress/form" class="btn btn-default btnTool" title="${text('新增房屋')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
						<% } %>
						<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
					</div>
				</div>

				<div class="box-body">
					<#form:form id="searchForm" model="${realEstateAddress}" action="${ctx}/estate/realEstateAddress/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
					<div class="form-group">
						<label class="control-label">${text('地址名称')}：</label>
						<div class="control-inline">
							<#form:input path="name" maxlength="1000" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('使用单位')}：</label>
						<div class="control-inline">
							<#form:input path="usedOffice.officeName" maxlength="1000" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('行政区划')}：</label>
						<div class="control-inline width-120">
							<#form:treeselect id="regionSelect" title="行政区划"
								path="region"
								url="${ctx}/sys/area/treeData?parentCode=0"
								class="" allowClear="true" returnFullName="true"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('详细地址')}：</label>
						<div class="control-inline">
							<#form:input path="address" maxlength="1000" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('层数')}：</label>
						<div class="control-inline">
							<#form:input path="floorCount" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
			</div>
			<div class="col-xs-6">
				<div class="box-header">
					<div class="box-title">
						<i class="fa icon-notebook"></i> ${text('房间管理')}
					</div>
<!--					<div class="box-tools pull-right">-->
<!--&lt;!&ndash;						<a href="#" class="btn btn-default" id="btnRoomSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>&ndash;&gt;-->
<!--						<% if(hasPermi('estate:realEstate:edit')){ %>-->
<!--						<a href="${ctx}/estate/realEstate/form" class="btn btn-default btnTool" title="${text('新增房间')}"><i class="fa fa-plus"></i> ${text('新增')}</a>-->
<!--						<% } %>-->
<!--&lt;!&ndash;						<a href="#" class="btn btn-default" id="btnRoomSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>&ndash;&gt;-->
<!--					</div>-->
				</div>

				<div class="box-body">
				<#form:form id="roomSearchForm" model="${realEstate}" action="${ctx}/estate/realEstate/listData" method="post" class="form-inline"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
					<div class="form-group">
						<label class="control-label">${text('类型')}：</label>
						<div class="control-inline" style="min-width: 80px;">
							<#form:select id="type" path="type" dictType="occupancy_classification" blankOption="true" class="form-control" />
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('用途')}：</label>
						<div class="control-inline" style="min-width: 80px;">
							<#form:select path="puroise" dictType="ob_house_purpose" blankOption="true" class="form-control" />
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('面积')}：</label>
						<div class="control-inline" style="width: 50px;">
							<#form:input path="area" class="form-control" style="width: 50px;"/>
						</div>
					</div>
					<#form:hidden path="realEstateAddressId"/>
					<div class="form-group" style="margin-left: 5px;">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
						<% if(hasPermi('estate:realEstate:edit')){ %>
						<a href="${ctx}/estate/realEstate/form" class="btn btn-default btn-sm" title="${text('新增房间')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
						<% } %>
					</div>
				</#form:form>
				<table id="roomDataGrid"></table>
				<div id="roomDataGridPage"></div>
			</div>

			</div>
		</div>
	</div>
</div>
<% } %>
<script>
	$(function () {

//# // 初始化DataGrid对象
		$('#dataGrid').dataGrid({
			searchForm: $('#searchForm'),
			columnModel: [
				{header:'${text("名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
						return '<a href="${ctx}/estate/realEstateAddress/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑不动产地址表")}">'+(val||row.id)+'</a>';
					}},
				{header:'${text("地址")}', name:'address', index:'a.address', width:150, align:"left"},
				{header:'${text("使用单位")}', name:'usedOffice.officeName', index:'usedOffice.officeName', width:150, align:"center"},
				{header:'${text("产权单位")}', name:'ownerOffice.officeName', index:'ownerOffice.officeName', width:150, align:"center"},
				{header:'${text("房屋结构")}', name:'structure', index:'a.structure', width:150, align:"center", formatter: function (val, obj, row, act) {
					return js.getDictLabel("#{@DictUtils.getDictListJson('ob_structure_type')}", val, '${text("未知")}', true);
				}},
				{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
						var actions = [];
						//# if(hasPermi('estate:realEstateAddress:edit')){
						actions.push('<a href="${ctx}/estate/realEstateAddress/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑不动产地址表")}">编辑</a>&nbsp;');
						actions.push('<a href="${ctx}/estate/realEstateAddress/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除不动产地址表")}" data-confirm="${text("确认要删除该不动产地址表吗？")}">删除</a>&nbsp;');
						//# }
						return actions.join('');
					}}
			],
			sortableColumn: false,
			onSelectRow: function(id, isSelect, event){
				$('#realEstateAddressId').val(id);
				$('#roomDataGrid').dataGrid('refresh');
			},
			//# // 加载成功后执行事件
			ajaxSuccess: function(data){

			}
		});

		$('#roomDataGrid').dataGrid({
			searchForm: $('#roomSearchForm'),
			columnModel: [
				{header:'${text("名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
						return '<a href="${ctx}/estate/realEstate/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑不动产信息表")}">'+(val||row.id)+'</a>';
					}},
				{header:'${text("类型")}', name:'type', index:'a.type', width:150, align:"center", formatter: function(val, obj, row, act){
						return js.getDictLabel("#{@DictUtils.getDictListJson('occupancy_classification')}", val, '未知', true);
					}},
				{header:'${text("用途")}', name:'puroise', index:'a.puroise', width:150, align:"center", formatter: function(val, obj, row, act){
						return js.getDictLabel("#{@DictUtils.getDictListJson('ob_house_purpose')}", val, '未知', true);
					}},
				{header:'${text("房屋")}', name:'realEstateAddress.name', index:'a.real_estate_address_id', width:150, align:"left"},
				{header:'${text("所属楼层")}', name:'realEstateAddressFloor.name', index:'realEstateAddressFloor.name', width:150, align:"center"},
				{header:'${text("面积")}', name:'area', index:'a.area', width:150, align:"center"},
				{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
						var actions = [];
						//# if(hasPermi('estate:realEstate:edit')){
						actions.push('<a href="${ctx}/estate/realEstate/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑不动产信息表")}">编辑</a>&nbsp;');
						actions.push('<a href="${ctx}/estate/realEstate/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除不动产信息表")}" data-confirm="${text("确认要删除该不动产信息表吗？")}">删除</a>&nbsp;');
						//# }
						return actions.join('');
					}}
			],
			sortableColumn: false,
			//# // 加载成功后执行事件
			ajaxSuccess: function(data){

			}
		});
	})
</script>