<% layout('/layouts/default.html', {title: '地图查询', libs: ['dataGrid', 'fileupload']}){ %>
<style>
    .map-control-panel {
        position: absolute;
        top: 20px;
        left: 20px;
        width: 380px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        height: 465px !important;;
        border: 1px solid #ebeef5;
    }
</style>
<div class="main-content">
    <div class="box box-main">
        <div class="box-body">
            <div class="content pb0">
                <div class="row">
                    <div id="mapContainer" class="col-sm-12" style="height: 800px; position: relative;">
                        <div id="map" style="height: 100%;"></div>

                        <div id="infoPanel" class="panel panel-default map-info-panel">
                            <i class="fa fa-times info-panel-close"></i>
                            <h4 class="info-title" style="margin-top: 0;"></h4>
                            <div class="info-content">

                            </div>
                        </div>



                        <div id="roomDetailModal" class="modal fade" role="dialog">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        <h4 class="modal-title">房间详情 - <span id="roomTitle"></span></h4>
                                    </div>
                                    <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <dl class="dl-horizontal">
                                                    <dt>房间编号：</dt>
                                                    <dd id="roomCode">-</dd>
                                                    <dt>房间面积：</dt>
                                                    <dd id="roomArea">-</dd>
<!--                                                    <dt>使用状态：</dt>-->
<!--                                                    <dd id="roomStatus">-</dd>-->
                                                </dl>
                                            </div>
<!--                                            <div class="col-md-6">-->
<!--                                                <dl class="dl-horizontal">-->
<!--                                                    <dt>所属楼层：</dt>-->
<!--                                                    <dd id="roomFloor">-</dd>-->
<!--                                                    <dt>使用单位：</dt>-->
<!--                                                    <dd id="roomOffice">-</dd>-->
<!--                                                    <dt>最后更新：</dt>-->
<!--                                                    <dd id="roomUpdate">-</dd>-->
<!--                                                </dl>-->
<!--                                            </div>-->
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <h5>房间照片</h5>
                                                <div id="roomImages" class="row" style="text-align: center"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <a id="roomDetailLink" href="#" class="btn btn-primary" target="_blank">
                                            <i class="fa fa-info-circle"></i> 查看完整详情
                                        </a>
                                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                                    </div>
                                </div>
                            </div>
                        </div>





                        <div class="panel panel-default map-control-panel">
                            <div class="panel-body">
                                <div class="form-group">
                                    <#form:form id="searchForm" model="${realEstateAddress}" action="${ctx}/estate/realEstateAddress/listData" method="post" class="form-inline"
                                        data-page-no="${parameter.pageNo}" data-page-size="7" data-order-by="${parameter.orderBy}">
                                        <div class="input-group">
                                            <#form:input id="name" path="name" maxlength="1000" class="form-control isQuick" style="width: 320px;"/>
                                            <span class="input-group-btn">
                                            <button class="btn btn-primary" type="submit"><i class="fa fa-search"></i></button>
                                          </span>
                                        </div>
                                    </#form:form>
                                    <table id="dataGrid"></table>
                                    <div id="dataGridPage" style="height: 350px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<% } %>
<style>
    /* 优化信息面板样式 */
    .map-info-panel {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 420px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 6px 16px rgba(0,0,0,0.15);
        z-index: 1000;
        padding: 20px;
        display: none;
        max-height: 85vh;
        overflow-y: auto;
    }
    .info-panel-close {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 18px;
        color: #999;
        cursor: pointer;
        transition: color 0.3s;
    }
    .info-panel-close:hover {
        color: #666;
    }
    .info-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
    }
    .house-images {
        margin: 10px 0;
    }
    .house-images .carousel-inner {
        border-radius: 4px;
        height: 200px;
    }
    .house-images img {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    .floor-list {
        margin-top: 15px;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }
    .floor-item {
        margin-bottom: 15px;
    }
    .floor-title {
        font-weight: bold;
        margin-bottom: 8px;
        color: #666;
    }
    .room-tag {
        display: inline-block;
        padding: 4px 12px;
        margin: 4px;
        background: #f0f2f5;
        border-radius: 15px;
        cursor: pointer;
        transition: all 0.3s;
    }
    .room-tag:hover {
        background: #e6f7ff;
        color: #1890ff;
        box-shadow: 0 2px 8px rgba(24,144,255,0.2);
    }
    #roomDetailModal .modal-body dt {
        width: 100px;
        text-align: left;
    }
    #roomDetailModal .modal-body dd {
        margin-left: 120px;
    }
    #roomImages img {
        width: 180px;
        height: 120px;
        object-fit: cover;
        margin: 5px;
        border-radius: 4px;
        cursor: pointer;
        transition: transform 0.3s;
    }
    #roomImages img:hover {
        transform: scale(1.05);
    }
</style>
<script type="text/javascript" src="${@Global.getConfig('tianditu.baseUrl')}"></script>
<script id="fileuploadTpl" type="text/template">//<!--<div>
<#form:fileupload id="{{d.id}}" bizKey="{{d.bizKey}}" bizType="{{d.bizType}}" uploadType="all" maxUploadNum="1"
	class="{{d.cssClass}}" isMini="false" preview="true" readonly="{{d.readonly}}"/>
</div>//--></script>
<script>

    function handleRoomClick(id, name, serialNumber, area) {
        showRoomDetail({id: id, name: name, serialNumber: serialNumber, area: area});
        // 这里可以添加房间点击后的处理逻辑，例如：
        // 1. 显示房间详情弹窗
        // 2. 在地图上定位房间位置
        // 3. 跳转到房间详情页面

        // 示例：在地图上添加标记（需要根据实际情况获取坐标）
        // let marker = new T.Marker(new T.LngLat(经度, 纬度));
        // map.addOverLay(marker);
    }
    // 添加显示房间详情的函数
    function showRoomDetail(roomData) {
        // 基础信息
        $('#roomTitle').text(roomData.name || '未命名房间');
        $('#roomCode').text(roomData.serialNumber || '-');
        $('#roomArea').text(roomData.area ? roomData.area + '㎡' : '-');

        // 详情链接
        $('#roomDetailLink').attr('href', ``+ctx+`/estate/realEstate/form?id=`+roomData.id+``);

        $('#roomImages').empty();
        $.ajax({
            url: ctx +'/file/fileList?__t=' + new Date().getTime(),
            data: {
                "bizKey" : roomData.id,
                "bizType" : 'realEstate_cad_file'
            },
            async: false,
            dataType : 'json',
            success : function(data){
                console.log(data);
                if (!(data.result == "false")) {
                    for (var i in data){
                        let fileUrl = data[i].fileUrl;
                        $('#roomImages').append(`<img src="` + ctxPath + fileUrl + `" style="width:380px; height:380px; cursor:zoom-in;" onclick="showImagePreview('` + ctxPath + fileUrl + `')" />`);
                    }
                } else {
                    $('#roomImages').html('<div class="col-md-12 text-muted">暂无照片</div>');
                }
            }
        });


        // // 显示弹窗
        $('#roomDetailModal').modal('show');
    }
    let defaultLng = 119.291320;
    let defaultLat = 26.077380;
    let map, geocoder;
    $(function(){
        function initMap() {
            map = new T.Map('map', {projection: 'EPSG:4326'});
            geocoder = new T.Geocoder();
            map.centerAndZoom(new T.LngLat(defaultLng, defaultLat), 15);
        }
        initMap();


    });

    // 显示信息窗口
    function showInfoWindow(lng, lat, name) {
        const content = `【` + name + `】<br>`;
        const infoWindow = new T.InfoWindow(content, {offset: new T.Point(0, -30)});
        infoWindow.setLngLat(new T.LngLat(lng, lat));
        map.addOverLay(infoWindow);
    }
    $('#dataGrid').dataGrid({
        searchForm: $('#searchForm'),
        columnModel: [
            {header:'${text("id")}',name: 'id', hidden: true},
            {header:'${text("latitude")}',name: 'latitude', hidden: true},
            {header:'${text("longitude")}',name: 'longitude', hidden: true},
            {header:'${text("地址名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
                    return val;
                }}
        ],
        onSelectRow: function(id, isSelect, event){
            let data = $('#dataGrid').dataGrid('getRowData');
            if (data) {
                let clickRowData = data.find(item => item.id === id);
                if (clickRowData.longitude.length > 0 && clickRowData.latitude.length > 0) {
                    map.centerAndZoom(new T.LngLat(clickRowData.longitude, clickRowData.latitude), 15);
                } else {
                    js.showMessage("${text('请配置坐标')}", null, 'warning');
                }
            } else {
                js.showMessage("${text('数据不存在')}", null, 'warning');
            }
            // $('#realEstateAddressId').val(id);
            // $('#roomDataGrid').dataGrid('refresh');
        },
        //# // 加载成功后执行事件
        ajaxSuccess: function(data){
            map.clearOverLays();
            let zoomArr = [];
            data.list.forEach(address => {
                if (address.longitude && address.latitude) {
                    let marker = new T.Marker(new T.LngLat(address.longitude, address.latitude), {draggable: false});
                    // 添加点击事件
                    marker.addEventListener('click', function(e){
                        showInfoPanel(address);
                        map.panTo(e.lnglat);
                    });
                    map.addOverLay(marker);
                    showInfoWindow(address.longitude, address.latitude, address.name);
                    zoomArr.push(new T.LngLat(address.longitude, address.latitude));
                }
            });
            map.setViewport(zoomArr);
        },
        showRownum: false,
        autoGridHeight: function () {
            return 280;
        },
    });
    function showImagePreview(src, width = 1200, height = 800) {
        const overlay = $('<div class="image-preview-overlay">')
            .css({
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'rgba(0,0,0,0.8)',
                zIndex: 9999,
                cursor: 'zoom-out',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
            })
            .click(function() {
                $(this).remove();
            });

        const img = $('<img>')
            .attr('src', src)
            .css({
                maxWidth: width + 'px',
                maxHeight: height + 'px',
                borderRadius: '4px',
                boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
            });

        overlay.append(img);
        $('body').append(overlay);
    }
    function showInfoPanel(address) {
        const panel = $('#infoPanel');

        const content = `
            <div class="row" style="margin-top: 5px;">
                <label class="col-md-3">详细地址：</label>
                <div class="col-md-9">` + (address.address || '-') + `</div>
            </div>
            <div class="row" style="margin-top: 5px;">
                <table style="width: 100%;" id="addressInfoGrid"></table>
            </div>
            <div class="row" style="margin-top: 5px;">
                <table style="width: 100%; height: 60px !important;" id="usedOfficeInfoGrid"></table>
            </div>
            <div class="row" style="margin-top: 5px;">
                <table style="width: 100%;" id="floorListGrid"></table>
            </div>
        `;
        panel.find('.info-title').text(address.name + ' ' + address.floorCount + '层');
        panel.find('.info-content').html(content);
        $('#addressInfoGrid').dataGrid({
            data: [],
            datatype: 'local', // 设置本地数据
            headers: false,
            columnModel: [
                {header:'主键', name:'id', editable:true, hidden:true},
                {header:'${text("现场照片")}', name: "id", width: 200, align: "center",
                    formatter: function(val, obj, row) {
                        if (val) {
                            let imgContent = "";
                            $.ajax({
                                url: ctx +'/file/fileList?__t=' + new Date().getTime(),
                                data: {
                                    "bizKey" : val,
                                    "bizType" : 'realEstateAddress_exterior_file'
                                },
                                async: false,
                                dataType : 'json',
                                success : function(data){
                                    if (!(data.result == "false")) {
                                        for (var i in data){
                                            let fileUrl = data[i].fileUrl;
                                            imgContent += `<img src="` + ctxPath + fileUrl + `" style="max-width:180px; max-height:100px; cursor:zoom-in;" onclick="showImagePreview('` + ctxPath + fileUrl + `')" />`;
                                        }
                                    }
                                }
                            });
                            return imgContent;
                        }
                        return '-';
                    },
                    editable: false // 设置为不可编辑
                },
            ],
            // 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
            autoGridHeight: () => '100px',   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
            autoGridWidth: true,    // 自动表格宽度（设置为false后，不自动调整表格宽度）
            showRownum: false,	    // 是否显示行号
            showCheckbox: false,    // 是否显示复选框
            sortableColumn: false,   // 列表是否允许排序（设置为false后，整个列表不允许排序）
            editGrid: true,				// 是否是编辑表格
            editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
            editGridAddRowInitData: {id: address.id},
            imageThumbName: '150*150.jpg'
        });

        $('#usedOfficeInfoGrid').dataGrid({
            data: [address.usedOffice?address.usedOffice: {officeName: undefined}],
            datatype: 'local', // 设置本地数据
            headers: false,
            columnModel: [
                {header:'主键', name:'id', editable:true, hidden:true},
                {header:'${text("使用单位名称")}', name: "officeName", width: 200, align: "center", formatter: function(val, obj, row) {
                    return val?'<a href="${ctx}/sys/office/form?officeCode='+row.officeCode+'" class="hsBtnList" data-title="${text("查看机构")}">'+(val||row.id)+'</a>': '-';
                }},
            ],
            // 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
            autoGridHeight: () => '80px',   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
            autoGridWidth: true,    // 自动表格宽度（设置为false后，不自动调整表格宽度）
            showRownum: false,	    // 是否显示行号
            showCheckbox: false,    // 是否显示复选框
            sortableColumn: false,   // 列表是否允许排序（设置为false后，整个列表不允许排序）
            editGrid: true,				// 是否是编辑表格
            editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
            editGridAddRowInitData: {id: address.id}
        });
        panel.fadeIn(200);
        $('#addressInfoGrid').dataGrid('reloadGrid');
        $('#usedOfficeInfoGrid').dataGrid('reloadGrid');


        js.ajaxSubmit(ctx + '/estate/realEstateAddress/findFloorAndRoomDto', {
            id: address.id
        }, function(data){

            $('#floorListGrid').dataGrid({
                data: data,
                datatype: 'local', // 设置本地数据
                headers: false,
                columnModel: [
                    {header:'主键', name:'id', editable:true, hidden:true},
                    {header:'${text("楼层")}', name: "id", width: 30, align: "center",
                        formatter: function(val, obj, row) {
                            return row.realEstateAddressFloor?.name || '-';
                        },
                    },
                    {header:'${text("房间")}', name: "id", width: 200, align: "center",
                        formatter: function(val, obj, row) {
                            let roomContent = `<div class="room-container" style="white-space: nowrap; overflow-x: auto; padding: 4px 0; max-height: 120px;">`;
                            row.realEstateList.forEach( item => {
                                roomContent += `
                                <div class="room-tag"
                                     data-id="`+item.id+`"
                                     data-name="`+item.name+`"
                                     onclick="handleRoomClick('`+item.id+`','`+item.name+`','`+item.serialNumber+`','`+item.area+`')"
                                     style="display: inline-flex;
                                            align-items: center;
                                            padding: 4px 12px;
                                            margin: 2px 4px;
                                            background: #f0f9eb;
                                            border-radius: 15px;
                                            color: #67c23a;
                                            border: 1px solid #e1f3d8;
                                            cursor: pointer;
                                            transition: all 0.3s;
                                            flex-shrink: 0;">
                                    `+item.name+`
                                    <i class="fa fa-home" style="margin-left: 6px; font-size: 12px;"></i>
                                </div>`;
                            });
                            roomContent += `</div>`;
                            return roomContent || '<div style="color: #999;">暂无房间信息</div>';
                        },
                    },
                ],
                // 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
                autoGridHeight: false,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
                autoGridWidth: true,    // 自动表格宽度（设置为false后，不自动调整表格宽度）
                showRownum: false,	    // 是否显示行号
                showCheckbox: false,    // 是否显示复选框
                sortableColumn: false,   // 列表是否允许排序（设置为false后，整个列表不允许排序）
                editGrid: false,				// 是否是编辑表格
                editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
                editGridAddRowInitData: {id: address.id},
            });
        });






        // 点击关闭按钮隐藏面板
        panel.find('.info-panel-close').off('click').on('click', function () {
            panel.fadeOut(200);
            $("#addressInfoGrid").jqGrid('GridUnload');
        });

        // 点击地图空白处隐藏面板
        map.addEventListener('click', function closePanel() {
            panel.fadeOut(200);
            map.removeEventListener('click', closePanel);
        });
    }

    $("#gridId").closest(".ui-jqgrid-view").find(".ui-jqgrid-hbox").hide();
</script>

<script>

</script>