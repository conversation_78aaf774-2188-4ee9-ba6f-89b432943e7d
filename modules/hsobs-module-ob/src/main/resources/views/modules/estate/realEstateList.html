<% layout('/layouts/default.html', {title: '不动产信息表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('不动产信息表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('estate:realEstate:edit')){ %>
					<a href="${ctx}/estate/realEstate/form" class="btn btn-default btnTool" title="${text('新增不动产信息表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${realEstate}" action="${ctx}/estate/realEstate/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('不动产名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="1000" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('不动产类型')}：</label>
					<div class="control-inline">
						<#form:input path="type" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('不动产用途')}：</label>
					<div class="control-inline">
						<#form:input path="puroise" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('地址ID')}：</label>
					<div class="control-inline">
						<#form:input path="realEstateAddressId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('组织机构')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="organizationId" title="${text('机构选择')}"
							path="organizationId" labelPath="" 
							url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('所属楼层')}：</label>
					<div class="control-inline">
						<#form:input path="floorNumber" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('面积')}：</label>
					<div class="control-inline">
						<#form:input path="area" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("不动产名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/estate/realEstate/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑不动产信息表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("不动产类型")}', name:'type', index:'a.type', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('occupancy_classification')}", val, '未知', true);
		}},
		{header:'${text("不动产用途")}', name:'puroise', index:'a.puroise', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('ob_house_purpose')}", val, '未知', true);
		}},
		{header:'${text("地址")}', name:'realEstateAddressName', index:'a.real_estate_address_id', width:150, align:"left"},
		{header:'${text("组织机构")}', name:'officeName', index:'a.organization_id', width:150, align:"center"},
		{header:'${text("所属楼层")}', name:'floorNumber', index:'a.floor_number', width:150, align:"center"},
		{header:'${text("面积")}', name:'area', index:'a.area', width:150, align:"center"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("修改时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('estate:realEstate:edit')){
				actions.push('<a href="${ctx}/estate/realEstate/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑不动产信息表")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/estate/realEstate/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除不动产信息表")}" data-confirm="${text("确认要删除该不动产信息表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>