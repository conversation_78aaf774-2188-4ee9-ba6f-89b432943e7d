<% layout('/layouts/default.html', {title: '预警消息管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('预警消息管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${earlyWarnMessage}" action="${ctx}/earlywarnmessage//listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
<!--				<div class="form-group">-->
<!--					<label class="control-label">${text('编号')}：</label>-->
<!--				</div>-->
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('预警消息')}：</label>
					<div class="control-inline">
						<#form:input path="earlyWarnMessageInfo" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('办公用房单位')}：</label>
					<div class="control-inline " >
						<#form:treeselect id="officeOccupancyUnitId" title="${text('机构选择')}"
							path="officeOccupancyUnitId" labelPath="" 
							url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('用房分类')}：</label>
					<div class="control-inline ">
						<#form:select path="occupancyClassification" dictType="occupancy_classification" blankOption="true" class="form-control"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('使用人')}：</label>
					<div class="control-inline " >
						<#form:treeselect id="userId" title="${text('用户选择')}"
							path="userId" labelPath="" 
							url="${ctx}/sys/office/treeData?isLoadUser=true" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('职位')}：</label>
					<div class="control-inline ">
						<#form:select path="positionId" dictType="ob_establishment_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('配置面积')}：</label>
					<div class="control-inline">
						<#form:input path="allocationArea" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('预警日期范围')}：</label>
					<div class="control-inline">
						<#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('预警类型')}：</label>
					<div class="control-inline ">
						<#form:select path="earlyWarnType" dictType="early_warn_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('预警状态')}：</label>
					<div class="control-inline ">
						<#form:select path="earlyWarnState" dictType="early_warn_state" blankOption="true" class="form-control"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('备注')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("编号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/earlywarnmessage//form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑预警消息")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("预警消息")}', name:'earlyWarnMessageInfo', index:'a.early_warn_message_info', width:150, align:"left"},
		{header:'${text("办公用房单位")}', name:'office.officeName', index:'a.office_occupancy_unit_id', width:150, align:"left"},
		{header:'${text("用房分类")}', name:'occupancyClassification', index:'a.occupancy_classification', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('occupancy_classification')}", val, '${text("未知")}', true);
		}},
		{header:'${text("使用人")}', name:'employee.empName', index:'a.user_id', width:150, align:"left"},
		{header:'${text("职位")}', name:'positionId', index:'a.position_id', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_establishment_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("配置面积")}', name:'allocationArea', index:'a.allocation_area', width:150, align:"left"},
		{header:'${text("预警日期")}', name:'warningDate', index:'a.warning_date', width:150, align:"left"},
		{header:'${text("预警类型")}', name:'earlyWarnType', index:'a.early_warn_type', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('early_warn_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("预警状态")}', name:'earlyWarnState', index:'a.early_warn_state', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('early_warn_state')}", val, '${text("未知")}', true);
		}},
		{header:'${text("推送状态")}', name:'pushTag', index:'a.push_tag', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_msg_push_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"left"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('earlywarnmessage::edit')){
				actions.push('<a href="${ctx}/earlywarnmessage//sendEarlyWarnMessageToOA?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("推送预警消息")}" data-confirm="${text("确认要推送该预警消息吗？")}">推送</a>&nbsp;');
				actions.push('<a href="${ctx}/earlywarnmessage//form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑预警消息")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/earlywarnmessage//delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除预警消息")}" data-confirm="${text("确认要删除该预警消息吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script><script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/earlywarnmessage//exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入预警消息")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/earlywarnmessage//importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/earlywarnmessage//importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>