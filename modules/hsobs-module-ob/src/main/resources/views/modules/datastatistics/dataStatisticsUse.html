<% layout('/layouts/default.html', {title: '办公用房使用情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('办公用房使用情况')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataStatistics}" action="${ctx}/datastatistics//maintainData" method="post" class="form-inline" >
			<div class="form-group">
				<label class="control-label">${text('区域')}：</label>
				<div class="control-inline width-120">
					<#form:treeselect id="area" title="${text('区域选择')}"
					path="areaCode" labelName="area.areaName" labelValue="${company.area.treeNames!}"
					url="${ctx}/sys/area/treeData" returnFullName="true"
					class=" " allowClear="true"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('统计年度')}：</label>
				<div class="control-inline">
					<#form:input path="year" id="yaer" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="year" data-format="yyyy"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-title">
							<i class="fa icon-notebook"></i> ${text('使用申请情况')}
						</div>
						<div class="box-body">
							<div class="col-md-3">
								<div class="chart">
									<div id="pieChart" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart1 = echarts.init(document.getElementById('pieChart'), chartTheme);
										pieChart1.setOption(option = {
											title:{
												show:true,
												text:'办公用房使用情况统计'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '0%',
												left: '0%',
												orient: 'horizontal',
											},
											series: [
												{
													name: '办公用房使用情况统计',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: false,
														position: 'center'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '使用申请总数' },
														{ value: 735, name: '待审批数' },
														{ value: 580, name: '核发证件数' },
														{ value: 484, name: '用房安排数' }
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-2">
								<div class="chart">
									<div id="gaugeChart" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart1 = echarts.init(document.getElementById('gaugeChart'), chartTheme);
										gaugeChart1.setOption(option = {
											title: {
												show: true,
												text: '使用申请总数'
											},
											series: [
												{
													type: 'gauge',
													radius: '60%',
													center: ['50%', '60%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#37a2da'
													},
													progress: {
														show: true,
														width: 20
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 20
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-10%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 间',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-2">
								<div class="chart">
									<div id="gaugeChart2" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart21 = echarts.init(document.getElementById('gaugeChart2'), chartTheme);
										gaugeChart21.setOption(option = {
											title: {
												show: true,
												text: '待审批数'
											},
											series: [
												{
													type: 'gauge',
													radius: '60%',
													center: ['50%', '60%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#32c5e9'
													},
													progress: {
														show: true,
														width: 20
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 20
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-10%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 间',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-2">
								<div class="chart">
									<div id="gaugeChart3" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart31 = echarts.init(document.getElementById('gaugeChart3'), chartTheme);
										gaugeChart31.setOption(option = {
											title: {
												show: true,
												text: '核发证件数'
											},
											series: [
												{
													type: 'gauge',
													radius: '60%',
													center: ['50%', '60%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#9fe6b8'
													},
													progress: {
														show: true,
														width: 20
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 20
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-10%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 间',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-2">
								<div class="chart">
									<div id="gaugeChart4" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart41 = echarts.init(document.getElementById('gaugeChart4'), chartTheme);
										gaugeChart41.setOption(option = {
											title: {
												show: true,
												text: '用房安排数'
											},
											series: [
												{
													type: 'gauge',
													radius: '60%',
													center: ['50%', '60%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#9fe6b8'
													},
													progress: {
														show: true,
														width: 20
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 20
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-10%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 间',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-title">
							<i class="fa icon-notebook"></i> ${text('办公用房使用面积分析')}
						</div>
						<div class="box-body">
							<div class="row" style="margin-left: 0px;margin-right: 0px;">
								<div class="col-md-6">
									<div class="chart">
										<div id="barChart2" style="height:430px;width:100%"></div><script>
										$(function(){
											barChart21 = echarts.init(document.getElementById('barChart2'), chartTheme);
											barChart21.setOption(option = {
												title: {
													show: true,
													text: '各单位使用面积占比'
												},
												tooltip: {
													trigger: 'axis',
													axisPointer: {
														type: 'shadow'
													}
												},
												grid: {
													left: '3%',
													right: '4%',
													bottom: '3%',
													containLabel: true
												},
												xAxis: [
													{
														type: 'category',
														data: ['省直', '福州', '厦门', '泉州', '莆田', '宁德', '漳州', '南平', '三明', '龙岩'],
														axisTick: {
															alignWithLabel: true
														}
													}
												],
												yAxis: [
													{
														type: 'value'
													}
												],
												series: [
													{
														name: '使用面积',
														type: 'bar',
														barWidth: '60%',
														data: [10, 52, 200, 334, 390, 330, 224, 390, 330, 220]
													}
												]
											});
										});
									</script>
									</div>
								</div>
								<div class="col-md-6">
									<div class="chart">
										<div id="barChart3" style="height:430px;width:100%"></div><script>
										$(function(){
											barChart31 = echarts.init(document.getElementById('barChart3'), chartTheme);
											barChart31.setOption(option = {
												title:{
													show:true,
													text:'各职级使用面积占比'
												},
												tooltip: {
													trigger: 'item'
												},
												legend: {
													top: '25%',
													right: '20%',
													orient: 'vertical'
												},
												series: [
													{
														name: '各职级使用面积占比',
														type: 'pie',
														center: ['30%', '50%'],
														avoidLabelOverlap: false,
														label: {
															show: false,
															position: 'center'
														},
														emphasis: {
															label: {
																show: true,
																fontSize: 14,
																fontWeight: 'bold'
															}
														},
														labelLine: {
															show: false
														},
														data: [
															{ value: 1048, name: '省级正职' },
															{ value: 735, name: '省级副职' },
															{ value: 580, name: '正厅（局）级' },
															{ value: 484, name: '副厅（局）级' },
															{ value: 484, name: '正处级' },
															{ value: 484, name: '副处级' },
															{ value: 484, name: '处级以下' }
														]
													}
												]
											});
										});
									</script>
									</div>
								</div>
							</div>
							<div class="row" style="margin-left: 0px;margin-right: 0px;">
								<div class="chart">
									<div id="barChart4" style="height:430px;width:100%;margin-top: 16px"></div><script>
									$(function(){
										barChart41 = echarts.init(document.getElementById('barChart4'), chartTheme);
										barChart41.setOption(option = {
											tooltip: {
												trigger: 'axis',
												axisPointer: {
													type: 'cross',
													crossStyle: {
														color: '#999'
													}
												}
											},
											title:{
												show:true,
												text:'办公用房超标使用分析'
												// left: '2%'
											},
											toolbox: {
												feature: {
													restore: { show: true },
													saveAsImage: { show: true }
												}
											},
											legend: {
												data: ['超标人数', '超标面积']
											},
											xAxis: [
												{
													type: 'category',
													data: ['省直', '福州', '厦门', '泉州', '莆田', '宁德', '漳州', '南平', '三明', '龙岩'],
													axisPointer: {
														type: 'shadow'
													}
												}
											],
											yAxis: [
												{
													type: 'value',
													name: '超标人数',
													min: 0,
													max: 250,
													interval: 50,
													axisLabel: {
														formatter: '{value} 人'
													}
												},{
													type: 'value',
													name: '超标面积',
													min: 0,
													max: 250,
													interval: 50,
													axisLabel: {
														formatter: '{value} m²'
													}
												}
											],
											series: [
												{
													name: '超标人数',
													type: 'bar',
													tooltip: {
														valueFormatter: function (value) {
															return value + ' m²';
														}
													},
													data: [
														2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
													]
												},
												{
													name: '超标面积',
													type: 'bar',
													tooltip: {
														valueFormatter: function (value) {
															return value + ' m²';
														}
													},
													data: [
														2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart1 = echarts.init(document.getElementById('barChart'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'办公用房使用状态分析'
											// left: '2%'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['办公室使用面积', '设备用房使用面积', '服务用房使用面积','技术用房使用面积','附属用房使用面积']
										},
										xAxis: [
											{
												type: 'category',
												data: ['省直', '福州', '厦门', '泉州', '莆田', '宁德', '漳州', '南平', '三明', '龙岩'],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '使用面积',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value} m²'
												}
											}
										],
										series: [
											{
												name: '办公室使用面积',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: [
													2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
												]
											},
											{
												name: '设备用房使用面积',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											},
											{
												name: '服务用房使用面积',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											},
											{
												name: '技术用房使用面积',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											},
											{
												name: '附属用房使用面积',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											}
										]
									};
									barChart1.setOption(option);
								});
							</script>
							</div>
						</div>
					</div>
				</div>
				<table id="dataGrid"></table>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){

	// 页面加载完成后首次获取数据
	$(document).ready(function () {
		// 使用 jQuery 动态获取数据并更新图表
		function calculateUsageAreaRatioByUnit(formData) {
			$.ajax({
				url: "${ctx}/datastatistics//calculateUsageAreaRatioByUnit", // 替换为你的数据接口 URL
				type: 'POST',
				data: formData,
				dataType: 'json',
				success: function (data) {

					// 更新图表数据
					barChart21.setOption({
						xAxis: [
							{
								data: data.categoryList
							}
						],
						series: [{
							data: data.dataList
						}]
					});
				},
				error: function (error) {
					console.error('获取数据失败:', error);
				}
			});
		}
		// 使用 jQuery 动态获取数据并更新图表
		function calculateUsageAreaRatioByRank(formData) {
			$.ajax({
				url: "${ctx}/datastatistics//calculateUsageAreaRatioByRank", // 替换为你的数据接口 URL
				type: 'POST',
				dataType: 'json',
				data: formData,
				success: function (data) {

					// 更新图表数据
					barChart31.setOption({
						series: [{
							data: data
						}]
					});
				},
				error: function (error) {
					console.error('获取数据失败:', error);
				}
			});
		}
		// 使用 jQuery 动态获取数据并更新图表
		function officeUsageStats(formData) {
			$.ajax({
				url: "${ctx}/datastatistics//officeUsageStats", // 替换为你的数据接口 URL
				type: 'POST',
				dataType: 'json',
				data: formData,
				success: function (data) {

					// 更新图表数据
					pieChart1.setOption({
						series: [{
							data: data
						}]
					});
				},
				error: function (error) {
					console.error('获取数据失败:', error);
				}
			});
		}
		// 使用 jQuery 动态获取数据并更新图表
		function officeUsageStatsCount(formData) {
			$.ajax({
				url: "${ctx}/datastatistics//officeUsageStatsCount", // 替换为你的数据接口 URL
				type: 'POST',
				dataType: 'json',
				data: formData,
				success: function (data) {

					// 更新图表数据
					gaugeChart1.setOption({
						series: [{
							data: data.number
						}]
					});
					// 更新图表数据
					gaugeChart21.setOption({
						series: [{
							data: data.awaitNumber
						}]
					});
					// 更新图表数据
					gaugeChart31.setOption({
						series: [{
							data: data.issueNumber
						}]
					});
					// 更新图表数据
					gaugeChart41.setOption({
						series: [{
							data: data.availableNumber
						}]
					});
				},
				error: function (error) {
					console.error('获取数据失败:', error);
				}
			});
		}
		// 使用 jQuery 动态获取数据并更新图表
		function analyzeOfficeSpaceUsageStatus(formData) {
			$.ajax({
				url: "${ctx}/datastatistics//analyzeOfficeSpaceUsageStatus", // 替换为你的数据接口 URL
				type: 'POST',
				data: formData,
				dataType: 'json',
				success: function (data) {
					if(data.length==0){
						barChart1.setOption({})
					}else{
						// 更新图表数据
						barChart1.setOption({
							xAxis: [{
								//区域
								data: data.data2
							}],
							series: [{
								//办公室使用面积
								name:data.data3.name,
								data: data.data3.value
							},{
								//设备用房使用面积
								name:data.data4.name,
								data: data.data4.value
							},{
								//服务用房使用面积
								name:data.data5.name,
								data: data.data5.value
							},{
								//技术用房使用面积
								name:data.data6.name,
								data: data.data6.value
							},{
								//附属用房使用面积
								name:data.data7.name,
								data: data.data7.value
							}]
						});
					}

				},
				error: function (error) {
					console.error('获取数据失败:', error);
				}
			});
		}
		// 使用 jQuery 动态获取数据并更新图表
		function analyzeOfficeSpaceAnalysis(formData) {
			$.ajax({
				url: "${ctx}/datastatistics//analyzeOfficeSpaceAnalysis", // 替换为你的数据接口 URL
				type: 'POST',
				data: formData,
				dataType: 'json',
				success: function (data) {
					if(data.length==0){
						barChart1.setOption({})
					}else{
						// 更新图表数据
						barChart41.setOption({
							xAxis: [{
								//区域
								data: data.data2
							}],
							series: [{
								//超标人数
								name:data.data3.name,
								data: data.data3.value
							},{
								//超标面积
								name:data.data4.name,
								data: data.data4.value
							}]
						});
					}

				},
				error: function (error) {
					console.error('获取数据失败:', error);
				}
			});
		}

		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			calculateUsageAreaRatioByUnit();
			calculateUsageAreaRatioByRank();
			analyzeOfficeSpaceUsageStatus();
			analyzeOfficeSpaceAnalysis();
			officeUsageStats();
			officeUsageStatsCount();
		});
		$('#searchForm').on('submit', function (event) {
			// 阻止表单默认提交行为
			event.preventDefault();
				var formData = $('#searchForm').serialize();
				calculateUsageAreaRatioByUnit(formData);
				calculateUsageAreaRatioByRank(formData);
				analyzeOfficeSpaceUsageStatus(formData);
				analyzeOfficeSpaceAnalysis(formData);
				officeUsageStats(formData);
				officeUsageStatsCount(formData);
		});

		$('#searchForm').on('reset', function (event) {
			// 阻止表单默认提交行为
			event.preventDefault();
			$("#searchForm")[0].reset(); // 先执行表单的默认重置行为

			// 手动清空 dataStatistics 相关的字段
			$("#searchForm").find("input, select, textarea").each(function () {
				$(this).val('');
			});

			// 清空 treeselect 组件的值（如果适用）
			$("#area").attr("labelValue", "").attr("labelName", "");

			// 触发 change 事件，确保 UI 组件更新
			$("#searchForm").find("input, select").trigger("change");

				calculateUsageAreaRatioByUnit();
				calculateUsageAreaRatioByRank();
				analyzeOfficeSpaceUsageStatus();
				analyzeOfficeSpaceAnalysis();
				officeUsageStats();
				officeUsageStatsCount();
		});
	});

	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(pieChart1) pieChart1.resize();
		if(gaugeChart1) gaugeChart1.resize();
		if(gaugeChart21) gaugeChart21.resize();
		if(gaugeChart31) gaugeChart31.resize();
		if(gaugeChart41) gaugeChart41.resize();
		if(barChart1) barChart1.resize();
		if(barChart21) barChart21.resize();
		if(barChart31) barChart31.resize();
		if(barChart41) barChart41.resize();
	}).resize();
});

</script>