<% layout('/layouts/default.html', {title: '办公用房闲置情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('办公用房闲置情况')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataStatisticsForResource}" action="${ctx}/datastatistics//unuseData" method="post" class="form-inline hide" >
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline width-120">
						<#form:treeselect id="area" title="${text('区域选择')}"
						path="areaCode" labelName="area.areaName" labelValue="${company.area.treeNames!}"
						url="${ctx}/sys/area/treeData" returnFullName="true"
						class=" " allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('单位名称')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="applicantUnitId" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('闲置日期')}：</label>
					<div class="control-inline">
						<#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">办公用房闲置情况表</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>

//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("区域")}', name:'areaName', index:'a.areaName', width:150, align:"left"},
		{header:'${text("单位信息")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
		{header:'${text("办公用房数量")}', name:'officeNumber', index:'a.officeNumber', width:150, align:"left"},
		{header:'${text("闲置日期")}', name:'unuseDate', index:'a.unuseDate', width:150, align:"left"},
		{header:'${text("闲置数量")}', name:'unuseNumber', index:'a.unuseNumber', width:150, align:"left"},
		{header:'${text("闲置面积（m²）")}', name:'unuseSpace', index:'a.unuseSpace', width:150, align:"left"},
		{header:'${text("闲置率（%）")}', name:'unuseRate', index:'a.unuseRate', width:150, align:"left"}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/datastatistics//exportUnuseData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>