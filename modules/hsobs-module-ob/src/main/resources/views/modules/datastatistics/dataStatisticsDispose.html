<% layout('/layouts/default.html', {title: '办公用房处置利用情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('办公用房处置利用情况')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${disposeTable}" action="${ctx}/datastatistics//disposeData" method="post" class="form-inline hide" >
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline width-120">
						<#form:treeselect id="area" title="${text('区域选择')}"
						path="region" labelName="area.areaName" labelValue="${company.area.treeNames!}"
						url="${ctx}/sys/area/treeData" returnFullName="true"
						class=" " allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('单位名称')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="applicantUnitId" title="${text('机构选择')}"
						path="office" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('统计年度')}：</label>
					<div class="control-inline">
						<#form:input path="year" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="year" data-format="yyyy"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="content pb0">
				<div class="row" style="text-align: right;padding-right: 10px;">
					<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">省直单位办公业务用房处置利用情况表</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("统计年度")}', name:'yearCount', index:'a.yearCount', width:150, align:"left"},
		{header:'${text("区域")}', name:'areaName', index:'a.areaName', width:150, align:"left"},
		{header:'${text("处置类型")}', name:'disposeType', index:'a.disposeType', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('disposal_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("处置单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
		{header:'${text("处置数量")}', name:'disposeNumber', index:'a.disposeNumber', width:150, align:"left"},
		{header:'${text("处置面积")}', name:'disposeArea', index:'a.disposeArea', width:150, align:"left"}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/datastatistics//exportDisposeData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>