<% layout('/layouts/default.html', {title: '办公用房维修情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('办公用房维修情况')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataStatisticsForResource}" action="${ctx}/datastatistics//maintainData" method="post" class="form-inline hide" >
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline width-120">
						<#form:treeselect id="area" title="${text('区域选择')}"
						path="areaCode" labelName="area.areaName" labelValue="${company.area.treeNames!}"
						url="${ctx}/sys/area/treeData" returnFullName="true"
						class=" " allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('统计年度')}：</label>
					<div class="control-inline">
						<#form:input path="year" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="year" data-format="yyyy"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart1 = echarts.init(document.getElementById('barChart'), chartTheme);
									barChart1.setOption(option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'全省办公用房维修总数'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['总数', '待审批', '已审批',"已维修"]
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '维修申请',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value} 次'
												}
											}
										],
										series: [
											{
												name: '总数',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 次';
													}
												},
												data: []
											},
											{
												name: '待审批',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 次';
													}
												},
												data: []
											},
											{
												name: '已审批',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 次';
													}
												},
												data: []
											},
											{
												name: '已维修',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 次';
													}
												},
												data: []
											}
										]
									});
									// 监听图表的点击事件
									barChart1.on('click', function (params) {
										// params 是一个对象，包含了点击事件的相关信息
										window.location.href = "${ctx}/repair/request/listLedger"
									});
								});
							</script>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">省直单位办公业务用房维修情况表</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-title">
							<i class="fa icon-notebook"></i> ${text('维修情况')}
						</div>
						<div class="box-body">
							<div class="col-md-3">
								<div class="chart">
									<div id="pieChart" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart1 = echarts.init(document.getElementById('pieChart'), chartTheme);
										pieChart1.setOption(option = {
											title:{
												show:true,
												text:'办公用房维修情况统计'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '0%',
												left: '0%',
												orient: 'horizontal',
											},
											series: [
												{
													name: '办公用房维修情况统计',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: false,
														position: 'center'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '水箱维修' },
														{ value: 735, name: '照明维修' },
														{ value: 580, name: '煤气管道维修' },
														{ value: 484, name: '其他维修' }
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-2">
								<div class="chart">
									<div id="gaugeChart" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart1 = echarts.init(document.getElementById('gaugeChart'), chartTheme);
										gaugeChart1.setOption(option = {
											title: {
												show: true,
												text: '水箱维修'
											},
											series: [
												{
													type: 'gauge',
													radius: '60%',
													center: ['50%', '60%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#37a2da'
													},
													progress: {
														show: true,
														width: 20
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 20
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-10%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 次',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-2">
								<div class="chart">
									<div id="gaugeChart2" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart21 = echarts.init(document.getElementById('gaugeChart2'), chartTheme);
										gaugeChart21.setOption(option = {
											title: {
												show: true,
												text: '照明维修'
											},
											series: [
												{
													type: 'gauge',
													radius: '60%',
													center: ['50%', '60%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#32c5e9'
													},
													progress: {
														show: true,
														width: 20
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 20
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-10%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 次',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-2">
								<div class="chart">
									<div id="gaugeChart3" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart31 = echarts.init(document.getElementById('gaugeChart3'), chartTheme);
										gaugeChart31.setOption(option = {
											title: {
												show: true,
												text: '煤气管道维修'
											},
											series: [
												{
													type: 'gauge',
													radius: '60%',
													center: ['50%', '60%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#9fe6b8'
													},
													progress: {
														show: true,
														width: 20
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 20
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-10%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 次',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-2">
								<div class="chart">
									<div id="gaugeChart4" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart41 = echarts.init(document.getElementById('gaugeChart4'), chartTheme);
										gaugeChart41.setOption(option = {
											title: {
												show: true,
												text: '其他维修'
											},
											series: [
												{
													type: 'gauge',
													radius: '60%',
													center: ['50%', '60%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#9fe6b8'
													},
													progress: {
														show: true,
														width: 20
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 20
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-10%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 次',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart2" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart21 = echarts.init(document.getElementById('barChart2'), chartTheme);
									barChart21.setOption(option = {
										title: {
											show: true,
											text: '维修费用分析'
										},
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'shadow'
											}
										},
										grid: {
											left: '3%',
											right: '4%',
											bottom: '3%',
											containLabel: true
										},
										xAxis: [
											{
												type: 'category',
												data: ['水箱维修费用', '照明维修费用', '路灯维修蜂拥', '煤气管道维修费用', '其他维修费用'],
												axisTick: {
													alignWithLabel: true
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												axisLabel: {
													formatter: '{value} 元'
												}
											}
										],
										series: [
											{
												name: '维修费用',
												type: 'bar',
												barWidth: '60%',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 元';
													}
												},
												data: [10, 52, 200, 334, 390]
											}
										]
									});
								});
							</script>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(pieChart1) pieChart1.resize();
		if(gaugeChart1) gaugeChart1.resize();
		if(gaugeChart21) gaugeChart21.resize();
		if(gaugeChart31) gaugeChart31.resize();
		if(gaugeChart41) gaugeChart41.resize();
		if(barChart1) barChart1.resize();
		if(barChart21) barChart21.resize();
	}).resize();
});

//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("办公用房信息")}', name:'roomInfo', index:'a.roomInfo', width:150, align:"center"},
		{header:'${text("房号及名称")}', name:'roomNumber', index:'a.roomNumber', width:150, align:"center"},
		{header:'${text("使用单位")}', name:'officeName', index:'a.officeName', width:150, align:"center"},
		{header:'${text("办公用房名称")}', name:'roomName', index:'a.roomName', width:150, align:"center"},
		{header:'${text("申请时间")}', name:'applyTime', index:'a.applyTime', width:150, align:"center"},
		{header:'${text("审批时间")}', name:'auditTime', index:'a.auditTime', width:150, align:"center"},
		{header:'${text("状态")}', name:'maintainStatus', index:'a.maintainStatus', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 使用 jQuery 动态获取数据并更新图表
			function fetchOfficeData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//maintainCount", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart1.setOption({
							xAxis: [{
								//区域
								data: data.data2
							}],
							series: [{
								//总数
								name:data.data3.name,
								data: data.data3.value
							},{
								//待审批
								name:data.data4.name,
								data: data.data4.value
							},{
								//已审批
								name:data.data5.name,
								data: data.data5.value
							},{
								//已维修
								name:data.data6.name,
								data: data.data6.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 使用 jQuery 动态获取数据并更新图表
			function fetchCountData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//maintainCountData", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart1.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 使用 jQuery 动态获取数据并更新图表
			function maintainCosAanalysis(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//maintainCosAanalysis", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart21.setOption({
							series: [{
								data: data.dataList
							}],
							xAxis: [
								{
									data: data.newList
								}
							],
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}

			// 使用 jQuery 动态获取数据并更新图表
			function fetchNumberData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//maintainNumberCountData", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						gaugeChart1.setOption({
							series: [{
								data: data.number1
							}]
						});
						// 更新图表数据
						gaugeChart21.setOption({
							series: [{
								data: data.number2
							}]
						});
						// 更新图表数据
						gaugeChart31.setOption({
							series: [{
								data: data.number3
							}]
						});
						// 更新图表数据
						gaugeChart41.setOption({
							series: [{
								data: data.number4
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}

			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchOfficeData(formData);
				fetchCountData(formData);
				maintainCosAanalysis(formData);
				fetchNumberData(formData);
			});
		});
	}
});
</script>