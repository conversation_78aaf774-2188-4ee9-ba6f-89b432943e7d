<% layout('/layouts/default.html', {title: '办公用房权属情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('办公用房权属情况')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataStatisticsForResource}" action="${ctx}/datastatistics//ownershipData" method="post" class="form-inline hide" >
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline width-120">
						<#form:treeselect id="area" title="${text('区域选择')}"
						path="areaCode" labelName="area.areaName" labelValue="${company.area.treeNames!}"
						url="${ctx}/sys/area/treeData" returnFullName="true"
						class=" " allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('登记时间')}：</label>
					<div class="control-inline">
						<#form:input path="year" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="year" data-format="yyyy"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart1 = echarts.init(document.getElementById('barChart'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'办公用房权属情况统计'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['总数','待审批','已审批']
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '权属登记数量',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value} 间'
												}
											}
										],
										series: [
											{
												name: '总数',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 间';
													}
												},
												data: [
													2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
												]
											},
											{
												name: '待审批',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 间';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											},
											{
												name: '已审批',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 间';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											}
										]
									};
									barChart1.setOption(option);
									// 监听图表的点击事件
									barChart1.on('click', function (params) {
										// params 是一个对象，包含了点击事件的相关信息
										window.location.href = "${ctx}/ownership/registration/listQuery"
									});
								});
							</script>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">省直单位办公业务用房房产权属情况表</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(barChart1) barChart1.resize();
	}).resize();
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		$(window).resize();
	});
	$('.ui-sortable').sortable({
		connectWith : '.ui-sortable',
		handle      : '.box-header, .nav-tabs',
		placeholder : 'sort-highlight',
		forcePlaceholderSize: true,
		zIndex : 999999
	}).find('.box-header, .nav-tabs').css('cursor', 'move');
});
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("办公用房信息")}', name:'roomInfo', index:'a.roomInfo', width:150, align:"left"},
		{header:'${text("房号及名称")}', name:'roomNumber', index:'a.roomNumber', width:150, align:"left"},
		{header:'${text("使用单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
		{header:'${text("办公用房名称")}', name:'roomName', index:'a.roomName', width:150, align:"left"},
		{header:'${text("登记时间")}', name:'registerTime', index:'a.registerTime', width:150, align:"left"},
		{header:'${text("审批时间")}', name:'auditTime', index:'a.auditTime', width:150, align:"left"},
		{header:'${text("状态")}', name:'ownershipStatus', index:'a.ownershipStatus', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 使用 jQuery 动态获取数据并更新图表
			function fetchOfficeData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//ownershipCount", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart1.setOption({
							xAxis: [{
								//区域
								data: data.data2
							}],
							series: [{
								//总数
								name:data.data3.name,
								data: data.data3.value
							},{
								//待审批
								name:data.data4.name,
								data: data.data4.value
							},{
								//已审批
								name:data.data5.name,
								data: data.data5.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}

			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchOfficeData(formData);
			});
		});
	}
});
</script>