<% layout('/layouts/default.html', {title: '地图查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-body">
			<div class="content pb0">
				<div class="row">
					<div id="mapContainer" class="col-sm-12" style="height: 800px; position: relative;">
						<div class="map-search-container">
							<div class="map-search-box">
								<div class="input-group">
									<input type="text" id="searchAddress" class="form-control"
										   placeholder="请输入地址进行搜索"
										   autocomplete="off">
									<span class="input-group-btn">
										<button class="btn btn-primary" type="button" id="btnSearch">
											<i class="fa fa-search"></i>
										</button>
									</span>
								</div>
								<div class="search-results-panel">
									<div id="searchResults" class="result-section"></div>
									<div id="pagination" class="result-pagination">
									</div>
								</div>
							</div>
						</div>
						<div id="map" style="height: 100%;"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>

<style>
	.map-search-container {
		position: absolute;
		top: 20px;
		left: 20px;
		z-index: 999;
		width: 400px;
	}

	.map-search-box {
		background: #fff;
		border-radius: 4px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.15);
		transition: all 0.3s ease;
	}

	.input-group {
		position: relative;
		display: flex;
	}

	#searchAddress {
		height: 40px;
		border: none;
		border-radius: 4px 0 0 4px;
		padding: 8px 15px;
		flex: 1;
	}

	#btnSearch {
		height: 40px;
		width: 50px;
		border: none;
		border-radius: 0 4px 4px 0;
		background: #1890ff;
		transition: background 0.3s;
	}

	#btnSearch:hover {
		background: #1472d6;
	}

	.search-results-panel {
		max-height: 500px;
		overflow-y: auto;
		display: none;
		border-top: 1px solid #eee;
		animation: slideDown 0.3s ease;
	}

	@keyframes slideDown {
		from { opacity: 0; transform: translateY(-10px); }
		to { opacity: 1; transform: translateY(0); }
	}

	.result-section {
		padding: 12px 15px;
		border-bottom: 1px solid #eee;
	}

	.result-section:last-child {
		border-bottom: none;
	}

	.result-section ul {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	.result-section li {
		padding: 8px 0;
		cursor: pointer;
		transition: background 0.2s;
	}

	.result-section li:hover {
		background: #f5f5f5;
	}

	.result-pagination {
		padding: 12px;
		text-align: center;
	}

	.search-results-panel {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 8px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		margin-top: 8px;
	}

	.poi-item {
		padding: 16px;
		border-bottom: 1px solid #eee;
		transition: all 0.2s ease;
		cursor: pointer;
		display: flex;
		gap: 12px;
		align-items: flex-start;
	}

	.poi-item:hover {
		background: #f8f9fa;
		transform: translateX(4px);
	}

	.poi-item:last-child {
		border-bottom: none;
	}

	.poi-content {
		flex: 1;
		min-width: 0;
	}

	.poi-title {
		font-weight: 500;
		color: #333;
		font-size: 15px;
		margin-bottom: 4px;
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.poi-address {
		color: #666;
		font-size: 13px;
		line-height: 1.4;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.search-empty {
		padding: 24px;
		text-align: center;
		color: #999;
	}
	.result-pagination {
		padding: 16px;
		background: #f8f9fa;
		border-radius: 0 0 8px 8px;
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
		align-items: center;
		justify-content: center;
	}

	.pagination-btn {
		height: 32px;
		padding: 0 12px;
		border: 1px solid #e8e8e8;
		border-radius: 6px;
		background: white;
		color: #666;
		font-size: 14px;
		cursor: pointer;
		transition: all 0.2s ease;
		display: inline-flex;
		align-items: center;
		gap: 6px;
	}

	.pagination-btn:hover:not(:disabled) {
		border-color: #1890ff;
		color: #1890ff;
		box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
	}

	.pagination-btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		background: #f5f5f5;
	}

	.page-jump-container {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-left: 12px;
	}

	.page-jump-input {
		width: 60px;
		height: 32px;
		padding: 0 8px;
		border: 1px solid #e8e8e8;
		border-radius: 6px;
		text-align: center;
		font-size: 14px;
	}

	.page-jump-input:focus {
		border-color: #1890ff;
		outline: none;
		box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
	}

	.icon-btn {
		width: 32px;
		padding: 0;
		justify-content: center;
	}

	.current-page {
		color: #1890ff;
		font-weight: 500;
		margin: 0 8px;
	}
</style>
<script type="text/javascript" src="${@Global.getConfig('tianditu.baseUrl')}"></script>
<script>
	$(function(){

		let map = new T.Map('map', { projection: 'EPSG:4326' });
		let localsearch;
		const pageCapacity = 10;

		map.centerAndZoom(new T.LngLat(119.291320,26.077380), 12);

		function clearSearchResults() {
			map.clearOverLays();
			$('#searchResults, #suggestsDiv, #statisticsDiv, #promptDiv').empty();
			$('.search-results-panel').slideUp(300);
		}

		const config = {
			pageCapacity: pageCapacity,
			onSearchComplete: localSearchResult
		};
		function initLocalSearch() {
			localsearch = new T.LocalSearch(map, config);
		}

		function localSearchResult(result) {
			$('.search-results-panel').stop(true, true).slideDown(300);
			$('#searchResults').empty();
			switch(parseInt(result.getResultType())) {
				case 1:
					updatePagination(result);
					processPois(result.getPois());
					break;
				case 3:
					processArea(result.getArea());
					break;
				default:
					break;
			}
		}

		function processPois(pois) {

			if(!pois) {
				$('#searchResults').html(`
					<div class="search-empty">
						<i class="fa fa-map-marker-slash fa-2x"></i>
						<div style="margin-top:8px">未找到相关地点</div>
					</div>
				`);
				return;
			}
			var zoomArr = [];
			pois.forEach((poi, index) => {
				var name = poi.name;
				var address = poi.address;

				var lnglatArr;
				const lonlat = poi.lonlat;
				const commaIndex = lonlat.indexOf(",");
				lnglatArr = commaIndex !== -1 ? lonlat.split(",") : lonlat.split(" ");
				var lnglat = new T.LngLat(lnglatArr[0], lnglatArr[1]);

				var winHtml = "名称:" + name + "<br/>地址:" + address;
				var marker = new T.Marker(lnglat);
				map.addOverLay(marker);
				var markerInfoWin = new T.InfoWindow(winHtml, {autoPan: true});
				marker.addEventListener("click", function () {
					marker.openInfoWindow(markerInfoWin);
				});

				zoomArr.push(lnglat);

				const item = $(`
					<div class="poi-item">
						<div class="poi-content">
							<div class="poi-title">
								<span>` + poi.name + `</span>
							</div>
							<div class="poi-address">
								` + (poi.address? poi.address: '暂无地址信息') + `
							</div>
						</div>
					</div>
				`);
				item.click(() => {
					centerMarker(poi);
					showPosition(marker, winHtml);
				});
				$('#searchResults').append(item);
			});
			map.setViewport(zoomArr);
		}

		function showPosition(marker, winHtml) {
			var markerInfoWin = new T.InfoWindow(winHtml, {autoPan: true});
			marker.openInfoWindow(markerInfoWin);
		}
		function processArea(obj) {
			if (obj) {
				if(obj.points){
					var pointsArr = [];
					var points = obj.points;
					for (var i = 0; i < points.length; i++) {
						var regionLngLats = [];
						var regionArr = points[i].region.split(",");
						for (var m = 0; m < regionArr.length; m++) {
							var lnglatArr;
							const lonlat = obj.lonlat;
							const commaIndex = lonlat.indexOf(",");
							lnglatArr = commaIndex !== -1 ? lonlat.split(",") : lonlat.split(" ");
							var lnglat = new T.LngLat(lnglatArr[0], lnglatArr[1]);

							regionLngLats.push(lnglat);
							pointsArr.push(lnglat);
						}
						var line = new T.Polyline(regionLngLats, {
							color: "blue",
							weight: 3,
							opacity: 1,
							lineStyle: "dashed"
						});
						map.addOverLay(line);
					}
					map.setViewport(pointsArr);
				}
				if(obj.lonlat){
					var lnglatArr;
					const lonlat = obj.lonlat;
					const commaIndex = lonlat.indexOf(",");
					lnglatArr = commaIndex !== -1 ? lonlat.split(",") : lonlat.split(" ");
					map.panTo(new T.LngLat(lnglatArr[0], lnglatArr[1]));
				}
			}
		}

		window.handleMapPaginationClick = function(action) {
			const actions = {
				first: () => localsearch.firstPage(),
				prev: () => localsearch.previousPage(),
				next: () => localsearch.nextPage(),
				last: () => localsearch.lastPage()
			};
			actions[action]();
		}
		function updatePagination(result) {
			const html = `
				<button class="pagination-btn icon-btn" onclick="handleMapPaginationClick('first')" >
					<i class="fa fa-angle-double-left"></i>
				</button>
				<button class="pagination-btn icon-btn" onclick="handleMapPaginationClick('prev')" >
					<i class="fa fa-chevron-left"></i>
				</button>
				<button class="pagination-btn icon-btn" onclick="handleMapPaginationClick('next')" }>
					<i class="fa fa-chevron-right"></i>
				</button>
				<button class="pagination-btn icon-btn" onclick="handleMapPaginationClick('last')" >
					<i class="fa fa-angle-double-right"></i>
				</button>
			`;
			$('#pagination').html(html);
		}

		$('#btnSearch').click(performSearch);
		$('#searchAddress').keypress(performSearch);

		function performSearch() {
			const keyword = $('#searchAddress').val().trim();
			if(keyword) {
				localsearch.search(keyword);
			}
		}

		$('#searchAddress').on('input', function() {
			if (!this.value.trim()) clearSearchResults();
		});

		function centerMarker(obj) {
			var lnglatArr;
			const lonlat = obj.lonlat;
			const commaIndex = lonlat.indexOf(",");
			lnglatArr = commaIndex !== -1 ? lonlat.split(",") : lonlat.split(" ");
			var lnglat = new T.LngLat(lnglatArr[0], lnglatArr[1]);
			map.panTo(lnglat);
			map.setZoom(15);
		}
		initLocalSearch();

	});
</script>