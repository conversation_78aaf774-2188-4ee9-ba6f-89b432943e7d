<% layout('/layouts/default.html', {title: '办公用房资源情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('办公用房资源情况')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataStatisticsForResource}" action="${ctx}/datastatistics//resourceCaseData" method="post" class="form-inline hide" >
				<div class="form-group">
					<label class="control-label">${text('办公用房类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="officeType" dictType="occupancy_classification" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('单位名称')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="applicantUnitId" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		<#form:form id="spaceVerificationForm" model="${dataStatisticsForResource}" action="${ctx}/datastatistics//spaceVerificationData" method="post" class="form-inline hide"
		data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}"></#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="col-md-3">
								<div class="chart">
									<div id="pieChart" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart1 = echarts.init(document.getElementById('pieChart'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'办公用房功能统计'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '0%',
												left: '0%',
												orient: 'horizontal',
												itemWidth: 10,
												itemHeight: 15
											},
											series: [
												{
													name: '办公用房功能统计',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: false,
														position: 'center'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '办公室用房' },
														{ value: 735, name: '服务用房' },
														{ value: 580, name: '设备用房' },
														{ value: 484, name: '附属用房' },
														{ value: 300, name: '技术用房' },
														{ value: 50, name: '其他' }
													]
												}
											]
										};
										pieChart1.setOption(option);
									});
								</script>
								</div>
							</div>
							<div class="col-md-3">
								<div class="chart">
									<div id="gaugeChart" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart1 = echarts.init(document.getElementById('gaugeChart'), chartTheme);
										var option = {
											title: {
												show: true,
												text: '办公用房数量'
											},
											series: [
												{
													type: 'gauge',
													center: ['50%', '70%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#37a2da'
													},
													progress: {
														show: true,
														width: 30
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 30
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-15%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 间',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										};
										gaugeChart1.setOption(option);
									});
								</script>
								</div>
							</div>
							<div class="col-md-3">
								<div class="chart">
									<div id="gaugeChart2" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart21 = echarts.init(document.getElementById('gaugeChart2'), chartTheme);
										gaugeChart21.setOption(option = {
											title: {
												show: true,
												text: '办公用房总面积'
											},
											series: [
												{
													type: 'gauge',
													center: ['50%', '70%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#32c5e9'
													},
													progress: {
														show: true,
														width: 30
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 30
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-15%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} m²',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-3">
								<div class="chart">
									<div id="gaugeChart3" style="height:230px;width:100%"></div><script>
									$(function(){
										gaugeChart31 = echarts.init(document.getElementById('gaugeChart3'), chartTheme);
										gaugeChart31.setOption(option = {
											title: {
												show: true,
												text: '办公用房单位数量'
											},
											series: [
												{
													type: 'gauge',
													center: ['50%', '70%'],
													splitNumber: 5,
													startAngle: 200,
													endAngle: -20,
													min: 0,
													max: 600,
													itemStyle: {
														color: '#9fe6b8'
													},
													progress: {
														show: true,
														width: 30
													},
													pointer: {
														show: false
													},
													axisLine: {
														lineStyle: {
															width: 30
														}
													},
													axisTick: {
														show: false
													},
													splitLine: {
														distance: -10,
														length: 0,
													},
													axisLabel: {
														distance: -20,
														color: '#999',
														fontSize: 14
													},
													anchor: {
														show: false
													},
													title: {
														show: false
													},
													detail: {
														valueAnimation: true,
														width: '60%',
														lineHeight: 40,
														borderRadius: 8,
														offsetCenter: [0, '-15%'],
														fontSize: 14,
														fontWeight: 'bolder',
														formatter: '{value} 家',
														color: 'inherit'
													},
													data: [
														{
															value: 200
														}
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">省直单位办公业务用房房产资源情况表(表一)</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="row" style="text-align: right;padding-right: 30px;">
						<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
					</div>
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">省直单位办公业务用房面积核定表(表二)</h3>
							</div>
							<table id="dataGrid2"></table>
							<div id="dataGrid2Page"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(pieChart1) pieChart1.resize();
		if(gaugeChart1) gaugeChart1.resize();
		if(gaugeChart21) gaugeChart21.resize();
		if(gaugeChart31) gaugeChart21.resize();
	}).resize();
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		$(window).resize();
	});
	$('.ui-sortable').sortable({
		connectWith : '.ui-sortable',
		handle      : '.box-header, .nav-tabs',
		placeholder : 'sort-highlight',
		forcePlaceholderSize: true,
		zIndex : 999999
	}).find('.box-header, .nav-tabs').css('cursor', 'move');
});
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("单位名称")}', name:'officeName', index:'a.officeName', width:150, align:"left",frozen: true},
		{header:'${text("单位类别")}', name:'officeType', index:'a.officeType', width:150, align:"left",frozen: true, formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_office_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("地址坐落")}', name:'address', index:'a.address', width:150, align:"left",frozen: true},
		{header:'${text("楼号及所在楼层")}', name:'floorNumber', index:'a.floorNumber', width:150, align:"left",frozen: true},
		{header:'${text("省级正职")}', name:'principalNumber', index:'a.principalNumber', width:150, align:"left"},
		{header:'${text("省级副职")}', name:'deputyNumber', index:'a.deputyNumber', width:150, align:"left"},
		{header:'${text("正厅（局）级")}', name:'bureauNumber', index:'a.deputyNumber', width:150, align:"left"},
		{header:'${text("副厅（局）级")}', name:'deputyBureauNumber', index:'a.deputyNumber', width:150, align:"left"},
		{header:'${text("正处级")}', name:'divisionNumber', index:'a.deputyNumber', width:150, align:"left"},
		{header:'${text("副处级")}', name:'deputyDivisionNumber', index:'a.deputyNumber', width:150, align:"left"},
		{header:'${text("处级以下")}', name:'underDivisionNumber', index:'a.deputyNumber', width:150, align:"left"},
		{header:'${text("单位编制总数")}', name:'staffingTotalNumber', index:'a.deputyNumber', width:150, align:"left"},
		{header:'${text("办公室")}', name:'officeSpace', index:'a.officeSpace', width:150, align:"left"},
		{header:'${text("服务用房")}', name:'serviceSpace', index:'a.serviceSpace', width:150, align:"left"},
		{header:'${text("设备用房")}', name:'deviceSpace', index:'a.serviceSpace', width:150, align:"left"},
		{header:'${text("合计")}', name:'baseTotalSpace', index:'a.serviceSpace', width:150, align:"left"},
		{header:'${text("建筑面积")}', name:'structureTotalSpace', index:'a.serviceSpace', width:150, align:"left"},
		{header:'${text("现有附属用房建筑面积")}', name:'auxiliarySpace', index:'a.auxiliarySpace', width:150, align:"left"},
		{header:'${text("现有办公用房建筑面积")}', name:'officeNowSpace', index:'a.auxiliarySpace', width:150, align:"left"},
		{header:'${text("现有技术用房建筑面积")}', name:'technologySpace', index:'a.technologySpace', width:150, align:"left"},
		{header:'${text("现有办公业务用房总建筑面积")}', name:'totalSpace', index:'a.technologySpace', width:150, align:"left"},
		{header:'${text("用地面积")}', name:'siteSpace', index:'a.siteSpace', width:150, align:"left"},
		{header:'${text("产权单位")}', name:'propertyUnit', index:'a.siteSpace', width:150, align:"left"},
		{header:'${text("建设年代")}', name:'constructionAge', index:'a.siteSpace', width:150, align:"left"},
		{header:'${text("是否为租（借）用")}', name:'rentTag', index:'a.siteSpace', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_yes_no')}", val, '${text("未知")}', true);
		}},
		{header:'${text("备注")}', name:'remark', index:'a.siteSpace', width:150, align:"left"}
	],
	// ================ 设置多级表头 BEGIN ==============
	// 设置多级表头
	groupHeaders: {
		twoLevel:[
			{startColumnName: 'officeSpace', numberOfColumns: 5, titleText: '现有基本办公用房面积'},
			{startColumnName: 'principalNumber', numberOfColumns:8, titleText:'单位批复编制人员情况（人）'}
		],
		threeLevel:[
			{startColumnName: 'spaceVerification.officeSpace', numberOfColumns:6, titleText:'现有基本办公用房面积'}
		]
	},
	frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	// ================ 设置多级表头 END ==============
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
//# // 初始化DataGrid对象
$('#dataGrid2').dataGrid({
	url: '${ctx}/datastatistics/spaceVerificationData',
	dataGridPage: $('#dataGrid2Page'),
	columnModel: [
		{header:'${text("单位名称")}', name:'officeName', index:'a.officeName', width:150, align:"left",frozen: true},
		{header:'${text("单位性质")}', name:'officeType', index:'a.officeType', width:150, align:"left",frozen: true, formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_office_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("编制人数")}', name:'staffingTotalNumber', index:'a.staffingTotalNumber', width:150, align:"left",frozen: true},
		{header:'${text("办公室")}', name:'officeSpace', index:'a.officeSpace', width:150, align:"left"},
		{header:'${text("服务用房")}', name:'serviceSpace', index:'a.serviceSpace', width:150, align:"left"},
		{header:'${text("设备用房")}', name:'deviceSpace', index:'a.deviceSpace', width:150, align:"left"},
		{header:'${text("小计")}', name:'baseTotalSpace', index:'a.baseTotalSpace', width:150, align:"left"},
		{header:'${text("建筑面积")}', name:'structureTotalSpace', index:'a.structureTotalSpace', width:150, align:"left"},
		{header:'${text("业务用房")}', name:'businessSpace', index:'a.businessSpace', width:150, align:"left"},
		{header:'${text("合计")}', name:'totalSpace', index:'a.totalSpace', width:150, align:"left"},
		{header:'${text("基本办公用房")}', name:'officeSiteSpace', index:'a.officeSiteSpace', width:150, align:"left"},
		{header:'${text("业务用房")}', name:'businessSiteSpace', index:'a.businessSiteSpace', width:150, align:"left"},
		{header:'${text("合计")}', name:'totalSiteSpace', index:'a.totalSiteSpace', width:150, align:"left"},
		{header:'${text("超标面积")}', name:'overproofTotalSpace', index:'a.overproofTotalSpace', width:150, align:"left"},
		{header:'${text("备注")}', name:'remark', index:'a.remark', width:150, align:"left"}
	],
	// ================ 设置多级表头 BEGIN ==============
	// 设置多级表头
	groupHeaders: {
		twoLevel:[
			{startColumnName: 'officeSpace', numberOfColumns: 5, titleText: '基本办公用房'}
		],
		threeLevel:[
			{startColumnName: 'officeSpace', numberOfColumns:7, titleText:'核定办公业务用房总建筑面积'},
			{startColumnName: 'officeSiteSpace', numberOfColumns:3, titleText:'现有总建筑面积'}
		]
	},
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	// ================ 设置多级表头 END ==============
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 使用 jQuery 动态获取数据并更新图表
			function fetchOfficeData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//officeResourceCout", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart1.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 使用 jQuery 动态获取数据并更新图表
			function fetchNumberData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//officeResourceNumberCout", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						gaugeChart1.setOption({
							series: [{
								data: data.number
							}]
						});
						// 更新图表数据
						gaugeChart21.setOption({
							series: [{
								data: data.area
							}]
						});
						// 更新图表数据
						gaugeChart31.setOption({
							series: [{
								data: data.officeNumber
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchOfficeData(formData);
				fetchNumberData(formData);
				// 定时获取数据（例如每秒获取一次数据）
				// setInterval(fetchData, 1000); // 可根据需要调整时间间隔
			});
		});
	}
});
$("#searchForm").unbind('submit').submit(function(e){
	// 添加成立再执行查询
	if (true) {
		// 获取表单数据
		var formData = $(this).serializeArray();
		console.log(formData)
		// 更新两个jqGrid实例的查询参数
		$('#dataGrid2').jqGrid('setGridParam', { postData: formData });
		$('#dataGrid2').trigger("reloadGrid");
		$("#dataGrid").dataGrid('refresh', 1);
		$("#dataGrid2").dataGrid('refresh', 1);
	}
	return false;
});

$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/datastatistics//exportSpaceVerificationData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>