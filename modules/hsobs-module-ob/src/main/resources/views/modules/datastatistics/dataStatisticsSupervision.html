<% layout('/layouts/default.html', {title: '办公用房监督整改情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('办公用房监督整改情况')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataStatisticsForSupervision}" action="${ctx}/datastatistics//supervisionListData" method="post" class="form-inline hide" >
			<div class="form-group">
				<label class="control-label">${text('区域')}：</label>
				<div class="control-inline width-120">
					<#form:treeselect id="area" title="${text('区域选择')}"
					path="region" labelName="area.areaName" labelValue="${company.area.treeNames!}"
					url="${ctx}/sys/area/treeData" returnFullName="true"
					class=" " allowClear="true"/>
				</div>
			</div>
				<div class="form-group">
					<label class="control-label">${text('单位名称')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="office" title="${text('机构选择')}"
						path="office" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('日期')}：</label>
					<div class="control-inline">
						<#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-title">
							<i class="fa icon-notebook"></i> ${text('办公用房监督整改情况')}
						</div>
						<div class="box-body">
							<div class="col-md-2" style="width: 20%;">
								<div class="chart">
									<div id="pieChart" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart1 = echarts.init(document.getElementById('pieChart'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'办公室用房情况'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '0%',
												left: '0%',
												orient: 'horizontal'
											},
											series: [
												{
													name: '办公室用房情况',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: true,
														position: 'inside',
														formatter: '{b}:{c}'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '抽查数' },
														{ value: 735, name: '问题数' },
														{ value: 580, name: '已整改数' },
														{ value: 484, name: '未整改数' }
													]
												}
											]
										};
										pieChart1.setOption(option);


									});

								</script>
								</div>
							</div>
							<div class="col-md-2" style="width: 20%;">
								<div class="chart">
									<div id="pieChart1" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart11 = echarts.init(document.getElementById('pieChart1'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'服务用房情况'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '0%',
												left: '0%',
												orient: 'horizontal'
											},
											series: [
												{
													name: '服务用房情况',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: true,
														position: 'inside',
														formatter: '{b}:{c}'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '抽查数' },
														{ value: 735, name: '问题数' },
														{ value: 580, name: '已整改数' },
														{ value: 484, name: '未整改数' }
													]
												}
											]
										};
										pieChart11.setOption(option);
									});
								</script>
								</div>
							</div>
							<div class="col-md-2" style="width: 20%;">
								<div class="chart">
									<div id="pieChart2" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart21 = echarts.init(document.getElementById('pieChart2'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'设备用房情况'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '0%',
												left: '0%',
												orient: 'horizontal'
											},
											series: [
												{
													name: '设备用房情况',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: true,
														position: 'inside',
														formatter: '{b}:{c}'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '抽查数' },
														{ value: 735, name: '问题数' },
														{ value: 580, name: '已整改数' },
														{ value: 484, name: '未整改数' }
													]
												}
											]
										};
										pieChart21.setOption(option);
									});
								</script>
								</div>
							</div>
							<div class="col-md-2" style="width: 20%;">
								<div class="chart">
									<div id="pieChart3" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart31 = echarts.init(document.getElementById('pieChart3'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'附属用房情况'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '0%',
												left: '0%',
												orient: 'horizontal'
											},
											series: [
												{
													name: '附属用房情况',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: true,
														position: 'inside',
														formatter: '{b}:{c}'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '抽查数' },
														{ value: 735, name: '问题数' },
														{ value: 580, name: '已整改数' },
														{ value: 484, name: '未整改数' }
													]
												}
											]
										};
										pieChart31.setOption(option);
									});
								</script>
								</div>
							</div>
							<div class="col-md-2" style="width: 20%;">
								<div class="chart">
									<div id="pieChart4" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart41 = echarts.init(document.getElementById('pieChart4'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'技术用房情况'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '0%',
												left: '0%',
												orient: 'horizontal'
											},
											series: [
												{
													name: '技术用房情况',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: true,
														position: 'inside',
														formatter: '{b}:{c}'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '抽查数' },
														{ value: 735, name: '问题数' },
														{ value: 580, name: '已整改数' },
														{ value: 484, name: '未整改数' }
													]
												}
											]
										};
										pieChart41.setOption(option);
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row" style="text-align: right;padding-right: 10px;">
					<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">办公用房监督整改情况表</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>

var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(pieChart1) pieChart1.resize();
		if(pieChart11) pieChart11.resize();
		if(pieChart21) pieChart21.resize();
		if(pieChart31) pieChart31.resize();
		if(pieChart41) pieChart41.resize();
	}).resize();
});
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("区域")}', name:'areaName', index:'a.areaName', width:150, align:"left"},
		{header:'${text("单位信息")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
		{header:'${text("业务类型")}', name:'businessType', index:'a.businessType', width:150, align:"left"},
		{header:'${text("抽查数")}', name:'spotNumber', index:'a.spotNumber', width:150, align:"left"},
		{header:'${text("问题数")}', name:'questionNumber', index:'a.questionNumber', width:150, align:"left"},
		{header:'${text("已整改数")}', name:'correctedNumber', index:'a.correctedNumber', width:150, align:"left"},
		{header:'${text("未整改数")}', name:'uncorrectedNumber', index:'a.uncorrectedNumber', width:150, align:"left"}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 使用 jQuery 动态获取数据并更新图表
			function fetchOfficeData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//officeOccupancy", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart1.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			function fetchServiceData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//serviceOccupancy", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart11.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			function fetchDeviceData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//deviceOccupancy", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart21.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			function fetchAccessoryData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//accessoryOccupancy", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart31.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			function fetchTechnologyData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//technologyOccupancy", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart41.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchOfficeData(formData);
				fetchServiceData(formData);
				fetchDeviceData(formData);
				fetchAccessoryData(formData);
				fetchTechnologyData(formData);

				// 定时获取数据（例如每秒获取一次数据）
				// setInterval(fetchData, 1000); // 可根据需要调整时间间隔
			});
		});
	}
});
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/datastatistics//exportSupervisionListData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>