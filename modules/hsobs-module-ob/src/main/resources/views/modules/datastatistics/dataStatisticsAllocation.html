<% layout('/layouts/default.html', {title: '办公用房配置情况', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('办公用房配置情况')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataStatisticsForResource}" action="${ctx}/datastatistics//allocationData" method="post" class="form-inline hide" >
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline width-120">
						<#form:treeselect id="area" title="${text('区域选择')}"
						path="areaCode" labelName="area.areaName" labelValue="${company.area.treeNames!}"
						url="${ctx}/sys/area/treeData" returnFullName="true"
						class=" " allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('登记时间')}：</label>
					<div class="control-inline">
						<#form:input path="year" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="year" data-format="yyyy"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart1 = echarts.init(document.getElementById('barChart'), chartTheme);
									barChart1.setOption(option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'办公用房配置情况统计'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['总数','待审批','已审批','同比变化率']
										},
										xAxis: [
											{
												type: 'category',
												data: ['省直', '福州', '厦门', '泉州', '莆田', '宁德', '漳州', '南平', '三明', '龙岩'],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '配置数量',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value} 间'
												}
											}
										],
										series: [
											{
												name: '总数',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 间';
													}
												},
												data: [
													2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
												]
											},
											{
												name: '待审批',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 间';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											},
											{
												name: '已审批',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 间';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											},
											{
												name: '同比变化率',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' %';
													}
												},
												data: [
													2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
												]
											}
										]
									});
								});
							</script>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="col-md-3">
								<div class="chart">
									<div id="pieChart" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart1 = echarts.init(document.getElementById('pieChart'), chartTheme);
										pieChart1.setOption(option = {
											title:{
												show:true,
												text:'全省办公用房配置总数'
											},
											tooltip: {
												trigger: 'item'
											},
											legend: {
												top: '75%',
												right: '10%',
												left: '10%',
												orient: 'horizontal',
											},
											series: [
												{
													name: '全省办公用房配置总数',
													type: 'pie',
													center: ['50%', '40%'],
													radius: ['30%', '50%'],
													avoidLabelOverlap: false,
													label: {
														show: false,
														position: 'center'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
														{ value: 1048, name: '调剂数量' },
														{ value: 735, name: '置换数量' },
														{ value: 580, name: '租用数量' },
														{ value: 484, name: '建设数量' }
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
							<div class="col-md-9">
								<div class="chart">
									<div id="barChart2" style="height:330px;width:100%"></div><script>
									$(function(){
										barChart21 = echarts.init(document.getElementById('barChart2'), chartTheme);
										barChart21.setOption(option = {
											tooltip: {
												trigger: 'axis',
												axisPointer: {
													type: 'cross',
													crossStyle: {
														color: '#999'
													}
												}
											},
											title:{
												show:true,
												text:'办公用房配置情况统计'
											},
											toolbox: {
												feature: {
													restore: { show: true },
													saveAsImage: { show: true }
												}
											},
											legend: {
												data: ['调剂数量','置换数量','租用数量','建设数量']
											},
											xAxis: [
												{
													type: 'category',
													data: ['省直', '福州', '厦门', '泉州', '莆田', '宁德', '漳州', '南平', '三明', '龙岩'],
													axisPointer: {
														type: 'shadow'
													}
												}
											],
											yAxis: [
												{
													type: 'value',
													name: '配置数量',
													min: 0,
													max: 250,
													interval: 50,
													axisLabel: {
														formatter: '{value} 间'
													}
												}
											],
											series: [
												{
													name: '调剂数量',
													type: 'bar',
													tooltip: {
														valueFormatter: function (value) {
															return value + ' 间';
														}
													},
													data: [
														2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
													]
												},
												{
													name: '置换数量',
													type: 'bar',
													tooltip: {
														valueFormatter: function (value) {
															return value + ' 间';
														}
													},
													data: [
														2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
													]
												},
												{
													name: '租用数量',
													type: 'bar',
													tooltip: {
														valueFormatter: function (value) {
															return value + ' 间';
														}
													},
													data: [
														2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
													]
												},
												{
													name: '建设数量',
													type: 'bar',
													tooltip: {
														valueFormatter: function (value) {
															return value + ' 间';
														}
													},
													data: [
														2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
													]
												}
											]
										});
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">省直单位办公业务用房配置情况表</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(barChart1) barChart1.resize();
		if(barChart21) barChart21.resize();
		if(pieChart1) pieChart1.resize();
	}).resize();
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		$(window).resize();
	});
	$('.ui-sortable').sortable({
		connectWith : '.ui-sortable',
		handle      : '.box-header, .nav-tabs',
		placeholder : 'sort-highlight',
		forcePlaceholderSize: true,
		zIndex : 999999
	}).find('.box-header, .nav-tabs').css('cursor', 'move');
});
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("办公用房信息")}', name:'roomInfo', index:'a.roomInfo', width:150, align:"left"},
		{header:'${text("房号及名称")}', name:'roomNumber', index:'a.roomNumber', width:150, align:"left"},
		{header:'${text("申请单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
		{header:'${text("配置类型")}', name:'arrangeType', index:'a.arrangeType', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_arrange_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("办公用房名称")}', name:'roomName', index:'a.roomName', width:150, align:"left"},
		{header:'${text("申请时间")}', name:'applyTime', index:'a.applyTime', width:150, align:"left"},
		{header:'${text("审批时间")}', name:'auditTime', index:'a.auditTime', width:150, align:"left"},
		{header:'${text("状态")}', name:'allocationStatus', index:'a.allocationStatus', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 使用 jQuery 动态获取数据并更新图表
			function fetchOfficeData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//allocationCountData", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart1.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 使用 jQuery 动态获取数据并更新图表
			function fetchOfficeAreaData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//allocationAreaCountData", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart1.setOption({
							xAxis: [{
								//区域
								data: data.data2
							}],
							series: [{
								//总数
								name:data.data3.name,
								data: data.data3.value
							},{
								//待审批
								name:data.data4.name,
								data: data.data4.value
							},{
								//已审批
								name:data.data5.name,
								data: data.data5.value
							},{
								//同比变化率
								name:data.data6.name,
								data: data.data6.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 使用 jQuery 动态获取数据并更新图表
			function allocationAreaCountBarData(formData) {
				$.ajax({
					url: "${ctx}/datastatistics//allocationAreaCountBarData", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart21.setOption({
							xAxis: [{
								//区域
								data: data.data2
							}],
							series: [{
								//调剂数量
								name:data.data3.name,
								data: data.data3.value
							},{
								//置换数量
								name:data.data4.name,
								data: data.data4.value
							},{
								//租用数量
								name:data.data5.name,
								data: data.data5.value
							},{
								//建设数量
								name:data.data6.name,
								data: data.data6.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchOfficeData(formData);
				fetchOfficeAreaData(formData);
				allocationAreaCountBarData(formData);
				// 定时获取数据（例如每秒获取一次数据）
				// setInterval(fetchData, 1000); // 可根据需要调整时间间隔
			});
		});
	}
});
</script>