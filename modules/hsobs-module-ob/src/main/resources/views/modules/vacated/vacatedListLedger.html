<% layout('/layouts/default.html', {title: '腾退明细台帐', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('腾退明细台帐')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${vacated}" action="${ctx}/vacated//listLedgerData" method="post" class="form-inline hide"
				data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('登记单位')}：</label>
					<div class="control-inline">
						<#form:treeselect id="officeCode" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("登记单位")}', name:'officeName', index:'a.officeName', width:150, align:"left",frozen: true},
			{header:'${text("超标使用清退数量")}', name:'overuseClearanceCount', index:'a.overuseClearanceCount', width:150, align:"left"},
			{header:'${text("离退休清退数量")}', name:'retireeClearanceCount', index:'a.retireeClearanceCount', width:150, align:"left"},
			{header:'${text("社会组织占用清退数量")}', name:'socialOrgClearanceCount', index:'a.socialOrgClearanceCount', width:150, align:"left"},
			{header:'${text("企事业清退数量")}', name:'enterpriseClearanceCount', index:'a.enterpriseClearanceCount', width:150, align:"left"},
			{header:'${text("其他情况清退数量")}', name:'otherClearanceCount', index:'a.otherClearanceCount', width:150, align:"left"},
			{header:'${text("超标使用清退面积")}', name:'overuseClearanceArea', index:'a.overuseClearanceArea', width:150, align:"left"},
			{header:'${text("离退休清退面积")}', name:'retireeClearanceArea', index:'a.retireeClearanceArea', width:150, align:"left"},
			{header:'${text("社会组织占用清退面积")}', name:'socialOrgClearanceArea', index:'a.socialOrgClearanceArea', width:150, align:"left"},
			{header:'${text("企事业清退面积")}', name:'enterpriseClearanceArea', index:'a.enterpriseClearanceArea', width:150, align:"left"},
			{header:'${text("其他情况清退面积")}', name:'otherClearanceArea', index:'a.otherClearanceArea', width:150, align:"left"},
		],
		// ================ 设置多级表头 BEGIN ==============
		// 设置多级表头
		groupHeaders: {
			twoLevel:[
				{startColumnName: 'overuseClearanceCount', numberOfColumns: 5, titleText: '基本办公用房腾退情况'},
				{startColumnName: 'overuseClearanceArea', numberOfColumns: 5, titleText: '清理腾退面积'}
			],
		},
		// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
		autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
		autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
		frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
		// ================ 设置多级表头 END ==============
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/vacated/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>