<% layout('/layouts/default.html', {title: '清理腾退管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('清理腾退管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('vacated::edit')){ %>
				<a href="${ctx}/vacated//form" class="btn btn-default btnTool" title="${text('新增清理腾退')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${vacated}" action="${ctx}/vacated//listData" method="post" class="form-inline hide"
				data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('腾退原因')}：</label>
					<div class="control-inline" style="min-width: 150px;">
						<#form:select path="type" dictType="ob_vacated_type" class="form-control" blankOption="true" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('移交单位')}：</label>
					<div class="control-inline">
						<#form:input path="transferOffice.officeName" maxlength="100" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('移交类别')}：</label>
					<div class="control-inline" style="min-width: 120px;">
						<#form:select path="transferType" dictType="ob_transfer_type" class="form-control" blankOption="true" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('移交开始时间')}：</label>
					<div class="control-inline">
						<#form:input path="transferDate_gte" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('移交结束时间')}：</label>
					<div class="control-inline">
						<#form:input path="transferDate_lte" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			// {header:'${text("原使用单位")}', name:'transferOffice.officeName', index:'transferOffice.officeName', width:150, align:"left"},
			{header:'${text("腾退原因")}', name:'type', index:'a.type', width:150, align:"center", frozen:true, formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('ob_vacated_type')}", val, '${text("未知")}', true);
				}},
			{header:'${text("核验日期")}', name:'verificationDate', index:'a.verificationDate', width:150, align:"left"},
			{header:'${text("房屋")}', name:'realEstateAddressId', index:'a.realEstateAddress.id', width:150, align:"center", formatter: function(val, obj, row, act){
					return row.realEstateAddress.name;
				}},
			{header:'${text("房间")}', name:'realEstateId', index:'a.realEstate.id', width:150, align:"center", formatter: function(val, obj, row, act){
					return row.realEstate?.name || '';
				}},
			{header:'${text("移交单位")}', name:'transferOffice.officeName', index:'transferOffice.officeName', width:150, align:"left"},
			{header:'${text("移交类别")}', name:'transferType', index:'a.transferType', width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('ob_transfer_type')}", val, '${text("未知")}', true);
			}},
			{header:'${text("移交日期")}', name:'transferDate', index:'a.transferDate', width:150, align:"center"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
			{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
					var actions = [];
					//# if(hasPermi('vacated::edit')){
					actions.push('<a href="${ctx}/vacated//form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑清理腾退")}">编辑</a>&nbsp;');
					actions.push('<a href="${ctx}/vacated//delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除清理腾退")}" data-confirm="${text("确认要删除该清理腾退吗？")}">删除</a>&nbsp;');
					//# }
					if (row.status != Global.STATUS_DRAFT){
						actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=ob_vacated&bizKey='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("流程追踪")}" data-layer="true">流程</a>&nbsp;');
					}
					return actions.join('');
				}}
		],
		sortableColumn: false,
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>
<script>
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/vacated//exportVacatedData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>