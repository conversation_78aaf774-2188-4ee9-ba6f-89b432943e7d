<% layout('/layouts/default.html', {title: '腾退明细查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('腾退明细查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${vacated}" action="${ctx}/vacated//listQueryData" method="post" class="form-inline hide"
				data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('腾退单位')}：</label>
					<div class="control-inline">
						<#form:treeselect id="officeCode" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
                <div class="form-group">
                    <label class="control-label">${text('腾退时间')}：</label>
                    <div class="control-inline">
                        <#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
                        dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
                        &nbsp;-&nbsp;
                        <#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
                        dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">${text('腾退状态')}：</label>
                    <div class="control-inline width-120">
                        <#form:select path="vacateStatus" dictType="bpm_biz_status" blankOption="true" class="form-control width-120" />
                    </div>
                </div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("腾退单位")}', name:'officeName', index:'a.officeName', width:150, align:"left",frozen: true},
			{header:'${text("腾退名称")}', name:'vacateName', index:'a.vacateName', width:150, align:"left",frozen: true},
			{header:'${text("腾退类型")}', name:'vacateType', index:'a.vacateType', width:150, align:"left",frozen: true, formatter: function(val, obj, row, act){
                return js.getDictLabel("#{@DictUtils.getDictListJson('ob_vacated_type')}", val, '${text("未知")}', true);
            }},
			{header:'${text("腾退时间")}', name:'vacateDate', index:'a.vacateDate', width:150, align:"left",frozen: true},
			{header:'${text("腾退状态")}', name:'vacateStatus', index:'a.vacateStatus', width:150, align:"left",frozen: true, formatter: function(val, obj, row, act){
                return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
            }},
			],

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});

</script>