<% layout('/layouts/default.html', {title: '清理腾退管理' , libs: ['validate','fileupload','dataGrid']}){ %>
	<div class="main-content">
		<% if(!vacated.isNewRecord){ %>
			<div class="box box-main" style="margin-bottom: 10px;">
				<div class="box-header with-border">
					<div class="hide" style="display: flex;justify-content: space-between;">
						<div class="box-title">
						</div>
						<div class="box-tools pull-right ">
							<button type="button" style="width: 120px;" class="btn btn-sm btn-primary"
								data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
						</div>
					</div>
					<div class="row">
						<#common:viewport entity="${vacated}" title="办公用房清理腾退申请" />
					</div>
				</div>
				<div class="box-body">
					<#common:steps entity="${vacated}" formKey="ob_vacated" />
				</div>
			</div>
			<% } %>
				<div class="box box-main">
					<div class="box-header with-border">
						<div class="box-title">
							<i class="fa icon-note"></i> ${text(vacated.isNewRecord ? '新增清理腾退' : '编辑清理腾退')}
						</div>
						<div class="box-tools pull-right hide">
							<button type="button" class="btn btn-box-tool" data-widget="collapse"><i
									class="fa fa-minus"></i></button>
						</div>
					</div>
					<#form:form id="assetSearchForm" model="${vacatedAsset}" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}"
						data-order-by="${parameter.orderBy}">
						<#form:hidden path="vacatedId" />
					</#form:form>
					<#form:form id="inputForm" model="${vacated}" action="${ctx}/vacated//save" method="post"
						class="form-horizontal">
						<div class="box-body">
							<div class="form-unit">${text('基本信息')}</div>
							<#form:hidden path="id" />
							<div class="row">
								<div class="row">
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required">*</span> ${text('腾退名称')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<div class="input-group">
													<#form:input path="vacatedName" maxlength="100"
														class="form-control required" readonly="${commonReadonly}" />
													<span class="input-group-btn">
														<button type="button" class="btn btn-primary"
															id="btnGetVacatedInfo">
															<i class="fa fa-search"></i> 获取信息
														</button>
													</span>
												</div>
											</div>
										</div>
									</div>
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required">*</span> ${text('腾退原因')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<#form:select path="type" dictType="ob_vacated_type"
													class="form-control required" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required">*</span> ${text('房屋')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<#form:listselect id="realEstateAddressId" title="地址选择"
													path="realEstateAddressId" labelPath="realEstateAddress.name"
													setSelectDataFuncName="realEstateAddressListselectSetSelectData"
													url="${ctx}/estate/realEstateAddress/realEstateAddressSelect"
													allowClear="false" checkbox="false" itemCode="id" itemName="name"
													class="form-control required" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required hide">*</span> ${text('房间')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<#form:listselect id="realEstateId" title="房间选择" allowClear="true"
													path="realEstateId" labelPath="realEstate.name"
													setSelectDataFuncName="realEstateListselectSetSelectData"
													url="${ctx}/estate/realEstate/realEstateSelect" allowClear="false"
													checkbox="false" itemCode="id" itemName="name"
													readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class=" ${commonReadonly?'hide':'required'}">*</span>
												${text('使用单位')}：<i class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
													path="usedOfficeCode" labelPath="usedOffice.officeName"
													callbackFuncName="usedOfficeListselectCallback"
													url="${ctx}/sys/office/treeData"
													class=" ${commonReadonly?'':'required'}" allowClear="true"
													readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
								</div>

								<div class="row">
									<div class="form-unit">${text('使用单位现状')}</div>
									<div class="col-xs-12">
										<div class="nav-tabs-custom">
											<ul class="nav nav-tabs" id="establishmentTabs">
												<li class="active"><a href="#centralTab" data-toggle="tab">中央机关</a></li>
												<li><a href="#provinceTab" data-toggle="tab">省级机关</a></li>
												<li><a href="#municipalTab" data-toggle="tab">市级机关</a></li>
												<li><a href="#countyTab" data-toggle="tab">县级机关</a></li>
												<li><a href="#townshipTab" data-toggle="tab">乡级机关</a></li>
											</ul>
											<div class="tab-content">
												<!-- 中央机关 -->
												<div class="tab-pane active" id="centralTab">
													<table class="table table-bordered">
														<thead>
															<tr>
																<th width="20%">${text("部级正职")}</th>
																<th width="20%">${text("部级副职")}</th>
																<th width="20%">${text("正司(局)级")}</th>
																<th width="20%">${text("副司(局)级")}</th>
																<th width="20%">${text("处级以下")}</th>
															</tr>
														</thead>
														<tbody>
															<tr>
																<td>
																	<#form:input name="ministerPositive"
																		path="ministerPositive"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="ministerDeputy"
																		path="ministerDeputy"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="departmentDirector"
																		path="departmentDirector"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="deputyDepartmentDirector"
																		path="deputyDepartmentDirector"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="belowDivisionLevel"
																		path="belowDivisionLevel"
																		class="form-control digits" defaultValue="0" />
																</td>
															</tr>
														</tbody>
													</table>
												</div>

												<!-- 省级机关 -->
												<div class="tab-pane" id="provinceTab">
													<table class="table table-bordered">
														<thead>
															<tr>
																<th width="15%">${text("省级正职")}</th>
																<th width="15%">${text("省级副职")}</th>
																<th width="14%">${text("正厅(局)级")}</th>
																<th width="14%">${text("副厅(局)级")}</th>
																<th width="14%">${text("正处级")}</th>
																<th width="14%">${text("副处级")}</th>
																<th width="14%">${text("处级以下")}</th>
															</tr>
														</thead>
														<tbody>
															<tr>
																<td>
																	<#form:input name="provincePositive"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="provinceDeputy"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="bureauDirector"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="deputyBureauDirector"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="divisionChief"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="deputyDivisionChief"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="belowDivisionChief"
																		class="form-control digits" defaultValue="0" />
																</td>
															</tr>
														</tbody>
													</table>
												</div>

												<!-- 市级机关 -->
												<div class="tab-pane" id="municipalTab">
													<table class="table table-bordered">
														<thead>
															<tr>
																<th width="20%">${text("市级正职")}</th>
																<th width="20%">${text("市级副职")}</th>
																<th width="20%">${text("正局(处)级")}</th>
																<th width="20%">${text("副局(处)级")}</th>
																<th width="20%">${text("局(处)级以下")}</th>
															</tr>
														</thead>
														<tbody>
															<tr>
																<td>
																	<#form:input name="municipalPositive"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="municipalDeputy"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="municipalBureauDirector"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="municipalDeputyBureauDirector"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="belowMunicipalBureau"
																		class="form-control digits" defaultValue="0" />
																</td>
															</tr>
														</tbody>
													</table>
												</div>

												<!-- 县级机关 -->
												<div class="tab-pane" id="countyTab">
													<table class="table table-bordered">
														<thead>
															<tr>
																<th width="20%">${text("县级正职")}</th>
																<th width="20%">${text("县级副职")}</th>
																<th width="20%">${text("正科级")}</th>
																<th width="20%">${text("副科级")}</th>
																<th width="20%">${text("科级以下")}</th>
															</tr>
														</thead>
														<tbody>
															<tr>
																<td>
																	<#form:input name="countyPositive"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="countyDeputy"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="sectionChief"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="deputySectionChief"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="belowSectionLevel"
																		class="form-control digits" defaultValue="0" />
																</td>
															</tr>
														</tbody>
													</table>
												</div>

												<!-- 乡级机关 -->
												<div class="tab-pane" id="townshipTab">
													<table class="table table-bordered">
														<thead>
															<tr>
																<th width="33%">${text("乡级正职")}</th>
																<th width="33%">${text("乡级副职")}</th>
																<th width="34%">${text("乡级以下")}</th>
															</tr>
														</thead>
														<tbody>
															<tr>
																<td>
																	<#form:input name="townshipPositive"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="townshipDeputy"
																		class="form-control digits" defaultValue="0" />
																</td>
																<td>
																	<#form:input name="belowTownshipLevel"
																		class="form-control digits" defaultValue="0" />
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</div>
										</div>
									</div>

									<div class="col-xs-12">
										<div class="panel panel-default">
											<div class="panel-heading">${text('用房现状')}</div>
											<div class="panel-body">
												<table class="table table-bordered">
													<thead>
														<tr>
															<th width="20%">${text("办公室")}</th>
															<th width="20%">${text("服务用房")}</th>
															<th width="20%">${text("设备用房")}</th>
															<th width="20%">${text("附属用房")}</th>
															<th width="20%">${text("技术业务用房")}</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td>
																<#form:input name="realEstateType_0"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="realEstateType_1"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="realEstateType_2"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="realEstateType_3"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="realEstateType_4"
																	class="form-control digits" defaultValue="0" />
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2" title="">
												<span class="required">*</span> ${text('腾退说明')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-10">
												<#form:textarea path="vacatedDescribe" rows="4" maxlength="1000"
													class="form-control" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2" title="">
												<span class="required hide">*</span> ${text('资产说明')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-10">
												<#form:textarea path="assetDescribe" rows="4" class="form-control"
													readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
								</div>
								<div class="form-unit">${text('核验信息')}</div>
								<div class="row">
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required ">*</span> ${text('核验日期')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<% if (currentTaskName=='现场核验' ) { %>
													<#form:input path="verificationDate" maxlength="20"
														class="form-control laydate required" dataFormat="datetime"
														data-type="datetime" data-format="yyyy-MM-dd HH:mm" />
													<% } else { %>
														<#form:input path="verificationDate" maxlength="20"
															class="form-control" dataFormat="datetime"
															readonly="true" />
														<% } %>
											</div>
										</div>
									</div>
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2" title="">
												<span class="required hide">*</span> ${text('核验结果')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-10">
												<#form:textarea path="verificationDescribe" rows="4" maxlength="1000"
													class="form-control" readonly="${currentTaskName!='现场核验'}" />
											</div>
										</div>
									</div>
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2">
												<span class="required hide">*</span> ${text('核验附件')}：</label>
											<div class="col-sm-10">
												<#form:fileupload id="uploadCheckFile" bizKey="${vacated.id}"
													bizType="obQwEstate_check" uploadType="all" class="" preview="true"
													dataMap="true" readonly="${currentTaskName!='现场核验'}" />
											</div>
										</div>
									</div>
								</div>
								<div class="form-unit">${text('移交信息')}</div>
								<div class="row">
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required">*</span> ${text('移交单位')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<#form:treeselect id="transferOfficeCode" title="${text('机构选择')}"
													path="transferOfficeCode" labelPath="transferOffice.officeName"
													url="${ctx}/sys/office/treeData"
													class=" ${currentTaskName!='资料移交'?'':'required'}" allowClear="true"
													readonly="${currentTaskName!='资料移交'}" />
											</div>
										</div>
									</div>
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required ">*</span> ${text('移交类别')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<#form:select path="transferType" dictType="ob_transfer_type"
													class="form-control required"
													readonly="${currentTaskName!='资料移交'}" />
											</div>
										</div>
									</div>
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required ">*</span> ${text('移交日期')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<% if (currentTaskName=='资料移交' ) { %>
													<#form:input path="transferDate" readonly="true" maxlength="20"
														class="form-control laydate required" dataFormat="datetime"
														data-type="datetime" data-format="yyyy-MM-dd HH:mm"
														defaultValue="${date()}" />
													<% } else { %>
														<#form:input path="transferDate" maxlength="20"
															class="form-control" dataFormat="datetime"
															readonly="true" />
														<% } %>
											</div>
										</div>
									</div>
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2">
												<span class="required hide">*</span> ${text('移交附件')}：</label>
											<div class="col-sm-10">
												<p class="text-muted">
												<h5><small>1、实物照片包括但不限于房屋外观、内部结构、办公用品、空调设备等动产及不动产的现场照片。</small></h5>
												</p>
												<p class="text-muted">
												<h5><small>2、批准文件：包括但不限于建房立项批准文件、规划许可证、用地批准文件、建设许可证等。</small></h5>
												</p>
												<p class="text-muted">
												<h5><small>3、不动产登记证：包括土地证、房产证等权属证明文件。</small></h5>
												</p>
												<p class="text-muted">
												<h5><small>4、公共管理交接文件：包括水、电、燃气等公共设施的交接文件。文件需为扫描件或电子版，确保清晰可读。</small>
												</h5>
												</p>
												<p class="text-muted">
												<h5><small>5、租赁合同或协议：包括出租、出借、租用等相关合同或协议。</small></h5>
												</p>
												<p class="text-muted">
												<h5><small>6、移交明细表：包括移交的房屋面积、间数、办公用品、空调设备等动产及不动产的详细清单。</small></h5>
												</p>
												<p class="text-muted">
												<h5><small>7、其他相关文件：包括但不限于接管、接受、沿用、购买、置换等形成的办公用房相关批准文件和资料。</small></h5>
												</p>
												<#form:fileupload id="vacated_transfer_file" bizKey="${vacated.id}"
													bizType="vacated_transfer_file" uploadType="all" class=""
													readonly="false" preview="true" dataMap="true"
													readonly="${currentTaskName!='资料移交'}" />
											</div>
										</div>
									</div>
								</div>
								<div class="form-unit">${text('资产信息')}</div>
								<div class="row">
									<div class="col-xs-2"></div>
									<div class="col-xs-8">
										<% if (!commonReadonly) { %>
											<div class="row" style="text-align: right;">
												<a href="#" id="assetDataGridAddRowBtn"
													class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i>
													新增</a>
												<a href="#" class="btn btn-default" id="btnExport"><i
														class="glyphicon glyphicon-export"></i> 导出</a>
												<a href="#" class="btn btn-default" id="btnImport"><i
														class="glyphicon glyphicon-import"></i> 导入</a>
											</div>
											<% } %>
												<table id="assetDataGrid"></table>
									</div>
									<div class="col-xs-2"></div>
								</div>
								<div class="form-unit">${text('其他信息')}</div>
								<div class="row">
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2">
												<span class="required hide">*</span> ${text('其他附件')}：</label>
											<div class="col-sm-10">
												<#form:fileupload id="uploadFile" bizKey="${vacated.id}"
													bizType="vacated_file" uploadType="all" class="" preview="true"
													dataMap="true" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
								</div>
								<div class="row taskComment hide">
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-xs-2">审批意见：</label>
											<div class="col-xs-10">
												<#bpm:comment bpmEntity="${vacated}" showCommWords="true" />
											</div>
										</div>
									</div>
								</div>
								<#bpm:nextTaskInfo bpmEntity="${vacated}" />
							</div>
							<div class="box-footer">
								<div class="row">
									<div class="col-sm-offset-2 col-sm-10">
										<% if (hasPermi('vacated::edit')){ %>
											<#form:hidden path="status" />
											<% if (vacated.isNewRecord || vacated.status=='9' ){ %>
												<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i
														class="fa fa-save"></i> 暂 存</button>&nbsp;
												<% } %>
													<#bpm:button bpmEntity="${vacated}" formKey="ob_vacated"
														completeText="提 交" />
													<% } %>
														<button type="button" class="btn btn-sm btn-default"
															id="btnCancel" onclick="js.closeCurrentTabPage()"><i
																class="fa fa-reply-all"></i> ${text('关 闭')}</button>
									</div>
								</div>
							</div>
					</#form:form>
				</div>
	</div>
	<% } %>

		<script>
			function updateEstablishmentTabs(officeTypeCode) {
				let tabMap = {
					'1': 'centralTab',     // 中央机关
					'2': 'provinceTab',   // 省级机关
					'3': 'municipalTab',  // 市级机关
					'4': 'countyTab',     // 县级机关
					'5': 'townshipTab'    // 乡级机关
				};

				// 隐藏所有标签页和标签头
				$('#establishmentTabs li').hide();
				$('.tab-content .tab-pane').hide();

				// 显示对应的标签页和标签头
				let defaultTab = tabMap[officeTypeCode] || 'centralTab';
				$('#establishmentTabs a[href="#' + defaultTab + '"]').parent().show();
				$('#' + defaultTab).show();

				// 激活对应的标签页
				$('#establishmentTabs a[href="#' + defaultTab + '"]').tab('show');
			}

			const reloadOfficeUsedRealEstateCount = (officeCode) => {
				js.ajaxSubmit(ctx + '/sys/office/getOffice', {
					officeCode: officeCode,
				}, function (data) {
					console.log(data);
					updateEstablishmentTabs(data.officeType);
					if (data.officeEstablishment) {
						Object.keys(data.officeEstablishment).forEach(key => {
							const input = $(`input[name="` + key + `"]:not([type='hidden'])`);
							if (input.length > 0) {
								input.val(data.officeEstablishment[key] || '0');
							}
						});
					}

					// 处理用房现状数据
					if (data.usedRealEstateCount && data.usedRealEstateCount.length > 0) {
						// 如果有数据则正常填充
						data.usedRealEstateCount.forEach(item => {
							const input = $(`input[name="realEstateType_` + item.type + `"]`);
							if (input.length > 0) {
								input.val(item.count || '0');
							}
						})
					} else {
						// 如果无数据则全部置零
						for (let i = 0; i <= 4; i++) {
							const input = $(`input[name="realEstateType_` + i + `"]`);
							if (input.length > 0) {
								input.val('0');
							}
						}
					}
				});
			}

			function usedOfficeListselectCallback(id, act, index, layero, nodes) {
				console.log(id, act, index, layero, nodes);
				if (id === 'usedOfficeCode' && act === 'ok') {
					reloadOfficeUsedRealEstateCount(nodes[0].code);
				}
			}

			// 初始化时根据当前单位类型显示对应的标签页
			if (`${vacated.usedOfficeCode}`) {
				reloadOfficeUsedRealEstateCount(`${vacated.usedOfficeCode}`);
			} else {
				// 如果没有单位，默认显示中央机关并隐藏其他标签
				updateEstablishmentTabs('1');
			}
		</script>
		<script>
			//初始化测试数据子表DataGrid对象
			$('#assetDataGrid').dataGrid({

				data: "#{toJson(vacated.vacatedAssetList)}",
				datatype: 'local', // 设置本地数据
				autoGridHeight: function () { return 'auto' }, // 设置自动高度

				// 设置数据表格列
				columnModel: [
					{ header: '主键', name: 'id', editable: true, hidden: true },
					{ header: '名称', name: 'name', width: 100, editable: true, edittype: 'text', editoptions: { 'maxlength': '200', 'class': 'form-control required' } },
					{ header: '数量', name: 'count', width: 100, editable: true, edittype: 'text', editoptions: { 'maxlength': '200', 'class': 'form-control required digits', 'rows': '1' } },
					{
						header: '操作', name: 'actions', width: 80, sortable: false, fixed: true, formatter: function (val, obj, row, act) {
							var actions = [];
							actions.push('<a href="#" onclick="js.confirm(\'你确认要删除这条数据吗？\', function(){$(\'#assetDataGrid\').dataGrid(\'delRowData\',\'' + obj.rowId + '\')});return false;">删除</a>&nbsp;');
							return actions.join('');
						}, editoptions: { defaultValue: 'new' }
					}
				],

				// 编辑表格参数
				editGrid: true,				// 是否是编辑表格
				editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
				editGridAddRowBtn: $('#assetDataGridAddRowBtn'),	// 子表增行按钮
				editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上 v4.1.7
				editGridAddRowInitData: { id: '', status: Global.STATUS_NORMAL },	// 新增行的时候初始化的数据

				// 编辑表格的提交数据参数
				editGridInputFormListName: 'vacatedAssetList', // 提交的数据列表名
				editGridInputFormListAttrs: 'id,name,count,', // 提交数据列表的属性字段

				//# // 加载成功后执行事件
				ajaxSuccess: function (data) {

				}
			});
		</script>
		<script>
			$(function () {
				console.log(`${currentTaskName}`);
			})
			// 业务实现草稿按钮
			$('#btnDraft').click(function () {
				$('#status').val(Global.STATUS_DRAFT);
			});

			// 流程按钮操作事件
			BpmButton = window.BpmButton || {};
			BpmButton.init = function (task) {
				if (task.status != '2') {
					$('.taskComment').removeClass('hide');
				}
			}
			BpmButton.complete = function ($this, task) {
				$('#status').val(Global.STATUS_AUDIT);
			};
			// 表单验证提交事件
			$('#inputForm').validate({
				submitHandler: function (form) {
					js.ajaxSubmitForm($(form), function (data) {
						js.showMessage(data.message);
						if (data.result == Global.TRUE) {
							js.closeCurrentTabPage(function (contentWindow) {
								contentWindow.page();
							});
						}
					}, "json");
				}
			});

			function realEstateListselectSetSelectData(id, selectData) {
				console.log(id, selectData);
				if (id == 'realEstateId') {
					let realEstateId = Object.keys(selectData)[0];
					console.log("real estate id: ", realEstateId);
					let realEstateName = selectData[realEstateId]['name'];
					console.log("real estate name: ", realEstateName);
					// let realEstateAddressId = selectData[realEstateId]['realEstateAddressId'];
					let realEstateAddressId = selectData[realEstateId].realEstateAddress.id;
					console.log("realEstateAddressId: ", selectData[realEstateId].realEstateAddress.id);
					// let realEstateAddressName = selectData[realEstateId]['realEstateAddressName'];
					let realEstateAddressName = selectData[realEstateId].realEstateAddress.name;
					console.log("realEstateAddressName: ", selectData[realEstateId].realEstateAddress.name);
					$('#realEstateIdName').val(realEstateName);
					$('#realEstateIdCode').val(realEstateId);
					$('#realEstateAddressIdName').val(realEstateAddressName);
					$('#realEstateAddressIdCode').val(realEstateAddressId);
				}
			}
			function realEstateAddressListselectSetSelectData(id, selectData) {
				if (id == 'realEstateAddressId') {
					let realEstateAddressId = Object.keys(selectData)[0];
					let realEstateAddressName = selectData[realEstateAddressId]['name'];
					$('#realEstateIdName').val(null);
					$('#realEstateIdCode').val(null);
					$('#realEstateAddressIdName').val(realEstateAddressName);
					$('#realEstateAddressIdCode').val(realEstateAddressId);
				}
			}
		</script>
		<script>
			$('#btnExport').click(function () {
				js.ajaxSubmitForm($('#assetSearchForm'), {
					url: '${ctx}/vacated/asset/exportData',
					clearParams: 'pageNo,pageSize',
					downloadFile: true
				});
			});
			$('#btnImport').click(function () {
				js.layer.open({
					type: 1,
					area: ['400px'],
					title: '${text("导入腾退资产")}',
					resize: false,
					scrollbar: true,
					content: js.template('importTpl'),
					btn: ['<i class="fa fa-check"></i> ${text("导入")}',
						'<i class="fa fa-remove"></i> ${text("关闭")}'],
					btn1: function (index, layero) {
						var form = {
							inputForm: layero.find('#vacatedAssetInputForm'),
							vacatedId: `${vacated.id}`,
							file: layero.find('#file').val()
						};
						if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))) {
							js.showMessage(js.text('文件不正确，请选择后缀为"xls"或"xlsx"的文件。'), null, 'warning');
							return false;
						}
						js.ajaxSubmitForm(form.inputForm, function (data) {
							js.showMessage(data.message);
							if (data.result == Global.TRUE) {
								js.layer.closeAll();
							}
							page();
						}, "json");
						return true;
					}
				});
			});
		</script>
		<script id="importTpl" type="text/template">//<!--
<form id="vacatedAssetInputForm" action="${ctx}/vacated/asset/importData?vacatedId=${vacated.id}" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入"xls"或"xlsx"格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/vacated/asset/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>

		<!-- 添加清理腾退信息模态框 -->
		<div class="modal fade" id="vacatedInfoModal" tabindex="-1" role="dialog">
			<div class="modal-dialog modal-lg" role="document" style="width: 90%; max-width: 1200px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
						<h4 class="modal-title">选择清理腾退信息</h4>
					</div>
					<div class="modal-body">
						<div class="table-responsive">
							<table id="vacatedInfoTable" class="table table-striped table-bordered table-hover">
								<thead>
									<tr>
										<th width="40px" class="text-center">选择</th>
										<th width="15%">单位名称</th>
										<th width="10%">单位类型</th>
										<th width="15%">房屋地址</th>
										<th width="10%">建筑面积<br>(㎡)</th>
										<th width="10%">超标面积<br>(㎡)</th>
										<th width="15%">腾退原因</th>
										<th width="15%">备注说明</th>
									</tr>
								</thead>
								<tbody>
									<!-- 数据将通过JavaScript动态填充 -->
								</tbody>
							</table>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
						<button type="button" class="btn btn-primary" id="btnSelectVacated">确定</button>
					</div>
				</div>
			</div>
		</div>

		<style>
			#vacatedInfoModal .modal-body {
				padding: 15px;
				max-height: calc(100vh - 200px);
				overflow-y: auto;
			}

			#vacatedInfoTable th {
				white-space: nowrap;
				background-color: #f5f5f5;
				vertical-align: middle;
			}

			#vacatedInfoTable td {
				vertical-align: middle;
			}

			#vacatedInfoTable input[type="radio"] {
				margin: 0;
				vertical-align: middle;
			}

			.table-responsive {
				border: none;
				margin-bottom: 0;
			}
		</style>

		<script>
			// 模拟的清理腾退数据
			var mockVacatedData = [
				{
					officeName: "福州市发展和改革委员会",
					officeCode: "350100002",
					officeType: "2", // 省级机关
					address: "福州市台江区广达路456号",
					buildingArea: 8000.00,
					excessArea: 1200.00,
					vacatedReason: "超面积标准",
					description: "根据《党政机关办公用房管理办法》规定，单位办公用房超出面积标准",
					establishment: {
						// 编制人数数据
						provincePositive: 1,
						provinceDeputy: 2,
						bureauDirector: 5,
						deputyBureauDirector: 8,
						divisionChief: 15,
						deputyDivisionChief: 20,
						belowDivisionChief: 150
					},
					realEstateCount: [
						{ type: "0", count: 80 }, // 办公室
						{ type: "1", count: 20 }, // 服务用房
						{ type: "2", count: 10 }, // 设备用房
						{ type: "3", count: 15 }, // 附属用房
						{ type: "4", count: 25 }  // 技术业务用房
					]
				},
				{
					officeName: "福州市市场监督管理局",
					officeCode: "350100004",
					officeType: "3", // 市级机关
					address: "福州市晋安区岳峰路789号",
					buildingArea: 5000.00,
					excessArea: 800.00,
					vacatedReason: "职能调整",
					description: "因机构改革，部分职能已调整，相应办公用房需进行腾退",
					establishment: {
						// 编制人数数据
						municipalPositive: 1,
						municipalDeputy: 2,
						municipalBureauDirector: 4,
						municipalDeputyBureauDirector: 6,
						belowMunicipalBureau: 100
					},
					realEstateCount: [
						{ type: "0", count: 50 }, // 办公室
						{ type: "1", count: 15 }, // 服务用房
						{ type: "2", count: 8 },  // 设备用房
						{ type: "3", count: 10 }, // 附属用房
						{ type: "4", count: 20 }  // 技术业务用房
					]
				}
			];

			// 获取清理腾退信息按钮点击事件
			$('#btnGetVacatedInfo').click(function () {
				var usedOfficeCode = $('#usedOfficeCode').val();

				// 清空表格
				var tbody = $('#vacatedInfoTable tbody');
				tbody.empty();

				// 填充模拟数据
				mockVacatedData.forEach(function (item, index) {
					var tr = $('<tr>');
					tr.append('<td class="text-center"><input type="radio" name="vacatedSelect" value="' + index + '"></td>');
					tr.append('<td>' + item.officeName + '</td>');
					tr.append('<td class="text-center">' + js.getDictLabel('sys_office_type', item.officeType, '未知') + '</td>');
					tr.append('<td>' + item.address + '</td>');
					tr.append('<td class="text-right">' + js.formatNumber(item.buildingArea, 2) + '</td>');
					tr.append('<td class="text-right">' + js.formatNumber(item.excessArea, 2) + '</td>');
					tr.append('<td>' + item.vacatedReason + '</td>');
					tr.append('<td>' + item.description + '</td>');
					tbody.append(tr);
				});

				// 显示模态框
				$('#vacatedInfoModal').modal('show');
			});

			// 确定选择按钮点击事件
			$('#btnSelectVacated').click(function () {
				var selectedIndex = $('input[name="vacatedSelect"]:checked').val();
				if (selectedIndex === undefined) {
					js.showMessage('请选择一条记录');
					return;
				}

				var selectedData = mockVacatedData[selectedIndex];

				// 回填数据到表单
				$('#usedOfficeCode').val(selectedData.officeCode);
				$('#usedOfficeCode_name').val(selectedData.officeName);
				$('#vacatedName').val(selectedData.officeName + "办公用房清理腾退");
				$('#vacatedDescribe').val(selectedData.description);

				// 更新编制信息
				if (selectedData.establishment) {
					Object.keys(selectedData.establishment).forEach(key => {
						const input = $(`input[name="` + key + `"]:not([type='hidden'])`);
						if (input.length > 0) {
							input.val(selectedData.establishment[key] || '0');
						}
					});
				}

				// 更新用房现状
				if (selectedData.realEstateCount) {
					selectedData.realEstateCount.forEach(item => {
						const input = $(`input[name="realEstateType_` + item.type + `"]`);
						if (input.length > 0) {
							input.val(item.count || '0');
						}
					});
				}

				// 根据单位类型显示对应标签页
				updateEstablishmentTabs(selectedData.officeType);

				// 关闭模态框
				$('#vacatedInfoModal').modal('hide');

				// 显示成功提示
				js.showMessage('数据获取成功，已自动填充相关信息');
			});
		</script>