<% layout('/layouts/default.html', {title: '设备用房面积核定管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text('设备用房面积核定配置')}
			</div>
		</div>

		<#form:form id="inputForm" model="${equipmentRoomsApprovedConfig}" action="${ctx}/approvedconfig/equipmentRoomsApprovedConfig/save" method="post" class="form-horizontal">
		<div class="box-body">
			<#form:hidden path="id"/>
			<div class="row">
				<div class="col-xs-12">
					<!-- 第一段配置 -->
					<div class="form-group config-item" style="margin-left: 0;">
						<label class="control-label config-desc" style="text-align: left;">
							<span class="config-text">设备用房使用面积应根据地理位置、建设规模以及相关设备需求确定，宜按办公室和服务用房使用面积之和的</span>
							<span class="config-input-box">
								<#form:input path="equipmentRoomRatio"
										   class="form-control number config-input"
										   maxlength="5"
										   data-msg-range="请输入0-100之间的数值"
										   data-rule-range="[0,100]"/>
							</span>
							<span class="config-unit">%</span>
							<span class="config-text">测算。</span>
						</label>
					</div>

					<div class="form-group config-item" style="margin-left: 0;">
						<label class="control-label config-desc" style="text-align: left;">
							<span class="config-text">党政机关办公用房应合理确定门厅、走廊、电梯厅等面积，提高使用面积系数。基本办公用房建筑总使用面积系数，多层建筑不应低于</span>
							<span class="config-input-box">
									<#form:input path="multistoryUsageCoefficientMin"
                                               class="form-control number config-input"
                                               maxlength="5"
                                               data-msg-range="请输入0-100之间的数值"
                                               data-rule-range="[0,100]"/>
								</span>
							<span class="config-unit">%</span>
							<span class="config-text">，高层建筑不应低于</span>
							<span class="config-input-box">
									<#form:input path="highriseUsageCoefficientMin"
                                               class="form-control number config-input"
                                               maxlength="5"
                                               data-msg-range="请输入0-100之间的数值"
                                               data-rule-range="[0,100]"/>
								</span>
							<span class="config-unit">%</span>
							<span class="config-text">。</span>
						</label>
					</div>
				</div>
			</div>
		</div>

		<div class="box-footer action-bar">
			<div class="text-center">
				<% if (hasPermi('approvedconfig:equipmentRoomsApprovedConfig:edit')){ %>
				<button type="submit" class="btn btn-submit">
					<i class="fa fa-save"></i> ${text('保存配置')}
				</button>
				<% } %>
			</div>
		</div>
	</#form:form>
</div>
</div>
<style>
	.box-main {
		max-width: 900px;
		margin: 20px auto;
		box-shadow: 0 2px 12px rgba(0,0,0,0.08);
	}

	.box-title {
		font-size: 18px;
		font-weight: 600;
		color: #2c3e50;
		padding: 18px 25px;
		border-bottom: 1px solid #eee;
	}

	.config-item {
		margin: 30px 0;
		line-height: 1.7;
	}

	.config-desc {
		font-size: 15px;
		color: #444;
		letter-spacing: 0.3px;
	}

	.config-text {
		/*display: inline-block;*/
		vertical-align: middle;
		max-width: 90%;
	}

	.config-input-box {
		display: inline-block;
		margin: 0 6px;
		position: relative;
		vertical-align: middle;
	}

	.config-input {
		width: 85px !important;
		height: 38px;
		padding: 8px 12px;
		border: 1px solid #dcdcdc;
		border-radius: 6px;
		font-size: 14px;
		color: #2c3e50;
		transition: all 0.25s ease;
		text-align: center;
	}

	.config-input:hover {
		border-color: #a0b3c6;
	}

	.config-input:focus {
		border-color: #3c8dbc;
		box-shadow: 0 0 8px rgba(60,141,188,0.15);
		outline: none;
	}

	.config-unit {
		color: #7f8c8d;
		font-size: 14px;
		margin: 0 3px;
	}

	.action-bar {
		padding: 30px 0;
		background: #fafbfc;
		border-top: 1px solid #eaeef1;
		text-align: center;
	}

	.btn-submit {
		background: #3e9bff linear-gradient(180deg, #3e9bff, #3e9bff);
		color: white;
		padding: 12px 45px;
		border-radius: 25px;
		font-size: 15px;
		font-weight: 500;
		letter-spacing: 0.8px;
		border: none;
		transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
		box-shadow: 0 3px 8px rgba(60,141,188,0.2);
	}

	.btn-submit:hover {
		background: #367fa9 linear-gradient(180deg, #438ebb, #367fa9);
		transform: translateY(-1.5px);
		box-shadow: 0 5px 15px rgba(60,141,188,0.3);
	}

	@media (max-width: 768px) {
		.box-main {
			margin: 10px;
			box-shadow: none;
		}

		.config-desc {
			font-size: 14px;
			line-height: 1.6;
			padding: 0 10px;
		}

		.config-input {
			width: 70px !important;
			height: 34px;
			padding: 6px 8px;
		}

		.btn-submit {
			padding: 10px 35px;
			font-size: 14px;
		}
	}

	@media (max-width: 480px) {
		.config-input {
			width: 60px !important;
		}

		.config-unit {
			margin: 0 2px;
		}
	}
</style>
<% } %>
<script>
	$('#inputForm').validate({
		rules: {
			equipmentRoomRatio: {required: true, number: true, range: [0,100]},
			multistoryUsageCoefficientMin: {required: true, number: true, range: [0,100]},
			highriseUsageCoefficientMin: {required: true, number: true, range: [0,100]}
		},
		messages: {
			equipmentRoomRatio: {number: "请输入有效数字", range: "请输入0-100之间的数值"},
			multistoryUsageCoefficientMin: {number: "请输入有效数字", range: "请输入0-100之间的数值"},
			highriseUsageCoefficientMin: {number: "请输入有效数字", range: "请输入0-100之间的数值"}
		},
		submitHandler: function(form){
			js.ajaxSubmitForm($(form), function(data){
				js.showMessage(data.message);
				if(data.result == 'success'){
					setTimeout(function(){
						js.closeCurrentTabPage();
					}, 1000);
				}
			}, "json");
		}
	});
</script>