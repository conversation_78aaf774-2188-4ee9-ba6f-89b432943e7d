<% layout('/layouts/default.html', {title: '设备用房面积核定管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('设备用房面积核定管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('approvedconfig:equipmentRoomsApprovedConfig:edit')){ %>
					<a href="${ctx}/approvedconfig/equipmentRoomsApprovedConfig/form" class="btn btn-default btnTool" title="${text('新增设备用房面积核定')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${equipmentRoomsApprovedConfig}" action="${ctx}/approvedconfig/equipmentRoomsApprovedConfig/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('用房面积指标')}：</label>
					<div class="control-inline">
						<#form:input path="equipmentRoomRatio" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('多层建筑指标')}：</label>
					<div class="control-inline">
						<#form:input path="multistoryUsageCoefficientMin" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('highrise_usage_coefficient_min')}：</label>
					<div class="control-inline">
						<#form:input path="highriseUsageCoefficientMin" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("用房面积指标")}', name:'equipmentRoomRatio', index:'a.equipment_room_ratio', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/approvedconfig/equipmentRoomsApprovedConfig/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑设备用房面积核定")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("多层建筑指标")}', name:'multistoryUsageCoefficientMin', index:'a.multistory_usage_coefficient_min', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("highrise_usage_coefficient_min")}', name:'highriseUsageCoefficientMin', index:'a.highrise_usage_coefficient_min', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("update_date")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('approvedconfig:equipmentRoomsApprovedConfig:edit')){
				actions.push('<a href="${ctx}/approvedconfig/equipmentRoomsApprovedConfig/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑设备用房面积核定")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/approvedconfig/equipmentRoomsApprovedConfig/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除设备用房面积核定")}" data-confirm="${text("确认要删除该设备用房面积核定吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>