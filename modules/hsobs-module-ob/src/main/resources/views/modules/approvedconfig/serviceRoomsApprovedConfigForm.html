<% layout('/layouts/default.html', {title: '服务用房使用面积核定管理', libs: ['validate','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
<!--		<div class="box-header with-border">-->
<!--			<div class="box-title">-->
<!--				<i class="fa icon-note"></i> ${text(serviceRoomsApprovedConfig.isNewRecord ? '服务用房使用面积核定配置' : '服务用房使用面积核定配置')}-->
<!--			</div>-->
<!--			<div class="box-tools pull-right hide">-->
<!--				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>-->
<!--			</div>-->
<!--		</div>-->

		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('服务用房使用面积核定管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('approvedconfig:serviceRoomsApprovedConfig:edit')){ %>
				<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入</a>
				<% } %>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${serviceRoomsApprovedConfig}" action="${ctx}/approvedconfig/serviceRoomsApprovedConfig/listData" method="post" class="form-inline hide"
				data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('适用对象')}：</label>
					<div class="control-inline" style="min-width: 120px;">
						<#form:select path="id" dictType="sys_office_type" blankOption="true" class="form-control" />
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<#form:form id="inputForm" model="${serviceRoomsApprovedConfig}" action="${ctx}/approvedconfig/serviceRoomsApprovedConfig/save" method="post" class="form-horizontal">
				<div class="row">
					<div class="form-unit-wrap table-form">
						<table id="jqGrid"></table>
					</div>
				</div>
				<div class="box-footer action-bar">
					<div class="text-center">
						<% if (hasPermi('approvedconfig:serviceRoomsApprovedConfig:edit')){ %>
						<button type="submit" class="btn btn-submit">
							<i class="fa fa-save"></i> ${text('保存配置')}
						</button>
						<% } %>
					</div>
				</div>
			</#form:form>
		</div>

	</div>
</div>
<% } %>

<style>

	.action-bar {
		padding: 25px 0;
		background: #f8f9fa;
		border-top: 1px solid #eee;
	}
	.btn-submit {
		background: #3e9bff;
		color: white;
		padding: 10px 35px;
		border-radius: 20px;
		transition: all 0.3s;
	}
	.btn-submit:hover {
		background: #367fa9;
		transform: translateY(-1px);
	}
	.btn-close {
		background: #f0f0f0;
		color: #666;
		padding: 10px 35px;
		margin-left: 25px;
		border-radius: 20px;
		transition: all 0.3s;
	}
	.btn-close:hover {
		background: #e0e0e0;
	}
</style>
<script>

	$('#jqGrid').dataGrid({
		data: $('#searchForm'),
		// data: "#{toJson(serviceRoomsApprovedConfigList)}",
		// datatype: 'local', // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度

		//# // 设置数据表格列
		columnModel: [
			{header:'状态', name:'status', editable:false, hidden:true},
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'适用对象', editable:false, width:80, align:'center', formatter: function (val, obj, row, act) {
				return js.getDictLabel("#{@DictUtils.getDictListJson('sys_office_type')}", row.id, '${text("未知")}', true);
			}},
			{header:'下限(平方米/人)', name:'minArea', editable:true, width:80, align:'center', formatter: function (val, obj, row, act) {
				return val || '';
			}},
			{header:'上限(平方米/人)', name:'maxArea', editable:true, width:80, align:'center', formatter: function (val, obj, row, act) {
				return val || '';
			}},
		],

		showRownum: false,
		//# // 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
		editGridInitAllRowEdit: true,  // 是否初始化就编辑所有行（*** 重点 ***）
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
		editGridAddRowInitData: {id: ''},	// 新增行的时候初始化的数据

		//# // 编辑表格的提交数据参数
		editGridInputFormListName: 'serviceRoomsApprovedConfigList', // 提交的数据列表名
		editGridInputFormListAttrs: 'id,minArea,maxArea', // 提交数据列表的属性字段
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){
		}
	});
</script>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
		}, "json");
    }
});
</script>
<script>
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/approvedconfig/serviceRoomsApprovedConfig/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
	$('#btnImport').click(function(){
		js.layer.open({
			type: 1,
			area: ['400px'],
			title: '${text("导入服务用房使用面积核定")}',
			resize: false,
			scrollbar: true,
			content: js.template('importTpl'),
			btn: ['<i class="fa fa-check"></i> ${text("导入")}',
				'<i class="fa fa-remove"></i> ${text("关闭")}'],
			btn1: function(index, layero){
				var form = {
					inputForm: layero.find('#inputForm'),
					file: layero.find('#file').val()
				};
				if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
					js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
					return false;
				}
				js.ajaxSubmitForm(form.inputForm, function(data){
					js.showMessage(data.message);
					if(data.result == Global.TRUE){
						js.layer.closeAll();
					}
					page();
				}, "json");
				return true;
			}
		});
	});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/approvedconfig/serviceRoomsApprovedConfig/importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/approvedconfig/serviceRoomsApprovedConfig/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>