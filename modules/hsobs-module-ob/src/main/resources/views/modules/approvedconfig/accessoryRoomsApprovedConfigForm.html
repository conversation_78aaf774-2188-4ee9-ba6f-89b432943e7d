<% layout('/layouts/default.html', {title: '附属用房面积核定配置管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title" style="font-size: 18px; color: #333;">
				<i class="fa icon-note"></i> ${text(accessoryRoomsApprovedConfig.isNewRecord ? '附属用房面积核定配置' : '附属用房面积核定配置')}
			</div>
		</div>

		<#form:form id="inputForm" model="${accessoryRoomsApprovedConfig}"
		action="${ctx}/approvedconfig/accessoryRoomsApprovedConfig/save"
		method="post"
		class="form-horizontal config-form">

		<div class="box-body" style="padding: 20px 30px;">
			<#form:hidden path="id"/>

			<!-- 食堂配置 -->
			<div class="form-section">
				<p class="config-paragraph">
					食堂：食堂餐厅及厨房建筑面积按编制定员计算，编制定员
					<span class="form-inline-input">
							<#form:input path="diningStaffThreshold"
								class="form-control digits input-number"/>
						</span>
					人及以下的，人均建筑面积为
					<span class="form-inline-input">
							<#form:input path="diningAreaPerCapitaBase"
								class="form-control number input-number"/>
						</span>
					平方米；编制定员超过
					<span class="form-inline-input">
							<#form:input path="diningExtraThreshold"
								class="form-control digits input-number"/>
						</span>
					人的，超出人员的人均建筑面积为
					<span class="form-inline-input">
							<#form:input path="diningAreaPerCapitaExtra"
								class="form-control number input-number"/>
						</span>
					平方米。
				</p>
			</div>

			<!-- 间隔线 -->
			<hr class="section-divider">

			<!-- 停车库配置 -->
			<div class="form-section">
				<p class="config-paragraph">
					停车库：总停车位数应满足城乡规划建设要求，汽车库建筑面积指标为
					<span class="form-inline-input">
							<#form:input path="carParkBaseArea"
								class="form-control number input-number"/>
						</span>
					平方米/辆，超出
					<span class="form-inline-input">
							<#form:input path="carParkExtraThreshold"
								class="form-control digits input-number-lg"/>
						</span>
					个车位以上部分为
					<span class="form-inline-input">
							<#form:input path="carParkExtraArea"
								class="form-control number input-number"/>
						</span>
					平方米/辆，可设置新能源汽车充电桩；自行车库建筑面积指标
					<span class="form-inline-input">
							<#form:input path="bicycleParkArea"
								class="form-control number input-number"/>
						</span>
					平方米/辆；电动车、摩托车库建筑面积指标为
					<span class="form-inline-input">
							<#form:input path="emotorcycleParkArea"
								class="form-control number input-number"/>
						</span>
					平方米/辆。
				</p>
			</div>

			<hr class="section-divider">

			<!-- 警卫用房 -->
			<div class="form-section">
				<p class="config-paragraph">
					警卫用房：宜按警卫编制定员及武警营房建筑面积标准计算，人均建筑面积为
					<span class="form-inline-input">
							<#form:input path="securityRoomAreaPerCapita"
								class="form-control number input-number"/>
						</span>
					平方米。
				</p>
			</div>

			<!-- 人防设施说明 -->
			<div class="form-section annotation">
				<p class="annotation-text">
					<i class="fa fa-info-circle"></i>
					人防设施：应按国家人防部门规定的设防范围和标准计列建筑面积，本着平战结合、充分利用的原则，在平时可以兼作地下车库、物品仓库等。
				</p>
			</div>
		</div>

		<div class="box-footer action-bar">
			<div class="text-center">
				<% if (hasPermi('approvedconfig:accessoryRoomsApprovedConfig:edit')){ %>
				<button type="submit" class="btn btn-submit">
					<i class="fa fa-save"></i> ${text('保存配置')}
				</button>
				<% } %>
			</div>
		</div>
	</#form:form>
</div>
</div>
<% } %>

<style>
	.config-form {
		font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Microsoft YaHei", sans-serif;
		font-size: 14px;
		color: #444;
	}

	.config-paragraph {
		text-indent: 2em;
		line-height: 1.8;
		margin: 20px 0;
		font-size: 15px;
	}

	.form-inline-input {
		display: inline-block;
		margin: 0 4px;
		vertical-align: baseline;
	}

	.input-number {
		width: 80px;
		height: 32px;
		padding: 5px 8px;
		border: 1px solid #c0c4cc;
		border-radius: 4px;
		text-align: center;
		transition: all 0.3s;
	}

	.input-number-lg {
		width: 100px;
	}

	.input-number:focus {
		border-color: #3c8dbc;
		box-shadow: 0 0 8px rgba(60,141,188,.2);
	}

	.section-divider {
		margin: 25px 0;
		border-color: #eee;
	}

	.annotation {
		background: #f8f9fa;
		border-left: 4px solid #3c8dbc;
		padding: 15px;
		margin-top: 30px;
		border-radius: 4px;
	}
	.annotation-text {
		color: #666;
		margin: 0!important;
	}
	.annotation i {
		color: #3c8dbc;
		margin-right: 8px;
	}

	.action-bar {
		padding: 25px 0;
		background: #f8f9fa;
		border-top: 1px solid #eee;
	}
	.btn-submit {
		background: #3e9bff;
		color: white;
		padding: 10px 35px;
		border-radius: 20px;
		transition: all 0.3s;
	}
	.btn-submit:hover {
		background: #367fa9;
		transform: translateY(-1px);
	}
	.btn-close {
		background: #f0f0f0;
		color: #666;
		padding: 10px 35px;
		margin-left: 25px;
		border-radius: 20px;
		transition: all 0.3s;
	}
	.btn-close:hover {
		background: #e0e0e0;
	}
</style>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
		}, "json");
    }
});
</script>