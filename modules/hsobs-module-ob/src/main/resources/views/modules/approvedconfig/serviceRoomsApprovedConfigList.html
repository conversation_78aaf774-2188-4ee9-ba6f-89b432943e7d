<% layout('/layouts/default.html', {title: '服务用房使用面积核定管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('服务用房使用面积核定管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('approvedconfig:serviceRoomsApprovedConfig:edit')){ %>
					<a href="${ctx}/approvedconfig/serviceRoomsApprovedConfig/form" class="btn btn-default btnTool" title="${text('新增服务用房使用面积核定')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${serviceRoomsApprovedConfig}" action="${ctx}/approvedconfig/serviceRoomsApprovedConfig/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('最小面积')}：</label>
					<div class="control-inline">
						<#form:input path="minArea" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('最大面积')}：</label>
					<div class="control-inline">
						<#form:input path="maxArea" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("最小面积")}', name:'minArea', index:'a.min_area', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/approvedconfig/serviceRoomsApprovedConfig/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑服务用房使用面积核定")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("最大面积")}', name:'maxArea', index:'a.max_area', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("update_date")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('approvedconfig:serviceRoomsApprovedConfig:edit')){
				actions.push('<a href="${ctx}/approvedconfig/serviceRoomsApprovedConfig/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑服务用房使用面积核定")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/approvedconfig/serviceRoomsApprovedConfig/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除服务用房使用面积核定")}" data-confirm="${text("确认要删除该服务用房使用面积核定吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>