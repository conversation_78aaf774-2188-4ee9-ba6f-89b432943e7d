<% layout('/layouts/default.html', {title: '附属用房面积核定配置管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('附属用房面积核定配置管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('approvedconfig:accessoryRoomsApprovedConfig:edit')){ %>
					<a href="${ctx}/approvedconfig/accessoryRoomsApprovedConfig/form" class="btn btn-default btnTool" title="${text('新增附属用房面积核定配置')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${accessoryRoomsApprovedConfig}" action="${ctx}/approvedconfig/accessoryRoomsApprovedConfig/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('食堂编制定员')}：</label>
					<div class="control-inline">
						<#form:input path="diningStaffThreshold" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('食堂人均面积')}：</label>
					<div class="control-inline">
						<#form:input path="diningAreaPerCapitaBase" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('食堂超编人均面积')}：</label>
					<div class="control-inline">
						<#form:input path="diningAreaPerCapitaExtra" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('停车库基础车位面积')}：</label>
					<div class="control-inline">
						<#form:input path="carParkBaseArea" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('停车库超额车位阈值')}：</label>
					<div class="control-inline">
						<#form:input path="carParkExtraThreshold" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('停车库超额车位面积')}：</label>
					<div class="control-inline">
						<#form:input path="carParkExtraArea" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('自行车面积')}：</label>
					<div class="control-inline">
						<#form:input path="bicycleParkArea" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('电动车面积')}：</label>
					<div class="control-inline">
						<#form:input path="emotorcycleParkArea" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('security_room_area_per_capita')}：</label>
					<div class="control-inline">
						<#form:input path="securityRoomAreaPerCapita" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("食堂编制定员")}', name:'diningStaffThreshold', index:'a.dining_staff_threshold', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/approvedconfig/accessoryRoomsApprovedConfig/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑附属用房面积核定配置")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("食堂人均面积")}', name:'diningAreaPerCapitaBase', index:'a.dining_area_per_capita_base', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("食堂超编人均面积")}', name:'diningAreaPerCapitaExtra', index:'a.dining_area_per_capita_extra', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("停车库基础车位面积")}', name:'carParkBaseArea', index:'a.car_park_base_area', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("停车库超额车位阈值")}', name:'carParkExtraThreshold', index:'a.car_park_extra_threshold', width:150, align:"center"},
		{header:'${text("停车库超额车位面积")}', name:'carParkExtraArea', index:'a.car_park_extra_area', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("自行车面积")}', name:'bicycleParkArea', index:'a.bicycle_park_area', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("电动车面积")}', name:'emotorcycleParkArea', index:'a.emotorcycle_park_area', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("security_room_area_per_capita")}', name:'securityRoomAreaPerCapita', index:'a.security_room_area_per_capita', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("update_date")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('approvedconfig:accessoryRoomsApprovedConfig:edit')){
				actions.push('<a href="${ctx}/approvedconfig/accessoryRoomsApprovedConfig/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑附属用房面积核定配置")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/approvedconfig/accessoryRoomsApprovedConfig/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除附属用房面积核定配置")}" data-confirm="${text("确认要删除该附属用房面积核定配置吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>