<% layout('/layouts/default.html', {title: '维修项目查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('维修项目查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${repairRequest}" action="${ctx}/repair/request/listQueryData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('维修单据号')}：</label>
				<div class="control-inline">
					<#form:input path="maintenanceOrderNumber" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('申请单位')}：</label>
				<div class="control-inline">
					<#form:treeselect id="officeCode" title="${text('机构选择')}"
					path="officeCode" labelPath=""
					url="${ctx}/sys/office/treeData" allowClear="true"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('维修类型')}：</label>
				<div class="control-inline width-120">
					<#form:select path="maintenanceType" dictType="ob_repair_type" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('申请日期')}：</label>
				<div class="control-inline">
					<#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
					&nbsp;-&nbsp;
					<#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('审批状态')}：</label>
				<div class="control-inline width-120">
					<#form:select path="approvalStatus" dictType="bpm_biz_status" blankOption="true" class="form-control width-120" />
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("维修单据号")}', name:'maintenanceOrderNumber', index:'a.maintenanceOrderNumber', width:150, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/repair/request/form?id='+row.maintenanceOrderNumber+'" class="hsBtnList" data-title="${text("查看维修申请单据")}">'+(val||row.maintenanceOrderNumber)+'</a>';
			}},
			{header:'${text("维修类型")}', name:'maintenanceType', index:'a.maintenanceType', width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('ob_repair_type')}", val, '${text("未知")}', true);
			}},
			{header:'${text("申请日期")}', name:'applyDate', index:'a.applyDate', width:150, align:"left"},
			{header:'${text("维修单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
			{header:'${text("维修工程名称")}', name:'maintenanceProjectName', index:'a.maintenanceProjectName', width:150, align:"left"},
			{header:'${text("办公用房名称")}', name:'officeRoomName', index:'a.officeRoomName', width:150, align:"left"},
			{header:'${text("维修面积")}', name:'maintenanceArea', index:'a.maintenanceArea', width:150, align:"left"},
			{header:'${text("预算金额")}', name:'budgetAmount', index:'a.budgetAmount', width:150, align:"left"},
			{header:'${text("开工日期")}', name:'commenceDate', index:'a.commenceDate', width:150, align:"left"},
			{header:'${text("竣工日期")}', name:'completionDate', index:'a.completionDate', width:150, align:"left"},
			{header:'${text("审批状态")}', name:'approvalStatus', index:'a.approvalStatus', width:150, align:"left", formatter: function (val, obj, row, act) {
				return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
			}},
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}//repair/request/exportQueryData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>