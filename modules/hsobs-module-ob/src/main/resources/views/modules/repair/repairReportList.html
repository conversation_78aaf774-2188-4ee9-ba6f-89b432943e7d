<% layout('/layouts/default.html', {title: '维修规划报备管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('维修规划报备管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('repair:report:edit')){ %>
					<a href="${ctx}/repair/report/form" class="btn btn-default btnTool" title="${text('新增维修规划报备')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${repairReport}" action="${ctx}/repair/report/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('维修单位')}：</label>
					<div class="control-inline">
						<#form:input path="office.officeName" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修性质')}：</label>
					<div class="control-inline width-120">
						<#form:select path="maintType" dictType="ob_repair_report_maint" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="repairCategory" dictType="ob_repair_report_category" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修预计开始时间')}：</label>
					<div class="control-inline">
						<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修预计结束时间')}：</label>
					<div class="control-inline">
						<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("工程编号")}', name:'projectNo', index:'a.project_no', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/repair/report/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑维修规划报备")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("单位名称")}', name:'office.officeName', index:'office.officeName', width:150, align:"left"},
		{header:'${text("维修工程名称")}', name:'projectName', index:'a.project_name', width:150, align:"left"},
		{header:'${text("维修改造方案")}', name:'renovationPlan', index:'a.renovation_plan', width:150, align:"left"},
		{header:'${text("工程预算")}', name:'budget', index:'a.budget', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("资金来源")}', name:'fundSource', index:'a.fund_source', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_repair_report_fund_source')}", val, '${text("未知")}', true);
		}},
		{header:'${text("维修性质")}', name:'maintType', index:'a.maint_type', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_repair_report_maint')}", val, '${text("未知")}', true);
		}},
		{header:'${text("维修类型")}', name:'repairCategory', index:'a.repair_category', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_repair_report_category')}", val, '${text("未知")}', true);
		}},
		{header:'${text("维修内容")}', name:'workContent', index:'a.work_content', width:150, align:"left"},
		{header:'${text("预计维修时间")}', name:'startDate', index:'a.start_date', width:150, align:"center", formatter: function (val, obj, row, act) {
			return row.startDate + "~" + row.endDate;
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('repair:report:edit')){
				actions.push('<a href="${ctx}/repair/report/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑维修规划报备")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/repair/report/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除维修规划报备")}" data-confirm="${text("确认要删除该维修规划报备吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	sortableColumn: false,
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>
<script>
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/repair/report/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>