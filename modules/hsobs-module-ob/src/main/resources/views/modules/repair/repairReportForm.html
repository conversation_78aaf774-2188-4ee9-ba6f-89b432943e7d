<% layout('/layouts/default.html', {title: '维修规划报备管理', libs: ['validate', 'dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(repairReport.isNewRecord ? '新增维修规划报备' : '编辑维修规划报备')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${repairReport}" action="${ctx}/repair/report/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('工程编号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="projectNo" maxlength="512" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('单位名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="officeCode" title="${text('机构选择')}"
									path="officeCode" labelPath="office.officeName"
									url="${ctx}/sys/office/treeData"
									callbackFuncName="listselectCallback"
									class="" allowClear="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('维修工程名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="projectName" maxlength="512" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required ">*</span> ${text('维修改造方案')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="renovationPlan" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('工程预算')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="budget" class="form-control required number"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('资金来源')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="fundSource" dictType="ob_funding" class="form-control required" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('维修性质')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="maintType" dictType="ob_repair_report_maint" class="form-control required" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('维修类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="repairCategory" dictType="ob_repair_report_category" class="form-control required" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('维修预计开始时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('维修预计结束时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required">*</span> ${text('维修内容')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="workContent" rows="4" maxlength="1000" class="form-control required"/>
							</div>
						</div>
					</div>

					<div class="col-xs-12">
						<table id="realEstateAddressDataGrid"></table>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('repair:report:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
	function initDataGrid(officeCode) {
		js.ajaxSubmit(ctx + '/estate/realEstateAddress/listData', {
			usedOfficeCode: officeCode
		}, function(data){
			$('#realEstateAddressDataGrid').empty();
			$('#realEstateAddressDataGrid').dataGrid({
				data: data.list,
				datatype: 'local', // 设置本地数据
				headers: true,
				columnModel: [
					{header:'${text("坐落")}', name: "address", width: 30, align: "center"},
					{header:'${text("结构")}', name: "structure", width: 30, align: "center", formatter: function(val, obj, row, act){
							return js.getDictLabel("#{@DictUtils.getDictListJson('ob_structure_type')}", val, '未知', true);
						}},
					{header:'${text("开工日期")}', name: "commencementDate", width: 30, align: "center"},
					{header:'${text("完工日期")}', name: "completionTime", width: 30, align: "center"},
				]
			});
		});

	}
	function listselectCallback(id, act, index, layero, selectData){
		console.log(id, act, index, layero, selectData);
		if (id == 'officeCode' && act == 'ok'){
			$.each(selectData, (key, value) => {
				console.log(key, value.code);
				initDataGrid(value.code);
			});
		}
	}
	$(function () {
		console.log(`${repairReport.officeCode}`);
		if (`${repairReport.officeCode}`) {
			initDataGrid(`${repairReport.officeCode}`);
		}
	})
</script>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>