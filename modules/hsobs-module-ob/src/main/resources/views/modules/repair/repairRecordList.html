<% layout('/layouts/default.html', {title: '维修组织记录表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('维修组织记录表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('repair:record:edit')){ %>
<!--						<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入</a>-->
					<a href="${ctx}/repair/record/form" class="btn btn-default btnTool" title="${text('新增维修组织记录表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${repairRecord}" action="${ctx}/repair/record/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('维修单位')}：</label>
					<div class="control-inline width-120" >
						<#form:input path="office.officeName" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修项目')}：</label>
					<div class="control-inline">
						<#form:input path="repairRequest.projectName" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="repairStatus" dictType="ob_repair_status" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修开始时间')}：</label>
					<div class="control-inline">
						<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修结束时间')}：</label>
					<div class="control-inline">
						<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("维修单位")}', name:'office.officeName', index:'office.officeName', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/repair/record/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑维修组织记录表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("维修项目")}', name:'repairRequest.projectName', index:'repairRequest.projectName', width:150, align:"left"},
		{header:'${text("维修状态")}', name:'repairStatus', index:'a.repair_status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_repair_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("维修开始时间")}', name:'startDate', index:'a.start_date', width:150, align:"center"},
		{header:'${text("维修结束时间")}', name:'endDate', index:'a.end_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('repair:record:edit')){
				actions.push('<a href="${ctx}/repair/record/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑维修组织记录表")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/repair/record/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除维修组织记录表")}" data-confirm="${text("确认要删除该维修组织记录表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	sortableColumn: false,
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script><script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/repair/record/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入维修组织记录表")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/repair/record/importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/repair/record/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>