<% layout('/layouts/default.html', {title: '维修申请管理', libs: ['validate','fileupload','inputmask']}){ %>
<div class="main-content">
	<% if(!repairRequest.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${repairRequest}" title="办公用房维修申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${repairRequest}" formKey="ob_repair" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(repairRequest.isNewRecord ? '新增维修申请' : '编辑维修申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${repairRequest}" action="${ctx}/repair/request/save" method="post" class="form-horizontal">
		<div class="box-body">
			<div class="form-unit">${text('基本信息')}</div>
			<#form:hidden path="id"/>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required hide">*</span> ${text('维修规划报备')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:listselect id="repairReportId" title="维修规划报备"
							allowClear="true"
							path="repairReportId"
							labelPath = "repairReport.projectName"
							callbackFuncName="listselectCallback"
							url="${ctx}/repair/report/repairReportSelect" allowClear="false"
							checkbox="false" itemCode="id" itemName="projectName"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required">*</span> ${text('单位名称')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:treeselect id="applyOfficeCode" title="${text('机构选择')}"
							path="applyOfficeCode" labelPath="applyOffice.officeName"
							url="${ctx}/sys/office/treeData"
							class="form-control required" allowClear="true"/>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required hide">*</span> ${text('维修编号')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:input path="repairNo" id="repairNo" maxlength="512" class="form-control"/>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required">*</span> ${text('维修工程名称')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:input path="projectName" id="projectName" maxlength="512" class="form-control required"/>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required">*</span> ${text('预算')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<div class="input-group">
								<span class="input-group-addon"><i class="fa fa-fw fa-rmb"></i></span>
								<#form:input path="budget" id="budget" maxlength="200" class="form-control inputmask required"
								data-inputmask-alias="money" data-inputmask="'digits':'2'"/>
							</div>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required">*</span> ${text('性质')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:input path="nature" maxlength="1000" class="form-control required"/>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required">*</span> ${text('类型')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:select path="type" dictType="ob_repair_type" class="form-control" />
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required hide">*</span> ${text('资金来源')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:select id="fundsSource" path="fundsSource" dictType="ob_funding" class="form-control" />
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required hide">*</span> ${text('优先级')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:select path="priority" dictType="ob_repair_priority" class="form-control" />
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required hide">*</span> ${text('房屋')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:listselect id="realEstateAddressId" title="房屋选择"
							class="form-control"
							path="realEstateAddressId"
							labelPath = "realEstateAddress.name"
							url="${ctx}/estate/realEstateAddress/realEstateAddressSelect" allowClear="false"
							checkbox="false" itemCode="id" itemName="name"/>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required hide">*</span> ${text('房间')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:listselect id="realEstateId" title="房间选择"
								allowClear="true"
								path="realEstateId"
								labelPath = "realEstate.name"
								setSelectDataFuncName="realEstateListselectSetSelectData"
								url="${ctx}/estate/realEstate/realEstateSelect" allowClear="false"
								checkbox="false" itemCode="id" itemName="name"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required">*</span> ${text('维修时间')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:input path="repairDate" readonly="true" maxlength="20" class="form-control laydate required"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4" title="">
							<span class="required hide">*</span> ${text('竣工时间')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-8">
							<#form:input path="completionDate" readonly="true" maxlength="20" class="form-control laydate"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label col-sm-2" title="">
							<span class="required">*</span> ${text('维修内容')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-10">
							<#form:textarea path="content" id="content" rows="4" maxlength="1000" class="form-control required"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label col-sm-2" title="">
							<span class="required">*</span> ${text('维修方案')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-10">
							<#form:textarea path="scheme" id="scheme" rows="4" maxlength="100" class="form-control required"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label col-sm-2">
							<span class="required">*</span> ${text('维修改造申请函')}：</label>
						<div class="col-sm-10">
							<#form:fileupload id="repairRequest_application_letter_file" bizKey="${repairRequest.id}" bizType="repairRequest_application_letter_file"
							uploadType="all" class="required" readonly="false" preview="true" dataMap="true"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label col-sm-2">
							<span class="required">*</span> ${text('维修改造方案及投资预算')}：</label>
						<div class="col-sm-10">
							<#form:fileupload id="repairRequest_plan_file" bizKey="${repairRequest.id}" bizType="repairRequest_plan_file"
							uploadType="all" class="required" readonly="false" preview="true" dataMap="true"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label col-sm-2">
							<span class="required hide">*</span> ${text('其它相关材料')}：</label>
						<div class="col-sm-10">
							<#form:fileupload id="repairRequest_file" bizKey="${repairRequest.id}" bizType="repairRequest_file"
							uploadType="all" class="" readonly="false" preview="true" dataMap="true"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label col-sm-2" title="">
							<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></label>
						<div class="col-sm-10">
							<#form:textarea path="remarks" rows="4" class="form-control"/>
						</div>
					</div>
				</div>
			</div>
			<div class="row taskComment hide">
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label col-xs-2">审批意见：</label>
						<div class="col-xs-10">
							<#bpm:comment bpmEntity="${repairRequest}" showCommWords="true" />
						</div>
					</div>
				</div>
			</div>
			<#bpm:nextTaskInfo bpmEntity="${repairRequest}" />
		</div>
		<div class="box-footer">
			<div class="row">
				<div class="col-sm-offset-2 col-sm-10">
					<% if (hasPermi('repair:request:edit')){ %>
					<#form:hidden path="status"/>
					<% if (repairRequest.isNewRecord || repairRequest.status == '9'){ %>
					<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
					<% } %>
					<#bpm:button bpmEntity="${repairRequest}" formKey="ob_repair" completeText="提 交"/>
					<% } %>
					<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
				</div>
			</div>
		</div>
	</#form:form>
</div>
</div>
<% } %>
<script>

	function listselectCallback(id, act, index, layero, selectData){
		if (id === 'repairReportId' && act === 'ok'){
			let row = Object.values(selectData)[0];
			$('#budget').val(row.budget || 0);
			$('#scheme').val(row.renovationPlan || '');
			$('#content').val(row.workContent || '');
			$('#projectName').val(row.projectName || '');
			$('#repairNo').val(row.projectNo || '');
			$('#fundsSource').val(row.fundSource || '1').trigger('change');;
		}
	}
	// 业务实现草稿按钮
	$('#btnDraft').click(function(){
		$('#status').val(Global.STATUS_DRAFT);
	});
	// 流程按钮操作事件
	BpmButton = window.BpmButton || {};
	BpmButton.init = function(task){
		if (task.status != '2') {
			$('.taskComment').removeClass('hide');
		}
	}
	BpmButton.complete = function($this, task){
		$('#status').val(Global.STATUS_AUDIT);
	};
	// 表单验证提交事件
	$('#inputForm').validate({
		submitHandler: function(form){
			js.ajaxSubmitForm($(form), function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.closeCurrentTabPage(function(contentWindow){
						contentWindow.page();
					});
				}
			}, "json");
		}
	});
</script>