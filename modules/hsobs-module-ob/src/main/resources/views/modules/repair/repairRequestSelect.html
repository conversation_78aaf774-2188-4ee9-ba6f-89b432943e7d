<% layout('/layouts/default.html', {title: '维修申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-body">
			<#form:form id="searchForm" model="${repairRequest}" action="${ctx}/repair/request/listData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('项目名称')}：</label>
				<div class="control-inline">
					<#form:input path="projectName" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('资金来源')}：</label>
				<div class="control-inline width-120">
					<#form:select path="fundsSource" dictType="ob_funding" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<div class="row">
			<div class="col-xs-10 pr10">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
			<div class="col-xs-2 pl0">
				<div id="selectData" class="tags-input"></div>
			</div>
		</div>
	</div>
</div>
</div>
<% } %>
<script>
	var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
			selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
				searchForm: $('#searchForm'),
				columnModel: [
					{header:'${text("项目名称")}', name:'projectName', index:'a.project_name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
							return '<a href="${ctx}/repair/request/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑维修申请")}">'+(val||row.id)+'</a>';
						}},
					{header:'${text("类型")}', name:'type', index:'a.type', width:150, align:"center", formatter: function(val, obj, row, act){
							return js.getDictLabel("#{@DictUtils.getDictListJson('ob_repair_type')}", val, '${text("未知")}', true);
						}},
					{header:'${text("预算")}', name:'budget', index:'a.budget', width:150, align:"center"},
					{header:'${text("资金来源")}', name:'fundsSource', index:'a.funds_source', width:150, align:"center", formatter: function(val, obj, row, act){
							return js.getDictLabel("#{@DictUtils.getDictListJson('ob_funding')}", val, '${text("未知")}', true);
						}},
					{header:'${text("不动产")}', name:'realEstateId', index:'a.real_estate_id', width:150, align:"left", formatter: function(val, obj, row, act){
							return row.realEstate?.name || '';
						}},
					{header:'${text("维修时间")}', name:'repairDate', index:'a.repair_date', width:150, align:"center"},
					{header:'${text("维修完成")}', name:'complete', index:'a.complete', width:150, align:"center", formatter: function(val, obj, row, act){
							return js.getDictLabel("#{@DictUtils.getDictListJson('sys_yes_no')}", val, '${text("否")}', true);
						}},
					{header:'${text("审核状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
							return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
						}},
					{header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
							return JSON.stringify(row);
						}}
				],
				autoGridHeight: function(){
					var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
					$('.tags-input').height($('.ui-jqgrid').height() - 10);
					return height;
				},
				showCheckbox: '${parameter.checkbox}' == 'true',
				multiboxonly: false, // 单击复选框时再多选
				ajaxSuccess: function(data){
					$.each(selectData, function(key, value){
						dataGrid.dataGrid('setSelectRow', key);
					});
					initSelectTag();
				},
				onSelectRow: function(id, isSelect, event){
					if ('${parameter.checkbox}' == 'true'){
						if(isSelect){
							selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
						}else{
							delete selectData[id];
						}
					}else{
						selectData = {};
						selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
					}
					initSelectTag();
				},
				onSelectAll: function(ids, isSelect){
					if ('${parameter.checkbox}' == 'true'){
						for (var i=0; i<ids.length; i++){
							if(isSelect){
								selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
							}else{
								delete selectData[ids[i]];
							}
						}
					}
					initSelectTag();
				},
				ondblClickRow: function(id, rownum, colnum, event){
					if ('${parameter.checkbox}' != 'true'){
						js.layer.$('#' + window.name).closest('.layui-layer')
								.find(".layui-layer-btn0").trigger("click");
					}
					initSelectTag();
				}
			});
	function initSelectTag(){
		selectNum = 0;
		var html = [];
		$.each(selectData, function(key, value){
			selectNum ++;
			html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.projectName||value.id)+' </span>'
					+ '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
		});
		html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
		$('#selectData').empty().append(html.join(''));
	}
	function removeSelectTag(key){
		delete selectData[key];
		dataGrid.dataGrid('resetSelection', key);
		$('#selectNum').html(--selectNum);
		$('#'+key+'_tags-input').remove();
	}
	function getSelectData(){
		return selectData;
	}
</script>