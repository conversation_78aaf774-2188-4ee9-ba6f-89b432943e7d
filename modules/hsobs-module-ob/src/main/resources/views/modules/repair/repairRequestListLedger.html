<% layout('/layouts/default.html', {title: '维修管理台帐', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('维修管理台帐')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${repairRequest}" action="${ctx}/repair/request/listLedgerData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('维修单位')}：</label>
				<div class="control-inline">
					<#form:treeselect id="officeCode" title="${text('机构选择')}"
					path="officeCode" labelPath=""
					url="${ctx}/sys/office/treeData" allowClear="true"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("维修单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
			{header:'${text("维修类型")}', name:'maintenanceType', index:'a.maintenanceType', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('ob_repair_type')}", val, '${text("未知")}', true);
			}},
			{header:'${text("维修金额")}', name:'maintenanceAmount', index:'a.maintenanceAmount', width:150, align:"center"},
			{header:'${text("维修总建筑面积")}', name:'maintenanceTotalArea', index:'a.maintenanceTotalArea', width:150, align:"left"},
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}//repair/request/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>