<% layout('/layouts/default.html', {title: '预警设置管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('预警设置管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('earlywarnset::edit')){ %>
					<a href="${ctx}/earlywarnset//form" class="btn btn-default btnTool" title="${text('新增预警设置')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${earlyWarnSet}" action="${ctx}/earlywarnset//listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
<!--				<div class="form-group">-->
<!--					<label class="control-label">${text('预警编号')}：</label>-->
<!--				</div>-->
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('预警类型')}：</label>
					<div class="control-inline ">
						<#form:select path="earlyWarnType" dictType="early_warn_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("预警编号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/earlywarnset//form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑预警设置")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("预警类型")}', name:'earlyWarnType', index:'a.early_warn_type', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('early_warn_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("预警对象")}', name:'earlyTarget', index:'a.earlyTarget', width:150, align:"left", formatter: function(val, obj, row, act) {
			return js.getDictLabel("#{@DictUtils.getDictListJson('early_target')}", val, '${text("未知")}', true);
		}},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"left"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('earlywarnset::edit')){
				actions.push('<a href="${ctx}/earlywarnset//form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑预警设置")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/earlywarnset//disable?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("停用预警设置")}" data-confirm="${text("确认要停用该预警设置吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/earlywarnset//enable?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("启用预警设置")}" data-confirm="${text("确认要启用该预警设置吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/earlywarnset//delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除预警设置")}" data-confirm="${text("确认要删除该预警设置吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>