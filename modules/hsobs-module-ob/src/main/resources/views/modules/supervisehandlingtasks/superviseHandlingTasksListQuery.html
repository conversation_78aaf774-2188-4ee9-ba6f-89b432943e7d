<% layout('/layouts/default.html', {title: '监督检查信息查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('监督检查信息查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${superviseHandlingTasks}" action="${ctx}/supervisehandlingtasks//listQueryData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('被检查单位')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="officeCode" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('审批状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="bpm_biz_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('整改状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="taskStatus" dictType="supervise_handling_task_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('检查时间')}：</label>
					<div class="control-inline">
						<#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("被检查单位")}', name:'officeName', index:'a.officeName', width:150, align:"center"},
		{header:'${text("检查时间")}', name:'checkDate', index:'a.checkDate', width:150, align:"left"},
		{header:'${text("审批状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
			}},
		{header:'${text("核实情况")}', name:'verifyTheSituation', index:'a.verifyTheSituation', width:150, align:"center"},
		{header:'${text("督办整改事项")}', name:'rectificationMatters', index:'a.rectificationMatters', width:150, align:"left"},
		{header:'${text("整改反馈结果")}', name:'rectificationResultFeedback', index:'a.rectificationResultFeedback', width:150, align:"left"},
		{header:'${text("整改状态")}', name:'taskStatus', index:'a.taskStatus', width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('supervise_handling_task_status')}", val, '${text("未知")}', true);
			}},
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script><script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/supervisehandlingtasks//exportQueryData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>