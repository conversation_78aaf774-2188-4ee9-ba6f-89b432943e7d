<% layout('/layouts/default.html', {title: '监督督办任务管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<% if(!superviseHandlingTasks.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${superviseHandlingTasks}" title="监督督办任务申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${superviseHandlingTasks}" formKey="supervise_handling_tasks" />
		</div>
	</div>
	<% } %>
	<% if(!superviseHandlingTasks.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${superviseHandlingTasks}" title="监督督办任务" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${superviseHandlingTasks}" formKey="supervise_handling_tasks" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(superviseHandlingTasks.isNewRecord ? '新增监督督办任务' : '编辑监督督办任务')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${superviseHandlingTasks}" action="${ctx}/supervisehandlingtasks//save" method="post" class="form-horizontal">
		<div class="box-body hs-box-body-bpm">
			<div class="form-unit">${text('基本信息')}</div>
			<#form:hidden path="id"/>
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<!-- 第一行：任务来源 + 任务名称 -->
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('任务来源')}：
						</td>
						<td>
							<#form:select readonly="true" path="taskSource" dictType="supervise_handling_task_source" class="form-control required" />
						</td>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('任务名称')}：
						</td>
						<td>
							<#form:input readonly="true" path="taskName" maxlength="256" class="form-control required"/>
						</td>
					</tr>

					<!-- 第二行：任务状态 + 单位 -->
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('任务状态')}：
						</td>
						<td>
							<#form:select readonly="true" path="taskStatus" dictType="supervise_handling_task_status" class="form-control required" />
						</td>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('单位')}：
						</td>
						<td>
							<#form:treeselect readonly="true" id="officeCode" title="${text('机构选择')}"
							path="officeCode" labelPath="office.officeName"
							url="${ctx}/sys/office/treeData"
							class=" required" allowClear="true"/>
						</td>
					</tr>

					<!-- 第三行：用房分类 + 任务详情 -->
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('用房分类')}：
						</td>
						<td>
							<#form:select readonly="true" path="occupancyClassification" dictType="occupancy_classification" class="form-control required" />
						</td>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('任务详情')}：
						</td>
						<td>
							<#form:input readonly="true" path="taskDetails" maxlength="256" class="form-control required"/>
						</td>
					</tr>

					<!-- 第四行：填报日期 + 检查日期 -->
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('填报日期')}：
						</td>
						<td>
							<#form:input disabled="true" path="taskDate" maxlength="20" class="form-control laydate required"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('检查日期')}：
						</td>
						<td>
							<#form:input path="checkDate" disabled="true" maxlength="20" class="form-control laydate"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</td>
					</tr>

					<!-- 第五行：巡检记录 + 调查核实结果 -->
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('巡检记录')}：
						</td>
						<td>
							<#form:input readonly="true" path="inspectionRecord" maxlength="256" class="form-control"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('调查核实结果')}：
						</td>
						<td>
							<#form:input readonly="true" path="verifyTheSituation" maxlength="256" class="form-control"/>
						</td>
					</tr>

					<!-- 第六行：整改期限 + 整改日期 -->
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('整改期限')}：
						</td>
						<td>
							<#form:input path="fixDeadlineDays" maxlength="20" class="form-control laydate required"
							dataFormat="datetime" disabled="true" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('整改日期')}：
						</td>
						<td>
							<#form:input path="fixDate" maxlength="20" class="form-control laydate required"
							dataFormat="datetime" disabled="true" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</td>
					</tr>

					<!-- 第七行：督办整改事项 + 整改结果反馈 -->
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('督办整改事项')}：
						</td>
						<td>
							<#form:input readonly="true" path="rectificationMatters" maxlength="256" class="form-control"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('整改结果反馈')}：
						</td>
						<td>
							<#form:input readonly="true" path="rectificationResultFeedback" maxlength="256" class="form-control"/>
						</td>
					</tr>

					<!-- 备注（跨列） -->
					<tr>
						<td class="form-label hs-form-label" colspan="2">
							<span class="required hide">*</span> ${text('备注')}：
						</td>
						<td colspan="2">
							<#form:textarea readonly="true" path="remarks" rows="4" maxlength="500" class="form-control"/>
						</td>
					</tr>

					<!-- 文件上传部分（每个文件上传单独一行跨列） -->
					<tr>
						<td class="form-label hs-form-label" colspan="2">
							<span class="required hide">*</span> ${text('督查建议书')}：
						</td>
						<td colspan="2">
							<#form:fileupload id="uploadFile4" bizKey="${superviseHandlingTasks.id}" bizType="superviseHandlingTasks_recommendation_file"
							uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label" colspan="2">
							<span class="required hide">*</span> ${text('督办任务材料')}：
						</td>
						<td colspan="2">
							<#form:fileupload id="uploadFile1" bizKey="${superviseHandlingTasks.id}" bizType="superviseHandlingTasks_supervise_file"
							uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label" colspan="2">
							<span class="required hide">*</span> ${text('调查核实材料')}：
						</td>
						<td colspan="2">
							<#form:fileupload id="uploadFile2" bizKey="${superviseHandlingTasks.id}" bizType="superviseHandlingTasks_verify_file"
							uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label" colspan="2">
							<span class="required hide">*</span> ${text('整改证明材料')}：
						</td>
						<td colspan="2">
							<#form:fileupload id="uploadFile3" bizKey="${superviseHandlingTasks.id}" bizType="superviseHandlingTasks_rectify_file"
							uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
						</td>
					</tr>

					<!-- 审批意见（隐藏） -->
					<tr class="taskComment hide">
						<td class="form-label hs-form-label" colspan="2">
							审批意见：
						</td>
						<td colspan="2">
							<#bpm:comment bpmEntity="${superviseHandlingTasks}" showCommWords="true" />
						</td>
					</tr>
				</table>
			</div>
		</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('supervisehandlingtasks::edit')){ %>
							<#form:hidden path="status"/>
							<% if (superviseHandlingTasks.isNewRecord || superviseHandlingTasks.status == '9'){ %>
								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
							<% } %>
							<#bpm:button bpmEntity="${superviseHandlingTasks}" formKey="supervise_handling_tasks" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};
// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>