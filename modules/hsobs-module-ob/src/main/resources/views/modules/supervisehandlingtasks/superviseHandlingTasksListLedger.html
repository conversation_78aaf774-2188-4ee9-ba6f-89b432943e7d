<% layout('/layouts/default.html', {title: '监督抽查台帐', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('监督抽查台帐')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${superviseHandlingTasks}" action="${ctx}/supervisehandlingtasks//listLedgerData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('抽查单位')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="officeCode" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("抽查单位")}', name:'officeName', index:'a.officeCode', width:150, align:"left"},
		{header:'${text("业务类型")}', name:'businessType', index:'a.businessType', width:150, align:"left"},
		{header:'${text("使用办公用房数量")}', name:'officeNumber', index:'a.officeNumber', width:150, align:"left"},
		{header:'${text("抽查数")}', name:'spotNumber', index:'a.spotNumber', width:150, align:"left"},
		{header:'${text("问题数")}', name:'questionNumber', index:'a.questionNumber', width:150, align:"left"},
		{header:'${text("已整改数")}', name:'correctedNumber', index:'a.correctedNumber', width:150, align:"left"},
		{header:'${text("未整改数")}', name:'uncorrectedNumber', index:'a.uncorrectedNumber', width:150, align:"left"},
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script><script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/supervisehandlingtasks//exportLedgerData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/supervisehandlingtasks//importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/supervisehandlingtasks//importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>