<% layout('/layouts/default.html', {title: '监督督办任务管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('监督督办任务管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="batchDistribution"><i class="glyphicon glyphicon-export"></i> 批量下发</a>
				<% if(hasPermi('supervisehandlingtasks::edit')){ %>
<!--						<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入</a>-->
					<a href="${ctx}/supervisehandlingtasks//form" class="btn btn-default btnTool" title="${text('新增监督督办任务')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${superviseHandlingTasks}" action="${ctx}/supervisehandlingtasks//listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('任务来源')}：</label>
					<div class="control-inline ">
						<#form:select path="taskSource" dictType="supervise_handling_task_source" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('任务名称')}：</label>
					<div class="control-inline">
						<#form:input path="taskName" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('任务状态')}：</label>
					<div class="control-inline ">
						<#form:select path="taskStatus" dictType="supervise_handling_task_status" blankOption="true" class="form-control"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('单位')}：</label>
					<div class="control-inline " >
						<#form:treeselect id="officeCode" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('用房分类')}：</label>
					<div class="control-inline ">
						<#form:select path="occupancyClassification" dictType="occupancy_classification" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('任务详情')}：</label>
					<div class="control-inline">
						<#form:input path="taskDetails" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('填报日期')}：</label>
					<div class="control-inline">
						<#form:input path="taskDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('检查日期')}：</label>
					<div class="control-inline">
						<#form:input path="checkDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('巡检记录')}：</label>
					<div class="control-inline">
						<#form:input path="inspectionRecord" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('调查核实结果')}：</label>
					<div class="control-inline">
						<#form:input path="verifyTheSituation" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('督办整改事项')}：</label>
					<div class="control-inline">
						<#form:input path="rectificationMatters" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('整改结果反馈')}：</label>
					<div class="control-inline">
						<#form:input path="rectificationResultFeedback" maxlength="256" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="bpm_biz_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("任务编号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/supervisehandlingtasks//form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑监督督办任务")}">'+(val||row.id)+'</a>';
			}},
		{header:'${text("任务来源")}', name:'taskSource', index:'a.task_source', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('supervise_handling_task_source')}", val, '${text("未知")}', true);
			}},
		{header:'${text("任务名称")}', name:'taskName', index:'a.task_name', width:150, align:"left"},
		{header:'${text("任务状态")}', name:'taskStatus', index:'a.task_status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('supervise_handling_task_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("单位")}', name:'office.officeName', index:'a.officeCode', width:150, align:"center"},
		{header:'${text("用房分类")}', name:'occupancyClassification', index:'a.occupancy_classification', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('occupancy_classification')}", val, '${text("未知")}', true);
		}},
		{header:'${text("任务详情")}', name:'taskDetails', index:'a.task_details', width:150, align:"left"},
		{header:'${text("填报日期")}', name:'taskDate', index:'a.task_date', width:150, align:"center"},
		{header:'${text("检查日期")}', name:'checkDate', index:'a.check_date', width:150, align:"center"},
		{header:'${text("巡检记录")}', name:'inspectionRecord', index:'a.inspection_record', width:150, align:"left"},
		{header:'${text("调查核实结果")}', name:'verifyTheSituation', index:'a.verify_the_situation', width:150, align:"left"},
		{header:'${text("督办整改事项")}', name:'rectificationMatters', index:'a.rectification_matters', width:150, align:"left"},
		{header:'${text("整改结果反馈")}', name:'rectificationResultFeedback', index:'a.rectification_result_feedback', width:150, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('supervisehandlingtasks::edit')){
			// 	actions.push('<a href="${ctx}/supervisehandlingtasks//form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑监督督办任务")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/supervisehandlingtasks//delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除监督督办任务")}" data-confirm="${text("确认要删除该监督督办任务吗？")}">删除</a>&nbsp;');
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=supervise_handling_tasks&bizKey='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("流程追踪")}" data-layer="true">流程</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	showCheckbox: true,
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script><script>
$('#btnExport').click(function(){
	var codes = $('#dataGrid').dataGrid('getSelectRows');

	if (codes != null && codes.length > 0){
		js.confirm('${text("确认要导出选中的监督督办任务吗？")}', function(){
			js.ajaxSubmitForm($('#searchForm'), {
				url: '${ctx}/supervisehandlingtasks//exportData',
				clearParams: 'pageNo,pageSize',
				downloadFile: true,
				data: {
					codesString: codes.join(',')
				}
			});
		});

	}else{
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/supervisehandlingtasks//exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	}
	return false;
});
$('#batchDistribution').click(function(){
	var codes = $('#dataGrid').dataGrid('getSelectRows');
	if (codes != null && codes.length > 0){
		js.confirm('${text("确认要批量下发选中的监督督办任务吗（仅下发可督办任务）？")}', function(){
			js.ajaxSubmit('${ctx}/supervisehandlingtasks//batchDistribution', {
				codesString: codes.join(',')
			}, function(data){
				js.showMessage(data.message);
				page();
			});
		});
	}else{
		js.showMessage('${text("请在列表选中要进行监督督办的任务！")}');
	}
	return false;
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入监督督办任务")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/supervisehandlingtasks//importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/supervisehandlingtasks//importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>