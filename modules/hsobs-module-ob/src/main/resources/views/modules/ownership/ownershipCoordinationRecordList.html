<% layout('/layouts/default.html', {title: '权属协调记录', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('权属协调记录')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('ownership:coordinationRecord:edit')){ %>
					<a href="${ctx}/ownership/coordinationRecord/form" class="btn btn-default btnTool" title="${text('新增权属登记协调记录')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${ownershipCoordinationRecord}" action="${ctx}/ownership/coordinationRecord/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('权属登记单号')}：</label>
					<div class="control-inline">
						<#form:input path="ownershipNumber" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('权属登记类型')}：</label>
					<div class="control-inline" style="min-width: 120px;">
						<#form:select path="ownershipType" dictType="ob_ownership_registration_type" class="form-control width-120" blankOption="true" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请单位')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="applyOfficeCode" title="${text('机构选择')}"
							path="applyOfficeCode" labelPath="" 
							url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('产权人名称')}：</label>
					<div class="control-inline">
						<#form:input path="ownerUserName" maxlength="100" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("权属登记单号")}', name:'ownershipNumber', index:'a.ownership_number', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/ownership/coordinationRecord/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑权属登记协调记录")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("权属登记类型")}', name:'ownershipType', index:'a.ownership_type', width:150, align:"left", formatter: function (val, obj, row, act) {
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_ownership_registration_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("申请单位")}', name:'applyOffice.officeName', index:'a.apply_office_code', width:150, align:"center"},
		{header:'${text("产权人名称")}', name:'ownerUserName', index:'a.owner_user_name', width:150, align:"left"},
		{header:'${text("房产证号")}', name:'propertyCertificateNumber', index:'a.property_certificate_number', width:150, align:"left"},
		{header:'${text("土地证号")}', name:'landCertificateNumber', index:'a.land_certificate_number', width:150, align:"left"},
		{header:'${text("使用人")}', name:'usedUser.userName', index:'a.user_by', width:150, align:"center"},
		{header:'${text("协调日期")}', name:'coordinationDate', index:'a.coordination_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('ownership:coordinationRecord:edit')){
				actions.push('<a href="${ctx}/ownership/coordinationRecord/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑权属登记协调记录")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/ownership/coordinationRecord/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除权属登记协调记录")}" data-confirm="${text("确认要删除该权属登记协调记录吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>
<script>
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/ownership/coordinationRecord/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>