<% layout('/layouts/default.html', {title: '权属登记协调记录管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(ownershipCoordinationRecord.isNewRecord ? '新增权属登记协调记录' : '编辑权属登记协调记录')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${ownershipCoordinationRecord}" action="${ctx}/ownership/coordinationRecord/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('权属登记单号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="ownershipNumber" maxlength="64" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('权属登记类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="ownershipType" dictType="ob_ownership_registration_type" class="form-control required" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('房产用途')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="usageType" dictType="ob_house_property_usage_type" class="form-control required" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="applyOfficeCode" title="${text('机构选择')}"
									path="applyOfficeCode" labelPath="applyOffice.officeName"
									url="${ctx}/sys/office/treeData"
									class=" required" allowClear="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('产权人名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="ownerUserName" maxlength="100" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('房产证号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="propertyCertificateNumber" maxlength="255" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('土地证号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="landCertificateNumber" maxlength="255" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('使用人')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="userBy" title="${text('用户选择')}"
									path="userBy" labelPath="usedUser.userName"
									url="${ctx}/sys/office/treeData?isLoadUser=true"
									class="" allowClear="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('协调日期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="coordinationDate" readonly="true" maxlength="20" class="form-control laydate"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required hide">*</span> ${text('附件上传')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${ownershipCoordinationRecord.id}" bizType="ownershipCoordinationRecord_file"
								uploadType="all" class="" readonly="false" preview="true" dataMap="true"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('ownership:coordinationRecord:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>