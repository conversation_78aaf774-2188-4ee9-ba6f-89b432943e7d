<% layout('/layouts/default.html', {title: '权属登记管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('权属登记管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('ownership:registration:edit')){ %>
				<a href="${ctx}/ownership/registration/form?type=${ownershipRegistration.type}" class="btn btn-default btnTool" title="${text('新增权属登记')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${ownershipRegistration}" action="${ctx}/ownership/registration/listData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('登记类型')}：</label>
				<div class="control-inline">
					<#form:select path="type" dictType="ob_ownership_registration_type" class="form-control required" readonly="true" />
				</div>
			</div>
			<#form:hidden path="type"/>
			<% if(ownershipRegistration.type == '4'){ %>
				<div class="form-group">
					<label class="control-label">${text('抵押类型')}：</label>
					<div class="control-inline" style="min-width: 120px;">
						<#form:select path="hypothecateType" dictType="ob_hypothecate_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('抵押单位')}：</label>
					<div class="control-inline width-100">
						<#form:input path="hypothecateOffice.officeName" class="form-control"/>
					</div>
				</div>
			<div class="form-group">
				<label class="control-label">${text('抵押开始日期')}：</label>
				<div class="control-inline">
					<#form:input path="hypothecateDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('抵押结束日期')}：</label>
				<div class="control-inline">
					<#form:input path="hypothecateDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<% } else if (ownershipRegistration.type == '5') { %>
			<div class="form-group">
				<label class="control-label">${text('注销单位')}：</label>
				<div class="control-inline width-100">
					<#form:input path="cancelOffice.officeName" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('注销开始日期')}：</label>
				<div class="control-inline">
					<#form:input path="cancelDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('注销结束日期')}：</label>
				<div class="control-inline">
					<#form:input path="cancelDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<% } else if (ownershipRegistration.type == '6') { %>
			<div class="form-group">
				<label class="control-label">${text('变更原因')}：</label>
				<div class="control-inline" style="min-width: 120px;">
					<#form:select path="changeType" dictType="ob_change_type" class="form-control" blankOption="true" />
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('原产权单位')}：</label>
				<div class="control-inline width-100">
					<#form:input path="preOwnerOffice.officeName" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('新产权单位')}：</label>
				<div class="control-inline width-100">
					<#form:input path="ownerOffice.officeName" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('变更开始日期')}：</label>
				<div class="control-inline">
					<#form:input path="changeDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('变更结束日期')}：</label>
				<div class="control-inline">
					<#form:input path="changeDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<% } else { %>
				<div class="form-group">
					<label class="control-label">${text('单位名称')}：</label>
					<div class="control-inline width-100">
						<#form:input path="ownerOffice.officeName" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('登记开始日期')}：</label>
					<div class="control-inline">
						<#form:input path="registrationDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('登记结束日期')}：</label>
					<div class="control-inline">
						<#form:input path="registrationDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
			<% } %>
			<div class="form-group">
				<label class="control-label">${text('审批状态')}：</label>
				<div class="control-inline" style="min-width: 120px;">
					<#form:select path="status" dictType="bpm_biz_status" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	const actionColumn = {header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
		var actions = [];
		//# if(hasPermi('ownership:registration:edit')){
		actions.push('<a href="${ctx}/ownership/registration/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑权属登记")}">编辑</a>&nbsp;');
		actions.push('<a href="${ctx}/ownership/registration/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除权属登记")}" data-confirm="${text("确认要删除该权属登记吗？")}">删除</a>&nbsp;');
		//# }
		if (row.status != Global.STATUS_DRAFT){
			actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=ob_ownership_registration&bizKey='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("流程追踪")}" data-layer="true">流程</a>&nbsp;');
		}
		return actions.join('');
	}};
	let columnData = [];
	if (`${ownershipRegistration.type}` === '4') {
		columnData = [
			{header:'${text("登记名称")}', name:'name', index:'a.name', width:150, align:"center", formatter: function(val, obj, row, act){
					return val || '';
				}},
			{header:'${text("抵押单位")}', name:'hypothecateOffice.officeName', index:'hypothecateOffice.officeName', width:150, align:"center", formatter: function(val, obj, row, act){
					return val || '';
				}},
			{header:'${text("抵押类型")}', name:'hypothecateType', index:'a.hypothecateType', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('ob_hypothecate_type')}", val, '${text("未知")}', true);
				}},
			{header:'${text("抵押日期")}', name:'hypothecateDate', index:'a.hypothecateDate', width:150, align:"center"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
			actionColumn
		]

	} else if (`${ownershipRegistration.type}` === '5') {
		columnData = [
			{header:'${text("登记名称")}', name:'name', index:'a.name', width:150, align:"center", formatter: function(val, obj, row, act){
					return val || '';
				}},
			{header:'${text("注销单位")}', name:'cancelOffice.officeName', index:'cancelOffice.officeName', width:150, align:"center", formatter: function(val, obj, row, act){
					return val || '';
				}},
			{header:'${text("注销日期")}', name:'cancelDate', index:'a.cancelDate', width:150, align:"center"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
			actionColumn
		]
	} else if (`${ownershipRegistration.type}` === '6') {
		columnData = [
			{header:'${text("变更原因")}', name:'changeType', index:'a.changeType', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('ob_change_type')}", val, '${text("未知")}', true);
				}},
			{header:'${text("使用单位")}', name:'usedOffice.officeName', index:'usedOffice.officeName', width:150, align:"center", formatter: function(val, obj, row, act){
					return val || '';
				}},
			{header:'${text("变更日期")}', name:'changeDate', index:'a.changeDate', width:150, align:"center"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
			actionColumn
		]
	} else {
		columnData = [
			{header:'${text("登记名称")}', name:'name', index:'a.name', width:150, align:"center", formatter: function(val, obj, row, act){
					return val || '';
				}},
			{header:'${text("登记类型")}', name:'type', index:'a.type', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('ob_ownership_registration_type')}", val, '${text("未知")}', true);
				}},
			{header:'${text("产权单位")}', name:'organizationId', index:'a.organization_id', width:150, align:"center", formatter: function(val, obj, row, act){
					return row.ownerOffice?.officeName || '';
				}},
			{header:'${text("登记日期")}', name:'registrationDate', index:'a.registrationDate', width:150, align:"center"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
				actionColumn
		]
	}
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: columnData,
		sortableColumn: false,
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>
<script>
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/ownership/registration/exportListData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>