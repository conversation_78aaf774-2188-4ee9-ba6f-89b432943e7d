<% layout('/layouts/default.html', {title: '权属登记管理' , libs: ['validate','fileupload','dataGrid']}){ %>
	<div class="main-content">
		<% if(!ownershipRegistration.isNewRecord){ %>
			<div class="box box-main" style="margin-bottom: 10px;">
				<div class="box-header with-border">
					<div class="hide" style="display: flex;justify-content: space-between;">
						<div class="box-title">
						</div>
						<div class="box-tools pull-right ">
							<button type="button" style="width: 120px;" class="btn btn-sm btn-primary"
								data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
						</div>
					</div>
					<div class="row">
						<#common:viewport entity="${ownershipRegistration}" title="办公用房权属登记" />
					</div>
				</div>
				<div class="box-body">
					<#common:steps entity="${ownershipRegistration}" formKey="ob_ownership_registration" />
				</div>
			</div>
			<% } %>
				<div class="box box-main">
					<div class="box-header with-border">
						<div class="box-title">
							<i class="fa icon-note"></i> ${text(ownershipRegistration.isNewRecord ? '新增权属登记' :
							'编辑权属登记')}
						</div>
						<div class="box-tools pull-right hide">
							<button type="button" class="btn btn-box-tool" data-widget="collapse"><i
									class="fa fa-minus"></i></button>
						</div>
					</div>
					<#form:form id="inputForm" model="${ownershipRegistration}"
						action="${ctx}/ownership/registration/save" method="post" class="form-horizontal">
						<div class="box-body">
							<div class="form-unit">${text('基本信息')}</div>
							<#form:hidden path="id" />
							<div class="row">
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('登记名称')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="name" maxlength="256" class="form-control required"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>

								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('登记类型')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:select path="type" dictType="ob_ownership_registration_type"
												class="form-control required" readonly="true" />
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="hide">*</span> ${text('证件号')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<div class="input-group">
												<#form:input path="certificateNo" maxlength="50"
													class="form-control " />
												<span class="input-group-btn">
													<button type="button" class="btn btn-primary"
														id="btnGetCertificateInfo">
														<i class="fa fa-search"></i> 获取信息
													</button>
												</span>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-xs-12">
									<div class="form-group">
										<label class="control-label col-sm-2" title="">
											<span class="required">*</span> ${text('登记内容')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-10">
											<#form:textarea path="content" rows="4" maxlength="1000"
												class="form-control required" readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('产权单位')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:treeselect id="ownerOfficeCode" title="${text('机构选择')}"
												path="ownerOfficeCode" labelPath="ownerOffice.officeName"
												url="${ctx}/sys/office/treeData" class="form-control required"
												allowClear="true" readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('产权人')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:listselect id="ownerUserCode" path="ownerUserCode"
												labelPath="ownerUser.userName" title="产权人"
												url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
												checkbox="false" itemCode="userCode" itemName="userName"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('使用单位')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
												path="usedOfficeCode" labelPath="usedOffice.officeName"
												url="${ctx}/sys/office/treeData" class="" allowClear="true"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('使用人')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:listselect id="usedUserCode" path="usedUserCode"
												labelPath="usedUser.userName" title="产权人"
												url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
												checkbox="false" itemCode="userCode" itemName="userName"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('登记日期')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="registrationDate" readonly="true" maxlength="20"
												class="form-control laydate required" dataFormat="datetime"
												data-type="datetime" data-format="yyyy-MM-dd HH:mm"
												defaultValue="${date()}" readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('协调日期')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="coordinateDate" readonly="true" maxlength="20"
												class="form-control laydate" dataFormat="datetime" data-type="datetime"
												data-format="yyyy-MM-dd HH:mm" readonly="${commonReadonly}" />
										</div>
									</div>
								</div>

								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('是否涉密')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:select path="classified" dictType="sys_yes_no" class="form-control"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('用途')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="purpose" maxlength="1000" class="form-control"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
							</div>
							<% if (ownershipRegistration.type=='2' ){ %>
								<div class="row">
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required hide">*</span> ${text('证件是否齐全')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<#form:select path="certificateComplete" dictType="sys_yes_no"
													maxlength="1000" class="form-control"
													readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
								</div>
								<% } %>
									<% if (ownershipRegistration.type=='3' || ownershipRegistration.type=='6' ){ %>
										<div class="row">
											<div class="col-xs-6">
												<div class="form-group">
													<label class="control-label col-sm-4" title="">
														<span class="required">*</span> ${text('原产权单位')}：<i
															class="fa icon-question hide"></i></label>
													<div class="col-sm-8">
														<#form:treeselect id="preOwnerOfficeCode"
															title="${text('机构选择')}" path="preOwnerOfficeCode"
															labelPath="preOwnerOffice.officeName"
															url="${ctx}/sys/office/treeData"
															class="form-control required" allowClear="true"
															readonly="${commonReadonly}" />
													</div>
												</div>
											</div>
										</div>
										<% } %>
											<% if (ownershipRegistration.type=='4' ){ %>
												<div class="row">
													<div class="col-xs-6">
														<div class="form-group">
															<label class="control-label col-sm-4" title="">
																<span class="required">*</span> ${text('抵押类型')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-8">
																<#form:select path="hypothecateType"
																	dictType="ob_hypothecate_type" maxlength="1000"
																	class="form-control required"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-6">
														<div class="form-group">
															<label class="control-label col-sm-4" title="">
																<span class="required">*</span> ${text('抵押日期')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-8">
																<#form:input path="hypothecateDate" maxlength="20"
																	class="form-control laydate required"
																	dataFormat="datetime" data-type="datetime"
																	data-format="yyyy-MM-dd HH:mm"
																	defaultValue="${date()}"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-6">
														<div class="form-group">
															<label class="control-label col-sm-4" title="">
																<span class="required">*</span> ${text('抵押单位')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-8">
																<#form:treeselect id="hypothecateOfficeCode"
																	title="${text('机构选择')}" path="hypothecateOfficeCode"
																	labelPath="hypothecateOffice.officeName"
																	url="${ctx}/sys/office/treeData" class="required"
																	allowClear="true" readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2">
																<span class="required">*</span>
																${text('当事人身份证明')}：</label>
															<div class="col-sm-10">
																<#form:fileupload
																	id="ownershipRegistration_identification_file"
																	bizKey="${ownershipRegistration.id}"
																	bizType="ownershipRegistration_identification_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2">
																<span class="required">*</span>
																${text('土地使用权出让合同')}：</label>
															<div class="col-sm-10">
																<#form:fileupload
																	id="ownershipRegistration_contract_assignment_file"
																	bizKey="${ownershipRegistration.id}"
																	bizType="ownershipRegistration_contract_assignment_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2">
																<span class="required">*</span>
																${text('房管处抵押合同')}：</label>
															<div class="col-sm-10">
																<#form:fileupload
																	id="ownershipRegistration_contract_mortgage_file"
																	bizKey="${ownershipRegistration.id}"
																	bizType="ownershipRegistration_contract_mortgage_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2">
																<span class="required">*</span>
																${text('借款合同')}：</label>
															<div class="col-sm-10">
																<#form:fileupload
																	id="ownershipRegistration_contract_loan_file"
																	bizKey="${ownershipRegistration.id}"
																	bizType="ownershipRegistration_contract_loan_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2">
																<span class="required">*</span>
																${text('国有土地使用证')}：</label>
															<div class="col-sm-10">
																<#form:fileupload
																	id="ownershipRegistration_land_use_certificate"
																	bizKey="${ownershipRegistration.id}"
																	bizType="ownershipRegistration_land_use_certificate_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2">
																<span class="required">*</span>
																${text('房管处权证')}：</label>
															<div class="col-sm-10">
																<#form:fileupload
																	id="ownershipRegistration_management_certificate"
																	bizKey="${ownershipRegistration.id}"
																	bizType="ownershipRegistration_management_certificate_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2">
																<span class="required">*</span>
																${text('房屋所有权证')}：</label>
															<div class="col-sm-10">
																<#form:fileupload
																	id="ownershipRegistration_ownership_certificate"
																	bizKey="${ownershipRegistration.id}"
																	bizType="ownershipRegistration_ownership_certificate_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2">
																<span class="required">*</span>
																${text('房管处产权证')}：</label>
															<div class="col-sm-10">
																<#form:fileupload
																	id="ownershipRegistration_property_ownership_certificate"
																	bizKey="${ownershipRegistration.id}"
																	bizType="ownershipRegistration_property_ownership_certificate_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
												</div>
												<% } %>
													<% if (ownershipRegistration.type=='5' ){ %>
														<div class="form-unit">${text('注销信息')}</div>
														<div class="row">
															<div class="col-xs-6">
																<div class="form-group">
																	<label class="control-label col-sm-4" title="">
																		<span class="required">*</span>
																		${text('注销单位')}：<i
																			class="fa icon-question hide"></i></label>
																	<div class="col-sm-8">
																		<#form:treeselect id="cancelOfficeCode"
																			title="${text('机构选择')}"
																			path="cancelOfficeCode"
																			labelPath="cancelOffice.officeName"
																			url="${ctx}/sys/office/treeData"
																			class="required" allowClear="true"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
															<div class="col-xs-6">
																<div class="form-group">
																	<label class="control-label col-sm-4" title="">
																		<span class="required">*</span>
																		${text('注销日期')}：<i
																			class="fa icon-question hide"></i></label>
																	<div class="col-sm-8">
																		<#form:input path="cancelDate" maxlength="20"
																			class="form-control laydate required"
																			dataFormat="datetime" data-type="datetime"
																			data-format="yyyy-MM-dd HH:mm"
																			defaultValue="${date()}"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
															<div class="col-xs-6">
																<div class="form-group">
																	<label class="control-label col-sm-4" title="">
																		<span class="required">*</span>
																		${text('原权属单位')}：<i
																			class="fa icon-question hide"></i></label>
																	<div class="col-sm-8">
																		<#form:treeselect id="preOwnerOfficeCode"
																			title="${text('机构选择')}"
																			path="preOwnerOfficeCode"
																			labelPath="preOwnerOffice.officeName"
																			callbackFuncName="preOwnerOfficeListselectCallback"
																			url="${ctx}/sys/office/treeData"
																			class="required" allowClear="true"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
														</div>
														<div class="row">
															<div class="form-unit">${text('原权属单位现状')}</div>
															<div class="col-xs-12">
																<div class="nav-tabs-custom">
																	<ul class="nav nav-tabs" id="establishmentTabs">
																		<li class="active"><a href="#centralTab"
																				data-toggle="tab">中央机关</a></li>
																		<li><a href="#provinceTab"
																				data-toggle="tab">省级机关</a></li>
																		<li><a href="#municipalTab"
																				data-toggle="tab">市级机关</a></li>
																		<li><a href="#countyTab"
																				data-toggle="tab">县级机关</a></li>
																		<li><a href="#townshipTab"
																				data-toggle="tab">乡级机关</a></li>
																	</ul>
																	<div class="tab-content">
																		<!-- 中央机关 -->
																		<div class="tab-pane active" id="centralTab">
																			<table class="table table-bordered">
																				<thead>
																					<tr>
																						<th width="20%">${text("部级正职")}
																						</th>
																						<th width="20%">${text("部级副职")}
																						</th>
																						<th width="20%">
																							${text("正司(局)级")}</th>
																						<th width="20%">
																							${text("副司(局)级")}</th>
																						<th width="20%">${text("处级以下")}
																						</th>
																					</tr>
																				</thead>
																				<tbody>
																					<tr>
																						<td>
																							<#form:input
																								name="ministerPositive"
																								path="ministerPositive"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="ministerDeputy"
																								path="ministerDeputy"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="departmentDirector"
																								path="departmentDirector"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="deputyDepartmentDirector"
																								path="deputyDepartmentDirector"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="belowDivisionLevel"
																								path="belowDivisionLevel"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</div>

																		<!-- 省级机关 -->
																		<div class="tab-pane" id="provinceTab">
																			<table class="table table-bordered">
																				<thead>
																					<tr>
																						<th width="15%">${text("省级正职")}
																						</th>
																						<th width="15%">${text("省级副职")}
																						</th>
																						<th width="14%">
																							${text("正厅(局)级")}</th>
																						<th width="14%">
																							${text("副厅(局)级")}</th>
																						<th width="14%">${text("正处级")}
																						</th>
																						<th width="14%">${text("副处级")}
																						</th>
																						<th width="14%">${text("处级以下")}
																						</th>
																					</tr>
																				</thead>
																				<tbody>
																					<tr>
																						<td>
																							<#form:input
																								name="provincePositive"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="provinceDeputy"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="bureauDirector"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="deputyBureauDirector"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="divisionChief"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="deputyDivisionChief"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="belowDivisionChief"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</div>

																		<!-- 市级机关 -->
																		<div class="tab-pane" id="municipalTab">
																			<table class="table table-bordered">
																				<thead>
																					<tr>
																						<th width="20%">${text("市级正职")}
																						</th>
																						<th width="20%">${text("市级副职")}
																						</th>
																						<th width="20%">
																							${text("正局(处)级")}</th>
																						<th width="20%">
																							${text("副局(处)级")}</th>
																						<th width="20%">
																							${text("局(处)级以下")}</th>
																					</tr>
																				</thead>
																				<tbody>
																					<tr>
																						<td>
																							<#form:input
																								name="municipalPositive"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="municipalDeputy"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="municipalBureauDirector"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="municipalDeputyBureauDirector"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="belowMunicipalBureau"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</div>

																		<!-- 县级机关 -->
																		<div class="tab-pane" id="countyTab">
																			<table class="table table-bordered">
																				<thead>
																					<tr>
																						<th width="20%">${text("县级正职")}
																						</th>
																						<th width="20%">${text("县级副职")}
																						</th>
																						<th width="20%">${text("正科级")}
																						</th>
																						<th width="20%">${text("副科级")}
																						</th>
																						<th width="20%">${text("科级以下")}
																						</th>
																					</tr>
																				</thead>
																				<tbody>
																					<tr>
																						<td>
																							<#form:input
																								name="countyPositive"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="countyDeputy"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="sectionChief"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="deputySectionChief"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="belowSectionLevel"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</div>

																		<!-- 乡级机关 -->
																		<div class="tab-pane" id="townshipTab">
																			<table class="table table-bordered">
																				<thead>
																					<tr>
																						<th width="33%">${text("乡级正职")}
																						</th>
																						<th width="33%">${text("乡级副职")}
																						</th>
																						<th width="34%">${text("乡级以下")}
																						</th>
																					</tr>
																				</thead>
																				<tbody>
																					<tr>
																						<td>
																							<#form:input
																								name="townshipPositive"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="townshipDeputy"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																						<td>
																							<#form:input
																								name="belowTownshipLevel"
																								class="form-control digits"
																								defaultValue="0" />
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</div>
																	</div>
																</div>
															</div>

															<div class="col-xs-12">
																<div class="panel panel-default">
																	<div class="panel-heading">${text('用房现状')}</div>
																	<div class="panel-body">
																		<table class="table table-bordered">
																			<thead>
																				<tr>
																					<th width="20%">${text("办公室")}</th>
																					<th width="20%">${text("服务用房")}</th>
																					<th width="20%">${text("设备用房")}</th>
																					<th width="20%">${text("附属用房")}</th>
																					<th width="20%">${text("技术业务用房")}
																					</th>
																				</tr>
																			</thead>
																			<tbody>
																				<tr>
																					<td>
																						<#form:input
																							name="realEstateType_0"
																							class="form-control digits"
																							defaultValue="0" />
																					</td>
																					<td>
																						<#form:input
																							name="realEstateType_1"
																							class="form-control digits"
																							defaultValue="0" />
																					</td>
																					<td>
																						<#form:input
																							name="realEstateType_2"
																							class="form-control digits"
																							defaultValue="0" />
																					</td>
																					<td>
																						<#form:input
																							name="realEstateType_3"
																							class="form-control digits"
																							defaultValue="0" />
																					</td>
																					<td>
																						<#form:input
																							name="realEstateType_4"
																							class="form-control digits"
																							defaultValue="0" />
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</div>
																</div>
															</div>
														</div>
														<div>
															<div class="col-xs-12">
																<div class="form-group">
																	<label class="control-label col-sm-2" title="">
																		<span class="required">*</span>
																		${text('注销原因')}：<i
																			class="fa icon-question hide"></i></label>
																	<div class="col-sm-10">
																		<#form:textarea path="cancelReason" rows="4"
																			maxlength="500"
																			class="form-control required"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
															<div class="col-xs-12">
																<div class="form-group">
																	<label class="control-label col-sm-2"
																		title="如注销申请函、权属证明等">
																		<span class="required">*</span>
																		${text('相关文件')}：<i
																			class="fa icon-question"></i></label>
																	<div class="col-sm-10">
																		<#form:fileupload
																			id="ownershipRegistration_property_ownership_certificate"
																			bizKey="${ownershipRegistration.id}"
																			bizType="ownershipRegistration_cancel_file"
																			uploadType="all" class="required"
																			readonly="false" preview="true"
																			dataMap="true"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
														</div>
														<% } %>
															<% if (ownershipRegistration.type=='6' ){ %>
																<div class="form-unit">${text('变更信息')}</div>
																<div class="row">
																	<div class="col-xs-6">
																		<div class="form-group">
																			<label class="control-label col-sm-4"
																				title="">
																				<span class="required">*</span>
																				${text('变更原因')}：<i
																					class="fa icon-question hide"></i></label>
																			<div class="col-sm-8">
																				<#form:select path="changeType"
																					dictType="ob_change_type"
																					maxlength="1000"
																					class="form-control required"
																					readonly="${commonReadonly}" />
																			</div>
																		</div>
																	</div>
																	<div class="col-xs-6">
																		<div class="form-group">
																			<label class="control-label col-sm-4"
																				title="">
																				<span class="required">*</span>
																				${text('变更日期')}：<i
																					class="fa icon-question hide"></i></label>
																			<div class="col-sm-8">
																				<#form:input path="changeDate"
																					readonly="true" maxlength="20"
																					class="form-control laydate required"
																					dataFormat="datetime"
																					data-type="datetime"
																					data-format="yyyy-MM-dd HH:mm"
																					defaultValue="${date()}"
																					readonly="${commonReadonly}" />
																			</div>
																		</div>
																	</div>
																	<div class="col-xs-12">
																		<div class="form-group">
																			<label class="control-label col-sm-2">
																				<span class="required">*</span>
																				${text('变动情况报告')}：</label>
																			<div class="col-sm-10">
																				<#form:fileupload
																					id="ownershipRegistration_change_report_file"
																					bizKey="${ownershipRegistration.id}"
																					bizType="ownershipRegistration_change_report_file"
																					uploadType="all" class="required"
																					readonly="false" preview="true"
																					dataMap="true"
																					readonly="${commonReadonly}" />
																			</div>
																		</div>
																	</div>
																	<div class="col-xs-12">
																		<div class="form-group">
																			<label class="control-label col-sm-2">
																				<span class="required">*</span>
																				${text('变动批准文件')}：</label>
																			<div class="col-sm-10">
																				<#form:fileupload
																					id="ownershipRegistration_change_approve_file"
																					bizKey="${ownershipRegistration.id}"
																					bizType="ownershipRegistration_change_approve_file"
																					uploadType="all" class="required"
																					readonly="false" preview="true"
																					dataMap="true"
																					readonly="${commonReadonly}" />
																			</div>
																		</div>
																	</div>
																	<div class="col-xs-12">
																		<div class="form-group">
																			<label class="control-label col-sm-2">
																				<span class="required">*</span>
																				${text('图件')}：</label>
																			<div class="col-sm-10">
																				<#form:fileupload
																					id="ownershipRegistration_change_map_file"
																					bizKey="${ownershipRegistration.id}"
																					bizType="ownershipRegistration_change_map_file"
																					uploadType="all" class="required"
																					readonly="false" preview="true"
																					dataMap="true"
																					readonly="${commonReadonly}" />
																			</div>
																		</div>
																	</div>
																	<div class="col-xs-12">
																		<div class="form-group">
																			<label class="control-label col-sm-2">
																				<span class="required">*</span>
																				${text('权属证书')}：</label>
																			<div class="col-sm-10">
																				<#form:fileupload
																					id="ownershipRegistration_change_owner_file"
																					bizKey="${ownershipRegistration.id}"
																					bizType="ownershipRegistration_change_owner_file"
																					uploadType="all" class="required"
																					readonly="false" preview="true"
																					dataMap="true"
																					readonly="${commonReadonly}" />
																			</div>
																		</div>
																	</div>
																</div>
																<% } %>
																	<div class="form-unit">${text('其他信息')}</div>
																	<div class="row">
																		<div class="col-xs-12">
																			<div class="form-group">
																				<label class="control-label col-sm-2">
																					<span class="required hide">*</span>
																					${text('其他附件')}：</label>
																				<div class="col-sm-10">
																					<#form:fileupload id="uploadFile"
																						bizKey="${ownershipRegistration.id}"
																						bizType="ownershipRegistration_file"
																						uploadType="all" class=""
																						readonly="false" preview="true"
																						dataMap="true"
																						readonly="${commonReadonly}" />
																				</div>
																			</div>
																		</div>
																	</div>
																	<div class="row">
																		<div class="col-xs-12">
																			<div class="form-group">
																				<label class="control-label col-sm-2"
																					title="">
																					<span class="required hide">*</span>
																					${text('备注')}：<i
																						class="fa icon-question hide"></i></label>
																				<div class="col-sm-10">
																					<#form:textarea path="remarks"
																						rows="4" class="form-control"
																						readonly="${commonReadonly}" />
																				</div>
																			</div>
																		</div>
																	</div>




																	<div class="form-unit">${text('不动产信息')}</div>

																	<div class="row">
																		<div class="col-xs-6">
																			<div class="form-group">
																				<label class="control-label col-sm-4"
																					title="">
																					<span class="required hide">*</span>
																					${text('不动产类型')}：<i
																						class="fa icon-question hide"></i></label>
																				<div class="col-sm-8">
																					<#form:select id="realEstateType"
																						path="realEstateType"
																						dictType="ob_real_estate_type"
																						class="form-control"
																						readonly="${commonReadonly}" />
																				</div>
																			</div>
																		</div>
																		<div class="col-xs-12">
																			<% if(!commonReadonly){ %>
																				<div class="form-unit-wrap table-form"
																					id="realEstateRoomWrapper">
																					<#form:btnlistselect
																						id="ownershipRealEstateDataGridAddRowListselectBtn"
																						title="关联房间" allowClear="true"
																						btnLabel="选择房间"
																						setSelectDataFuncName="realEstateBtnListselectSetSelectData"
																						url="${ctx}/estate/realEstate/realEstateSelect"
																						allowClear="false"
																						checkbox="true" itemCode="id"
																						itemName="name" />
																				</div>
																				<div class="form-unit-wrap table-form"
																					id="realEstateHouseWrapper"
																					style="display:none;">
																					<#form:btnlistselect
																						id="ownershipRealEstateAddressDataGridAddRowListselectBtn"
																						title="关联房屋" allowClear="true"
																						btnLabel="选择房屋"
																						setSelectDataFuncName="realEstateAddressBtnListselectSetSelectData"
																						url="${ctx}/estate/realEstateAddress/realEstateAddressSelect"
																						allowClear="false"
																						checkbox="true" itemCode="id"
																						itemName="name" />
																				</div>
																				<% } %>
																					<div
																						class="form-unit-wrap table-form">
																						<table id="realEstateDataGrid">
																						</table>
																					</div>
																		</div>
																	</div>


																	<div class="form-unit">${text('审批信息')}</div>
																	<div class="row taskComment hide">
																		<div class="col-xs-12">
																			<div class="form-group">
																				<label
																					class="control-label col-xs-2">审批意见：</label>
																				<div class="col-xs-10">
																					<#bpm:comment
																						bpmEntity="${ownershipRegistration}"
																						showCommWords="true" />
																				</div>
																			</div>
																		</div>
																	</div>
																	<!--			<#bpm:nextTaskInfo bpmEntity="${ownershipRegistration}" />-->
						</div>
						<div class="box-footer">
							<div class="row">
								<div class="col-sm-offset-2 col-sm-10">
									<% if (hasPermi('ownership:registration:edit')){ %>
										<#form:hidden path="status" />
										<% if (ownershipRegistration.isNewRecord || ownershipRegistration.status=='9' ){
											%>
											<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i
													class="fa fa-save"></i> 暂 存</button>&nbsp;
											<% } %>
												<#bpm:button bpmEntity="${ownershipRegistration}"
													formKey="ob_ownership_registration" completeText="提 交" />
												<% } %>
													<button type="button" class="btn btn-sm btn-default" id="btnCancel"
														onclick="js.closeCurrentTabPage()"><i
															class="fa fa-reply-all"></i> ${text('关 闭')}</button>
								</div>
							</div>
						</div>
					</#form:form>
				</div>
	</div>
	<% } %>

		<script>
			function updateEstablishmentTabs(officeTypeCode) {
				let tabMap = {
					'1': 'centralTab',     // 中央机关
					'2': 'provinceTab',   // 省级机关
					'3': 'municipalTab',  // 市级机关
					'4': 'countyTab',     // 县级机关
					'5': 'townshipTab'    // 乡级机关
				};

				// 隐藏所有标签页和标签头
				$('#establishmentTabs li').hide();
				$('.tab-content .tab-pane').hide();

				// 显示对应的标签页和标签头
				let defaultTab = tabMap[officeTypeCode] || 'centralTab';
				$('#establishmentTabs a[href="#' + defaultTab + '"]').parent().show();
				$('#' + defaultTab).show();

				// 激活对应的标签页
				$('#establishmentTabs a[href="#' + defaultTab + '"]').tab('show');
			}

			const reloadOfficeUsedRealEstateCount = (officeCode) => {
				js.ajaxSubmit(ctx + '/sys/office/getOffice', {
					officeCode: officeCode,
				}, function (data) {
					console.log(data);
					updateEstablishmentTabs(data.officeType);
					if (data.officeEstablishment) {
						Object.keys(data.officeEstablishment).forEach(key => {
							const input = $(`input[name="` + key + `"]:not([type='hidden'])`);
							if (input.length > 0) {
								input.val(data.officeEstablishment[key] || '0');
							}
						});
					}

					// 处理用房现状数据
					if (data.usedRealEstateCount && data.usedRealEstateCount.length > 0) {
						// 如果有数据则正常填充
						data.usedRealEstateCount.forEach(item => {
							const input = $(`input[name="realEstateType_` + item.type + `"]`);
							if (input.length > 0) {
								input.val(item.count || '0');
							}
						})
					} else {
						// 如果无数据则全部置零
						for (let i = 0; i <= 4; i++) {
							const input = $(`input[name="realEstateType_` + i + `"]`);
							if (input.length > 0) {
								input.val('0');
							}
						}
					}
				});
			}

			function preOwnerOfficeListselectCallback(id, act, index, layero, nodes) {
				console.log(id, act, index, layero, nodes);
				if (id === 'preOwnerOfficeCode' && act === 'ok') {
					reloadOfficeUsedRealEstateCount(nodes[0].code);
				}
			}

			// 初始化时根据当前单位类型显示对应的标签页
			if (`${ownershipRegistration.preOwnerOfficeCode}`) {
				reloadOfficeUsedRealEstateCount(`${ownershipRegistration.preOwnerOfficeCode}`);
			} else {
				// 如果没有单位，默认显示中央机关并隐藏其他标签
				updateEstablishmentTabs('1');
			}
		</script>
		<script>
			$(function () {
				var gridInitialized = false;
				// Listen for changes on the real estate type select field
				$('#realEstateType').change(function () {
					var selectedType = $(this).val(); // Get the selected real estate type


					if (!gridInitialized) {
						gridInitialized = true;  // Set the flag after the first load
					} else {
						$('#realEstateDataGrid').jqGrid('clearGridData');
					}
					if (selectedType == '2') {  // Replace 'someRoomType' with the type for rooms
						$('#realEstateRoomWrapper').show();  // Show room selection
						$('#realEstateHouseWrapper').hide(); // Hide house selection
					} else if (selectedType == '1') {  // Replace 'someHouseType' with the type for houses
						$('#realEstateRoomWrapper').hide();  // Hide room selection
						$('#realEstateHouseWrapper').show(); // Show house selection
					} else {
						$('#realEstateRoomWrapper').hide();
						$('#realEstateHouseWrapper').hide(); // Default, hide both if no specific type is selected
					}
				});

				$('#realEstateType').trigger('change');
			});
		</script>
		<script>

			function realEstateBtnListselectSetSelectData(id, selectData) {
				if (id == 'ownershipRealEstateDataGridAddRowListselectBtn') {
					if ($.isEmptyObject(selectData)) {
						return;
					}
					let gridData = $('#realEstateDataGrid').dataGrid('getRowData').map(item => item.realEstateId);
					$.each(selectData, (key, value) => {
						let realEstateId = key.toString();
						if (gridData.includes(realEstateId)) {
							return true;
						}
						let realEstateName = value['name'];
						let realEstateAddressName = value?.realEstateAddress?.name ?? '';
						$('#realEstateDataGrid').jqGrid('addRow', {
							position: 'first',
							addRowParams: { keys: false, focusField: true },
							initdata: {
								ownershipId: { id: '' },
								realEstateId: realEstateId,
								realEstate: { name: realEstateName, realEstateAddressName: realEstateAddressName },
								realEstateAddressName: realEstateAddressName,
								status: Global.STATUS_NORMAL
							}
						});
						gridData.push(realEstateId);
					});
				}
			}
			function realEstateAddressBtnListselectSetSelectData(id, selectData) {
				if (id == 'ownershipRealEstateAddressDataGridAddRowListselectBtn') {
					if ($.isEmptyObject(selectData)) {
						return;
					}
					let gridData = $('#realEstateDataGrid').dataGrid('getRowData').map(item => item.realEstateId);
					$.each(selectData, (key, value) => {
						let realEstateId = key.toString();
						if (gridData.includes(realEstateId)) {
							return true;
						}
						let realEstateName = value['name'];
						$('#realEstateDataGrid').jqGrid('addRow', {
							position: 'first',
							addRowParams: { keys: false, focusField: true },
							initdata: {
								ownershipId: { id: '' },
								realEstateId: realEstateId,
								realEstate: { name: realEstateName },
								status: Global.STATUS_NORMAL
							}
						});
						gridData.push(realEstateId);
					});
				}
			}
			$(function () {
			})
		</script>
		<script>
			//# // 初始化权属登记关联不动产表DataGrid对象
			$('#realEstateDataGrid').dataGrid({

				data: "#{toJson(ownershipRegistration.ownershipRealEstateList)}",
				datatype: 'local', // 设置本地数据
				autoGridHeight: function () { return 'auto' }, // 设置自动高度

				//# // 设置数据表格列
				columnModel: [
					{ header: '状态', name: 'status', editable: false, hidden: true },
					{ header: '主键', name: 'id', editable: true, hidden: true },
					{ header: '${text("ownership_id")}', name: 'ownershipId.id', editable: true, hidden: true },
					{ header: '${text("real_estate_id")}', name: 'realEstateId', width: 150, editable: true, hidden: true },
					{
						header: '名称', name: 'realEstate.name', editable: false, width: 80, align: 'center', formatter: function (val, obj, row, act) {
							return row.realEstate?.name || row.realEstateAddress?.name || '';
						}
					},
					{
						header: '${text("操作")}', name: 'actions', width: 80, align: 'center', formatter: function (val, obj, row, act) {
							var actions = [];
							actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#realEstateDataGrid\').dataGrid(\'delRowData\',\'' + obj.rowId + '\')});return false;">删除</a>&nbsp;');
							return actions.join('');
						}, editoptions: { defaultValue: 'new' }
					}
				],

				//# // 编辑表格参数
				editGrid: true,				// 是否是编辑表格
				editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
				editGridInitAllRowEdit: true,  // 是否初始化就编辑所有行（*** 重点 ***）
				editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
				editGridAddRowInitData: { id: '', ownershipId: { id: '' }, realEstateId: '' },	// 新增行的时候初始化的数据

				//# // 编辑表格的提交数据参数
				editGridInputFormListName: 'ownershipRealEstateList', // 提交的数据列表名
				editGridInputFormListAttrs: 'id,ownershipId.id,realEstateId,createBy,createDate,updateBy,updateDate,', // 提交数据列表的属性字段

				//# // 加载成功后执行事件
				ajaxSuccess: function (data) {
				}
			});
		</script>
		<script>
			// 业务实现草稿按钮
			$('#btnDraft').click(function () {
				$('#status').val(Global.STATUS_DRAFT);
			});
			// 流程按钮操作事件
			BpmButton = window.BpmButton || {};
			BpmButton.init = function (task) {
				if (task.status != '2') {
					$('.taskComment').removeClass('hide');
				}
			}
			BpmButton.complete = function ($this, task) {
				$('#status').val(Global.STATUS_AUDIT);
			};
			// 表单验证提交事件
			$('#inputForm').validate({
				submitHandler: function (form) {
					js.ajaxSubmitForm($(form), function (data) {
						js.showMessage(data.message);
						if (data.result == Global.TRUE) {
							js.closeCurrentTabPage(function (contentWindow) {
								contentWindow.page();
							});
						}
					}, "json");
				}
			});
		</script>

		<!-- 添加模态对话框 -->
		<div class="modal fade" id="certificateInfoModal" tabindex="-1" role="dialog">
			<div class="modal-dialog modal-lg" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
						<h4 class="modal-title">选择证件信息</h4>
					</div>
					<div class="modal-body">
						<table id="certificateInfoTable" class="table table-striped table-bordered table-hover">
							<thead>
								<tr>
									<th width="50px">选择</th>
									<th>证件号</th>
									<th>用地批件号</th>
									<th>批准日期</th>
									<th>批准单位</th>
									<th>用地面积(㎡)</th>
								</tr>
							</thead>
							<tbody>
								<!-- 数据将通过JavaScript动态填充 -->
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
						<button type="button" class="btn btn-primary" id="btnSelectCertificate">确定</button>
					</div>
				</div>
			</div>
		</div>

		<script>
			// 模拟的证件信息数据
			var mockCertificateData = [
				{
					certificateNo: "YD2023001",
					approvalNo: "FJ-YD-2023-001",
					approvalDate: "2023-01-15",
					approvalUnit: "福建省自然资源厅",
					landArea: 5000.00,
					content: "根据《中华人民共和国土地管理法》《福建省土地管理条例》等有关规定，经研究，同意使用福州市台江区某街道某地块，用于机关办公用房建设。该地块土地面积5000平方米，土地用途为机关团体用地，使用年限50年。",
					purpose: "机关办公用房",
					ownerOfficeCode: "350100001", // 产权单位代码
					ownerOfficeName: "福州市机关事务管理局", // 产权单位名称
					ownerUserCode: "U0001", // 产权人代码
					ownerUserName: "张三", // 产权人姓名
					usedOfficeCode: "350100002", // 使用单位代码
					usedOfficeName: "福州市发展和改革委员会", // 使用单位名称
					usedUserCode: "U0002", // 使用人代码
					usedUserName: "李四", // 使用人姓名
					registrationDate: "2023-01-20",
					coordinateDate: "2023-01-18",
					classified: "0", // 是否涉密 0否1是
					attachments: [
						{ name: "用地批复文件.pdf", url: "#" },
						{ name: "土地勘测定界图.pdf", url: "#" },
						{ name: "建设工程规划许可证.pdf", url: "#" }
					]
				},
				{
					certificateNo: "YD2023002",
					approvalNo: "FJ-YD-2023-002",
					approvalDate: "2023-02-20",
					approvalUnit: "福建省人民政府",
					landArea: 3500.00,
					content: "经研究，同意与福州市台江区房地产开发公司进行办公用房置换。置换房产位于福州市台江区某街道，建筑面积3500平方米，房屋性质为机关办公用房。该置换方案符合机关事务管理的相关规定，有利于优化办公资源配置。",
					purpose: "机关办公及业务用房",
					ownerOfficeCode: "350100003",
					ownerOfficeName: "福州市公安局",
					ownerUserCode: "U0003",
					ownerUserName: "王五",
					usedOfficeCode: "350100003",
					usedOfficeName: "福州市公安局",
					usedUserCode: "U0004",
					usedUserName: "赵六",
					registrationDate: "2023-02-25",
					coordinateDate: "2023-02-22",
					classified: "1",
					attachments: [
						{ name: "置换批复文件.pdf", url: "#" },
						{ name: "房产测绘图.pdf", url: "#" },
						{ name: "房屋所有权证.pdf", url: "#" }
					]
				},
				{
					certificateNo: "YD2023003",
					approvalNo: "FJ-YD-2023-003",
					approvalDate: "2023-03-10",
					approvalUnit: "福建省机关事务管理局",
					landArea: 2800.00,
					content: "根据《机关事务管理条例》和《福建省机关办公用房管理办法》相关规定，经审核研究，同意购置位于福州市鼓楼区某街道的办公用房，建筑面积2800平方米，用于机关办公。该房产权属清晰，价格合理，符合机关办公用房配置标准。",
					purpose: "机关办公",
					ownerOfficeCode: "350100004",
					ownerOfficeName: "福州市市场监督管理局",
					ownerUserCode: "U0005",
					ownerUserName: "孙七",
					usedOfficeCode: "350100004",
					usedOfficeName: "福州市市场监督管理局",
					usedUserCode: "U0006",
					usedUserName: "周八",
					registrationDate: "2023-03-15",
					coordinateDate: "2023-03-12",
					classified: "0",
					attachments: [
						{ name: "购置批复文件.pdf", url: "#" },
						{ name: "房屋权属证明.pdf", url: "#" },
						{ name: "房屋交易合同.pdf", url: "#" }
					]
				}
			];

			// 获取证件信息按钮点击事件
			$('#btnGetCertificateInfo').click(function () {
				var certificateNo = $('#certificateNo').val();
				if (!certificateNo) {
					js.showMessage('请输入证件号');
					return;
				}

				// 清空表格
				var tbody = $('#certificateInfoTable tbody');
				tbody.empty();

				// 填充模拟数据
				mockCertificateData.forEach(function (item, index) {
					var tr = $('<tr>');
					tr.append('<td><input type="radio" name="certificateSelect" value="' + index + '"></td>');
					tr.append('<td>' + item.certificateNo + '</td>');
					tr.append('<td>' + item.approvalNo + '</td>');
					tr.append('<td>' + item.approvalDate + '</td>');
					tr.append('<td>' + item.approvalUnit + '</td>');
					tr.append('<td>' + item.landArea + '</td>');
					tbody.append(tr);
				});

				// 显示模态框
				$('#certificateInfoModal').modal('show');
			});

			// 确定选择按钮点击事件
			$('#btnSelectCertificate').click(function () {
				var selectedIndex = $('input[name="certificateSelect"]:checked').val();
				if (selectedIndex === undefined) {
					js.showMessage('请选择一条记录');
					return;
				}

				var selectedData = mockCertificateData[selectedIndex];

				// 回填数据到表单
				$('#name').val('关于' + selectedData.ownerOfficeName + '办公用房权属登记');
				$('#content').val(selectedData.content);
				$('#purpose').val(selectedData.purpose);

				// 设置产权单位
				if (selectedData.ownerOfficeCode && selectedData.ownerOfficeName) {
					$('#ownerOfficeCode').val(selectedData.ownerOfficeCode);
					$('#ownerOfficeCode_name').val(selectedData.ownerOfficeName);
				}

				// 设置产权人
				if (selectedData.ownerUserCode && selectedData.ownerUserName) {
					$('#ownerUserCode').val(selectedData.ownerUserCode);
					$('#ownerUserCode_name').val(selectedData.ownerUserName);
				}

				// 设置使用单位
				if (selectedData.usedOfficeCode && selectedData.usedOfficeName) {
					$('#usedOfficeCode').val(selectedData.usedOfficeCode);
					$('#usedOfficeCode_name').val(selectedData.usedOfficeName);
				}

				// 设置使用人
				if (selectedData.usedUserCode && selectedData.usedUserName) {
					$('#usedUserCode').val(selectedData.usedUserCode);
					$('#usedUserCode_name').val(selectedData.usedUserName);
				}

				// 设置日期
				$('#registrationDate').val(selectedData.registrationDate);
				$('#coordinateDate').val(selectedData.coordinateDate);

				// 设置是否涉密
				$('#classified').val(selectedData.classified);

				// 如果有附件，可以在这里处理附件的显示
				// TODO: 根据实际情况处理附件

				// 关闭模态框
				$('#certificateInfoModal').modal('hide');

				// 显示成功提示
				js.showMessage('数据获取成功，已自动填充相关信息');
			});
		</script>