<% layout('/layouts/default.html', {title: '权属备案管理', libs: ['validate', 'fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(ownershipRecord.isNewRecord ? '新增权属备案' : '编辑权属备案')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${ownershipRecord}" action="${ctx}/ownership/record/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<#form:hidden path="recordStatus"/>
				<#form:hidden path="report" defaultValue="false"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('使用单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="officeCode" title="${text('机构选择')}"
									path="officeCode" labelPath="office.officeName"
									url="${ctx}/sys/office/treeData"
									class=" required" allowClear="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('权属单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="ownerOfficeCode" title="${text('机构选择')}"
									path="ownerOfficeCode" labelPath="ownerOffice.officeName"
									url="${ctx}/sys/office/treeData"
									class=" required" allowClear="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required ">*</span> ${text('备案原因')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="description" rows="4" class="form-control required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('备案日期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="ownershipDate" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								相关文件：
								<i class="fa fa-question-circle" data-toggle="tooltip" title="备案申请函、权属证明等"></i>
							</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${ownershipRecord.id}" bizType="ownershipRecord_file"
									uploadType="all" class="" readonly="false" preview="true"
									serviceFileList="${ctxAdmin}/file/fileList?bizKeyIsLike=true"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('ownership:record:edit')){ %>
							<% if (ownershipRecord.recordStatus == '1') { %>
								<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
								<button type="button" class="btn btn-sm btn-success" id="btnReport"><i class="fa fa-upload"></i> ${text('上 报')}</button>&nbsp;
							<% } %>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
	$(function () {
		$('[data-toggle="tooltip"]').tooltip();
	})
</script>
<script>
	$('#btnReport').click(function() {
		$('#report').val('true');
		$('#inputForm').submit();
	});
</script>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>