<% layout('/layouts/default.html', {title: '使用凭证管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('使用凭证管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('usevouchers::edit')){ %>
					<a href="${ctx}/usevouchers//form" class="btn btn-default btnTool" title="${text('新增使用凭证')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${useVouchers}" action="${ctx}/usevouchers//listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('签订日期')}：</label>
					<div class="control-inline">
						<#form:input path="signingDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/usevouchers//form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑使用凭证")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("组织机构")}', name:'organizationId', index:'a.organization_id', width:150, align:"left", formatter: function(val, obj, row, act){
			return row.office?.officeName || '';
		}},
		{header:'${text("房屋")}', name:'realEstateAddressId', index:'a.real_estate_address_id', width:150, align:"left", formatter: function(val, obj, row, act){
			return row.realEstateAddress?.name || '';
		}},
		{header:'${text("房间")}', name:'realEstateId', index:'a.real_estate_id', width:150, align:"left", formatter: function(val, obj, row, act){
			return row.realEstate?.name || '';
		}},
		{header:'${text("登记负责人")}', name:'registrationManagerBy', index:'a.registration_manager_by', width:150, align:"left", formatter: function(val, obj, row, act){
			return row.registrationManagerByUser?.userName || '';
		}},
		{header:'${text("经办人")}', name:'handledBy', index:'a.handled_by', width:150, align:"left", formatter: function(val, obj, row, act){
			return row.handledByUser?.userName || '';
		}},
		{header:'${text("签订日期")}', name:'signingDate', index:'a.signing_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('usevouchers::edit')){
				actions.push('<a href="${ctx}/usevouchers//form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑使用凭证")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/usevouchers//delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除使用凭证")}" data-confirm="${text("确认要删除该使用凭证吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>