<% layout('/layouts/default.html', {title: '使用凭证管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(useVouchers.isNewRecord ? '新增使用凭证' : '编辑使用凭证')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${useVouchers}" action="${ctx}/usevouchers//save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="512" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('组织机构')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="organizationId" title="${text('机构选择')}"
									path="organizationId" labelPath="office.officeName"
									url="${ctx}/sys/office/treeData"
									class="" allowClear="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('不动产地址')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="realEstateAddressId" title="地址选择"
									path="realEstateAddressId"
									labelPath = "realEstateAddress.name"
									setSelectDataFuncName="realEstateAddressListselectSetSelectData"
									url="${ctx}/estate/realEstateAddress/realEstateAddressSelect" allowClear="false"
									checkbox="false" itemCode="id" itemName="name"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('房间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="realEstateId" title="房间选择"
									allowClear="true"
									path="realEstateId"
									labelPath = "realEstate.name"
									setSelectDataFuncName="realEstateListselectSetSelectData"
									url="${ctx}/estate/realEstate/realEstateSelect" allowClear="false"
									checkbox="false" itemCode="id" itemName="name"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('登记负责人')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="registrationManagerBy" title="用户选择"
									path="registrationManagerBy" labelPath="registrationManagerByUser.userName"
									url="${ctx}/sys/office/treeData?isLoadUser=true"
									class="" allowClear="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('经办人')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="handledBy" title="用户选择"
									path="handledBy" labelPath="handledByUser.userName"
									url="${ctx}/sys/office/treeData?isLoadUser=true"
									class="" allowClear="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('签订日期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="signingDate" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required hide">*</span> ${text('附件上传')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${useVouchers.id}" bizType="useVouchers_file"
									uploadType="all" class="" readonly="false" preview="true" dataMap="true"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('usevouchers::edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});

function realEstateListselectSetSelectData(id, selectData){
	if (id == 'realEstateId'){
		let realEstateId = Object.keys(selectData)[0];
		let realEstateName = selectData[realEstateId]['name'];
		let realEstateAddressId = selectData[realEstateId]['realEstateAddressId'];
		let realEstateAddressName = selectData[realEstateId]['realEstateAddressName'];
		$('#realEstateIdName').val(realEstateName);
		$('#realEstateIdCode').val(realEstateId);
		$('#realEstateAddressIdName').val(realEstateAddressName);
		$('#realEstateAddressIdCode').val(realEstateAddressId);
	}
}
function realEstateAddressListselectSetSelectData(id, selectData){
	if (id == 'realEstateAddressId'){
		let realEstateAddressId = Object.keys(selectData)[0];
		let realEstateAddressName = selectData[realEstateAddressId]['name'];
		$('#realEstateIdName').val(null);
		$('#realEstateIdCode').val(null);
		$('#realEstateAddressIdName').val(realEstateAddressName);
		$('#realEstateAddressIdCode').val(realEstateAddressId);
	}
}
</script>