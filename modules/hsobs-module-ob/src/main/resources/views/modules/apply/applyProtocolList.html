<% layout('/layouts/default.html', {title: '有偿使用协议管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('有偿使用协议管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('apply:protocol:edit')){ %>
					<a href="${ctx}/apply/protocol/form" class="btn btn-default btnTool" title="${text('新增有偿使用协议')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${applyProtocol}" action="${ctx}/apply/protocol/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('使用单位')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
							path="usedOfficeCode" labelPath="" 
							url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('租用办公用房地址')}：</label>
					<div class="control-inline">
						<#form:input path="address" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('租用开始时间')}：</label>
					<div class="control-inline">
						<#form:input path="leaseBeginDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('租用结束时间')}：</label>
					<div class="control-inline">
						<#form:input path="leaseEndDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('租金')}：</label>
					<div class="control-inline">
						<#form:input path="rent" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("单位名称")}', name:'usedOfficeCode', index:'a.used_office_code', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/apply/protocol/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑有偿使用协议")}">'+row.usedOffice.officeName+'</a>';
		}},
		{header:'${text("租用办公用房地址")}', name:'address', index:'a.address', width:150, align:"left"},
		{header:'${text("使用面积")}', name:'usedArea', index:'a.used_area', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("租用开始时间")}', name:'leaseBeginDate', index:'a.lease_begin_date', width:150, align:"center"},
		{header:'${text("租用结束时间")}', name:'leaseEndDate', index:'a.lease_end_date', width:150, align:"center"},
		{header:'${text("租金")}', name:'rent', index:'a.rent', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('apply:protocol:edit')){
				actions.push('<a href="${ctx}/apply/protocol/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑有偿使用协议")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/apply/protocol/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除有偿使用协议")}" data-confirm="${text("确认要删除该有偿使用协议表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>