<% layout('/layouts/default.html', {title: '使用明细查询', libs: ['dataGrid', 'validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('使用明细查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${apply}" action="${ctx}/apply//listApplyDetailQueryData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('使用人')}：</label>
				<div class="control-inline">
					<#form:input path="userName" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('功能分类')}：</label>
				<div class="control-inline width-120">
					<#form:select path="useType" dictType="occupancy_classification" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('职级')}：</label>
				<div class="control-inline width-120">
					<#form:select path="rank" dictType="ob_establishment_type" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('使用日期')}：</label>
				<div class="control-inline">
					<#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
					&nbsp;-&nbsp;
					<#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('申请原由')}：</label>
				<div class="control-inline">
					<#form:input path="describe" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('用途')}：</label>
				<div class="control-inline">
					<#form:input path="purpose" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('位置')}：</label>
				<div class="control-inline">
					<#form:input path="position" maxlength="256" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('房间数')}：</label>
				<div class="control-inline">
					<#form:input path="roomNum" dataFormat="digits"  class="form-control digits width-120"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		validate: true,
		columnModel: [
			{header:'${text("使用单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
			{header:'${text("使用人")}', name:'userName', index:'a.userName', width:150, align:"left"},
			{header:'${text("功能分类")}', name:'useType', index:'a.useType', width:150, align:"left", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('occupancy_classification')}", val, '${text("未知")}', true);
				}},
			{header:'${text("职级")}', name:'rank', index:'a.rank', width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('ob_establishment_type')}", val, '${text("未知")}', true);
			}},
			{header:'${text("用途")}', name:'purpose', index:'a.purpose', width:150, align:"left"},
			{header:'${text("位置")}', name:'position', index:'a.position', width:150, align:"left"},
			{header:'${text("房间数")}', name:'roomNum', index:'a.roomNum', width:150, align:"left"},
			{header:'${text("申请原由")}', name:'describe', index:'a.describe', width:150, align:"left"},
			{header:'${text("使用日期")}', name:'usageDate', index:'a.usageDate', width:150, align:"left"},
			{header:'${text("使用面积")}', name:'usageArea', index:'a.usageArea', width:150, align:"left"},
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});

	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/apply/exportDetailData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>