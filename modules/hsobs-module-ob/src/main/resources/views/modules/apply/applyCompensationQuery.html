<% layout('/layouts/default.html', {title: '有偿使用查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('有偿使用查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${apply}" action="${ctx}/apply//listApplyDetailQueryData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('使用人')}：</label>
				<div class="control-inline">
					<#form:input path="userName" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('功能分类')}：</label>
				<div class="control-inline width-120">
					<#form:select path="useType" dictType="occupancy_classification" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
					<#form:hidden path="paId" defaultValue="1" />
			</div>
			<div class="form-group">
				<label class="control-label">${text('职级')}：</label>
				<div class="control-inline width-120">
					<#form:select path="rank" dictType="ob_establishment_type" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('使用日期')}：</label>
				<div class="control-inline">
					<#form:input path="dateGte" readonly="true" maxlength="20" class="form-control laydate width-date"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="dateLte.click()"/>
					&nbsp;-&nbsp;
					<#form:input path="dateLte" readonly="true" maxlength="20" class="form-control laydate width-date"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('审批状态')}：</label>
				<div class="control-inline width-120">
					<#form:select path="status" dictType="bpm_biz_status" blankOption="true" class="form-control isQuick"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("使用单位")}', name:'officeName', index:'a.officeName', width:150, align:"center"},
			{header:'${text("使用人")}', name:'userName', index:'a.userName', width:150, align:"center"},
			{header:'${text("功能分类")}', name:'useType', index:'a.useType', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('occupancy_classification')}", val, '${text("未知")}', true);
				}},
			{header:'${text("职级")}', name:'rank', index:'a.rank', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('ob_establishment_type')}", val, '${text("未知")}', true);
			}},
			{header:'${text("使用日期")}', name:'usageDate', index:'a.usageDate', width:150, align:"center"},
			{header:'${text("使用面积")}', name:'usageArea', index:'a.usageArea', width:150, align:"center"},
			{header:'${text("审批状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
			}},
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});

	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/apply/exportDetailData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>