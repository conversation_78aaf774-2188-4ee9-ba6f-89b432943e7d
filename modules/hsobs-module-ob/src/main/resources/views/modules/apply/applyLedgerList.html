<% layout('/layouts/default.html', {title: '使用申请', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('使用明细')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${apply}" action="${ctx}/apply//listLedgerData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('建筑名称')}：</label>
				<div class="control-inline">
					<#form:input path="name" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('使用单位')}：</label>
				<div class="control-inline">
					<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
					path="usedOfficeCode" labelPath=""
					url="${ctx}/sys/office/treeData" allowClear="true"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("建筑名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true},
			{header:'${text("坐落地址")}', name:'address', index:'a.address', width:150, align:"left"},
			{header:'${text("房屋所有权")}', name:'ownerOfficeName', index:'a.ownerOfficeName', width:150, align:"left"},
			{header:'${text("使用单位")}', name:'usedOfficeName', index:'a.usedOfficeName', width:150, align:"left"},
			{header:'${text("使用单位性质")}', name:'officeType', index:'a.officeType', width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('sys_office_type')}", val, '${text("未知")}', true);
			}},
			{header:'${text("编制人数")}', name:'employeeCount', index:'a.employeeCount', width:150, align:"left"},
			{header:'${text("使用层数")}', name:'floorCount', index:'a.floorCount', width:150, align:"left"},
			{header:'${text("具体用房数")}', name:'number', index:'a.number', width:150, align:"left"},

		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/apply/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>