<% layout('/layouts/default.html', {title: '技术业务用房申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('技术业务用房申请管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('apply:businessHouse:edit')){ %>
					<a href="${ctx}/apply/businessHouse/form" class="btn btn-default btnTool" title="${text('新增技术业务用房申请')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${applyBusinessHouse}" action="${ctx}/apply/businessHouse/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('使用单位')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
							path="usedOfficeCode" labelPath="" 
							url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('使用日期')}：</label>
					<div class="control-inline">
						<#form:input path="applyDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('理由')}：</label>
					<div class="control-inline">
						<#form:input path="reason" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('功能')}：</label>
					<div class="control-inline">
						<#form:input path="ability" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注')}：</label>
					<div class="control-inline">
						<#form:input path="remark" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('需求面积')}：</label>
					<div class="control-inline">
						<#form:input path="area" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("申请单位")}', name:'usedOfficeCode', index:'a.used_office_code', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/apply/businessHouse/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑技术业务用房申请")}">'+row.usedOffice.officeName+'</a>';
		}},
		{header:'${text("申请日期")}', name:'applyDate', index:'a.apply_date', width:150, align:"center"},
		{header:'${text("需求面积")}', name:'area', index:'a.area', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("使用功能")}', name:'ability', index:'a.ability', width:150, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('apply:businessHouse:edit')){
				actions.push('<a href="${ctx}/apply/businessHouse/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑技术业务用房申请")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/apply/businessHouse/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除技术业务用房申请")}" data-confirm="${text("确认要删除该技术业务用房申请吗？")}">删除</a>&nbsp;');
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=obApplyBusinessHouse&bizKey='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("流程追踪")}" data-layer="true">流程</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>