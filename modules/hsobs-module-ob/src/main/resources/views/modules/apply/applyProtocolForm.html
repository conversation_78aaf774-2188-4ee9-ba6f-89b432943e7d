<% layout('/layouts/default.html', {title: '有偿使用协议管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(applyProtocol.isNewRecord ? '新增有偿使用协议' : '编辑有偿使用协议')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${applyProtocol}" action="${ctx}/apply/protocol/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('使用单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
									path="usedOfficeCode" labelPath="usedOffice.officeName"
									url="${ctx}/sys/office/treeData"
									class="form-control required" allowClear="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('定编人数')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="personnelNumber" class="form-control required digits" defaultValue="0"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('租用办公用房地址')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="address" maxlength="512" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('使用面积')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="usedArea" class="form-control required number" defaultValue="0"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required ">*</span> ${text('租用理由')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" class="form-control required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('租赁开始时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="leaseBeginDate" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" defaultValue="${date()}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('租赁结束时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="leaseEndDate" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" defaultValue="${date()}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('租金')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="rent" class="form-control required number" defaultValue="0" />
							</div>
						</div>
					</div>
				</div>
				<div class="row">

					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('登记负责人')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="registrationUserCode" path="registrationUserCode" labelPath="registrationUser.userName" title="登记负责人"
								url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
								checkbox="false" itemCode="userCode" itemName="userName"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('经办人')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="handledUserCode" path="handledUserCode" labelPath="handledUser.userName" title="经办人"
								url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
								checkbox="false" itemCode="userCode" itemName="userName"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('签订日期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="signingDate" readonly="true" maxlength="20" class="form-control laydate required"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" defaultValue="${date()}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required">*</span> ${text('相关文件')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${applyProtocol.id}" bizType="applyProtocol_file"
									uploadType="all" class="form-control required" readonly="false" preview="true" dataMap="true"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('apply:protocol:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>