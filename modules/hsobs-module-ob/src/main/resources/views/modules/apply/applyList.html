<% layout('/layouts/default.html', {title: '使用申请', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('使用申请')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('apply::edit')){ %>
				<a href="${ctx}/apply//form" class="btn btn-default btnTool" title="${text('新增使用申请')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${apply}" action="${ctx}/apply//listData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('名称')}：</label>
				<div class="control-inline">
					<#form:input path="name" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('是否有偿')}：</label>
				<div class="control-inline width-120">
					<#form:select path="paid" dictType="sys_yes_no" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('租金')}：</label>
				<div class="control-inline">
					<#form:input path="rent" maxlength="100" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('使用单位')}：</label>
				<div class="control-inline">
					<#form:input path="organizationId" maxlength="64" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('使用人')}：</label>
				<div class="control-inline">
					<#form:input path="applyBy" maxlength="64" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('状态')}：</label>
				<div class="control-inline width-120">
					<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply//form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑申请")}">'+(val||row.id)+'</a>';
			}},
			{header:'${text("是否有偿")}', name:'paid', index:'a.paid', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('sys_yes_no')}", val, '${text("未知")}', true);
			}},
			{header:'${text("使用单位")}', name:'organizationId', index:'a.organization_id', width:150, align:"center", formatter: function(val, obj, row, act){
				return row.usedOffice?.officeName || '';
			}},
			{header:'${text("使用人")}', name:'applyBy', index:'a.apply_by', width:150, align:"center", formatter: function(val, obj, row, act){
				return row.usedUser?.userName || '';
			}},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
			}},
			{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
					var actions = [];
					// if(hasPermi('apply::edit')){
						actions.push('<a href="${ctx}/apply//form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑使用信息")}">编辑</a>&nbsp;');
						actions.push('<a href="${ctx}/apply//delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除使用信息")}" data-confirm="${text("确认要删除该使用申请吗？")}">删除</a>&nbsp;');
					// }
					if (row.status != Global.STATUS_DRAFT){
						actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=ob_apply&bizKey='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("流程追踪")}" data-layer="true">流程</a>&nbsp;');
					}
					return actions.join('');
				}}
		],
		sortableColumn: false,
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>