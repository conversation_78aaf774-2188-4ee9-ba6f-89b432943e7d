<% layout('/layouts/default.html', {title: '使用信息查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('使用信息查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${apply}" action="${ctx}/apply//listApplyInfoQueryData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('使用单位')}：</label>
				<div class="control-inline">
					<#form:treeselect id="officeCode" title="${text('机构选择')}"
					path="officeCode" labelPath=""
					url="${ctx}/sys/office/treeData" allowClear="true"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('房间类型')}：</label>
				<div class="control-inline width-120">
					<#form:select path="type" dictType="occupancy_classification" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('房间用途')}：</label>
				<div class="control-inline">
					<#form:input path="describe" maxlength="512" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("使用单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
			{header:'${text("房间类型")}', name:'type', index:'a.type', width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('occupancy_classification')}", val, '${text("未知")}', true);
			}},
			{header:'${text("申请原由")}', name:'describe', index:'a.describe', width:150, align:"left"},
			{header:'${text("房间用途")}', name:'purpose', index:'a.purpose', width:150, align:"left"},
			{header:'${text("核定面积")}', name:'approvedArea', index:'a.approvedArea', width:150, align:"left"},
			{header:'${text("实际面积")}', name:'actualArea', index:'a.actualArea', width:150, align:"left"},
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});

	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/apply/exportInfoData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>