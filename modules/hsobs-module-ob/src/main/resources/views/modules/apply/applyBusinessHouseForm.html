<% layout('/layouts/default.html', {title: '技术业务用房申请管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<% if(!applyBusinessHouse.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${applyBusinessHouse}" title="技术业务用房使用申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${applyBusinessHouse}" formKey="obApplyBusinessHouse" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(applyBusinessHouse.isNewRecord ? '新增技术业务用房申请' : '编辑技术业务用房申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${applyBusinessHouse}" action="${ctx}/apply/businessHouse/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
									path="usedOfficeCode" labelPath="usedOffice.officeName"
									url="${ctx}/sys/office/treeData"
									class=" required" allowClear="true" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请日期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<% if(commonReadonly) { %>
									<#form:input path="applyDate" class="form-control required" dataFormat="datetime" readonly="${commonReadonly}"/>
								<% } else { %>
									<#form:input path="applyDate" maxlength="20" class="form-control laydate required"
										dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" defaultValue="${date()}"/>
								<% } %>

							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('技术用房理由')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="reason" class="form-control required" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('需求面积')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="area" class="form-control required number" defaultValue="0" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('使用功能')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="ability" maxlength="512" class="form-control required" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remark" rows="4" maxlength="1000" class="form-control" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required hide">*</span> ${text('其余相关材料')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${applyBusinessHouse.id}" bizType="applyBusinessHouse_file"
									uploadType="all" class="" readonly="false" preview="true" dataMap="true" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-xs-2">审批意见：</label>
							<div class="col-xs-10">
								<#bpm:comment bpmEntity="${applyBusinessHouse}" showCommWords="true" />
							</div>
						</div>
					</div>
				</div>
				<#bpm:nextTaskInfo bpmEntity="${applyBusinessHouse}" />
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('apply:businessHouse:edit')){ %>
							<#form:hidden path="status"/>
							<% if (applyBusinessHouse.isNewRecord || applyBusinessHouse.status == '9'){ %>
								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
							<% } %>
							<#bpm:button bpmEntity="${applyBusinessHouse}" formKey="obApplyBusinessHouse" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};
// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>