<% layout('/layouts/default.html', {title: '建设项目管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('建设项目管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('apply:newProject:edit')){ %>
						<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入</a>
					<a href="${ctx}/apply/newProject/form" class="btn btn-default btnTool" title="${text('新增新建项目管理')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${newProject}" action="${ctx}/apply/newProject/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('办公用房名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('地址')}：</label>
					<div class="control-inline">
						<#form:input path="address" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('办公用房分类')}：</label>
					<div class="control-inline">
						<#form:input path="classification" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('面积')}：</label>
					<div class="control-inline">
						<#form:input path="area" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('使用功能')}：</label>
					<div class="control-inline">
						<#form:input path="ability" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("办公用房名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/apply/newProject/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑新建项目管理")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("地址")}', name:'address', index:'a.address', width:150, align:"left"},
		{header:'${text("办公用房分类")}', name:'classification', index:'a.classification', width:150, align:"left"},
		{header:'${text("面积")}', name:'area', index:'a.area', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("使用功能")}', name:'ability', index:'a.ability', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('apply:newProject:edit')){
				actions.push('<a href="${ctx}/apply/newProject/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑新建项目")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/apply/newProject/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除新建项目")}" data-confirm="${text("确认要删除该新建项目吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script><script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/apply/newProject/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入新建项目管理")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/apply/newProject/importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/apply/newProject/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>