<% layout('/layouts/default.html', {title: '使用申请管理', libs: ['validate','fileupload','inputmask','dataGrid']}){ %>
<div class="main-content">
	<% if(!apply.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${apply}" title="办公用房使用申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${apply}" formKey="ob_apply" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(apply.isNewRecord ? '新增使用申请' : '编辑使用申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${apply}" action="${ctx}/apply//save" method="post" class="form-horizontal">
		<div class="box-body">
			<div class="form-unit">${text('基本信息')}</div>
			<#form:hidden path="id"/>
			<div class="row">
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="512" class="form-control required" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required">*</span> ${text('申请原由')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="describe" rows="4" maxlength="1000" class="form-control required" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required">*</span> ${text('用途')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:input path="purpose" maxlength="1000" class="form-control required" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('是否有偿')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="paid" dictType="sys_yes_no" class="form-control" readonly="${commonReadonly}" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('租金')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<div class="input-group">
									<span class="input-group-addon"><i class="fa fa-fw fa-rmb"></i></span>
									<#form:input path="rent" maxlength="200" class="form-control inputmask"
									data-inputmask-alias="money" data-inputmask="'digits':'2'" defaultValue="0" readonly="${commonReadonly}"/>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('使用单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
									callbackFuncName="listselectCallback"
									path="usedOfficeCode" labelPath="usedOffice.officeName"
									url="${ctx}/sys/office/treeData"
									class="form-control required" allowClear="true" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('使用人')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="usedUserCode" path="usedUserCode" labelPath="usedUser.userName" title="使用人"
									url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
									checkbox="false" itemCode="userCode" itemName="userName" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>


				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('使用面积')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="area" dataFormat="number2" class="form-control required" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('使用时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applyDate" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" defaultValue="${date()}" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('位置')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="position"  class="form-control" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('房间数')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="roomNum" dataFormat="digits" class="form-control digits" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required">*</span> ${text('相关文件')}：</label>
							<div class="col-sm-10">
								<p class="text-muted"><h5><small>申请函、现状报告、合同等</small></h5></p>
								<#form:fileupload id="uploadFile" bizKey="${apply.id}" bizType="apply_file"
								uploadType="all" class="required" readonly="false" preview="true" dataMap="true" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" class="form-control" readonly="${commonReadonly}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="form-unit">${text('使用单位现状')}</div>
					<div class="col-xs-12" style="text-align: center">
						<div class="nav-tabs-custom">
							<ul class="nav nav-tabs" id="establishmentTabs">
								<li class="active"><a href="#centralTab" data-toggle="tab">中央机关</a></li>
								<li><a href="#provinceTab" data-toggle="tab">省级机关</a></li>
								<li><a href="#municipalTab" data-toggle="tab">市级机关</a></li>
								<li><a href="#countyTab" data-toggle="tab">县级机关</a></li>
								<li><a href="#townshipTab" data-toggle="tab">乡级机关</a></li>
							</ul>
							<div class="tab-content">
								<!-- 中央机关 -->
								<div class="tab-pane active" id="centralTab">
									<table class="table table-bordered">
										<thead>
										<tr>
											<th width="20%">${text("部级正职")}</th>
											<th width="20%">${text("部级副职")}</th>
											<th width="20%">${text("正司(局)级")}</th>
											<th width="20%">${text("副司(局)级")}</th>
											<th width="20%">${text("处级以下")}</th>
										</tr>
										</thead>
										<tbody>
										<tr>
											<td><#form:input name="ministerPositive" path="ministerPositive" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="ministerDeputy" path="ministerDeputy" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="departmentDirector" path="departmentDirector" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="deputyDepartmentDirector" path="deputyDepartmentDirector" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="belowDivisionLevel" path="belowDivisionLevel" class="form-control digits" defaultValue="0"/></td>
										</tr>
										</tbody>
									</table>
								</div>

								<!-- 省级机关 -->
								<div class="tab-pane" id="provinceTab">
									<table class="table table-bordered">
										<thead>
										<tr>
											<th width="15%">${text("省级正职")}</th>
											<th width="15%">${text("省级副职")}</th>
											<th width="14%">${text("正厅(局)级")}</th>
											<th width="14%">${text("副厅(局)级")}</th>
											<th width="14%">${text("正处级")}</th>
											<th width="14%">${text("副处级")}</th>
											<th width="14%">${text("处级以下")}</th>
										</tr>
										</thead>
										<tbody>
										<tr>
											<td><#form:input name="provincePositive" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="provinceDeputy" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="bureauDirector" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="deputyBureauDirector" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="divisionChief" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="deputyDivisionChief" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="belowDivisionChief" class="form-control digits" defaultValue="0"/></td>
										</tr>
										</tbody>
									</table>
								</div>

								<!-- 市级机关 -->
								<div class="tab-pane" id="municipalTab">
									<table class="table table-bordered">
										<thead>
										<tr>
											<th width="20%">${text("市级正职")}</th>
											<th width="20%">${text("市级副职")}</th>
											<th width="20%">${text("正局(处)级")}</th>
											<th width="20%">${text("副局(处)级")}</th>
											<th width="20%">${text("局(处)级以下")}</th>
										</tr>
										</thead>
										<tbody>
										<tr>
											<td><#form:input name="municipalPositive" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="municipalDeputy" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="municipalBureauDirector" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="municipalDeputyBureauDirector" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="belowMunicipalBureau" class="form-control digits" defaultValue="0"/></td>
										</tr>
										</tbody>
									</table>
								</div>

								<!-- 县级机关 -->
								<div class="tab-pane" id="countyTab">
									<table class="table table-bordered">
										<thead>
										<tr>
											<th width="20%">${text("县级正职")}</th>
											<th width="20%">${text("县级副职")}</th>
											<th width="20%">${text("正科级")}</th>
											<th width="20%">${text("副科级")}</th>
											<th width="20%">${text("科级以下")}</th>
										</tr>
										</thead>
										<tbody>
										<tr>
											<td><#form:input name="countyPositive" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="countyDeputy" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="sectionChief" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="deputySectionChief" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="belowSectionLevel" class="form-control digits" defaultValue="0"/></td>
										</tr>
										</tbody>
									</table>
								</div>

								<!-- 乡级机关 -->
								<div class="tab-pane" id="townshipTab">
									<table class="table table-bordered">
										<thead>
										<tr>
											<th width="33%">${text("乡级正职")}</th>
											<th width="33%">${text("乡级副职")}</th>
											<th width="34%">${text("乡级以下")}</th>
										</tr>
										</thead>
										<tbody>
										<tr>
											<td><#form:input name="townshipPositive" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="townshipDeputy" class="form-control digits" defaultValue="0"/></td>
											<td><#form:input name="belowTownshipLevel" class="form-control digits" defaultValue="0"/></td>
										</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>

					<div class="col-xs-12">
						<div class="panel panel-default">
							<div class="panel-heading">${text('用房现状')}</div>
							<div class="panel-body">
								<table class="table table-bordered">
									<thead>
									<tr>
										<th width="20%">${text("办公室")}</th>
										<th width="20%">${text("服务用房")}</th>
										<th width="20%">${text("设备用房")}</th>
										<th width="20%">${text("附属用房")}</th>
										<th width="20%">${text("技术业务用房")}</th>
									</tr>
									</thead>
									<tbody>
									<tr>
										<td><#form:input name="realEstateType_0" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="realEstateType_1" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="realEstateType_2" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="realEstateType_3" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="realEstateType_4" class="form-control digits" defaultValue="0"/></td>
									</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>


				<div class="form-unit">${text('使用凭证')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required hide">*</span> ${text('凭证材料')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadVouchersFile" bizKey="${apply.id}" bizType="apply_vouchers_file"
									uploadType="all" class="" readonly="false" preview="true" dataMap="true" maxUploadNum="1"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('使用协议')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required hide">*</span> ${text('协议材料')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadAgreementFile" bizKey="${apply.id}" bizType="apply_agreement_file"
									uploadType="all" class="" readonly="false" preview="true" dataMap="true" maxUploadNum="1"/>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('不动产信息')}</div>

				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('不动产类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select id="realEstateType" path="realEstateType" dictType="ob_real_estate_type" class="form-control" />
							</div>
						</div>
					</div>
					<div class="col-xs-12">

						<div class="form-unit-wrap table-form" id="realEstateRoomWrapper">
							<#form:btnlistselect id="applyRealEstateDataGridAddRowListselectBtn" title="关联房间"
							allowClear="true"
							btnLabel="选择房间"
							setSelectDataFuncName="realEstateBtnListselectSetSelectData"
							url="${ctx}/estate/realEstate/realEstateSelect"
							allowClear="false"
							checkbox="true" itemCode="id" itemName="name" />
						</div>
						<div class="form-unit-wrap table-form" id="realEstateHouseWrapper" style="display:none;">
							<#form:btnlistselect id="applyRealEstateAddressDataGridAddRowListselectBtn" title="关联房屋"
							allowClear="true"
							btnLabel="选择房屋"
							setSelectDataFuncName="realEstateAddressBtnListselectSetSelectData"
							url="${ctx}/estate/realEstateAddress/realEstateAddressSelect"
							allowClear="false"
							checkbox="true" itemCode="id" itemName="name" />
						</div>

						<div class="form-unit-wrap table-form">
							<table id="realEstateDataGrid"></table>
						</div>
					</div>
				</div>



				<div class="form-unit">${text('审批')}</div>
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-xs-2">审批意见：</label>
							<div class="col-xs-10">
								<#bpm:comment bpmEntity="${apply}" showCommWords="true" />
							</div>
						</div>
					</div>
				</div>
				<#bpm:nextTaskInfo bpmEntity="${apply}" />
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('apply::edit')){ %>
						<#form:hidden path="status"/>
						<% if (apply.isNewRecord || apply.status == '9'){ %>
						<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
						<% } %>
						<#bpm:button bpmEntity="${apply}" formKey="ob_apply" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>

<script>
	function updateEstablishmentTabs(officeTypeCode) {
		let tabMap = {
			'1': 'centralTab',     // 中央机关
			'2': 'provinceTab',   // 省级机关
			'3': 'municipalTab',  // 市级机关
			'4': 'countyTab',     // 县级机关
			'5': 'townshipTab'    // 乡级机关
		};

		// 隐藏所有标签页和标签头
		$('#establishmentTabs li').hide();
		$('.tab-content .tab-pane').hide();

		// 显示对应的标签页和标签头
		let defaultTab = tabMap[officeTypeCode] || 'centralTab';
		$('#establishmentTabs a[href="#' + defaultTab + '"]').parent().show();
		$('#' + defaultTab).show();

		// 激活对应的标签页
		$('#establishmentTabs a[href="#' + defaultTab + '"]').tab('show');
	}

	const reloadOfficeUsedRealEstateCount = (officeCode) => {
		js.ajaxSubmit(ctx + '/sys/office/getOffice', {
			officeCode: officeCode,
		}, function(data){
			updateEstablishmentTabs(data.officeType);
			if (data.officeEstablishment) {
				Object.keys(data.officeEstablishment).forEach(key => {
					const input = $(`input[name="`+key+`"]:not([type='hidden'])`);
					if (input.length > 0) {
						input.val(data.officeEstablishment[key] || '0');
					}
				});
			}

			// 处理用房现状数据
			if (data.usedRealEstateCount && data.usedRealEstateCount.length > 0) {
				// 如果有数据则正常填充
				data.usedRealEstateCount.forEach(item => {
					const input = $(`input[name="realEstateType_`+item.type+`"]`);
					if (input.length > 0) {
						input.val(item.count || '0');
					}
				})
			} else {
				// 如果无数据则全部置零
				for(let i = 0; i <= 4; i++) {
					const input = $(`input[name="realEstateType_`+i+`"]`);
					if (input.length > 0) {
						input.val('0');
					}
				}
			}
		});
	}

	function listselectCallback(id, act, index, layero, nodes){
		if (id === 'usedOfficeCode' && act === 'ok'){
			reloadOfficeUsedRealEstateCount(nodes[0].code);
		}
	}

	// 初始化时根据当前单位类型显示对应的标签页
	if (`${apply.usedOfficeCode}`) {
		reloadOfficeUsedRealEstateCount(`${apply.usedOfficeCode}`);
	} else {
		// 如果没有单位，默认显示中央机关并隐藏其他标签
		updateEstablishmentTabs('1');
	}
</script>
<script>
	$(function() {
		var gridInitialized = false;
		// Listen for changes on the real estate type select field
		$('#realEstateType').change(function() {
			var selectedType = $(this).val(); // Get the selected real estate type


			if (!gridInitialized) {
				gridInitialized = true;  // Set the flag after the first load
			} else {
				$('#realEstateDataGrid').jqGrid('clearGridData');
			}
			if (selectedType == '2') {  // Replace 'someRoomType' with the type for rooms
				$('#realEstateRoomWrapper').show();  // Show room selection
				$('#realEstateHouseWrapper').hide(); // Hide house selection
			} else if (selectedType == '1') {  // Replace 'someHouseType' with the type for houses
				$('#realEstateRoomWrapper').hide();  // Hide room selection
				$('#realEstateHouseWrapper').show(); // Show house selection
			} else {
				$('#realEstateRoomWrapper').hide();
				$('#realEstateHouseWrapper').hide(); // Default, hide both if no specific type is selected
			}
		});

		$('#realEstateType').trigger('change');
	});
</script>
<script>
	function realEstateBtnListselectSetSelectData(id, selectData){
		if (id == 'applyRealEstateDataGridAddRowListselectBtn'){
			if ($.isEmptyObject(selectData)) {
				return;
			}
			let gridData = $('#realEstateDataGrid').dataGrid('getRowData').map(item => item.realEstateId);
			$.each(selectData, (key, value) => {
				let realEstateId = key.toString();
				if (gridData.includes(realEstateId)) {
					return true;
				}
				let realEstateName = value['name'];
				let realEstateAddressName =  value?.realEstateAddress?.name ?? '';
				$('#realEstateDataGrid').jqGrid('addRow', {
					position: 'first',
					addRowParams: {keys: false, focusField: true},
					initdata: {
						applyId: {id: ''},
						realEstateId: realEstateId,
						realEstate: {name: realEstateName, realEstateAddressName: realEstateAddressName},
						realEstateAddressName: realEstateAddressName,
						status: Global.STATUS_NORMAL
					}
				});
				gridData.push(realEstateId);
			});
		}
	}
	function realEstateAddressBtnListselectSetSelectData(id, selectData){
		if (id == 'applyRealEstateAddressDataGridAddRowListselectBtn'){
			if ($.isEmptyObject(selectData)) {
				return;
			}
			let gridData = $('#realEstateDataGrid').dataGrid('getRowData').map(item => item.realEstateId);
			$.each(selectData, (key, value) => {
				let realEstateId = key.toString();
				if (gridData.includes(realEstateId)) {
					return true;
				}
				let realEstateName = value['name'];
				$('#realEstateDataGrid').jqGrid('addRow', {
					position: 'first',
					addRowParams: {keys: false, focusField: true},
					initdata: {
						applyId: {id: ''},
						realEstateId: realEstateId,
						realEstate: {name: realEstateName},
						status: Global.STATUS_NORMAL
					}
				});
				gridData.push(realEstateId);
			});
		}
	}
</script>
<script>
	//# // 初始化权属登记关联不动产表DataGrid对象
	$('#realEstateDataGrid').dataGrid({

		data: "#{toJson(apply.applyRealEstateList)}",
		datatype: 'local', // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度

		//# // 设置数据表格列
		columnModel: [
			{header:'状态', name:'status', editable:false, hidden:true},
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'${text("apply_id")}', name:'applyId.id', editable:true, hidden:true},
			{header:'${text("real_estate_id")}', name:'realEstateId', width:150, editable:true, hidden: true},
			{header:'名称', name:'realEstate.name', editable:false, width:80, align:'center', formatter: function (val, obj, row, act) {
					return row.realEstate?.name || row.realEstateAddress?.name || '';
				}},
			{header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
					var actions = [];
					actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#realEstateDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
					return actions.join('');
				}, editoptions: {defaultValue: 'new'}}
		],

		//# // 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
		editGridInitAllRowEdit: true,  // 是否初始化就编辑所有行（*** 重点 ***）
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
		editGridAddRowInitData: {id: '',applyId: {id: ''},realEstateId: ''},	// 新增行的时候初始化的数据

		//# // 编辑表格的提交数据参数
		editGridInputFormListName: 'applyRealEstateList', // 提交的数据列表名
		editGridInputFormListAttrs: 'id,applyId.id,realEstateId,createBy,createDate,updateBy,updateDate,', // 提交数据列表的属性字段

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){
		}
	});
</script>
<script>

	$(function () {
		$('#realEstateType').change(function () {
			toggleRealEstateFields();
		});
		toggleRealEstateFields();
	});

	function toggleRealEstateFields() {
		let realEstateType = $('#realEstateType').val();
		if (realEstateType === '1') {
			$('#realEstateAddressField').show();
			$('#realEstateField').hide();
			$('#realEstateIdName').val('');
			$('#realEstateIdCode').val('');
		} else {
			$('#realEstateAddressField').hide();
			$('#realEstateField').show();
			$('#realEstateAddressIdName').val('');
			$('#realEstateAddressIdCode').val('');
		}
	}
	// 业务实现草稿按钮
	$('#btnDraft').click(function(){
		$('#status').val(Global.STATUS_DRAFT);
	});
	// 流程按钮操作事件
	BpmButton = window.BpmButton || {};
	BpmButton.init = function(task){
		if (task.status != '2') {
			$('.taskComment').removeClass('hide');
		}
	}
	BpmButton.complete = function($this, task){
		$('#status').val(Global.STATUS_AUDIT);
	};
	// 表单验证提交事件
	$('#inputForm').validate({
		submitHandler: function(form){
			js.ajaxSubmitForm($(form), function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.closeCurrentTabPage(function(contentWindow){
						contentWindow.page();
					});
				}
			}, "json");
		}
	});

</script>