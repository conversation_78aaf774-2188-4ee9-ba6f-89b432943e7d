<% layout('/layouts/default.html', {title: '处置利用管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<% if(!disposalUtilizationManagement.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${disposalUtilizationManagement}" title="处置利用管理申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${disposalUtilizationManagement}" formKey="disposal_utilization_management" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(disposalUtilizationManagement.isNewRecord ? '新增处置利用管理' : '编辑处置利用管理')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${disposalUtilizationManagement}" action="${ctx}/disposalutilizationmanagement//save" method="post" class="form-horizontal">
		<div class="box-body hs-box-body-bpm">
			<div class="form-unit">${text('基本信息')}</div>
			<#form:hidden path="id"/>
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('房间信息')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:listselect id="roomId" title="房间选择"
							path="roomId" labelPath="realEstate.name"
							url="${ctx}/estate/realEstate/realEstateSelectDisposal" allowClear="false"
							checkbox="false" itemCode="id" itemName="name" callbackFuncName="treeselectCallback"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('申请人')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:treeselect id="applicantId" title="${text('用户选择')}"
							path="applicantId" labelPath="employee.empName"
							url="${ctx}/sys/office/treeData?isLoadUser=true"
							class="required" allowClear="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('申请单位')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:treeselect id="applicantUnitId" title="${text('机构选择')}"
							path="applicantUnitId" labelPath="office.officeName"
							callbackFuncName="listselectCallback"
							url="${ctx}/sys/office/treeData"
							class="required" allowClear="true"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('申请理由')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input path="reasonForApplication" maxlength="256" class="form-control"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('申请用途')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input path="applicationForUse" maxlength="256" class="form-control"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('申请日期')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input path="applicationDate" readonly="true" maxlength="20" class="form-control laydate required"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('申请处置函')}：
						</td>
						<td colspan="3">
							<#form:fileupload id="uploadFile" bizKey="${disposalUtilizationManagement.id}" bizType="disposalUtilizationManagement_file"
							uploadType="all" class="" readonly="false" preview="true" dataMap="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('处置类型')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:select readonly="true" path="disposalType" dictType="disposal_type" blankOption="true" class="form-control" />
						</td>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('处置意见')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input disabled="true" path="disposalOpinion" maxlength="255" class="form-control"/>
						</td>
					</tr>
				</table>
			</div>
			<div class="row">
				<div class="form-unit">${text('申请单位现状')}</div>
				<div class="col-xs-12">
					<div class="nav-tabs-custom">
						<ul class="nav nav-tabs" id="establishmentTabs">
							<li class="active"><a href="#centralTab" data-toggle="tab">中央机关</a></li>
							<li><a href="#provinceTab" data-toggle="tab">省级机关</a></li>
							<li><a href="#municipalTab" data-toggle="tab">市级机关</a></li>
							<li><a href="#countyTab" data-toggle="tab">县级机关</a></li>
							<li><a href="#townshipTab" data-toggle="tab">乡级机关</a></li>
						</ul>
						<div class="tab-content">
							<!-- 中央机关 -->
							<div class="tab-pane active" id="centralTab">
								<table class="table table-bordered">
									<thead>
									<tr>
										<th width="20%">${text("部级正职")}</th>
										<th width="20%">${text("部级副职")}</th>
										<th width="20%">${text("正司(局)级")}</th>
										<th width="20%">${text("副司(局)级")}</th>
										<th width="20%">${text("处级以下")}</th>
									</tr>
									</thead>
									<tbody>
									<tr>
										<td><#form:input name="ministerPositive" path="ministerPositive" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="ministerDeputy" path="ministerDeputy" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="departmentDirector" path="departmentDirector" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="deputyDepartmentDirector" path="deputyDepartmentDirector" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="belowDivisionLevel" path="belowDivisionLevel" class="form-control digits" defaultValue="0"/></td>
									</tr>
									</tbody>
								</table>
							</div>

							<!-- 省级机关 -->
							<div class="tab-pane" id="provinceTab">
								<table class="table table-bordered">
									<thead>
									<tr>
										<th width="15%">${text("省级正职")}</th>
										<th width="15%">${text("省级副职")}</th>
										<th width="14%">${text("正厅(局)级")}</th>
										<th width="14%">${text("副厅(局)级")}</th>
										<th width="14%">${text("正处级")}</th>
										<th width="14%">${text("副处级")}</th>
										<th width="14%">${text("处级以下")}</th>
									</tr>
									</thead>
									<tbody>
									<tr>
										<td><#form:input name="provincePositive" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="provinceDeputy" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="bureauDirector" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="deputyBureauDirector" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="divisionChief" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="deputyDivisionChief" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="belowDivisionChief" class="form-control digits" defaultValue="0"/></td>
									</tr>
									</tbody>
								</table>
							</div>

							<!-- 市级机关 -->
							<div class="tab-pane" id="municipalTab">
								<table class="table table-bordered">
									<thead>
									<tr>
										<th width="20%">${text("市级正职")}</th>
										<th width="20%">${text("市级副职")}</th>
										<th width="20%">${text("正局(处)级")}</th>
										<th width="20%">${text("副局(处)级")}</th>
										<th width="20%">${text("局(处)级以下")}</th>
									</tr>
									</thead>
									<tbody>
									<tr>
										<td><#form:input name="municipalPositive" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="municipalDeputy" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="municipalBureauDirector" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="municipalDeputyBureauDirector" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="belowMunicipalBureau" class="form-control digits" defaultValue="0"/></td>
									</tr>
									</tbody>
								</table>
							</div>

							<!-- 县级机关 -->
							<div class="tab-pane" id="countyTab">
								<table class="table table-bordered">
									<thead>
									<tr>
										<th width="20%">${text("县级正职")}</th>
										<th width="20%">${text("县级副职")}</th>
										<th width="20%">${text("正科级")}</th>
										<th width="20%">${text("副科级")}</th>
										<th width="20%">${text("科级以下")}</th>
									</tr>
									</thead>
									<tbody>
									<tr>
										<td><#form:input name="countyPositive" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="countyDeputy" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="sectionChief" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="deputySectionChief" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="belowSectionLevel" class="form-control digits" defaultValue="0"/></td>
									</tr>
									</tbody>
								</table>
							</div>

							<!-- 乡级机关 -->
							<div class="tab-pane" id="townshipTab">
								<table class="table table-bordered">
									<thead>
									<tr>
										<th width="33%">${text("乡级正职")}</th>
										<th width="33%">${text("乡级副职")}</th>
										<th width="34%">${text("乡级以下")}</th>
									</tr>
									</thead>
									<tbody>
									<tr>
										<td><#form:input name="townshipPositive" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="townshipDeputy" class="form-control digits" defaultValue="0"/></td>
										<td><#form:input name="belowTownshipLevel" class="form-control digits" defaultValue="0"/></td>
									</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>

				<div class="col-xs-12">
					<div class="panel panel-default">
						<div class="panel-heading">${text('用房现状')}</div>
						<div class="panel-body">
							<table class="table table-bordered">
								<thead>
								<tr>
									<th width="20%">${text("办公室")}</th>
									<th width="20%">${text("服务用房")}</th>
									<th width="20%">${text("设备用房")}</th>
									<th width="20%">${text("附属用房")}</th>
									<th width="20%">${text("技术业务用房")}</th>
								</tr>
								</thead>
								<tbody>
								<tr>
									<td><#form:input name="realEstateType_0" class="form-control digits" defaultValue="0"/></td>
									<td><#form:input name="realEstateType_1" class="form-control digits" defaultValue="0"/></td>
									<td><#form:input name="realEstateType_2" class="form-control digits" defaultValue="0"/></td>
									<td><#form:input name="realEstateType_3" class="form-control digits" defaultValue="0"/></td>
									<td><#form:input name="realEstateType_4" class="form-control digits" defaultValue="0"/></td>
								</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<!-- 以下是隐藏部分，保持原有结构 -->
			<div class="hide" id="demolition1" style="padding: 0;">
				<div class="form-unit">${text('拆除征收信息')}</div>
				<div class="hs-table-div">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拆除征收地址')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input readonly="true" path="realEstateAddress.address" maxlength="255" class="form-control"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('征收日期')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input disabled="true" path="demolitionDisposalService.collectionTime" readonly="true" maxlength="20" class="form-control laydate"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('征收单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td colspan="3">
								<#form:treeselect readonly="true" id="applicantUnitId2" title="${text('机构选择')}"
								path="applicantUnitId2" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								allowClear="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('征收文件')}：
							</td>
							<td colspan="3">
								<#form:fileupload readonly="true" id="uploadFile2" bizKey="${disposalUtilizationManagement.id}" bizType="disposalUtilizationManagement_demolitionDisposalService_file"
								uploadType="all" class="" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
			</div>

			<!-- 拍卖转让部分 -->
			<div class="hide" id="demolition2" style="padding: 0;">
				<div class="form-unit">${text('拍卖转让信息')}</div>
				<div class="hs-table-div">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拍卖备案单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:treeselect readonly="true" id="applicantUnitId3" title="${text('机构选择')}"
								path="applicantUnitId3" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								allowClear="true"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拍卖日期')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input disabled="true" path="auctionTransferDisposalBusiness.auctionTime" readonly="true" maxlength="20" class="form-control laydate"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('购买单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:treeselect readonly="true" id="applicantUnitId4" title="${text('机构选择')}"
								path="auctionTransferDisposalBusiness.purchasingUnit" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								allowClear="true"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拍卖成交价格')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input disabled="true" path="auctionTransferDisposalBusiness.salePrice" maxlength="255" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拍卖相关文件')}：
							</td>
							<td colspan="3">
								<#form:fileupload readonly="true" id="uploadFile3" bizKey="${disposalUtilizationManagement.id}" bizType="disposalUtilizationManagement_auctionTransferDisposalBusiness_file"
								uploadType="all" class="" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
			</div>

			<!-- 用途转换部分 -->
			<div class="hide" id="demolition3" style="padding: 0;">
				<div class="form-unit">${text('用途转换信息')}</div>
				<div class="hs-table-div">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('转换后用途')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:select readonly="true" path="usageConversion.convertedUsage" dictType="occupancy_classification" blankOption="true" class="form-control" />
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('审批单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:treeselect readonly="true" title="${text('机构选择')}"
								path="usageConversion.approvalUnit" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								allowClear="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('审批日期')}：<i class="fa icon-question hide"></i>
							</td>
							<td colspan="3">
								<#form:input disabled="true" path="usageConversion.approvalTime" readonly="true" maxlength="20" class="form-control laydate"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('转换用途相关文件')}：
							</td>
							<td colspan="3">
								<#form:fileupload readonly="true" id="uploadFile4" bizKey="${disposalUtilizationManagement.id}" bizType="disposalUtilizationManagement_usageConversion_file"
								uploadType="all" class="" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('disposalutilizationmanagement::edit')){ %>
							<#form:hidden path="status"/>
							<% if (disposalUtilizationManagement.isNewRecord || disposalUtilizationManagement.status == '9'){ %>
								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
							<% } %>
							<#bpm:button bpmEntity="${disposalUtilizationManagement}" formKey="disposal_utilization_management" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};
// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
$(document).ready(function () {
	const selectedValue = $('#disposalType').val();
	// 根据选择的值显示或隐藏字段
	if (selectedValue === '5') {
		$('#demolition1').removeClass('hide');
		$('#demolition2').addClass('hide');
		$('#demolition3').addClass('hide');
	} else if (selectedValue === '4') {
		$('#demolition1').addClass('hide');
		$('#demolition2').removeClass('hide');
		$('#demolition3').addClass('hide');
	}  else if (selectedValue === '1') {
		$('#demolition1').addClass('hide');
		$('#demolition2').addClass('hide');
		$('#demolition3').removeClass('hide');
	} else {
		$('#demolition1').addClass('hide');
		$('#demolition2').addClass('hide');
		$('#demolition3').addClass('hide');
	}
});

function treeselectCallback(id, act, index, layero, nodes){
	if(id=='roomId'){
		for (const [key, value] of Object.entries(nodes)) {
			console.log(value)
			updateApplicantSelect(value);  // 选择的节点数据
		}
	}
}
function updateApplicantSelect(userData) {
	// 获取树形选择组件的实例
	var treeSelect = $('#applicantId');
	if (treeSelect && userData) {
		// 方法1：如果只需要设置选中值
		// $('#applicantId').select2('data', { id: userData.usedUser.userCode, text: userData.usedUser.userName })
		if(userData.usedUser!=null){
			$('#applicantIdName').val(userData.usedUser.userName).trigger('change');
			$('#applicantIdCode').val(userData.usedUser.userCode).trigger('change');
		}
		if(userData.usedOffice!=null){
			$('#applicantUnitIdName').val(userData.usedOffice.officeName).trigger('change');
			$('#applicantUnitIdCode').val(userData.usedOffice.officeCode).trigger('change');
			reloadOfficeUsedRealEstateCount(userData.usedOffice.officeCode);
		}
	}
}
</script>
<script>
	function updateEstablishmentTabs(officeTypeCode) {
		let tabMap = {
			'1': 'centralTab',     // 中央机关
			'2': 'provinceTab',   // 省级机关
			'3': 'municipalTab',  // 市级机关
			'4': 'countyTab',     // 县级机关
			'5': 'townshipTab'    // 乡级机关
		};

		// 隐藏所有标签页和标签头
		$('#establishmentTabs li').hide();
		$('.tab-content .tab-pane').hide();

		// 显示对应的标签页和标签头
		let defaultTab = tabMap[officeTypeCode] || 'centralTab';
		$('#establishmentTabs a[href="#' + defaultTab + '"]').parent().show();
		$('#' + defaultTab).show();

		// 激活对应的标签页
		$('#establishmentTabs a[href="#' + defaultTab + '"]').tab('show');
	}

	const reloadOfficeUsedRealEstateCount = (officeCode) => {
		js.ajaxSubmit(ctx + '/sys/office/getOffice', {
			officeCode: officeCode,
		}, function(data){
			updateEstablishmentTabs(data.officeType);
			if (data.officeEstablishment) {
				Object.keys(data.officeEstablishment).forEach(key => {
					const input = $(`input[name="`+key+`"]:not([type='hidden'])`);
					if (input.length > 0) {
						input.val(data.officeEstablishment[key] || '0');
					}
				});
			}

			// 处理用房现状数据
			if (data.usedRealEstateCount && data.usedRealEstateCount.length > 0) {
				// 如果有数据则正常填充
				data.usedRealEstateCount.forEach(item => {
					const input = $(`input[name="realEstateType_`+item.type+`"]`);
					if (input.length > 0) {
						input.val(item.count || '0');
					}
				})
			} else {
				// 如果无数据则全部置零
				for(let i = 0; i <= 4; i++) {
					const input = $(`input[name="realEstateType_`+i+`"]`);
					if (input.length > 0) {
						input.val('0');
					}
				}
			}
		});
	}

	function listselectCallback(id, act, index, layero, nodes){
		console.log(id);
		console.log(act);
		if (id === 'applicantUnitId' && act === 'ok'){
			reloadOfficeUsedRealEstateCount(nodes[0].code);
		}
	}

	// 初始化时根据当前单位类型显示对应的标签页
	if (`${disposalUtilizationManagement.applicantUnitId}`) {
		reloadOfficeUsedRealEstateCount(`${disposalUtilizationManagement.applicantUnitId}`);
	} else {
		// 如果没有单位，默认显示中央机关并隐藏其他标签
		updateEstablishmentTabs('1');
	}
</script>