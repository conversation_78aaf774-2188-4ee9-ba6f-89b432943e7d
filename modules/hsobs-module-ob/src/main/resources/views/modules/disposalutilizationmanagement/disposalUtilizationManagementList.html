<% layout('/layouts/default.html', {title: '处置利用管理管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('处置利用管理管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('disposalutilizationmanagement::edit')){ %>
				<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入</a>
				<a href="${ctx}/disposalutilizationmanagement//form" class="btn btn-default btnTool" title="${text('新增处置利用管理')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${disposalUtilizationManagement}" action="${ctx}/disposalutilizationmanagement//listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('申请人')}：</label>
					<div class="control-inline " >
						<#form:treeselect id="applicantId" title="${text('用户选择')}"
							path="applicantId" labelPath="" 
							url="${ctx}/sys/office/treeData?isLoadUser=true" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请单位')}：</label>
					<div class="control-inline " >
						<#form:treeselect id="applicantUnitId" title="${text('机构选择')}"
							path="applicantUnitId" labelPath="" 
							url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请日期')}：</label>
					<div class="control-inline">
						<#form:input path="applicationDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('资产移交类别')}：</label>
					<div class="control-inline">
						<#form:select path="demolitionDisposalService.assetTransferClass" dictType="asset_transfer_class" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('资产移交日期')}：</label>
					<div class="control-inline">
						<#form:input path="demolitionDisposalService.collectionTime" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('处置类型')}：</label>
					<div class="control-inline ">
						<#form:select path="disposalType" dictType="disposal_type" blankOption="true" class="form-control required" />
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="bpm_biz_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("申请单号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/disposalutilizationmanagement//form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑处置利用管理")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("房间信息")}', name:'realEstate.name', index:'a.room_id', width:150, align:"center"},
		{header:'${text("申请人")}', name:'employee.empName', index:'a.applicant_id', width:150, align:"center"},
		{header:'${text("申请单位")}', name:'office.officeName', index:'a.applicant_unit_id', width:150, align:"center"},
		{header:'${text("申请理由")}', name:'reasonForApplication', index:'a.reason_for_application', width:150, align:"left"},
		{header:'${text("申请用途")}', name:'applicationForUse', index:'a.application_for_use', width:150, align:"left"},
		{header:'${text("申请日期")}', name:'applicationDate', index:'a.application_date', width:150, align:"center"},
		{header:'${text("处置类型")}', name:'disposalType', index:'a.disposal_type', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('disposal_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("资产移交类别")}', name:'demolitionDisposalService.assetTransferClass', index:'a.assetTransferClass', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('asset_transfer_class')}", val, '${text("未知")}', true);
		}},
		{header:'${text("资产移交日期")}', name:'demolitionDisposalService.collectionTime', index:'a.collectionTime', width:150, align:"left"},
		{header:'${text("处置意见")}', name:'disposalOpinion', index:'a.disposal_opinion', width:150, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('disposalutilizationmanagement::edit')){
			// 	actions.push('<a href="${ctx}/disposalutilizationmanagement//form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑处置利用管理")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/disposalutilizationmanagement//delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除处置利用管理")}" data-confirm="${text("确认要删除该处置利用管理吗？")}">删除</a>&nbsp;');
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=disposal_utilization_management&bizKey='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("流程追踪")}" data-layer="true">流程</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>><script>
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/disposalutilizationmanagement//exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
	$('#btnImport').click(function(){
		js.layer.open({
			type: 1,
			area: ['400px'],
			title: '${text("导入处置利用管理")}',
			resize: false,
			scrollbar: true,
			content: js.template('importTpl'),
			btn: ['<i class="fa fa-check"></i> ${text("导入")}',
				'<i class="fa fa-remove"></i> ${text("关闭")}'],
			btn1: function(index, layero){
				var form = {
					inputForm: layero.find('#inputForm'),
					file: layero.find('#file').val()
				};
				if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
					js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
					return false;
				}
				js.ajaxSubmitForm(form.inputForm, function(data){
					js.showMessage(data.message);
					if(data.result == Global.TRUE){
						js.layer.closeAll();
					}
					page();
				}, "json");
				return true;
			}
		});
	});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/disposalutilizationmanagement//importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/disposalutilizationmanagement//importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>