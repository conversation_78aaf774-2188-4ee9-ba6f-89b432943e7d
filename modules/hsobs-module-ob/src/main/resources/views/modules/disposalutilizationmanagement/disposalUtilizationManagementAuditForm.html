<% layout('/layouts/default.html', {title: '处置利用管理审核', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<% if(!disposalUtilizationManagement.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${disposalUtilizationManagement}" title="处置利用管理申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${disposalUtilizationManagement}" formKey="disposal_utilization_management" />
		</div>
	</div>
	<% } %>
	<% if(!disposalUtilizationManagement.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${disposalUtilizationManagement}" title="处置利用管理" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${disposalUtilizationManagement}" formKey="disposal_utilization_management" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(disposalUtilizationManagement.isNewRecord ? '新增处置利用管理' : '编辑处置利用管理')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${disposalUtilizationManagement}" action="${ctx}/disposalutilizationmanagement//save" method="post" class="form-horizontal">
		<div class="box-body hs-box-body-bpm">
			<div class="form-unit">${text('基本信息')}</div>
			<#form:hidden path="id"/>
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('房间信息')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:listselect readonly="true" id="roomId" title="房间选择"
							path="roomId" labelPath="realEstate.name"
							url="${ctx}/estate/realEstate/realEstateSelectDisposal" allowClear="false"
							checkbox="false" itemCode="id" itemName="name"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('申请人')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:treeselect readonly="true" id="applicantId" title="${text('用户选择')}"
							path="applicantId" labelPath="employee.empName"
							url="${ctx}/sys/office/treeData?isLoadUser=true"
							class="required" allowClear="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('申请单位')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:treeselect readonly="true" id="applicantUnitId" title="${text('机构选择')}"
							path="applicantUnitId" labelPath="office.officeName"
							url="${ctx}/sys/office/treeData"
							class="required" allowClear="true"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('申请理由')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input disabled="true" path="reasonForApplication" maxlength="256" class="form-control"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('申请用途')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input disabled="true" path="applicationForUse" maxlength="256" class="form-control"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('申请日期')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input disabled="true" path="applicationDate" readonly="true" maxlength="20" class="form-control laydate required"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:textarea disabled="true" path="remarks" rows="4" maxlength="500" class="form-control"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required">*</span> ${text('申请处置函')}：
						</td>
						<td colspan="3">
							<#form:fileupload id="uploadFile" bizKey="${disposalUtilizationManagement.id}" bizType="disposalUtilizationManagement_file"
							uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('处置类型')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:select readonly="true" path="disposalType" dictType="disposal_type" blankOption="true" class="form-control" />
						</td>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('处置意见')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input disabled="true" path="disposalOpinion" maxlength="255" class="form-control"/>
						</td>
					</tr>
				</table>
			</div>

			<!-- 拆除征收部分 -->
			<div class="hs-table-div hide" id="demolition1" style="padding: 0;">
				<div class="form-unit">${text('拆除征收信息')}</div>
				<div class="hs-table-div">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拆除征收地址')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input readonly="true" path="realEstateAddress.address" maxlength="255" class="form-control"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('征收日期')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input disabled="true" path="demolitionDisposalService.collectionTime" readonly="true" maxlength="20" class="form-control laydate"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('征收单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td colspan="3">
								<#form:treeselect readonly="true" id="applicantUnitId2" title="${text('机构选择')}"
								path="applicantUnitId2" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								allowClear="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('征收文件')}：
							</td>
							<td colspan="3">
								<#form:fileupload readonly="true" id="uploadFile2" bizKey="${disposalUtilizationManagement.id}" bizType="disposalUtilizationManagement_demolitionDisposalService_file"
								uploadType="all" class="" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
			</div>

			<!-- 拍卖转让部分 -->
			<div class="hs-table-div hide" id="demolition2" style="padding: 0;">
				<div class="form-unit">${text('拍卖转让信息')}</div>
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拍卖备案单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:treeselect readonly="true" id="applicantUnitId3" title="${text('机构选择')}"
								path="applicantUnitId3" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								allowClear="true"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拍卖日期')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input disabled="true" path="auctionTransferDisposalBusiness.auctionTime" readonly="true" maxlength="20" class="form-control laydate"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('购买单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:treeselect readonly="true" id="applicantUnitId4" title="${text('机构选择')}"
								path="auctionTransferDisposalBusiness.purchasingUnit" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								allowClear="true"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拍卖成交价格')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input disabled="true" path="auctionTransferDisposalBusiness.salePrice" maxlength="255" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('拍卖相关文件')}：
							</td>
							<td colspan="3">
								<#form:fileupload readonly="true" id="uploadFile3" bizKey="${disposalUtilizationManagement.id}" bizType="disposalUtilizationManagement_auctionTransferDisposalBusiness_file"
								uploadType="all" class="" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
			</div>

			<!-- 用途转换部分 -->
			<div class="hs-table-div hide" id="demolition3" style="padding: 0;">
				<div class="form-unit">${text('用途转换信息')}</div>
				<div class="hs-table-div">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('转换后用途')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:select readonly="true" path="usageConversion.convertedUsage" dictType="occupancy_classification" blankOption="true" class="form-control" />
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('审批单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:treeselect readonly="true" title="${text('机构选择')}"
								path="usageConversion.approvalUnit" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								allowClear="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('审批日期')}：<i class="fa icon-question hide"></i>
							</td>
							<td colspan="3">
								<#form:input disabled="true" path="usageConversion.approvalTime" readonly="true" maxlength="20" class="form-control laydate"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('转换用途相关文件')}：
							</td>
							<td colspan="3">
								<#form:fileupload readonly="true" id="uploadFile4" bizKey="${disposalUtilizationManagement.id}" bizType="disposalUtilizationManagement_usageConversion_file"
								uploadType="all" class="" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
			</div>

			<!-- 审批意见部分 -->
			<div class="hs-table-div taskComment hide">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							${text('审批意见')}：
						</td>
						<td colspan="3">
							<#bpm:comment bpmEntity="${disposalUtilizationManagement}" showCommWords="true" />
						</td>
					</tr>
				</table>
			</div>
		</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('disposalutilizationmanagement::edit')){ %>
							<#form:hidden path="status"/>
							<% if (disposalUtilizationManagement.isNewRecord || disposalUtilizationManagement.status == '9'){ %>
								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
							<% } %>
							<#bpm:button bpmEntity="${disposalUtilizationManagement}" formKey="disposal_utilization_management" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};
// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
$(document).ready(function () {
	const selectedValue = $('#disposalType').val();
	// 根据选择的值显示或隐藏字段
	if (selectedValue === '5') {
		$('#demolition1').removeClass('hide');
		$('#demolition2').addClass('hide');
		$('#demolition3').addClass('hide');
	} else if (selectedValue === '4') {
		$('#demolition1').addClass('hide');
		$('#demolition2').removeClass('hide');
		$('#demolition3').addClass('hide');
	}  else if (selectedValue === '1') {
		$('#demolition1').addClass('hide');
		$('#demolition2').addClass('hide');
		$('#demolition3').removeClass('hide');
	} else {
		$('#demolition1').addClass('hide');
		$('#demolition2').addClass('hide');
		$('#demolition3').addClass('hide');
	}
});
</script>