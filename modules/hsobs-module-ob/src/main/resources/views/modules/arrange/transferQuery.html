<% layout('/layouts/default.html', {title: '调剂查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('调剂查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${arrange}" action="${ctx}/arrange//listDataTransferQuery" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('申请单位')}：</label>
				<div class="control-inline">
					<#form:treeselect id="officeCode" title="${text('机构选择')}"
					path="officeCode" labelPath=""
					url="${ctx}/sys/office/treeData" allowClear="true"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('审批状态')}：</label>
				<div class="control-inline width-120">
					<#form:select readonly="false" blankOption="true" path="transferApprovalStatus" dictType="bpm_biz_status" class="form-control required width-120" />
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("申请单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
			{header:'${text("申请日期")}', name:'transferApplyDate', index:'a.transferApplyDate', width:150, align:"left"},
			{header:'${text("办公用房总建筑面积")}', name:'transferTotalArea', index:'a.transferTotalArea', width:150, align:"left"},
			{header:'${text("基本办公用房面积")}', name:'transferBasicArea', index:'a.transferBasicArea', width:150, align:"left"},
			{header:'${text("附属办公用房面积")}', name:'transferAuxiliaryArea', index:'a.transferAuxiliaryArea', width:150, align:"left"},
			{header:'${text("调剂原由")}', name:'transferReason', index:'a.transferReason', width:150, align:"left"},
			{header:'${text("调剂日期")}', name:'transferDate', index:'a.transferDate', width:150, align:"left"},
			{header:'${text("审批状态")}', name:'transferApprovalStatus', index:'a.transferApprovalStatus', width:150, align:"left", formatter: function (val, obj, row, act) {
				return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '未知', true);
			}},
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/arrange/exportDataTransferQuery',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>