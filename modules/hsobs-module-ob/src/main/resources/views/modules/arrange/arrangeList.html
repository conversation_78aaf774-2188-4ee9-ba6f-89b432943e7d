<% layout('/layouts/default.html', {title: '配置管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${arrange.arrangeTypeName}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('arrange::edit')){ %>
				<a href="${ctx}/arrange//form?arrangeType=${arrange.arrangeType}" class="btn btn-default btnTool" title="${text('新增配置')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${arrange}" action="${ctx}/arrange//listData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<#form:hidden path="arrangeType"/>
			<% if (arrange.arrangeType == '1'){ %>
				<div class="form-group">
					<label class="control-label">${text('配置名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="1000" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('调剂申请单位')}：</label>
					<div class="control-inline">
						<#form:input path="usedOffice.officeName" maxlength="1000" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('用途')}：</label>
					<div class="control-inline">
						<#form:input path="purpose" maxlength="1000" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('审批开始日期')}：</label>
					<div class="control-inline">
						<#form:input path="lastApprovalDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('审批结束日期')}：</label>
					<div class="control-inline">
						<#form:input path="lastApprovalDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
			<% } %>
			<% if (arrange.arrangeType == '2'){ %>
			<div class="form-group">
				<label class="control-label">${text('置换申请单号')}：</label>
				<div class="control-inline">
					<#form:input path="approvalCode" maxlength="1000" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('置换申请单位')}：</label>
				<div class="control-inline">
					<#form:input path="usedOffice.officeName" maxlength="1000" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('配置方式')}：</label>
				<div class="control-inline">
					<#form:input path="way" maxlength="1000" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('现场调研结果')}：</label>
				<div class="control-inline">
					<#form:input path="researchResults" maxlength="1000" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('审批开始日期')}：</label>
				<div class="control-inline">
					<#form:input path="lastApprovalDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('审批结束日期')}：</label>
				<div class="control-inline">
					<#form:input path="lastApprovalDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('审批意见')}：</label>
				<div class="control-inline">
					<#form:input path="lastApprovalCommon" maxlength="1000" class="form-control width-120"/>
				</div>
			</div>
			<% } %>
			<% if (arrange.arrangeType == '3'){ %>
			<div class="form-group">
				<label class="control-label">${text('租用申请单位')}：</label>
				<div class="control-inline">
					<#form:input path="usedOffice.officeName" maxlength="1000" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('租用开始日期')}：</label>
				<div class="control-inline">
					<#form:input path="rentalStartDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('租用结束日期')}：</label>
				<div class="control-inline">
					<#form:input path="rentalEndDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<% } %>
			<% if (arrange.arrangeType == '4'){ %>
			<div class="form-group">
				<label class="control-label">${text('建设项目名称')}：</label>
				<div class="control-inline">
					<#form:input path="likeEntryNameOrProjectName" maxlength="1000" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('建设类型')}：</label>
				<div class="control-inline" style="min-width: 120px;">
					<#form:select path="constructionType" dictType="ob_construction_type" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('主要使用单位')}：</label>
				<div class="control-inline">
					<#form:input path="mainUsedOffice.officeName" maxlength="1000" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('申请开始日期')}：</label>
				<div class="control-inline">
					<#form:input path="applicationDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('申请结束日期')}：</label>
				<div class="control-inline">
					<#form:input path="applicationDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			<% } %>
			<div class="form-group">
				<label class="control-label">${text('审批状态')}：</label>
				<div class="control-inline" style="min-width: 120px;">
					<#form:select path="status" dictType="bpm_biz_status" blankOption="true" class="form-control"/>
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>

	const actionColumn = {header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
		var actions = [];
		//# if(hasPermi('arrange::edit')){
		actions.push('<a href="${ctx}/arrange//form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑配置信息")}">编辑</a>&nbsp;');
		actions.push('<a href="${ctx}/arrange//delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除配置信息")}" data-confirm="${text("确认要删除该配置信息吗？")}">删除</a>&nbsp;');
		//# }
		if (row.status != Global.STATUS_DRAFT){
			actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=adjustment_application&bizKey='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("流程追踪")}" data-layer="true">流程</a>&nbsp;');
		}
		return actions.join('');
	}};
	let columnData = [];

	if (`${arrange.arrangeType}` === '1') {
		columnData = [
			{header:'${text("配置名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
					return '<a href="${ctx}/arrange//form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑配置信息")}">'+(val||row.id)+'</a>';
				}},
			{header:'${text("调剂申请单位")}', name:'organizationId', index:'a.organization_id', width:150, align:"left", formatter: function (val, obj, row, act) {
					return row.usedOffice?.officeName || '';
				}},
			{header:'${text("说明")}', name:'describe', index:'a.describe', width:150, align:"left"},
			{header:'${text("用途")}', name:'purpose', index:'a.purpose', width:150, align:"left"},
			{header:'${text("审批意见")}', name:'lastApprovalCommon', index:'a.lastApprovalCommon', width:150, align:"left"},
			{header:'${text("审批日期")}', name:'lastApprovalDate', index:'a.lastApprovalDate', width:150, align:"left"},
			{header:'${text("调剂通过日期")}', name:'approval_date', index:'a.approval_date', width:150, align:"center"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
			actionColumn
		]
	} else if (`${arrange.arrangeType}` === '2') {
		columnData = [
			{header:'${text("置换单号")}', name:'approvalCode', index:'a.id', width:150, align:"left"},
			{header:'${text("置换申请单位")}', name:'organizationId', index:'a.organization_id', width:150, align:"left", formatter: function (val, obj, row, act) {
					return row.usedOffice?.officeName || '';
				}},
			{header:'${text("置换原由")}', name:'describe', index:'a.describe', width:150, align:"left"},
			{header:'${text("审批意见")}', name:'lastApprovalCommon', index:'a.lastApprovalCommon', width:150, align:"left"},
			{header:'${text("审批日期")}', name:'lastApprovalDate', index:'a.lastApprovalDate', width:150, align:"left"},
			{header:'${text("配置方式")}', name:'way', index:'a.way', width:150, align:"left"},
			{header:'${text("现场调研结果")}', name:'researchResults', index:'a.researchResults', width:150, align:"left"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
			actionColumn
		]
	} else if (`${arrange.arrangeType}` === '3') {
		columnData = [
			{header:'${text("租用申请单号")}', name:'approvalCode', index:'a.id', width:150, align:"left"},
			{header:'${text("租用申请单位")}', name:'organizationId', index:'a.organization_id', width:150, align:"left", formatter: function (val, obj, row, act) {
					return row.usedOffice?.officeName || '';
				}},
			{header:'${text("租用原由")}', name:'reason', index:'a.reason', width:150, align:"left"},
			{header:'${text("租用开始时间")}', name:'rentalStartDate', index:'a.rentalStartDate', width:150, align:"left"},
			{header:'${text("租用开始时间")}', name:'rentalEndDate', index:'a.rentalEndDate', width:150, align:"left"},
			{header:'${text("审批意见")}', name:'lastApprovalCommon', index:'a.lastApprovalCommon', width:150, align:"left"},
			{header:'${text("审批日期")}', name:'lastApprovalDate', index:'a.lastApprovalDate', width:150, align:"left"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
			actionColumn
		]
	} else {
		columnData = [
			{header:'${text("建设项目名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
				let name = "";
				if (row.constructionType === '4') {
					name = row.projectName;
				} else {
					name = row.entryName;
				}
				return '<a href="${ctx}/arrange//form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑配置信息")}">'+(name||row.id)+'</a>';
			}},
			{header:'${text("建设类型")}', name:'constructionType', index:'a.constructionType', width:150, align:"left", formatter: function (val, obj, row, act) {
					return js.getDictLabel("#{@DictUtils.getDictListJson('ob_construction_type')}", val, '未知', true);
				}},
			{header:'${text("主要使用单位")}', name:'mainUsedOffice.officeName', index:'mainUsedOffice.officeName', width:150, align:"left", formatter: function (val, obj, row, act) {
					return row.mainUsedOffice?.officeName || '';
				}},
			{header:'${text("申请日期")}', name:'applicationDate', index:'a.applicationDate', width:150, align:"center"},
			{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '${text("未知")}', true);
				}},
			actionColumn
		]
	}
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: columnData,
		sortableColumn: false,
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>
<script>
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/arrange/exportListData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>