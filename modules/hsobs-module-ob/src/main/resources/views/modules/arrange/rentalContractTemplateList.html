<% layout('/layouts/default.html', {title: '租用合同模版管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租用合同模版管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('arrange:rentalContractTemplate:edit')){ %>
					<a href="${ctx}/arrange/rentalContractTemplate/form" class="btn btn-default btnTool" title="${text('新增租用合同模版')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${rentalContractTemplate}" action="${ctx}/arrange/rentalContractTemplate/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('合同名称')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同编号')}：</label>
					<div class="control-inline">
						<#form:input path="number" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('版本')}：</label>
					<div class="control-inline">
						<#form:input path="version" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("合同名称")}', name:'name', index:'a.name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/arrange/rentalContractTemplate/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租用合同模版")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("合同编号")}', name:'number', index:'a.number', width:150, align:"left"},
		{header:'${text("版本")}', name:'version', index:'a.version', width:150, align:"left"},
		{header:'${text("启用")}', name:'enable', index:'a.enable', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_yes_no')}", val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
				actions.push('<a href="${ctx}/arrange/rentalContractTemplate/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑租用合同模版")}">编辑</a>&nbsp;');
				if (row.enable == '0'){
					actions.push('<a href="${ctx}/arrange/rentalContractTemplate/enable?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("启用租用合同模版")}" data-confirm="${text("启用当前模版会禁用已启用模版，确认要启用该租用合同模版吗？")}">启用</a>&nbsp;');
					actions.push('<a href="${ctx}/arrange/rentalContractTemplate/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除租用合同模版")}" data-confirm="${text("确认要删除该租用合同模版吗？")}">删除</a>&nbsp;');
				}

			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>