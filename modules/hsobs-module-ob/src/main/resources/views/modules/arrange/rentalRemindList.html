<% layout('/layouts/default.html', {title: '租借提醒管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租借提醒管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('arrange:rentalRemind:edit')){ %>
					<a href="${ctx}/arrange/rentalRemind/form" class="btn btn-default btnTool" title="${text('新增租借提醒')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${rentalRemind}" action="${ctx}/arrange/rentalRemind/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('合同编号')}：</label>
					<div class="control-inline">
						<#form:input path="contractCode" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('承租单位')}：</label>
					<div class="control-inline width-120" >
						<#form:input path="rentalOffice.officeName" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同到期时间')}：</label>
					<div class="control-inline">
						<#form:input path="rentEndDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						-
						<#form:input path="rentEndDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="contractStatus" dictType="ob_contract_status" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("合同编号")}', name:'contractCode', index:'a.contract_code', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/arrange/rentalRemind/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租借提醒")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("承租单位")}', name:'rentalOffice.officeName', index:'rentalOffice.officeName', width:150, align:"center"},
		{header:'${text("房屋地址")}', name:'realEstateAddress', index:'a.real_estate_address', width:150, align:"left"},
		{header:'${text("合同到期时间")}', name:'rentEndDate', index:'a.rent_end_date', width:150, align:"center"},
		{header:'${text("合同状态")}', name:'contractStatus', index:'a.contract_status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_contract_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('arrange:rentalRemind:edit')){
				actions.push('<a href="${ctx}/arrange/rentalRemind/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑租借提醒")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/arrange/rentalRemind/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除租借提醒")}" data-confirm="${text("确认要删除该租借提醒吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	sortableColumn: false,
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script><script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/arrange/rentalRemind/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>