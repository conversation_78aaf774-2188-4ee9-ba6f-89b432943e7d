<% layout('/layouts/default.html', {title: '配置管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text('租借合同')}
			</div>
		</div>
		<#form:form id="inputForm" model="${rentInfoQuery}" action="${ctx}/rentInfoQuery//save" method="post" class="form-horizontal">
		<div class="box-body">
			<div class="form-unit">${text('基本信息')}</div>
			<#form:hidden path="id"/>
			<div class="col-xs-12">
				<div class="form-group">
					<label class="control-label col-sm-2" title="">
						<span class="required hide">*</span> ${text('租借合同')}：<i class="fa icon-question hide"></i></label>
					<div class="col-sm-10">
						<#form:fileupload id="arrange_contract_file" bizKey="${rentInfoQuery.id}" bizType="arrange_contract_file"
						uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
					</div>
				</div>
			</div>
		</div>
		<div class="box-footer">
			<div class="row">
				<div class="col-sm-offset-2 col-sm-10">
					<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
				</div>
			</div>
		</div>
		</#form:form>
	</div>
</div>
<% } %>