<% layout('/layouts/default.html', {title: '租用信息查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租用信息查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${arrange}" action="${ctx}/arrange//listDataRentInfoQuery" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="form-group">
				<label class="control-label">${text('申请单位')}：</label>
				<div class="control-inline">
					<#form:treeselect id="officeCode" title="${text('机构选择')}"
					path="officeCode" labelPath=""
					url="${ctx}/sys/office/treeData" allowClear="true"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('审批状态')}：</label>
				<div class="control-inline width-120">
					<#form:select readonly="false" blankOption="true" path="transferApprovalStatus" dictType="bpm_biz_status" class="form-control required width-120" />
				</div>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		columnModel: [
			{header:'${text("租借申请单位")}', name:'officeName', index:'a.officeName', width:150, align:"left"},
			{header:'${text("租借房屋档案")}', name:'roomInfo', index:'a.roomInfo', width:150, align:"left"},
			{header:'${text("申请日期")}', name:'transferApplyDate', index:'a.exchangeApplyDate', width:150, align:"left"},
			{header:'${text("租借开始时间")}', name:'rentStartDate', index:'a.rentStartDate', width:150, align:"left"},
			{header:'${text("租借结束时间")}', name:'rentEndDate', index:'a.rentEndDate', width:150, align:"left"},
			{header:'${text("审批状态")}', name:'transferApprovalStatus', index:'a.transferApprovalStatus', width:150, align:"left", formatter: function (val, obj, row, act) {
				return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_biz_status')}", val, '未知', true);
			}},
			{header:'${text("租借信息合同")}', name:'actions', width:150, formatter: function(val, obj, row, act){
				var actions = [];
				actions.push('<a href="${ctx}/arrange//arrangeFormContract?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("租借信息合同")}" >查看</a>&nbsp;');
				return actions.join('');
			}}
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
	$('#btnExport').click(function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/arrange/exportDataRentInfoQuery',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	});
</script>