<% layout('/layouts/default.html', {title: '配置管理' , libs: ['validate','fileupload','dataGrid']}){ %>
	<div class="main-content">
		<% if(!arrange.isNewRecord){ %>
			<div class="box box-main" style="margin-bottom: 10px;">
				<div class="box-header with-border">
					<div class="hide" style="display: flex;justify-content: space-between;">
						<div class="box-title">
						</div>
						<div class="box-tools pull-right ">
							<button type="button" style="width: 120px;" class="btn btn-sm btn-primary"
								data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
						</div>
					</div>
					<div class="row">
						<#common:viewport entity="${arrange}" title="配置管理" />
					</div>
				</div>
				<div class="box-body">
					<#common:steps entity="${arrange}" formKey="adjustment_application" />
				</div>
			</div>
			<% } %>
				<div class="box box-main">
					<div class="box-header with-border">
						<div class="box-title">
							<i class="fa icon-note"></i> ${text(arrange.isNewRecord ? '新增' : '编辑')}
						</div>
						<div class="box-tools pull-right hide">
							<button type="button" class="btn btn-box-tool" data-widget="collapse"><i
									class="fa fa-minus"></i></button>
						</div>
					</div>
					<#form:form id="inputForm" model="${arrange}" action="${ctx}/arrange//save" method="post"
						class="form-horizontal">
						<div class="box-body">
							<div class="form-unit">${text('基本信息')}</div>
							<#form:hidden path="id" />
							<div class="row">
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('配置名称')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="name" maxlength="1000" class="form-control required"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('配置类型')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:select readonly="true" path="arrangeType" dictType="ob_arrange_type"
												class="form-control required" />
										</div>
									</div>
								</div>

								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('面积')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:input path="area" dataFormat="number2" class="form-control required"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required hide">*</span> ${text('整体搬迁')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:select id="overallRelocation" path="overallRelocation"
												dictType="sys_no_yes" class="form-control"
												readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('申请单位')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:treeselect id="usedOfficeCode" title="${text('机构选择')}"
												path="usedOfficeCode" labelPath="usedOffice.officeName"
												callbackFuncName="listselectCallback" url="${ctx}/sys/office/treeData"
												class="required" allowClear="true" readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-sm-4" title="">
											<span class="required">*</span> ${text('申请人')}：<i
												class="fa icon-question hide"></i></label>
										<div class="col-sm-8">
											<#form:listselect id="usedUserCode" path="usedUserCode"
												labelPath="usedUser.userName" title="用户选择"
												url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
												class="form-control required" checkbox="false" itemCode="userCode"
												itemName="userName" readonly="${commonReadonly}" />
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="form-unit">${text('申请单位现状')}</div>
								<div class="col-xs-12">
									<div class="nav-tabs-custom">
										<ul class="nav nav-tabs" id="establishmentTabs">
											<li class="active"><a href="#centralTab" data-toggle="tab">中央机关</a></li>
											<li><a href="#provinceTab" data-toggle="tab">省级机关</a></li>
											<li><a href="#municipalTab" data-toggle="tab">市级机关</a></li>
											<li><a href="#countyTab" data-toggle="tab">县级机关</a></li>
											<li><a href="#townshipTab" data-toggle="tab">乡级机关</a></li>
										</ul>
										<div class="tab-content">
											<!-- 中央机关 -->
											<div class="tab-pane active" id="centralTab">
												<table class="table table-bordered">
													<thead>
														<tr>
															<th width="20%">${text("部级正职")}</th>
															<th width="20%">${text("部级副职")}</th>
															<th width="20%">${text("正司(局)级")}</th>
															<th width="20%">${text("副司(局)级")}</th>
															<th width="20%">${text("处级以下")}</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td>
																<#form:input name="ministerPositive"
																	path="ministerPositive" class="form-control digits"
																	defaultValue="0" />
															</td>
															<td>
																<#form:input name="ministerDeputy" path="ministerDeputy"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="departmentDirector"
																	path="departmentDirector"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="deputyDepartmentDirector"
																	path="deputyDepartmentDirector"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="belowDivisionLevel"
																	path="belowDivisionLevel"
																	class="form-control digits" defaultValue="0" />
															</td>
														</tr>
													</tbody>
												</table>
											</div>

											<!-- 省级机关 -->
											<div class="tab-pane" id="provinceTab">
												<table class="table table-bordered">
													<thead>
														<tr>
															<th width="15%">${text("省级正职")}</th>
															<th width="15%">${text("省级副职")}</th>
															<th width="14%">${text("正厅(局)级")}</th>
															<th width="14%">${text("副厅(局)级")}</th>
															<th width="14%">${text("正处级")}</th>
															<th width="14%">${text("副处级")}</th>
															<th width="14%">${text("处级以下")}</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td>
																<#form:input name="provincePositive"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="provinceDeputy"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="bureauDirector"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="deputyBureauDirector"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="divisionChief"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="deputyDivisionChief"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="belowDivisionChief"
																	class="form-control digits" defaultValue="0" />
															</td>
														</tr>
													</tbody>
												</table>
											</div>

											<!-- 市级机关 -->
											<div class="tab-pane" id="municipalTab">
												<table class="table table-bordered">
													<thead>
														<tr>
															<th width="20%">${text("市级正职")}</th>
															<th width="20%">${text("市级副职")}</th>
															<th width="20%">${text("正局(处)级")}</th>
															<th width="20%">${text("副局(处)级")}</th>
															<th width="20%">${text("局(处)级以下")}</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td>
																<#form:input name="municipalPositive"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="municipalDeputy"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="municipalBureauDirector"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="municipalDeputyBureauDirector"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="belowMunicipalBureau"
																	class="form-control digits" defaultValue="0" />
															</td>
														</tr>
													</tbody>
												</table>
											</div>

											<!-- 县级机关 -->
											<div class="tab-pane" id="countyTab">
												<table class="table table-bordered">
													<thead>
														<tr>
															<th width="20%">${text("县级正职")}</th>
															<th width="20%">${text("县级副职")}</th>
															<th width="20%">${text("正科级")}</th>
															<th width="20%">${text("副科级")}</th>
															<th width="20%">${text("科级以下")}</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td>
																<#form:input name="countyPositive"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="countyDeputy"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="sectionChief"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="deputySectionChief"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="belowSectionLevel"
																	class="form-control digits" defaultValue="0" />
															</td>
														</tr>
													</tbody>
												</table>
											</div>

											<!-- 乡级机关 -->
											<div class="tab-pane" id="townshipTab">
												<table class="table table-bordered">
													<thead>
														<tr>
															<th width="33%">${text("乡级正职")}</th>
															<th width="33%">${text("乡级副职")}</th>
															<th width="34%">${text("乡级以下")}</th>
														</tr>
													</thead>
													<tbody>
														<tr>
															<td>
																<#form:input name="townshipPositive"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="townshipDeputy"
																	class="form-control digits" defaultValue="0" />
															</td>
															<td>
																<#form:input name="belowTownshipLevel"
																	class="form-control digits" defaultValue="0" />
															</td>
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>

								<div class="col-xs-12">
									<div class="panel panel-default">
										<div class="panel-heading">${text('用房现状')}</div>
										<div class="panel-body">
											<table class="table table-bordered">
												<thead>
													<tr>
														<th width="20%">${text("办公室")}</th>
														<th width="20%">${text("服务用房")}</th>
														<th width="20%">${text("设备用房")}</th>
														<th width="20%">${text("附属用房")}</th>
														<th width="20%">${text("技术业务用房")}</th>
													</tr>
												</thead>
												<tbody>
													<tr>
														<td>
															<#form:input name="realEstateType_0"
																class="form-control digits" defaultValue="0" />
														</td>
														<td>
															<#form:input name="realEstateType_1"
																class="form-control digits" defaultValue="0" />
														</td>
														<td>
															<#form:input name="realEstateType_2"
																class="form-control digits" defaultValue="0" />
														</td>
														<td>
															<#form:input name="realEstateType_3"
																class="form-control digits" defaultValue="0" />
														</td>
														<td>
															<#form:input name="realEstateType_4"
																class="form-control digits" defaultValue="0" />
														</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
							<% if (arrange.arrangeType=='1' ){ %>
								<div class="form-unit">${text('调剂信息')}</div>
								<div class="row">
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required">*</span> ${text('调剂时间')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<% if (commonReadonly) { %>
													<#form:input path="regulateDate" maxlength="20" class="form-control"
														dataFormat="datetime" defaultValue="${date()}"
														readonly="true" />
													<% } else { %>
														<#form:input path="regulateDate" maxlength="20"
															class="form-control laydate required" dataFormat="datetime"
															data-type="datetime" data-format="yyyy-MM-dd HH:mm"
															defaultValue="${date()}" />
														<% } %>

											</div>
										</div>
									</div>
									<div class="col-xs-6">
										<div class="form-group">
											<label class="control-label col-sm-4" title="">
												<span class="required">*</span> ${text('用途')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-8">
												<#form:input path="purpose" maxlength="1000"
													class="form-control required" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2" title="">
												<span class="required">*</span> ${text('理由')}：<i
													class="fa icon-question hide"></i></label>
											<div class="col-sm-10">
												<#form:textarea path="reason" rows="4" maxlength="1000"
													class="form-control required" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
								</div>
								<div class="form-unit">${text('相关文件')}</div>
								<div class="row">
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2">
												<span class="required">*</span> ${text('产权证')}：</label>
											<div class="col-sm-10">
												<#form:fileupload id="arrange_certificate_file" bizKey="${arrange.id}"
													bizType="arrange_certificate_file" uploadType="all" class="required"
													preview="true" dataMap="true" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2">
												<span class="required">*</span> ${text('楼层平面图')}：</label>
											<div class="col-sm-10">
												<#form:fileupload id="arrange_floor_file" bizKey="${arrange.id}"
													bizType="arrange_floor_file" uploadType="all" class="required"
													preview="true" dataMap="true" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2">
												<span class="required">*</span> ${text('编制人数依据文件')}：</label>
											<div class="col-sm-10">
												<#form:fileupload id="arrange_person_file" bizKey="${arrange.id}"
													bizType="arrange_person_file" uploadType="all" class="required"
													preview="true" dataMap="true" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2">
												<span class="required">*</span> ${text('申请调配函')}：</label>
											<div class="col-sm-10">
												<#form:fileupload id="arrange_allocation_file" bizKey="${arrange.id}"
													bizType="arrange_allocation_file" uploadType="all" class="required"
													preview="true" dataMap="true" readonly="${commonReadonly}" />
											</div>
										</div>
									</div>
									<div class="col-xs-12">
										<div class="form-group">
											<label class="control-label col-sm-2">
												<span class="required">*</span> ${text('省委省政府意见')}：
											</label>
											<div class="col-sm-10">
												<#form:fileupload id="arrange_provincial_reason_file"
													bizKey="${arrange.id}" bizType="arrange_provincial_reason_file"
													uploadType="all"
													class="${currentTaskName != '省委省政府'? '':'required'}" preview="true"
													dataMap="true" readonly="${currentTaskName != '省委省政府'}" />
											</div>
										</div>
									</div>
								</div>
								<% } %>
									<% if (arrange.arrangeType=='2' ){ %>
										<div class="form-unit">${text('置换信息')}</div>

										<div class="row">
											<div class="col-xs-6">
												<div class="form-group">
													<label class="control-label col-sm-4" title="">
														<span class="required">*</span> ${text('置换日期')}：<i
															class="fa icon-question hide"></i></label>
													<div class="col-sm-8">
														<% if (commonReadonly) { %>
															<#form:input path="regulateDate" maxlength="20"
																class="form-control" dataFormat="datetime"
																defaultValue="${date()}" readonly="true" />
															<% } else { %>
																<#form:input path="regulateDate" maxlength="20"
																	class="form-control laydate required"
																	dataFormat="datetime" data-type="datetime"
																	data-format="yyyy-MM-dd HH:mm"
																	defaultValue="${date()}" />
																<% } %>
													</div>
												</div>
											</div>
											<div class="col-xs-12">
												<div class="form-group">
													<label class="control-label col-sm-2" title="">
														<span class="required">*</span> ${text('置换原由')}：<i
															class="fa icon-question hide"></i></label>
													<div class="col-sm-10">
														<#form:textarea path="reason" rows="4" maxlength="1000"
															class="form-control required"
															readonly="${commonReadonly}" />
													</div>
												</div>
											</div>
											<div class="col-xs-6">
												<div class="form-group">
													<label class="control-label col-sm-4" title="">
														<span class="required hide">*</span> ${text('配置方式')}：<i
															class="fa icon-question hide"></i></label>
													<div class="col-sm-8">
														<#form:input path="way" maxlength="1000"
															class="form-control required"
															readonly="${commonReadonly}" />
													</div>
												</div>
											</div>
											<div class="col-xs-12">
												<div class="form-group">
													<label class="control-label col-sm-2" title="">
														<span class="required hide">*</span> ${text('现场调研结果')}：<i
															class="fa icon-question hide"></i></label>
													<div class="col-sm-10">
														<#form:textarea path="researchResults" rows="4" maxlength="1000"
															class="form-control required"
															readonly="${commonReadonly}" />
													</div>
												</div>
											</div>
											<div class="col-xs-12">
												<div class="form-group">
													<label class="control-label col-sm-2" title="如置换申请函、调研报告等">
														<span class="required hide">*</span> ${text('相关文件')}：<i
															class="fa icon-question hide"></i></label>
													<div class="col-sm-10">
														<p class="text-muted">
														<h5><small>如置换申请函、调研报告等</small></h5>
														</p>
														<#form:fileupload id="uploadFile" bizKey="${arrange.id}"
															bizType="arrange_file" uploadType="all" class=""
															readonly="false" preview="true" dataMap="true"
															readonly="${commonReadonly}" />
													</div>
												</div>
											</div>
										</div>
										<% } %>
											<% if (arrange.arrangeType=='3' ){ %>
												<div class="form-unit">${text('租用信息')}</div>
												<div class="row">
													<div class="col-xs-6">
														<div class="form-group">
															<label class="control-label col-sm-4" title="">
																<span class="required">*</span> ${text('租金')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-8">
																<#form:input path="budget" dataFormat="number2"
																	class="form-control required"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-6">
														<div class="form-group">
															<label class="control-label col-sm-4" title="">
																<span class="required">*</span> ${text('出租人')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-8">
																<#form:input path="lessor" maxlength="1000"
																	class="form-control required"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2" title="">
																<span class="required">*</span> ${text('租用地址')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-10">
																<#form:input path="rentalAddress" maxlength="1000"
																	class="form-control required"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-6">
														<div class="form-group">
															<label class="control-label col-sm-4" title="">
																<span class="required">*</span> ${text('租用开始时间')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-8">
																<#form:input path="rentalStartDate" maxlength="20"
																	class="form-control laydate required"
																	dataFormat="datetime" data-type="datetime"
																	data-format="yyyy-MM-dd HH:mm"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-6">
														<div class="form-group">
															<label class="control-label col-sm-4" title="">
																<span class="required">*</span> ${text('租用结束时间')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-8">
																<#form:input path="rentalEndDate" maxlength="20"
																	class="form-control laydate required"
																	dataFormat="datetime" data-type="datetime"
																	data-format="yyyy-MM-dd HH:mm"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2" title="">
																<span class="required">*</span> ${text('租用原由')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-10">
																<#form:textarea path="reason" rows="4" maxlength="1000"
																	class="form-control required"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2" title="">
																<span class="required">*</span> ${text('办公现状')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-10">
																<#form:textarea path="currentOfficeSituation" rows="4"
																	maxlength="1000" class="form-control required"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2" title="">
																<span class="required hide">*</span> ${text('租借合同')}：<i
																	class="fa icon-question hide"></i></label>
															<div class="col-sm-10">
																<#form:fileupload id="arrange_contract_file"
																	bizKey="${arrange.id}"
																	bizType="arrange_contract_file" uploadType="all"
																	class="" readonly="false" preview="true"
																	dataMap="true" readonly="${commonReadonly}" />

																<% if (arrange.status=='0' ){ %>
																	<a id="genContractBtn" onclick="genContract()"
																		href="javascript:;"
																		class="btnList btn btn-link info btn-xs"
																		title="${text(" 生成租借合同")}"
																		data-confirm="${text(" 确认要生成租借合同吗？")}">合同生成</a>
																	<% } %>

															</div>
														</div>
													</div>
													<div class="col-xs-12">
														<div class="form-group">
															<label class="control-label col-sm-2" title="如租用申请函、调研报告等">
																<span class="required">*</span> ${text('相关文件')}：<i
																	class="fa icon-question"></i></label>
															<div class="col-sm-10">
																<#form:fileupload id="arrange_file"
																	bizKey="${arrange.id}" bizType="arrange_file"
																	uploadType="all" class="required" readonly="false"
																	preview="true" dataMap="true"
																	readonly="${commonReadonly}" />
															</div>
														</div>
													</div>
												</div>
												<% } %>
													<% if (arrange.arrangeType=='4' ){ %>
														<div class="form-unit">${text('建设信息')}</div>
														<div class="row">
															<div class="col-xs-6">
																<div class="form-group">
																	<label class="control-label col-sm-4" title="">
																		<span class="required">*</span>
																		${text('建设类型')}：<i
																			class="fa icon-question hide"></i></label>
																	<div class="col-sm-8">
																		<#form:select path="constructionType"
																			dictType="ob_construction_type"
																			class="form-control required"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
															<div class="col-xs-6">
																<div class="form-group">
																	<label class="control-label col-sm-4" title="">
																		<span class="required">*</span>
																		${text('主要使用单位')}：<i
																			class="fa icon-question hide"></i></label>
																	<div class="col-sm-8">
																		<#form:treeselect id="mainUsedOfficeCode"
																			title="${text('机构选择')}"
																			path="mainUsedOfficeCode"
																			labelPath="mainUsedOffice.officeName"
																			url="${ctx}/sys/office/treeData"
																			class="required" allowClear="true"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
														</div>
														<div class="row">
															<div class="col-xs-6">
																<div class="form-group">
																	<label class="control-label col-sm-4" title="">
																		<span class="required">*</span>
																		${text('地址坐落')}：<i
																			class="fa icon-question hide"></i></label>
																	<div class="col-sm-8">
																		<#form:listselect id="realEstateAddressId"
																			title="地址选择" class="form-control required"
																			path="realEstateAddressId"
																			labelPath="realEstateAddress.name"
																			url="${ctx}/estate/realEstateAddress/realEstateAddressSelect"
																			allowClear="false"
																			callbackFuncName="realEstateAddressListselectCallback"
																			checkbox="false" itemCode="id"
																			itemName="name"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
														</div>
														<div class="row">

															<div class="col-xs-12">
																<div class="panel panel-default">
																	<div class="panel-heading">${text('房屋信息')}</div>
																	<div class="panel-body">
																		<table class="table table-bordered">
																			<thead>
																				<tr>
																					<th width="20%">${text("房屋结构")}</th>
																					<th width="20%">${text("总建筑面积")}
																					</th>
																					<th width="20%">${text("地上建筑面积")}
																					</th>
																					<th width="20%">${text("地下建筑面积")}
																					</th>
																				</tr>
																			</thead>
																			<tbody>
																				<tr>
																					<td>
																						<#form:select
																							path="realEstateAddress.structure"
																							dictType="ob_structure_type"
																							class="form-control"
																							readonly="true" />
																					</td>
																					<td>
																						<#form:input
																							path="realEstateAddress.landArea"
																							class="form-control digits"
																							defaultValue="0" />
																					</td>
																					<td>
																						<#form:input
																							path="realEstateAddress.buildingOccupationArea"
																							class="form-control digits"
																							defaultValue="0" />
																					</td>
																					<td>
																						<#form:input
																							path="realEstateAddress.buildingArea"
																							class="form-control digits"
																							defaultValue="0" />
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</div>
																</div>
															</div>
														</div>
														<div class="row">
															<!-- 新扩改建设字段组 -->
															<div class="construction-new-expand-modify">
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('工程名称')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<div class="input-group">
																				<#form:input path="entryName"
																					maxlength="1000"
																					class="form-control required"
																					readonly="${commonReadonly}" />
																				<span class="input-group-btn">
																					<button type="button"
																						class="btn btn-primary"
																						id="btnGetProjectInfo">
																						<i class="fa fa-search"></i>
																						获取信息
																					</button>
																				</span>
																			</div>
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('工程预算')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="entryBudget"
																				dataFormat="number2"
																				class="form-control required"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('施工许可证号')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="constructionPermitNumber"
																				maxlength="1000"
																				class="form-control required"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('开工日期')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="commencementDate"
																				maxlength="20"
																				class="form-control laydate required"
																				dataFormat="datetime"
																				data-type="datetime"
																				data-format="yyyy-MM-dd HH:mm"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('完工日期')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="completionDate"
																				maxlength="20"
																				class="form-control laydate required"
																				dataFormat="datetime"
																				data-type="datetime"
																				data-format="yyyy-MM-dd HH:mm"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('决算日期')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="finalSettlementDate"
																				maxlength="20"
																				class="form-control laydate required"
																				dataFormat="datetime"
																				data-type="datetime"
																				data-format="yyyy-MM-dd HH:mm"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('决算金额')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="finalSettlementBudget"
																				dataFormat="number2"
																				class="form-control required"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('施工单位名称')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="constructionUnitName"
																				maxlength="1000"
																				class="form-control required"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('监理单位名称')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="supervisionUnitName"
																				maxlength="1000"
																				class="form-control required"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
															</div>
															<!-- 购置建设字段组 -->
															<div class="construction-purchase">
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('项目名称')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="projectName"
																				maxlength="1000"
																				class="form-control required"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required">*</span>
																			${text('项目预算')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:input path="budget"
																				dataFormat="number2"
																				class="form-control required"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-12">
																	<div class="form-group">
																		<label class="control-label col-sm-2" title="">
																			<span class="required">*</span>
																			${text('房屋信息')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-10">
																			<#form:textarea path="housingInformation"
																				rows="4" maxlength="1000"
																				class="form-control required"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
															</div>
															<!-- 公共字段 -->
															<div class="col-xs-12">
																<div class="form-group">
																	<label class="control-label col-sm-2" title="">
																		<span class="required">*</span>
																		${text('省委省政府意见')}：<i
																			class="fa icon-question hide"></i></label>
																	<div class="col-sm-10">
																		<#form:textarea path="provinceOpinion" rows="4"
																			maxlength="1000"
																			class="form-control ${currentTaskName!='省委省政府'?'':'required'}"
																			readonly="${currentTaskName!='省委省政府'}" />
																	</div>
																</div>
															</div>
															<div class="col-xs-12">
																<div class="form-group">
																	<label class="control-label col-sm-2"
																		title="如项目文档、审批文件等">
																		<span class="required">*</span>
																		${text('相关文件')}：<i class="fa icon-question"
																			title="如项目文档、审批文件等"></i></label>
																	<div class="col-sm-10">
																		<#form:fileupload id="arrange_file"
																			bizKey="${arrange.id}"
																			bizType="arrange_file" uploadType="all"
																			class="required" readonly="false"
																			preview="true" dataMap="true"
																			readonly="${commonReadonly}" />
																	</div>
																</div>
															</div>
														</div>
														<% } %>
															<div class="form-unit">${text('其他信息')}</div>
															<div class="row">
																<!--				<div class="col-xs-12">-->
																<!--					<div class="form-group">-->
																<!--						<label class="control-label col-sm-2" title="">-->
																<!--							<span class="required hide">*</span> ${text('省委省政府意见')}：<i class="fa icon-question hide"></i></label>-->
																<!--						<div class="col-sm-10">-->
																<!--							<#form:textarea path="provinceOpinion" rows="4" maxlength="1000" class="form-control"/>-->
																<!--						</div>-->
																<!--					</div>-->
																<!--				</div>-->
																<div class="col-xs-12">
																	<div class="form-group">
																		<label class="control-label col-sm-2" title="">
																			<span class="required hide">*</span>
																			${text('说明')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-10">
																			<#form:textarea path="describe" rows="4"
																				maxlength="1000" class="form-control"
																				readonly="${commonReadonly}" />
																		</div>
																	</div>
																</div>
															</div>


															<div class="form-unit">${text('不动产信息')}</div>

															<div class="row">
																<div class="col-xs-6">
																	<div class="form-group">
																		<label class="control-label col-sm-4" title="">
																			<span class="required hide">*</span>
																			${text('不动产类型')}：<i
																				class="fa icon-question hide"></i></label>
																		<div class="col-sm-8">
																			<#form:select id="realEstateType"
																				path="realEstateType"
																				dictType="ob_real_estate_type"
																				class="form-control"
																				readonly="${currentTaskName != '办公用房调配意见'}" />
																		</div>
																	</div>
																</div>
																<div class="col-xs-12">
																	<% if (currentTaskName=='办公用房调配意见' ) { %>
																		<div class="form-unit-wrap table-form"
																			id="realEstateRoomWrapper">
																			<#form:btnlistselect
																				id="arrangeRealEstateDataGridAddRowListselectBtn"
																				title="关联房间" allowClear="true"
																				btnLabel="选择房间"
																				setSelectDataFuncName="realEstateBtnListselectSetSelectData"
																				url="${ctx}/estate/realEstate/realEstateSelect"
																				allowClear="false" checkbox="true"
																				itemCode="id" itemName="name" />
																		</div>
																		<div class="form-unit-wrap table-form"
																			id="realEstateHouseWrapper"
																			style="display:none;">
																			<#form:btnlistselect
																				id="arrangeRealEstateAddressDataGridAddRowListselectBtn"
																				title="关联房屋" allowClear="true"
																				btnLabel="选择房屋"
																				setSelectDataFuncName="realEstateAddressBtnListselectSetSelectData"
																				url="${ctx}/estate/realEstateAddress/realEstateAddressSelect"
																				allowClear="false" checkbox="true"
																				itemCode="id" itemName="name" />
																		</div>
																		<% } %>
																			<div class="form-unit-wrap table-form">
																				<table id="realEstateDataGrid"></table>
																			</div>
																</div>
															</div>


															<% if (!arrange.isNewRecord && arrange.status !='9' ){ %>
																<div class="form-unit">${text('审批信息')}</div>
																<div class="row taskComment hide">
																	<div class="col-xs-12">
																		<div class="form-group">
																			<label
																				class="control-label col-xs-2">审批意见：</label>
																			<div class="col-xs-10">
																				<#bpm:comment bpmEntity="${arrange}"
																					showCommWords="true" />
																			</div>
																		</div>
																	</div>
																</div>
																<% } %>
																	<#bpm:nextTaskInfo bpmEntity="${arrange}" />
						</div>
						<div class="box-footer">
							<div class="row">
								<div class="col-sm-offset-2 col-sm-10">
									<% if (hasPermi('arrange::edit')){ %>
										<#form:hidden path="status" />
										<% if (arrange.isNewRecord || arrange.status=='9' ){ %>
											<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i
													class="fa fa-save"></i> 暂 存</button>&nbsp;
											<% } %>
												<#bpm:button bpmEntity="${arrange}" formKey="adjustment_application"
													completeText="提 交" />
												<% } %>
													<button type="button" class="btn btn-sm btn-default" id="btnCancel"
														onclick="js.closeCurrentTabPage()"><i
															class="fa fa-reply-all"></i> ${text('关 闭')}</button>
								</div>
							</div>
						</div>
					</#form:form>
				</div>
	</div>
	<% } %>
		<script>
			function genContract() {
				js.ajaxSubmit(ctx + '/arrange//genContract', {
					id: `${arrange.id}`,
				}, function (data) {
					js.showMessage(data.message);
				});
			}
		</script>
		<script>
			function realEstateAddressListselectCallback(id, act, index, layero, selectData) {
				if (id == 'realEstateAddressId' && act == 'ok') {
					$.each(selectData, (key, value) => {
						// 房屋结构赋值（select下拉框）
						$('select[name="realEstateAddress.structure"]').val(value.structure || '').trigger('change');

						// 总建筑面积赋值（input数字框）
						$('input[name="realEstateAddress.landArea"]').val(value.landArea || '0');

						// 地上建筑面积赋值（input数字框）
						$('input[name="realEstateAddress.buildingOccupationArea"]').val(value.buildingOccupationArea || '0');

						// 地下建筑面积赋值（input数字框）
						$('input[name="realEstateAddress.buildingArea"]').val(value.buildingArea || '0');
					});
				}
			}
		</script>
		<script>
			function updateEstablishmentTabs(officeTypeCode) {
				let tabMap = {
					'1': 'centralTab',     // 中央机关
					'2': 'provinceTab',   // 省级机关
					'3': 'municipalTab',  // 市级机关
					'4': 'countyTab',     // 县级机关
					'5': 'townshipTab'    // 乡级机关
				};

				// 隐藏所有标签页和标签头
				$('#establishmentTabs li').hide();
				$('.tab-content .tab-pane').hide();

				// 显示对应的标签页和标签头
				let defaultTab = tabMap[officeTypeCode] || 'centralTab';
				$('#establishmentTabs a[href="#' + defaultTab + '"]').parent().show();
				$('#' + defaultTab).show();

				// 激活对应的标签页
				$('#establishmentTabs a[href="#' + defaultTab + '"]').tab('show');
			}

			const reloadOfficeUsedRealEstateCount = (officeCode) => {
				js.ajaxSubmit(ctx + '/sys/office/getOffice', {
					officeCode: officeCode,
				}, function (data) {
					updateEstablishmentTabs(data.officeType);
					if (data.officeEstablishment) {
						Object.keys(data.officeEstablishment).forEach(key => {
							const input = $(`input[name="` + key + `"]:not([type='hidden'])`);
							if (input.length > 0) {
								input.val(data.officeEstablishment[key] || '0');
							}
						});
					}

					// 处理用房现状数据
					if (data.usedRealEstateCount && data.usedRealEstateCount.length > 0) {
						// 如果有数据则正常填充
						data.usedRealEstateCount.forEach(item => {
							const input = $(`input[name="realEstateType_` + item.type + `"]`);
							if (input.length > 0) {
								input.val(item.count || '0');
							}
						})
					} else {
						// 如果无数据则全部置零
						for (let i = 0; i <= 4; i++) {
							const input = $(`input[name="realEstateType_` + i + `"]`);
							if (input.length > 0) {
								input.val('0');
							}
						}
					}
				});
			}

			function listselectCallback(id, act, index, layero, nodes) {
				if (id === 'usedOfficeCode' && act === 'ok') {
					reloadOfficeUsedRealEstateCount(nodes[0].code);
				}
			}

			// 初始化时根据当前单位类型显示对应的标签页
			if (`${arrange.usedOfficeCode}`) {
				reloadOfficeUsedRealEstateCount(`${arrange.usedOfficeCode}`);
			} else {
				// 如果没有单位，默认显示中央机关并隐藏其他标签
				updateEstablishmentTabs('1');
			}
		</script>
		<script>
			$(function () {

				// 处理建设类型变化
				function updateConstructionFields() {
					var constructionType = $('#constructionType').val();
					var isNewExpandModify = ['1', '2', '3'].includes(constructionType);
					var isPurchase = constructionType === '4';

					// 切换字段组显示并管理必填校验
					manageRequired('.construction-new-expand-modify', isNewExpandModify);
					manageRequired('.construction-purchase', isPurchase);
				}

				// 统一管理必填校验（新增函数）
				function manageRequired(selector, isRequired) {
					var $group = $(selector);
					$group.find(':input').each(function () {
						var $input = $(this);
						if (isRequired) {
							$input.addClass('required').rules('add', { required: true });
						} else {
							$input.removeClass('required').rules('remove', 'required');
						}
					});
					$group.toggle(isRequired); // 显示/隐藏字段组
				}

				// 初始化显示并设置校验规则
				updateConstructionFields();
				// 监听建设类型变化
				$('#constructionType').change(updateConstructionFields);
			});
		</script>
		<script>
			$(function () {

				var gridInitialized = false;
				// Listen for changes on the real estate type select field
				$('#realEstateType').change(function () {
					var selectedType = $(this).val(); // Get the selected real estate type


					if (!gridInitialized) {
						gridInitialized = true;  // Set the flag after the first load
					} else {
						$('#realEstateDataGrid').jqGrid('clearGridData');
					}
					if (selectedType == '2') {  // Replace 'someRoomType' with the type for rooms
						$('#realEstateRoomWrapper').show();  // Show room selection
						$('#realEstateHouseWrapper').hide(); // Hide house selection
					} else if (selectedType == '1') {  // Replace 'someHouseType' with the type for houses
						$('#realEstateRoomWrapper').hide();  // Hide room selection
						$('#realEstateHouseWrapper').show(); // Show house selection
					} else {
						$('#realEstateRoomWrapper').hide();
						$('#realEstateHouseWrapper').hide(); // Default, hide both if no specific type is selected
					}
				});

				$('#realEstateType').trigger('change');
			});
		</script>
		<script>
			function realEstateBtnListselectSetSelectData(id, selectData) {
				if (id == 'arrangeRealEstateDataGridAddRowListselectBtn') {
					if ($.isEmptyObject(selectData)) {
						return;
					}
					let gridData = $('#realEstateDataGrid').dataGrid('getRowData').map(item => item.realEstateId);
					$.each(selectData, (key, value) => {
						let realEstateId = key.toString();
						if (gridData.includes(realEstateId)) {
							return true;
						}
						let realEstateName = value['name'];
						let realEstateAddressName = value?.realEstateAddress?.name ?? '';
						$('#realEstateDataGrid').jqGrid('addRow', {
							position: 'first',
							addRowParams: { keys: false, focusField: true },
							initdata: {
								arrangeId: { id: '' },
								realEstateId: realEstateId,
								realEstate: { name: realEstateName, realEstateAddressName: realEstateAddressName },
								realEstateAddressName: realEstateAddressName,
								status: Global.STATUS_NORMAL
							}
						});
						gridData.push(realEstateId);
					});
				}
			}
			function realEstateAddressBtnListselectSetSelectData(id, selectData) {
				if (id == 'arrangeRealEstateAddressDataGridAddRowListselectBtn') {
					if ($.isEmptyObject(selectData)) {
						return;
					}
					let gridData = $('#realEstateDataGrid').dataGrid('getRowData').map(item => item.realEstateId);
					$.each(selectData, (key, value) => {
						let realEstateId = key.toString();
						if (gridData.includes(realEstateId)) {
							return true;
						}
						let realEstateName = value['name'];
						$('#realEstateDataGrid').jqGrid('addRow', {
							position: 'first',
							addRowParams: { keys: false, focusField: true },
							initdata: {
								arrangeId: { id: '' },
								realEstateId: realEstateId,
								realEstate: { name: realEstateName },
								status: Global.STATUS_NORMAL
							}
						});
						gridData.push(realEstateId);
					});
				}
			}
		</script>
		<script>
			//# // 初始化权属登记关联不动产表DataGrid对象
			$('#realEstateDataGrid').dataGrid({
				data: "#{toJson(arrange.arrangeRealEstateList)}",
				datatype: 'local', // 设置本地数据
				autoGridHeight: function () { return 'auto' }, // 设置自动高度

				//# // 设置数据表格列
				columnModel: [
					{ header: '状态', name: 'status', editable: false, hidden: true },
					{ header: '主键', name: 'id', editable: true, hidden: true },
					{ header: '${text("arrange_id")}', name: 'arrangeId.id', editable: true, hidden: true },
					{ header: '${text("real_estate_id")}', name: 'realEstateId', width: 150, editable: true, hidden: true },
					{
						header: '名称', name: 'realEstate.name', editable: false, width: 80, align: 'center', formatter: function (val, obj, row, act) {
							return row.realEstate?.name || row.realEstateAddress?.name || '';
						}
					},
					{
						header: '${text("操作")}', name: 'actions', width: 80, align: 'center', formatter: function (val, obj, row, act) {
							var actions = [];
							actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#realEstateDataGrid\').dataGrid(\'delRowData\',\'' + obj.rowId + '\')});return false;">删除</a>&nbsp;');
							return actions.join('');
						}, editoptions: { defaultValue: 'new' }
					}
				],

				//# // 编辑表格参数
				editGrid: true,				// 是否是编辑表格
				editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
				editGridInitAllRowEdit: true,  // 是否初始化就编辑所有行（*** 重点 ***）
				editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
				editGridAddRowInitData: { id: '', arrangeId: { id: '' }, realEstateId: '' },	// 新增行的时候初始化的数据

				//# // 编辑表格的提交数据参数
				editGridInputFormListName: 'arrangeRealEstateList', // 提交的数据列表名
				editGridInputFormListAttrs: 'id,arrangeId.id,realEstateId,createBy,createDate,updateBy,updateDate,', // 提交数据列表的属性字段

				//# // 加载成功后执行事件
				ajaxSuccess: function (data) {
				}
			});
		</script>
		<script>
			// 业务实现草稿按钮
			$('#btnDraft').click(function () {
				$('#status').val(Global.STATUS_DRAFT);
			});
			// 流程按钮操作事件
			BpmButton = window.BpmButton || {};
			BpmButton.init = function (task) {
				if (task.status != '2') {
					$('.taskComment').removeClass('hide');
				}
			}
			BpmButton.complete = function ($this, task) {
				$('#status').val(Global.STATUS_AUDIT);
			};
			// 表单验证提交事件
			$('#inputForm').validate({
				ignore: "", // 强制校验隐藏字段（需配合动态规则管理）
				submitHandler: function (form) {
					js.ajaxSubmitForm($(form), function (data) {
						js.showMessage(data.message);
						if (data.result == Global.TRUE) {
							js.closeCurrentTabPage(function (contentWindow) {
								contentWindow.page();
							});
						}
					}, "json");
				}
			});
		</script>

		<!-- 添加项目信息模态对话框 -->
		<div class="modal fade" id="projectInfoModal" tabindex="-1" role="dialog">
			<div class="modal-dialog modal-lg" role="document" style="width: 90%; max-width: 1200px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
						<h4 class="modal-title">选择项目信息</h4>
					</div>
					<div class="modal-body">
						<div class="table-responsive">
							<table id="projectInfoTable" class="table table-striped table-bordered table-hover">
								<thead>
									<tr>
										<th width="40px" class="text-center">选择</th>
										<th width="20%">工程名称</th>
										<th width="10%">工程预算<br>(万元)</th>
										<th width="15%">主要使用单位</th>
										<th width="10%">建设类型</th>
										<th width="25%">建设地点</th>
										<th width="10%">建筑面积<br>(㎡)</th>
									</tr>
								</thead>
								<tbody>
									<!-- 数据将通过JavaScript动态填充 -->
								</tbody>
							</table>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
						<button type="button" class="btn btn-primary" id="btnSelectProject">确定</button>
					</div>
				</div>
			</div>
		</div>

		<style>
			#projectInfoModal .modal-body {
				padding: 15px;
				max-height: calc(100vh - 200px);
				overflow-y: auto;
			}

			#projectInfoTable th {
				white-space: nowrap;
				background-color: #f5f5f5;
				vertical-align: middle;
			}

			#projectInfoTable td {
				vertical-align: middle;
			}

			#projectInfoTable input[type="radio"] {
				margin: 0;
				vertical-align: middle;
			}

			.table-responsive {
				border: none;
				margin-bottom: 0;
			}
		</style>

		<script>
			// 模拟的项目信息数据
			var mockProjectData = [
				{
					entryName: "福州市机关事务管理局办公大楼新建工程",
					entryBudget: 5000.00,
					mainUsedOfficeCode: "350100001",
					mainUsedOfficeName: "福州市机关事务管理局",
					constructionType: "1", // 新建
					location: "福州市鼓楼区五四路123号",
					buildingArea: 15000.00,
					content: "根据《机关事务管理条例》等相关规定，拟在福州市鼓楼区五四路新建机关办公大楼一栋，建筑面积15000平方米，总投资5000万元。项目已完成可行性研究报告编制和立项审批。",
					attachments: [
						{ name: "可行性研究报告.pdf", url: "#" },
						{ name: "立项批复.pdf", url: "#" },
						{ name: "规划设计方案.pdf", url: "#" }
					]
				},
				{
					entryName: "福州市发改委办公楼扩建项目",
					entryBudget: 3000.00,
					mainUsedOfficeCode: "350100002",
					mainUsedOfficeName: "福州市发展和改革委员会",
					constructionType: "2", // 扩建
					location: "福州市台江区广达路456号",
					buildingArea: 8000.00,
					content: "为满足机关办公需求，拟对现有办公楼进行扩建，新增建筑面积8000平方米，总投资3000万元。项目已获得相关部门审批同意。",
					attachments: [
						{ name: "扩建方案.pdf", url: "#" },
						{ name: "环评报告.pdf", url: "#" },
						{ name: "施工图纸.pdf", url: "#" }
					]
				},
				{
					entryName: "福州市市场监督管理局办公楼改建工程",
					entryBudget: 2000.00,
					mainUsedOfficeCode: "350100004",
					mainUsedOfficeName: "福州市市场监督管理局",
					constructionType: "3", // 改建
					location: "福州市晋安区岳峰路789号",
					buildingArea: 5000.00,
					content: "对现有办公楼进行改建和功能优化，改建面积5000平方米，总投资2000万元。项目已完成前期论证和审批。",
					attachments: [
						{ name: "改建方案.pdf", url: "#" },
						{ name: "结构安全评估报告.pdf", url: "#" },
						{ name: "施工组织设计.pdf", url: "#" }
					]
				}
			];

			// 获取项目信息按钮点击事件
			$('#btnGetProjectInfo').click(function () {
				var entryName = $('#entryName').val();

				// 清空表格
				var tbody = $('#projectInfoTable tbody');
				tbody.empty();

				// 填充模拟数据
				mockProjectData.forEach(function (item, index) {
					var tr = $('<tr>');
					tr.append('<td class="text-center"><input type="radio" name="projectSelect" value="' + index + '"></td>');
					tr.append('<td>' + item.entryName + '</td>');
					tr.append('<td class="text-right">' + js.formatNumber(item.entryBudget, 2) + '</td>');
					tr.append('<td>' + item.mainUsedOfficeName + '</td>');
					tr.append('<td class="text-center">' + js.getDictLabel('ob_construction_type', item.constructionType, '未知') + '</td>');
					tr.append('<td>' + item.location + '</td>');
					tr.append('<td class="text-right">' + js.formatNumber(item.buildingArea, 2) + '</td>');
					tbody.append(tr);
				});

				// 显示模态框
				$('#projectInfoModal').modal('show');
			});

			// 确定选择按钮点击事件
			$('#btnSelectProject').click(function () {
				var selectedIndex = $('input[name="projectSelect"]:checked').val();
				if (selectedIndex === undefined) {
					js.showMessage('请选择一条记录');
					return;
				}

				var selectedData = mockProjectData[selectedIndex];

				// 回填数据到表单
				$('#entryName').val(selectedData.entryName);
				$('#entryBudget').val(selectedData.entryBudget);
				$('#mainUsedOfficeCode').val(selectedData.mainUsedOfficeCode);
				$('#mainUsedOfficeCode_name').val(selectedData.mainUsedOfficeName);
				$('#constructionType').val(selectedData.constructionType).trigger('change');

				// 关闭模态框
				$('#projectInfoModal').modal('hide');

				// 显示成功提示
				js.showMessage('数据获取成功，已自动填充相关信息');
			});

			// 获取数据字典标签
			function getDictLabel(type, value) {
				var dictData = js.getDictListJson(type);
				for (var i = 0; i < dictData.length; i++) {
					if (dictData[i].dictValue == value) {
						return dictData[i].dictLabel;
					}
				}
				return '';
			}
		</script>