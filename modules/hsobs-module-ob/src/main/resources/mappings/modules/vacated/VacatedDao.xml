<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.vacated.dao.VacatedDao">

	<!-- 查询数据
	<select id="findList" resultType="Vacated">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

    <select id="listLedger" resultType="com.hsobs.ob.modules.vacated.entity.VacatedLedger">
        SELECT
            jso.OFFICE_NAME AS "officeName",
            SUM(CASE WHEN ov.type= 1 THEN 1 ELSE 0 END) AS "overuseClearanceCount",
            SUM(CASE WHEN ov.type= 2 THEN 1 ELSE 0 END) AS "retireeClearanceCount",
            SUM(CASE WHEN ov.type= 3 THEN 1 ELSE 0 END) AS "socialOrgClearanceCount",
            SUM(CASE WHEN ov.type= 4 THEN 1 ELSE 0 END) AS "enterpriseClearanceCount",
            SUM(CASE WHEN ov.type= 5 THEN 1 ELSE 0 END) AS "otherClearanceCount",
            SUM(CASE WHEN ov.type= 1 THEN ore.AREA ELSE 0 END) AS "overuseClearanceArea",
            SUM(CASE WHEN ov.type= 2 THEN ore.AREA ELSE 0 END) AS "retireeClearanceArea",
            SUM(CASE WHEN ov.type= 3 THEN ore.AREA ELSE 0 END) AS "socialOrgClearanceArea",
            SUM(CASE WHEN ov.type= 4 THEN ore.AREA ELSE 0 END) AS "enterpriseClearanceArea",
            SUM(CASE WHEN ov.type = 5 THEN ore.AREA ELSE 0 END) AS "otherClearanceArea"
        FROM
            ob_vacated ov
                LEFT JOIN ob_real_estate ore ON ORE.ID=ov.REAL_ESTATE_ID
                LEFT JOIN js_sys_office jso ON
                jso.OFFICE_CODE = ov.APPLY_OFFICE_CODE
        WHERE
            1 = 1 ${sqlMap.dsfOffice}
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        GROUP BY
            jso.OFFICE_NAME
    </select>
    <select id="listQueryData" resultType="com.hsobs.ob.modules.vacated.entity.VacatedQuery">
        SELECT
            jso.OFFICE_NAME AS "officeName",
            ov.VACATED_NAME AS "vacateName",
            ov."TYPE" AS "vacateType",
            ov.CREATE_DATE AS "vacateDate",
            ov.STATUS AS "vacateStatus"
        FROM
            ob_vacated ov
                LEFT JOIN ob_real_estate ore ON ORE.ID=ov.REAL_ESTATE_ID
                LEFT JOIN js_sys_office jso ON
                jso.OFFICE_CODE = ov.APPLY_OFFICE_CODE
        WHERE
            1 = 1 ${sqlMap.dsfOffice}
        <if test="vacateStatus != null and vacateStatus != ''">
            AND ov.STATUS = '${vacateStatus}'
        </if>
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="dateGte != null and dateGte != ''">
            AND a.CREATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
        </if>
        <if test="dateLte != null and dateLte != ''">
            AND a.CREATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
        </if>
    </select>
</mapper>