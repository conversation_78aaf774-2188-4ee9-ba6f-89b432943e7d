<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.estate.dao.RealEstateDao">
	
	<!-- 查询数据
	<select id="findList" resultType="RealEstate">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<select id="getOfficeTechnicalBusinessArea" resultType="com.hsobs.ob.modules.estate.entity.OfficeTechnicalBusinessAreaDto">
		SELECT
			jso.OFFICE_CODE,
			jso.OFFICE_NAME,
			COALESCE(jso.TECHNICAL_BUSINESS_AREA, 0) TECHNICAL_BUSINESS_AREA,
			COALESCE(t.AREA, 0) AREA,
			CASE
				WHEN COALESCE(t.AREA, 0) > COALESCE(jso.TECHNICAL_BUSINESS_AREA, 0)
					THEN ABS(COALESCE(t.AREA, 0) - COALESCE(jso.TECHNICAL_BUSINESS_AREA, 0))
				ELSE 0
				END AS OVER_AREA
		FROM
			JS_SYS_OFFICE jso
				LEFT JOIN (SELECT
							   jse.OFFICE_CODE, SUM(COALESCE(ore.AREA, 0)) AREA
						   FROM
							   JS_SYS_EMPLOYEE jse
								   LEFT JOIN OB_REAL_ESTATE_USED_USER oreuu ON
								   jse.EMP_CODE = oreuu.USER_CODE
								   LEFT JOIN OB_REAL_ESTATE ore ON oreuu.REAL_ESTATE_ID = ore.ID
						   WHERE ore."TYPE" = '4'
						   GROUP BY jse.OFFICE_CODE) t ON jso.OFFICE_CODE = t.OFFICE_CODE
		<where>
			<if test="officeName != null and officeName != ''">
				jso.OFFICE_NAME LIKE '%${officeName}%'
			</if>
		</where>
		ORDER BY OVER_AREA DESC;
	</select>
	<select id="listQueryData" resultType="com.hsobs.ob.modules.estate.entity.RealEstateQuery">
		SELECT
		ore.name AS "officeRoomName",
		rea.NAME AS "officeAddressName",
		ore.OFFICE_TYPE AS "officeType",
		jso.OFFICE_NAME AS "officeName",
		jso.OFFICE_CODE AS "officeCode",
		rea.ADDRESS AS "officeRoomAddress",
		rea.FLOOR_COUNT AS "officeRoomNo",
		(CASE WHEN ore.USED_USER_CODE IS NULL THEN '闲置中' ELSE '使用中' END) AS "usageStatus",
		ore.type AS "officeRoomCategory",
		jsu.USER_NAME AS "userName",
		ore.UPDATE_DATE AS "usageDate",
		ore.area as "area"
		FROM
		ob_real_estate ore
		LEFT JOIN js_sys_office jso ON
		jso.OFFICE_CODE = ore.USED_OFFICE_CODE
		LEFT JOIN ob_real_estate_used_user oreuu ON
		oreuu.REAL_ESTATE_ID = ore.ID
		LEFT JOIN JS_SYS_USER jsu ON
		jsu.USER_CODE = oreuu.USER_CODE
		LEFT JOIN ob_real_estate_address rea ON
		ore.real_estate_address_id = rea.id
		where 1=1 ${sqlMap.dsfOffice}
		<if test="officeRoomNo != null and officeRoomNo != ''">
			AND rea.FLOOR_COUNT like '%${officeRoomNo}%'
		</if>
		<if test="officeRoomAddress != null and officeRoomAddress != ''">
			AND rea.ADDRESS like '%${officeRoomAddress}%'
		</if>
		<if test="officeCode != null and officeCode != ''">
			AND jso.office_code = '${officeCode}'
		</if>
		<if test="officeRoomName != null and officeRoomName != ''">
			AND ore.name = '${officeRoomName}'
		</if>
		<if test="officeRoomCategory != null and officeRoomCategory != ''">
			AND ore.type = '${officeRoomCategory}'
		</if>
		<if test="userCode != null and userCode != ''">
			AND jsu.user_code = '${userCode}'
		</if>
		<if test="userName != null and userName != ''">
			AND jsu.user_name like '%${userName}%'
		</if>
		<if test="useTag != null and useTag == 1">
			AND ore.USED_USER_CODE IS NULL
		</if>
		<if test="usageStatus != '' and usageStatus == 0">
			AND ore.USED_USER_CODE IS not NULL
		</if>
		<if test="usageStatus != '' and usageStatus == 1">
			AND ore.USED_USER_CODE IS NULL
		</if>
		<if test="dateGte != null and dateGte != ''">
			AND ore.UPDATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
		</if>
		<if test="dateLte != null and dateLte != ''">
			AND ore.UPDATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
		</if>
	</select>
</mapper>