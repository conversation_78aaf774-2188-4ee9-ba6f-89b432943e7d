<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.estate.dao.RealEstateAddressFloorDao">

    <!-- 查询数据
    <select id="findList" resultType="RealEstateAddressFloor">
        SELECT ${sqlMap.column.toSql()}
        FROM ${sqlMap.table.toSql()}
        <where>
            ${sqlMap.where.toSql()}
        </where>
        ORDER BY ${sqlMap.order.toSql()}
    </select> -->

</mapper>