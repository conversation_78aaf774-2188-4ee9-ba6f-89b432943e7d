<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.ownership.dao.OwnershipRealEstateDao">

    <!-- 查询数据
    <select id="findList" resultType="OwnershipRealEstate">
        SELECT ${sqlMap.column.toSql()}
        FROM ${sqlMap.table.toSql()}
        <where>
            ${sqlMap.where.toSql()}
        </where>
        ORDER BY ${sqlMap.order.toSql()}
    </select> -->

    <select id="listQueryData" resultType="com.hsobs.ob.modules.ownership.entity.OwnershipRegistrationQuery">
        SELECT
        oor.CREATE_DATE AS "applyDate",
        jso.OFFICE_NAME AS "officeName",
        NVL(ore.name,orea.NAME) AS "officeRoomName",
        oor."TYPE" AS "ownershipStatus",
        oor.NAME AS "title",
        oor.PURPOSE AS "purpose",
        oor.STATUS AS "approvalStatus"
        FROM
            ob_ownership_registration oor
                LEFT JOIN js_sys_office jso ON
                jso.OFFICE_CODE = oor.OWNER_OFFICE_CODE
                LEFT JOIN ob_ownership_real_estate oore ON
                oore.OWNERSHIP_ID = oor.ID
                LEFT JOIN ob_real_estate ore ON
                ore.id = oore.REAL_ESTATE_ID
            LEFT JOIN OB_REAL_ESTATE_ADDRESS orea ON
            orea.id = oore.REAL_ESTATE_ID
        where 1=1 AND oor.STATUS !=1 AND oor."TYPE" !=1 ${sqlMap.dsfOffice}
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="officeRoomName != null and officeRoomName != ''">
            AND (ore.name = '${officeRoomName}' or orea.name = '${officeRoomName}')
        </if>
        <if test="approvalStatus != null and approvalStatus != ''">
            AND oor.STATUS = '${approvalStatus}'
        </if>
        <if test="ownershipStatus != null and ownershipStatus != ''">
            AND oor."TYPE" = '${ownershipStatus}'
        </if>
        <if test="dateGte != null and dateGte != ''">
            AND oor.CREATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
        </if>
        <if test="dateLte != null and dateLte != ''">
            AND oor.CREATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
        </if>
    </select>
</mapper>