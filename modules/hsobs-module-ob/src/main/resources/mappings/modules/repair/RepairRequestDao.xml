<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.repair.dao.RepairRequestDao">

	<!-- 查询数据
	<select id="findList" resultType="RepairRequest">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

    <select id="listLedger" resultType="com.hsobs.ob.modules.repair.entity.RepairRequestLedger">
        SELECT
            jso.OFFICE_NAME AS "officeName",
            orr.TYPE AS "maintenanceType",
            orr.BUDGET AS "maintenanceAmount",
            ore.AREA AS "maintenanceTotalArea"
        FROM
            ob_repair_request orr
        LEFT JOIN ob_real_estate ore ON
        ore.ID = orr.REAL_ESTATE_ID
        LEFT JOIN js_sys_office jso ON
        jso.OFFICE_CODE = orr.APPLY_OFFICE_CODE
        where 1=1
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>

    </select>
    <select id="listQueryData" resultType="com.hsobs.ob.modules.repair.entity.RepairRequestQuery">
        SELECT
            orr.ID AS "maintenanceOrderNumber",
            jso.OFFICE_NAME AS "officeName",
            orr.TYPE AS "maintenanceType",
            orr.CREATE_DATE AS "applyDate",
            orr.PROJECT_NAME AS "maintenanceProjectName",
            orr.BUDGET AS "budgetAmount",
            COALESCE(ore.AREA,0) AS "maintenanceArea",
            ore.NAME AS "officeRoomName",
            orr.REPAIR_DATE AS "commenceDate",
            orr.COMPLETION_DATE AS "completionDate",
            orr.STATUS AS "approvalStatus"
        FROM
            ob_repair_request orr
                LEFT JOIN ob_real_estate ore ON
                ore.ID = orr.REAL_ESTATE_ID
                LEFT JOIN js_sys_office jso ON
                jso.OFFICE_CODE = orr.APPLY_OFFICE_CODE
        where 1=1
        <if test="maintenanceOrderNumber != null and maintenanceOrderNumber != ''">
            AND orr.ID = '${maintenanceOrderNumber}'
        </if>
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="maintenanceType != null and maintenanceType != ''">
            AND orr.TYPE = '${maintenanceType}'
        </if>
        <if test="approvalStatus != null and approvalStatus != ''">
            AND orr.STATUS = '${approvalStatus}'
        </if>
        <if test="dateGte != null and dateGte != ''">
            AND orr.CREATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
        </if>
        <if test="dateLte != null and dateLte != ''">
            AND orr.CREATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
        </if>
    </select>
</mapper>