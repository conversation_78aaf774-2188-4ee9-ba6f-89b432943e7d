<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.bpm.dao.ObCommonBpmDao">
    <select id="findAllTaskList" resultType="Map">
        SELECT
            ${columns},
            bpmt.TASK_ID as "taskId",
            bpmt.TASK_NAME as "taskName",
            bpmt.PROC_INS_NAME_ as "procInsName",
            bpmt.TASK_DUE_DATE_ as "taskDueDate",
            bpmt.TASK_CLAIM_TIME_ as "taskClaimTime",
            bpmt.TASK_CREATE_TIME_ as "taskCreateTime",
            bpmt.FORM_KEY_ as "formKey",
            bpmt.TASK_STATUS as "taskStatus",
            bpmt.ASSIGNEE_ as "task_assignee"
        FROM (
            SELECT
            RES.ID_ AS "TASK_ID",
            RES.NAME_ AS "TASK_NAME",
            RES.END_TIME_ AS "TASK_DUE_DATE_",
            RES.CLAIM_TIME_ AS "TASK_CLAIM_TIME_",
            RES.START_TIME_ AS "TASK_CREATE_TIME_",
            HPI.NAME_ AS "PROC_INS_NAME_",
            RES.ASSIGNEE_ AS "ASSIGNEE_",
            SUBSTR(HPI.BUSINESS_KEY_, 0, INSTR(HPI.BUSINESS_KEY_, ':') - 1) AS "FORM_KEY_",
            SUBSTR(HPI.BUSINESS_KEY_, INSTR(HPI.BUSINESS_KEY_, ':') + 1) AS "BUSINESS_KEY_",
            CASE
            WHEN RES.END_TIME_ IS NULL THEN '1'
            ELSE '2'
            END AS "TASK_STATUS"
            FROM ACT_HI_TASKINST RES
            INNER JOIN ACT_HI_PROCINST HPI ON RES.PROC_INST_ID_ = HPI.ID_
            WHERE RES.TENANT_ID_ = #{tenantId}
            AND HPI.BUSINESS_KEY_ LIKE #{businessKeyLike}
            <if test="bpmStatus != null and bpmStatus != 3">
                AND (
                <choose>
                    <when test="bpmStatus == 1">
                        RES.END_TIME_ IS NULL
                    </when>
                    <when test="bpmStatus == 2">
                        -- 任务完成，且流程实例也结束，才算已办
                        RES.END_TIME_ IS NOT NULL
                        OR (HPI.END_TIME_ IS NULL OR HPI.END_TIME_ = '')
                    </when>
                </choose>
                )
            </if>
            <if test="taskNames != null and taskNames.length>0">
                AND RES.NAME_ IN
                <foreach collection="taskNames" item="taskName" open="(" separator="," close=")">
                    #{taskName}
                </foreach>
            </if>
            <if test="processName != null and processName != ''">
                AND RES.NAME_= #{processName}
            </if>
            AND (
            RES.ASSIGNEE_ = #{assignee}
            OR (
            RES.ASSIGNEE_ IS NULL
            AND EXISTS(
            SELECT 1
            FROM ACT_HI_IDENTITYLINK LINK1
            WHERE LINK1.TYPE_ = 'candidate'
            AND LINK1.TASK_ID_ = RES.ID_
            AND (
            LINK1.USER_ID_ = #{candidateUser}
            <if test="candidateGroups != null and candidateGroups.length>0">
                OR LINK1.GROUP_ID_ IN
                <foreach collection="candidateGroups" item="groupId" separator="," open="(" close=")">
                    #{groupId}
                </foreach>
            </if>
            )
            )
            )
            )
        ) bpmt
        INNER JOIN ${businessTable}
        <if test="bizWhere != null and bizWhere != ''">
            WHERE ${bizWhere}
        </if>
        <choose>
            <when test="bizOrder != null and bizOrder != ''">
                ORDER BY ${bizOrder}
            </when>
            <otherwise>
                ORDER BY bpmt.TASK_CREATE_TIME_ DESC
            </otherwise>
        </choose>
    </select>

    <select id="findAllObjecList" resultType="Map">
        SELECT
        ${columns},
        bpmt.TASK_ID AS "taskId",
        bpmt.TASK_NAME AS "taskName",
        bpmt.PROC_INS_NAME_ AS "procInsName",
        bpmt.TASK_DUE_DATE_ AS "taskDueDate",
        bpmt.TASK_CLAIM_TIME_ AS "taskClaimTime",
        bpmt.TASK_CREATE_TIME_ AS "taskCreateTime",
        bpmt.FORM_KEY_ AS "formKey",
        bpmt.TASK_STATUS AS "taskStatus",
        bpmt.ASSIGNEE_ AS "task_assignee"
        FROM (
        SELECT *
        FROM (
        SELECT
        RES.ID_ AS "TASK_ID",
        RES.NAME_ AS "TASK_NAME",
        RES.END_TIME_ AS "TASK_DUE_DATE_",
        RES.CLAIM_TIME_ AS "TASK_CLAIM_TIME_",
        RES.START_TIME_ AS "TASK_CREATE_TIME_",
        HPI.NAME_ AS "PROC_INS_NAME_",
        HPI.END_TIME_ AS "PROC_END_TIME",
        RES.ASSIGNEE_ AS "ASSIGNEE_",
        SUBSTR(HPI.BUSINESS_KEY_, 0, INSTR(HPI.BUSINESS_KEY_, ':') - 1) AS "FORM_KEY_",
        SUBSTR(HPI.BUSINESS_KEY_, INSTR(HPI.BUSINESS_KEY_, ':') + 1) AS "BUSINESS_KEY_",
        CASE
        WHEN HPI.END_TIME_ IS NOT NULL THEN '3'
        WHEN RES.END_TIME_ IS NULL THEN '1'
        ELSE '2'
        END AS "TASK_STATUS",
        ROW_NUMBER() OVER (
        PARTITION BY HPI.BUSINESS_KEY_
        ORDER BY
        RES.START_TIME_ DESC
        ) AS RN
        FROM ACT_HI_TASKINST RES
        INNER JOIN ACT_HI_PROCINST HPI ON RES.PROC_INST_ID_ = HPI.ID_
        WHERE RES.TENANT_ID_ = #{tenantId}
        <if test="taskNames != null and taskNames.length>0">
            AND RES.NAME_ IN
            <foreach collection="taskNames" item="taskName" open="(" separator="," close=")">
                #{taskName}
            </foreach>
        </if>
        <if test="processName != null and processName != ''">
            AND RES.NAME_= #{processName}
        </if>
        AND HPI.BUSINESS_KEY_ LIKE #{businessKeyLike}
        ) t
        WHERE t.RN = 1
        ) bpmt
        RIGHT JOIN ${businessTable}
        <if test="bizWhere != null and bizWhere != ''">
            WHERE ${bizWhere}
        </if>

        <choose>
            <when test="bizOrder != null and bizOrder != ''">
                ORDER BY ${bizOrder}
            </when>
            <otherwise>
                ORDER BY bpmt.TASK_CREATE_TIME_ DESC
            </otherwise>
        </choose>

    </select>
</mapper>