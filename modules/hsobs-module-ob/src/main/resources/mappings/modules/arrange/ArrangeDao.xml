<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.arrange.dao.ArrangeDao">

	<!-- 查询数据
	<select id="findList" resultType="Arrange">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

    <select id="listLedger" resultType="com.hsobs.ob.modules.arrange.entity.ArrangeLedger">
        SELECT
            jso.OFFICE_NAME AS "officeName",
            oa.ARRANGE_TYPE AS "arrangeType",
            count(1) AS "arrangeNumber",
            sum(oa.AREA) AS "arrangeTotalArea"
        FROM
            ob_arrange oa
                LEFT JOIN js_sys_office jso ON
                jso.OFFICE_CODE = oa.USED_OFFICE_CODE
        where 1=1 ${sqlMap.dsfOffice}
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="arrangeType != null and arrangeType != ''">
            AND oa.ARRANGE_TYPE = '${arrangeType}'
        </if>
        GROUP BY jso.OFFICE_NAME,oa.ARRANGE_TYPE
    </select>
    <select id="listDataTransferQuery" resultType="com.hsobs.ob.modules.arrange.entity.TransferQuery">
        SELECT
            jso.OFFICE_NAME AS "officeName",
            oa.CREATE_DATE AS "transferApplyDate",
            COALESCE(NVL((SELECT SUM(ore.AREA) FROM ob_arrange_real_estate oare LEFT JOIN ob_real_estate ore ON ore.ID = oare.REAL_ESTATE_ID WHERE oare.ARRANGE_ID = oa.ID ),(SELECT SUM(orea.BUILDING_AREA) FROM ob_arrange_real_estate oare LEFT JOIN OB_REAL_ESTATE_ADDRESS orea ON orea.ID = oare.REAL_ESTATE_ID WHERE oare.ARRANGE_ID = oa.ID )),0) AS "transferTotalArea",
            COALESCE((SELECT SUM(ore.AREA) FROM ob_arrange_real_estate oare LEFT JOIN ob_real_estate ore ON ore.ID = oare.REAL_ESTATE_ID WHERE oare.ARRANGE_ID = oa.ID AND (ore."TYPE" = 0 OR ore."TYPE" = 1 OR ore."TYPE" = 2 )),0) AS "transferBasicArea",
            COALESCE((SELECT SUM(ore.AREA) FROM ob_arrange_real_estate oare LEFT JOIN ob_real_estate ore ON ore.ID = oare.REAL_ESTATE_ID WHERE oare.ARRANGE_ID = oa.ID AND (ore."TYPE" = 3 OR ore."TYPE" = 4)),0) AS "transferAuxiliaryArea",
            oa."DESCRIBE" AS "transferReason",
            oa.REGULATE_DATE AS "transferDate",
            oa.STATUS AS "transferApprovalStatus"
        FROM
            ob_arrange oa
            LEFT JOIN js_sys_office jso ON
            jso.OFFICE_CODE = oa.USED_OFFICE_CODE
        where oa.arrange_type = 1 and oa.status != 1 and 1=1 ${sqlMap.dsfOffice}
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="transferApprovalStatus != null and transferApprovalStatus != ''">
            AND oa.STATUS = '${transferApprovalStatus}'
        </if>
    </select>
    <select id="listDataExchangeQuery" resultType="com.hsobs.ob.modules.arrange.entity.ExchangeQuery">
        SELECT
        jso.OFFICE_NAME AS "officeName",
        oa.CREATE_DATE AS "exchangeApplyDate",
        oa."DESCRIBE" AS "exchangeConfigMethod",
        oa.FINDINGS AS "exchangeSurveyResult",
        oa.STATUS AS "exchangeApprovalStatus"
        FROM
        ob_arrange oa
        LEFT JOIN js_sys_office jso ON
        jso.OFFICE_CODE = oa.USED_OFFICE_CODE
        where oa.arrange_type = 2 and oa.status != 1 and 1=1
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="exchangeApprovalStatus != null and exchangeApprovalStatus != ''">
            AND oa.STATUS = '${exchangeApprovalStatus}'
        </if>
    </select>
    <select id="listDataRentInfoQuery" resultType="com.hsobs.ob.modules.arrange.entity.RentInfoQuery">
        SELECT
        oa.id AS "id",
        CASE
        WHEN oa.REAL_ESTATE_TYPE = '1' THEN c.address
        ELSE b.ADDRESS
        END AS "roomInfo",
        jso.OFFICE_NAME AS "officeName",
        oa.CREATE_DATE AS "transferApplyDate",
        oa.STATUS AS "transferApprovalStatus",
        oa.RENTAL_START_DATE AS "rentStartDate",
        oa.RENTAL_END_DATE AS "rentEndDate"
        FROM
        ob_arrange oa
        LEFT JOIN js_sys_office jso ON
        jso.OFFICE_CODE = oa.USED_OFFICE_CODE
        LEFT JOIN ob_arrange_real_estate oare ON
        oa.ID = oare.ARRANGE_ID
        LEFT JOIN ob_real_estate ore ON
        ore.ID = oare.REAL_ESTATE_ID
        LEFT JOIN ob_real_estate_address b ON
        b.id = ore.REAL_ESTATE_ADDRESS_ID
        LEFT JOIN ob_real_estate_address c ON
        c.id = oare.REAL_ESTATE_ID
        where 1=1 and oa.arrange_type = 3 and oa.status != 1 ${sqlMap.dsfOffice}
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="transferApprovalStatus != null and transferApprovalStatus != ''">
            AND oa.STATUS = '${transferApprovalStatus}'
        </if>
    </select>
    <select id="listDataProjectQuery" resultType="com.hsobs.ob.modules.arrange.entity.ProjectQuery">
        SELECT
        jso.OFFICE_NAME AS "officeName",
        oa.CREATE_DATE AS "transferApplyDate",
        oa.STATUS AS "transferApprovalStatus",
        oa.CONSTRUCTION_TYPE AS "projectCategory",
        oa.COMMENCEMENT_DATE AS "projectCategoryDate"
        FROM
        ob_arrange oa
        LEFT JOIN js_sys_office jso ON
        jso.OFFICE_CODE = oa.USED_OFFICE_CODE
        where oa.arrange_type = 4 and oa.status != 1 and 1=1
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="transferApprovalStatus != null and transferApprovalStatus != ''">
            AND oa.STATUS = '${transferApprovalStatus}'
        </if>
        <if test="projectCategory != null and projectCategory != ''">
            AND oa.CONSTRUCTION_TYPE = '${projectCategory}'
        </if>
        <if test="dateGte != null and dateGte != ''">
            AND oa.COMMENCEMENT_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
        </if>
        <if test="dateLte != null and dateLte != ''">
            AND oa.COMMENCEMENT_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
        </if>
    </select>
</mapper>