<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.apply.dao.ApplyDao">

	<select id="listLedgerData" resultType="com.hsobs.ob.modules.apply.entity.ApplyLedger">
		SELECT
			a.id AS "id",
			a.name AS "name",
			a.address AS "address",
			o1.office_name AS "ownerOfficeName",
			o2.office_name AS "usedOfficeName",
			o2.OFFICE_TYPE AS "officeType",
			(SELECT count(1) FROM js_sys_employee jse WHERE jse.OFFICE_CODE = o2.OFFICE_CODE) AS "employeeCount",
			a.FLOOR_COUNT AS "floorCount",
			SUM(CASE WHEN ore.USED_USER_CODE IS NOT NULL THEN 1 ELSE 0 END) AS "number"
		FROM
			ob_real_estate_address a
				LEFT JOIN js_sys_area o ON
				a.region = o.area_code
				LEFT JOIN js_sys_office o1 ON
				a.owner_office_code = o1.office_code
				LEFT JOIN js_sys_office o2 ON
				a.used_office_code = o2.office_code
				LEFT JOIN ob_real_estate ore ON
				ore.REAL_ESTATE_ADDRESS_ID = a.ID
		where 1=1
		<if test="name != null and name != ''">
			AND a.name = '${name}'
		</if>
		<if test="usedOfficeCode != null and usedOfficeCode != ''">
			AND o2.office_code = '${usedOfficeCode}'
		</if>
		GROUP BY
			a.id,
			a.name,
			a.address,
			o1.office_name,
			o2.office_name,
			o2.OFFICE_TYPE,
			a.FLOOR_COUNT,
			o2.OFFICE_CODE
	</select>

	<select id="findCredentialsAgreementsList" resultType="com.hsobs.ob.modules.apply.entity.CredentialsAgreementsListResponse">
		SELECT
			jsfu.id,
			jsfu.FILE_NAME ldmc,
			jsfu.ID pzbh,
			jsfu.BIZ_TYPE type,
			jsfu.CREATE_DATE gxsj,
			jsfu.ID fileId,
			jsfu.BIZ_TYPE fileType
		FROM
			JS_SYS_FILE_UPLOAD jsfu
				LEFT JOIN OB_APPLY oa ON
				jsfu.BIZ_KEY = oa.ID

		<where>
			(JSFU.BIZ_TYPE = 'apply_vouchers_file'
			OR jsfu.BIZ_TYPE = 'apply_agreement_file')
			<if test="fileType != null and fileType != ''">
				AND jsfu.BIZ_TYPE = #{fileType}
			</if>
		</where>
		 ORDER BY jsfu.UPDATE_DATE DESC
	</select>

	<select id="findCredentialsAgreementsListCount" resultType="long">
		SELECT count(1) FROM JS_SYS_FILE_UPLOAD jsfu
		LEFT JOIN OB_APPLY oa ON
		jsfu.BIZ_KEY = oa.ID
		<where>
			(JSFU.BIZ_TYPE = 'apply_vouchers_file'
			OR jsfu.BIZ_TYPE = 'apply_agreement_file')
			<if test="fileType != null and fileType != ''">
				AND jsfu.BIZ_TYPE = #{fileType}
			</if>
		</where>
	</select>
    <select id="listApplyDetailQueryData" resultType="com.hsobs.ob.modules.apply.entity.ApplyDetailQuery">
		SELECT
		jso.OFFICE_NAME AS "officeName",
		oa.REAL_ESTATE_TYPE AS "useType",
		jse.EMP_NAME AS "userName",
		jse.ESTABLISHMENT_TYPE AS "rank",
		oa.CREATE_DATE AS "usageDate",
		oa.AREA AS "usageArea",
		oa.DESCRIBE AS "describe",
		oa.PURPOSE AS "purpose",
		oa.POSITION AS "position",
		oa.ROOM_NUM AS "roomNum",
		oa.status AS "status"
		FROM
		ob_apply oa
		LEFT JOIN js_sys_office jso ON
		jso.OFFICE_CODE = oa.USED_OFFICE_CODE
		LEFT JOIN JS_SYS_EMPLOYEE jse ON
		jse.EMP_CODE = oa.USED_USER_CODE
		WHERE
		oa.STATUS =0
		<if test="userName != null and userName != ''">
			AND jse.EMP_NAME = '${userName}'
		</if>
		<if test="describe != null and describe != ''">
			AND oa.DESCRIBE like '%${describe}%'
		</if>
		<if test="purpose != null and purpose != ''">
			AND oa.PURPOSE like '%${purpose}%'
		</if>
		<if test="position != null and position != ''">
			AND oa.POSITION like '%${position}%'
		</if>
		<if test="roomNum != null">
			AND oa.ROOM_NUM = ${roomNum}
		</if>
		<if test="useType != null and useType != ''">
			AND oa.REAL_ESTATE_TYPE = '${useType}'
		</if>
		<if test="rank != null and rank != ''">
			AND jse.ESTABLISHMENT_TYPE = '${rank}'
		</if>
		<if test="paId != null and paId != ''">
			AND oa.PAID = '${paId}'
		</if>
		<if test="status != null and status != ''">
			AND oa.status = '${status}'
		</if>
		<if test="dateGte != null and dateGte != ''">
			AND oa.CREATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
		</if>
		<if test="dateLte != null and dateLte != ''">
			AND oa.CREATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
		</if>
	</select>
    <select id="listApplyInfoQueryData" resultType="com.hsobs.ob.modules.apply.entity.ApplyInfoQuery">
		SELECT
		jso.OFFICE_NAME AS "officeName",
		ore."TYPE" AS "type",
		OA."DESCRIBE" AS "describe",
		oa.purpose AS "purpose",
		COALESCE(CASE WHEN ore."TYPE" =0 then
		(SELECT ooe.province_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =30)
		+ (SELECT ooe.province_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =40)
		+ (SELECT ooe.bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =50)
		+ (SELECT ooe.deputy_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =60)
		+ (SELECT ooe.division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =70)
		+ (SELECT ooe.deputy_division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =80)
		+ (SELECT ooe.below_division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =90)
		+ (SELECT ooe.municipal_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =91)
		+ (SELECT ooe.municipal_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =92)
		+ (SELECT ooe.municipal_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =93)
		+ (SELECT ooe.municipal_deputy_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =94)
		+ (SELECT ooe.below_municipal_bureau FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =95)
		+ (SELECT ooe.county_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =96)
		+ (SELECT ooe.county_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =97)
		+ (SELECT ooe.section_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =98)
		+ (SELECT ooe.deputy_section_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =99)
		+ (SELECT ooe.below_section_level FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =100)
		+ (SELECT ooe.township_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =101)
		+ (SELECT ooe.township_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =102)
		+ (SELECT ooe.below_township_level FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =103)
		WHEN ore."TYPE" =1 then
		(CASE WHEN jso.EXTERNAL_STAFF &lt; 200  THEN jso.EXTERNAL_STAFF*(SELECT osrac.min_area FROM ob_service_rooms_approved_config osrac WHERE osrac.ID = jso.OFFICE_TYPE)
		WHEN jso.EXTERNAL_STAFF > 400 THEN jso.EXTERNAL_STAFF*(SELECT osrac.MAX_AREA FROM ob_service_rooms_approved_config osrac WHERE osrac.ID = jso.OFFICE_TYPE)
		ELSE (1100-jso.EXTERNAL_STAFF)/100 END)
		WHEN ore."TYPE" =2 then ((CASE WHEN jso.EXTERNAL_STAFF &gt; 200  THEN jso.EXTERNAL_STAFF*(SELECT osrac.min_area FROM ob_service_rooms_approved_config osrac WHERE osrac.ID = jso.OFFICE_TYPE)
		WHEN jso.EXTERNAL_STAFF > 400 THEN jso.EXTERNAL_STAFF*(SELECT osrac.MAX_AREA FROM ob_service_rooms_approved_config osrac WHERE osrac.ID = jso.OFFICE_TYPE)
		ELSE (1100-jso.EXTERNAL_STAFF)/100 END)+(SELECT ooe.province_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =30)
		+ (SELECT ooe.province_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =40)
		+ (SELECT ooe.bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =50)
		+ (SELECT ooe.deputy_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =60)
		+ (SELECT ooe.division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =70)
		+ (SELECT ooe.deputy_division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =80)
		+ (SELECT ooe.below_division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =90)
		+ (SELECT ooe.municipal_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =91)
		+ (SELECT ooe.municipal_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =92)
		+ (SELECT ooe.municipal_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =93)
		+ (SELECT ooe.municipal_deputy_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =94)
		+ (SELECT ooe.below_municipal_bureau FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =95)
		+ (SELECT ooe.county_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =96)
		+ (SELECT ooe.county_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =97)
		+ (SELECT ooe.section_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =98)
		+ (SELECT ooe.deputy_section_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =99)
		+ (SELECT ooe.below_section_level FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =100)
		+ (SELECT ooe.township_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =101)
		+ (SELECT ooe.township_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =102)
		+ (SELECT ooe.below_township_level FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =103))*(SELECT oerac.equipment_room_ratio AS "equipmentRoomRatio" FROM ob_equipment_rooms_approved_config oerac WHERE oerac.ID =2)/100
		WHEN ore."TYPE" =4 then jso.TECHNICAL_BUSINESS_AREA
		ELSE 0 END,0) AS "approvedArea",
		CASE WHEN sum(oa.AREA) IS NULL THEN 0 ELSE sum(oa.AREA) END AS "actualArea"
		FROM
		ob_apply oa
		LEFT JOIN js_sys_office jso ON
		jso.OFFICE_CODE = oa.USED_OFFICE_CODE
		LEFT JOIN JS_SYS_EMPLOYEE jse ON
		jse.EMP_CODE = oa.USED_USER_CODE
		LEFT JOIN ob_apply_real_estate oar ON
		oar.APPLY_ID = oa.ID
		LEFT JOIN ob_real_estate ore ON
		ore.ID = oar.REAL_ESTATE_ID
		WHERE
		oa.STATUS =0
		<if test="officeCode != null and officeCode != ''">
			AND jso.OFFICE_CODE = '${officeCode}'
		</if>
		<if test="type != null and type != ''">
			AND ore."TYPE" = '${type}'
		</if>
		<if test="describe != null and describe != ''">
			AND OA."DESCRIBE" like '%${describe}%'
		</if>
		GROUP BY
		jso.OFFICE_NAME,
		OA."DESCRIBE",
		jse.ESTABLISHMENT_TYPE,
		ore."TYPE",
		jso.OFFICE_ESTABLISHMENT_ID,
		jso.EXTERNAL_STAFF,
		jso.OFFICE_TYPE,
		jso.TECHNICAL_BUSINESS_AREA,
		oa.purpose
	</select>
</mapper>