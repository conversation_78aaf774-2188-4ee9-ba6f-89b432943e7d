<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.supervisehandlingtasks.dao.SuperviseHandlingTasksDao">
	

	<select id="listLedger" resultType="com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasksLedger">
		SELECT
			jso.OFFICE_NAME AS "officeName",
			(SELECT count(1) FROM ob_real_estate ore WHERE ore.USED_OFFICE_CODE = osht.office_code) AS "officeNumber",
			(
				SELECT a.tree_names AS "treeNames"
				FROM
					js_sys_dict_data a
				WHERE
					a.status != 1
				AND a.parent_code = 0
				AND a.DICT_VALUE = osht.TASK_SOURCE
				AND a.dict_type = 'supervise_handling_task_source'
				AND ( a.is_sys = 1
				OR a.corp_code = 0 )) AS "businessType",
			COUNT(1) AS "SpotNumber",
			(
		SELECT COUNT(1)
		FROM
			ob_supervise_handling_tasks obsht
		WHERE
			(obsht.TASK_STATUS = 2
		   OR obsht.TASK_STATUS = 3)
		  AND obsht.office_code = osht.office_code AND obsht.status = 0) AS "questionNumber",
			(
		SELECT COUNT(1)
		FROM
			ob_supervise_handling_tasks obsht
		WHERE
			obsht.TASK_STATUS = 3
		  AND obsht.office_code = osht.office_code AND obsht.status = 0) AS "correctedNumber",
			(
		SELECT COUNT(1)
		FROM
			ob_supervise_handling_tasks obsht
		WHERE
			(obsht.TASK_STATUS = 2)
		  AND obsht.office_code = osht.office_code AND obsht.status = 0) AS "uncorrectedNumber"
		FROM
			ob_supervise_handling_tasks osht
			LEFT JOIN JS_SYS_OFFICE jso ON
			osht.OFFICE_CODE = JSO.OFFICE_CODE
			LEFT JOIN ob_real_estate_address orea ON
			jso.ADDRESS = orea.ID
			LEFT JOIN js_sys_area jsa ON
			jsa.AREA_CODE = orea.REGION
		WHERE osht.status = 0
		<if test="officeCode != null and officeCode != ''">
			AND jso.office_code = '${officeCode}'
		</if>
		GROUP BY
			jso.OFFICE_NAME,
			osht.TASK_SOURCE,
			osht.office_code,
			jsa.AREA_NAME
	</select>
    <select id="listQueryData"
            resultType="com.hsobs.ob.modules.supervisehandlingtasks.entity.SuperviseHandlingTasksQuery">
		SELECT jso.OFFICE_NAME AS "officeName",
			   osht.CHECK_DATE AS "checkDate",
			   osht.STATUS AS "status",
			   osht.TASK_STATUS AS "taskStatus",
			   osht.VERIFY_THE_SITUATION AS "verifyTheSituation",
			   osht.RECTIFICATION_MATTERS AS "rectificationMatters",
			   osht.RECTIFICATION_RESULT_FEEDBACK AS "rectificationResultFeedback"
		FROM
			ob_supervise_handling_tasks osht
				LEFT JOIN JS_SYS_OFFICE jso ON
				osht.OFFICE_CODE = JSO.OFFICE_CODE
				LEFT JOIN ob_real_estate_address orea ON
				jso.ADDRESS = orea.ID
		where 1=1
		<if test="officeCode != null and officeCode != ''">
			AND jso.office_code = '${officeCode}'
		</if>
		<if test="status != null and status != ''">
			AND osht.STATUS = '${status}'
		</if>
		<if test="taskStatus != null and taskStatus != ''">
			AND osht.TASK_STATUS = '${taskStatus}'
		</if>
		<if test="dateGte != null and dateGte != ''">
			AND osht.CHECK_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
		</if>
		<if test="dateLte != null and dateLte != ''">
			AND osht.CHECK_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
		</if>
	</select>

</mapper>