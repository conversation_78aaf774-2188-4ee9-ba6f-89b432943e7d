<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.earlywarnmessage.dao.EarlyWarnMessageDao">

    <select id="countOwnership" resultType="map">
        SELECT
            jse.EMP_CODE AS "empCode",
            jse.EMP_NAME AS "empName",
            jse.ESTABLISHMENT_TYPE AS "establishmentType",
            jso.office_code AS "officeCode",
            re."TYPE" AS "realEstateType",
            re.AREA AS "area",
            jso.OFFICE_TYPE AS "officeType"
        FROM
            ob_ownership_registration owr
        LEFT JOIN ob_real_estate re ON
            owr.REAL_ESTATE_ID = re.ID
        LEFT JOIN js_sys_employee jse ON
            re.used_by = jse.emp_code
        LEFT JOIN js_sys_office jso ON
            jso.office_code = jse.office_code
        WHERE re."TYPE" =  ${realEstateType} AND jse.ESTABLISHMENT_TYPE=${establishmentType}
    </select>
    <select id="countOfficeUserNumber" resultType="java.util.Map">
        SELECT
            jso.office_code AS "officeCode",
            jso.office_name AS "officeName",
            sum(re.AREA) AS "area",
            COUNT(DISTINCT jse.emp_code) AS "uniqueEmployeeCount"
        FROM
            ob_ownership_registration owr
        LEFT JOIN ob_real_estate re ON
            owr.REAL_ESTATE_ID = re.ID
        LEFT JOIN js_sys_employee jse ON
            re.used_by = jse.emp_code
        LEFT JOIN js_sys_office jso ON
            jso.office_code = jse.office_code
        WHERE re."TYPE" =  ${realEstateType} AND jso.office_type=${earlyWarnIndex}
        GROUP BY jso.office_name,jso.office_code
    </select>
    <select id="countOfficeAndServiceUseSituation" resultType="java.util.Map">
        SELECT
            jso.office_code AS "officeCode",
            jso.office_name AS "officeName",
            sum(re.AREA) AS "area",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore LEFT JOIN js_sys_employee jsee ON ore.used_by = jsee.emp_code WHERE jsee.OFFICE_CODE=jso.OFFICE_CODE GROUP BY jsee.OFFICE_CODE) AS "devArea"
        FROM
            ob_ownership_registration owr
        LEFT JOIN ob_real_estate re ON
            owr.REAL_ESTATE_ID = re.ID
        LEFT JOIN js_sys_employee jse ON
            re.used_by = jse.emp_code
        LEFT JOIN js_sys_office jso ON
            jso.office_code = jse.office_code
        WHERE re."TYPE" = '0' or re."TYPE" = '1'
        GROUP BY jso.office_name,jso.office_code
    </select>
    <select id="roomAreaExcessRecordListData"
            resultType="com.hsobs.ob.modules.earlywarnmessage.entity.RoomAreaExcessRecord">
        SELECT
        jsu.USER_NAME AS "userName",
        jsu.USER_CODE AS "userCode",
        jso.OFFICE_NAME AS "officeName",
        jso.OFFICE_CODE AS "officeCode",
        ore."TYPE" AS "officeType",
        jse.ESTABLISHMENT_TYPE AS "rank",
        COALESCE((SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =jse.ESTABLISHMENT_TYPE),0) AS "approvedArea",
        COALESCE(SUM(ore.AREA),	0)/decode((SELECT count(1) FROM OB_REAL_ESTATE_USED_USER oreuu2 WHERE OREUU2.REAL_ESTATE_ID = ore.ID),0,1,(SELECT count(1) FROM OB_REAL_ESTATE_USED_USER oreuu2 WHERE OREUU2.REAL_ESTATE_ID = ore.ID)) AS "sharedArea"
        FROM
        js_sys_office jso
        LEFT JOIN JS_SYS_EMPLOYEE jse ON
        jse.OFFICE_CODE = jso.OFFICE_CODE
        LEFT JOIN js_sys_user jsu ON
        jsu.REF_CODE = jse.EMP_CODE
        LEFT JOIN OB_REAL_ESTATE_USED_USER oreuu ON
        oreuu.USER_CODE = jse.EMP_CODE
        LEFT JOIN ob_real_estate ore ON
        ore.id = OREUU.REAL_ESTATE_ID
        where 1=1
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="userName != null and userName != ''">
            AND jsu.USER_NAME like '%${userName}%'
        </if>
        <if test="rank != null and rank != ''">
            AND jse.ESTABLISHMENT_TYPE = '${rank}'
        </if>
        GROUP BY jsu.USER_NAME,jsu.USER_CODE,jso.OFFICE_CODE,jso.OFFICE_NAME,jse.ESTABLISHMENT_TYPE,ore."TYPE",ore.ID
        HAVING (CASE
        WHEN SUM(ore.AREA) IS NULL THEN 0
        ELSE SUM(ore.AREA)
        END)>0
    </select>
    <select id="unitAreaExcessRecordListData"
            resultType="com.hsobs.ob.modules.earlywarnmessage.entity.UnitAreaExcessRecord">
        SELECT
            jso.OFFICE_NAME AS "officeName",
            jso.OFFICE_CODE AS "officeCode",
            jse.ESTABLISHMENT_TYPE AS "rank",
            jso.OFFICE_TYPE AS "officeType",
            ore."TYPE" AS "officeRoomType",
            COALESCE(CASE WHEN ore."TYPE" =0 then
                     (SELECT ooe.province_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =30)
                         + (SELECT ooe.province_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =40)
                         + (SELECT ooe.bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =50)
                         + (SELECT ooe.deputy_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =60)
                         + (SELECT ooe.division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =70)
                         + (SELECT ooe.deputy_division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =80)
                         + (SELECT ooe.below_division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =90)
                         + (SELECT ooe.municipal_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =91)
                         + (SELECT ooe.municipal_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =92)
                         + (SELECT ooe.municipal_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =93)
                         + (SELECT ooe.municipal_deputy_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =94)
                         + (SELECT ooe.below_municipal_bureau FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =95)
                         + (SELECT ooe.county_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =96)
                         + (SELECT ooe.county_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =97)
                         + (SELECT ooe.section_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =98)
                         + (SELECT ooe.deputy_section_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =99)
                         + (SELECT ooe.below_section_level FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =100)
                         + (SELECT ooe.township_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =101)
                         + (SELECT ooe.township_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =102)
                         + (SELECT ooe.below_township_level FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =103)
                 WHEN ore."TYPE" =1 then
                     (CASE WHEN jso.EXTERNAL_STAFF &lt; 200  THEN jso.EXTERNAL_STAFF*(SELECT osrac.min_area FROM ob_service_rooms_approved_config osrac WHERE osrac.ID = jso.OFFICE_TYPE)
                           WHEN jso.EXTERNAL_STAFF &gt; 400 THEN jso.EXTERNAL_STAFF*(SELECT osrac.MAX_AREA FROM ob_service_rooms_approved_config osrac WHERE osrac.ID = jso.OFFICE_TYPE)
                           ELSE (1100-jso.EXTERNAL_STAFF)/100 END)
                 WHEN ore."TYPE" =2 then ((CASE WHEN jso.EXTERNAL_STAFF &lt; 200  THEN jso.EXTERNAL_STAFF*(SELECT osrac.min_area FROM ob_service_rooms_approved_config osrac WHERE osrac.ID = jso.OFFICE_TYPE)
                                                WHEN jso.EXTERNAL_STAFF &gt; 400 THEN jso.EXTERNAL_STAFF*(SELECT osrac.MAX_AREA FROM ob_service_rooms_approved_config osrac WHERE osrac.ID = jso.OFFICE_TYPE)
                                                ELSE (1100-jso.EXTERNAL_STAFF)/100 END)+(SELECT ooe.province_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =30)
                     + (SELECT ooe.province_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =40)
                     + (SELECT ooe.bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =50)
                     + (SELECT ooe.deputy_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =60)
                     + (SELECT ooe.division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =70)
                     + (SELECT ooe.deputy_division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =80)
                     + (SELECT ooe.below_division_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =90)
                     + (SELECT ooe.municipal_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =91)
                     + (SELECT ooe.municipal_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =92)
                     + (SELECT ooe.municipal_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =93)
                     + (SELECT ooe.municipal_deputy_bureau_director FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =94)
                     + (SELECT ooe.below_municipal_bureau FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =95)
                     + (SELECT ooe.county_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =96)
                     + (SELECT ooe.county_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =97)
                     + (SELECT ooe.section_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =98)
                     + (SELECT ooe.deputy_section_chief FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =99)
                     + (SELECT ooe.below_section_level FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =100)
                     + (SELECT ooe.township_positive FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =101)
                     + (SELECT ooe.township_deputy FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =102)
                     + (SELECT ooe.below_township_level FROM OB_OFFICE_ESTABLISHMENT ooe WHERE ooe.id=jso.OFFICE_ESTABLISHMENT_ID)*(SELECT ooac.area AS "area" FROM ob_office_approved_config ooac WHERE ooac.ID =103))*(SELECT oerac.equipment_room_ratio AS "equipmentRoomRatio" FROM ob_equipment_rooms_approved_config oerac WHERE oerac.ID =2)/100
                 WHEN ore."TYPE" =4 then jso.TECHNICAL_BUSINESS_AREA
                 ELSE 0 END,0) AS "approvedArea",
            CASE WHEN sum(ore.AREA) IS NULL THEN 0 ELSE sum(ore.AREA) END AS "actualArea",
            count(1) AS "number"
        FROM
            js_sys_office jso
                LEFT JOIN JS_SYS_EMPLOYEE jse ON
                jse.OFFICE_CODE = jso.OFFICE_CODE
                LEFT JOIN ob_real_estate ore ON ore.USED_OFFICE_CODE = jso.OFFICE_CODE
                LEFT JOIN OB_OFFICE_ESTABLISHMENT oe ON
                jso.office_establishment_id = oe.id
        where 1=1
        <if test="officeCode != null and officeCode != ''">
            AND jso.office_code = '${officeCode}'
        </if>
        <if test="officeType != null and officeType != ''">
            AND jso.OFFICE_TYPE = '${officeType}'
        </if>
        <if test="officeRoomType != null and officeRoomType != ''">
            AND ore."TYPE" = '${officeRoomType}'
        </if>
        <if test="rank != null and rank != ''">
            AND jse.ESTABLISHMENT_TYPE = '${rank}'
        </if>
        GROUP BY jso.OFFICE_NAME,jso.OFFICE_CODE,jse.ESTABLISHMENT_TYPE,ore."TYPE",jso.OFFICE_ESTABLISHMENT_ID,jso.OFFICE_TYPE,jso.EXTERNAL_STAFF,jso.TECHNICAL_BUSINESS_AREA,jso.OFFICE_TYPE
        HAVING (CASE
        WHEN SUM(ore.AREA) IS NULL THEN 0
        ELSE SUM(ore.AREA)
        END)>0
    </select>
</mapper>