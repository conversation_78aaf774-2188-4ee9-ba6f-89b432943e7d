<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.datastatistics.dao.DataStatisticsDao">

	<!-- 查询数据
	<select id="findList" resultType="DataStatistics">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

    <select id="countDisposalNumber" resultType="com.hsobs.ob.modules.datastatistics.entity.DisposeTable">
        SELECT
            TO_CHAR(odu.CREATE_DATE, 'YYYY') AS "yearCount",
            jsa.AREA_NAME AS "areaName",
            jso.OFFICE_NAME AS "officeName",
            odu.DISPOSAL_TYPE AS "disposeType",
            COUNT(1) AS "disposeNumber",
            SUM(ore.AREA)  AS "disposeArea"
        FROM
            ob_disposal_utilization_management odu
            LEFT JOIN JS_SYS_EMPLOYEE jse ON
            odu.APPLICANT_ID = jse.EMP_CODE
            LEFT JOIN JS_SYS_OFFICE jso ON
            jse.OFFICE_CODE = JSO.OFFICE_CODE
            LEFT JOIN OB_REAL_ESTATE ore ON
            ore.ID = ROOM_ID
            LEFT JOIN ob_real_estate_address rea ON
            ore.real_estate_address_id = rea.id
            LEFT JOIN js_sys_area jsa ON
            jsa.AREA_CODE = rea.REGION
        WHERE 1=1 ${sqlMap.dsfOffice}
        <if test="year != null and year != ''">
            AND TO_CHAR(odu.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        <if test="office != null and office != ''">
            AND jso.OFFICE_CODE = '${office}'
        </if>
        <if test="region != null and region != ''">
            AND rea.REGION = '${region}'
        </if>
        GROUP BY
            jso.OFFICE_NAME ,jsa.AREA_NAME,odu.DISPOSAL_TYPE,
            TO_CHAR(odu.CREATE_DATE, 'YYYY')
    </select>
    <select id="countSupervisionNumber"
            resultType="com.hsobs.ob.modules.datastatistics.entity.SupervisionTable">
        SELECT
            jsa.AREA_NAME AS "areaName",
            jso.OFFICE_NAME AS "officeName",
            (
                SELECT a.tree_names AS "treeNames"
                FROM
                    js_sys_dict_data a
                WHERE
                    a.status != 1
                AND a.parent_code = 0
                AND a.DICT_VALUE = osht.TASK_SOURCE
                AND a.dict_type = 'supervise_handling_task_source'
                AND ( a.is_sys = 1
                OR a.corp_code = 0 )) AS "businessType",
            COUNT(1) AS "SpotNumber",
        (
        SELECT COUNT(1)
        FROM
        ob_supervise_handling_tasks obsht
        WHERE
        (obsht.TASK_STATUS = 2
        OR obsht.TASK_STATUS = 3)
        AND obsht.office_code = osht.office_code AND obsht.TASK_SOURCE = osht.TASK_SOURCE AND obsht.status = 0) AS "questionNumber",
        (
        SELECT COUNT(1)
        FROM
        ob_supervise_handling_tasks obsht
        WHERE
        obsht.TASK_STATUS = 3
        AND obsht.office_code = osht.office_code AND obsht.TASK_SOURCE = osht.TASK_SOURCE AND obsht.status = 0) AS "correctedNumber",
        (
        SELECT COUNT(1)
        FROM
        ob_supervise_handling_tasks obsht
        WHERE
        (obsht.TASK_STATUS = 2)
        AND obsht.office_code = osht.office_code AND obsht.TASK_SOURCE = osht.TASK_SOURCE AND obsht.status = 0) AS "uncorrectedNumber"
        FROM
            ob_supervise_handling_tasks osht
            LEFT JOIN JS_SYS_OFFICE jso ON
            osht.OFFICE_CODE = JSO.OFFICE_CODE
            LEFT JOIN js_sys_area jsa ON
            jsa.AREA_CODE = jso.REGION
        WHERE osht.status = 0 ${sqlMap.dsfOffice}
        <if test="office != null and office != ''">
            AND jso.OFFICE_CODE = '${office}'
        </if>
        <if test="region != null and region != ''">
            AND jso.REGION = '${region}'
        </if>
        <if test="occupancyClassification != null and occupancyClassification != ''">
            AND osht.occupancy_classification = '${occupancyClassification}'
        </if>
        <if test="dateGte != null and dateGte != ''">
            AND osht.CREATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
        </if>
        <if test="dateLte != null and dateLte != ''">
            AND osht.CREATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
        </if>
        GROUP BY
            jso.OFFICE_NAME,
            osht.TASK_SOURCE,
            osht.office_code,
            jsa.AREA_NAME
    </select>
    <select id="officeOccupancy" resultType="java.util.Map">
        SELECT
            osht.occupancy_classification,
            COUNT(1) AS "SpotNumber",
        (
        SELECT
        COUNT(1)
        FROM
        ob_supervise_handling_tasks obsht
        WHERE
        (obsht.TASK_STATUS = 2
        OR obsht.TASK_STATUS = 3)
        AND obsht.office_code = osht.office_code
        AND obsht.OCCUPANCY_CLASSIFICATION = osht.OCCUPANCY_CLASSIFICATION
        AND obsht.status = 0) AS "questionNumber",
        (
        SELECT
        COUNT(1)
        FROM
        ob_supervise_handling_tasks obsht
        WHERE
        obsht.TASK_STATUS = 3
        AND obsht.office_code = osht.office_code
        AND obsht.OCCUPANCY_CLASSIFICATION = osht.OCCUPANCY_CLASSIFICATION
        AND obsht.status = 0) AS "correctedNumber",
        (
        SELECT
        COUNT(1)
        FROM
        ob_supervise_handling_tasks obsht
        WHERE
        (obsht.TASK_STATUS = 2)
        AND obsht.office_code = osht.office_code
        AND obsht.OCCUPANCY_CLASSIFICATION = osht.OCCUPANCY_CLASSIFICATION
        AND obsht.status = 0) AS "uncorrectedNumber"
        FROM
            ob_supervise_handling_tasks osht
                LEFT JOIN JS_SYS_OFFICE jso ON
                osht.OFFICE_CODE = JSO.OFFICE_CODE
        WHERE
            osht.status = 0 ${sqlMap.dsfOffice}
        <if test="office != null and office != ''">
            AND jso.OFFICE_CODE = '${office}'
        </if>
        <if test="region != null and region != ''">
            AND jso.REGION = '${region}'
        </if>
        <if test="occupancyClassification != null and occupancyClassification != ''">
            AND osht.occupancy_classification = '${occupancyClassification}'
        </if>
        <if test="dateGte != null and dateGte != ''">
            AND osht.CREATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
        </if>
        <if test="dateLte != null and dateLte != ''">
            AND osht.CREATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
        </if>
        GROUP BY
            osht.occupancy_classification,
            osht.office_code
    </select>
    <select id="findResourceCaseData" resultType="com.hsobs.ob.modules.datastatistics.entity.ResourceCase">
        SELECT
            o.office_name AS "officeName",
        o.OFFICE_TYPE AS "officeType",
            rea.ADDRESS AS "address",
            rea.FLOOR_COUNT AS "floorNumber",
            (SELECT count(1) FROM JS_SYS_EMPLOYEE jse WHERE jse.OFFICE_CODE = o.OFFICE_CODE AND jse.ESTABLISHMENT_TYPE='30') AS "principalNumber",
            (SELECT count(1) FROM JS_SYS_EMPLOYEE jse WHERE jse.OFFICE_CODE = o.OFFICE_CODE AND jse.ESTABLISHMENT_TYPE='40') AS "deputyNumber",
            (SELECT count(1) FROM JS_SYS_EMPLOYEE jse WHERE jse.OFFICE_CODE = o.OFFICE_CODE AND jse.ESTABLISHMENT_TYPE='50') AS "bureauNumber",
            (SELECT count(1) FROM JS_SYS_EMPLOYEE jse WHERE jse.OFFICE_CODE = o.OFFICE_CODE AND jse.ESTABLISHMENT_TYPE='60') AS "deputyBureauNumber",
            (SELECT count(1) FROM JS_SYS_EMPLOYEE jse WHERE jse.OFFICE_CODE = o.OFFICE_CODE AND jse.ESTABLISHMENT_TYPE='70') AS "divisionNumber",
            (SELECT count(1) FROM JS_SYS_EMPLOYEE jse WHERE jse.OFFICE_CODE = o.OFFICE_CODE AND jse.ESTABLISHMENT_TYPE='80') AS "deputyDivisionNumber",
            (SELECT count(1) FROM JS_SYS_EMPLOYEE jse WHERE jse.OFFICE_CODE = o.OFFICE_CODE AND (jse.ESTABLISHMENT_TYPE='90' OR  jse.ESTABLISHMENT_TYPE IS null) ) AS "underDivisionNumber",
            (SELECT count(1) FROM JS_SYS_EMPLOYEE jse WHERE jse.OFFICE_CODE = o.OFFICE_CODE) AS "staffingTotalNumber",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore WHERE ore."TYPE" = '0' AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "officeSpace",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore WHERE ore."TYPE" = '1' AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "serviceSpace",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore WHERE ore."TYPE" = '2' AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "deviceSpace",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore WHERE (ore."TYPE" = '0' OR ore."TYPE" = '1' OR ore."TYPE" = '2') AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "baseTotalSpace",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore WHERE ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "structureTotalSpace",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore WHERE (ore."TYPE" = '0' OR ore."TYPE" = '1' OR ore."TYPE" = '2') AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "officeNowSpace",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore WHERE ore."TYPE" = '3' AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "auxiliarySpace",
            (SELECT sum(ore.AREA) FROM ob_real_estate ore WHERE ore."TYPE" = '4' AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "technologySpace",
            oo.OFFICE_NAME AS "propertyUnit"
        FROM
            ob_real_estate a
                LEFT JOIN js_sys_office o ON
                a.USED_OFFICE_CODE = o.office_code
                LEFT JOIN ob_real_estate_address rea ON
                a.real_estate_address_id = rea.id
                LEFT JOIN js_sys_office oo ON
                a.USED_OFFICE_CODE = oo.office_code
                LEFT JOIN JS_SYS_EMPLOYEE u ON
                A.USED_USER_CODE = u.COMPANY_CODE
        WHERE
        1=1 and rea.id IS NOT NULL AND a."TYPE" IS NOT NULL
        <if test="officeCode != null and officeCode != ''">
            AND o.OFFICE_CODE = '${officeCode}'
        </if>
        <if test="officeType != null and officeType != ''">
            AND a."TYPE" = '${officeType}'
        </if>
        GROUP BY o.office_name,o.office_code,o.OFFICE_TYPE,rea.ADDRESS,rea.FLOOR_COUNT,oo.OFFICE_NAME
    </select>
    <select id="findSpaceVerificationData" resultType="com.hsobs.ob.modules.datastatistics.entity.SpaceVerification">
        SELECT
        o.office_name AS "officeName",
        o.OFFICE_TYPE AS "officeType",
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) AS "staffingTotalNumber",
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '30')*54 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '40')*42 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '50')*30 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '60')*24 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '70')*18 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '80')*12 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND (jse.ESTABLISHMENT_TYPE = '90'
        OR jse.ESTABLISHMENT_TYPE IS NULL) )*9 AS "officeSpace",
        CASE
        WHEN(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &lt; 200  THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*9
        WHEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &gt; 400 THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*7
        ELSE (1100-(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE))/100
        END AS "serviceSpace",
        ((
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '30')*54 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '40')*42 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '50')*30 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '60')*24 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '70')*18 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '80')*12 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND (jse.ESTABLISHMENT_TYPE = '90'
        OR jse.ESTABLISHMENT_TYPE IS NULL) )*9 +
        CASE
        WHEN(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &lt; 200  THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*9
        WHEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &gt; 400 THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*7
        ELSE (1100-(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE))/100
        END)*0.09 AS "deviceSpace",
        ((
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '30')*54 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '40')*42 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '50')*30 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '60')*24 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '70')*18 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '80')*12 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND (jse.ESTABLISHMENT_TYPE = '90'
        OR jse.ESTABLISHMENT_TYPE IS NULL) )*9 +
        CASE
        WHEN(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &lt; 200  THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*9
        WHEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &gt; 400 THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*7
        ELSE (1100-(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE))/100
        END +
        ((
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '30')*54 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '40')*42 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '50')*30 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '60')*24 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '70')*18 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '80')*12 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND (jse.ESTABLISHMENT_TYPE = '90'
        OR jse.ESTABLISHMENT_TYPE IS NULL) )*9 +
        CASE
        WHEN(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &lt; 200 THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*9
        WHEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &gt; 400 THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*7
        ELSE (1100-(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE))/100
        END)*0.09) AS "baseTotalSpace",
        (((
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '30')*54 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '40')*42 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '50')*30 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '60')*24 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '70')*18 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '80')*12 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND (jse.ESTABLISHMENT_TYPE = '90'
        OR jse.ESTABLISHMENT_TYPE IS NULL) )*9 +
        CASE
        WHEN(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &lt; 200 THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*9
        WHEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &gt; 400 THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*7
        ELSE (1100-(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE))/100
        END +
        ((
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '30')*54 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '40')*42 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '50')*30 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '60')*24 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '70')*18 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND jse.ESTABLISHMENT_TYPE = '80')*12 +
        (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE
        AND (jse.ESTABLISHMENT_TYPE = '90'
        OR jse.ESTABLISHMENT_TYPE IS NULL) )*9 +
        CASE
        WHEN(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &lt; 200  THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*9
        WHEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE) &gt; 400 THEN (
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE)*7
        ELSE (1100-(
        SELECT COUNT(1)
        FROM
        JS_SYS_EMPLOYEE jse
        WHERE
        jse.OFFICE_CODE = o.OFFICE_CODE))/100
        END)*0.09)/0.6) AS "structureTotalSpace",
        (
        SELECT sum(ore.AREA)
        FROM
        ob_real_estate ore
        WHERE
        (ore."TYPE" = '0'
        OR ore."TYPE" = '1'
        OR ore."TYPE" = '2')
        AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "officeSiteSpace",
        (
        SELECT sum(ore.AREA)
        FROM
        ob_real_estate ore
        WHERE
        (ore."TYPE" = '3'
        OR ore."TYPE" = '4')
        AND ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "businessSiteSpace",
        (
        SELECT SUM(ore.AREA)
        FROM
        ob_real_estate ore
        WHERE
        ore.USED_OFFICE_CODE = o.OFFICE_CODE) AS "totalSpace"
        FROM
        ob_real_estate a
        LEFT JOIN js_sys_office o ON
        a.USED_OFFICE_CODE = o.office_code
        LEFT JOIN js_sys_office oo ON
        a.USED_OFFICE_CODE = oo.office_code
        LEFT JOIN JS_SYS_EMPLOYEE u ON
        A.USED_USER_CODE = u.COMPANY_CODE
        LEFT JOIN ob_real_estate_address rea ON
        a.real_estate_address_id = rea.id
        WHERE
        1=1 and rea.id IS NOT NULL AND a."TYPE" IS NOT NULL
        <if test="officeCode != null and officeCode != ''">
            AND o.OFFICE_CODE = '${officeCode}'
        </if>
        <if test="officeType != null and officeType != ''">
            AND a."TYPE" = '${officeType}'
        </if>
        GROUP BY o.office_name,o.OFFICE_CODE,o.OFFICE_TYPE,oo.OFFICE_NAME
    </select>
    <select id="officeResourceCout" resultType="java.util.Map">
        SELECT
        a."TYPE" AS "officeType",
            count(1) AS "number"
        FROM
            ob_real_estate a
                LEFT JOIN js_sys_office o ON
                a.USED_OFFICE_CODE = o.office_code
                LEFT JOIN JS_SYS_EMPLOYEE u ON
                A.USED_USER_CODE = u.COMPANY_CODE
                LEFT JOIN ob_real_estate_address rea ON
                a.real_estate_address_id = rea.id
        where 1=1 and rea.id IS NOT NULL AND a."TYPE" IS NOT NULL
        <if test="officeCode != null and officeCode != ''">
            AND o.OFFICE_CODE = '${officeCode}'
        </if>
        <if test="officeType != null and officeType != ''">
            AND a."TYPE" = '${officeType}'
        </if>
        GROUP BY a."TYPE"
    </select>
    <select id="officeResourceNumberCout" resultType="java.util.Map">
        SELECT
        count(1) AS "number",
        sum(a.AREA) AS "area",
        count(DISTINCT(o.OFFICE_CODE)) AS "officeNumber"
        FROM
        ob_real_estate a
        LEFT JOIN js_sys_office o ON
        a.USED_OFFICE_CODE = o.office_code
        LEFT JOIN JS_SYS_EMPLOYEE u ON
        A.USED_USER_CODE = u.COMPANY_CODE
        LEFT JOIN ob_real_estate_address rea ON
        a.real_estate_address_id = rea.id
        where 1=1 and rea.id IS NOT NULL AND a."TYPE" IS NOT NULL
        <if test="officeCode != null and officeCode != ''">
            AND o.OFFICE_CODE = '${officeCode}'
        </if>
        <if test="officeType != null and officeType != ''">
            AND a."TYPE" = '${officeType}'
        </if>
    </select>
    <select id="findOwnershipDataPage"
            resultType="com.hsobs.ob.modules.datastatistics.entity.OwnershipTable">
        SELECT
        COALESCE(NVL(rea.ADDRESS,rea2.ADDRESS),'未知') AS "roomInfo",
        NVL(rea.FLOOR_COUNT,rea2.FLOOR_COUNT) AS "roomNumber",
        o.OFFICE_NAME AS "officeName",
        COALESCE(nvl(a.NAME,rea2.NAME),'未知') AS "roomName",
        oor.CREATE_DATE AS "registerTime",
        oor.status AS "ownershipStatus",
        (SELECT	max(aht.END_TIME_) FROM	ACT_HI_PROCINST RES	LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_ LEFT OUTER JOIN ACT_HI_TASKINST AHT ON AHT.PROC_INST_ID_ = RES.ID_ WHERE RES.BUSINESS_KEY_ LIKE CONCAT('%',oor.id)) AS "auditTime"
        FROM
        ob_ownership_registration oor
        LEFT JOIN OB_OWNERSHIP_REAL_ESTATE oore ON
        oore.OWNERSHIP_ID = oor.ID
        LEFT JOIN ob_real_estate a ON
        oore.REAL_ESTATE_ID = a.id
        LEFT JOIN js_sys_office o ON
        oor.OWNER_OFFICE_CODE = o.office_code
        LEFT JOIN ob_real_estate_address rea ON
        a.real_estate_address_id = rea.id
        LEFT JOIN ob_real_estate_address rea2 ON
        oore.REAL_ESTATE_ID = rea2.id
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = rea.REGION OR jsa.AREA_CODE = rea2.REGION
        WHERE
        1=1 AND oor.STATUS !=1 AND oor.type != 1
        <if test="areaCode != null and areaCode != ''">
            AND rea.REGION = '${areaCode}' OR rea2.REGION ='${areaCode}'
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(oor.CREATE_DATE, 'YYYY') = '${year}'
        </if>
    </select>
    <select id="ownershipCount" resultType="java.util.Map">
        SELECT
        COALESCE(jsa.AREA_NAME,'未知') AS "areaName",
            count(1) AS "number",
            SUM(CASE WHEN oor.status = 4 THEN 1 ELSE 0 END) AS "awaitNumber" ,
            SUM(CASE WHEN oor.status = 0 THEN 1 ELSE 0 END) AS "approvedNumber"
        FROM
        ob_ownership_registration oor
        LEFT JOIN OB_OWNERSHIP_REAL_ESTATE oore ON
        oore.OWNERSHIP_ID = oor.ID
        LEFT JOIN ob_real_estate a ON
        oore.REAL_ESTATE_ID = a.id
        LEFT JOIN js_sys_office o ON
        oor.OWNER_OFFICE_CODE = o.office_code
        LEFT JOIN ob_real_estate_address rea ON
        a.real_estate_address_id = rea.id
        LEFT JOIN ob_real_estate_address rea2 ON
        oore.REAL_ESTATE_ID = rea2.id
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = rea.REGION OR jsa.AREA_CODE = rea2.REGION
        WHERE
        1=1 AND oor.STATUS !=1 AND oor.type != 1
        <if test="areaCode != null and areaCode != ''">
            AND rea.REGION = '${areaCode}' OR rea2.REGION ='${areaCode}'
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(oor.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY jsa.AREA_NAME
    </select>

    <select id="findAllocationDataPage"
            resultType="com.hsobs.ob.modules.datastatistics.entity.AllocationTable">
        SELECT
        CASE
        WHEN a.REAL_ESTATE_TYPE = '1' THEN orea.address
        ELSE b.ADDRESS
        END AS "roomInfo",
        CASE
        WHEN a.REAL_ESTATE_TYPE = '1' THEN orea.floor_count
        ELSE b.floor_count
        END AS "roomNumber",
        o.OFFICE_NAME AS "officeName",
        a.ARRANGE_TYPE AS "arrangeType",
        CASE
        WHEN a.REAL_ESTATE_TYPE = '1' THEN orea.NAME
        ELSE b.NAME
        END AS "roomName",
        a.create_date AS "applyTime",
        a.status AS "allocationStatus",
        (SELECT	max(aht.END_TIME_) FROM	ACT_HI_PROCINST RES	LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_ LEFT OUTER JOIN ACT_HI_TASKINST AHT ON AHT.PROC_INST_ID_ = RES.ID_ WHERE RES.BUSINESS_KEY_ LIKE CONCAT('%',a.id)) AS "auditTime"
        FROM
        ob_arrange a
        LEFT JOIN js_sys_office o ON
        o.office_code = a.USED_OFFICE_CODE
        LEFT JOIN ob_arrange_real_estate oar ON
        oar.ARRANGE_ID = a.ID
        LEFT JOIN ob_real_estate ore ON
        ore.ID = oar.REAL_ESTATE_ID
        LEFT JOIN ob_real_estate_address orea ON
        oar.REAL_ESTATE_ID = orea.ID
        LEFT JOIN ob_real_estate_address b ON
        b.id = ore.real_estate_address_id
        LEFT JOIN js_sys_office oo ON
        a.USED_OFFICE_CODE = oo.office_code
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = b.REGION OR jsa.AREA_CODE =orea.REGION
        WHERE
        1=1
        <if test="areaCode != null and areaCode != ''">
            AND (b.REGION = '${areaCode}' OR orea.REGION ='${areaCode}')
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(a.CREATE_DATE, 'YYYY') = '${year}'
        </if>
    </select>
    <select id="allocationCountData" resultType="java.util.Map">
        SELECT
        a.ARRANGE_TYPE AS "arrangeType",
        count(1) AS "number"
        FROM
        ob_arrange a
        LEFT JOIN js_sys_office o ON
        o.office_code = a.USED_OFFICE_CODE
        LEFT JOIN ob_arrange_real_estate oar ON
        oar.ARRANGE_ID = a.ID
        LEFT JOIN ob_real_estate ore ON
        ore.ID = oar.REAL_ESTATE_ID
        LEFT JOIN ob_real_estate_address orea ON
        oar.REAL_ESTATE_ID = orea.ID
        LEFT JOIN ob_real_estate_address b ON
        b.id = ore.real_estate_address_id
        LEFT JOIN js_sys_office oo ON
        a.USED_OFFICE_CODE = oo.office_code
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = b.REGION OR jsa.AREA_CODE =orea.REGION
        WHERE
        1=1
        <if test="areaCode != null and areaCode != ''">
            AND (b.REGION = '${areaCode}' OR orea.REGION ='${areaCode}')
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(a.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY a.ARRANGE_TYPE
    </select>
    <select id="allocationAreaCountData" resultType="java.util.Map">
        SELECT
        COALESCE(jsa.AREA_NAME,'未知') AS "areaName",
        count(1) AS "number",
        SUM(CASE WHEN a.status = 4 THEN 1 ELSE 0 END) AS "awaitNumber" ,
        SUM(CASE WHEN a.status = 0 THEN 1 ELSE 0 END) AS "approvedNumber",
        (count(1)-SUM(CASE WHEN YEAR(a.CREATE_DATE) = YEAR(DATEADD(YEAR, -1, CURRENT_DATE)) THEN 1 ELSE 0 END))/COALESCE(NULLIF(SUM(CASE WHEN YEAR(a.CREATE_DATE) = YEAR(DATEADD(YEAR, -1, CURRENT_DATE)) THEN 1 ELSE 0 END),0),1)*100 AS "preNumber"
        FROM
        ob_arrange a
        LEFT JOIN js_sys_office o ON
        o.office_code = a.USED_OFFICE_CODE
        LEFT JOIN ob_arrange_real_estate oar ON
        oar.ARRANGE_ID = a.ID
        LEFT JOIN ob_real_estate ore ON
        ore.ID = oar.REAL_ESTATE_ID
        LEFT JOIN ob_real_estate_address orea ON
        oar.REAL_ESTATE_ID = orea.ID
        LEFT JOIN ob_real_estate_address b ON
        b.id = ore.real_estate_address_id
        LEFT JOIN js_sys_office oo ON
        a.USED_OFFICE_CODE = oo.office_code
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = b.REGION OR jsa.AREA_CODE =orea.REGION
        WHERE
        1=1
        <if test="areaCode != null and areaCode != ''">
            AND (b.REGION = '${areaCode}' OR orea.REGION ='${areaCode}')
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(a.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY jsa.AREA_NAME
    </select>
    <select id="findMaintainPage" resultType="com.hsobs.ob.modules.datastatistics.entity.MaintainTable">
        SELECT
        b.ADDRESS as "roomInfo",
        b.FLOOR_COUNT as "roomNumber",
        c.NAME as "roomName",
        o.OFFICE_NAME as "officeName",
        a.status as "maintainStatus",
        a.create_date as "applyTime",
        (SELECT	max(aht.END_TIME_) FROM	ACT_HI_PROCINST RES	LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_ LEFT OUTER JOIN ACT_HI_TASKINST AHT ON AHT.PROC_INST_ID_ = RES.ID_ WHERE RES.BUSINESS_KEY_ LIKE CONCAT('%',a.id)) AS "auditTime"
        FROM
        ob_repair_request a
        LEFT JOIN ob_real_estate c ON
        c.id = a.real_estate_id
        LEFT JOIN ob_real_estate_address b ON
        b.id = c.real_estate_address_id
        LEFT JOIN js_sys_office o ON
        o.office_code = c.USED_OFFICE_CODE
        WHERE 1=1  AND a.status != 1
        <if test="areaCode != null and areaCode != ''">
            AND b.REGION = '${areaCode}'
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(a.CREATE_DATE, 'YYYY') = '${year}'
        </if>
    </select>
    <select id="findMaintainCount" resultType="java.util.Map">
        SELECT
        COALESCE(jsa.AREA_NAME,'未知') AS "areaName",
        count(1) AS "number",
        SUM(CASE WHEN a.status = 4 THEN 1 ELSE 0 END) AS "awaitNumber" ,
        SUM(CASE WHEN a.status = 0 THEN 1 ELSE 0 END) AS "approvedNumber"
        FROM
        ob_repair_request a
        LEFT JOIN ob_real_estate c ON
        c.id = a.real_estate_id
        LEFT JOIN ob_real_estate_address b ON
        b.id = c.real_estate_address_id
        LEFT JOIN js_sys_office o ON
        o.office_code = c.OWNER_OFFICE_CODE
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = b.REGION
        WHERE 1=1  AND a.status != 1
        <if test="areaCode != null and areaCode != ''">
            AND b.REGION = '${areaCode}'
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(a.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY jsa.AREA_NAME
    </select>
    <select id="maintainNumberCountData" resultType="java.util.Map">
        SELECT
            a.type AS "type",
            count(1) AS "number",
            sum(a.BUDGET) as "total"
        FROM
            ob_repair_request a
                LEFT JOIN ob_real_estate c ON
                c.id = a.real_estate_id
                LEFT JOIN ob_real_estate_address b ON
                b.id = c.real_estate_address_id
                LEFT JOIN js_sys_office o ON
                o.office_code = c.OWNER_OFFICE_CODE
                LEFT JOIN js_sys_area jsa ON
                jsa.AREA_CODE = b.REGION
        WHERE 1=1  AND a.status != 1
        <if test="areaCode != null and areaCode != ''">
            AND b.REGION = '${areaCode}'
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(a.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY a.type
    </select>
    <select id="findUnusePage" resultType="com.hsobs.ob.modules.datastatistics.entity.UnuseTable">
        SELECT
        COALESCE(jsa.AREA_NAME,'未知') AS "areaName",
            o.OFFICE_NAME AS "officeName",
            count(1) AS "officeNumber",
            a.update_date AS "unuseDate",
            SUM(CASE WHEN A.USED_USER_CODE IS NULL THEN 1 ELSE 0 END) AS "unuseNumber",
            sum(CASE WHEN A.USED_USER_CODE IS NULL THEN a.AREA ELSE 0 END) AS "unuseSpace",
            ROUND(CAST(SUM(CASE WHEN A.USED_USER_CODE IS NULL THEN 1 ELSE 0 END) AS FLOAT) / COUNT(1) * 100, 2) AS "unuseRate"
        FROM
            ob_real_estate a
                LEFT JOIN js_sys_office o ON
                a.USED_OFFICE_CODE = o.office_code
                LEFT JOIN ob_real_estate_address b ON
                b.id = a.real_estate_address_id
                LEFT JOIN js_sys_area jsa ON
                jsa.AREA_CODE = b.REGION
        WHERE 1=1 ${sqlMap.dsfOffice}
        <if test="areaCode != null and areaCode != ''">
            AND b.REGION = '${areaCode}'
        </if>
        <if test="officeCode != null and officeCode != ''">
            AND o.OFFICE_CODE = '${officeCode}'
        </if>
        <if test="dateGte != null and dateGte != ''">
            AND a.UPDATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
        </if>
        <if test="dateLte != null and dateLte != ''">
            AND a.UPDATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
        </if>
        GROUP BY jsa.AREA_NAME,o.OFFICE_NAME,a.update_date HAVING SUM(CASE WHEN A.USED_USER_CODE IS NULL THEN 1 ELSE 0 END) >0
    </select>

    <select id="calculateUsageAreaRatioByUnit" resultType="java.util.Map">
        SELECT
        COALESCE(o.OFFICE_NAME,'未知') AS "areaName",
        COALESCE(sum(re.AREA),0) AS "area"
        FROM
        ob_apply a
        LEFT JOIN ob_apply_real_estate oare ON
        oare.APPLY_ID = a.ID
        LEFT JOIN ob_real_estate re ON
        re.id = oare.real_estate_id
        LEFT JOIN ob_real_estate_address rea ON
        rea.id = re.real_estate_address_id
        LEFT JOIN js_sys_office o ON
        o.office_code = a.USED_OFFICE_CODE
        LEFT JOIN js_sys_user abu ON
        abu.user_code = a.USED_USER_CODE
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = rea.REGION
        LEFT JOIN ob_real_estate_address rea2 ON
        oare.REAL_ESTATE_ID = rea2.id
        WHERE 1=1
        <if test="areaCode != null and areaCode != ''">
            AND (rea.REGION = '${areaCode}' or rea2.REGION = '${areaCode}')
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(oa.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY o.OFFICE_NAME
    </select>

    <select id="calculateUsageAreaRatioByRank" resultType="java.util.Map">
        SELECT
        COALESCE(d.DICT_LABEL,'未知') AS "type",
        (CASE WHEN sum(ore.AREA) IS NOT null THEN sum(ore.AREA) ELSE 0 END) AS "area"
        FROM
        ob_apply oa
        LEFT JOIN OB_APPLY_REAL_ESTATE oar ON
        oar.APPLY_ID = oa.ID
        LEFT JOIN ob_real_estate ore ON
        ore.ID = oar.REAL_ESTATE_ID
        LEFT JOIN ob_real_estate_address rea ON
        ore.REAL_ESTATE_ADDRESS_ID = rea.id
        LEFT JOIN ob_real_estate_address rea2 ON
        oar.REAL_ESTATE_ID = rea2.id
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = rea.REGION
        OR jsa.AREA_CODE = rea2.REGION
        LEFT JOIN JS_SYS_EMPLOYEE jse ON
        jse.EMP_CODE = oa.USED_USER_CODE
        LEFT JOIN js_sys_dict_data d ON d.dict_type = 'ob_establishment_type' AND d.dict_value = jse.ESTABLISHMENT_TYPE AND d.status != '1'
        WHERE oa.STATUS !=1
        <if test="areaCode != null and areaCode != ''">
            AND (rea.REGION = '${areaCode}' or rea2.REGION = '${areaCode}')
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(oa.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY d.DICT_LABEL
    </select>

    <select id="analyzeOfficeSpaceUsageStatus" resultType="java.util.Map">
        SELECT
        COALESCE(o.office_name,'未知') AS "areaName",
        COALESCE(SUM(CASE WHEN ore."TYPE" = 0 THEN ore.AREA ELSE 0 END),0) AS "officeArea" ,
        COALESCE(SUM(CASE WHEN ore."TYPE" = 1 THEN ore.AREA ELSE 0 END),0) AS "serviceArea" ,
        COALESCE(SUM(CASE WHEN ore."TYPE" = 2 THEN ore.AREA ELSE 0 END),0) AS "equipmentArea" ,
        COALESCE(SUM(CASE WHEN ore."TYPE" = 3 THEN ore.AREA ELSE 0 END),0) AS "techArea" ,
        COALESCE(SUM(CASE WHEN ore."TYPE" = 4 THEN ore.AREA ELSE 0 END),0) AS "auxiliaryArea"
        FROM
        ob_apply oa
        LEFT JOIN OB_APPLY_REAL_ESTATE oar ON
        oar.APPLY_ID = oa.ID
        LEFT JOIN ob_real_estate ore ON
        ore.ID = oar.REAL_ESTATE_ID
        LEFT JOIN js_sys_office o ON
        oa.USED_OFFICE_CODE = o.office_code
        LEFT JOIN ob_real_estate_used_user oruu ON
        oruu.REAL_ESTATE_ID = oar.REAL_ESTATE_ID
        LEFT JOIN JS_SYS_EMPLOYEE u ON
        oruu.USER_CODE = u.COMPANY_CODE
        LEFT JOIN ob_real_estate_address rea ON
        ore.REAL_ESTATE_ADDRESS_ID = rea.id
        LEFT JOIN ob_real_estate_address rea2 ON
        oar.REAL_ESTATE_ID = rea2.id
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = rea.REGION
        OR jsa.AREA_CODE = rea2.REGION
        where 1=1
        <if test="areaCode != null and areaCode != ''">
            AND (rea.REGION = '${areaCode}' or rea2.REGION = '${areaCode}')
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(oa.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY o.office_name
        order by SUM(CASE WHEN ore."TYPE" = 0 THEN ore.AREA ELSE 0 END) desc
    </select>

    <select id="analyzeOfficeSpaceAnalysis" resultType="java.util.Map">
        SELECT
        COALESCE(jsa.AREA_NAME,'未知') AS "areaName",
        COUNT(1) AS "employeeCount",
        sum(a."area") AS "area"
        FROM (
        SELECT
        jse.EMP_CODE,
        orea.REGION,
        SUM(ore.AREA) - (SELECT ooac.area FROM ob_office_approved_config ooac WHERE ooac.ID = jse.ESTABLISHMENT_TYPE)  AS "area"
        FROM
        js_sys_office jso
        LEFT JOIN JS_SYS_EMPLOYEE jse ON jse.OFFICE_CODE = jso.OFFICE_CODE
        LEFT JOIN ob_real_estate_used_user oruu ON oruu.USER_CODE = jse.EMP_CODE
        LEFT JOIN ob_real_estate ore ON ore.id = oruu.REAL_ESTATE_ID
        LEFT JOIN ob_real_estate_address orea ON orea.ID = ore.REAL_ESTATE_ADDRESS_ID
        WHERE 1=1
        <if test="areaCode != null and areaCode != ''">
            AND orea.REGION = '${areaCode}'
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(ore.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY
        jse.EMP_CODE,
        orea.REGION,
        jse.ESTABLISHMENT_TYPE,
        ore."TYPE"
        HAVING
        (SELECT ooac.area FROM ob_office_approved_config ooac WHERE ooac.ID = jse.ESTABLISHMENT_TYPE) - SUM(ore.AREA) &lt; 0
        ) a
        LEFT JOIN js_sys_area jsa ON a.REGION = jsa.AREA_CODE

        GROUP BY
        jsa.AREA_NAME
    </select>

    <select id="officeUsageStats" resultType="java.util.Map">
        SELECT
        COUNT(1) AS "number",
        SUM(CASE WHEN oa.status = 4 THEN 1 ELSE 0 END) AS "awaitNumber",
        (select count(1) FROM ob_apply_vouchers oav) AS "issueNumber",
        SUM(CASE WHEN oa.status = 0 THEN 1 ELSE 0 END) AS "availableNumber"
        FROM
        ob_apply oa
        LEFT JOIN OB_APPLY_REAL_ESTATE oar ON
        oar.APPLY_ID = oa.ID
        LEFT JOIN ob_real_estate a ON
        a.ID = oar.REAL_ESTATE_ID
        LEFT JOIN js_sys_office o ON
        a.OWNER_OFFICE_CODE = o.office_code
        LEFT JOIN ob_real_estate_used_user oruu ON
        oruu.REAL_ESTATE_ID = oar.REAL_ESTATE_ID
        LEFT JOIN JS_SYS_EMPLOYEE u ON
        oruu.USER_CODE = u.COMPANY_CODE
        LEFT JOIN ob_real_estate_address rea ON
        a.REAL_ESTATE_ADDRESS_ID = rea.id
        LEFT JOIN ob_real_estate_address rea2 ON
        oar.REAL_ESTATE_ID = rea2.id
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = rea.REGION
        OR jsa.AREA_CODE = rea2.REGION
        where 1=1
        <if test="areaCode != null and areaCode != ''">
            AND (rea.REGION = '${areaCode}' or rea2.REGION = '${areaCode}')
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(oa.CREATE_DATE, 'YYYY') = '${year}'
        </if>
    </select>
    <select id="allocationAreaCountBarData" resultType="java.util.Map">
        SELECT
        COALESCE(jsa.AREA_NAME,'未知') AS "areaName",
        count(1) AS "number",
        SUM(CASE WHEN a.ARRANGE_TYPE = 1 THEN 1 ELSE 0 END) AS "adjustmentQuantity" ,
        SUM(CASE WHEN a.ARRANGE_TYPE = 2 THEN 1 ELSE 0 END) AS "replacementQuantity" ,
        SUM(CASE WHEN a.ARRANGE_TYPE = 3 THEN 1 ELSE 0 END) AS "rentalQuantity" ,
        SUM(CASE WHEN a.ARRANGE_TYPE = 4 THEN 1 ELSE 0 END) AS "constructionQuantity"
        FROM
        ob_arrange a
        LEFT JOIN js_sys_office o ON
        o.office_code = a.USED_OFFICE_CODE
        LEFT JOIN ob_arrange_real_estate oar ON
        oar.ARRANGE_ID = a.ID
        LEFT JOIN ob_real_estate ore ON
        ore.ID = oar.REAL_ESTATE_ID
        LEFT JOIN ob_real_estate_address orea ON
        oar.REAL_ESTATE_ID = orea.ID
        LEFT JOIN ob_real_estate_address b ON
        b.id = ore.real_estate_address_id
        LEFT JOIN js_sys_office oo ON
        a.USED_OFFICE_CODE = oo.office_code
        LEFT JOIN js_sys_area jsa ON
        jsa.AREA_CODE = b.REGION OR jsa.AREA_CODE =orea.REGION
        WHERE
        1=1
        <if test="areaCode != null and areaCode != ''">
            AND (b.REGION = '${areaCode}' OR orea.REGION ='${areaCode}')
        </if>
        <if test="year != null and year != ''">
            AND TO_CHAR(a.CREATE_DATE, 'YYYY') = '${year}'
        </if>
        GROUP BY jsa.AREA_NAME
    </select>
</mapper>