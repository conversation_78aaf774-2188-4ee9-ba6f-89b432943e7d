<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.ob.modules.disposalutilizationmanagement.dao.DisposalUtilizationManagementDao">
	

	<select id="listLedgerData" resultType="com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagementLedger">
		SELECT
			o.OFFICE_NAME AS "applicantUnitName",
			a.DISPOSAL_TYPE AS "disposalType",
			rea.ADDRESS AS "disposalAddress",
			sum(ore.AREA) AS "disposalSpace"
		FROM
			ob_disposal_utilization_management a
				LEFT JOIN js_sys_office o ON
				o.office_code = a.APPLICANT_UNIT_ID
				LEFT JOIN ob_real_estate ore ON
				ore.ID = a.ROOM_ID
				LEFT JOIN ob_real_estate_address rea ON
				ore.real_estate_address_id = rea.id
		WHERE a.DISPOSAL_TYPE IS NOT NULL AND rea.ADDRESS IS NOT NULL
		<if test="disposalType != null and disposalType != ''">
			AND a.DISPOSAL_TYPE = '${disposalType}'
		</if>
		<if test="applicantUnitId != null and applicantUnitId != ''">
			AND a.APPLICANT_UNIT_ID = '${applicantUnitId}'
		</if>
		GROUP BY o.OFFICE_NAME,
				 a.DISPOSAL_TYPE,
				 rea.ADDRESS
	</select>
    <select id="listQueryData" resultType="com.hsobs.ob.modules.disposalutilizationmanagement.entity.DisposalUtilizationManagementQuery">
		SELECT
		a.id AS "orderNumber",
		a.CREATE_DATE AS "applyDate",
		o.OFFICE_NAME AS "applicantUnitName",
		a.DISPOSAL_TYPE AS "disposalType",
		rea.ADDRESS AS "disposalAddress",
		sum(ore.AREA) AS "disposalSpace"
		FROM
		ob_disposal_utilization_management a
		LEFT JOIN js_sys_office o ON
		o.office_code = a.APPLICANT_UNIT_ID
		LEFT JOIN ob_real_estate ore ON
		ore.ID = a.ROOM_ID
		LEFT JOIN ob_real_estate_address rea ON
		ore.real_estate_address_id = rea.id
		WHERE a.DISPOSAL_TYPE IS NOT NULL AND rea.ADDRESS IS NOT NULL
		<if test="disposalType != null and disposalType != ''">
			AND a.DISPOSAL_TYPE = '${disposalType}'
		</if>
		<if test="orderNumber != null and orderNumber != ''">
			AND a.id = '${orderNumber}'
		</if>
		<if test="applicantUnitId != null and applicantUnitId != ''">
			AND a.APPLICANT_UNIT_ID = '${applicantUnitId}'
		</if>
		<if test="dateGte != null and dateGte != ''">
			AND a.CREATE_DATE &gt;= to_date('${dateGte}','yyyy-MM-dd')
		</if>
		<if test="dateLte != null and dateLte != ''">
			AND a.CREATE_DATE &lt;= to_date('${dateLte}','yyyy-MM-dd')
		</if>
		GROUP BY  a.id,a.CREATE_DATE,o.OFFICE_NAME,
		a.DISPOSAL_TYPE,
		rea.ADDRESS
	</select>

</mapper>