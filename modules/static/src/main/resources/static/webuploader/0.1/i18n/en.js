(function($) {
$.fn.webuploader.defaults.i18n = {
	
	'安装失败！': 'Installation failed!',
	'安装已成功，请刷新！': 'Installation successful, please refresh!',
	'文件上传组件不支持您的浏览器，请使用高版本浏览器！': 'Does not support your browser, please use the high version browser!',
	
	'点击选择文件': 'Select files',
	'点击选择图片': 'Select images',
	'点击选择视频': 'Select videos',
	'继续添加': 'Add files',
	
	'暂停上传': 'Pause upload',
	'继续上传': 'Continue upload',
	'开始上传': 'Start upload',
	'上传成功': 'Upload success',
	
	'张图片': ' images',
	'个文件': ' files',
	
	'上传失败': ' Failure',
	'重新上传': ' Retry ',	
	'或': ' or ',
	'忽略': ' Ignore ',
	
	'总共': 'Total ',
	'已上传': 'Uploaded ',
	'失败{0}个': ' {0} failure',
	
	'您只能上传{0}个文件': 'You can only upload {0} files.',
	'正在验证文件，请稍等。': 'File is being validated.',
	
	'删除': 'Delete',
	'向右旋转': 'Rotate right',
	'向左旋转': 'Rotate left',
	
	'不能上传空文件': 'Empty file error',
	'文件类型不对': 'File type error',
	'文件大小超出': 'File size exceeded',
	'文件传输中断': 'File transfer interrupt',
	'HTTP请求错误': 'HTTP request error',
	'文件格式不允许': 'File format not allowed',
	'不要选择重复文件': 'Do not select duplicate files',
	'上传失败，请重试': 'Upload failed, please try again',
	'服务器返回出错': 'The server returned an error',
	
	'预览生成中': 'Preview create',
	'不能预览': 'Cannot preview',
	
	'确定删除该图片吗？': 'Are you sure to delete the image?',
	'确定删除该文件吗？': 'Are you sure to delete this file?',
	
	'等待上传': 'Waiting for the upload',
	'正在上传': 'Are uploading',
	
	'下载': 'Download',
	'预览': 'Preview',
	'查看': 'To view'
};
})(jQuery);
