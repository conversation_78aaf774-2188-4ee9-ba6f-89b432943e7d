(function ($) {
	$.extend( $.validator.messages, {
		required: "このフィールドは必須です。",
		remote: "このフィールドを修正してください。",
		email: "有効なEメールアドレスを入力してください。",
		url: "有効なURLを入力してください。",
		date: "有効な日付を入力してください。",
		dateISO: "有効な日付（ISO）を入力してください。",
		number: "有効な数字を入力してください。",
		digits: "数字のみを入力してください。",
		equalTo: "同じ値をもう一度入力してください。",
		maxlength: $.validator.format( "{0} 文字以内で入力してください。" ),
		minlength: $.validator.format( "{0} 文字以上で入力してください。" ),
		rangelength: $.validator.format( "{0} 文字から {1} 文字までの値を入力してください。" ),
		range: $.validator.format( "{0} から {1} までの値を入力してください。" ),
		max: $.validator.format( "{0} 以下の値を入力してください。" ),
		min: $.validator.format( "{0} 以上の値を入力してください。" ),
		errorMessage: "ご記入頂いた情報に誤りがございましたので、提示に基づき修正をお願いします。",
		userName: "ご記入頂いた情報に誤りがございましたので、提示に基づき修正をお願いします。登录アカウントは中文字、英字、数字、下線のみです",
		realName: "名前は2 ~ 30字程度",
		abc: "数字または下線を入力してください，アルファベット開始",
		noEqualTo: "再度異なる値を入力して下さい",
		mobile: "お电话番号を正确にお书きください。ただ13、14、15、16、17、18、19番です",
		simplePhone: "お电话番号を正しくお书きください。固定番号(3-4位)番号(7-9位)",
		phone: "お电话番号を正确にお书きください。固定番号(3-4位)番号(7-9位)、携帯电话は13、14、15、16、17、18、19番です",
		zipCode: "郵便番号を正しく入力してください",
		integer: "整数を入力してください",
		ipv4: "有効なIP v4アドレスを入力してください",
		ipv6: "有効なIP v6アドレスを入力してください",
		qq: "あなたのQQ番号を正しく入力してください",
		isBlank: "全フレームを入力することはできません",
		idcard: "正しい住民登録番号(15 ~ 18位)を入力してください。"
	});
}(jQuery));
