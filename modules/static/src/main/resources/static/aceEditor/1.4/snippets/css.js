ace.define("ace/snippets/css",["require","exports","module"],function(e,t,n){"use strict";t.snippetText="snippet .\n	${1} {\n		${2}\n	}\nsnippet !\n	 !important\nsnippet bdi:m+\n	-moz-border-image: url(${1}) ${2:0} ${3:0} ${4:0} ${5:0} ${6:stretch} ${7:stretch};\nsnippet bdi:m\n	-moz-border-image: ${1};\nsnippet bdrz:m\n	-moz-border-radius: ${1};\nsnippet bxsh:m+\n	-moz-box-shadow: ${1:0} ${2:0} ${3:0} #${4:000};\nsnippet bxsh:m\n	-moz-box-shadow: ${1};\nsnippet bdi:w+\n	-webkit-border-image: url(${1}) ${2:0} ${3:0} ${4:0} ${5:0} ${6:stretch} ${7:stretch};\nsnippet bdi:w\n	-webkit-border-image: ${1};\nsnippet bdrz:w\n	-webkit-border-radius: ${1};\nsnippet bxsh:w+\n	-webkit-box-shadow: ${1:0} ${2:0} ${3:0} #${4:000};\nsnippet bxsh:w\n	-webkit-box-shadow: ${1};\nsnippet @f\n	@font-face {\n		font-family: ${1};\n		src: url(${2});\n	}\nsnippet @i\n	@import url(${1});\nsnippet @m\n	@media ${1:print} {\n		${2}\n	}\nsnippet bg+\n	background: #${1:FFF} url(${2}) ${3:0} ${4:0} ${5:no-repeat};\nsnippet bga\n	background-attachment: ${1};\nsnippet bga:f\n	background-attachment: fixed;\nsnippet bga:s\n	background-attachment: scroll;\nsnippet bgbk\n	background-break: ${1};\nsnippet bgbk:bb\n	background-break: bounding-box;\nsnippet bgbk:c\n	background-break: continuous;\nsnippet bgbk:eb\n	background-break: each-box;\nsnippet bgcp\n	background-clip: ${1};\nsnippet bgcp:bb\n	background-clip: border-box;\nsnippet bgcp:cb\n	background-clip: content-box;\nsnippet bgcp:nc\n	background-clip: no-clip;\nsnippet bgcp:pb\n	background-clip: padding-box;\nsnippet bgc\n	background-color: #${1:FFF};\nsnippet bgc:t\n	background-color: transparent;\nsnippet bgi\n	background-image: url(${1});\nsnippet bgi:n\n	background-image: none;\nsnippet bgo\n	background-origin: ${1};\nsnippet bgo:bb\n	background-origin: border-box;\nsnippet bgo:cb\n	background-origin: content-box;\nsnippet bgo:pb\n	background-origin: padding-box;\nsnippet bgpx\n	background-position-x: ${1};\nsnippet bgpy\n	background-position-y: ${1};\nsnippet bgp\n	background-position: ${1:0} ${2:0};\nsnippet bgr\n	background-repeat: ${1};\nsnippet bgr:n\n	background-repeat: no-repeat;\nsnippet bgr:x\n	background-repeat: repeat-x;\nsnippet bgr:y\n	background-repeat: repeat-y;\nsnippet bgr:r\n	background-repeat: repeat;\nsnippet bgz\n	background-size: ${1};\nsnippet bgz:a\n	background-size: auto;\nsnippet bgz:ct\n	background-size: contain;\nsnippet bgz:cv\n	background-size: cover;\nsnippet bg\n	background: ${1};\nsnippet bg:ie\n	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='${1}',sizingMethod='${2:crop}');\nsnippet bg:n\n	background: none;\nsnippet bd+\n	border: ${1:1px} ${2:solid} #${3:000};\nsnippet bdb+\n	border-bottom: ${1:1px} ${2:solid} #${3:000};\nsnippet bdbc\n	border-bottom-color: #${1:000};\nsnippet bdbi\n	border-bottom-image: url(${1});\nsnippet bdbi:n\n	border-bottom-image: none;\nsnippet bdbli\n	border-bottom-left-image: url(${1});\nsnippet bdbli:c\n	border-bottom-left-image: continue;\nsnippet bdbli:n\n	border-bottom-left-image: none;\nsnippet bdblrz\n	border-bottom-left-radius: ${1};\nsnippet bdbri\n	border-bottom-right-image: url(${1});\nsnippet bdbri:c\n	border-bottom-right-image: continue;\nsnippet bdbri:n\n	border-bottom-right-image: none;\nsnippet bdbrrz\n	border-bottom-right-radius: ${1};\nsnippet bdbs\n	border-bottom-style: ${1};\nsnippet bdbs:n\n	border-bottom-style: none;\nsnippet bdbw\n	border-bottom-width: ${1};\nsnippet bdb\n	border-bottom: ${1};\nsnippet bdb:n\n	border-bottom: none;\nsnippet bdbk\n	border-break: ${1};\nsnippet bdbk:c\n	border-break: close;\nsnippet bdcl\n	border-collapse: ${1};\nsnippet bdcl:c\n	border-collapse: collapse;\nsnippet bdcl:s\n	border-collapse: separate;\nsnippet bdc\n	border-color: #${1:000};\nsnippet bdci\n	border-corner-image: url(${1});\nsnippet bdci:c\n	border-corner-image: continue;\nsnippet bdci:n\n	border-corner-image: none;\nsnippet bdf\n	border-fit: ${1};\nsnippet bdf:c\n	border-fit: clip;\nsnippet bdf:of\n	border-fit: overwrite;\nsnippet bdf:ow\n	border-fit: overwrite;\nsnippet bdf:r\n	border-fit: repeat;\nsnippet bdf:sc\n	border-fit: scale;\nsnippet bdf:sp\n	border-fit: space;\nsnippet bdf:st\n	border-fit: stretch;\nsnippet bdi\n	border-image: url(${1}) ${2:0} ${3:0} ${4:0} ${5:0} ${6:stretch} ${7:stretch};\nsnippet bdi:n\n	border-image: none;\nsnippet bdl+\n	border-left: ${1:1px} ${2:solid} #${3:000};\nsnippet bdlc\n	border-left-color: #${1:000};\nsnippet bdli\n	border-left-image: url(${1});\nsnippet bdli:n\n	border-left-image: none;\nsnippet bdls\n	border-left-style: ${1};\nsnippet bdls:n\n	border-left-style: none;\nsnippet bdlw\n	border-left-width: ${1};\nsnippet bdl\n	border-left: ${1};\nsnippet bdl:n\n	border-left: none;\nsnippet bdlt\n	border-length: ${1};\nsnippet bdlt:a\n	border-length: auto;\nsnippet bdrz\n	border-radius: ${1};\nsnippet bdr+\n	border-right: ${1:1px} ${2:solid} #${3:000};\nsnippet bdrc\n	border-right-color: #${1:000};\nsnippet bdri\n	border-right-image: url(${1});\nsnippet bdri:n\n	border-right-image: none;\nsnippet bdrs\n	border-right-style: ${1};\nsnippet bdrs:n\n	border-right-style: none;\nsnippet bdrw\n	border-right-width: ${1};\nsnippet bdr\n	border-right: ${1};\nsnippet bdr:n\n	border-right: none;\nsnippet bdsp\n	border-spacing: ${1};\nsnippet bds\n	border-style: ${1};\nsnippet bds:ds\n	border-style: dashed;\nsnippet bds:dtds\n	border-style: dot-dash;\nsnippet bds:dtdtds\n	border-style: dot-dot-dash;\nsnippet bds:dt\n	border-style: dotted;\nsnippet bds:db\n	border-style: double;\nsnippet bds:g\n	border-style: groove;\nsnippet bds:h\n	border-style: hidden;\nsnippet bds:i\n	border-style: inset;\nsnippet bds:n\n	border-style: none;\nsnippet bds:o\n	border-style: outset;\nsnippet bds:r\n	border-style: ridge;\nsnippet bds:s\n	border-style: solid;\nsnippet bds:w\n	border-style: wave;\nsnippet bdt+\n	border-top: ${1:1px} ${2:solid} #${3:000};\nsnippet bdtc\n	border-top-color: #${1:000};\nsnippet bdti\n	border-top-image: url(${1});\nsnippet bdti:n\n	border-top-image: none;\nsnippet bdtli\n	border-top-left-image: url(${1});\nsnippet bdtli:c\n	border-corner-image: continue;\nsnippet bdtli:n\n	border-corner-image: none;\nsnippet bdtlrz\n	border-top-left-radius: ${1};\nsnippet bdtri\n	border-top-right-image: url(${1});\nsnippet bdtri:c\n	border-top-right-image: continue;\nsnippet bdtri:n\n	border-top-right-image: none;\nsnippet bdtrrz\n	border-top-right-radius: ${1};\nsnippet bdts\n	border-top-style: ${1};\nsnippet bdts:n\n	border-top-style: none;\nsnippet bdtw\n	border-top-width: ${1};\nsnippet bdt\n	border-top: ${1};\nsnippet bdt:n\n	border-top: none;\nsnippet bdw\n	border-width: ${1};\nsnippet bd\n	border: ${1};\nsnippet bd:n\n	border: none;\nsnippet b\n	bottom: ${1};\nsnippet b:a\n	bottom: auto;\nsnippet bxsh+\n	box-shadow: ${1:0} ${2:0} ${3:0} #${4:000};\nsnippet bxsh\n	box-shadow: ${1};\nsnippet bxsh:n\n	box-shadow: none;\nsnippet bxz\n	box-sizing: ${1};\nsnippet bxz:bb\n	box-sizing: border-box;\nsnippet bxz:cb\n	box-sizing: content-box;\nsnippet cps\n	caption-side: ${1};\nsnippet cps:b\n	caption-side: bottom;\nsnippet cps:t\n	caption-side: top;\nsnippet cl\n	clear: ${1};\nsnippet cl:b\n	clear: both;\nsnippet cl:l\n	clear: left;\nsnippet cl:n\n	clear: none;\nsnippet cl:r\n	clear: right;\nsnippet cp\n	clip: ${1};\nsnippet cp:a\n	clip: auto;\nsnippet cp:r\n	clip: rect(${1:0} ${2:0} ${3:0} ${4:0});\nsnippet c\n	color: #${1:000};\nsnippet ct\n	content: ${1};\nsnippet ct:a\n	content: attr(${1});\nsnippet ct:cq\n	content: close-quote;\nsnippet ct:c\n	content: counter(${1});\nsnippet ct:cs\n	content: counters(${1});\nsnippet ct:ncq\n	content: no-close-quote;\nsnippet ct:noq\n	content: no-open-quote;\nsnippet ct:n\n	content: normal;\nsnippet ct:oq\n	content: open-quote;\nsnippet coi\n	counter-increment: ${1};\nsnippet cor\n	counter-reset: ${1};\nsnippet cur\n	cursor: ${1};\nsnippet cur:a\n	cursor: auto;\nsnippet cur:c\n	cursor: crosshair;\nsnippet cur:d\n	cursor: default;\nsnippet cur:ha\n	cursor: hand;\nsnippet cur:he\n	cursor: help;\nsnippet cur:m\n	cursor: move;\nsnippet cur:p\n	cursor: pointer;\nsnippet cur:t\n	cursor: text;\nsnippet d\n	display: ${1};\nsnippet d:mib\n	display: -moz-inline-box;\nsnippet d:mis\n	display: -moz-inline-stack;\nsnippet d:b\n	display: block;\nsnippet d:cp\n	display: compact;\nsnippet d:ib\n	display: inline-block;\nsnippet d:itb\n	display: inline-table;\nsnippet d:i\n	display: inline;\nsnippet d:li\n	display: list-item;\nsnippet d:n\n	display: none;\nsnippet d:ri\n	display: run-in;\nsnippet d:tbcp\n	display: table-caption;\nsnippet d:tbc\n	display: table-cell;\nsnippet d:tbclg\n	display: table-column-group;\nsnippet d:tbcl\n	display: table-column;\nsnippet d:tbfg\n	display: table-footer-group;\nsnippet d:tbhg\n	display: table-header-group;\nsnippet d:tbrg\n	display: table-row-group;\nsnippet d:tbr\n	display: table-row;\nsnippet d:tb\n	display: table;\nsnippet ec\n	empty-cells: ${1};\nsnippet ec:h\n	empty-cells: hide;\nsnippet ec:s\n	empty-cells: show;\nsnippet exp\n	expression()\nsnippet fl\n	float: ${1};\nsnippet fl:l\n	float: left;\nsnippet fl:n\n	float: none;\nsnippet fl:r\n	float: right;\nsnippet f+\n	font: ${1:1em} ${2:Arial},${3:sans-serif};\nsnippet fef\n	font-effect: ${1};\nsnippet fef:eb\n	font-effect: emboss;\nsnippet fef:eg\n	font-effect: engrave;\nsnippet fef:n\n	font-effect: none;\nsnippet fef:o\n	font-effect: outline;\nsnippet femp\n	font-emphasize-position: ${1};\nsnippet femp:a\n	font-emphasize-position: after;\nsnippet femp:b\n	font-emphasize-position: before;\nsnippet fems\n	font-emphasize-style: ${1};\nsnippet fems:ac\n	font-emphasize-style: accent;\nsnippet fems:c\n	font-emphasize-style: circle;\nsnippet fems:ds\n	font-emphasize-style: disc;\nsnippet fems:dt\n	font-emphasize-style: dot;\nsnippet fems:n\n	font-emphasize-style: none;\nsnippet fem\n	font-emphasize: ${1};\nsnippet ff\n	font-family: ${1};\nsnippet ff:c\n	font-family: ${1:'Monotype Corsiva','Comic Sans MS'},cursive;\nsnippet ff:f\n	font-family: ${1:Capitals,Impact},fantasy;\nsnippet ff:m\n	font-family: ${1:Monaco,'Courier New'},monospace;\nsnippet ff:ss\n	font-family: ${1:Helvetica,Arial},sans-serif;\nsnippet ff:s\n	font-family: ${1:Georgia,'Times New Roman'},serif;\nsnippet fza\n	font-size-adjust: ${1};\nsnippet fza:n\n	font-size-adjust: none;\nsnippet fz\n	font-size: ${1};\nsnippet fsm\n	font-smooth: ${1};\nsnippet fsm:aw\n	font-smooth: always;\nsnippet fsm:a\n	font-smooth: auto;\nsnippet fsm:n\n	font-smooth: never;\nsnippet fst\n	font-stretch: ${1};\nsnippet fst:c\n	font-stretch: condensed;\nsnippet fst:e\n	font-stretch: expanded;\nsnippet fst:ec\n	font-stretch: extra-condensed;\nsnippet fst:ee\n	font-stretch: extra-expanded;\nsnippet fst:n\n	font-stretch: normal;\nsnippet fst:sc\n	font-stretch: semi-condensed;\nsnippet fst:se\n	font-stretch: semi-expanded;\nsnippet fst:uc\n	font-stretch: ultra-condensed;\nsnippet fst:ue\n	font-stretch: ultra-expanded;\nsnippet fs\n	font-style: ${1};\nsnippet fs:i\n	font-style: italic;\nsnippet fs:n\n	font-style: normal;\nsnippet fs:o\n	font-style: oblique;\nsnippet fv\n	font-variant: ${1};\nsnippet fv:n\n	font-variant: normal;\nsnippet fv:sc\n	font-variant: small-caps;\nsnippet fw\n	font-weight: ${1};\nsnippet fw:b\n	font-weight: bold;\nsnippet fw:br\n	font-weight: bolder;\nsnippet fw:lr\n	font-weight: lighter;\nsnippet fw:n\n	font-weight: normal;\nsnippet f\n	font: ${1};\nsnippet h\n	height: ${1};\nsnippet h:a\n	height: auto;\nsnippet l\n	left: ${1};\nsnippet l:a\n	left: auto;\nsnippet lts\n	letter-spacing: ${1};\nsnippet lh\n	line-height: ${1};\nsnippet lisi\n	list-style-image: url(${1});\nsnippet lisi:n\n	list-style-image: none;\nsnippet lisp\n	list-style-position: ${1};\nsnippet lisp:i\n	list-style-position: inside;\nsnippet lisp:o\n	list-style-position: outside;\nsnippet list\n	list-style-type: ${1};\nsnippet list:c\n	list-style-type: circle;\nsnippet list:dclz\n	list-style-type: decimal-leading-zero;\nsnippet list:dc\n	list-style-type: decimal;\nsnippet list:d\n	list-style-type: disc;\nsnippet list:lr\n	list-style-type: lower-roman;\nsnippet list:n\n	list-style-type: none;\nsnippet list:s\n	list-style-type: square;\nsnippet list:ur\n	list-style-type: upper-roman;\nsnippet lis\n	list-style: ${1};\nsnippet lis:n\n	list-style: none;\nsnippet mb\n	margin-bottom: ${1};\nsnippet mb:a\n	margin-bottom: auto;\nsnippet ml\n	margin-left: ${1};\nsnippet ml:a\n	margin-left: auto;\nsnippet mr\n	margin-right: ${1};\nsnippet mr:a\n	margin-right: auto;\nsnippet mt\n	margin-top: ${1};\nsnippet mt:a\n	margin-top: auto;\nsnippet m\n	margin: ${1};\nsnippet m:4\n	margin: ${1:0} ${2:0} ${3:0} ${4:0};\nsnippet m:3\n	margin: ${1:0} ${2:0} ${3:0};\nsnippet m:2\n	margin: ${1:0} ${2:0};\nsnippet m:0\n	margin: 0;\nsnippet m:a\n	margin: auto;\nsnippet mah\n	max-height: ${1};\nsnippet mah:n\n	max-height: none;\nsnippet maw\n	max-width: ${1};\nsnippet maw:n\n	max-width: none;\nsnippet mih\n	min-height: ${1};\nsnippet miw\n	min-width: ${1};\nsnippet op\n	opacity: ${1};\nsnippet op:ie\n	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=${1:100});\nsnippet op:ms\n	-ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=${1:100})';\nsnippet orp\n	orphans: ${1};\nsnippet o+\n	outline: ${1:1px} ${2:solid} #${3:000};\nsnippet oc\n	outline-color: ${1:#000};\nsnippet oc:i\n	outline-color: invert;\nsnippet oo\n	outline-offset: ${1};\nsnippet os\n	outline-style: ${1};\nsnippet ow\n	outline-width: ${1};\nsnippet o\n	outline: ${1};\nsnippet o:n\n	outline: none;\nsnippet ovs\n	overflow-style: ${1};\nsnippet ovs:a\n	overflow-style: auto;\nsnippet ovs:mq\n	overflow-style: marquee;\nsnippet ovs:mv\n	overflow-style: move;\nsnippet ovs:p\n	overflow-style: panner;\nsnippet ovs:s\n	overflow-style: scrollbar;\nsnippet ovx\n	overflow-x: ${1};\nsnippet ovx:a\n	overflow-x: auto;\nsnippet ovx:h\n	overflow-x: hidden;\nsnippet ovx:s\n	overflow-x: scroll;\nsnippet ovx:v\n	overflow-x: visible;\nsnippet ovy\n	overflow-y: ${1};\nsnippet ovy:a\n	overflow-y: auto;\nsnippet ovy:h\n	overflow-y: hidden;\nsnippet ovy:s\n	overflow-y: scroll;\nsnippet ovy:v\n	overflow-y: visible;\nsnippet ov\n	overflow: ${1};\nsnippet ov:a\n	overflow: auto;\nsnippet ov:h\n	overflow: hidden;\nsnippet ov:s\n	overflow: scroll;\nsnippet ov:v\n	overflow: visible;\nsnippet pb\n	padding-bottom: ${1};\nsnippet pl\n	padding-left: ${1};\nsnippet pr\n	padding-right: ${1};\nsnippet pt\n	padding-top: ${1};\nsnippet p\n	padding: ${1};\nsnippet p:4\n	padding: ${1:0} ${2:0} ${3:0} ${4:0};\nsnippet p:3\n	padding: ${1:0} ${2:0} ${3:0};\nsnippet p:2\n	padding: ${1:0} ${2:0};\nsnippet p:0\n	padding: 0;\nsnippet pgba\n	page-break-after: ${1};\nsnippet pgba:aw\n	page-break-after: always;\nsnippet pgba:a\n	page-break-after: auto;\nsnippet pgba:l\n	page-break-after: left;\nsnippet pgba:r\n	page-break-after: right;\nsnippet pgbb\n	page-break-before: ${1};\nsnippet pgbb:aw\n	page-break-before: always;\nsnippet pgbb:a\n	page-break-before: auto;\nsnippet pgbb:l\n	page-break-before: left;\nsnippet pgbb:r\n	page-break-before: right;\nsnippet pgbi\n	page-break-inside: ${1};\nsnippet pgbi:a\n	page-break-inside: auto;\nsnippet pgbi:av\n	page-break-inside: avoid;\nsnippet pos\n	position: ${1};\nsnippet pos:a\n	position: absolute;\nsnippet pos:f\n	position: fixed;\nsnippet pos:r\n	position: relative;\nsnippet pos:s\n	position: static;\nsnippet q\n	quotes: ${1};\nsnippet q:en\n	quotes: '\\201C' '\\201D' '\\2018' '\\2019';\nsnippet q:n\n	quotes: none;\nsnippet q:ru\n	quotes: '\\00AB' '\\00BB' '\\201E' '\\201C';\nsnippet rz\n	resize: ${1};\nsnippet rz:b\n	resize: both;\nsnippet rz:h\n	resize: horizontal;\nsnippet rz:n\n	resize: none;\nsnippet rz:v\n	resize: vertical;\nsnippet r\n	right: ${1};\nsnippet r:a\n	right: auto;\nsnippet tbl\n	table-layout: ${1};\nsnippet tbl:a\n	table-layout: auto;\nsnippet tbl:f\n	table-layout: fixed;\nsnippet tal\n	text-align-last: ${1};\nsnippet tal:a\n	text-align-last: auto;\nsnippet tal:c\n	text-align-last: center;\nsnippet tal:l\n	text-align-last: left;\nsnippet tal:r\n	text-align-last: right;\nsnippet ta\n	text-align: ${1};\nsnippet ta:c\n	text-align: center;\nsnippet ta:l\n	text-align: left;\nsnippet ta:r\n	text-align: right;\nsnippet td\n	text-decoration: ${1};\nsnippet td:l\n	text-decoration: line-through;\nsnippet td:n\n	text-decoration: none;\nsnippet td:o\n	text-decoration: overline;\nsnippet td:u\n	text-decoration: underline;\nsnippet te\n	text-emphasis: ${1};\nsnippet te:ac\n	text-emphasis: accent;\nsnippet te:a\n	text-emphasis: after;\nsnippet te:b\n	text-emphasis: before;\nsnippet te:c\n	text-emphasis: circle;\nsnippet te:ds\n	text-emphasis: disc;\nsnippet te:dt\n	text-emphasis: dot;\nsnippet te:n\n	text-emphasis: none;\nsnippet th\n	text-height: ${1};\nsnippet th:a\n	text-height: auto;\nsnippet th:f\n	text-height: font-size;\nsnippet th:m\n	text-height: max-size;\nsnippet th:t\n	text-height: text-size;\nsnippet ti\n	text-indent: ${1};\nsnippet ti:-\n	text-indent: -9999px;\nsnippet tj\n	text-justify: ${1};\nsnippet tj:a\n	text-justify: auto;\nsnippet tj:d\n	text-justify: distribute;\nsnippet tj:ic\n	text-justify: inter-cluster;\nsnippet tj:ii\n	text-justify: inter-ideograph;\nsnippet tj:iw\n	text-justify: inter-word;\nsnippet tj:k\n	text-justify: kashida;\nsnippet tj:t\n	text-justify: tibetan;\nsnippet to+\n	text-outline: ${1:0} ${2:0} #${3:000};\nsnippet to\n	text-outline: ${1};\nsnippet to:n\n	text-outline: none;\nsnippet tr\n	text-replace: ${1};\nsnippet tr:n\n	text-replace: none;\nsnippet tsh+\n	text-shadow: ${1:0} ${2:0} ${3:0} #${4:000};\nsnippet tsh\n	text-shadow: ${1};\nsnippet tsh:n\n	text-shadow: none;\nsnippet tt\n	text-transform: ${1};\nsnippet tt:c\n	text-transform: capitalize;\nsnippet tt:l\n	text-transform: lowercase;\nsnippet tt:n\n	text-transform: none;\nsnippet tt:u\n	text-transform: uppercase;\nsnippet tw\n	text-wrap: ${1};\nsnippet tw:no\n	text-wrap: none;\nsnippet tw:n\n	text-wrap: normal;\nsnippet tw:s\n	text-wrap: suppress;\nsnippet tw:u\n	text-wrap: unrestricted;\nsnippet t\n	top: ${1};\nsnippet t:a\n	top: auto;\nsnippet va\n	vertical-align: ${1};\nsnippet va:bl\n	vertical-align: baseline;\nsnippet va:b\n	vertical-align: bottom;\nsnippet va:m\n	vertical-align: middle;\nsnippet va:sub\n	vertical-align: sub;\nsnippet va:sup\n	vertical-align: super;\nsnippet va:tb\n	vertical-align: text-bottom;\nsnippet va:tt\n	vertical-align: text-top;\nsnippet va:t\n	vertical-align: top;\nsnippet v\n	visibility: ${1};\nsnippet v:c\n	visibility: collapse;\nsnippet v:h\n	visibility: hidden;\nsnippet v:v\n	visibility: visible;\nsnippet whsc\n	white-space-collapse: ${1};\nsnippet whsc:ba\n	white-space-collapse: break-all;\nsnippet whsc:bs\n	white-space-collapse: break-strict;\nsnippet whsc:k\n	white-space-collapse: keep-all;\nsnippet whsc:l\n	white-space-collapse: loose;\nsnippet whsc:n\n	white-space-collapse: normal;\nsnippet whs\n	white-space: ${1};\nsnippet whs:n\n	white-space: normal;\nsnippet whs:nw\n	white-space: nowrap;\nsnippet whs:pl\n	white-space: pre-line;\nsnippet whs:pw\n	white-space: pre-wrap;\nsnippet whs:p\n	white-space: pre;\nsnippet wid\n	widows: ${1};\nsnippet w\n	width: ${1};\nsnippet w:a\n	width: auto;\nsnippet wob\n	word-break: ${1};\nsnippet wob:ba\n	word-break: break-all;\nsnippet wob:bs\n	word-break: break-strict;\nsnippet wob:k\n	word-break: keep-all;\nsnippet wob:l\n	word-break: loose;\nsnippet wob:n\n	word-break: normal;\nsnippet wos\n	word-spacing: ${1};\nsnippet wow\n	word-wrap: ${1};\nsnippet wow:no\n	word-wrap: none;\nsnippet wow:n\n	word-wrap: normal;\nsnippet wow:s\n	word-wrap: suppress;\nsnippet wow:u\n	word-wrap: unrestricted;\nsnippet z\n	z-index: ${1};\nsnippet z:a\n	z-index: auto;\nsnippet zoo\n	zoom: 1;\n",t.scope="css"});                (function() {
                    ace.require(["ace/snippets/css"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            