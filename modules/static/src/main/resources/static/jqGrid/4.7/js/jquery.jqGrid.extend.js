/*
 Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 No deletion without permission, or be held responsible to law.
 The JS file, without author permission, shall not be copied, 
 published and spread, can not be used for commercial purposes
 <AUTHOR> Gem, Email: ThinkGem at 163.COM
 @version 2023-09-19
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var g=a[e];if(b.call(c,g,e,a))return{i:e,v:g}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){a!=Array.prototype&&a!=Object.prototype&&(a[b]=c.value)};$jscomp.getGlobal=function(a){a=["object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global,a];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.polyfill=function(a,b,c,d){if(b){c=$jscomp.global;a=a.split(".");for(d=0;d<a.length-1;d++){var e=a[d];e in c||(c[e]={});c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&$jscomp.defineProperty(c,a,{configurable:!0,writable:!0,value:b})}};$jscomp.polyfill("Array.prototype.find",function(a){return a?a:function(a,c){return $jscomp.findInternal(this,a,c).v}},"es6","es3");
(function(a){var b=function(c,b){function d(){f.jqGrid(c);w.html(pageHtml({}));c.groupHeaders&&c.groupHeaders.twoLevel&&(c.groupHeaders.threeLevel?f.jqGrid("setComplexHeaders",{complexHeaders:{defaultStyle:!0,twoLevel:c.groupHeaders.twoLevel,threeLevel:c.groupHeaders.threeLevel}}):f.jqGrid("setGroupHeaders",{useColSpanStyle:!0,groupHeaders:c.groupHeaders.twoLevel}));c.frozenCols&&(f.jqGrid("setFrozenColumns"),f.jqGrid("setFrozenRightColumns"));f.jqGrid("initFormMore");window.dataGrids||(window.dataGrids=
[]);window.dataGrids.push(f);window.dataGridRunOnce||(window.dataGridRunOnce=!0,a(window).resizeEnd(function(){window.dataGrids||(window.dataGrids=[]);for(var a in window.dataGrids)window.dataGrids[a].dataGrid("resize")}));q();f.parent().scrollEnd(function(){"function"==typeof window.webuploaderRefresh&&window.webuploaderRefresh()});f.on("jqGridRemapColumns",function(){q()})}function g(){if(c.autoGridHeight){if("function"==typeof c.autoGridHeight)var b=c.autoGridHeight();else{b=a(f).height();var d=
a(f).parent();0!=d.length&&(b=d.height());b=a(window).height()-a("body").height()+b-c.autoGridHeightFix;b<c.gridMinMeight&&(b=c.gridMinMeight);d.height(b)}0!=b&&f.jqGrid("setGridHeight",b)}}function k(){if(c.autoGridWidth){var b=0;if("function"==typeof c.autoGridWidth)b=c.autoGridWidth();else{var d=a(f).parents(".ui-jqgrid").parent();d.is(":visible")&&(b=d.width()-2)}0!=b&&(b-=c.autoGridWidthFix,c.smartToFit&&(f.width()<b?c.shrinkToFit=!0:c.shrinkToFit=I,d=f[0].p,d.showColData&&3>d.showColData.length&&
(c.shrinkToFit=I)),f.jqGrid("setGridWidth",b,c.shrinkToFit&&600<a(window).width()))}}function q(){g();k();setTimeout(function(){g();k()},navigator.userAgent.match(/MSIE 8.0/)?200:100)}function n(a,c,b){0!=c.length&&void 0==c.attr("data-"+a+"-binded")&&(c.attr("data-"+a+"-binded",!0),"radio"==c.attr("type")&&c.closest(".icheck").length&&(a="ifChecked"),c.bind(a,b))}function t(a){return!a||""==a||"0"==a}function p(a){return f.jqGrid("getGridParam",a)}function h(a,c){return f.jqGrid("setGridParam",a,
c)}function v(a,b){var d={};a&&(d.page=a,c.inputPageNo.val(a),c.editGrid||(x.scrollTop=!0));b&&(d.rowNum=b,c.inputPageSize.val(b));c.lazyLoad&&(c.datatype=d.datatype=c.datatype_bak);h(d);u();return x}function u(){f.trigger("reloadGrid")}function H(a){f.jqGrid("delTreeNode",a);return x}function z(c){js.loading();setTimeout(function(){f.expandLevel=c;f.currentLevel=1;f.expandNodeIds=[];a(".jqgrow:visible .tree-plus",f).each(function(){var c=a(this).parents(".jqgrow")[0].id;f.expandNodeIds.push(c)});
100<f.expandNodeIds.length?(js.showMessage(a.jgrid.format(a.jgrid.extend.expandTooMany,f.expandNodeIds.length)),f.expandNodeIds=[]):a("#"+f.expandNodeIds.shift()+":visible .tree-plus",f).click();for(var b=0;b<f.expandLevel;b++)a(".jqgrow:visible .tree-plus",f).click();js.closeLoading()},10);return x}function B(){js.loading();setTimeout(function(){a(".tree-minus",f).click();js.closeLoading()},10);return x}function E(a,b){a&&(c.defaultExpandLevel=a);t(b)?void 0!=b&&(c.defaultExpandLevel=c.initExpandLevel):
h({postData:{id:b}});u();return x}function F(a,c){c&&H(c);t(a)?u():""!=a&&(a=f.find("#"+a+" .treeclick"),0<a.length?a.dblclick():u())}function C(c){var b=a("#"+m+",#"+m+"__frozen").find(">tbody>tr:gt(0):not(.jqgroup)");a.each(c.split(","),function(c,d){var h=b.eq(0).children("[aria-describedby='"+m+"_"+d+"']"),f=h.index(),A=1;b.slice(1).each(function(c,b){c=a(b).children("td").eq(f);h.text()===c.text()?(A++,c.hide()):(h.attr("rowspan",A),h=c,A=1);h.attr("rowspan",A)})})}function D(b,d){b=b||c.editGridInputFormListName;
d=d||c.editGridInputFormListAttrs;a.each(d.split(","),function(d,h){if(""!=(h=a.trim(h))){var A=c.editGridInputFormListIsMap?"["+h+"]":"."+h;f.find(['[name="'+h+'"]:not(div,a,script)','[name^="'+b+'["][name$="]'+A+'"]:not(div,a,script)'].join()).each(function(c,d){a(this).attr("name",b+"["+c+"]"+A)});f.find(['[name="!'+h+'"]:not(div,a,script)','[name^="!'+b+'["][name$="]'+A+'"]:not(div,a,script)'].join()).each(function(c,d){a(this).attr("name","!"+b+"["+c+"]"+A)});f.find(['div.editable[role="radio"][name="'+
h+'"]','div.editable[role="checkbox"][name="'+h+'"]'].join()).each(function(c,d){a(this).find('[type="radio"],[type="checkbox"]').attr("name",b+"["+c+"]"+A)})}})}var f="undefined"!=typeof b?b:c.dataGrid?c.dataGrid:a("#dataGrid"),m=f.attr("id"),w=c.dataGridPage?c.dataGridPage:a("#"+m+"Page"),y=w.attr("id"),l=c.searchForm?c.searchForm:c.url?a():a("#searchForm");b=c.dataId?c.dataId:"id";c=a.extend({url:l.attr("action"),postData:l.serializeArray(),mtype:"POST",datatype:"json",jsonReader:{id:b,root:"list",
page:"pageNo",userdata:"otherData",total:"last",records:"count",subgrid:{root:"list"}},treeReader:{level_field:"treeLevel",parent_id_field:"parentCode",userdata:"otherData",leaf_field:"isTreeLeaf",expanded_field:"isOpen",icon_field:"_icon"},prmNames:{page:"pageNo",rows:"pageSize",sort:"orderBy",order:null,search:null,nd:null,id:b,oper:null,editoper:null,addoper:null,deloper:null,subgridid:b,npage:null,totalrows:null},editurl:"clientArray",rowNum:-1,rownumWidth:39,multiselectWidth:32,altRows:!0,minColWidth:35,
subGridWidth:32,autoencode:!0,columnModel:[],colNames:[],colModel:[],dataId:b,lazyLoad:!1,shrinkToFit:!0,smartToFit:!0,showRownum:!0,showCheckbox:!1,sortableColumn:!0,multiSort:!1,sortable:!0,validate:!0,emptyDataHint:!1,emptyDataHintContent:"<center>"+a.jgrid.defaults.emptyrecords+"</center>",autoGridHeight:!0,autoGridHeightFix:0,autoGridWidth:!0,autoGridWidthFix:0,gridMinMeight:200,btnSearch:a("#btnSearch"),btnRefreshTree:a("#btnRefreshTree"),btnExpandTreeNode:a("#btnExpandTreeNode"),btnCollapseTreeNode:a("#btnCollapseTreeNode"),
btnSetting:a("#btnSetting"),editGrid:!1,editGridInitRowNum:1,editGridInitAllRowEdit:!0,editGridAddRow:function(a){if("new"==a||"init"==a)f.jqGrid("addRow",{position:c.editGridAddRowPosition,src:c.editGridAddRowPositionSrc,addRowParams:{keys:c.editGridAddRowKeys,focusField:"new"==a},initdata:"function"==typeof c.editGridAddRowInitData?c.editGridAddRowInitData(m):c.editGridAddRowInitData}),null==c.editGridAddRowPositionSrc&&(a=f.parent(),a.scrollTop(a.prop("scrollHeight"))),"function"==typeof c.editGridAddRowCallback&&
c.editGridAddRowCallback(m,c),"function"==typeof window.webuploaderRefresh&&window.webuploaderRefresh()},editGridAddRowBtn:a("#"+m+"AddRowBtn"),editGridAddRowBtnToHeader:!0,editGridAddRowInitData:{},editGridAddRowKeys:!1,editGridAddRowPosition:"last",editGridAddRowPositionSrc:null,editGridAddRowCallback:function(){},editGridInputForm:f.parents("form"),editGridInputFormListName:"",editGridInputFormListAttrs:"",editGridInputFormListIsMap:!1,treeGrid:!1,treeGridModel:"adjacency",treeColumn:null,ExpandColClick:!0,
ExpandColumn:c.treeColumn,defaultExpandLevel:0,initExpandLevel:c.defaultExpandLevel,expandNodeClearPostData:!1,inputPageNo:a("#pageNo",l),inputPageSize:a("#pageSize",l),inputOrderBy:a("#orderBy",l),beforeRequest:function(b){switch(c.datatype&&c.datatype.toLowerCase()){case "json":case "jsonp":case "xml":case "script":if(void 0==c.url||""==c.url)return js.showMessage("\u8bbe\u7f6esearchForm\u8868\u5355\u6307\u5b9a\u9519\u8bef\u6216URL\u4e3a\u7a7a\u3002"),!1}js.loading();if(c.treeGrid){var d=p("postData");
if(d.id)h({postData:{id:d.id}});else if(d.nodeid)if(!0===c.expandNodeClearPostData)h({postData:{parentCode:d.nodeid}});else{var e={};a.each(l.serializeArray(),function(a,c){e[c.name]=c.value});"string"===typeof c.expandNodeClearPostData&&a.each(c.expandNodeClearPostData.split(","),function(c,b){b=a.trim(b);""!==b&&(e[b]="")});e.parentCode=d.nodeid;h({postData:e})}else 0<l.length&&(h({postData:{}}),h({url:l.attr("action"),postData:l.serializeArray()}));f.expandNodeIds&&0<f.expandNodeIds.length&&js.loading()}else 0<
l.length?(h({postData:{}}),h({url:l.attr("action"),postData:l.serializeArray()})):(d=p("postData"),c.postData.orderBy&&(d.orderBy=c.postData.orderBy));"function"==typeof c.ajaxLoad&&c.ajaxLoad(b);a(".btn").attr("disabled",!0);a(".ui-jqgrid .loading").remove()},loadComplete:function(b){1<=w.length&&b&&(b.funcParam=m,c.treeGrid?void 0==p("postData").parentCode&&(w.html(pageHtml(b,y)),pageHtmlControlInput(y)):(w.html(pageHtml(b,y)),pageHtmlControlInput(y)));c.treeGrid&&(f.expandNodeIds?setTimeout(function(){if(0<
f.expandNodeIds.length){var c=a("#"+f.expandNodeIds.shift()+":visible .tree-plus",f);0==c.length?js.closeLoading(0,!0):c.click()}else f.currentLevel<f.expandLevel?(f.currentLevel++,f.expandNodeIds=[],a(".jqgrow:visible .tree-plus",f).each(function(){var c=a(this).parents(".jqgrow").attr("id");f.expandNodeIds.push(c)}),a("#"+f.expandNodeIds.shift()+":visible .tree-plus",f).click()):f.expandNodeIds=null,js.closeLoading(0,!0)},10):c.defaultExpandLevel&&0<c.defaultExpandLevel&&(z(c.defaultExpandLevel),
c.defaultExpandLevel=0),h({postData:{id:"",nodeid:""}}));c.editGrid&&(a(function(){var a=f.jqGrid("getDataIDs");if(0<a.length){if(1==c.editGridInitAllRowEdit)for(var b=0;b<a.length;b++)f.jqGrid("editRow",a[b],{keys:c.editGridAddRowKeys,focusField:!1})}else if(c.editGridInitRowNum&&c.editGridAddRow)for(b=0;b<c.editGridInitRowNum;b++)c.editGridAddRow("init");"function"==typeof c.editGridInitRowSuccess&&c.editGridInitRowSuccess(a)}),c.editGridAddRowBtnToHeader&&0==a("#jqgh_"+m+"_rn .headerAddRowBtn").size()&&
0<c.editGridAddRowBtn.length&&a("#jqgh_"+m+"_rn").append('<a href="javascript:" class="headerAddRowBtn" onclick="$(\'#'+c.editGridAddRowBtn.attr("id")+'\').click();"><i class="fa fa-plus"></i></a>'));if(c.emptyDataHint)if(0==f.jqGrid("getGridParam","reccount")){if(0==a("#jqgh_"+m+"_edh").size()){var d="function"==typeof c.emptyDataHintContent?c.emptyDataHintContent():c.emptyDataHintContent;f.parent().append('<div id="jqgh_'+m+'_edh" class="mt5 mb20">'+d+"</div>")}}else a("#jqgh_"+m+"_edh").remove();
"function"==typeof c.ajaxSuccess&&c.ajaxSuccess(b);"function"==typeof c.btnEventBind&&(c.btnEventBind(f.find(".btnList")),c.btnEventBind(a("#"+m+"__frozen").find(".btnList")),c.btnEventBind(a("#"+m+"__frozen_right").find(".btnList")));"function"==typeof c.btnMoreEventBind&&(c.btnMoreEventBind(f.find(".btnMore")),c.btnMoreEventBind(a("#"+m+"__frozen").find(".btnMore")),c.btnMoreEventBind(a("#"+m+"__frozen_right").find(".btnMore")));b&&b.message&&js.showMessage(b.message);x.scrollTop&&(x.scrollTop=
!1,f.parent().scrollTop(0));a(".btn").attr("disabled",!1);js.closeLoading()},loadError:function(b){"function"==typeof c.ajaxError&&c.ajaxError(b);a(".btn").attr("disabled",!1);js.showErrorMessage(b.responseText);js.closeLoading(0,!0)},gridComplete:function(){"function"==typeof c.complete&&c.complete();q()},onSortCol:function(a,b,d){b="";a&&(b=a+" "+d);"object"==typeof c.postData&&(c.postData.orderBy=b);c.inputOrderBy&&c.inputOrderBy.length&&c.inputOrderBy.val(b)},btnEventBind:function(b){b.each(function(){n("click",
a(this),function(b){var d=a(this);b=d.data("href")||d.attr("href");var h=0==d.data("title")?!1:d.data("title")||d.attr("title")||d.text(),e=d.data("confirm");void 0!=e?js.confirm(e,b,function(a){js.showMessage(a.message);if(a.result==Global.TRUE)if(a=d.data("confirmSuccess"),void 0!=a)try{eval(a)}catch(J){log("confirmSuccess error: "+J)}else c.treeGrid?(a=d.data("deltreenode"),void 0!=a?f.dataGrid("delTreeNode",a):(a=f.jqGrid("getRowData",d.parents(".jqgrow").attr("id")))&&!t(a.parentCode)?F(a.parentCode):
E()):v()},"json"):js.addTabPage(a(this),h,b,c.tabPageId);return!1})});return x},btnMoreEventBind:function(c){c.each(function(){var c;n("mouseover",a(this),function(){var b=a("#gview_"+m),d=f.closest(".ui-jqgrid-bdiv"),h=a(this),e=h.next(),k=h.position1x(),g=k.top-e.height()/2+6;k=k.left;if(e.hasClass("moreItems")){b.find(".btnMore.open").removeClass("open").find("i").removeClass("fa-chevron-circle-left").addClass("fa-chevron-circle-right");b.find(".moreItems.open").removeClass("open").hide();b=d.scrollTop()+
5;g<=b?g=b:(b=d.height()-e.height()+b-12,g>=b&&(g=b));b=h.closest(".frozen-bdiv");if(0<b.length){var v=h.closest("tr").index();e=f.find("tr:eq("+v+") .moreItems");k=d.scrollLeft()+b.position1x().left+k}k=k+e.width()>d.width()-80?k-e.width():k+h.width();h.addClass("open").find("i").removeClass("fa-chevron-circle-right").addClass("fa-chevron-circle-left");e.addClass("open").css({top:g,left:k}).show();void 0==e.attr("data-hover-binded")&&(e.attr("data-hover-binded",!0),e.hover(function(){window.clearTimeout(c)},
function(){window.clearTimeout(c);c=window.setTimeout(function(){h.removeClass("open").find("i").removeClass("fa-chevron-circle-left").addClass("fa-chevron-circle-right");e.hide()},500)}).find("a").click(function(){h.removeClass("open").find("i").removeClass("fa-chevron-circle-left").addClass("fa-chevron-circle-right");e.hide()}))}})});return x},ajaxLoad:function(a){},ajaxSuccess:function(a){},ajaxError:function(a){},complete:function(){}},c);if(0<c.columnModel.length){c.colNames=[];c.colModel=[];
for(var G=b=0,r,K=c.columnModel.length;G<K;G++)r=c.columnModel[G],r.header?c.colNames.push(r.header):r.label&&c.colNames.push(r.label),!c.treeGrid&&c.sortableColumn||void 0!=r.sortable||(r.sortable=!1),"actions"===r.name&&(void 0==r.frozen&&(r.frozen=!0),void 0==r.sortable&&(r.sortable=!1),void 0==r.fixed&&(r.fixed=!0),void 0==r.title&&(r.title=!1)),c.frozenCols&&(r.frozen||b++,r.frozen&&0<b&&(r.fixed=!0)),c.colModel.push(r)}c.treeGrid&&(c.showRownum=!1);c.showRownum&&(c.rownumbers=!0);c.showCheckbox&&
(c.multiselect=!0);"local"==c.datatype&&-1==c.rowNum&&(c.rowNum=5E3);c.lazyLoad&&!c.treeGrid&&(c.datatype_bak=c.datatype,c.datatype="local");if(c.groupHeaders||c.frozenCols)c.sortable=!1,c.shrinkToFit=!1;var I=c.shrinkToFit;c.showFooter&&(c.footerrow=!0,c.userDataOnFooter=!0);0<l.length&&(0==c.inputPageNo.length&&(b=l.data("pageNo"),l.append('<input id="pageNo" name="pageNo" type="hidden" value="'+(b?b:"")+'"/>'),c.inputPageNo=a("#pageNo",l)),0==c.inputPageSize.length&&(b=l.data("pageSize"),l.append('<input id="pageSize" name="pageSize" type="hidden" value="'+
(b?b:"")+'"/>'),c.inputPageSize=a("#pageSize",l)),0==c.inputOrderBy.length&&(b=l.data("orderBy"),l.append('<input id="orderBy" name="orderBy" type="hidden" value="'+(b?b:"")+'"/>'),c.inputOrderBy=a("#orderBy",l)),c.validate&&a.fn.validate?l.validate({submitHandler:function(a){v(1);return!1}}):l.submit(function(){v(1);return!1}),l.on("reset",function(){l.find(".isReset").each(function(){a(this).val(a(this).data("defaultValue"))});setTimeout(function(){void 0!==a.fn.iCheck&&l.find("input[type=checkbox].form-control:not(.noicheck),input[type=radio].form-control:not(.noicheck)").iCheck("update");
void 0!==a.fn.select2&&l.find("select.form-control:not(.noselect2)").trigger("change.select2");0<l.find(".isQuick").length&&l.submit()},200)}),n("change",l.find(".isQuick"),function(){l.submit()}),0<c.btnSearch.length&&(n("click",c.btnSearch,function(){var b=a(this);l.hasClass("hide")?(l.removeClass("hide"),b.addClass("active"),b.html(b.find("i").prop("outerHTML")+" "+a.jgrid.extend.btnHideSearch)):(l.addClass("hide"),b.removeClass("active"),b.html(b.find("i").prop("outerHTML")+" "+a.jgrid.extend.btnSearch));
q();return!1}),l.hasClass("hide")||(b=c.btnSearch,b.addClass("active"),b.html(b.find("i").prop("outerHTML")+" "+a.jgrid.extend.btnHideSearch))),c.treeGrid&&(n("click",c.btnRefreshTree,function(){l.each(function(){this.reset()});0==l.find(".isQuick").length&&l.submit();return!1}),n("click",c.btnExpandTreeNode,function(){z(1);return!1}),n("click",c.btnCollapseTreeNode,function(){B();return!1})));"function"==typeof c.btnEventBind&&c.btnEventBind(a(".btnTool"));0<c.btnSetting.length&&n("click",c.btnSetting,
function(){f.jqGrid("columnChooser")});c.editGrid&&(f.addClass("editgrid"),n("click",c.editGridAddRowBtn,function(){c.editGridAddRow&&c.editGridAddRow("new");return!1}),c.editGridInputForm&&0<c.editGridInputForm.length&&""!=c.editGridInputFormListName&&""!=c.editGridInputFormListAttrs&&c.editGridInputForm.submit(function(){D()}));var x={jqGrid:function(a,b,c,d,h,e){return f.jqGrid(a,b,c,d,h,e)},setParam:function(a,b){return h(a,b)},getParam:function(a){return p(a)},getDataIDs:function(){return f.jqGrid("getDataIDs")},
getRowData:function(a){return f.jqGrid("getRowData",a)},getSelectRow:function(){return p("selrow")},getSelectRows:function(){return p("selarrrow")},setSelectRow:function(a,b){void 0==a&&void 0==b?f.jqGrid("resetSelection"):void 0!=a&&b?f.jqGrid("resetSelection",a):f.jqGrid("setSelection",a);return x},resize:function(){return q()},refresh:function(a,b){return v(a,b)},reloadGrid:function(){return u()},delRowData:function(a){f.jqGrid("delRowData",a);return x},delTreeNode:function(a){return H(a)},expandTreeNode:function(a){return z(a)},
collapseTreeNode:function(){B()},refreshTree:function(a,b){return E(a,b)},refreshTreeChildren:function(a,b){return F(a,b)},mergeCell:function(a){return C(a)},updateListFieldName:function(a,b){D(a,b)},initialize:function(){d()}};return x};a.fn.dataGrid=function(c,d,e,g,k,q){var n=this.each(function(){var v=a(this),u=v.data("dataGrid"),p="object"===typeof c&&c;u||(u=new b(p,v),v.data("dataGrid",u),u.initialize());"string"===typeof c&&"function"===typeof u[c]&&(h=d instanceof Array?u[c].apply(u,d,e,
g,k,q):u[c](d,e,g,k,q))});if(void 0===h){var t=a.jgrid.getMethod(c);if(t){var p=a.makeArray(arguments).slice(1);var h=t.apply(this,p)}}return void 0===h?n:h};a.extend(a.jgrid,{stripHtml:function(a){a=String(a);return a.replace(/<[^>]*>/g,"")}})})(jQuery);function page(a,b,c,d){try{if(d){let last=parseInt(d/b);0==d%b&&0!=last||last++;if(last<1){last=1}if(a>last){a=last}};c&&""!=c?$("#"+c).dataGrid("refresh",a,b):$(".ui-jqgrid-btable:eq(0)").dataGrid("refresh",a,b)}catch(d){}return!1}
function pageHtml(a,b){a.pageNo||(a.pageNo=1);a.pageSize||(a.pageSize=20);a.count||(a.count=0);a.bothNum||(a.bothNum=1);a.centerNum||(a.centerNum=5);a.funcName||(a.funcName="page");a.funcParam||(a.funcParam="");a.pageInfo||(a.pageInfo="");0>=a.pageSize&&(a.pageSize=20);a.first=1;a.last=parseInt(a.count/a.pageSize)+a.first-1;0==a.count%a.pageSize&&0!=a.last||a.last++;a.last<a.first&&(a.last=a.first);a.pageNo<=a.first&&(a.pageNo=a.first);a.pageNo>=a.last&&(a.pageNo=a.last);a.prev=1<a.pageNo?a.pageNo-
1:a.first;a.next=a.pageNo<a.last-1?a.pageNo+1:a.last;a.centerBegin=a.pageNo-parseInt(a.centerNum/2);a.centerBegin<a.first&&(a.centerBegin=a.first);a.centerEnd=a.centerBegin+a.centerNum-1;a.centerEnd>=a.last&&(a.centerEnd=a.last,a.centerBegin=a.centerEnd-a.centerNum+1);a.centerBegin==a.first&&(a.centerEnd+=a.bothNum);a.centerEnd==a.last&&(a.centerBegin-=a.bothNum);a.last-a.centerEnd>a.bothNum||a.centerBegin--;a.centerBegin<a.first&&(a.centerBegin=a.first);var c=[],d;c.push(pageHtmlControlAfter(a,b));
c.push('<ul class="pagination">\n');a.pageNo==a.first?c.push('<li class="disabled"><a href="javascript:"><i class="fa fa-angle-left"></i></a></li>\n'):c.push('<li><a href="javascript:" onclick="'+a.funcName+"("+a.prev+","+a.pageSize+",'"+a.funcParam+'\');"> <i class="fa fa-angle-left"></i></a></li>\n');for(d=a.first;d<a.first+a.bothNum&&d<a.centerBegin;d++)c.push('<li><a href="javascript:" onclick="'+a.funcName+"("+d+","+a.pageSize+",'"+a.funcParam+"');\">"+(d+1-a.first)+"</a></li>\n");d<a.centerBegin?
c.push('<li class="disabled"><a href="javascript:">...</a></li>\n'):a.centerEnd++;a.centerEnd>a.last&&(a.centerEnd=a.last);for(d=a.centerBegin;d<=a.centerEnd;d++)d==a.pageNo?c.push('<li class="active"><a href="javascript:">'+(d+1-a.first)+"</a></li>\n"):c.push('<li><a href="javascript:" onclick="'+a.funcName+"("+d+","+a.pageSize+",'"+a.funcParam+"');\">"+(d+1-a.first)+"</a></li>\n");a.last-a.centerEnd>a.bothNum&&(c.push('<li class="disabled"><a href="javascript:">...</a></li>\n'),a.centerEnd=a.last-
a.bothNum);for(d=a.centerEnd+1;d<=a.last;d++)c.push('<li><a href="javascript:" onclick="'+a.funcName+"("+d+","+a.pageSize+",'"+a.funcParam+"');\">"+(d+1-a.first)+"</a></li>\n");a.pageNo==a.last?c.push('<li class="disabled"><a href="javascript:"><i class="fa fa-angle-right"></i></a></li>\n'):c.push('<li><a href="javascript:" onclick="'+a.funcName+"("+a.next+","+a.pageSize+",'"+a.funcParam+'\');"><i class="fa fa-angle-right"></i></a></li>\n');c.push("</ul>\n");c.push(pageHtmlControl(a,b));c.push('<div style="clear:both;"></div>');
return c.join("")}function pageHtmlControl(a,b){b=['<ul class="pagination"><li class="disabled controls" title="'+$.jgrid.extend.pageTitle+'">'];b.push((a.pageInfo&&""!=a.pageInfo?a.pageInfo:"")+" ");b.push($.jgrid.format($.jgrid.extend.pageLabelA,a.count));b.push("</li></ul>\n");return b.join("")}
function pageHtmlControlAfter(a,b){var c=['<ul class="pagination"><li class="disabled controls after" title="'+$.jgrid.extend.pageTitle+'">'];c.push($.jgrid.extend.pageLabelB+" ");c.push('<input type="text" value="'+a.pageSize+'" onkeyup="this.value=this.value.replace(/[^0-9]+/,\'\')"');c.push(' onchange="'+a.funcName+"("+a.pageNo+",this.value,'"+a.funcParam+"',"+a.count+")\" ");c.push(" oninput=\"pageHtmlControlInput('"+b+'\')" onclick="this.select()"/> ');c.push($.jgrid.extend.pageLabelC+" ");c.push('<input type="text" value="'+
a.pageNo+'" onkeyup="this.value=this.value.replace(/[^0-9]+/,\'\')"');c.push(' onchange="'+a.funcName+"(this.value,"+a.pageSize+",'"+a.funcParam+"',"+a.count+")\"");c.push(" oninput=\"pageHtmlControlInput('"+b+'\')" onclick="this.select()"/> ');c.push($.jgrid.extend.pageLabelD+" ");c.push("</li></ul>\n");return c.join("")}function pageHtmlControlInput(a){$("#"+a).find(".controls input").each(function(){var a=8*(this.value.length+1);this.style.width=(22>a?22:a)+"px"})}
(function(a){a.jgrid.extend({initFormMore:function(){this.each(function(){var b=a(this),c=b.getGridParam("searchForm");if(c&&0<c.size()){var d=c.find(".btnFormMore"),e=c.find(".form-more");d.click(function(){e.is(":hidden")?(e.slideDown(50),d.addClass("active")):(e.slideUp(50),d.removeClass("active"))});b.bind("jqGridLoadComplete",function(){e.slideUp(50);d.removeClass("active")})}})},getIdsByLevel:function(a){var b=[];this.each(function(){if(this.grid&&this.p.treeGrid)for(var c=this.p.treeReader.leaf_field,
e=this.p.treeReader.level_field,g=this.p.data,k=0;k<g.length;k++)g[k][c]||g[k][e]!=a||b.push(g[k]._id_)});return b},delTreeChildNode:function(b){return this.each(function(){var c=this.p.localReader.id,d=this.p.treeReader.loaded;if(this.grid&&this.p.treeGrid){var e=this.p._index[b];if(void 0!==e){var g=this.p.data[e],k=a(this).jqGrid("getNodeChildren",g);if(0<k.length)for(e=0;e<k.length;e++)a(this).jqGrid("delRowData",k[e][c]);a(this).jqGrid("collapseRow",g);a(this).jqGrid("collapseNode",g);g[d]=void 0}}})}})})(jQuery);
(function(a){a.jgrid.extend({setComplexHeaders:function(b){b=a.extend({complexHeaders:{defaultStyle:!0,threeLevel:[],twoLevel:[]}},b||{});return this.each(function(){var c=b.complexHeaders,d=c.threeLevel,e=c.twoLevel;if(0!==d.length&&0!==e.length){this.p.complexHeader=b;var g=void 0===c.defaultStyle?!0:c.defaultStyle,k,q=0,n=0,t=!1,p,h=this.p.colModel,v=h.length,u=this.grid.headers;c=a("table.ui-jqgrid-htable",this.grid.hDiv);var H=c.children("thead").children("tr.ui-jqgrid-labels:last").addClass("jqg-second-row-header");
var z=c.children("thead");var B=c.find(".jqg-first-row-header");void 0===B[0]?B=a("<tr>",{role:"row","aria-hidden":"true"}).addClass("jqg-first-row-header").css("height","auto"):B.empty();var E=function(a,b){for(var c=0,d=b.length;c<d;c++)if(b[c]&&b[c].startColumnName===a)return c;return-1};a(this).prepend(z);z=a("<tr>",{role:"rowheader"}).addClass("ui-jqgrid-labels jqg-third-row-header");var F=a("<tr>",{role:"rowheader"}).addClass("ui-jqgrid-labels jqg-four-row-header");for(k=0;k<v;k++){var C=u[k].el;
var D=a(C);var f=h[k];var m={height:"0px",width:u[k].width+"px",display:f.hidden?"none":""};a("<th>",{role:"gridcell"}).css(m).addClass("ui-first-th-"+this.p.direction).appendTo(B);C.style.width="";m=E(f.name,d);if(0<=m){var w=d[m];var y=parseInt(w.numberOfColumns,10);var l=w.titleText;for(m=p=0;m<y&&k+m<v;m++)h[k+m].hidden||p++;l=a("<th>").attr({role:"columnheader",noWrap:!0}).addClass("ui-state-default ui-th-column-header ui-th-"+this.p.direction+" "+(w.classes||"")).html(l);0<p&&l.attr("colspan",
String(p));this.p.headertitles&&l.attr("title",l.text());0===p&&l.hide();D.before(l);q=y;if(!1===g)for(t=!1,p=0;p<q&&p+k<v;p++)if(m=E(h[p+k].name,e),0<=m){w=e[m];y=parseInt(w.numberOfColumns,10);for(m=0;m<y&&p+k+m<v;m++)if(!h[p+k+m].hidden){t=!0;break}if(!0===t)break}}f=E(f.name,e);if(0<q&&0<=f){w=e[f];y=parseInt(w.numberOfColumns,10);l=w.titleText;for(m=p=0;m<y&&k+m<v;m++)h[k+m].hidden||p++;l=a("<th>").attr({role:"columnheader",noWrap:!0}).addClass("ui-state-default ui-th-column-header ui-th-"+this.p.direction).html(l);
0<p&&l.attr("colspan",String(p));this.p.headertitles&&l.attr("title",l.text());0===p&&l.hide();z.append(l);n=y}0===q?(D.attr("rowspan","3"),n=0):0<q&&0===n?(g?(D.attr("rowspan","2"),z.append(C)):t?(D.attr("rowspan","2"),z.append(C)):(l.attr("rowspan","2"),F.append(C)),q--):0<q&&0<n&&(F.append(C),q--,n--)}d=a(this).children("thead");d.prepend(B);z.insertAfter(H);F.insertAfter(z);c.append(d);c.find("span.ui-jqgrid-resize").each(function(){var b=a(this).parent();b.is(":visible")&&(this.style.cssText=
"height: "+b.height()+"px !important; cursor: col-resize;")});c.find("div.ui-jqgrid-sortable").each(function(){var b=a(this),c=b.parent();c.is(":visible")&&c.is(":has(span.ui-jqgrid-resize)")&&b.css("top",(c.height()-b.outerHeight())/2+"px")});var G=d.find("tr.jqg-first-row-header");a(this).bind("jqGridResizeStop.setGroupHeaders",function(a,b,c){G.find("th").eq(c).width(b-5)})}})}})})(jQuery);
(function(a){a.jgrid.msie&&8===a.jgrid.msiever()&&(a.expr[":"].hidden=function(a){return 0===a.offsetWidth||0===a.offsetHeight||"none"===a.style.display});a.jgrid.extend({sortableColumns:function(b){return this.each(function(){function c(){d.p.disableClick=!0}var d=this,e=a.jgrid.jqID(d.p.id);e={tolerance:"pointer",axis:"x",scrollSensitivity:"1",items:">th:not(:has(#jqgh_"+e+"_cb,#jqgh_"+e+"_rn,#jqgh_"+e+"_subgrid),:hidden)",placeholder:{element:function(b){return a(document.createElement(b[0].nodeName)).addClass(b[0].className+
" ui-sortable-placeholder ui-state-highlight").removeClass("ui-sortable-helper")[0]},update:function(a,b){b.height(a.currentItem.innerHeight()-parseInt(a.currentItem.css("paddingTop")||0,10)-parseInt(a.currentItem.css("paddingBottom")||0,10));b.width(a.currentItem.innerWidth()-parseInt(a.currentItem.css("paddingLeft")||0,10)-parseInt(a.currentItem.css("paddingRight")||0,10))}},update:function(b,c){b=a(c.item).parent();b=a(">th",b);var e={},g=d.p.id+"_";a.each(d.p.colModel,function(b){e[a.jgrid.jqID((this.name||
"")+"_"+(this.header||this.name||""))]=b});var k=[];b.each(function(){var b=a(">div",this).get(0).id.replace(/^jqgh_/,"").replace(g,"");b=a.jgrid.jqID(b+"_"+(a(">div",this).text().trim()||b||""));e.hasOwnProperty(b)&&k.push(e[b])});a(d).jqGrid("remapColumns",k,!0,!0);a.isFunction(d.p.sortable.update)&&d.p.sortable.update(k);setTimeout(function(){d.p.disableClick=!1},50)}};d.p.sortable.options?a.extend(e,d.p.sortable.options):a.isFunction(d.p.sortable)&&(d.p.sortable={update:d.p.sortable});if(e.start){var g=
e.start;e.start=function(a,b){c();g.call(this,a,b)}}else e.start=c;d.p.sortable.exclude&&(e.items+=":not("+d.p.sortable.exclude+")");e=b.sortable(e);e=e.data("sortable")||e.data("uiSortable");null!=e&&(e.data("sortable").floating=!0)})},columnChooser:function(b){var c=this,d=c[0].p,e={},g={},k={};layer.open({type:1,shade:.01,shadeClose:!0,area:["500px","300px"],title:a.jgrid.col.caption,scrollbar:!0,id:"sort-column",content:'<div class="sort-column"></div>',success:function(b,c){g=d.colModel;k=d.colNames;
(500>a(layer.window).width()||300>a(layer.window).height())&&layer.full(c);b.find(".layui-layer-btn").append('<span class="icheck check_all" title="'+a.jgrid.col.all+'"><label><input type="checkbox" value="all" style="left:-5px;"/> '+a.jgrid.col.all+"</label></span>");var n=b.find(".sort-column"),p=!0;a.each(g,function(b){e[a.jgrid.jqID(this.name+"_"+k[b])]=b;d.frozenCols&&this.frozen||this.hidedlg||(n.append('<span class="icheck" data-name="'+this.name+'" data-header="'+this.header+'"><label><input type="checkbox" '+
(this.hidden?"":"checked")+' value="'+b+'"/> '+this.header+'</label><i class="fa fa-arrows" style="color:#ccc;cursor:move"></i></span>'),this.hidden&&(p=!1))});b.find(".icheck").iCheck();p&&b.find(".check_all").iCheck("check");b.find(".check_all").on("ifChecked",function(){b.find(".icheck").iCheck("check")}).on("ifUnchecked",function(){b.find(".icheck").iCheck("uncheck")});n.sortable&&n.sortable({connectWith:".icheck",handle:"i",placeholder:"icheck"})},btn:['<i class="fa fa-check"></i> '+a.jgrid.col.bSubmit,
'<i class="fa fa-remove"></i> '+a.jgrid.col.bCancel],btn1:function(b,n){d.showColData=[];n.find(".icheck").each(function(b){b=a(this);var h=b.data();h.name&&h.header&&(b.find("input").is(":checked")?(c.jqGrid("showCol",a.jgrid.jqID(h.name+"_"+h.header),!0),d.showColData.push(h)):c.jqGrid("hideCol",a.jgrid.jqID(h.name+"_"+h.header),!0))});var q=[];n.find(".icheck input:checked").each(function(){"all"!=this.value&&q.push(parseInt(this.value,10))});a.each(q,function(){delete e[a.jgrid.jqID(g[this].name+
"_"+k[this])]});a.each(e,function(){var a=parseInt(this,10);var b=q,c=a;if(0<=c){var d=b.slice();var e=d.splice(c,Math.max(b.length-c,c));c>b.length&&(c=b.length);d[c]=a;q=d.concat(e)}else q=b});c.jqGrid("remapColumns",q,!0);c.triggerHandler("jqGridAfterGridComplete")}})},sortableRows:function(b){return this.each(function(){var c=this;c.grid&&!c.p.treeGrid&&a.fn.sortable&&(b=a.extend({cursor:"move",axis:"y",items:".jqgrow"},b||{}),b.start&&a.isFunction(b.start)?(b._start_=b.start,delete b.start):
b._start_=!1,b.update&&a.isFunction(b.update)?(b._update_=b.update,delete b.update):b._update_=!1,b.start=function(d,e){a(e.item).css("border-width","0");a("td",e.item).each(function(a){this.style.width=c.grid.cols[a].style.width});if(c.p.subGrid){var g=a(e.item).attr("id");try{a(c).jqGrid("collapseSubGridRow",g)}catch(k){}}b._start_&&b._start_.apply(this,[d,e]);a(c).triggerHandler("jqGridSortableRowsStart")},b.update=function(d,e){a(e.item).css("border-width","");!0===c.p.rownumbers&&a("td.jqgrid-rownum",
c.rows).each(function(b){a(this).html(b+1+(parseInt(c.p.page,10)-1)*parseInt(c.p.rowNum,10))});b._update_&&b._update_.apply(this,[d,e]);a(c).triggerHandler("jqGridSortableRowsUpdate")},a("tbody:first",c).sortable(b))})},setFrozenColumns:function(){return this.each(function(){if(this.grid){var b=this,c=b.p.colModel,d=0,e=c.length,g=-1,k=!1;if(!0!==b.p.subGrid&&!0!==b.p.treeGrid&&!0!==b.p.cellEdit&&!b.p.sortable&&!b.p.scroll){b.p.rownumbers&&d++;for(b.p.multiselect&&d++;d<e;){if(!0===c[d].frozen)k=
!0,g=d;else break;d++}if(0<=g&&k){c=b.p.caption?a(b.grid.cDiv).outerHeight():0;d=a(".ui-jqgrid-htable","#gview_"+a.jgrid.jqID(b.p.id)).height();b.p.toppager&&(c+=a(b.grid.topDiv).outerHeight());!0===b.p.toolbar[0]&&"bottom"!==b.p.toolbar[1]&&(c+=a(b.grid.uDiv).outerHeight());b.grid.fhDiv=a('<div style="position:absolute;left:0px;top:'+c+"px;height:"+d+'px;z-index:1" class="frozen-div frozen-left ui-state-default ui-jqgrid-hdiv"></div>');b.grid.fbDiv=a('<div style="position:absolute;left:0px;top:'+
(c+d)+'px;overflow:hidden;z-index:1" class="frozen-bdiv frozen-left ui-jqgrid-bdiv"></div>');a("#gview_"+a.jgrid.jqID(b.p.id)).append(b.grid.fhDiv);c=a(".ui-jqgrid-htable","#gview_"+a.jgrid.jqID(b.p.id)).clone(!0);if(b.p.groupHeader||b.p.groupHeaders){a("tr.jqg-first-row-header",c).each(function(){a("th:gt("+g+")",this).remove()});var q=-1,n=-1,t,p;a("tr.jqg-second-row-header th",c).each(function(){t=parseInt(a(this).attr("colspan"),10);if(p=parseInt(a(this).attr("rowspan"),10))q++,n++;t&&(q+=t,n++);
if(q===g)return!1});q!==g&&(n=g);a("tr.jqg-second-row-header",c).each(function(){0<n?a("th:gt("+n+")",this).remove():a("th",this).remove()});a("tr.jqg-third-row-header",c).each(function(){a("th:gt("+g+")",this).remove()});a("tr.jqg-four-row-header",c).each(function(){a("th:gt("+g+")",this).remove()})}else a("tr",c).each(function(){a("th:gt("+g+")",this).remove()});a(c).width(1);a(b.grid.fhDiv).append(c).mousemove(function(a){if(b.grid.resizing)return b.grid.dragMove(a),!1});b.p.footerrow&&(a(".ui-jqgrid-bdiv",
"#gview_"+a.jgrid.jqID(b.p.id)).height(),b.grid.fsDiv=a('<div style="position:absolute;left:0px;bottom:0px;z-index:1" class="frozen-sdiv frozen-left ui-jqgrid-sdiv"></div>'),a("#gview_"+a.jgrid.jqID(b.p.id)).append(b.grid.fsDiv),c=a(".ui-jqgrid-ftable","#gview_"+a.jgrid.jqID(b.p.id)).clone(!0),a("tr",c).each(function(){a("td:gt("+g+")",this).remove()}),a(c).width(1),a(b.grid.fsDiv).append(c));a(b).bind("jqGridResizeStop.setFrozenColumns",function(c,d,e){c=a(".ui-jqgrid-htable",b.grid.fhDiv);a("th:eq("+
e+")",c).width(d);c=a(".ui-jqgrid-btable",b.grid.fbDiv);a("tr:first td:eq("+e+")",c).width(d);b.p.footerrow&&(c=a(".ui-jqgrid-ftable",b.grid.fsDiv),a("tr:first td:eq("+e+")",c).width(d))});a(b).bind("jqGridSortCol.setFrozenColumns",function(c,d,e){c=a("tr.ui-jqgrid-labels:last th:eq("+b.p.lastsort+")",b.grid.fhDiv);d=a("tr.ui-jqgrid-labels:last th:eq("+e+")",b.grid.fhDiv);a("span.ui-grid-ico-sort",c).addClass("ui-state-disabled");a(c).attr("aria-selected","false");a("span.ui-icon-"+b.p.sortorder,
d).removeClass("ui-state-disabled");a(d).attr("aria-selected","true");b.p.viewsortcols[0]||b.p.lastsort===e||(a("span.s-ico",c).hide(),a("span.s-ico",d).show())});a("#gview_"+a.jgrid.jqID(b.p.id)).append(b.grid.fbDiv);a(b.grid.bDiv).scroll(function(){a(b.grid.fbDiv).scrollTop(a(this).scrollTop())});a(b.grid.fbDiv).on("mousewheel DOMMouseScroll MozMousePixelScroll",function(c){var d=c.originalEvent.wheelDelta;c=c.originalEvent.detail;var e=a(b.grid.bDiv).scrollTop();d?a(b.grid.bDiv).scrollTop(e-d):
c&&a(b.grid.bDiv).scrollTop(e+c)});!0===b.p.hoverrows&&a("#"+a.jgrid.jqID(b.p.id)).unbind("mouseover").unbind("mouseout");a(b).bind("jqGridAfterGridComplete.setFrozenColumns",function(){a("#"+a.jgrid.jqID(b.p.id)+"__frozen").remove();a(b.grid.fhDiv).height(a(b.grid.hDiv).height());a(b.grid.fbDiv).height(a(b.grid.bDiv).height()-a.jgrid.scrollsize);var c=[];a("#"+a.jgrid.jqID(b.p.id)+" tr[role=row].jqgrow").each(function(){c.push(a(this).outerHeight())});var d=a("#"+a.jgrid.jqID(b.p.id)).clone(!0);
a("tr[role=row]",d).each(function(){a("td[role=gridcell]:gt("+g+")",this).remove()});a(d).width(1).attr("id",b.p.id+"__frozen");a(b.grid.fbDiv).append(d);a(b.grid.bDiv).scroll();a("tr[role=row].jqgrow",d).each(function(b,d){a(this).height(c[b])});a("#gview_"+a.jgrid.jqID(b.p.id)+" .frozen-left").each(function(){var c=a(b.grid.bDiv).find("tr:first td:eq("+g+")");0<c.length&&(c=c.width()+3,a("tr:first th:last",this).width(c),a("tr:first td:last",this).width(c))});!0===b.p.hoverrows&&(a("tr.jqgrow",
d).hover(function(){a(this).addClass("ui-state-hover");a("#"+a.jgrid.jqID(this.id),"#"+a.jgrid.jqID(b.p.id)).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover");a("#"+a.jgrid.jqID(this.id),"#"+a.jgrid.jqID(b.p.id)).removeClass("ui-state-hover")}),a("tr.jqgrow","#"+a.jgrid.jqID(b.p.id)).hover(function(){a(this).addClass("ui-state-hover");a("#"+a.jgrid.jqID(this.id),"#"+a.jgrid.jqID(b.p.id)+"__frozen").addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover");
a("#"+a.jgrid.jqID(this.id),"#"+a.jgrid.jqID(b.p.id)+"__frozen").removeClass("ui-state-hover")}));d=null});b.grid.hDiv.loading||a(b).triggerHandler("jqGridAfterGridComplete");b.p.frozenColumns=!0}}}})},destroyFrozenColumns:function(){return this.each(function(){if(this.grid&&!0===this.p.frozenColumns){a(this.grid.fhDiv).remove();a(this.grid.fbDiv).remove();this.grid.fhDiv=null;this.grid.fbDiv=null;this.p.footerrow&&(a(this.grid.fsDiv).remove(),this.grid.fsDiv=null);a(this).unbind(".setFrozenColumns");
if(!0===this.p.hoverrows){var b;a("#"+a.jgrid.jqID(this.p.id)).bind("mouseover",function(c){b=a(c.target).closest("tr.jqgrow");"ui-subgrid"!==a(b).attr("class")&&a(b).addClass("ui-state-hover")}).bind("mouseout",function(c){b=a(c.target).closest("tr.jqgrow");a(b).removeClass("ui-state-hover")})}this.p.frozenColumns=!1}})},setFrozenRightColumns:function(){return this.each(function(){if(this.grid){var b=this,c=b.p.colModel,d=c.length-1,e=c.length,g=-1,k=!1;if(!0!==b.p.subGrid&&!0!==b.p.treeGrid&&!0!==
b.p.cellEdit&&!b.p.sortable&&!b.p.scroll){for(;d<e;){if(!0===c[d].frozen)k=!0,g=d;else break;d--}if(0<=g&&k){d=b.p.caption?a(b.grid.cDiv).outerHeight():0;e=a(".ui-jqgrid-htable","#gview_"+a.jgrid.jqID(b.p.id)).height();c=a.jgrid.scrollsize;b.p.toppager&&(d+=a(b.grid.topDiv).outerHeight());!0===b.p.toolbar[0]&&"bottom"!==b.p.toolbar[1]&&(d+=a(b.grid.uDiv).outerHeight());b.grid.fhDivRight=a('<div style="position:absolute;right:'+c+"px;top:"+d+"px;height:"+e+'px;z-index:2" class="frozen-div frozen-right ui-state-default ui-jqgrid-hdiv"></div>');
b.grid.fhDivRightMask=a('<div style="position:absolute;right:0px;width:'+c+"px;top:"+d+"px;height:"+e+'px;z-index:1" class="ui-jqgrid-hdiv"></div>');b.grid.fbDivRight=a('<div style="position:absolute;right:'+c+"px;top:"+(d+e)+'px;overflow:hidden;z-index:1" class="frozen-bdiv frozen-right ui-jqgrid-bdiv"></div>');a("#gview_"+a.jgrid.jqID(b.p.id)).append(b.grid.fhDivRight).append(b.grid.fhDivRightMask);d=a(".ui-jqgrid-htable","#gview_"+a.jgrid.jqID(b.p.id)).clone(!0);if(b.p.groupHeader||b.p.groupHeaders){a("tr.jqg-first-row-header",
d).each(function(){a("th:lt("+g+")",this).remove()});var q=-1,n=-1,t,p;a("tr.jqg-second-row-header th",d).each(function(){t=parseInt(a(this).attr("colspan"),10);if(p=parseInt(a(this).attr("rowspan"),10))q++,n++;t&&(q+=t,n++);if(q===g)return!1});q!==g&&(n=g);a("tr.jqg-second-row-header",d).each(function(){0<n?a("th:lt("+n+")",this).remove():a("th",this).remove()});a("tr.jqg-third-row-header",d).each(function(){a("th:lt("+g+")",this).remove()});a("tr.jqg-four-row-header",d).each(function(){a("th:lt("+
g+")",this).remove()})}else a("tr",d).each(function(){a("th:lt("+g+")",this).remove()});a(d).width(1).find(".ui-jqgrid-resize").css({cursor:"auto"}).addClass("ui-jqgrid-resize-disable");a(b.grid.fhDivRight).append(d).mousemove(function(a){if(b.grid.resizing)return b.grid.dragMove(a),!1});b.p.footerrow&&(a(".ui-jqgrid-bdiv","#gview_"+a.jgrid.jqID(b.p.id)).height(),b.grid.fsDivRight=a('<div style="position:absolute;right:'+c+'px;bottom:0px;z-index:1" class="frozen-sdiv frozen-right ui-jqgrid-sdiv"></div>'),
a("#gview_"+a.jgrid.jqID(b.p.id)).append(b.grid.fsDivRight),c=a(".ui-jqgrid-ftable","#gview_"+a.jgrid.jqID(b.p.id)).clone(!0),a("tr",c).each(function(){a("td:lt("+g+")",this).remove()}),a(c).width(1),a(b.grid.fsDivRight).append(c));a(b).bind("jqGridResizeStop.setFrozenColumns",function(c,d,e){a(b).triggerHandler("jqGridFrozenColumnsToggle")});a(b).bind("jqGridFrozenColumnsToggle",function(c,d,e){c=a(b.grid.bDiv);600>a(window).width()||c[0].scrollWidth<c.innerWidth()?(a(b.grid.fhDiv).hide(),a(b.grid.fsDiv).hide(),
a(b.grid.fbDiv).hide(),a(b.grid.fhDivRight).hide(),a(b.grid.fsDivRight).hide(),a(b.grid.fbDivRight).hide()):(a(b.grid.fhDiv).show(),a(b.grid.fsDiv).show(),a(b.grid.fbDiv).show(),a(b.grid.fhDivRight).show(),a(b.grid.fsDivRight).show(),a(b.grid.fbDivRight).show())});a(b).bind("jqGridSortCol.setFrozenColumns",function(c,d,e){c=b.p.lastsort-g;e-=g;if(!(0>c||0>e)){d=a("tr.ui-jqgrid-labels:first th:eq("+c+")",b.grid.fhDivRight);var h=a("tr.ui-jqgrid-labels:first th:eq("+e+")",b.grid.fhDivRight);a("span.ui-grid-ico-sort",
d).addClass("ui-state-disabled");a(d).attr("aria-selected","false");a("span.ui-icon-"+b.p.sortorder,h).removeClass("ui-state-disabled");a(h).attr("aria-selected","true");b.p.viewsortcols[0]||c===e||(a("span.s-ico",d).hide(),a("span.s-ico",h).show())}});a("#gview_"+a.jgrid.jqID(b.p.id)).append(b.grid.fbDivRight);a(b.grid.bDiv).css({overflow:"scroll"}).scroll(function(){a(b.grid.fbDivRight).scrollTop(a(this).scrollTop())});a(b.grid.fbDivRight).on("mousewheel DOMMouseScroll MozMousePixelScroll",function(c){var d=
c.originalEvent.wheelDelta;c=c.originalEvent.detail;var e=a(b.grid.bDiv).scrollTop();d?a(b.grid.bDiv).scrollTop(e-d):c&&a(b.grid.bDiv).scrollTop(e+c)});!0===b.p.hoverrows&&a("#"+a.jgrid.jqID(b.p.id)).unbind("mouseover").unbind("mouseout");a(b).bind("jqGridAfterGridComplete.setFrozenColumns",function(){a("#"+a.jgrid.jqID(b.p.id)+"__frozen_right").remove();a(b.grid.fhDivRight).height(a(b.grid.hDiv).height());a(b.grid.fbDivRight).height(a(b.grid.bDiv).height()-a.jgrid.scrollsize);var c=[];a("#"+a.jgrid.jqID(b.p.id)+
" tr[role=row].jqgrow").each(function(){c.push(a(this).outerHeight())});var d=a("#"+a.jgrid.jqID(b.p.id)).clone(!0);a("tr[role=row]",d).each(function(){a("td[role=gridcell]:lt("+g+")",this).remove();a(this).hasClass("jqgroup")&&a("td",this).empty()});a(d).width(1).attr("id",b.p.id+"__frozen_right");a(b.grid.fbDivRight).append(d);a(b.grid.bDiv).scroll();a("tr[role=row].jqgrow",d).each(function(b,d){a(this).height(c[b])});a("#gview_"+a.jgrid.jqID(b.p.id)+" .frozen-right").each(function(){var c=a(b.grid.bDiv).find("tr:first td:eq("+
g+")");0<c.length&&(c=c.width()+1,a("tr:first th:first",this).width(c),a("tr:first td:first",this).width(c))});!0===b.p.hoverrows&&(a("tr.jqgrow",d).hover(function(){a(this).addClass("ui-state-hover");a("#"+a.jgrid.jqID(this.id),"#"+a.jgrid.jqID(b.p.id)).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover");a("#"+a.jgrid.jqID(this.id),"#"+a.jgrid.jqID(b.p.id)).removeClass("ui-state-hover")}),a("tr.jqgrow","#"+a.jgrid.jqID(b.p.id)).hover(function(){a(this).addClass("ui-state-hover");
a("#"+a.jgrid.jqID(this.id),"#"+a.jgrid.jqID(b.p.id)+"__frozen_right").addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover");a("#"+a.jgrid.jqID(this.id),"#"+a.jgrid.jqID(b.p.id)+"__frozen_right").removeClass("ui-state-hover")}));d=null});b.grid.hDiv.loading||a(b).triggerHandler("jqGridAfterGridComplete");b.p.frozenColumns=!0}}}})},destroyFrozenRightColumns:function(){return this.each(function(){if(this.grid&&!0===this.p.frozenColumns){a(this.grid.fhDivRight).remove();a(this.grid.fbDivRight).remove();
this.grid.fhDivRight=null;this.grid.fbDivRight=null;this.p.footerrow&&(a(this.grid.fsDivRight).remove(),this.grid.fsDivRight=null);a(this).unbind(".setFrozenColumns");if(!0===this.p.hoverrows){var b;a("#"+a.jgrid.jqID(this.p.id)).bind("mouseover",function(c){b=a(c.target).closest("tr.jqgrow");"ui-subgrid"!==a(b).attr("class")&&a(b).addClass("ui-state-hover")}).bind("mouseout",function(c){b=a(c.target).closest("tr.jqgrow");a(b).removeClass("ui-state-hover")})}this.p.frozenColumns=!1}})}})})(jQuery);
(function(a){a.extend(a.jgrid,{extend:{btnSearch:"\u67e5\u8be2",btnHideSearch:"\u9690\u85cf",expandTooMany:"\u672c\u6b21\u9700\u8981\u5c55\u5f00\u201c{0}\u201d\u4e2a\u8282\u70b9\uff0c\u56e0\u4e3a\u5c55\u5f00\u8282\u70b9\u8fc7\u591a\uff0c\u6267\u884c\u53d6\u6d88\u3002",pageTitle:"\u70b9\u51fb\u6570\u5b57\uff0c\u53ef\u586b\u5199\u9875\u7801\u548c\u6bcf\u9875\u6761\u6570\uff0c\u6309\u56de\u8f66\u5373\u53ef\u751f\u6548\uff01",pageLabelA:"\u5f53\u524d",pageLabelB:"\u9875\uff0c\u6bcf\u9875",pageLabelC:"\u6761\uff0c\u5171 {0} \u6761",
pageLabelD:"\u6761/\u9875",pageLabelE:"\u8df3\u81f3",pageLabelF:"\u9875"},defaults:{recordtext:"{0} - {1}\u3000\u5171 {2} \u6761",emptyrecords:"\u65e0\u6570\u636e\u663e\u793a",loadtext:"\u6b63\u5728\u52a0\u8f7d..."}})})(jQuery);
