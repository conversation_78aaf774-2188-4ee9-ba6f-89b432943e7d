/*!
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 * @version 2020-5-21
 */
/*Grid*/
.ui-jqgrid {position: relative;}
.ui-jqgrid .ui-jqgrid-view {position: relative;left:0; top: 0; padding: 0; font-size: 13px;}
/* caption*/
.ui-jqgrid .ui-jqgrid-titlebar {padding: .3em .2em .2em .3em; position: relative; font-size: 12px; border-left: 0 none;border-right: 0 none; border-top: 0 none;}
.ui-jqgrid .ui-jqgrid-caption {text-align: left;}
.ui-jqgrid .ui-jqgrid-title { margin: .1em 0 .2em; }
.ui-jqgrid .ui-jqgrid-titlebar-close { position: absolute;top: 50%; width: 19px; margin: -10px 0 0 0; padding: 1px; height:18px; cursor:pointer;}
.ui-jqgrid .ui-jqgrid-titlebar-close span { display: block; margin: 1px; }
.ui-jqgrid .ui-jqgrid-titlebar-close:hover { padding: 0; }
/* header*/
.ui-jqgrid .ui-jqgrid-hdiv {position: relative; margin: 0;padding: 0; overflow: hidden; border-left: 0 none !important; border-top : 0 none !important; border-right : 0 none !important;}
.ui-jqgrid .ui-jqgrid-hbox {float: left; padding-right: 20px;}
.ui-jqgrid .ui-jqgrid-htable {table-layout:fixed;margin:0;}
.ui-jqgrid .ui-jqgrid-htable th {height:22px;padding: 0 2px 0 2px;}
.ui-jqgrid .ui-jqgrid-htable th div {overflow: hidden; position:relative; height:17px;}
.ui-th-column, .ui-jqgrid .ui-jqgrid-htable th.ui-th-column {overflow: hidden;white-space: nowrap;text-align:center;border-top : 0 none;border-bottom : 0 none;}
.ui-th-ltr, .ui-jqgrid .ui-jqgrid-htable th.ui-th-ltr {border-left : 0 none;}
.ui-th-rtl, .ui-jqgrid .ui-jqgrid-htable th.ui-th-rtl {border-right : 0 none;}
.ui-first-th-ltr {border-right: 1px solid; }
.ui-first-th-rtl {border-left: 1px solid; }
.ui-jqgrid .ui-th-div-ie {white-space: nowrap; zoom :1; height:17px;}
.ui-jqgrid .ui-jqgrid-resize {height:20px !important;position: relative; cursor :e-resize;display: inline;overflow: hidden;}
.ui-jqgrid .ui-grid-ico-sort {overflow:hidden;position:absolute;display:inline; cursor: pointer !important;}
.ui-jqgrid .ui-icon-asc {margin-top:-3px; height:12px;}
.ui-jqgrid .ui-icon-desc {margin-top:3px;height:12px;}
.ui-jqgrid .ui-i-asc {margin-top:0;height:16px;}
.ui-jqgrid .ui-i-desc {margin-top:0;margin-left:13px;height:16px;}
.ui-jqgrid .ui-jqgrid-sortable {cursor:pointer;}
.ui-jqgrid tr.ui-search-toolbar th { border-top-width: 1px !important; border-top-color: inherit !important; border-top-style: ridge !important }
.ui-jqgrid tr.ui-search-toolbar input {margin: 1px 0 0 0}
.ui-jqgrid tr.ui-search-toolbar select {margin: 1px 0 0 0}
/* body */ 
.ui-jqgrid .ui-jqgrid-bdiv {position: relative; margin: 0; padding:0; overflow: auto; text-align:left;}
.ui-jqgrid .ui-jqgrid-btable {table-layout:fixed; margin:0; outline-style: none; }
.ui-jqgrid tr.jqgrow { outline-style: none; }
.ui-jqgrid tr.jqgroup { outline-style: none; }
.ui-jqgrid tr.jqgrow td {font-weight: normal; overflow: hidden; white-space: pre; height: 22px;padding: 0 2px 0 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid tr.jqgfirstrow td {padding: 0 2px 0 2px;border-right-width: 1px; border-right-style: solid;}
.ui-jqgrid tr.jqgroup td {font-weight: normal; overflow: hidden; white-space: pre; height: 22px;padding: 0 2px 0 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid tr.jqfoot td {font-weight: bold; overflow: hidden; white-space: pre; height: 22px;padding: 0 2px 0 2px;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid tr.ui-row-ltr td {text-align:left;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;}
.ui-jqgrid tr.ui-row-rtl td {text-align:right;border-left-width: 1px; border-left-color: inherit; border-left-style: solid;}
.ui-jqgrid td.jqgrid-rownum { padding: 0 2px 0 2px; margin: 0; border: 0 none;}
.ui-jqgrid .ui-jqgrid-resize-mark { width:2px; left:0; background-color:#777; cursor: e-resize; cursor: col-resize; position:absolute; top:0; height:100px; overflow:hidden; display:none; border:0 none; z-index: 99999;}
/* footer */
.ui-jqgrid .ui-jqgrid-sdiv {position: relative; margin: 0;padding: 0; overflow: hidden; border-left: 0 none !important; border-top : 0 none !important; border-right : 0 none !important;}
.ui-jqgrid .ui-jqgrid-ftable {table-layout:fixed; margin-bottom:0;}
.ui-jqgrid tr.footrow td {font-weight: bold; overflow: hidden; white-space:nowrap; height: 21px;padding: 0 2px 0 2px;border-top-width: 1px; border-top-color: inherit; border-top-style: solid;}
.ui-jqgrid tr.footrow-ltr td {text-align:left;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;}
.ui-jqgrid tr.footrow-rtl td {text-align:right;border-left-width: 1px; border-left-color: inherit; border-left-style: solid;}
/* Pager*/
.ui-jqgrid .ui-jqgrid-pager { border-left: 0 none !important;border-right: 0 none !important; border-bottom: 0 none !important; margin: 0 !important; padding: 0 !important; position: relative; height: 25px;white-space: nowrap;overflow: hidden;font-size: 13px;}
.ui-jqgrid .ui-pager-control {position: relative;}
.ui-jqgrid .ui-pg-table {position: relative; padding-bottom:2px; width:auto; margin: 0;}
.ui-jqgrid .ui-pg-table td {font-weight:normal; vertical-align:middle; padding:1px;}
.ui-jqgrid .ui-pg-button  { height:19px !important;}
.ui-jqgrid .ui-pg-button span { display: block; margin: 1px; float:left;}
.ui-jqgrid .ui-pg-button:hover { padding: 0; }
.ui-jqgrid .ui-state-disabled:hover {padding:1px;}
.ui-jqgrid .ui-pg-input { height:13px;font-size:.8em; margin: 0;}
.ui-jqgrid .ui-pg-selbox {font-size:.8em; line-height:18px; display:block; height:18px; margin: 0;}
.ui-jqgrid .ui-separator {height: 18px; border-left: 1px solid #ccc ; border-right: 1px solid #ccc ; margin: 1px; float: right;}
.ui-jqgrid .ui-paging-info {font-weight: normal;height:19px; margin-top:3px;margin-right:4px;}
.ui-jqgrid .ui-jqgrid-pager .ui-pg-div {padding:1px 0;float:left;position:relative;}
.ui-jqgrid .ui-jqgrid-pager .ui-pg-button { cursor:pointer; }
.ui-jqgrid .ui-jqgrid-pager .ui-pg-div  span.ui-icon {float:left;margin:0 2px;}
.ui-jqgrid td input, .ui-jqgrid td select, .ui-jqgrid td textarea { margin: 0;}
.ui-jqgrid td textarea {width:auto;height:auto;}
.ui-jqgrid .ui-jqgrid-toppager {border-left: 0 none !important;border-right: 0 none !important; border-top: 0 none !important; margin: 0 !important; padding: 0 !important; position: relative; height: 25px !important;white-space: nowrap;overflow: hidden;}
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-div {padding:1px 0;float:left;position:relative;}
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-button { cursor:pointer; }
.ui-jqgrid .ui-jqgrid-toppager .ui-pg-div  span.ui-icon {float:left;margin:0 2px;}
/*subgrid*/
.ui-jqgrid .ui-jqgrid-btable .ui-sgcollapsed {text-align:center;}
.ui-jqgrid .ui-jqgrid-btable .ui-sgcollapsed span {display:inline-block;margin-top:3px;}
.ui-jqgrid .ui-subgrid {margin:0;padding:0; width:100%;}
.ui-jqgrid .ui-subgrid table {table-layout: fixed;}
.ui-jqgrid .ui-subgrid tr.ui-subtblcell td {height:18px;border-right-width: 1px; border-right-color: inherit; border-right-style: solid;border-bottom-width: 1px; border-bottom-color: inherit; border-bottom-style: solid;}
.ui-jqgrid .ui-subgrid td.subgrid-data {border-top:  0 none !important;}
.ui-jqgrid .ui-subgrid td.subgrid-cell {border-width: 0 0 1px 0;}
.ui-jqgrid .ui-th-subgrid {height:20px;}
/* loading */
.ui-jqgrid .loading {position: absolute; top: 45%;left: 45%;width: auto;z-index:101;padding: 6px; margin: 5px;text-align: center;font-weight: bold;display: none;border-width: 2px !important; font-size: 13px;}
.ui-jqgrid .jqgrid-overlay {display:none;z-index:100;}
/* IE * html .jqgrid-overlay {width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');} */
* .jqgrid-overlay iframe {position:absolute;top:0;left:0;z-index:-1;}
/* IE width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');}*/
/* end loading div */
/* toolbar */
.ui-jqgrid .ui-userdata {border-left: 0 none;    border-right: 0 none;	height : 21px;overflow: hidden;	}
/*Modal Window */
.ui-jqgrid .ui-jqdialog { font-size: 13px; }
.ui-jqdialog { display: none; width: 300px; position: absolute; padding: .2em; font-size: 13px; overflow:visible;}
.ui-jqdialog .ui-jqdialog-titlebar { padding: .3em .2em; position: relative;  }
.ui-jqdialog .ui-jqdialog-title { margin: .1em 0 .2em; } 
.ui-jqdialog .ui-jqdialog-titlebar-close { position: absolute;  top: 50%; width: 19px; margin: -10px 0 0 0; padding: 1px; height: 18px; cursor:pointer;}
.ui-jqdialog .ui-jqdialog-titlebar-close span { display: block; margin: 1px; }
.ui-jqdialog .ui-jqdialog-titlebar-close:hover, .ui-jqdialog .ui-jqdialog-titlebar-close:focus { padding: 0; }
.ui-jqdialog-content, .ui-jqdialog .ui-jqdialog-content { border: 0; padding: .3em .2em; background: none; height:auto;}
.ui-jqdialog .ui-jqconfirm {padding: .4em 1em; border-width:3px;position:absolute;bottom:10px;right:10px;overflow:visible;display:none;height:80px;width:220px;text-align:center;}
.ui-jqdialog>.ui-resizable-se { bottom: -3px; right: -3px}
.ui-jqgrid>.ui-resizable-se { bottom: -3px; right: -3px }
/* end Modal window*/
/* Form edit */
.ui-jqdialog-content .FormGrid {margin: 0;}
.ui-jqdialog-content .EditTable { width: 100%; margin-bottom:0;}
.ui-jqdialog-content .DelTable { width: 100%; margin-bottom:0;}
.EditTable td input, .EditTable td select, .EditTable td textarea {margin: 0;}
.EditTable td textarea { width:auto; height:auto;}
.ui-jqdialog-content td.EditButton {text-align: right;border-top: 0 none;border-left: 0 none;border-right: 0 none; padding-bottom:5px; padding-top:5px;}
.ui-jqdialog-content td.navButton {text-align: center; border-left: 0 none;border-top: 0 none;border-right: 0 none; padding-bottom:5px; padding-top:5px;}
.ui-jqdialog-content input.FormElement {padding:.3em}
.ui-jqdialog-content select.FormElement {padding:.3em}
.ui-jqdialog-content .data-line {padding-top:.1em;border: 0 none;}

.ui-jqdialog-content .CaptionTD {vertical-align: middle;border: 0 none; padding: 2px;white-space: nowrap;}
.ui-jqdialog-content .DataTD {padding: 2px; border: 0 none; vertical-align: top;}
.ui-jqdialog-content .form-view-data {white-space:pre}
.fm-button { display: inline-block; margin:0 4px 0 0; padding: .4em .5em; text-decoration:none !important; cursor:pointer; position: relative; text-align: center; zoom: 1; }
.fm-button-icon-left { padding-left: 1.9em; }
.fm-button-icon-right { padding-right: 1.9em; }
.fm-button-icon-left .ui-icon { right: auto; left: .2em; margin-left: 0; position: absolute; top: 50%; margin-top: -8px; }
.fm-button-icon-right .ui-icon { left: auto; right: .2em; margin-left: 0; position: absolute; top: 50%; margin-top: -8px;}
#nData, #pData { float: left; margin:3px;padding: 0; width: 15px; }
/* End Eorm edit */
/*.ui-jqgrid .edit-cell {}*/
.ui-jqgrid .selected-row, div.ui-jqgrid .selected-row td {font-style : normal;border-left: 0 none;}
/* inline edit actions button*/
.ui-inline-del.ui-state-hover span, .ui-inline-edit.ui-state-hover span,
.ui-inline-save.ui-state-hover span, .ui-inline-cancel.ui-state-hover span { margin: -1px; }
/* Tree Grid */
.ui-jqgrid .tree-wrap {float: left; position: relative;height: 18px;white-space: nowrap;overflow: hidden;}
.ui-jqgrid .tree-minus {position: absolute; height: 18px; width: 18px; overflow: hidden;}
.ui-jqgrid .tree-plus {position: absolute;	height: 18px; width: 18px;	overflow: hidden;}
.ui-jqgrid .tree-leaf {position: absolute;	height: 18px; width: 18px;overflow: hidden;}
.ui-jqgrid .treeclick {cursor: pointer;}
/* moda dialog */
* iframe.jqm {position:absolute;top:0;left:0;z-index:-1;}
/*	 width: expression(this.parentNode.offsetWidth+'px');height: expression(this.parentNode.offsetHeight+'px');}*/
.ui-jqgrid-dnd tr td {border-right-width: 1px; border-right-color: inherit; border-right-style: solid; height:20px}
/* RTL Support */
.ui-jqgrid .ui-jqgrid-caption-rtl {text-align: right;}
.ui-jqgrid .ui-jqgrid-hbox-rtl {float: right; padding-left: 20px;}
.ui-jqgrid .ui-jqgrid-resize-ltr {float: right;margin: -2px -2px -2px 0;}
.ui-jqgrid .ui-jqgrid-resize-rtl {float: left;margin: -2px 0 -1px -3px;}
.ui-jqgrid .ui-sort-rtl {left:0;}
.ui-jqgrid .tree-wrap-ltr {float: left;}
.ui-jqgrid .tree-wrap-rtl {float: right;}
.ui-jqgrid .ui-ellipsis {-moz-text-overflow:ellipsis;text-overflow:ellipsis;}

/* Toolbar Search Menu */
.ui-search-menu { position: absolute; padding: 2px 5px;}
.ui-search-menu.ui-menu .ui-menu-item { list-style-image: none; padding-right: 0; padding-left: 0; }
.ui-search-menu.ui-menu .ui-menu-item a { display: block; }
.ui-search-menu.ui-menu .ui-menu-item a.g-menu-item:hover { margin: -1px; font-weight: normal; }
.ui-jqgrid .ui-search-table { padding: 0; border: 0 none; height:20px; width:100%;}
.ui-jqgrid .ui-search-table .ui-search-oper { width:20px; }
a.g-menu-item, a.soptclass, a.clearsearchclass { cursor: pointer; }
.ui-jqgrid .ui-search-table .ui-search-input>input,
.ui-jqgrid .ui-search-table .ui-search-input>select {display: block;-moz-box-sizing: border-box;-webkit-box-sizing: border-box;box-sizing: border-box;} 
.ui-jqgrid .ui-jqgrid-view input,.ui-jqgrid .ui-jqgrid-view select,
.ui-jqgrid .ui-jqgrid-view textarea,.ui-jqgrid .ui-jqgrid-view button {font-size: 13px}

/* ========== jquery-ui icon for jeesite =========== */

.ui-icon {width:16px;height:16px;background-image:url(images/ui-icons_217bc0_256x240.png)!important;}
.ui-icon-carat-1-n {background-position:0 0;}.ui-icon-carat-1-ne {background-position:-16px 0;}
.ui-icon-carat-1-e {background-position:-32px 0;}.ui-icon-carat-1-se {background-position:-48px 0;}
.ui-icon-carat-1-s {background-position:-64px 0;}.ui-icon-carat-1-sw {background-position:-80px 0;}
.ui-icon-carat-1-w {background-position:-96px 0;}.ui-icon-carat-1-nw {background-position:-112px 0;}
.ui-icon-carat-2-n-s {background-position:-128px 0;}.ui-icon-carat-2-e-w {background-position:-144px 0;}
.ui-icon-triangle-1-n {background-position:0 -16px;}.ui-icon-triangle-1-ne {background-position:-16px -16px;}
.ui-icon-triangle-1-e {background-position:-32px -16px;}.ui-icon-triangle-1-se {background-position:-48px -16px;}
.ui-icon-triangle-1-s {background-position:-65px -16px;}.ui-icon-triangle-1-sw {background-position:-80px -16px;}
.ui-icon-triangle-1-w {background-position:-96px -16px;}.ui-icon-triangle-1-nw {background-position:-112px -16px;}
.ui-icon-triangle-2-n-s {background-position:-128px -16px;}.ui-icon-triangle-2-e-w {background-position:-144px -16px;}
.ui-icon-radio-off {background-position:-96px -144px;}.ui-icon-radio-on {background-position:-112px -144px;}
.ui-icon-arrow-1-n {background-position:0 -32px;}.ui-icon-arrow-1-ne {background-position:-16px -32px;}
.ui-icon-arrow-1-e {background-position:-32px -32px;}.ui-icon-arrow-1-se {background-position:-48px -32px;}
.ui-icon-arrow-1-s {background-position:-64px -32px;}.ui-icon-arrow-1-sw {background-position:-80px -32px;}
.ui-icon-arrow-1-w {background-position:-96px -32px;}.ui-icon-arrow-1-nw {background-position:-112px -32px;}
.ui-icon-arrow-2-n-s {background-position:-128px -32px;}.ui-icon-arrow-2-ne-sw {background-position:-144px -32px;}
.ui-icon-arrow-2-e-w {background-position:-160px -32px;}.ui-icon-arrow-2-se-nw {background-position:-176px -32px;}
.ui-icon-arrowstop-1-n {background-position:-192px -32px;}.ui-icon-arrowstop-1-e {background-position:-208px -32px;}
.ui-icon-arrowstop-1-s {background-position:-224px -32px;}.ui-icon-arrowstop-1-w {background-position:-240px -32px;}
.ui-icon-arrowthick-1-n {background-position:0 -48px;}.ui-icon-arrowthick-1-ne {background-position:-16px -48px;}
.ui-icon-arrowthick-1-e {background-position:-32px -48px;}.ui-icon-arrowthick-1-se {background-position:-48px -48px;}
.ui-icon-arrowthick-1-s {background-position:-64px -48px;}.ui-icon-arrowthick-1-sw {background-position:-80px -48px;}
.ui-icon-arrowthick-1-w {background-position:-96px -48px;}.ui-icon-arrowthick-1-nw {background-position:-112px -48px;}
.ui-icon-arrowthick-2-n-s {background-position:-128px -48px;}.ui-icon-arrowthick-2-ne-sw {background-position:-144px -48px;}
.ui-icon-arrowthick-2-e-w {background-position:-160px -48px;}.ui-icon-arrowthick-2-se-nw {background-position:-176px -48px;}
.ui-icon-arrowthickstop-1-n {background-position:-192px -48px;}.ui-icon-arrowthickstop-1-e {background-position:-208px -48px;}
.ui-icon-arrowthickstop-1-s {background-position:-224px -48px;}.ui-icon-arrowthickstop-1-w {background-position:-240px -48px;}
.ui-icon-arrowreturnthick-1-w {background-position:0 -64px;}.ui-icon-arrowreturnthick-1-n {background-position:-16px -64px;}
.ui-icon-arrowreturnthick-1-e {background-position:-32px -64px;}.ui-icon-arrowreturnthick-1-s {background-position:-48px -64px;}
.ui-icon-arrowreturn-1-w {background-position:-64px -64px;}.ui-icon-arrowreturn-1-n {background-position:-80px -64px;}
.ui-icon-arrowreturn-1-e {background-position:-96px -64px;}.ui-icon-arrowreturn-1-s {background-position:-112px -64px;}
.ui-icon-arrowrefresh-1-w {background-position:-128px -64px;}.ui-icon-arrowrefresh-1-n {background-position:-144px -64px;}
.ui-icon-arrowrefresh-1-e {background-position:-160px -64px;}.ui-icon-arrowrefresh-1-s {background-position:-176px -64px;}
.ui-icon-arrow-4 {background-position:0 -80px;}.ui-icon-arrow-4-diag {background-position:-16px -80px;}
.ui-icon-extlink {background-position:-32px -80px;}.ui-icon-newwin {background-position:-48px -80px;}
.ui-icon-refresh {background-position:-64px -80px;}.ui-icon-shuffle {background-position:-80px -80px;}
.ui-icon-transfer-e-w {background-position:-96px -80px;}.ui-icon-transferthick-e-w {background-position:-112px -80px;}
.ui-icon-folder-collapsed {background-position:0 -96px;}.ui-icon-folder-open {background-position:-16px -96px;}
.ui-icon-document {background-position:-32px -96px;}.ui-icon-document-b {background-position:-48px -96px;}
.ui-icon-note {background-position:-64px -96px;}.ui-icon-mail-closed {background-position:-80px -96px;}
.ui-icon-mail-open {background-position:-96px -96px;}.ui-icon-suitcase {background-position:-112px -96px;}
.ui-icon-comment {background-position:-128px -96px;}.ui-icon-person {background-position:-144px -96px;}
.ui-icon-print {background-position:-160px -96px;}.ui-icon-trash {background-position:-176px -96px;}
.ui-icon-locked {background-position:-192px -96px;}.ui-icon-unlocked {background-position:-208px -96px;}
.ui-icon-bookmark {background-position:-224px -96px;}.ui-icon-tag {background-position:-240px -96px;}
.ui-icon-home {background-position:0 -112px;}.ui-icon-flag {background-position:-16px -112px;}
.ui-icon-calendar {background-position:-32px -112px;}.ui-icon-cart {background-position:-48px -112px;}
.ui-icon-pencil {background-position:-64px -112px;}.ui-icon-clock {background-position:-80px -112px;}
.ui-icon-disk {background-position:-96px -112px;}.ui-icon-calculator {background-position:-112px -112px;}
.ui-icon-zoomin {background-position:-128px -112px;}.ui-icon-zoomout {background-position:-144px -112px;}
.ui-icon-search {background-position:-160px -112px;}.ui-icon-wrench {background-position:-176px -112px;}
.ui-icon-gear {background-position:-192px -112px;}.ui-icon-heart {background-position:-208px -112px;}
.ui-icon-star {background-position:-224px -112px;}.ui-icon-link {background-position:-240px -112px;}
.ui-icon-cancel {background-position:0 -128px;}.ui-icon-plus {background-position:-16px -128px;}
.ui-icon-plusthick {background-position:-32px -128px;}.ui-icon-minus {background-position:-48px -128px;}
.ui-icon-minusthick {background-position:-64px -128px;}.ui-icon-close {background-position:-80px -128px;}
.ui-icon-closethick {background-position:-96px -128px;}.ui-icon-key {background-position:-112px -128px;}
.ui-icon-lightbulb {background-position:-128px -128px;}.ui-icon-scissors {background-position:-144px -128px;}
.ui-icon-clipboard {background-position:-160px -128px;}.ui-icon-copy {background-position:-176px -128px;}
.ui-icon-contact {background-position:-192px -128px;}.ui-icon-image {background-position:-208px -128px;}
.ui-icon-video {background-position:-224px -128px;}.ui-icon-script {background-position:-240px -128px;}
.ui-icon-alert {background-position:0 -144px;}.ui-icon-info {background-position:-16px -144px;}
.ui-icon-notice {background-position:-32px -144px;}.ui-icon-help {background-position:-48px -144px;}
.ui-icon-check {background-position:-64px -144px;}.ui-icon-bullet {background-position:-80px -144px;}
.ui-icon-radio-off {background-position:-96px -144px;}.ui-icon-radio-on {background-position:-112px -144px;}
.ui-icon-pin-w {background-position:-128px -144px;}.ui-icon-pin-s {background-position:-144px -144px;}
.ui-icon-play {background-position:0 -160px;}.ui-icon-pause {background-position:-16px -160px;}
.ui-icon-seek-next {background-position:-32px -160px;}.ui-icon-seek-prev {background-position:-48px -160px;}
.ui-icon-seek-end {background-position:-64px -160px;}.ui-icon-seek-start {background-position:-80px -160px;}
.ui-icon-stop {background-position:-96px -160px;}.ui-icon-eject {background-position:-112px -160px;}
.ui-icon-volume-off {background-position:-128px -160px;}.ui-icon-volume-on {background-position:-144px -160px;}
.ui-icon-power {background-position:0 -176px;}.ui-icon-signal-diag {background-position:-16px -176px;}
.ui-icon-signal {background-position:-32px -176px;}.ui-icon-battery-0 {background-position:-48px -176px;}
.ui-icon-battery-1 {background-position:-64px -176px;}.ui-icon-battery-2 {background-position:-80px -176px;}
.ui-icon-battery-3 {background-position:-96px -176px;}.ui-icon-circle-plus {background-position:0 -192px;}
.ui-icon-circle-minus {background-position:-16px -192px;}.ui-icon-circle-close {background-position:-32px -192px;}
.ui-icon-circle-triangle-e {background-position:-48px -192px;}.ui-icon-circle-triangle-s {background-position:-64px -192px;}
.ui-icon-circle-triangle-w {background-position:-80px -192px;}.ui-icon-circle-triangle-n {background-position:-96px -192px;}
.ui-icon-circle-arrow-e {background-position:-112px -192px;}.ui-icon-circle-arrow-s {background-position:-128px -192px;}
.ui-icon-circle-arrow-w {background-position:-144px -192px;}.ui-icon-circle-arrow-n {background-position:-160px -192px;}
.ui-icon-circle-zoomin {background-position:-176px -192px;}.ui-icon-circle-zoomout {background-position:-192px -192px;}
.ui-icon-circle-check {background-position:-208px -192px;}.ui-icon-circlesmall-plus {background-position:0 -208px;}
.ui-icon-circlesmall-minus {background-position:-16px -208px;}.ui-icon-circlesmall-close {background-position:-32px -208px;}
.ui-icon-squaresmall-plus {background-position:-48px -208px;}.ui-icon-squaresmall-minus {background-position:-64px -208px;}
.ui-icon-squaresmall-close {background-position:-80px -208px;}.ui-icon-grip-dotted-vertical {background-position:0 -224px;}
.ui-icon-grip-dotted-horizontal {background-position:-16px -224px;}.ui-icon-grip-solid-vertical {background-position:-32px -224px;}
.ui-icon-grip-solid-horizontal {background-position:-48px -224px;}.ui-icon-gripsmall-diagonal-se {background-position:-64px -224px;}
.ui-icon-grip-diagonal-se {background-position:-80px -224px;}

/* ========== jqGrid for jeesite =========== */

.ui-widget-content {border:1px solid #e5e5e5;background:#fff;}
.ui-jqgrid .ui-jqgrid-view, .ui-jqgrid .ui-jqgrid-view input,.ui-jqgrid .ui-jqgrid-view select,
.ui-jqgrid .ui-jqgrid-view textarea,.ui-jqgrid .ui-jqgrid-view button {font-size:14px;}
.ui-jqgrid .ui-jqgrid-view textarea {padding-bottom:3px;}
.ui-jqgrid .ui-priority-secondary {background:#fafafa;}
.ui-jqgrid table {border-collapse:separate!important;}

/* header */
.ui-jqgrid .ui-jqgrid-hdiv {line-height:15px;}
.ui-jqgrid .ui-jqgrid-htable th {height:39px;}
.ui-jqgrid .ui-jqgrid-htable th div {padding:9px 0 8px 2px;line-height:17px;}
.ui-jqgrid .ui-jqgrid-labels th {border-right:1px solid #ddd;font-weight:normal;text-align:center;}
.ui-jqgrid .ui-jqgrid-htable th.ui-th-column {border-bottom:1px solid #ddd;position:relative;overflow:visible;}
.ui-jqgrid .ui-jqgrid-htable th.ui-th-column-header {border-bottom:1px solid #ddd;padding:5px 4px;}
.ui-jqgrid .ui-jqgrid-htable .cbox {margin-top:1px;} /* v4.7 */
.ui-jqgrid .ui-jqgrid-htable .ui-th-div {margin-top:0;font-weight:normal;}
.ui-jqgrid .ui-jqgrid-htable .jqg-third-row-header .ui-th-div {margin-top:3px;}
.ui-jqgrid .ui-state-disabled {opacity:.35;filter:Alpha(Opacity=35);background-image:none;}
.ui-jqgrid .ui-jqgrid-resize__ {position:absolute;right:-2px;top:2px;z-index:1;width:8px;height:34px!important;}
.ui-jqgrid .ui-jqgrid-resize {height:35px !important;}
.ui-jqgrid .ui-icon-desc {margin-top:4px;margin-left:0;}
.ui-jqgrid .ui-icon-asc {margin-top:-2px;}
.ui-jqgrid .ui-search-toolbar {border:0;}
.ui-jqgrid .ui-search-toolbar th div {padding:2px 0;}
.ui-jqgrid .ui-search-toolbar th.ui-th-column {border-right:1px solid #ddd;}
.ui-jqgrid .ui-search-toolbar th.ui-th-column input {border:1px solid #ddd;font-weight:normal;}

/* body */ 
.ui-jqgrid .ui-widget-content {border:1px solid #ddd;}
.ui-jqgrid tr.jqgrow, .ui-jqgrid tr.ui-row-ltr, .ui-jqgrid tr.ui-row-rtl {border:none;}
.ui-jqgrid tr.ui-row-ltr td, .ui-jqgrid tr.ui-row-rtl td {border-color:#e1e1e1;border-bottom:1px solid #e1e1e1;}
.ui-jqgrid tr.jqgrow td {height:39px;padding:3px 6px;white-space:nowrap;text-overflow:ellipsis;} /* 溢出省略 */
.ui-jqgrid tr.jqgrow td.clip {text-overflow:clip;} /* 不显示省略号，截断显示 */
.ui-jqgrid tr.jqgrow td.actions {overflow-x:auto;text-overflow:clip;} /* 操作列样式 */
.ui-jqgrid tr.jqgrow td.textarea {white-space:normal;} /* 允许单元格换行 */
.ui-jqgrid td.jqgrid-rownum {padding:0 !important;margin:0;border:0 none;} /* 序号 */
.ui-jqgrid tr.jqgrow td.jqgrid-multibox {text-overflow:clip;} /* 复选框列样式 */
.ui-jqgrid tr.jqgrow td.cb {padding:1px 0 0 1px;text-overflow:clip;}
.ui-jqgrid tr.jqgrow td.cb,.ui-jqgrid .ui-jqgrid-htable th.cb {text-overflow:clip;}/* 复选框列样式 4.7 */
.ui-jqgrid .ui-jqgrid-btable {padding-bottom:18px;position:relative;border-collapse:collapse;} /* 表单验证 */
.ui-jqgrid .ui-jqgrid-btable label.has-error{margin-bottom:0;} /* 表单验证 */
/* .ui-jqgrid .ui-jqgrid-btable .cbox {margin-top:3px;margin-left:2px;} 复选框 */
.ui-jqgrid-table-striped > tbody > tr:nth-of-type(odd) {background-color:#fafafa;opacity:1;} /* 斑马线 */
.ui-jqgrid tr.ui-state-highlight td {background-color:#e3edf5;} /* 当前行 */

/* footer */
.ui-jqgrid tr.jqgroup td {height:32px;padding:0 2px;background:#fff;} /* 分组标题 */
.ui-jqgrid tr.jqfoot td {height:25px;padding:0 2px;background:#fafafa;font-size:12px;} /* 分组小计 #f0fffb*/
.ui-jqgrid tr.footrow td {height:28px;padding:0 2px;background:#e6e6e6;} /* 合计 */

/* frozen */
.ui-jqgrid .frozen-div .ui-jqgrid-resize-ltr,
.ui-jqgrid .frozen-div .ui-jqgrid-resize-rtl {margin:2px -2px -2px 0;}
.ui-jqgrid .frozen-bdiv {overflow:hidden;} /* ie9冻结滚动条 */
.ui-jqgrid .frozen-bdiv .ui-jqgrid-btable {padding-bottom:19px;} /* ie9冻结拖到底部对齐 */
.ui-jqgrid .ui-jqgrid-frozen .ui-jqgrid-htable th div {height:36px!important;}
.ui-jqgrid .frozen-left th.ui-th-ltr,
.ui-jqgrid .frozen-left tr.ui-row-ltr td {border-right:1px solid #eaeaea;}
.ui-jqgrid .frozen-right th.ui-th-ltr,
.ui-jqgrid .frozen-right tr.ui-row-ltr td {border-right:0;border-left:1px solid #eaeaea;}
.ui-jqgrid .frozen-bdiv.frozen-left {box-shadow:6px 0 8px -5px rgba(0,0,0,.12);}
.ui-jqgrid .frozen-bdiv.frozen-right {box-shadow:-6px 0 8px -5px rgba(0,0,0,.12);}

/* subgrid */
.ui-jqgrid .tablediv{padding:3px;}

/* editgrid */
.ui-jqgrid .editable{height:33px;}
.ui-jqgrid .editable.icheck{padding-top:0;overflow:hidden;background:transparent;}
.ui-jqgrid .editable .input-group {z-index:1;/* 显示到锁定列下面 */}
.ui-jqgrid .editable .wup_container .placeholder p {display:none;}
.ui-jqgrid .editable .wup_container .queueList .table {margin-bottom:2px;}
.ui-jqgrid .editable .wup_container .placeholder .webuploader-pick {margin:0 auto 0 auto;}
.ui-jqgrid .editable .wup_container .wup_img .placeholder {padding:0;}
.ui-jqgrid .editable .wup_container .wup_img .placeholder .webuploader-pick {margin: 3px 0 0 0;}
.ui-jqgrid .editable .wup_container .wup_input {top:10px;left:2%;}
.ui-jqgrid .editgrid tr.ui-state-hover.ui-row-ltr td {background-color:#fafafa;}
.ui-jqgrid .editgrid tr.ui-state-highlight.ui-row-ltr td {background-color:#f6fbff;}

/* TreeGrid */
.ui-jqgrid .tree-leaf {opacity:.35;filter:Alpha(Opacity=35);}
.ui-jqgrid .treeclick {margin-top:2px;}

/* 更多操作列 */
.ui-jqgrid .actions > a i {display:inline-block;width:15px;float:none;position:static;
	text-align:center;opacity:0.85;-webkit-transition:all 0.12s;-o-transition:all 0.12s;
	transition:all 0.12s;margin:0 0 2px 3px;vertical-align:middle;cursor:pointer;font-size:14px;}
.ui-jqgrid .actions > a i:hover, .ui-jqgrid .actions > a.open i {
	-moz-transform:scale(1.2);-webkit-transform:scale(1.2);-o-transform:scale(1.2);
	-ms-transform:scale(1.2);transform:scale(1.2);opacity:1;position:static;margin:0 0 2px 3px;}
.ui-jqgrid .actions > a i.glyphicon {font-size:13px;}
.ui-jqgrid .actions > a i.fa-check {color:#1e5edb;}
.ui-jqgrid .actions > a i.fa-pencil, .ui-jqgrid .actions > a i.glyphicon-ok-circle {color:#108c53;}
.ui-jqgrid .actions > a i.fa-trash-o, .ui-jqgrid .actions > a i.glyphicon-ban-circle {color:#dd5a43;}
.ui-jqgrid .actions .moreItems {padding:5px 0 0 5px;background:#fff;border:1px solid #ddd;border-radius:4px;box-shadow:2px 2px 15px #ccc;position:absolute;z-index:5;display:none;}
.ui-jqgrid .actions .moreItems a {padding:1px 5px 2px;margin:0 0 4px 0;font-size:13px;}

/* 拖动表格样式  */
.ui-jqgrid tr.dragClass td,
.ui-jqgrid tr.dragClass td input,
.ui-jqgrid tr.dragClass td .select2-container--default .select2-selection--single {
    /*color:yellow!important;*/
    background-color:rgb(240 242 245 / 57%)!important;
    /*text-shadow:0 0 10px #ddd, 0 0 10px #ddd, 0 0 8px #ddd, 0 0 6px #ddd, 0 0 6px #ddd;*/
    /*box-shadow:0 12px 14px -12px #111 inset, 0 -2px 2px -1px #333 inset;*/
}
.ui-jqgrid tr.dragClass td .select2-container--default .select2-selection--single .select2-selection__rendered {
	/*color:yellow!important;*/
}
/* 拖拽表头目标样式 */
.ui-jqgrid .ui-state-highlight {background:#eee;}
/* 排序表格列头样式 */
.sort-header,.sort-column {margin:13px 20px;}
.sort-header .icheck,.sort-column .icheck {display:inline-block;min-width:150px;}
.check_all{position:absolute;left:20px;bottom:7px;}

/* 标题自动换行 */
/* th.ui-th-column div{white-space:normal!important;height:auto!important;padding:0;}
.ui-jqgrid .ui-jqgrid-htable th div{padding-top:2px;} */
th.ui-th-column div{white-space:normal!important;height:auto!important;}
/* .ui-jqgrid .ui-jqgrid-htable th div{height:auto!important;} */

/* 调整标题高度
.ui-jqgrid .ui-jqgrid-htable th {height:24px;}
.ui-jqgrid .ui-jqgrid-htable th div {padding:4px 0 3px 2px;}
.ui-jqgrid .ui-jqgrid-frozen .ui-jqgrid-htable th div {height:24px!important;} */

/* print style */
@media print {
	a[href]:after {content:""!important;}
	abbr[title]:after {content:""!important;}
	#pager{display:none;z-index:-1;} 
	#accordion h3, #vcol, div.loading, div.ui-tabs-hide,ul.ui-tabs-nav li, td.HeaderRight {display:none }
	.ui-widget-content {border-width:1px 0 0 1px!important;}
	.ui-jqgrid-titlebar, .ui-jqgrid-title {display:none } 
	.ui-jqgrid-bdiv_self {position:relative;margin:0em;padding:0;text-align:left;} 
	.ui-jqgrid-bdiv, .ui-jqgrid-bdiv>div {height:auto!important;}
	.ui-jqgrid .ui-jqgrid-btable {padding-bottom:0;}
}
