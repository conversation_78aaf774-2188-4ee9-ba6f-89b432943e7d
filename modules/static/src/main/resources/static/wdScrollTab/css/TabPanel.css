
.tabpanel {
	border: solid 1px #8DB2E3;
	width: 100%;
	height: 100%;
}

.tabpanel_tab_content {
	width: 100%;
	height: 38px;
	line-height: 21px;
	background-color: #F9F9F9;
	border-bottom: 1px solid #e5e5e5;
	overflow: hidden;
	white-space: nowrap;
	position: relative;
}

.tabpanel_tab_content .tabpanel_move_content {
	width: 0px;
	min-height: 35px;
	overflow: hidden;
}

.tabpanel_move_content_scroll {
	margin-left: 18px;
	margin-right: 18px;
}

.tabpanel_mover {
	width: 5000px;
	margin: 0;
	padding: 0;
	position: relative;
}

.tabpanel_mover li {
	width: 105px;
	/*line-height: 20px;*/
	padding: 9px 16px 9px 5px;
	background-color: #f4f6f8;
	float: left;
	position: relative;
	list-style-type: none;
	cursor: pointer;
	border-right: 1px solid #ddd;
	overflow: hidden;
}

.tabpanel_mover li .refresher {
	display: none;
}

.tabpanel_mover li .closer {
	background: transparent url(../image/tab-close.png) no-repeat;
	position: absolute;
	right: 5px;
	top: 14px;
	width: 11px;
	height: 11px;
	cursor: pointer;
}

.tabpanel_mover li .closer:hover {
	background: transparent url(../image/tab-close.gif) no-repeat;
}

.tabpanel_mover li .title {
/* 	font-size: 13px; */
	padding-left: 5px;
	overflow: hidden;
	*height: 20px;
	*line-height: 20px;
	*margin-top: 2px;
	white-space: nowrap;
    text-overflow: ellipsis;
}

.tabpanel_mover li .title i{
	font-size: 18px;
	vertical-align: middle;
	margin-top: -3px;
	opacity: 0.8
}

.tabpanel_mover li div {
	color: #23508E;
}

.tabpanel_mover li .icon_title {
	font-size: 12px;
	color: #23508E;
	padding-left: 25px;
	background-repeat: no-repeat;
	background-position: 5px 4px;
	overflow: hidden;
	*height: 20px;
	*line-height: 20px;
	*margin-top: 2px;
	*background-position: 5px 1px;
}

.tabpanel_mover li.active {
	background-color: #eaedf1;
}

.tabpanel_tab_content .tabpanel_left_scroll {
	background: transparent url(../image/scroll-left.gif) no-repeat 0 0;
	position: absolute;
	width: 18px;
	height: 24px;
	left: 0px;
	top: 8px;
	cursor: pointer;
	z-index: 10;
}

.tabpanel_tab_content .tabpanel_right_scroll {
	background: transparent url(../image/scroll-right.gif) no-repeat 0 0;
	position: absolute;
	width: 18px;
	height: 24px;
	right: 0px;
	top: 8px;
	cursor: pointer;
	z-index: 10;
}

.tabpanel_tab_content .tabpanel_scroll_over {
	background-position: -18px 1px;
}

.tabpanel_tab_content .tabpanel_left_scroll_disabled {
	background-position: 0 0;
	opacity: .5;
	-moz-opacity: .5;
	filter: alpha(opacity = 50);
	cursor: default;
}

.tabpanel_right_scroll_disabled {
	background-position: -36px 0;
	opacity: .5;
	-moz-opacity: .5;
	filter: alpha(opacity = 50);
	cursor: default;
}

.display_none {
	display: none;
}

.disabled {
	color: gray;
}

.tabpanel_tab_content .tabpanel_tab_spacer {
	height: 2px;
	font-size: 1px;
	line-height: 1px;
	margin-top: -1px;
}

.tabpanel_content {
	background-color: #FFF;
	overflow: hidden;
	position: relative;
/* 	transition:all 50ms; */
}

.tabpanel_content .html_content {
	width: 100%;
	height: 100%;
/* 	background-color:#FFF; */
	position: absolute;
	z-index: 0;
	left: 0;
	top: 0;
}

.div_RightMenu{position:absolute;list-style:none;width:130px;z-index:999999;display:none;} 
.div_RightMenu div{background:#eee;position:relative;font-size:13px;}
.div_RightMenu ul{position:relative;background:#fff;border:1px solid #ddd;left:-2px;top:-2px;margin:0;padding:1px 0;border-radius:3px;}
.div_RightMenu ul li{list-style:none;padding:5px 12px;cursor:pointer;text-align:left;}
.div_RightMenu ul li.RM_mouseover{background-color:#e1e3e9;}
.div_RightMenu ul li i{width:18px;color:#666;}

.skin-dark .div_RightMenu div,
.skin-dark .div_RightMenu ul {background-color:#1a1a1a;border-color: #414141;color:#c6c6c6;}
.skin-dark .div_RightMenu ul li.RM_mouseover{background-color:#3e3e3e;border-color:#3e3e3e;color:#eee;}