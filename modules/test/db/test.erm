<?xml version="1.0" encoding="UTF-8"?>
<diagram>
	<page_setting>
		<direction_horizontal>true</direction_horizontal>
		<scale>100</scale>
		<paper_size>A4 210 x 297 mm</paper_size>
		<top_margin>30</top_margin>
		<left_margin>30</left_margin>
		<bottom_margin>30</bottom_margin>
		<right_margin>30</right_margin>
	</page_setting>
	<category_index>0</category_index>
	<zoom>1.0</zoom>
	<x>0</x>
	<y>0</y>
	<default_color>
		<r>128</r>
		<g>128</g>
		<b>192</b>
	</default_color>
	<color>
		<r>255</r>
		<g>255</g>
		<b>255</b>
	</color>
	<font_name>Arial</font_name>
	<font_size>14</font_size>
	<settings>
		<database>StandardSQL</database>
		<capital>false</capital>
		<table_style></table_style>
		<notation></notation>
		<notation_level>0</notation_level>
		<notation_expand_group>true</notation_expand_group>
		<view_mode>2</view_mode>
		<outline_view_mode>1</outline_view_mode>
		<view_order_by>1</view_order_by>
		<auto_ime_change>false</auto_ime_change>
		<validate_physical_name>true</validate_physical_name>
		<use_bezier_curve>false</use_bezier_curve>
		<suspend_validator>false</suspend_validator>
		<export_setting>
			<export_ddl_setting>
				<output_path>db/test.sql</output_path>
				<encoding>UTF-8</encoding>
				<line_feed>CR+LF</line_feed>
				<is_open_after_saved>false</is_open_after_saved>
				<environment_id>7be191506f9daa8070b3ac14921dffd44063d2bb</environment_id>
				<category_id>null</category_id>
				<ddl_target>
					<create_comment>true</create_comment>
					<create_foreignKey>false</create_foreignKey>
					<create_index>true</create_index>
					<create_sequence>false</create_sequence>
					<create_table>true</create_table>
					<create_tablespace>false</create_tablespace>
					<create_trigger>false</create_trigger>
					<create_view>false</create_view>
					<drop_index>false</drop_index>
					<drop_sequence>false</drop_sequence>
					<drop_table>false</drop_table>
					<drop_tablespace>false</drop_tablespace>
					<drop_trigger>false</drop_trigger>
					<drop_view>false</drop_view>
					<inline_column_comment>false</inline_column_comment>
					<inline_table_comment>true</inline_table_comment>
					<comment_value_description>false</comment_value_description>
					<comment_value_logical_name>true</comment_value_logical_name>
					<comment_value_logical_name_description>false</comment_value_logical_name_description>
					<comment_replace_line_feed>false</comment_replace_line_feed>
					<comment_replace_string></comment_replace_string>
				</ddl_target>
			</export_ddl_setting>
			<export_excel_setting>
				<category_id>null</category_id>
				<output_path>db/test.xls</output_path>
				<template></template>
				<template_path></template_path>
				<used_default_template_lang>en</used_default_template_lang>
				<image_output></image_output>
				<is_open_after_saved>true</is_open_after_saved>
				<is_put_diagram>true</is_put_diagram>
				<is_use_logical_name>true</is_use_logical_name>
			</export_excel_setting>
			<export_html_setting>
				<output_dir></output_dir>
				<with_category_image>true</with_category_image>
				<with_image>true</with_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_html_setting>
			<export_image_setting>
				<output_file_path>db/test.png</output_file_path>
				<category_dir_path></category_dir_path>
				<with_category_image>true</with_category_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_image_setting>
			<export_java_setting>
				<java_output></java_output>
				<package_name></package_name>
				<class_name_suffix></class_name_suffix>
				<src_file_encoding></src_file_encoding>
				<with_hibernate>false</with_hibernate>
			</export_java_setting>
			<export_testdata_setting>
				<file_encoding></file_encoding>
				<file_path></file_path>
				<format>0</format>
			</export_testdata_setting>
		</export_setting>
		<category_settings>
			<free_layout>false</free_layout>
			<show_referred_tables>false</show_referred_tables>
			<categories>
			</categories>
		</category_settings>
		<translation_settings>
			<use>false</use>
			<translations>
			</translations>
		</translation_settings>
		<model_properties>
			<id></id>
			<height>-1</height>
			<width>-1</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>50</x>
			<y>50</y>
			<color>
				<r>255</r>
				<g>255</g>
				<b>255</b>
			</color>
			<connections>
			</connections>
			<display>false</display>
			<creation_date>2016-12-25 17:25:00</creation_date>
			<model_property>
				<name>Project Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Model Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Version</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Company</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Author</name>
				<value></value>
			</model_property>
		</model_properties>
		<table_properties>
			<schema></schema>
		</table_properties>
		<environment_setting>
			<environment>
				<id>7be191506f9daa8070b3ac14921dffd44063d2bb</id>
				<name>Default</name>
			</environment>
		</environment_setting>
	</settings>
	<dictionary>
		<word>
			<id>c71533e4d2f429ff8466fd6a8de5719f1741377b</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>租户代码</description>
			<logical_name>租户代码</logical_name>
			<physical_name>corp_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>11c59294fe142d108ca4dac5a03033f50188a450</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>租户名称</description>
			<logical_name>租户名称</logical_name>
			<physical_name>corp_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>创建者</logical_name>
			<physical_name>create_by</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>170410257b4d8e712b8c1d499b415de82ce9683c</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>创建时间</logical_name>
			<physical_name>create_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>038ea74a700b763fa1f8fcdbd2c27e7941f82f7a</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 1</logical_name>
			<physical_name>extend_d1</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>19aa2e40abff2bb4e908f18f5f5e5b30a074cd8b</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 2</logical_name>
			<physical_name>extend_d2</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>06f59428b50e62e636a58a2f3cbcee2c75764506</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 3</logical_name>
			<physical_name>extend_d3</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>8f08aeb4b9ceb072f0232d3d23fd5371ef8f379c</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 4</logical_name>
			<physical_name>extend_d4</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>0733a829c52af8af725ace50d4ed3e0179969f56</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 1</logical_name>
			<physical_name>extend_f1</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>0b7230df29d6df213507c8855d019d2d55c9a561</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 2</logical_name>
			<physical_name>extend_f2</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>6cb3dad83f3e1f358155892dcee3167ef55bacc7</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 3</logical_name>
			<physical_name>extend_f3</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>e54b261fc7ab81e0ef19a89e657453664abe5593</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 4</logical_name>
			<physical_name>extend_f4</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>b67071354a8d43cabe9a0ac29303f8b144a15985</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 1</logical_name>
			<physical_name>extend_i1</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>70b8240f049f658b97394b7d4784481fdb477f5c</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 2</logical_name>
			<physical_name>extend_i2</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>ce377c081415847061b2efc58b701ff847dcaaca</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 3</logical_name>
			<physical_name>extend_i3</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>fb96f6dc60d31576c278a3c64024154eda67d3fe</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 4</logical_name>
			<physical_name>extend_i4</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>3962f4b130a803841a193a163e42679946b5ae1f</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 JSON</logical_name>
			<physical_name>extend_json</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b1acb2df17677e0ed818dea6e1150bfc088fcd50</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 1</logical_name>
			<physical_name>extend_s1</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>d27290b84b207a7c87f4d78d80e62048c67fac04</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 2</logical_name>
			<physical_name>extend_s2</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>5e25d7f0e327d2d760a04b32140b639bb96bfd8d</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 3</logical_name>
			<physical_name>extend_s3</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>d05572af90653d4ddfd880402a15ed9b27c81888</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 4</logical_name>
			<physical_name>extend_s4</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>acc372d31709a1133f6c13fc8abfef7881ca26ed</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 5</logical_name>
			<physical_name>extend_s5</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>59763a16504e4136a7a2fbed38476272d337105c</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 6</logical_name>
			<physical_name>extend_s6</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>600b96b04bf4f65919eabfc6613d3c5e370931e1</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 7</logical_name>
			<physical_name>extend_s7</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>a9dc80e5c484dc7b9d5fff9294eaa383819edc26</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 8</logical_name>
			<physical_name>extend_s8</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>编号</logical_name>
			<physical_name>id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>e4465172e2cc64907a386237cc7d0c7d79a074dc</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>父级编号</logical_name>
			<physical_name>parent_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b4f9837136b0a84afc9a611a563fb51141fdac1f</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>所有父级编号</logical_name>
			<physical_name>parent_codes</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>备注信息</logical_name>
			<physical_name>remarks</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>a5d0a391d0d09acab89c829d271b0a96a7058ca0</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>状态（0正常 1删除 2停用 3冻结 4审核 5驳回 9草稿）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>70d065a29d01e3875167a20441e0ebbfaf5a05f8</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>状态（0正常 1删除 2停用）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>5f188d6afcf9d1afbbe94ac6d92f800caef67cbe</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>区域选择</logical_name>
			<physical_name>test_area_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>6adbc087f6a7805c1d979bee36098c497d399378</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>区域名称</logical_name>
			<physical_name>test_area_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>5cab10f2b339972b1f10cb150baf883a8adffdbb</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>复选框</logical_name>
			<physical_name>test_checkbox</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>80c7c6394e75c6f7acbf75bae587afdc02d3990b</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>父表主键</logical_name>
			<physical_name>test_data_id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>7b4cc71483ef98673d4cc313eb99314e27722e28</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>日期选择</logical_name>
			<physical_name>test_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>bc2e5b104e72723c9c6710c9a3a02804111b6846</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>日期时间</logical_name>
			<physical_name>test_datetime</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>89d26a106a3b0895ebfe6ccf9b5930ef4b1612c1</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>单行文本</logical_name>
			<physical_name>test_input</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>efa0f7e9084cf3b09f7e31101ec496a44aadaaa4</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>机构选择</logical_name>
			<physical_name>test_office_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>7b62803aab5f5a3c3f9ff63f7d5122a9ef3a41bb</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>单选框</logical_name>
			<physical_name>test_radio</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>3a0f10db94ed1cb6e8cb088ca0275b664d4fca4c</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>下拉框</logical_name>
			<physical_name>test_select</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>cb6ff93372480fd7d11e66a1e9c9fa028149e99c</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>下拉多选</logical_name>
			<physical_name>test_select_multiple</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>f1929c4074c74e32b7e4ac70c95476add194c0a0</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>排序号</logical_name>
			<physical_name>test_sort</physical_name>
			<type>integer</type>
		</word>
		<word>
			<id>c22eb21bd16761440f79d8782099fa4b0e9115cb</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>多行文本</logical_name>
			<physical_name>test_textarea</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>54338e0229264fd01ec1efff7c49b30d966df58e</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>用户选择</logical_name>
			<physical_name>test_user_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>abe7e075aa224d4322d021e5659d170e0a927621</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>节点编码</logical_name>
			<physical_name>tree_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>8fb086ad8b2a35cdf18c1a1e247005cdb140faf0</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>是否最末级</logical_name>
			<physical_name>tree_leaf</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>f22022df4ccf9c7081b4d8f98deed04c1d8c987c</id>
			<length>4</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>层次级别</logical_name>
			<physical_name>tree_level</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>b023ebdb5c45aa960edd4a77dbe90dcdcc346664</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>节点名称</logical_name>
			<physical_name>tree_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>44f852382fba107fe668304530dea0233f38a321</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>全节点名</logical_name>
			<physical_name>tree_names</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>65265fefa00907a60e8fe2ebd6d2ad58760415e5</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>排序号（升序）</logical_name>
			<physical_name>tree_sort</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>cf5f1e3cc5cc0869f539c3c5b2adf05677e367b8</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>所有排序号</logical_name>
			<physical_name>tree_sorts</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>c60616703cf07aaf21f84215d092e2101efb9bca</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>更新者</logical_name>
			<physical_name>update_by</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b5c3a59170184a01c15c78a6778cb73083fe6321</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>更新时间</logical_name>
			<physical_name>update_date</physical_name>
			<type>datetime</type>
		</word>
	</dictionary>
	<tablespace_set>
	</tablespace_set>
	<contents>
		<table>
			<id>e553474c37270813e70025e433a4cf8a64653e13</id>
			<height>438</height>
			<width>387</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>864</x>
			<y>36</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>test_tree</physical_name>
			<logical_name>测试树表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>abe7e075aa224d4322d021e5659d170e0a927621</word_id>
					<id>7e417ee9d0dd69c767a5853922621946ed4fb2d8</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>a535b6c506004a7fdf4d48984c9ff2cfa59c157a</column_group>
				<normal_column>
					<word_id>b023ebdb5c45aa960edd4a77dbe90dcdcc346664</word_id>
					<id>39f501890586173d229e83610cfbfaa6e3a85374</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>5435ef11ea53f170fe3491b199c113e47932e175</id>
			<height>401</height>
			<width>320</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>36</x>
			<y>36</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>test_data</physical_name>
			<logical_name>测试数据</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>d82778c36626013cd39fd790da6f55a9762f0c76</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>89d26a106a3b0895ebfe6ccf9b5930ef4b1612c1</word_id>
					<id>3e14b40dc07c134329a40752973acfad2ffdc48b</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>c22eb21bd16761440f79d8782099fa4b0e9115cb</word_id>
					<id>8050d948828b16267482e9e3716219321f206b81</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3a0f10db94ed1cb6e8cb088ca0275b664d4fca4c</word_id>
					<id>3664605e054c39531ca8e91aa4463c955a993357</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>cb6ff93372480fd7d11e66a1e9c9fa028149e99c</word_id>
					<id>f7b189ecdc92c78bfaae9c736318ab6a5aedd396</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>7b62803aab5f5a3c3f9ff63f7d5122a9ef3a41bb</word_id>
					<id>6e4837ab55b592669ccc7ebfc8b1ed96a00607b3</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5cab10f2b339972b1f10cb150baf883a8adffdbb</word_id>
					<id>9fb81bdc12ed017f62e9dd4529025e536eff7f08</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>7b4cc71483ef98673d4cc313eb99314e27722e28</word_id>
					<id>9d9478798ed2766a81b0e5a8a022eb89d9c5cc34</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>bc2e5b104e72723c9c6710c9a3a02804111b6846</word_id>
					<id>c131dc5cb7ce6f1aa5e8d5f86f2002c8ae1bb8f6</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>54338e0229264fd01ec1efff7c49b30d966df58e</word_id>
					<id>cfe3c330968a9a824c2cf933b227887d3e9615ac</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>efa0f7e9084cf3b09f7e31101ec496a44aadaaa4</word_id>
					<id>23041cb30875514136904ea11043c7ea1924f048</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5f188d6afcf9d1afbbe94ac6d92f800caef67cbe</word_id>
					<id>b6d5dc8745c0fa484ed6f14e90a42282ca1b285d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6adbc087f6a7805c1d979bee36098c497d399378</word_id>
					<id>d244401ff7302bb5b75092016531952d093b238b</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>5a836e654b7b7d19f102e87336fefa079d98a2e6</id>
			<height>438</height>
			<width>346</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>468</x>
			<y>36</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>test_data_child</physical_name>
			<logical_name>测试数据子表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>34477707bcf8a7810e12e9565aff085f6fb1e0ad</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>f1929c4074c74e32b7e4ac70c95476add194c0a0</word_id>
					<id>ec8a047dffe3cf2e4d95ba3e26c3bac0382d95c9</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>integer</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>80c7c6394e75c6f7acbf75bae587afdc02d3990b</word_id>
					<id>b095c44611ed08156277676d9a3a0ce52b9b05ef</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>89d26a106a3b0895ebfe6ccf9b5930ef4b1612c1</word_id>
					<id>78715aa66a10a9b190ad69b8ed792e9a2f4946e3</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>c22eb21bd16761440f79d8782099fa4b0e9115cb</word_id>
					<id>693f8af2fe6cf45255b6f2bc7e9077f3630c3dd8</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3a0f10db94ed1cb6e8cb088ca0275b664d4fca4c</word_id>
					<id>04216c384533b70b8e86b571fa2beef7157bab5a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>cb6ff93372480fd7d11e66a1e9c9fa028149e99c</word_id>
					<id>0b6f63094be90aa8674460cce26be10327827ceb</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>7b62803aab5f5a3c3f9ff63f7d5122a9ef3a41bb</word_id>
					<id>363e8cfcf4a6b228e656decadacba29280906555</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5cab10f2b339972b1f10cb150baf883a8adffdbb</word_id>
					<id>7875aa5fee3d6f9587180fb8fce9343a56cdc34e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>7b4cc71483ef98673d4cc313eb99314e27722e28</word_id>
					<id>61c49a2f7807c55c8f85110c30889d5b403e4b04</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>bc2e5b104e72723c9c6710c9a3a02804111b6846</word_id>
					<id>b91171b99f3628aadb0e6986fbfa30cc547b280e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>54338e0229264fd01ec1efff7c49b30d966df58e</word_id>
					<id>9f33d190101e56f93f5ece0fd7c5cdda3e704b4c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>efa0f7e9084cf3b09f7e31101ec496a44aadaaa4</word_id>
					<id>16121b5aa08fc170883408ec2d0487281a132d9d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5f188d6afcf9d1afbbe94ac6d92f800caef67cbe</word_id>
					<id>1afc7f146271f5c90ea811aa24c08ce25d12552e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6adbc087f6a7805c1d979bee36098c497d399378</word_id>
					<id>343f0db997b913f299b0496c4306d3617ad708de</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
	</contents>
	<column_groups>
			<column_group>
				<id>845c82ebd869d5620b1ef2c2b6f438b11a045082</id>
				<group_name>BaseEntity</group_name>
				<columns>
					<normal_column>
						<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
						<id>02ecedc0de5850cba25bc91919ed39d414b74111</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a5d0a391d0d09acab89c829d271b0a96a7058ca0</word_id>
						<id>2fe6a36385238c1b21c76deae00a7afa00ff5538</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>ea920cba2fe0eaee64a2310ece7cda4b198b37ec</id>
				<group_name>BaseEntityCorp</group_name>
				<columns>
					<normal_column>
						<word_id>c71533e4d2f429ff8466fd6a8de5719f1741377b</word_id>
						<id>b8ea4b73d6d32a222e5abfd453287575ae518480</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value>0</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>11c59294fe142d108ca4dac5a03033f50188a450</word_id>
						<id>b94f5fe344185c40739cf93d1090686001bb11e0</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value>JeeSite</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>35ae805d1da92afdb99b2fe8c536d1649356fccd</id>
				<group_name>DataEntity</group_name>
				<columns>
					<normal_column>
						<word_id>70d065a29d01e3875167a20441e0ebbfaf5a05f8</word_id>
						<id>f0036584bd8711715579d21994a0105935605a7e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value>0</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</word_id>
						<id>c391a15752a8eb58bc558a39d1b431f7ee125e0e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>170410257b4d8e712b8c1d499b415de82ce9683c</word_id>
						<id>e2e82ba86e15fd67397355e711255b1625078ae1</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>c60616703cf07aaf21f84215d092e2101efb9bca</word_id>
						<id>fd0546fc2d4e01c35dcbc23913add68a99fabd73</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b5c3a59170184a01c15c78a6778cb73083fe6321</word_id>
						<id>f8ea4fc4a778a0b94398a661a1ed8608f0e8d28d</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</word_id>
						<id>69e01b6d4f42df40a09540ef4ba10ed8e006abaa</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>85024a2953cf3e3c9c1cce49b2351853ab0d125b</id>
				<group_name>DataEntityNoStatus</group_name>
				<columns>
					<normal_column>
						<word_id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</word_id>
						<id>e5355faba5ec3c9128507dd4c48ea9230631cf83</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>170410257b4d8e712b8c1d499b415de82ce9683c</word_id>
						<id>6bed374c39d181003a4f92d76d79a4119176ba0c</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>c60616703cf07aaf21f84215d092e2101efb9bca</word_id>
						<id>f9db19bb567760bbdd554d75bbfdc891c89f9da9</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b5c3a59170184a01c15c78a6778cb73083fe6321</word_id>
						<id>ee78b079f7d319bf8119fd01439cd97424ff49fa</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</word_id>
						<id>f7b88ecec0ef386bb384c228842a7587432112fb</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>118dab95fc1f792cd468b9f66af2d4fabd98c39b</id>
				<group_name>ExtendEntity</group_name>
				<columns>
					<normal_column>
						<word_id>b1acb2df17677e0ed818dea6e1150bfc088fcd50</word_id>
						<id>6ccadddab6ce48441ca7abd798cda6f3debf4a0c</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>d27290b84b207a7c87f4d78d80e62048c67fac04</word_id>
						<id>93ab0ba3b47b01934614dbd3e572358c9f99e6ea</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>5e25d7f0e327d2d760a04b32140b639bb96bfd8d</word_id>
						<id>a78c7961910a5e697027d1a3530b1afaa8ea8c94</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>d05572af90653d4ddfd880402a15ed9b27c81888</word_id>
						<id>40085364ec7a58653e96f8659aadd258d7556bc7</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>acc372d31709a1133f6c13fc8abfef7881ca26ed</word_id>
						<id>9787d7fe93ee31c5b4979fd620ff6e4b2777eccf</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>59763a16504e4136a7a2fbed38476272d337105c</word_id>
						<id>95c55b81b7e9e1a9bb01aa3d88fb90c648641c4e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>600b96b04bf4f65919eabfc6613d3c5e370931e1</word_id>
						<id>16f44dfc7964796f109293bc49afd58dcb4eec1f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a9dc80e5c484dc7b9d5fff9294eaa383819edc26</word_id>
						<id>39b1dffa083f74afc30df621845cf7f0ed71394f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b67071354a8d43cabe9a0ac29303f8b144a15985</word_id>
						<id>7584cc6360ae7edc99e1f619042eba5865b2c4c7</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>70b8240f049f658b97394b7d4784481fdb477f5c</word_id>
						<id>f0b5383e05c6b3f6e5f65b33b33009826c83d014</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>ce377c081415847061b2efc58b701ff847dcaaca</word_id>
						<id>260d5f31009fff18000d1e64f4f877926e621306</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>fb96f6dc60d31576c278a3c64024154eda67d3fe</word_id>
						<id>a83144f40e7ae64e46a4b4ed651379774a953b17</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>0733a829c52af8af725ace50d4ed3e0179969f56</word_id>
						<id>2a5203a275171a250870cf6cb224a910aa9354ec</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>0b7230df29d6df213507c8855d019d2d55c9a561</word_id>
						<id>3ef5bd65a7dcd74b9a9d8a292ec395f66b7de32b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>6cb3dad83f3e1f358155892dcee3167ef55bacc7</word_id>
						<id>01d0849bdda56a8d8f24befdadc3fc9b007ae92b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>e54b261fc7ab81e0ef19a89e657453664abe5593</word_id>
						<id>1c8ed63d72f40f0fe2f05815675771bdf3f824f8</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>038ea74a700b763fa1f8fcdbd2c27e7941f82f7a</word_id>
						<id>2b49e875138bfb329aaa352629650b7881435123</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>19aa2e40abff2bb4e908f18f5f5e5b30a074cd8b</word_id>
						<id>5c6ec16226d85b0411b7077cb9a6e0c7aa8d74d1</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>06f59428b50e62e636a58a2f3cbcee2c75764506</word_id>
						<id>d92b8f7fa7a2be49c7f00c447a603b136e84261d</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8f08aeb4b9ceb072f0232d3d23fd5371ef8f379c</word_id>
						<id>095a76f07a3cd2bdc6cc442757c11012e1974f4a</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>3962f4b130a803841a193a163e42679946b5ae1f</word_id>
						<id>42c5d8f490f69b93e77698efa030ca23988ae696</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>a535b6c506004a7fdf4d48984c9ff2cfa59c157a</id>
				<group_name>TreeEntity</group_name>
				<columns>
					<normal_column>
						<word_id>e4465172e2cc64907a386237cc7d0c7d79a074dc</word_id>
						<id>394369b90c0a5b6efeed3cf823c642605d7a1653</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b4f9837136b0a84afc9a611a563fb51141fdac1f</word_id>
						<id>e8d877396943acfec73023dba2c1c6e3d7802d62</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>65265fefa00907a60e8fe2ebd6d2ad58760415e5</word_id>
						<id>23f973124aedd0244533f4e7b3b103c548b966be</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>cf5f1e3cc5cc0869f539c3c5b2adf05677e367b8</word_id>
						<id>984d5eac2b3221118a61655e4a5a49c78e0f0151</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8fb086ad8b2a35cdf18c1a1e247005cdb140faf0</word_id>
						<id>b2f246a3f0ade317eaa9915e2fd539abae5a5ec8</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f22022df4ccf9c7081b4d8f98deed04c1d8c987c</word_id>
						<id>f5a9968479420f08da2e98d21136b3ed4b6e396f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>44f852382fba107fe668304530dea0233f38a321</word_id>
						<id>618194ebfc8c6c42efcef3a4af0b8054f6af209b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
	</column_groups>
	<test_data_list>
	</test_data_list>
	<sequence_set>
	</sequence_set>
	<trigger_set>
	</trigger_set>
	<change_tracking_list>
	</change_tracking_list>
</diagram>
