<% layout('/layouts/default.html', {title: '数据管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(testTree.isNewRecord ? '新增数据' : '编辑数据')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${testTree}" action="${ctx}/test/testTree/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('上级数据')}：</label>
							<div class="col-sm-8">
								<#form:treeselect id="parent" title="${text('上级数据')}"
									path="parent.id" labelPath="parent.treeName"
									url="${ctx}/test/testTree/treeData?excludeCode=${testTree.id}"
									class="" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('节点编码')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:hidden path="isNewRecord"/>
								<#form:input path="treeCode" maxlength="64" readonly="${!testTree.isNewRecord}" class="form-control required abc"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('节点名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="treeName" maxlength="200" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('排序号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="treeSort" class="form-control required digits"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">${text('图片上传')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadImage" bizKey="${testTree.id}" bizType="testTree_image"
									uploadType="image" class="" readonly="false" preview="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">${text('附件上传')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${testTree.id}" bizType="testTree_file"
									uploadType="all" class="" readonly="false" preview="true"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('test:testTree:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.$('#dataGrid').dataGrid('refreshTreeChildren',
							$('#parentCode').val(), '${testTree.id}');
				});
			}
		}, "json");
    }
});

// 选择上级节点回调方法
function treeselectCallback(id, act, index, layero){
	if (id == 'parent' && (act == 'ok' || act == 'clear')){
		// 创建并初始化下一个节点信息，如：排序号、默认值
		$.get('${ctx}/test/testTree/createNextNode?parentCode='
				+$('#parentCode').val(), function(data){
			$('#treeSort').val(data.treeSort);
		});
	}
}
</script>