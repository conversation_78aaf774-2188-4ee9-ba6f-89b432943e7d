<% layout('/layouts/default.html', {title: '数据管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('数据管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('test:testData:edit')){ %>
					<a href="${ctx}/test/testData/form" class="btn btn-default btnTool" title="${text('新增数据')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnTrunsTest" title="事务测试"><i class="fa fa-refresh"></i> 事务测试</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${testData}" action="${ctx}/test/testData/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('单行文本')}：</label>
					<div class="control-inline">
						<#form:input path="testInput" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group hide">
					<label class="control-label">${text('多行文本')}：</label>
					<div class="control-inline">
						<#form:input path="testTextarea" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('下拉框')}：</label>
					<div class="control-inline width-120">
						<#form:select path="testSelect" dictType="sys_menu_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('日期选择')}：</label>
					<div class="control-inline">
						<#form:input path="testDate_gte" readonly="true" maxlength="20" class="form-control laydate width-date"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="testDate_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="testDate_lte" readonly="true" maxlength="20" class="form-control laydate width-date"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					<button type="button" class="btn btn-link btn-sm btnFormMore"><i class="fa fa-angle-double-down"></i></button>
				</div>
				<div class="form-more">
					<div class="form-group">
						<label class="control-label">${text('日期时间')}：</label>
						<div class="control-inline">
							<#form:input path="testDatetime_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="testDatetime_lte.click()"/>
							&nbsp;-&nbsp;
							<#form:input path="testDatetime_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('用户选择')}：</label>
						<div class="control-inline width-120" >
							<#form:treeselect id="testUser" title="${text('用户选择')}"
								path="testUser.userCode" labelPath="testUser.userName" 
								url="${ctx}/sys/office/treeData?isLoadUser=true" allowClear="true"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('机构选择')}：</label>
						<div class="control-inline width-120" >
							<#form:treeselect id="testOffice" title="${text('机构选择')}"
								path="testOffice.officeCode" labelPath="testOffice.officeName" 
								url="${ctx}/sys/office/treeData" allowClear="true"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('区域选择')}：</label>
						<div class="control-inline width-120" >
							<#form:treeselect id="testAreaCode" title="${text('区域选择')}"
								path="testAreaCode" labelPath="testAreaName" 
								url="${ctx}/sys/area/treeData" allowClear="true"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('下拉多选')}：</label>
						<div class="control-inline width-120">
							<#form:select path="testSelectMultiple" dictType="sys_menu_type" multiple="true" blankOption="true" class="form-control"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('数据状态')}：</label>
						<div class="control-inline width-120">
							<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('单选框')}：</label>
						<div class="control-inline">
							<#form:radio path="testRadio" dictType="sys_menu_type" blankOption="true" class="form-control"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('复选框')}：</label>
						<div class="control-inline">
							<#form:checkbox path="testCheckbox" dictType="sys_menu_type" blankOption="true" class="form-control"/>
						</div>
					</div>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("单行文本")}', name:'testInput', index:'a.test_input', width:250, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/test/testData/form?id='+row.id+'&flag=2" class="hsBtnList" data-title="${text("编辑数据")}">'+(val||row.id)+'</a>';
		}, searchoptions: { dataInit: function (element) {
			$(element).attr('form', 'searchForm').attr('name', 'testInput2');
		}}},
		{header:'${text("多行文本")}', name:'testTextarea', index:'a.test_textarea', width:150, align:"left"},
		{header:'${text("下拉框")}', name:'testSelect', index:'a.test_select', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_menu_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("下拉多选")}', name:'testSelectMultiple', index:'a.test_select_multiple', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_menu_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("单选框")}', name:'testRadio', index:'a.test_radio', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_menu_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("复选框")}', name:'testCheckbox', index:'a.test_checkbox', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_menu_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("日期选择")}', name:'testDate', index:'a.test_date', width:150, align:"center"},
		{header:'${text("日期时间")}', name:'testDatetime', index:'a.test_datetime', width:150, align:"center"},
		{header:'${text("用户选择")}', name:'testUser.userName', index:'a.test_user_code', width:150, align:"center"},
		{header:'${text("机构选择")}', name:'testOffice.officeName', index:'a.test_office_code', width:150, align:"center"},
		{header:'${text("区域选择")}', name:'testAreaName', index:'a.test_area_code', width:150, align:"center"},
		{header:'${text("区域名称")}', name:'testAreaName', index:'a.test_area_name', width:150, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("创建时间")}', name:'createDate', index:'a.create_date', firstsortorder:'desc', width:150, align:"center"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:100, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('test:testData:edit')){
				actions.push('<a href="${ctx}/test/testData/form?id='+row.id+'" class="hsBtnList" title="${text("编辑数据")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/test/testData/disable?id='+row.id+'" class="btnList" title="${text("停用数据")}" data-confirm="${text("确认要停用该数据吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/test/testData/enable?id='+row.id+'" class="btnList" title="${text("启用数据")}" data-confirm="${text("确认要启用该数据吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/test/testData/delete?id='+row.id+'" class="btnList" title="${text("删除数据")}" data-confirm="${text("确认要删除该数据吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	// 双击表格行时调用
	ondblClickRow: function(id, rownum, colnum, event){
		js.addTabPage(null, '编辑数据', '${ctx}/test/testData/form?id='+id);
	},
	
	// 子表格支持演示
	subGrid: true,
	subGridRowExpanded: function (subgridId, rowId) {
		$('#'+subgridId).html('<h5><i class="icon-docs"></i> 子表数据</h5>'
				+'<table id="'+subgridId+'_subgrid"></table>');
		$('#'+subgridId+'_subgrid').dataGrid({
			url: '${ctx}/test/testData/subListData',
			postData: {'testData.id': rowId},
			autoGridHeight: function(){return 'auto'}, // 设置自动高度
			autoGridWidth:  function(){return $("#"+subgridId).width()}, // 设置自动高度
			// 设置数据表格列
			columnModel: [
				{header:'${text("单行文本")}', name:'testInput', width:150},
				{header:'${text("多行文本")}', name:'testTextarea', width:150},
				{header:'${text("下拉框")}', name:'testSelect', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('sys_menu_type')}", val, '${text("未知")}', true);
				}},
				{header:'${text("下拉多选")}', name:'testSelectMultiple', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('sys_menu_type')}", val, '${text("未知")}', true);
				}},
				{header:'${text("单选框")}', name:'testRadio', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('sys_menu_type')}", val, '${text("未知")}', true);
				}},
				{header:'${text("复选框")}', name:'testCheckbox', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('sys_menu_type')}", val, '${text("未知")}', true);
				}},
				{header:'${text("日期时间")}', name:'testDatetime', width:150, align:"center"},
				{header:'${text("用户名称")}', name:'testUser.userName', sortable:false, width:150, align:"center"},
				{header:'${text("机构名称")}', name:'testOffice.officeName', sortable:false, width:150, align:"center"},
				{header:'${text("区域名称")}', name:'testAreaName', width:150, align:"center"}
			],
			emptyDataHint: true, 	// 表格内没有数据的时候提示 “无数据显示” v4.1.7
			//# // 加载成功后执行事件
			ajaxSuccess: function(data){
				$(window).resize();
			}
		});
	},

	multiSort: true,		// 是否支持多列排序，给列指定 firstsortorder 可设定初次排序方式
	emptyDataHint: true, 	// 表格内没有数据的时候提示 “无数据显示” v4.1.7
	
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
//		// 无数据显示，v4.1.7 之前版本
// 		if (data.count == 0){
//			$('#dataGrid').parent().append("<div class=\"ml10\">没有符合数据</div>");
//		}
//		// 分页控件后面追加附加信息实例
// 		$('#dataGridPage ul:last').after('<ul class="pagination"><li class="disabled controls">分页附加信息</li></ul>');
// 		// 根据数据状态，高亮行，变色行，着色行
// 		$.each(data.list, function(k, v){
// 			if (v.status == Global.STATUS_DISABLE){
// 				$('#'+v.id+' td').css('background', '#e4fda8');
// 			}
// 		});
	}
})
// 开启表头下放搜索工具条
//.jqGrid('filterToolbar')
// 列表设置显示隐藏或排序后的事件（可用于设置持久化）
.on('jqGridRemapColumns',function(){
	log($('#dataGrid').dataGrid('getParam', 'columnModel'));
});
$("#btnTrunsTest").click(function(){ 
	js.ajaxSubmit("${ctx}/test/testData/transTest", function(data){
		js.showMessage(data.message);
		page();
	});
	return false;
});
</script>