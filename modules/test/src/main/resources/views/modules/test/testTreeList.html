<% layout('/layouts/default.html', {title: '数据管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('数据管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="${text('刷新')}"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="${text('展开一级')}"><i class="fa fa-angle-double-down"></i> ${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a>
				<% if(hasPermi('test:testTree:edit')){ %>
					<a href="${ctx}/test/testTree/form" class="btn btn-default btnTool" title="${text('新增数据')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
					<a href="#" class="btn btn-default" id="btnFixTreeData" title="修复树表数据（包含字段：parentCodes、treeLeaf、treeLevel、treeSorts、treeNames）"><i class="fa fa-refresh"></i> 修复</a>
				<% } %>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${testTree}" action="${ctx}/test/testTree/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('节点名称')}：</label>
					<div class="control-inline">
						<#form:input path="treeName" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注信息')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("节点名称")}', name:'treeName', index:'a.tree_name', width:250, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '( '+row.treeCode+' ) '+'<a href="${ctx}/test/testTree/form?treeCode='+row.treeCode+'" class="hsBtnList" data-title="${text("编辑数据")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("排序号")}', name:'treeSort', index:'a.tree_sort', width:150, align:"center"},
		{header:'${text("节点名称")}', name:'treeName', index:'a.tree_name', width:150, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("创建时间")}', name:'createDate', index:'a.create_date', width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('test:testTree:edit')){
				actions.push('<a href="${ctx}/test/testTree/form?treeCode='+row.treeCode+'" class="hsBtnList" title="${text("编辑数据")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/test/testTree/disable?treeCode='+row.treeCode+'" class="btnList" title="${text("停用数据")}" data-confirm="${text("确认要停用该数据吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/test/testTree/enable?treeCode='+row.treeCode+'" class="btnList" title="${text("启用数据")}" data-confirm="${text("确认要启用该数据吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/test/testTree/delete?treeCode='+row.treeCode+'" class="btnList" title="${text("删除数据")}" data-confirm="${text("确认要删除该数据及所有子数据吗？")}" data-deltreenode="'+row.id+'">删除</a>&nbsp;');
				actions.push('<a href="${ctx}/test/testTree/form?parentCode='+row.id+'" class="hsBtnList" title="${text("新增下级数据")}"><i class="fa fa-plus-square"></i></a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'treeName,remarks,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});

//# // 修复树表数据（包含字段：parentCodes、treeLeaf、treeLevel、treeSorts、treeNames）
$("#btnFixTreeData").click(function(){ 
	js.ajaxSubmit("${ctx}/test/testTree/fixTreeData", function(data){
		js.showMessage(data.message);
	});
	return false;
});
</script>