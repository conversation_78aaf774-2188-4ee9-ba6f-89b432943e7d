<% layout('/layouts/default.html', {title: '编辑表格多行编辑', libs: ['dataGrid','validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> 编辑表格多行编辑
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="查询"><i class="fa fa-filter"></i> 查询</a>
				<a href="#" id="dataGridAddRowBtn" class="btn btn-default"><i class="fa fa-plus"></i> 增行</a>
				<a href="#" id="btnGetData" class="btn btn-default"><i class="fa fa-hand-lizard-o"></i> 获取表格数据</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${testData}" action="${ctx}/test/testData/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">单行文本：</label>
					<div class="control-inline">
						<#form:input path="testInput" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">多行文本：</label>
					<div class="control-inline">
						<#form:input path="testTextarea" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">下拉框：</label>
					<div class="control-inline width-120">
						<#form:select path="testSelect" dictType="sys_menu_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">下拉多选：</label>
					<div class="control-inline width-120">
						<#form:select path="testSelectMultiple" dictType="sys_menu_type" multiple="true" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">单选框：</label>
					<div class="control-inline">
						<#form:radio path="testRadio" dictType="sys_menu_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">复选框：</label>
					<div class="control-inline">
						<#form:checkbox path="testCheckbox" dictType="sys_menu_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">日期选择：</label>
					<div class="control-inline">
						<#form:input path="testDate_gte" readonly="true" maxlength="20" class="form-control laydate width-date"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="testDate_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="testDate_lte" readonly="true" maxlength="20" class="form-control laydate width-date"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">日期时间：</label>
					<div class="control-inline">
						<#form:input path="testDatetime_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="testDatetime_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="testDatetime_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">用户选择：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="testUser" title="用户选择"
							path="testUser.userCode" labelPath="testUser.userName" 
							url="${ctx}/sys/office/treeData?isLoadUser=true" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">机构选择：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="testOffice" title="机构选择"
							path="testOffice.officeCode" labelPath="testOffice.officeName" 
							url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">区域选择：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="testAreaCode" title="区域选择"
							path="testAreaCode" labelPath="testAreaName" 
							url="${ctx}/sys/area/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">状态：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">备注信息：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<#form:form id="inputForm" model="${testData}" action="${ctx}/test/testData/save" method="post" class="form-horizontal table-form">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</#form:form>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	// 设置数据表格列
	columnModel: [
		{header:'状态', name:'status', editable:true, hidden:true, unformat: function(val, obj, cell){return $('#'+obj.rowId+'_'+obj.colModel.name, cell).val();}},
		{header:'主键', name:'id', editable:true, hidden:true, unformat: function(val, obj, cell){return $('#'+obj.rowId+'_'+obj.colModel.name, cell).val();}},
		{header:'单行文本', name:'testInput', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'200', 'class':'form-control'}, unformat: function(val, obj, cell){return $('#'+obj.rowId+'_'+obj.colModel.name, cell).val();}},
		{header:'多行文本', name:'testTextarea', width:150, editable:true, edittype:'textarea', editoptions:{'maxlength':'200', 'class':'form-control', 'rows':'1'}, unformat: function(val, obj, cell){return $('#'+obj.rowId+'_'+obj.colModel.name, cell).val();}},
		{header:'下拉框', name:'testSelect', width:100, 
			editable:true, edittype:'select', editoptions:{'class':'form-control',
				items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('sys_menu_type')}"),
				itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
					js.select2(element).on("change",function(){$(this).resetValid()});
				}
			}, unformat: function(val, obj, cell){return $('#'+obj.rowId+'_'+obj.colModel.name, cell).val();}
		},
// 		{header:'下拉多选', name:'testSelectMultiple', width:100, 
// 			editable:true, edittype:'select', editoptions:{multiple:true, 'class':'form-control',
// 				items: $.merge([], "#{@DictUtils.getDictListJson('sys_menu_type')}"),
// 				itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
// 					js.select2(element).on("change",function(){$(this).resetValid()});
// 				}
// 			}, unformat: function(val, obj, cell){return $('#'+obj.rowId+'_'+obj.colModel.name, cell).val();}
// 		},
		{header:'日期选择', name:'testDate', width:150, 
			formatter:'date', formatoptions:{srcformat:'Y-m-d H:i:s',newformat:'Y-m-d'},
			editable:true, edittype:'text', editoptions:{'class':'form-control laydate required', 'readonly':'true',
				dataInit: function(element){
					laydate.render({elem:element, type:'date', format:'yyyy-MM-dd'});
				}
			}, unformat: function(val, obj, cell){return $('#'+obj.rowId+'_'+obj.colModel.name, cell).val();}
		},
		{header:'日期时间', name:'testDatetime', width:150, 
			formatter:'date', formatoptions:{srcformat:'Y-m-d H:i:s',newformat:'Y-m-d H:i:s'},
			editable:true, edittype:'text', editoptions:{'class':'form-control laydate required', 'readonly':'true',
				dataInit: function(element){
					laydate.render({elem:element, type:'datetime', format:'yyyy-MM-dd HH:mm'});
				}
			}, unformat: function(val, obj, cell){return $('#'+obj.rowId+'_'+obj.colModel.name, cell).val();}
		},
		{header:'用户选择', name:'testUser', width:150,
			formatter: function(val, obj, row, act){
				return js.val(row, 'testUser.userCode')+'|'+js.val(row, 'testUser.userName');
			}, editable: true, edittype: "custom", editoptions: {
				custom_element: function(val, editOptions) {
					return js.template('treeselectTpl', {
						id: 'user_'+editOptions.id, title: '用户选择', 
						name: 'testUser.userCode', value: val.split('|')[0], 
						labelName: 'testUser.userName', labelValue: val.split('|')[1],
						url: '${ctx}/sys/office/treeData?isLoadUser=true', cssClass: ''
					});
				}
			}, unformat: function(val, obj, cell){return $('#user_'+obj.rowId+'_'+obj.colModel.name+'Code', cell).val();}
		},
		{header:'机构选择', name:'testOffice', width:150,
			formatter: function(val, obj, row, act){
				return js.val(row, 'testOffice.officeCode')+'|'+js.val(row, 'testOffice.officeName');
			}, editable: true, edittype: "custom", editoptions: {
				custom_element: function(val, editOptions) {
					return js.template('treeselectTpl', {
						id: 'office_'+editOptions.id, title: '机构选择', 
						name: 'testOffice.officeCode', value: val.split('|')[0], 
						labelName: 'testOffice.officeName', labelValue: val.split('|')[1],
						url: '${ctx}/sys/office/treeData?officeTypes=1,2', cssClass: ''
					});
				}
			}, unformat: function(val, obj, cell){return $('#office_'+obj.rowId+'_'+obj.colModel.name+'Code', cell).val();}
		},
		{header:'区域选择', name:'testAreaCode', width:150,
			formatter: function(val, obj, row, act){
				return js.val(row, 'testAreaCode')+'|'+js.val(row, 'testAreaName');
			}, editable: true, edittype: "custom", editoptions: {
				custom_element: function(val, editOptions) {
					return js.template('treeselectTpl', {
						id: 'area_'+editOptions.id, title: '区域选择', 
						name: 'testAreaCode', value: val.split('|')[0], 
						labelName: 'testAreaName', labelValue: val.split('|')[1],
						url: '${ctx}/sys/area/treeData', cssClass: ''
					});
				}
			}, unformat: function(val, obj, cell){return $('#area_'+obj.rowId+'_'+obj.colModel.name+'Code', cell).val();}
		},
		{header:'列表选择', name:'testListSelect', width:150,
			formatter: function(val, obj, row, act){
				return js.val(row, 'testListSelectCode')+'|'+js.val(row, 'testListSelectName');
			}, editable: true, edittype: "custom", editoptions: {
				custom_element: function(val, editOptions) {
					return js.template('listselectTpl', {
						id: 'user_'+editOptions.id, title: '用户选择', 
						name: 'testListSelectCode', value: val.split('|')[0], 
						labelName: 'testListSelectName', labelValue: val.split('|')[1],
						url: '${ctx}/sys/empUser/empUserSelect', 
						itemCode: 'userCode', itemName: 'userName',
						cssClass: ''
					});
				}
			}, unformat: function(val, obj, cell){return $('#user_'+obj.rowId+'_'+obj.colModel.name+'Code', cell).val();}
		},
		{header:'操作', name:'actions', width:80, sortable:false, fixed:true, formatter: function(val, obj, row, act){
			var actions = [];
			if (val == 'new'){
				actions.push('<a href="#" onclick="js.confirm(\'你确认要删除这条数据吗？\', function(){$(\'#dataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
			}else{
				actions.push('<a href="#" onclick="js.confirm(\'你确认要删除这条数据吗？\', function(){$(\'#dataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'})});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');return false;">删除</a>&nbsp;');
			}
			return actions.join('');
		}, editoptions: {defaultValue: 'new'}, unformat: function(val, obj, cell){return '';}}
	],
	
	// 编辑表格参数
	editGrid: true,				// 是否是编辑表格
	editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#dataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据
	
	// 编辑表格的提交数据参数
	editGridInputFormListName: 'testDataChildList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,testSort,testData.id,testInput,testTextarea,testSelect,testSelectMultiple,testRadio,testCheckbox,testDate,testDatetime,testUser.userCode,testOffice.officeCode,testAreaCode,testAreaName,', // 提交数据列表的属性字段
	
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
$('#btnGetData').click(function(){
	var data = $('#dataGrid').dataGrid('getRowData');
	log(data)
	js.showMessage('请按 F12 打开控制台，查看数据');
});
</script>
<script id="treeselectTpl" type="text/template">//<!--<div>
<#form:treeselect id="{{d.id}}" title="{{d.title}}" name="{{d.name}}" value="{{d.value}}"
	labelName="{{d.labelName}}" labelValue="{{d.labelValue}}" url="{{d.url}}"
	class="{{d.cssClass}}" btnClass="btn-sm" allowClear="true"/>
</div>//--></script>
<script id="listselectTpl" type="text/template">//<!--<div>
<#form:listselect id="{{d.id}}" title="{{d.title}}" name="{{d.name}}" value="{{d.value}}"
	labelName="{{d.labelName}}" labelValue="{{d.labelValue}}" url="{{d.url}}"
	itemCode="{{d.itemCode}}" itemName="{{d.itemName}}"
	class="{{d.cssClass}}" btnClass="btn-sm" allowClear="true"/>
</div>//--></script>