<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.test.dao.TestDataDao">
	
	<!-- 查询数据
	<select id="findList" resultType="TestData">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->
	
	<!-- 演示Map参数和返回值，支持分页 -->
	<select id="findListForMap" resultType="map">
		SELECT * FROM test_data a
		<where>
			<if test="testInput != null and testInput != ''">
				AND a.test_input = #{testInput}
			</if>
		</where>
		<if test="page != null and page.orderBy != null and page.orderBy != ''">
			ORDER BY ${page.orderBy}
		</if>
	</select>
	
</mapper>