<?xml version="1.0" encoding="UTF-8"?>
<diagram>
	<page_setting>
		<direction_horizontal>true</direction_horizontal>
		<scale>100</scale>
		<paper_size>A4 210 x 297 mm</paper_size>
		<top_margin>30</top_margin>
		<left_margin>30</left_margin>
		<bottom_margin>30</bottom_margin>
		<right_margin>30</right_margin>
	</page_setting>
	<category_index>0</category_index>
	<zoom>0.75</zoom>
	<x>0</x>
	<y>74</y>
	<default_color>
		<r>128</r>
		<g>128</g>
		<b>192</b>
	</default_color>
	<color>
		<r>255</r>
		<g>255</g>
		<b>255</b>
	</color>
	<font_name>Arial</font_name>
	<font_size>14</font_size>
	<settings>
		<database>StandardSQL</database>
		<capital>false</capital>
		<table_style></table_style>
		<notation></notation>
		<notation_level>0</notation_level>
		<notation_expand_group>true</notation_expand_group>
		<view_mode>2</view_mode>
		<outline_view_mode>2</outline_view_mode>
		<view_order_by>1</view_order_by>
		<auto_ime_change>false</auto_ime_change>
		<validate_physical_name>true</validate_physical_name>
		<use_bezier_curve>false</use_bezier_curve>
		<suspend_validator>false</suspend_validator>
		<export_setting>
			<export_ddl_setting>
				<output_path>db/job.sql</output_path>
				<encoding>UTF-8</encoding>
				<line_feed>CR+LF</line_feed>
				<is_open_after_saved>false</is_open_after_saved>
				<environment_id>7be191506f9daa8070b3ac14921dffd44063d2bb</environment_id>
				<category_id>null</category_id>
				<ddl_target>
					<create_comment>true</create_comment>
					<create_foreignKey>false</create_foreignKey>
					<create_index>true</create_index>
					<create_sequence>false</create_sequence>
					<create_table>true</create_table>
					<create_tablespace>false</create_tablespace>
					<create_trigger>false</create_trigger>
					<create_view>false</create_view>
					<drop_index>false</drop_index>
					<drop_sequence>false</drop_sequence>
					<drop_table>false</drop_table>
					<drop_tablespace>false</drop_tablespace>
					<drop_trigger>false</drop_trigger>
					<drop_view>false</drop_view>
					<inline_column_comment>false</inline_column_comment>
					<inline_table_comment>true</inline_table_comment>
					<comment_value_description>false</comment_value_description>
					<comment_value_logical_name>true</comment_value_logical_name>
					<comment_value_logical_name_description>false</comment_value_logical_name_description>
					<comment_replace_line_feed>false</comment_replace_line_feed>
					<comment_replace_string></comment_replace_string>
				</ddl_target>
			</export_ddl_setting>
			<export_excel_setting>
				<category_id>null</category_id>
				<output_path>db/job.xls</output_path>
				<template></template>
				<template_path></template_path>
				<used_default_template_lang>en</used_default_template_lang>
				<image_output></image_output>
				<is_open_after_saved>true</is_open_after_saved>
				<is_put_diagram>true</is_put_diagram>
				<is_use_logical_name>true</is_use_logical_name>
			</export_excel_setting>
			<export_html_setting>
				<output_dir></output_dir>
				<with_category_image>true</with_category_image>
				<with_image>true</with_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_html_setting>
			<export_image_setting>
				<output_file_path>db/job.png</output_file_path>
				<category_dir_path></category_dir_path>
				<with_category_image>true</with_category_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_image_setting>
			<export_java_setting>
				<java_output></java_output>
				<package_name></package_name>
				<class_name_suffix></class_name_suffix>
				<src_file_encoding></src_file_encoding>
				<with_hibernate>false</with_hibernate>
			</export_java_setting>
			<export_testdata_setting>
				<file_encoding></file_encoding>
				<file_path></file_path>
				<format>0</format>
			</export_testdata_setting>
		</export_setting>
		<category_settings>
			<free_layout>false</free_layout>
			<show_referred_tables>false</show_referred_tables>
			<categories>
			</categories>
		</category_settings>
		<translation_settings>
			<use>false</use>
			<translations>
			</translations>
		</translation_settings>
		<model_properties>
			<id></id>
			<height>-1</height>
			<width>-1</width>
				<font_name>Microsoft YaHei UI</font_name>
				<font_size>9</font_size>
			<x>50</x>
			<y>50</y>
			<color>
				<r>255</r>
				<g>255</g>
				<b>255</b>
			</color>
			<connections>
			</connections>
			<display>false</display>
			<creation_date>2016-12-25 17:25:00</creation_date>
			<model_property>
				<name>Project Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Model Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Version</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Company</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Author</name>
				<value></value>
			</model_property>
		</model_properties>
		<table_properties>
			<schema></schema>
		</table_properties>
		<environment_setting>
			<environment>
				<id>7be191506f9daa8070b3ac14921dffd44063d2bb</id>
				<name>Default</name>
			</environment>
		</environment_setting>
	</settings>
	<dictionary>
		<word>
			<id>8c2bca44d85612a0faf7fc999d564d3c76df8fd7</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>true</char_semantics>
			<description>触发器数据</description>
			<logical_name>触发器数据</logical_name>
			<physical_name>BLOB_DATA</physical_name>
			<type>blob</type>
		</word>
		<word>
			<id>36171bdbf7d9e6ad88fb01855c4cba449346eb7d</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>布尔属性1</description>
			<logical_name>布尔属性1</logical_name>
			<physical_name>BOOL_PROP_1</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>efcf5b21df3e62ebaaaf1a5bf6f131fca75e4340</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>布尔属性2</description>
			<logical_name>布尔属性2</logical_name>
			<physical_name>BOOL_PROP_2</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>a51f21e6a666ed19a8a2cf3e18579c599084fec8</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>true</char_semantics>
			<description>日历数据</description>
			<logical_name>日历数据</logical_name>
			<physical_name>CALENDAR</physical_name>
			<type>blob</type>
		</word>
		<word>
			<id>51162b291fed9b4bf9a33de7728cd1a803cb8fc1</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>日历名称</description>
			<logical_name>日历名称</logical_name>
			<physical_name>CALENDAR_NAME</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>8865a43474606f58dbc39685485a973952586b20</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>检查间隔</description>
			<logical_name>检查间隔</logical_name>
			<physical_name>CHECKIN_INTERVAL</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>c770f81e9e4df3bee7e09a8ac1c5031f9c725e9f</id>
			<length>120</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>Cron表达式</description>
			<logical_name>Cron表达式</logical_name>
			<physical_name>CRON_EXPRESSION</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>3158d764269a1c77f7436ae0c235c27c66eea274</id>
			<length>13</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>数值属性1</description>
			<logical_name>数值属性1</logical_name>
			<physical_name>DEC_PROP_1</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>0541263f4573dbff8241dee0adf10539bbc79b1d</id>
			<length>13</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>数值属性2</description>
			<logical_name>数值属性2</logical_name>
			<physical_name>DEC_PROP_2</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>121659ea1a25493d2d2268bf5bf4ca219de154e5</id>
			<length>250</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>作业描述</description>
			<logical_name>作业描述</logical_name>
			<physical_name>DESCRIPTION</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>69b9aff3159eabc17f075dd0b94efe81921fddc1</id>
			<length>250</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>说明</description>
			<logical_name>说明</logical_name>
			<physical_name>DESCRIPTION</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>852ec4bf108750eb72f92ec9fff3d1214a823984</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>结束时间</description>
			<logical_name>结束时间</logical_name>
			<physical_name>END_TIME</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>28d63db58389363224edc71fc91c4c8dff43b8a7</id>
			<length>95</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>登记编号</description>
			<logical_name>登记编号</logical_name>
			<physical_name>ENTRY_ID</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>8d724a342c5a837de52af5fec52afae5dd8246e0</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>记录开始时间</description>
			<logical_name>记录开始时间</logical_name>
			<physical_name>FIRED_TIME</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>05b22183fcbae3dbdb33ea88fc0e5a1cc5dcf26b</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>实例名称</description>
			<logical_name>实例名称</logical_name>
			<physical_name>INSTANCE_NAME</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>13343a75da86877ef77a4773de88b7043e15bfc2</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>整型属性1</description>
			<logical_name>整型属性1</logical_name>
			<physical_name>INT_PROP_1</physical_name>
			<type>integer</type>
		</word>
		<word>
			<id>216b805624b543a45e83129ca370b1d5b1c0169c</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>整型属性2</description>
			<logical_name>整型属性2</logical_name>
			<physical_name>INT_PROP_2</physical_name>
			<type>integer</type>
		</word>
		<word>
			<id>9f061d27e94cd4b7e0f42f875df87d448e58de41</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否持久化</description>
			<logical_name>是否持久化</logical_name>
			<physical_name>IS_DURABLE</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>f3b85f91534b748cbd0969d1bebcdb069d8095e7</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否并发</description>
			<logical_name>是否并发</logical_name>
			<physical_name>IS_NONCONCURRENT</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>2b1027a631ab0d739014fe3bf2876959ace93daa</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否并发执行</description>
			<logical_name>是否并发执行</logical_name>
			<physical_name>IS_NONCONCURRENT</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>86940d0cc869fb92e7bff3d174c2b653466933e8</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否更新数据</description>
			<logical_name>是否更新数据</logical_name>
			<physical_name>IS_UPDATE_DATA</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>2aa612cb4fae6026e771870cc8f483576704df3a</id>
			<length>250</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>任务class名称</description>
			<logical_name>任务Class名称</logical_name>
			<physical_name>JOB_CLASS_NAME</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>c31404775ca168969bc0a5758514b85a4c6a79d9</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>true</char_semantics>
			<description>调度数据对象</description>
			<logical_name>调度数据对象</logical_name>
			<physical_name>JOB_DATA</physical_name>
			<type>blob</type>
		</word>
		<word>
			<id>579a9bae0e75170e91d7c64a8b55d3a03cf37d10</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>true</char_semantics>
			<description>调用数据对象</description>
			<logical_name>调用数据对象</logical_name>
			<physical_name>JOB_DATA</physical_name>
			<type>blob</type>
		</word>
		<word>
			<id>b1c668048c08d02f02f431b7c4b94ffff3f172b4</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>任务群组</description>
			<logical_name>任务群组</logical_name>
			<physical_name>JOB_GROUP</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>dc0aa8e576c3f04be5c0acb22aa98d4fab3b9886</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>作业组名称</description>
			<logical_name>作业组名称</logical_name>
			<physical_name>JOB_GROUP</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>a4d19582cca2731b49caedff0f6a8fda22daa3e9</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>任务名称</description>
			<logical_name>任务名称</logical_name>
			<physical_name>JOB_NAME</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>46baab228ec79ab21ffa6c1cf851eba82bf6a31b</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>作业名称</description>
			<logical_name>作业名称</logical_name>
			<physical_name>JOB_NAME</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>e581ac8bae84992339fc3874725518f7408d0e08</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>检查时间</description>
			<logical_name>检查时间</logical_name>
			<physical_name>LAST_CHECKIN_TIME</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>4aeb3aecfbb1009f13fa36d3b057f6161aed5299</id>
			<length>40</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>锁定名称</description>
			<logical_name>锁定名称</logical_name>
			<physical_name>LOCK_NAME</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>40910165ced0d80a64a37d6c70c49cc6cc538b5c</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>长整型属性1</description>
			<logical_name>长整型属性1</logical_name>
			<physical_name>LONG_PROP_1</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>a677d4de200300c8c78ecfd77bf1f58f77ce0074</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>长整型属性2</description>
			<logical_name>长整型属性2</logical_name>
			<physical_name>LONG_PROP_2</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>1c8ebfe1afa3433652018dea46cf994de8b9f7f3</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>错过策略</description>
			<logical_name>错过策略</logical_name>
			<physical_name>MISFIRE_INSTR</physical_name>
			<type>smallint</type>
		</word>
		<word>
			<id>409e683bf812fd675ee82cfeff61dab8902c8c92</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>下次触发时间</description>
			<logical_name>下次触发时间</logical_name>
			<physical_name>NEXT_FIRE_TIME</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>d862c74aaa677177060ed1355b66b86276ae6552</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>上次触发时间</description>
			<logical_name>上次触发时间</logical_name>
			<physical_name>PREV_FIRE_TIME</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>0606be4215305501645ed371de04f719e82c935b</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>触发器优先级</description>
			<logical_name>触发器优先级</logical_name>
			<physical_name>PRIORITY</physical_name>
			<type>integer</type>
		</word>
		<word>
			<id>b94a2a836d04ccfdf540c2d0a21a1b8b77bcbf73</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>记录优先级</description>
			<logical_name>记录优先级</logical_name>
			<physical_name>PRIORITY</physical_name>
			<type>integer</type>
		</word>
		<word>
			<id>fbc403c25d095b2516bf975af6f477277ce1fd61</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>重复次数</description>
			<logical_name>重复次数</logical_name>
			<physical_name>REPEAT_COUNT</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>de89e481bbfb81ad86fc4373c28b3f10de1c258f</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>重复间隔</description>
			<logical_name>重复间隔</logical_name>
			<physical_name>REPEAT_INTERVAL</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>b93ca954085d2f47e16825fba91a5afacfe100bb</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否恢复</description>
			<logical_name>是否恢复</logical_name>
			<physical_name>REQUESTS_RECOVERY</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>74f98f1f79b816ceda4d547e28da42cc125bf990</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否接受恢复</description>
			<logical_name>是否接受恢复</logical_name>
			<physical_name>REQUESTS_RECOVERY</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>7866417c06d0b670da8e9242ca7a98fd66f0b415</id>
			<length>120</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>计划名称</description>
			<logical_name>计划名称</logical_name>
			<physical_name>SCHED_NAME</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>28997baceb09ffd866ad72d2ad881fdc84006184</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>记录结束时间</description>
			<logical_name>记录结束时间</logical_name>
			<physical_name>SCHED_TIME</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>6646e9bef89464ca1b96b8df62ceb02c580d62a5</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>开始时间</description>
			<logical_name>开始时间</logical_name>
			<physical_name>START_TIME</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>51f09d9e4a3b48b3fe0bef9b23a3a43a2b2bd4eb</id>
			<length>16</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>记录状态</description>
			<logical_name>记录状态</logical_name>
			<physical_name>STATE</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>5b9b4ce333f0da1bd04cc1d7b8b5ecaf6c112e61</id>
			<length>512</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>字符串属性1</description>
			<logical_name>字符串属性1</logical_name>
			<physical_name>STR_PROP_1</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>ba079f5789f071053823d467ddf094fcc8b1e89b</id>
			<length>512</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>字符串属性2</description>
			<logical_name>字符串属性2</logical_name>
			<physical_name>STR_PROP_2</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>45607edc151a847b0c7c2614cfcc910b80dca81f</id>
			<length>512</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>字符串属性3</description>
			<logical_name>字符串属性3</logical_name>
			<physical_name>STR_PROP_3</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>19f845577cdadb0805a8f893adf2eda32ad04fcc</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>触发时间</description>
			<logical_name>触发时间</logical_name>
			<physical_name>TIMES_TRIGGERED</physical_name>
			<type>bigint(n)</type>
		</word>
		<word>
			<id>c1f3c280ba3ade8000a9909e8cd35df5e3f7a9de</id>
			<length>80</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>时间地域编号</description>
			<logical_name>时间地域编号</logical_name>
			<physical_name>TIME_ZONE_ID</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>5d4a0e10b2c81b216217822e5ec5d9a13f73304d</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>触发器组名称</description>
			<logical_name>触发器组名称</logical_name>
			<physical_name>TRIGGER_GROUP</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>1c9f7bac61bee6470aaca125a2a2be6e92d38b03</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>触发组名称</description>
			<logical_name>触发组名称</logical_name>
			<physical_name>TRIGGER_GROUP</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>fe9d5973919feb5ff883d8a70c05e307dea0a3b9</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>触发器名称</description>
			<logical_name>触发器名称</logical_name>
			<physical_name>TRIGGER_NAME</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>4ed4548dfe0f6be674a1a30d9ec302cfb78b02d3</id>
			<length>16</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>触发器状态</description>
			<logical_name>触发器状态</logical_name>
			<physical_name>TRIGGER_STATE</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b23fb0ad01596a97b6c0f83b7b033bd7ee2457a5</id>
			<length>8</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>触发器类型</description>
			<logical_name>触发器类型</logical_name>
			<physical_name>TRIGGER_TYPE</physical_name>
			<type>varchar(n)</type>
		</word>
	</dictionary>
	<tablespace_set>
	</tablespace_set>
	<contents>
		<table>
			<id>0a669d2ca807e09513e934569f80c3818164267a</id>
			<height>149</height>
			<width>389</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>46</x>
			<y>720</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>6aeb7041ffe823d9828e82925c9cc2b2f59cabeb</id>
					<source>6bc57d78a1e278688bd2243aa44245ceff3762e0</source>
					<target>0a669d2ca807e09513e934569f80c3818164267a</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name>js_job_blob_triggers_ibfk_1</name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_job_blob_triggers</physical_name>
			<logical_name>Blob的触发器表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<id>c08d94d55d4ccbb15aa585a32f536d38ba637a2f</id>
					<referenced_column>bef43cda7135e2ad5244f31539e87f72869d4d50</referenced_column>
					<relation>6aeb7041ffe823d9828e82925c9cc2b2f59cabeb</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>7e232d13f76fcaada864ac8f3e5c96f35936f481</id>
					<referenced_column>479aea404d425bc2539586eea354d2dd7d7c4fbf</referenced_column>
					<relation>6aeb7041ffe823d9828e82925c9cc2b2f59cabeb</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>d4a879db54ce0a79f584b8fea8972f0738a487a6</id>
					<referenced_column>3506a9e682ab1f467e0110291032ab961dd60dcc</referenced_column>
					<relation>6aeb7041ffe823d9828e82925c9cc2b2f59cabeb</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8c2bca44d85612a0faf7fc999d564d3c76df8fd7</word_id>
					<id>9aa228eb8a878ff76d4d7bd7a4c272d4dbda9989</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>blob</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>faa914f7c4270ea2622669197abe21f36fa50366</id>
			<height>123</height>
			<width>363</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>948</x>
			<y>1095</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_job_calendars</physical_name>
			<logical_name>调度日历表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>7866417c06d0b670da8e9242ca7a98fd66f0b415</word_id>
					<id>bbe00aaa091ce6f0dec583542b2b4c0db546e9c3</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>51162b291fed9b4bf9a33de7728cd1a803cb8fc1</word_id>
					<id>daba71adddb396d54f500970d40ced93861720ce</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a51f21e6a666ed19a8a2cf3e18579c599084fec8</word_id>
					<id>7191c839daf3c519c214cd20507b14fa9451fa9b</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>blob</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>5a7b42fcb919ebc8bfe84df047923c6cc95cbbd8</id>
			<height>305</height>
			<width>413</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>60</x>
			<y>84</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_job_job_details</physical_name>
			<logical_name>作业详情表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>7866417c06d0b670da8e9242ca7a98fd66f0b415</word_id>
					<id>0877af097636bb4eac55b4c6e41438d1eb3e1fed</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a4d19582cca2731b49caedff0f6a8fda22daa3e9</word_id>
					<id>486e71379ac8ae8e87211cda02b1bb32cadd4b7f</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b1c668048c08d02f02f431b7c4b94ffff3f172b4</word_id>
					<id>4a837efa64308c42bd503992dde096366924fb0f</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>69b9aff3159eabc17f075dd0b94efe81921fddc1</word_id>
					<id>c008e6cfc0e51e9172b7803536776f9d2311cd31</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2aa612cb4fae6026e771870cc8f483576704df3a</word_id>
					<id>e3fa9a64a7b5ca62c2d6c6854ac9acc81afcf88c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>9f061d27e94cd4b7e0f42f875df87d448e58de41</word_id>
					<id>c38ff79986e9b0bbb6ac31e78d3be2af913f424f</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2b1027a631ab0d739014fe3bf2876959ace93daa</word_id>
					<id>3c5820faf8c315471042a49d0724dbe63dde16c7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>86940d0cc869fb92e7bff3d174c2b653466933e8</word_id>
					<id>62b5329990511958ffb13266ca0c6ac62ef4c86e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b93ca954085d2f47e16825fba91a5afacfe100bb</word_id>
					<id>31da7bbee6ec2d37989491aba51200d1823268b7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>579a9bae0e75170e91d7c64a8b55d3a03cf37d10</word_id>
					<id>65cf0ba9bdd938fb615497341609ba4c1f790cca</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>blob</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_J_GRP</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>0877af097636bb4eac55b4c6e41438d1eb3e1fed</id>
							<desc>false</desc>
						</column>
						<column>
							<id>4a837efa64308c42bd503992dde096366924fb0f</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_J_REQ_RECOVERY</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>0877af097636bb4eac55b4c6e41438d1eb3e1fed</id>
							<desc>false</desc>
						</column>
						<column>
							<id>31da7bbee6ec2d37989491aba51200d1823268b7</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>d3188f768a9a90cea390630888b956f99945518c</id>
			<height>97</height>
			<width>335</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>516</x>
			<y>1095</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_job_locks</physical_name>
			<logical_name>调度数据锁表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>7866417c06d0b670da8e9242ca7a98fd66f0b415</word_id>
					<id>58e48eee34e9c75b387ae011ebc06cd026ec08b5</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>4aeb3aecfbb1009f13fa36d3b057f6161aed5299</word_id>
					<id>9fd4702de4332dcf990f2c61354afc8d3591ed42</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>f674068c91333a67f7fb339b7c5ba4a45c6e419f</id>
			<height>97</height>
			<width>403</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>1421</x>
			<y>1095</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_job_paused_trigger_grps</physical_name>
			<logical_name>已暂停的触发器组</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>7866417c06d0b670da8e9242ca7a98fd66f0b415</word_id>
					<id>2d7f5466d40425123d4d637d3dbe590d90309db0</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>1c9f7bac61bee6470aaca125a2a2be6e92d38b03</word_id>
					<id>cb69d031b84d634e13c3cee7fb1574d5e40045a2</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>ae8dc24f83c383a4e82be1686b12fe3a3d9416c6</id>
			<height>149</height>
			<width>356</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>39</x>
			<y>1095</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_job_scheduler_state</physical_name>
			<logical_name>调度状态检查表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>7866417c06d0b670da8e9242ca7a98fd66f0b415</word_id>
					<id>0f7632ca317787a010e87c941d86f36aca73d818</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>05b22183fcbae3dbdb33ea88fc0e5a1cc5dcf26b</word_id>
					<id>3feae9871ea28eb229eea7a1b227047faba594fd</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e581ac8bae84992339fc3874725518f7408d0e08</word_id>
					<id>db2eaf339faaf919d0f9e29ece56576a0b2d3d6c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8865a43474606f58dbc39685485a973952586b20</word_id>
					<id>4a57318d895865a197b091a330670e1c9c6e139a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>54776d97b42964ac7ce49ad4b0ba9fa277d74461</id>
			<height>-1</height>
			<width>-1</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>986</x>
			<y>720</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>ba36e142cc5d5fd6b5a727496c53cdadf1a67853</id>
					<source>6bc57d78a1e278688bd2243aa44245ceff3762e0</source>
					<target>54776d97b42964ac7ce49ad4b0ba9fa277d74461</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name>js_job_simple_triggers_ibfk_1</name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_job_simple_triggers</physical_name>
			<logical_name>简单的触发器表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<id>2b891a84b19252ca0933447a82587d81ed0fb401</id>
					<referenced_column>bef43cda7135e2ad5244f31539e87f72869d4d50</referenced_column>
					<relation>ba36e142cc5d5fd6b5a727496c53cdadf1a67853</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>dec12b6c308687428126f8179c412725f6cdaa66</id>
					<referenced_column>479aea404d425bc2539586eea354d2dd7d7c4fbf</referenced_column>
					<relation>ba36e142cc5d5fd6b5a727496c53cdadf1a67853</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>1b12095d3efc75a5347fd8c5ec89231813ca72bf</id>
					<referenced_column>3506a9e682ab1f467e0110291032ab961dd60dcc</referenced_column>
					<relation>ba36e142cc5d5fd6b5a727496c53cdadf1a67853</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>fbc403c25d095b2516bf975af6f477277ce1fd61</word_id>
					<id>ce33aa0fad3683259353098060046eb0c3bc7abd</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>de89e481bbfb81ad86fc4373c28b3f10de1c258f</word_id>
					<id>047c4063f9f5edcf990812721b5b907ec4d67c40</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>19f845577cdadb0805a8f893adf2eda32ad04fcc</word_id>
					<id>34b3ee5159b6078545207c76974a398c0683126c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>949d0666b959a724d98e2950a1558a79ab5963a7</id>
			<height>409</height>
			<width>389</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>1440</x>
			<y>603</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>d8587efa8d383378d951a147eee889773e5fa7fb</id>
					<source>6bc57d78a1e278688bd2243aa44245ceff3762e0</source>
					<target>949d0666b959a724d98e2950a1558a79ab5963a7</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name>js_job_simprop_triggers_ibfk_1</name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_job_simprop_triggers</physical_name>
			<logical_name>日历的触发器表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<id>71e10881adbb474de7f49f2d40827986d0ede709</id>
					<referenced_column>bef43cda7135e2ad5244f31539e87f72869d4d50</referenced_column>
					<relation>d8587efa8d383378d951a147eee889773e5fa7fb</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>568c8fb076378cf4f6f33460a34fd393c67f899a</id>
					<referenced_column>479aea404d425bc2539586eea354d2dd7d7c4fbf</referenced_column>
					<relation>d8587efa8d383378d951a147eee889773e5fa7fb</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>bf1a46c68ff9dcb56c9903c4b46942de00e2af4b</id>
					<referenced_column>3506a9e682ab1f467e0110291032ab961dd60dcc</referenced_column>
					<relation>d8587efa8d383378d951a147eee889773e5fa7fb</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5b9b4ce333f0da1bd04cc1d7b8b5ecaf6c112e61</word_id>
					<id>55d6cf973f45c913184a3d21e951033e90592675</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ba079f5789f071053823d467ddf094fcc8b1e89b</word_id>
					<id>e20d54855f6c82519ad1ae4e4799f885e4d1b73a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>45607edc151a847b0c7c2614cfcc910b80dca81f</word_id>
					<id>3f6c1b21f7a030039e09e3360d3b7aa68f76e788</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>13343a75da86877ef77a4773de88b7043e15bfc2</word_id>
					<id>20f302dc71c1854c502860acd3de5d7607df004a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>integer</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>216b805624b543a45e83129ca370b1d5b1c0169c</word_id>
					<id>82c5c8166f6f6ba79bff84fb3732436bbe963343</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>integer</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>40910165ced0d80a64a37d6c70c49cc6cc538b5c</word_id>
					<id>f860e73ab95ea61b7020310a14adfdbceee70e3c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a677d4de200300c8c78ecfd77bf1f58f77ce0074</word_id>
					<id>37b137288d895ce6cc9db6a5e7bfcc38f3e9ffcf</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3158d764269a1c77f7436ae0c235c27c66eea274</word_id>
					<id>38c2538a593845e0cd04777ba6ba41898791429f</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>decimal(p,s)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>0541263f4573dbff8241dee0adf10539bbc79b1d</word_id>
					<id>5927e220c5ee979477073d844fef92b016bb883c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>decimal(p,s)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>36171bdbf7d9e6ad88fb01855c4cba449346eb7d</word_id>
					<id>dba4bf66d8d0581f310db27b48945a65bd972753</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>efcf5b21df3e62ebaaaf1a5bf6f131fca75e4340</word_id>
					<id>a6193aaecfee05d0c475c8f7141821a471063437</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>336d6ca3cbb2cd5710dbf7dbe23c39bb48488ef7</id>
			<height>383</height>
			<width>389</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>1428</x>
			<y>75</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_job_fired_triggers</physical_name>
			<logical_name>已触发的作业表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>7866417c06d0b670da8e9242ca7a98fd66f0b415</word_id>
					<id>f4ae03dc6bd634f0fc98bc1eb810a2506be13600</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>28d63db58389363224edc71fc91c4c8dff43b8a7</word_id>
					<id>6f8b26b75fcea8438182ac68f3d5a223ba5d0318</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>fe9d5973919feb5ff883d8a70c05e307dea0a3b9</word_id>
					<id>c2808aece324caf392a1507eb4ffc8a2bc7588eb</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5d4a0e10b2c81b216217822e5ec5d9a13f73304d</word_id>
					<id>5f08956f036c50ea2ac423d7b74c3905feb2a2ca</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>05b22183fcbae3dbdb33ea88fc0e5a1cc5dcf26b</word_id>
					<id>96a166eb428fbbaf2a8acff16276845842c182d9</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8d724a342c5a837de52af5fec52afae5dd8246e0</word_id>
					<id>795932196f4b65d18fa556cdc110a05bb73f75c3</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>28997baceb09ffd866ad72d2ad881fdc84006184</word_id>
					<id>664b0a35576d15f08082027576b6dfaefb4065e4</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b94a2a836d04ccfdf540c2d0a21a1b8b77bcbf73</word_id>
					<id>b8a7c498362a6381b1b5d229ee8ae343429069b1</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>integer</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>51f09d9e4a3b48b3fe0bef9b23a3a43a2b2bd4eb</word_id>
					<id>bc2a8cdbba275ad432e0503073cd82bb1331b887</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>46baab228ec79ab21ffa6c1cf851eba82bf6a31b</word_id>
					<id>2d97f56bfe346120db66773ed0b2e8128e0b8beb</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>dc0aa8e576c3f04be5c0acb22aa98d4fab3b9886</word_id>
					<id>8bcdef0cf198720988fd38705410650b7f7a32ba</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>f3b85f91534b748cbd0969d1bebcdb069d8095e7</word_id>
					<id>195d4a854b8d216b5153f6e6c2847c2924ef0bbe</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>74f98f1f79b816ceda4d547e28da42cc125bf990</word_id>
					<id>d0eb542a7dc1166d31fb1d0ea29c431ae83d4762</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_FT_INST_JOB_REQ_RCVRY</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>f4ae03dc6bd634f0fc98bc1eb810a2506be13600</id>
							<desc>false</desc>
						</column>
						<column>
							<id>96a166eb428fbbaf2a8acff16276845842c182d9</id>
							<desc>false</desc>
						</column>
						<column>
							<id>d0eb542a7dc1166d31fb1d0ea29c431ae83d4762</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_FT_J_G</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>f4ae03dc6bd634f0fc98bc1eb810a2506be13600</id>
							<desc>false</desc>
						</column>
						<column>
							<id>2d97f56bfe346120db66773ed0b2e8128e0b8beb</id>
							<desc>false</desc>
						</column>
						<column>
							<id>8bcdef0cf198720988fd38705410650b7f7a32ba</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_FT_JG</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>f4ae03dc6bd634f0fc98bc1eb810a2506be13600</id>
							<desc>false</desc>
						</column>
						<column>
							<id>8bcdef0cf198720988fd38705410650b7f7a32ba</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_FT_T_G</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>f4ae03dc6bd634f0fc98bc1eb810a2506be13600</id>
							<desc>false</desc>
						</column>
						<column>
							<id>c2808aece324caf392a1507eb4ffc8a2bc7588eb</id>
							<desc>false</desc>
						</column>
						<column>
							<id>5f08956f036c50ea2ac423d7b74c3905feb2a2ca</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_FT_TG</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>f4ae03dc6bd634f0fc98bc1eb810a2506be13600</id>
							<desc>false</desc>
						</column>
						<column>
							<id>5f08956f036c50ea2ac423d7b74c3905feb2a2ca</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_FT_TRIG_INST_NAME</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>f4ae03dc6bd634f0fc98bc1eb810a2506be13600</id>
							<desc>false</desc>
						</column>
						<column>
							<id>96a166eb428fbbaf2a8acff16276845842c182d9</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>6bc57d78a1e278688bd2243aa44245ceff3762e0</id>
			<height>461</height>
			<width>389</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>744</x>
			<y>36</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>411c9b3243030c6503ab26503e39ad2e733b9040</id>
					<source>5a7b42fcb919ebc8bfe84df047923c6cc95cbbd8</source>
					<target>6bc57d78a1e278688bd2243aa44245ceff3762e0</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name>js_job_triggers_ibfk_1</name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_job_triggers</physical_name>
			<logical_name>作业触发器表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
					<referenced_column>0877af097636bb4eac55b4c6e41438d1eb3e1fed</referenced_column>
					<relation>411c9b3243030c6503ab26503e39ad2e733b9040</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>fe9d5973919feb5ff883d8a70c05e307dea0a3b9</word_id>
					<id>479aea404d425bc2539586eea354d2dd7d7c4fbf</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5d4a0e10b2c81b216217822e5ec5d9a13f73304d</word_id>
					<id>3506a9e682ab1f467e0110291032ab961dd60dcc</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>12b02a5d0594a292344aea350c589dfbe568e3a3</id>
					<referenced_column>486e71379ac8ae8e87211cda02b1bb32cadd4b7f</referenced_column>
					<relation>411c9b3243030c6503ab26503e39ad2e733b9040</relation>
					<description>作业名称</description>
					<unique_key_name></unique_key_name>
					<logical_name>作业名称</logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>ce0c63dcd207036a6722c80172644ab7838b94b0</id>
					<referenced_column>4a837efa64308c42bd503992dde096366924fb0f</referenced_column>
					<relation>411c9b3243030c6503ab26503e39ad2e733b9040</relation>
					<description>作业组名称</description>
					<unique_key_name></unique_key_name>
					<logical_name>作业组名称</logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>121659ea1a25493d2d2268bf5bf4ca219de154e5</word_id>
					<id>2e146e3d9ea9dd8e25851ab9509c6975f7d27703</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>409e683bf812fd675ee82cfeff61dab8902c8c92</word_id>
					<id>8764c3373449a0bea2a033c2143b4398f80df996</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>d862c74aaa677177060ed1355b66b86276ae6552</word_id>
					<id>fe63da48d5aebf4a0c3def6b36db9ad56ecdbbda</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>0606be4215305501645ed371de04f719e82c935b</word_id>
					<id>51753e9915b085ea47ee2edbba8852f6fba53ab7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>integer</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>4ed4548dfe0f6be674a1a30d9ec302cfb78b02d3</word_id>
					<id>73d0abe397d3ea04d0784338d607b2d9b71f8fb6</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b23fb0ad01596a97b6c0f83b7b033bd7ee2457a5</word_id>
					<id>2ac2efa9320ca2cf31a15dc68f8e6c04b509f71d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6646e9bef89464ca1b96b8df62ceb02c580d62a5</word_id>
					<id>7bcc7408ec0ff0d3bce7dc220d20decd5a1a321a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>852ec4bf108750eb72f92ec9fff3d1214a823984</word_id>
					<id>4947f81068525095d559ea5a8623d31d1925db8d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>bigint(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>51162b291fed9b4bf9a33de7728cd1a803cb8fc1</word_id>
					<id>1e4ab4f5550d6670568ea859d3f5fb1d55f86ecc</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>1c8ebfe1afa3433652018dea46cf994de8b9f7f3</word_id>
					<id>80e78d40b98dcd43914bfb838295029bdea9f545</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>smallint</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>c31404775ca168969bc0a5758514b85a4c6a79d9</word_id>
					<id>eca6d569650e93570e35c535fd3becda9bb0edf0</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>blob</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_C</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>1e4ab4f5550d6670568ea859d3f5fb1d55f86ecc</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_G</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>3506a9e682ab1f467e0110291032ab961dd60dcc</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_J</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>12b02a5d0594a292344aea350c589dfbe568e3a3</id>
							<desc>false</desc>
						</column>
						<column>
							<id>ce0c63dcd207036a6722c80172644ab7838b94b0</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_JG</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>ce0c63dcd207036a6722c80172644ab7838b94b0</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_N_G_STATE</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>3506a9e682ab1f467e0110291032ab961dd60dcc</id>
							<desc>false</desc>
						</column>
						<column>
							<id>73d0abe397d3ea04d0784338d607b2d9b71f8fb6</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_N_STATE</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>479aea404d425bc2539586eea354d2dd7d7c4fbf</id>
							<desc>false</desc>
						</column>
						<column>
							<id>3506a9e682ab1f467e0110291032ab961dd60dcc</id>
							<desc>false</desc>
						</column>
						<column>
							<id>73d0abe397d3ea04d0784338d607b2d9b71f8fb6</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_NEXT_FIRE_TIME</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>8764c3373449a0bea2a033c2143b4398f80df996</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_NFT_MISFIRE</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>80e78d40b98dcd43914bfb838295029bdea9f545</id>
							<desc>false</desc>
						</column>
						<column>
							<id>8764c3373449a0bea2a033c2143b4398f80df996</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_NFT_ST</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>73d0abe397d3ea04d0784338d607b2d9b71f8fb6</id>
							<desc>false</desc>
						</column>
						<column>
							<id>8764c3373449a0bea2a033c2143b4398f80df996</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_NFT_ST_MISFIRE</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>80e78d40b98dcd43914bfb838295029bdea9f545</id>
							<desc>false</desc>
						</column>
						<column>
							<id>8764c3373449a0bea2a033c2143b4398f80df996</id>
							<desc>false</desc>
						</column>
						<column>
							<id>73d0abe397d3ea04d0784338d607b2d9b71f8fb6</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_NFT_ST_MISFIRE_GRP</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>80e78d40b98dcd43914bfb838295029bdea9f545</id>
							<desc>false</desc>
						</column>
						<column>
							<id>8764c3373449a0bea2a033c2143b4398f80df996</id>
							<desc>false</desc>
						</column>
						<column>
							<id>3506a9e682ab1f467e0110291032ab961dd60dcc</id>
							<desc>false</desc>
						</column>
						<column>
							<id>73d0abe397d3ea04d0784338d607b2d9b71f8fb6</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>IDX_QRTZ_T_STATE</name>
					<type>BTREE</type>
					<description></description>
					<columns>
						<column>
							<id>bef43cda7135e2ad5244f31539e87f72869d4d50</id>
							<desc>false</desc>
						</column>
						<column>
							<id>73d0abe397d3ea04d0784338d607b2d9b71f8fb6</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>cb6e0bced6631e13b509fdfff94c032ace5d1a05</id>
			<height>175</height>
			<width>394</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>516</x>
			<y>720</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>8b6275bc330475f6e06cb92eae84d91d99bc407e</id>
					<source>6bc57d78a1e278688bd2243aa44245ceff3762e0</source>
					<target>cb6e0bced6631e13b509fdfff94c032ace5d1a05</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name>js_job_cron_triggers_ibfk_1</name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_job_cron_triggers</physical_name>
			<logical_name>Cron的触发器表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<id>0b96d67d14af20c7fa602fba0c9a846f49790da5</id>
					<referenced_column>bef43cda7135e2ad5244f31539e87f72869d4d50</referenced_column>
					<relation>8b6275bc330475f6e06cb92eae84d91d99bc407e</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>65a769649d9af73f3ffb59b8ab14f28c29775120</id>
					<referenced_column>479aea404d425bc2539586eea354d2dd7d7c4fbf</referenced_column>
					<relation>8b6275bc330475f6e06cb92eae84d91d99bc407e</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>901e42d7b8eb8f2962594b6a4194d945023cd10e</id>
					<referenced_column>3506a9e682ab1f467e0110291032ab961dd60dcc</referenced_column>
					<relation>8b6275bc330475f6e06cb92eae84d91d99bc407e</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>c770f81e9e4df3bee7e09a8ac1c5031f9c725e9f</word_id>
					<id>28ee5c7bce918c8c4c70ccfbad7093a63978dac8</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>c1f3c280ba3ade8000a9909e8cd35df5e3f7a9de</word_id>
					<id>9a273f7be9eaf47f5c88ba72e63b5bc6e5fc912d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
	</contents>
	<column_groups>
	</column_groups>
	<test_data_list>
	</test_data_list>
	<sequence_set>
	</sequence_set>
	<trigger_set>
	</trigger_set>
	<change_tracking_list>
	</change_tracking_list>
</diagram>
