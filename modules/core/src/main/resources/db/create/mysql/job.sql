--
-- In your Quartz properties file, you'll need to set 
-- org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
--
--
-- By: <PERSON>
--  I didn't see this anywhere, so I thought I'd post it here. This is the script from Quartz to create the tables in a MySQL database, modified to use INNODB instead of MYISAM.

CREATE TABLE ${_prefix}job_JOB_DETAILS(
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	JOB_NAME VARCHAR(200) NOT NULL COMMENT '任务名称',
	JOB_GROUP VARCHAR(200) NOT NULL COMMENT '任务群组',
	DESCRIPTION VARCHAR(250) NULL COMMENT '说明',
	JOB_CLASS_NAME VARCHAR(250) NOT NULL COMMENT '任务Class名称',
	IS_DURABLE VARCHAR(1) NOT NULL COMMENT '是否持久化',
	IS_NONCONCURRENT VARCHAR(1) NOT NULL COMMENT '是否并发执行',
	IS_UPDATE_DATA VARCHAR(1) NOT NULL COMMENT '是否更新数据',
	REQUESTS_RECOVERY VARCHAR(1) NOT NULL COMMENT '是否恢复',
	JOB_DATA BLOB NULL COMMENT '调用数据对象',
	PRIMARY KEY (SCHED_NAME,JOB_NAME,JOB_GROUP))
ENGINE=InnoDB COMMENT='任务详情表';

CREATE TABLE ${_prefix}job_TRIGGERS (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	TRIGGER_NAME VARCHAR(200) NOT NULL COMMENT '触发器名称',
	TRIGGER_GROUP VARCHAR(200) NOT NULL COMMENT '触发器组名称',
	JOB_NAME VARCHAR(200) NOT NULL COMMENT '作业名称',
	JOB_GROUP VARCHAR(200) NOT NULL COMMENT '作业组名称',
	DESCRIPTION VARCHAR(250) NULL COMMENT '作业描述',
	NEXT_FIRE_TIME BIGINT(13) NULL COMMENT '下次触发时间',
	PREV_FIRE_TIME BIGINT(13) NULL COMMENT '上次触发时间',
	PRIORITY INTEGER NULL COMMENT '触发器优先级',
	TRIGGER_STATE VARCHAR(16) NOT NULL COMMENT '触发器状态',
	TRIGGER_TYPE VARCHAR(8) NOT NULL COMMENT '触发器类型',
	START_TIME BIGINT(13) NOT NULL COMMENT '开始时间',
	END_TIME BIGINT(13) NULL COMMENT '结束时间',
	CALENDAR_NAME VARCHAR(200) NULL COMMENT '日历名称',
	MISFIRE_INSTR SMALLINT(2) NULL COMMENT '错过策略',
	JOB_DATA BLOB NULL COMMENT '调度数据对象',
	PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
	FOREIGN KEY (SCHED_NAME,JOB_NAME,JOB_GROUP)
	REFERENCES ${_prefix}job_JOB_DETAILS(SCHED_NAME,JOB_NAME,JOB_GROUP))
ENGINE=InnoDB COMMENT='触发器表';

CREATE TABLE ${_prefix}job_SIMPLE_TRIGGERS (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	TRIGGER_NAME VARCHAR(200) NOT NULL COMMENT '触发器名称',
	TRIGGER_GROUP VARCHAR(200) NOT NULL COMMENT '触发器组名称',
	REPEAT_COUNT BIGINT(7) NOT NULL COMMENT '重复次数',
	REPEAT_INTERVAL BIGINT(12) NOT NULL COMMENT '重复间隔',
	TIMES_TRIGGERED BIGINT(10) NOT NULL COMMENT '触发时间',
	PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
	FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP)
	REFERENCES ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP))
ENGINE=InnoDB COMMENT='简单触发器表';

CREATE TABLE ${_prefix}job_CRON_TRIGGERS (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	TRIGGER_NAME VARCHAR(200) NOT NULL COMMENT '触发器名称',
	TRIGGER_GROUP VARCHAR(200) NOT NULL COMMENT '触发器组名称',
	CRON_EXPRESSION VARCHAR(120) NOT NULL COMMENT 'Cron表达式',
	TIME_ZONE_ID VARCHAR(80) COMMENT '时间地域编号',
	PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
	FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP)
	REFERENCES ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP))
ENGINE=InnoDB COMMENT='Cron触发器表';

CREATE TABLE ${_prefix}job_SIMPROP_TRIGGERS (      
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	TRIGGER_NAME VARCHAR(200) NOT NULL COMMENT '触发器名称',
	TRIGGER_GROUP VARCHAR(200) NOT NULL COMMENT '触发器组名称',
    STR_PROP_1 VARCHAR(512) NULL COMMENT '字符串属性1',
    STR_PROP_2 VARCHAR(512) NULL COMMENT '字符串属性2',
    STR_PROP_3 VARCHAR(512) NULL COMMENT '字符串属性3',
    INT_PROP_1 INT NULL COMMENT '整型属性1',
    INT_PROP_2 INT NULL COMMENT '整型属性2',
    LONG_PROP_1 BIGINT NULL COMMENT '长整型属性1',
    LONG_PROP_2 BIGINT NULL COMMENT '长整型属性2',
    DEC_PROP_1 NUMERIC(13,4) NULL COMMENT '数值属性1',
    DEC_PROP_2 NUMERIC(13,4) NULL COMMENT '数值属性2',
    BOOL_PROP_1 VARCHAR(1) NULL COMMENT '布尔属性1',
    BOOL_PROP_2 VARCHAR(1) NULL COMMENT '布尔属性2',
    PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
    FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP) 
    REFERENCES ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP))
ENGINE=InnoDB COMMENT='日历触发器表';

CREATE TABLE ${_prefix}job_BLOB_TRIGGERS (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	TRIGGER_NAME VARCHAR(200) NOT NULL COMMENT '触发器名称',
	TRIGGER_GROUP VARCHAR(200) NOT NULL COMMENT '触发器组名称',
	BLOB_DATA BLOB NULL COMMENT '触发器数据',
	PRIMARY KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP),
	INDEX (SCHED_NAME,TRIGGER_NAME, TRIGGER_GROUP),
	FOREIGN KEY (SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP)
	REFERENCES ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP))
ENGINE=InnoDB COMMENT='Blob类型触发器表';

CREATE TABLE ${_prefix}job_CALENDARS (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	CALENDAR_NAME VARCHAR(200) NOT NULL COMMENT '日历名称',
	CALENDAR BLOB NOT NULL COMMENT '日历数据',
	PRIMARY KEY (SCHED_NAME,CALENDAR_NAME))
ENGINE=InnoDB COMMENT='日历表';

CREATE TABLE ${_prefix}job_PAUSED_TRIGGER_GRPS (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	TRIGGER_GROUP VARCHAR(200) NOT NULL COMMENT '触发组名称',
	PRIMARY KEY (SCHED_NAME,TRIGGER_GROUP))
ENGINE=InnoDB COMMENT='暂停触发器表';

CREATE TABLE ${_prefix}job_FIRED_TRIGGERS (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	ENTRY_ID VARCHAR(95) NOT NULL COMMENT '登记编号',
	TRIGGER_NAME VARCHAR(200) NOT NULL COMMENT '触发器名称',
	TRIGGER_GROUP VARCHAR(200) NOT NULL COMMENT '触发器组名称',
	INSTANCE_NAME VARCHAR(200) NOT NULL COMMENT '实例名称',
	FIRED_TIME BIGINT(13) NOT NULL COMMENT '记录开始时间',
	SCHED_TIME BIGINT(13) NOT NULL COMMENT '记录结束时间',
	PRIORITY INTEGER NOT NULL COMMENT '记录优先级',
	STATE VARCHAR(16) NOT NULL COMMENT '记录状态',
	JOB_NAME VARCHAR(200) NULL COMMENT '作业名称',
	JOB_GROUP VARCHAR(200) NULL COMMENT '作业组名称',
	IS_NONCONCURRENT VARCHAR(1) NULL COMMENT '是否并发',
	REQUESTS_RECOVERY VARCHAR(1) NULL COMMENT '是否接受恢复',
	PRIMARY KEY (SCHED_NAME,ENTRY_ID))
ENGINE=InnoDB COMMENT='正在执行的触发器表';

CREATE TABLE ${_prefix}job_SCHEDULER_STATE (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	INSTANCE_NAME VARCHAR(200) NOT NULL COMMENT '实例名称',
	LAST_CHECKIN_TIME BIGINT(13) NOT NULL COMMENT '检查时间',
	CHECKIN_INTERVAL BIGINT(13) NOT NULL COMMENT '检查间隔',
	PRIMARY KEY (SCHED_NAME,INSTANCE_NAME))
ENGINE=InnoDB COMMENT='状态检查表';

CREATE TABLE ${_prefix}job_LOCKS (
	SCHED_NAME VARCHAR(120) NOT NULL COMMENT '计划名称',
	LOCK_NAME VARCHAR(40) NOT NULL COMMENT '锁定名称',
	PRIMARY KEY (SCHED_NAME,LOCK_NAME))
ENGINE=InnoDB COMMENT='状态锁表';

CREATE INDEX IDX_QRTZ_J_REQ_RECOVERY ON ${_prefix}job_JOB_DETAILS(SCHED_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_QRTZ_J_GRP ON ${_prefix}job_JOB_DETAILS(SCHED_NAME,JOB_GROUP);

CREATE INDEX IDX_QRTZ_T_J ON ${_prefix}job_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_T_JG ON ${_prefix}job_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_T_C ON ${_prefix}job_TRIGGERS(SCHED_NAME,CALENDAR_NAME);
CREATE INDEX IDX_QRTZ_T_G ON ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_QRTZ_T_STATE ON ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_N_STATE ON ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_N_G_STATE ON ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_GROUP,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_NEXT_FIRE_TIME ON ${_prefix}job_TRIGGERS(SCHED_NAME,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_ST ON ${_prefix}job_TRIGGERS(SCHED_NAME,TRIGGER_STATE,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_MISFIRE ON ${_prefix}job_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME);
CREATE INDEX IDX_QRTZ_T_NFT_ST_MISFIRE ON ${_prefix}job_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_STATE);
CREATE INDEX IDX_QRTZ_T_NFT_ST_MISFIRE_GRP ON ${_prefix}job_TRIGGERS(SCHED_NAME,MISFIRE_INSTR,NEXT_FIRE_TIME,TRIGGER_GROUP,TRIGGER_STATE);

CREATE INDEX IDX_QRTZ_FT_TRIG_INST_NAME ON ${_prefix}job_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME);
CREATE INDEX IDX_QRTZ_FT_INST_JOB_REQ_RCVRY ON ${_prefix}job_FIRED_TRIGGERS(SCHED_NAME,INSTANCE_NAME,REQUESTS_RECOVERY);
CREATE INDEX IDX_QRTZ_FT_J_G ON ${_prefix}job_FIRED_TRIGGERS(SCHED_NAME,JOB_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_FT_JG ON ${_prefix}job_FIRED_TRIGGERS(SCHED_NAME,JOB_GROUP);
CREATE INDEX IDX_QRTZ_FT_T_G ON ${_prefix}job_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP);
CREATE INDEX IDX_QRTZ_FT_TG ON ${_prefix}job_FIRED_TRIGGERS(SCHED_NAME,TRIGGER_GROUP);

commit; 
