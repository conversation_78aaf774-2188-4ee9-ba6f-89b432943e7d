
#=========== 作业监控 ===========

名称及组名已经存在=名称およびグループ名はすでに存在する
目标字符串=ターゲット文字列
调用目标字符串中，没有这个类：=呼び出し先文字列には,このクラスはない:
调用目标字符串中，没有这个方法：=呼び出し先の文字列には,この方法はない:
调用目标字符串不能为空！=呼び出し先文字列は空ではない!
调用目标字符串中，找不到该Bean名或Class名：=呼び出し先の文字列には,そのBean名やClass名が見つからない:
最近\ {0}\ 次运行时间=最近の\{0}\回の実行時間
Cron表达式正确。=Cron式は正しい。
Cron表达式错误：=Cron式のエラー:
保存任务成功=保存任務に成功
启动定时器失败=タイマー起動に失敗
启动定时器成功=タイマー起動成功
停止定时器失败=タイマー停止失敗
停止定时器成功=タイマー停止成功
暂停运行成功=一時停止に成功
恢复运行成功=運行再開に成功した
运行一次成功=1回成功
删除任务成功=削除タスク成功
添加任务失败=タスク追加失敗
暂停运行失败=一時停止に失敗
恢复运行失败=再開に失敗
运行一次失败=一度の失敗
删除任务失败=削除タスク失敗

新增作业调度=ジョブ・スケジューラの追加
编辑作业调度=編集ジョブスケジュール
任务名称=タスク名
任务分组=タスクグループ
任务描述=タスク記述
调用目标串=オブジェクト列を呼び出す
Cron执行表达式=Cronは式を実行する
计划错误策略=計画を誤る策略。
是否并发执行=並行実行の有無
请填写Cron表达式！=Cron式を記入してください!
作业调度=ジョブスケジューリング
定时器=タイマー
运行中=運行中
停止中=停止中
运行定时器=実行タイマー
启动定时器=起動タイマー
停止定时器=タイマーを止める
刷新页面=ページを刷新する
作业调度日志=ジョブスケジュール履歴
调度日志=スケジュールログ
调用目标字符串=呼び出し先文字列
周期表达式=周期表現
上次运行时间=前回の運行時間
下次运行时间=次回の運行時間
恢复作业=復旧作業
确认要恢复该作业吗？=この作業を復旧することを確認しますか?
暂停作业=作業を一時停止する
确认要暂停该作业吗？=確認暂停同作業をするの?
立即运行一次=直ちに一回運行
确认要立即运行一次该作业吗？=直ちに確認運行に一度この宿题?
编辑作业=編集作業
删除作业=削除作
确认要删除该作业吗？=確認を削除しなければならないこの宿题?

监控调度日志=監視スケジュールログ
页面已刷新。=ページが更新された。
日志类型=ログ型
日志事件=ログ事件
日志信息=ログ情報
发生时间=発生時刻
异常信息=異常情報
调度日志查询=ログ検索
任务组名=タスクグループ名
是否异常=異常かどうか
查看日志=ログを見る

错过计划等待本次计划完成后立即执行一次=計画を見逃せば、今回の計画が完了すればすぐに実行される
本次执行时间根据上次结束时间重新计算（时间间隔方式）=今回の実行時間を前回の終了時間から再計算する(時間間隔方式)。

默认=黙認
系统=システム

#=========== 作业监控 Cron ===========

Cron表达式生成器=Cron表現生成器
刷新=更新
每秒=秒速
允许的通配符=許されたワイルドカード
周期从=周期は
秒=秒
从=から
秒开始,每=秒が始まるごとに
秒执行一次=秒に1回
指定=指定
分钟=分
分钟开始,每=分から、毎
分钟执行一次=分に1回
小时=時間
小时开始,每=時間が始まるごとに
小时执行一次=1時間に1回
上午=午前
下午=午後
日=日
不指定=指定しない
日开始,每=日が始まる
天执行一次=日に一度
每月=毎月
号最近的那个工作日=日の一番近い勤務日です
本月最后一天=今月最後の日
月=月
月执行一次=月に1回
周=周
周期\ 从星期=周期は曜日
星期=曜日
的,\ 第=的,第
本月最后一个星期=今月最後の一週間
周日=日曜日
周一=月曜日
周二=火曜日
周三=水曜
周四=木曜日
周五=金曜日
周六=土曜日に
非必填=非必填
每年=毎年
周期\ 从=周期は
表达式=数式
年=年
表达式字段=エクスプレションフィールド
Cron\ 表达式=Cron
表达式反解析到界面=エクスプレションと反解析にインタフェース

