
#=========== 作业监控 ===========

名称及组名已经存在=The name and group name already exist
目标字符串=Target string
调用目标字符串中，没有这个类：=Call target string without this class:
调用目标字符串中，没有这个方法：=Call target string without this method:
调用目标字符串不能为空！=Call target string cannot be empty!
调用目标字符串中，找不到该Bean名或Class名：=Bean name or Class name cannot be found in the invocation target string:
最近\ {0}\ 次运行时间=Last \ {0}\ run time
Cron表达式正确。=Cron expression is correct.
Cron表达式错误：=Cron expression error:
保存任务成功=Save task successful
启动定时器失败=Start timer failed
启动定时器成功=Start timer successfully
停止定时器失败=Stop timer failed
停止定时器成功=Stop timer successful
暂停运行成功=Suspended run successful
恢复运行成功=Resume run successful
运行一次成功=Run one successful
删除任务成功=Delete task successful
添加任务失败=Add task failed
暂停运行失败=Suspension failure
恢复运行失败=Recovery operation failed
运行一次失败=Run one failure
删除任务失败=Delete task failed

新增作业调度=New job scheduling
编辑作业调度=Edit job scheduling
任务名称=Task name
任务分组=Task group
任务描述=Task description
调用目标串=Call target string
Cron执行表达式=Cron expressions
计划错误策略=Planning error strategy
是否并发执行=Concurrent execution
请填写Cron表达式！=Please fill in the Cron expression!
作业调度=Job scheduling
定时器=The timer
运行中=In the operation of the
停止中=Stop in the
运行定时器=Running timer
启动定时器=Start timer
停止定时器=Stop timer
刷新页面=Refresh the page
作业调度日志=Job scheduling log
调度日志=Operation log
调用目标字符串=Call target string
周期表达式=Periodic expression
上次运行时间=Last run time
下次运行时间=Next run time
恢复作业=Restore operation
确认要恢复该作业吗？=Are you sure you want to resume the job?
暂停作业=Suspend operations
确认要暂停该作业吗？=Are you sure you want to suspend the job?
立即运行一次=Run once now
确认要立即运行一次该作业吗？=Confirm that you want to run the job once?
编辑作业=Edit operations
删除作业=Delete operation
确认要删除该作业吗？=Are you sure you want to delete the job?

监控调度日志=Monitor scheduling log
页面已刷新。=The page has been refreshed.
日志类型=Log type
日志事件=Log event
日志信息=Log information
发生时间=Time of occurrence
异常信息=Exception information
调度日志查询=Scheduling log query
任务组名=The task group name
是否异常=Exception
查看日志=View log

错过计划等待本次计划完成后立即执行一次=Miss the plan to wait for the completion of the plan immediately executed once
本次执行时间根据上次结束时间重新计算（时间间隔方式）=This execution time is recalculated according to the last end time (time interval)

默认=Default
系统=System

#=========== 作业监控 Cron ===========

Cron表达式生成器=Cron generator
刷新=The refresh
每秒=second
允许的通配符=Allowed wildcards
周期从=Cycle from
秒=seconds
从=From
秒开始,每=seconds start, per
秒执行一次=execute once per second
指定=the specified
分钟=minutes
分钟开始,每=minutes start each
分钟执行一次=execute once per minute
小时=hours
小时开始,每=Hours start every hour
小时执行一次=Hourly execution
上午=AM
下午=PM
日=day
不指定=do not specify
日开始,每=every day begins
天执行一次=daily execution
每月=Monthly
号最近的那个工作日=on the nearest working day
本月最后一天=the last day of the month
月=month
月执行一次=monthly execution
周=weeks
周期\ 从星期=cycle from week
星期=week
的,\ 第=the first
本月最后一个星期=the last week of the month
周日=Sunday
周一=Monday
周二=Tuesday
周三=Wednesday
周四=Thursday
周五=Friday
周六=Saturday
非必填=Not required
每年=Annually
周期\ 从=Cycle from
表达式=expression
年=years
表达式字段=Field
Cron\ 表达式=Cron
表达式反解析到界面=Reverse mapping
