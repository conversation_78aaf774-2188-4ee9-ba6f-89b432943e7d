
# =========== 缓存监控  ===========

缓存列表=キャッシュリスト
键名列表=キーリスト
缓存内容=キャッシュ内容
清理全部缓存=キャッシュを全部クリアする
清理全部缓存，包括属性文件的配置=プロパティファイルの配置を含めてキャッシュ全体をクリアする
缓存名称=キャッシュ名
缓存键名=キャッシュキー人
清理缓存=キャッシュを消去する
确认要清理该缓存吗？=キャッシュをクリアすることを確認しますか?
缓存列表刷新完成=キャッシュリスト更新完了

# =========== 服务器监控  ===========

属性=属性
值=値
核心数=核心数
最大功率=最大出力
实时频率=リアルタイム周波数
使用率=使用率
内存=メモリー
总内存=総メモリ
剩余内存=残りメモリ
已用内存=すでに用メモリー
堆/非堆=ヒープ/非ヒープ
执行垃圾回收任务=ごみ回収任務を遂行する
堆=积み
非堆=非炉
初始大小=初期の大きさ
最大内存=最大メモリ
已用内存=使用済みメモリ
可用内存=利用可能メモリ
服务器信息=サーバー情報
服务器名称=サーバー名称
操作系统=os
版本=バージョン
服务器IP=サーバIP
系统架构=システムアーキテクチャ
Java虚拟机信息=java仮想マシン情報
Java名称=java名称
Java版本=Java版
供应商=サプライヤー
启动时间=起動時間
运行时长=運行时长
安装路径=実装経路
启动参数=稼動パラメータ
平台参数=プラットフォームのパラメータ
当前工作路径=当面の仕事の経路
日志存放路径=日誌熟成経路
上传文件路径=文書を掲載し経路
磁盘状态=ディスク状態
盘符名称=盤符名称
盘符路径=盤符経路
文件系统=ファイルシステム
盘符类型=盤符タイプ
总大小=総の大きさ
可用大小=利用可能な大きさ
已用大小=すでに用の大きさ
已用百分比=すでに用%

执行垃圾回收任务=ごみ回収任務を遂行する
发起执行垃圾回收任务成功=成功にゴミ回収の任務を遂行する
