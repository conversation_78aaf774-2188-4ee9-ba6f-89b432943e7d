
# =========== 缓存监控  ===========

缓存列表=Cache list
键名列表=Key list
缓存内容=Cache content
清理全部缓存=Clear all caches
清理全部缓存，包括属性文件的配置=Clear all caches, including configuration of properties files
缓存名称=Cache name
缓存键名=Cache key
清理缓存=Clear cache
确认要清理该缓存吗？=Are you sure you want to clear up the cache?
缓存列表刷新完成=The cache list refresh is complete

# =========== 服务器监控  ===========

属性=Property
值=Value
核心数=Core number
最大功率=Maximum power
实时频率=Current frequency
核心个数=CPU cores
核心频率=CPU frequency
系统/用户=System/User
使用率=Used rate
内存=Memory
总内存=The total memory
剩余内存=Remaining memory
已用内存=Used memory
堆/非堆=Heap/non-heap
执行垃圾回收任务=Perform garbage collection task
堆=Heap
非堆=Non-heap
初始大小=Initial size
最大内存=Maximum memory
已用内存=Used memory
可用内存=Available memory
服务器信息=Server information
服务器名称=Server name
操作系统=Operating system
版本=Version
服务器IP=Server IP
系统架构=System architecture
Java虚拟机信息=JVM information
Java名称=Java name
Java版本=Java version
供应商=Vendor
启动时间=Start time
运行时长=Running time
安装路径=Install path
启动参数=Start parameter
平台参数=Platform parameter
当前工作路径=Current working path
日志存放路径=Log storage path
上传文件路径=Upload file path
磁盘状态=Disk status
盘符名称=Disk name
盘符路径=Disk path
文件系统=File system
盘符类型=Disk type
总大小=Total size
可用大小=Available size
已用大小=Used size
已用百分比=Percent used

执行垃圾回收任务=Perform garbage collection tasks
发起执行垃圾回收任务成功=Initiated garbage collection task successfully
