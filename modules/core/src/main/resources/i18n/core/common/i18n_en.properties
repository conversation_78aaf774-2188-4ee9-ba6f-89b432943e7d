
# =========== 登录登出相关 ===========

sys.login.notLongIn=Your login information has expired. Please log in again.
sys.login.success=Login successful!
sys.login.getInfo=Get info successful!
sys.login.failure=Account or password error, please try again.
sys.login.error=Sorry, system error. Please try again later.
sys.logout.success=Logout successful!

# =========== 账号登录相关 ===========

sys.login.accountIsBlank=Login account cannot be empty.
sys.login.validCodeError=Login verification code error.
sys.login.accountDisabled=This Account has disabled.
sys.login.accountFreezed=This Account has freezed.
sys.login.accountAudited=This Account has audited.
sys.login.accountInvalid=This Account has invalid.
sys.login.tickOutMessage=The account has been removed by the administrator. Please login again.
sys.login.multiAddrMessage=The account has been logged in elsewhere. Please login again.
sys.login.failedNumLock=Login failed, try too many times, the account has been locked, please {0} in minutes after retry.

# =========== 用户管理相关 ===========

sys.user.loginCodeExists=Login account already exists.
sys.user.userCodeNotExists=UserCode does not exist.
sys.user.userNameNotBlank=UserName does not empty.
sys.user.infoSaveSuccess=User info save success.

# =========== 用户密码安全策略 ===========

sys.user.oldPasswordError=Old password error, please retype.
sys.user.confirmPasswrodError=The new password is different from the confirm password. please retype.
sys.user.passwordModifySuccess=Change password success
sys.user.passwordModifyNotRepeat=The new password cannot be the same as the previous {0}.
sys.user.passwordModifySecurityLevel=Password update failed because you set the password to weak password!
sys.user.initPasswordModifyTip=Your password is the initial password, please change the password!
sys.user.passwordModifyTip=Your password has not been modified for {0} days, please change the password!
sys.user.passwordError=Password error, please retype.
sys.user.pwdQuestionModifySuccess=The security problem modified success.
sys.user.pwdQuestionAnswerError=The questions and answers are incorrect.

# =========== 错误页面相关 ===========

sys.error.400.title=Request parameter error
sys.error.400.message=Request parameter error, server cannot parse.
sys.error.403.title=Insufficient permissions
sys.error.403.message=Insufficient permissions!
sys.error.403.message.p1=I am sorry that you do not permissions. Please contact the administrator.
sys.error.404.title=Page doesn't exist
sys.error.404.message=Visited page does not exist!
sys.error.404.message.p1=This error may be caused by:
sys.error.404.message.p2=Address input error, link has expired.
sys.error.404.message.p3=The address you visit is:
sys.error.404.message.p4=Please contact the administrator.
sys.error.500.title=Internal system error
sys.error.500.message=The page you visited error!
sys.error.500.message.p1=Sorry, there is a problem on the page you visited, please contact the administrator in time!
sys.error.returnButton=Previous page

# =========== 文件上传相关 ===========

sys.file.uploadFileIsEmpty=No files to upload!
sys.file.uploadValidNotBlank=File md5 and file name cannot be empty!
sys.file.uploadValidImage=Can only upload images
sys.file.uploadValidVideo=Can only upload video
sys.file.uploadValidFile=Can only upload document
sys.file.uploadValidAll=File format is not allowed
sys.file.uploadValidSize=Size cannot exceed {0}
sys.file.uploadValidContent=File content format not allowed!
sys.file.uploadSuccessSeconds=Seconds upload success , time {0}
sys.file.uploadSuccess=Upload success {0}
sys.file.downloadFileNotExist=File lost or non-existent!
sys.file.chunkUploading=Uploading {0}/{1}

# =========== 后端验证提示提醒消息 ===========

web.validator.id=Id maximum of 64 characters and can contain letters, digits, _, -, /, #, and chinese
web.validator.user.loginCode=LoginCode 4 to 20 characters and can contain only letters, digits, _, and chinese  
