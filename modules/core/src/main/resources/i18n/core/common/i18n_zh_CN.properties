
# =========== 登录登出相关 ===========

sys.login.notLongIn=您的登录信息已过期，请重新登录。
sys.login.success=登录成功！
sys.login.getInfo=获取信息成功！
sys.login.failure=账号或密码错误，请重试。
sys.login.error=对不起，系统遇见了点问题，请稍候再试！
sys.logout.success=退出成功！

# =========== 账号登录相关 ===========

sys.login.accountIsBlank=登录账号不能为空。
sys.login.validCodeError=登录验证码错误，请重试。
sys.login.accountDisabled=该帐号已停用。
sys.login.accountFreezed=该帐号已冻结。
sys.login.accountAudited=该帐号等待审核。
sys.login.accountInvalid=该帐号无效状态。
sys.login.tickOutMessage=账号已被管理员移出在线，请重新登录。
sys.login.multiAddrMessage=账号已在其它地方登录，请重新登录。
sys.login.failedNumLock=登录失败，尝试次数过多，账号已锁定，请 {0} 分钟后重试.

# =========== 用户管理相关 ===========

sys.user.loginCodeExists=登录账号已存在
sys.user.userCodeNotExists=用户编码不存在
sys.user.userNameNotBlank=用户昵称不能为空
sys.user.infoSaveSuccess=用户信息保存成功

# =========== 用户密码安全策略 ===========

sys.user.oldPasswordError=旧密码错误，请重新输入
sys.user.confirmPasswrodError=新密码与确认新密码不同，请重新输入
sys.user.passwordModifySuccess=修改密码成功
sys.user.passwordModifyNotRepeat=新密码不能与前 {0} 次，设置的密码相同
sys.user.passwordModifySecurityLevel=密码更新失败，因为你设置的密码为弱密码！
sys.user.initPasswordModifyTip=您的密码还是初始密码，请修改密码！
sys.user.passwordModifyTip=您的密码已经 {0} 天未修改了，请修改密码！
sys.user.passwordError=登录密码错误，请重新输入
sys.user.pwdQuestionModifySuccess=密保问题修改成功
sys.user.pwdQuestionAnswerError=密保问题与答案不正确

# =========== 错误页面相关 ===========

sys.error.400.title=请求参数错误
sys.error.400.message=请求参数错误，服务器无法解析。
sys.error.403.title=操作权限不足
sys.error.403.message=您的操作权限不足！
sys.error.403.message.p1=很抱歉，您没有权限访问此页面，若有疑问请联系管理员。
sys.error.404.title=页面不存在
sys.error.404.message=您访问的页面不存在！
sys.error.404.message.p1=可能是如下原因引起的这个错误：
sys.error.404.message.p2=地址输入错误，链接已经失效过期.
sys.error.404.message.p3=您访问的地址为：
sys.error.404.message.p4=若有疑问请联系管理员.
sys.error.500.title=系统内部错误
sys.error.500.message=您访问的页面出错啦！
sys.error.500.message.p1=对不起，你访问的页面出现了一点问题，请及时联系管理员解决！
sys.error.returnButton=返回上一页

# =========== 文件上传相关 ===========

sys.file.uploadFileIsEmpty=服务器上没有这个文件！
sys.file.uploadValidNotBlank=文件校验码和文件名不能为空！
sys.file.uploadValidImage=只能上传图片
sys.file.uploadValidVideo=只能上传视频
sys.file.uploadValidFile=只能上传文档
sys.file.uploadValidAll=文件格式不允许
sys.file.uploadValidSize=大小不能超过{0}
sys.file.uploadValidContent=文件内容格式不允许！
sys.file.uploadSuccessSeconds=秒传成功,用时{0}
sys.file.uploadSuccess=上传成功,用时{0}
sys.file.downloadFileNotExist=文件已丢失或不存在！
sys.file.chunkUploading=正在上传 {0}/{1}

# =========== 后端验证提示提醒消息 ===========

web.validator.id=编码长度不能超过 64 个字符，并且只能包含字母、数字、下划线、减号、斜杠、井号或中文
web.validator.user.loginCode=登录账号长度应为 4 到 20 个字符，并且只能包含字母、数字、下划线或中文
