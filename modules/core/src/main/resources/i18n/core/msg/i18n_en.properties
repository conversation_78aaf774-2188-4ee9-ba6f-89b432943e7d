
# =========== 消息提醒 ===========

消息=Message
你有\ {0}\ 条消息=You have {0} messages
查看全部消息=See All Messages
查看消息=See message
系统消息=System message
您有\ {0}\ 条新消息，由于消息太多，这里为您合并，请点击查看按钮看详情。=You have {0} new message, because there are too many messages, for you merge, click View see details.

# =========== 消息提醒列表 ===========

未读消息=Unread message
已读消息=Read message

发送者=The sender
发送时间=Sending time
延迟推送=Delay delivery

全部标记为已读=All marked as read
全部标记已读成功！=All marks read successfully!
是否要将全部未读信息标记为已读？=Do you want to mark all unread information as read?

# =========== 消息推送管理 ===========

消息推送=Message Push
未完成消息=Uncompleted message
已完成消息=Completed message

消息类型=Message type
消息标题=Message title
消息内容=Message content
业务主键=Business Key
业务类型=Business Type
接受者信息=Receiver information
接受者账号=Receiver account 
接受者姓名=Receiver name
接受者编码=Receiver code
发送者信息=Sender message
发送者编码=Sender code
发送者姓名=Sender name
发送时间=Sending time
合并推送=Merge push
计划推送时间=Plan push time
推送状态信息=Push status information
推送次数=Push status
推送返回结果码=Push return result code
推送返回消息编号=Push return message code
推送返回的内容信息=Push return content
推送状态=Push status
推送时间=Push time
读取状态信息=Read status information
读取状态=Read status
读取时间=Read time

删除消息推送成功！=Delete message push successfully!


# =========== 消息模板管理 ===========

消息模板管理=Message template
新增消息模板=New message template
编辑消息模板=Edit message template
删除消息模板=Delete message template

模板类型=Template type
模板键值=Template key
模板名称=Template name
归属模块=Module 
模板内容=Template content
语法格式=Content syntax format

# =========== 消息相关字典 ===========

短信=SMS
邮件=Email
微信=WeChat

待推送=Wait push
成功=Success
失败=Failure

未送达=Undelivered
已读=Read
未读=Unread
