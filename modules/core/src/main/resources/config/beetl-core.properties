#######默认配置（/org/beetl/core/beetl-default.properties）
ENGINE=org.beetl.core.engine.FastRuntimeEngine
DELIMITER_PLACEHOLDER_START=${
DELIMITER_PLACEHOLDER_END=}
DELIMITER_PLACEHOLDER_START2="#{
DELIMITER_PLACEHOLDER_END2=}"
DELIMITER_STATEMENT_START=<%
DELIMITER_STATEMENT_END=%>
DELIMITER_STATEMENT_START2=//#
DELIMITER_STATEMENT_END2=
DIRECT_BYTE_OUTPUT = FALSE
HTML_TAG_SUPPORT = true
HTML_TAG_FLAG = #
HTML_TAG_BINDING_ATTRIBUTE = var,export
HTML_TAG_ATTR_CONVERT=com.jeesite.common.beetl.text.BlankAttributeNameConvert
NATIVE_CALL = TRUE
TEMPLATE_CHARSET = UTF-8
ERROR_HANDLER = com.jeesite.common.beetl.handler.LoggerErrorHandler
NATIVE_SECUARTY_MANAGER = org.beetl.core.DefaultNativeSecurityManager
RESOURCE_LOADER = org.beetl.core.resource.ClasspathResourceLoader
MVC_STRICT = FALSE

#导入调用静态方法类
IMPORT_PACKAGE=

##导入JeeSite调用静态方法类（自动合并IMPORT_PACKAGE设置）
IMPORT_PACKAGE_core=\
	com.jeesite.common.config.;\
	com.jeesite.common.codec.;\
	com.jeesite.common.collect.;\
	com.jeesite.common.idgen.;\
	com.jeesite.common.lang.;\
	com.jeesite.common.mapper.;\
	com.jeesite.common.reflect.;\
	com.jeesite.common.security.;\
	com.jeesite.common.web.http.;\
	com.jeesite.modules.sys.utils.;\
	com.jeesite.common.entity.;\
	com.jeesite.common.service.;\
	com.jeesite.common.utils.;\

### 资源配置，resource后的属性只限于特定ResourceLoader ####
#classpath 跟路径，与框架集成的时候，此配置会被框架代码覆盖而不能生效
RESOURCE.root = views
#是否检测文件变化
RESOURCE.autoCheck = TRUE
#自定义脚本方法文件位置
RESOURCE.functionRoot = functions
#自定义脚本方法文件的后缀
RESOURCE.functionSuffix = html
#自定义标签文件位置
RESOURCE.tagRoot = htmltags
#自定义标签文件后缀
RESOURCE.tagSuffix = html

#如果采用beetl集成的web应用，可以在渲染模板前调用如下类,此类必须实现WebRenderExt接口
WEBAPP_EXT = 

#允许html function or Tag 使用特殊的定界符，因为function或者tag通常有大量beetl语句
FUNCTION_TAG_LIMITER=

#####  扩展，也可以通过特定框架注册##############
## 内置的方法
FN.date = org.beetl.ext.fn.DateFunction
FN.nvl = org.beetl.ext.fn.NVLFunction
FN.debug = org.beetl.ext.fn.DebugFunction
#兼容以前版本，用has代替
FN.exist = org.beetl.ext.fn.CheckExistFunction
FN.has = org.beetl.ext.fn.CheckExistFunction
FN.printf = org.beetl.ext.fn.Printf
FN.decode = org.beetl.ext.fn.DecodeFunction
FN.assert = org.beetl.ext.fn.AssertFunction
FN.print = org.beetl.ext.fn.Print
FN.println = org.beetl.ext.fn.Println
FN.printFile = org.beetl.ext.fn.PrintFile
FN.trunc = org.beetl.ext.fn.TruncFunction
FN.trim = org.beetl.ext.fn.TruncFunction2
#兼容以前版本 empty，用isEmpty代替
FN.empty = org.beetl.ext.fn.EmptyFunction
FN.qmark = org.beetl.ext.fn.QuestionMark
FN.isEmpty = org.beetl.ext.fn.EmptyExpressionFunction
FN.isNotEmpty = org.beetl.ext.fn.IsNotEmptyExpressionFunction
FN.parseInt = org.beetl.ext.fn.ParseInt
FN.parseLong = org.beetl.ext.fn.ParseLong
FN.parseDouble= org.beetl.ext.fn.ParseDouble
FN.range = org.beetl.ext.fn.Range
FN.flush = org.beetl.ext.fn.Flush
FN.json = org.beetl.ext.fn.Json
FN.pageCtx = org.beetl.ext.fn.PageContextFunction
FN.type.new=org.beetl.ext.fn.TypeNewFunction
FN.type.name=org.beetl.ext.fn.TypeNameFunction
FN.type.fullName=com.jeesite.common.beetl.ext.fn.TypeFullNameFunction
FN.global=org.beetl.ext.fn.DynamicGlobalValueFunction
FN.allGlobal=org.beetl.ext.fn.AllGlobaAsJsonlFunction
FN.hasAttribute=org.beetl.ext.fn.HasAttributeFunction
FN.env=org.beetl.ext.fn.EnvFunction
FN.parentTag=org.beetl.ext.fn.ParentTagFunction

##JeeSite自定义函数
FN.isBlank = com.jeesite.common.beetl.ext.fn.IsBlank
FN.isNotBlank = com.jeesite.common.beetl.ext.fn.IsNotBlank
FN.toBoolean = com.jeesite.common.beetl.ext.fn.ToBoolean
FN.toInteger = com.jeesite.common.beetl.ext.fn.ToInteger
FN.toJson = com.jeesite.common.beetl.ext.fn.ToJson
FN.fromJson = com.jeesite.common.beetl.ext.fn.FromJson
FN.hasPermi = com.jeesite.common.beetl.ext.fn.HasPermi
FN.hasRole = com.jeesite.common.beetl.ext.fn.HasRole
FN.cookie = com.jeesite.common.beetl.ext.fn.Cookie
FN.lang = com.jeesite.common.beetl.ext.fn.Lang
FN.text = com.jeesite.common.beetl.ext.fn.Text
FN.user = com.jeesite.common.beetl.ext.fn.User

##内置的功能包
FNP.strutil = org.beetl.ext.fn.StringUtil
FNP.reg = org.beetl.ext.fn.RegxFunctionUtil
FNP.array = org.beetl.ext.fn.ArrayUtil
FNP.dict = com.jeesite.common.beetl.ext.fn.DictUtil

##内置的格式化函数
FT.dateFormat = org.beetl.ext.format.DateFormat
FT.numberFormat = com.jeesite.common.beetl.ext.format.NumberFormat
FT.xss = com.jeesite.common.beetl.ext.format.XssFormat

##内置的默认格式化函数
FTC.java.util.Date = org.beetl.ext.format.DateFormat
FTC.java.sql.Date = org.beetl.ext.format.DateFormat
FTC.java.sql.Time = org.beetl.ext.format.DateFormat
FTC.java.sql.Timestamp = org.beetl.ext.format.DateFormat
FTC.java.lang.Short = com.jeesite.common.beetl.ext.format.NumberFormat
FTC.java.lang.Long = com.jeesite.common.beetl.ext.format.NumberFormat
FTC.java.lang.Integer = com.jeesite.common.beetl.ext.format.NumberFormat
FTC.java.lang.Float = com.jeesite.common.beetl.ext.format.NumberFormat
FTC.java.lang.Double = com.jeesite.common.beetl.ext.format.NumberFormat
FTC.java.math.BigInteger = com.jeesite.common.beetl.ext.format.NumberFormat
FTC.java.math.BigDecimal = com.jeesite.common.beetl.ext.format.NumberFormat
FTC.java.util.concurrent.atomic.AtomicLong = com.jeesite.common.beetl.ext.format.NumberFormat
FTC.java.util.concurrent.atomic.AtomicInteger = com.jeesite.common.beetl.ext.format.NumberFormat

##虚拟属性 无
## 标签类
TAG.include= org.beetl.ext.tag.IncludeTag
TAG.includeFileTemplate= org.beetl.ext.tag.IncludeTag
TAG.incdlueJSP= org.beetl.ext.jsp.IncludeJSPTag
TAG.layout= org.beetl.ext.tag.LayoutTag
TAG.delete= org.beetl.ext.tag.DeleteTag
#TAG.htmltag= org.beetl.ext.tag.HTMLTagSupportWrapper
#TAG.htmltagvar= org.beetl.ext.tag.HTMLTagVarBindingWrapper
TAG.htmltag= com.jeesite.common.beetl.ext.tag.HTMLTag
TAG.htmltagvar= com.jeesite.common.beetl.ext.tag.HTMLTagVar
TAG.htmltagexport= com.jeesite.common.beetl.ext.tag.HTMLTagVar
TAG.cache= org.beetl.ext.tag.cache.CacheTag
# 一个html风格的include和layout <#html:include file="">
TAG.html.include= org.beetl.ext.tag.html.IncludeResourceHtmlTag
TAG.html.layout= org.beetl.ext.tag.html.LayoutResourceHtmlTag
TAG.html.set= org.beetl.ext.tag.html.SetHtmlTag
TAG.html.if= org.beetl.ext.tag.html.IfHtmlTag
TAG.html.foreach= org.beetl.ext.tag.html.ForeachHtmlTag
#TAG.includeUrl=org.beetl.ext.tag.IncludeUrlTag
