# transactions.properties not found - looking for jta.properties in classpath...
com.atomikos.icatch.service=com.atomikos.icatch.standalone.UserTransactionServiceFactory
#com.atomikos.icatch.log_base_dir=./transaction-logs/
com.atomikos.icatch.max_timeout=-1
com.atomikos.icatch.max_actives=-1
com.atomikos.icatch.enable_logging=false
com.atomikos.icatch.registered=true

# 注意：如果报  oracle.jdbc.xa.OracleXAResource.recover 错误，则需要授权如下：
# grant select on sys.dba_pending_transactions to jeesite;
# grant select on sys.pending_trans$ to jeesite;
# grant select on sys.dba_2pc_pending to jeesite;
# grant execute on sys.dbms_system to jeesite;