<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.sys.dao.EmpUserDao">

	<!-- 结果集映射
	<resultMap id="empUserResult" type="EmpUser">
		<result column="mobile" property="mobile" javaType="java.lang.String"
			 typeHandler="com.jeesite.common.mybatis.type.AesTypeHandler"/>
	</resultMap> -->

    <!-- 查询数据
	<select id="findList" resultMap="empUserResult">  -->
	<select id="findList" resultType="EmpUser">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<if test="roleCode != null and roleCode != ''">
			JOIN ${_prefix}sys_user_role ur2 ON ur2.user_code = a.user_code
		</if>
		<if test="employee.postCode != null and employee.postCode != ''">
			JOIN ${_prefix}sys_employee_post ep ON e.emp_code = ep.emp_code
		</if>
		<where>
			${sqlMap.where.toSql()}
			<if test="roleCode != null and roleCode != ''">
				AND ur2.role_code = #{roleCode}
			</if>
			<if test="employee.postCode != null and employee.postCode != ''">
				AND (
					ep.post_code = #{employee.postCode}
					OR EXISTS (
						SELECT 1 FROM ${_prefix}sys_employee_office
						WHERE emp_code = e.emp_code
							AND post_code = #{employee.postCode}
					)
				)
			</if>
			<!-- 附属部门查询，根据业务需要添加查询条件
			<if test="employee.office.officeCode != null and employee.office.officeCode != ''">
				OR EXISTS (
					SELECT 1 FROM ${_prefix}sys_employee_office
					WHERE emp_code = e.emp_code
						AND office_code = #{employee.office.officeCode}
				)
			</if> -->
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select>
	
	<sql id="userColumns">
		a.user_code as "userCode",
		a.user_name as "userName"
	</sql>
	
	<!-- 查询全部用户，仅返回基本信息  -->
	<select id="findUserList" resultType="EmpUser">
		SELECT 
		    <include refid="userColumns"/>
		FROM ${_prefix}sys_user a
		WHERE a.status = #{STATUS_NORMAL}
			AND a.user_type = #{USER_TYPE_EMPLOYEE}
			<if test="global.useCorpModel">
		    	AND a.corp_code = #{corpCode}
		    </if>
	</select>
	
	<!-- 根据部门编码查询用户，仅返回基本信息  -->
	<select id="findUserListByOfficeCodes" resultType="EmpUser">
		SELECT 
		    <include refid="userColumns"/>
		FROM ${_prefix}sys_user a
		JOIN ${_prefix}sys_employee e ON e.emp_code = a.ref_code
		JOIN ${_prefix}sys_office o ON o.office_code = e.office_code
		WHERE a.status = #{STATUS_NORMAL}
			AND a.user_type = #{USER_TYPE_EMPLOYEE}
			<if test="global.useCorpModel">
		    	AND a.corp_code = #{corpCode}
		    </if>
		    AND e.status = #{STATUS_NORMAL}
		    AND o.status = #{STATUS_NORMAL}
			AND o.office_code IN
			<foreach item="code" index="index" collection="codes" open="(" separator="," close=")">
				#{code}
			</foreach>
	</select>
	
	<!-- 根据角色编码查询用户，仅返回基本信息  -->
	<select id="findUserListByRoleCodes" resultType="EmpUser">
		SELECT 
		    <include refid="userColumns"/>
		FROM ${_prefix}sys_user a
		JOIN ${_prefix}sys_user_role ur2 ON ur2.user_code = a.user_code
		JOIN ${_prefix}sys_role r ON r.role_code = ur2.role_code
		WHERE a.status = #{STATUS_NORMAL}
			AND a.user_type = #{USER_TYPE_EMPLOYEE}
			<if test="global.useCorpModel">
		    	AND a.corp_code = #{corpCode}
		    </if>
		    AND r.status = #{STATUS_NORMAL}
			AND r.role_code IN
			<foreach item="code" index="index" collection="codes" open="(" separator="," close=")">
				#{code}
			</foreach>
	</select>
	
	<!-- 根据岗位编码查询用户，仅返回基本信息  -->
	<select id="findUserListByPostCodes" resultType="EmpUser">
		SELECT 
		    <include refid="userColumns"/>
		FROM ${_prefix}sys_user a
		JOIN ${_prefix}sys_employee e ON e.emp_code = a.ref_code
		JOIN ${_prefix}sys_employee_post ep ON ep.emp_code = e.emp_code
		JOIN ${_prefix}sys_post p ON p.post_code = ep.post_code
		WHERE a.status = #{STATUS_NORMAL}
			AND a.user_type = #{USER_TYPE_EMPLOYEE}
			<if test="global.useCorpModel">
		    	AND a.corp_code = #{corpCode}
		    </if>
		    AND e.status = #{STATUS_NORMAL}
		    AND p.status = #{STATUS_NORMAL}
			AND p.post_code IN
			<foreach item="code" index="index" collection="codes" open="(" separator="," close=")">
				#{code}
			</foreach>
	</select>

	<select id="getUsedAreaByUserCode" resultType="Double">
		WITH avg_area_calc AS (
			SELECT
				ore.ID AS REAL_ESTATE_ID,
				ore.TYPE,
				CASE
					WHEN COUNT(oreuu.USER_CODE) > 0 THEN ore.AREA / COUNT(oreuu.USER_CODE)
					ELSE 0
					END AS avg_area
			FROM
				OB_REAL_ESTATE ore
					LEFT JOIN OB_REAL_ESTATE_USED_USER oreuu ON
					ore.ID = oreuu.REAL_ESTATE_ID
			WHERE
				ore.TYPE IN (0, 1, 3)
			GROUP BY
				ore.ID,
				ore.TYPE,
				ore.AREA
		)
		SELECT
			SUM(COALESCE(acc.AVG_AREA, 0))
		FROM
			OB_REAL_ESTATE_USED_USER oreuu
				LEFT JOIN avg_area_calc acc ON
				acc.REAL_ESTATE_ID = oreuu.REAL_ESTATE_ID
		<where>
			<if test="userCode != null and userCode != ''">
				USER_CODE = #{userCode}
			</if>
		</where>
		GROUP BY oreuu.USER_CODE;
	</select>
	
</mapper>