<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.sys.dao.LogDao">
	
	<!-- 查询数据
	<select id="findList" resultType="Log">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->
	
	<!-- 判断数据库类型例子
	<select id="findList" resultType="Log">
		<if test="global.dbName == 'mysql'"></if>
		<if test="global.dbName == 'oracle'"></if>
		<if test="global.dbName == 'mssql'"></if>
		<if test="global.dbName == 'postgresql'"></if>
	</select> -->
	
	<!-- 删除某个日期之前创建的日志 -->
	<delete id="deleteLogBefore">
		DELETE FROM ${_prefix}sys_log
		WHERE create_date &lt; #{createDate}
	</delete>
	
</mapper>