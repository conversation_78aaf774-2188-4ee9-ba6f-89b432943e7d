<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.sys.dao.OfficeDao">

	<!-- 查询数据（支持根据公司编码查询部门） -->
	<select id="findList" resultType="Office">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<if test="companyCode != null and companyCode != ''">
			JOIN ${_prefix}sys_company_office b ON a.office_code = b.office_code
			JOIN ${_prefix}sys_company c ON c.company_code = b.company_code
		</if>
		<where>
			${sqlMap.where.toSql()}
			<if test="companyCode != null and companyCode != ''">
				AND b.company_code = #{companyCode}
			</if>
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select>

	<select id="getOfficeUsedRealEstateCount" resultType="com.jeesite.modules.sys.entity.OfficeUsedRealEstateCount">
		SELECT ore.TYPE, COUNT(*) AS COUNT FROM (SELECT oreuu.REAL_ESTATE_ID, jse.OFFICE_CODE
		FROM OB_REAL_ESTATE_USED_USER oreuu
				 LEFT JOIN JS_SYS_EMPLOYEE jse ON oreuu.USER_CODE = jse.EMP_CODE
		<where>
			<if test="officeCode != null and officeCode != ''">
				jse.OFFICE_CODE = #{officeCode}
			</if>
		</where>
		GROUP BY oreuu.REAL_ESTATE_ID, jse.OFFICE_CODE) t
		   LEFT JOIN OB_REAL_ESTATE ore ON t.REAL_ESTATE_ID = ore.ID
		GROUP BY ore."TYPE";
	</select>
	<select id="getOfficeApprovedArea" resultType="Office">
		WITH avg_area_calc AS (
		SELECT
		ore.ID AS REAL_ESTATE_ID,
		ore.TYPE,
		CASE
		WHEN COUNT(oreuu.USER_CODE) > 0 THEN ore.AREA / COUNT(oreuu.USER_CODE)
		ELSE 0
		END AS avg_area
		FROM OB_REAL_ESTATE ore
		LEFT JOIN OB_REAL_ESTATE_USED_USER oreuu ON ore.ID = oreuu.REAL_ESTATE_ID
		WHERE ore.TYPE IN (0, 1, 3)
		GROUP BY ore.ID, ore.TYPE, ore.AREA
		),
		equipment_ratio AS (
		SELECT COALESCE(EQUIPMENT_ROOM_RATIO / 100, 0) AS eq_ratio
		FROM OB_EQUIPMENT_ROOMS_APPROVED_CONFIG
		LIMIT 1
		)
		SELECT
		t1.OFFICE_CODE,
		t1.serviceRoomApprovedArea,
		COALESCE(t2.SERVICE_OR_OFFICE_ROOM_USED_AREA, 0) * er.eq_ratio AS equipmentRoomApprovedArea,
		COALESCE(t2.SUBSIDIARY_ROOM_USED_AREA, 0) AS ancillaryRoomAreaApprovedArea
		FROM (
		SELECT
		jso.OFFICE_CODE,
		CASE
		WHEN jso.OFFICE_TYPE IN (1, 2) THEN
		CASE
		WHEN COALESCE(jso.TOTAL_STAFF, 0) &lt;= 200 THEN osrac.MAX_AREA
		WHEN COALESCE(jso.TOTAL_STAFF, 0) &gt;= 400 THEN osrac.MIN_AREA
		ELSE (1100 - COALESCE(jso.TOTAL_STAFF, 0)) / 100
		END
		WHEN jso.OFFICE_TYPE = 3 THEN
		CASE
		WHEN COALESCE(jso.TOTAL_STAFF, 0) &lt;= 200 THEN osrac.MAX_AREA
		WHEN COALESCE(jso.TOTAL_STAFF, 0) &gt;= 400 THEN osrac.MIN_AREA
		ELSE (1000 - COALESCE(jso.TOTAL_STAFF, 0)) / 100
		END
		WHEN jso.OFFICE_TYPE IN (4, 5) THEN
		CASE
		WHEN COALESCE(jso.TOTAL_STAFF, 0) &lt;= 100 THEN osrac.MAX_AREA
		WHEN COALESCE(jso.TOTAL_STAFF, 0) &gt;= 200 THEN osrac.MIN_AREA
		ELSE (500 - COALESCE(jso.TOTAL_STAFF, 0)) / 50
		END
		ELSE NULL
		END AS serviceRoomApprovedArea
		FROM JS_SYS_OFFICE jso
		LEFT JOIN OB_SERVICE_ROOMS_APPROVED_CONFIG osrac
		ON jso.OFFICE_TYPE = osrac.ID
		) t1
		LEFT JOIN (
		SELECT
		jso.OFFICE_CODE,
		SUM(CASE WHEN aac.TYPE IN (0, 1) THEN aac.avg_area ELSE 0 END) AS SERVICE_OR_OFFICE_ROOM_USED_AREA,
		SUM(CASE WHEN aac.TYPE = 3 THEN aac.avg_area ELSE 0 END) AS SUBSIDIARY_ROOM_USED_AREA
		FROM JS_SYS_OFFICE jso
		LEFT JOIN JS_SYS_EMPLOYEE jse
		ON jso.OFFICE_CODE = jse.OFFICE_CODE
		LEFT JOIN OB_REAL_ESTATE_USED_USER oreuu
		ON jse.EMP_CODE = oreuu.USER_CODE
		LEFT JOIN avg_area_calc aac
		ON oreuu.REAL_ESTATE_ID = aac.REAL_ESTATE_ID
		GROUP BY jso.OFFICE_CODE
		) t2
		ON t1.OFFICE_CODE = t2.OFFICE_CODE
		CROSS JOIN equipment_ratio er
		<where>
			<if test="officeCode != null and officeCode != ''">
				t1.OFFICE_CODE = #{officeCode}
			</if>
		</where>


<!--		SELECT-->
<!--		SERVICEROOMAPPROVEDAREA AS serviceRoomApprovedArea,-->
<!--		SERVICE_OR_OFFICE_ROOM_USED_AREA * (-->
<!--		SELECT-->
<!--		oerac.EQUIPMENT_ROOM_RATIO / 100-->
<!--		FROM-->
<!--		OB_EQUIPMENT_ROOMS_APPROVED_CONFIG oerac-->
<!--		LIMIT 1) AS equipmentRoomApprovedArea,-->
<!--		SUBSIDIARY_ROOM_USED_AREA ancillaryRoomAreaApprovedArea-->
<!--		FROM-->
<!--		(-->
<!--		SELECT-->
<!--		t1.OFFICE_CODE,-->
<!--		t1.SERVICEROOMAPPROVEDAREA,-->
<!--		t2.SERVICE_OR_OFFICE_ROOM_USED_AREA,-->
<!--		t2.SUBSIDIARY_ROOM_USED_AREA-->
<!--		FROM-->
<!--		(-->
<!--		SELECT-->
<!--		jso.OFFICE_CODE,-->
<!--		CASE-->
<!--		WHEN jso.OFFICE_TYPE IN (1, 2) THEN-->
<!--		CASE-->
<!--		WHEN COALESCE(jso.TOTAL_STAFF, 0) <= 200 THEN osrac.MAX_AREA-->
<!--		WHEN COALESCE(jso.TOTAL_STAFF, 0) >= 400 THEN osrac.MIN_AREA-->
<!--		ELSE (1100 - COALESCE(jso.TOTAL_STAFF, 0)) / 100-->
<!--		END-->
<!--		WHEN jso.OFFICE_TYPE = 3 THEN-->
<!--		CASE-->
<!--		WHEN COALESCE(jso.TOTAL_STAFF, 0) <= 200 THEN osrac.MAX_AREA-->
<!--		WHEN COALESCE(jso.TOTAL_STAFF, 0) >= 400 THEN osrac.MIN_AREA-->
<!--		ELSE (1000 - COALESCE(jso.TOTAL_STAFF, 0)) / 100-->
<!--		END-->
<!--		WHEN jso.OFFICE_TYPE IN (4, 5) THEN-->
<!--		CASE-->
<!--		WHEN COALESCE(jso.TOTAL_STAFF, 0) <= 100 THEN osrac.MAX_AREA-->
<!--		WHEN COALESCE(jso.TOTAL_STAFF, 0) >= 200 THEN osrac.MIN_AREA-->
<!--		ELSE (500 - COALESCE(jso.TOTAL_STAFF, 0)) / 50-->
<!--		END-->
<!--		ELSE NULL-->
<!--		END AS serviceRoomApprovedArea-->
<!--		FROM-->
<!--		JS_SYS_OFFICE jso-->
<!--		LEFT JOIN OB_SERVICE_ROOMS_APPROVED_CONFIG osrac ON-->
<!--		jso.OFFICE_TYPE = osrac.ID-->
<!--		GROUP BY-->
<!--		jso.OFFICE_CODE,-->
<!--		jso.OFFICE_NAME,-->
<!--		jso.OFFICE_TYPE,-->
<!--		jso.TOTAL_STAFF,-->
<!--		osrac.MAX_AREA,-->
<!--		osrac.MIN_AREA) t1-->
<!--		LEFT JOIN (-->
<!--		SELECT-->
<!--		t.OFFICE_CODE,-->
<!--		SUM(t.SERVICE_OR_OFFICE_ROOM_USED_AREA) SERVICE_OR_OFFICE_ROOM_USED_AREA,-->
<!--		SUM(t.SUBSIDIARY_ROOM_USED_AREA) SUBSIDIARY_ROOM_USED_AREA-->
<!--		FROM-->
<!--		(-->
<!--		SELECT-->
<!--		jso.OFFICE_CODE,-->
<!--		ore.ID REAL_ESTATE_ID,-->
<!--		COUNT(oreuu.USER_CODE) USED_USER_COUNT,-->
<!--		COUNT(oreuu.USER_CODE) * COALESCE(t1.AVG_AREA, 0) SERVICE_OR_OFFICE_ROOM_USED_AREA,-->
<!--		COUNT(oreuu.USER_CODE) * COALESCE(t2.AVG_AREA, 0) SUBSIDIARY_ROOM_USED_AREA-->
<!--		FROM-->
<!--		JS_SYS_OFFICE jso-->
<!--		LEFT JOIN JS_SYS_EMPLOYEE jse ON-->
<!--		jso.OFFICE_CODE = jse.OFFICE_CODE-->
<!--		LEFT JOIN OB_REAL_ESTATE_USED_USER oreuu ON-->
<!--		jse.EMP_CODE = oreuu.USER_CODE-->
<!--		LEFT JOIN OB_REAL_ESTATE ore ON-->
<!--		oreuu.REAL_ESTATE_ID = ore.ID-->
<!--		LEFT JOIN (-->
<!--		SELECT-->
<!--		ore.ID REAL_ESTATE_ID,-->
<!--		CASE-->
<!--		WHEN COUNT(oreuu.USER_CODE) > 0-->
<!--		THEN ore.area / COUNT(oreuu.USER_CODE)-->
<!--		ELSE 0-->
<!--		END avg_area-->
<!--		FROM-->
<!--		OB_REAL_ESTATE ore-->
<!--		LEFT JOIN OB_REAL_ESTATE_USED_USER oreuu ON-->
<!--		ore.ID = oreuu.REAL_ESTATE_ID-->
<!--		WHERE-->
<!--		ore."TYPE" IN (0, 1)-->
<!--		GROUP BY-->
<!--		ore.ID,-->
<!--		ore.area-->
<!--		) t1 ON-->
<!--		ore.ID = t1.REAL_ESTATE_ID-->
<!--		LEFT JOIN (-->
<!--		SELECT-->
<!--		ore.ID REAL_ESTATE_ID,-->
<!--		CASE-->
<!--		WHEN COUNT(oreuu.USER_CODE) > 0-->
<!--		THEN ore.area / COUNT(oreuu.USER_CODE)-->
<!--		ELSE 0-->
<!--		END avg_area-->
<!--		FROM-->
<!--		OB_REAL_ESTATE ore-->
<!--		LEFT JOIN OB_REAL_ESTATE_USED_USER oreuu ON-->
<!--		ore.ID = oreuu.REAL_ESTATE_ID-->
<!--		WHERE-->
<!--		ore."TYPE" IN (3)-->
<!--		GROUP BY-->
<!--		ore.ID,-->
<!--		ore.area-->
<!--		) t2 ON-->
<!--		ore.ID = t2.REAL_ESTATE_ID-->
<!--		GROUP BY-->
<!--		jso.OFFICE_CODE,-->
<!--		ore.ID,-->
<!--		t1.AVG_AREA,-->
<!--		t2.AVG_AREA) t-->
<!--		GROUP BY-->
<!--		t.OFFICE_CODE) t2 ON-->
<!--		t1.OFFICE_CODE = t2.OFFICE_CODE) t3;-->
	</select>
	
</mapper>