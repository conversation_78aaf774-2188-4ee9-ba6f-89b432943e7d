<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.filemanager.dao.FilemanagerDao">

	<sql id="tagColumn">
		<choose>
			<when test="global.dbName == 'oracle'">
				LISTAGG(ft.tag_id, '|') WITHIN GROUP (ORDER BY ft.tag_id) AS "tagId",
				LISTAGG(ft.tag_name, '|') WITHIN GROUP (ORDER BY ft.tag_name) AS "tagName",
				LISTAGG(ft.tag_color, '|') WITHIN GROUP (ORDER BY ft.tag_color) AS "tagColor"
			</when>
			<when test="global.dbName == 'postgresql'">
				STRING_AGG(ft.tag_id, '|') AS "tagId",
				STRING_AGG(ft.tag_name, '|') AS "tagName",
				STRING_AGG(ft.tag_color, '|') AS "tagColor"
			</when>
			<when test="global.dbName == 'mssql'">
				STUFF(
					(
						SELECT '|' + CAST(ft.tag_id AS VARCHAR(10))
						FROM ${_prefix}filemanager_upload_tag fut
						JOIN ${_prefix}filemanager_tag ft ON ft.tag_id = fut.tag_id AND ft.status = '0'
						WHERE (a.folder_id = fut.upload_id OR a.file_upload_id = fut.upload_id)
						FOR XML PATH('')
					), 1, 1, '') AS "tagId",
				STUFF(
					(
						SELECT '|' + ft.tag_name
						FROM ${_prefix}filemanager_upload_tag fut
						JOIN ${_prefix}filemanager_tag ft ON ft.tag_id = fut.tag_id AND ft.status = '0'
						WHERE (a.folder_id = fut.upload_id OR a.file_upload_id = fut.upload_id)
						FOR XML PATH('')
					), 1, 1, '') AS "tagName",
				STUFF(
					(
						SELECT '|' + ft.tag_color
						FROM ${_prefix}filemanager_upload_tag fut
						JOIN ${_prefix}filemanager_tag ft ON ft.tag_id = fut.tag_id AND ft.status = '0'
						WHERE (a.folder_id = fut.upload_id OR a.file_upload_id = fut.upload_id)
						FOR XML PATH('')
					), 1, 1, '') AS "tagColor"
			</when>
			<otherwise>
				GROUP_CONCAT(ft.tag_id SEPARATOR '|') AS "tagId",
				GROUP_CONCAT(ft.tag_name SEPARATOR '|') AS "tagName",
				GROUP_CONCAT(ft.tag_color SEPARATOR '|') AS "tagColor"
			</otherwise>
		</choose>
	</sql>

	<sql id="tagTable">
		LEFT JOIN ${_prefix}filemanager_upload_tag fut ON a.folder_id = fut.upload_id OR a.file_upload_id = fut.upload_id
		LEFT JOIN ${_prefix}filemanager_tag ft ON ft.tag_id = fut.tag_id AND ft.status = '0'
	</sql>

	<sql id="tagWhere">
		<if test="tagId != null and tagId != ''">
			AND ft.tag_id = #{tagId}
		</if>
		<if test="tagName != null and tagName != ''">
			AND ft.tag_name = #{tagName}
		</if>
	</sql>

	<!-- 查询数据 -->
	<select id="findList" resultType="Filemanager" weight="100" >
		WITH
		folder_data AS (
		SELECT
		a.parent_code AS parent_code,
		a.id AS folder_id,
		'' AS file_upload_id,
		a.folder_name AS file_name,
		'folder' AS file_type,
		a.tree_sort AS tree_sort,
		a.remarks AS remarks,
		a.create_by AS create_by,
		a.create_date AS create_date,
		a.update_by AS update_by,
		a.update_date AS update_date,
		null AS file_extension,
		null AS file_size,
		cu.user_name AS create_by_name
		FROM ${_prefix}filemanager_folder a
		JOIN ${_prefix}sys_user cu ON cu.user_code = a.create_by
		<where>
			${sqlMap.where.toSql()}
			<if test="global.useCorpModel">
				AND a.corp_code = #{corpCode}
				AND cu.corp_code =  #{corpCode}
			</if>
			<if test="groupType != null and groupType != ''">
				AND a.group_type = #{groupType}
			</if>
			<choose>
				<when test="groupType != null and groupType == GROUP_TYPE_SELF">
					AND a.create_by = #{createBy}
				</when>
				<when test="groupType != null and groupType == GROUP_TYPE_OFFICE and officeCode != null and officeCode != ''">
					AND a.office_code = #{officeCode}
				</when>
			</choose>
			<choose>
				<when test="folderId != null and folderId != ''">
					AND a.parent_code = #{folderId}
				</when>
				<otherwise>
					AND a.parent_code = '0'
				</otherwise>
			</choose>
			<if test="fileName != null and fileName != ''">
				AND a.folder_name LIKE #{fileNameLike}
			</if>
		</where>
        ),
		upload_data AS (
		SELECT
		null AS parent_code,
		'' AS folder_id,
		a.id AS file_upload_id,
		a.file_name AS file_name,
		a.file_type AS file_type,
		9999999999 AS tree_sort,
		a.remarks AS remarks,
		a.create_by AS create_by,
		a.create_date AS create_date,
		a.update_by AS update_by,
		a.update_date AS update_date,
		e.file_extension AS file_extension,
		e.file_size AS file_size,
		cu.user_name AS create_by_name
		FROM ${_prefix}filemanager_upload a
		JOIN ${_prefix}sys_file_entity e ON e.file_id = a.file_id
		JOIN ${_prefix}sys_user cu ON cu.user_code = a.create_by
		<where>
			${sqlMap.where.toSql()}
			<if test="global.useCorpModel">
				AND a.corp_code = #{corpCode}
				AND cu.corp_code = #{corpCode}
			</if>
			<if test="createBy != null and createBy != ''">
				AND a.create_by = #{createBy}
			</if>
			<if test="groupType != null and groupType != ''">
				AND a.biz_type = #{bizType}
			</if>
			<if test="folderId != null and folderId != ''">
				AND a.biz_key = #{folderId}
			</if>
			<if test="fileName != null and fileName != ''">
				AND a.file_name LIKE #{fileNameLike}
			</if>
		</where>
		),
		combined_data AS (
		SELECT * FROM folder_data
		UNION ALL
		SELECT * FROM upload_data
		)
		SELECT
			a.parent_code,
			a.folder_id,
			a.file_upload_id,
			a.file_name,
			a.file_type,
			a.tree_sort,
			a.remarks,
			a.create_by,
			a.create_date,
			a.update_by,
			a.update_date,
			a.file_extension,
			a.file_size,
			a.create_by_name,
			<include refid="com.jeesite.modules.filemanager.dao.FilemanagerDao.tagColumn" />
		FROM combined_data a
		<include refid="com.jeesite.modules.filemanager.dao.FilemanagerDao.tagTable" />
		<where>
			<include refid="com.jeesite.modules.filemanager.dao.FilemanagerDao.tagWhere" />
		</where>
		GROUP BY
			a.parent_code,
			a.folder_id,
			a.file_upload_id,
			a.file_name,
			a.file_type,
			a.tree_sort,
			a.remarks,
			a.create_by,
			a.create_date,
			a.update_by,
			a.update_date,
			a.file_extension,
			a.file_size,
			a.create_by_name
		ORDER BY ${sqlMap.order.toSql()}
	</select>
	
</mapper>