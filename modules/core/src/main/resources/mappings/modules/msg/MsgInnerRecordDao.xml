<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.msg.dao.MsgInnerRecordDao">
	
	<!-- 查询数据
	<select id="findList" resultType="MsgInnerRecord">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->
	
	<update id="updateReadStatus">
		UPDATE ${_prefix}sys_msg_inner_record SET
			read_status = #{readStatus},
			read_date = #{readDate}
		WHERE msg_inner_id = #{msgInnerId}
			AND receive_user_code = #{receiveUserCode}
			AND read_date is null
	</update>
	
</mapper>