<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */
@servlet.getResponse().setStatus(500);

var message = @ObjectUtils.toString(@request.getAttribute("message"));

var ex;
if (isBlank(message)){
	ex = @ExceptionUtils.getThrowable(request);
	if (ex != null){
		var m = @ExceptionUtils.getExceptionMessage(ex);
		if (isNotBlank(m)){
			message = m;
		}else{
			@org.slf4j.LoggerFactory.getLogger("error/500").error('', ex);
		}
	}
}

if (isBlank(message)){
	message = text('sys.error.500.message');
}

// 如果是异步请求或是手机端，则直接返回信息
if (@ServletUtils.isAjaxRequest(request)) {
	if (@Global.getPropertyToBoolean('error.page.printErrorInfo', 'true')
			&& !@StringUtils.equals(message, "演示模式，不允许操作！") && ex != null){
		print(@ServletUtils.renderResult(@Global.FALSE, message, @ExceptionUtils.getStackTraceAsString(ex)));
	}else{
		print(@ServletUtils.renderResult(@Global.FALSE, message));
	}
}

// 输出异常信息页面
else {
%>
<% layout('/layouts/default.html', {title: '500 - '+text('sys.error.500.title')}){ %>
<link rel="stylesheet" href="${ctxStatic}/common/error.css?${_version}">
<div class="error-page">
	<div class="headline text-red">500</div>
	<div class="error-content">
		<h3>${message}</h3>
		<p>${text('sys.error.500.message.p1')}</p>
		<button type="button" class="btn btn-danger btn-sm" onclick="history.go(-1);"><i
			class="fa fa-reply-all"></i> ${text('sys.error.returnButton')}</button>
	</div>
	<div class="copyright">
		&copy; ${@DateUtils.getYear()} ${@Global.getConfig('productNameAssigned', @Global.getConfig('productName'))} - Powered By <a
			href="http://jeesite.com" target="_blank">JeeSite ${@Global.getProperty('jeesiteVersion')}</a>
	</div>
</div>
<% if (@Global.getPropertyToBoolean('error.page.printErrorInfo', 'true')
	&& !@StringUtils.equals(message, "演示模式，不允许操作！")){ %>
<div class="box mt20">
	${@StringUtils.toHtml(@ExceptionUtils.getStackTraceAsString(ex))}<br/>
	此异常信息若不想输出，可打开 'application.yml' 文件，设置 'error.page.printErrorInfo: false' 即可
</div>
<% } %>
<% } %>
<% } %>