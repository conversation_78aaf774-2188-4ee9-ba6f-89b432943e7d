<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */
@servlet.getResponse().setStatus(400);

var message = @ObjectUtils.toString(@request.getAttribute('message'));

if (isBlank(message)){
	var ex = @ExceptionUtils.getThrowable(request);
	if (ex != null){
		if(@StringUtils.startsWith(@ex.getMessage(), 'msg:')){
			message = @StringUtils.replace(@ex.getMessage(), 'msg:', '');
		}else if (type.fullName(ex) == 'org.springframework.validation.BindException'
				|| type.fullName(ex) == 'org.springframework.web.bind.MethodArgumentNotValidException'){
			for (var e in ex.globalErrors){
				message = message + '☆ ' + text(e.defaultMessage) + '<br/>';
			}
			for (var e in ex.fieldErrors){
				if (@StringUtils.inString(e.field, 'pageNo', 'pageSize')){
					message = text('提交的分页参数，超出有效范围');
					break;
				}
				message = message + '☆ ' + text(e.defaultMessage) + '<br/>';
			}
		}else if (type.fullName(ex) == 'javax.validation.ConstraintViolationException'){
			for (var v in ex.constraintViolations) {
				message = message + '☆ ' + text(v.message) + '<br/>';
			}
		}else{
			message = message + ex.message;
		}
		@org.slf4j.LoggerFactory.getLogger('error/400').info(ex.message, ex);
	}
}

if (isBlank(message)){
	message = text('sys.error.400.message');
}

// 如果是异步请求或是手机端，则直接返回信息
if (@ServletUtils.isAjaxRequest(request)) {
	print(@ServletUtils.renderResult(@Global.FALSE, message));
}

// 输出异常信息页面
else {
%>
<% layout('/layouts/default.html', {title: '400 - '+text('sys.error.400.title')}){ %>
<link rel="stylesheet" href="${ctxStatic}/common/error.css?${_version}">
<div class="error-page">
	<div class="headline text-yellow">400</div>
	<div class="error-content">
		<h3>${text('sys.error.400.title')}</h3>
		<p>${message}</p>
		<button type="button" class="btn btn-warning btn-sm" onclick="history.go(-1);"><i
			class="fa fa-reply-all"></i> ${text('sys.error.returnButton')}</button>
	</div>
	<div class="copyright">
		&copy; ${@DateUtils.getYear()} ${@Global.getConfig('productNameAssigned', @Global.getConfig('productName'))} - Powered By <a
			href="http://jeesite.com" target="_blank">JeeSite ${@Global.getProperty('jeesiteVersion')}</a>
	</div>
</div>
<% } %>
<% } %>