<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */
@servlet.getResponse().setStatus(403);

var message = @ObjectUtils.toString(@request.getAttribute("message"));

if (isBlank(message)){
	var ex = @ExceptionUtils.getThrowable(request);
	if (ex != null){
		var m = @ExceptionUtils.getExceptionMessage(ex);
		if (isNotBlank(m)){
			message = m;
		}
	}
}

if (isBlank(message)){
	message = text('sys.error.403.message');
}

// 如果是异步请求或是手机端，则直接返回信息
if (@ServletUtils.isAjaxRequest(request)) {
	print(@ServletUtils.renderResult(@Global.FALSE, message));
}

// 输出异常信息页面
else {
%>
<% layout('/layouts/default.html', {title: '403 - '+text('sys.error.403.title')}){ %>
<link rel="stylesheet" href="${ctxStatic}/common/error.css?${_version}">
<div class="error-page">
	<div class="headline text-yellow">403</div>
	<div class="error-content">
		<h3>${message}</h3>
		<p>${text('sys.error.403.message.p1')}</p>
		<button type="button" class="btn btn-warning btn-sm" onclick="history.go(-1);"><i
			class="fa fa-reply-all"></i> ${text('sys.error.returnButton')}</button>
	</div>
	<div class="copyright">
		&copy; ${@DateUtils.getYear()} ${@Global.getConfig('productNameAssigned', @Global.getConfig('productName'))} - Powered By <a
			href="http://jeesite.com" target="_blank">JeeSite ${@Global.getProperty('jeesiteVersion')}</a>
	</div>
</div>
<% } %>
<% } %>