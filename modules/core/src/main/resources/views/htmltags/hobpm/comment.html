<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
* No deletion without permission, or be held responsible to law. */

/**
* 审批意见
* <AUTHOR>
* @version 2019-11-3
*/
var p = {

// 业务流程实体对象
bpmEntity: bpmEntity!,
showCommWords: toBoolean(showCommWords!true),
rows: rows!3,
required: toBoolean(required!false),
// 内置参数
thisTag: thisTag
};
%><#form:textarea path="bpm.comment" rows="${p.rows}" maxlength="200" class="form-control ${p.required?'required':''} "/>
<% if(p.showCommWords){ %>
<p class="help-block" id="commWords">
    <font class="btn-xs" color="#888">${text('常用语')}：</font>
    <button type="button" class="btn btn-xs mr5">${text('同意')}</button>
    <button type="button" class="btn btn-xs mr5">${text('已阅')}</button>
</p>
<script>
    $('#commWords button').click(function(){
        $('#bpm_comment').text($(this).text())
    });
</script>
<% } %>