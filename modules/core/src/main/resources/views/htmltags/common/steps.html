<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
* No deletion without permission, or be held responsible to law. */

/**
* 步骤条
* <AUTHOR>
* @version 2025-03-21
*/
var p = {

// 业务流程实体对象
entity: entity!,
// 内置参数
thisTag: thisTag,
readonly: readonly!'false',
formKey: formKey
};


// 编译属性参数
form.attrs(p);

%>

<style type="text/css">
    .custom-steps {
        display: flex;
        justify-content: space-between;
        padding: 28px 0 32px 0;
        border-radius: 4px;
        background: #ffffff;
    }

    .step-item {
        display: flex;
        position: relative;
        flex: 1;
        padding: 0 10px;
    }

    .step-connector {
        margin: 10px 0 0 10px;
        width: 100%;
        height: 1px;
        background: #ffffff;
        z-index: 1;
        border-top: 2px dashed rgba(62,155,255,.8);
        transform: translateY(-50%);
        position: relative;
    }

    .step-item:last-child .step-connector {
        display: none;
    }

    .step-item.solid-step .step-connector {
        background: #ffffff;
        border-top: 2px solid rgba(62,155,255,.8);
    }

    .dot-container {
        width: 20px;
        height: 20px;
        background: #dcebff; /* 蓝色背景 */
        border-radius: 20px; /* 椭圆圆角 */
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2px; /* 圆点间距 */
        position: absolute; /* 绝对定位 */
        top: 50%; /* 垂直居中 */
        left: 45%; /* 水平居中 */
        transform: translate(-50%, -50%); /* 调整位置 */
        margin: 0 5px; /* 左右各隔开5px */
        box-shadow: 0 0 0 5px #ffffff; /* 使用box-shadow模拟背景色覆盖线条 */
    }
    .dot {
        width: 2px;
        height: 2px;
        background: #59a1ff; /* 蓝色圆点 */
        border-radius: 50%; /* 圆点圆角 */
    }

    .step-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #dcebff;
        color: #59a1ff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        flex-shrink: 0;
    }

    .step-icon.done {
        background: #409eff;
        color: white;
        border: 2px solid #409eff;
    }

    .step-process {
        /*width: 70px;*/
        color: #606266;
        font-size: 14px;
        margin-left: 8px;
        text-align: left;
    }

    .step-process p {
        margin: 3px 0;
        font-size: 12px;
        color: #909399;
    }

    .step-process .title {
        font-weight: 550;
    }

    .step-process .user {
        width: 60px;
        white-space: nowrap;
        overflow: visible;
    }
    .step-process .time {
        width: 60px;
        white-space: nowrap;
        overflow: visible;
    }
</style>

<div class="flex-container-end">
    <a href="${ctx}/bpm/bpmRuntime/trace?formKey=${formKey}&bizKey=${entity.id}" title="流程追踪" data-layer="true" class=" addTabPage btnTool process_details_btn" style=""><img data-v-ea2ed64e="" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAdJJREFUOE+lk09IVGEUxX/nPaVCsqCFtGhThAQtI4qaGSmiIIgiIqGIaEpLQjJMalO4Eo2EkiSdEVq0khYh1EIoaiaTNq3b6Mr+iBBURqS9d2pEYYxXFn3be86Pe893r/jPpyR/Xd4HY3MXWFWqCyYJOPQsq9Ff9YmAdM7DwJ5ysSAPDBqGEG+JuVVo0M1EQCbvNpvOMoAFx6Zj7leFbHZMrcR1ie5EQMmYznlTMD/CTMzUi0aNlXeU7vcBRO9vAUtlWzfgLVHMSCIg1ee1CmiTWF0CWbz/toyOl8f1KdXn/cVGPczk3R+bdXOAHQNeGUQcFtTMRtyrrOAGcLS8C4suzDuJdmDCpsohu5S+442EPAE+SIzH5hrmikT9IoDpqoBHUUAO0b68kgfDJ/RF8182VjijcwuG3TnXzMIliTVzI5g3UUDnSFafd/Z6/fMmjS9oS4AZh2wrntKrpYJLqiuT84QDmgpZDW3v9orRi/p6ZNDh5Ef22lTPmQKmiqf1OBGQyvmCTKvFZZn6KKQhNC2Y1kUZBDQXs+pJXOVU3ucFzTYbfs7dY1Mr2LdIbPoKDTr7x1vI5H11OqKjOmTrd7it+U0EJoOIk08b9fqvjulfwvwB64utcaaALFEAAAAASUVORK5CYII=">流程详情
    </a>
</div>
<div class="container">
    <div class="custom-steps" id="stepsContainer"></div>
</div>

<script>
    function renderSteps(stepsData) {
        const container = document.getElementById('stepsContainer');
        const stepCount = stepsData.length;
        let html = '';
        // 处理超过5个节点的情况
        if (stepCount > 5) {
            let currentIndex = stepsData.findIndex(item => item.status === 'process');
            let newData = [];
            if (currentIndex === -1) {
                newData = [
                    stepsData[0], // 开始节点
                    ...stepsData.slice(Math.max(1, stepCount - 3), stepCount - 1), // 当前节点及前后
                    stepsData[stepCount - 1] // 结束节点
                ];
            } else {
                newData = [
                    stepsData[0], // 开始节点
                    ...stepsData.slice(Math.max(1, currentIndex - 2), Math.min(stepCount - 1, currentIndex + 1)), // 当前节点及前后
                    stepsData[stepCount - 1] // 结束节点
                ];
            }

            let newCurrentIndex = newData.findIndex(item => item.status === 'process');
            let newCurrentIndex_1 = newCurrentIndex;
            if (newCurrentIndex === -1) {
                newCurrentIndex = newData.length;
                newCurrentIndex_1 = newCurrentIndex - 1;
            }
            let newLength = newData.length;
            newData.forEach((item, index) => {
                const iconContent = index < newCurrentIndex ? '✔' : item.no;

                const iconClass = index < newCurrentIndex-1 ? 'solid-step' : '';
                const iconStyle = index < newCurrentIndex ? 'done' : '';
                let dotEle = '';
                if ((index === 0 && newData[newCurrentIndex_1].no > 3)
                    || (index === newLength - 2 && newData[newCurrentIndex_1].no < newData[newLength - 1].no - 1)) {
                    dotEle = '<div class="dot-container"><span class="dot"></span><span class="dot"></span><span class="dot"></span></div>';
                }

                html +=  ('<div class="step-item '
                    + iconClass
                    + '"><div class="step-icon '
                    + iconStyle
                    +'">' + iconContent
                    + '</div><div class="step-process"><div class="title"><span>' +item.title + '</span></div><div class="user">'
                    + (item.user ? '<p>' + item.user + '</p>' : '') + '</div><div class="time">'
                    + (item.time ? '<p>' + item.time + '</p>' : '') + '</div></div><div class="step-connector">'
                    + dotEle + '</div></div>');
            });
        } else {
            // 不超过5个节点正常渲染
            let newCurrentIndex = stepsData.findIndex(item => item.status === 'process');
            let newCurrentIndex_1 = newCurrentIndex;
            if (newCurrentIndex === -1) {
                newCurrentIndex = stepsData.length;
                newCurrentIndex_1 = newCurrentIndex - 1;
            }

            stepsData.forEach((item, index) => {
                const iconContent = item.status === 'done' ? '✔' : item.no;
                const iconClass = index < newCurrentIndex-1 ? 'solid-step' : '';
                const iconStyle = item.status === 'done' ? 'done' : '';

                let dotEle = '';
                if ((index === stepsData.length - 2 && stepsData[newCurrentIndex_1].no < stepsData[stepsData.length - 1].no - 1)) {
                    dotEle = '<div class="dot-container"><span class="dot"></span><span class="dot"></span><span class="dot"></span></div>';
                }

                html += (
                        '<div class="step-item ' + iconClass + '"><div class="step-icon '
                    + iconStyle + '">' + iconContent + '</div><div class="step-process"><div class="title"><span>'
                    + item.title + '</span></div><div class="user">'
                    + (item.user ? '<p>' + item.user + '</p>' : '' )
                    + '</div><div class="time">'
                    + (item.time ? '<p>' + item.time + '</p>' : '')
                    + '</div></div><div class="step-connector">' + dotEle + '</div></div>'
                );
            });
        }

        container.innerHTML = html;
    }

    function loadModelJson(procIns, history) {

        let fUrl = "";
        if (history) {
            fUrl = "${ctx}/bpm/display/app/rest/process-instances/history/" + procIns.id + "/model-json?t=" + new Date().getTime();
        } else {
            fUrl = "${ctx}/bpm/display/app/rest/process-instances/" + procIns.id + "/model-json?t=" + new Date().getTime();
        }

        let modelJson = {};
        $.ajax({
            type: 'GET',
            url: fUrl,
            data: {},
            dataType: 'json',
            async: false,
            error: function(data){
                // js.showErrorMessage(data.responseText);
                console.log(data);
            },
            success: function(data, status, xhr){
                if (data != null) {
                    modelJson = data;
                }
            }
        });
        return modelJson;
    }

    function getProcIns() {
        let procIns = {};
        $.ajax({
            type: 'GET',
            url: "${ctx}/bpm/bpmRuntime/getProcIns?formKey=${formKey}&bizKey=${entity.id}&t=" + new Date().getTime(),
            data: {},
            dataType: 'json',
            async: false,
            error: function(data){
                // js.showErrorMessage(data.responseText);
                console.log(data);
            },
            success: function(data, status, xhr){
                if (data != null) {
                    procIns = data;
                }
            }
        });
        return procIns;
    }


    function loadTraceData() {
        let procIns = getProcIns();
        if (procIns.id === undefined || procIns.id === null) {
            return;
        }
        $.ajax({
            type: 'POST',
            url: "${ctx}/bpm/display/app/rest/process-instances/" + procIns.id + "/trace-json?t=" + new Date().getTime(),
            data: {},
            dataType: 'json',
            async: false,
            error: function(data){
                console.log(data);
                // js.showErrorMessage(data.responseText);
            },
            success: function(data, status, xhr){
                if (data != null) {

                    let lastItem = data[data.length - 1];
                    let modelJson = {};
                    if (lastItem.status === "2") {
                        modelJson = loadModelJson(procIns, true)
                    } else {
                        modelJson = loadModelJson(procIns);
                    }

                    let stepsData = [];
                    stepsData = data.map((item, index) => {
                        let statusText = "";
                        if (item.status === "2") {
                            statusText = "done";
                        } else if (item.status === "1") {
                            statusText = "process";
                        }

                        return {
                            no: index + 1,
                            title: item.name,
                            user: item.assigneeInfo || "",
                            time: item.endTime || "",
                            status: statusText
                        };
                    });


                    if (lastItem.status === "1" && modelJson.elements && modelJson.flows) {
                        let endEvents = modelJson.elements.filter(item => item.type === 'EndEvent');
                        let userTasks = modelJson.elements.filter(item => item.type === 'UserTask');
                        let totalUserTask = 0;
                        if (userTasks) {
                            totalUserTask = userTasks.length;
                        }
                        let relevantFlows = [];
                        endEvents.forEach(endEvent => {
                            let matchingFlows = modelJson.flows.filter(flow => flow.targetRef === endEvent.id);
                            relevantFlows.push(...matchingFlows);
                        });
                        let sourceElements = [];
                        relevantFlows.forEach(flow => {
                            let sourceElement = modelJson.elements.find(item => item.id === flow.sourceRef);
                            if (sourceElement) {
                                sourceElements.push(sourceElement);
                            }
                        });
                        if (sourceElements.length > 0) {
                            let endItem = sourceElements.find(item => item.id === lastItem.activityId);
                            if (endItem) {
                            } else {
                                let stepData = {
                                    no: Math.max(stepsData.length + 1, totalUserTask),
                                    title: sourceElements[0].name,
                                    user: "",
                                    time: "",
                                    status: ""
                                };
                                stepsData.push(stepData);
                            }
                        }

                    }
                    renderSteps(stepsData);

                }
            }
        });
    }

    $(function() {
        loadTraceData();
    });

</script>



