<%
/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
* No deletion without permission, or be held responsible to law. */

/**
* 信息描述
* <AUTHOR>
* @version 2025-03-21
*/
var p = {

// 业务流程实体对象
entity: entity!,
// 内置参数
thisTag: thisTag,
readonly: readonly!'false',
title: title!'申请'

};
// 编译属性参数
form.attrs(p);

var createUserName = '';
var createUnitName = '';

if(!entity.isNewRecord){
    var entityCreateBy = @UserUtils.get(entity.createBy!);
    if (entityCreateBy != null) {
        createUserName = entityCreateBy.userName;

        var entityEmployee = @EmpUtils.get(entityCreateBy);
        if (entityEmployee != null && entityEmployee.office != null) {
            createUnitName = entityEmployee.office.officeName;
        }
    }
}

%>

<style type="text/css">
    .viewport-container {
        display: flex;
        align-items: flex-start;
        padding: 20px;
        gap: 15px;
        height: 135px;
    }
    .viewport-container .viewport-icon {
        width: 90px;
        height: 95px;
    }
    .viewport-container .viewport-content {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding-top: 10px;
    }
    .viewport-container .viewport-title {
        font-size: 20px;
        font-weight: bold;
    }
    .viewport-container .viewport-info {
        display: flex;
        gap: 20px;
        font-size: 16px;
        color: #666;
    }
</style>
<div class="viewport-container">
    <img src="${ctxStatic}/viewport.png" alt="公共机构图标" class="viewport-icon">
    <div class="viewport-content">
        <div class="viewport-title">${p.title}</div>
        <div class="viewport-info">
            <span>填报人：${createUserName}</span>
            <span>填报单位：${createUnitName}</span>
            <% if(!entity.isNewRecord){ %>
            <span>填报日期：${p.entity == null ? '' : p.entity.createDate, 'yyyy-MM-dd HH:mm:ss'}</span>
            <% } %>
        </div>
    </div>
</div>
