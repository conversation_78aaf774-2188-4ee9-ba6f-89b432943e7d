<% layout('/layouts/default.html', {title: '房源房源信息表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-body">
            <#form:form id="searchForm" model="${assetQueryRequest}" action="${ctx}/szgz/assets/query" method="post" class="form-inline"
            data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
            <div class="form-group">
                <label class="control-label">${text('所属单位')}：</label>
                <div class="control-inline width-120">
                    <#form:treeselect id="agencyCode" title="${text('机构选择')}"
                    path="agencyCode" labelPath="office.officeName"
                    url="${ctx}/sys/office/treeData"
                    callbackFuncName="orgCodeSelectCallback"
                    class="required" allowClear="true" />
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('资产名称')}：</label>
                <div class="control-inline">
                    <#form:input path="assetName" maxlength="10" class="form-control width-120 required"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('资产编号')}：</label>
                <div class="control-inline">
                    <#form:input path="assetCode" maxlength="10" class="form-control width-120"/>
                </div>
            </div>
            <#form:input path="mofDivCode" type="hidden" id="mofDivCode" defaultValue="350000000"/>
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
            </div>
        </#form:form>
        <div class="row">
            <div class="col-xs-10 pr10">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
            <div class="col-xs-2 pl0">
                <div id="selectData" class="tags-input"></div>
            </div>
        </div>
    </div>
</div>
</div>
<% } %>
<script>
    var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
        selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
            searchForm: $('#searchForm'),
            autoLoad: false, 
            columnModel: [
                {header:'${text("资产id")}', name:'assetId',  sortable:false, width:150, align:"left"},
                {header:'${text("单位名称")}', name:'agencyName',  sortable:false, width:150, align:"left"},
                {header:'${text("资产编码")}', name:'assetCode',  sortable:false, width:150, align:"left"},
                {header:'${text("资产名称")}', name:'assetName',  sortable:false, width:150, align:"left"},
                {header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
                        return JSON.stringify(row);
                    }}
            ],
            autoGridHeight: function(){
                var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
                $('.tags-input').height($('.ui-jqgrid').height() - 10);
                return height;
            },
            showCheckbox: '${parameter.checkbox}' == 'true',
            multiboxonly: false, // 单击复选框时再多选
            ajaxSuccess: function(data){
                $.each(selectData, function(key, value){
                    dataGrid.dataGrid('setSelectRow', key);
                });
                initSelectTag();
            },
            onSelectRow: function(id, isSelect, event){
                if ('${parameter.checkbox}' == 'true'){
                    if(isSelect){
                        selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
                    }else{
                        delete selectData[id];
                    }
                }else{
                    selectData = {};
                    selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
                }
                initSelectTag();
            },
            onSelectAll: function(ids, isSelect){
                if ('${parameter.checkbox}' == 'true'){
                    for (var i=0; i<ids.length; i++){
                        if(isSelect){
                            selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
                        }else{
                            delete selectData[ids[i]];
                        }
                    }
                }
                initSelectTag();
            },
            ondblClickRow: function(id, rownum, colnum, event){
                if ('${parameter.checkbox}' != 'true'){
                    js.layer.$('#' + window.name).closest('.layui-layer')
                        .find(".layui-layer-btn0").trigger("click");
                }
                initSelectTag();
            }
        });
    function initSelectTag(){
        selectNum = 0;
        var html = [];
        $.each(selectData, function(key, value){
            selectNum ++;
            html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.assetName)+' </span>'
                + '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
        });
        html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
        $('#selectData').empty().append(html.join(''));
    }
    function removeSelectTag(key){
        delete selectData[key];
        dataGrid.dataGrid('resetSelection', key);
        $('#selectNum').html(--selectNum);
        $('#'+key+'_tags-input').remove();
    }
    function getSelectData(){
        return selectData;
    }
    function orgCodeSelectCallback(id, act, index, layero, nodes){
        if (act == 'ok'){
            $('#agencyCodeCode').val(nodes[0].orgCode);
        }
    }

</script>