<% layout('/layouts/default.html', {title: '站内消息', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-speech"></i> ${text('站内消息')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('msg:msgInner:edit')){ %>
					<a href="${ctx}/msg/msgInner/form" class="btn btn-default btnTool" title="${text('新增')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${msgInner}" action="${ctx}/msg/msgInner/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('标题')}：</label>
					<div class="control-inline">
						<#form:input path="msgTitle" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('级别')}：</label>
					<div class="control-inline width-60">
						<#form:select path="contentLevel" dictType="msg_inner_content_level" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('类型')}：</label>
					<div class="control-inline width-60">
						<#form:select path="contentType" dictType="msg_inner_content_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('发送时间')}：</label>
					<div class="control-inline">
						<#form:input path="sendDate_gte" readonly="true" maxlength="20" class="form-control laydate width-date"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd" data-done="sendDate_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="sendDate_lte" readonly="true" maxlength="20" class="form-control laydate width-date"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-60">
						<#form:select path="status" dictType="msg_inner_msg_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table> 
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("标题")}', name:'msgTitle', index:'a.msg_title', width:280, align:"left", frozen:true, formatter: function(val, obj, row, act){
			if(row.status == Global.STATUS_DRAFT){
				return '<a href="${ctx}/msg/msgInner/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑消息")}">'
						+(val||row.id)+'</a>'+(row.isAttac==1?' <i class="fa fa-paperclip"></i>':'');
			}else{
				return '<a href="${ctx}/msg/msgInner/view?id='+row.id+'" class="hsBtnList" data-title="${text("查看消息")}">'
						+(val||row.id)+'</a>'+(row.isAttac==1?' <i class="fa fa-paperclip"></i>':'');
			}
		}},
		{header:'${text("等级")}', name:'contentLevel', index:'a.content_level', width:80, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('msg_inner_content_level')}", val, '${text("未知")}', true);
		}},
		{header:'${text("类型")}', name:'contentType', index:'a.content_type', width:80, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('msg_inner_content_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("发送者")}', name:'sendUserName', index:'a.send_user_name', width:100, align:"center"},
		{header:'${text("发送时间")}', name:'sendDate', index:'a.send_date', width:150, align:"center"},
// 		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:80, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('msg_inner_msg_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			if(row.status == Global.STATUS_DRAFT){
			//# if(hasPermi('msg:msgInner:edit')){
				actions.push('<a href="${ctx}/msg/msgInner/form?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("编辑消息")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/msg/msgInner/delete?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("删除消息")}" data-confirm="${text("确认要删除该消息吗？")}">删除</a>&nbsp;');
			//# }
			}else{
				actions.push('<a href="${ctx}/msg/msgInner/view?id='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("查看消息")}">查看</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>