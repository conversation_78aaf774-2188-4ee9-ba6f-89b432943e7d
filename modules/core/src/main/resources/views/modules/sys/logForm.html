<% layout('/layouts/default.html', {title: '日志详情', libs: ['validate']}){ %>
<link rel="stylesheet" href="${ctxStatic}/modules/sys/logForm.css?${_version}">
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa fa-bug"></i> ${text('日志详情')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${log}" action="${ctx}/sys/log/save" method="post" class="form-horizontal">
			<div class="box-body"><br/>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('日志标题')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="logTitle" maxlength="500" class="form-control required "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('日志类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="logType" dictType="sys_log_type" class="form-control required " />
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('请求地址')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<div class="input-group">
									<span class="input-group-addon control-label">&nbsp;${log.serverAddr} &nbsp;</span>
									<#form:input value="${log.requestUri}" maxlength="255" class="form-control "/>
									<span class="input-group-addon control-label">&nbsp;${log.requestMethod} &nbsp;</span>
				                </div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('请求数据')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="requestParams" rows="1" class="form-control autoHeight"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('操作用户')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="createByName" maxlength="100" class="form-control required "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('操作账号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="createBy" maxlength="100" class="form-control required "/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('业务类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="bizType" maxlength="64" class="form-control "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('业务主键')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="bizKey" maxlength="64" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('操作时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="createDate" dataFormat="datetime" class="form-control required "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('客户端IP')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="remoteAddr" maxlength="255" class="form-control required "/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('用户代理')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:input path="userAgent" maxlength="500" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('设备名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="deviceName" maxlength="100" class="form-control "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('浏览器名')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="browserName" maxlength="100" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('响应时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="executeTimeFormat" maxlength="100" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
				<% if(@Global.YES.equals(log.isException)){ %>
				<div class="form-unit">${text('异常信息')}</div>
				<div class="ml10 mr10 mb10">
					<#form:textarea path="exceptionInfo" rows="20" class="form-control autoHeight"/>
				</div>
				<% } %>
				<% if(isNotBlank(log.diffModifyData)){ %>
				<div class="form-unit">${text('差异修改数据')}</div>
				<div class="ml10 mr10 mb10 modify-log">
					${log.diffModifyData}
				</div>
				<% } %>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> 关 闭</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>
