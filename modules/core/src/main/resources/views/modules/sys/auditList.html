<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */ %>
<% layout('/layouts/default.html', {title: '安全审计', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="nav-tabs-custom nav-main">
		<ul class="nav nav-tabs">
			<li class="active"><a href="${ctx}/sys/audit/list"><i class="fa icon-energy"></i> ${text('账号密码审计')}</a></li>
			<li><a href="${ctx}/sys/audit/userList"><i class="fa icon-book-open"></i> ${text('菜单权限审计')}</a></li>
			<li><a href="${ctx}/sys/audit/menuList"><i class="fa icon-user"></i> ${text('用户权限审计')}</a></li>
		</ul>
		<div class="box-body">
			<#form:form id="searchForm" model="${audit}" action="${ctx}/sys/audit/listData" method="post" class="form-inline"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('类型')}：</label>
					<div class="control-inline">
						<div class="btn-group" id="btnAuditType">
							<button type="button" class="btn btn-default active" data-type="0">${text('全部问题')}</button>
							<button type="button" class="btn btn-default" data-type="1">${text('未修改初始密码')}</button>
							<button type="button" class="btn btn-default" data-type="2" title="${text('密码等级是弱或者很弱的')}">${text('使用简单密码')}</button>
							<button type="button" class="btn btn-default" data-type="3" title="${text('定期未修改密码的账号 {0\} 天内不被审查', @Global.getConfig('sys.user.passwordModifyCycle'))}"><i class="fa icon-question "></i> ${text('定期未修改密码')}</button>
							<button type="button" class="btn btn-default" data-type="4" title="${text('长期未登录的账号 {0\} 天内不被审查', @Global.getConfig('sys.user.passwordModifyCycle'))}"><i class="fa icon-question "></i> ${text('长期未登录')}</button>
							<button type="button" class="btn btn-default" data-type="5">${text('未设置密保')}</button>
						</div>
						<#form:hidden path="auditType" />
					</div>
				</div>
				<div class="inline-block">
					<div class="form-group">
						<label class="control-label">${text('账号')}：</label>
						<div class="control-inline">
							<#form:input path="loginCode" maxlength="100" class="form-control width-90"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('昵称')}：</label>
						<div class="control-inline">
							<#form:input path="userName" maxlength="100" class="form-control width-90"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('机构')}：</label>
						<div class="control-inline width-90">
							<#form:treeselect id="office" title="${text('机构选择')}"
									path="officeCode" labelPath="officeName"
									url="${ctx}/sys/office/treeData" btnClass="btn-sm" allowClear="true" canSelectParent="true"/>
						</div>
					</div>
					<div class="form-group">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
						<button type="button" id="btnExport" class="btn btn-default btn-sm"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</button>
					</div>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
		    <div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [ 
		{header:'${text("登录账号")}', name:'loginCode', index:'u.login_code', width:80, align:"center"},
		{header:'${text("用户昵称")}', name:'userName', index:'u.user_name', width:80, align:"center"},
		{header:'${text("归属机构")}', name:'officeName', index:'o.office_name', width:90, align:"center"},
		{header:'${text("审计结果")}', name:'auditResult', sortable:false, width:235, align:"left"},
		{header:'${text("创建时间")}', name:'createDate', index:'u.create_date', width:100, align:"center"},
	    {header:'${text("密码修改时间")}', name:'pwdUpdateDate',index:'u.pwd_update_date', align:"center", width:100},
	    {header:'${text("最后登录时间")}', name:'lastLoginDate',index:'u.last_login_date', align:"center", width:100},
	    {header:'${text("状态")}', name:'status', index:'u.status', width:50, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
		}},
		{header:'${text("类型")}', name:'userType', index:'u.user_type', width:50, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_user_type')}", val, '无', true);
		}},
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
$('#btnAuditType button').click(function(){
	$('#btnAuditType button').removeClass('active');
	$('#auditType').val($(this).addClass('active').data('type'));
	$('#searchForm').submit();
});
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/sys/audit/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>