<%
// 侧边栏的默认显示样式：1：默认显示侧边栏；2：默认折叠侧边栏
var sidebarStyle = @Global.getConfig('sys.index.sidebarStyle', '1');
var sidebarCollapse = (sidebarStyle == '2' ? 'sidebar-collapse' : '');
var bodyClass = 'fixed noscroll2 sidebar-mini ' + sidebarCollapse;
%>
<% layout('/layouts/default.html', {title: '', bodyClass: bodyClass, libs: ['tabPage', 'layout']}){ %>
<!--[if lte IE 8]><script>window.location.href='${ctxStatic}/upbw/index.html';</script><![endif]-->
<link rel="stylesheet" href="${ctxStatic}/jquery-toastr/2.1/toastr.min.css?${_version}">
<link rel="stylesheet" href="${ctxStatic}/modules/sys/sysIndex.css?${_version}">
<header class="main-header">
	<% include('/include/sysIndex/topMenu.html'){} %>
</header>
<aside class="main-sidebar">
	<% include('/include/sysIndex/leftMenu.html'){} %>
</aside>
<div class="content-wrapper">
	<div id="tabpanel"></div>
</div>
<% } %>
<div class="hide" id="desktopTabPage" data-title="${text('开始')}" data-url="${ctx}${desktopUrl!}"></div>
<!--<div class="hide" id="modifyPasswordTip" data-message="${modifyPasswordTip!}"></div>-->
<script src="${ctxStatic}/jquery-toastr/2.1/toastr.min.js?${_version}"></script>
<script src="${ctxStatic}/jquery-plugins/jquery.slimscroll.js"></script>
<script src="${ctxStatic}/modules/sys/sysIndex.js?${_version}"></script>