<% layout('/layouts/default.html', {title: '用户管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-user"></i> ${text('用户管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<% if(hasPermi('sys:empUser:edit')){ %>
					<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> ${text('导入')}</a>
					<a href="${ctx}/sys/empUser/form?op=add" class="btn btn-default btnTool" title="${text('新增用户')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
				<!-- <div class="btn-group">
					<a href="javascript:" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
						<i class="fa fa-navicon"></i> <span class="caret"></span>
					</a>
					<ul class="dropdown-menu">
						<li><a href="javascript:" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a></li>
						<li><a href="javascript:" id="btnImport"><i class="glyphicon glyphicon-import"></i> ${text('导入')}</a></li>
						<li><a href="javascript:" id="btnSetting"><i class="fa fa-navicon"></i> ${text('设置')}</a></li>
					</ul>
				</div> -->
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${empUser}" action="${ctx}/sys/empUser/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<#form:hidden name="ctrlPermi" value="${ctrlPermi}"/>
				<div class="form-group">
					<label class="control-label">${text('姓名')}：</label>
					<div class="control-inline">
						<#form:input path="userName" maxlength="100" class="form-control width-90"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('部门')}：</label>
					<div class="control-inline width-120">
						<#form:treeselect id="office" title="${text('机构选择')}"
						path="employee.office.officeCode" labelPath="employee.office.officeName"
						url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
						btnClass="btn-sm" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('职级')}：</label>
					<div class="control-inline width-90">
						<#form:select path="employee.establishmentType" dictType="ob_establishment_type" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('入职时间')}：</label>
					<div class="control-inline width-200">
						<#form:input path="employee.joinedDate_gte" maxlength="20" class="form-control laydate"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				-
				<div class="form-group">
					<div class="control-inline width-200">
						<#form:input path="employee.joinedDate_lte" maxlength="20" class="form-control laydate"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<!-- <div class="form-row"></div> -->
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					<button type="button" class="btn btn-link btn-sm btnFormMore"><i class="fa fa-angle-double-down"></i></button>
				</div>
				<div class="form-more">
					<div class="form-group">
						<label class="control-label">${text('手机')}：</label>
						<div class="control-inline">
							<#form:input path="mobile" maxlength="100" class="form-control width-90"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('邮箱')}：</label>
						<div class="control-inline">
							<#form:input path="email" maxlength="300" class="form-control width-90"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('电话')}：</label>
						<div class="control-inline">
							<#form:input path="phone" maxlength="100" class="form-control width-90"/>
						</div>
					</div>
					<div class="inline-block">
						<div class="form-group">
							<label class="control-label">${text('角色')}：</label>
							<div class="control-inline width-90">
								<#form:select name="roleCode" items="${roleList}"
									itemLabel="roleName" itemValue="roleCode" blankOption="true" class="form-control isQuick"/>
							</div>
						</div>
					</div>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("登录账号")}', name:'loginCode', index:'a.login_code', width:125, align:"center", frozen:true, fixed:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/sys/empUser/form?userCode='+row.userCode+'&op=edit" class="hsBtnList" data-title="${text("编辑用户")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("姓名")}', name:'userName', index:'userName', width:125, align:"center"},
		{header:'${text("性别")}', name:'sex', index:'sex', width:125, align:"center", formatter: function (val, obj, row, act) {
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_user_sex')}", val, '${text("未知")}', true);
		}},
		{header:'${text("年龄")}', name:'employee.age', index:'e.age', width:135, align:"center"},
		{header:'${text("手机号")}', name:'mobile', index:'mobile', width:135, align:"center"},
		{header:'${text("所属部门")}', name:'employee.office.officeName', index:'o.office_name', width:135, align:"center"},
		{header:'${text("职级")}', name:'employee.establishmentType', index:'e.establishment_type', width:135, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('ob_establishment_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("入职日期")}', name:'employee.joinedDate', index:'e.joined_date', width:135, align:"center"},
		{header:'${text("状态")}', name:'status', index:'status', width:60, align:"center", frozen:true, formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('sys:empUser:edit')){
				actions.push('<a href="${ctx}/sys/empUser/form?userCode='+row.userCode+'&op=edit" class="hsBtnList" title="${text("编辑用户")}">编辑</a>&nbsp;');
			//# }
			//# if(hasPermi('sys:empUser:updateStatus')){
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/sys/empUser/disable?userCode='+row.userCode+'" class="btnList" title="${text("停用用户")}" data-confirm="${text("确认要停用该用户吗？")}">停用</a>&nbsp;');
				}else if (row.status == Global.STATUS_DISABLE || row.status == Global.STATUS_AUDIT){
					actions.push('<a href="${ctx}/sys/empUser/enable?userCode='+row.userCode+'" class="btnList" title="${text("启用用户")}" data-confirm="${text("确认要启用该用户吗？")}">启用</a>&nbsp;');
				}else if (row.status == Global.STATUS_FREEZE){
					actions.push('<a href="${ctx}/sys/empUser/enable?userCode='+row.userCode+'&freeze=true" class="btnList" title="${text("解冻用户")}" data-confirm="${text("确认要解冻该用户吗？")}">启用</a>&nbsp;');
				}
			//# }
			//# if(hasPermi('sys:empUser:edit')){
				actions.push('<a href="${ctx}/sys/empUser/delete?userCode='+row.userCode+'" class="btnList" title="${text("删除用户")}" data-confirm="${text("确认要删除该用户吗？")}">删除</a>&nbsp;');
			//# }
			//# if(hasPermi('sys:empUser:authRole,sys:empUser:authDataScope,sys:empUser:resetpwd', 'or')){
				actions.push('<a href="javascript:" class="btnMore" title="${text("更多操作")}"><i class="fa fa-chevron-circle-right"></i></a>&nbsp;');
				actions.push('<div class="moreItems">');
				//# if(hasPermi('sys:empUser:authRole')){
					actions.push('<a href="${ctx}/sys/empUser/form?userCode='+row.userCode+'&op=auth" class="btn btn-default btn-xs btnList" title="${text("用户分配角色")}"><i class="fa fa-check-square-o"></i> ${text("分配角色")}</a>&nbsp;');
				//# }
				//# if(hasPermi('sys:empUser:authDataScope')){
					actions.push('<a href="${ctx}/sys/empUser/formAuthDataScope?userCode='+row.userCode+'" class="btn btn-default btn-xs btnList" title="${text("用户分配数据权限")}"><i class="fa fa-check-circle-o"></i> ${text("数据权限")}</a>&nbsp;');
				//# }
				//# if(hasPermi('sys:empUser:resetpwd')){
					actions.push('<a href="${ctx}/sys/empUser/resetpwd?userCode='+row.userCode+'" class="btn btn-default btn-xs btnList" title="${text("用户密码重置")}" data-confirm="${text("确认要将该用户密码重置到初始状态吗？")}"><i class="fa fa-key"></i> ${text("重置密码")}</a>&nbsp;');
				//# }
				//    actions.push('<a href="${ctx}/sys/empUser/dataSyncKj?userCode='+row.userCode+'" class="btn btn-default btn-xs btnList" title="${text("空间数据同步")}" data-confirm="${text("确认同步该用户空间数据吗？")}"><i class="fa fa-key"></i> ${text("数据同步")}</a>&nbsp;');
				//# if(hasPermi('sys:empUser:updateStatus')){
				// 	if (row.status == Global.STATUS_NORMAL){
				// 		actions.push('<a href="${ctx}/sys/empUser/disable?userCode='+row.userCode+'&freeze=true" class="btn btn-default btn-xs btnList" title="${text("冻结用户")}" data-confirm="${text("确认要冻结该用户吗？")}">停用 ${text("冻结用户")}</a>&nbsp;');
				// 	}
				//# }
				actions.push('</div>');
			//# }
			return actions.join('');
		}}
	],
	frozenCols: false,
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){

	}
});
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/sys/empUser/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入用户数据")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		success: function(layero, index){
			layero.find('input[type="checkbox"]').iCheck();
		},
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
// emitter.on('emp-user-list-page', page);
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/sys/empUser/importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5">
				<#form:checkbox name="updateSupport" label="${text('是否更新已经存在的用户数据')}" class="form-control"
					title="${text('如果用户编码已经存在，更新这条数据。')}"/> &nbsp;
				<a href="${ctx}/sys/empUser/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
			<font color="red" class="pull-left mt10">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</font>
		</div>
	</div>
</form>
//--></script>