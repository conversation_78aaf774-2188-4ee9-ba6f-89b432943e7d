<% layout('/layouts/default.html', {title: '字典数据管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header" style="display:block">
			<div class="box-title">
				<i class="fa icon-social-dropbox"></i> ${text('字典数据')}（${dictData.dictType}）
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="${text('刷新')}"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="${text('展开一级')}"><i class="fa fa-angle-double-down"></i> ${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a>
				<% if(hasPermi('sys:dictData:edit')){ %>
					<a href="${ctx}/sys/dictData/form?dictType=${dictData.dictType}" class="btn btn-default btnTool" title="${text('新增字典数据')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dictData}" action="${ctx}/sys/dictData/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('字典标签')}：</label>
					<div class="control-inline">
						<#form:input path="dictLabelRaw" maxlength="100" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字典键值')}：</label>
					<div class="control-inline">
						<#form:input path="dictValue" maxlength="500" class="form-control"/>
					</div>
				</div>
				<div class="form-group hide">
					<label class="control-label">${text('字典类型')}：</label>
					<div class="control-inline">
						<#form:input path="dictType" maxlength="100" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('系统内置')}：</label>
					<div class="control-inline width-60">
						<#form:select path="isSys" dictType="sys_yes_no" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-60">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>			
			<table id="dataGrid"></table>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("字典标签")}', name:'dictLabelRaw', index:'a.dict_label', width:200, align:"left", frozen:true, formatter: function(val, obj, row, act){
			var icon = row.dictIcon, iconHtml = '';
			if (icon && icon != ''){
				if (icon.indexOf('://') != -1){
					iconHtml = '<img src="' + icon + '" width="20" height="20">';
				}else if (icon.indexOf('/') == 0){
					iconHtml = '<img src="' + ctxPath + icon + '" width="20" height="20">';
				}else{
					iconHtml = '<i class="fa fa-fw ' + icon + '"></i>&nbsp;';
				}
			}
			return '<a href="${ctx}/sys/dictData/form?dictCode='+row.dictCode+'" class="btnList '+row.cssClass+'" data-title="${text("编辑字典数据")}" style="'
					+row.cssStyle+'">&nbsp;'+iconHtml+'&nbsp; '+(val||row.id)+'</a>';
		}},
		{header:'${text("字典键值")}', name:'dictValue', index:'a.dict_value', width:200, align:"left"},
		{header:'${text("排序号")}', name:'treeSort', width:63, align:"center", fixed:true},
		{header:'${text("系统内置")}', name:'isSys', index:'a.is_sys', width:80, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_yes_no')}", val, '未知', true);
		}},
		{header:'${text("CSS类名")}', name:'cssClass', index:'a.css_class', width:100, align:"left"},
		{header:'${text("CSS样式")}', name:'cssStyle', index:'a.css_style', width:100, align:"left"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:200, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:60, fixed:true, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('sys:dictData:edit')){
				actions.push('<a href="${ctx}/sys/dictData/form?dictCode='+row.dictCode+'" class="hsBtnList" title="${text("编辑字典数据")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/sys/dictData/disable?dictCode='+row.dictCode+'" class="btnList" title="${text("停用字典数据")}" data-confirm="${text("确认要停用该字典数据吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/sys/dictData/enable?dictCode='+row.dictCode+'" class="btnList" title="${text("启用字典数据")}" data-confirm="${text("确认要启用该字典数据吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/sys/dictData/delete?dictCode='+row.dictCode+'" class="btnList" title="${text("删除字典数据")}" data-confirm="${text("确认要删除该字典数据及所有子字典数据吗？")}" data-deltreenode="'+row.id+'">删除</a>&nbsp;');
				actions.push('<a href="${ctx}/sys/dictData/form?parentCode='+row.id+'&dictType='+row.dictType+'" class="hsBtnList" title="${text("新增下级字典数据")}"><i class="fa fa-plus-square"></i></a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'dictLabelRaw,dictValue,dictType,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>