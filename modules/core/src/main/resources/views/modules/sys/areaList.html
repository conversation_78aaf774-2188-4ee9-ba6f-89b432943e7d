<% layout('/layouts/default.html', {title: '行政区划', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-map"></i> ${text('行政区划')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="查询"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="刷新"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="展开一级"><i class="fa fa-angle-double-down"></i> ${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="折叠全部"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a>
				<% if(hasPermi('sys:area:edit')){ %>
					<a href="${ctx}/sys/area/form" class="btn btn-default btnTool" title="${text('新增区域')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${area}" action="${ctx}/sys/area/listPageData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('区域代码')}：</label>
					<div class="control-inline">
						<#form:input path="areaCode" maxlength="100" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('区域名称')}：</label>
					<div class="control-inline">
						<#form:input path="areaName" maxlength="100" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-60">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>			
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("区域名称")}', name:'areaName', index:'a.area_name', width:230, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '( '+row.areaCode+' ) '+'<a href="${ctx}/sys/area/form?areaCode='+row.areaCode+'" class="hsBtnList" data-title="${text("编辑区域")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("区域类型")}', name:'areaType', index:'a.area_type', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_area_type')}", val, '未知', true);
		}},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:200, align:"left"},
		{header:'${text("排序号")}', name:'treeSort', index:'a.tree_sort', width:100, align:"center"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:80, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:130, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('sys:area:edit')){
				actions.push('<a href="${ctx}/sys/area/form?areaCode='+row.areaCode+'" class="btnList btn btn-link info btn-xs" title="${text("编辑区域")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/sys/area/disable?areaCode='+row.areaCode+'" class="btnList btn btn-link info btn-xs" title="${text("停用区域")}" data-confirm="${text("确认要停用该区域吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/sys/area/enable?areaCode='+row.areaCode+'" class="btnList btn btn-link info btn-xs" title="${text("启用区域")}" data-confirm="${text("确认要启用该区域吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/sys/area/delete?areaCode='+row.areaCode+'" class="btnList btn btn-link info btn-xs" title="${text("删除区域")}" data-confirm="${text("确认要删除该区域及所有子区域吗？")}" data-deltreenode="'+row.id+'">删除</a>&nbsp;');
				actions.push('<a href="${ctx}/sys/area/form?parentCode='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("新增下级区域")}">新增下级</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'areaCode,areaName,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		if ($('#areaCode').val() != ''){
			$('#areaCode').val('');
			$('#btnExpandTreeNode').click();
		}
	}
});
</script>