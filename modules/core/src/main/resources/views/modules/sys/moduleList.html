<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */ %>
<% layout('/layouts/default.html', {title: '模块管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-grid f14"></i> ${text('模块管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('sys:module:edit')){ %>
					<a href="${ctx}/sys/module/form" class="btn btn-default btnTool" title="${text('新增模块')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${module}" action="${ctx}/sys/module/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('模块名称')}：</label>
					<div class="control-inline">
						<#form:input path="moduleName" maxlength="100" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('主类全名')}：</label>
					<div class="control-inline">
						<#form:input path="mainClassName" maxlength="500" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-60">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>			
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
    	{header:'${text("模块名称")}', name:'moduleName', index:'a.module_name', width:200, align:"center", frozen:true, formatter: function(val, obj, row, act){
   			return '<a href="${ctx}/sys/module/form?moduleCode='+row.moduleCode+'" class="hsBtnList" data-title="${text("编辑模块")}">'+(val||row.id)+'</a>';
   		}},
		{header:'${text("模块编码")}', name:'moduleCode', index:'a.module_code', width:200, align:"center"},
		{header:'${text("模块描述")}', name:'description', index:'a.description', width:300, align:"left"},
		{header:'${text("版本")}', name:'currentVersion', index:'a.current_version', width:100, align:"center"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:60, sortable:false, fixed:true, align:"center", formatter: function(val, obj, row, act){
			return row.isLoader ? js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '<font color=red>${text("未知")}</font>', true) : '<font color=red>${text("未安装")}</font>';
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('sys:module:edit')){
				actions.push('<a href="${ctx}/sys/module/form?moduleCode='+row.moduleCode+'" class="hsBtnList" title="${text("编辑模块")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/sys/module/disable?moduleCode='+row.moduleCode+'" class="btnList" title="${text("停用模块")}" data-confirm="${text("确认要停用该模块吗？")}">停用</a>&nbsp;');
				}else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/sys/module/enable?moduleCode='+row.moduleCode+'" class="btnList" title="${text("启用模块")}" data-confirm="${text("确认要启用该模块吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/sys/module/delete?moduleCode='+row.moduleCode+'" class="btnList" title="${text("删除模块")}" data-confirm="${text("确认要删除该模块吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>