<% layout('/layouts/default.html', {title: '字典数据管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-social-dropbox"></i> ${text(dictData.isNewRecord ? '新增字典' : '编辑字典')}（${dictData.dictType}）
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${dictData}" action="${ctx}/sys/dictData/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('上级字典')}：</label>
							<div class="col-sm-8">
								<#form:treeselect id="parent" title="${text('上级字典')}"
									path="parent.id" labelPath="parent.dictLabelRaw" 
									url="${ctx}/sys/dictData/treeData?excludeCode=${dictData.id}&dictType=${dictData.dictType}&isShowRawName=true"
									class="" allowClear="true" canSelectRoot="true" canSelectParent="true" isReturnValue="false"/>
							</div>
						</div>
					</div>
				</div>
				<#form:hidden path="dictCode"/>
				<#form:hidden path="dictType"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('字典标签')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="dictLabelRaw" maxlength="100" class="form-control required "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('字典键值')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="dictValue" maxlength="500" class="form-control required "/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="当前字典树结构层次级别的排序号">
								<span class="required ">*</span> ${text('排序号')}：<i class="fa icon-question "></i></label>
							<div class="col-sm-8">
								<#form:input path="treeSort" maxlength="9" class="form-control required digits"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('系统内置')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:radio path="isSys" dictType="sys_yes_no" class="form-control required " />
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="工具提示，如select鼠标放到option上去会显示该描述信息">
								<span class="required hide">*</span> ${text('字典描述')}：<i class="fa icon-question "></i></label>
							<div class="col-sm-8">
								<#form:input path="description" maxlength="500" class="form-control "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="下拉选项的图标。支持选择字体图标，也支持设置一个 img 地址，如：http://host/js/img/icon.jpg 或  /img/icon.jpg">
								${text('选项图标')}：<i class="fa icon-question "></i></label>
							<div class="col-sm-8">
								<#form:iconselect path="dictIcon" class=""/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('其它信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="CSS类名（如：class=“red”, 请填写：red）">
								<span class="required hide">*</span> ${text('CSS类名')}：<i class="fa icon-question "></i></label>
							<div class="col-sm-8">
								<#form:input path="cssClass" maxlength="500" class="form-control "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="CSS样式（如：style=“color:red”, 请填写：color:red）">
								<span class="required hide">*</span> ${text('CSS样式')}：<i class="fa icon-question "></i></label>
							<div class="col-sm-8">
								<#form:input path="cssStyle" maxlength="500" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
				<#form:extend collapsed="true" />
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('sys:dictData:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					(contentWindow.win||contentWindow).$('#dataGrid').dataGrid('refreshTreeChildren',
							$('#parentCode').val(), '${dictData.id}');
				});
			}
		}, "json");
    }
});
</script>