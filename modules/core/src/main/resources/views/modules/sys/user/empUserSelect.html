<% layout('/layouts/default.html', {title: '用户选择', libs: ['layout', 'zTree', 'dataGrid']}){ %>
<div class="ui-layout-west">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title dropdown">
				<div class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">
					<i class="fa icon-grid"></i> <span id="queryTypeName">${text('按组织查询123')}</span> <b class="caret"></b>
				</div>
				<ul class="dropdown-menu">
					<li><a href="javascript:" onclick="loadTree('office');"> <i class="fa fa-angle-right"></i> ${text('按组织查询')}</a></li>
					<li><a href="javascript:" onclick="loadTree('post');"> <i class="fa fa-angle-right"></i> ${text('按岗位查询')}</a></li>
					<li><a href="javascript:" onclick="loadTree('role');"> <i class="fa fa-angle-right"></i> ${text('按角色查询')}</a></li>
					<li><a href="javascript:" onclick="loadTree('userType');"> <i class="fa fa-angle-right"></i> ${text('按用户类型')}</a></li>
				</ul>
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" id="btnExpand" title="${text('展开')}" style="display:none;"><i class="fa fa-chevron-up"></i></button>
				<button type="button" class="btn btn-box-tool" id="btnCollapse" title="${text('折叠')}"><i class="fa fa-chevron-down"></i></button>
				<button type="button" class="btn btn-box-tool" id="btnRefresh" title="${text('刷新')}"><i class="fa fa-refresh"></i></button>
			</div>
		</div>
		<div class="ui-layout-content">
			<div id="tree" class="ztree"></div>
		</div>
	</div>
</div>
<div class="ui-layout-center">
	<div class="main-content">
		<div class="box box-main">
			<div class="box-body pb0">
				<#form:form id="searchForm" model="${empUser}" action="${ctx}/sys/empUser/listData" method="post" class="form-inline "
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
					<#form:hidden name="status" value="${isNotBlank(empUser.status) ? empUser.status : '0'}" class="isReset"/>
					<#form:hidden name="isAll" value="${parameter.isAll}" class=""/>
					<#form:hidden id="postCode" name="employee.postCode" class="isReset"/>
					<#form:hidden id="roleCode" name="roleCode" class="isReset"/>
					<#form:hidden id="userType" name="userType" class="isReset"/>
					<#form:hidden id="hasUsedRoom" name="hasUsedRoom" value="${empUser.hasUsedRoom}" class=""/>
					<div class="form-group">
						<label class="control-label">${text('昵称')}：</label>
						<div class="control-inline">
							<#form:input path="userName" maxlength="100" class="form-control width-90"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('姓名')}：</label>
						<div class="control-inline">
							<#form:input path="refName" maxlength="100" class="form-control width-90"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('机构')}：</label>
						<div class="control-inline width-90">
							<#form:treeselect id="office" title="${text('机构选择')}"
									path="employee.office.officeCode" labelPath="employee.office.officeName" 
									url="${ctx}/sys/office/treeData?isAll=${parameter.isAll}" btnClass="btn-sm" allowClear="true" canSelectParent="true"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('公司')}：</label>
						<div class="control-inline width-90">
							<#form:treeselect id="company" title="${text('公司选择')}"
									path="employee.company.companyCode" labelPath="employee.company.companyName" 
									url="${ctx}/sys/company/treeData" btnClass="btn-sm" allowClear="true" canSelectParent="true"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('手机')}：</label>
						<div class="control-inline">
							<#form:input path="mobile" maxlength="100" class="form-control width-90"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('身份证号')}：</label>
						<div class="control-inline">
							<#form:input path="idNum" maxlength="100" class="form-control width-90"/>
						</div>
					</div>
					<div class="form-group">
						<button type="submit" class="btn btn-primary btn-sm">查询</button>
						<button type="reset" class="btn btn-default btn-sm isQuick">重置</button>
					</div>
				</#form:form>
				<div class="col-xs-10 p0 pr10">
					<table id="dataGrid"></table>
					<div id="dataGridPage"></div>
				</div>
				<% if (!empUser.isHasUsedRoom){ %>
					<div class="col-xs-2 p0">
						<div id="selectData" class="tags-input"></div>
					</div>
				<% } %>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化布局
$('body').layout({
	west__size: window.lang == 'en' ? 225 : 200,
	onresize_end: function(){
		$('#dataGrid').dataGrid('resize');
	}
});
//# // 树结构初始化加载
var setting = {view:{selectedMulti:false},data:{simpleData:{enable:true}},
	callback:{onClick:function(event, treeId, treeNode){
		tree.expandNode(treeNode);
		//$('button[type=reset]').click();
		$('input.isReset').val('');
		if (queryType == 'userType'){
			$('#userType').val(treeNode.id);
			if (treeNode.id == 'employee'){
				$('#searchForm').attr('action', '${ctx}/sys/empUser/listData');
			}else{
				$('#searchForm').attr('action', '${ctx}/sys/user/listData');
			}
		}else{
			if (!queryType || queryType == 'office'){
				$('#officeCode').val(treeNode.id);
				$('#officeName').val(treeNode.name);
			} else if (queryType == 'post'){
				$('#postCode').val(treeNode.id);
			} else if (queryType == 'role'){
				$('#roleCode').val(treeNode.id);
			} 
		}
		page(1);
	}}
}, queryType, url, tree, loadTree = function(queryType){
	window.queryType = queryType;
	if (queryType == 'userType'){
		$('#queryTypeName').text('${text("按用户类型")}');
		tree = $.fn.zTree.init($("#tree"), setting, [
			//# for(dict in @DictUtils.getDictList('sys_user_type')){
			{id: '${dict.dictValue}', name: '${text(dict.dictLabel + "类型")}'},
			//# }
			{id: 'none', name: '${text("系统管理员")}'}
		]);
		tree.expandAll(true);
	}else{
		if (!queryType || queryType == 'office'){
			$('#queryTypeName').text('${text("按组织查询")}');
			url = "${ctx}/sys/office/treeData?isAll=${parameter.isAll}&___t=" + new Date().getTime();
		} else if (queryType == 'post'){
			$('#queryTypeName').text('${text("按岗位查询")}');
			url = "${ctx}/sys/post/treeData?___t=" + new Date().getTime();
		} else if (queryType == 'role'){
			$('#queryTypeName').text('${text("按角色查询")}');
			url = "${ctx}/sys/role/treeData?isAll=${parameter.isAll}&userType=__all&___t=" + new Date().getTime();
		}
		js.ajaxSubmit(url, {ctrlPermi:'1'/*1拥有的权限 2管理的权限*/}, function(data){
			tree = $.fn.zTree.init($("#tree"), setting, data);
			var level = -1, nodes;
			while (++level <= 1) {
				nodes = tree.getNodesByParam("level", level);
				if (nodes.length > 10) { break; }
				for(var i=0; i<nodes.length; i++) {
		 			tree.expandNode(nodes[i], true, false, false);
				}
			}
		}, null, null, js.text('loading.message'));
	}
};loadTree();
//# // 工具栏按钮绑定
$('#btnExpand').click(function(){
	tree.expandAll(true);
	$(this).hide();
	$('#btnCollapse').show();
});
$('#btnCollapse').click(function(){
	tree.expandAll(false);
	$(this).hide();
	$('#btnExpand').show();
});
$('#btnRefresh').click(function(){
	loadTree();
});
//# // 加载用户列表
var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
	selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("员工姓名")}', name:'refName', index:'a.ref_name', width:200, align:"center"},
		{header:'${text("归属机构")}', name:'employee.office.officeName', index:'o.office_name', width:200, align:"center"},
		{header:'${text("岗位编制")}', name:'employee.establishmentType', index:'o.establishmentType', width:200, align:"center", formatter: function (val, obj, row, act) {
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_establishment_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("手机号码")}', name:'mobile', index:'a.mobile', width:200, align:"center"},
		// {header:'${text("身份证号")}', name:'idNum', index:'a.id_num', width:200, align:"center"},
		{header:'${text("办公用房核定面积")}', name:'employee.officeApprovedConfig.area', index:'employee.officeApprovedConfig.area', width:200, align:"center"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:200, align:"center"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:140, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
		}},
		{header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
			return JSON.stringify(row);
		}}
	],
// 	autoGridWidthFix: 100,
	autoGridHeight: function(){
		var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 78;
		$('.tags-input').height($('.ui-jqgrid').height() - 10);
		return height;
	},
	showCheckbox: '${parameter.checkbox}' == 'true',
	multiboxonly: false, // 单击复选框时再多选
	ajaxSuccess: function(data){
		$.each(selectData, function(key, value){
			dataGrid.dataGrid('setSelectRow', key);
		});
		initSelectTag();
	},
	onSelectRow: function(id, isSelect, event){
		if ('${parameter.checkbox}' == 'true'){
			if(isSelect){
				selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
			}else{
				delete selectData[id];
			}
		}else{
			selectData = {};
			selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
		}
		initSelectTag();
	},
	onSelectAll: function(ids, isSelect){
		if ('${parameter.checkbox}' == 'true'){
			for (var i=0; i<ids.length; i++){
				if(isSelect){
					selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
				}else{
					delete selectData[ids[i]];
				}
			}
		}
		initSelectTag();
	},
	ondblClickRow: function(id, rownum, colnum, event){
		if ('${parameter.checkbox}' != 'true'){
			js.layer.$('#' + window.name).closest('.layui-layer')
				.find(".layui-layer-btn0").trigger("click");
		}
		initSelectTag();
	}
});
function initSelectTag(){
	selectNum = 0;
	var html = [];
	$.each(selectData, function(key, value){
		selectNum ++;
		html.push('<span class="tag" id="'+key+'_tags-input"><span>'+value.userName+' </span>'
			+ '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
	});
	html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
	$('#selectData').empty().append(html.join(''));
}
function removeSelectTag(key){
	delete selectData[key];
	dataGrid.dataGrid('resetSelection', key);
	$('#selectNum').html(--selectNum);
	$('#'+key+'_tags-input').remove();
}
function getSelectData(){
	return selectData;
}
</script>
