<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */ %>
<% layout('/layouts/default.html', {title: '参数设置', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-wrench"></i> ${text(config.isNewRecord ? '新增参数' : '编辑参数')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${config}" action="${ctx}/sys/config/save" method="post" class="form-horizontal">
			<#form:hidden path="id" />
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">
								<span class="required">*</span> ${text('参数名称')}：</label>
							<div class="col-sm-8">
								<#form:input path="configName" maxlength="100" class="form-control required" 
									readonly="${!user().superAdmin}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">
								<span class="required">*</span> ${text('参数键名')}：</label>
							<div class="col-sm-8">
								<#form:input path="configKey" maxlength="100" class="form-control required"
									readonly="${!user().superAdmin}"
									remote="${ctx}/sys/config/checkConfigKey?oldConfigKey=${config.configKey}"
									data-msg-remote="${text('参数键名已存在')}"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								${text('参数键值')}：</label>
							<div class="col-sm-8">
								<#form:textarea path="configValue" rows="4" maxlength="2000" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('系统参数')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:radio path="isSys" dictType="sys_yes_no" class="form-control required " />
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">${text('参数描述')}：</label>
							<div class="col-sm-8">
								<#form:textarea path="remarks" rows="3" maxlength="300" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('sys:config:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
	}
});
</script>