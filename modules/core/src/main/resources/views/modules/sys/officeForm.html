<% layout('/layouts/default.html', {title: '机构管理', libs: ['validate', 'fileupload', 'dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-grid"></i> ${text(office.isNewRecord ? '新增机构' : '编辑机构')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${office}" action="${ctx}/sys/office/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('上级单位')}：</label>
							<div class="col-sm-8">
								<#form:treeselect id="parent" title="${text('上级机构')}"
									path="parent.id" labelPath="parent.officeName" 
									url="${ctx}/sys/office/treeData?excludeCode=${office.id}&ctrlPermi=${ctrlPermi}"
									class="" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('单位名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="officeName" maxlength="100" class="form-control required "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('单位代码')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:hidden path="isNewRecord"/>
								<#form:hidden path="officeCode"/>
								<#form:input path="viewCode" maxlength="64" readonly="${!office.isNewRecord}" class="form-control required abc"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
<!--					<div class="col-xs-6">-->
<!--						<div class="form-group">-->
<!--							<label class="control-label col-sm-4" title="">-->
<!--								<span class="required hide">*</span> ${text('单位全称')}：<i class="fa icon-question hide"></i></label>-->
<!--							<div class="col-sm-8">-->
<!--								<#form:input path="fullName" maxlength="200" class="form-control "/>-->
<!--							</div>-->
<!--						</div>-->
<!--					</div>-->
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('排序号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="treeSort" maxlength="9" class="form-control required digits"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('机关类别')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="officeType" dictType="sys_office_type" class="form-control required " />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('代码证号')}：

								<i class="fa fa-question-circle" data-toggle="tooltip" title="统一社会信用代码"></i>
							</label>
							<div class="col-sm-8">
								<#form:input path="codeCertificateNumber" maxlength="256" class="form-control required abc"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('机关性质')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="officeNature" dictType="ob_office_nature" class="form-control required " />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('技术业务用房核定面积')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="technicalBusinessArea" maxlength="256" dataFormat="number2" defaultValue="0" class="form-control required number"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="unit" dictType="ob_is_unit" class="form-control required " />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('行政地区')}：<i class="fa icon-question hide"></i>
							</label>
							<div class="col-sm-8">
								<#form:treeselect id="region" title="行政区划"
									path="region" labelPath="area.treeNames"
									url="${ctx}/sys/area/treeData?parentCode=0"
									class="required" allowClear="true" returnFullName="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">${text('技术业务用房面积核定材料')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${office.id}" bizType="technical_business_premises_area_file"
								uploadType="all" class="" readonly="false" preview="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">
					${text('编制人数')}
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('编制人数')}：
								<i class="fa fa-question-circle" data-toggle="tooltip" title="自动计算：当前机关类型下所有职级人数之和"></i>
							</label>
							<div class="col-sm-8">
								<#form:input id="totalStaff" path="totalStaff" readonly="true" maxlength="100" value="0" class="form-control required "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('编外人数')}：
<!--								<i class="fa fa-question-circle" data-toggle="tooltip" title="自动计算：当前单位下岗位编制为编制外的人员总数"></i>-->
							</label>
							<div class="col-sm-8">
								<#form:input path="externalStaff" name="externalStaff" maxlength="100" value="0" class="form-control required "/>
							</div>
						</div>
					</div>
				</div>

				<table class="table-form" id="establishmentTable">
					<thead>
						<tr>
							<!-- 中央机关 -->
							<th data-group="central" class="hide">${text("部级正职")}</th>
							<th data-group="central" class="hide">${text("部级副职")}</th>
							<th data-group="central" class="hide">${text("正司(局)级")}</th>
							<th data-group="central" class="hide">${text("副司(局)级")}</th>
							<th data-group="central" class="hide">${text("处级")}</th>
							<th data-group="central" class="hide">${text("处级以下")}</th>

							<!-- 省级机关 -->
							<th data-group="province" class="hide">${text("省级正职")}</th>
							<th data-group="province" class="hide">${text("省级副职")}</th>
							<th data-group="province" class="hide">${text("正厅(局)级")}</th>
							<th data-group="province" class="hide">${text("副厅(局)级")}</th>
							<th data-group="province" class="hide">${text("正处级")}</th>
							<th data-group="province" class="hide">${text("副处级")}</th>
							<th data-group="province" class="hide">${text("处级以下")}</th>

							<!-- 市级机关. -->
							<th data-group="municipal" class="hide">${text("市级正职")}</th>
							<th data-group="municipal" class="hide">${text("市级副职")}</th>
							<th data-group="municipal" class="hide">${text("正局(处)级")}</th>
							<th data-group="municipal" class="hide">${text("副局(处)级")}</th>
							<th data-group="municipal" class="hide">${text("局(处)级以下")}</th>
							<!-- 县级机关. -->
							<th data-group="county" class="hide">${text("县级正职")}</th>
							<th data-group="county" class="hide">${text("县级副职")}</th>
							<th data-group="county" class="hide">${text("正科级")}</th>
							<th data-group="county" class="hide">${text("副科级")}</th>
							<th data-group="county" class="hide">${text("科级以下")}</th>
							<!-- 乡级机关. -->
							<th data-group="township" class="hide">${text("乡级正职")}</th>
							<th data-group="township" class="hide">${text("乡级副职")}</th>
							<th data-group="township" class="hide">${text("乡级以下")}</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<!-- 中央机关 -->
							<td data-group="central" class="hide">
								<#form:input name="ministerPositive" path="officeEstablishment.ministerPositive" class="form-control digits"/>
							</td>
							<td data-group="central" class="hide">
								<#form:input name="ministerDeputy" path="officeEstablishment.ministerDeputy" class="form-control digits"/>
							</td>
							<td data-group="central" class="hide">
								<#form:input name="departmentDirector" path="officeEstablishment.departmentDirector" class="form-control digits"/>
							</td>
							<td data-group="central" class="hide">
								<#form:input name="deputyDepartmentDirector" path="officeEstablishment.deputyDepartmentDirector" class="form-control digits"/>
							</td>
							<td data-group="central" class="hide">
								<#form:input name="divisionLevel" path="officeEstablishment.divisionLevel" class="form-control digits"/>
							</td>
							<td data-group="central" class="hide">
								<#form:input name="belowDivisionLevel" path="officeEstablishment.belowDivisionLevel" class="form-control digits"/>
							</td>

							<!-- 省级机关 -->
							<td data-group="province" class="hide">
								<#form:input path="officeEstablishment.provincePositive" class="form-control digits"/>
							</td>
							<td data-group="province" class="hide">
								<#form:input path="officeEstablishment.provinceDeputy" class="form-control digits"/>
							</td>
							<td data-group="province" class="hide">
								<#form:input path="officeEstablishment.bureauDirector" class="form-control digits"/>
							</td>
							<td data-group="province" class="hide">
								<#form:input path="officeEstablishment.deputyBureauDirector" class="form-control digits"/>
							</td>
							<td data-group="province" class="hide">
								<#form:input path="officeEstablishment.divisionChief" class="form-control digits"/>
							</td>
							<td data-group="province" class="hide">
								<#form:input path="officeEstablishment.deputyDivisionChief" class="form-control digits"/>
							</td>
							<td data-group="province" class="hide">
								<#form:input path="officeEstablishment.belowDivisionChief" class="form-control digits"/>
							</td>

							<!-- 市级机关. -->
							<td data-group="municipal" class="hide">
								<#form:input path="officeEstablishment.municipalPositive" class="form-control digits"/>
							</td>
							<td data-group="municipal" class="hide">
								<#form:input path="officeEstablishment.municipalDeputy" class="form-control digits"/>
							</td>
							<td data-group="municipal" class="hide">
								<#form:input path="officeEstablishment.municipalBureauDirector" class="form-control digits"/>
							</td>
							<td data-group="municipal" class="hide">
								<#form:input path="officeEstablishment.municipalDeputyBureauDirector" class="form-control digits"/>
							</td>
							<td data-group="municipal" class="hide">
								<#form:input path="officeEstablishment.belowMunicipalBureau" class="form-control digits"/>
							</td>
							<!-- 县级机关. -->
							<td data-group="county" class="hide">
								<#form:input path="officeEstablishment.countyPositive" class="form-control digits"/>
							</td>
							<td data-group="county" class="hide">
								<#form:input path="officeEstablishment.countyDeputy" class="form-control digits"/>
							</td>
							<td data-group="county" class="hide">
								<#form:input path="officeEstablishment.sectionChief" class="form-control digits"/>
							</td>
							<td data-group="county" class="hide">
								<#form:input path="officeEstablishment.deputySectionChief" class="form-control digits"/>
							</td>
							<td data-group="county" class="hide">
								<#form:input path="officeEstablishment.belowSectionLevel" class="form-control digits"/>
							</td>
							<!-- 乡级机关. -->
							<td data-group="township" class="hide">
								<#form:input path="officeEstablishment.townshipPositive" class="form-control digits"/>
							</td>
							<td data-group="township" class="hide">
								<#form:input path="officeEstablishment.townshipDeputy" class="form-control digits"/>
							</td>
							<td data-group="township" class="hide">
								<#form:input path="officeEstablishment.belowTownshipLevel" class="form-control digits"/>
							</td>
						</tr>
					</tbody>
				</table>
				<div class="form-unit-wrap table-form">
					<table id="dataGrid"></table>
				</div>

				<br/>
				<div class="form-unit">
					${text('核定面积')}
					<p class="text-warning" style="font-size: 12px;">不可编辑, 保存后根据实际情况自动计算</p>
				</div>
				<div class="row">
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('服务用房')}：
							</label>
							<div class="col-sm-8">
								<#form:input path="serviceRoomApprovedArea" name="serviceRoomApprovedArea" readonly="true" maxlength="100" value="0" class="form-control "/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('设备用房')}：
							</label>
							<div class="col-sm-8">
								<#form:input path="equipmentRoomApprovedArea" name="equipmentRoomApprovedArea" readonly="true" maxlength="100" value="0" class="form-control "/>
							</div>
						</div>
					</div>
					<div class="col-xs-4">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('附属用房')}：
							</label>
							<div class="col-sm-8">
								<#form:input path="ancillaryRoomAreaApprovedArea" name="ancillaryRoomAreaApprovedArea" readonly="true" maxlength="100" value="0" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('详细信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('负责人')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="leader" maxlength="100" class="form-control "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('办公电话')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="phone" maxlength="100" class="form-control phone"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('联系地址')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="realEstateAddressId" title="地址选择"
								path="address"
								labelPath = "obRealEstateAddress.name"
								url="${ctx}/estate/realEstateAddress/realEstateAddressSelect" allowClear="false"
								checkbox="false" itemCode="id" itemName="address"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('邮政编码')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="zipCode" maxlength="100" class="form-control zipCode"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('电子邮箱')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="email" maxlength="300" class="form-control email"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control "/>
							</div>
						</div>
					</div>
				</div>
<!--				<#form:extend collapsed="true" />-->
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('sys:office:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<style>
	/* 添加表格响应式样式 */
	.table-form {
		width: 100%;
		overflow-x: auto;
		white-space: nowrap;
	}
	.table-form th, .table-form td {
		min-width: 100px;
		vertical-align: middle !important;
	}
	.table-form .hide {
		display: none;
	}
</style>
<script>

	let officeTypeGroups = {
		// 中央机关列组 (group: 'central')
		'central': [
			'ministerPositive',        // ${text("部级正职")}
			'ministerDeputy',          // ${text("部级副职")}
			'departmentDirector',      // ${text("正司(局)级")}
			'deputyDepartmentDirector',// ${text("副司(局)级")}
			'divisionLevel',           // ${text("处级")}
			'belowDivisionLevel'       // ${text("处级以下")}
		],

		// 省级机关列组 (group: 'province')
		'province': [
			'provincePositive',        // ${text("省级正职")}
			'provinceDeputy',          // ${text("省级副职")}
			'bureauDirector',          // ${text("正厅(局)级")}
			'deputyBureauDirector',    // ${text("副厅(局)级")}
			'divisionChief',           // ${text("正处级")}
			'deputyDivisionChief',     // ${text("副处级")}
			'belowDivisionChief'       // ${text("处级以下")}
		],

		// 市级机关列组 (group: 'municipal')
		'municipal': [
			'municipalPositive',           // ${text("市级正职")}
			'municipalDeputy',             // ${text("市级副职")}
			'municipalBureauDirector',     // ${text("正局(处)级")}
			'municipalDeputyBureauDirector',// ${text("副局(处)级")}
			'belowMunicipalBureau'         // ${text("局(处)级以下")}
		],

		// 县级机关列组 (group: 'county')
		'county': [
			'countyPositive',        // ${text("县级正职")}
			'countyDeputy',          // ${text("县级副职")}
			'sectionChief',          // ${text("正科级")}
			'deputySectionChief',    // ${text("副科级")}
			'belowSectionLevel'      // ${text("科级以下")}
		],

		// 乡级机关列组 (group: 'township')
		'township': [
			'townshipPositive',      // ${text("乡级正职")}
			'townshipDeputy',        // ${text("乡级副职")}
			'belowTownshipLevel'     // ${text("乡级以下")}
		]
	};
	// 机关类型与列组映射关系（需与字典值一致）
	let groupMap = {
		'1': 'central',     // 中央机关
		'2': 'province',    // 省级机关（修正之前的拼写错误）
		'3': 'municipal',   // 市级机关
		'4': 'county',      // 县级机关
		'5': 'township'     // 乡级机关
	};
</script>
<script>
	$(function () {
		// 初始化工具提示
		$('[data-toggle="tooltip"]').tooltip();

		// 自动计算编制人数
		function calculateStaffNumbers() {
			let currentGroup = groupMap[$('#officeType').val()] || '';
			let total = 0;

			// 计算当前可见职级的人数总和
			$('#establishmentTable [data-group="'+currentGroup+'"] input').each(function(){
				let val = parseFloat($(this).val()) || 0;
				total += val;
			});

			// 更新编制人数
			$('#totalStaff').val(total);

			// 计算服务用房（编制人数*20）并更新
			let serviceRoomApprovedArea = total * `${office.serviceRoomApprovedArea}`;
			$('#serviceRoomApprovedArea').val(serviceRoomApprovedArea);

			// 编外人数计算逻辑（根据业务需求补充）
			// let external = 0;
			// $('#externalStaff').val(external);
		}

		// 监听职级人数变化
		$('#establishmentTable').on('input', 'input', function(){
			calculateStaffNumbers();
		});

		// 监听机关类型变化
		$('#officeType').on('change', function(){
			// 延迟100ms确保DOM更新完成
			setTimeout(calculateStaffNumbers, 100);
		});

		// 初始化计算
		calculateStaffNumbers();
	});
</script>
<!--<script>-->
<!--	$(function () {-->
<!--		// 初始化工具提示-->
<!--		$('[data-toggle="tooltip"]').tooltip();-->

<!--		// 自动计算编制人数-->
<!--		function calculateStaffNumbers() {-->
<!--			let currentGroup = groupMap[$('#officeType').val()] || '';-->
<!--			let total = 0;-->

<!--			// 计算当前可见职级的人数总和-->
<!--			$('#establishmentTable [data-group="'+currentGroup+'"] input').each(function(){-->
<!--				let val = parseFloat($(this).val()) || 0;-->
<!--				total += val;-->
<!--			});-->

<!--			// 更新编制人数-->
<!--			$('#totalStaff').val(total);-->

<!--			// 编外人数计算逻辑（根据业务需求补充）-->
<!--			// let external = 0;-->
<!--			// $('#externalStaff').val(external);-->
<!--		}-->

<!--		// 监听职级人数变化-->
<!--		$('#establishmentTable').on('input', 'input', function(){-->
<!--			calculateStaffNumbers();-->
<!--		});-->

<!--		// 监听机关类型变化-->
<!--		$('#officeType').on('change', function(){-->
<!--			// 延迟100ms确保DOM更新完成-->
<!--			setTimeout(calculateStaffNumbers, 100);-->
<!--		});-->

<!--		// 初始化计算-->
<!--		calculateStaffNumbers();-->
<!--	});-->
<!--</script>-->
<script>
	$(function () {
		// 更新列显示逻辑
		function updateColumnsByOfficeType(officeTypeCode) {
			// 获取当前机关类型对应的组名
			let currentGroup = groupMap[officeTypeCode] || '';

			// Hide all columns by default
			$('#establishmentTable [data-group]').each(function(){
				$(this).addClass('hide');
			});

			const townshipElements = document.querySelectorAll('[data-group="'+currentGroup+'"]');

			townshipElements.forEach(element => {
				element.classList.remove('hide');
			});
		}

		// 初始化表格
		function initEstablishmentTable() {
			// 添加行操作按钮（如果需要）
			$('#establishmentTable tbody tr').each(function(){
				// let $tr = $(this);
				// let $actionTd = $('<td class="nowrap">')
				// 		.append('<button type="button" class="btn btn-xs btn-danger" onclick="deleteRow(this)"><i class="fa fa-trash"></i></button>');
				// $tr.append($actionTd);
			});
		}

		initEstablishmentTable();
		updateColumnsByOfficeType($('#officeType').val());

		// 监听机关类型变化
		$('#officeType').on('change', function() {
			updateColumnsByOfficeType($(this).val());
		});

	})
</script>
<script>
$("#inputForm").validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					(contentWindow.win||contentWindow).$('#dataGrid').dataGrid('refreshTreeChildren',
							$('#parentCode').val(), '${office.id}');
				});
			}
		}, "json");
    }
});
$('#officeName').change(function(){
	if ($('#fullName').val()==''){
		$('#fullName').val($(this).val());
	}
});
// 选择上级节点回调方法
function treeselectCallback(id, act, index, layero){
	if (id == 'parent' && (act == 'ok' || act == 'clear')){
		// 创建并初始化下一个节点信息，如：排序号、默认值
		$.get('${ctx}/sys/office/createNextNode?parentCode='
				+$('#parentCode').val(), function(data){
			$('#treeSort').val(data.treeSort);
		});
	}
}
</script>
