<% layout('/layouts/default.html', {title: '岗位管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-trophy"></i> ${text('岗位管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('sys:post:edit')){ %>
					<a href="${ctx}/sys/post/form" class="btn btn-default btnTool" title="${text('新增岗位')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${post}" action="${ctx}/sys/post/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('岗位名称')}：</label>
					<div class="control-inline">
						<#form:input path="postName_like" maxlength="100" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('岗位代码')}：</label>
					<div class="control-inline">
						<#form:input path="viewCode_like" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('岗位分类')}：</label>
					<div class="control-inline width-90">
						<#form:select path="postType" dictType="sys_post_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-60">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>			
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("岗位名称")}', name:'postName', index:'a.post_name', width:200, align:"center", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/sys/post/form?postCode='+row.postCode+'" class="hsBtnList" data-title="${text("编辑岗位")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("岗位代码")}', name:'viewCode', index:'a.view_code', width:200, align:"center"},
		{header:'${text("排序号")}', name:'postSort', index:'a.post_sort', width:80, align:"center"},
		{header:'${text("岗位分类")}', name:'postType', index:'a.post_type', width:100, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_post_type')}", val, '未知', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:200, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:80, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
		}},
		{header:'${text("操作")}', name:'actions', width:130, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('sys:post:edit')){
				actions.push('<a href="${ctx}/sys/post/form?postCode='+row.postCode+'" class="hsBtnList" title="${text("编辑岗位")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/sys/post/disable?postCode='+row.postCode+'" class="btnList" title="${text("停用岗位")}" data-confirm="${text("确认要停用该岗位吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/sys/post/enable?postCode='+row.postCode+'" class="btnList" title="${text("启用岗位")}" data-confirm="${text("确认要启用该岗位吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/sys/post/delete?postCode='+row.postCode+'" class="btnList" title="${text("删除岗位")}" data-confirm="${text("确认要删除该岗位吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>