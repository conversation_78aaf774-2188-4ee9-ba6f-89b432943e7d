<% layout('/layouts/default.html', {title: '', bodyClass: ''}){ %>
<!-- <link rel="stylesheet" href="${ctxStatic}/modules/sys/sysDesktop.css?${_version}"> -->
<div class="content pb0">
    <section class="col-md-7 ui-sortable">

    </section>
</div>
<footer class="main-footer m0" style="padding-right:50px">
	<div class="pull-right hidden-xs">当前版本： ${@Global.getConfig('productVersion')}</div>
	&copy; ${@DateUtils.getYear()} ${@Global.getConfig('productNameAssigned', @Global.getConfig('productName'))} - Powered By
	<a href="http://jeesite.com" target="_blank">JeeSite</a>
</footer>
<% } %>
<script src="${ctxStatic}/jquery/jquery-ui-sortable-1.13.2.min.js"></script>
<script src="${ctxStatic}/echarts/4.2/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
    $(window).resize(function(){
        var footerHeight = $('.main-footer').outerHeight() || 0;
        var windowHeight = $(window).height();
        $('.content').css('min-height', windowHeight - footerHeight);
        if(myChart1) myChart1.resize();
        if(myChart2) myChart2.resize();
        if(myChart3) myChart3.resize();
    }).resize();
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        $(window).resize();
    });
    $('.ui-sortable').sortable({
        connectWith : '.ui-sortable',
        handle      : '.box-header, .nav-tabs',
        placeholder : 'sort-highlight',
        forcePlaceholderSize: true,
        zIndex : 999999
    }).find('.box-header, .nav-tabs').css('cursor', 'move');
});
</script>