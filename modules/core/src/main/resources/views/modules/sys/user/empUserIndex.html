<% layout('/layouts/default.html', {title: '用户管理', libs: ['layout','zTree']}){ %>
<div class="ui-layout-west">
	<div class="main-content">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title">
					<i class="fa icon-grid"></i> ${text('组织机构')}
				</div>
				<div class="box-tools pull-right">
					<% if(hasPermi('sys:office:edit')){ %>
						<button type="button" class="btn btn-box-tool addTabPage" data-href="${ctx}/sys/office/list" title="${text('机构管理')}"><i class="fa fa-edit"></i></button>
					<% } %>
					<button type="button" class="btn btn-box-tool" id="btnExpand" title="${text('展开')}" style="display:none;"><i class="fa fa-chevron-up"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnCollapse" title="${text('折叠')}"><i class="fa fa-chevron-down"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnRefresh" title="${text('刷新')}"><i class="fa fa-refresh"></i></button>
				</div>
			</div>
			<div class="ui-layout-content">
				<div id="tree" class="ztree"></div>
			</div>
		</div>
	</div>
</div>
<div class="ui-layout-center">
	<iframe id="mainFrame" name="mainFrame" class="ui-layout-content p0"
		src="${ctx}/sys/empUser/list"></iframe>
</div> 
<% } %>
<script>
//# // 初始化布局
$('body').layout({
	west__initClosed: $(window).width() <= 767, // 是否默认关闭
	west__size: window.lang == 'en' ? 215 : 200
});
//# // 主页框架
var win = $("#mainFrame")[0].contentWindow;
//# // 树结构初始化加载
var setting = {view:{selectedMulti:false},data:{key:{title:"title"},simpleData:{enable:true}},
	async:{enable:true,autoParam:["id=parentCode"],url:"${ctx}/sys/office/treeData"},
	callback:{onClick:function(event, treeId, treeNode){
		tree.expandNode(treeNode);
		//win.$('button[type=reset]').click();
		win.$('#officeCode').val(treeNode.id);
		win.$('#officeName').val(treeNode.name);
		win.page(1);
	}}
}, tree, loadTree = function(){
	js.ajaxSubmit(setting.async.url+"?___t="+new Date().getTime(), {
			ctrlPermi:'${ctrlPermi}'/*1拥有的权限 2管理的权限*/,
			parentCode:'${parameter.parentCode!}'}, function(data){
		tree = $.fn.zTree.init($("#tree"), setting, data);
		var level = -1, nodes;
		while (++level <= 1) {
			nodes = tree.getNodesByParam("level", level);
			if (nodes.length > 10) { break; }
			for(var i=0; i<nodes.length; i++) {
	 			tree.expandNode(nodes[i], true, false, false);
			}
		}
	}, null, null, js.text('loading.message'));
};loadTree();
//# // 工具栏按钮绑定
$('#btnExpand').click(function(){
	tree.expandAll(true);
	$(this).hide();
	$('#btnCollapse').show();
});
$('#btnCollapse').click(function(){
	tree.expandAll(false);
	$(this).hide();
	$('#btnExpand').show();
});
$('#btnRefresh').click(function(){
	loadTree();
});
//# // 调用子页分页函数
function page(){
	win.page();
}
</script>
