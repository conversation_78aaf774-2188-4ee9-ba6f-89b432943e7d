<% layout('/layouts/default.html', {title: '机构管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-grid"></i> ${text('机构管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="${text('刷新')}"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="${text('展开一级')}"><i class="fa fa-angle-double-down"></i> ${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<% if(hasPermi('sys:office:edit')){ %>
					<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> ${text('导入')}</a>
					<a href="${ctx}/sys/office/form" class="btn btn-default btnTool" title="${text('新增机构')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${office}" action="${ctx}/sys/office/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<#form:hidden name="ctrlPermi" value="${ctrlPermi}"/>
				<#form:hidden path="officeCode" class="isReset"/>
				<div class="form-group">
					<label class="control-label">${text('机构代码')}：</label>
					<div class="control-inline">
						<#form:input path="viewCode_like" maxlength="100" class="form-control width-90"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('机构名称')}：</label>
					<div class="control-inline">
						<#form:input path="officeName" maxlength="100" class="form-control width-90"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('机构全称')}：</label>
					<div class="control-inline">
						<#form:input path="fullName" maxlength="200" class="form-control width-90"/>
					</div>
				</div>
				<!--<div class="form-group">
					<label class="control-label">${text('机构类型')}：</label>
					<div class="control-inline width-90">
						<#form:select path="officeType" dictType="sys_office_type" blankOption="true" class="form-control"/>
					</div>
				</div>-->
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-60">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>			
			<table id="dataGrid"></table>
		</div>
	</div>
</div>
<% } %>
<script>
	function formatCountColumn(val, obj, row, act) {
		if (null === val || undefined === val) {
			return 0;
		}
		return val;
	}
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("单位名称")}', name:'officeName', index:'a.office_name', width:250, align:"left", formatter: function(val, obj, row, act){
			return '( '+row.viewCode+' ) '+'<a href="${ctx}/sys/office/form?officeCode='+row.officeCode+'" class="hsBtnList" data-title="${text("编辑机构")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("统一社会信用代码")}', name:'codeCertificateNumber', index:'codeCertificateNumber.name', width:150, align:"center"},
		{header:'${text("机关性质")}', name:'officeNature', index:'a.officeNature', width:100, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('ob_office_nature')}", val, '${text("未知")}', true);
		}},

		// // 中央机关列组 (group: 'central')
		// {header:'${text("部级正职")}', name:'officeEstablishment.ministerPositive', align: 'center', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("部级副职")}', name:'officeEstablishment.ministerDeputy', group: 'central', align: 'center', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("正司(局)级")}', name:'officeEstablishment.departmentDirector', group: 'central', align: 'center', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("副司(局)级")}', name:'officeEstablishment.deputyDepartmentDirector', group: 'central', align: 'center', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("处级")}', name:'officeEstablishment.divisionLevel', group: 'central', align: 'center', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("处级以下")}', name:'officeEstablishment.belowDivisionLevel', group: 'central', align: 'center', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		//
		// // 省级机关列组 (group: 'province')
		// {header:'${text("省级正职")}', name:'officeEstablishment.provincePositive', group: 'province', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("省级副职")}', name:'officeEstablishment.provinceDeputy', group: 'province', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("正厅(局)级")}', name:'officeEstablishment.bureauDirector', group: 'province', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("副厅(局)级")}', name:'officeEstablishment.deputyBureauDirector', group: 'province', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("正处级")}', name:'officeEstablishment.divisionChief', group: 'province', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("副处级")}', name:'officeEstablishment.deputyDivisionChief', group: 'province', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("处级以下")}', name:'officeEstablishment.belowDivisionChief', group: 'province', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		//
		// // 市级机关列组 (group: 'municipal')
		// {header:'${text("市级正职")}', name:'officeEstablishment.municipalPositive', group: 'municipal', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("市级副职")}', name:'officeEstablishment.municipalDeputy', group: 'municipal', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("正局(处)级")}', name:'officeEstablishment.municipalBureauDirector', group: 'municipal', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("副局(处)级")}', name:'officeEstablishment.municipalDeputyBureauDirector', group: 'municipal', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("局(处)级以下")}', name:'officeEstablishment.belowMunicipalBureau', group: 'municipal', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		//
		// // 县级机关列组 (group: 'county')
		// {header:'${text("县级正职")}', name:'officeEstablishment.countyPositive', group: 'county', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("县级副职")}', name:'officeEstablishment.countyDeputy', group: 'county', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("正科级")}', name:'officeEstablishment.sectionChief', group: 'county', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("副科级")}', name:'officeEstablishment.deputySectionChief', group: 'county', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("科级以下")}', name:'officeEstablishment.belowSectionLevel', group: 'county', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		//
		// // 乡级机关列组 (group: 'township')
		// {header:'${text("乡级正职")}', name:'officeEstablishment.townshipPositive', group: 'township', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("乡级副职")}', name:'officeEstablishment.townshipDeputy', group: 'township', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		// {header:'${text("乡级以下")}', name:'officeEstablishment.belowTownshipLevel', group: 'township', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'11', 'class':'form-control digits'}, formatter: formatCountColumn},
		{header:'${text("编制总数")}', index:'totalStaffNumber', width:150, align:"center", formatter: function (val, obj, row, act) {

			if (row.officeEstablishment === undefined) {
				return 0;
			} else {
				let count = 0;
				$.each(row.officeEstablishment, function (key, value) {
					if (key !== 'id' && key !== 'isNewRecord' && key !== 'createDate' && key !== 'createBy' && key !== 'updateBy' && key !== 'updateDate' ) {
						if (undefined === value) value = 0;
						count += value;
					}
				})
				return count;
			}
		}},
		{header:'${text("编外人数")}', name: 'externalStaff', index:'externalStaff', width:150, align:"center", formatter: function (val, obj, row, act) {
			if (null === val || undefined === val) {
				return 0;
			}
			return val;
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
				actions.push('<a href="${ctx}/sys/office/form?officeCode='+row.officeCode+'" class="btnList btn btn-link info btn-xs" title="${text("编辑机构")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/sys/office/disable?officeCode='+row.officeCode+'" class="btnList btn btn-link info btn-xs" title="${text("停用机构")}" data-confirm="${text("确认要停用该机构吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/sys/office/enable?officeCode='+row.officeCode+'" class="btnList btn btn-link info btn-xs" title="${text("启用机构")}" data-confirm="${text("确认要启用该机构吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/sys/office/delete?officeCode='+row.officeCode+'" class="btnList btn btn-link info btn-xs" title="${text("删除机构")}" data-confirm="${text("确认要删除该机构及所有子机构吗？")}" data-deltreenode="'+row.id+'">删除</a>&nbsp;');

				actions.push('<a href="javascript:" class="btnMore info" title="${text("更多操作")}"><i class="fa fa-chevron-circle-right"></i></a>&nbsp;');
				actions.push('<div class="moreItems">');
				actions.push('<a href="${ctx}/sys/office/form?parentCode='+row.id+'" class="btnList btn btn-link info btn-xs" title="${text("新增下级机构")}">新增下级</a>&nbsp;');
				actions.push('</div>');
			return actions.join('');
		}}
	],
	autoGridWidth: true,
	frozenCols: false,
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'viewCode,officeName,fullName,officeType,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		if ($('#officeCode').val() != ''){
			$('#officeCode').val('');
			$('#btnExpandTreeNode').click();
		}
	}
});
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/sys/office/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入机构数据")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		success: function(layero, index){
			layero.find('input[type="checkbox"]').iCheck();
		},
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/sys/office/importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5">
				<#form:checkbox name="updateSupport" label="${text('是否更新已经存在的机构数据')}" class="form-control"
					title="${text('如果机构编码已经存在，更新这条数据。')}"/> &nbsp;
				<a href="${ctx}/sys/office/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
			<font color="red" class="pull-left mt10">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</font>
		</div>
	</div>
</form>
//--></script>