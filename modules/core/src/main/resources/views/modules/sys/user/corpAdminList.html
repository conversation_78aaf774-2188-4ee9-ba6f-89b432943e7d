<% layout('/layouts/default.html', {title: '租户管理员', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-badge"></i> ${text('租户管理员')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('sys:corpAdmin:edit')){ %>
					<% if(useCorpModel){ %>
					<a href="${ctx}/sys/corpAdmin/form?op=addCorp" class="btn btn-default btnTool" title=" ${text('新增管理员')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
					<% }else{ %>
					<a href="${ctx}/sys/corpAdmin/form?corpCode_=${currentCorpCode}&corpName_=${currentCorpName}&op=addAdmin" class="btn btn-default btnTool" title="${text('新增管理员')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
					<% } %>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${user}" action="${ctx}/sys/corpAdmin/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('登录账号')}：</label>
					<div class="control-inline">
						<#form:input path="loginCode" maxlength="100" class="form-control width-90"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('用户昵称')}：</label>
					<div class="control-inline">
						<#form:input path="userName" maxlength="100" class="form-control width-90"/>
					</div>
				</div>
				<div class="form-group ${useCorpModel?'':'hide'}">
					<label class="control-label">${text('租户代码')}：</label>
					<div class="control-inline">
						<#form:input path="corpCode_" maxlength="100" class="form-control width-90"/>
					</div>
				</div>
				<div class="form-group ${useCorpModel?'':'hide'}">
					<label class="control-label">${text('租户名称')}：</label>
					<div class="control-inline">
						<#form:input path="corpName_" maxlength="100" class="form-control width-90"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-90">
						<#form:select path="status" dictType="sys_user_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>			
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("登录账号")}', name:'loginCode', index:'a.login_code', width:200, align:"center", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/sys/corpAdmin/form?userCode='+row.userCode+'&op=edit" class="hsBtnList" data-title="${text("编辑用户")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("用户昵称")}', name:'userName', index:'a.user_name', width:200, align:"center"},
		//# if(useCorpModel){
		{header:'${text("租户代码")}', name:'corpCode_', index:'a.corp_code', width:200, align:"center", formatter: function(val, obj, row, act){
			return '<a href="javascript:" onclick="$(\'#corpCode_\').val(\''+val+'\');$(\'#searchForm\').submit()">'+val+'</a>';
		}},
		{header:'${text("租户名称")}', name:'corpName_', index:'a.corp_name', width:200, align:"center"},
		//# }
		{header:'${text("电子邮箱")}', name:'email', index:'a.email', width:200, align:"center"},
		{header:'${text("手机号码")}', name:'mobile', index:'a.mobile', width:200, align:"center"},
		{header:'${text("办公电话")}', name:'phone', index:'a.phone', width:200, align:"center"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:200, align:"center"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:100, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('sys:corpAdmin:edit')){
				actions.push('<a href="${ctx}/sys/corpAdmin/form?userCode='+row.userCode+'&op=edit" class="hsBtnList" title="${text("编辑用户")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/sys/corpAdmin/disable?userCode='+row.userCode+'" class="btnList" title="${text("停用用户")}" data-confirm="${text("确认要停用该用户吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/sys/corpAdmin/enable?userCode='+row.userCode+'" class="btnList" title="${text("启用用户")}" data-confirm="${text("确认要启用该用户吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/sys/corpAdmin/delete?userCode='+row.userCode+'" class="btnList" title="${text("删除用户")}" data-confirm="${text("确认要删除该用户吗？")}">删除</a>&nbsp;');
				//# if(useCorpModel){
				actions.push('<a href="${ctx}/sys/corpAdmin/form?corpCode_='+row.corpCode_+'&corpName_='+row.corpName_+'&op=addAdmin" class="hsBtnList" title="${text("新增管理员")}"><i class="fa fa-plus-square"></i></a>&nbsp;');
				//# }
				actions.push('<a href="javascript:" class="btnMore" title="${text("更多操作")}"><i class="fa fa-chevron-circle-right"></i></a>&nbsp;');
				actions.push('<div class="moreItems">');
				actions.push('<a href="${ctx}/sys/corpAdmin/resetpwd?userCode='+row.userCode+'" class="btn btn-default btn-xs btnList" title="${text("用户密码重置")}" data-confirm="${text("确认要将该用户密码重置到初始状态吗？")}"><i class="fa fa-reply-all"></i> ${text("重置密码")}</a>&nbsp;');
				actions.push('</div>');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>