<% layout('/layouts/default.html', {title: '字典管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-social-dropbox"></i> ${text('字典管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('sys:dictType:edit')){ %>
					<a href="${ctx}/sys/dictType/form" class="btn btn-default btnTool" title="${text('新增字典类型')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dictType}" action="${ctx}/sys/dictType/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('字典名称')}：</label>
					<div class="control-inline">
						<#form:input path="dictName" maxlength="100" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字典类型')}：</label>
					<div class="control-inline">
						<#form:input path="dictType_like" maxlength="100" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('系统字典')}：</label>
					<div class="control-inline width-60">
						<#form:select path="isSys" dictType="sys_yes_no" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-60">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>			
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $("#searchForm"),
	columnModel: [
		{header:'${text("字典名称")}', name:'dictName', index:'a.dict_name', width:200, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/sys/dictType/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑字典类型")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("字典类型")}', name:'dictType', index:'a.dict_type', width:200, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/sys/dictData/list?dictType='+row.dictType+'" class="hsBtnList" data-title="${text("字典数据")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("系统字典")}', name:'isSys', index:'a.is_sys', width:80, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_yes_no')}", val, '未知', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:200, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:60, fixed:true, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
		}},
		{header:'${text("操作")}', name:'actions', width:130, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('sys:dictType:edit')){
				actions.push('<a href="${ctx}/sys/dictType/form?id='+row.id+'" class="hsBtnList" title="${text("编辑字典类型")}">编辑</a>&nbsp;');
			//# }
			actions.push('<a href="${ctx}/sys/dictData/list?dictType='+row.dictType+'" class="btnList" title="${text("字典数据")}">数据</a>&nbsp;');
			//# if(hasPermi('sys:dictType:edit')){
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/sys/dictType/disable?id='+row.id+'" class="btnList" title="${text("停用字典类型")}" data-confirm="${text("确认要停用该字典类型吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/sys/dictType/enable?id='+row.id+'" class="btnList" title="${text("启用字典类型")}" data-confirm="${text("确认要启用该字典类型吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/sys/dictType/delete?id='+row.id+'" class="btnList" title="${text("删除字典类型")}" data-confirm="${text("确认要删除该字典类型吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>