<% if(@Global.getPropertyToBoolean('msg.enabled', 'false')){ %>
<li class="dropdown messages-menu">
	<a href="javascript:" class="dropdown-toggle" data-hover="dropdown">
		<i class="fa fa-envelope-o"></i>
		<span class="label label-success" id="msgNum">0</span>
	</a>
	<ul class="dropdown-menu">
		<li class="header">${text('你有 {0\} 条消息', '<span id="msgNum2">0</span>')}</li>
		<li>
			<ul class="menu" id="msgList"
				data-mergeMsgLimit="${@Global.getConfig('sys.msg.mergeMsgLimit', '5')}"
				data-mergeMsgTitle="${text('系统消息')}"
				data-mergeMsgContent="${text('您有 {0\} 条新消息，由于消息太多，这里为您合并，请点击查看按钮看详情。')}"
				data-loginTimeout="${text('您当前的会话已超时，请重新登录。')}"
				data-pullMsgPollingInterval="1000*60"
				data-showMsgCloseTimeout="1000*60"></ul>
		</li>
		<li class="footer"><a href="javascript:" data-href="${ctx}/msg/list"
			data-title="${text('查看全部消息')}" class="addTabPage">${text('查看全部消息')}</a></li>
	</ul>
	<audio id="audioMessage" controls="controls" preload="auto" hidden="hidden">
		<source src="${ctxStatic}/modules/msg/message.mp3" type="audio/mpeg" />
	</audio>
	<script src="${ctxStatic}/jquery-timeago/jquery.timeago.js"></script>
	<script src="${ctxStatic}/jquery-timeago/i18n/jquery.timeago.${lang()}.js"></script>
	<script src="${ctxStatic}/modules/msg/topMenuMsg.js"></script>
	<script type="text/template" id="msgListTpl">
		<li id="msg{{d.id}}"><a href="javascript:"
				data-href="${ctx}/msg/readMsg?id={{d.id}}"
				onclick="readMsg(this, '${text('查看消息')}', '{{d.id}}')" >
			<div class="pull-left"><i class="img-circle bg-aqua icon-bubble"></i></div>
			<h4>{{d.msgContentEntity.title}} <small><i class="fa fa-clock-o"></i>
				<abbr class="timeago" title="{{d.sendDate}}">{{d.sendDate}}</abbr></small></h4>
			<p>{{=d.msgContentEntity.content}}</p>
		</a></li>
	</script>
	<script type="text/template" id="msgTipTpl">
		<abbr class="timeago" title="{{d.sendDate}}">{{d.sendDate}}</abbr>
			{{d.sendUserName}} <br/> {{=d.msgContentEntity.content}} 
		<div style="margin:8px 0 0;float:right;">
			{{# $.each(d.msgContentEntity.buttons, function(idx, item){ }}
				<button class="btn btn-default btn-sm" type="button"
					data-href="${ctxPath}{{item.href}}"
					onclick="readMsg(this, '${text('查看消息')}', '{{item.id}}');">{{item.name}}</button>&nbsp;
			{{# }); }}
			<button type="button" class="btn btn-default btn-sm"
				data-href="${ctx}/msg/readMsg?id={{d.id}}"
				onclick="readMsg(this, '${text('消息详情')}', '{{d.id}}')">${text('查看')}</button>&nbsp;
		</div>
	</script>
</li>
<% } %>