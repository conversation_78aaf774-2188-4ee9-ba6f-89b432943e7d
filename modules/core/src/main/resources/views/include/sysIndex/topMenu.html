<nav class="navbar navbar-static-top">
	<div class="logo" >
<!--		<i class="fa fa-institution" id="productLogo"></i>-->
		<img src="${ctxStatic}/logo-2.svg" alt="${@Global.getConfig('productNameAssigned', @Global.getConfig('productName'))}" id="productLogo" class="productLogo">
		<b>${@Global.getConfig('productNamePrefix')}${title}</b>
<!--		<small title="${text('点击缩小侧边栏，右键隐藏侧边栏')}">&nbsp; &nbsp;<i class="fa fa-bars"></i></small>-->
	</div>
	<%/*<!--%><a href="javascript:" class="sidebar-toggle" data-toggle="push-menu" role="button">
		<span class="sr-only">菜单切换</span><span class="icon-bar"></span>
		<span class="icon-bar"></span><span class="icon-bar"></span>
	</a><%-->*/%>
	<div class="navbar-custom-menu">
		<ul class="nav navbar-nav">
			<% include('/include/sysIndex/topMenuCorp.html'){} %>
			<li><a href="javascript:" id="fullScreen" title="${text('全屏')}" data-placement="bottom" data-container="body"><i class="fa fa-arrows-alt"></i></a></li>
			<li><a href="javascript:" id="switchSkin" title="${text('切换主题')}" style="margin-top:-1px;" data-placement="bottom" data-container="body" data-layer-width="600" data-layer-height="350"><i class="fa fa-dashboard"></i></a></li>
			<% include('/include/sysIndex/topMenuLang.html'){} %>
			<% include('/include/sysIndex/topMenuOnline.html'){} %>
			<% include('/include/sysIndex/topMenuMsg.html'){} %>
			<% //include('/include/sysIndex/topMenuNotify.html'){} %>
			<% //include('/include/sysIndex/topMenuTask.html'){} %>
			<% include('/include/sysIndex/topMenuUser.html'){} %>
		</ul>
	</div>
	<% if(@Global.getConfig('sys.index.menuStyle', '1') == '2'){ %>
	<div class="navbar-custom-menu pull-right" id="topMenu">
		<ul class="nav navbar-nav">
			<li class="treeview dropdown" id="topMenuMore" style="position:relative;">
				<a href="javascript:" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">
					<i class="fa fa-fw icon-options"></i>
				</a>
				<ul class="treeview-menu dropdown-menu" style="left:0;">
					<%
					var p = {parentCode: '0', children: false};
					menu.tree(p); print(p.html);
					%>
				</ul>
			</li>
		</ul>
		<script src="${ctxStatic}/modules/sys/topMenu.js"></script>
	</div>
	<% } %>
</nav>