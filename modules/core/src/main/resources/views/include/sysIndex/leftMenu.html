<section class="sidebar">
	<%/*<!--%>
	<form action="#" method="get" class="sidebar-form">
		<div class="input-group">
			<input type="text" name="q" class="form-control" placeholder="查询...">
			<span class="input-group-btn">
				<button type="submit" name="search" id="search-btn" class="btn btn-flat"><i
					class="fa fa-search"></i></button>
			</span>
		</div>
	</form>
	<%-->*/%>
	<%/*<!--%>
	<ul class="sidebar-menu">
		<% var menuSysCode = @ObjectUtils.toStringIgnoreNull(session.sysCode, 'default'); %>
		<li class="header">${@DictUtils.getDictLabel('sys_menu_sys_code', menuSysCode, '')}</li>
	</ul>
	<%-->*/%>
	<div id="leftSideMenu" class="menuBox" style="padding-top: 5px;">
		<ul class="sidebar-menu">
			<li class="dropdown" style="position:relative;">
				<ul class="sidebar-menu" style="left:0;">
					<%
					var p = {parentCode: '0', children: false};
					menu.tree(p); print(p.html);
					%>
				</ul>
			</li>
		</ul>
		<script src="${ctxStatic}/modules/sys/leftSideMenu.js"></script>
		<script src="${ctxStatic}/modules/sys/leftMenu.js"></script>
	</div>
	<div id="leftMenu" class="menuBox" data-widget="tree" style="width: 100%;">

	</div>
	<%/*<!--%>
	<ul class="sidebar-menu">
		<li class="header">快捷菜单</li>
		<li><a href="#"><i class="fa fa-circle-o text-red"></i> <span>快捷菜单1</span></a></li>
		<li><a href="#"><i class="fa fa-circle-o text-yellow"></i> <span>快捷菜单2</span></a></li>
		<li><a href="#"><i class="fa fa-circle-o text-aqua"></i> <span>快捷菜单3</span></a></li>
	</ul>
	<%-->*/%>
</section>