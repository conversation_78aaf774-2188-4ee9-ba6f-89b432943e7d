<section class="sidebar">
	<%/*<!--%>
	<form action="#" method="get" class="sidebar-form">
		<div class="input-group">
			<input type="text" name="q" class="form-control" placeholder="查询...">
			<span class="input-group-btn">
				<button type="submit" name="search" id="search-btn" class="btn btn-flat"><i
					class="fa fa-search"></i></button>
			</span>
		</div>
	</form>
	<%-->*/%>
	<%/*<!--%>
	<ul class="sidebar-menu">
		<% var menuSysCode = @ObjectUtils.toStringIgnoreNull(session.sysCode, 'default'); %>
		<li class="header">${@DictUtils.getDictLabel('sys_menu_sys_code', menuSysCode, '')}</li>
	</ul>
	<%-->*/%>

	<div id="menu-layout" style="width: 280px; height: 480px;">
		<div class="ui-layout-center" style="width: 110px;">
<!--			<div class="ui-layout-content">-->
<!--			</div>-->
			主菜单
		</div>
		<div class="ui-layout-east" style="width: 170px;">
			<div class="ui-layout-content">
                子菜单
			</div>
		</div>
	</div>


	<%/*<!--%>
	<ul class="sidebar-menu">
		<li class="header">快捷菜单</li>
		<li><a href="#"><i class="fa fa-circle-o text-red"></i> <span>快捷菜单1</span></a></li>
		<li><a href="#"><i class="fa fa-circle-o text-yellow"></i> <span>快捷菜单2</span></a></li>
		<li><a href="#"><i class="fa fa-circle-o text-aqua"></i> <span>快捷菜单3</span></a></li>
	</ul>
	<%-->*/%>
</section>

<script>
	$(function () {
	    $('#menu-layout').layout({
	    	east__size: 170,
	    	onresize_end: function(){
				console.log('布局已调整');
	    	}
	    });
	});
</script>