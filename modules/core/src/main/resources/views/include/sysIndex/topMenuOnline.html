<li>
	<a href="javascript:" id="btnOnline" data-href="${ctx}/sys/online/list" data-title="${text('在线人员')}" title="${text('在线人员')}"
		class="${hasPermi('sys:online:view') ? 'addTabPage' : ''} " data-placement="bottom" data-container="body">
		<i class="icon-people"></i><span id="onlineCount" class="label label-success">0</span>
	</a>
	<script>
		function refreshOnlineCount(){
			$.get('${ctx}/sys/online/count?__notUpdateSession=true&__t='
					+ new Date().getTime(), function(data){
				if (!(data && data.result != 'login')){
					if (window.rocInt) {
						clearInterval(window.rocInt);
					}
				}
				try{$('#onlineCount').html(Number(data))}catch(e){}
			})
		}
		refreshOnlineCount(); // 先执行一次
		window.rocInt = setInterval(refreshOnlineCount, 180000); // 3分钟执行一次
		$(function(){$('#btnOnline').tooltip()});
	</script>
</li>