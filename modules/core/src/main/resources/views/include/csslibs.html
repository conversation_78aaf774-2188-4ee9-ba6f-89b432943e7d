<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */ %>
<link rel="stylesheet" href="${ctxStatic}/fonts/font-icons.min.css">
<link rel="stylesheet" href="${ctxStatic}/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet" href="${ctxStatic}/select2/4.0/select2.css?${_version}">
<link rel="stylesheet" href="${ctxStatic}/icheck/1.0/minimal/grey.css?${_version}">
<% if (@ListUtils.inString('zTree', libs!)){ %>
<link rel="stylesheet" href="${ctxStatic}/jquery-ztree/3.5/css/awesome/zTreeStyle.css?${_version}">
<% } %>
<% if (@ListUtils.inString('tabPage', libs!)){ %>
<link rel="stylesheet" href="${ctxStatic}/wdScrollTab/css/TabPanel.css?${_version}">
<% } %>
<% if (@ListUtils.inString('dataGrid', libs!)){ %>
<link rel="stylesheet" href="${ctxStatic}/jqGrid/4.7/css/ui.jqgrid.css?${_version}">
<% } %>
<% if (@ListUtils.inString('layout', libs!)){ %>
<link rel="stylesheet" href="${ctxStatic}/jquery-plugins/jquery.layout-latest.css?${_version}">
<% } %>
<% if (@ListUtils.inString('fileupload', libs!)){ %>
<link rel="stylesheet" href="${ctxStatic}/webuploader/0.1/webuploader.extend.css?${_version}">
<% } %>
<link rel="stylesheet" href="${ctxStatic}/adminlte/css/AdminLTE.min.css?${_version}">
<link rel="stylesheet" href="${ctxStatic}/common/jeesite.css?${_version}">
