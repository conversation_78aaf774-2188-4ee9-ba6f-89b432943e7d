<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>Dockerfile</name>
	<filePath>${baseDir}/${moduleCode}/${moduleCode}/bin/docker</filePath>
	<fileName>Dockerfile</fileName>
	<content><![CDATA[FROM openjdk:8-slim
LABEL maintainer="<EMAIL>"
ENV TZ "Asia/Shanghai"
ENV LANG C.UTF-8
VOLUME /tmp

WORKDIR /app

#RUN mkdir WEB-INF
#ADD jeesite.lic ./WEB-INF
ADD ./maven/web.war ./app.war

#ENV JAVA_OPTS "$JAVA_OPTS -Xms256m -Xmx1024m"
ENV JAVA_OPTS "$JAVA_OPTS -Dspring.profiles.active=prod"

ENTRYPOINT jar -xvf app.war && rm app.war && cd WEB-INF && sh startup.sh

EXPOSE 8983

#docker run -p 8983:8983 thinkgem/jeesite-cloud-module-${moduleCode}:5.0
]]>
	</content>
</template>