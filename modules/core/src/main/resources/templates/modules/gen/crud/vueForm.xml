<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>vueForm</name>
	<filePath>${frontDir}/src/views/${urlPrefix}</filePath>
	<fileName>form.vue</fileName>
	<content><![CDATA[
<% var modalOrDrawer = @StringUtils.contains(table.tplCategory, '_modal') ? 'Modal' : 'Drawer'; %>
<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <Basic${modalOrDrawer}
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'${permissionPrefix}:edit'"
    @register="register${modalOrDrawer}"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <% if (table.childList.~size > 0){ %>
    <BasicForm @register="registerForm">
      <% for (child in table.childList){ %>
      <template #${@StringUtils.uncap(child.classNameSimple)}List>
        <Form${@StringUtils.cap(child.classNameSimple)}List ref="form${@StringUtils.cap(child.classNameSimple)}ListRef" />
      </template>
      <% } %>
    </BasicForm>
    <% }else{ %>
    <BasicForm @register="registerForm" />
    <% } %>
    <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
    <template #footer>
      <BpmButton
        v-model:bpmEntity="record"
        bpmEntityKey="id"
        formKey="${table.optionMap['bpmFormKey']}"
        completeText="提交"
        :completeModal="true"
        :loading="loadingRef"
        :auth="'${permissionPrefix}:edit'"
        @validate="handleValidate"
        @complete="handleSubmit"
        @success="handleSuccess"
        @close="close${modalOrDrawer}"
      />
    </template>
    <% } %>
  </Basic${modalOrDrawer}>
</template>
<script lang="ts" setup name="${compNamePrefix}Form">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { Basic${modalOrDrawer}, use${modalOrDrawer}Inner } from '/@/components/${modalOrDrawer}';
  import { ${ClassName}, ${className}Save, ${className}Form<% if(table.isTreeEntity){ %>, ${className}TreeData<% } %> } from '/@/api/${moduleName}${isNotEmpty(subModuleName)?'/'+subModuleName:''}/${className}';
  <%
  var userselectExists = false;
  var officeselectExists = false;
  var companyselectExists = false;
  var areaselectExists = false;
  for(c in table.columnList){
    if(c.isQuery == "1" && !c.isTreeEntityColumn){
      if(c.showType == 'userselect'){
        userselectExists = true;
      }else if(c.showType == 'officeselect'){
        officeselectExists = true;
      }else if(c.showType == 'companyselect'){
        companyselectExists = true;
      }else if(c.showType == 'areaselect'){
        areaselectExists = true;
      }
    }
  }
  %>
  <% if(userselectExists || officeselectExists) { %>
  import { officeTreeData } from '/@/api/sys/office';
  <% } %>
  <% if(companyselectExists) { %>
  import { companyTreeData } from '/@/api/sys/company';
  <% } %>
  <% if(areaselectExists) { %>
  import { areaTreeData } from '/@/api/sys/area';
  <% } %>
  <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
  import { BpmButton } from '/@/components/Bpm';
  <% } %>
  <% for (child in table.childList){ %>
  import Form${@StringUtils.cap(child.classNameSimple)}List from './form${@StringUtils.cap(child.classNameSimple)}List.vue';
  <% } %>

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('${moduleName}${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${className}');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<${ClassName}>({} as ${ClassName});
  <% for (child in table.childList){ %>
  const form${@StringUtils.cap(child.classNameSimple)}ListRef = ref<InstanceType<typeof Form${@StringUtils.cap(child.classNameSimple)}List>>();
  <% } %>
  <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
  const loadingRef = ref(false);
  <% } %>

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增${functionNameSimple}') : t('编辑${functionNameSimple}'),
  }));

  const inputFormSchemas: FormSchema[] = [
  <% if(table.isTreeEntity){ %>
    {
      label: t('上级${functionNameSimple}'),
      field: 'parentCode',
      fieldLabel: 'parentName',
      component: 'TreeSelect',
      componentProps: {
        allowClear: true,
        style: 'width: calc(50% - 60px)',
      },
      colProps: { md: 24, lg: 24 },
    },
<% }
for (c in table.columnList){
  if (c.isEdit == '1' && c.showType != 'hidden'){
    // 如果是树结构的字段，则自动忽略
    if(table.isTreeEntity && @StringUtils.inString(c.columnName, 'parent_code',
      'parent_codes', 'tree_sorts', 'tree_leaf', 'tree_level', 'tree_names')
        && c.attrName != table.treeViewCodeAttrName
        && c.attrName != table.treeViewNameAttrName){
      continue;
    }
    // 是否强制新行获取，生成字段界面用户设定的
    var isNewLine = @Global.YES.equals(c.optionMap['isNewLine']);
    if (isBlank(c.optionMap['isNewLine'])){
      if (c.showType == 'textarea'){
        isNewLine = true;
      }
    }
  %>
    {
      label: t('${c.columnLabel}'),
      field: '${c.attrName}',
  <% if(c.showType == 'input' || c.showType == 'textarea'){ %>
    <% if (c.simpleAttrType == 'Integer' && c.attrName == 'treeSort'){ %>
      helpMessage: '升序',
      component: 'InputNumber',
      defaultValue: '30',
      <% }else{ %>
      component: '${c.showType == 'input' ? 'Input' : 'InputTextArea'}',
      <% } %>
      <% if (c.dataLength != '0'){ %>
      componentProps: {
        maxlength: ${c.dataLength},
      },
    <% } %>
  <% }else if(c.showType == 'select' || c.showType == 'select_multiple'){
    var isMultiple = (c.showType == 'select_multiple'); %>
      component: 'Select',
      componentProps: {
        dictType: '${c.optionMap['dictType']}',
        allowClear: true,
        <% if(isMultiple){ %>
        mode: 'multiple',
        <% } %>
      },
  <% }else if(c.showType == 'radio' || c.showType == 'checkbox'){ %>
      component: '${@StringUtils.cap(c.showType)}Group',
      componentProps: {
        dictType: '${c.optionMap['dictType']}',
      },
  <% }else if(c.showType == 'date' || c.showType == 'datetime'){
    var isTime = (c.showType == 'datetime'); %>
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD${isTime?' HH:mm':''}',
        showTime: ${isTime?'{ format: \'HH:mm\' \}':'false'},
      },
  <% }else if(c.showType == 'userselect'){
      if (isNotBlank(c.attrName2)){ %>
      fieldLabel: '${c.attrName2}',
      <% } %>
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        params: { isLoadUser: true, userIdPrefix: '' },
        canSelectParent: false,
        allowClear: true,
      },
  <% }else if(c.showType == 'officeselect'){
      if (isNotBlank(c.attrName2)){ %>
      fieldLabel: '${c.attrName2}',
      <% } %>
      component: 'TreeSelect',
      componentProps: {
        api: officeTreeData,
        canSelectParent: false,
        allowClear: true,
      },
  <% }else if(c.showType == 'companyselect'){
      if (isNotBlank(c.attrName2)){ %>
      fieldLabel: '${c.attrName2}',
      <% } %>
      component: 'TreeSelect',
      componentProps: {
        api: companyTreeData,
        canSelectParent: false,
        allowClear: true,
      },
  <% }else if(c.showType == 'areaselect'){
      if (isNotBlank(c.attrName2)){ %>
      fieldLabel: '${c.attrName2}',
      <% } %>
      component: 'TreeSelect',
      componentProps: {
        api: areaTreeData,
        canSelectParent: false,
        allowClear: true,
      },
  <% }else{ %>
      component: 'Input',
  <% } 
  var fieldValid = c.optionMap['fieldValid'], fvs = [], rules = [];
  if(isNotEmpty(fieldValid)){
    var t = type.name(fieldValid);
    if (t == 'String[]' || t == 'ArrayList'){
      fvs = fieldValid;
    }else if(t == 'String' && isNotBlank(fieldValid)){
      @fvs.add(fieldValid);
    }
  }
  for(var fv in fvs){
    if (fv == 'email'){
      var s = { %>{ type: 'email', message: t('请输入邮箱地址') }<% };
      @rules.add(s);
    }
    if (fv == 'number'){
      var s = { %>{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }<% };
      @rules.add(s);
    }
    if (fv == 'integer'){
      var s = { %>{ pattern: /^-?\d+$/, message: t('请输入一个整数') }<% };
      @rules.add(s);
    }
    if (fv == 'digits'){
      var s = { %>{ pattern: /^\d+$/, message: t('请输入一个正整数') }<% };
      @rules.add(s);
    }
    if (fv == 'userName'){
      var s = { %>{ pattern: /^[\u0391-\uFFE5\w]+$/, message: t('请输入登录账号') }<% };
      @rules.add(s);
    }
    if (fv == 'realName'){
      var s = { %>{ pattern: /^[\u4e00-\u9fa5]{2,30}$/, message: t('请输入真实姓名') }<% };
      @rules.add(s);
    }
    if (fv == 'abc'){
      var s = { %>{ pattern: /^[a-zA-Z0-9_]*$/, message: t('请输入字母数字下划线') }<% };
      @rules.add(s);
    }
    if (fv == 'mobile'){
      var s = { %>{ pattern: /^1[3,4,5,6,7,8,9]\d{9}$/g, message: t('请输入手机号码') }<% };
      @rules.add(s);
    }
    if (fv == 'simplePhone'){
      var s = { %>{ pattern: /^(\d{3,4}-?)?\d{7,9}$/g, message: t('请输入固话号码') }<% };
      @rules.add(s);
    }
    if (fv == 'phone'){
      var s = { %>{ pattern: /(^0[1-9]{1}\d{8,10}$)|(^1[3,4,5,6,7,8,9]\d{9}$)/g, message: t('请输入固话或手机号码') }<% };
      @rules.add(s);
    }
    if (fv == 'zipCode'){
      var s = { %>{ pattern: /^[0-9]{6}$/, message: t('请输入邮政编码') }<% };
      @rules.add(s);
    }
  }
  if(rules.~size == 0){
    if(c.isRequired == '1'){
  %>      required: true,
  <%
    }
  } else { %>
      rules: [<%
    if(c.isRequired == '1'){ 
          %>{ required: true }, <%
        }
        for (var rule in rules){
          print(rule);
          if (ruleLP.index < rules.~size) {
            print(', ');
          }
        } %>],
  <%
    }
    if (isNewLine){ %>
      colProps: { md: 24, lg: 24 },
  <%
    }
  %>
    },
  <%
  }
} %>
  <% if(toBoolean(table.optionMap['isImageUpload'])){ %>
    {
      label: t('图片上传'),
      field: 'dataMap',
      component: 'Upload',
      componentProps: {
        loadTime: computed(() => record.value.__t),
        bizKey: computed(() => record.value.id),
        bizType: '${className}_image',
        uploadType: 'image',
      },
      colProps: { md: 24, lg: 24 },
    },
  <%
  }
  if(toBoolean(table.optionMap['isFileUpload'])){ %>
    {
      label: t('附件上传'),
      field: 'dataMap',
      component: 'Upload',
      componentProps: {
        loadTime: computed(() => record.value.__t),
        bizKey: computed(() => record.value.id),
        bizType: '${className}_file',
        uploadType: 'all',
      },
      colProps: { md: 24, lg: 24 },
    },
  <%
  }
  for (child in table.childList){ %>
    {
      label: t('${child.comments}'),
      field: '${@StringUtils.uncap(child.classNameSimple)}List',
      component: 'Input',
      colProps: { md: 24, lg: 24 },
      slot: '${@StringUtils.uncap(child.classNameSimple)}List',
    },
  <%
  }
  if(false && toBoolean(table.optionMap['isBpmForm'])){ %>
    {
      label: t('审批意见'),
      field: 'bpm.comment',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { md: 24, lg: 24 },
      show: () => record.value.bpm.status != '2',
    },
    {
      label: t('下一步流程信息'),
      field: 'nextTaskInfo',
      component: 'FormGroup',
      colProps: { md: 24, lg: 24 },
    },
    {
      label: t('要求完成时间'),
      field: 'bpm.dueDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('任务优先级'),
      field: 'bpm.priority',
      component: 'Select',
      componentProps: {
        dictType: 'bpm_task_priority',
        allowClear: true,
      },
    },
    {
      label: t('下一步处理人'),
      field: 'bpm.nextUserCodes',
      component: 'ListSelect',
      componentProps: {
        selectType: 'empUserSelect',
      },
    },
<% } %>
  ];
<%
var updateSchemas = [];
if(table.isTreeEntity){
  var s = {
%>      {
        field: 'parentCode',
        componentProps: {
          api: ${className}TreeData,
          params: {
            excludeCode: record.value.id,
            isShowRawName: true,
          },
        },
      },<%
  };
  @updateSchemas.add(s);
}
for (c in table.columnList){
  if (c.isPk == '1' && c.showType == 'input'){
    var s = {
%>      {
        field: '${c.attrName}',
        componentProps: {
          disabled: !record.value.isNewRecord,
        },
      },<%
    };
    @updateSchemas.add(s);
  }
}
%>
  const [registerForm, { resetFields, setFieldsValue<% if(updateSchemas.~size > 0){ %>, updateSchema<% } %>, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    <% var formColNum = table.optionMap['formColNum']; %>
    baseColProps: { md: 24, lg: ${formColNum=="1"?24:formColNum=="3"?8:12} },
  });

  const [register${modalOrDrawer}, { set${modalOrDrawer}Props, close${modalOrDrawer} }] = use${modalOrDrawer}Inner(async (data) => {
    set${modalOrDrawer}Props({ loading: true });
    await resetFields();
    const res = await ${className}Form(data);
    record.value = (res.${className} || {}) as ${ClassName};
    record.value.__t = new Date().getTime();
  <% if(table.isTreeEntity){ %>
    if (data.parentCode && data.parentName) {
      record.value.parentCode = data.parentCode;
      record.value.parentName = data.parentName;
    }
  <% } %>
    setFieldsValue(record.value);
  <% for (child in table.childList){ %>
    form${@StringUtils.cap(child.classNameSimple)}ListRef.value?.setTableData(record.value);
  <% } %>
  <% if(updateSchemas.~size > 0){ %>
    updateSchema([
    <% for(updateSchema in updateSchemas){
      print(updateSchema + '\n');  
    } %>
    ]);
  <% } %>
    set${modalOrDrawer}Props({ loading: false });
  });
  <% if(toBoolean(table.optionMap['isBpmForm'])){ %>

  async function handleValidate(_event: any, formData: any) {
    try {
      const data = await validate();
      formData(true, data); // 将表单数据传递给 BpmButton
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(error.message || t('common.validateError'));
      }
      console.log('error', error);
    }
  }
  <% } %>

  async function handleSubmit(<% if(toBoolean(table.optionMap['isBpmForm'])){ %>event: any<% } %>) {
    try {
    <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
      loadingRef.value = true;
      const data = event?.formData || (await validate()); // 接受 BpmButton 传递过来的表单数据
      data.bpm = Object.assign(data.bpm || {}, record.value.bpm); // 流程信息
      data.status = record.value.status; // 提交状态
    <% } else { %>
      const data = await validate();
    <% } %>
      set${modalOrDrawer}Props({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
  <%
  for (c in table.columnList){
    if (c.isPk == '1' || c.showType == 'hidden'){ %>
        ${c.attrName}: record.value.${c.attrName},
      <% 
    }
  }
  %>
      };
    <% for (child in table.childList){ %>
      await form${@StringUtils.cap(child.classNameSimple)}ListRef.value?.getTableData(data);
    <% } %>
    <% if(table.isTreeEntity){ %>
      data.oldParentCode = record.value.parentCode;
    <% } %>
      // console.log('submit', params, data, record);
      const res = await ${className}Save(params, data);
      showMessage(res.message);
      setTimeout(close${modalOrDrawer});
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(error.message || t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
      loadingRef.value = false;
      <% } %>
      set${modalOrDrawer}Props({ confirmLoading: false });
    }
  }
  <% if(toBoolean(table.optionMap['isBpmForm'])){ %>

  async function handleSuccess() {
    emit('success');
  }
  <% } %>
</script>
<% %>
]]>
	</content>
</template>