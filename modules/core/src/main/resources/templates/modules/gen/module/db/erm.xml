<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>erm</name>
	<filePath>${baseDir}/${moduleCode}/db</filePath>
	<fileName>${moduleCode}.erm</fileName>
	<content><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<diagram>
	<page_setting>
		<direction_horizontal>true</direction_horizontal>
		<scale>100</scale>
		<paper_size>A4 210 x 297 mm</paper_size>
		<top_margin>30</top_margin>
		<left_margin>30</left_margin>
		<bottom_margin>30</bottom_margin>
		<right_margin>30</right_margin>
	</page_setting>
	<category_index>0</category_index>
	<zoom>1.0</zoom>
	<x>0</x>
	<y>0</y>
	<default_color>
		<r>128</r>
		<g>128</g>
		<b>192</b>
	</default_color>
	<color>
		<r>255</r>
		<g>255</g>
		<b>255</b>
	</color>
	<font_name>Arial</font_name>
	<font_size>14</font_size>
	<settings>
		<database>StandardSQL</database>
		<capital>false</capital>
		<table_style></table_style>
		<notation></notation>
		<notation_level>0</notation_level>
		<notation_expand_group>true</notation_expand_group>
		<view_mode>2</view_mode>
		<outline_view_mode>1</outline_view_mode>
		<view_order_by>1</view_order_by>
		<auto_ime_change>false</auto_ime_change>
		<validate_physical_name>true</validate_physical_name>
		<use_bezier_curve>false</use_bezier_curve>
		<suspend_validator>false</suspend_validator>
		<export_setting>
			<export_ddl_setting>
				<output_path>db/${moduleCode}.sql</output_path>
				<encoding>UTF-8</encoding>
				<line_feed>CR+LF</line_feed>
				<is_open_after_saved>false</is_open_after_saved>
				<environment_id>7be191506f9daa8070b3ac14921dffd44063d2bb</environment_id>
				<category_id>null</category_id>
				<ddl_target>
					<create_comment>true</create_comment>
					<create_foreignKey>false</create_foreignKey>
					<create_index>true</create_index>
					<create_sequence>false</create_sequence>
					<create_table>true</create_table>
					<create_tablespace>false</create_tablespace>
					<create_trigger>false</create_trigger>
					<create_view>false</create_view>
					<drop_index>false</drop_index>
					<drop_sequence>false</drop_sequence>
					<drop_table>false</drop_table>
					<drop_tablespace>false</drop_tablespace>
					<drop_trigger>false</drop_trigger>
					<drop_view>false</drop_view>
					<inline_column_comment>false</inline_column_comment>
					<inline_table_comment>true</inline_table_comment>
					<comment_value_description>false</comment_value_description>
					<comment_value_logical_name>true</comment_value_logical_name>
					<comment_value_logical_name_description>false</comment_value_logical_name_description>
					<comment_replace_line_feed>false</comment_replace_line_feed>
					<comment_replace_string></comment_replace_string>
				</ddl_target>
			</export_ddl_setting>
			<export_excel_setting>
				<category_id>null</category_id>
				<output_path>db/${moduleCode}.xls</output_path>
				<template></template>
				<template_path></template_path>
				<used_default_template_lang>en</used_default_template_lang>
				<image_output></image_output>
				<is_open_after_saved>true</is_open_after_saved>
				<is_put_diagram>true</is_put_diagram>
				<is_use_logical_name>true</is_use_logical_name>
			</export_excel_setting>
			<export_html_setting>
				<output_dir></output_dir>
				<with_category_image>true</with_category_image>
				<with_image>true</with_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_html_setting>
			<export_image_setting>
				<output_file_path>db/${moduleCode}.png</output_file_path>
				<category_dir_path></category_dir_path>
				<with_category_image>true</with_category_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_image_setting>
			<export_java_setting>
				<java_output></java_output>
				<package_name></package_name>
				<class_name_suffix></class_name_suffix>
				<src_file_encoding></src_file_encoding>
				<with_hibernate>false</with_hibernate>
			</export_java_setting>
			<export_testdata_setting>
				<file_encoding></file_encoding>
				<file_path></file_path>
				<format>0</format>
			</export_testdata_setting>
		</export_setting>
		<category_settings>
			<free_layout>false</free_layout>
			<show_referred_tables>false</show_referred_tables>
			<categories>
			</categories>
		</category_settings>
		<translation_settings>
			<use>false</use>
			<translations>
			</translations>
		</translation_settings>
		<model_properties>
			<id></id>
			<height>-1</height>
			<width>-1</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>50</x>
			<y>50</y>
			<color>
				<r>255</r>
				<g>255</g>
				<b>255</b>
			</color>
			<connections>
			</connections>
			<display>false</display>
			<creation_date>2016-12-25 17:25:00</creation_date>
			<model_property>
				<name>Project Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Model Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Version</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Company</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Author</name>
				<value></value>
			</model_property>
		</model_properties>
		<table_properties>
			<schema></schema>
		</table_properties>
		<environment_setting>
			<environment>
				<id>7be191506f9daa8070b3ac14921dffd44063d2bb</id>
				<name>Default</name>
			</environment>
		</environment_setting>
	</settings>
	<dictionary>
		<word>
			<id>136ca02f1b3a96a8f2e242d5dd64d48f566143ef</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>租户代码</description>
			<logical_name>租户代码</logical_name>
			<physical_name>corp_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>f116706ac00cd3a3ee88b2a88debf7ebc3eeb12d</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>租户名称</description>
			<logical_name>租户名称</logical_name>
			<physical_name>corp_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>1a3b3f8bccfce8894d117d6bdb0a6b104bfb80ef</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>创建者</logical_name>
			<physical_name>create_by</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>dba1aec0c72d79ea73ed4ebde07696cf4df174b7</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>创建时间</logical_name>
			<physical_name>create_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>f01926071736b56b898949cc0720149c71504324</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 1</logical_name>
			<physical_name>extend_d1</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>942700093ab61c3be0bdf6b23bcba210bcc30281</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 2</logical_name>
			<physical_name>extend_d2</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>f221902bf89fe94dece8ccf309f59cc2c479d63f</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 3</logical_name>
			<physical_name>extend_d3</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>c8d21e24bc69aac295703b0bae56269035b729f0</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 4</logical_name>
			<physical_name>extend_d4</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>4c0cc4ae32f8774cc319f516784430204aef0bdb</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 1</logical_name>
			<physical_name>extend_f1</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>2e958c528620621985af4394590198feed57cdf9</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 2</logical_name>
			<physical_name>extend_f2</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>7c25ad75662553c7d4a58fa66eb50ca7c0ffee59</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 3</logical_name>
			<physical_name>extend_f3</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>1f81990c9694963f032c302d1834b972a6f2eb74</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 4</logical_name>
			<physical_name>extend_f4</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>a1d747cd40768ac9f85176518ee48cb513bae110</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 1</logical_name>
			<physical_name>extend_i1</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>a9f0e14d6691c397990abe4ef1ff21674dccf401</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 2</logical_name>
			<physical_name>extend_i2</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>2ce06c5cf87d93bb1e3f47268dbc679be4b6dd8d</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 3</logical_name>
			<physical_name>extend_i3</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>53d8c730fcec69d341f44089817ae06eb4844278</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 4</logical_name>
			<physical_name>extend_i4</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>80cd53da9d5a1b19676537e590e20fa2793e902c</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 JSON</logical_name>
			<physical_name>extend_json</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>ad6f9eff50476669df62b7601cbc3a2e0c905d36</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 1</logical_name>
			<physical_name>extend_s1</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>3a24133d2be4831e99d1319983e5393bcf964ff9</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 2</logical_name>
			<physical_name>extend_s2</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>40e1afbbad28d28e371dd1ab77fb56640b1cb66b</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 3</logical_name>
			<physical_name>extend_s3</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>a2d6b5a494fc1a3d29360d922296521c6640856b</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 4</logical_name>
			<physical_name>extend_s4</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>29f701cc6a308fbfc5b12b80fee621ceeb231dcc</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 5</logical_name>
			<physical_name>extend_s5</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>c9a37a7b6a5451930ca63e36814767f742cd1393</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 6</logical_name>
			<physical_name>extend_s6</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>8c26203d310a4e602cf0c0fc8a7b2c818219c1dc</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 7</logical_name>
			<physical_name>extend_s7</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>54448f19b0f5d1630bf29f9f99787802c36ebddb</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 8</logical_name>
			<physical_name>extend_s8</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>869fc70cf3a4e92e8056b40814df8e03f9f9efde</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>编号</logical_name>
			<physical_name>id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b18ce64a2a72d00b26515583d8bbfea282f30ea8</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>父级编号</logical_name>
			<physical_name>parent_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>5887f9db78a9ebc7b23b9a163c6f68100257c0e5</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>所有父级编号</logical_name>
			<physical_name>parent_codes</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>8f7d3761c17a1b8632d186a3c67cb08dca18c498</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>备注信息</logical_name>
			<physical_name>remarks</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>ba0fb53af3ccc8b0e5d73baa58ec27fbb7973097</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>状态（0正常 1删除 2停用 3冻结 4审核 5驳回 9草稿）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>17718c2364a2368c2072da279c927d7ad3bfcf08</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>状态（0正常 1删除 2停用）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>23b25e48c87be0e4f3952f7a8330594e9d511a4e</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>区域选择</logical_name>
			<physical_name>test_area_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>8bc627205b6e55931d09079fcd07bfacbbd38f41</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>区域名称</logical_name>
			<physical_name>test_area_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>cdc46f802299958a2b3140200410caf5cbeb8a27</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>复选框</logical_name>
			<physical_name>test_checkbox</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>d829e707316a49d39e5a11da5f5d36030a856b86</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>父表主键</logical_name>
			<physical_name>test_data_id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>a27ebfd109532e2f551b101a0c78b3f50a3d58dd</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>日期选择</logical_name>
			<physical_name>test_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>95ec23c1b2704817cca94d169aed5e11deb0adbb</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>日期时间</logical_name>
			<physical_name>test_datetime</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>ac02f2d1fff63fa66db583735eb1c8e9b466872f</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>单行文本</logical_name>
			<physical_name>test_input</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>e2270df5e0974bf471fdfe36e29bc9a8aa1774cb</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>机构选择</logical_name>
			<physical_name>test_office_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>791691b791efedc2d14867a152b4de58f4567326</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>单选框</logical_name>
			<physical_name>test_radio</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>6697b12cf2f6d7135570c2a581212ee067c13206</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>下拉框</logical_name>
			<physical_name>test_select</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>68b9381c1fb6668186fef70f52fc0c14b1bd6f1c</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>下拉多选</logical_name>
			<physical_name>test_select_multiple</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>ae103dc1fb50094552e56afd72015481271913db</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>排序号</logical_name>
			<physical_name>test_sort</physical_name>
			<type>integer</type>
		</word>
		<word>
			<id>2fed92437d28f1427e913aba1251fe0807c7b209</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>多行文本</logical_name>
			<physical_name>test_textarea</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>2eda719a62fb7e1d3face62555046d938abddda9</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>用户选择</logical_name>
			<physical_name>test_user_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>70e9482ae432d16e734a730100e366ddab33564c</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>节点编码</logical_name>
			<physical_name>tree_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>40b63ab3e485fd55370d6d04b063c4397483ebc1</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>是否最末级</logical_name>
			<physical_name>tree_leaf</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>3b9c3307b7140f27edeb47ffe307a662b2856627</id>
			<length>4</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>层次级别</logical_name>
			<physical_name>tree_level</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>5356a60d0801c47941dd2fb4565cf785bb58e2d3</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>节点名称</logical_name>
			<physical_name>tree_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>bf5e60dee567b3ed0f12e8f3ffa1f74e43012b3a</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>全节点名</logical_name>
			<physical_name>tree_names</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>16c9c333062ea3614d2e044803b872676c8a5377</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>排序号（升序）</logical_name>
			<physical_name>tree_sort</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>a886757c87fb1e04c5f84b6a802e129baaec2ca6</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>所有排序号</logical_name>
			<physical_name>tree_sorts</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>2c6b290f30c6f9e100f6c77eab0cba7bb3386768</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>更新者</logical_name>
			<physical_name>update_by</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>f0bdbc4002f4a1b7fb7c5026bc21a7689c549728</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>更新时间</logical_name>
			<physical_name>update_date</physical_name>
			<type>datetime</type>
		</word>
	</dictionary>
	<tablespace_set>
	</tablespace_set>
	<contents>
		<table>
			<id>e553474c37270813e70025e433a4cf8a64653e13</id>
			<height>438</height>
			<width>387</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>864</x>
			<y>36</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>test_tree</physical_name>
			<logical_name>测试树表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>70e9482ae432d16e734a730100e366ddab33564c</word_id>
					<id>7e417ee9d0dd69c767a5853922621946ed4fb2d8</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>a535b6c506004a7fdf4d48984c9ff2cfa59c157a</column_group>
				<normal_column>
					<word_id>5356a60d0801c47941dd2fb4565cf785bb58e2d3</word_id>
					<id>39f501890586173d229e83610cfbfaa6e3a85374</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>5435ef11ea53f170fe3491b199c113e47932e175</id>
			<height>401</height>
			<width>320</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>36</x>
			<y>36</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>test_data</physical_name>
			<logical_name>测试数据</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>869fc70cf3a4e92e8056b40814df8e03f9f9efde</word_id>
					<id>d82778c36626013cd39fd790da6f55a9762f0c76</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ac02f2d1fff63fa66db583735eb1c8e9b466872f</word_id>
					<id>3e14b40dc07c134329a40752973acfad2ffdc48b</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2fed92437d28f1427e913aba1251fe0807c7b209</word_id>
					<id>8050d948828b16267482e9e3716219321f206b81</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6697b12cf2f6d7135570c2a581212ee067c13206</word_id>
					<id>3664605e054c39531ca8e91aa4463c955a993357</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>68b9381c1fb6668186fef70f52fc0c14b1bd6f1c</word_id>
					<id>f7b189ecdc92c78bfaae9c736318ab6a5aedd396</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>791691b791efedc2d14867a152b4de58f4567326</word_id>
					<id>6e4837ab55b592669ccc7ebfc8b1ed96a00607b3</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>cdc46f802299958a2b3140200410caf5cbeb8a27</word_id>
					<id>9fb81bdc12ed017f62e9dd4529025e536eff7f08</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a27ebfd109532e2f551b101a0c78b3f50a3d58dd</word_id>
					<id>9d9478798ed2766a81b0e5a8a022eb89d9c5cc34</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>95ec23c1b2704817cca94d169aed5e11deb0adbb</word_id>
					<id>c131dc5cb7ce6f1aa5e8d5f86f2002c8ae1bb8f6</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2eda719a62fb7e1d3face62555046d938abddda9</word_id>
					<id>cfe3c330968a9a824c2cf933b227887d3e9615ac</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e2270df5e0974bf471fdfe36e29bc9a8aa1774cb</word_id>
					<id>23041cb30875514136904ea11043c7ea1924f048</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>23b25e48c87be0e4f3952f7a8330594e9d511a4e</word_id>
					<id>b6d5dc8745c0fa484ed6f14e90a42282ca1b285d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8bc627205b6e55931d09079fcd07bfacbbd38f41</word_id>
					<id>d244401ff7302bb5b75092016531952d093b238b</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>5a836e654b7b7d19f102e87336fefa079d98a2e6</id>
			<height>438</height>
			<width>346</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>468</x>
			<y>36</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>test_data_child</physical_name>
			<logical_name>测试数据子表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>869fc70cf3a4e92e8056b40814df8e03f9f9efde</word_id>
					<id>34477707bcf8a7810e12e9565aff085f6fb1e0ad</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ae103dc1fb50094552e56afd72015481271913db</word_id>
					<id>ec8a047dffe3cf2e4d95ba3e26c3bac0382d95c9</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>integer</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>d829e707316a49d39e5a11da5f5d36030a856b86</word_id>
					<id>b095c44611ed08156277676d9a3a0ce52b9b05ef</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ac02f2d1fff63fa66db583735eb1c8e9b466872f</word_id>
					<id>78715aa66a10a9b190ad69b8ed792e9a2f4946e3</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2fed92437d28f1427e913aba1251fe0807c7b209</word_id>
					<id>693f8af2fe6cf45255b6f2bc7e9077f3630c3dd8</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6697b12cf2f6d7135570c2a581212ee067c13206</word_id>
					<id>04216c384533b70b8e86b571fa2beef7157bab5a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>68b9381c1fb6668186fef70f52fc0c14b1bd6f1c</word_id>
					<id>0b6f63094be90aa8674460cce26be10327827ceb</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>791691b791efedc2d14867a152b4de58f4567326</word_id>
					<id>363e8cfcf4a6b228e656decadacba29280906555</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>cdc46f802299958a2b3140200410caf5cbeb8a27</word_id>
					<id>7875aa5fee3d6f9587180fb8fce9343a56cdc34e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a27ebfd109532e2f551b101a0c78b3f50a3d58dd</word_id>
					<id>61c49a2f7807c55c8f85110c30889d5b403e4b04</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>95ec23c1b2704817cca94d169aed5e11deb0adbb</word_id>
					<id>b91171b99f3628aadb0e6986fbfa30cc547b280e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2eda719a62fb7e1d3face62555046d938abddda9</word_id>
					<id>9f33d190101e56f93f5ece0fd7c5cdda3e704b4c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e2270df5e0974bf471fdfe36e29bc9a8aa1774cb</word_id>
					<id>16121b5aa08fc170883408ec2d0487281a132d9d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>23b25e48c87be0e4f3952f7a8330594e9d511a4e</word_id>
					<id>1afc7f146271f5c90ea811aa24c08ce25d12552e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8bc627205b6e55931d09079fcd07bfacbbd38f41</word_id>
					<id>343f0db997b913f299b0496c4306d3617ad708de</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
	</contents>
	<column_groups>
			<column_group>
				<id>845c82ebd869d5620b1ef2c2b6f438b11a045082</id>
				<group_name>BaseEntity</group_name>
				<columns>
					<normal_column>
						<word_id>869fc70cf3a4e92e8056b40814df8e03f9f9efde</word_id>
						<id>02ecedc0de5850cba25bc91919ed39d414b74111</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>ba0fb53af3ccc8b0e5d73baa58ec27fbb7973097</word_id>
						<id>2fe6a36385238c1b21c76deae00a7afa00ff5538</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>ea920cba2fe0eaee64a2310ece7cda4b198b37ec</id>
				<group_name>BaseEntityCorp</group_name>
				<columns>
					<normal_column>
						<word_id>136ca02f1b3a96a8f2e242d5dd64d48f566143ef</word_id>
						<id>b8ea4b73d6d32a222e5abfd453287575ae518480</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value>0</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f116706ac00cd3a3ee88b2a88debf7ebc3eeb12d</word_id>
						<id>b94f5fe344185c40739cf93d1090686001bb11e0</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value>JeeSite</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>35ae805d1da92afdb99b2fe8c536d1649356fccd</id>
				<group_name>DataEntity</group_name>
				<columns>
					<normal_column>
						<word_id>17718c2364a2368c2072da279c927d7ad3bfcf08</word_id>
						<id>f0036584bd8711715579d21994a0105935605a7e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value>0</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>1a3b3f8bccfce8894d117d6bdb0a6b104bfb80ef</word_id>
						<id>c391a15752a8eb58bc558a39d1b431f7ee125e0e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>dba1aec0c72d79ea73ed4ebde07696cf4df174b7</word_id>
						<id>e2e82ba86e15fd67397355e711255b1625078ae1</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>2c6b290f30c6f9e100f6c77eab0cba7bb3386768</word_id>
						<id>fd0546fc2d4e01c35dcbc23913add68a99fabd73</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f0bdbc4002f4a1b7fb7c5026bc21a7689c549728</word_id>
						<id>f8ea4fc4a778a0b94398a661a1ed8608f0e8d28d</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8f7d3761c17a1b8632d186a3c67cb08dca18c498</word_id>
						<id>69e01b6d4f42df40a09540ef4ba10ed8e006abaa</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>85024a2953cf3e3c9c1cce49b2351853ab0d125b</id>
				<group_name>DataEntityNoStatus</group_name>
				<columns>
					<normal_column>
						<word_id>1a3b3f8bccfce8894d117d6bdb0a6b104bfb80ef</word_id>
						<id>e5355faba5ec3c9128507dd4c48ea9230631cf83</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>dba1aec0c72d79ea73ed4ebde07696cf4df174b7</word_id>
						<id>6bed374c39d181003a4f92d76d79a4119176ba0c</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>2c6b290f30c6f9e100f6c77eab0cba7bb3386768</word_id>
						<id>f9db19bb567760bbdd554d75bbfdc891c89f9da9</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f0bdbc4002f4a1b7fb7c5026bc21a7689c549728</word_id>
						<id>ee78b079f7d319bf8119fd01439cd97424ff49fa</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8f7d3761c17a1b8632d186a3c67cb08dca18c498</word_id>
						<id>f7b88ecec0ef386bb384c228842a7587432112fb</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>118dab95fc1f792cd468b9f66af2d4fabd98c39b</id>
				<group_name>ExtendEntity</group_name>
				<columns>
					<normal_column>
						<word_id>ad6f9eff50476669df62b7601cbc3a2e0c905d36</word_id>
						<id>6ccadddab6ce48441ca7abd798cda6f3debf4a0c</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>3a24133d2be4831e99d1319983e5393bcf964ff9</word_id>
						<id>93ab0ba3b47b01934614dbd3e572358c9f99e6ea</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>40e1afbbad28d28e371dd1ab77fb56640b1cb66b</word_id>
						<id>a78c7961910a5e697027d1a3530b1afaa8ea8c94</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a2d6b5a494fc1a3d29360d922296521c6640856b</word_id>
						<id>40085364ec7a58653e96f8659aadd258d7556bc7</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>29f701cc6a308fbfc5b12b80fee621ceeb231dcc</word_id>
						<id>9787d7fe93ee31c5b4979fd620ff6e4b2777eccf</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>c9a37a7b6a5451930ca63e36814767f742cd1393</word_id>
						<id>95c55b81b7e9e1a9bb01aa3d88fb90c648641c4e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8c26203d310a4e602cf0c0fc8a7b2c818219c1dc</word_id>
						<id>16f44dfc7964796f109293bc49afd58dcb4eec1f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>54448f19b0f5d1630bf29f9f99787802c36ebddb</word_id>
						<id>39b1dffa083f74afc30df621845cf7f0ed71394f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a1d747cd40768ac9f85176518ee48cb513bae110</word_id>
						<id>7584cc6360ae7edc99e1f619042eba5865b2c4c7</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a9f0e14d6691c397990abe4ef1ff21674dccf401</word_id>
						<id>f0b5383e05c6b3f6e5f65b33b33009826c83d014</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>2ce06c5cf87d93bb1e3f47268dbc679be4b6dd8d</word_id>
						<id>260d5f31009fff18000d1e64f4f877926e621306</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>53d8c730fcec69d341f44089817ae06eb4844278</word_id>
						<id>a83144f40e7ae64e46a4b4ed651379774a953b17</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>4c0cc4ae32f8774cc319f516784430204aef0bdb</word_id>
						<id>2a5203a275171a250870cf6cb224a910aa9354ec</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>2e958c528620621985af4394590198feed57cdf9</word_id>
						<id>3ef5bd65a7dcd74b9a9d8a292ec395f66b7de32b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>7c25ad75662553c7d4a58fa66eb50ca7c0ffee59</word_id>
						<id>01d0849bdda56a8d8f24befdadc3fc9b007ae92b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>1f81990c9694963f032c302d1834b972a6f2eb74</word_id>
						<id>1c8ed63d72f40f0fe2f05815675771bdf3f824f8</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f01926071736b56b898949cc0720149c71504324</word_id>
						<id>2b49e875138bfb329aaa352629650b7881435123</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>942700093ab61c3be0bdf6b23bcba210bcc30281</word_id>
						<id>5c6ec16226d85b0411b7077cb9a6e0c7aa8d74d1</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f221902bf89fe94dece8ccf309f59cc2c479d63f</word_id>
						<id>d92b8f7fa7a2be49c7f00c447a603b136e84261d</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>c8d21e24bc69aac295703b0bae56269035b729f0</word_id>
						<id>095a76f07a3cd2bdc6cc442757c11012e1974f4a</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>80cd53da9d5a1b19676537e590e20fa2793e902c</word_id>
						<id>42c5d8f490f69b93e77698efa030ca23988ae696</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>a535b6c506004a7fdf4d48984c9ff2cfa59c157a</id>
				<group_name>TreeEntity</group_name>
				<columns>
					<normal_column>
						<word_id>b18ce64a2a72d00b26515583d8bbfea282f30ea8</word_id>
						<id>394369b90c0a5b6efeed3cf823c642605d7a1653</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>5887f9db78a9ebc7b23b9a163c6f68100257c0e5</word_id>
						<id>e8d877396943acfec73023dba2c1c6e3d7802d62</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>16c9c333062ea3614d2e044803b872676c8a5377</word_id>
						<id>23f973124aedd0244533f4e7b3b103c548b966be</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a886757c87fb1e04c5f84b6a802e129baaec2ca6</word_id>
						<id>984d5eac2b3221118a61655e4a5a49c78e0f0151</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>40b63ab3e485fd55370d6d04b063c4397483ebc1</word_id>
						<id>b2f246a3f0ade317eaa9915e2fd539abae5a5ec8</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>3b9c3307b7140f27edeb47ffe307a662b2856627</word_id>
						<id>f5a9968479420f08da2e98d21136b3ed4b6e396f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>bf5e60dee567b3ed0f12e8f3ffa1f74e43012b3a</word_id>
						<id>618194ebfc8c6c42efcef3a4af0b8054f6af209b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
	</column_groups>
	<test_data_list>
	</test_data_list>
	<sequence_set>
	</sequence_set>
	<trigger_set>
	</trigger_set>
	<change_tracking_list>
	</change_tracking_list>
</diagram>]]>
	</content>
</template>