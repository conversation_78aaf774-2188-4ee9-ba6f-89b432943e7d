<%
	// 输出子表控件
	for (child in table.childList){
%>
				<div class="form-unit">\${text('${child.comments}')}</div>
				<div class="form-unit-wrap table-form">
					<table id="${@StringUtils.uncap(child.classNameSimple)}DataGrid"></table>
				<% if(table.tplCategory != 'query'){ %>
					\<% if (hasPermi('${permissionPrefix}:edit')){ %>
						<a href="#" id="${@StringUtils.uncap(child.classNameSimple)}DataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> \${text('增行')}</a>
					\<% } %>
				<% } %>
				</div>
<%
	}
%>