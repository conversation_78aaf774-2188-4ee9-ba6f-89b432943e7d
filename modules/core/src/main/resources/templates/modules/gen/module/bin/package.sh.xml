<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>package</name>
	<filePath>${baseDir}/${moduleCode}/bin</filePath>
	<fileName>package.sh</fileName>
	<content><![CDATA[#!/bin/sh
# /**
#  * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
#  * No deletion without permission, or be held responsible to law.
#  *
#  * Author: <EMAIL>
#  */
echo ""
echo "[信息] 打包安装工程，生成jar包文件。"
echo ""

mvn -v
echo ""

cd ..
mvn clean install -Dmaven.test.skip=true -Ppackage

cd bin]]>
	</content>
</template>