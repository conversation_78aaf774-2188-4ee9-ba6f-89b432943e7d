<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>entity</name>
	<filePath>${baseDir}/src/main/java/${packagePath}/${moduleName}/entity/${subModuleName}</filePath>
	<fileName>${ClassName}.java</fileName>
	<content><![CDATA[
package ${packageName}.${moduleName}.entity${isNotBlank(subModuleName)?'.'+subModuleName:''};

<% if(table.childList.~size > 0){ %>
import javax.validation.Valid;
<% } %>
<% for(i in table.importList){ %>
import ${i};
<% } %>

import com.jeesite.common.entity.DataEntity;
<% if(table.isTreeEntity){ %>
import com.jeesite.common.entity.TreeEntity;
<% } %>
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
<% if(toBoolean(table.optionMap['isBpmForm'])){ %>
	<% if(table.isTreeEntity){ %>
import com.jeesite.modules.bpm.entity.BpmTreeEntity;
	<% }else{ %>
import com.jeesite.modules.bpm.entity.BpmEntity;
	<% } %>
<% } %>
<% if(!table.parentExists && toBoolean(table.optionMap['isImportExport'])){ %>
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
<% } %>

/**
 * ${functionName}Entity
 * <AUTHOR>
 * @version ${functionVersion}
 */
@Table(name="${table.genTableName}", alias="a", label="${functionNameSimple}信息", columns={
		<%
		var isBase = false, isData = false,
			isTree = false, isExtend = false;
		// ◆ 生成字段属性
		for(c in table.columnList){
			// ● 如果是BaseEntity类属性
			if(table.isBaseEntity && c.isBaseEntityColumn){
				if(!isBase){
					isBase = true;
		%>
		@Column(includeEntity=BaseEntity.class),
		<%
				}
			// ● 如果是DataEntity类属性
			}else if(table.isDataEntity && c.isDataEntityColumn){
				if(!isData){
					isData = true;
		%>
		@Column(includeEntity=DataEntity.class),
		<%
				}
			// ● 如果是TreeEntity类属性
			}else if(table.isTreeEntity && c.isTreeEntityColumn){
				if(c.columnName == 'parent_code'){
		%>
		@Column(name="${c.columnName}", attrName="${c.attrName}", label="${c.columnLabel}", isParentCode=true),
		<%
				}
				if(!isTree){
					isTree = true;
		%>
		@Column(includeEntity=TreeEntity.class),
		<%
				}
			// ● 如果是Extend类属性
			}else if(table.isExtendEntity && c.isExtendColumn){
				if(!isExtend){
					isExtend = true;
		%>
		@Column(includeEntity=Extend.class, attrName="extend"),
		<%
				}
			// ● 其它情况下
			}else{
				// 容错，如果没有设置父表的主键属性，则设置
				if(!@StringUtils.contains(c.attrName, ".")){
					if (table.parentExists && table.parentTableFkName == c.columnName){
						for (pk in table.parent.pkList){
							c.fullAttrName = c.fullAttrName + '.' + pk.attrName;
							break;
						}
					}
				}
		%>
		@Column(name="${c.columnName}", attrName="${c.attrName}", label="${c.columnLabel}"<%
				if (c.comments != c.columnLabel){
					print(', comment="'+c.comments+'"');
				}
				if (c.isPk == @Global.YES){
					print(', isPK=true');
				}else{
					if (c.isInsert != @Global.YES){
						print(', isInsert=false');
					}
					if (c.isUpdate != @Global.YES){
						print(', isUpdate=false');
					}
					if (c.isQuery == @Global.YES && @StringUtils.inString(c.queryType,
							'NE', 'GT', 'GTE', 'LT', 'LTE', 'LIKE', 'LEFT_LIKE', 'RIGHT_LIKE')){
						print(', queryType=QueryType.'+c.queryType);
					}
					if (c.isQuery != @Global.YES){
						print(', isQuery=false');
					}
					if (c.attrName == table.treeViewNameAttrName){
						print(', isTreeName=true');
					}
					if (c.isNull == @Global.YES && @StringUtils.inString(c.simpleAttrType,
							'Long', 'Integer', 'Double', 'BigDecimal', 'Date')){
						print(', isUpdateForce=true');
					}
				}
		%>),
		<%
			}
		}
		%>
	}, <%
		// ◆ 生成关联表
		var joinTables = '';
		for(c in table.columnList){
			if (c.attrType == 'com.jeesite.modules.sys.entity.User'){
				var joinTable = {
	%>
		@JoinTable(type=Type.LEFT_JOIN, entity=User.class, attrName="${c.simpleAttrName}", alias="u${cLP.index}",
			on="u${cLP.index}.user_code = a.${c.columnName}", columns={
				@Column(name="user_code", label="用户编码", isPK=true),
				@Column(name="user_name", label="用户名称", isQuery=false),
		}),
	<%
				};
				joinTables = joinTables + joinTable;
			}
			else if (c.attrType == 'com.jeesite.modules.sys.entity.Office'){
				var joinTable = {
	%>
		@JoinTable(type=Type.LEFT_JOIN, entity=Office.class, attrName="${c.simpleAttrName}", alias="u${cLP.index}",
			on="u${cLP.index}.office_code = a.${c.columnName}", columns={
				@Column(name="office_code", label="机构编码", isPK=true),
				@Column(name="office_name", label="机构名称", isQuery=false),
		}),
	<%
				};
				joinTables = joinTables + joinTable;
			}
		}
		if (isNotBlank(joinTables)){
			print('joinTable={');
			print(joinTables);
			print('}, ');
		}
		// ◆ 生成排序字段
	%>orderBy="<% if (isTree){
						%>a.tree_sorts<%
							for(pk in table.pkList){
								%>, a.${pk.columnName}<%
							}
					}else if(table.parentExists && table.createDateExists){
						%>a.create_date ASC<%
					}else if(table.updateDateExists){
						%>a.update_date DESC<%
					}else{
						for(pk in table.pkList){
							%>${pkLP.index!=1?', ':''}a.${pk.columnName} ${table.parentExists?'ASC':'DESC'}<%
						}
					} %>"
)
public class ${ClassName} extends ${toBoolean(table.optionMap['isBpmForm'])?(table.isTreeEntity?'BpmTree':'Bpm'):table.isTreeEntity?'Tree':'Data'}Entity<${ClassName}> {
	
	private static final long serialVersionUID = 1L;
	<%
	isExtend = false;
	// 生成字段属性
	for(c in table.columnList){
		// 如果是Extend类属性
		if(table.isExtendEntity && c.isExtendColumn){
			if(!isExtend){
				isExtend = true;
	%>
	private Extend extend;		// 扩展字段
	<%
			}
		}
		// 如果不是基类属性
		else if(!@StringUtils.equalsIgnoreCase(c.simpleAttrName, 'id') && !c.isSuperColumn){
			// 父类对象
			if(table.parentExists && table.parentTableFkName == c.columnName){
	%>
	private ${@StringUtils.cap(table.parent.className)} ${c.simpleAttrName};		<% if (isNotBlank(c.comments)){ %>// ${c.comments} 父类<% } %>
	<%
			// 其它字段
			}else{
	%>
	private ${c.simpleAttrType} ${c.simpleAttrName};		<%if(isNotBlank(c.comments)){%>// ${c.comments}<%}%>
	<%
			}
		}
	}
	
	// 生成子表列表字段
	for(child in table.childList){
	%>
	private List<${@StringUtils.cap(child.className)}> ${@StringUtils.uncap(child.classNameSimple)}List = ListUtils.newArrayList();		// 子表列表
	<%
	}
	
	// 生成构造方法
	%>

	<% if(!table.parentExists && toBoolean(table.optionMap['isImportExport'])){ %>
	@ExcelFields({
		<% for(c in table.columnList){ if(c.optionMap['isImportExport'] == @Global.YES){ %>
		@ExcelField(title="${c.columnLabel}", attrName="${c.attrName}"${
			isNotBlank(c.optionMap['dictType'])?', dictType="'+c.optionMap['dictType']+'"':''
		}, align=Align.CENTER, sort=${c.columnSort}${
			c.showType == 'date'?', dataFormat="yyyy-MM-dd"':c.showType == 'datetime'?', dataFormat="yyyy-MM-dd hh:mm"':''
		}),
		<% } } %>
	})
	<% } %>
	public ${ClassName}() {
		this(<% for(pk in table.pkList){ %>${pkLP.index!=1?', ':''}null<% } %>);
	}
	<%
	// 生成带主键参数的构造
	if (!table.parentExists){
		if (table.pkList.~size == 1){ %>
	
	public ${ClassName}(String id){
		super(id);
	}
	<%	}else{ %>
	
	public ${ClassName}(<% for(pk in table.pkList){ %>${pkLP.index!=1?', ':''}${pk.simpleAttrType} ${pk.simpleAttrName}<% } %>){
		<% for(pk in table.pkList){ %>
		this.${pk.simpleAttrName} = ${pk.simpleAttrName};
		<% } %>
	}
	<%
		}
	}
	
	// 生成父表参数的构造
	else{
		for(c in table.columnList){
			if(table.parentExists && table.parentTableFkName == c.columnName){
	%>

	public ${ClassName}(${@StringUtils.cap(table.parent.className)} ${c.simpleAttrName}){
		this.${c.simpleAttrName} = ${c.simpleAttrName};
	}
	<%
			}
		}
	}
	
	// 如果是树实体，则输出相应方法
	if (table.isTreeEntity){
	%>
	
	@Override
	public ${ClassName} getParent() {
		return parent;
	}

	@Override
	public void setParent(${ClassName} parent) {
		this.parent = parent;
	}
	<%
	}
	
	// 生成属性的get和set方法 
	isExtend = false;
	for(c in table.columnList){
		// 如果是Extend类属性
		if(c.isExtendColumn){
				if(!isExtend){
					isExtend = true;
	%>
	
	public Extend getExtend() {
		return extend;
	}

	public void setExtend(Extend extend) {
		this.extend = extend;
	}
	<%
				}
		// 如果不是基类属性
		}else if(!@StringUtils.equalsIgnoreCase(c.simpleAttrName, 'id') && !c.isSuperColumn){
	%>
	
	<%
			// 父类对象
			if(table.parentExists && table.parentTableFkName == c.columnName){
	%>
	public ${@StringUtils.cap(table.parent.className)} get${@StringUtils.cap(c.simpleAttrName)}() {
		return ${c.simpleAttrName};
	}

	public void set${@StringUtils.cap(c.simpleAttrName)}(${@StringUtils.cap(table.parent.className)} ${c.simpleAttrName}) {
		this.${c.simpleAttrName} = ${c.simpleAttrName};
	}
	<%
			// 其它字段
			}else{
				for(a in c.simpleAnnotationList){
	%>
	@${a}
	<%
				}
	%>
	public ${c.simpleAttrType} get${@StringUtils.cap(c.simpleAttrName)}() {
		return ${c.simpleAttrName};
	}

	public void set${@StringUtils.cap(c.simpleAttrName)}(${c.simpleAttrType} ${c.simpleAttrName}) {
		this.${c.simpleAttrName} = ${c.simpleAttrName};
	}
	<%
			}
		}
	}
	
	// 生成条件字段get和set方法（范围类型）
	for(c in table.columnList){
		if(c.isQuery == "1" && c.queryType == "BETWEEN"){
	%>
	
	public ${c.simpleAttrType} get${@StringUtils.cap(c.simpleAttrName)}_gte() {
		return sqlMap.getWhere().getValue("${c.columnName}", QueryType.GTE);
	}

	public void set${@StringUtils.cap(c.simpleAttrName)}_gte(${c.simpleAttrType} ${c.simpleAttrName}) {
		sqlMap.getWhere().and("${c.columnName}", QueryType.GTE, ${c.simpleAttrName});
	}
	
	public ${c.simpleAttrType} get${@StringUtils.cap(c.simpleAttrName)}_lte() {
		return sqlMap.getWhere().getValue("${c.columnName}", QueryType.LTE);
	}

	public void set${@StringUtils.cap(c.simpleAttrName)}_lte(${c.simpleAttrType} ${c.simpleAttrName}) {
		sqlMap.getWhere().and("${c.columnName}", QueryType.LTE, ${c.simpleAttrName});
	}
	<%
		}
	}
	
	// 生成子表列表get和set方法
	for(child in table.childList){
	%>
	
	@Valid
	public List<${@StringUtils.cap(child.className)}> get${@StringUtils.cap(child.classNameSimple)}List() {
		return ${@StringUtils.uncap(child.classNameSimple)}List;
	}

	public void set${@StringUtils.cap(child.classNameSimple)}List(List<${@StringUtils.cap(child.className)}> ${@StringUtils.uncap(child.classNameSimple)}List) {
		this.${@StringUtils.uncap(child.classNameSimple)}List = ${@StringUtils.uncap(child.classNameSimple)}List;
	}
	<% } %>
	
}]]>
	</content>
</template>