<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>controller</name>
	<filePath>${baseDir}/${moduleName}-client/src/main/java/${packagePath}/${moduleName}/api/${subModuleName}</filePath>
	<fileName>${ClassName}ServiceApi.java</fileName>
	<content><![CDATA[
package ${packageName}.${moduleName}.api${isNotEmpty(subModuleName)?'.'+subModuleName:''};

import org.springframework.web.bind.annotation.RequestMapping;

import com.jeesite.common.service.rest.${table.isTreeEntity?'Tree':'Crud'}ServiceRest;
import ${packageName}.${moduleName}.entity${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName};

/**
 * ${functionName}API
 * <AUTHOR>
 * @version ${functionVersion}
 */
@RequestMapping(value = "/inner/api/${urlPrefix}")
public interface ${ClassName}ServiceApi extends ${table.isTreeEntity?'Tree':'Crud'}ServiceRest<${ClassName}> {
	
}]]>
	</content>
</template>