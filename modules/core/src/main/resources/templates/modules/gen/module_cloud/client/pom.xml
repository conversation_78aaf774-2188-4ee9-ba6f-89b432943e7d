<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>pom</name>
	<filePath>${baseDir}/${moduleCode}/${moduleCode}-client</filePath>
	<fileName>pom.xml</fileName>
	<charset></charset>
	<content><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.jeesite</groupId>
		<artifactId>jeesite-cloud-parent</artifactId>
		<version>${jeesiteVersion}-SNAPSHOT</version>
		<relativePath>../../../parent/pom.xml</relativePath>
	</parent>
	
	<artifactId>jeesite-cloud-module-${moduleCode}-client</artifactId>
	<packaging>jar</packaging>
	
	<name>JeeSite Cloud Module ${moduleName} Client</name>
	<url>http://jeesite.com</url>
	<inceptionYear>2013-Now</inceptionYear>
	
	<dependencies>
		
		<!-- 云客户端 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-cloud-framework</artifactId>
			<version>\${project.parent.version}</version>
		</dependency>
		
	</dependencies>
	
	<developers>
		<developer>
			<id>thinkgem</id>
			<name>WangZhen</name>
			<email>thinkgem at 163.com</email>
			<roles><role>Project lead</role></roles>
			<timezone>+8</timezone>
		</developer>
	</developers>
	
	<organization>
		<name>JeeSite</name>
		<url>http://jeesite.com</url>
	</organization>
	
</project>
]]>
	</content>
</template>