<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>vueList</name>
	<filePath>${frontDir}/src/views/${urlPrefix}</filePath>
	<fileName>list.vue</fileName>
	<content><![CDATA[
<% var modalOrDrawer = @StringUtils.contains(table.tplCategory, '_modal') ? 'Modal' : 'Drawer'; %>
<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable"<% if(table.isTreeEntity){ %> @fetch-success="fetchSuccess"<% } %>>
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <% if(table.isTreeEntity){ %>
        <a-button @click="expandAll" :title="t('展开一级')">
          <Icon icon="i-bi:chevron-double-down" /> {{ t('展开') }}
        </a-button>
        <a-button @click="collapseAll" :title="t('折叠全部')">
          <Icon icon="i-bi:chevron-double-up" /> {{ t('折叠') }}
        </a-button>
        <% } %>
        <% if(toBoolean(table.optionMap['isImportExport'])){ %>
        <a-button type="default" :loading="loading" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <a-button type="default" @click="handleImport()">
          <Icon icon="i-ant-design:upload-outlined" /> {{ t('导入') }}
        </a-button>
        <% } %>
        <a-button type="primary" @click="handleForm({})" v-auth="'${permissionPrefix}:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
<%
var idParam = '';
for(pk in table.pkList){
  idParam = idParam + (pk.attrName + ': record.' + pk.attrName);
  if (pkLP.index != table.pkList.~size) {
    idParam = idParam + ', ';
  }
}
if(table.isTreeEntity){
%>
	<% if(table.isPkCustom){ %>
        <span class="cursor-pointer" @click="expandCollapse(record)">
          ( {{ record.${table.treeViewCodeAttrName} }} )
        </span>
    <% } %>
        <a @click="handleForm({ ${idParam} })">
          {{ record.${table.treeViewNameAttrName} }}
        </a>
<%
}else{
  for(c in table.columnList){ 
    if(c.isList == "1"){
      // 如果是树结构的字段，则自动忽略
      if(table.isTreeEntity && @StringUtils.inString(c.columnName, 'parent_code',
        'parent_codes', 'tree_sorts', 'tree_leaf', 'tree_level', 'tree_names')
          && c.attrName != table.treeViewCodeAttrName
          && c.attrName != table.treeViewNameAttrName){
        continue;
      }
%>
        <a @click="handleForm({ ${idParam} })">
          {{ record.${c.attrName} }}
        </a>
<%
      break;
    }
  }
}
%>
      </template>
    </BasicTable>
    <% if(!@StringUtils.contains(table.tplCategory, '_route')) { %>
    <InputForm @register="register${modalOrDrawer}" @success="handleSuccess" />
    <% } %>
    <% if(toBoolean(table.optionMap['isImportExport'])){ %>
    <FormImport @register="registerImportModal" @success="handleSuccess" />
    <% } %>
    <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
    <BpmRuntimeTrace @register="registerTraceModal" />
    <% } %>
  </div>
</template>
<script lang="ts" setup name="${compNamePrefix}List">
  import { unref<% if(toBoolean(table.optionMap['isImportExport'])){ %>, ref<% } %><% if(table.isTreeEntity || isNotBlank(table.optionMap['leftTreeRightTableFk'])){ %>, watch<% }
  	%><% if(table.isTreeEntity){ %>, nextTick<% } %> } from 'vue';
  <% if(@StringUtils.contains(table.tplCategory, '_route')) { %>
  import { useEmitter } from '/@/store/modules/user';
  <% } %>
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  <% if(toBoolean(table.optionMap['isImportExport'])){ %>
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  <% } %>
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { ${className}Delete, ${className}ListData } from '/@/api/${moduleName}${isNotEmpty(subModuleName)?'/'+subModuleName:''}/${className}';
  <% if(toBoolean(table.optionMap['isHaveDisableEnable'])){ %>
  import { ${className}Disable, ${className}Enable } from '/@/api/${moduleName}${isNotEmpty(subModuleName)?'/'+subModuleName:''}/${className}';
  <% } %>
  <%
  var userselectExists = false;
  var officeselectExists = false;
  var companyselectExists = false;
  var areaselectExists = false;
  for(c in table.columnList){
    if(c.isQuery == "1" && !c.isTreeEntityColumn){
      if(c.showType == 'userselect'){
        userselectExists = true;
      }else if(c.showType == 'officeselect'){
        officeselectExists = true;
      }else if(c.showType == 'companyselect'){
        companyselectExists = true;
      }else if(c.showType == 'areaselect'){
        areaselectExists = true;
      }
    }
  }
  %>
  <% if(userselectExists || officeselectExists) { %>
  import { officeTreeData } from '/@/api/sys/office';
  <% } %>
  <% if(companyselectExists) { %>
  import { companyTreeData } from '/@/api/sys/company';
  <% } %>
  <% if(areaselectExists) { %>
  import { areaTreeData } from '/@/api/sys/area';
  <% } %>
  <% if(modalOrDrawer == 'Drawer' && !@StringUtils.contains(table.tplCategory, '_route')){ %>
  import { useDrawer } from '/@/components/Drawer';
  <% } %>
  <% if(modalOrDrawer == 'Modal' || (toBoolean(table.optionMap['isBpmForm'])
       || toBoolean(table.optionMap['isImportExport']))){ %>
  import { useModal } from '/@/components/Modal';
  <% } %>
  <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
  import { BpmRuntimeTrace } from '/@/components/Bpm';
  <% } %>
  import { FormProps } from '/@/components/Form';
  <% if(!@StringUtils.contains(table.tplCategory, '_route')) { %>
  import InputForm from './form.vue';
  <% } %>
  <% if(toBoolean(table.optionMap['isImportExport'])){ %>
  import FormImport from './formImport.vue';
  <% } %>
  <% if(table.isTreeEntity || isNotBlank(table.optionMap['leftTreeRightTableFk'])){ %>

  const props = defineProps({
    treeCode: String,
  });
  <% } %>
  <% if(@StringUtils.contains(table.tplCategory, '_route')) { %>

  const emitter = useEmitter();
  <% } %>

  const { t } = useI18n('${moduleName}${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${className}');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('${functionNameSimple}管理'),
  };
  <% if(toBoolean(table.optionMap['isImportExport'])){ %>
  const loading = ref(false);
  <% } %>

  const searchForm: FormProps = {
    baseColProps: { md: 8, lg: 6 },
    labelWidth: 90,
    schemas: [
<% for(c in table.columnList){ %>
  <% if(c.isQuery == "1" && !c.isTreeEntityColumn){ %>
      {
        label: t('${c.columnLabel}${c.queryType == 'BETWEEN'?'起':''}'),
        field: '${c.attrName}${c.queryType == 'BETWEEN'?'_gte':''}',
    <% if(c.showType == 'input' || c.showType == 'textarea'){ %>
        component: 'Input',
      <% if (c.queryType == 'BETWEEN'){ %>
      },
      {
        field: '${c.attrName}${c.queryType == 'BETWEEN'?'_lte':''}',
        label: t('${c.columnLabel}${c.queryType == 'BETWEEN'?'止':''}'),
        component: 'Input',
      <% } %>
    <% }else if(c.showType == 'select' || c.showType == 'select_multiple'){
    var isMultiple = (c.showType == 'select_multiple'); %>
        component: 'Select',
        componentProps: {
          dictType: '${c.optionMap['dictType']}',
          allowClear: true,
          <% if(isMultiple){ %>
          mode: 'multiple',
          <% } %>
          <% if(c.columnName == 'status'){ %>
          onChange: handleSuccess,
          <% } %>
        },
    <% }else if(c.showType == 'radio' || c.showType == 'checkbox'){ %>
        component: '${@StringUtils.cap(c.showType)}Group',
        componentProps: {
          dictType: '${c.optionMap['dictType']}',
        },
    <% }else if(c.showType == 'date' || c.showType == 'datetime'){
    var isTime = (c.showType == 'datetime'); %>
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD${isTime?' HH:mm':''}',
          showTime: ${isTime?'{ format: \'HH:mm\' \}':'false'},
        },
      <% if (c.queryType == 'BETWEEN'){ %>
      },
      {
        label: t('${c.columnLabel}${c.queryType == 'BETWEEN'?'止':''}'),
        field: '${c.attrName}${c.queryType == 'BETWEEN'?'_lte':''}',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD${isTime?' HH:mm':''}',
          showTime: ${isTime?'{ format: \'HH:mm\' \}':'false'},
        },
      <% } %>
    <% }else if(c.showType == 'userselect'){ %>
        component: 'TreeSelect',
        componentProps: {
          api: officeTreeData,
          params: { isLoadUser: true, userIdPrefix: '' },
          canSelectParent: false,
          allowClear: true,
        },
    <% }else if(c.showType == 'officeselect'){ %>
        component: 'TreeSelect',
        componentProps: {
          api: officeTreeData,
          allowClear: true,
        },
    <% }else if(c.showType == 'companyselect'){ %>
        component: 'TreeSelect',
        componentProps: {
          api: companyTreeData,
          allowClear: true,
        },
    <% }else if(c.showType == 'areaselect'){ %>
        component: 'TreeSelect',
        componentProps: {
          api: areaTreeData,
          allowClear: true,
        },
    <% }else{ %>
        component: 'Input',
    <% } %>
      },
  <% } %>
<% } %>
    ],
  };

  const tableColumns: BasicColumn[] = [
<%
var firstColumn = true;
// 生成树表的节点列
if(table.isTreeEntity){
  for(c in table.columnList){
    if(c.attrName == table.treeViewNameAttrName){
    %>
    {
      title: t('${c.columnLabel}'),
      dataIndex: '${c.attrName}',
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    <%
      firstColumn = false;
      break;
    }
  }
}
for(c in table.columnList){
  if(c.isList == "1"){
    // 如果是树结构的字段，则自动忽略
    if(table.isTreeEntity && (@StringUtils.inString(c.columnName, 'parent_code',
      'parent_codes', 'tree_sorts', 'tree_leaf', 'tree_level', 'tree_names')
        || c.attrName == table.treeViewCodeAttrName
        || c.attrName == table.treeViewNameAttrName)){
      continue;
    }
%>
    {
      title: t('${c.columnLabel}'),
    <% if(c.showType == "userselect" || c.showType == "officeselect" || c.showType == "companyselect" || c.showType == "areaselect"){ %>
      dataIndex: '${c.attrName2}',
    <% }else{ %>
      dataIndex: '${c.attrName}',
    <% } %>
    <% if(!table.isTreeEntity){ %>
      key: 'a.${c.columnName}',
      sorter: true,
    <% } %>
    <% if(firstColumn){ %>
      width: 230,
    <% }else{ %>
      width: 130,
    <% } %>
    <% if ((isNotBlank(c.optionMap['dictType']) || @StringUtils.inString(c.attrType, 'java.util.Date', 'Integer', 'Long')) && !firstColumn){ %>
      align: 'center',
    <% }else if (@StringUtils.inString(c.attrType, 'Float', 'Double') && !firstColumn){ %>
      align: 'right',
    <% }else{ %>
      align: 'left',
    <% } %>
    <% if(c.showType == 'select' || c.showType == 'select_multiple' || c.showType == 'checkbox' || c.showType == 'radio'){ %>
      dictType: '${c.optionMap['dictType']}',
    <% } %>
    <% if(firstColumn){ %>
      slot: 'firstColumn',
    <% } %>
    },
<%
    if(firstColumn){
      firstColumn = false;
    }
  }
}
%>
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑${functionNameSimple}'),
        onClick: handleForm.bind(this, { ${idParam} }),
        auth: '${permissionPrefix}:edit',
      },
      <% if(toBoolean(table.optionMap['isHaveDisableEnable'])){ %>
      {
        icon: 'i-ant-design:stop-outlined',
        color: 'error',
        title: t('停用${functionNameSimple}'),
        popConfirm: {
          title: t('是否确认停用${functionNameSimple}'),
          confirm: handleDisable.bind(this, record),
        },
        auth: '${permissionPrefix}:edit',
        ifShow: () => record.status === '0',
      },
      {
        icon: 'i-ant-design:check-circle-outlined',
        color: 'success',
        title: t('启用${functionNameSimple}'),
        popConfirm: {
          title: t('是否确认启用${functionNameSimple}'),
          confirm: handleEnable.bind(this, record),
        },
        auth: '${permissionPrefix}:edit',
        ifShow: () => record.status === '2',
      },
      <% } %>
      <% if(toBoolean(table.optionMap['isHaveDelete'])){ %>
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除${functionNameSimple}'),
        popConfirm: {
          title: t('是否确认删除${functionNameSimple}'),
          confirm: handleDelete.bind(this, record),
        },
        auth: '${permissionPrefix}:edit',
        <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
        ifShow: () => record.status == '9',
        <% } %>
      },
      <% } %>
      <% if(table.isTreeEntity){ %>
      {
        icon: 'i-fluent:add-circle-24-regular',
        title: t('新增下级${functionNameSimple}'),
        onClick: handleForm.bind(this, {
          parentCode: record.id,
          parentName: record.${table.treeViewNameAttrName},
        }),
        auth: '${permissionPrefix}:edit',
      },
      <% } %>
      <% if(toBoolean(table.optionMap['isBpmForm'])){ %>
      {
        icon: 'i-fluent:flowchart-20-regular',
        title: t('流程追踪'),
        onClick: handleTrace.bind(this, record),
        ifShow: () => record.status != '9',
      },
      <% } %>
    ],
  };

  const [registerTable, { reload<% if(table.isTreeEntity){ %>, expandAll, collapseAll, expandCollapse<% } %><%
  		if (table.isTreeEntity || isNotBlank(table.optionMap['leftTreeRightTableFk'])
  		    || toBoolean(table.optionMap['isImportExport'])){ %>, getForm<% } %> }] = useTable({
    api: ${className}ListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    <% if(table.isTreeEntity){ %>
    isTreeTable: true,
    pagination: false,
    <% } %>
    canResize: true,
  });

  <% if(!@StringUtils.contains(table.tplCategory, '_route')) { %>
  const [register${modalOrDrawer}, { open${modalOrDrawer} }] = use${modalOrDrawer}();
  <% } %>
  <% if(table.isTreeEntity || isNotBlank(table.optionMap['leftTreeRightTableFk'])){ %>

  watch(
    () => props.treeCode,
    async () => {
      await getForm().setFieldsValue({
    <% if (isNotBlank(table.optionMap['leftTreeRightTableFk'])) { %>
        '${table.optionMap['leftTreeRightTableFk']}': props.treeCode,
    <% }else if(table.isTreeEntity){ %>
      <% for(pk in table.pkList){ %>
        '${pk.attrName}': props.treeCode,
      <% } %>
    <% } %>
      });
      reload();
    },
  );
  <% } %>
  <% if(table.isTreeEntity){ %>

  function fetchSuccess() {
    if (props.treeCode) {
      nextTick(expandAll);
    }
  }
  <% } %>

  function handleForm(record: Recordable) {
    <% if(!@StringUtils.contains(table.tplCategory, '_route')) { %>
    open${modalOrDrawer}(true, record);
    <% } else { %>
    router.push({
      path: '/${urlPrefix}/formRoute',
      query: record,
    });
    <% } %>
  }
  <% if(toBoolean(table.optionMap['isImportExport'])){ %>

  async function handleExport() {
    loading.value = true;
    const { ctxAdminPath } = useGlobSetting();
    await downloadByUrl({
      url: ctxAdminPath + '/${urlPrefix}/exportData',
      params: getForm().getFieldsValue(),
    });
    loading.value = false;
  }

  const [registerImportModal, { openModal: importModal }] = useModal();

  function handleImport() {
    importModal(true, {});
  }
  <% } %>
  <% if(toBoolean(table.optionMap['isHaveDisableEnable'])){ %>

  async function handleDisable(record: Recordable) {
    const params = { ${idParam} };
    const res = await ${className}Disable(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  async function handleEnable(record: Recordable) {
    const params = { ${idParam} };
    const res = await ${className}Enable(params);
    showMessage(res.message);
    handleSuccess(record);
  }
  <% } %>

  async function handleDelete(record: Recordable) {
    const params = { ${idParam} };
    const res = await ${className}Delete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
  <% if(toBoolean(table.optionMap['isBpmForm'])){ %>

  const [registerTraceModal, { openModal: traceModel }] = useModal();

  function handleTrace(record: Recordable) {
    traceModel(true, { formKey: '${table.optionMap['bpmFormKey']}', bizKey: record.id });
  }
  <% } %>
  <% if(@StringUtils.contains(table.tplCategory, '_route')) { %>

  emitter.on('${moduleName}${isNotEmpty(subModuleName)?'-'+subModuleName:''}-${className}-reload', reload, true);
  <% } %>
</script>
<% %>
]]>
	</content>
</template>