<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>client</name>
	<filePath>${baseDir}/${moduleName}-client/src/main/java/${packagePath}/${moduleName}/client/${subModuleName}</filePath>
	<fileName>${ClassName}ServiceClient.java</fileName>
	<content><![CDATA[
package ${packageName}.${moduleName}.client${isNotEmpty(subModuleName)?'.'+subModuleName:''};

import org.springframework.cloud.openfeign.FeignClient;

import com.jeesite.modules.cloud.feign.condition.ConditionalOnNotCurrentApplication;
import ${packageName}.${moduleName}.api${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName}ServiceApi;

/**
 * ${functionName}API
 * <AUTHOR>
 * @version ${functionVersion}
 */
@FeignClient(name="\${service.${moduleName}.name}", path="\${service.${moduleName}.path}")
@ConditionalOnNotCurrentApplication(name="\${service.${moduleName}.name}")
public interface ${ClassName}ServiceClient extends ${ClassName}ServiceApi {
	
}]]>
	</content>
</template>