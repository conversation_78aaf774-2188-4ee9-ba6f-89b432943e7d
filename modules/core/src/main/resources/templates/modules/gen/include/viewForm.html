<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */%>
<%
	var extLibs = '';
	if(toBoolean(table.optionMap['isImageUpload']) || toBoolean(table.optionMap['isFileUpload'])){
		extLibs = extLibs + ',\'fileupload\'';
	}
	if(table.childList.~size > 0){
		extLibs = extLibs + ',\'dataGrid\'';
	}
%>
\<% layout('/layouts/default.html', {title: '${functionNameSimple}管理', libs: ['validate'${extLibs}]}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> \${text(${className}.isNewRecord ? '新增${functionNameSimple}' : '编辑${functionNameSimple}')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<${'#'}form:form id="inputForm" model="\${${className}}" action="\${ctx}/${urlPrefix}/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">\${text('基本信息')}</div>
			<% if(table.isTreeEntity){ %>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">\${text('上级${functionNameSimple}')}：</label>
							<div class="col-sm-8">
								<${'#'}form:treeselect id="parent" title="\${text('上级${functionNameSimple}')}"
									path="parent.id" labelPath="parent.${table.treeViewNameAttrName}"
									url="\${ctx}/${urlPrefix}/treeData?excludeCode=\${${className}.id}"
									class="" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
							</div>
						</div>
					</div>
				</div>
			<% } %>
				<% include('/templates/modules/gen/include/formControl.html'){} %>
				<% include('/templates/modules/gen/include/formChildTable.html'){} %>
			<% if(toBoolean(table.optionMap['isBpmForm'])){ %>
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-xs-2">${text('审批意见')}：</label>
							<div class="col-xs-10">
								<${'#'}bpm:comment bpmEntity="\${${className}}" showCommWords="true" />
							</div>
						</div>
					</div>
				</div>
				<${'#'}bpm:nextTaskInfo bpmEntity="\${${className}}" />
			<% } %>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
					<% if(toBoolean(table.optionMap['isBpmForm'])){ %>
						\<% if (hasPermi('${permissionPrefix}:edit')){ %>
							<${'#'}form:hidden path="status"/>
							\<% if (${className}.isNewRecord || ${className}.status == '9'){ %>
								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> ${text('暂 存')}</button>&nbsp;
							\<% } %>
							<${'#'}bpm:button bpmEntity="\${${className}}" formKey="${table.optionMap['bpmFormKey']}" completeText="${text('提 交')}"/>
						\<% } %>
					<% }else{ %>
						\<% if (hasPermi('${permissionPrefix}:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> \${text('保 存')}</button>&nbsp;
						\<% } %>
					<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> \${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</${'#'}form:form>
	</div>
</div>
\<% } %>
<% include('/templates/modules/gen/include/formChildTableScript.html'){} %>
<script>
<% if(toBoolean(table.optionMap['isBpmForm'])){ %>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};
// 表单验证提交事件
<% } %>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
				<% if(table.isTreeEntity){ %>
					(contentWindow.win||contentWindow).$('#dataGrid').dataGrid('refreshTreeChildren',
							$('#parentCode').val(), '\${${className}.id}');
				<% }else{ %>
					contentWindow.page();
				<% } %>
				});
			}
		}, "json");
    }
});
<% if(table.isTreeEntity){ %>

// 选择上级节点回调方法
function treeselectCallback(id, act, index, layero){
	if (id == 'parent' && (act == 'ok' || act == 'clear')){
		// 创建并初始化下一个节点信息，如：排序号、默认值
		$.get('\${ctx}/${urlPrefix}/createNextNode?parentCode='
				+$('#parentCode').val(), function(data){
			$('#treeSort').val(data.treeSort);
		});
	}
}
<% } %>
</script>