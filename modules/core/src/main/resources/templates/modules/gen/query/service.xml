<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>service</name>
	<filePath>${baseDir}/src/main/java/${packagePath}/${moduleName}/service/${subModuleName}</filePath>
	<fileName>${ClassName}Service.java</fileName>
	<content><![CDATA[
package ${packageName}.${moduleName}.service${isNotEmpty(subModuleName)?'.'+subModuleName:''};

import java.util.List;
<% if (table.childList.~size > 0){ %>
import org.springframework.beans.factory.annotation.Autowired;
<% } %>
import org.springframework.stereotype.Service;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.${table.isTreeEntity?'Tree':'Query'}Service;
import ${packageName}.${moduleName}.entity${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName};
import ${packageName}.${moduleName}.dao${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName}Dao;
<% for (child in table.childList){ %>
import ${packageName}.${moduleName}.entity${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${@StringUtils.cap(child.className)};
import ${packageName}.${moduleName}.dao${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${@StringUtils.cap(child.className)}Dao;
<% } %>

/**
 * ${functionName}Service
 * <AUTHOR>
 * @version ${functionVersion}
 */
@Service
public class ${ClassName}Service extends ${table.isTreeEntity?'Tree':'Query'}Service<${ClassName}Dao, ${ClassName}> {
	<% for (child in table.childList){ %>
	
	@Autowired
	private ${@StringUtils.cap(child.className)}Dao ${@StringUtils.uncap(child.className)}Dao;
	<% } %>
	
	/**
	 * 获取单条数据
	 * @param ${className}
	 * @return
	 */
	@Override
	public ${ClassName} get(${ClassName} ${className}) {
		<% if (table.childList.~size > 0){ %>
		${ClassName} entity = super.get(${className});
		if (entity != null){
			<% for (child in table.childList){ %>
			${@StringUtils.cap(child.className)} ${@StringUtils.uncap(child.className)} = new ${@StringUtils.cap(child.className)}(entity);
			${@StringUtils.uncap(child.className)}.setStatus(${@StringUtils.cap(child.className)}.STATUS_NORMAL);
			entity.set${@StringUtils.cap(child.classNameSimple)}List(${@StringUtils.uncap(child.className)}Dao.findList(${@StringUtils.uncap(child.className)}));
			<% } %>
		}
		return entity;
		<% }else{ %>
		return super.get(${className});
		<% } %>
	}
	
	/**
	 * 查询分页数据
	 * @param ${className} 查询条件
	 * @param ${className} page 分页对象
	 * @return
	 */
	@Override
	public Page<${ClassName}> findPage(${ClassName} ${className}) {
		return super.findPage(${className});
	}
	
	/**
	 * 查询列表数据
	 * @param ${className}
	 * @return
	 */
	@Override
	public List<${ClassName}> findList(${ClassName} ${className}) {
		return super.findList(${className});
	}
	<% for (child in table.childList){ %>
	
	/**
	 * 查询子表分页数据
	 * @param ${@StringUtils.uncap(child.className)}
	 * @param ${@StringUtils.uncap(child.className)} page 分页对象
	 * @return
	 */
	public Page<${@StringUtils.cap(child.className)}> findSubPage(${@StringUtils.cap(child.className)} ${@StringUtils.uncap(child.className)}) {
		Page<${@StringUtils.cap(child.className)}> page = ${@StringUtils.uncap(child.className)}.getPage();
		page.setList(${@StringUtils.uncap(child.className)}Dao.findList(${@StringUtils.uncap(child.className)}));
		return page;
	}
	<% } %>
	
}]]>
	</content>
</template>