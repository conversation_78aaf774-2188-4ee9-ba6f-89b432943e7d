<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>viewForm</name>
	<filePath>${baseDir}/src/main/resources/views/${lastPackageName}/${moduleName}/${subModuleName}</filePath>
	<fileName>${className}Form.html</fileName>
	<content><![CDATA[
<%
	var extLibs = '';
	if(toBoolean(table.optionMap['isImageUpload']) || toBoolean(table.optionMap['isFileUpload'])){
		extLibs = extLibs + ',\'fileupload\'';
	}
	if(table.childList.~size > 0){
		extLibs = extLibs + ',\'dataGrid\'';
	}
%>
\<% layout('/layouts/default.html', {title: '${functionNameSimple}查询', libs: ['validate'${extLibs}]}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> \${text(${className}.isNewRecord ? '新增${functionNameSimple}' : '详情${functionNameSimple}')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<${'#'}form:form id="inputForm" model="\${${className}}" action="\${ctx}/${urlPrefix}/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">\${text('基本信息')}</div>
			<% if(table.isTreeEntity){ %>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">\${text('上级${functionNameSimple}')}：</label>
							<div class="col-sm-8">
								<${'#'}form:treeselect id="parent" title="\${text('上级${functionNameSimple}')}"
									path="parent.id" labelPath="parent.${table.treeViewNameAttrName}"
									url="\${ctx}/${urlPrefix}/treeData?excludeCode=\${${className}.id}"
									class="" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
							</div>
						</div>
					</div>
				</div>
			<% } %>
				<% include('/templates/modules/gen/include/formControl.html'){} %>
				<% include('/templates/modules/gen/include/formChildTable.html'){} %>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> \${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</${'#'}form:form>
	</div>
</div>
\<% } %>
<% include('/templates/modules/gen/include/formChildTableScript.html'){} %>]]>
	</content>
</template>