<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */%>
\<% layout('/layouts/default.html', {title: '${functionNameSimple}管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> \${text('${functionNameSimple}管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="\${text('查询')}"><i class="fa fa-filter"></i> \${text('查询')}</a>
				<% if(table.isTreeEntity){ %>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="\${text('刷新')}"><i class="fa fa-refresh"></i> \${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="\${text('展开一级')}"><i class="fa fa-angle-double-down"></i> \${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="\${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> \${text('折叠')}</a>
				<% } %>
				<% if(toBoolean(table.optionMap['isImportExport'])){ %>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<% } %>
				\<% if(hasPermi('${permissionPrefix}:edit')){ %>
					<% if(toBoolean(table.optionMap['isImportExport'])){ %>
						<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> ${text('导入')}</a>
					<% } %>
					<a href="\${ctx}/${urlPrefix}/form" class="btn btn-default btnTool" title="\${text('新增${functionNameSimple}')}"><i class="fa fa-plus"></i> \${text('新增')}</a>
				\<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="\${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<% include('/templates/modules/gen/include/searchForm.html'){} %>
			<table id="dataGrid"></table>
			<% if(!table.isTreeEntity){ %>
			<div id="dataGridPage"></div>
			<% } %>
		</div>
	</div>
</div>
\<% } %>
<% include('/templates/modules/gen/include/dataGridScript.html'){} %>
<% if(toBoolean(table.optionMap['isImportExport'])){ %>
<script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '\${ctx}/${urlPrefix}/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '\${text("导入${functionNameSimple}")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> \${text("导入")}',
			'<i class="fa fa-remove"></i> \${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("\${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="\${ctx}/${urlPrefix}/importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				\${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="\${ctx}/${urlPrefix}/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> \${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script><% } %>