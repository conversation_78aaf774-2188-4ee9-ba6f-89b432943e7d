<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<!-- 该文件可自定义，复制 config.xml 到 config-custom.xml 相同目录下，即可生效。 -->
<config>
	<!-- 模板分类 -->
	<tplCategory>
		<category value="crud" label="单表/主子表 （增删改查） beetl">
			<template>category-ref:crud_java</template>
			<template>crud/viewList.xml</template>
			<template>crud/viewForm.xml</template>
			<template>crud/viewIndex.xml</template>
			<childTable>
				<template>category-ref:dao</template>
			</childTable>
		</category>
		<category value="crud_vue" label="单表/主子表 （增删改查） vue">
			<template>category-ref:crud_java</template>
			<template>crud/vueApi.xml</template>
			<template>crud/vueList.xml</template>
			<template>crud/vueForm.xml</template>
			<template>crud/vueIndex.xml</template>
			<template>crud/vueImport.xml</template>
			<childTable>
				<template>category-ref:dao</template>
				<template>crud/vueFormChildList.xml</template>
			</childTable>
		</category>
		<category value="crud_only_vue" label="单表/主子表 （增删改查） 仅vue">
			<template>crud/vueApi.xml</template>
			<template>crud/vueList.xml</template>
			<template>crud/vueForm.xml</template>
			<template>crud/vueIndex.xml</template>
			<template>crud/vueImport.xml</template>
			<childTable>
				<template>crud/vueFormChildList.xml</template>
			</childTable>
		</category>
		<category value="crud_only_vue_modal" label="单表/主子表 （增删改查，弹窗表单） 仅vue ">
			<template>category-ref:crud_only_vue</template>
			<childTable>
				<template>crud/vueFormChildList.xml</template>
			</childTable>
		</category>
		<category value="crud_only_vue_modal_route" label="单表/主子表 （增删改查，路由表单） 仅vue ">
			<template>crud/vueApi.xml</template>
			<template>crud/vueList.xml</template>
			<template>crud/vueFormRoute.xml</template>
			<template>crud/vueIndex.xml</template>
			<template>crud/vueImport.xml</template>
			<childTable>
				<template>crud/vueFormChildList.xml</template>
			</childTable>
		</category>
		<category value="crud_select" label="单表/主子表 （增删改查，含 listselect 选择页面） beetl">
			<template>category-ref:crud</template>
			<template>crud/viewSelect.xml</template>
			<childTable>
				<template>category-ref:dao</template>
			</childTable>
		</category>
		<category value="crud_select_vue" label="单表/主子表 （增删改查，含 listselect 选择页面） vue">
			<template>category-ref:crud_vue</template>
			<template>crud/vueSelect.xml</template>
			<childTable>
				<template>category-ref:dao</template>
				<template>crud/vueFormChildList.xml</template>
			</childTable>
		</category>
		<category value="crud_java" label="单表/主子表 （增删改查，只生成 java/mapper） 仅后端 ">
			<template>category-ref:dao</template>
			<template>crud/service.xml</template>
			<template>crud/controller.xml</template>
			<childTable>
				<template>category-ref:dao</template>
			</childTable>
		</category>
		<category value="crud_cloud" label="单表/主子表 （增删改查 Cloud，生成 api/client） beetl">
			<template>category-ref:crud_cloud_java</template>
			<template>crud_cloud/viewList.xml</template>
			<template>crud_cloud/viewForm.xml</template>
			<template>crud_cloud/viewIndex.xml</template>
			<childTable>
				<template>category-ref:dao_cloud</template>
			</childTable>
		</category>
		<category value="crud_cloud_vue" label="单表/主子表 （增删改查 Cloud，生成 api/client） Vue">
			<template>category-ref:crud_cloud_java</template>
			<template>crud/vueApi.xml</template>
			<template>crud/vueList.xml</template>
			<template>crud/vueIndex.xml</template>
			<template>crud/vueForm.xml</template>
			<template>crud/vueImport.xml</template>
			<childTable>
				<template>category-ref:dao_cloud</template>
				<template>crud/vueFormChildList.xml</template>
			</childTable>
		</category>
		<category value="crud_cloud_select" label="单表/主子表 （增删改查 Cloud，含 listselect 选择页面） beetl">
			<template>category-ref:crud_cloud</template>
			<template>crud_cloud/viewSelect.xml</template>
			<childTable>
				<template>category-ref:dao_cloud</template>
			</childTable>
		</category>
		<category value="crud_cloud_select_vue" label="单表/主子表 （增删改查 Cloud，含 listselect 选择页面） vue">
			<template>category-ref:crud_cloud_vue</template>
			<template>crud/vueSelect.xml</template>
			<childTable>
				<template>category-ref:dao_cloud</template>
				<template>crud/vueFormChildList.xml</template>
			</childTable>
		</category>
		<category value="crud_cloud_java" label="单表/主子表 （增删改查 Cloud，只生成 java/mapper） 仅后端">
			<template>category-ref:dao_cloud</template>
			<template>crud_cloud/api.xml</template>
			<template>crud_cloud/client.xml</template>
			<template>crud_cloud/service.xml</template>
			<template>crud_cloud/controller.xml</template>
			<childTable>
				<template>category-ref:dao_cloud</template>
			</childTable>
		</category>
		<category value="treeGrid" label="树表/树结构表（增删改查） beetl">
			<template>category-ref:crud</template>
		</category>
		<category value="treeGrid_vue" label="树表/树结构表（增删改查） vue">
			<template>category-ref:crud_vue</template>
		</category>
		<category value="treeGrid_only_vue" label="树表/树结构表（增删改查） 仅vue">
			<template>category-ref:crud_only_vue</template>
		</category>
		<category value="treeGrid_java" label="树表/树结构表（增删改查，只生成 java/mapper） 仅后端">
			<template>category-ref:crud_java</template>
		</category>
		<category value="treeGrid_cloud" label="树表/树结构表（增删改查 Cloud，含 api/client） beetl">
			<template>category-ref:crud_cloud</template>
		</category>
		<category value="treeGrid_cloud_vue" label="树表/树结构表（增删改查 Cloud，含 api/client） vue">
			<template>category-ref:crud_cloud_vue</template>
		</category>
		<category value="treeGrid_cloud_java" label="树表/树结构表（增删改查 Cloud，只生成 java/mapper） 仅后端">
			<template>category-ref:crud_cloud_java</template>
		</category>
		<category value="service" label="仅持久层和业务层（只生成 java/dao层/service层）">
			<template>category-ref:dao</template>
			<template>crud/service.xml</template>
		</category>
		<category value="dao" label="仅持久层（只生成 java/entity/mapper/dao 文件）">
			<template>crud/entity.xml</template>
			<template>crud/mapper.xml</template>
			<template>crud/dao.xml</template>
		</category>
		<category value="dao_cloud" label="仅持久层（只生成 Cloud 版 java/entity/mapper/dao 文件）">
			<template>crud_cloud/entity.xml</template>
			<template>crud_cloud/mapper.xml</template>
			<template>crud_cloud/dao.xml</template>
		</category>
		<category value="query" label="仅查询功能（不含增删改，只生成数据查询列表和详情）">
			<template>crud/entity.xml</template>
			<template>crud/mapper.xml</template>
			<template>query/dao.xml</template>
			<template>query/service.xml</template>
			<template>query/controller.xml</template>
			<template>query/viewList.xml</template>
			<template>query/viewForm.xml</template>
			<childTable>
				<template>crud/mapper.xml</template>
				<template>crud/entity.xml</template>
				<template>query/dao.xml</template>
			</childTable>
		</category>
	</tplCategory>
	<!-- 属性类型 -->
	<attrType>
		<dict value="String" label="String"/>
		<dict value="Long" label="Long"/>
		<dict value="Integer" label="Integer"/>
		<dict value="Double" label="Double"/>
		<dict value="java.math.BigDecimal" label="BigDecimal"/>
		<dict value="java.util.Date" label="Date"/>
		<dict value="com.jeesite.modules.sys.entity.User" label="User" attrName="userCode|userName"/>
		<dict value="com.jeesite.modules.sys.entity.Office" label="Office" attrName="officeCode|officeName"/>
		<dict value="com.jeesite.modules.sys.entity.Company" label="Company" attrName="companyCode|companyName"/>
		<dict value="Parent" label="Parent" description="生成父对象"/>
	</attrType>
	<!-- 查询类型 -->
	<queryType>
		<dict value="EQ" label="="/>
		<dict value="NE" label="!="/>
		<dict value="GT" label="&gt;"/>
		<dict value="GTE" label="&gt;="/>
		<dict value="LT" label="&lt;"/>
		<dict value="LTE" label="&lt;="/>
		<dict value="BETWEEN" label="Between"/>
		<dict value="LIKE" label="Like"/>
		<dict value="LEFT_LIKE" label="左 Like"/>
		<dict value="RIGHT_LIKE" label="右 Like"/>
	</queryType>
	<!-- 字段显示类型 -->
	<showType>
		<dict value="input" label="单行文本框"/>
		<dict value="textarea" label="多行文本框"/>
		<dict value="hidden" label="隐藏域字段"/>
		<dict value="select" label="单选下拉框"/>
		<dict value="select_multiple" label="多选下拉框"/>
		<dict value="radio" label="单选按钮"/>
		<dict value="checkbox" label="复选框"/>
		<dict value="date" label="日期选择"/>
		<dict value="datetime" label="日期时间"/>
		<dict value="userselect" label="人员选择"/>
		<dict value="officeselect" label="机构选择"/>
		<dict value="companyselect" label="公司选择"/>
		<dict value="areaselect" label="区域选择"/>
	</showType>
	<!-- 字段验证 -->
	<fieldValid>
		<dict value="email" label="电子邮件"/>
		<dict value="number" label="数值"/>
		<dict value="integer" label="整数"/>
		<dict value="digits" label="正整数"/>
		<dict value="userName" label="登录账号"/>
		<dict value="realName" label="真实姓名"/>
		<dict value="abc" label="字母数字下划线"/>
		<dict value="mobile" label="手机号"/>
		<dict value="simplePhone" label="固定电话"/>
		<dict value="phone" label="手机或电话"/>
		<dict value="zipCode" label="邮政编码"/>
		<dict value="ipv4" label="IPv4"/>
		<dict value="ipv6" label="IPv6"/>
		<dict value="qq" label="QQ号"/>
		<dict value="url" label="网址"/>
		<dict value="date" label="日期"/>
		<dict value="idcard" label="身份证"/>
	</fieldValid>
	<!-- 栅格布局 -->
	<gridRowCol>
		<dict value="6/4/8" label="6/4/8  两列"/>
		<dict value="12/2/10" label="12/2/10  一列"/>
		<dict value="12/2/8" label="12/2/8  小一列"/>
		<dict value="12/2/5" label="12/2/5  中一列"/>
		<dict value="4/4/8" label="4/4/8  三列"/>
		<dict value="3/4/8" label="3/4/8  四列"/>
	</gridRowCol>
	<!-- 模块生成模板分类 -->
	<moduleTplCategory>
		<category value="module" label="生成模块代码">
			<template>module/bin/deploy.bat.xml</template>
			<template>module/bin/deploy.sh.xml</template>
			<template>module/bin/package.bat.xml</template>
			<template>module/bin/package.sh.xml</template>
			<template>module/db/erm.xml</template>
			<template>module/src/main/java/package.xml</template>
			<template>module/src/main/resources/config/jeesite.xml</template>
			<template>module/src/main/resources/db/versions.xml</template>
			<template>module/src/main/resources/static/static.xml</template>
			<template>module/pom.xml</template>
		</category>
		<category value="module_cloud" label="生成微服务模块代码（Cloud）">
			<template>module_cloud/client/bin/deploy.bat.xml</template>
			<template>module_cloud/client/bin/deploy.sh.xml</template>
			<template>module_cloud/client/bin/package.bat.xml</template>
			<template>module_cloud/client/bin/package.sh.xml</template>
			<template>module_cloud/client/src/main/java/package.xml</template>
			<template>module_cloud/client/pom.xml</template>
			<template>module_cloud/web/bin/docker/Dockerfile.xml</template>
			<template>module_cloud/web/bin/docker-build.bat.xml</template>
			<template>module_cloud/web/bin/docker-build.sh.xml</template>
			<template>module_cloud/web/bin/package.bat.xml</template>
			<template>module_cloud/web/bin/package.sh.xml</template>
			<template>module_cloud/web/bin/run-tomcat.bat.xml</template>
			<template>module_cloud/web/bin/run-tomcat.sh.xml</template>
			<template>module_cloud/web/bin/run-web.bat.xml</template>
			<template>module_cloud/web/bin/run-web.sh.xml</template>
			<template>module_cloud/web/db/erm.xml</template>
			<template>module_cloud/web/src/main/java/package.xml</template>
			<template>module_cloud/web/src/main/java/startClass.xml</template>
			<template>module_cloud/web/src/main/resources/config/bootstrap.xml</template>
			<template>module_cloud/web/src/main/resources/config/bootstrap-elk.xml</template>
			<template>module_cloud/web/src/main/resources/config/bootstrap-prod.xml</template>
			<template>module_cloud/web/src/main/resources/config/logback-spring.xml</template>
			<template>module_cloud/web/src/main/resources/config/logback-spring-elk.xml</template>
			<template>module_cloud/web/src/main/resources/config/logback-spring-prod.xml</template>
			<template>module_cloud/web/src/main/resources/static/static.xml</template>
			<template>module_cloud/web/src/main/webapp/WEB-INF/startup.bat.xml</template>
			<template>module_cloud/web/src/main/webapp/WEB-INF/startup.sh.xml</template>
			<template>module_cloud/web/pom.xml</template>
			<template>module_cloud/pom.xml</template>
		</category>
	</moduleTplCategory>
</config>