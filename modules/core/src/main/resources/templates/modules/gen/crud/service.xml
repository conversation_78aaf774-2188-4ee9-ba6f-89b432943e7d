<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>service</name>
	<filePath>${baseDir}/src/main/java/${packagePath}/${moduleName}/service/${subModuleName}</filePath>
	<fileName>${ClassName}Service.java</fileName>
	<content><![CDATA[
package ${packageName}.${moduleName}.service${isNotEmpty(subModuleName)?'.'+subModuleName:''};

import java.util.List;
<% if (table.childList.~size > 0){ %>
import org.springframework.beans.factory.annotation.Autowired;
<% } %>
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.${table.isTreeEntity?'Tree':'Crud'}Service;
import ${packageName}.${moduleName}.entity${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName};
import ${packageName}.${moduleName}.dao${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName}Dao;
<% if(toBoolean(table.optionMap['isBpmForm'])){ %>
import java.util.Map;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
<% } %>
<% if(toBoolean(table.optionMap['isBpmForm']) || toBoolean(table.optionMap['isImportExport'])){ %>
import com.jeesite.common.service.ServiceException;
<% } %>
<% if(toBoolean(table.optionMap['isImageUpload']) || toBoolean(table.optionMap['isFileUpload'])){ %>
import com.jeesite.modules.file.utils.FileUploadUtils;
<% } %>
<% for (child in table.childList){ %>
import ${packageName}.${moduleName}.entity${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${@StringUtils.cap(child.className)};
import ${packageName}.${moduleName}.dao${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${@StringUtils.cap(child.className)}Dao;
<% } %>
<% if(toBoolean(table.optionMap['isImportExport'])){ %>
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
<% } %>

/**
 * ${functionName}Service
 * <AUTHOR>
 * @version ${functionVersion}
 */
@Service
public class ${ClassName}Service extends ${table.isTreeEntity?'Tree':'Crud'}Service<${ClassName}Dao, ${ClassName}> {
	<% for (child in table.childList){ %>
	
	@Autowired
	private ${@StringUtils.cap(child.className)}Dao ${@StringUtils.uncap(child.className)}Dao;
	<% } %>
	
	/**
	 * 获取单条数据
	 * @param ${className}
	 * @return
	 */
	@Override
	public ${ClassName} get(${ClassName} ${className}) {
		<% if (table.childList.~size > 0){ %>
		${ClassName} entity = super.get(${className});
		if (entity != null){
			<% for (child in table.childList){ %>
			${@StringUtils.cap(child.className)} ${@StringUtils.uncap(child.className)} = new ${@StringUtils.cap(child.className)}(entity);
			${@StringUtils.uncap(child.className)}.setStatus(${@StringUtils.cap(child.className)}.STATUS_NORMAL);
			entity.set${@StringUtils.cap(child.classNameSimple)}List(${@StringUtils.uncap(child.className)}Dao.findList(${@StringUtils.uncap(child.className)}));
			<% } %>
		}
		return entity;
		<% }else{ %>
		return super.get(${className});
		<% } %>
	}
	
	/**
	 * 查询分页数据
	 * @param ${className} 查询条件
	 * @param ${className} page 分页对象
	 * @return
	 */
	@Override
	public Page<${ClassName}> findPage(${ClassName} ${className}) {
		return super.findPage(${className});
	}
	
	/**
	 * 查询列表数据
	 * @param ${className}
	 * @return
	 */
	@Override
	public List<${ClassName}> findList(${ClassName} ${className}) {
		return super.findList(${className});
	}
	<% for (child in table.childList){ %>
	
	/**
	 * 查询子表分页数据
	 * @param ${@StringUtils.uncap(child.className)}
	 * @param ${@StringUtils.uncap(child.className)} page 分页对象
	 * @return
	 */
	public Page<${@StringUtils.cap(child.className)}> findSubPage(${@StringUtils.cap(child.className)} ${@StringUtils.uncap(child.className)}) {
		Page<${@StringUtils.cap(child.className)}> page = ${@StringUtils.uncap(child.className)}.getPage();
		page.setList(${@StringUtils.uncap(child.className)}Dao.findList(${@StringUtils.uncap(child.className)}));
		return page;
	}
	<% } %>
	
	/**
	 * 保存数据（插入或更新）
	 * @param ${className}
	 */
	@Override
	@Transactional
	public void save(${ClassName} ${className}) {
	<% if(toBoolean(table.optionMap['isBpmForm'])){ %>
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(${className}.getStatus())){
			${className}.setStatus(${ClassName}.STATUS_AUDIT);
		}
		
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (${ClassName}.STATUS_NORMAL.equals(${className}.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		
		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (${ClassName}.STATUS_DRAFT.equals(${className}.getStatus())
				|| ${ClassName}.STATUS_AUDIT.equals(${className}.getStatus())){
			super.save(${className});
		}

		// 如果为审核状态，则进行审批流操作
		if (${ClassName}.STATUS_AUDIT.equals(${className}.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", ${className}.getLeaveDays());
			
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(${className}.getBpm().getProcInsId())
					&& StringUtils.isBlank(${className}.getBpm().getTaskId())){
				BpmUtils.start(${className}, "${table.optionMap['bpmFormKey']}", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(${className}, variables, null);
			}
		}
	<% }else{ %>
		super.save(${className});
	<% } %>
	<% if(toBoolean(table.optionMap['isImageUpload'])){ %>
		// 保存上传图片
		FileUploadUtils.saveFileUpload(${className}, ${className}.getId(), "${className}_image");
	<% } %>
	<% if(toBoolean(table.optionMap['isFileUpload'])){ %>
		// 保存上传附件
		FileUploadUtils.saveFileUpload(${className}, ${className}.getId(), "${className}_file");
	<% } %>
	<% for (child in table.childList) { %>
		// 保存 ${ClassName}子表
		for (${@StringUtils.cap(child.className)} ${@StringUtils.uncap(child.className)} : ${className}.get${@StringUtils.cap(child.classNameSimple)}List()){
			if (!${@StringUtils.cap(child.className)}.STATUS_DELETE.equals(${@StringUtils.uncap(child.className)}.getStatus())){
				<%
				for(c in child.columnList){
					if (child.parentExists && child.parentTableFkName == c.columnName){
				%>
				${@StringUtils.uncap(child.className)}.set${@StringUtils.cap(c.simpleAttrName)}(${className});
				<%
					}
				}
				%>
				if (${@StringUtils.uncap(child.className)}.getIsNewRecord()){
					${@StringUtils.uncap(child.className)}Dao.insert(${@StringUtils.uncap(child.className)});
				}else{
					${@StringUtils.uncap(child.className)}Dao.update(${@StringUtils.uncap(child.className)});
				}
			}else{
				${@StringUtils.uncap(child.className)}Dao.delete(${@StringUtils.uncap(child.className)});
			}
		}
	<% } %>
	}
	<% if(toBoolean(table.optionMap['isImportExport'])){ %>

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<${ClassName}> list = ei.getDataList(${ClassName}.class);
			for (${ClassName} ${className} : list) {
				try{
					ValidatorUtils.validateWithException(${className});
					this.save(${className});
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + ${className}.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + ${className}.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	<% } %>
	
	/**
	 * 更新状态
	 * @param ${className}
	 */
	@Override
	@Transactional
	public void updateStatus(${ClassName} ${className}) {
		super.updateStatus(${className});
	}
	
	/**
	 * 删除数据
	 * @param ${className}
	 */
	@Override
	@Transactional
	public void delete(${ClassName} ${className}) {
	<% if (table.pkList.~size > 0 && table.pkList[0].showType == 'input') { %>
		${className}.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
	<% } %>
		super.delete(${className});
	<% for (child in table.childList) { %>
		${@StringUtils.cap(child.className)} ${@StringUtils.uncap(child.className)} = new ${@StringUtils.cap(child.className)}();
		<%
		for(c in child.columnList){
			if (child.parentExists && child.parentTableFkName == c.columnName){
		%>
		${@StringUtils.uncap(child.className)}.set${@StringUtils.cap(c.simpleAttrName)}(${className});
		<%
			}
		}
		%>
		${@StringUtils.uncap(child.className)}Dao.deleteByEntity(${@StringUtils.uncap(child.className)});
	<% } %>
	}
	
}]]>
	</content>
</template>