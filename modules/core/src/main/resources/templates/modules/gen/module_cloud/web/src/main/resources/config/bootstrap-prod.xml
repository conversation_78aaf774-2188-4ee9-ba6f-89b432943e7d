<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>bootstrap</name>
	<filePath>${baseDir}/${moduleCode}/${moduleCode}/src/main/resources/config</filePath>
	<fileName>bootstrap-prod.yml</fileName>
	<content><![CDATA[

# 使用环境配置，只需 JVM 参数里加：-Dspring.profiles.active=prod

#======================================#
#========== Server settings ===========#
#======================================#

server:

  port: \${random.int[10000,19999]}

#======================================#
#========== Cloud settings ============#
#======================================#

# 服务注册
eureka:

  # 实例设置
  instance:
    # 实例主机名称
    hostname: ************
    # 实例是否允许使用IP
    preferIpAddress: false

  # 客户端设置
  client:
    # 注册中心地址（集群时指定另外一个注册中心地址）
    serviceUrl:
      defaultZone: http://************:8970/eureka/

#======================================#
#========== Spring settings ===========#
#======================================#

spring:

  # 分布式配置中心
  cloud:
    config:
      discovery:
        enabled: true
        serviceId: jeesite-cloud-config

    # Consul 服务发现
    consul:
      host: ************
      port: 8500
      discovery:
        hostname: ************
        preferIpAddress: false

    # 服务注册和配置
    nacos:
      discovery:
        server-addr: ************:8848
      config:
        server-addr: ************:8848
        file-extension: yml
        group: jeesite-cloud-yml
        extension-configs:
          - data-id: application.yml
            group: jeesite-cloud-yml

    # 服务发现与注册优选IP前缀
    #inetutils:
    #  preferred-networks: 192.168.56.

# 日志配置
logging:
  config: classpath:config/logback-spring-prod.xml
]]>
	</content>
</template>