<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>bootstrap</name>
	<filePath>${baseDir}/${moduleCode}/${moduleCode}/src/main/resources/config</filePath>
	<fileName>bootstrap.yml</fileName>
	<content><![CDATA[
#======================================#
#========== Server settings ===========#
#======================================#

server:

  port: \${random.int[10000,19999]}
    
#======================================#
#========== Cloud settings ============#
#======================================#

# 服务注册
eureka:

  # 实例设置
  instance:
    # 实例主机名称
    hostname: 127.0.0.1
    # 实例是否允许使用IP
    preferIpAddress: false

  # 客户端设置
  client:
    # 注册中心地址（集群时指定另外一个注册中心地址）
    serviceUrl:
      defaultZone: http://127.0.0.1:8970/eureka/

#======================================#
#========== Spring settings ===========#
#======================================#

spring:
  
  # 应用程序名称
  application:
    name: jeesite-cloud-module-${moduleCode}

  # 当前环境名称（注意：不可设置为 test 它是单元测试专用的名称）
  profiles:
    active: default
    
  # 分布式配置中心
  cloud:
    config:
      discovery:
        enabled: true
        serviceId: jeesite-cloud-config
    
    # Consul 服务发现
    consul:
      host: 127.0.0.1
      port: 8500
      discovery:
        hostname: 127.0.0.1
        preferIpAddress: false
        
    # 服务注册和配置
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ~
        group: jeesite-cloud
      config:
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ~
        file-extension: yml
        group: jeesite-cloud-yml
        extension-configs:
          - data-id: application.yml
            group: jeesite-cloud-yml

    # 服务发现与注册优选IP前缀
    #inetutils:
    #  preferred-networks: 192.168.56.

  # 打印横幅
  main:
    bannerMode: "off"
    allow-bean-definition-overriding: true
    
# 日志配置
logging:
  config: classpath:config/logback-spring.xml
]]>
	</content>
</template>