<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>dao</name>
	<filePath>${baseDir}/src/main/java/${packagePath}/${moduleName}/dao/${subModuleName}</filePath>
	<fileName>${ClassName}Dao.java</fileName>
	<content><![CDATA[
package ${packageName}.${moduleName}.dao${isNotEmpty(subModuleName)?'.'+subModuleName:''};

import com.jeesite.common.dao.${table.isTreeEntity?'Tree':'Crud'}Dao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import ${packageName}.${moduleName}.entity${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName};

/**
 * ${functionName}DAO接口
 * <AUTHOR>
 * @version ${functionVersion}
 */
@MyBatisDao<% if(isNotBlank(table.dataSourceName)){ %>(dataSourceName="${table.dataSourceName}")<% } %>
public interface ${ClassName}Dao extends ${table.isTreeEntity?'Tree':'Crud'}Dao<${ClassName}> {
	
}]]>
	</content>
</template>