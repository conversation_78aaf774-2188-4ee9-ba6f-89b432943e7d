<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>deploy</name>
	<filePath>${baseDir}/${moduleCode}/${moduleCode}-client/bin</filePath>
	<fileName>deploy.sh</fileName>
	<content><![CDATA[#!/bin/sh
# /**
#  * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
#  * No deletion without permission, or be held responsible to law.
#  *
#  * Author: <EMAIL>
#  */
echo ""
echo "[信息] 部署工程到Maven服务器。"
echo ""

mvn -v
echo ""

cd ..
mvn clean deploy -Dmaven.test.skip=true -Pdeploy

cd bin]]>
	</content>
</template>