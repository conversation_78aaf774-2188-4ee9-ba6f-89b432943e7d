<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>logback-spring</name>
	<filePath>${baseDir}/${moduleCode}/${moduleCode}/src/main/resources/config</filePath>
	<fileName>logback-spring-elk.xml</fileName>
	<content><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">

	<!-- Log file path  -Dspring.profiles.active=prod,elk -->
	<property name="log.path" value="\${logPath:-\${java.io.tmpdir:-.}}/logs" />
	<springProperty name="appname" source="spring.application.name" />

	<!-- Framework level setting -->
	<include resource="config/logger-core.xml"/>

	<!-- Project level setting -->
	<!-- <logger name="your.package" level="DEBUG" /> -->
	<logger name="io.seata" level="INFO" />
	<logger name="zipkin2.reporter.AsyncReporter$BoundedAsyncReporter" level="ERROR" />
	<logger name="org.springframework.cloud.openfeign.FeignClientFactoryBean" level="ERROR" />
<!-- 	<logger name="org.springframework.cloud.openfeign.support.SpringMvcContract" level="DEBUG" /> -->
	<logger name="org.springframework.context.annotation.AnnotationConfigApplicationContext" level="ERROR" />

	<!-- Console log output -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{MM-dd HH:mm:ss.SSS} %clr(%-5p) %clr([%-39logger{39}]){cyan} - %m%n%wEx</pattern>
		</encoder>
	</appender>

	<appender name="logstash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
		<destination>localhost:5055</destination> <!-- 注意：替换为您的 Logstash 服务地址 -->
		<encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder" >
			<customFields>{"appname":"\${appname}"}</customFields>
		</encoder>
	</appender>

	<!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
	<root level="WARN">
		<appender-ref ref="console" />
		<appender-ref ref="logstash" />
	</root>

</configuration>]]>
	</content>
</template>