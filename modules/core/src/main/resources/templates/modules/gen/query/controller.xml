<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>controller</name>
	<filePath>${baseDir}/src/main/java/${packagePath}/${moduleName}/web/${subModuleName}</filePath>
	<fileName>${ClassName}Controller.java</fileName>
	<content><![CDATA[
package ${packageName}.${moduleName}.web${isNotEmpty(subModuleName)?'.'+subModuleName:''};

<% if (table.isTreeEntity){ %>
import java.util.List;
import java.util.Map;
<% }else{ %>
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
<% } %>

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
<% if(table.isTreeEntity){ %>
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.modules.sys.utils.UserUtils;
<% }else{ %>
import com.jeesite.common.entity.Page;
<% } %>
import com.jeesite.common.web.BaseController;
import ${packageName}.${moduleName}.entity${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName};
<% for (child in table.childList){ %>
import ${packageName}.${moduleName}.entity${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${@StringUtils.cap(child.className)};
<% } %>
import ${packageName}.${moduleName}.service${isNotEmpty(subModuleName)?'.'+subModuleName:''}.${ClassName}Service;

/**
 * ${functionName}Controller
 * <AUTHOR>
 * @version ${functionVersion}
 */
@Controller
@RequestMapping(value = "\${adminPath}/${urlPrefix}")
public class ${ClassName}Controller extends BaseController {

	@Autowired
	private ${ClassName}Service ${className}Service;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public ${ClassName} get(<% for(pk in table.pkList){ %>${pkLP.index!=1?', ':''}${pk.simpleAttrType} ${pk.simpleAttrName}<% } %>, boolean isNewRecord) {
		<% if (table.pkList.~size == 1){ %>
		return ${className}Service.get(<% for(pk in table.pkList){ %>${pkLP.index!=1?', ':''}${pk.simpleAttrName}<% } %>, isNewRecord);
		<% }else{ %>
		${ClassName} ${className} = new ${ClassName}();
		<% for(pk in table.pkList){ %>
		${className}.set${@StringUtils.cap(pk.simpleAttrName)}(${pk.simpleAttrName});
		<% } %>
		${className}.setIsNewRecord(isNewRecord);
		return ${className}Service.getAndValid(${className});
		<% } %>
	}
	<% if(table.isTreeEntity){ %>
	
	/**
	 * 管理主页
	 */
	@RequiresPermissions("${permissionPrefix}:view")
	@RequestMapping(value = "index")
	public String index(${ClassName} ${className}, Model model) {
		model.addAttribute("${className}", ${className});
		return "${lastPackageName}/${viewPrefix}Index";
	}
	<% } %>
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("${permissionPrefix}:view")
	@RequestMapping(value = {"list", ""})
	public String list(${ClassName} ${className}, Model model) {
		model.addAttribute("${className}", ${className});
		return "${lastPackageName}/${viewPrefix}List";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("${permissionPrefix}:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	<% if(table.isTreeEntity){ %>
	public List<${ClassName}> listData(${ClassName} ${className}) {
		if (StringUtils.isBlank(${className}.getParentCode())) {
			${className}.setParentCode(${ClassName}.ROOT_CODE);
		}
		<% for(c in table.columnList){ %>
			<% if(c.isQuery == "1" && !c.isTreeEntityColumn && c.attrName != 'status'){ %>
				<% if(c.attrType == 'String'){ %>
		if (StringUtils.isNotBlank(${className}.${c.attrNameForGetMethod})){
			${className}.setParentCode(null);
		}
				<% }else{ %>
		if (${className}.${c.attrNameForGetMethod} != null){
			${className}.setParentCode(null);
		}
				<% } %>
			<% } %>
		<% } %>
		List<${ClassName}> list = ${className}Service.findList(${className});
		return list;
	}
	<% }else{ %>
	public Page<${ClassName}> listData(${ClassName} ${className}, HttpServletRequest request, HttpServletResponse response) {
		${className}.setPage(new Page<>(request, response));
		Page<${ClassName}> page = ${className}Service.findPage(${className});
		return page;
	}
	<% } %>
	<% for (child in table.childList){ %>
	
	/**
	 * 查询子表数据
	 */
	@RequiresPermissions("${permissionPrefix}:view")
	@RequestMapping(value = "${@StringUtils.uncap(child.classNameSimple)}ListData")
	@ResponseBody
	public Page<${@StringUtils.cap(child.className)}> subListData(${@StringUtils.cap(child.className)} ${@StringUtils.uncap(child.className)}, HttpServletRequest request, HttpServletResponse response) {
		${@StringUtils.uncap(child.className)}.setPage(new Page<>(request, response));
		Page<${@StringUtils.cap(child.className)}> page = ${className}Service.findSubPage(${@StringUtils.uncap(child.className)});
		return page;
	}
	<% } %>

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("${permissionPrefix}:view")
	@RequestMapping(value = "form")
	public String form(${ClassName} ${className}, Model model) {
		model.addAttribute("${className}", ${className});
		return "${lastPackageName}/${viewPrefix}Form";
	}
	<% if(table.isTreeEntity){ %>
	
	/**
	 * 获取树结构数据
	 * @param excludeCode 排除的Code
	 * @param parentCode 设置父级编码返回一级
	 * @param isShowCode 是否显示编码（true or 1：显示在左侧；2：显示在右侧；false or null：不显示）
	 * @return
	 */
	@RequiresPermissions("${permissionPrefix}:view")
	@RequestMapping(value = "treeData")
	@ResponseBody
	public List<Map<String, Object>> treeData(String excludeCode, String parentCode, String isShowCode) {
		List<Map<String, Object>> mapList = ListUtils.newArrayList();
		${ClassName} where = new ${ClassName}();
		where.setStatus(${ClassName}.STATUS_NORMAL);
		if (StringUtils.isNotBlank(parentCode)){
			where.setParentCode(parentCode);
		}
		List<${ClassName}> list = ${className}Service.findList(where);
		for (int i=0; i<list.size(); i++){
			${ClassName} e = list.get(i);
			<% if (table.statusExists){ %>
			// 过滤非正常的数据
			if (!${ClassName}.STATUS_NORMAL.equals(e.getStatus())){
				continue;
			}
			<% } %>
			// 过滤被排除的编码（包括所有子级）
			if (StringUtils.isNotBlank(excludeCode)){
				if (e.getId().equals(excludeCode)){
					continue;
				}
				if (e.getParentCodes().contains("," + excludeCode + ",")){
					continue;
				}
			}
			Map<String, Object> map = MapUtils.newHashMap();
			map.put("id", e.getId());
			map.put("pId", e.getParentCode());
			map.put("name", StringUtils.getTreeNodeName(isShowCode, e.get${@StringUtils.cap(table.treeViewCodeAttrName)}(), e.get${@StringUtils.cap(table.treeViewNameAttrName)}()));
			map.put("isParent", !e.getIsTreeLeaf());
			mapList.add(map);
		}
		return mapList;
	}
	<% } %>
	
}]]>
	</content>
</template>