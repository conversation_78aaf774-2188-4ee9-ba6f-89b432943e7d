<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>start-class</name>
	<filePath>${baseDir}/${moduleCode}/${moduleCode}/src/main/java/com/jeesite/modules</filePath>
	<fileName>${@StringUtils.capCamelCase(moduleCode)}Application.java</fileName>
	<content><![CDATA[
/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Application
 * <AUTHOR>
 * @version ${@DateUtils.getDate()}
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages={"com.jeesite.modules"})
public class ${@StringUtils.capCamelCase(moduleCode)}Application extends SpringBootServletInitializer {

	private static final Logger logger = LoggerFactory.getLogger(${@StringUtils.capCamelCase(moduleCode)}Application.class);

	public static void main(String[] args) {
		SpringApplication.run(${@StringUtils.capCamelCase(moduleCode)}Application.class, args);
		logger.info(
				"\r\n\r\n==============================================================\r\n"
				+ "\r\n   " + ${@StringUtils.capCamelCase(moduleCode)}Application.class.getName() + " 启动完成。"
				+ "\r\n\r\n==============================================================\r\n");
	}
	
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		this.setRegisterErrorPageFilter(false); // 错误页面有容器来处理，而不是SpringBoot
		return builder.sources(${@StringUtils.capCamelCase(moduleCode)}Application.class);
	}
	
}]]>
	</content>
</template>