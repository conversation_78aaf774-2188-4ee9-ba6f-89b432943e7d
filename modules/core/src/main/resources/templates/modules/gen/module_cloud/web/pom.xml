<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013-Now http://jeesite.com All rights reserved.
     No deletion without permission, or be held responsible to law. -->
<template>
	<name>pom</name>
	<filePath>${baseDir}/${moduleCode}/${moduleCode}</filePath>
	<fileName>pom.xml</fileName>
	<charset></charset>
	<content><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.jeesite</groupId>
		<artifactId>jeesite-cloud-parent-web</artifactId>
		<version>${jeesiteVersion}-SNAPSHOT</version>
		<relativePath>../../../parent/web/pom.xml</relativePath>
	</parent>
	
	<artifactId>jeesite-cloud-module-${moduleCode}-web</artifactId>
	<packaging>war</packaging>
	
	<name>JeeSite Cloud Module ${@StringUtils.capCamelCase(moduleCode)} Web</name>
	<url>http://jeesite.com</url>
	<inceptionYear>2013-Now</inceptionYear>
	
	<properties>
		
		<finalName>web</finalName><!-- war包的名称 -->
		<start-class>com.jeesite.modules.${@StringUtils.capCamelCase(moduleCode)}Application</start-class>

		<!-- docker setting -->
		<docker.run.port>8989:8989</docker.run.port>

	</properties>

	<dependencies>
		
		<!-- 核心模块 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-cloud-module-core-client</artifactId>
			<version>\${project.parent.version}</version>
		</dependency>
		
		<!-- ${moduleName}模块 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-cloud-module-${moduleCode}-client</artifactId>
			<version>\${project.parent.version}</version>
		</dependency>
		
		<!-- 业务流程
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-cloud-module-bpm-client</artifactId>
			<version>\${project.parent.version}</version>
		</dependency> -->
		
		<!-- 分布式事务
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-cloud-module-seata-client</artifactId>
			<version>\${project.parent.version}</version>
		</dependency> -->
		
	</dependencies>
	
	<build>
		<finalName>\${finalName}</finalName>
		<outputDirectory>\${project.basedir}/src/main/webapp/WEB-INF/classes/</outputDirectory>
		<plugins>
			
			<!-- Spring Boot -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			
			<!-- war插件，war包名称不带版本号 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<configuration>
					<warName>\${finalName}</warName>
				</configuration>
			</plugin>
			
			<!-- Eclipse插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-eclipse-plugin</artifactId>
				<configuration>
					<wtpContextName>\${finalName}</wtpContextName>
				</configuration>
			</plugin>
			
		</plugins>
	</build>
	
	<developers>
		<developer>
			<id>thinkgem</id>
			<name>WangZhen</name>
			<email>thinkgem at 163.com</email>
			<roles><role>Project lead</role></roles>
			<timezone>+8</timezone>
		</developer>
	</developers>
	
	<organization>
		<name>JeeSite</name>
		<url>http://jeesite.com</url>
	</organization>

	<repositories>
		<repository>
			<id>aliyun-repos</id>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases><enabled>true</enabled></releases>
			<snapshots><enabled>false</enabled></snapshots>
		</repository>
		<repository>
			<id>jeesite-repos</id>
			<url>https://maven.jeesite.net/repository/maven-public</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>aliyun-repos</id>
			<url>https://maven.aliyun.com/repository/public</url>
		</pluginRepository>
		<pluginRepository>
			<id>jeesite-repos</id>
			<url>https://maven.jeesite.net/repository/maven-public</url>
		</pluginRepository>
	</pluginRepositories>
	
</project>
]]>
	</content>
</template>