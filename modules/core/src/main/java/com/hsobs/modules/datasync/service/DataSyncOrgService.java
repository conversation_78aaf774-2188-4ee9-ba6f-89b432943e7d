package com.hsobs.modules.datasync.service;

import com.hsobs.modules.datasync.bean.FullOrgData;
import com.jeesite.common.entity.Extend;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.OfficeService;
import dm.jdbc.driver.DMException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class DataSyncOrgService {

    @Autowired
    private OfficeService officeService;

    public Office syncOrg(FullOrgData fullOrgData, Map<String, String> orgIdMap, Map<String, FullOrgData> orgMap, String rootOrgPid) {

        Office parentOffice = null;
        Office office = null;

        int tryNum = 1;
        int tryNumLimit = 3;

        do {
            try {
                Office query = new Office();
                query.setExtAspId(fullOrgData.getOrgPid());
                List<Office> officeList = officeService.findList(query);
                if (officeList != null && !officeList.isEmpty()) {
                    parentOffice = officeList.get(0);
                } else if (fullOrgData.getOrgPid() != null && !rootOrgPid.equals(fullOrgData.getOrgPid())) {
                    parentOffice = syncOrg(orgMap.get(fullOrgData.getOrgPid()), orgIdMap, orgMap, rootOrgPid);
                }

                String orgPid = null;
                if (parentOffice != null) {
                    orgPid = parentOffice.getId();
                } else {
                    orgPid = rootOrgPid;
                    parentOffice = new Office();
                    parentOffice.setId(orgPid);
                }


                query = new Office();
                query.setExtAspId(fullOrgData.getOrgId());
                officeList = officeService.findList(query);
                if (officeList != null && !officeList.isEmpty()) {
                    office = officeList.get(0);
                } else {
                    office = new Office();
                }

                office.setParent(parentOffice);
                office.setParentCode(orgPid);

                office.setViewCode(fullOrgData.getOrgId());

                office.setOfficeName(fullOrgData.getOrgName());
                office.setRegion(fullOrgData.getCode());
                office.setAddress(fullOrgData.getAddress());
                office.setTreeSort(fullOrgData.getSort());
                office.setAdministrativeLevel(fullOrgData.getAdministrativeLevel());
                office.setOrgType(fullOrgData.getOrgType());
                office.setOrgCode(fullOrgData.getOrgCode());
                office.setSocialCreditCode(fullOrgData.getSocialCreditCode());

                office.setExtAspId(fullOrgData.getOrgId());
                office.setExtAspPid(fullOrgData.getOrgPid());

                Extend extend = new Extend();
                extend.setExtendS1(fullOrgData.getExtendField());
                office.setExtend(extend);


                office.setOfficeType("0");

                officeService.save(office);
                break;
            } catch (DataAccessResourceFailureException dm) {
                tryNum++;
                if (tryNum > tryNumLimit) {
                    throw dm;
                }
                try {
                    Thread.sleep(1000L);
                } catch (Exception ignore) {
                }
            }
        } while (tryNum <= tryNumLimit);
        return office;
    }
}
