package com.hsobs.modules.datasync.service;

import com.hsobs.modules.datasync.bean.*;
import com.jeesite.common.config.Global;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.Role;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.entity.sso.SsoLoginUser;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.service.RoleService;
import com.jeesite.modules.sys.service.UserService;
import com.jeesite.modules.sys.utils.UserUtils;
import groovy.lang.Tuple2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class DataUserLoginService {

    @Autowired
    private OfficeService officeService;
    @Autowired
    private ApiZcptService apiZcptService;
    @Autowired
    private DataSyncUserService dataSyncUserService;
    @Autowired
    private DataCommonService dataCommonService;
    @Autowired
    private DataSyncRoleService dataSyncRoleService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserService userService;

    public EmpUser getUserBySsoLoginUserRemote(SsoLoginUser ssoLoginUser, boolean syncRoleFlag, boolean defaultFlag) {
        if (ssoLoginUser == null || StringUtils.isEmpty(ssoLoginUser.getUserId()) || StringUtils.isEmpty(ssoLoginUser.getOrgId())) {
            return null;
        }
        // 获取当前用户的权限
        Office queryOffice = new Office();
        queryOffice.setExtAspId(ssoLoginUser.getOrgId());
        List<Office> officeList = officeService.findList(queryOffice);
        if (officeList == null || officeList.isEmpty()) {
            return null;
        }
        Office office = officeList.get(0);
        // 获取用户详细信息
        // 批量获取用户详细信息
        List<String> userIds = new java.util.ArrayList<String>();
        userIds.add(ssoLoginUser.getUserId());
        Api2ResponseBody<List<UserDetailData>> userDetailResp = apiZcptService.listUserInfo(userIds);
        if (userDetailResp.getData().isEmpty()) {
            return null;
        }
        User systemUser = UserUtils.get("system");
        EmpUser empUser = dataSyncUserService.syncUser(ssoLoginUser, office, userDetailResp.getData().get(0));
        if (!syncRoleFlag) {
            if (empUser != null) {
                Role roleQuery = new Role();
                roleQuery.setUserCode(empUser.getUserCode());

                List<Role> roleList = roleService.findListByUserCode(roleQuery);
                if (defaultFlag && (roleList == null || roleList.isEmpty())) {
                    String defaultRoleCode = Global.getConfig("shiro.defaultRoleCode", "SYS_USER");
                    empUser.currentUser(systemUser);
                    List<String> roleCodeList = new ArrayList<>();
                    roleCodeList.add(defaultRoleCode);
                    dataSyncUserService.syncUserRole(empUser, roleCodeList);

                    roleList = roleService.findListByUserCode(roleQuery);
                }
                empUser.setRoleList(roleList);
            }
            return empUser;
        }
        // 获取数据权限
        Tuple2<Map<String, String>, Map<String, String>> tuple2 = dataCommonService.getAllOrgConfigMap();
        Map<String, String> allOrgCodeMap = tuple2.getV1();
        Map<String, String> allRoleCodeMap = tuple2.getV2();

        List<String> roleCodeList = new ArrayList<>();
        boolean allFlag = false;
        if (allOrgCodeMap.containsKey(office.getOfficeCode())) {
            allFlag = true;
        }
        UserRoleAndDataAuthData roleDataAllObj = dataCommonService.getAllOrgObj();
        Map<String, UserRoleAndDataAuthData> roleMap = new HashMap<String, UserRoleAndDataAuthData>();

        Api2ResponseBody<List<UserRoleAndDataAuthData>> userRoleAndDataAuthResp = apiZcptService.getUserAndRoleOrg(ssoLoginUser.getUserId());
        if (userRoleAndDataAuthResp.getData() != null && !userRoleAndDataAuthResp.getData().isEmpty()) {
            // 获取本系统的授权角色
            Map<String, RoleSystemAccessData> hasRoleMap = new HashMap<>();
            Api2ResponseBody<List<RoleSystemAccessData>> roleSystemAccessResp = apiZcptService.getRoleBySystemId();
            if (roleSystemAccessResp.getData() != null && !roleSystemAccessResp.getData().isEmpty()) {
                for (RoleSystemAccessData roleSystemAccessData : roleSystemAccessResp.getData()) {
                    hasRoleMap.put(roleSystemAccessData.getRoleId(), roleSystemAccessData);
                }
            }
            for (UserRoleAndDataAuthData userRoleAndDataAuthData : userRoleAndDataAuthResp.getData()) {
                if (!hasRoleMap.containsKey(userRoleAndDataAuthData.getRoleId())) {
                    continue;
                }

                if (StringUtils.isEmpty(userRoleAndDataAuthData.getCode())) {
                    userRoleAndDataAuthData.setCode("EMPTY_" + userRoleAndDataAuthData.getRoleId());
                }

                roleMap.put(userRoleAndDataAuthData.getCode(), userRoleAndDataAuthData);
                roleCodeList.add(userRoleAndDataAuthData.getCode());

                if (allRoleCodeMap.containsKey(userRoleAndDataAuthData.getCode())) {
                    allFlag = true;
                }

                String priType = userRoleAndDataAuthData.getPriType();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(priType)) {
                    String[] priTypeArr = org.apache.commons.lang3.StringUtils.split(priType, ",");
                    if (priTypeArr.length >= 2) {
                        boolean flag3 = false;
                        String priTypeTmp = "1";
                        for (String tmp : priTypeArr) {
                            if ("3".equals(tmp)) {
                                flag3 = true;
                            } else if ("2".equals(tmp)) {
                                priTypeTmp = "2";
                            }
                        }
                        // 拆分角色 数据权限
                        if (flag3) {
                            userRoleAndDataAuthData.setPriType("3");
                            UserRoleAndDataAuthData roleCopy = new UserRoleAndDataAuthData();
                            BeanUtils.copyProperties(userRoleAndDataAuthData, roleCopy);
                            roleCopy.setPriType(priTypeTmp);
                            roleCopy.setCopyFlag(true);
                            roleCopy.setCode(String.format("%s_COPY_FF_OTHER", userRoleAndDataAuthData.getCode()));
                            roleCopy.setRoleName(String.format("%s_COPY_FF_OTHER", userRoleAndDataAuthData.getRoleName()));

                            roleCodeList.add(roleCopy.getCode());
                            roleMap.put(roleCopy.getCode(), roleCopy);
                        } else {
                            userRoleAndDataAuthData.setPriType(priTypeTmp);
                        }
                    }
                }
            }
        }
        // 9 全部数据
        if (allFlag) {
            roleCodeList.add(dataCommonService.getAllOrgCode());
        }
        // 更新用户的角色信息
        empUser.currentUser(systemUser);
        dataSyncUserService.syncUserRole(empUser, roleCodeList);
        for (UserRoleAndDataAuthData roleData : roleMap.values()) {
            // 角色同步
            Role role = dataSyncRoleService.syncRole(roleData, systemUser);
            role.setIsNewRecord(false);
            // 菜单授权信息同步
            if (!roleData.getCopyFlag()) {
                Api2ResponseBody<List<RoleResourceAuthData>> roleResourceResp = apiZcptService.getRoleResource(roleData.getRoleId(), roleData.getCode(), apiZcptService.getSystemId());
                dataSyncRoleService.syncRoleAuth(role, systemUser, roleResourceResp);
            }
            // 数据权限同步
            dataSyncRoleService.syncRoleAuthDataScope(role, systemUser, roleData, null, 2, office);
        }
        if (!roleCodeList.isEmpty()) {
            Role roleQuery = new Role();
            roleQuery.setUserCode(empUser.getUserCode());
            empUser.setRoleList(roleService.findListByUserCode(roleQuery));
        }
        return empUser;
    }

    public void dataSyncKj(String userCode) {



    }
}
