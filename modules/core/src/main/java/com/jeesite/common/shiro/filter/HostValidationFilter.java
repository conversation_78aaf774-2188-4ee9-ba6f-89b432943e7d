package com.jeesite.common.shiro.filter;

import com.jeesite.common.config.Global;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class HostValidationFilter implements Filter {

    private List<String> allowedHosts;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 从配置文件中读取允许的 Host
        String allowedHostsStr = Global.getConfig("allowHosts", "");
        allowedHosts = Arrays.asList(allowedHostsStr.split(","));
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String hostHeader = httpRequest.getHeader("Host");

        // 校验 Host 是否合法
        if (hostHeader == null || !isHostAllowed(hostHeader)) {
            httpResponse.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid Host");
            return;
        }

        chain.doFilter(request, response);
    }

    private boolean isHostAllowed(String host) {
        // 支持通配符（如 *.example.com）
        return allowedHosts.stream()
                .anyMatch(allowed -> host.matches(allowed.replace(".", "\\.").replace("*", ".*")));
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
