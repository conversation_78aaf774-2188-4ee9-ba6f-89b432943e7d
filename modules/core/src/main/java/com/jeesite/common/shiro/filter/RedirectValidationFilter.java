package com.jeesite.common.shiro.filter;

import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class RedirectValidationFilter extends OncePerRequestFilter {


    /**
     *
     * @param request 请求头
     * @param response 响应头
     * @param chain
     * @throws ServletException
     * @throws IOException
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        // 包装 Response，拦截 Location 头
        RedirectValidationWrapper wrappedResponse = new RedirectValidationWrapper(response);
        chain.doFilter(request, wrappedResponse);
    }

    private static class RedirectValidationWrapper extends HttpServletResponseWrapper {
        public RedirectValidationWrapper(HttpServletResponse response) {
            super(response);
        }

        @Override
        public String encodeURL(String url) {
            return super.encodeURL(url);
        }

        @Override
        public String encodeRedirectURL(String url) {
            return super.encodeRedirectURL(url);
        }

        @Override
        public void sendRedirect(String location) throws IOException {
            // 校验 Location 域名是否合法
            if (!isValidRedirectDomain(location)) {
                throw new IllegalArgumentException("Invalid redirect domain");
            }
            super.sendRedirect(location);
        }

        private boolean isValidRedirectDomain(String location) {
            return true;
//            // 提取域名并校验（如只允许当前域名或白名单）
//            String domain = extractDomain(location);
//            return domain.equals("www.baidu.com:8080") || domain.equals("localhost:8080");
        }

        private String extractDomain(String url) {
            // 解析 URL 的域名（可借助 URI 类）
            try {
                java.net.URI uri = new java.net.URI(url);
                return uri.getHost() + (uri.getPort() != -1 ? ":" + uri.getPort() : "");
            } catch (Exception e) {
                return "";
            }
        }
    }

}
