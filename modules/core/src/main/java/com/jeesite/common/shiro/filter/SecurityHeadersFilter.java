package com.jeesite.common.shiro.filter;

import com.jeesite.common.config.Global;
import com.jeesite.common.lang.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class SecurityHeadersFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        // 添加其他安全头
        httpResponse.setHeader("X-Frame-Options", "SAMEORIGIN");
        httpResponse.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
//        httpResponse.setHeader("Content-Security-Policy", "default-src 'self'");
//
        String contentSecurityPolicy = Global.getConfig("http.response.contentSecurityPolicy", "");
        if (StringUtils.isEmpty(contentSecurityPolicy)) {
            contentSecurityPolicy = "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' http://*************:82 http://api.tianditu.gov.cn; font-src 'self' data: http://*************:82 http://api.tianditu.gov.cn; style-src 'self' 'unsafe-inline' http://*************:82 http://api.tianditu.gov.cn; img-src 'self' data: 'unsafe-inline' http://*************:82 http://*.tianditu.gov.cn";
        }
        httpResponse.setHeader("Content-Security-Policy", contentSecurityPolicy);

//        if (request.isSecure()) {
//            httpResponse.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
//        }

        // strict-origin-when-cross-origin same-origin unsafe-url
        httpResponse.setHeader("Referrer-Policy", "unsafe-url");

        // none master-only by-content-type all
        httpResponse.setHeader("X-Permitted-Cross-Domain-Policies", "all");

        httpResponse.setHeader("X-Download-Options", "noopen");

        httpResponse.setHeader("Powered-By", "");

        httpResponse.setHeader("Permissions-Policy", "unload=*, microphone=self, geolocation=self");
//        httpResponse.setHeader("Cross-Origin-Embedder-Policy", "require-corp");
        httpResponse.setHeader("Cross-Origin-Embedder-Policy", "unsafe-none");
//        httpResponse.setHeader("Cross-Origin-Opener-Policy", "same-origin");
//        httpResponse.setHeader("Cross-Origin-Rresource-Policy", "same-origin");
        httpResponse.setHeader("Cross-Origin-Opener-Policy", "unsafe-none");
        httpResponse.setHeader("Cross-Origin-Rresource-Policy", "unsafe-none");

        // Clear-Site-Data: "cache", "cookies", "storage", "executionContexts"
//        httpResponse.setHeader("Clear-site-data", "");
//        httpResponse.setHeader("Access-control-allow-origin", "");

        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
