package com.jeesite.common.shiro.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class SecurityHeadersFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        // 添加其他安全头
        httpResponse.setHeader("X-Frame-Options", "SAMEORIGIN");
        httpResponse.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
//        httpResponse.setHeader("Content-Security-Policy", "default-src 'self'");
//
//        httpResponse.setHeader("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-eval'");

//        if (request.isSecure()) {
//            httpResponse.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
//        }

        // strict-origin-when-cross-origin same-origin unsafe-url
        httpResponse.setHeader("Referrer-Policy", "unsafe-url");

        // none master-only by-content-type all
        httpResponse.setHeader("X-Permitted-Cross-Domain-Policies", "all");

        httpResponse.setHeader("X-Download-Options", "noopen");

        // DENY SAMEORIGIN  ALLOW-FROM
//        httpResponse.setHeader("X-Frame-Options", "DENY");
//        httpResponse.setHeader("Content-Security-Policy", "frame-ancestors 'none'");

        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
