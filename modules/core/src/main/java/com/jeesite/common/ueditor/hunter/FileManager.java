package com.jeesite.common.ueditor.hunter;

import com.jeesite.common.config.Global;
import com.jeesite.common.ueditor.PathFormat;
import com.jeesite.common.ueditor.define.AppInfo;
import com.jeesite.common.ueditor.define.BaseState;
import com.jeesite.common.ueditor.define.MultiState;
import com.jeesite.common.ueditor.define.State;
import org.apache.commons.io.FileUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;

public class FileManager {

    public static final String USERFILES_BASE_URL = "/userfiles/";

    private String dir = null;
    private String rootPath = null;
    private String[] allowFiles = null;
    private int count = 0;

    public FileManager(Map<String, Object> conf) {
        this.rootPath = (String) conf.get("rootPath");
        this.dir = this.rootPath + (String) conf.get("dir");
        this.allowFiles = this.getAllowFiles(conf.get("allowFiles"));
        this.count = (Integer) conf.get("count");
    }

    public State listFile(HttpServletRequest request, int index) {
        File dir = new File(PathFormat.parse(this.dir));  // ThinkGem 路径中有变量时变量不转化问题
        State state = null;
        if (!dir.exists()) {
            return new BaseState(false, AppInfo.NOT_EXIST);
        }
        if (!dir.isDirectory()) {
            return new BaseState(false, AppInfo.NOT_DIRECTORY);
        }
        Collection<File> list = FileUtils.listFiles(dir, this.allowFiles, true);
        if (index < 0 || index > list.size()) {
            state = new MultiState(true);
        } else {
            Object[] fileList = Arrays.copyOfRange(list.toArray(), index, index + this.count);
            state = this.getState(request, fileList);
        }
        state.putInfo("start", index);
        state.putInfo("total", list.size());
        return state;
    }

    private State getState(HttpServletRequest request, Object[] files) {
        MultiState state = new MultiState(true);
        BaseState fileState = null;
        File file = null;

        for (Object obj : files) {
            if (obj == null) {
                break;
            }
            file = (File) obj;
            fileState = new BaseState(true);
            //fileState.putInfo( "url", PathFormat.format( this.getPath( file ) ) );
            // ThinkGem 将绝对路径转换为虚拟路径
            String url = PathFormat.format(this.getPath(file));
            int index = url.indexOf(USERFILES_BASE_URL);
            if (index >= 0) {
                url = url.substring(index + USERFILES_BASE_URL.length());
            }
            fileState.putInfo("url", Global.getCtxPath() + USERFILES_BASE_URL + url);
            state.addState(fileState);
        }
        return state;
    }

    private String getPath(File file) {
        String path = file.getAbsolutePath();
        return path.replace(this.rootPath, "/");
    }

    private String[] getAllowFiles(Object fileExt) {
        String[] exts = null;
        String ext = null;
        if (fileExt == null) {
            return new String[0];
        }
        exts = (String[]) fileExt;
        for (int i = 0, len = exts.length; i < len; i++) {
            ext = exts[i];
            exts[i] = ext.replace(".", "");
        }
        return exts;
    }

}
