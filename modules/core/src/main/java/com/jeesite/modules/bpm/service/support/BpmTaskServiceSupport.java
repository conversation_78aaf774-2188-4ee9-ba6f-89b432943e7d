//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON><PERSON><PERSON><PERSON><PERSON> decompiler)
//

package com.jeesite.modules.bpm.service.support;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.collect.SetUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmBackActivity;
import com.jeesite.modules.bpm.entity.BpmSignUser;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.flowable.query.FlowableHistoricTaskInstanceQueryImpl;
import com.jeesite.modules.bpm.flowable.query.FlowableTaskQueryImpl;
import com.jeesite.modules.bpm.flowable.service.FlowableTaskServiceImpl;
import com.jeesite.modules.bpm.flowable.utils.FlowableUtils;
import com.jeesite.modules.bpm.service.BpmTaskService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.TaskInfoQuery;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

public class BpmTaskServiceSupport extends BaseService implements BpmTaskService {
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;

    public BpmTaskServiceSupport() {
    }

    public BpmTask getTask(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new ServiceException(this.text("任务编号不能为空", new String[0]));
        } else {
            BpmTask params = new BpmTask();
            params.setId(taskId);
            Page<BpmTask> page = this.findTaskPage(params);
            return !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
        }
    }

    public BpmTask getTaskByBusinessKey(String formKey, String bizKey, String userCode) {
        BpmTask params = new BpmTask();
        params.getProcIns().setBusinessKey(formKey + ":" + bizKey);
        params.setUserCode(userCode);
        Page<BpmTask> page = this.findTaskPage(params);
        return !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
    }

    public Page<BpmTask> findTaskPage(BpmTask params) {
        Object query = this.getQuery(params);
        Page<BpmTask> page = this.getPage(params, query);

        List<?> listPage = params.getPage() == null ? ((TaskInfoQuery) query).list() : ((TaskInfoQuery) query).listPage(page.getFirstResult(), page.getMaxResults());

        Set<String> processInstanceIds = SetUtils.newHashSet();
        Iterator task = listPage.iterator();

        while (task.hasNext()) {
            Object o = task.next();
            TaskInfo t = (TaskInfo) o;
            if (t.getProcessInstanceId() != null) {
                processInstanceIds.add(t.getProcessInstanceId());
            }
        }

        while (task.hasNext()) {
            Object o = task.next();
            TaskInfo t = (TaskInfo) o;
            if (t.getProcessInstanceId() != null) {
                processInstanceIds.add(t.getProcessInstanceId());
            }
        }
        return this.findTaskPage(params, processInstanceIds, task, listPage, page);
    }

    public Set<String> getProcessInstanceIds(BpmTask params){
        Object query = this.getQuery(params);
        Page<BpmTask> page = this.getPage(params, query);

        List<?> listPage = params.getPage() == null ? ((TaskInfoQuery) query).list() : ((TaskInfoQuery) query).listPage(page.getFirstResult(), page.getMaxResults());

        Set<String> processInstanceIds = SetUtils.newHashSet();
        Iterator task = listPage.iterator();

        while (task.hasNext()) {
            Object o = task.next();
            TaskInfo t = (TaskInfo) o;
            if (t.getProcessInstanceId() != null) {
                processInstanceIds.add(t.getProcessInstanceId());
            }
        }

        while (task.hasNext()) {
            Object o = task.next();
            TaskInfo t = (TaskInfo) o;
            if (t.getProcessInstanceId() != null) {
                processInstanceIds.add(t.getProcessInstanceId());
            }
        }
        return processInstanceIds;
    }

    private Object getQuery(BpmTask params){
        Object query;
        if ("1".equals(params.getStatus())) {
            query = this.taskService.createTaskQuery();
        } else {
            query = this.historyService.createHistoricTaskInstanceQuery();
        }

        if (params.getBeginDate() != null) {
            ((TaskInfoQuery) query).taskCreatedAfter(params.getBeginDate());
        }

        if (params.getEndDate() != null) {
            ((TaskInfoQuery) query).taskCreatedBefore(params.getEndDate());
        }

        if (StringUtils.isNotBlank(params.getStatus())) {
            if ("1".equals(params.getStatus())) {
                ((TaskQuery) query).active();
            } else if ("2".equals(params.getStatus())) {
                ((HistoricTaskInstanceQuery) query).finished();
            }
        }

        ((TaskInfoQuery) query).taskTenantId(FlowableUtils.getCurrentTenantId());
        if (StringUtils.isNotBlank(params.getUserCode())) {
            ((TaskInfoQuery) query).or().taskCandidateUser(params.getUserCode()).taskAssignee(params.getUserCode()).endOr();
        }

        if (StringUtils.isNotBlank(params.getId())) {
            ((TaskInfoQuery) query).taskId(params.getId());
        }

        if (StringUtils.isNotBlank(params.getName())) {
            ((TaskInfoQuery) query).taskNameLike("%" + params.getName() + "%");
        }

        if (params instanceof HsBpmTask && StringUtils.isEmpty(params.getName())) {
            HsBpmTask paramsh = (HsBpmTask) params;
            if (paramsh.getNames() != null && !paramsh.getNames().isEmpty()) {
                ((TaskInfoQuery) query).taskNameIn(paramsh.getNames());
            }
        }

        if (params.getPriority() != null) {
            ((TaskInfoQuery) query).taskPriority(params.getPriority());
        }

        String value;
        ArrayList list;
        if (StringUtils.isNotBlank(params.getProcIns().getId())) {
            value = params.getProcIns().getId();
            if (StringUtils.contains(value, ",")) {
                list = ListUtils.newArrayList(StringUtils.splitComma(value));
                ((TaskInfoQuery) query).processInstanceIdIn(list);
            } else {
                ((TaskInfoQuery) query).processInstanceId(value);
            }
        }

        if (StringUtils.isNotBlank(params.getProcIns().getName())) {
            if (query instanceof FlowableTaskQueryImpl) {
                ((FlowableTaskQueryImpl) query).processInstanceNameLike("%" + params.getProcIns().getName() + "%");
            } else if (query instanceof FlowableHistoricTaskInstanceQueryImpl) {
                ((FlowableHistoricTaskInstanceQueryImpl) query).processInstanceNameLike("%" + params.getProcIns().getName() + "%");
            }
        }

        if (StringUtils.isNotBlank(params.getProcIns().getProcDef().getId())) {
            ((TaskInfoQuery) query).processDefinitionId(params.getProcIns().getProcDef().getId());
        }

        if (StringUtils.isNotBlank(params.getProcIns().getProcDef().getKey())) {
            value = params.getProcIns().getProcDef().getKey();
            if (StringUtils.contains(value, ",")) {
                list = ListUtils.newArrayList(StringUtils.splitComma(value));
                ((TaskInfoQuery) query).processDefinitionKeyIn(list);
            } else {
                ((TaskInfoQuery) query).processDefinitionKey(value);
            }
        }

        if (StringUtils.isNotBlank(params.getProcIns().getFormKey())) {
            ((TaskInfoQuery) query).processInstanceBusinessKeyLike(params.getProcIns().getFormKey() + ":%");
        }

        if (StringUtils.isNotBlank(params.getProcIns().getBizKey())) {
            ((TaskInfoQuery) query).processInstanceBusinessKeyLike("%:" + params.getProcIns().getBizKey());
        }

        if (StringUtils.isNotBlank(params.getProcIns().getBusinessKey())) {
            ((TaskInfoQuery) query).processInstanceBusinessKey(params.getProcIns().getBusinessKey());
        }

        if (StringUtils.isNotBlank(params.getProcIns().getProcDef().getCategory())) {
            value = params.getProcIns().getProcDef().getCategory();
            list = ListUtils.newArrayList(StringUtils.splitComma(value));
            ((TaskInfoQuery) query).processCategoryIn(list);
        }

        if (params.getExtendMap() != null) {
            if (query instanceof FlowableTaskQueryImpl) {
                ((FlowableTaskQueryImpl) query).setExtendMap(params.getExtendMap());
            } else if (query instanceof FlowableHistoricTaskInstanceQueryImpl) {
                ((FlowableHistoricTaskInstanceQueryImpl) query).setExtendMap(params.getExtendMap());
            }
        }

        ((TaskInfoQuery) query).includeIdentityLinks();
        return query;
    }
    private Page getPage(BpmTask params, Object query){
        Page<BpmTask> page = params.getPage();
        if (page == null) {
            page = new Page();
        } else {
            if (page.isOnlyCount()) {
                page.setCount(((TaskInfoQuery) query).count());
                return page;
            }

            page.setCount(((TaskInfoQuery) query).count());
        }

        if (StringUtils.equals(page.getOrderBy(), "processDefinitionId asc")) {
            ((TaskInfoQuery) query).orderByProcessDefinitionId().asc();
        } else if (StringUtils.equals(page.getOrderBy(), "processDefinitionId desc")) {
            ((TaskInfoQuery) query).orderByProcessDefinitionId().desc();
        } else if (StringUtils.equals(page.getOrderBy(), "processInstanceId asc")) {
            ((TaskInfoQuery) query).orderByProcessInstanceId().asc();
        } else if (StringUtils.equals(page.getOrderBy(), "processInstanceId desc")) {
            ((TaskInfoQuery) query).orderByProcessInstanceId().desc();
        } else if (StringUtils.equals(page.getOrderBy(), "processInstanceName asc")) {
            if (query instanceof HistoricTaskInstanceQuery) {
                ((FlowableHistoricTaskInstanceQueryImpl) query).orderByProcessInstanceName().asc();
            } else if (query instanceof FlowableTaskQueryImpl) {
                ((FlowableTaskQueryImpl) query).orderByProcessInstanceName().asc();
            }
        } else if (StringUtils.equals(page.getOrderBy(), "processInstanceName desc")) {
            if (query instanceof HistoricTaskInstanceQuery) {
                ((FlowableHistoricTaskInstanceQueryImpl) query).orderByProcessInstanceName().desc();
            } else if (query instanceof FlowableTaskQueryImpl) {
                ((FlowableTaskQueryImpl) query).orderByProcessInstanceName().desc();
            }
        } else if (StringUtils.equals(page.getOrderBy(), "name asc")) {
            ((TaskInfoQuery) query).orderByTaskName().asc();
        } else if (StringUtils.equals(page.getOrderBy(), "name desc")) {
            ((TaskInfoQuery) query).orderByTaskName().desc();
        } else if (StringUtils.equals(page.getOrderBy(), "createTime asc")) {
            ((TaskInfoQuery) query).orderByTaskCreateTime().asc();
        } else if (StringUtils.equals(page.getOrderBy(), "createTime desc")) {
            ((TaskInfoQuery) query).orderByTaskCreateTime().desc();
        } else if (StringUtils.equals(page.getOrderBy(), "endTime asc") && query instanceof HistoricTaskInstanceQuery) {
            ((HistoricTaskInstanceQuery) query).orderByHistoricTaskInstanceEndTime().asc();
        } else if (StringUtils.equals(page.getOrderBy(), "endTime desc") && query instanceof HistoricTaskInstanceQuery) {
            ((HistoricTaskInstanceQuery) query).orderByHistoricTaskInstanceEndTime().desc();
        } else if (StringUtils.equals(page.getOrderBy(), "dueDate asc")) {
            ((TaskInfoQuery) query).orderByTaskDueDate().asc();
        } else if (StringUtils.equals(page.getOrderBy(), "dueDate desc")) {
            ((TaskInfoQuery) query).orderByTaskDueDate().desc();
        } else if (StringUtils.equals(page.getOrderBy(), "priority asc")) {
            ((TaskInfoQuery) query).orderByTaskPriority().asc();
        } else if (StringUtils.equals(page.getOrderBy(), "priority desc")) {
            ((TaskInfoQuery) query).orderByTaskPriority().desc();
        } else if ("2".equals(params.getStatus())) {
            ((HistoricTaskInstanceQuery) query).orderByHistoricTaskInstanceEndTime().desc();
        } else {
            ((TaskInfoQuery) query).orderByTaskCreateTime().desc();
        }
        return page;
    }

    public Page<BpmTask> findTaskPage(BpmTask params, Set<String> processInstanceIds,Iterator task, List<?> listPage ,Page<BpmTask> page) {
        Map<String, ProcessInstance> processInstanceMap = MapUtils.newHashMap();
        Map<String, HistoricProcessInstance> historicProcessInstanceMap = MapUtils.newHashMap();
        if (!processInstanceIds.isEmpty()) {
            List processInstances;
            Iterator var17;
            if ("1".equals(params.getStatus())) {
                processInstances = this.runtimeService.createProcessInstanceQuery().processInstanceIds(processInstanceIds).list();
                var17 = processInstances.iterator();

                while (var17.hasNext()) {
                    ProcessInstance processInstance = (ProcessInstance) var17.next();
                    processInstanceMap.put(processInstance.getId(), processInstance);
                }
            } else {
                processInstances = this.historyService.createHistoricProcessInstanceQuery().processInstanceIds(processInstanceIds).list();
                var17 = processInstances.iterator();

                while (var17.hasNext()) {
                    HistoricProcessInstance processInstance = (HistoricProcessInstance) var17.next();
                    historicProcessInstanceMap.put(processInstance.getId(), processInstance);
                }
            }
        }

        task = null;
        List<BpmTask> list1 = ListUtils.newArrayList();
        Iterator var22 = listPage.iterator();

        while (var22.hasNext()) {
            Object o = var22.next();
            TaskInfo t = (TaskInfo) o;
            ProcessDefinition procDef = FlowableUtils.getProcessDefinition(t.getProcessDefinitionId());
            BpmTask task1;
            if ("1".equals(params.getStatus())) {
                task1 = new BpmTask(t, (ProcessInstance) processInstanceMap.get(t.getProcessInstanceId()), procDef);
            } else {
                task1 = new BpmTask(t, (HistoricProcessInstance) historicProcessInstanceMap.get(t.getProcessInstanceId()), procDef);
            }

            task1.initializeAssigneeInfo(t);
            list1.add(task1);
        }

        page.setList(list1);
        return page;
    }

    private FlowableTaskServiceImpl getTaskService() {
        return (FlowableTaskServiceImpl) this.taskService;
    }

    @Transactional
    public BpmTask startProcess(BpmTask params) {
        return this.getTaskService().startProcess(params);
    }

    @Transactional
    public BpmTask claimTask(BpmTask params) {
        return this.getTaskService().claimTask(params);
    }

    @Transactional
    public BpmTask unclaimTask(BpmTask params) {
        return this.getTaskService().unclaimTask(params);
    }

    @Transactional
    public BpmTask completeTask(BpmTask params) {
        return this.getTaskService().completeTask(params);
    }

    @Transactional
    public BpmTask turnTask(BpmTask params) {
        return this.getTaskService().turnTask(params);
    }

    public List<BpmBackActivity> getBackActivity(BpmTask params) {
        return this.getTaskService().getBackActivity(params);
    }

    @Transactional
    public BpmTask backTask(BpmTask params) {
        return this.getTaskService().backTask(params);
    }

    @Transactional
    public BpmTask moveTask(BpmTask params) {
        return this.getTaskService().moveTask(params);
    }

    @Transactional
    public BpmTask rollbackTask(BpmTask params) {
        return this.getTaskService().rollbackTask(params);
    }

    public List<BpmSignUser> getSignUserList(BpmTask params) {
        return this.getTaskService().getSignUserList(params);
    }

    public BpmTask modifySignTask(BpmTask params) {
        return this.getTaskService().modifySignTask(params);
    }
}
