package com.jeesite.modules.bpm.web;


import com.fasterxml.jackson.annotation.JsonView;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.entity.BpmForm;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmFormUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(
        tags = {"ABpmMyTask - 我的任务"}
)
@RequestMapping({"${adminPath}/bpm/bpmMyTask"})
public class BpmMyTaskController extends BaseController {
    
    @Autowired
    private BpmTaskService bpmTaskService;

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"todoList"})
    @ApiOperation("待办页面")
    @ApiImplicitParams({})
    public String todoList(BpmTask params, Model model) {
        model.addAttribute("params", params);
        return "modules/bpm/bpmMyTaskTodoList";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"historyList"})
    @ApiOperation("已办页面")
    @ApiImplicitParams({})
    public String list(BpmTask params, Model model) {
        model.addAttribute("params", params);
        return "modules/bpm/bpmMyTaskHistoryList";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"listData"})
    @ResponseBody
    @JsonView({DataEntity.SimpleView.class})
    @ApiOperation("待办已办数据")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "procIns.name",
            value = "流程名称",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "processInstanceId",
            value = "流程实例ID",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "name",
            value = "环节名称",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "procIns.procDef.category",
            value = "流程分类",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "priority",
            value = "优先级",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "beginDate",
            value = "创建时间-开始",
            required = false,
            paramType = "query",
            dataType = "Date"
    ), @ApiImplicitParam(
            name = "endDate",
            value = "创建时间-结束",
            required = false,
            paramType = "query",
            dataType = "Date"
    ), @ApiImplicitParam(
            name = "status",
            value = "流程状态",
            required = false,
            paramType = "query",
            dataType = "String",
            example = "1待办；2已办"
    ), @ApiImplicitParam(
            name = "pageNo",
            value = "当前页码",
            required = false,
            paramType = "query",
            dataType = "Integer"
    ), @ApiImplicitParam(
            name = "pageSize",
            value = "每页条数",
            required = false,
            paramType = "query",
            dataType = "Integer"
    ), @ApiImplicitParam(
            name = "orderBy",
            value = "排序方式",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public Page<BpmTask> listData(BpmTask params, HttpServletRequest request, HttpServletResponse response) {
        params.setPage(new Page(request, response));
        params.setUserCode(params.currentUser().getUserCode());
        Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);
        return page;
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"form"})
    @ResponseBody
    @ApiOperation("获取任务表单")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String form(BpmTask params, Boolean addPrefix, HttpServletRequest request) {
        if (StringUtils.isBlank(params.getId())) {
            throw new ServiceException(text("任务编号不能为空", new String[0]));
        } else {
            BpmTask task = this.bpmTaskService.getTask(params.getId());
            if (task == null) {
                return this.renderResult("false", text("提交失败，任务不存在。", new String[0]));
            } else if (StringUtils.isNotBlank(task.getProcIns().getFormKey())) {
                BpmForm form = BpmFormUtils.getForm(task.getProcIns().getFormKey(), task.getProcIns().getProcDef().getVersion(), task.getActivityId());
                if (form == null) {
                    return this.renderResult("false", text("该流程未绑定流程表单", new String[0]));
                } else {
                    Map<String, Object> data = MapUtils.newHashMap();
                    data.put("pcUrl", form.buildUrl(form.getPcUrl(), task, addPrefix == null || addPrefix));
                    data.put("pcViewUrl", form.buildUrl(form.getPcViewUrl(), task, addPrefix == null || addPrefix));
                    data.put("mobileUrl", form.buildUrl(form.getMobileUrl(), task, false));
                    data.put("mobileViewUrl", form.buildUrl(form.getMobileViewUrl(), task, false));
                    return this.renderResult("true", text("获取流程表单成功", new String[0]), data);
                }
            } else if (UserUtils.getSubject().isPermitted("bpm:bpmTask")) {
                Map<String, Object> data = MapUtils.newHashMap();
                data.put("pcUrl", Global.getCtxPath() + this.adminPath + "/bpm/bpmTask/form?id=" + task.getId());
                return this.renderResult("true", text("该流程未关联业务，进入调试页面", new String[0]), data);
            } else {
                return this.renderResult("false", text("该流程未关联业务", new String[0]));
            }
        }
    }
}
