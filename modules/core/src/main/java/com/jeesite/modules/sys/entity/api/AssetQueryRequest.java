/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.entity.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jeesite.common.entity.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 资产查询请求参数实体类
 * 
 * <AUTHOR>
 * @version 2025-06-22
 */
public class AssetQueryRequest extends BaseEntity<AssetQueryRequest> {

    /**
     * 区划编码 - 必填
     */
    @NotBlank(message = "区划编码不能为空")
    @JsonProperty("mofDivCode")
    private String mofDivCode;

    /**
     * 单位编码 - 必填
     */
    @NotBlank(message = "单位编码不能为空")
    @JsonProperty("agencyCode")
    private String agencyCode;


    /**
     * 资产编码 - 可选
     */
    @JsonProperty("assetCode")
    private String assetCode;

    /**
     * 资产名称 - 可选
     */
    @JsonProperty("assetName")
    private String assetName;

    /**
     * 资产分类编码 - 可选
     */
    @JsonProperty("fixedAssetTypeCode")
    private String fixedAssetTypeCode;

    /**
     * 单位会计科目编码 - 可选
     */
    @JsonProperty("fiAcctClsCode")
    private String fiAcctClsCode;

    /**
     * 记账日期区间 - 可选
     */
    @JsonProperty("posterDateRange")
    private List<String> posterDateRange;

    // Getters and Setters
    public String getMofDivCode() {
        return mofDivCode;
    }

    public void setMofDivCode(String mofDivCode) {
        this.mofDivCode = mofDivCode;
    }

    public String getAgencyCode() {
        return agencyCode;
    }

    public void setAgencyCode(String agencyCode) {
        this.agencyCode = agencyCode;
    }

    public String getAssetCode() {
        return assetCode;
    }

    public void setAssetCode(String assetCode) {
        this.assetCode = assetCode;
    }

    public String getAssetName() {
        return assetName;
    }

    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }

    public String getFixedAssetTypeCode() {
        return fixedAssetTypeCode;
    }

    public void setFixedAssetTypeCode(String fixedAssetTypeCode) {
        this.fixedAssetTypeCode = fixedAssetTypeCode;
    }

    public String getFiAcctClsCode() {
        return fiAcctClsCode;
    }

    public void setFiAcctClsCode(String fiAcctClsCode) {
        this.fiAcctClsCode = fiAcctClsCode;
    }

    public List<String> getPosterDateRange() {
        return posterDateRange;
    }

    public void setPosterDateRange(List<String> posterDateRange) {
        this.posterDateRange = posterDateRange;
    }

    @Override
    public String toString() {
        return "AssetQueryRequest{" +
                "mofDivCode='" + mofDivCode + '\'' +
                ", agencyCode='" + agencyCode + '\'' +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", assetCode='" + assetCode + '\'' +
                ", assetName='" + assetName + '\'' +
                ", fixedAssetTypeCode='" + fixedAssetTypeCode + '\'' +
                ", fiAcctClsCode='" + fiAcctClsCode + '\'' +
                ", posterDateRange=" + posterDateRange +
                '}';
    }
}