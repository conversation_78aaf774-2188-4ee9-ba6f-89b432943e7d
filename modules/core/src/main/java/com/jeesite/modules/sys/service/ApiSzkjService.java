/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.service;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.asymmetric.SM2;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.config.Global;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.entity.api.*;
import com.jeesite.modules.sys.utils.SM2Utils;
import org.bouncycastle.pqc.legacy.math.linearalgebra.ByteUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.File;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 数字空间交互
 *
 * <AUTHOR>
 * @version 2017-03-25
 */

@Service
public class ApiSzkjService extends BaseService {

    ObjectMapper objectMapper = new ObjectMapper();

    private static String szkj_server; //数字空间接口地址
    private static String accountId; //数字空间分配的账号id

    private String sign; //数字空间接口签名

    private String accessToken; //数字空间接口token

    private String expiresIn; //数字空间有效期
    private Instant expiresTime;


    static {

        try{
            szkj_server = Global.getConfig("szkj.server").toString();
            //szkj_server = "http://www.163.com";
            accountId = Global.getConfig("szkj.accountId-szkj").toString();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取数字空间接口的请求token
     * @return
     */
    private void token() {

        try {
            String mypky = Global.getConfig("szkj.sm2.prk-my").toString();
            sign = SM2Utils.SM2Signature(accountId, mypky);
            String signUrlEncode = URLEncoder.encode(sign, StandardCharsets.UTF_8.name());
            String url = String.format("%s/digit-space/digit-space-main/thirdSystem/token?accountId=%s&sign=%s", szkj_server, accountId, signUrlEncode);

            Api2ResponseBody<Map<String, String>> result = this.requet(url, HttpMethod.GET, JSON.toJSONString(new HashMap<>()), true);
            if(result.getCode() == 1 && result.getData() != null) {
                accessToken = result.getData().get("accessToken");
                expiresIn = result.getData().get("expiresIn");
                if(expiresIn != null && expiresIn.length() > 0) {
                    expiresTime = Instant.now().plus(Integer.parseInt(expiresIn)-10, ChronoUnit.SECONDS);
                }
            }else{
                accessToken = null;
            }
        }catch (Exception e){
            accessToken = null;
        }
    }

    /**
     * 获取 AccessToken（如果已过期则重新获取）
     */
    private void ensureValidToken(boolean init) {
        if (init) {
            return;
        }
        // 如果 token 为空或已过期，则重新获取
        if (accessToken == null || Instant.now().isAfter(expiresTime)) {
            logger.debug("令牌已过期或为空，重新执行获取token");
            this.token();
        }
    }

    /**
     * 数字空间通用请求方法
     *
     * @param body
     * @return
     */
    private Api2ResponseBody requet(String url,  HttpMethod method, Object body) {
        return this.requet(url, method, body, false);
    }

    public static CloseableHttpClient createHttpClient() throws KeyManagementException, NoSuchAlgorithmException, KeyStoreException {
        // 创建一个信任所有证书的 SSLContext
        SSLContext sslContext = new SSLContextBuilder()
                .loadTrustMaterial(null, (certificate, authType) -> true) // 信任所有证书
                .build();

        // 创建一个 SSLConnectionSocketFactory，跳过主机名验证
        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        // 创建 HttpClient
        return HttpClients.custom()
                .setSSLSocketFactory(socketFactory)
                .build();
    }

    public static RestTemplate createRestTemplate() throws Exception {
        // 创建跳过 SSL 验证的 HttpClient
        CloseableHttpClient httpClient = createHttpClient();

        // 配置 RestTemplate 使用自定义的 HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory(httpClient);

        return new RestTemplate(requestFactory);
    }

    /**
     * 数字空间通用请求方法
     *
     * @param body
     * @param init 是否初始化方法
     * @return
     */
    private Api2ResponseBody requet(String url, HttpMethod method, Object body, boolean init) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL); // 忽略 null 字段

        try {
            // 验证是否已登录
            this.ensureValidToken(init);
            if (!init && accessToken == null) {
                return Api2ResponseBody.error(100, "token获取失败！");
            }

            // 序列化请求体为 JSON 并打印
            String requestJson = objectMapper.writeValueAsString(body);
            logger.debug("SZKJ请求 URL: {}", url);
            logger.debug("SZKJ请求 JSON 报文: {}", requestJson);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("accountId", accountId);
            headers.add("accessToken", accessToken);

            // 构造请求实体（注意使用对象，不是 JSON 字符串）
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, headers);

            // 使用 postForEntity 发送 POST 请求（适配 Api2ResponseBody.class）
            ResponseEntity<Api2ResponseBody> responseEntity = createRestTemplate().exchange(url, method, requestEntity, Api2ResponseBody.class);

            Api2ResponseBody response = responseEntity.getBody();

            // 日志打印响应内容
            logger.debug("SZKJ响应对象: {}", objectMapper.writeValueAsString(response));

            if (response != null && response.getCode() != 1) {
                throw new ServiceException(response.getMessage());
            }

            return response;

        } catch (ServiceException e) {
            logger.error("服务异常: {}", e.getMessage(), e);
            return Api2ResponseBody.error("数字空间接口请求失败，请核查！");
        } catch (HttpServerErrorException.InternalServerError e) {
            logger.error("服务器内部错误: {}", e.getMessage(), e);
            return Api2ResponseBody.error("数字空间接口请求失败，请核查！");
        } catch (Exception e) {
            logger.error("请求处理异常: {}", e.getMessage(), e);
            return Api2ResponseBody.error("数字空间接口请求失败，请核查！");
        }
    }




    /**
     * 上传文件到指定接口
     */
    public Api2ResponseBody uploadFile(List<String> files, String msgId) {
        logger.debug("SZKJ上传文件: ");
        // 验证是否已登录
        this.ensureValidToken(false);
        if(accessToken == null){
            return Api2ResponseBody.error(100, "token获取失败！");
        }

        // 1. 设置请求头（multipart/form-data）
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.add("accountId", accountId);
        headers.add("accessToken", accessToken);

        try {
            // 2. 组装请求体（文件参数名称根据 API 要求调整，比如 "file"）
            MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
            for(int i = 0; i < files.size(); i++) {
                requestBody.add("files", new FileSystemResource(files.get(i)));
            }
            requestBody.add("msgId", msgId);
            requestBody.add("systemId", accountId);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 3. 发送 POST 请求
            ResponseEntity<Api2ResponseBody> response = createRestTemplate().exchange(szkj_server+"/digit-space/digit-space-main/thirdSystem/msgNotice/uploadMsgFiles", HttpMethod.POST, requestEntity, Api2ResponseBody.class);
            logger.debug("SZKJ结果报文: " + response.toString());
            // 4. 处理返回结果
            return response.getBody();

        } catch (ServiceException e) {
            return Api2ResponseBody.error("接口请求失败，请核查！" + e.getMessage());
        } catch (HttpServerErrorException.InternalServerError e) {
            return Api2ResponseBody.error("接口请求失败，请核查！" + e.getMessage());
        } catch (Exception e) {
            return Api2ResponseBody.error("接口请求失败，请核查！" + e.getMessage());
        }
    }

    public Api2ResponseBody uploadNotice(Api2NoticeBody body) {

        body.setSystemId(accountId);
        return this.requet(szkj_server+"/digit-space/digit-space-main/thirdSystem/msgNotice/realTimeAdd", HttpMethod.POST, JSON.toJSONString(body));
    }

    public Api2ResponseBody uploadTaskBatchAdd(List<Api2TaskInfo> taskInfos) {

        for(Api2TaskInfo taskInfo : taskInfos){
            taskInfo.setSystemId(accountId);
        }
        return this.requet(szkj_server+"/digit-space/digit-space-main/thirdSystem/businessItem/batchAdd", HttpMethod.POST, JSON.toJSONString(taskInfos));
    }

    public Api2ResponseBody uploadTaskBatchSave(List<Api2TaskInfo> taskInfos) {

        for(Api2TaskInfo taskInfo : taskInfos){
            taskInfo.setSystemId(accountId);
        }
        return this.requet(szkj_server+"/digit-space/digit-space-main/thirdSystem/businessItem/batchSaveOrUpdate", HttpMethod.POST, JSON.toJSONString(taskInfos));
    }

    public Api2ResponseBody uploadTaskRealTimeSave(Api2TaskInfo taskInfo) {

        taskInfo.setSystemId(accountId);
        return this.requet(szkj_server+"/digit-space/digit-space-main/thirdSystem/businessItem/realTimeSaveOrUpdate", HttpMethod.POST, JSON.toJSONString(taskInfo));
    }
}