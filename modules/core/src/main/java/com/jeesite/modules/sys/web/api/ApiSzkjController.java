/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.web.api;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.cache.CacheUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.shiro.session.SessionDAO;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.api.*;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.SM2Utils;
import com.jeesite.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import org.apache.shiro.session.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数字空间交互
 *
 * <AUTHOR>
 * @version 2020-9-19
 */
@Controller
@Api(tags = "API - API接口")
@ConditionalOnProperty(name = {"user.enabled", "web.core.enabled"}, havingValue = "true", matchIfMissing = true)
public class ApiSzkjController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ApiSzkjController.class);

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    EmpUserService empUserService;

    @Autowired
    ApiSzkjService apiSzkjService;

    @Autowired
    private SessionDAO sessionDAO;

    @ResponseBody
    @RequestMapping(value = "api2/token")
    public Api2ResponseBody token(@RequestBody ApiTokenBody body) {
        String accountId = body.getAccountId();
        if (!Global.getConfig("szkj.accountId-my").toString().equals(accountId)) {
            return Api2ResponseBody.error("用户不存在！");
        }
        boolean bVerfier = SM2Utils.SM2SigVerifier(accountId, body.getSign(), Global.getConfig("szkj.sm2.pbk-szkj"));
        if (!bVerfier) {
            return Api2ResponseBody.error("平台认证失败！");
        }
        String accessToken = this.getMztAccessToken(accountId);
        Map loginInfo = new HashMap<>();
        loginInfo.put("accessToken", accessToken);
        loginInfo.put("expiresIn", Global.getConfig("szkj.expiresIn", "3600"));
        return Api2ResponseBody.sucess(loginInfo);
    }

    private String getMztAccessToken(String accountId) {
        String accessToken = CacheUtils.get("szkj_token", accountId);
        if (accessToken == null) {
            accessToken = UUID.randomUUID().toString();//生成随机token
            CacheUtils.put("szkj_token", accountId, accessToken, Long.parseLong(Global.getConfig("szkj.expiresIn", "3600")));
        }
        return accessToken;
    }

    ObjectMapper objectMapper = new ObjectMapper();

    @ResponseBody
    @RequestMapping(value = "api2/**")
    public Api2ResponseBody request2(@RequestHeader Map<String, String> head, @RequestBody String bodyRaw, HttpServletRequest request) {

        try {

            Map<String, String> body = Stream.of(bodyRaw.split("&"))
                    .map(s -> s.split("="))
                    .collect(Collectors.toMap(
                            data -> data[0],
                            data -> data[1]
                    ));
            if(body == null){
                throw new ServiceException("body转换失败");
            }

            this.checkAccessToken(head.get("accessToken"), head.get("accountId"));
            String fullPath = request.getRequestURI().substring(request.getContextPath().length() + 5);
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.add("Cookie", "jeesite.session.id=" + this.getSessionByPhone(body.get("mobile")));

            // 设置表单参数
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            body.forEach((key, value) -> formData.add(key.toString(), value.toString()));

            // 构建请求
            HttpEntity<MultiValueMap<String, String>> requestEntity =
                    new HttpEntity<>(formData, headers);

            String response = "";
            try {
                String exchangeUrl = "http://127.0.0.1:" + Global.getProperty("server.port") + FileUtils.path("/"
                        + Global.getProperty("server.servlet.context-path")) + "/admin" + fullPath;
                logger.info("exchange url: {}", exchangeUrl);
                // 发送请求并接收响应
                response = restTemplate.exchange(
                        exchangeUrl,
                        HttpMethod.POST,
                        requestEntity,
                        String.class
                ).getBody();
            } catch (Exception e) {
                e.printStackTrace();
                return Api2ResponseBody.error("接口请求失败，请核查！");
            }
            String replaceStr = response.replaceAll("\"count\"", "\"total\"");
            replaceStr = replaceStr.replaceAll("\"list\"", "\"dataList\"");
            return Api2ResponseBody.sucess(objectMapper.readValue(replaceStr, Map.class));
        } catch (ServiceException | JsonProcessingException e) {
            return Api2ResponseBody.error(e.getMessage());
        }
    }

    /**
     * 核验请求是否合法
     *
     * @param accessToken
     * @param accountId
     */
    private void checkAccessToken(String accessToken, String accountId) {
        if (accessToken == null){
            throw new ServiceException("accessToken为空");
        }
        if(accountId == null) {
            throw new ServiceException("accountId为空");
        }
        String accessTokenCache = CacheUtils.get("szkj_token", accountId);
        if (!accessToken.equals(accessTokenCache)) {
            throw new ServiceException("Token已失效或者无效");
        }
    }

    /**
     * 解析获取用户信息
     *
     * @param object
     * @return
     */
    private String getSessionByPhone(Object object) {
        String sessionId = "";
        if (object == null) {
            throw new ServiceException("没有用户身份信息:mobile");
        }
        String phone = object.toString();
        sessionId = this.checkSessionId(phone);
        sessionId = CacheUtils.get("sjzq_session", phone);
        if (StringUtils.isEmpty(sessionId)) {
            EmpUser user = this.getUserByPhone(phone);
            String token = UserUtils.getSsoToken(user.getLoginCode());
            JSONObject result = restTemplate.getForObject("http://127.0.0.1:" + Global.getProperty("server.port") + FileUtils.path("/"
                    + Global.getProperty("server.servlet.context-path")) + "/ssoJson/" + user.getLoginCode() + "/" + token, JSONObject.class);
            sessionId = result.getString("session");
            CacheUtils.put("sjzq_session", phone, sessionId, Long.parseLong(Global.getConfig("expiresIn", "3600")));
        }
        return sessionId;
    }

    /**
     * 验证用户是否还在登录
     * @param phone
     * @return
     */
    private String checkSessionId(String phone) {
        String sessionId = CacheUtils.get("sjzq_session", phone);
        Session session = sessionDAO.readSession(sessionId);
        if (session == null){
            CacheUtils.remove("sjzq_session", phone);
            return null;
        }
        return sessionId;
    }

    private EmpUser getUserByPhone(String phone) {
        EmpUser query = new EmpUser();
        query.setMobile(phone);
        return empUserService.findList(query).stream().findFirst().orElseThrow(() -> new ServiceException("手机号" + phone + "用户不存在！"));
    }
}
