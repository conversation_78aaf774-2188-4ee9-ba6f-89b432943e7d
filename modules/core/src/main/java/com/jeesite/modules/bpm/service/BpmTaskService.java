//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.jeesite.modules.bpm.service;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.api.BaseServiceApi;
import com.jeesite.modules.bpm.entity.BpmBackActivity;
import com.jeesite.modules.bpm.entity.BpmSignUser;
import com.jeesite.modules.bpm.entity.BpmTask;
import java.util.List;
import java.util.Set;

public interface BpmTaskService extends BaseServiceApi {
    BpmTask getTask(String taskId);

    BpmTask getTaskByBusinessKey(String formKey, String bizKey, String userCode);

    Page<BpmTask> findTaskPage(BpmTask params);

    public Set<String> getProcessInstanceIds(BpmTask params);

    BpmTask startProcess(BpmTask params);

    BpmTask claimTask(BpmTask params);

    BpmTask unclaimTask(BpmTask params);

    BpmTask completeTask(BpmTask params);

    BpmTask turnTask(BpmTask params);

    List<BpmBackActivity> getBackActivity(BpmTask params);

    BpmTask backTask(BpmTask params);

    BpmTask moveTask(BpmTask params);

    BpmTask rollbackTask(BpmTask params);

    List<BpmSignUser> getSignUserList(BpmTask params);

    BpmTask modifySignTask(BpmTask params);
}
