package com.jeesite.modules.sys.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;

/**
 * 组织机构编制
 * <AUTHOR>
 * @version 2025-03-08
 */
@Table(name="OB_OFFICE_ESTABLISHMENT", alias="a", label="组织机构编制表信息", columns={
        @Column(name="id", attrName="id", label="主键", isPK=true),
        // ==================== 编制字段 ====================
        // 中央机关编制
        @Column(name="minister_positive", attrName="ministerPositive", label="部级正职"),
        @Column(name="minister_deputy", attrName="ministerDeputy", label="部级副职"),
        @Column(name="department_director", attrName="departmentDirector", label="正司(局)级"),
        @Column(name="deputy_department_director", attrName="deputyDepartmentDirector", label="副司(局)级"),
        @Column(name="division_level", attrName="divisionLevel", label="处级"),
        @Column(name="below_division_level", attrName="belowDivisionLevel", label="处级以下"),

        // 省级机关编制
        @Column(name="province_positive", attrName="provincePositive", label="省级正职"),
        @Column(name="province_deputy", attrName="provinceDeputy", label="省级副职"),
        @Column(name="bureau_director", attrName="bureauDirector", label="正厅(局)级"),
        @Column(name="deputy_bureau_director", attrName="deputyBureauDirector", label="副厅(局)级"),
        @Column(name="division_chief", attrName="divisionChief", label="正处级"),
        @Column(name="deputy_division_chief", attrName="deputyDivisionChief", label="副处级"),
        @Column(name="below_division_chief", attrName="belowDivisionChief", label="处级以下"),

        // 市级机关编制
        @Column(name="municipal_positive", attrName="municipalPositive", label="市级正职"),
        @Column(name="municipal_deputy", attrName="municipalDeputy", label="市级副职"),
        @Column(name="municipal_bureau_director", attrName="municipalBureauDirector", label="正局(处)级"),
        @Column(name="municipal_deputy_bureau_director", attrName="municipalDeputyBureauDirector", label="副局(处)级"),
        @Column(name="below_municipal_bureau", attrName="belowMunicipalBureau", label="局(处)级以下"),

        // 县级机关编制
        @Column(name="county_positive", attrName="countyPositive", label="县级正职"),
        @Column(name="county_deputy", attrName="countyDeputy", label="县级副职"),
        @Column(name="section_chief", attrName="sectionChief", label="正科级"),
        @Column(name="deputy_section_chief", attrName="deputySectionChief", label="副科级"),
        @Column(name="below_section_level", attrName="belowSectionLevel", label="科级以下"),

        // 乡级机关编制
        @Column(name="township_positive", attrName="townshipPositive", label="乡级正职"),
        @Column(name="township_deputy", attrName="townshipDeputy", label="乡级副职"),
        @Column(name="below_township_level", attrName="belowTownshipLevel", label="乡级以下"),
        @Column(name="create_by", attrName="createBy", label="create_by", isUpdate=false, isQuery=false),
        @Column(name="create_date", attrName="createDate", label="create_date", isUpdate=false, isQuery=false),
        @Column(name="update_by", attrName="updateBy", label="update_by", isQuery=false),
        @Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false),
}, joinTable = {}, orderBy="a.update_date DESC"
)
public class OfficeEstablishment extends DataEntity<OfficeEstablishment> {

    private static final long serialVersionUID = 1L;

    // 中央机关编制
    private Integer ministerPositive;    // 部级正职
    private Integer ministerDeputy;      // 部级副职
    private Integer departmentDirector;  // 正司(局)级
    private Integer deputyDepartmentDirector; // 副司(局)级
    private Integer divisionLevel;       // 处级
    private Integer belowDivisionLevel;  // 处级以下

    // 省级机关编制
    private Integer provincePositive;    // 省级正职
    private Integer provinceDeputy;      // 省级副职
    private Integer bureauDirector;      // 正厅(局)级
    private Integer deputyBureauDirector;// 副厅(局)级
    private Integer divisionChief;       // 正处级
    private Integer deputyDivisionChief; // 副处级
    private Integer belowDivisionChief;  // 处级以下

    // 市级机关编制
    private Integer municipalPositive;   // 市级正职
    private Integer municipalDeputy;     // 市级副职
    private Integer municipalBureauDirector;     // 正局(处)级
    private Integer municipalDeputyBureauDirector; // 副局(处)级
    private Integer belowMunicipalBureau;// 局(处)级以下

    // 县级机关编制
    private Integer countyPositive;      // 县级正职
    private Integer countyDeputy;        // 县级副职
    private Integer sectionChief;        // 正科级
    private Integer deputySectionChief;  // 副科级
    private Integer belowSectionLevel;   // 科级以下

    // 乡级机关编制
    private Integer townshipPositive;    // 乡级正职
    private Integer townshipDeputy;      // 乡级副职
    private Integer belowTownshipLevel;  // 乡级以下

    public OfficeEstablishment() {
        this.ministerPositive = 0;
        this.ministerDeputy = 0;
        this.departmentDirector = 0;
        this.deputyDepartmentDirector = 0;
        this.divisionLevel = 0;
        this.belowDivisionLevel = 0;
        this.provincePositive = 0;
        this.provinceDeputy = 0;
        this.bureauDirector = 0;
        this.deputyBureauDirector = 0;
        this.divisionChief = 0;
        this.deputyDivisionChief = 0;
        this.belowDivisionChief = 0;
        this.municipalPositive = 0;
        this.municipalDeputy = 0;
        this.municipalBureauDirector = 0;
        this.municipalDeputyBureauDirector = 0;
        this.belowMunicipalBureau = 0;
        this.countyPositive = 0;
        this.countyDeputy = 0;
        this.sectionChief = 0;
        this.deputySectionChief = 0;
        this.belowSectionLevel = 0;
        this.townshipPositive = 0;
        this.townshipDeputy = 0;
        this.belowTownshipLevel = 0;
    }

    public static OfficeEstablishment init() {
        OfficeEstablishment officeEstablishment = new OfficeEstablishment();
        officeEstablishment.setMinisterPositive(0);
        officeEstablishment.setMinisterDeputy(0);
        officeEstablishment.setDepartmentDirector(0);
        officeEstablishment.setDeputyDepartmentDirector(0);
        officeEstablishment.setDivisionLevel(0);
        officeEstablishment.setBelowDivisionLevel(0);
        officeEstablishment.setProvincePositive(0);
        officeEstablishment.setProvinceDeputy(0);
        officeEstablishment.setBureauDirector(0);
        officeEstablishment.setDeputyBureauDirector(0);
        officeEstablishment.setDivisionChief(0);
        officeEstablishment.setDeputyDivisionChief(0);
        officeEstablishment.setBelowDivisionChief(0);
        officeEstablishment.setMunicipalPositive(0);
        officeEstablishment.setMunicipalDeputy(0);
        officeEstablishment.setMunicipalBureauDirector(0);
        officeEstablishment.setMunicipalDeputyBureauDirector(0);
        officeEstablishment.setBelowMunicipalBureau(0);
        officeEstablishment.setCountyPositive(0);
        officeEstablishment.setCountyDeputy(0);
        officeEstablishment.setSectionChief(0);
        officeEstablishment.setDeputySectionChief(0);
        officeEstablishment.setBelowSectionLevel(0);
        officeEstablishment.setTownshipPositive(0);
        officeEstablishment.setTownshipDeputy(0);
        officeEstablishment.setBelowTownshipLevel(0);
        return officeEstablishment;
    }

    public Integer getMinisterPositive() {
        return ministerPositive;
    }

    public void setMinisterPositive(Integer ministerPositive) {
        this.ministerPositive = ministerPositive;
    }

    public Integer getMinisterDeputy() {
        return ministerDeputy;
    }

    public void setMinisterDeputy(Integer ministerDeputy) {
        this.ministerDeputy = ministerDeputy;
    }

    public Integer getDepartmentDirector() {
        return departmentDirector;
    }

    public void setDepartmentDirector(Integer departmentDirector) {
        this.departmentDirector = departmentDirector;
    }

    public Integer getDeputyDepartmentDirector() {
        return deputyDepartmentDirector;
    }

    public void setDeputyDepartmentDirector(Integer deputyDepartmentDirector) {
        this.deputyDepartmentDirector = deputyDepartmentDirector;
    }

    public Integer getDivisionLevel() {
        return divisionLevel;
    }

    public void setDivisionLevel(Integer divisionLevel) {
        this.divisionLevel = divisionLevel;
    }

    public Integer getBelowDivisionLevel() {
        return belowDivisionLevel;
    }

    public void setBelowDivisionLevel(Integer belowDivisionLevel) {
        this.belowDivisionLevel = belowDivisionLevel;
    }

    public Integer getProvincePositive() {
        return provincePositive;
    }

    public void setProvincePositive(Integer provincePositive) {
        this.provincePositive = provincePositive;
    }

    public Integer getProvinceDeputy() {
        return provinceDeputy;
    }

    public void setProvinceDeputy(Integer provinceDeputy) {
        this.provinceDeputy = provinceDeputy;
    }

    public Integer getBureauDirector() {
        return bureauDirector;
    }

    public void setBureauDirector(Integer bureauDirector) {
        this.bureauDirector = bureauDirector;
    }

    public Integer getDeputyBureauDirector() {
        return deputyBureauDirector;
    }

    public void setDeputyBureauDirector(Integer deputyBureauDirector) {
        this.deputyBureauDirector = deputyBureauDirector;
    }

    public Integer getDivisionChief() {
        return divisionChief;
    }

    public void setDivisionChief(Integer divisionChief) {
        this.divisionChief = divisionChief;
    }

    public Integer getDeputyDivisionChief() {
        return deputyDivisionChief;
    }

    public void setDeputyDivisionChief(Integer deputyDivisionChief) {
        this.deputyDivisionChief = deputyDivisionChief;
    }

    public Integer getBelowDivisionChief() {
        return belowDivisionChief;
    }

    public void setBelowDivisionChief(Integer belowDivisionChief) {
        this.belowDivisionChief = belowDivisionChief;
    }

    public Integer getMunicipalPositive() {
        return municipalPositive;
    }

    public void setMunicipalPositive(Integer municipalPositive) {
        this.municipalPositive = municipalPositive;
    }

    public Integer getMunicipalDeputy() {
        return municipalDeputy;
    }

    public void setMunicipalDeputy(Integer municipalDeputy) {
        this.municipalDeputy = municipalDeputy;
    }

    public Integer getMunicipalBureauDirector() {
        return municipalBureauDirector;
    }

    public void setMunicipalBureauDirector(Integer municipalBureauDirector) {
        this.municipalBureauDirector = municipalBureauDirector;
    }

    public Integer getMunicipalDeputyBureauDirector() {
        return municipalDeputyBureauDirector;
    }

    public void setMunicipalDeputyBureauDirector(Integer municipalDeputyBureauDirector) {
        this.municipalDeputyBureauDirector = municipalDeputyBureauDirector;
    }

    public Integer getBelowMunicipalBureau() {
        return belowMunicipalBureau;
    }

    public void setBelowMunicipalBureau(Integer belowMunicipalBureau) {
        this.belowMunicipalBureau = belowMunicipalBureau;
    }

    public Integer getCountyPositive() {
        return countyPositive;
    }

    public void setCountyPositive(Integer countyPositive) {
        this.countyPositive = countyPositive;
    }

    public Integer getCountyDeputy() {
        return countyDeputy;
    }

    public void setCountyDeputy(Integer countyDeputy) {
        this.countyDeputy = countyDeputy;
    }

    public Integer getSectionChief() {
        return sectionChief;
    }

    public void setSectionChief(Integer sectionChief) {
        this.sectionChief = sectionChief;
    }

    public Integer getDeputySectionChief() {
        return deputySectionChief;
    }

    public void setDeputySectionChief(Integer deputySectionChief) {
        this.deputySectionChief = deputySectionChief;
    }

    public Integer getBelowSectionLevel() {
        return belowSectionLevel;
    }

    public void setBelowSectionLevel(Integer belowSectionLevel) {
        this.belowSectionLevel = belowSectionLevel;
    }

    public Integer getTownshipPositive() {
        return townshipPositive;
    }

    public void setTownshipPositive(Integer townshipPositive) {
        this.townshipPositive = townshipPositive;
    }

    public Integer getTownshipDeputy() {
        return townshipDeputy;
    }

    public void setTownshipDeputy(Integer townshipDeputy) {
        this.townshipDeputy = townshipDeputy;
    }

    public Integer getBelowTownshipLevel() {
        return belowTownshipLevel;
    }

    public void setBelowTownshipLevel(Integer belowTownshipLevel) {
        this.belowTownshipLevel = belowTownshipLevel;
    }
}
