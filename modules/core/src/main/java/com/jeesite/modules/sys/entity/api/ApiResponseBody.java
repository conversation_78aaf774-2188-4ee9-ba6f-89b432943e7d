package com.jeesite.modules.sys.entity.api;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class ApiResponseBody {

    private ApiResponseHead head;

    private Object data;

    public static ApiResponseBody sucess(Object result) {
        ApiResponseBody response = new ApiResponseBody();
        ApiResponseHead head = new ApiResponseHead();
        head.setStatus("0");
        head.setMessage("success");
        response.setHead(head);
        response.setData(result);
        return response;
    }

    public static ApiResponseBody sucessMap() {
        ApiResponseBody response = new ApiResponseBody();
        ApiResponseHead head = new ApiResponseHead();
        head.setStatus("0");
        head.setMessage("success");
        response.setHead(head);
        response.setData(new HashMap());
        return response;
    }

    public static ApiResponseBody error(String message) {
        ApiResponseBody response = new ApiResponseBody();
        ApiResponseHead head = new ApiResponseHead();
        head.setStatus("500");
        head.setMessage(message);
        response.setHead(head);
        return response;
    }

    public ApiResponseHead getHead() {
        return head;
    }

    public void setHead(ApiResponseHead head) {
        this.head = head;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public String toString() {
        StringBuffer sbh = new StringBuffer();
        if (head != null) {
            sbh.append(head.toString());
        }
        StringBuffer sbd = new StringBuffer();
        if (data != null) {
            sbd.append(data.toString());
        }
        return "ApiResponseBody{" +
                "head=" + sbh.toString() +
                ", data=" + sbd.toString() +
                '}';
    }
}
