/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.Extend;
import com.jeesite.common.entity.TreeEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.utils.EmpUtils;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 组织机构Entity
 * <AUTHOR>
 * @version 2017-03-23
 */
@Table(name="${_prefix}sys_office", alias="a", label="组织机构", columns={
		@Column(includeEntity=BaseEntity.class),
		@Column(includeEntity=DataEntity.class),
		@Column(includeEntity=TreeEntity.class),
		@Column(name="office_code", attrName="officeCode", 	label="机构编码", isPK=true),
		@Column(name="view_code", 	attrName="viewCode", 	label="机构代码"),
		@Column(name="office_name", attrName="officeName", 	label="机构名称", queryType=QueryType.LIKE, isTreeName=true),
		@Column(name="full_name", 	attrName="fullName", 	label="机构全称", queryType=QueryType.LIKE),
		@Column(name="office_type", attrName="officeType", 	label="机构类型"),
		@Column(name="office_nature", attrName="officeNature", 	label="机构性质"),
		@Column(name="code_certificate_number", attrName="codeCertificateNumber", 	label="代码证号"),
		@Column(name="technical_business_area", attrName="technicalBusinessArea", 	label="技术业务用房核定面积"),
		@Column(name="unit", 		attrName="unit", 		label="是否是单位", queryType=QueryType.LIKE),
		@Column(name="office_codes", attrName="officeCodeTree", 	label="机构编码树"),
		@Column(name="leader", 		attrName="leader", 		label="负责人", queryType=QueryType.LIKE),
		@Column(name="phone", 		attrName="phone", 		label="电话", queryType=QueryType.LIKE),
		@Column(name="address", 	attrName="address", 	label="联系地址", queryType=QueryType.LIKE),
		@Column(name="zip_code", 	attrName="zipCode", 	label="邮政编码", queryType=QueryType.LIKE),
		@Column(name="email", 		attrName="email", 		label="邮箱", queryType=QueryType.LIKE),
		@Column(name="region", 		attrName="region", 		label="行政区划"),
		@Column(name="office_establishment_id", 		attrName="officeEstablishmentId", 		label="组织机构编制信息ID"),
		@Column(name="external_staff", 		attrName="externalStaff", 		label="编外人数"),
		@Column(name="total_staff", 		attrName="totalStaff", 		label="编制总人数"),
		@Column(name="ext_asp_id", attrName="extAspId", label="应用支撑平台主键ID"),
		@Column(name="ext_asp_pid", attrName="extAspPid", label="应用支撑平台父ID"),
		@Column(name="ext_asp_full_parent_id", attrName="extAspFullParentId", label="应用支撑平台父id全路径"),
		@Column(name="administrative_level", attrName="administrativeLevel", label="行政级别 ADMINISTRATIVE_LEVEL"),
		@Column(name="org_type", attrName="orgType", label="单位类型 ORG_TYPE"),
		@Column(name="org_code", attrName="orgCode", label="单位编码"),
		@Column(name="social_credit_code", attrName="socialCreditCode", label="统一社会信用代码"),
		@Column(includeEntity=Extend.class, attrName="extend"),
	},
		joinTable={
			@JoinTable(
				type = JoinTable.Type.LEFT_JOIN,
				entity = ObRealEstateAddress.class,
				attrName = "obRealEstateAddress",
				alias = "rea",
				on = "a.address = rea.id",
				columns = {
						@Column(includeEntity = ObRealEstateAddress.class),
				}
			),
			@JoinTable(
					type = JoinTable.Type.LEFT_JOIN,
					entity = Area.class,
					attrName = "area",
					alias = "o",
					on = "a.region = o.area_code",
					columns = {
							@Column(includeEntity = Area.class),
					}
			),
			@JoinTable(
					type = JoinTable.Type.LEFT_JOIN,
					entity = OfficeEstablishment.class,
					attrName = "officeEstablishment",
					alias = "oe",
					on = "a.office_establishment_id = oe.id",
					columns = {
							@Column(includeEntity = OfficeEstablishment.class),
					}
			),
		},
		extWhereKeys="dsf",
		orderBy="a.tree_sorts, a.office_code"
)
public class Office extends TreeEntity<Office> {
	
	private static final long serialVersionUID = 1L;
	private String officeCode;		// 机构编码
	private String viewCode;		// 机构代码（作为显示用，多租户内唯一）
	private String officeName;		// 机构名称
	private String fullName;		// 机构全称
	private String officeType;		// 机构类型（1：省级公司；2：市级公司；3：部门）
	private String officeNature;		// 机构性质
	private String codeCertificateNumber;		// 代码证号
	private Double technicalBusinessArea;		// 技术业务用房核定面积
	private String unit;
	private String officeCodeTree;		// 机构编码树
	private String leader;		// 负责人
	private String phone;		// 电话
	private String address;		// 联系地址ID
	private String realEstateAddressName;		// 联系地址
	private String region;		// 行政区划
	private String regionName;		// 行政区划
	private String zipCode;		// 邮政编码
	private String email;		// 邮箱
	private Extend extend;		// 扩展字段

	private String companyCode; // 根据公司查询机构，组织机构所属公司
	private String officeEstablishmentId;

	private Integer externalStaff; // 编外人数
	private Integer totalStaff; // 编制总人数

	private ObRealEstateAddress obRealEstateAddress;
	private Area area;
	private OfficeEstablishmentInfo officeEstablishmentInfo;

	private OfficeEstablishment officeEstablishment;

	private Double serviceRoomApprovedArea; // 服务用房核定面积
	private Double equipmentRoomApprovedArea; // 设备用房核定面积
	private Double ancillaryRoomAreaApprovedArea; // 附属用房核定面积

	private List<OfficeUsedRealEstateCount> usedRealEstateCount;

	private String extAspId;		// 应用支撑平台主键ID
	private String extAspPid;		// 应用支撑平台父ID
	private String extAspFullParentId;		// 应用支撑平台父id全路径
	private String administrativeLevel;		// 行政级别 ADMINISTRATIVE_LEVEL
	private String orgType;		// 单位类型 ORG_TYPE
	private String orgCode;   // 单位编码
	private String socialCreditCode;		// 统一社会信用代码

	@ExcelFields({
		@ExcelField(title="上级编码", attrName="parentCode", align=Align.LEFT, sort=10),
		@ExcelField(title="机构编码", attrName="officeCode", align=Align.LEFT, sort=20),
		@ExcelField(title="机构名称", attrName="officeName", align=Align.LEFT, sort=40),
		@ExcelField(title="机构类别", attrName="officeType", align=Align.CENTER, sort=60, dictType="sys_office_type"),
		@ExcelField(title="代码证号", attrName="codeCertificateNumber", align=Align.CENTER, sort=70),
		@ExcelField(title="负责人", attrName="leader", align=Align.CENTER, sort=90),
		@ExcelField(title="电话", attrName="phone", align=Align.CENTER, sort=100),
		@ExcelField(title="联系地址", attrName="address", align=Align.CENTER, sort=110),
		@ExcelField(title="邮箱", attrName="email", align=Align.CENTER, sort=120),
	})
	public Office() {
		this(null);
	}

	public Office(String id){
		super(id);
	}
	
	@Override
	public Office getParent() {
		return parent;
	}

	@Override
	public void setParent(Office parent) {
		this.parent = parent;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
	
//	@NotBlank(message="机构代码不能为空")
	@Pattern(regexp="[a-zA-Z0-9_]{0,30}", message="代码长度不能大于 30 个字符，并且只能包含字母、数字、下划线")
	public String getViewCode() {
		return viewCode;
	}

	public void setViewCode(String viewCode) {
		this.viewCode = viewCode;
	}

	public String getViewCode_like() {
		return sqlMap().getWhere().getValue("view_code", QueryType.LIKE);
	}

	public void setViewCode_like(String viewCode) {
		sqlMap().getWhere().and("view_code", QueryType.LIKE, viewCode);
	}

	@NotBlank(message="机构名称不能为空")
	@Size(min=0, max=100, message="机构名称长度不能超过 100 个字符")
	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}
	
	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}
	
	@NotBlank(message="机构类型不能为空")
	@Size(min=0, max=1, message="机构类型长度不能超过 1 个字符")
	public String getOfficeType() {
		return officeType;
	}

	public void setOfficeType(String officeType) {
		this.officeType = officeType;
	}

	public String getOfficeNature() {
		return officeNature;
	}

	public void setOfficeNature(String officeNature) {
		this.officeNature = officeNature;
	}

	public String getCodeCertificateNumber() {
		return codeCertificateNumber;
	}

	public void setCodeCertificateNumber(String codeCertificateNumber) {
		this.codeCertificateNumber = codeCertificateNumber;
	}

	public String getOfficeCodeTree() {
		return officeCodeTree;
	}

	public void setOfficeCodeTree(String officeCodeTree) {
		this.officeCodeTree = officeCodeTree;
	}

	@ApiModelProperty("包含某机构类型")
	public String[] getOfficeType_in(){
		return sqlMap.getWhere().getValue("office_type", QueryType.IN);
	}
	
	public void setOfficeType_in(String[] officeTypes){
		sqlMap.getWhere().and("office_type", QueryType.IN, officeTypes);
	}
	
	@Size(min=0, max=100, message="负责人长度不能超过 100 个字符")
	public String getLeader() {
		return leader;
	}

	public void setLeader(String leader) {
		this.leader = leader;
	}
	
	@Size(min=0, max=100, message="电话长度不能超过 100 个字符")
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	@Size(min=0, max=255, message="联系地址长度不能超过 255 个字符")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getRealEstateAddressName() {
		return realEstateAddressName;
	}

	public void setRealEstateAddressName(String realEstateAddressName) {
		this.realEstateAddressName = realEstateAddressName;
	}

	@Size(min=0, max=100, message="邮政编码长度不能超过 100 个字符")
	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}
	
	@Size(min=0, max=200, message="邮箱长度不能超过 200 个字符")
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public Extend getExtend() {
		return extend;
	}

	public void setExtend(Extend extend) {
		this.extend = extend;
	}

	@ApiModelProperty("公司编码")
	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public OfficeEstablishmentInfo getOfficeEstablishmentInfo() {
		return officeEstablishmentInfo;
	}

	public void setOfficeEstablishmentInfo(OfficeEstablishmentInfo officeEstablishmentInfo) {
		this.officeEstablishmentInfo = officeEstablishmentInfo;
	}

	public Double getTechnicalBusinessArea() {
		return technicalBusinessArea;
	}

	public void setTechnicalBusinessArea(Double technicalBusinessArea) {
		this.technicalBusinessArea = technicalBusinessArea;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public ObRealEstateAddress getObRealEstateAddress() {
		return obRealEstateAddress;
	}

	public void setObRealEstateAddress(ObRealEstateAddress obRealEstateAddress) {
		this.obRealEstateAddress = obRealEstateAddress;
	}

	public Area getArea() {
		return area;
	}

	public void setArea(Area area) {
		this.area = area;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	/**
	 * 根据类型查找上级部门
	 * 1、例如当前机构类型为部门，你想获取部门所在的省公司名称
	 * 2、例如当前机构类型为部门的子部门，你想获取部门所在省公司名称
	 * 3、例如当前机构类型为小组，你想获取所在公司名称
	 * @param type 机构类型
	 * @return
	 */
	@JsonIgnore
	public Office getParentByType(String type){
		if (type == null){
			return null;
		}
		// 获取当前用户部门
		Office office = EmpUtils.getOffice(getOfficeCode());
		if (office == null){
			return null;
		}
		// 查找相同类型的上级部门
		do{
			office = EmpUtils.getOffice(office.getParentCode());
			if (office == null){
				return null;
			}
		}while(!type.equals(office.getOfficeType()));
		// 返回相同类型的上级部门对象
		return office;
	}

	public OfficeEstablishment getOfficeEstablishment() {
		return officeEstablishment;
	}

	public void setOfficeEstablishment(OfficeEstablishment officeEstablishment) {
		this.officeEstablishment = officeEstablishment;
	}

	public String getOfficeEstablishmentId() {
		return officeEstablishmentId;
	}

	public void setOfficeEstablishmentId(String officeEstablishmentId) {
		this.officeEstablishmentId = officeEstablishmentId;
	}

	public Integer getExternalStaff() {
		return externalStaff;
	}

	public void setExternalStaff(Integer externalStaff) {
		this.externalStaff = externalStaff;
	}

	public Double getServiceRoomApprovedArea() {
		return serviceRoomApprovedArea;
	}

	public void setServiceRoomApprovedArea(Double serviceRoomApprovedArea) {
		this.serviceRoomApprovedArea = serviceRoomApprovedArea;
	}

	public Double getEquipmentRoomApprovedArea() {
		return equipmentRoomApprovedArea;
	}

	public void setEquipmentRoomApprovedArea(Double equipmentRoomApprovedArea) {
		this.equipmentRoomApprovedArea = equipmentRoomApprovedArea;
	}

	public Double getAncillaryRoomAreaApprovedArea() {
		return ancillaryRoomAreaApprovedArea;
	}

	public void setAncillaryRoomAreaApprovedArea(Double ancillaryRoomAreaApprovedArea) {
		this.ancillaryRoomAreaApprovedArea = ancillaryRoomAreaApprovedArea;
	}

	public Integer getTotalStaff() {
		return totalStaff;
	}

	public void setTotalStaff(Integer totalStaff) {
		this.totalStaff = totalStaff;
	}

	public List<OfficeUsedRealEstateCount> getUsedRealEstateCount() {
		return usedRealEstateCount;
	}

	public void setUsedRealEstateCount(List<OfficeUsedRealEstateCount> usedRealEstateCount) {
		this.usedRealEstateCount = usedRealEstateCount;
	}

	//	@Override
//	public String toString() {
//		return officeCode;
//	}


	public String getExtAspId() {
		return extAspId;
	}

	public void setExtAspId(String extAspId) {
		this.extAspId = extAspId;
	}

	public String getExtAspPid() {
		return extAspPid;
	}

	public void setExtAspPid(String extAspPid) {
		this.extAspPid = extAspPid;
	}

	public String getExtAspFullParentId() {
		return extAspFullParentId;
	}

	public void setExtAspFullParentId(String extAspFullParentId) {
		this.extAspFullParentId = extAspFullParentId;
	}

	public String getAdministrativeLevel() {
		return administrativeLevel;
	}

	public void setAdministrativeLevel(String administrativeLevel) {
		this.administrativeLevel = administrativeLevel;
	}

	public String getOrgType() {
		return orgType;
	}

	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getSocialCreditCode() {
		return socialCreditCode;
	}

	public void setSocialCreditCode(String socialCreditCode) {
		this.socialCreditCode = socialCreditCode;
	}
}