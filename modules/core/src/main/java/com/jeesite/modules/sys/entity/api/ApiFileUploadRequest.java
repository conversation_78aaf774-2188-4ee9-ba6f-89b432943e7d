package com.jeesite.modules.sys.entity.api;

import com.jeesite.modules.sys.entity.api.ApiUserInfo;

// 用于接收前端传过来的文件数据
public class ApiFileUploadRequest extends ApiUserInfo {

    private String fileContent;
    private String fileName;
    private String fileType;
    private String type;

    private String fileOriginName;

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileOriginName() {
        return fileOriginName;
    }

    public void setFileOriginName(String fileOriginName) {
        this.fileOriginName = fileOriginName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}