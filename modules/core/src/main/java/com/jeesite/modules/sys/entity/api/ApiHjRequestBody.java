package com.jeesite.modules.sys.entity.api;

public class ApiHjRequestBody {

    private String sirc_token;          //"e3f6d692-c63a-4341-b4c0-3db6e14aa886",//必传参数：获取到的token，这个token应该是sm2获取到的

    private long sirc_time;           // 1691128084410,//必传参数，13位调用时间戳
    private String comm_ip;             // "**************",//必传参数，申请应用系统服务器局域网IP地址
    private String comm_mac;            // 00-FF-AE-07-AE-FF",//必传参数，申请应用系统服务器MAC地址

    private String comm_applicationID;  // DHJ222344,//必传参数，实际应用系统ID
    private String comm_applicationSystemName;  //省网上办事大厅",//必传参数，实际应用系统名称
    private String comm_netType;        // "1",//必传参数，应用系统服务访问网络
    private String comm_callerType;     // "1",//必传参数，本次调用应用场景类型
    private String comm_queryCause;     // "查询企业注册信息",//必传参数，查询事由
    private String comm_queryResume;    // "因业务需要，需查询某公司的注册信息",//必传参数，查询信息简述


    // 以下字段涉及政务服务办理时该字段必填，例如：省网上办事大厅、地市行政中心等系统接口调用时此字段必填
    //private String comm_itemBaseDirectoryCode;  // "000131003000",//选填参数：政务服务事项办理过程中对应的办理项编码
    //private String comm_itemCode;       // "11350000MB1859212YXK000283", //选填参数：政务服务事项办理过程中对应的办理项编码
    //private String comm_itemCodeName;   // "公司设立登记注册（省级权限）",//选填参数，业务办理项名称，如业务办理项编码不为空，则业务办理项名称不能为空，反之，可为空
    //private String comm_itemNumber;     // "2113F0AABD65087E0633264A85",//选填参数，办件编号，如业务办理项编码不为空，则办件编号不能为空，反之，可为空

    private String comm_checkType;          //"501&502",//必传参数，依据调用场景要求传参；核验类型参数

    //private String comm_userVerifyAccessToken;  //"7f77c5371d3e2f76ba807d984f8389a9",//选填参数，依据调用场景要求传参；办理申请人员(公众用户)登录凭证
        // 办理申请人员(公众用户)登录凭证（comm_checkType传参包含：501类型时必填）
    //private String comm_userVerifyCode;     // "6c5a3ba9-ed9f-4521-a7ca-45f4a921b737",//选填参数，依据调用场景要求传参；办理申请人员(公众用户)身份凭证；
        // 办理申请人员(公众用户)身份凭证（comm_checkType传参包含：502或504类型时必填）
    //private String comm_userCheckIdCard;    // "35010319880220011X",//选填参数，依据调用场景要求传参；办理申请人员(公众用户)证件号码
        // 办理申请人员(公众用户)证件号码（comm_checkType传参包含：501或502或504类型时必填；当comm_checkType组合里同时包含501、502或504时，该参数只需传一个）
    //private String comm_userCheckName;      // "李四",//选填参数，依据调用场景要求传参；办理申请人员(公众用户)证件姓名
        // 办理申请人员(公众用户)证件姓名（comm_checkType传参包含：501或502或504类型时必填；当comm_checkType组合里同时包含501、502或504时，该参数只需传一个）

    //private String comm_clerksVerifyAccessToken;    //"7f77c5371d3e2f76ba8845kfdjsda89a9",//选填参数，依据调用场景要求传参；工作人员登录凭证
        // 工作人员登录凭证（comm_checkType传参包含：511类型时必填）
    //private String comm_clerksVerifyCode;   // "6c5a3ba9-ed9f-4521-a7ca-45f4a921fds737",//选填参数，依据调用场景要求传参；工作人员身份凭证；
        // 工作人员身份凭证（comm_checkType传参包含：512或513类型时必填）
    //private String comm_clerksCheckIdCard;  // "35010319880220886X",//选填参数，依据调用场景要求传参；工作人员证件号码
        // 工作人员证件号码（comm_checkType传参包含：511或512或513类型时必填；当comm_checkType组合里同时包含511、512或513时，该参数只需传一个）
    //private String comm_clerksCheckName;    // "王五",//选填参数，依据调用场景要求传参；工作人员证件姓名
        // 工作人员证件姓名（comm_checkType传参包含：511或512或513类型时必填；当comm_checkType组合里同时包含511、512或513时，该参数只需传一个）

    // 以下几个字段都是涉及代办的，我们应该不会用到
    //private String comm_consignerVerifyCode;    // "2024072910564193",//选填参数，依据调用场景要求传参；代办人员或企业授权凭证码
    //private String comm_consignerVerifyType;    // "1",//选填参数，依据调用场景要求传参；代办人员或企业授权凭证码核验场景
    //private String comm_consignerCheckIdCard;   // "35010319870220033X",//选填参数，依据调用场景要求传参；代办人员或企业证件号码
    //private String comm_consignerCheckName;     // "张三",//选填参数，依据调用场景要求传参；代办人员或企业证件姓名

    private String accountId;                   // "123", //用户标识——必填,由省/地市公积金中心线下提供
    private String secret;                      // "123", //用户签名密钥——必填,由省/地市公积金中心线下提供
    private String zxbh;                        // "123" //中心编号——必填，参见附表《中心编

    // 获取公积金token

    // 根据证件获取公积金信息
    private String zjlx;                        // "123", //证件类型——必填, 参见附表《个人证件类型编码表》
    private String zjhm;                        // "12333", //证件号码——必填
    //private String zxbh;                        // "123", //中心编号——必填，参见附表《中心编号编码表》
    private String reqTrsTell;                  // "123", //交易发起操作员证件号码——必填
    private String reqTrsTellName;              // "123", //交易发起操作员姓名——必填
    private String token;                       // "123", //访问凭证——必填,由公积金查询服务认证接口获取
    //private accountId;                          // "123" //用户标识——必填,由省/地市公积金中心线下提供


    public String getSirc_token() {
        return sirc_token;
    }
    public void setSirc_token(String sirc_token) {
        this.sirc_token = sirc_token;
    }
    public long getSirc_time() {
        return sirc_time;
    }
    public void setSirc_time(long sirc_time) {
        this.sirc_time = sirc_time;
    }
    public String getComm_ip() {
        return comm_ip;
    }
    public void setComm_ip(String comm_ip) {
        this.comm_ip = comm_ip;
    }
    public String getComm_mac() {
        return comm_mac;
    }
    public void setComm_mac(String comm_mac) {
        this.comm_mac = comm_mac;
    }
    public String getComm_applicationID() {
        return comm_applicationID;
    }
    public void setComm_applicationID(String comm_applicationID) {
        this.comm_applicationID = comm_applicationID;
    }
    public String getComm_applicationSystemName() {
        return comm_applicationSystemName;
    }
    public void setComm_applicationSystemName(String comm_applicationSystemName) {
        this.comm_applicationSystemName = comm_applicationSystemName;
    }
    public String getComm_netType() {
        return comm_netType;
    }
    public void setComm_netType(String comm_netType) {
        this.comm_netType = comm_netType;
    }
    public String getComm_callerType() {
        return comm_callerType;
    }
    public void setComm_callerType(String comm_callerType) {
        this.comm_callerType = comm_callerType;
    }
    public String getComm_queryCause() {
        return comm_queryCause;
    }
    public void setComm_queryCause(String comm_queryCause) {
        this.comm_queryCause = comm_queryCause;
    }
    public String getComm_queryResume() {
        return comm_queryResume;
    }
    public void setComm_queryResume(String comm_queryResume) {
        this.comm_queryResume = comm_queryResume;
    }
    public String getComm_checkType() {
        return comm_checkType;
    }
    public void setComm_checkType(String comm_checkType) {
        this.comm_checkType = comm_checkType;
    }
    public String getAccountId() {
        return accountId;
    }
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }
    public String getSecret() {
        return secret;
    }
    public void setSecret(String secret) {
        this.secret = secret;
    }
    public String getZxbh() {
        return zxbh;
    }
    public void setZxbh(String zxbh) {
        this.zxbh = zxbh;
    }
    public String getZjlx() {
        return zjlx;
    }
    public void setZjlx(String zjlx) {
        this.zjlx = zjlx;
    }
    public String getZjhm() {
        return zjhm;
    }
    public void setZjhm(String zjhm) {
        this.zjhm = zjhm;
    }
    public String getReqTrsTell() {
        return reqTrsTell;
    }
    public void setReqTrsTell(String reqTrsTell) {
        this.reqTrsTell = reqTrsTell;
    }
    public String getReqTrsTellName() {
        return reqTrsTellName;
    }
    public void setReqTrsTellName(String reqTrsTellName) {
        this.reqTrsTellName = reqTrsTellName;
    }
    public String getToken() {
        return token;
    }
    public void setToken(String token) {
        this.token = token;
    }


    
}
