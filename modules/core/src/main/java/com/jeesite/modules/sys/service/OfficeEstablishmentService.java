package com.jeesite.modules.sys.service;

import com.jeesite.common.service.CrudService;
import com.jeesite.modules.sys.dao.OfficeEstablishmentDao;
import com.jeesite.modules.sys.entity.OfficeEstablishment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class OfficeEstablishmentService extends CrudService<OfficeEstablishmentDao, OfficeEstablishment> {


    /**
     * 保存数据（插入或更新）
     * @param officeEstablishment
     */
    @Override
    @Transactional
    public void save(OfficeEstablishment officeEstablishment) {
        super.save(officeEstablishment);
    }


}
