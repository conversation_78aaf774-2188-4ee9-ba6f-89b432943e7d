package com.jeesite.modules.sys.entity.api;

import java.util.Map;

public class ApiRequestBody<T> {

    private ApiRequestHead head;

    private T data;



    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public ApiRequestHead getHead() {
        return head;
    }

    public void setHead(ApiRequestHead head) {
        this.head = head;
    }

    @Override
    public String toString() {
        return "ApiRequestBody{" +
                "head=" + head.toString() +
                ", data=" + data.toString() +
                '}';
    }
}
