package com.jeesite.modules.bpm.web;


import com.fasterxml.jackson.annotation.JsonView;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.ObjectUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.entity.BpmBackActivity;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.bpm.entity.BpmForm;
import com.jeesite.modules.bpm.entity.BpmProcDef;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.entity.BpmSignUser;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.entity.BpmVariable;
import com.jeesite.modules.bpm.flowable.utils.FlowableUtils;
import com.jeesite.modules.bpm.service.BpmProcessService;
import com.jeesite.modules.bpm.service.BpmRuntimeService;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmFormUtils;
import com.jeesite.modules.bpm.utils.BpmNextNodeUtils;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.Role;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.RoleUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.UserTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(
        tags = {"ABpmTask - 任务处理"}
)
@RequestMapping({"${adminPath}/bpm/bpmTask"})
public class BpmTaskController extends BaseController {
    @Autowired
    private BpmProcessService bpmProcessService;
    @Autowired
    private BpmRuntimeService bpmRuntimeService;
    @Autowired
    private BpmTaskService bpmTaskService;

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"getTask"})
    @ResponseBody
    @JsonView({DataEntity.SimpleView.class})
    @ApiOperation("获取任务实例数据")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "流程实例ID",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "procIns.formKey",
            value = "表单Key",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "procIns.bizKey",
            value = "业务主键",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public BpmTask getTask(BpmTask params) {
        BpmTask task = null;
        if (StringUtils.isNotBlank(params.getId())) {
            task = this.bpmTaskService.getTask(params.getId());
        } else if (StringUtils.isNotBlank(params.getProcIns().getFormKey()) && StringUtils.isNotBlank(params.getProcIns().getBizKey())) {
            task = this.bpmTaskService.getTaskByBusinessKey(params.getProcIns().getFormKey(), params.getProcIns().getBizKey(), params.currentUser().getUserCode());
            if (task == null) {
                BpmProcIns procIns = this.bpmRuntimeService.getProcessInstanceByBusinessKey(params.getProcIns().getFormKey(), params.getProcIns().getBizKey());
                if (procIns != null) {
                    task = new BpmTask(procIns);
                }
            }
        }

        if (task != null) {
            if (StringUtils.isNotBlank(task.getProcIns().getFormKey())) {
                BpmForm form = BpmFormUtils.getForm(task.getProcIns().getFormKey(), task.getProcIns().getProcDef().getVersion(), task.getActivityId());
                if (form != null) {
                    task.getProcIns().getProcDef().setForm(form);
                }
            }

            BpmnModel bpmnModel = FlowableUtils.getBpmnModel(task.getProcIns().getProcDef().getId());
            task.setHasMultiInstance(FlowableUtils.hasMultiInstance(bpmnModel, task.getActivityId())[0]);
            return task;
        } else {
            return params;
        }
    }

    @ApiOperation("获取下一步处理人信息")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "prodDefKey",
            value = "流程定义Key",
            required = true,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "activityId",
            value = "流程活动ID",
            required = true,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "dataMap",
            value = "流程变量",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"getNextUser"})
    @ResponseBody
    public String getNextUser(@RequestBody BpmNextNodeUtils.NextNodeParams params) {
        Map<String, Object> data = MapUtils.newHashMap();
        List<Map<String, Object>> nextUsers = ListUtils.newArrayList();
        List<UserTask> nextUserTaskList = BpmNextNodeUtils.getNextUserTasks(params);
        if (ListUtils.isNotEmpty(nextUserTaskList)) {
            for(UserTask userTask : nextUserTaskList) {
                Map<String, Object> nextUserTask = MapUtils.newHashMap();
                if (StringUtils.isNotBlank(userTask.getAssignee())) {
                    User u = UserUtils.get(userTask.getAssignee());
                    if (u != null) {
                        nextUserTask.put("userCode", u.getUserCode());
                        nextUserTask.put("userName", u.getUserName());
                    }
                } else if (ListUtils.isNotEmpty(userTask.getCandidateUsers())) {
                    List<String> userCodes = ListUtils.newArrayList();
                    List<String> userNames = ListUtils.newArrayList();
                    userTask.getCandidateUsers().forEach((userCode) -> {
                        User u = UserUtils.get(userCode);
                        if (u != null) {
                            userCodes.add(u.getUserCode());
                            userNames.add(u.getUserName());
                        }

                    });
                    if (ListUtils.isNotEmpty(userCodes)) {
                        nextUserTask.put("userCodes", userCodes);
                        nextUserTask.put("userNames", userNames);
                    }
                } else if (ListUtils.isNotEmpty(userTask.getCandidateGroups())) {
                    List<String> roleCodes = ListUtils.newArrayList();
                    List<String> roleNames = ListUtils.newArrayList();
                    userTask.getCandidateGroups().forEach((roleCode) -> {
                        Role r = RoleUtils.get(roleCode);
                        if (r != null) {
                            roleCodes.add(r.getRoleCode());
                            roleNames.add(r.getRoleName());
                        }

                    });
                    if (ListUtils.isNotEmpty(roleCodes)) {
                        nextUserTask.put("roleCodes", roleCodes);
                        nextUserTask.put("roleNames", roleNames);
                    }
                }

                if (!nextUserTask.isEmpty()) {
                    nextUsers.add(nextUserTask);
                }
            }
        }

        data.put("nextUsers", nextUsers);
        Map<String, Object> office = MapUtils.newHashMap();
        Office o = EmpUtils.getOffice();
        office.put("officeCode", o.getOfficeCode());
        office.put("parentCode", o.getParentCode());
        data.put("office", office);
        return this.renderResult("true", text("获取成功", new String[0]), data);
    }

    @ApiOperation("任务查询列表")
    @RequiresPermissions({"bpm:bpmTask"})
    @RequestMapping({"list"})
    public String list(BpmTask params, Model model) {
        model.addAttribute("params", params);
        return "modules/bpm/bpmTaskList";
    }

    @ApiOperation("任务查询数据")
    @RequiresPermissions({"bpm:bpmTask"})
    @RequestMapping({"listData"})
    @ResponseBody
    public Page<BpmTask> listData(BpmTask params, HttpServletRequest request, HttpServletResponse response) {
        params.setPage(new Page(request, response));
        Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);
        return page;
    }

    @ApiOperation("启动流程页面")
    @RequiresPermissions({"bpm:bpmTask"})
    @RequestMapping({"start"})
    public String start(BpmProcDef params, Model model) {
        BpmProcDef pd = null;
        if (StringUtils.isNotBlank(params.getId())) {
            pd = this.bpmProcessService.getProcessDefinition(params.getId());
        } else if (StringUtils.isNotBlank(params.getKey())) {
            pd = this.bpmProcessService.getProcessDefinitionByKey(params.getKey());
        }

        if (pd != null) {
            List<BpmVariable> dataObject = this.bpmProcessService.getProcessDataObject(pd.getId());
            model.addAttribute("dataObject", dataObject);
            params = pd;
        }

        params.setName(text("{0} 在 {1} 发起了 {2}", new String[]{params.currentUser().getUserName(), DateUtils.formatDate(new Date(), "yyyy-MM-dd"), params.getName()}) + "【" + text("调试", new String[0]) + "】");
        model.addAttribute("procDef", params);
        return "modules/bpm/bpmTaskStart";
    }

    @ApiOperation("启动流程发起")
    @RequiresPermissions({"bpm:bpmTask"})
    @RequestMapping({"startProcess"})
    @ResponseBody
    public String startProcess(BpmTask params) {
        Global.assertDemoMode();

        try {
            BpmTask bpmTask = this.bpmTaskService.startProcess(params);
            return this.renderResult("true", text("启动流程成功", new String[0]), bpmTask);
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("启动流程失败", new String[0]), e);
        }
    }

    @ApiOperation("任务办理页面")
    @RequiresPermissions({"bpm:bpmTask"})
    @RequestMapping({"form"})
    public String form(BpmTask params, Model model) {
        BpmEntity<?> bpmEntity = new BpmEntity();
        bpmEntity.getBpm().setTaskId(params.getId());
        model.addAttribute("bpmEntity", bpmEntity);
        return "modules/bpm/bpmTaskForm";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"claim"})
    @ResponseBody
    @ApiOperation("签收任务")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String claim(BpmTask params) {
        Global.assertDemoMode();

        try {
            this.bpmTaskService.claimTask(params);
            return this.renderResult("true", text("签收成功", new String[0]));
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("签收失败", new String[0]), e);
        }
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"unclaim"})
    @ResponseBody
    @ApiOperation("取消签收任务")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String unclaim(BpmTask params) {
        Global.assertDemoMode();

        try {
            this.bpmTaskService.unclaimTask(params);
            return this.renderResult("true", text("取消签收成功", new String[0]));
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("取消签收失败", new String[0]), e);
        }
    }

    @ApiOperation("任务提交")
    @RequiresPermissions({"bpm:bpmTask"})
    @RequestMapping({"complete"})
    @ResponseBody
    public String complete(BpmTask params) {
        Global.assertDemoMode();

        try {
            this.bpmTaskService.completeTask(params);
            return this.renderResult("true", text("提交成功", new String[0]));
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("提交失败", new String[0]), e);
        }
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"turn"})
    @ApiOperation("转办或委托任务页面")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String turn(BpmTask params, Model model) {
        BpmTask task = this.bpmTaskService.getTask(params.getId());
        if (task != null) {
            params = task;
        }

        model.addAttribute("task", params);
        return "modules/bpm/bpmTaskTurn";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"turnTask"})
    @ResponseBody
    @ApiOperation("转办或委托任务执行")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "delegateState",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String",
            example = "true委托；false转办"
    )})
    public String turnTask(BpmTask params) {
        Global.assertDemoMode();
        String op = ObjectUtils.toBoolean(params.getDelegateState()) ? "委托" : "转办";

        try {
            this.bpmTaskService.turnTask(params);
            return this.renderResult("true", text(op + "成功", new String[0]));
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text(op + "失败", new String[0]), e);
        }
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"back"})
    @ApiOperation("退回任务页面")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String back(BpmTask params, Model model) {
        BpmTask task = this.bpmTaskService.getTask(params.getId());
        if (task != null) {
            List<BpmBackActivity> backActivity = this.bpmTaskService.getBackActivity(task);
            model.addAttribute("backActivity", backActivity);
            params = task;
        }

        model.addAttribute("task", params);
        return "modules/bpm/bpmTaskBack";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"backTask"})
    @ResponseBody
    @ApiOperation("退回任务执行")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "comment",
            value = "退回原因",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "activityId",
            value = "退回环节ID",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "nextUserCodes",
            value = "退回给谁",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public String backTask(BpmTask params) {
        Global.assertDemoMode();

        try {
            this.bpmTaskService.backTask(params);
            return this.renderResult("true", text("退回成功", new String[0]));
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("退回失败", new String[0]), e);
        }
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"move"})
    @ApiOperation("自由跳转任务页面")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String move(BpmTask params, Model model) {
        if (StringUtils.isNotBlank(params.getId())) {
            params.setStatus("1");
            BpmTask task = this.bpmTaskService.getTask(params.getId());
            if (task != null) {
                params = task;
            }
        } else if (params.currentUser().isAdmin() || UserUtils.getSubject().isPermitted("bpm:bpmRuntime:admin") || UserUtils.getSubject().isPermitted("bpm:bpmRuntime:move")) {
            params.setPage(new Page(1, 1, -1L));
            Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);
            if (ListUtils.isNotEmpty(page.getList())) {
                params = (BpmTask)page.getList().get(0);
            }
        }

        model.addAttribute("task", params);
        return "modules/bpm/bpmTaskMove";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"moveTask"})
    @ResponseBody
    @ApiOperation("自由跳转任务执行")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "comment",
            value = "退回原因",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "activityId",
            value = "退回环节ID",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public String moveTask(BpmTask params) {
        Global.assertDemoMode();

        try {
            this.bpmTaskService.moveTask(params);
            return this.renderResult("true", text("自由跳转成功", new String[0]));
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("自由跳转失败", new String[0]), e);
        }
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"rollback"})
    @ResponseBody
    @ApiOperation("撤销取回任务执行")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String rollback(BpmTask params) {
        Global.assertDemoMode();

        try {
            this.bpmTaskService.rollbackTask(params);
            return this.renderResult("true", text("撤回成功", new String[0]));
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("撤回失败", new String[0]), e);
        }
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"modifySign"})
    @ApiOperation("任务加签减签页面")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String modifySign(BpmTask params, Model model) {
        BpmTask task = this.bpmTaskService.getTask(params.getId());
        if (task != null) {
            List<BpmSignUser> signUserList = this.bpmTaskService.getSignUserList(task);
            model.addAttribute("signUserList", signUserList);
            BpmnModel bpmnModel = FlowableUtils.getBpmnModel(task.getProcIns().getProcDef().getId());
            boolean isSequential = FlowableUtils.hasMultiInstance(bpmnModel, task.getActivityId())[1];
            model.addAttribute("isSequential", isSequential);
            params = task;
        }

        model.addAttribute("task", params);
        return "modules/bpm/bpmTaskModifySign";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"modifySignTask"})
    @ResponseBody
    @ApiOperation("任务加签减签执行")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "任务ID",
            required = true,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "executionId",
            value = "减签执行编码",
            required = true,
            paramType = "query",
            dataType = "String",
            example = "多个用逗号隔开"
    ), @ApiImplicitParam(
            name = "assignee",
            value = "加签用户编码",
            required = true,
            paramType = "query",
            dataType = "String",
            example = "多个用逗号隔开"
    )})
    public String modifySignTask(BpmTask params) {
        Global.assertDemoMode();

        try {
            this.bpmTaskService.modifySignTask(params);
            return this.renderResult("true", text("加减签成功", new String[0]));
        } catch (Exception e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("加减签失败", new String[0]), e);
        }
    }
}
