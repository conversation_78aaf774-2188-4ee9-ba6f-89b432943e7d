/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.web;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.hsobs.modules.datasync.service.DataUserLoginService;
import com.jeesite.common.codec.Md5Utils;
import com.jeesite.common.config.Global;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.ObjectUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.shiro.authc.FormToken;
import com.jeesite.common.shiro.filter.FormFilter;
import com.jeesite.common.web.BaseController;
import com.jeesite.common.web.http.ServletUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.entity.sso.SsoLoginUser;
import com.jeesite.modules.sys.entity.sso.SsoResponseBody;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authz.UnauthorizedException;
import org.apache.shiro.session.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 单点登录Controller
 *
 * <AUTHOR>
 * @version 2020-9-19
 */
@Controller
@Api(tags = "SSO - 单点登录")
@ConditionalOnProperty(name = {"user.enabled", "web.core.enabled"}, havingValue = "true", matchIfMissing = true)
public class SsoController extends BaseController {

    @Autowired
    private EmpUserService empUserService;
    @Autowired
    private DataUserLoginService dataUserLoginService;
    @Autowired
    RestTemplate restTemplate;

    public static void main(String[] args) {
        String username = "jgjb1";
        String secretKey = "kRh#1g5ssQ"; // Global.getConfig("shiro.sso.secretKey");
//        String token = UserUtils.getSsoToken("jgjb1");
        String token = Md5Utils.md5(secretKey + username + DateUtils.getDate("yyyyMMddHH"));
        System.out.println("http://127.0.0.1:8980/hsobs/ssoJson/" + username + "/" + token + "?url=/admin/index#/hsobs/admin/clearance/hsQwClearance/list#审批待办");
        System.out.println("http://192.168.56.1:3100/hsobs/sso/" + username + "/" + token + "?url=../sys/office/index&relogin=true");

//		String secretKey = Global.getConfig("shiro.loginSubmit.secretKey");
//		String username = DesUtils.encode("system", secretKey);
//		String password = DesUtils.encode("HsObs@tt951.", secretKey);
//		System.out.println("&username=" + username + "&password=" + password);
    }

    private SsoResponseBody testUserData() {
        SsoResponseBody result = new SsoResponseBody();
        result.setCode("1");
        result.setMessage("");
        SsoLoginUser ssoLoginUser = new SsoLoginUser();
        ssoLoginUser.setUserId("2FAC3635F10C4431E06300000000C53F");
        ssoLoginUser.setIdCard("");
        ssoLoginUser.setMobile("***********");
        ssoLoginUser.setName("");
        ssoLoginUser.setOrgId("");
        ssoLoginUser.setOrgCode("");
        ssoLoginUser.setOrgName("");
        ssoLoginUser.setTgt("");
        ssoLoginUser.setAccount("***********");
        result.setData(ssoLoginUser);
        return result;
    }
    private SsoResponseBody testSysUserData() {
        SsoResponseBody result = new SsoResponseBody();
        result.setCode("1");
        result.setMessage("");
        SsoLoginUser ssoLoginUser = new SsoLoginUser();
        ssoLoginUser.setUserId("2BE4F2ABCC6E4BBFE06300000000C127");
        ssoLoginUser.setIdCard("");
        ssoLoginUser.setMobile("***********");
        ssoLoginUser.setName("");
        ssoLoginUser.setOrgId("");
        ssoLoginUser.setOrgCode("");
        ssoLoginUser.setOrgName("");
        ssoLoginUser.setTgt("");
        ssoLoginUser.setAccount("***********");
        result.setData(ssoLoginUser);
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "sso/login")
    public String ssoLogin(String ticket) throws JsonProcessingException {
        HttpServletRequest request = ServletUtils.getRequest();

        String hostHeader = request.getHeader("Host");
        String priUrl = Global.getConfig("shiro.casServerUrl");
        String pubUrl = Global.getConfig("shiro.casServerPublicUrl");
        String pubHostHeader = Global.getConfig("shiro.casServerPublicHostHeader", "jgswglj.fujian.gov.cn");
        String ssoLoginType = Global.getConfig("shiro.ssoLoginType", "2");

        String finalUrl = priUrl;
        if (pubHostHeader.contains(hostHeader)) {
            finalUrl = pubUrl;
        }

        String successUrl = request.getParameter("__url");
        if (StringUtils.isNotBlank(successUrl)) {
            finalUrl += "?__url=" + successUrl;
        }

        SsoResponseBody result = restTemplate.getForObject(finalUrl + "&format=json&ticket=" + ticket, SsoResponseBody.class);
//        SsoResponseBody result = testUserData();

        if (result != null && "1".equals(result.getCode())) {
            logger.info("接收sso信息：" + result.getLogMsg());
            HttpServletResponse response = ServletUtils.getResponse();
            try {
                // FormToken 构造方法的三个参数：登录名、是否内部登录无条件、请求对象
                // 通过 userId 和 手机号码获取用户信息，并登录
//                EmpUser user = empUserService.getUserBySsoLoginUser(result.getData());
                EmpUser user = null;
                if ("2".equals(ssoLoginType)) {
                    user = dataUserLoginService.getUserBySsoLoginUserRemote(result.getData(), true, false);
                } else if ("1".equals(ssoLoginType)) {
                    user = empUserService.getUserBySsoLoginUser(result.getData());
                } else if ("3".equals(ssoLoginType)) {
                    user = dataUserLoginService.getUserBySsoLoginUserRemote(result.getData(), false, false);
                } else {
                    user = dataUserLoginService.getUserBySsoLoginUserRemote(result.getData(), false, true);
                }
                if (user == null || CollectionUtil.isEmpty(user.getRoleList())) {
                    try {
                        // 如果访问的是未授权页面，则直接转到403页面（2016-11-3）
                        request.getRequestDispatcher("/error/403").forward(request, response);
                    } catch (Exception e) {
                        throw new UnauthorizedException(e);
                    }
//                    FormFilter.onLoginFailure(new AuthenticationException("用户不存在"), request, response);
                } else {
                    UserUtils.getSubject().login(new FormToken(user.getLoginCode(), true, request));
                    if (StringUtils.isNotEmpty(successUrl)) {
                        successUrl = successUrl.replaceAll("_sctffs_", "#");
                        request.setAttribute("__url", successUrl);
                    }
                    FormFilter.onLoginSuccess(request, response);
                }
            } catch (AuthenticationException e) {
                FormFilter.onLoginFailure(e, request, response);
            }
        } else {
            logger.error("接收异常信息：" + result.getLogMsg());
            throw new ServiceException("单点登录异常！");
        }
        return null;
    }
    /**
     * 单点登录-json接口（如已经登录，则直接跳转）
     * @param username 	登录用户名（loginCode）
     * @param token 	单点登录令牌，令牌组成：sso密钥+用户名+日期，进行md5加密，举例：
     * 		// 注意如果 shiro.sso.encryptKey 为 true，则 secretKey 会自动加密。
     * 		String secretKey = Global.getConfig("shiro.sso.secretKey");
     * 		String token = Md5Utils.md5(secretKey + username + DateUtils.getDate("yyyyMMdd"));
     * @param params 	登录附加参数（JSON格式），或 param_ 前缀的请求参数。
     * @param url 		登录成功后跳转的url地址。
     * @param relogin 	是否强制重新登录，需要强制重新登录传递true
     * @see 调用示例：
     * 	http://localhost/project/sso/{username}/{token}?url=/sys/user/list?p1=v1%26p2=v2&relogin=true
     * 	如果url中携带参数，请使用转义字符，如“&”号，使用“%26”转义。
     */
    @ResponseBody
    @RequestMapping(value = "ssoJson/{username}/{token}")
    public String ssoJson(@PathVariable String username, @PathVariable String token,
                          @RequestParam(defaultValue="${adminPath}/index") String url, String relogin,
                          HttpServletRequest request, HttpServletResponse response, Model model){
        // 通过令牌重定向登录
        if (token != null){
            try {
                // FormToken 构造方法的三个参数：登录名、单点登录的令牌秘钥、请求对象
                UserUtils.getSubject().login(new FormToken(username, token, request));
                User user = UserUtils.getUser();
                if(StringUtils.isNotBlank(user.getUserCode())
                        && StringUtils.equals(user.getLoginCode(), username)
                        && !ObjectUtils.toBoolean(relogin)){
                    // 获取当前会话对象，并返回一些数据
                    Session session = UserUtils.getSession();
                    // 获取登录数据
                    model.addAllAttributes(FormFilter.getLoginData(request, response));
                    model.addAttribute("session", session.getId());
                    return ServletUtils.renderResult(response, Global.TRUE, text("账号已登录"), model);
                }else {
                    return ServletUtils.renderResult(response, Global.TRUE, text("账号未登录"));
                }
            } catch (AuthenticationException e) {
                FormFilter.onLoginFailure(e, request, response);
            }
        }
        return ServletUtils.renderResult(response, Global.TRUE, text("登录异常"));
    }

    @ResponseBody
    @RequestMapping(value = "digit-space-sso/p3/serviceValidate")
    public SsoResponseBody mockLogin(String ticket, String service, HttpServletResponse response) throws IOException {
        if (StringUtils.isNotBlank(ticket)) {
            SsoResponseBody result = new SsoResponseBody();
            result.setCode("1");
            result.setMessage("ssss");
            SsoLoginUser user = new SsoLoginUser();
            user.setUserId("system");
            result.setData(user);
            return result;
        } else {
            response.sendRedirect(service + "?ticket=ddd");
            return null;
        }
    }
}
