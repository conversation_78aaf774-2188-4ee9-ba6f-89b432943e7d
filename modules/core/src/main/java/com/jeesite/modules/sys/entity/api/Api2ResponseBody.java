package com.jeesite.modules.sys.entity.api;

public class Api2ResponseBody<T> {

    private Integer code;
    private String message;
    private T data;

    public static Api2ResponseBody sucess(Object result) {
        Api2ResponseBody response = new Api2ResponseBody();
        response.setCode(1);
        response.setMessage("success");
        response.setData(result);
        return response;
    }

    public static Api2ResponseBody error(String message) {
        Api2ResponseBody response = new Api2ResponseBody();
        response.setCode(500);
        response.setMessage(message);
        response.setData(null);
        return response;
    }
    public static Api2ResponseBody error(Integer code, String message) {
        Api2ResponseBody response = new Api2ResponseBody();
        response.setCode(code);
        response.setMessage(message);
        response.setData(null);
        return response;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "Api2ResponseBody{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
