package com.jeesite.modules.sys.entity;

import java.util.Date;

/**
 * 资产
 * 2.1.6字段描述
 * 序号	字段名称	中文名称	类型	强制/可选	是否必填	库表要素编号	备注
 * 1 	ASSET_ID	资产主键	String(38)	M	是	BE00001	主键
 * 2 	MOF_DIV_CODE	财政区划代码	NString(9)	M	是	BE00017	　
 * 3 	MOF_DIV_NAME	财政区划名称	GBString(360)	M	是	BE00016	　
 * 4 	ACC_DEP	累计折旧/摊销	Currency	M	是	BE03187	资产累计折旧/摊销额，累计折旧/摊销为各月计提折旧/摊销值之和
 * 5 	ACC_DEP_MONTH	已提折旧/摊销月数	Integer(8)	M	否	BE03110	当折旧状态为提折旧和已完成折旧时必填
 * 6 	ACC_DEP_YEAR	折旧/摊销年限	Integer(8)	M	否	BE03088	当折旧状态为提折旧和已完成折旧时必填
 * 7 	ACCOUNTING_STATUS	财务入账状态	NString(1)	M	是	BE03086	按照资产入账登记账簿选择填列
 * 8 	ACCOUNTING_STATUS_NAME	财务入账状态名称	GBString(30)	M	是	BE03085	　
 * 9 	ACQU_DATE	取得日期	Date	M	否	BE03033	单位取得资产的日期或政府储备物资的储备日期
 * 10 	AGENCY_CODE	单位代码	NString(21)	M	是	BE01001	财政部设置统一的单位代码规则
 * 11 	AGENCY_NAME	单位名称	GBString(300)	M	是	BE01002	　
 * 12 	ASSET_CODE	资产编号	NString(60)	M	是	BE03004	资产唯一编码，由系统自动生成
 * 13 	ASSET_NAME	资产名称	GBString(1000)	M	是	BE03001	资产完整名称或公共基础设施完整名称
 * 14 	ASSET_USE_CODE	资产用途代码	NString(6)	M	否	BE03092	地类用途：按照土地利用现状分类GB/T21010-2007进行填列
 * 15 	ASSET_USE_NAME	资产用途名称	GBString(40)	M	否	BE03091	　
 * 16 	FI_ACCT_CLS_CODE	单位会计科目代码	NString(50)	M	是	BE14034	按照资产入账财务会计一级科目选择填列
 * 17 	FI_ACCT_CLS_NAME	单位会计科目名称	GBString(100)	M	是	BE14035	　
 * 18 	FIXED_ASS_ACQ_CODE	取得方式代码	NString(2)	M	否	BE03054	单位取得资产的方式
 * 19 	FIXED_ASS_ACQ_NAME	取得方式名称	GBString(20)	M	否	BE03053	　
 * 20 	FIXED_ASSET_STATE_CODE	资产状态代码	NString(2)	M	否	BE03006	固定资产资产状态，非固定资产可为空
 * 21 	FIXED_ASSET_STATE_NAME	资产状态名称	GBString(60)	M	否	BE03005	　
 * 22 	FIXED_ASSET_TYPE_CODE	资产分类代码	NString(36)	M	否	BE03003	固定资产依据《固定资产等资产基础分类与代码》（GB/T14885-2022）标准编码的选项，按固定资产分类代码进行填列；无形资产依据《固定资产等资产基础分类与代码》（GB/T14885-2022）标准中附录A进行填列
 * 23 	FIXED_ASSET_TYPE_NAME	资产分类名称	GBString(60)	M	否	BE03002	　
 * 24 	INIT_ASSET_VAL	资产原值	Currency	M	是	BE03016	资产在本单位登记入账的价值
 * 25 	IS_SHARING	是否共享共用	Integer(1)	M	是	BE03084	资产是否针对其他单位进行共享，单位内部跨部门使用不属于共享共用
 * 26 	LOCATION_NAME	存放地点	GBString(300)	M	否	BE03128	资产存放地点，基础数据可手动维护
 * 27 	MANAGE_DEPT_NAME	管理部门	GBString(300)	M	否	BE03126	资产管理部门，基础数据可手动维护
 * 28 	MANAGER_NAME	管理人	GBString(60)	M	是	BE03127	资产管理责任人，基础数据可手动维护
 * 29 	MOF_APP	财政拨款	Currency	M	是	BE03103	经费来源为财政拨款的金额
 * 30 	MONTH_ACC_DEP	月折旧/摊销额	Currency	M	否	BE03108	当折旧状态为提折旧和已完成折旧时自动计算
 * 31 	NET_VAL	净值	Currency	M	是	BE03049	资产净值，由资产原值-累计折旧/摊销自动计算
 * 32 	NON_MOF_APP	非财政拨款	Currency	M	是	BE03104	经费来源为非财政拨款的金额
 * 33 	NUM_OR_AREA	数量/面积	Decimal(15,4)	M	否	BE03014	资产数量，应与数量计量单位搭配合理填列
 * 34 	NUM_UNIT	数量计量单位	GBString(60)	M	否	BE03015	部分资产分类的数量计量单位可由系统自动生成，若系统不能自动生成，需要手工填写，与资产数量搭配合理填列
 * 35 	POSTER_DATE	记账日期	Date	M	否	BE14018	当财务入账状态为已入财务账时必填
 * 36 	STA_USE_DATE	投入使用日期	Date	M	否	BE03031	资产首次投入使用的日期
 * 37 	VALUE_TYPE_CODE	价值类型代码	NString(4)	M	是	BE03048	资产的计量属性，参照《政府会计准则——基本准则》中计量属性选择填列
 * 38 	VALUE_TYPE_NAME	价值类型名称	GBString(20)	M	是	BE03047	　
 * 39 	VOUCHER_NO	记账凭证号	GBString(100)	M	否	BE14007	当财务入账状态为已入财务账时必填
 * 40 	CREATE_TIME	创建时间	DateTime	M	是	BE00036	　
 * 41 	UPDATE_TIME	更新时间	DateTime	M	是	BE00023	　
 * 42 	START_DATE	启用日期	Date	M	是	BE00020	　
 * 43 	END_DATE	停用日期	Date	M	是	BE00021	　
 * 44 	IS_LAST_INST	是否终审	Integer(1)	M	是	BE00037	　
 * 45 	BIZ_KEY	业务唯一标识	String(38)	O	否	BE00001	　
 * 46 	ACT_USE_AREA	其中：本单位实际使用面积	Decimal(15,4)	M	否	BE03079	房屋资产信息卡，使用状态为在用的资产，其中为本单位实际使用的面积
 * 47 	ASSET_AUTH_SITU_CODE	资产编制情况代码	NString(3)	M	否	BE03061	车辆编制情况
 * 48 	ASSET_AUTH_SITU_NAME	资产编制情况名称	GBString(30)	M	否	BE03060	　
 * 49 	BISNISS_HOUSE_ACT_USE_AREA	业务用房其中：本单位实际使用面积	Decimal(15,4)	M	否	BE03050	　
 * 50 	BISNISS_HOUSE_AREA	业务用房面积	Decimal(15,4)	M	否	BE03023	小计业务用房
 * 51 	BISNISS_HOUSE_BORROW_AREA	业务用房出借面积	Decimal(15,4)	M	否	BE03077	　
 * 52 	BISNISS_HOUSE_IDLE_AREA	业务用房闲置面积	Decimal(15,4)	M	否	BE03087	　
 * 53 	BISNISS_HOUSE_LEASED_AREA	业务用房出租面积	Decimal(15,4)	M	否	BE03051	　
 * 54 	BISNISS_HOUSE_OTHER_AREA	业务用房待处置(待报废、毁损等)面积	Decimal(15,4)	M	否	BE03070	　
 * 55 	BISNISS_HOUSE_SELF_AREA	业务用房在用面积	Decimal(15,4)	M	否	BE03224	　
 * 56 	BLD_STRUCTURE	建筑结构	NString(1)	M	否	BE03160	建筑物建筑结构，与折旧年限相关
 * 57 	BLD_STRUCTURE_NAME	建筑结构名称	GBString(10)	M	否	BE03159	建筑物建筑结构，与折旧年限相关
 * 58 	BORROW_AREA	出借面积	Decimal(15,4)	M	否	BE03150	　
 * 59 	BRAND	品牌	GBString(200)	M	否	BE03170	按照车辆行驶证中的品牌型号进行填列
 * 60 	FLOOR_AREA	占地面积	Decimal(15,4)	M	否	BE03019	不动产证：宗地面积
 * 61 	IDLE_AREA	闲置面积	Decimal(15,4)	M	否	BE03151	　
 * 62 	LEASED_AREA	出租面积	Decimal(15,4)	M	否	BE03149	　
 * 63 	LOC	坐落位置	GBString(300)	M	否	BE03025	房屋/土地所在地
 * 64 	MANUFACTURER	生产厂家	GBString(100)	M	否	BE03177	　
 * 65 	OFFICE_ACT_USE_AREA	其中：办公室用房其中：本单位实际使用面积	Decimal(15,4)	M	否	BE03219	　
 * 66 	OFFICE_AREA	小计其中：办公室用房	Decimal(15,4)	M	否	BE03211	　
 * 67 	OFFICE_BORROW_AREA	其中：办公室用房出借面积	Decimal(15,4)	M	否	BE03221	　
 * 68 	OFFICE_HOUSE_ACT_USE_AREA	办公用房其中：本单位实际使用面积	Decimal(15,4)	M	否	BE03213	　
 * 69 	OFFICE_HOUSE_AREA	办公用房面积	Decimal(15,4)	M	否	BE03022	小计办公用房
 * 70 	OFFICE_HOUSE_BORROW_AREA	办公用房出借面积	Decimal(15,4)	M	否	BE03215	　
 * 71 	OFFICE_HOUSE_IDLE_AREA	办公用房闲置面积	Decimal(15,4)	M	否	BE03216	　
 * 72 	OFFICE_HOUSE_LEASED_AREA	办公用房出租面积	Decimal(15,4)	M	否	BE03214	　
 * 73 	OFFICE_HOUSE_OTHER_AREA	办公用房待处置(待报废、毁损等)面积	Decimal(15,4)	M	否	BE03217	　
 * 74 	OFFICE_HOUSE_SELF_AREA	办公用房在用面积	Decimal(15,4)	M	否	BE03212	　
 * 75 	OFFICE_IDLE_AREA	其中：办公室用房闲置面积	Decimal(15,4)	M	否	BE03222	　
 * 76 	OFFICE_LEASED_AREA	其中：办公室用房出租面积	Decimal(15,4)	M	否	BE03220	　
 * 77 	OFFICE_OTHER_AREA	其中：办公室用房待处置(待报废、毁损等)面积	Decimal(15,4)	M	否	BE03223	　
 * 78 	OFFICE_SELF_AREA	其中：办公室用房在用面积	Decimal(15,4)	M	否	BE03218	　
 * 79 	OTHER_AREA	待处置(待报废、毁损等)面积	Decimal(15,4)	M	否	BE03152	　
 * 80 	OTHER_HOUSE_ACT_USE_AREA	其他用房其中：本单位实际使用面积	Decimal(15,4)	M	否	BE03037	　
 * 81 	OTHER_HOUSE_AREA	其他用房面积	Decimal(15,4)	M	否	BE03024	小计其他用房
 * 82 	OTHER_HOUSE_BORROW_AREA	其他用房出借面积	Decimal(15,4)	M	否	BE03030	　
 * 83 	OTHER_HOUSE_IDLE_AREA	其他用房闲置面积	Decimal(15,4)	M	否	BE03029	　
 * 84 	OTHER_HOUSE_LEASED_AREA	其他用房出租面积	Decimal(15,4)	M	否	BE03032	　
 * 85 	OTHER_HOUSE_OTHER_AREA	其他用房待处置(待报废、毁损等)面积	Decimal(15,4)	M	否	BE03078	　
 * 86 	OTHER_HOUSE_SELF_AREA	其他用房在用面积	Decimal(15,4)	M	否	BE03040	　
 * 87 	SELF_USE_AREA	在用面积	Decimal(15,4)	M	否	BE03148	　
 * 88 	SPEC_MOD	规格型号	GBString(300)	M	否	BE03171	按照车辆行驶证中的品牌型号进行填列
 * 89 	VEHICLE_DISP	汽车排气量代码	NString(2)	M	否	BE03175	车辆动力情况及排气量
 * 90 	VEHICLE_DISP_NAME	汽车排气量名称	GBString(20)	M	否	BE03174	　
 * 91 	VEHICLE_LICENCE_NO	车牌号	GBString(100)	M	否	BE03046	按照车辆行驶证中的号牌号码进行填列
 * 92 	VEHICLE_MADE_IN	车辆产地	GBString(100)	M	否	BE03173	　
 * 93 	VEHICLE_TYPE	车辆类型	GBString(200)	M	否	BE03083	按照车辆行驶证中的车辆类型进行填列
 * 	SELF_USE_VAL	在用价值	Currency	M	否
 * 	LEASED_VAL	出租价值	Currency	M	否
 * 	BORROW_VAL	出借价值	Currency	M	否
 * 	IDLE_VAL	闲置价值	Currency	M	否
 * 	OTHER_VAL	待处置（待报废、毁损等）价值	Currency	M	否
 * 	FINAL_ACC_DATE	竣工决算日期	DATE	M	否
 * 	DANGEROUS_BLD_AREA	危房面积	Decimal(15,4)	M	否
 * 	HEAT_AREA	取暖面积	Decimal(15,4)	M	否
 * 	PUR_CONTRACT_NO	采购合同编号	GBString(100)	M	否
 * 	INVOICE_NO	发票号	GBString(900)	M	否
 * 	VEHICLE_DRIVING_LICENSE	车辆行驶证	GBString(100)	M	否
 * 	VEHICLE_OWNER	车辆所有人	GBString(300)	M	否
 * 	MOTOR_NUM	发动机号	GBString(100)	M	否
 * 	VEHICLE_IDENTI_CODE	车辆识别代码	GBString(100)	M	否
 * 	WARRANTY_EXPIRATION_DATE	保修截止日期	DATE	M	否
 * 	REGISTER_DATE	注册日期	DATE	M	否
 * 	PRODUCT_ID	产品序列号	GBString(200)	M	否
 * 	OWNERSHIP_NATURE_CODE	权属性质代码	GBString(1)	M	否
 * 	OWNERSHIP_FORM_CODE	产权形式代码	GBString(2)	M	否
 * 	OWNERSHIP_CERT	权属证明	GBString(180)	M	否
 * 	CERTIFI_DATE	发证日期	DATE	M	否
 * 	OWNERSHIP_NO	权属证号	GBString(100)	M	否
 * 	OWNERSHIP_AREA	权属面积	Decimal(15,4)	M	否
 * 	RIGHT_TYPE	使用权类型	GBString(1)	M	否
 * 	USE_AREA	独用面积	Decimal(15,4)	M	否
 * 	PROPORTION_AREA	分摊面积	Decimal(15,4)	M	否
 * 	OWNERSHIP_YEAR	权属年限	NUMBER	M	否
 * 	GROUND_RANK_CODE	土地级次编码	GBString(1)	M	否
 * 	BUILD_NATURE_CODE	房产性质编码	GBString(1)	M	否
 * 	MAC_ADDRESS	MAC地址	GBString(50)	M	否
 * 	IP_ADDRESS	IP地址	GBString(50)	M	否
 * <AUTHOR>
 * @version 2025-01-20
 * */
public class ApiSzgzAsset {

    private String assetId; // 资产主键
    private String mofDivCode; // 财政区划代码
    private String mofDivName; // 财政区划名称
    private double accDep; // 累计折旧/摊销
    private int accDepMonth; // 已提折旧/摊销月数
    private int accDepYear; // 折旧/摊销年限
    private String accountingStatus; // 财务入账状态
    private String accountingStatusName; // 财务入账状态名称
    private Date acqDate; // 取得日期
    private String agencyCode; // 单位代码
    private String agencyName; // 单位名称
    private String assetCode; // 资产编号
    private String assetName; // 资产名称
    private String assetUseCode; // 资产用途代码
    private String assetUseName; // 资产用途名称
    private String fiAcctClsCode; // 单位会计科目代码
    private String fiAcctClsName; // 单位会计科目名称
    private String fixAssAcqCode; // 取得方式代码
    private String fixAssAcqName; // 取得方式名称
    private String fixedAssetStateCode; // 资产状态代码
    private String fixedAssetStateName; // 资产状态名称
    private String fixedAssetTypeCode; // 资产分类代码
    private String fixedAssetTypeName; // 资产分类名称
    private double initAssetVal; // 资产原值
    private int isSharing; // 是否共享共用
    private String locationName; // 存放地点
    private String manageDeptName; // 管理部门
    private String managerName; // 管理人
    private double moFApp; // 财政拨款
    private double monthAccDep; // 月折旧/摊销额
    private double netVal; // 净值
    private double nonMofApp; // 非财政拨款
    private double numOrArea; // 数量/面积
    private String numUnit; // 数量计量单位
    private Date postDate; // 记账日期
    private Date staUseDate; // 投入使用日期
    private String valueTypeCode; // 价值类型代码
    private String valueTypeName; // 价值类型名称
    private String voucherNo; // 记账凭证号
    private Date createTime; // 创建时间
    private Date updateTime; // 更新时间
    private Date startDate; // 启用日期
    private Date endDate; // 停用日期
    private int isLastInst; // 是否终审
    private String bizKey; // 业务唯一标识
    private double actUseArea; // 其中：本单位实际使用面积
    private String assetAuthSituCode; // 资产编制情况代码
    private String assetAuthSituName; // 资产编制情况名称
    private double busnissHouseActUseArea; // 业务用房其中：本单位实际使用面积
    private double busnissHouseArea; // 业务用房面积
    private double busnissHouseBorrowArea; // 业务用房出借面积
    private double busnissHouseIdleArea; // 业务用房闲置面积
    private double busnissHouseLeasedArea; // 业务用房出租面积
    private double busnissHouseOtherArea; // 业务用房待处置(待报废、毁损等)面积
    private double busnissHouseSelfArea; // 业务用房在用面积
    private String bldStructure; // 建筑结构
    private String bldStructureName; // 建筑结构名称
    private String bldType; // 建筑类型
    private String bldTypeName; // 建筑类型名称
    private double borrowArea; // 出借面积
    private String brand; // 品牌
    private double floorArea; // 占地面积
    private double idleArea; // 闲置面积
    private double leasedArea; // 出租面积
    private String loc; // 坐落位置
    private String manufacturer; // 生产厂家
    private double officeActUseArea; // 其中：办公室用房其中：本单位实际使用面积
    private double officeArea; // 小计其中：办公室用房
    private double officeBorrowArea; // 其中：办公室用房出借面积
    private double officeIdleArea; // 其中：办公室用房闲置面积
    private double officeLeasedArea; // 其中：办公室用房出租面积
    private double officeOtherArea; // 其中：办公室用房待处置(待报废、毁损等)面积
    private double officeSelfArea; // 其中：办公室用房在用面积
    private String otherArea; // 待处置(待报废、毁损等)面积
    private double otherHouseActUseArea; // 其他用房其中：本单位实际使用面积
    private double otherHouseArea; // 其他用房面积
    private double otherHouseBorrowArea; // 其他用房出借面积
    private double otherHouseIdleArea; // 其他用房闲置面积
    private double otherHouseLeasedArea; // 其他用房出租面积
    private double otherHouseOtherArea; // 其他用房待处置(待报废、毁损等)面积
    private double otherHouseSelfArea; // 其他用房在用面积
    private double selfUseArea; // 在用面积
    private String specMod; // 规格型号
    private String vehicleDisp; // 汽车排气量代码
    private String vehicleDispName; // 汽车排气量名称
    private String vehicleLicenceNo; // 车牌号
    private String vehicleMadeIn; // 车辆产地
    private String vehicleType; // 车辆类型
    private double selfUseVal; // 在用价值
    private double leasedVal; // 出租价值
    private double borrowVal; // 出借价值
    private double idleVal; // 闲置价值
    private double otherVal; // 待处置（待报废、毁损等）价值
    private Date finalAccDate; // 竣工决算日期
    private double dangerousBldArea; // 危房面积
    private double heatArea; // 取暖面积
    private String purContractNo; // 采购合同编号
    private String invoiceNo; // 发票号
    private String vehicleDrivingLicense; // 车辆行驶证
    private String vehicleOwner; // 车辆所有人
    private String motorNum; // 发动机号
    private String vehicleIdentiCode; // 车辆识别代码
    private Date warrantyExpirationDate; // 保修截止日期
    private Date registerDate; // 注册日期
    private String productId; // 产品序列号
    private String ownershipNatureCode; // 权属性质代码
    private String ownershipFormCode; // 产权形式代码
    private String ownershipCert; // 权属证明
    private Date certifiDate; // 发证日期
    private String ownershipNo; // 权属证号
    private double ownershipArea; // 权属面积
    private String rightType; // 使用权类型
    private double useArea; // 独用面积
    private double proportionArea; // 分摊面积
    private int ownershipYear; // 权属年限
    private String groundRankCode; // 土地级次编码
    private String buildNatureCode; // 房产性质编码
    private String macAddress; // MAC地址
    private String ipAddress; // IP地址

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getMofDivCode() {
        return mofDivCode;
    }

    public void setMofDivCode(String mofDivCode) {
        this.mofDivCode = mofDivCode;
    }

    public String getMofDivName() {
        return mofDivName;
    }

    public void setMofDivName(String mofDivName) {
        this.mofDivName = mofDivName;
    }

    public double getAccDep() {
        return accDep;
    }

    public void setAccDep(double accDep) {
        this.accDep = accDep;
    }

    public int getAccDepMonth() {
        return accDepMonth;
    }

    public void setAccDepMonth(int accDepMonth) {
        this.accDepMonth = accDepMonth;
    }

    public int getAccDepYear() {
        return accDepYear;
    }

    public void setAccDepYear(int accDepYear) {
        this.accDepYear = accDepYear;
    }

    public String getAccountingStatus() {
        return accountingStatus;
    }

    public void setAccountingStatus(String accountingStatus) {
        this.accountingStatus = accountingStatus;
    }

    public String getAccountingStatusName() {
        return accountingStatusName;
    }

    public void setAccountingStatusName(String accountingStatusName) {
        this.accountingStatusName = accountingStatusName;
    }

    public Date getAcqDate() {
        return acqDate;
    }

    public void setAcqDate(Date acqDate) {
        this.acqDate = acqDate;
    }

    public String getAgencyCode() {
        return agencyCode;
    }

    public void setAgencyCode(String agencyCode) {
        this.agencyCode = agencyCode;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public String getAssetCode() {
        return assetCode;
    }

    public void setAssetCode(String assetCode) {
        this.assetCode = assetCode;
    }

    public String getAssetName() {
        return assetName;
    }

    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }

    public String getAssetUseCode() {
        return assetUseCode;
    }

    public void setAssetUseCode(String assetUseCode) {
        this.assetUseCode = assetUseCode;
    }

    public String getAssetUseName() {
        return assetUseName;
    }

    public void setAssetUseName(String assetUseName) {
        this.assetUseName = assetUseName;
    }

    public String getFiAcctClsCode() {
        return fiAcctClsCode;
    }

    public void setFiAcctClsCode(String fiAcctClsCode) {
        this.fiAcctClsCode = fiAcctClsCode;
    }

    public String getFiAcctClsName() {
        return fiAcctClsName;
    }

    public void setFiAcctClsName(String fiAcctClsName) {
        this.fiAcctClsName = fiAcctClsName;
    }

    public String getFixAssAcqCode() {
        return fixAssAcqCode;
    }

    public void setFixAssAcqCode(String fixAssAcqCode) {
        this.fixAssAcqCode = fixAssAcqCode;
    }

    public String getFixAssAcqName() {
        return fixAssAcqName;
    }

    public void setFixAssAcqName(String fixAssAcqName) {
        this.fixAssAcqName = fixAssAcqName;
    }

    public String getFixedAssetStateCode() {
        return fixedAssetStateCode;
    }

    public void setFixedAssetStateCode(String fixedAssetStateCode) {
        this.fixedAssetStateCode = fixedAssetStateCode;
    }

    public String getFixedAssetStateName() {
        return fixedAssetStateName;
    }

    public void setFixedAssetStateName(String fixedAssetStateName) {
        this.fixedAssetStateName = fixedAssetStateName;
    }

    public String getFixedAssetTypeCode() {
        return fixedAssetTypeCode;
    }

    public void setFixedAssetTypeCode(String fixedAssetTypeCode) {
        this.fixedAssetTypeCode = fixedAssetTypeCode;
    }

    public String getFixedAssetTypeName() {
        return fixedAssetTypeName;
    }

    public void setFixedAssetTypeName(String fixedAssetTypeName) {
        this.fixedAssetTypeName = fixedAssetTypeName;
    }

    public double getInitAssetVal() {
        return initAssetVal;
    }

    public void setInitAssetVal(double initAssetVal) {
        this.initAssetVal = initAssetVal;
    }

    public int getIsSharing() {
        return isSharing;
    }

    public void setIsSharing(int isSharing) {
        this.isSharing = isSharing;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getManageDeptName() {
        return manageDeptName;
    }

    public void setManageDeptName(String manageDeptName) {
        this.manageDeptName = manageDeptName;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public double getMoFApp() {
        return moFApp;
    }

    public void setMoFApp(double moFApp) {
        this.moFApp = moFApp;
    }

    public double getMonthAccDep() {
        return monthAccDep;
    }

    public void setMonthAccDep(double monthAccDep) {
        this.monthAccDep = monthAccDep;
    }

    public double getNetVal() {
        return netVal;
    }

    public void setNetVal(double netVal) {
        this.netVal = netVal;
    }

    public double getNonMofApp() {
        return nonMofApp;
    }

    public void setNonMofApp(double nonMofApp) {
        this.nonMofApp = nonMofApp;
    }

    public double getNumOrArea() {
        return numOrArea;
    }

    public void setNumOrArea(double numOrArea) {
        this.numOrArea = numOrArea;
    }

    public String getNumUnit() {
        return numUnit;
    }

    public void setNumUnit(String numUnit) {
        this.numUnit = numUnit;
    }

    public Date getPostDate() {
        return postDate;
    }

    public void setPostDate(Date postDate) {
        this.postDate = postDate;
    }

    public Date getStaUseDate() {
        return staUseDate;
    }

    public void setStaUseDate(Date staUseDate) {
        this.staUseDate = staUseDate;
    }

    public String getValueTypeCode() {
        return valueTypeCode;
    }

    public void setValueTypeCode(String valueTypeCode) {
        this.valueTypeCode = valueTypeCode;
    }

    public String getValueTypeName() {
        return valueTypeName;
    }

    public void setValueTypeName(String valueTypeName) {
        this.valueTypeName = valueTypeName;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getIsLastInst() {
        return isLastInst;
    }

    public void setIsLastInst(int isLastInst) {
        this.isLastInst = isLastInst;
    }

    public String getBizKey() {
        return bizKey;
    }

    public void setBizKey(String bizKey) {
        this.bizKey = bizKey;
    }

    public double getActUseArea() {
        return actUseArea;
    }

    public void setActUseArea(double actUseArea) {
        this.actUseArea = actUseArea;
    }

    public String getAssetAuthSituCode() {
        return assetAuthSituCode;
    }

    public void setAssetAuthSituCode(String assetAuthSituCode) {
        this.assetAuthSituCode = assetAuthSituCode;
    }

    public String getAssetAuthSituName() {
        return assetAuthSituName;
    }

    public void setAssetAuthSituName(String assetAuthSituName) {
        this.assetAuthSituName = assetAuthSituName;
    }

    public double getBusnissHouseActUseArea() {
        return busnissHouseActUseArea;
    }

    public void setBusnissHouseActUseArea(double busnissHouseActUseArea) {
        this.busnissHouseActUseArea = busnissHouseActUseArea;
    }

    public double getBusnissHouseArea() {
        return busnissHouseArea;
    }

    public void setBusnissHouseArea(double busnissHouseArea) {
        this.busnissHouseArea = busnissHouseArea;
    }

    public double getBusnissHouseBorrowArea() {
        return busnissHouseBorrowArea;
    }

    public void setBusnissHouseBorrowArea(double busnissHouseBorrowArea) {
        this.busnissHouseBorrowArea = busnissHouseBorrowArea;
    }

    public double getBusnissHouseIdleArea() {
        return busnissHouseIdleArea;
    }

    public void setBusnissHouseIdleArea(double busnissHouseIdleArea) {
        this.busnissHouseIdleArea = busnissHouseIdleArea;
    }

    public double getBusnissHouseLeasedArea() {
        return busnissHouseLeasedArea;
    }

    public void setBusnissHouseLeasedArea(double busnissHouseLeasedArea) {
        this.busnissHouseLeasedArea = busnissHouseLeasedArea;
    }

    public double getBusnissHouseOtherArea() {
        return busnissHouseOtherArea;
    }

    public void setBusnissHouseOtherArea(double busnissHouseOtherArea) {
        this.busnissHouseOtherArea = busnissHouseOtherArea;
    }

    public double getBusnissHouseSelfArea() {
        return busnissHouseSelfArea;
    }

    public void setBusnissHouseSelfArea(double busnissHouseSelfArea) {
        this.busnissHouseSelfArea = busnissHouseSelfArea;
    }

    public String getBldStructure() {
        return bldStructure;
    }

    public void setBldStructure(String bldStructure) {
        this.bldStructure = bldStructure;
    }

    public String getBldStructureName() {
        return bldStructureName;
    }

    public void setBldStructureName(String bldStructureName) {
        this.bldStructureName = bldStructureName;
    }

    public String getBldType() {
        return bldType;
    }

    public void setBldType(String bldType) {
        this.bldType = bldType;
    }

    public String getBldTypeName() {
        return bldTypeName;
    }

    public void setBldTypeName(String bldTypeName) {
        this.bldTypeName = bldTypeName;
    }

    public double getBorrowArea() {
        return borrowArea;
    }

    public void setBorrowArea(double borrowArea) {
        this.borrowArea = borrowArea;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public double getFloorArea() {
        return floorArea;
    }

    public void setFloorArea(double floorArea) {
        this.floorArea = floorArea;
    }

    public double getIdleArea() {
        return idleArea;
    }

    public void setIdleArea(double idleArea) {
        this.idleArea = idleArea;
    }

    public double getLeasedArea() {
        return leasedArea;
    }

    public void setLeasedArea(double leasedArea) {
        this.leasedArea = leasedArea;
    }

    public String getLoc() {
        return loc;
    }

    public void setLoc(String loc) {
        this.loc = loc;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public double getOfficeActUseArea() {
        return officeActUseArea;
    }

    public void setOfficeActUseArea(double officeActUseArea) {
        this.officeActUseArea = officeActUseArea;
    }

    public double getOfficeArea() {
        return officeArea;
    }

    public void setOfficeArea(double officeArea) {
        this.officeArea = officeArea;
    }

    public double getOfficeBorrowArea() {
        return officeBorrowArea;
    }

    public void setOfficeBorrowArea(double officeBorrowArea) {
        this.officeBorrowArea = officeBorrowArea;
    }

    public double getOfficeIdleArea() {
        return officeIdleArea;
    }

    public void setOfficeIdleArea(double officeIdleArea) {
        this.officeIdleArea = officeIdleArea;
    }

    public double getOfficeLeasedArea() {
        return officeLeasedArea;
    }

    public void setOfficeLeasedArea(double officeLeasedArea) {
        this.officeLeasedArea = officeLeasedArea;
    }

    public double getOfficeOtherArea() {
        return officeOtherArea;
    }

    public void setOfficeOtherArea(double officeOtherArea) {
        this.officeOtherArea = officeOtherArea;
    }

    public double getOfficeSelfArea() {
        return officeSelfArea;
    }

    public void setOfficeSelfArea(double officeSelfArea) {
        this.officeSelfArea = officeSelfArea;
    }

    public String getOtherArea() {
        return otherArea;
    }

    public void setOtherArea(String otherArea) {
        this.otherArea = otherArea;
    }

    public double getOtherHouseActUseArea() {
        return otherHouseActUseArea;
    }

    public void setOtherHouseActUseArea(double otherHouseActUseArea) {
        this.otherHouseActUseArea = otherHouseActUseArea;
    }

    public double getOtherHouseArea() {
        return otherHouseArea;
    }

    public void setOtherHouseArea(double otherHouseArea) {
        this.otherHouseArea = otherHouseArea;
    }

    public double getOtherHouseBorrowArea() {
        return otherHouseBorrowArea;
    }

    public void setOtherHouseBorrowArea(double otherHouseBorrowArea) {
        this.otherHouseBorrowArea = otherHouseBorrowArea;
    }

    public double getOtherHouseIdleArea() {
        return otherHouseIdleArea;
    }

    public void setOtherHouseIdleArea(double otherHouseIdleArea) {
        this.otherHouseIdleArea = otherHouseIdleArea;
    }

    public double getOtherHouseLeasedArea() {
        return otherHouseLeasedArea;
    }

    public void setOtherHouseLeasedArea(double otherHouseLeasedArea) {
        this.otherHouseLeasedArea = otherHouseLeasedArea;
    }

    public double getOtherHouseOtherArea() {
        return otherHouseOtherArea;
    }

    public void setOtherHouseOtherArea(double otherHouseOtherArea) {
        this.otherHouseOtherArea = otherHouseOtherArea;
    }

    public double getOtherHouseSelfArea() {
        return otherHouseSelfArea;
    }

    public void setOtherHouseSelfArea(double otherHouseSelfArea) {
        this.otherHouseSelfArea = otherHouseSelfArea;
    }

    public double getSelfUseArea() {
        return selfUseArea;
    }

    public void setSelfUseArea(double selfUseArea) {
        this.selfUseArea = selfUseArea;
    }

    public String getSpecMod() {
        return specMod;
    }

    public void setSpecMod(String specMod) {
        this.specMod = specMod;
    }

    public String getVehicleDisp() {
        return vehicleDisp;
    }

    public void setVehicleDisp(String vehicleDisp) {
        this.vehicleDisp = vehicleDisp;
    }

    public String getVehicleDispName() {
        return vehicleDispName;
    }

    public void setVehicleDispName(String vehicleDispName) {
        this.vehicleDispName = vehicleDispName;
    }

    public String getVehicleLicenceNo() {
        return vehicleLicenceNo;
    }

    public void setVehicleLicenceNo(String vehicleLicenceNo) {
        this.vehicleLicenceNo = vehicleLicenceNo;
    }

    public String getVehicleMadeIn() {
        return vehicleMadeIn;
    }

    public void setVehicleMadeIn(String vehicleMadeIn) {
        this.vehicleMadeIn = vehicleMadeIn;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public double getSelfUseVal() {
        return selfUseVal;
    }

    public void setSelfUseVal(double selfUseVal) {
        this.selfUseVal = selfUseVal;
    }

    public double getLeasedVal() {
        return leasedVal;
    }

    public void setLeasedVal(double leasedVal) {
        this.leasedVal = leasedVal;
    }

    public double getBorrowVal() {
        return borrowVal;
    }

    public void setBorrowVal(double borrowVal) {
        this.borrowVal = borrowVal;
    }

    public double getIdleVal() {
        return idleVal;
    }

    public void setIdleVal(double idleVal) {
        this.idleVal = idleVal;
    }

    public double getOtherVal() {
        return otherVal;
    }

    public void setOtherVal(double otherVal) {
        this.otherVal = otherVal;
    }

    public Date getFinalAccDate() {
        return finalAccDate;
    }

    public void setFinalAccDate(Date finalAccDate) {
        this.finalAccDate = finalAccDate;
    }

    public double getDangerousBldArea() {
        return dangerousBldArea;
    }

    public void setDangerousBldArea(double dangerousBldArea) {
        this.dangerousBldArea = dangerousBldArea;
    }

    public double getHeatArea() {
        return heatArea;
    }

    public void setHeatArea(double heatArea) {
        this.heatArea = heatArea;
    }

    public String getPurContractNo() {
        return purContractNo;
    }

    public void setPurContractNo(String purContractNo) {
        this.purContractNo = purContractNo;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getVehicleDrivingLicense() {
        return vehicleDrivingLicense;
    }

    public void setVehicleDrivingLicense(String vehicleDrivingLicense) {
        this.vehicleDrivingLicense = vehicleDrivingLicense;
    }

    public String getVehicleOwner() {
        return vehicleOwner;
    }

    public void setVehicleOwner(String vehicleOwner) {
        this.vehicleOwner = vehicleOwner;
    }

    public String getMotorNum() {
        return motorNum;
    }

    public void setMotorNum(String motorNum) {
        this.motorNum = motorNum;
    }

    public String getVehicleIdentiCode() {
        return vehicleIdentiCode;
    }

    public void setVehicleIdentiCode(String vehicleIdentiCode) {
        this.vehicleIdentiCode = vehicleIdentiCode;
    }

    public Date getWarrantyExpirationDate() {
        return warrantyExpirationDate;
    }

    public void setWarrantyExpirationDate(Date warrantyExpirationDate) {
        this.warrantyExpirationDate = warrantyExpirationDate;
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getOwnershipNatureCode() {
        return ownershipNatureCode;
    }

    public void setOwnershipNatureCode(String ownershipNatureCode) {
        this.ownershipNatureCode = ownershipNatureCode;
    }

    public String getOwnershipFormCode() {
        return ownershipFormCode;
    }

    public void setOwnershipFormCode(String ownershipFormCode) {
        this.ownershipFormCode = ownershipFormCode;
    }

    public String getOwnershipCert() {
        return ownershipCert;
    }

    public void setOwnershipCert(String ownershipCert) {
        this.ownershipCert = ownershipCert;
    }

    public Date getCertifiDate() {
        return certifiDate;
    }

    public void setCertifiDate(Date certifiDate) {
        this.certifiDate = certifiDate;
    }

    public String getOwnershipNo() {
        return ownershipNo;
    }

    public void setOwnershipNo(String ownershipNo) {
        this.ownershipNo = ownershipNo;
    }

    public double getOwnershipArea() {
        return ownershipArea;
    }

    public void setOwnershipArea(double ownershipArea) {
        this.ownershipArea = ownershipArea;
    }

    public String getRightType() {
        return rightType;
    }

    public void setRightType(String rightType) {
        this.rightType = rightType;
    }

    public double getUseArea() {
        return useArea;
    }

    public void setUseArea(double useArea) {
        this.useArea = useArea;
    }

    public double getProportionArea() {
        return proportionArea;
    }

    public void setProportionArea(double proportionArea) {
        this.proportionArea = proportionArea;
    }

    public int getOwnershipYear() {
        return ownershipYear;
    }

    public void setOwnershipYear(int ownershipYear) {
        this.ownershipYear = ownershipYear;
    }

    public String getGroundRankCode() {
        return groundRankCode;
    }

    public void setGroundRankCode(String groundRankCode) {
        this.groundRankCode = groundRankCode;
    }

    public String getBuildNatureCode() {
        return buildNatureCode;
    }

    public void setBuildNatureCode(String buildNatureCode) {
        this.buildNatureCode = buildNatureCode;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
