package com.jeesite.modules.config.swagger;

import com.jeesite.modules.swagger.config.SwaggerConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.spring.web.plugins.Docket;

@Configuration
@ConditionalOnProperty(name="web.swagger.enabled", havingValue="true", matchIfMissing=false)
public class CustomApiConfig {

    @Bean
    @ConditionalOnProperty(name="web.swagger.custom.enabled", havingValue="true", matchIfMissing=true)
    public Docket customApi() {
        String moduleCode = "hsobs";
        String moduleName = "住房保障";
        String basePackage = "com.hsobs.hs.modules";
        return SwaggerConfig.docket(moduleCode, moduleName, basePackage)
                .select()
                .apis(
                        RequestHandlerSelectors.basePackage(basePackage)
                ).build();
    }

}
