//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.jeesite.modules.bpm.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.modules.bpm.entity.api.BpmEntityApi;

import java.util.Map;

@Table
public class BpmEntity<T extends DataEntity<?>> extends DataEntity<T> implements BpmEntityApi<T> {
    public static final String STATUS_RESTART = "6";
    private static final long serialVersionUID = 1L;
    private BpmParams bpm;

    private String bpmStatus; //流程状态

    private String taskId;

    private String flowStatus;

    private String processName;

    private String applyTitle;

    private String taskDueDate;

    private String taskStartDate;

    private String formKey;

    private String redirectPage; //跳转页面

    private String idq;//用于检索的id

    private String taskAssignee;

    private Map<String, String> changeMap;  // 变更信息Map


    public String getBpmStatus() {
        return bpmStatus;
    }

    public void setBpmStatus(String bpmStatus) {
        this.bpmStatus = bpmStatus;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus;
    }

    public String getApplyTitle() {
        return applyTitle;
    }

    public void setApplyTitle(String applyTitle) {
        this.applyTitle = applyTitle;
    }

    public BpmEntity() {
    }

    public BpmEntity(String id) {
        super(id);
    }

    public BpmParams getBpm() {
        if (this.bpm == null) {
            this.bpm = new BpmParams();
        }

        return this.bpm;
    }

    public void setBpm(BpmParams bpm) {
        this.bpm = bpm;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getTaskDueDate() {
        return taskDueDate;
    }

    public void setTaskDueDate(String taskDueDate) {
        this.taskDueDate = taskDueDate;
    }

    public String getTaskStartDate() {
        return taskStartDate;
    }

    public void setTaskStartDate(String taskStartDate) {
        this.taskStartDate = taskStartDate;
    }

    public String getFormKey() {
        return formKey;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    public String getRedirectPage() {
        return redirectPage;
    }

    public void setRedirectPage(String redirectPage) {
        this.redirectPage = redirectPage;
    }

    public String getIdq() {
        return idq;
    }

    public void setIdq(String idq) {
        this.idq = idq;
    }

    public Map<String, String> getChangeMap() {
        return changeMap;
    }

    public void setChangeMap(Map<String, String> changeMap) {
        this.changeMap = changeMap;
    }

    public String getTaskAssignee() {
        return taskAssignee;
    }

    public void setTaskAssignee(String taskAssignee) {
        this.taskAssignee = taskAssignee;
    }
}
