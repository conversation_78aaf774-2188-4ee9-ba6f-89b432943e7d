/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.config.Global;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.sys.entity.api.*;
import com.jeesite.modules.sys.utils.SM2Utils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数字屏山
 *
 * <AUTHOR>
 * @version 2017-03-25
 */

@Service
public class ApiSzpsService extends BaseService {

    private static String szps_server; //数字空间接口地址
    private static String accountId; //数字空间分配的账号id

    private String sign; //数字空间接口签名

    private String accessToken; //数字空间接口token

    private String expiresIn; //数字空间有效期
    private Instant expiresTime;


    static {

        try{
            szps_server = Global.getConfig("szps.server").toString();
            //szps_server = "http://www.163.com";
            accountId = Global.getConfig("szps.accountId-szps").toString();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取数字空间接口的请求token
     * @return
     */
    private void token() {

        try {
            String mypky = Global.getConfig("szps.sm2.prk-my").toString();
            sign = SM2Utils.SM2Signature(accountId, mypky);
            String url = String.format("%s/token", szps_server);
            Map<String,String> map = new HashMap<>();
            map.put("accountId", accountId);
            map.put("sign", sign);
            ApiResponseBody result = this.requet(url, HttpMethod.GET, JSON.toJSONString(map), true);
            if(result.getHead().getStatus().equals("0") && result.getData() != null) {
                Map resultMap = (Map) result.getData();
                accessToken = resultMap.get("accessToken").toString();
                expiresIn = resultMap.get("expiresIn").toString();
                if(expiresIn != null && expiresIn.length() > 0) {
                    expiresTime = Instant.now().plus(Integer.parseInt(expiresIn)-10, ChronoUnit.SECONDS);
                }
            }else{
                accessToken = null;
                throw new ServiceException("闽政通程序接口token获取失败！" + result.getHead()!=null?result.getHead().getMessage():"");
            }
        }catch (Exception e){
            accessToken = null;
            throw new ServiceException("闽政通程序接口token获取失败！");
        }
    }

    /**
     * 获取 AccessToken（如果已过期则重新获取）
     */
    private void ensureValidToken(boolean init) {
        if (init) {
            return;
        }
        // 如果 token 为空或已过期，则重新获取
        if (accessToken == null || Instant.now().isAfter(expiresTime)) {
            logger.debug("令牌已过期或为空，重新执行获取token");
            this.token();
        }
    }

    /**
     * 数字屏山通用请求方法
     *
     * @param body
     * @return
     */
    private ApiResponseBody requet(String url,  HttpMethod method, Object body) {
        ApiRequestBody apiRequestBody = new ApiRequestBody();
        apiRequestBody.setData(body);
        apiRequestBody.setHead(this.initHead());
        return this.requet(url, method, apiRequestBody, false);
    }

    /**
     * 初始化请求头
     * @return
     */
    private ApiRequestHead initHead() {
        ApiRequestHead apiRequestHead = new ApiRequestHead();
        this.ensureValidToken(false);
        apiRequestHead.setAccessToken(accessToken);
        apiRequestHead.setAccountId(accountId);
        return apiRequestHead;
    }

    public static CloseableHttpClient createHttpClient() throws KeyManagementException, NoSuchAlgorithmException, KeyStoreException {
        // 创建一个信任所有证书的 SSLContext
        SSLContext sslContext = new SSLContextBuilder()
                .loadTrustMaterial(null, (certificate, authType) -> true) // 信任所有证书
                .build();

        // 创建一个 SSLConnectionSocketFactory，跳过主机名验证
        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        // 创建 HttpClient
        return HttpClients.custom()
                .setSSLSocketFactory(socketFactory)
                .build();
    }

    public static RestTemplate createRestTemplate() throws Exception {
        // 创建跳过 SSL 验证的 HttpClient
        CloseableHttpClient httpClient = createHttpClient();

        // 配置 RestTemplate 使用自定义的 HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory(httpClient);

        return new RestTemplate(requestFactory);
    }

    /**
     * 数字屏山通用请求方法
     *
     * @param init 是否初始化方法
     * @return
     */
    private ApiResponseBody requet(String url, HttpMethod method, Object body, boolean init) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        try {
            // 验证是否已登录
            this.ensureValidToken(init);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 序列化请求体为 JSON 并打印日志
            String requestJson = objectMapper.writeValueAsString(body);
            logger.info("【SZPS】请求地址: {}", url);
            logger.debug("【SZPS】请求报文: {}", requestJson);

            // 构建请求实体
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, headers);

            // 发起 POST 请求
            ResponseEntity<ApiResponseBody> responseEntity =
                    createRestTemplate().postForEntity(url, requestEntity, ApiResponseBody.class);

            ApiResponseBody response = responseEntity.getBody();

            // 日志输出响应报文
            if (response != null) {
                logger.debug("【SZPS】响应报文: {}", objectMapper.writeValueAsString(response));
            } else {
                logger.warn("【SZPS】响应体为空！");
            }

            // 业务错误判断
            if (response != null && response.getHead() != null &&
                    !"0".equals(response.getHead().getStatus())) {
                throw new ServiceException(response.getHead().getMessage());
            }

            return response;

        } catch (ServiceException e) {
            logger.error("【SZPS】业务异常: {}", e.getMessage(), e);
            return ApiResponseBody.error("接口请求失败，请核查！");
        } catch (HttpServerErrorException.InternalServerError e) {
            logger.error("【SZPS】服务器内部错误: {}", e.getMessage(), e);
            return ApiResponseBody.error("接口请求失败，请核查！");
        } catch (Exception e) {
            logger.error("【SZPS】未知异常: {}", e.getMessage(), e);
            return ApiResponseBody.error("接口请求失败，请核查！");
        }
    }


    /**
     * 文件上传
     * @param fileUpload
     * @return
     * @throws IOException
     */
    public String uploadFile(FileUpload fileUpload) throws IOException {
        ApiFileUploadRequest request = new ApiFileUploadRequest();
        request.setFileContent(this.getFileBase64(fileUpload));
        request.setFileName(fileUpload.getFileName());
        request.setType(fileUpload.getFileType());
        request.setFileOriginName(fileUpload.getFileName());
        ApiResponseBody result  = this.requet(szps_server+"/szpsMobile/uploadFile", HttpMethod.POST, JSON.toJSONString(request));
        if(result.getHead().getStatus().equals("0") && result.getData()!= null) {
            Map resultMap = (Map) result.getData();
            return resultMap.get("fileId").toString();
        }
        throw new ServiceException(result.getHead().getMessage());
    }

    /**
     * 提交公告
     * @param type
     * @param title
     * @param publishTime
     * @param noticeContent
     * @param fileIds
     * @param publishDeptName
     * @return
     */
    public boolean submitPublicNotice(String type, String title, String publishTime,
                                      String noticeContent,List<String> fileIds, String publishDeptName) {
        ApiNoticeBody request = new ApiNoticeBody();
        request.setType(type);
        request.setTitle(title);
        request.setPublishTime(publishTime);
        request.setNoticeContent(noticeContent);
        request.setUpLoadFileList(fileIds);
        request.setPublishDeptName(publishDeptName);
        ApiResponseBody result  = this.requet(szps_server+"/szpsMobile/submitPublicNotice", HttpMethod.POST, request);
        if(result.getHead().getStatus().equals("0")) {
            return true;
        }
        throw new ServiceException(result.getHead().getMessage());
    }

    /**
     * 文件转换成base64
     *
     * @param fileUpload
     * @return
     * @throws IOException
     */
    private String getFileBase64(FileUpload fileUpload) throws IOException {
        // 获取文件输入流（可以是文件、数据库等）
        InputStream fileInputStream = Files.newInputStream(new File(fileUpload.getFileEntity().getFileRealPath()).toPath());  // 假设这个方法返回一个文件的 InputStream

        // Step 1: 使用 ByteArrayOutputStream 存储字节数据
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        byte[] buffer = new byte[8192];  // 设定缓冲区大小
        int length;

        // Step 2: 读取文件流并写入 ByteArrayOutputStream
        while ((length = fileInputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, length);
        }

        // Step 3: 获取文件字节数据
        byte[] fileBytes = byteArrayOutputStream.toByteArray();

        // Step 4: 将字节数据转换为 Base64 编码的字符串
        String fileContent = Base64.getEncoder().encodeToString(fileBytes);
        return fileContent;
    }

}