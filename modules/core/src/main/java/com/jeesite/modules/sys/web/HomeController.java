//package com.jeesite.modules.sys.web;
//
//import com.jeesite.common.web.http.ServletUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.Model;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
///**
// * <AUTHOR>
// */
//@Controller
//public class HomeController {
//
//    @Value("${adminPath}")
//    protected String adminPath;
//
//    @RequestMapping(path = "/", method = {RequestMethod.GET})
//    public String home(HttpServletRequest request, HttpServletResponse response, Model model) {
//
//        ServletUtils.redirectUrl(request, response, adminPath + "/login");
//        return null;
//
//        // 返回自定义的视图名称（如模板引擎中的页面）
////        return "custom-home";
//
//        // 或者直接重定向到指定路径
//        // return "redirect:/custom-page";
//    }
//
//}
