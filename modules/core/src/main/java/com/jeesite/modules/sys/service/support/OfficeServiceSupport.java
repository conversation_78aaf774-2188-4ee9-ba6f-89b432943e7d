/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.service.support;

import com.jeesite.common.config.Global;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.mybatis.mapper.query.QueryWhere;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.service.TreeService;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.dao.OfficeDao;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.OfficeEstablishment;
import com.jeesite.modules.sys.entity.OfficeEstablishmentInfo;
import com.jeesite.modules.sys.entity.OfficeUsedRealEstateCount;
import com.jeesite.modules.sys.service.DataScopeService;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.service.OfficeEstablishmentService;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机构Service
 * <AUTHOR>
 * @version 2016-4-23
 */
public class OfficeServiceSupport extends TreeService<OfficeDao, Office>
		implements OfficeService{

	@Autowired
	private DataScopeService dataScopeService;

	@Autowired
	private EmpUserService empUserService;

	@Autowired
	private OfficeEstablishmentService officeEstablishmentService;

	@Autowired
	private OfficeDao officeDao;
	
	/**
	 * 获取单条数据
	 */
	@Override
	public Office get(Office office) {
		return super.get(office);
	}

	@Override
	public void loadOfficeApprovedArea(Office office) {
		if (null == office.getOfficeCode()) {
			return;
		}
		Office newOffice = officeDao.getOfficeApprovedArea(office);
		office.setServiceRoomApprovedArea(newOffice.getServiceRoomApprovedArea());
		office.setEquipmentRoomApprovedArea(newOffice.getEquipmentRoomApprovedArea());
		office.setAncillaryRoomAreaApprovedArea(newOffice.getAncillaryRoomAreaApprovedArea());
	}

	@Override
	public List<OfficeUsedRealEstateCount> getOfficeUsedRealEstateCount(String officeCode) {
		return officeDao.getOfficeUsedRealEstateCount(officeCode);
	}

	/**
	 * 添加数据权限过滤条件
	 */
	@Override
	public void addDataScopeFilter(Office office, String ctrlPermi) {
		office.sqlMap().getDataScope().addFilter("dsf", "Office", "a.office_code",
				null, ctrlPermi , "office_user");
	}

	@Override
	public List<Office> findListAndUserCount(Office office) {
		List<Office> officeList = super.findList(office);
//		officeList.stream().forEach(item -> {
//			Office child = new Office();
//			SqlMap sqlMap = child.sqlMap();
//			sqlMap.getWhere()
//					.andBracket("office_code", QueryType.EQ, item.getOfficeCode(), 1)
//					.or("parent_codes", QueryType.RIGHT_LIKE, item.getParentCodes().concat(item.getOfficeCode()).concat(","), 2)
//					.endBracket();
//			List<String> childrenOfficeCodeList = super.findList(child).stream().map(Office::getOfficeCode).collect(Collectors.toList());
//			if (!childrenOfficeCodeList.isEmpty()) {
//				EmpUser empUser = new EmpUser();
//				empUser.getEmployee().getOffice().setIsQueryChildren(true);
//				empUser.getEmployee().getOffice().setOfficeCode(item.getOfficeCode());
//				List<EmpUser> findUserList = empUserService.findList(empUser);
//				item.setOfficeEstablishmentInfo(getOfficeEstablishmentInfo(findUserList));
//			}
//		});
		return officeList;
	}

	/**
	 * 查询组织机构列表
	 */
	@Override
	public List<Office> findList(Office office) {
		return super.findList(office);
	}

	private OfficeEstablishmentInfo getOfficeEstablishmentInfo(List<EmpUser> empUserList) {
		OfficeEstablishmentInfo officeEstablishmentInfo = new OfficeEstablishmentInfo();
		officeEstablishmentInfo.setTotalStaffNumber(empUserList.size());
		// 省级正职人员数量
		officeEstablishmentInfo.setProvincialLeaderCount(empUserListFilterByType(empUserList, "30").size());
		// 省级副职人员数量
		officeEstablishmentInfo.setProvincialDeputyLeaderCount(empUserListFilterByType(empUserList, "40").size());
		// 正厅（局）级人员数量
		officeEstablishmentInfo.setDirectorLevelCount(empUserListFilterByType(empUserList, "50").size());
		// 副厅（局级）人员数量
		officeEstablishmentInfo.setDeputyDirectorLevelCount(empUserListFilterByType(empUserList, "60").size());
		// 正处级人员数量
		officeEstablishmentInfo.setPositiveDepartmentLevelCount(empUserListFilterByType(empUserList, "70").size());
		// 副处级人员数量
		officeEstablishmentInfo.setDeputyDepartmentLevelCount(empUserListFilterByType(empUserList, "80").size());
		// 处级以下人员数量
		officeEstablishmentInfo.setBelowDepartmentLevelCount(empUserListFilterByType(empUserList, "90").size());
		return officeEstablishmentInfo;
	}

	private List<EmpUser> empUserListFilterByType(List<EmpUser> empUserList, String type) {
		return empUserList.stream().filter(item -> item.getEmployee().getEstablishmentType() != null && item.getEmployee().getEstablishmentType().equals(type)).collect(Collectors.toList());
	}

	/**
	 * 保存数据（插入或更新）
	 */
	@Override
	@Transactional
	public void save(Office office) {

		if (null == office.getFullName() || office.getFullName().isEmpty()) {
			office.setFullName(office.getOfficeName());
		}
		if (null != office.getOfficeEstablishmentId()){
			OfficeEstablishment officeEstablishment = new OfficeEstablishment();
			officeEstablishment.setId(office.getOfficeEstablishmentId());
			officeEstablishmentService.delete(officeEstablishment);
		}

		OfficeEstablishment saveOfficeEstablishment = new OfficeEstablishment();
		if (null != office.getOfficeEstablishment()){
			saveOfficeEstablishment = office.getOfficeEstablishment();
		}
		saveOfficeEstablishment.setId(null);
		resetOtherFieldsByType(office.getOfficeType(), saveOfficeEstablishment);
		officeEstablishmentService.save(saveOfficeEstablishment);

		office.setOfficeEstablishmentId(saveOfficeEstablishment.getId());
		if (office.getIsNewRecord()){
			try {
				// 生成主键，并验证改主键是否存在，如存在则抛出验证信息
				genIdAndValid(office, office.getViewCode());
				// 当前新数据授权，如果用户有上级数据权限，则当前数据也有相应的数据权限
				dataScopeService.insertIfParentExists(office, "Office");
			} catch (Exception e) {
				throw new ServiceException(e.getMessage());
			}
		}
		super.save(office);

		// 保存上传附件
		FileUploadUtils.saveFileUpload(office, office.getId(), "technical_business_premises_area_file");

		Office office1 = new Office();
		String officeCode = office.getOfficeCode();
		office1.setOfficeCode(officeCode);
		office1 = super.get(officeCode);
		if(officeCode.length() < 10){
			String zero = "0000000000";
			officeCode = zero.substring(officeCode.length()) + officeCode;
		}
		office1.setOfficeCodeTree(office.getParentCodes()+officeCode+",");
		super.save(office1);

		// 清理部门相关缓存
		clearOfficeCache();
	}
	/**
	 * 根据机关类型重置非对应字段为0
	 * @param officeType 机关类型
	 * @param establishment 编制信息对象
	 */
	private void resetOtherFieldsByType(String officeType, OfficeEstablishment establishment) {
		switch (officeType) {
			case "1": // 中央机关
				resetProvinceFields(establishment);
				resetMunicipalFields(establishment);
				resetCountyFields(establishment);
				resetTownshipFields(establishment);
				break;
			case "2": // 省级机关
				resetCentralFields(establishment);
				resetMunicipalFields(establishment);
				resetCountyFields(establishment);
				resetTownshipFields(establishment);
				break;
			case "3": // 市级机关
				resetCentralFields(establishment);
				resetProvinceFields(establishment);
				resetCountyFields(establishment);
				resetTownshipFields(establishment);
				break;
			case "4": // 县级机关
				resetCentralFields(establishment);
				resetProvinceFields(establishment);
				resetMunicipalFields(establishment);
				resetTownshipFields(establishment);
				break;
			case "5": // 乡级机关
				resetCentralFields(establishment);
				resetProvinceFields(establishment);
				resetMunicipalFields(establishment);
				resetCountyFields(establishment);
				break;
			default:
				// 默认全部重置（根据业务需求调整）
				resetAllEstablishmentFields(establishment);
		}
	}

	// 重置中央机关字段
	private void resetCentralFields(OfficeEstablishment e) {
		e.setMinisterPositive(0);
		e.setMinisterDeputy(0);
		e.setDepartmentDirector(0);
		e.setDeputyDepartmentDirector(0);
		e.setDivisionLevel(0);
		e.setBelowDivisionLevel(0);
	}

	// 重置省级机关字段
	private void resetProvinceFields(OfficeEstablishment e) {
		e.setProvincePositive(0);
		e.setProvinceDeputy(0);
		e.setBureauDirector(0);
		e.setDeputyBureauDirector(0);
		e.setDivisionChief(0);
		e.setDeputyDivisionChief(0);
		e.setBelowDivisionChief(0);
	}

	// 重置市级机关字段
	private void resetMunicipalFields(OfficeEstablishment e) {
		e.setMunicipalPositive(0);
		e.setMunicipalDeputy(0);
		e.setMunicipalBureauDirector(0);
		e.setMunicipalDeputyBureauDirector(0);
		e.setBelowMunicipalBureau(0);
	}

	// 重置县级机关字段
	private void resetCountyFields(OfficeEstablishment e) {
		e.setCountyPositive(0);
		e.setCountyDeputy(0);
		e.setSectionChief(0);
		e.setDeputySectionChief(0);
		e.setBelowSectionLevel(0);
	}

	// 重置乡级机关字段
	private void resetTownshipFields(OfficeEstablishment e) {
		e.setTownshipPositive(0);
		e.setTownshipDeputy(0);
		e.setBelowTownshipLevel(0);
	}

	// 重置所有编制字段（可选）
	private void resetAllEstablishmentFields(OfficeEstablishment e) {
		resetCentralFields(e);
		resetProvinceFields(e);
		resetMunicipalFields(e);
		resetCountyFields(e);
		resetTownshipFields(e);
	}

	/**
	 * 导入机构数据
	 * @param file 导入的机构数据文件
	 * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
	 */
	@Override
	@Transactional
	public String importData(MultipartFile file, Boolean isUpdateSupport) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<Office> list = ei.getDataList(Office.class);
			for (Office office : list) {
				try{
					// 验证数据文件
					ValidatorUtils.validateWithException(office);
					// 验证是否存在这个机构
					Office e = get(office.getOfficeCode());
					if (e == null){
						office.setIsNewRecord(true);
						this.save(office);
						successNum++;
						successMsg.append("<br/>" + successNum + "、机构 " + office.getOfficeCode() + " 导入成功");
					} else if (isUpdateSupport){
						office.setOfficeCode(e.getOfficeCode());
						this.save(office);
						successNum++;
						successMsg.append("<br/>" + successNum + "、机构 " + office.getOfficeCode() + " 更新成功");
					} else {
						failureNum++;
						failureMsg.append("<br/>" + failureNum + "、机构 " + office.getOfficeCode() + " 已存在");
					}
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、机构 " + office.getOfficeCode() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			failureMsg.append(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}

	/**
	 * 更新部门状态
	 */
	@Override
	@Transactional
	public void updateStatus(Office office) {
		super.updateStatus(office);
		// 清理部门相关缓存
		clearOfficeCache();
	}

	/**
	 * 删除数据
	 */
	@Override
	@Transactional
	public void delete(Office office) {
		office.sqlMap().markIdDelete();
		super.delete(office);
		// 清理部门相关缓存
		clearOfficeCache();
	}
	
	/**
	 * 清理部门相关缓存
	 */
	private void clearOfficeCache(){
//		EmpUtils.removeCache(EmpUtils.CACHE_OFFICE_LIST);
		EmpUtils.removeCache(EmpUtils.CACHE_OFFICE_ALL_LIST);
	}

}
