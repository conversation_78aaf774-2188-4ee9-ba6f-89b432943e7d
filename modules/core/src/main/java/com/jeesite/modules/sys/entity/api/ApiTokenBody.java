package com.jeesite.modules.sys.entity.api;

public class ApiTokenBody {

    private String accountId;

    private String sign;

    private String accessToken;

    private String expiresIn;

    public ApiTokenBody() {
    }

    public ApiTokenBody(String accountId, String sign) {
        this.accountId = accountId;
        this.sign = sign;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(String expiresIn) {
        this.expiresIn = expiresIn;
    }

    @Override
    public String toString() {
        return "ApiTokenBody{" +
                "accountId='" + accountId + '\'' +
                ", sign='" + sign + '\'' +
                '}';
    }
}
