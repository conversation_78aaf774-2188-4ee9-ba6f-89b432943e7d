package com.jeesite.modules.bpm.web;


import com.fasterxml.jackson.annotation.JsonView;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.entity.BpmForm;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.entity.BpmVariable;
import com.jeesite.modules.bpm.service.BpmRuntimeService;
import com.jeesite.modules.bpm.utils.BpmFormUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(
        tags = {"ABpmRuntime - 流程处理"}
)
@RequestMapping({"${adminPath}/bpm/bpmRuntime"})
public class BpmRuntimeController extends BaseController {
    @Autowired
    private BpmRuntimeService bpmRuntimeService;

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"getProcIns"})
    @ResponseBody
    @JsonView({DataEntity.SimpleView.class})
    @ApiOperation("获取流程实例数据")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "流程实例ID",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "formKey",
            value = "表单Key",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "bizKey",
            value = "业务主键",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public BpmProcIns getProcIns(BpmProcIns params) {
        BpmProcIns procIns = null;
        if (StringUtils.isNotBlank(params.getId())) {
            procIns = this.bpmRuntimeService.getProcessInstance(params.getId());
        } else if (StringUtils.isNotBlank(params.getFormKey()) && StringUtils.isNotBlank(params.getBizKey())) {
            procIns = this.bpmRuntimeService.getProcessInstanceByBusinessKey(params.getFormKey(), params.getBizKey());
        }

        if (procIns != null) {
            if (StringUtils.isNotBlank(procIns.getFormKey())) {
                BpmForm form = BpmFormUtils.getForm(procIns.getFormKey(), procIns.getProcDef().getVersion(), (String)null);
                if (form != null) {
                    procIns.getProcDef().setForm(form);
                }
            }

            return procIns;
        } else {
            return params;
        }
    }

    @ApiOperation("流程实例列表")
    @RequiresPermissions({"bpm:bpmRuntime"})
    @RequestMapping({"list"})
    public String list(BpmProcIns params, Model model) {
        model.addAttribute("params", params);
        return "modules/bpm/bpmRuntimeList";
    }

    @ApiOperation("流程实例数据")
    @RequiresPermissions({"bpm:bpmRuntime"})
    @RequestMapping({"listData"})
    @ResponseBody
    public Page<BpmProcIns> listData(BpmProcIns params, HttpServletRequest request, HttpServletResponse response) {
        params.setPage(new Page(request, response));
        Page<BpmProcIns> page = this.bpmRuntimeService.findProcessInstancePage(params);
        return page;
    }

    @ApiOperation("获取实例变量")
    @RequiresPermissions({"bpm:bpmRuntime"})
    @RequestMapping({"getVariable"})
    @ResponseBody
    public List<BpmVariable> getVariable(BpmProcIns params, String executionId) {
        return this.bpmRuntimeService.getProcessInstanceVariable(params.getId(), executionId);
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"stop"})
    @ApiOperation("终止流程实例页面")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "流程实例ID",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public String stop(BpmProcIns params, Model model) {
        model.addAttribute("id", params.getId());
        BpmProcIns pi = this.bpmRuntimeService.getProcessInstance(params.getId());
        model.addAttribute("deleteReason", pi != null ? pi.getDeleteReason() : "");
        return "modules/bpm/bpmRuntimeStop";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"stopProcess"})
    @ResponseBody
    @ApiOperation("终止流程实例执行")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "流程实例ID",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "deleteReason",
            value = "终止原因",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public String stopProcess(BpmProcIns params) {
        Global.assertDemoMode();

        try {
            this.bpmRuntimeService.stopProcessInstance(params);
            return this.renderResult("true", text("结束流程成功！", new String[0]));
        } catch (FlowableObjectNotFoundException e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", text("结束失败，流程已结束！", new String[0]));
        }
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"trace"})
    @ApiOperation("流程跟踪页面")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "流程实例ID",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "formKey",
            value = "表单Key",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "bizKey",
            value = "业务主键",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public String trace(BpmProcIns params, boolean app, Model model) {
        if (StringUtils.isBlank(params.getId()) && StringUtils.isNotBlank(params.getFormKey()) && StringUtils.isNotBlank(params.getBizKey())) {
            BpmProcIns pi = this.bpmRuntimeService.getProcessInstanceByBusinessKey(params.getFormKey(), params.getBizKey());
            if (pi != null) {
                params.setId(pi.getId());
                params.setStatus(pi.getStatus());
            } else {
                model.addAttribute("message", text("未查询到该流程的信息", new String[0]));
            }
        }

        model.addAttribute("params", params);
        return "modules/bpm/bpmRuntimeTrace" + (app ? "App" : "");
    }

    @ApiOperation("删除流程实例")
    @RequiresPermissions({"bpm:bpmRuntime"})
    @RequestMapping({"delete"})
    @ResponseBody
    public String delete(BpmProcIns params) {
        Global.assertDemoMode();

        try {
            this.bpmRuntimeService.deleteProcessInstance(params);
            return this.renderResult("true", text("删除流程成功！", new String[0]));
        } catch (FlowableException e) {
            this.logger.debug(e.getMessage(), e);
            return this.renderResult("false", e.getMessage());
        }
    }
}
