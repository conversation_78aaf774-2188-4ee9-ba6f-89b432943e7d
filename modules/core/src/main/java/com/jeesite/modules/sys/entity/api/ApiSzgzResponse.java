/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.entity.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 资产查询响应实体类
 * 
 * <AUTHOR>
 * @version 2025-06-22
 */
public class ApiSzgzResponse {

    /**
     * 响应码
     */
    @JsonProperty("code")
    private String code;

    /**
     * 响应消息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    private AssetData data;

    private String userTip;

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public AssetData getData() {
        return data;
    }

    public void setData(AssetData data) {
        this.data = data;
    }

    public String getUserTip() {
        return userTip;
    }

    public void setUserTip(String userTip) {
        this.userTip = userTip;
    }

    /**
     * 资产数据内部类
     */
    public static class AssetData {
        /**
         * 当前页
         */
        @JsonProperty("current")
        private Integer current;

        /**
         * 页大小
         */
        @JsonProperty("size")
        private Integer size;

        /**
         * 总记录数
         */
        @JsonProperty("total")
        private Long total;

        /**
         * 资产记录列表
         */
        @JsonProperty("record")
        private List<ApiSzgzAsset> record;

        // Getters and Setters
        public Integer getCurrent() {
            return current;
        }

        public void setCurrent(Integer current) {
            this.current = current;
        }

        public Integer getSize() {
            return size;
        }

        public void setSize(Integer size) {
            this.size = size;
        }

        public Long getTotal() {
            return total;
        }

        public void setTotal(Long total) {
            this.total = total;
        }

        public List<ApiSzgzAsset> getRecord() {
            return record;
        }

        public void setRecord(List<ApiSzgzAsset> record) {
            this.record = record;
        }
    }



    @Override
    public String toString() {
        return "AssetQueryResponse{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
