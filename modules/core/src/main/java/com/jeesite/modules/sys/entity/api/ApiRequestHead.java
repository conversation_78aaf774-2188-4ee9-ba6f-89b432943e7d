package com.jeesite.modules.sys.entity.api;

public class ApiRequestHead {

    private String accessToken;
    private String accountId;



    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    @Override
    public String toString() {
        return "ApiRequestHead{" +
                "accessToken='" + accessToken + '\'' +
                ", accountId='" + accountId + '\'' +
                '}';
    }
}
