/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.entity.api.ApiSzgzAsset;
import com.jeesite.modules.sys.entity.api.ApiSzgzResponse;
import com.jeesite.modules.sys.entity.api.AssetQueryRequest;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

/**
 * 资产一体化平台接口Service
 *
 * <AUTHOR>
 * @version 2025-06-22
 */

@Service
public class ApiSzgzService extends BaseService {

    ObjectMapper objectMapper = new ObjectMapper();

    private static String szgz_server; //资产一体化平台地址
    private static String assetPath; //资产数据信息查询地址

    /**
     * 资产数据信息查询接口
     * @param request 查询请求参数
     * @return 资产查询响应
     */
    public ApiSzgzResponse queryAssets(AssetQueryRequest request) {
        try {
            // 获取配置
            szgz_server = Global.getConfig("szgz.server");
            assetPath = Global.getConfig("szgz.assetPath", "/api/asset/query");

            if (szgz_server == null || szgz_server.trim().isEmpty()) {
                throw new ServiceException("资产一体化平台服务器地址未配置");
            }

            // 构建请求URL
            String url = szgz_server + assetPath;

            // 创建RestTemplate
            RestTemplate restTemplate = createRestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("User-Agent", "HSOBS-Asset-Client/1.0");

            // 创建请求实体
            HttpEntity<AssetQueryRequest> requestEntity = new HttpEntity<>(request, headers);

            // 发送POST请求
            logger.info("发送资产查询请求到: {}", url);
            logger.debug("请求参数: {}", objectMapper.writeValueAsString(request));

            ResponseEntity<ApiSzgzResponse> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                    ApiSzgzResponse.class
            );

            ApiSzgzResponse result = response.getBody();

            if (!result.getCode().equals("00000")) {
                logger.error("资产查询失败: {}", result.getMessage() + result.getUserTip());
                throw new ServiceException("资产查询失败: " + result.getMessage() + result.getUserTip());
            }

            if (result != null) {
                logger.info("资产查询成功，返回码: {}, 消息: {}", result.getCode(), result.getMessage());
                if (result.getData() != null) {
                    logger.info("查询到 {} 条资产记录", result.getData().getTotal());
                }
            }

            return result;

        } catch (HttpServerErrorException e) {
            logger.error("资产查询服务器错误: {}", e.getMessage(), e);
            throw new ServiceException("资产查询服务器错误: " + e.getMessage());
        } catch (Exception e) {
            logger.error("资产查询失败: {}", e.getMessage(), e);
            throw new ServiceException("资产查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建RestTemplate，支持HTTPS
     */
    private RestTemplate createRestTemplate() {
        try {
            // 创建信任所有证书的SSL上下文
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial((chain, authType) -> true)
                    .build();

            // 创建SSL连接工厂
            SSLConnectionSocketFactory sslConnectionSocketFactory =
                new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

            // 创建HttpClient
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLSocketFactory(sslConnectionSocketFactory)
                    .build();

            // 创建请求工厂
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setHttpClient(httpClient);
            factory.setConnectTimeout(30000); // 30秒连接超时
            factory.setReadTimeout(60000);    // 60秒读取超时

            return new RestTemplate(factory);

        } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
            logger.error("创建RestTemplate失败: {}", e.getMessage(), e);
            throw new ServiceException("创建HTTP客户端失败: " + e.getMessage());
        }
    }

    /**
     * 查询资产信息
     */
    public List<ApiSzgzAsset> getSzgzAssetList(AssetQueryRequest query) {
        return this.queryAssets(query).getData().getRecord();
    }

    /**
     * 查询资产信息-page
     */
    public Page<ApiSzgzAsset> getSzgzAssetPage(AssetQueryRequest query) {
        if (query.getPageNo() == null) {
            query.setPageNo(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        ApiSzgzResponse response =  this.queryAssets(query);
        Page<ApiSzgzAsset> page = new Page<>();
        page.initialize();
//        page.setList(response.getData().getRecord());
        List<ApiSzgzAsset> list = new ArrayList<>();
        ApiSzgzAsset asset = new ApiSzgzAsset();
        asset.setAssetId("1");
        asset.setAssetCode("1");
        asset.setAssetName("1");
        list.add(asset);
        page.setList(list);
        page.setCount(response.getData().getTotal());
        page.setPageSize(query.getPageSize());
        page.setPageNo(query.getPageNo());
        return page;
    }

}