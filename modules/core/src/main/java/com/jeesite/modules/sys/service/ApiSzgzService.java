/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.config.Global;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.entity.ApiSzgzAsset;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.entity.api.Api2TaskInfo;
import com.jeesite.modules.sys.utils.SM2Utils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资产一体化平台接口Service
 *
 * <AUTHOR>
 * @version 2025-06-22
 */

@Service
public class ApiSzgzService extends BaseService {

    ObjectMapper objectMapper = new ObjectMapper();

    private static String szgz_server; //资产一体化平台地址
    private static String assetPath; //资产数据信息查询地址

    /**
     * 资产数据信息查询接口
     * @return
     */
    public List<ApiSzgzAsset> getSzgzAssetList(ApiSzgzAsset query){
        List<ApiSzgzAsset> szgzAssetList = new ArrayList<>();
        szgz_server = Global.getConfig("szgz.server");

        return szgzAssetList;
    }
}