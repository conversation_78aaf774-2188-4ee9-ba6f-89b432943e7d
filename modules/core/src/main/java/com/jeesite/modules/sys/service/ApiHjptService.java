/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.service;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.asymmetric.SM2;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.config.Global;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.entity.api.*;
import com.jeesite.modules.sys.utils.SM2Utils;
import com.jeesite.modules.sys.utils.SM4Utils;
import org.bouncycastle.pqc.legacy.math.linearalgebra.ByteUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDate;
import java.util.UUID;
import java.io.File;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 汇聚平台交互
 *
 * <AUTHOR>
 * @version 2017-03-25
 */

@Service
public class ApiHjptService extends BaseService {

    private static String hupt_server; //汇聚平台接口地址
    private static String appId; //汇聚平台分配的appId
    private static String appKey; //汇聚平台分配的appKey
    private static String hjptPublicKey;

    private static String hjptMyPrivateKey;
    private static String hjptMyPublicKey;

    private static String hjptSm4Key;
    private static LocalDate dateToday;

    private String accessToken; //汇聚平台接口token
    private Instant expiresTime;

    private String accountToken;            // 公积金token
    private Instant accountExpiresTime;     //
    private AtomicInteger accountTick;      // 访问次数，限制一个token只能访问50次

    private static String comm_ip;
    private static String comm_mac;

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    static {

        try{
            /*hupt_server = Global.getConfig("hjpt.server").toString();
            appId = Global.getConfig("hjpt.app-id").toString();
            appKey = Global.getConfig("hjpt.app-key").toString();
            hjptPublicKey = Global.getConfig("hjpt.sm2.pkb-hjpt").toString();*/

            hupt_server = "https://**************:19090";
            appId = "737917aaafc76839440357f30c65b7f9";
            appKey = "ec0a51be3c19aa6af3729f6f3da4b1fe";
            hjptPublicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEKJmFIq/Y48kpRmpzHwdkDEr/K7aOcSt/GDfV7+8d+Uq1jKvY+jhG7souKq/PZpfjZJPrJAEC+wOg71ouuGeKFw==";

            hjptMyPrivateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgrqUuHvHTedRkKVRFn+CjS8Y3eA9P6/Kuh+PfJEjb65WgCgYIKoEcz1UBgi2hRANCAAScBKYWPsIFm0Tyxh4tjpK8sSMRFZoWzkluaUT4Ht+ZQomdfviKlerTp/XZLQ7k81NewrugsVGeI1pFzDwqM90P";
            hjptMyPublicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEnASmFj7CBZtE8sYeLY6SvLEjERWaFs5JbmlE+B7fmUKJnX74ipXq06f12S0O5PNTXsK7oLFRniNaRcw8KjPdDw==";

            comm_ip = "***********";
            comm_mac = "70-2A-D7-76-4A-B0";

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取数字空间接口的请求token
     * @return
     */
    private void token() {

        try {

            String appIdSm2Base64 = SM2Utils.getInstance().SM2Encrypt(appId, hjptPublicKey);
            String appKeySm2Base64 = SM2Utils.getInstance().SM2Encrypt(appKey, hjptPublicKey);
            Map<String,String> map = new HashMap<>();
            map.put("username", appIdSm2Base64);
            map.put("password", appKeySm2Base64);
            ApiHjResponseBody result = this.requet(hupt_server+"/new/convergence/baseService/getToken", HttpMethod.POST, JSON.toJSONString(map), true);
            if(result.getCode() != null && result.getCode().equals("01") && result.getToken() != null) {

                accessToken = SM2Utils.getInstance().SM2Decrypt(result.getToken(), hjptMyPrivateKey);
                //accessToken = Base64.getEncoder().encodeToString(token.getBytes(StandardCharsets.UTF_8));
                expiresTime = Instant.now().plus(Integer.parseInt("1500"), ChronoUnit.SECONDS);

            }else{
                accessToken = null;
            }
        }catch (Exception e){
            accessToken = null;
        }
    }

    /**
     * 获取 AccessToken（如果已过期则重新获取）
     */
    private void ensureValidToken(boolean init) {
        if (init) {
            return;
        }
        // 如果 token 为空或已过期，则重新获取
        if (accessToken == null || Instant.now().isAfter(expiresTime)) {
            logger.debug("令牌已过期或为空，重新执行获取token");
            this.token();
        }
    }

    /**
     * 数字空间通用请求方法
     *
     * @param body
     * @return
     */
    private ApiHjResponseBody requet(String url,  HttpMethod method, Object body) {
        return this.requet(url, method, body, false);
    }

    public static CloseableHttpClient createHttpClient() throws KeyManagementException, NoSuchAlgorithmException, KeyStoreException {
        // 创建一个信任所有证书的 SSLContext
        SSLContext sslContext = new SSLContextBuilder()
                .loadTrustMaterial(null, (certificate, authType) -> true) // 信任所有证书
                .build();

        // 创建一个 SSLConnectionSocketFactory，跳过主机名验证
        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        // 创建 HttpClient
        return HttpClients.custom()
                .setSSLSocketFactory(socketFactory)
                .build();
    }

    public static RestTemplate createRestTemplate() throws Exception {
        // 创建跳过 SSL 验证的 HttpClient
        CloseableHttpClient httpClient = createHttpClient();

        // 配置 RestTemplate 使用自定义的 HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory(httpClient);

        return new RestTemplate(requestFactory);
    }

    /**
     * 数字空间通用请求方法
     *
     * @param body
     * @param init 是否初始化方法
     * @return
     */
    private ApiHjResponseBody requet(String url, HttpMethod method, Object body, boolean init) {
        logger.debug("SZKJ请求报文: " + body.toString());

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type","application/json;charset=UTF-8");
        headers.add("X-APP-ID", appId);
        String uuidId = UUID.randomUUID().toString().replace("-", "");;
        headers.add("X-CTG-Request-ID", uuidId);

        try {
            // 组合请求体和请求头
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, headers);

            // 发送 POST 请求并解析 JSON 响应
            logger.debug(String.format("request uuid:%s  url: %s", uuidId, url));
            ResponseEntity<ApiHjResponseBody> responseEntity = createRestTemplate().exchange(url, method, requestEntity, ApiHjResponseBody.class);

            // 处理返回数据
            ApiHjResponseBody response = responseEntity.getBody();
            if(response == null){
                throw new ServiceException("请求没有响应");
            }
            if (!response.getCode().equals("01")) {
                throw new ServiceException(response.getMessage());
            }
            return response;

        } catch (ServiceException e) {
            return ApiHjResponseBody.error("接口请求失败，请核查！" + e.getMessage());
        } catch (Exception e) {
            return ApiHjResponseBody.error("接口请求失败，请核查！" + e.getMessage());
        }
    }

    private ApiHjResponseBody requet2(String url, HttpMethod method, Object body, boolean init) {
        logger.debug("SZKJ请求报文: " + body.toString());

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type","application/json;charset=UTF-8");
        headers.add("X-APP-ID", appId);
        String uuidId = UUID.randomUUID().toString().replace("-", "");;
        headers.add("X-CTG-Request-ID", uuidId);

        try {
            // 组合请求体和请求头
            HttpEntity<Object> requestEntity = new HttpEntity<>(body, headers);

            // 发送 POST 请求并解析 JSON 响应
            logger.debug(String.format("request uuid:%s  url: %s", uuidId, url));

            ResponseEntity<String> responseEntity = createRestTemplate().exchange(url, method, requestEntity, String.class);

            System.out.println(responseEntity.getBody());
            ObjectMapper objectMapper = new ObjectMapper();
            /*Map<String, String> map = objectMapper.readValue(responseEntity.getBody(), new TypeReference<Map<String, String>>() {});

            if(map.containsKey("data")){
                return ApiHjResponseBody.sucess(map.get("data"));
            }*/

            return ApiHjResponseBody.error("接口请求失败，请核查");

        } catch (ServiceException e) {
            return ApiHjResponseBody.error("接口请求失败，请核查！" + e.getMessage());
        } catch (Exception e) {
            return ApiHjResponseBody.error("接口请求失败，请核查！" + e.getMessage());
        }
    }

    public ApiHjResponseBody getSM4Key() {

        this.ensureValidToken(false);
        if(accessToken == null){
            throw new ServiceException("token获取失败！");
        }
        LocalDate now = LocalDate.now();
        if(dateToday != null
        && dateToday.getYear()==now.getYear() 
        && dateToday.getDayOfWeek()==now.getDayOfWeek()){
            return ApiHjResponseBody.sucess();
        }

        try {

            String tokenSm2Base64 = SM2Utils.getInstance().SM2Encrypt(accessToken, hjptPublicKey);
        Map<String, String> map = new HashMap<>();
            map.put("token", tokenSm2Base64);
        ApiHjResponseBody result = this.requet(hupt_server + "/new/convergence/baseService/getSm4Key", HttpMethod.POST, JSON.toJSONString(map), false);
        if (result.getCode() != null && result.getCode().equals("01") && result.getSm4key() != null) {

                String sm4key = result.getSm4key();    // 返回的是sm2加密，然后Base64编码
                hjptSm4Key = SM2Utils.getInstance().SM2Decrypt(sm4key, hjptMyPrivateKey);
                dateToday = LocalDate.now();

                return ApiHjResponseBody.sucess();
            }

            } catch (Exception e) {
            
            }

        return ApiHjResponseBody.error("获取sm4key失败");
        }

    private ApiHjRequestBody initHjRequestBody(){
        ApiHjRequestBody body = new ApiHjRequestBody();

        // 设置共用参数
        body.setSirc_token(accessToken);
        System.currentTimeMillis();
        Date date = new Date(System.currentTimeMillis());
        System.out.println(String.format("%04d-%02d-%02d %02d:%02d", date.getYear()+1900,date.getMonth()+1,date.getDate(), date.getHours(), date.getMinutes()));;
        body.setSirc_time(System.currentTimeMillis());

        body.setComm_ip(comm_ip);
        body.setComm_mac(comm_mac);

        body.setComm_applicationID("1873573591620538368");   // 实际应用系统ID
        body.setComm_applicationSystemName("住房保障管理平台"); // 实际应用系统名称
        body.setComm_netType("2");          // 应用系统服务访问网络
        body.setComm_callerType("5");    // 本次调用应用场景类型
        body.setComm_queryCause("查询公积金信息");    // 查询事由
        body.setComm_queryResume("因业务需要，需查询公积金信息");  // 查询信息简述
        body.setComm_checkType("513");      // 核验类型参数

        // nettype=1 callerType=1  checkType=501&504 公众用户登录凭证核验(501) + 公众用户人脸身份凭证核验(504)
            // 必填项comm_userVerifyAccessToken  办理申请人员(公众用户)登录凭证
        // nettype=2 callerType=5  checkType=513 工作人员人脸身份凭证核验(513)
            // 必填项comm_clerksCheckName

        body.setAccountId(appId);   // 用户标识——必填,由省/地市公积金中心线下提供
        body.setSecret(appKey);     // 用户签名密钥——必填,由省/地市公积金中心线下提供
        body.setZxbh("35001");  // 福建省直单位住房公积金管理中心

        return body;
    }

    // 获取省直单位住房公积金token
    public ApiHjResponseBody getAccountToken(){

        ApiHjResponseBody result = getSM4Key();
        if(!result.isSuccess()){
            return result;
        }

        try{
            ApiHjRequestBody body = initHjRequestBody();
            String data = JSON.toJSONString(body);
            logger.debug("上报数据"+data);
            String dataEString = SM4Utils.getInstance().SM4Encrypt(data, "SM4/ECB/PKCS5Padding", hjptSm4Key, null);
            Map<String, String> map = new HashMap<>();
            map.put("data", dataEString);
            result = this.requet2(hupt_server + "/new/convergence/customizationInterface/fjszdwzfgjjglzx1/getAccessToken", HttpMethod.POST, JSON.toJSONString(map), false);
            if (result.getCode() != null && result.getCode().equals("01") && result.getData() != null){

                data = result.getData();
                String restMsg = SM4Utils.getInstance().SM4Decrypt(data, "SM4/ECB/PKCS5Padding", hjptSm4Key, null);
                System.out.println(restMsg);

                return ApiHjResponseBody.sucess();
            }

        } catch(Exception e) {
        }
        return ApiHjResponseBody.error("获取公积金token失败");
    }

    public ApiHjResponseBody getAccountInfo(String id){ // 身份证

        ApiHjResponseBody result = getSM4Key();
        if(!result.isSuccess()){
            return result;
        }

        try{
            ApiHjRequestBody body = initHjRequestBody();
            String data = SM4Utils.getInstance().SM4Encrypt(JSON.toJSONString(body), "SM4/ECB/PKCS5Padding", hjptSm4Key, null);
            Map<String, String> map = new HashMap<>();
            map.put("data", data);
            result = this.requet(hupt_server + "/new/convergence/customizationInterface/fjszdwzfgjjglzx1/personAccountInfoByNumAndType", HttpMethod.POST, JSON.toJSONString(map), false);

            if (result.getCode() != null && result.getCode().equals("01") && result.getData() != null){

                data = result.getData();
                String restMsg = SM4Utils.getInstance().SM4Decrypt(data, "SM4/ECB/PKCS5Padding", hjptSm4Key, null);
                System.out.println(restMsg);

                return ApiHjResponseBody.sucess();
            }

        } catch(Exception e) {
        }
        return ApiHjResponseBody.error("获取公积金信息失败");
    }

    public static void main(String[] args) {

        
        /*String data = "{\"data\":\"z9kjJweJ1bCNtrmZVEGQyoRySz1T4KNzv/+qPZRC/z9ljTYuWMtpeeNxDu6Cf4ULHNNSdQjX1xfalH50MlTgPg==\"}";

        try{
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, String> map = objectMapper.readValue(data, new TypeReference<Map<String, String>>() {});
            String dd = map.get("data");

            int iiii = 0;
            
        } catch(Exception e){

        }*/
    

        ApiHjptService service = new ApiHjptService();

        /*try{
            String restMsg = SM4Utils.getInstance().SM4Decrypt("KwfYqVn4dZB9DNoYKhzYCvbSQdooAZnhU/iMGS+Ok0cRI/CXjMU5gTKF2F7hgM6inYdWwOo3wMVffeoHCrAHrg==", "SM4/ECB/PKCS5Padding", "P0m5PrPKu2MsEVmf8XfoKg==", null);
            System.out.println(restMsg);
        }catch(Exception e){

        }*/

        service.getAccountToken();
    }

}