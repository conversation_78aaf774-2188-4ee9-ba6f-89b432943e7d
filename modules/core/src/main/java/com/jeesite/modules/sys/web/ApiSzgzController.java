/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.web;

import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.sys.entity.api.ApiSzgzAsset;
import com.jeesite.modules.sys.entity.api.AssetQueryRequest;
import com.jeesite.modules.sys.service.ApiSzgzService;
import io.swagger.annotations.Api;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资产一体化平台接口Controller
 * 
 * <AUTHOR>
 * @version 2025-06-22
 */
@Controller
@RequestMapping("${adminPath}/szgz")
@Validated
public class ApiSzgzController extends BaseController {

    @Autowired
    private ApiSzgzService apiSzgzService;

    /**
     * 2.1 资产数据信息查询接口
     * 
     * @param request 查询请求参数
     * @return 资产查询响应
     */
    @RequestMapping(value = "/assets/query")
    @ResponseBody
    public Page<ApiSzgzAsset> queryAssets(AssetQueryRequest request) {
        logger.info("接收到资产查询请求: 区划编码={}, 单位编码={}, 页码={}, 页大小={}",
                   request.getMofDivCode(), request.getAgencyCode(),
                   request.getPageNo(), request.getPageSize());
        return apiSzgzService.getSzgzAssetPage(request);
    }

    /**
     * 资产列表信息选择
     *
     * @return 资产选择页面
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "/assets/assetSelect")
    public String applySelect(AssetQueryRequest assetQueryRequest, String selectData, Model model) {
        String selectDataJson = EncodeUtils.decodeUrl(selectData);
        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
            model.addAttribute("selectData", selectDataJson);
        }
        model.addAttribute("assetQueryRequest", assetQueryRequest);
        return "modules/szgz/szgzAssetListSelect";
    }
}
