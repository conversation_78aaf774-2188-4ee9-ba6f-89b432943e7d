package com.jeesite.modules.config.web;

import com.jeesite.common.shiro.filter.HostValidationFilter;
import com.jeesite.common.shiro.filter.RedirectValidationFilter;
import com.jeesite.common.shiro.filter.SecurityHeadersFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * <AUTHOR>
 */
@Configuration
public class HostValidationFilterConfig {

    @Bean
    public FilterRegistrationBean<HostValidationFilter> hostValidationFilter() {
        FilterRegistrationBean<HostValidationFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new HostValidationFilter());
        registration.addUrlPatterns("/*"); // 拦截所有请求
        registration.setName("hostValidationFilter");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE); // 优先级最高
        return registration;
    }

//    @Bean
//    public FilterRegistrationBean<RedirectValidationFilter> redirectValidationFilter() {
//        FilterRegistrationBean<RedirectValidationFilter> registration = new FilterRegistrationBean<>();
//        registration.setFilter(new RedirectValidationFilter());
//        registration.addUrlPatterns("/*"); // 拦截所有请求
////        registration.setName("redirectValidationFilter");
////        registration.setOrder(Ordered.HIGHEST_PRECEDENCE); // 优先级最高
//        return registration;
//    }

    @Bean
    public FilterRegistrationBean<SecurityHeadersFilter> securityHeadersFilter() {
        FilterRegistrationBean<SecurityHeadersFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new SecurityHeadersFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return registration;
    }

}
