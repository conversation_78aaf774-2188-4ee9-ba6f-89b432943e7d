package com.jeesite.modules.bpm.web;

import com.fasterxml.jackson.annotation.JsonView;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.entity.BpmForm;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmRuntimeService;
import com.jeesite.modules.bpm.utils.BpmFormUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(
        tags = {"ABpmMyRuntime - 我的流程"}
)
@RequestMapping({"${adminPath}/bpm/bpmMyRuntime"})
public class BpmMyRuntimeController extends BaseController {
    @Autowired
    private BpmRuntimeService bpmRuntimeService;

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"list"})
    @ApiOperation("我相关的流程页面")
    @ApiImplicitParams({})
    public String list(BpmProcIns params, Model model) {
        model.addAttribute("params", params);
        return "modules/bpm/bpmMyRuntimeList";
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"listData"})
    @ResponseBody
    @JsonView({DataEntity.SimpleView.class})
    @ApiOperation("我相关的流程数据")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "name",
            value = "流程名称",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "procDef.category",
            value = "流程分类",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "startUserId",
            value = "发起人",
            required = false,
            paramType = "query",
            dataType = "String"
    ), @ApiImplicitParam(
            name = "beginDate",
            value = "启动时间-开始",
            required = false,
            paramType = "query",
            dataType = "Date"
    ), @ApiImplicitParam(
            name = "endDate",
            value = "启动时间-结束",
            required = false,
            paramType = "query",
            dataType = "Date"
    ), @ApiImplicitParam(
            name = "status",
            value = "流程状态",
            required = false,
            paramType = "query",
            dataType = "String",
            example = "1未完成；2已完成"
    ), @ApiImplicitParam(
            name = "pageNo",
            value = "当前页码",
            required = false,
            paramType = "query",
            dataType = "Integer"
    ), @ApiImplicitParam(
            name = "pageSize",
            value = "每页条数",
            required = false,
            paramType = "query",
            dataType = "Integer"
    ), @ApiImplicitParam(
            name = "orderBy",
            value = "排序方式",
            required = false,
            paramType = "query",
            dataType = "String"
    )})
    public Page<BpmProcIns> listData(BpmProcIns params, HttpServletRequest request, HttpServletResponse response) {
        params.setPage(new Page(request, response));
        params.setUserCode(params.currentUser().getUserCode());
        Page<BpmProcIns> page = this.bpmRuntimeService.findProcessInstancePage(params);
        return page;
    }

    @RequiresPermissions(value = {"user", "userCommon"}, logical = Logical.OR)
    @RequestMapping({"form"})
    @ResponseBody
    @ApiOperation("获取流程表单")
    @ApiImplicitParams({@ApiImplicitParam(
            name = "id",
            value = "流程实例ID",
            required = true,
            paramType = "query",
            dataType = "String"
    )})
    public String form(BpmProcIns params, Boolean addPrefix, HttpServletRequest request) {
        if (StringUtils.isBlank(params.getId())) {
            throw new ServiceException(text("任务编号不能为空", new String[0]));
        } else {
            BpmProcIns procIns = this.bpmRuntimeService.getProcessInstance(params.getId());
            if (procIns == null) {
                return this.renderResult("false", text("提交失败，任务不存在。", new String[0]));
            } else if (StringUtils.isNotBlank(procIns.getFormKey())) {
                BpmForm form = BpmFormUtils.getForm(procIns.getFormKey(), procIns.getProcDef().getVersion(), (String)null);
                if (form == null) {
                    return this.renderResult("false", text("该流程未绑定流程表单", new String[0]));
                } else {
                    BpmTask task = new BpmTask(procIns);
                    Map<String, Object> data = MapUtils.newHashMap();
                    data.put("pcUrl", form.buildUrl(form.getPcUrl(), task, addPrefix == null || addPrefix));
                    data.put("pcViewUrl", form.buildUrl(form.getPcViewUrl(), task, addPrefix == null || addPrefix));
                    data.put("mobileUrl", form.buildUrl(form.getMobileUrl(), task, false));
                    data.put("mobileViewUrl", form.buildUrl(form.getMobileViewUrl(), task, false));
                    return this.renderResult("true", text("获取流程表单成功", new String[0]), data);
                }
            } else if (UserUtils.getSubject().isPermitted("bpm:bpmTask")) {
                Map<String, Object> data = MapUtils.newHashMap();
                data.put("pcUrl", Global.getCtxPath() + this.adminPath + "/bpm/bpmTask/form?id=" + params.getId());
                return this.renderResult("true", text("该流程未关联业务，进入调试页面", new String[0]), data);
            } else {
                return this.renderResult("false", text("该流程未关联业务", new String[0]));
            }
        }
    }
}

