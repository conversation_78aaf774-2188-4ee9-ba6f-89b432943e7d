package com.jeesite.modules.sys.entity.api;

public class ApiHjResponseBody {

    private String code;
    private String message;
    private String token;
    private String sm4key;
    private String data;

    public boolean isSuccess(){
        return code.equals("01");
    }

    public static ApiHjResponseBody sucess() {
        ApiHjResponseBody response = new ApiHjResponseBody();
        response.setCode("01");
        response.setMessage("success");
        return response;
    }
    public static ApiHjResponseBody sucess(String data) {
        ApiHjResponseBody response = new ApiHjResponseBody();
        response.setCode("01");
        response.setMessage("success");
        response.setData(data);
        return response;
    }

    public static ApiHjResponseBody error(String message) {
        ApiHjResponseBody response = new ApiHjResponseBody();
        response.setCode("500");
        response.setMessage(message);
        return response;
    }
    public static ApiHjResponseBody error(String code, String message) {
        ApiHjResponseBody response = new ApiHjResponseBody();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getToken() {
        return token;
    }
    public void setToken(String token) {
        this.token = token;
    }

    public String getSm4key() {
        return sm4key;
    }

    public void setSm4key(String sm4key) {
        this.sm4key = sm4key;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    
}
