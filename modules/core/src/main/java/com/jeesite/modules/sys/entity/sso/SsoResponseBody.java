package com.jeesite.modules.sys.entity.sso;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.lang.StringUtils;

public class SsoResponseBody {

    private String code;

    private String message;

    private SsoLoginUser data;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public SsoLoginUser getData() {
        return data;
    }

    public void setData(SsoLoginUser data) {
        this.data = data;
    }

    public String getLogMsg() throws JsonProcessingException {
//        // 转换为 JSON 字符串
//        ObjectMapper objectMapper = new ObjectMapper();
//        return objectMapper.writeValueAsString(this);
        return this.toString();
    }

    @Override
    public String toString() {
        return "SsoResponseBody{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + (this.data != null ? this.data.toString() : "null") +
                '}';
    }
}
