/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.file.web;

import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Controller;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.security.DigestInputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * 文件管理Controller
 * <AUTHOR>
 * @version 2019-12-23
 */
@Controller
@RequestMapping(value = "${adminPath}/file")
@ConditionalOnProperty(name={"file.enabled","web.core.enabled"}, havingValue="true", matchIfMissing=true)
public class FileUploadController extends BaseController {

	@Autowired
	private FileUploadService fileUploadService;

	@Autowired
	ResourceLoader resourceLoader;

	/**
	 * 上传文件参数
	 */
	@RequestMapping(value = "params")
	@ResponseBody
	public Map<String, Object> params() {
		Map<String, Object> model = MapUtils.newHashMap();
		model.put("imageAllowSuffixes", Global.getConfig("file.imageAllowSuffixes", FileUploadParams.DEFAULT_IMAGE_ALLOW_SUFFIXES));
		model.put("mediaAllowSuffixes", Global.getConfig("file.mediaAllowSuffixes", FileUploadParams.DEFAULT_MEDIA_ALLOW_SUFFIXES));
		model.put("fileAllowSuffixes", Global.getConfig("file.fileAllowSuffixes", FileUploadParams.DEFAULT_FILE_ALLOW_SUFFIXES));
		model.put("chunked", Global.getConfig("file.chunked", "true"));
		model.put("chunkSize", Global.getConfigToInteger("file.chunkSize", "10*1024*1024"));
		model.put("threads", Global.getConfigToInteger("file.threads", "3"));
		model.put("imageMaxWidth", Global.getConfigToInteger("file.imageMaxWidth", "1024"));
		model.put("imageMaxHeight", Global.getConfigToInteger("file.imageMaxHeight", "768"));
		return model;
	}

	/**
	 * 上传文件
	 */
	@RequestMapping(value = "upload")
	@ResponseBody
	public Map<String, Object> uploadFile(FileUploadParams params) {
		Resource resource = null;
		if (params.getFileName().equals("1.dwg")) {
			resource = resourceLoader.getResource("classpath:image/1.jpg");
		} else if (params.getFileName().equals("A-CAD.dwg")) {
			resource = resourceLoader.getResource("classpath:image/A-CAD.jpg");
		}
		if (null != resource && resource.exists()) {
			String contentType = "image/jpeg"; // 默认内容类型
			params.setFileName(resource.getFilename());
			try (InputStream inputStream = resource.getInputStream()) {
                MessageDigest md5Digest = null;
                try {
                    md5Digest = MessageDigest.getInstance("MD5");
                } catch (NoSuchAlgorithmException e) {
                    throw new RuntimeException(e);
                }
                // 使用DigestInputStream自动计算摘要
				DigestInputStream dis = new DigestInputStream(inputStream, md5Digest);
				// 读取资源内容到字节数组
				byte[] fileBytes = StreamUtils.copyToByteArray(dis);

				// 获取MD5哈希值
				byte[] digest = md5Digest.digest();
				String md5Hash = bytesToHex(digest);

				// 设置MD5到参数对象
				params.setFileMd5(md5Hash);

				// 创建MultipartFile对象
				MultipartFile multipartFile = new MockMultipartFile(
                        Objects.requireNonNull(resource.getFilename()),         // 文件名
						resource.getFilename(),        // 原始文件名
						contentType,                   // 内容类型
						fileBytes                      // 文件内容
				);
				// 设置到参数中
				params.setFile(multipartFile);
			} catch (IOException e) {
				// 异常处理，返回错误信息
				return Collections.singletonMap("error", "文件读取失败");
			}
		}
		return fileUploadService.uploadFile(new FileUpload(), params);
	}

	/**
	 * 将字节数组转换为十六进制字符串
	 */
	private static String bytesToHex(byte[] bytes) {
		StringBuilder hexString = new StringBuilder();
		for (byte b : bytes) {
			String hex = Integer.toHexString(0xff & b);
			if (hex.length() == 1) {
				hexString.append('0');
			}
			hexString.append(hex);
		}
		return hexString.toString();
	}
	/**
	 * 下载文件
	 */
	@RequestMapping(value = "/download/{fileUploadId}")
	public String downloadFile(@PathVariable("fileUploadId") String fileUploadId, String preview, HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		FileUpload fileUpload = fileUploadService.getFile(new FileUpload(fileUploadId));
		return fileUploadService.downloadFile(fileUpload, preview, request, response);
	}

	/**
	 * 获取文件列表
	 * @param fileUpload bizKey 和 bizType 为必填参数
	 * @param bizKeyIsLike 是否对 bizKey 使用 RightLike 右模糊查询
	 */
	@RequestMapping(value = "fileList")
	@ResponseBody
	public String getFileList(FileUpload fileUpload, Boolean bizKeyIsLike) {
		return fileUploadService.getFileList(fileUpload, bizKeyIsLike);
	}

}