package com.jeesite.modules.sys.entity;

public class OfficeEstablishmentInfo {
    // 省级正职人员数量
    private int provincialLeaderCount = 0;

    // 省级副职人员数量
    private int provincialDeputyLeaderCount = 0;

    // 正厅（局）级人员数量
    private int directorLevelCount = 0;

    // 副厅（局级）人员数量
    private int deputyDirectorLevelCount = 0;

    // 正处级人员数量
    private int positiveDepartmentLevelCount = 0;

    // 副处级人员数量
    private int deputyDepartmentLevelCount = 0;

    // 处级以下人员数量
    private int belowDepartmentLevelCount = 0;

    // 编制总数
    private int totalStaffNumber = 0;

    public int getProvincialLeaderCount() {
        return provincialLeaderCount;
    }

    public void setProvincialLeaderCount(int provincialLeaderCount) {
        this.provincialLeaderCount = provincialLeaderCount;
    }

    public int getProvincialDeputyLeaderCount() {
        return provincialDeputyLeaderCount;
    }

    public void setProvincialDeputyLeaderCount(int provincialDeputyLeaderCount) {
        this.provincialDeputyLeaderCount = provincialDeputyLeaderCount;
    }

    public int getDirectorLevelCount() {
        return directorLevelCount;
    }

    public void setDirectorLevelCount(int directorLevelCount) {
        this.directorLevelCount = directorLevelCount;
    }

    public int getDeputyDirectorLevelCount() {
        return deputyDirectorLevelCount;
    }

    public void setDeputyDirectorLevelCount(int deputyDirectorLevelCount) {
        this.deputyDirectorLevelCount = deputyDirectorLevelCount;
    }

    public int getPositiveDepartmentLevelCount() {
        return positiveDepartmentLevelCount;
    }

    public void setPositiveDepartmentLevelCount(int positiveDepartmentLevelCount) {
        this.positiveDepartmentLevelCount = positiveDepartmentLevelCount;
    }

    public int getDeputyDepartmentLevelCount() {
        return deputyDepartmentLevelCount;
    }

    public void setDeputyDepartmentLevelCount(int deputyDepartmentLevelCount) {
        this.deputyDepartmentLevelCount = deputyDepartmentLevelCount;
    }

    public int getBelowDepartmentLevelCount() {
        return belowDepartmentLevelCount;
    }

    public void setBelowDepartmentLevelCount(int belowDepartmentLevelCount) {
        this.belowDepartmentLevelCount = belowDepartmentLevelCount;
    }

    public int getTotalStaffNumber() {
        return totalStaffNumber;
    }

    public void setTotalStaffNumber(int totalStaffNumber) {
        this.totalStaffNumber = totalStaffNumber;
    }
}
