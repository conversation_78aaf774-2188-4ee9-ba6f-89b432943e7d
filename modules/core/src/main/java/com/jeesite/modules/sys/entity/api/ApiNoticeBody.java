package com.jeesite.modules.sys.entity.api;

import java.util.List;

public class ApiNoticeBody {
    private String type; // 类型（0：公告 1：公示）
    private String title; // 标题
    private String publishTime; // 发布时间（yyyy-MM-dd HH:mm:ss格式）
    private String publishDeptName; // 发布单位
    private String noticeContent; // 富文本内容（如有嵌入图片内容，传图片访问互联网链接地址）
    private List<String> upLoadFileList; // 文件上传标识集合（调用6.2.2接口返回值）

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }

    public String getPublishDeptName() {
        return publishDeptName;
    }

    public void setPublishDeptName(String publishDeptName) {
        this.publishDeptName = publishDeptName;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public List<String> getUpLoadFileList() {
        return upLoadFileList;
    }

    public void setUpLoadFileList(List<String> upLoadFileList) {
        this.upLoadFileList = upLoadFileList;
    }
}
