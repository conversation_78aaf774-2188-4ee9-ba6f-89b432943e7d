//package com.jeesite.modules.exception;
//
//import com.jeesite.common.service.ServiceException;
//import io.swagger.annotations.ApiResponse;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//
//@RestControllerAdvice
//public class GlobalExceptionHandler {
//
//    @ExceptionHandler(ServiceException.class)
//    @ResponseBody
//    public ResponseEntity<GlobalResponse> handleServiceException(ServiceException ex) {
//        // 返回200状态码，但封装业务错误信息
//        GlobalResponse response = new GlobalResponse();
//        response.setResult("false");
//        response.setMessage(ex.getMessage());
//        return ResponseEntity
//            .ok(response);
//    }
//
//    @ExceptionHandler(Exception.class)
//    @ResponseBody
//    public ResponseEntity<GlobalResponse> handleException(Exception ex) {
//        // 返回200状态码，但封装业务错误信息
//        GlobalResponse response = new GlobalResponse();
//        ex.printStackTrace();
//        response.setResult("false");
//        response.setMessage("系统异常，请联系管理员");
//        return ResponseEntity
//                .ok(response);
//    }
//
//}
