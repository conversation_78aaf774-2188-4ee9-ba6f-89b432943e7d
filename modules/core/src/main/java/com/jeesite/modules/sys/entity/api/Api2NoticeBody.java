package com.jeesite.modules.sys.entity.api;

import com.alibaba.fastjson.JSONObject;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class Api2NoticeBody {

    private String msgId;
    private String systemId;
    private String msgType;
    private String msgSource;
    private String topic;
    private String content;
    private String fileIds;
    private String publishUnit;
    private String publishUnitId;
    private Date publishTime;

    private String receiveUserIds;
    private String receiveOrgIds;

    public String getReceiveOrgIds() {
        return receiveOrgIds;
    }

    public void setReceiveOrgIds(String receiveOrgIds) {
        this.receiveOrgIds = receiveOrgIds;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getMsgSource() {
        return msgSource;
    }

    public void setMsgSource(String msgSource) {
        this.msgSource = msgSource;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFileIds() {
        return fileIds;
    }

    public void setFileIds(String fileIds) {
        this.fileIds = fileIds;
    }

    public String getPublishUnit() {
        return publishUnit;
    }

    public void setPublishUnit(String publishUnit) {
        this.publishUnit = publishUnit;
    }

    public String getPublishUnitId() {
        return publishUnitId;
    }

    public void setPublishUnitId(String publishUnitId) {
        this.publishUnitId = publishUnitId;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getReceiveUserIds() {
        return receiveUserIds;
    }

    public void setReceiveUserIds(String receiveUserIds) {
        this.receiveUserIds = receiveUserIds;
    }
}
