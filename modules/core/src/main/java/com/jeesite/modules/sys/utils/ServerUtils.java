package com.jeesite.modules.sys.utils;

import cn.hutool.http.HttpRequest;
import com.jeesite.common.config.Global;

import javax.servlet.http.HttpServletRequest;

/**
 * Server related utility methods
 */
public class ServerUtils {
    
    /**
     * Check if server is internal (0) or external (1)
     * @param request
     * @return "0" for internal, "1" for external
     */
    public static String checkServerType(HttpServletRequest request) {
        String serverName = request.getServerName();
        if (serverName == null) {
            return setGlobalExternal("1");
        }
        // Get configured internal servers
        String internalServers = Global.getConfig("server.internal");
        for (String s : internalServers.split(",")) {
            if (serverName.equals(s.trim())) {
                return setGlobalExternal("0");
            }
        }
        
        // Get configured external servers
        String externalServers = Global.getConfig("server.external");
        for (String s : externalServers.split(",")) {
            if (serverName.equals(s.trim())) {
                return setGlobalExternal("1");
            }
        }
        
        // Default to external if not matched
        return setGlobalExternal("1");
    }

    private static String setGlobalExternal(String flag) {
        String externalUrl = Global.getConfig("tianditu.baseInternetUrl");
        String internalUrl = Global.getConfig("tianditu.baseUrl");
        if (flag.equals("1")){
            Global.updateProperty("tianditu.myurl", externalUrl);
        } else {
            Global.updateProperty("tianditu.myurl", internalUrl);
        }
        return flag;
    }
}
