<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.hsobs</groupId>
		<artifactId>hsobs-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath>../../parent/pom.xml</relativePath>
	</parent>
	
	<artifactId>hsobs-module-core</artifactId>
	<packaging>jar</packaging>
	
	<properties>
	
	</properties>
	
	<dependencies>

		<!-- MySQL -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- Oracle 11g -->
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc6</artifactId>
			<version>11.2.0.3</version>
			<scope>runtime</scope>
		</dependency>
		<!-- Oracle 12c 及以上版本
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<scope>runtime</scope>
		</dependency> -->

		<!-- SqlServer 2008
		<dependency>
			<groupId>net.sourceforge.jtds</groupId>
			<artifactId>jtds</artifactId>
			<scope>runtime</scope>
		</dependency> -->
		<!-- SqlServer 2012 及以上版本 -->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- PostgreSQL -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- H2 DB
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>runtime</scope>
		</dependency> -->

		<!-- 达梦数据库 -->
		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>Dm8JdbcDriver18</artifactId>
			<version>8.1.1.49</version>
		</dependency>

		<!-- 人大金仓数据库
		<dependency>
			<groupId>com.kingbase</groupId>
			<artifactId>kingbasejdbc8</artifactId>
			<version>8.6.0</version>
		</dependency> -->

		<!-- Web Server -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<!--<artifactId>spring-boot-starter-undertow</artifactId>-->
		</dependency>

		<!-- Common -->
		<dependency>
			<groupId>com.hsobs</groupId>
			<artifactId>hsobs-common</artifactId>
			<version>${project.parent.version}</version>
		</dependency>

		<!-- Framework -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-framework</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>

		<!-- Data/Dynamic Base Manage -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-framework-dbm</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>

		<!-- Core Static -->
		<dependency>
			<groupId>com.hsobs</groupId>
			<artifactId>hsobs-module-static</artifactId>
			<version>${project.parent.version}</version>
		</dependency>

		<!-- CAS 单点登录模块 -->
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-cas</artifactId>
			<version>${project.jeesite.version}</version>
		</dependency>

		<!-- ELK 日志收集 -->
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
			<version>${logstash-logback.version}</version>
		</dependency>
        <dependency>
            <groupId>com.jeesite</groupId>
            <artifactId>jeesite-framework-bpm</artifactId>
            <version>5.9.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>com.jeesite</groupId>
			<artifactId>jeesite-module-bpm</artifactId>
			<version>5.9.1-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
        <dependency>
            <groupId>com.jeesite</groupId>
            <artifactId>jeesite-module-swagger</artifactId>
            <version>5.9.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.18</version>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-crypto</artifactId>
			<version>5.8.18</version>
		</dependency>

    </dependencies>

	
</project>
