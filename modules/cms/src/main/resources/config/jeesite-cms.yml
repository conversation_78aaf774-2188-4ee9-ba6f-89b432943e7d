# 温馨提示：不建议直接修改此文件，为了平台升级方便，建议将需要修改的参数值，复制到application.yml里进行覆盖该参数值。

cms:
  pageCache:
    # 是否开启页面静态化缓存
    #enabled: true
    # 缓存名称标识
    cacheName: cmsPageCache
    # 拦截的网页地址
    urlPatterns: ${frontPath}/*
    # 只静态化 .html 后缀的网页
    urlSuffixes: .html

j2cache:
  caffeine:
    region:
      # 设置缓存的超期时间，默认 7天，根据需要可以设置更久。
      cmsPageCache: 100000, 7d

#spring:
#  elasticsearch:
#    enabled: true
#    uris: http://Win11:9200
#    connection-timeout: 120s
