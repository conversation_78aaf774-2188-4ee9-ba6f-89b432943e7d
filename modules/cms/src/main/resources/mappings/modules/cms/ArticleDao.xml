<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeesite.modules.cms.dao.ArticleDao">
	
	<!-- 查询数据 -->
	<select id="findList" resultType="Article">
		SELECT ${sqlMap.column.toSql()}
			<if test="isQueryArticleData">,
				ad.content AS "articleData.content",
				ad.relation AS "articleData.relation",
				ad.is_can_comment AS "articleData.isCanComment"
			</if>
		FROM ${sqlMap.table.toSql()}
		<if test="isQueryArticleData">
			JOIN ${_prefix}cms_article_data ad ON ad.id = a.id
		</if>
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select>
	
	<update id="updateExpiredWeight">
		UPDATE ${_prefix}cms_article SET weight = 0
		WHERE weight &gt; 0 AND weight_date &lt; #{weightDate}
	</update>
	
	<update id="updateHitsAddOne">
		UPDATE ${_prefix}cms_article SET
			 hits = hits + 1
		WHERE id = #{id}
	</update>

	<select id="getHits" resultType="long">
		SELECT hits FROM ${_prefix}cms_article
		WHERE id = #{id}
	</select>
	
</mapper>