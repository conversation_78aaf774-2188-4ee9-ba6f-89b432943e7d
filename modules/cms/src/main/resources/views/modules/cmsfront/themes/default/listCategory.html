<% layout('layouts/default.html', {title: '列表页面', libs: []}){ %>
<% include('include/banner.html'){} %>
<div class="row main main-list">
	<div class="col-sm-2 col-xs-12 main-left">
		<h4>栏目列表</h4>
		<ul class="article-list">
			<#html:foreach items="${qmark(categoryList! != null, categoryList!, categoryList(site.siteCode, category.parentCode, 50, ''))}" var="category,status">
				<li><a href="${category.url}" target="${category.target}" >${category.categoryName}</a></li>
			</#html:foreach>
		</ul>
	</div>
	<div class="col-sm-10 col-xs-12 main-right">
		<h4>${category.categoryName}</h4>
		<div class="row"><br/>
			<#html:foreach items="${qmark(categoryList! != null, categoryList!, [])}" var="category,status">
				<#html:if test="${category.moduleType == 'article'}">
					<div class="col-lg-6">
						<div class="bs-component">
							<div class="panel panel-default">
								<div class="panel-heading">
									<h3 class="panel-title"><small><a href="${category.url}" class="pull-right more">更多&gt;&gt;</a></small>${category.categoryName}</h3>
								</div>
								<div class="panel-body">
									<ul class="article-list">
										<#html:foreach items="${articleList(site.siteCode, category.categoryCode, 5)}" var="article,status">
											<li>${status.index}. &nbsp; <span class="pull-right">${article.updateDate,'yyyy-MM-dd'}</span>
												<a href="${article.url}" style="color:${article.color}">${abbr(article.title,28)}</a></li>
										</#html:foreach>
									</ul>
								</div>
								<div class="panel-footer"></div>
							</div>
						</div>
					</div>
				</#html:if>
			</#html:foreach>
		</div>
	</div>
</div>
<% } %>
