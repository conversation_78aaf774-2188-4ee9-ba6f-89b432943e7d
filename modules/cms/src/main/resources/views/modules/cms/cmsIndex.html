<% layout('/layouts/default.html', {title: '内容管理', libs:['layout','zTree']}){ %>
<div class="ui-layout-west">
	<div class="main-content">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title dropdown input-inline">
					<div class="dropdown-toggle" data-hover="dropdown" data-toggle="dropdown">
						${@CmsUtils.getCurrentSite().getSiteName()}<b class="caret"></b>
					</div>
					<ul class="dropdown-menu">
						<% for(var site in @CmsUtils.getSiteList()){ %>
						<li><a href="${ctx}/cms/site/select?siteCode=${site.siteCode}&redirect=${@Global.getProperty('adminPath')}/cms/index"> <i
								class="fa fa-angle-right"></i> ${site.siteName}
						</a></li>
						<% } %>
					</ul>
				</div>
				<div class="box-tools pull-right">
					<button type="button" class="btn btn-box-tool addTabPage" data-href="${ctx}/cms/category/list" title="${text('站点管理')}"><i class="fa fa-edit"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnExpand" title="${text('展开')}" style="display:none;"><i class="fa fa-chevron-up"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnCollapse" title="${text('折叠')}"><i class="fa fa-chevron-down"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnRefresh" title="${text('刷新')}"><i class="fa fa-refresh"></i></button>
				</div>
			</div>
			<div class="ui-layout-content">
				<div id="tree" class="ztree"></div>
			</div>
		</div>
	</div>
</div>
<div class="ui-layout-center">
	<iframe id="mainFrame" name="mainFrame" class="ui-layout-content p0"
		src="${ctx}/cms/article/list"></iframe>
</div>
<% } %>
<script>
//# // 初始化布局
$('body').layout({
	west__size : 180
});
//# // 主页框架
var win = $("#mainFrame")[0].contentWindow;
//# // 树结构初始化加载
var setting = {
	view : {selectedMulti : false},
	data : {key : {title : "title"}, simpleData : {enable : true}},
	callback : {
		onClick : function(event, treeId, treeNode) {
			var adminUrl = (treeNode.adminUrl && treeNode.adminUrl != '' ? treeNode.adminUrl : '');
			adminUrl += (adminUrl.indexOf('?') == -1 ? '?' : '&');
			// 如果栏目展现方式，是展现第一条内容（3：简介类栏目，栏目第一条内容），则加上显示模式条件。
			adminUrl += (treeNode.showModes == "3" && treeNode.module == "article") ? '&category.showModes=3' : '';
			// 设置刷新内容管理列表
			var ifr = $("#mainFrame"), src = ifr.attr("src");
			if (src.indexOf("://") == -1 // 不是外部链接
				&& src.indexOf("category.showModes=3") == -1 // 原地址中，不是展现的栏目第一条内容
				&& adminUrl.indexOf("category.showModes=3") == -1 // 新地址中，不是展现的栏目第一条内容
				&& ((src.indexOf("article") > 0 && adminUrl.indexOf("article") > 0)
					|| (src.indexOf("link") > 0 && adminUrl.indexOf("link") > 0))){
				var win = ifr[0].contentWindow, conts = ifr.contents();
				conts.find('input[type=reset]').click();
				conts.find('#categoryCode').val(treeNode.id);
				conts.find('#outline').val(adminUrl.indexOf("outline=true") != -1); // 文章模型是否显示大纲视图
				conts.find('#fileDown').val(adminUrl.indexOf("fileDown=true") != -1); // 链接模型是否是下载栏目
				var caption = conts.find('.portlet-title .caption');
				caption.html(caption.find('i').prop("outerHTML") + " " + treeNode.name);
				win.page();
			}else{
				$('#mainFrame').attr("src", adminUrl);
			}
		}
	}
}, tree, loadTree = function() {
	js.ajaxSubmit("${ctx}/cms/category/treeData?___t="
			+ new Date().getTime(), { }, function(data) {
		tree = $.fn.zTree.init($("#tree"), setting, data);
		var level = -1, nodes;
		while (++level <= 1) {
			nodes = tree.getNodesByParam("level", level);
			if (nodes.length > 10) { break; }
			for(var i=0; i<nodes.length; i++) {
	 			tree.expandNode(nodes[i], true, false, false);
			}
		}
	}, null, null, js.text('loading.message'));
};
loadTree();
//# // 工具栏按钮绑定
$('#btnExpand').click(function() {
	tree.expandAll(true);
	$(this).hide();
	$('#btnCollapse').show();
});
$('#btnCollapse').click(function() {
	tree.expandAll(false);
	$(this).hide();
	$('#btnExpand').show();
});
$('#btnRefresh').click(function() {
	loadTree();
});
//调用子页分页函数
function page() {
	win.page();
}
</script>
