<% layout('/layouts/default.html', {title: '栏目管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
 				<i class="fa icon-organization"></i> ${text('栏目管理')}（
			</div>
			<div class="box-title dropdown input-inline">
				<div class="dropdown-toggle" data-hover="dropdown" data-toggle="dropdown">
					${@CmsUtils.getCurrentSite().getSiteName()}<b class="caret"></b>
				</div>
				<ul class="dropdown-menu">
					<% for(var site in @CmsUtils.getSiteList()){ %>
					<li><a href="${ctx}/cms/site/select?siteCode=${site.siteCode}&redirect=${@Global.getProperty('adminPath')}/cms/category/list"> <i
							class="fa fa-angle-right"></i> ${site.siteName}
					</a></li>
					<% } %>
				</ul>
			</div>
			<div class="box-title">）</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i>${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="${text('刷新')}"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="${text('展开一级')}"><i class="fa fa-angle-double-down"></i>${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a> <%
				if(hasPermi('cms:category:edit')){ %>
					<a href="${ctx}/cms/category/form?site.siteCode=${category.site.siteCode}" class="btn btn-default btnTool" title="${text('栏目添加')}"><i class="fa fa-plus"></i>${text('新增')}</a>
				<% } %>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${category}" action="${ctx}/cms/category/listData" method="post" class="form-inline "
				data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<#form:hidden path="site.siteCode" maxlength="64" class="form-control width-120" />
				<div class="form-group">
					<label class="control-label">${text('栏目名称')}：</label>
					<div class="control-inline">
						<#form:input path="categoryName" maxlength="100" class="form-control width-120" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('模块类型')}：</label>
					<div class="control-inline">
						<#form:input path="moduleType" maxlength="50" class="form-control width-120" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注信息')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120" />
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("名称")}', name:'categoryName', index:'a.category_name', width:250, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '( '+row.categoryCode+' ) '+'<a href="${ctx}/cms/category/form?categoryCode='+row.categoryCode+'" class="hsBtnList" data-title="${text("编辑栏目表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("模型")}', name:'moduleType', index:'a.module_type', width:100, align:"center"},
		{header:'${text("排序")}', name:'treeSort', index:'a.tree_sort', width:50, align:"center"},
		{header:'${text("导航栏目")}', name:'inMenu', index:'a.in_menu', width:80, fixed:true, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_show_hide')}", val, '未知', true);
		}},
		{header:'${text("栏目列表")}', name:'inList', index:'a.in_list', width:80, fixed:true, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_show_hide')}", val, '未知', true);
		}},
		{header:'${text("展现方式")}', name:'showModes', index:'a.show_modes', width:150, fixed:true, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('cms_show_modes')}", val, '未知', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('cms:category:edit')){
				actions.push('<a href="${ctx}/cms/category/form?categoryCode='+row.categoryCode+'" class="hsBtnList" title="${text("编辑栏目表")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/cms/category/disable?categoryCode='+row.categoryCode+'" class="btnList" title="${text("停用栏目表")}" data-confirm="${text("确认要停用该栏目表吗？")}">停用</a>&nbsp;');
				}
				if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/cms/category/enable?categoryCode='+row.categoryCode+'" class="btnList" title="${text("启用栏目表")}" data-confirm="${text("确认要启用该栏目表吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/cms/category/delete?categoryCode='+row.categoryCode+'" class="btnList" title="${text("删除栏目表")}" data-confirm="${text("确认要删除该栏目表及所有子栏目表吗？")}" data-deltreenode="'+row.id+'">删除</a>&nbsp;');
				actions.push('<a href="${ctx}/cms/category/form?parentCode='+row.id+'&site.siteCode=${category.site.siteCode}" class="hsBtnList" title="${text("新增下级栏目表")}"><i class="fa fa-plus-square"></i></a>&nbsp;');
				//# if(hasPermi('cms:category:rebuildIndex')){
				actions.push('<a href="${ctx}/cms/category/rebuildIndex?categoryCode='+row.categoryCode+'" class="hsBtnList" title="${text("重建该栏目索引")}" data-confirm="${text("确认重建该栏目下文章索引吗")}？"><i class="fa fa-crosshairs"></i></a>&nbsp;');
				//# }
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctxFront}/list-'+row.categoryCode+'" target="_blank" title="${text("访问栏目")}"><i class="fa fa-globe"></i></a>&nbsp;');
				}
			//# }
			return actions.join('');
		}}
	],
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'categoryName,siteCode,moduleType,image,href,target,keywords,description,inMenu,inList,showModes,isNeedAudit,isCanComment,customListView,customContentView,viewConfig,status,remarks,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>