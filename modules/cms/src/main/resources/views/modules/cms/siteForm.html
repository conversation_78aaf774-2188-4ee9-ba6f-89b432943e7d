<% layout('/layouts/default.html', {title: '站点管理', libs: ['validate','fileupload','ueditor','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa fa-list-alt"></i> ${text(site.isNewRecord ? '新增站点' : '编辑站点')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${site}" action="${ctx}/cms/site/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('站点名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="siteName" maxlength="100"  placeholder="默认站点" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('站点编码')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:hidden path="isNewRecord" />
								<#form:input path="siteCode" maxlength="64" readonly="${!site.isNewRecord}"
									class="form-control required abc" />
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('站点域名')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="domain" maxlength="500" class="form-control" placeholder="www.jeesite.com"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('站点排序')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="siteSort" maxlength="500" class="form-control required digits"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required ">*</span> ${text('站点标题')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:input path="title" maxlength="100"  placeholder="演示站点"  class="form-control required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">${text('站点logo')}</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadLogo" bizKey="${site.id}" bizType="site_logo" returnPath="true"
									filePathInputId="logo" uploadType="image" readonly="false" maxUploadNum="1" isMini="false"/>
								<#form:input path="logo" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('详细信息')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('描述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="description" maxlength="500" rows="3" placeholder="填写描述，有助于搜索引擎优化"  class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('关键字')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:input path="keywords" maxlength="500"  placeholder="填写关键词，通过,或|分割，有助于搜索引擎优化" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('主题风格')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="theme" dictType="cms_theme"  class="form-control" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('首页视图')}：<i class="fa icon-question " title='自定义首页视图名称必须以"index"开始'></i></label>
							<div class="col-sm-8">
								<#form:select path="customIndexView" items="${indexViewList}" itemLabel="id" itemValue="id" class="form-control " />
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required ">*</span> ${text('版权信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
							<#form:ueditor name="copyright" path="copyright" maxlength="10000" height="200" class="required"
							    simpleToolbars="false" readonly="false" outline="false"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('其他信息')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('cms:site:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>