<% layout('/layouts/default.html', {title: '内容举报表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa fa-list-alt"></i> ${text('内容举报表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('cms:report:edit')){ %>
					<a href="${ctx}/cms/report/form" class="btn btn-default btnTool" title="${text('新增内容举报表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${report}" action="${ctx}/cms/report/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('举报来源')}：</label>
					<div class="control-inline">
						<#form:input path="reportSource" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('举报内容')}：</label>
					<div class="control-inline">
						<#form:input path="reportContent" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('举报的URL')}：</label>
					<div class="control-inline">
						<#form:input path="reportUrl" maxlength="1000" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('举报类型')}：</label>
					<div class="control-inline">
						<#form:input path="reportType" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('举报原因')}：</label>
					<div class="control-inline">
						<#form:input path="reportCause" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text('举报来源')}', name:'reportSource', index:'a.report_source', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/cms/report/form?id='+row.id+'" class="hsBtnList" data-title="${text('编辑内容举报表')}">'+(val||row.id)+'</a>';
		}},
		{header:'${text('举报内容')}', name:'reportContent', index:'a.report_content', width:150, align:"left"},
		{header:'${text('举报的URL')}', name:'reportUrl', index:'a.report_url', width:150, align:"left"},
		{header:'${text('举报类型')}', name:'reportType', index:'a.report_type', width:150, align:"left"},
		{header:'${text('举报原因')}', name:'reportCause', index:'a.report_cause', width:150, align:"left"},
		{header:'${text('操作')}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('cms:report:edit')){
				actions.push('<a href="${ctx}/cms/report/form?id='+row.id+'" class="hsBtnList" title="${text('编辑内容举报表')}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/cms/report/disable?id='+row.id+'" class="btnList" title="${text('停用内容举报表')}" data-confirm="${text('确认要停用该内容举报表吗？')}">停用</a>&nbsp;');
				}
				if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/cms/report/enable?id='+row.id+'" class="btnList" title="${text('启用内容举报表')}" data-confirm="${text('确认要启用该内容举报表吗？')}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/cms/report/delete?id='+row.id+'" class="btnList" title="${text('删除内容举报表')}" data-confirm="${text('确认要删除该内容举报表吗？')}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>