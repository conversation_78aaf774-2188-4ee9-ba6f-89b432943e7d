<% layout('/layouts/default.html', {title: '站点模版',libs:['dataGrid','layout','zTree']}){ %>

<div class="ui-layout-west">
	<div class="main-content">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title">
					<i class="fa icon-grid"></i> ${text('模板目录')}
				</div>
				<div class="box-tools pull-right">
					<button type="button" class="btn btn-box-tool" id="btnExpand"
						title="${text('展开')}" style="display: none;">
						<i class="fa fa-chevron-up"></i>
					</button>
					<button type="button" class="btn btn-box-tool" id="btnCollapse"
						title="${text('折叠')}">
						<i class="fa fa-chevron-down"></i>
					</button>
					<button type="button" class="btn btn-box-tool" id="btnRefresh"
						title="${text('刷新')}">
						<i class="fa fa-refresh"></i>
					</button>
				</div>
			</div>
			<div class="ui-layout-content">
				<div id="tree" class="ztree"></div>
			</div>
		</div>
	</div>
</div>
<div class="ui-layout-center">
	<iframe id="mainFrame" name="mainFrame" class="ui-layout-content p0"
		src="${ctx}/cms/template/help"></iframe>
</div>
<% } %>
<script>
//# // 初始化布局
$('body').layout({
	west__size : 300
});
//# // 主页框架
var win = $("#mainFrame")[0].contentWindow;
//# // 树结构初始化加载
var setting = {
	view : { selectedMulti : false },
	data : { key : { title : "title" }, simpleData : { enable : true } },
	callback : {
		onClick : function(event, treeId, treeNode) {
			tree.expandNode(treeNode);
			if (!treeNode.isDirectory) {
				window.open("${ctx}/cms/template/form?name=" + treeNode.id, "mainFrame");
			}

		}
	}
}, tree, loadTree = function() {
	js.ajaxSubmit("${ctx}/cms/template/treeData?___t=" + new Date().getTime(), function(data) {
		tree = $.fn.zTree.init($("#tree"), setting, data);
		var level = -1, nodes;
		while (++level <= 1) {
			nodes = tree.getNodesByParam("level", level);
			if (nodes.length > 10) { break; }
			for(var i=0; i<nodes.length; i++) {
	 			tree.expandNode(nodes[i], true, false, false);
			}
		}
	}, null, null, js.text('loading.message'));
};
loadTree();
//# // 工具栏按钮绑定
$('#btnExpand').click(function() {
	tree.expandAll(true);
	$(this).hide();
	$('#btnCollapse').show();
});
$('#btnCollapse').click(function() {
	tree.expandAll(false);
	$(this).hide();
	$('#btnExpand').show();
});
$('#btnRefresh').click(function() {
	loadTree();
});
//调用子页分页函数
function page() {
	win.page();
}
</script>
