<% layout('/layouts/default.html', {title: '模板内容', libs: ['validate','ueditor']}){%>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa fa-list-alt"></i> ${text('模板内容')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool"
					data-widget="collapse">
					<i class="fa fa-minus"></i>
				</button>
			</div>
		</div>
		<#form:form id="inputForm" model="${template}" class="form-horizontal2">
			<div class="box-body">
				<div class="row">
					<div class="col-xs-12">
						<div class="input-group mb10">
							<span class="input-group-addon text-right">&nbsp;${text('文件名')}：</span>
							<#form:input value="${template.filePath}/${template.fileName}"
								name="fileName" maxlength="255" class="form-control required nofocus" />
		                </div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<#form:textarea path="fileContent" rows="23"
							encodeHtml="true" maxlength="990000"
							class="form-control autoHeight" />
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% /*if (hasPermi('cms:template:edit')){ %>
 							<button type="button" class="btn btn-sm btn-primary" id="btnSave"><i class="fa fa-check"></i> ${text('保 存')}</button> &nbsp;
 							<button type="button" class="btn btn-sm btn-danger" id="btnDelete">删除 ${text('删 除')}</button> &nbsp;
						<% }*/ %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<% /*if (hasPermi('cms:template:edit')){ %>
<script>
	$('#btnSave').click(function(){
		js.confirm('${text("确认要保存当前模板吗？")}', function(){
			js.ajaxSubmit("${ctx}/cms/template/saveFileTemplate", {
				fileName: $("#fileName").val(),
				fileContent: Base64.encode($("#fileContent").val())
			}, function(data) {
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					window.self.parent.loadTree();
				}
			});
		});
	});
	$('#btnDelete').click(function(){
		js.confirm('${text("确认要删除当前模板吗？")}', function(){
			js.ajaxSubmit("${ctx}/cms/template/deleteFileTemplate", {
				fileName: $("#fileName").val()
			}, function(data) {
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					window.self.parent.loadTree();
				}
			});
		});
	});
</script>
<% }*/ %>
