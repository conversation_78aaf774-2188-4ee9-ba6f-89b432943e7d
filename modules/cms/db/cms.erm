<?xml version="1.0" encoding="UTF-8"?>
<diagram>
	<page_setting>
		<direction_horizontal>true</direction_horizontal>
		<scale>100</scale>
		<paper_size>A4 210 x 297 mm</paper_size>
		<top_margin>30</top_margin>
		<left_margin>30</left_margin>
		<bottom_margin>30</bottom_margin>
		<right_margin>30</right_margin>
	</page_setting>
	<category_index>0</category_index>
	<zoom>1.0</zoom>
	<x>330</x>
	<y>0</y>
	<default_color>
		<r>128</r>
		<g>128</g>
		<b>192</b>
	</default_color>
	<color>
		<r>255</r>
		<g>255</g>
		<b>255</b>
	</color>
	<font_name>Arial</font_name>
	<font_size>14</font_size>
	<settings>
		<database>StandardSQL</database>
		<capital>false</capital>
		<table_style></table_style>
		<notation></notation>
		<notation_level>0</notation_level>
		<notation_expand_group>true</notation_expand_group>
		<view_mode>2</view_mode>
		<outline_view_mode>1</outline_view_mode>
		<view_order_by>1</view_order_by>
		<auto_ime_change>false</auto_ime_change>
		<validate_physical_name>true</validate_physical_name>
		<use_bezier_curve>false</use_bezier_curve>
		<suspend_validator>false</suspend_validator>
		<export_setting>
			<export_ddl_setting>
				<output_path>db/cms.sql</output_path>
				<encoding>UTF-8</encoding>
				<line_feed>CR+LF</line_feed>
				<is_open_after_saved>false</is_open_after_saved>
				<environment_id>7be191506f9daa8070b3ac14921dffd44063d2bb</environment_id>
				<category_id>null</category_id>
				<ddl_target>
					<create_comment>true</create_comment>
					<create_foreignKey>false</create_foreignKey>
					<create_index>true</create_index>
					<create_sequence>false</create_sequence>
					<create_table>true</create_table>
					<create_tablespace>false</create_tablespace>
					<create_trigger>false</create_trigger>
					<create_view>false</create_view>
					<drop_index>false</drop_index>
					<drop_sequence>false</drop_sequence>
					<drop_table>false</drop_table>
					<drop_tablespace>false</drop_tablespace>
					<drop_trigger>false</drop_trigger>
					<drop_view>false</drop_view>
					<inline_column_comment>false</inline_column_comment>
					<inline_table_comment>true</inline_table_comment>
					<comment_value_description>false</comment_value_description>
					<comment_value_logical_name>true</comment_value_logical_name>
					<comment_value_logical_name_description>false</comment_value_logical_name_description>
					<comment_replace_line_feed>false</comment_replace_line_feed>
					<comment_replace_string></comment_replace_string>
				</ddl_target>
			</export_ddl_setting>
			<export_excel_setting>
				<category_id>null</category_id>
				<output_path>db/cms.xlsx</output_path>
				<template></template>
				<template_path></template_path>
				<used_default_template_lang>en</used_default_template_lang>
				<image_output></image_output>
				<is_open_after_saved>true</is_open_after_saved>
				<is_put_diagram>false</is_put_diagram>
				<is_use_logical_name>true</is_use_logical_name>
			</export_excel_setting>
			<export_html_setting>
				<output_dir>C:\Users\<USER>\Desktop\cms</output_dir>
				<with_category_image>true</with_category_image>
				<with_image>true</with_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_html_setting>
			<export_image_setting>
				<output_file_path>db/cms.png</output_file_path>
				<category_dir_path></category_dir_path>
				<with_category_image>true</with_category_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_image_setting>
			<export_java_setting>
				<java_output></java_output>
				<package_name></package_name>
				<class_name_suffix></class_name_suffix>
				<src_file_encoding></src_file_encoding>
				<with_hibernate>false</with_hibernate>
			</export_java_setting>
			<export_testdata_setting>
				<file_encoding></file_encoding>
				<file_path></file_path>
				<format>0</format>
			</export_testdata_setting>
		</export_setting>
		<category_settings>
			<free_layout>false</free_layout>
			<show_referred_tables>false</show_referred_tables>
			<categories>
			</categories>
		</category_settings>
		<translation_settings>
			<use>false</use>
			<translations>
			</translations>
		</translation_settings>
		<model_properties>
			<id></id>
			<height>-1</height>
			<width>-1</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>50</x>
			<y>50</y>
			<color>
				<r>255</r>
				<g>255</g>
				<b>255</b>
			</color>
			<connections>
			</connections>
			<display>false</display>
			<creation_date>2016-12-25 17:25:00</creation_date>
			<model_property>
				<name>Project Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Model Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Version</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Company</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Author</name>
				<value></value>
			</model_property>
		</model_properties>
		<table_properties>
			<schema></schema>
		</table_properties>
		<environment_setting>
			<environment>
				<id>7be191506f9daa8070b3ac14921dffd44063d2bb</id>
				<name>Default</name>
			</environment>
		</environment_setting>
	</settings>
	<dictionary>
		<word>
			<id>793b67ee171c7f094150910f3a7cd322ab4b1f9b</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>来源（转载/原创）</description>
			<logical_name>来源（转载/原创）</logical_name>
			<physical_name>article_source</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>0a87abc6a037a6fb06aefa07bd97708ed2771168</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>栏目内容的标题</description>
			<logical_name>内容标题</logical_name>
			<physical_name>article_title</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>b3c6d5be4f35caad8d24f4fa35207ab2c3f026dc</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>审核意见</logical_name>
			<physical_name>audit_comment</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>0db458281a9f843e54736d41a335e977f04d3ab8</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>审核时间</description>
			<logical_name>审核时间</logical_name>
			<physical_name>audit_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>55bb4192504f15f138af34b3324447da63250cb7</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>审核人</description>
			<logical_name>审核人</logical_name>
			<physical_name>audit_user_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>187469fddef22c4c96d63a3cc098b0ad7dbc3834</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>栏目编码</description>
			<logical_name>栏目编码</logical_name>
			<physical_name>category_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>bb1e56bd34be19cf654acc1fde6062dea4477f26</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>栏目名称</description>
			<logical_name>栏目名称</logical_name>
			<physical_name>category_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>0e17c0f08391aab8c32909b55ddc1d622e0a6c1a</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>点击次数</logical_name>
			<physical_name>clicknum</physical_name>
			<type>numeric(p)</type>
		</word>
		<word>
			<id>bb9df43be17fb1eb672ecc111218e42df2adbc75</id>
			<length>50</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>标题颜色</description>
			<logical_name>标题颜色</logical_name>
			<physical_name>color</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>d30fda93843661fe1fc8bae97b93472c56451bda</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>文章内容</description>
			<logical_name>文章内容</logical_name>
			<physical_name>content</physical_name>
			<type>longtext</type>
		</word>
		<word>
			<id>64b0c3e3ea90482c472a8be8136a3ee33c2eb525</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>留言内容</description>
			<logical_name>留言内容</logical_name>
			<physical_name>content</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>1827265e648182aa205807b8564e8f09189f84dd</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>评论内容</description>
			<logical_name>评论内容</logical_name>
			<physical_name>content</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>8737d86c1a567c3c3d03da8465d56ae6498a0915</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>栏目内容的编号</description>
			<logical_name>栏目内容编号</logical_name>
			<physical_name>content_id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>6f996ffe79e499c44fd24f140191ae038d68ba50</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>访问页面标题</description>
			<logical_name>访问页面标题</logical_name>
			<physical_name>content_title</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>04e0881c10e81677076ae9ebc0a0d5bc9c5e4b27</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>文章来源出处</description>
			<logical_name>文章来源出处</logical_name>
			<physical_name>copyfrom</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>10013cc4ca7ecccaa3d9d7683cdcb10464ab5a38</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>版权信息</description>
			<logical_name>版权信息</logical_name>
			<physical_name>copyright</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>c71533e4d2f429ff8466fd6a8de5719f1741377b</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>租户代码</description>
			<logical_name>租户代码</logical_name>
			<physical_name>corp_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>11c59294fe142d108ca4dac5a03033f50188a450</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>租户名称</description>
			<logical_name>租户名称</logical_name>
			<physical_name>corp_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>创建者</logical_name>
			<physical_name>create_by</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>170410257b4d8e712b8c1d499b415de82ce9683c</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>创建时间</logical_name>
			<physical_name>create_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>1de3007f27aa30d0771965c412938540311d3414</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>自定义内容视图</description>
			<logical_name>自定义内容视图</logical_name>
			<physical_name>custom_content_view</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b2f495ae45df19c844b61a29027c5f9657640950</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>自定义站点首页视图</description>
			<logical_name>自定义站点首页视图</logical_name>
			<physical_name>custom_index_view</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>ac819b1c0fe0316b06356855f3b570321e6517b8</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>自定义列表视图</description>
			<logical_name>自定义列表视图</logical_name>
			<physical_name>custom_list_view</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>adda41026df6c73bdcc35b7b9d2e29075a6f162f</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>描述、摘要</description>
			<logical_name>描述</logical_name>
			<physical_name>description</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>028e460308bd935c315b7c5cebe128cf60e616fd</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>站点域名</description>
			<logical_name>站点域名</logical_name>
			<physical_name>domain_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>c18af94646cf20d6d5001a6090f2a2a59eca9251</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>邮箱</description>
			<logical_name>邮箱</logical_name>
			<physical_name>email</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>038ea74a700b763fa1f8fcdbd2c27e7941f82f7a</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 1</logical_name>
			<physical_name>extend_d1</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>19aa2e40abff2bb4e908f18f5f5e5b30a074cd8b</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 2</logical_name>
			<physical_name>extend_d2</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>06f59428b50e62e636a58a2f3cbcee2c75764506</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 3</logical_name>
			<physical_name>extend_d3</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>8f08aeb4b9ceb072f0232d3d23fd5371ef8f379c</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 4</logical_name>
			<physical_name>extend_d4</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>0733a829c52af8af725ace50d4ed3e0179969f56</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 1</logical_name>
			<physical_name>extend_f1</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>0b7230df29d6df213507c8855d019d2d55c9a561</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 2</logical_name>
			<physical_name>extend_f2</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>6cb3dad83f3e1f358155892dcee3167ef55bacc7</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 3</logical_name>
			<physical_name>extend_f3</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>e54b261fc7ab81e0ef19a89e657453664abe5593</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 4</logical_name>
			<physical_name>extend_f4</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>b67071354a8d43cabe9a0ac29303f8b144a15985</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 1</logical_name>
			<physical_name>extend_i1</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>70b8240f049f658b97394b7d4784481fdb477f5c</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 2</logical_name>
			<physical_name>extend_i2</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>ce377c081415847061b2efc58b701ff847dcaaca</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 3</logical_name>
			<physical_name>extend_i3</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>fb96f6dc60d31576c278a3c64024154eda67d3fe</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 4</logical_name>
			<physical_name>extend_i4</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>3962f4b130a803841a193a163e42679946b5ae1f</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 JSON</logical_name>
			<physical_name>extend_json</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b1acb2df17677e0ed818dea6e1150bfc088fcd50</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 1</logical_name>
			<physical_name>extend_s1</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>d27290b84b207a7c87f4d78d80e62048c67fac04</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 2</logical_name>
			<physical_name>extend_s2</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>5e25d7f0e327d2d760a04b32140b639bb96bfd8d</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 3</logical_name>
			<physical_name>extend_s3</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>d05572af90653d4ddfd880402a15ed9b27c81888</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 4</logical_name>
			<physical_name>extend_s4</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>acc372d31709a1133f6c13fc8abfef7881ca26ed</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 5</logical_name>
			<physical_name>extend_s5</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>59763a16504e4136a7a2fbed38476272d337105c</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 6</logical_name>
			<physical_name>extend_s6</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>600b96b04bf4f65919eabfc6613d3c5e370931e1</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 7</logical_name>
			<physical_name>extend_s7</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>a9dc80e5c484dc7b9d5fff9294eaa383819edc26</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 8</logical_name>
			<physical_name>extend_s8</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>36fc46a5f5c941cccb3b1b823f1082511b9b6066</id>
			<length>20</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>首次访问时间戳（30分钟内）</description>
			<logical_name>首次访问时间戳（30分钟内）</logical_name>
			<physical_name>first_visit_time</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>a11ba8c2d68b98159077cd4ab9a31f0c8d50f07f</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>留言分类</description>
			<logical_name>留言分类</logical_name>
			<physical_name>gb_type</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>2a0199273650e7efb02228ace74e3a6ff9cf6156</id>
			<length>20</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>点击数</description>
			<logical_name>点击数</logical_name>
			<physical_name>hits</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>155a7eaacd5ace299e2377e9810e5a94a9f359c0</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>反对数</logical_name>
			<physical_name>hits_minus</physical_name>
			<type>numeric(p)</type>
		</word>
		<word>
			<id>8895e8a7aaf9eb10a3dd34076b8fb455030d8cf5</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>支持数</logical_name>
			<physical_name>hits_plus</physical_name>
			<type>numeric(p)</type>
		</word>
		<word>
			<id>ac509486994eb9ba67fa1bb1d0ea9bbeb3af9d49</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>外部链接</description>
			<logical_name>外部链接</logical_name>
			<physical_name>href</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>3b7edb7edb602cc39576f37efa2d2df33aaefe28</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>链接</description>
			<logical_name>链接</logical_name>
			<physical_name>href</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>编号</logical_name>
			<physical_name>id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>6935e308e57e9883c9d7039d494fb3d7f74d8276</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>内容图片</description>
			<logical_name>内容图片</logical_name>
			<physical_name>image</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>9a4bf3237fc2b018dbfa538a3829c2ffa8898e1e</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>栏目图片</description>
			<logical_name>栏目图片</logical_name>
			<physical_name>image</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>c538e72e1dffe92907500f45d502a124fe24e446</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否在分类页中显示列表</description>
			<logical_name>是否在分类页中显示列表</logical_name>
			<physical_name>in_list</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>b4e1d80ab20fa3b33fb05a7f8e84510591063718</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否在导航中显示</description>
			<logical_name>是否在导航中显示</logical_name>
			<physical_name>in_menu</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>3138f8d288350701cc88f8e7b35df4353f4d30ea</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>IP</description>
			<logical_name>IP</logical_name>
			<physical_name>ip</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>dede04c906e6cf3f9f49e48e118a07d93e8535bd</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>评论IP</description>
			<logical_name>评论IP</logical_name>
			<physical_name>ip</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>3e822f92a8f08754e2c713df0463297fa91d0f66</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否允许评论</description>
			<logical_name>是否允许评论</logical_name>
			<physical_name>is_can_comment</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>17ca518285184abccdb8e6854ccb9af3b4fa0f3a</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否需要审核</description>
			<logical_name>是否需要审核</logical_name>
			<physical_name>is_need_audit</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>632a14ff0c67075a0d31e4024cb14f0aaa3de02a</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>是否新访问（30分内）</description>
			<logical_name>是否新访问（30分内）</logical_name>
			<physical_name>is_new_visit</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>e536a1fd40fcde4fd0f6e6eee976148caca997b9</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>关键字（搜索引擎优化，5个关键词以内）</description>
			<logical_name>关键字</logical_name>
			<physical_name>keywords</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>a357603768870d14bdf1e2bb9736c227a5468301</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>站点Logo</description>
			<logical_name>站点Logo</logical_name>
			<physical_name>logo</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>e41be76c164ce528ca42bf4f6ae1ccfa35bf8b8a</id>
			<length>50</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>模块类型（none未定义、article文章、link链接、picture图片、video视频、down下载）</description>
			<logical_name>模块类型</logical_name>
			<physical_name>module_type</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>ba90244ccd39f9e26d44a0c67289cd5e32590840</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>姓名</description>
			<logical_name>姓名</logical_name>
			<physical_name>name</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>2f09198043d7f22ef84a72d15bd08f045db36c84</id>
			<length>50</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>评论姓名</description>
			<logical_name>评论姓名</logical_name>
			<physical_name>name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>e4465172e2cc64907a386237cc7d0c7d79a074dc</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>父级编号</logical_name>
			<physical_name>parent_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b4f9837136b0a84afc9a611a563fb51141fdac1f</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>所有父级编号</logical_name>
			<physical_name>parent_codes</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>54c72943c2269393a702a7d758bc5c183dc4938b</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>父级评论</logical_name>
			<physical_name>parent_id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>d053d0146a0bccb9c45ce73a19a93d5c79ba1f28</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>电话</description>
			<logical_name>电话</logical_name>
			<physical_name>phone</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>fd481873c467054b531d45fdd9fb45ba9c109afe</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>推荐位置（1轮播图 2首页推荐 3栏目页面）</description>
			<logical_name>推荐位置（1轮播图 2首页推荐 3栏目页面）</logical_name>
			<physical_name>postid</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>4f34963c5287c1b6e0c92e0d4aa162e020d4b696</id>
			<length>20</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>上页面停留时间（秒）</description>
			<logical_name>上页面停留时间（秒）</logical_name>
			<physical_name>prev_remain_time</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>6eb4e127fce70565dc8fa980598245bcd5747c7c</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>相关文章</description>
			<logical_name>相关文章</logical_name>
			<physical_name>relation</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>备注信息</logical_name>
			<physical_name>remarks</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>0a75802a3eb6922122abaeec5f326611a11b5e8e</id>
			<length>50</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>客户IP地址</description>
			<logical_name>客户IP地址</logical_name>
			<physical_name>remote_addr</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>6bf1199a39cfee8fb1e7f1e2ffc81aff24c2942f</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>举报原因</logical_name>
			<physical_name>report_cause</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>bd637d523962b7491c627f7881af1c639349cac2</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>内容简要</description>
			<logical_name>举报内容（文章标题 评论内容）</logical_name>
			<physical_name>report_content</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>a9f52107cf17b5e6b2561c39db24926087b007c4</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>举报来源（1文章、2评论）</logical_name>
			<physical_name>report_source</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>f42bc2553026596d60b97b06b1eb561399c69e2b</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>诈骗 色情 政治 广告 抄袭 招聘 骂人 其它</description>
			<logical_name>举报类型（色情 政治...）</logical_name>
			<physical_name>report_type</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>b1b8faab493b1397c27f3732ed9d45f82878c4a0</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>举报的URL</logical_name>
			<physical_name>report_url</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>f3b6b47c2e18bf48cfde5a934262c26354ad75b4</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>请求的URL地址</description>
			<logical_name>请求的URL地址</logical_name>
			<physical_name>request_url</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>ad1d4b0e921ba17841bdf3cfb705e2959859f669</id>
			<length>128</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>受访域名</description>
			<logical_name>受访域名</logical_name>
			<physical_name>request_url_host</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>2f52e6be7252ecc45d37a850f6baf2d39e081840</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>回复内容</description>
			<logical_name>回复内容</logical_name>
			<physical_name>re_content</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>ca6e452bcf44a98a5a867cb37cb752e55bf0502f</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>回复时间</description>
			<logical_name>回复时间</logical_name>
			<physical_name>re_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>ece29d55cef5562f210bfcbba448b2908eefe3e5</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>回复人</description>
			<logical_name>回复人</logical_name>
			<physical_name>re_user_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>55fdcf8680add116d8faa2d4316652d999b8ff71</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>使用的搜索引擎</description>
			<logical_name>使用的搜索引擎</logical_name>
			<physical_name>search_engine</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>a30d1fd618499d96bf73d9ef1009d0943b559190</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>搜索的关键词</description>
			<logical_name>搜索的关键词</logical_name>
			<physical_name>search_word</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>3876461330c858cea9630a7f0694b92145b6e928</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>展现模式（1自动、2栏目列表、3第一条内容）</description>
			<logical_name>展现模式</logical_name>
			<physical_name>show_modes</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>de1cad868759bf8da849c3b716dcf350e62005c3</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>站点编码</description>
			<logical_name>站点编码</logical_name>
			<physical_name>site_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>5ff9066ca3275b0a2c21772431299256036d7e78</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>站点名称</description>
			<logical_name>站点名称</logical_name>
			<physical_name>site_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>41007693982f7b8fb254ff7dc940ceb614249262</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>站点排序号</logical_name>
			<physical_name>site_sort</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>ef18e7063ffc6e8947c1cdd9375b5133eb5d7fd0</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>来源页面/上一个页面</description>
			<logical_name>来源页面/上一个页面</logical_name>
			<physical_name>source_referer</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>43c22813a57290455edae9ac267f3f95e9089168</id>
			<length>128</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>来源域名</description>
			<logical_name>来源域名</logical_name>
			<physical_name>source_referer_host</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>8a761a197d5ab17ef0dcbd94a1223875c7ac8651</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>访问来源类型（1直接访问 2搜索引擎 3外部链接 4内部访问）</description>
			<logical_name>访问来源类型（1直接访问 2搜索引擎 3外部链接 4内部访问）</logical_name>
			<physical_name>source_type</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>a5d0a391d0d09acab89c829d271b0a96a7058ca0</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>状态（0正常 1删除 2停用 3冻结 4审核 5驳回 9草稿）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>70d065a29d01e3875167a20441e0ebbfaf5a05f8</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>状态（0正常 1删除 2停用）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>1f83112bd9cda1be37be1dd081e4657002f5e774</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>状态（0正常 1删除 2停用）</description>
			<logical_name>状态（0正常 1删除 2停用）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>b16f76d76225b8a4f53eaee1e3663606da4b167c</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>保存前验证：不能包含空格、特殊字符</description>
			<logical_name>标签名称</logical_name>
			<physical_name>tag_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>e46b07ff47824949ccf2f1c5b0331d0b52cdf721</id>
			<length>20</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>目标</description>
			<logical_name>目标</logical_name>
			<physical_name>target</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>2e06ba4a97550771adf7b779f09815006091377b</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>主题</description>
			<logical_name>主题</logical_name>
			<physical_name>theme</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>504791eb25563da16558dd1a85e0dffe9c32468f</id>
			<length>255</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>标题</description>
			<logical_name>内容标题</logical_name>
			<physical_name>title</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>19048ae1cce0c3938a70ca9f9989808d6357775b</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>站点标题</description>
			<logical_name>站点标题</logical_name>
			<physical_name>title</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>b8d89ccad76deedfcf649b17f11e8c21e1436a17</id>
			<length>20</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>本次访问总停留时间（秒）</description>
			<logical_name>本次访问总停留时间（秒）</logical_name>
			<physical_name>total_remain_time</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>8fb086ad8b2a35cdf18c1a1e247005cdb140faf0</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>是否最末级</logical_name>
			<physical_name>tree_leaf</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>f22022df4ccf9c7081b4d8f98deed04c1d8c987c</id>
			<length>4</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>层次级别</logical_name>
			<physical_name>tree_level</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>3433cbc6e2484ba20481ec9f733898bc7466a60a</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>全节点名</logical_name>
			<physical_name>tree_names</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>65265fefa00907a60e8fe2ebd6d2ad58760415e5</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>排序号（升序）</logical_name>
			<physical_name>tree_sort</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>cf5f1e3cc5cc0869f539c3c5b2adf05677e367b8</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>所有排序号</logical_name>
			<physical_name>tree_sorts</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>775157d238e6162b3dba0b1761f96224adf597af</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>唯一访问标识</description>
			<logical_name>唯一访问标识</logical_name>
			<physical_name>unique_visit_id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>c60616703cf07aaf21f84215d092e2101efb9bca</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>更新者</logical_name>
			<physical_name>update_by</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b5c3a59170184a01c15c78a6778cb73083fe6321</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>更新时间</logical_name>
			<physical_name>update_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>dd5c2dda89221ec5a1eceb3d871c8a15e9683bfb</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>用户代理字符串</description>
			<logical_name>用户代理字符串</logical_name>
			<physical_name>user_agent</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>e2d38878b5a4985d62c96cae43468cd794393118</id>
			<length>32</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>客户机浏览器</description>
			<logical_name>客户机浏览器</logical_name>
			<physical_name>user_browser</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>37fc268aa0bf09e65b4bc1d21f347833084406cd</id>
			<length>16</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>浏览器版本</description>
			<logical_name>浏览器版本</logical_name>
			<physical_name>user_browser_version</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>3b825589a701b047cbb27d6838c7e3dc2dbb0157</id>
			<length>32</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>客户机设备类型（电脑、平板、手机、未知）</description>
			<logical_name>客户机设备类型（电脑、平板、手机、未知）</logical_name>
			<physical_name>user_device</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>c252f75db245a52ad1e610285393bceb94a0cd46</id>
			<length>32</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>客户机语言</description>
			<logical_name>客户机语言</logical_name>
			<physical_name>user_language</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>9ce8958ae667cad288beaf1601ca19c1ef10de33</id>
			<length>32</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>客户机操作系统</description>
			<logical_name>客户机操作系统</logical_name>
			<physical_name>user_os_name</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>8835c2871a3ff7e902ef56fe55409bceb30d94da</id>
			<length>32</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>客户机屏幕大小0x0</description>
			<logical_name>客户机屏幕大小0x0</logical_name>
			<physical_name>user_screen_size</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>69e7a4305e057e99abf882c4165625b2bae49785</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>视图配置</description>
			<logical_name>视图配置</logical_name>
			<physical_name>view_config</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>0e4a3916a2fa9a5cf0f9a4fb24882cf3027194fb</id>
			<length>8</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>本次访问日期（年月日）</description>
			<logical_name>本次访问日期（年月日）</logical_name>
			<physical_name>visit_date</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>0eafd0e63944941df2e6c7cc47168f27bf35e7be</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>本次访问时间</description>
			<logical_name>本次访问时间</logical_name>
			<physical_name>visit_time</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>28e8e627e41fde1072c7611d210bb32f06b6fd3d</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>访问用户编码</description>
			<logical_name>访问用户编码</logical_name>
			<physical_name>visit_user_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>22c8b388d0a9b2f20bfcfa4a30dde56c62efb44c</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>访问用户姓名</description>
			<logical_name>访问用户姓名</logical_name>
			<physical_name>visit_user_name</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>5e9e6051abbbda5d4ae6375c5a063348cd227a07</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>权重，越大越靠前</description>
			<logical_name>权重，越大越靠前</logical_name>
			<physical_name>weight</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>00af42a37d664cbe84a4c1edb4b4bb4bd28cbfa7</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>权重期限</description>
			<logical_name>权重期限</logical_name>
			<physical_name>weight_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>e8a53d4b2de303cd81a47e1800d23f178ac9608a</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>不包含html</description>
			<logical_name>字数（不包含html）</logical_name>
			<physical_name>word_count</physical_name>
			<type>numeric(p)</type>
		</word>
		<word>
			<id>05acdfa6339bf8fd4f7d657c9f6d426fec2643a7</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>单位</description>
			<logical_name>单位</logical_name>
			<physical_name>workunit</physical_name>
			<type>varchar(n)</type>
		</word>
	</dictionary>
	<tablespace_set>
	</tablespace_set>
	<contents>
		<table>
			<id>bbb794ce9047561ca00645264e019452df6985c9</id>
			<height>464</height>
			<width>383</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>678</x>
			<y>1911</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_cms_guestbook</physical_name>
			<logical_name>留言板表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>052814be6ac553aeaf48456c40a56b7bb07d8e1c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a11ba8c2d68b98159077cd4ab9a31f0c8d50f07f</word_id>
					<id>708a91e44bd76d9b048346ca378eb2d8b0ec6693</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>64b0c3e3ea90482c472a8be8136a3ee33c2eb525</word_id>
					<id>c9ffac35c779194ed4597e9ffad10ef5d74a241f</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ba90244ccd39f9e26d44a0c67289cd5e32590840</word_id>
					<id>9db48e0afaca1b801565f268c23e4ec16bae201c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>c18af94646cf20d6d5001a6090f2a2a59eca9251</word_id>
					<id>0081da55676f6d2f99f03ba58f66bf68e5aee726</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>d053d0146a0bccb9c45ce73a19a93d5c79ba1f28</word_id>
					<id>3dda39016f4c27f61972558cbd73b68ab50fb3ea</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>05acdfa6339bf8fd4f7d657c9f6d426fec2643a7</word_id>
					<id>f7c356eb848e5e459650a4057c7c7d1cd0215c5d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3138f8d288350701cc88f8e7b35df4353f4d30ea</word_id>
					<id>cdaf26c12c897ca383fbb69716b508aecb621850</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</word_id>
					<id>9dfdc2c1467655a55805a4ec37116f5865d3e2b2</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>170410257b4d8e712b8c1d499b415de82ce9683c</word_id>
					<id>90bf3f6bc190ba2adaf843bb6a9ed721d5aead11</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ece29d55cef5562f210bfcbba448b2908eefe3e5</word_id>
					<id>161e1e32456aceb2d5122f3b031b615175e4ff9a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ca6e452bcf44a98a5a867cb37cb752e55bf0502f</word_id>
					<id>30bbfd928c88a1bf1804e2adcefd63467bac27fb</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2f52e6be7252ecc45d37a850f6baf2d39e081840</word_id>
					<id>75fa65cb78ce9a1ff4401d2c9a9690b3418cfa9a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>1f83112bd9cda1be37be1dd081e4657002f5e774</word_id>
					<id>ec57e1438e874c1f06b03f03d9dfdb5371d493db</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>ea920cba2fe0eaee64a2310ece7cda4b198b37ec</column_group>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_guestbook_cc</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>b8ea4b73d6d32a222e5abfd453287575ae518480</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_guestbook_status</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>ec57e1438e874c1f06b03f03d9dfdb5371d493db</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_guestbook_type</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>708a91e44bd76d9b048346ca378eb2d8b0ec6693</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>6289c23e7fadb945c2636a1d143937aa77b0f528</id>
			<height>906</height>
			<width>623</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>1182</x>
			<y>1490</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_cms_visit_log</physical_name>
			<logical_name>访问日志表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>758f68adcfe7d38c1892ef7c83853af637877426</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>f3b6b47c2e18bf48cfde5a934262c26354ad75b4</word_id>
					<id>e5cde86b5469c9c872ca90960deb684da307db39</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ad1d4b0e921ba17841bdf3cfb705e2959859f669</word_id>
					<id>488bf86acbdce3d6a00a20f019f400502f71445e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ef18e7063ffc6e8947c1cdd9375b5133eb5d7fd0</word_id>
					<id>7e5a0c23f1ad7bbb4b3218ac555fcafe56898d10</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>43c22813a57290455edae9ac267f3f95e9089168</word_id>
					<id>a3101fa6d57e6eb153290319b71e65e64e766061</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8a761a197d5ab17ef0dcbd94a1223875c7ac8651</word_id>
					<id>b52df321029b844a7bfa78010d8f3971998bf301</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>55fdcf8680add116d8faa2d4316652d999b8ff71</word_id>
					<id>9bd3a96a7aa9a28eaaf137a1150aefacef548844</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a30d1fd618499d96bf73d9ef1009d0943b559190</word_id>
					<id>25120ab26de16de06751965ad340774ce0d263a7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>0a75802a3eb6922122abaeec5f326611a11b5e8e</word_id>
					<id>5c4f4d350a1add35db1f8569b172e7c562c40f97</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>dd5c2dda89221ec5a1eceb3d871c8a15e9683bfb</word_id>
					<id>5ab4e07953e586536e35c214ed9c218cd12395f0</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>c252f75db245a52ad1e610285393bceb94a0cd46</word_id>
					<id>c8a22c7af18ddf0427ee32736cc71afd4c3f5757</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8835c2871a3ff7e902ef56fe55409bceb30d94da</word_id>
					<id>c516a2936a5c5353175b02eada0f17a167f8475e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3b825589a701b047cbb27d6838c7e3dc2dbb0157</word_id>
					<id>6cfb61364f9b1ff61d508968e9ff6c0d95274bfb</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>9ce8958ae667cad288beaf1601ca19c1ef10de33</word_id>
					<id>ed5e1eb4954f339010a30de6cba5834363b50940</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e2d38878b5a4985d62c96cae43468cd794393118</word_id>
					<id>6d60510a3055a28671f58dbe68dbcabc66914ed5</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>37fc268aa0bf09e65b4bc1d21f347833084406cd</word_id>
					<id>98cda3c01ecd3f70bc0c8052fd174cfd91a7bfb7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>775157d238e6162b3dba0b1761f96224adf597af</word_id>
					<id>aa81548cdaee0f75161c82ebc34ba8f6141f5672</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>0e4a3916a2fa9a5cf0f9a4fb24882cf3027194fb</word_id>
					<id>daf8bca13ba345d6cdaa9301afba0c6854b4afbc</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>0eafd0e63944941df2e6c7cc47168f27bf35e7be</word_id>
					<id>5f233be0ca30e83b57f6abc88b524fffcd083438</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>632a14ff0c67075a0d31e4024cb14f0aaa3de02a</word_id>
					<id>144fe1a3053ef69b5afeb0a9303d9e1636983d3c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>36fc46a5f5c941cccb3b1b823f1082511b9b6066</word_id>
					<id>d11720c8999e8c32966f6fa8749801abcb6dfbce</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>decimal(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>4f34963c5287c1b6e0c92e0d4aa162e020d4b696</word_id>
					<id>203a7737a701a06b0e95477a4b878e6a930dc55c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>decimal(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b8d89ccad76deedfcf649b17f11e8c21e1436a17</word_id>
					<id>7455ea9ba041221cabed55bfc9d593a7013b32c1</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>decimal(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>de1cad868759bf8da849c3b716dcf350e62005c3</word_id>
					<id>422e64abc608e8fc88d0b60b9e292914b16b7b03</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5ff9066ca3275b0a2c21772431299256036d7e78</word_id>
					<id>09fb8ac13825e6eb40d031d41a591d706cb98a06</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>187469fddef22c4c96d63a3cc098b0ad7dbc3834</word_id>
					<id>f10b3487a7cc0fda6df95e104c1e40119c8b3ffd</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>bb1e56bd34be19cf654acc1fde6062dea4477f26</word_id>
					<id>7d809555199f92692e663598e1b54b06bbb92ce3</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8737d86c1a567c3c3d03da8465d56ae6498a0915</word_id>
					<id>21d47905e8cb686fc0435ca912ffc49c2ac18cf7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6f996ffe79e499c44fd24f140191ae038d68ba50</word_id>
					<id>769bdb75e6da9ee88bd30b18786ed3350f8107fc</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>28e8e627e41fde1072c7611d210bb32f06b6fd3d</word_id>
					<id>a788d046d20aa5cd4bbf4604999a45a8ca8400a1</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>22c8b388d0a9b2f20bfcfa4a30dde56c62efb44c</word_id>
					<id>308b95d417d9ac2677c3a5ef9748330f1b757497</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>ea920cba2fe0eaee64a2310ece7cda4b198b37ec</column_group>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_cc</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>f10b3487a7cc0fda6df95e104c1e40119c8b3ffd</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_ci</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>21d47905e8cb686fc0435ca912ffc49c2ac18cf7</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_fvt</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>d11720c8999e8c32966f6fa8749801abcb6dfbce</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_inv</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>144fe1a3053ef69b5afeb0a9303d9e1636983d3c</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_ra</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>5c4f4d350a1add35db1f8569b172e7c562c40f97</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_sc</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>422e64abc608e8fc88d0b60b9e292914b16b7b03</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_uvid</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>aa81548cdaee0f75161c82ebc34ba8f6141f5672</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_vd</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>daf8bca13ba345d6cdaa9301afba0c6854b4afbc</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>cms_visit_log_vt</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>5f233be0ca30e83b57f6abc88b524fffcd083438</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_visit_log_corpc</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>b8ea4b73d6d32a222e5abfd453287575ae518480</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>b181b4852f4586caf8cfc5075ab5e557cb3a9023</id>
			<height>100</height>
			<width>326</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>1218</x>
			<y>1023</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>33ceed330ef0be29d0a98e6be418f1e42c3d0e57</id>
					<source>48d4897a1dba08c801d47f53cc989f40a21c33b1</source>
					<target>b181b4852f4586caf8cfc5075ab5e557cb3a9023</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>11</target_xp>
						<target_yp>0</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name></name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
				<relation>
					<id>9462cd72fd690565099b69fb2db8354d95eee82f</id>
					<source>eebd3feb320d59c2f2b4a9ef6fe003c8e6d45e18</source>
					<target>b181b4852f4586caf8cfc5075ab5e557cb3a9023</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name></name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_cms_article_tag</physical_name>
			<logical_name>文章与标签关系</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<id>3af42dafa08423c2c7dfe867c9cec403d09fde89</id>
					<referenced_column>eb5d114785c793115e222f88cbce9d6cda0f1543</referenced_column>
					<relation>33ceed330ef0be29d0a98e6be418f1e42c3d0e57</relation>
					<description>栏目内容的编号</description>
					<unique_key_name></unique_key_name>
					<logical_name>内容编号</logical_name>
					<physical_name>article_id</physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>34e626c56b4e968630d15843006b3d3c34b49073</id>
					<referenced_column>5045fa44fb0404a5984aab9f2cf32930b7e8df96</referenced_column>
					<relation>9462cd72fd690565099b69fb2db8354d95eee82f</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>eebd3feb320d59c2f2b4a9ef6fe003c8e6d45e18</id>
			<height>100</height>
			<width>326</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>1218</x>
			<y>1227</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_cms_tag</physical_name>
			<logical_name>内容标签</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>b16f76d76225b8a4f53eaee1e3663606da4b167c</word_id>
					<id>5045fa44fb0404a5984aab9f2cf32930b7e8df96</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>0e17c0f08391aab8c32909b55ddc1d622e0a6c1a</word_id>
					<id>4d8e937f390abf82b84aaede33ccd04c973189fa</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>numeric(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>33debedab8a6a3aabd7ec79972b5d2e30a9765cc</id>
			<height>204</height>
			<width>486</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>627</x>
			<y>1611</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_cms_report</physical_name>
			<logical_name>内容举报表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>2358181fe16d4f3fc756d2f5c22f99eb13728c0a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a9f52107cf17b5e6b2561c39db24926087b007c4</word_id>
					<id>d7bd7a4faed61e09992593cc523beb28a0c83b55</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>bd637d523962b7491c627f7881af1c639349cac2</word_id>
					<id>5ba1ca3f84fe6fbf34acfecc66e0031a812b3cf6</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b1b8faab493b1397c27f3732ed9d45f82878c4a0</word_id>
					<id>260fcf70275e004e5cb7cb8707b191f1a6e483a2</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>f42bc2553026596d60b97b06b1eb561399c69e2b</word_id>
					<id>eaae3f0c8552c2397873f87447f7eb1abdc15221</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6bf1199a39cfee8fb1e7f1e2ffc81aff24c2942f</word_id>
					<id>fed117aa979da2f29a057833f25d99f2629c57b5</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>e933b6038049534dd00553a4f6b62ec3002c8ad0</id>
			<height>698</height>
			<width>321</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>1290</x>
			<y>60</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>96b82d3cb06570b061844ada5b55e048445d3755</id>
					<source>48d4897a1dba08c801d47f53cc989f40a21c33b1</source>
					<target>e933b6038049534dd00553a4f6b62ec3002c8ad0</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name></name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_cms_article_data</physical_name>
			<logical_name>文章详情表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<id>a1ea205b23430d5e2419ab6fc7b81e6a1063b163</id>
					<referenced_column>eb5d114785c793115e222f88cbce9d6cda0f1543</referenced_column>
					<relation>96b82d3cb06570b061844ada5b55e048445d3755</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>d30fda93843661fe1fc8bae97b93472c56451bda</word_id>
					<id>f2b4ec165eac4ea31d76132a98606f83c1b7c3f8</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>longtext</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6eb4e127fce70565dc8fa980598245bcd5747c7c</word_id>
					<id>ab35e50baf172cfb277259d60316d292f2f6c597</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3e822f92a8f08754e2c713df0463297fa91d0f66</word_id>
					<id>52ca3f50c35dc1675c84185106a760fe5fd14211</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>118dab95fc1f792cd468b9f66af2d4fabd98c39b</column_group>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>42f7819cd01ef2cfe1dfd3ba915e0fce4fb4ade0</id>
			<height>100</height>
			<width>499</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>1218</x>
			<y>831</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>ad08091ac185c5abe1902360e139e829a021c910</id>
					<source>48d4897a1dba08c801d47f53cc989f40a21c33b1</source>
					<target>42f7819cd01ef2cfe1dfd3ba915e0fce4fb4ade0</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>13</target_xp>
						<target_yp>0</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name></name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_cms_article_posid</physical_name>
			<logical_name>文章推荐位</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<id>44aaef65b524bb63e467466470a99025376b001f</id>
					<referenced_column>eb5d114785c793115e222f88cbce9d6cda0f1543</referenced_column>
					<relation>ad08091ac185c5abe1902360e139e829a021c910</relation>
					<description>栏目内容的编号</description>
					<unique_key_name></unique_key_name>
					<logical_name>内容编号</logical_name>
					<physical_name>article_id</physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>fd481873c467054b531d45fdd9fb45ba9c109afe</word_id>
					<id>bd580d568c1b2cf2a726ed789d8a755c1ed2ebdf</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>48d4897a1dba08c801d47f53cc989f40a21c33b1</id>
			<height>750</height>
			<width>405</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>681</x>
			<y>60</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>045b43d627f96a6c4efadc60531c1e37f0fae191</id>
					<source>4a20c4ba9ab461c7ad1976f90d915e5245262ebe</source>
					<target>48d4897a1dba08c801d47f53cc989f40a21c33b1</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name></name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_cms_article</physical_name>
			<logical_name>文章表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>eb5d114785c793115e222f88cbce9d6cda0f1543</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>dde1496bcd5d55330967cc24f53513e102a163c0</id>
					<referenced_column>0d993b7470d7bde3d20e9c7f24d1811518cf44cc</referenced_column>
					<relation>045b43d627f96a6c4efadc60531c1e37f0fae191</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e41be76c164ce528ca42bf4f6ae1ccfa35bf8b8a</word_id>
					<id>bd1a5ed3ac07f4338055d8a12c8146cae37c164c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>504791eb25563da16558dd1a85e0dffe9c32468f</word_id>
					<id>37e50d5a25f2367cc57c056ee45ad5cb48810968</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ac509486994eb9ba67fa1bb1d0ea9bbeb3af9d49</word_id>
					<id>7567aa8fbfa2799eb9807ae765a45afa51eae992</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>bb9df43be17fb1eb672ecc111218e42df2adbc75</word_id>
					<id>61c2101a6c97f7c9abceaba4a45d63d40eeafdc8</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>6935e308e57e9883c9d7039d494fb3d7f74d8276</word_id>
					<id>b07468735f2575b6de202a14eabc7ceba4a1c5df</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e536a1fd40fcde4fd0f6e6eee976148caca997b9</word_id>
					<id>f08dc2008900b7fbcdc7eb960b3c7e9c133031a4</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>adda41026df6c73bdcc35b7b9d2e29075a6f162f</word_id>
					<id>2a7ae7d128a83d1adafdff9af86aa469aed2f338</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5e9e6051abbbda5d4ae6375c5a063348cd227a07</word_id>
					<id>382d9135460cf8d24c5c7f645b27f24b1fb766d1</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>decimal(p)</type>
					<constraint></constraint>
					<default_value>0</default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>00af42a37d664cbe84a4c1edb4b4bb4bd28cbfa7</word_id>
					<id>e91726c8306698a6343c46de78c595fdc7e37d72</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>793b67ee171c7f094150910f3a7cd322ab4b1f9b</word_id>
					<id>3066d49c1dab340cb05c5bfdded97da5c7a644c7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>04e0881c10e81677076ae9ebc0a0d5bc9c5e4b27</word_id>
					<id>07eeddae2d3773cf417d15e8ed0c2265bb171874</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2a0199273650e7efb02228ace74e3a6ff9cf6156</word_id>
					<id>96483eea696711f59326da945873daf5fbdd3aa0</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>decimal(p)</type>
					<constraint></constraint>
					<default_value>0</default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8895e8a7aaf9eb10a3dd34076b8fb455030d8cf5</word_id>
					<id>641d9b4a2dea1fc445bb1290fb3ef0cc377b20d7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>numeric(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>155a7eaacd5ace299e2377e9810e5a94a9f359c0</word_id>
					<id>16343f2b171b860692297df77d27308e987b3471</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>numeric(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e8a53d4b2de303cd81a47e1800d23f178ac9608a</word_id>
					<id>3697e6e1dabca0cce7e3f2abb680b98f3dc14b96</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>numeric(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>1de3007f27aa30d0771965c412938540311d3414</word_id>
					<id>a34855a3562f1820ebd2a1537f07a9fdacaab286</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>69e7a4305e057e99abf882c4165625b2bae49785</word_id>
					<id>dcd2ddd1ebdabd4774839be4423c1fc9fd89d0ac</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
				<column_group>ea920cba2fe0eaee64a2310ece7cda4b198b37ec</column_group>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_article_cb</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>c391a15752a8eb58bc558a39d1b431f7ee125e0e</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_article_cc</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>dde1496bcd5d55330967cc24f53513e102a163c0</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_article_corp_code</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>b8ea4b73d6d32a222e5abfd453287575ae518480</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_article_status</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>f0036584bd8711715579d21994a0105935605a7e</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_article_ud</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>f8ea4fc4a778a0b94398a661a1ed8608f0e8d28d</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_article_weight</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>382d9135460cf8d24c5c7f645b27f24b1fb766d1</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>0208a90ffa3e83ced912dfdbc27d6bb819d1baea</id>
			<height>516</height>
			<width>383</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>692</x>
			<y>975</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>8c413123d08b4a5c1bc53459899a413aeecab8f0</id>
					<source>48d4897a1dba08c801d47f53cc989f40a21c33b1</source>
					<target>0208a90ffa3e83ced912dfdbc27d6bb819d1baea</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name></name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
				<relation>
					<id>879984c304fe758fb6889d04f440a2f3164fb6c2</id>
					<source>4a20c4ba9ab461c7ad1976f90d915e5245262ebe</source>
					<target>0208a90ffa3e83ced912dfdbc27d6bb819d1baea</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name></name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_cms_comment</physical_name>
			<logical_name>文章评论表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>54f3bf02f4a2786c4d79239effed0560d06a2506</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>835faef487ae6874a525a8cb8ba19825f93091aa</id>
					<referenced_column>0d993b7470d7bde3d20e9c7f24d1811518cf44cc</referenced_column>
					<relation>879984c304fe758fb6889d04f440a2f3164fb6c2</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>41bd3c792a5a382accd971c4235c30842d0c051c</id>
					<referenced_column>eb5d114785c793115e222f88cbce9d6cda0f1543</referenced_column>
					<relation>8c413123d08b4a5c1bc53459899a413aeecab8f0</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name>内容编号</logical_name>
					<physical_name>article_id</physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>54c72943c2269393a702a7d758bc5c183dc4938b</word_id>
					<id>12af69d900b4f137e87c95ac882720cb01912b7e</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>0a87abc6a037a6fb06aefa07bd97708ed2771168</word_id>
					<id>39078af05c1a3ab1323e0859582e0bfc8f2f7605</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>1827265e648182aa205807b8564e8f09189f84dd</word_id>
					<id>d3f60cc1222cb3bbc7d84b47028c677cf52a50e5</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2f09198043d7f22ef84a72d15bd08f045db36c84</word_id>
					<id>a57b864623f2df4e556c1d367721949689f11545</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>dede04c906e6cf3f9f49e48e118a07d93e8535bd</word_id>
					<id>a4c9fa6fedd5812cea2c8dee58cd514f37939e3a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</word_id>
					<id>42ad3ccac5e8dfe17c5721e21e63cb9189473977</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>170410257b4d8e712b8c1d499b415de82ce9683c</word_id>
					<id>623a8b5b174a3bc9fdbdb5c65ecd561a4bf65a40</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>55bb4192504f15f138af34b3324447da63250cb7</word_id>
					<id>0387a23bf7ea8e2f5857c4ae536700d9f6dc03f2</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>0db458281a9f843e54736d41a335e977f04d3ab8</word_id>
					<id>ed3a282e11c83f8ebe01ed3f85e772e410e4c454</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>datetime</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b3c6d5be4f35caad8d24f4fa35207ab2c3f026dc</word_id>
					<id>fbcb204fc3da5e62abe9f90524f27b483acdeee3</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>8895e8a7aaf9eb10a3dd34076b8fb455030d8cf5</word_id>
					<id>18b968329827c79290b0db10b00131e30f97047f</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>numeric(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>155a7eaacd5ace299e2377e9810e5a94a9f359c0</word_id>
					<id>84ea11032fe1a36de5fe346aa16142ad3d35a497</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>numeric(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>1f83112bd9cda1be37be1dd081e4657002f5e774</word_id>
					<id>97a2c22c48e2b4049d84dd0a637491c0674512ea</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>ea920cba2fe0eaee64a2310ece7cda4b198b37ec</column_group>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_comment_catc</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>835faef487ae6874a525a8cb8ba19825f93091aa</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_comment_ai</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>41bd3c792a5a382accd971c4235c30842d0c051c</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_comment_cc</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>b8ea4b73d6d32a222e5abfd453287575ae518480</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_comment_status</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>97a2c22c48e2b4049d84dd0a637491c0674512ea</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>4a20c4ba9ab461c7ad1976f90d915e5245262ebe</id>
			<height>1374</height>
			<width>405</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>96</x>
			<y>615</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
				<relation>
					<id>a30307e2bbd64684a4a2a54f8de5f6db5bca8a9a</id>
					<source>e1353e5e616727fad8dfdf445ad2f33e4d47d510</source>
					<target>4a20c4ba9ab461c7ad1976f90d915e5245262ebe</target>
						<source_xp>-1</source_xp>
						<source_yp>-1</source_yp>
						<target_xp>-1</target_xp>
						<target_yp>-1</target_yp>
						<color>
							<r>0</r>
							<g>0</g>
							<b>0</b>
						</color>
					<child_cardinality>1..n</child_cardinality>
					<parent_cardinality>1</parent_cardinality>
					<reference_for_pk>true</reference_for_pk>
					<name></name>
					<on_delete_action>RESTRICT</on_delete_action>
					<on_update_action>RESTRICT</on_update_action>
					<referenced_column>null</referenced_column>
					<referenced_complex_unique_key>null</referenced_complex_unique_key>
				</relation>
			</connections>
			<physical_name>js_cms_category</physical_name>
			<logical_name>栏目表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>187469fddef22c4c96d63a3cc098b0ad7dbc3834</word_id>
					<id>0d993b7470d7bde3d20e9c7f24d1811518cf44cc</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>a535b6c506004a7fdf4d48984c9ff2cfa59c157a</column_group>
				<normal_column>
					<word_id>bb1e56bd34be19cf654acc1fde6062dea4477f26</word_id>
					<id>3b97e52e25c24c77e03a9b78fad4426535a6aafd</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<id>f86eb0e3c56d958c2d1ee2c0ea77353b01b89973</id>
					<referenced_column>f51ed54a54f6cd6b1d74072be62c808f6848470c</referenced_column>
					<relation>a30307e2bbd64684a4a2a54f8de5f6db5bca8a9a</relation>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>true</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e41be76c164ce528ca42bf4f6ae1ccfa35bf8b8a</word_id>
					<id>79b3fbe699e303c21335c291836b3f5593aa206c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>9a4bf3237fc2b018dbfa538a3829c2ffa8898e1e</word_id>
					<id>21bf89f56281674a78a844f355bcb369fba985e7</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3b7edb7edb602cc39576f37efa2d2df33aaefe28</word_id>
					<id>f58a6d67f5350333488a9469d442fec790b4c9d6</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e46b07ff47824949ccf2f1c5b0331d0b52cdf721</word_id>
					<id>229683cbe3d486c25f4b46624c787ec842d14f2a</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e536a1fd40fcde4fd0f6e6eee976148caca997b9</word_id>
					<id>5d68d3e666fc97755fedc3ef791f438fedda5581</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>adda41026df6c73bdcc35b7b9d2e29075a6f162f</word_id>
					<id>84cd10b6db6d10aac1e605b88e51b22ec06b63ea</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b4e1d80ab20fa3b33fb05a7f8e84510591063718</word_id>
					<id>d53d3c8a1026f5bde2747c545e1b78f294da7f56</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>c538e72e1dffe92907500f45d502a124fe24e446</word_id>
					<id>f66206c046be58bb2c575069be681ef2a135551c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3876461330c858cea9630a7f0694b92145b6e928</word_id>
					<id>8648e63a9b97fe54ef553105d7b5b8eef2c1b71f</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>17ca518285184abccdb8e6854ccb9af3b4fa0f3a</word_id>
					<id>7187ff36f855a4e82c0e1a0d6de0e605757014d2</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>3e822f92a8f08754e2c713df0463297fa91d0f66</word_id>
					<id>107135f4d6737d38cfd4ec44dd086240b11393db</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>ac819b1c0fe0316b06356855f3b570321e6517b8</word_id>
					<id>b4bd194b30b49fbedf203ddffdd19a2aaedc0138</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>1de3007f27aa30d0771965c412938540311d3414</word_id>
					<id>c65d68fef1799df61f34daf6620be5cebfde3ebb</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>69e7a4305e057e99abf882c4165625b2bae49785</word_id>
					<id>0e766a93c3bec26b54b0c71ca3fc1c0f7d0ab838</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
				<column_group>118dab95fc1f792cd468b9f66af2d4fabd98c39b</column_group>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_category_pc</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>394369b90c0a5b6efeed3cf823c642605d7a1653</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_category_ts</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>23f973124aedd0244533f4e7b3b103c548b966be</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_category_status</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>f0036584bd8711715579d21994a0105935605a7e</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_category_tss</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>984d5eac2b3221118a61655e4a5a49c78e0f0151</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>e1353e5e616727fad8dfdf445ad2f33e4d47d510</id>
			<height>490</height>
			<width>423</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>96</x>
			<y>60</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_cms_site</physical_name>
			<logical_name>站点表</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>de1cad868759bf8da849c3b716dcf350e62005c3</word_id>
					<id>f51ed54a54f6cd6b1d74072be62c808f6848470c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5ff9066ca3275b0a2c21772431299256036d7e78</word_id>
					<id>09eda0fb75fb20fff9ecefb569a44e9cd5f47491</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>41007693982f7b8fb254ff7dc940ceb614249262</word_id>
					<id>c1641887e5caa2d44efdf192693d34e644d3160d</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>decimal(p)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>19048ae1cce0c3938a70ca9f9989808d6357775b</word_id>
					<id>a605e8a7ac657c636b1451487ff405c8a615e73c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a357603768870d14bdf1e2bb9736c227a5468301</word_id>
					<id>d2499d3351df9579fcbcc4c1f45286adb24fcf81</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>028e460308bd935c315b7c5cebe128cf60e616fd</word_id>
					<id>e86a2d89de6b24c4dce26da5ca383728cd94b982</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>e536a1fd40fcde4fd0f6e6eee976148caca997b9</word_id>
					<id>7b44460b25fbdc4f63b3cd5e3edc3bac96924388</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>adda41026df6c73bdcc35b7b9d2e29075a6f162f</word_id>
					<id>311d2d8a882c36968e57f78baa154d50c5c7a99b</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2e06ba4a97550771adf7b779f09815006091377b</word_id>
					<id>667d7d9fad9b270d314690c04abfe9a75633d8da</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>10013cc4ca7ecccaa3d9d7683cdcb10464ab5a38</word_id>
					<id>3cba027f7b250b59a87215af752671980326b3e5</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b2f495ae45df19c844b61a29027c5f9657640950</word_id>
					<id>15d8f8152057ebe557394121c7534643fd12ccb2</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
			</columns>
			<indexes>
				<inidex>
					<full_text>false</full_text>
					<non_unique>true</non_unique>
					<name>idx_cms_site_status</name>
					<type></type>
					<description></description>
					<columns>
						<column>
							<id>f0036584bd8711715579d21994a0105935605a7e</id>
							<desc>false</desc>
						</column>
					</columns>
				</inidex>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
	</contents>
	<column_groups>
			<column_group>
				<id>845c82ebd869d5620b1ef2c2b6f438b11a045082</id>
				<group_name>BaseEntity</group_name>
				<columns>
					<normal_column>
						<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
						<id>02ecedc0de5850cba25bc91919ed39d414b74111</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a5d0a391d0d09acab89c829d271b0a96a7058ca0</word_id>
						<id>2fe6a36385238c1b21c76deae00a7afa00ff5538</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>ea920cba2fe0eaee64a2310ece7cda4b198b37ec</id>
				<group_name>BaseEntityCorp</group_name>
				<columns>
					<normal_column>
						<word_id>c71533e4d2f429ff8466fd6a8de5719f1741377b</word_id>
						<id>b8ea4b73d6d32a222e5abfd453287575ae518480</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value>0</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>11c59294fe142d108ca4dac5a03033f50188a450</word_id>
						<id>b94f5fe344185c40739cf93d1090686001bb11e0</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value>JeeSite</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>35ae805d1da92afdb99b2fe8c536d1649356fccd</id>
				<group_name>DataEntity</group_name>
				<columns>
					<normal_column>
						<word_id>70d065a29d01e3875167a20441e0ebbfaf5a05f8</word_id>
						<id>f0036584bd8711715579d21994a0105935605a7e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value>0</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</word_id>
						<id>c391a15752a8eb58bc558a39d1b431f7ee125e0e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>170410257b4d8e712b8c1d499b415de82ce9683c</word_id>
						<id>e2e82ba86e15fd67397355e711255b1625078ae1</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>c60616703cf07aaf21f84215d092e2101efb9bca</word_id>
						<id>fd0546fc2d4e01c35dcbc23913add68a99fabd73</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b5c3a59170184a01c15c78a6778cb73083fe6321</word_id>
						<id>f8ea4fc4a778a0b94398a661a1ed8608f0e8d28d</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</word_id>
						<id>69e01b6d4f42df40a09540ef4ba10ed8e006abaa</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>85024a2953cf3e3c9c1cce49b2351853ab0d125b</id>
				<group_name>DataEntityNoStatus</group_name>
				<columns>
					<normal_column>
						<word_id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</word_id>
						<id>e5355faba5ec3c9128507dd4c48ea9230631cf83</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>170410257b4d8e712b8c1d499b415de82ce9683c</word_id>
						<id>6bed374c39d181003a4f92d76d79a4119176ba0c</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>c60616703cf07aaf21f84215d092e2101efb9bca</word_id>
						<id>f9db19bb567760bbdd554d75bbfdc891c89f9da9</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b5c3a59170184a01c15c78a6778cb73083fe6321</word_id>
						<id>ee78b079f7d319bf8119fd01439cd97424ff49fa</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</word_id>
						<id>f7b88ecec0ef386bb384c228842a7587432112fb</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>118dab95fc1f792cd468b9f66af2d4fabd98c39b</id>
				<group_name>ExtendEntity</group_name>
				<columns>
					<normal_column>
						<word_id>b1acb2df17677e0ed818dea6e1150bfc088fcd50</word_id>
						<id>6ccadddab6ce48441ca7abd798cda6f3debf4a0c</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>d27290b84b207a7c87f4d78d80e62048c67fac04</word_id>
						<id>93ab0ba3b47b01934614dbd3e572358c9f99e6ea</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>5e25d7f0e327d2d760a04b32140b639bb96bfd8d</word_id>
						<id>a78c7961910a5e697027d1a3530b1afaa8ea8c94</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>d05572af90653d4ddfd880402a15ed9b27c81888</word_id>
						<id>40085364ec7a58653e96f8659aadd258d7556bc7</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>acc372d31709a1133f6c13fc8abfef7881ca26ed</word_id>
						<id>9787d7fe93ee31c5b4979fd620ff6e4b2777eccf</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>59763a16504e4136a7a2fbed38476272d337105c</word_id>
						<id>95c55b81b7e9e1a9bb01aa3d88fb90c648641c4e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>600b96b04bf4f65919eabfc6613d3c5e370931e1</word_id>
						<id>16f44dfc7964796f109293bc49afd58dcb4eec1f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a9dc80e5c484dc7b9d5fff9294eaa383819edc26</word_id>
						<id>39b1dffa083f74afc30df621845cf7f0ed71394f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b67071354a8d43cabe9a0ac29303f8b144a15985</word_id>
						<id>7584cc6360ae7edc99e1f619042eba5865b2c4c7</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>70b8240f049f658b97394b7d4784481fdb477f5c</word_id>
						<id>f0b5383e05c6b3f6e5f65b33b33009826c83d014</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>ce377c081415847061b2efc58b701ff847dcaaca</word_id>
						<id>260d5f31009fff18000d1e64f4f877926e621306</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>fb96f6dc60d31576c278a3c64024154eda67d3fe</word_id>
						<id>a83144f40e7ae64e46a4b4ed651379774a953b17</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>0733a829c52af8af725ace50d4ed3e0179969f56</word_id>
						<id>2a5203a275171a250870cf6cb224a910aa9354ec</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>0b7230df29d6df213507c8855d019d2d55c9a561</word_id>
						<id>3ef5bd65a7dcd74b9a9d8a292ec395f66b7de32b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>6cb3dad83f3e1f358155892dcee3167ef55bacc7</word_id>
						<id>01d0849bdda56a8d8f24befdadc3fc9b007ae92b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>e54b261fc7ab81e0ef19a89e657453664abe5593</word_id>
						<id>1c8ed63d72f40f0fe2f05815675771bdf3f824f8</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>038ea74a700b763fa1f8fcdbd2c27e7941f82f7a</word_id>
						<id>2b49e875138bfb329aaa352629650b7881435123</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>19aa2e40abff2bb4e908f18f5f5e5b30a074cd8b</word_id>
						<id>5c6ec16226d85b0411b7077cb9a6e0c7aa8d74d1</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>06f59428b50e62e636a58a2f3cbcee2c75764506</word_id>
						<id>d92b8f7fa7a2be49c7f00c447a603b136e84261d</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8f08aeb4b9ceb072f0232d3d23fd5371ef8f379c</word_id>
						<id>095a76f07a3cd2bdc6cc442757c11012e1974f4a</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>3962f4b130a803841a193a163e42679946b5ae1f</word_id>
						<id>42c5d8f490f69b93e77698efa030ca23988ae696</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>a535b6c506004a7fdf4d48984c9ff2cfa59c157a</id>
				<group_name>TreeEntity</group_name>
				<columns>
					<normal_column>
						<word_id>e4465172e2cc64907a386237cc7d0c7d79a074dc</word_id>
						<id>394369b90c0a5b6efeed3cf823c642605d7a1653</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b4f9837136b0a84afc9a611a563fb51141fdac1f</word_id>
						<id>e8d877396943acfec73023dba2c1c6e3d7802d62</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>65265fefa00907a60e8fe2ebd6d2ad58760415e5</word_id>
						<id>23f973124aedd0244533f4e7b3b103c548b966be</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>cf5f1e3cc5cc0869f539c3c5b2adf05677e367b8</word_id>
						<id>984d5eac2b3221118a61655e4a5a49c78e0f0151</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8fb086ad8b2a35cdf18c1a1e247005cdb140faf0</word_id>
						<id>b2f246a3f0ade317eaa9915e2fd539abae5a5ec8</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f22022df4ccf9c7081b4d8f98deed04c1d8c987c</word_id>
						<id>f5a9968479420f08da2e98d21136b3ed4b6e396f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>3433cbc6e2484ba20481ec9f733898bc7466a60a</word_id>
						<id>618194ebfc8c6c42efcef3a4af0b8054f6af209b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
	</column_groups>
	<test_data_list>
	</test_data_list>
	<sequence_set>
	</sequence_set>
	<trigger_set>
	</trigger_set>
	<change_tracking_list>
	</change_tracking_list>
</diagram>
