/**
 * 公共表格样式 - 筛选条件和固定列
 * 使用方法：在页面中引入此CSS文件
 * <link rel="stylesheet" href="${ctx}/static/css/common-table.css">
 */

/* ==================== 筛选条件区域样式 ==================== */

/* 筛选条件容器 */
.search-form-container {
    padding: 15px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    margin-bottom: 15px;
    background-color: #fafafa;
    width: 100%;
}

/* 筛选条件行 */
.search-form-row {
    display: flex;
    flex-wrap: wrap;
    /*margin-bottom: 15px;*/
    align-items: flex-start;
    width: 100%;
}

/* 筛选条件组 - 每行3个条件 */
.search-form-row .form-group {
    flex: 0 0 calc(33.333333%); /* 每行3个条件，减去间距 */
    max-width: calc(33.333333%);
    margin-right: 0px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
}

.search-form-row .form-group:nth-child(3n) {
    margin-right: 0;
}

/* 筛选条件标签 */
.search-form-row .form-group .control-label {
    width: 136px;
    text-align: right;
    /*padding-right: 8px;*/
    margin-bottom: 0;
    white-space: nowrap;
    font-weight: normal;
    flex-shrink: 0;
}

/* 筛选条件输入框容器 */
.search-form-row .form-group .control-inline {
    width: calc(100% - 146px);
    flex-shrink: 0;
}

/* 筛选条件输入框 - 统一固定长度 */
.search-form-row .form-group .control-inline .form-control {
    width: calc(100%) !important;
    /*min-width: 260px;*/
    /*max-width: 260px;*/
}

/* 特殊处理width-120类的输入框 */
.search-form-row .form-group .control-inline .width-120 {
    width: calc(100%) !important;
    /*min-width: 250px;*/
    /*max-width: 250px;*/
}

/* 特殊处理日期范围输入框 */
.search-form-row .form-group .control-inline .width-datetime {
    width: calc(100%) !important;
    /*min-width: 150px;*/
    /*max-width: 150px;*/
    display: inline-block;
}
.search-form-row .form-group .control-inline .width-date {
    width: calc(100%) !important;
    /*min-width: 150px;*/
    /*max-width: 150px;*/
    display: inline-block;
}

/* 特殊处理下拉选择框 */
.search-form-row .form-group .control-inline .width-120 {
    width: calc(100%) !important;
    /*min-width: 260px;*/
    /*max-width: 260px;*/
}

/* 日期范围容器特殊处理 */
.search-form-row .form-group .control-inline:has(.width-datetime) {
    width: calc(100% - 146px);
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.search-form-row .form-group .control-inline:has(.width-date) {
    width: calc(100% - 146px);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.search-form-row .form-group .control-inline:has(.treeselect) {
    width: calc(100% - 136px);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* 树形选择器特殊处理 */
.search-form-row .form-group .control-inline .treeselect {
    width: calc(100%) !important;
    /*min-width: 260px;*/
    /*max-width: 260px;*/
}

/* 按钮行样式 */
.search-button-row {
    display: flex;
    justify-content: flex-start;
    padding-top: 15px;
    border-top: 1px solid #e5e5e5;
    margin-top: 10px;
}

.search-button-row .btn {
    margin-right: 10px;
}

/* ==================== 表格固定列样式 ==================== */

/* 表格列文字左对齐 */
.ui-jqgrid tr.jqgrow td {
    text-align: left !important;
}

/* 表格固定列容器 */
.fixed-table-container {
    position: relative;
    overflow: hidden;
    width: 100%;
}

/* 表格水平滚动条样式 */
.ui-jqgrid .ui-jqgrid-bdiv {
    overflow-x: auto;
    overflow-y: auto;
}

/* 优化表格显示 */
.ui-jqgrid {
    border: 1px solid #ddd;
    width: 100% !important;
}

.ui-jqgrid .ui-jqgrid-htable th {
    background-color: #f5f5f5;
    font-weight: bold;
}

/* 表格列宽控制 - 确保只显示5列 */
.ui-jqgrid .ui-jqgrid-htable {
    min-width: 800px; /* 最小宽度确保横向滚动 */
}

.ui-jqgrid .ui-jqgrid-btable {
    min-width: 800px; /* 最小宽度确保横向滚动 */
}

/* 固定列样式优化 */
.ui-jqgrid .ui-jqgrid-frozen-lh,
.ui-jqgrid .ui-jqgrid-frozen-rh {
    background-color: #f8f9fa;
    border-right: 2px solid #dee2e6;
}

.ui-jqgrid .ui-jqgrid-frozen-div {
    background-color: #ffffff;
    border-right: 2px solid #dee2e6;
}

/* 滚动区域样式 */
.ui-jqgrid .ui-jqgrid-hbox {
    overflow-x: auto;
}

/* 手动实现的固定列样式 */
/* 固定checkbox列 */
.fixed-checkbox-column {
    position: -webkit-sticky !important; /* Safari兼容性 */
    position: sticky !important;
    left: 0 !important;
    z-index: 11 !important;
    background-color: #ffffff !important;
    border-right: 1px solid #dee2e6 !important;
    min-width: 30px !important;
}

/* 固定第一个数据列（住房编号等） */
.fixed-left-column {
    position: -webkit-sticky !important; /* Safari兼容性 */
    position: sticky !important;
    left: 30px !important; /* 设置在checkbox列之后，checkbox列约30px宽 */
    z-index: 10 !important;
    background-color: #ffffff !important;
    border-right: 2px solid #dee2e6 !important;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
}

/* 固定右侧操作列 */
.fixed-right-column {
    position: -webkit-sticky !important; /* Safari兼容性 */
    position: sticky !important;
    right: 0 !important;
    z-index: 10 !important;
    background-color: #ffffff !important;
    border-left: 2px solid #dee2e6 !important;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1) !important;
}

/* 表头固定列样式 */
.ui-jqgrid-htable .fixed-checkbox-column {
    background-color: #f5f5f5 !important;
}

.ui-jqgrid-htable .fixed-left-column {
    background-color: #f5f5f5 !important;
}

.ui-jqgrid-htable .fixed-right-column {
    background-color: #f5f5f5 !important;
}

/* 确保表格容器支持横向滚动 */
.ui-jqgrid {
    overflow-x: auto;
}

.ui-jqgrid-bdiv {
    overflow-x: auto;
    max-height: 500px; /* 限制表格高度，超过显示垂直滚动条 */
}
