# 表格UI交互优化使用指南

## 基于 applyListAll.html 的标准实现

本指南基于 `modules/hsobs-module-hs/src/main/resources/views/modules/apply/include/applyListAll.html` 文件，为后续其他文件的UI交互优化提供标准模板。

## 核心特性

1. **筛选条件布局**：每行3个查询条件，整齐排列
2. **表格固定列**：左侧固定checkbox和第一列，右侧固定操作列
3. **横向滚动**：超过5列时支持横向滚动查看
4. **左对齐文字**：所有表格内容统一左对齐
5. **加载优化**：避免加载框卡死问题

## 标准HTML结构

### 1. 页面头部
```html
<% layout('/layouts/default.html', {title: '页面标题', libs: ['dataGrid']}){ %>
<!-- 可选：引入公共表格样式 -->
<!-- <link rel="stylesheet" href="${ctx}/static/css/common-table.css"> -->
```

### 2. 筛选条件区域
```html
<div class="search-form-container">
    <#form:form id="searchForm" model="${entity}" action="${ctx}/path/listQueryData" method="post" class="form-inline hide">
        <!-- 第一行：3个查询条件 -->
        <div class="search-form-row">
            <div class="form-group">
                <label class="control-label">条件1：</label>
                <div class="control-inline">
                    <#form:input path="field1" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">条件2：</label>
                <div class="control-inline">
                    <#form:select path="field2" dictType="dict_type" class="form-control"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">日期范围：</label>
                <div class="control-inline">
                    <#form:input path="startDate" class="form-control laydate width-datetime"/>
                    &nbsp;-&nbsp;
                    <#form:input path="endDate" class="form-control laydate width-datetime"/>
                </div>
            </div>
        </div>
        
        <!-- 按钮行 -->
        <div class="search-button-row">
            <button type="submit" class="btn btn-primary btn-sm">
                <i class="glyphicon glyphicon-search"></i> 查询
            </button>
            <button type="reset" class="btn btn-default btn-sm">
                <i class="glyphicon glyphicon-repeat"></i> 重置
            </button>
        </div>
    </#form:form>
</div>
```

### 3. 表格区域
```html
<div class="fixed-table-container">
    <table id="dataGrid"></table>
    <div id="dataGridPage"></div>
</div>
```

## 标准JavaScript配置

### 1. DataGrid配置
```javascript
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false, // 禁用自动调整列宽
    autowidth: false, // 禁用自动宽度
    scroll: true, // 启用滚动条
    scrollOffset: 18,
    width: '100%', // 表格宽度
    height: 'auto', // 表格高度自适应
    columnModel: [
        {header: '编号', name: 'id', sortable: false, align: "left", width: 150},
        {header: '名称', name: 'name', sortable: false, align: "left", width: 120},
        {header: '状态', name: 'status', sortable: false, align: "left", width: 100},
        {header: '操作', name: 'actions', align: "left", width: 150, formatter: function(val, obj, row, act) {
            return '<a href="#" class="btn btn-sm btn-primary">编辑</a>';
        }}
    ],
    loadComplete: function() {
        // 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
        requestAnimationFrame(function() {
            setupFixedColumns();
        });
        js.closeLoading(0, true);
    }
});
```

### 2. 公共功能初始化
```javascript
<!-- 引入公共表格JS -->
<script>
    // 初始化公共表格功能
    CommonTable.init('dataGrid');
</script>
```

## 关键CSS类说明

### 筛选条件相关
- `.search-form-container` - 筛选条件容器
- `.search-form-row` - 筛选条件行（每行3个条件）
- `.search-button-row` - 按钮行
- `.width-120` - 普通输入框样式类
- `.width-datetime` - 日期输入框样式类

### 表格相关
- `.fixed-table-container` - 表格容器
- `.fixed-checkbox-column` - 固定的checkbox列
- `.fixed-left-column` - 固定的左侧列
- `.fixed-right-column` - 固定的右侧列

## 实施步骤

### 1. 复制标准结构
从 `applyListAll.html` 复制以下部分：
- HTML结构（筛选条件区域和表格区域）
- JavaScript配置（DataGrid配置和公共功能初始化）

### 2. 调整具体内容
- 修改筛选条件字段和标签
- 调整表格列配置
- 更新表单action和model

### 3. 引入公共文件
```html
<!-- 可选：如果需要完整样式支持 -->
<link rel="stylesheet" href="${ctx}/static/css/common-table.css">

<!-- 必需：公共功能支持 -->
<script src="${ctx}/static/js/common-table.js"></script>
```

### 4. 测试验证
- 确认筛选条件每行显示3个
- 验证表格固定列效果
- 测试横向滚动功能
- 检查加载框正常关闭

## 注意事项

1. **保持一致性**：严格按照标准结构实施，确保所有页面风格统一
2. **CSS类名**：使用标准的CSS类名，不要随意修改
3. **JavaScript配置**：保持DataGrid配置的一致性，特别是固定列相关配置
4. **测试充分**：每次实施后都要测试所有功能是否正常

## 支持文件

- `common-table.css` - 公共样式文件（可选）
- `common-table.js` - 公共功能文件（必需）
- `README-table-common.md` - 详细技术文档
- `applyListAll.html` - 标准实现参考

通过遵循这个指南，可以确保所有页面的UI交互优化都保持一致的高质量标准。
