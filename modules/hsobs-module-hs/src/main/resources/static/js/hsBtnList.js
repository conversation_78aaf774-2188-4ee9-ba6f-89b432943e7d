/**
 * 处理带有 hsBtnList 类的链接点击事件
 * 用于在打开新标签页前检查链接是否可访问
 */
(function($) {
    'use strict';
    
    // 初始化 hsBtnList 功能
    $.fn.initHsBtnList = function() {
        $(document).on('click', 'a.hsBtnList', function(e) {
            e.preventDefault(); // 阻止默认行为
            var $this = $(this);
            var href = $this.attr('href');
            var title = $this.attr('title') || $this.attr('data-title')  || '${text("详情")}';
            
            // 检查是否是表单链接（包含 /form? 的链接）
                $.ajax({
                    url: href,
                    type: 'get',
                    success: function(response) {
                        // 如果请求成功，使用 js.addTabPage 打开新标签页
                        js.addTabPage($this, title, href, true, true);
                        js.closeLoading(0, true);

                    },
                    error: function(xhr, status, error) {
                        // 如果请求失败，显示错误信息
                        var errorMsg = '${text("加载失败")}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        }
                        js.closeLoading(0, true);
                        js.showErrorMessage(errorMsg);
                    }
                });
        });
    };
    
    // 页面加载完成后自动初始化
    $(document).ready(function() {
        $.fn.initHsBtnList();
    });
    
})(jQuery); 