/**
 * 公共表格功能 - 筛选条件和固定列
 * 使用方法：在页面中引入此JS文件
 * <script src="${ctx}/static/js/common-table.js"></script>
 *
 * 主要功能：
 * 1. setupFixedColumns() - 设置表格固定列
 * 2. initTableObserver() - 初始化表格变化监听
 * 3. 自动应用固定列效果
 */

(function(window, $) {
    'use strict';

    // 命名空间
    window.CommonTable = window.CommonTable || {};

    /**
     * 设置固定列的函数
     * @param {string} gridId - 表格ID，默认为 'dataGrid'
     */
    function setupFixedColumns(gridId) {
        gridId = gridId || 'dataGrid';

        try {
            var $grid = $('#' + gridId);
            var $gridContainer = $grid.closest('.ui-jqgrid');
            var $gridTable = $gridContainer.find('.ui-jqgrid-btable');
            var $gridHeader = $gridContainer.find('.ui-jqgrid-htable');

            // 快速检查，如果表格未加载完成则延迟执行
            if ($gridTable.length === 0 || $gridHeader.length === 0) {
                setTimeout(function() {
                    setupFixedColumns(gridId);
                }, 100);
                return;
            }

            // 移除之前的固定列样式
            $('.fixed-checkbox-column, .fixed-left-column, .fixed-right-column')
                .removeClass('fixed-checkbox-column fixed-left-column fixed-right-column');

            // 为表头添加固定列样式
            $gridHeader.find('tr th').each(function(index) {
                var $cell = $(this);
                if (index === 0) {
                    $cell.addClass('fixed-checkbox-column');
                } else if (index === 1) {
                    $cell.addClass('fixed-left-column');
                } else if (index === $gridHeader.find('tr:first th').length - 1) {
                    $cell.addClass('fixed-right-column');
                }
            });

            // 为表格数据行添加固定列样式
            $gridTable.find('tr').each(function() {
                var $cells = $(this).find('td');
                if ($cells.length > 2) {
                    $cells.eq(0).addClass('fixed-checkbox-column');
                    $cells.eq(1).addClass('fixed-left-column');
                    $cells.last().addClass('fixed-right-column');
                }
            });

            // 确保表格容器支持横向滚动
            $gridContainer.find('.ui-jqgrid-bdiv').css({
                'overflow-x': 'auto',
                'overflow-y': 'auto'
            });

            console.log('固定列设置完成 - ' + gridId);
        } catch (e) {
            console.error('设置固定列时出错:', e);
        }
    }

    /**
     * 初始化表格变化监听
     * @param {string} gridId - 表格ID，默认为 'dataGrid'
     */
    function initTableObserver(gridId) {
        gridId = gridId || 'dataGrid';

        // 监听表格的变化，当有新数据时重新应用固定列
        var lastUpdateTime = 0;
        var observer = new MutationObserver(function(mutations) {
            var now = Date.now();
            // 防抖：限制更新频率，避免频繁触发影响性能
            if (now - lastUpdateTime < 300) {
                return;
            }

            var shouldUpdate = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' &&
                    mutation.target.tagName === 'TBODY') {
                    shouldUpdate = true;
                }
            });

            if (shouldUpdate) {
                lastUpdateTime = now;
                // 使用requestAnimationFrame确保在下一帧执行，不影响当前操作
                requestAnimationFrame(function() {
                    setupFixedColumns(gridId);
                });
            }
        });

        // 开始观察表格变化
        setTimeout(function() {
            var $gridTBody = $('#' + gridId).closest('.ui-jqgrid').find('.ui-jqgrid-btable tbody');
            if ($gridTBody.length > 0) {
                observer.observe($gridTBody[0], {
                    childList: true,
                    subtree: false // 只监听直接子元素变化，减少触发频率
                });
                console.log('开始监听表格变化 - ' + gridId);
            }
        }, 2000); // 延迟更长时间，确保页面完全加载完成
    }

    /**
     * 获取DataGrid配置的固定列选项
     * @returns {object} DataGrid配置对象
     */
    function getFixedColumnDataGridOptions() {
        return {
            shrinkToFit: false, // 禁用自动调整列宽
            autowidth: false, // 禁用自动宽度
            scroll: true, // 启用滚动条
            scrollOffset: 18,
            width: '100%', // 表格宽度
            height: 'auto', // 表格高度自适应

            // 加载成功后执行事件
            ajaxSuccess: function(data) {
                // 不在这里调用setupFixedColumns，避免影响加载框关闭
            },

            // 表格完成加载后的回调
            loadComplete: function() {
                // 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
                requestAnimationFrame(function() {
                    setupFixedColumns();
                });
            }
        };
    }

    /**
     * 初始化公共表格功能
     * @param {string} gridId - 表格ID，默认为 'dataGrid'
     * @param {object} options - 配置选项
     */
    function init(gridId, options) {
        gridId = gridId || 'dataGrid';
        options = options || {};

        // 页面加载完成后立即执行
        $(document).ready(function() {
            setTimeout(function() {
                setupFixedColumns(gridId);
            }, 500);

            // 初始化表格变化监听
            if (options.enableObserver !== false) {
                initTableObserver(gridId);
            }
        });
    }

    // 暴露公共方法
    window.CommonTable = {
        setupFixedColumns: setupFixedColumns,
        initTableObserver: initTableObserver,
        getFixedColumnDataGridOptions: getFixedColumnDataGridOptions,
        init: init
    };

})(window, jQuery);
