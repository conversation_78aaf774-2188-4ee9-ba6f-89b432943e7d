<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>固定列功能测试</title>
    <link rel="stylesheet" href="css/common-table.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { margin-bottom: 30px; }
        .test-table { width: 100%; border-collapse: collapse; }
        .test-table th, .test-table td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: left; 
            white-space: nowrap;
        }
        .test-table th { background-color: #f5f5f5; }
        .table-container { 
            overflow-x: auto; 
            max-width: 600px; 
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>

<h1>固定列功能测试</h1>

<div class="test-container">
    <h2>测试1：基本固定列效果</h2>
    <p>这个表格应该显示：左侧固定checkbox和第一列，右侧固定操作列</p>
    
    <div class="table-container">
        <table class="test-table">
            <thead>
                <tr>
                    <th class="fixed-checkbox-column">☑</th>
                    <th class="fixed-left-column">ID</th>
                    <th>列1</th>
                    <th>列2</th>
                    <th>列3</th>
                    <th>列4</th>
                    <th>列5</th>
                    <th>列6</th>
                    <th>列7</th>
                    <th>列8</th>
                    <th class="fixed-right-column">操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="fixed-checkbox-column">☑</td>
                    <td class="fixed-left-column">001</td>
                    <td>数据1-1</td>
                    <td>数据1-2</td>
                    <td>数据1-3</td>
                    <td>数据1-4</td>
                    <td>数据1-5</td>
                    <td>数据1-6</td>
                    <td>数据1-7</td>
                    <td>数据1-8</td>
                    <td class="fixed-right-column">编辑 删除</td>
                </tr>
                <tr>
                    <td class="fixed-checkbox-column">☐</td>
                    <td class="fixed-left-column">002</td>
                    <td>数据2-1</td>
                    <td>数据2-2</td>
                    <td>数据2-3</td>
                    <td>数据2-4</td>
                    <td>数据2-5</td>
                    <td>数据2-6</td>
                    <td>数据2-7</td>
                    <td>数据2-8</td>
                    <td class="fixed-right-column">编辑 删除</td>
                </tr>
                <tr>
                    <td class="fixed-checkbox-column">☐</td>
                    <td class="fixed-left-column">003</td>
                    <td>数据3-1</td>
                    <td>数据3-2</td>
                    <td>数据3-3</td>
                    <td>数据3-4</td>
                    <td>数据3-5</td>
                    <td>数据3-6</td>
                    <td>数据3-7</td>
                    <td>数据3-8</td>
                    <td class="fixed-right-column">编辑 删除</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<div class="test-container">
    <h2>测试说明</h2>
    <ul>
        <li>如果固定列正常工作，您应该看到：</li>
        <li>1. 左侧的checkbox列和ID列保持固定</li>
        <li>2. 右侧的操作列保持固定</li>
        <li>3. 中间的列可以横向滚动</li>
        <li>4. 固定列有阴影效果</li>
    </ul>
    
    <h3>如果固定列不工作，可能的原因：</h3>
    <ul>
        <li>浏览器不支持 position: sticky</li>
        <li>CSS文件未正确加载</li>
        <li>样式被其他CSS覆盖</li>
    </ul>
</div>

<script>
// 检查浏览器支持
document.addEventListener('DOMContentLoaded', function() {
    var testDiv = document.createElement('div');
    testDiv.style.position = 'sticky';
    
    if (testDiv.style.position === 'sticky') {
        console.log('✅ 浏览器支持 position: sticky');
    } else {
        console.log('❌ 浏览器不支持 position: sticky');
        alert('您的浏览器不支持 position: sticky，固定列功能可能无法正常工作。请使用现代浏览器。');
    }
    
    // 检查CSS类是否应用
    var fixedElements = document.querySelectorAll('.fixed-checkbox-column, .fixed-left-column, .fixed-right-column');
    console.log('找到固定列元素数量:', fixedElements.length);
    
    fixedElements.forEach(function(el, index) {
        var styles = window.getComputedStyle(el);
        console.log('元素 ' + index + ' 的 position 值:', styles.position);
    });
});
</script>

</body>
</html>
