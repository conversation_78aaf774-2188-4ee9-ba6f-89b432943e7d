# 公共表格功能使用说明

## 概述

本项目提供了一套公共的表格功能，包括：
1. **筛选条件区域样式** - 统一的筛选条件布局（每行3个条件）
2. **表格固定列功能** - 左侧固定checkbox和第一列，右侧固定操作列

## 文件结构

```
static/
├── css/
│   └── common-table.css          # 公共表格样式（可选）
├── js/
│   └── common-table.js           # 公共表格功能
└── examples/
    └── table-usage-example.html  # 使用示例
```

## 快速开始

### 1. 引入文件

**推荐方式（轻量级）**：只引入JS文件
```html
<script src="${ctx}/static/js/common-table.js"></script>
```

**完整方式**：同时引入CSS和JS文件
```html
<link rel="stylesheet" href="${ctx}/static/css/common-table.css">
<script src="${ctx}/static/js/common-table.js"></script>
```

### 2. 筛选条件区域

使用标准的HTML结构：
```html
<div class="search-form-container">
    <form id="searchForm" class="form-inline">
        <!-- 第一行：3个查询条件 -->
        <div class="search-form-row">
            <div class="form-group">
                <label class="control-label">条件1：</label>
                <div class="control-inline">
                    <input type="text" class="form-control" placeholder="请输入">
                </div>
            </div>
            <!-- 更多条件... -->
        </div>

        <!-- 按钮行 -->
        <div class="search-button-row">
            <button type="submit" class="btn btn-primary btn-sm">查询</button>
            <button type="reset" class="btn btn-default btn-sm">重置</button>
        </div>
    </form>
</div>
```

### 3. 表格固定列

#### 推荐方式（当前标准）：
```javascript
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false, // 禁用自动调整列宽
    autowidth: false, // 禁用自动宽度
    scroll: true, // 启用滚动条
    scrollOffset: 18,
    width: '100%', // 表格宽度
    height: 'auto', // 表格高度自适应
    columnModel: [
        // 列配置...
    ],
    loadComplete: function() {
        // 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
        requestAnimationFrame(function() {
            setupFixedColumns();
        });
        js.closeLoading(0, true);
    }
});

// 初始化公共表格功能
CommonTable.init('dataGrid');
```

#### 备选方式：使用预设配置
```javascript
$('#dataGrid').dataGrid($.extend({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    columnModel: [
        // 列配置...
    ]
}, CommonTable.getFixedColumnDataGridOptions()));

CommonTable.init('dataGrid');
```

## API 文档

### CommonTable.init(gridId, options)
初始化公共表格功能

**参数：**
- `gridId` (string): 表格ID，默认为 'dataGrid'
- `options` (object): 配置选项
  - `enableObserver` (boolean): 是否启用变化监听，默认为 true

**示例：**
```javascript
CommonTable.init('myTable', {
    enableObserver: true
});
```

### CommonTable.setupFixedColumns(gridId)
手动设置表格固定列

**参数：**
- `gridId` (string): 表格ID，默认为 'dataGrid'

**示例：**
```javascript
CommonTable.setupFixedColumns('myTable');
```

### CommonTable.getFixedColumnDataGridOptions()
获取DataGrid的固定列配置选项

**返回：**
- (object): DataGrid配置对象

**示例：**
```javascript
var options = CommonTable.getFixedColumnDataGridOptions();
$('#dataGrid').dataGrid($.extend(options, {
    // 其他配置...
}));
```

### CommonTable.initTableObserver(gridId)
初始化表格变化监听

**参数：**
- `gridId` (string): 表格ID，默认为 'dataGrid'

## 样式说明

### 筛选条件样式类

- `.search-form-container` - 筛选条件容器
- `.search-form-row` - 筛选条件行（每行3个条件）
- `.search-button-row` - 按钮行

### 固定列样式类

- `.fixed-checkbox-column` - 固定的checkbox列
- `.fixed-left-column` - 固定的左侧列（通常是第一个数据列）
- `.fixed-right-column` - 固定的右侧列（通常是操作列）

## 完整使用示例

基于当前标准的完整页面示例：

```html
<% layout('/layouts/default.html', {title: '页面标题', libs: ['dataGrid']}){ %>

<div class="main-content">
    ${layoutContent!}
    <div class="box-body">
        <!-- 筛选条件区域 -->
        <div class="search-form-container">
            <#form:form id="searchForm" model="${entity}" action="${ctx}/path/listQueryData" method="post" class="form-inline hide">
                <!-- 第一行：3个查询条件 -->
                <div class="search-form-row">
                    <div class="form-group">
                        <label class="control-label">条件1：</label>
                        <div class="control-inline">
                            <#form:input path="field1" class="form-control width-120"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label">条件2：</label>
                        <div class="control-inline">
                            <#form:select path="field2" dictType="dict_type" class="form-control"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label">日期范围：</label>
                        <div class="control-inline">
                            <#form:input path="startDate" class="form-control laydate width-datetime"/>
                            &nbsp;-&nbsp;
                            <#form:input path="endDate" class="form-control laydate width-datetime"/>
                        </div>
                    </div>
                </div>

                <!-- 按钮行 -->
                <div class="search-button-row">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="glyphicon glyphicon-search"></i> 查询
                    </button>
                    <button type="reset" class="btn btn-default btn-sm">
                        <i class="glyphicon glyphicon-repeat"></i> 重置
                    </button>
                </div>
            </#form:form>
        </div>

        <!-- 表格区域 -->
        <div class="fixed-table-container">
            <table id="dataGrid"></table>
            <div id="dataGridPage"></div>
        </div>
    </div>
</div>
<% } %>

<script>
    $('#dataGrid').dataGrid({
        searchForm: $('#searchForm'),
        showCheckbox: true,
        shrinkToFit: false,
        autowidth: false,
        scroll: true,
        scrollOffset: 18,
        width: '100%',
        height: 'auto',
        columnModel: [
            {header: '编号', name: 'id', sortable: false, align: "left", width: 150},
            {header: '名称', name: 'name', sortable: false, align: "left", width: 120},
            {header: '状态', name: 'status', sortable: false, align: "left", width: 100},
            {header: '操作', name: 'actions', align: "left", width: 150, formatter: function(val, obj, row, act) {
                return '<a href="#" class="btn btn-sm btn-primary">编辑</a>';
            }}
        ],
        loadComplete: function() {
            requestAnimationFrame(function() {
                setupFixedColumns();
            });
            js.closeLoading(0, true);
        }
    });
</script>

<script>
    CommonTable.init('dataGrid');
</script>
```

## 注意事项

1. **表格列配置**：
   - 第一列通常是checkbox列
   - 第二列是主要数据列（如ID、编号等）
   - 最后一列是操作列
   - 所有列使用 `align: "left"` 左对齐

2. **筛选条件**：
   - 每行最多3个条件
   - 使用 `width-120` 类设置输入框宽度
   - 日期范围使用 `width-datetime` 类

3. **必需的HTML结构**：
   - 筛选条件容器：`.search-form-container`
   - 筛选条件行：`.search-form-row`
   - 按钮行：`.search-button-row`
   - 表格容器：`.fixed-table-container`

4. **兼容性**：
   - 需要jQuery支持
   - 需要jqGrid支持
   - 支持现代浏览器的sticky定位

5. **性能优化**：
   - 使用防抖机制避免频繁更新
   - 使用requestAnimationFrame优化渲染
   - 限制MutationObserver的监听范围

## 故障排除

### 固定列不生效
1. 检查是否正确引入JS文件：`<script src="${ctx}/static/js/common-table.js"></script>`
2. 确认调用了 `CommonTable.init('dataGrid')`
3. 检查表格是否完全加载完成
4. 查看浏览器控制台是否有错误信息
5. 确认使用了正确的HTML结构（`.search-form-container`, `.fixed-table-container`等）

### 加载框不关闭
1. 确保在 `loadComplete` 中使用了 `requestAnimationFrame`
2. 确保调用了 `js.closeLoading(0, true)`
3. 避免在 `ajaxSuccess` 中直接调用固定列函数

### 筛选条件布局问题
1. 检查是否使用了正确的CSS类：`.search-form-row`, `.form-group`
2. 确认每行不超过3个筛选条件
3. 检查是否有其他CSS样式冲突

### 样式冲突
1. 如果使用了公共CSS文件，检查是否有其他CSS覆盖
2. 确保CSS文件的引入顺序正确
3. 可以只使用JS文件，依赖内联样式

## 更新日志

### v1.1 (当前版本)
- 简化了引入方式，支持只使用JS文件
- 优化了固定列的实现方式
- 改进了加载框关闭的处理
- 提供了更灵活的配置选项

### v1.0
- 初始版本，提供基础的筛选条件和固定列功能
