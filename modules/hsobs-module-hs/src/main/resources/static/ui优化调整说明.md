# 表格UI交互优化使用指南

## 基于标准实现的完整指南

本指南基于 `applyListAll.html` 和 `rentalHouseList.html` 的实际实现，为后续其他文件的UI交互优化提供标准模板和最佳实践。

## 核心特性

1. **筛选条件布局**：每行3个查询条件，整齐排列，统一样式
2. **表格固定列**：左侧固定checkbox和第一列，右侧固定操作列
3. **横向滚动**：超过5列时支持横向滚动查看
4. **左对齐文字**：所有表格内容统一左对齐
5. **加载优化**：避免加载框卡死问题
6. **浏览器兼容**：支持现代浏览器的sticky定位

## 公共文件引入

**重要说明**：公共CSS和JS文件已在 `default.html` 中统一引入，无需在各个页面中重复引入。

在 `modules/core/src/main/resources/views/layouts/default.html` 中已包含：
```html
<link rel="stylesheet" href="${ctxStatic}/css/common-table.css">
<script src="${ctxStatic}/js/common-table.js"></script>
```

因此，各个页面只需要：
1. 使用标准的HTML结构
2. 使用标准的JavaScript配置

## 标准HTML结构

### 1. 筛选条件区域
```html
<div class="search-form-container">
    <#form:form id="searchForm" model="${entity}" action="${ctx}/path/listQueryData" method="post" class="form-inline hide">
        <!-- 第一行：3个查询条件 -->
        <div class="search-form-row">
            <div class="form-group">
                <label class="control-label">条件1：</label>
                <div class="control-inline">
                    <#form:input path="field1" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">条件2：</label>
                <div class="control-inline">
                    <#form:select path="field2" dictType="dict_type" class="form-control"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">日期范围：</label>
                <div class="control-inline">
                    <#form:input path="startDate" class="form-control laydate width-datetime"/>
                    &nbsp;-&nbsp;
                    <#form:input path="endDate" class="form-control laydate width-datetime"/>
                </div>
            </div>
        </div>

        <!-- 按钮行 -->
        <div class="search-button-row">
            <button type="submit" class="btn btn-primary btn-sm">
                <i class="glyphicon glyphicon-search"></i> 查询
            </button>
            <button type="reset" class="btn btn-default btn-sm">
                <i class="glyphicon glyphicon-repeat"></i> 重置
            </button>
        </div>
    </#form:form>
</div>
```

### 2. 表格区域
```html
<div class="fixed-table-container">
    <table id="dataGrid"></table>
    <div id="dataGridPage"></div>
</div>
```

## 标准JavaScript配置

### 1. DataGrid配置
```javascript
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false, // 禁用自动调整列宽
    autowidth: false, // 禁用自动宽度
    scrollOffset: 18,
    width: '100%', // 表格宽度
    height: 'auto', // 表格高度自适应
    columnModel: [
        {header: '编号', name: 'id', sortable: false, align: "left", width: 150},
        {header: '名称', name: 'name', sortable: false, align: "left", width: 120},
        {header: '状态', name: 'status', sortable: false, align: "left", width: 100},
        {header: '操作', name: 'actions', align: "left", width: 150, formatter: function(val, obj, row, act) {
            return '<a href="#" class="btn btn-sm btn-primary">编辑</a>';
        }}
    ],
    ajaxSuccess: function() {
        // 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
        requestAnimationFrame(function() {
            CommonTable.setupFixedColumns('dataGrid'); // 注意：使用正确的函数调用
        });
    }
});
```

**重要配置说明**：
- `shrinkToFit: false` 和 `autowidth: false` 是固定列功能的必需配置
- 所有列必须设置 `align: "left"` 实现左对齐
- `ajaxSuccess` 中必须使用 `CommonTable.setupFixedColumns('dataGrid')` 而不是 `setupFixedColumns()`


**重要注意事项**：
- 建议添加调试代码检查固定列是否正常应用

## 关键CSS类说明

### 筛选条件相关
- `.search-form-container` - 筛选条件容器
- `.search-form-row` - 筛选条件行（每行3个条件）
- `.search-button-row` - 按钮行
- `.width-120` - 普通输入框样式类
- `.width-datetime` - 日期输入框样式类

### 表格相关
- `.fixed-table-container` - 表格容器
- `.fixed-checkbox-column` - 固定的checkbox列
- `.fixed-left-column` - 固定的左侧列
- `.fixed-right-column` - 固定的右侧列

## 实施步骤

### 1. 复制标准结构
从 `applyListAll.html` 或 `rentalHouseList.html` 复制以下部分：
- HTML结构（筛选条件区域和表格区域）
- JavaScript配置（DataGrid配置和公共功能初始化）

### 2. 调整具体内容
- 修改筛选条件字段和标签
- 调整表格列配置（确保所有列都有 `align: "left"`）
- 更新表单action和model


### 3. 测试验证
- 确认筛选条件每行显示3个
- 验证表格固定列效果（使用浏览器开发者工具检查）
- 测试横向滚动功能
- 检查加载框正常关闭
- 查看浏览器控制台确认无JavaScript错误

### 4. 故障排除
如果固定列不生效，按以下步骤检查：

#### 4.1 检查浏览器兼容性
```javascript
// 在控制台运行此代码检查浏览器支持
var testDiv = document.createElement('div');
testDiv.style.position = 'sticky';
console.log('浏览器支持sticky:', testDiv.style.position === 'sticky');
```

#### 4.2 检查CSS类应用
```javascript
// 检查固定列样式是否正确应用
console.log('checkbox列:', $('.fixed-checkbox-column').length);
console.log('左固定列:', $('.fixed-left-column').length);
console.log('右固定列:', $('.fixed-right-column').length);
```

#### 4.3 检查公共文件是否正确加载
```javascript
// 检查CommonTable对象是否存在
console.log('CommonTable对象:', typeof CommonTable);
console.log('CommonTable方法:', Object.keys(CommonTable || {}));
```

#### 4.4 手动触发固定列设置
```javascript
// 如果自动设置失败，手动触发
CommonTable.setupFixedColumns('dataGrid');
```

#### 4.5 使用测试页面
访问 `/static/test-fixed-columns.html` 测试基础固定列功能

## 注意事项

### 关键要点
1. **保持一致性**：严格按照标准结构实施，确保所有页面风格统一
2. **CSS类名**：使用标准的CSS类名，不要随意修改
3. **JavaScript配置**：保持DataGrid配置的一致性，特别是固定列相关配置
4. **公共文件依赖**：确保 `default.html` 中已正确引入公共CSS和JS文件
5. **测试充分**：每次实施后都要测试所有功能是否正常

### 常见错误避免
1. **函数调用错误**：使用 `CommonTable.setupFixedColumns('dataGrid')` 而不是 `setupFixedColumns()`
3. **浏览器兼容性**：确保使用支持sticky定位的现代浏览器
4. **样式冲突**：检查是否有其他CSS覆盖了固定列样式
5. **表格未完全加载**：确保在表格数据加载完成后再应用固定列
6. **公共文件未加载**：检查 `default.html` 中的公共文件引入路径是否正确

### 浏览器支持
- **Chrome**: 56+ ✅
- **Firefox**: 32+ ✅
- **Safari**: 6.1+ ✅
- **Edge**: 16+ ✅
- **IE**: 不支持 ❌

### 性能优化建议
1. 使用 `requestAnimationFrame` 避免阻塞UI
2. 添加防抖机制减少频繁触发
3. 限制MutationObserver的监听范围
4. 适当延迟初始化时间

## 支持文件

- `common-table.css` - 公共样式文件（已在default.html中引入）
- `common-table.js` - 公共功能文件（已在default.html中引入）
- `test-fixed-columns.html` - 功能测试页面
- `README-table-common.md` - 详细技术文档
- `applyListAll.html` - 标准实现参考
- `rentalHouseList.html` - 实际应用示例
- `default.html` - 布局模板（包含公共文件引入）

## 版本更新记录

### v1.3 (当前版本)
- **重要更新**：公共CSS和JS文件已在 `default.html` 中统一引入
- 简化了页面实施流程，无需手动引入公共文件
- 更新了故障排除指南，增加了公共文件加载检查
- 优化了初始化流程说明

### v1.2
- 修复了函数调用错误问题
- 完善了浏览器兼容性支持
- 添加了详细的故障排除指南
- 提供了测试页面和调试方法

### v1.1
- 简化了引入方式
- 优化了固定列实现
- 改进了加载框处理

### v1.0
- 初始版本，基础功能实现

## 总结

通过遵循这个指南，可以确保所有页面的UI交互优化都保持一致的高质量标准。

**关键优势**：
- ✅ 公共文件统一管理，避免重复引入
- ✅ 标准化的HTML结构和JavaScript配置
- ✅ 完善的故障排除和调试机制
- ✅ 简化的实施流程，提高开发效率
