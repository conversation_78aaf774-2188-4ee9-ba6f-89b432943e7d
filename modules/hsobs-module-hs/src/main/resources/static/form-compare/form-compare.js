/**
 * 表单对比插件
 */
(function($) {
    "use strict";
    
    $.fn.formCompare = function(options) {
        // 默认配置
        var defaults = {
            tooltipClass: 'form-compare-tooltip',
            highlightClass: 'form-compare-highlight',
            onDismiss: null,
            confirmMessage: '是否确认删除该提醒？'
        };
        
        // 合并配置
        var settings = $.extend({}, defaults, options);
        
        // 初始化样式
        initStyles();
        
        // 处理变更
        if (options && options.changeMap) {
            for (var key in options.changeMap) {
                if (options.changeMap.hasOwnProperty(key)) {
                    handleChange(key, options.changeMap[key]);
                }
            }
        }

        // 初始化样式
        function initStyles() {
            if (!$('#formCompareStyles').length) {
                $('<style id="formCompareStyles">')
                    .text(`
                        /* 感叹号图标样式 */
                        .change-indicator {
                            cursor: pointer;
                            vertical-align: middle;
                            transition: transform 0.2s;
                            position: relative;
                            z-index: 1;
                            color: #dc3545;
                            margin-left: 5px;
                            font-size: 16px;
                            display: inline-block;
                        }
                        
                        .change-indicator:hover {
                            transform: scale(1.1);
                        }
                        
                        /* 提示框样式 */
                        .${settings.tooltipClass} {
                            background: #dc3545;
                            border: 1px solid #dc3545;
                            padding: 8px 12px;
                            font-size: 12px;
                            border-radius: 3px;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                            min-width: 200px;
                            max-width: 300px;
                            white-space: pre-line;
                            line-height: 1.5;
                            pointer-events: auto;
                            z-index: 100000;
                            color: white;
                        }
                        
                        /* DataGrid 中的感叹号位置调整 */
                        .ui-jqgrid tr.jqgrow td .change-indicator {
                            float: right;
                        }

                        /* 高亮样式 */
                        .${settings.highlightClass} {
                            background-color: #ffe6e6 !important;
                        }
                        
                        /* 文件上传区域高亮 */
                        .wup_container.${settings.highlightClass} {
                            background-color: #ffe6e6 !important;
                            border: 2px solid #dc3545 !important;
                            border-radius: 4px;
                            padding: 8px !important;
                        }

                        /* 表格单元格高亮 */
                        td.${settings.highlightClass} {
                            background-color: #ffe6e6 !important;
                            border: 1px solid #dc3545 !important;
                        }

                        /* DataGrid 单元格高亮 */
                        .ui-jqgrid tr.jqgrow td.${settings.highlightClass} {
                            background-color: #ffe6e6 !important;
                            border: 1px solid #dc3545 !important;
                        }
                    `)
                    .appendTo('head');
            }
        }

        // 主处理方法
        function handleChange(key, message) {
            try {
                
                // 检查是否是数组字段
                if (key.indexOf('[') !== -1 && key.indexOf(']') !== -1) {
                    handleArrayField(key, message);
                } // 检查是否是文件字段 - 修复条件判断
                else if (key.indexOf('_') !== -1 || key.indexOf('uploadImage') !== -1) {
                    handleFileChange(key, message);
                } 
                else {
                    handleSingleField(key, message);
                }
            } catch (e) {
                console.error('处理变更失败:', e);
            }
        }

        // 处理单个字段
        function handleSingleField(key, message) {
            console.log('处理单个字段:', {
                key: key,
                message: message
            });
            
            var fieldName = key.split('.').pop();
            var $field = $('[name$="' + fieldName + '"]');
            
            console.log('查找字段结果:', {
                fieldName: fieldName,
                found: $field.length > 0,
                elements: $field.map(function() {
                    return {
                        name: $(this).attr('name'),
                        type: this.type,
                        tagName: this.tagName
                    };
                }).get()
            });
            
            if ($field.length) {
                $field.addClass(settings.highlightClass);
                console.log('已添加高亮样式');
                addChangeIndicator($field, message);
            }
        }

        // 处理数组字段（包括 DataGrid）
        function handleArrayField(key, message) {
            try {
                console.log('处理数组字段:', {
                    key: key,
                    message: message
                });
                
                // 从 key 中提取 ID
                var matches = key.match(/\[(\d+)\]/);
                if (!matches) {
                    console.log('未找到ID');
                    return;
                }
                
                var rowId = matches[1];  // 直接使用 ID
                var fieldName = key.split('.').pop();
                
                // 查找页面上所有的 jqGrid
                var $grids = $('.ui-jqgrid-btable');
                console.log('找到的DataGrid数量:', $grids.length);
                
                $grids.each(function() {
                    var $grid = $(this);
                    console.log('检查DataGrid:', $grid.attr('id'));
                    
                    try {
                        // 直接使用 ID 查找行
                        var $cell = $grid.find('#' + rowId + ' td[aria-describedby$="_' + fieldName + '"]');
                        
                        console.log('单元格查找结果:', {
                            gridId: $grid.attr('id'),
                            rowId: rowId,
                            fieldName: fieldName,
                            found: $cell.length > 0
                        });
                        
                        if ($cell.length) {
                            $cell.addClass(settings.highlightClass);
                            console.log('已添加高亮样式到单元格');
                            addChangeIndicator($cell, message, rowId);
                            return false;
                        }
                    } catch (e) {
                        console.log('处理DataGrid失败:', e);
                    }
                });
            } catch (e) {
                console.error('处理数组字段失败:', e, e.stack);
            }
        }

        // 处理文件变更
        function handleFileChange(key, message) {
            try {
                console.log('开始处理文件变更:', {
                    key: key,
                    message: message
                });
                
                var $uploadArea;
                
                // 从完整路径中提取文件字段标识
                var fileKey = key.split('.').pop(); // 获取 hsQwApply_marry
                console.log('提取的文件标识:', {
                    originalKey: key,
                    fileKey: fileKey
                });
                
                // 1. 先尝试通过 dataMap 查找
                var selector = 'input[name="dataMap[' + fileKey + ']"]';
                console.log('尝试查找文件输入框:', {
                    selector: selector,
                    existingInputs: $('input[name*="dataMap"]').map(function() {
                        return {
                            name: $(this).attr('name'),
                            id: $(this).attr('id')
                        };
                    }).get()
                });
                
                $uploadArea = $(selector).closest('.wup_container');
                console.log('通过 dataMap 查找结果:', {
                    found: $uploadArea.length > 0,
                    uploadAreaId: $uploadArea.attr('id')
                });
                
                // 2. 如果没找到，尝试通过 uploadImage 查找
                if (!$uploadArea || !$uploadArea.length) {
                    console.log('尝试通过 uploadImage 查找');
                    // 查找所有 uploadImage 相关的输入框
                    $('input[id^="uploadImage"]').each(function() {
                        console.log('检查 uploadImage 输入框:', {
                            id: $(this).attr('id'),
                            name: $(this).attr('name'),
                            matchTarget: 'dataMap[' + fileKey + ']'
                        });
                        
                        if ($(this).attr('name') === 'dataMap[' + fileKey + ']') {
                            $uploadArea = $(this).closest('.wup_container');
                            console.log('找到匹配的上传区域:', {
                                inputId: $(this).attr('id'),
                                uploadAreaId: $uploadArea.attr('id')
                            });
                            return false; // 找到后停止遍历
                        }
                    });
                }
                
                console.log('最终查找结果:', {
                    found: $uploadArea && $uploadArea.length > 0,
                    uploadAreaId: $uploadArea ? $uploadArea.attr('id') : null,
                    uploadAreaHtml: $uploadArea ? $uploadArea.html() : null
                });

                if ($uploadArea && $uploadArea.length) {
                    // 添加高亮
                    $uploadArea.addClass(settings.highlightClass);
                    console.log('已添加高亮样式');
                    
                    // 找到表单组和标签
                    var $formGroup = $uploadArea.closest('.form-group');
                    var $label = $formGroup.find('label.control-label');
                    
                    console.log('表单组和标签查找结果:', {
                        formGroupFound: $formGroup.length > 0,
                        labelFound: $label.length > 0,
                        labelText: $label.text()
                    });
                    
                    if ($label.length) {
                        console.log('添加感叹号到标签');
                        addChangeIndicator($label, message);
                    } else {
                        console.log('添加感叹号到上传区域');
                        addChangeIndicator($uploadArea, message);
                    }
                } else {
                    console.warn('未找到文件上传区域:', {
                        key: key,
                        fileKey: fileKey,
                        allUploadImageInputs: $('input[id^="uploadImage"]').length,
                        allWupContainers: $('.wup_container').length
                    });
                }
            } catch (e) {
                console.error('处理文件变更失败:', e, e.stack);
            }
        }
        
        // 添加变更指示器
        function addChangeIndicator($element, message, rowId) {
            // 创建感叹号图标
            var $icon = $('<i>', {
                'class': 'fa fa-exclamation-circle change-indicator'
            });
            
            // 创建提示框
            var $tooltip = $('<div/>', {
                'class': settings.tooltipClass,
                'html': message.replace(/\\n/g, '<br>'),
                'css': {
                    'display': 'none',
                    'position': 'fixed'
                }
            }).appendTo('body');
            
            // 根据元素类型决定图标放置位置
            if ($element.is('td')) {
                $element.append($icon);
            } else if ($element.is('label')) {
                $element.append($icon);
            } else {
                var $label = $element.closest('.form-group').find('label.control-label');
                if ($label.length) {
                    $label.append($icon);
                } else {
                    $element.after($icon);
                }
            }
            
            // 绑定鼠标事件
            $icon.on({
                'mouseenter': function(e) {
                    var iconPos = $(this).offset();
                    var iconWidth = $(this).width();
                    var iconHeight = $(this).height();
                    var tooltipWidth = $tooltip.outerWidth();
                    var tooltipHeight = $tooltip.outerHeight();
                    var windowWidth = $(window).width();
                    var windowHeight = $(window).height();
                    
                    // 计算提示框位置
                    var left = iconPos.left + iconWidth + 10;
                    var top = iconPos.top - (tooltipHeight / 2) + (iconHeight / 2);
                    
                    // 确保提示框不会超出窗口右侧
                    if (left + tooltipWidth > windowWidth) {
                        left = iconPos.left - tooltipWidth - 10;
                    }
                    
                    // 确保提示框不会超出窗口顶部和底部
                    if (top < 0) {
                        top = 0;
                    } else if (top + tooltipHeight > windowHeight) {
                        top = windowHeight - tooltipHeight;
                    }
                    
                    $tooltip.css({
                        'left': left,
                        'top': top
                    }).fadeIn(200);
                },
                'mouseleave': function() {
                    $tooltip.fadeOut(200);
                },
                'click': function(e) {
                    e.stopPropagation();
                    
                    if (confirm(settings.confirmMessage)) {
                        if (typeof settings.onDismiss === 'function') {
                            settings.onDismiss({
                                element: $element,
                                message: message,
                                key: key,
                                rowId: rowId,
                                success: function() {
                                    $element.removeClass(settings.highlightClass);
                                    $icon.remove();
                                    $tooltip.remove();
                                    // 刷新当前行
                                    if (rowId) {
                                        var $grid = $element.closest('.ui-jqgrid-btable');
                                        if ($grid.length) {
                                            $grid.jqGrid('setRowData', rowId, null, {reloadAfterSubmit: false});
                                        }
                                    }
                                },
                                error: function(error) {
                                    alert('删除提醒失败：' + (error || '未知错误'));
                                }
                            });
                        }
                    }
                }
            });
            
            // 提示框的鼠标事件
            $tooltip.on({
                'mouseenter': function() {
                    $(this).stop(true, true).show();
                },
                'mouseleave': function() {
                    $(this).stop(true, true).fadeOut(200);
                }
            });
        }
        
        // 返回 jQuery 对象以支持链式调用
        return this;
    };
})(jQuery);