-- 创建公房房源智能核验表
CREATE TABLE hs_qw_apply_bureau_check (
    id VARCHAR2(64) NOT NULL,
    check_date TIMESTAMP,
    bureau_id VARCHAR2(64) NOT NULL,
    check_result CHAR(1) DEFAULT '0' NOT NULL,
    check_type CHAR(1) DEFAULT '0' NOT NULL,
    noticed CHAR(1) DEFAULT '0' NOT NULL,
    property_info CLOB,
    verify_detail CLOB,
    review_status CHAR(1) DEFAULT '0' NOT NULL,
    review_user VARCHAR2(64),
    review_date TIMESTAMP,
    review_remark VARCHAR2(500),
    clearance_status CHAR(1) DEFAULT '0' NOT NULL,
    clearance_date DATE,
    actual_clearance_date DATE,
    status CHAR(1) DEFAULT '0' NOT NULL,
    create_by VARCHAR2(64) NOT NULL,
    create_date TIMESTAMP NOT NULL,
    update_by VARCHAR2(64) NOT NULL,
    update_date TIMESTAMP NOT NULL,
    remarks VARCHAR2(500),
    PRIMARY KEY (id)
);

COMMENT ON TABLE hs_qw_apply_bureau_check IS '公房房源智能核验';
COMMENT ON COLUMN hs_qw_apply_bureau_check.id IS 'ID';
COMMENT ON COLUMN hs_qw_apply_bureau_check.check_date IS '核验时间';
COMMENT ON COLUMN hs_qw_apply_bureau_check.bureau_id IS '局直公房申请单ID';
COMMENT ON COLUMN hs_qw_apply_bureau_check.check_result IS '核验结果（0正常 1异常）';
COMMENT ON COLUMN hs_qw_apply_bureau_check.check_type IS '状态（0厅局公房 1省直公房）';
COMMENT ON COLUMN hs_qw_apply_bureau_check.noticed IS '通知状态（0未通知 1已通知）';
COMMENT ON COLUMN hs_qw_apply_bureau_check.property_info IS '不动产信息';
COMMENT ON COLUMN hs_qw_apply_bureau_check.verify_detail IS '核验详情';
COMMENT ON COLUMN hs_qw_apply_bureau_check.review_status IS '复核状态（0未复核 1已复核 2复核通过 3复核不通过）';
COMMENT ON COLUMN hs_qw_apply_bureau_check.review_user IS '复核人';
COMMENT ON COLUMN hs_qw_apply_bureau_check.review_date IS '复核时间';
COMMENT ON COLUMN hs_qw_apply_bureau_check.review_remark IS '复核备注';
COMMENT ON COLUMN hs_qw_apply_bureau_check.clearance_status IS '清退状态（0未清退 1已通知 2已清退）';
COMMENT ON COLUMN hs_qw_apply_bureau_check.clearance_date IS '应清退时间';
COMMENT ON COLUMN hs_qw_apply_bureau_check.actual_clearance_date IS '实际清退时间';
COMMENT ON COLUMN hs_qw_apply_bureau_check.status IS '状态（0正常 1删除 2停用）';
COMMENT ON COLUMN hs_qw_apply_bureau_check.create_by IS '创建者';
COMMENT ON COLUMN hs_qw_apply_bureau_check.create_date IS '创建时间';
COMMENT ON COLUMN hs_qw_apply_bureau_check.update_by IS '更新者';
COMMENT ON COLUMN hs_qw_apply_bureau_check.update_date IS '更新时间';
COMMENT ON COLUMN hs_qw_apply_bureau_check.remarks IS '备注信息';

-- 创建索引
CREATE INDEX idx_hs_qw_apply_bureau_check_bureau_id ON hs_qw_apply_bureau_check (bureau_id);
CREATE INDEX idx_hs_qw_apply_bureau_check_check_date ON hs_qw_apply_bureau_check (check_date);
CREATE INDEX idx_hs_qw_apply_bureau_check_check_result ON hs_qw_apply_bureau_check (check_result);
CREATE INDEX idx_hs_qw_apply_bureau_check_check_type ON hs_qw_apply_bureau_check (check_type);
CREATE INDEX idx_hs_qw_apply_bureau_check_review_status ON hs_qw_apply_bureau_check (review_status);
CREATE INDEX idx_hs_qw_apply_bureau_check_clearance_status ON hs_qw_apply_bureau_check (clearance_status);

-- 添加数据字典（如果需要）
-- 核验结果字典
INSERT INTO js_sys_dict_type(id, dict_name, dict_type, is_sys, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931970', '公房核验结果', 'hs_qw_apply_check_result', '0', '0', 'system', SYSDATE, 'system', SYSDATE, '公房房源智能核验结果');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931971', '0', '0,', 10, '000010', '1', 0, '正常', '正常', '0', 'hs_qw_apply_check_result', '0', '', '', 'text-success', '0', 'system', SYSDATE, 'system', SYSDATE, '');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931972', '0', '0,', 20, '000020', '1', 0, '异常', '异常', '1', 'hs_qw_apply_heck_result', '0', '', '', 'text-danger', '0', 'system', SYSDATE, 'system', SYSDATE, '');

-- 公房类型字典
INSERT INTO js_sys_dict_type(id, dict_name, dict_type, is_sys, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931973', '公房类型', 'hs_qw_apply_check_type', '0', '0', 'system', SYSDATE, 'system', SYSDATE, '公房类型');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931974', '0', '0,', 10, '000010', '1', 0, '厅局公房', '厅局公房', '0', 'hs_qw_apply_check_type', '0', '', '', '', '0', 'system', SYSDATE, 'system', SYSDATE, '');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931975', '0', '0,', 20, '000020', '1', 0, '省直公房', '省直公房', '1', 'hs_qw_apply_check_type', '0', '', '', '', '0', 'system', SYSDATE, 'system', SYSDATE, '');

-- 通知状态字典
INSERT INTO js_sys_dict_type(id, dict_name, dict_type, is_sys, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931976', '公房通知状态', 'hs_qw_apply_check_noticed', '0', '0', 'system', SYSDATE, 'system', SYSDATE, '公房通知状态');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931977', '0', '0,', 10, '000010', '1', 0, '未通知', '未通知', '0', 'hs_qw_apply_check_noticed', '0', '', '', 'text-muted', '0', 'system', SYSDATE, 'system', SYSDATE, '');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931978', '0', '0,', 20, '000020', '1', 0, '已通知', '已通知', '1', 'hs_qw_apply_check_noticed', '0', '', '', 'text-success', '0', 'system', SYSDATE, 'system', SYSDATE, '');

-- 复核状态字典
INSERT INTO js_sys_dict_type(id, dict_name, dict_type, is_sys, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931979', '公房复核状态', 'hs_qw_apply_check_review_status', '0', '0', 'system', SYSDATE, 'system', SYSDATE, '公房复核状态');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931980', '0', '0,', 10, '000010', '1', 0, '未复核', '未复核', '0', 'hs_qw_apply_check_review_status', '0', '', '', 'text-muted', '0', 'system', SYSDATE, 'system', SYSDATE, '');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931981', '0', '0,', 20, '000020', '1', 0, '已复核', '已复核', '1', 'hs_qw_apply_check_review_status', '0', '', '', 'text-primary', '0', 'system', SYSDATE, 'system', SYSDATE, '');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931982', '0', '0,', 30, '000030', '1', 0, '复核通过', '复核通过', '2', 'hs_qw_apply_check_review_status', '0', '', '', 'text-success', '0', 'system', SYSDATE, 'system', SYSDATE, '');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931983', '0', '0,', 40, '000040', '1', 0, '复核不通过', '复核不通过', '3', 'hs_qw_apply_check_review_status', '0', '', '', 'text-danger', '0', 'system', SYSDATE, 'system', SYSDATE, '');

-- 清退状态字典
INSERT INTO js_sys_dict_type(id, dict_name, dict_type, is_sys, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931984', '公房清退状态', 'hs_qw_apply_check_clearance_status', '0', '0', 'system', SYSDATE, 'system', SYSDATE, '公房清退状态');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931985', '0', '0,', 10, '000010', '1', 0, '未清退', '未清退', '0', 'hs_qw_apply_check_clearance_status', '0', '', '', 'text-muted', '0', 'system', SYSDATE, 'system', SYSDATE, '');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931986', '0', '0,', 20, '000020', '1', 0, '已通知', '已通知', '1', 'hs_qw_apply_check_clearance_status', '0', '', '', 'text-warning', '0', 'system', SYSDATE, 'system', SYSDATE, '');

INSERT INTO js_sys_dict_data(dict_code, parent_code, parent_codes, tree_sort, tree_sorts, tree_leaf, tree_level, tree_names, dict_label, dict_value, dict_type, is_sys, description, css_style, css_class, status, create_by, create_date, update_by, update_date, remarks)
VALUES ('1910328821294931987', '0', '0,', 30, '000030', '1', 0, '已清退', '已清退', '2', 'hs_qw_apply_check_clearance_status', '0', '', '', 'text-success', '0', 'system', SYSDATE, 'system', SYSDATE, '');
