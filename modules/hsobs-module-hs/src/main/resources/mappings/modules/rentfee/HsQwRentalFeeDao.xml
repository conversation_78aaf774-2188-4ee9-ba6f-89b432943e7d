<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.rentfee.dao.HsQwRentalFeeDao">
	
	<!-- 查询数据
	<select id="findList" resultType="HsQwRentalFee">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

    <select id="getApplyHouseByEstate" resultType="com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse">
        SELECT hs.*
        FROM HS_QW_APPLY h JOIN HS_QW_PUBLIC_RENTAL_HOUSE hs ON h.HOUSE_ID = hs.ID JOIN HS_QW_PUBLIC_RENTAL_ESTATE t ON hs.ESTATE_ID = t.ID
        WHERE h.STATUS = 0
              and t.id = ${estateId}
    </select>

	<!-- 查询所有需要生成租金账单的申请单 -->
	<select id="findAppliesNeedGenerateFee" resultType="com.hsobs.hs.modules.apply.entity.HsQwApply">
		SELECT 
			a.*,
			c.id AS "compact.id",
			c.compact_code AS "compact.compactCode",
			c.month_fee AS "compact.monthFee",
			c.start_date AS "compact.startDate",
			c.end_date AS "compact.endDate",
			h.id AS "hsQwApplyHouse.id",
			h.building_area AS "hsQwApplyHouse.buildingArea",
			h.shared_area AS "hsQwApplyHouse.sharedArea",
			e.id AS "hsQwApplyHouse.estate.id",
			e.name AS "hsQwApplyHouse.estate.estateName",
			e.public_area AS "hsQwApplyHouse.estate.publicArea",
			e.management_fee AS "hsQwApplyHouse.estate.managementFee",
			m.id AS "mainApplyer.id",
			m.user_id AS "mainApplyer.userId",
			m.name AS "mainApplyer.name"
		FROM hs_qw_apply a
		LEFT JOIN hs_qw_compact c ON a.id = c.apply_id
		LEFT JOIN hs_qw_public_rental_house h ON a.house_id = h.id
		LEFT JOIN hs_qw_public_rental_estate e ON h.estate_id = e.id
		LEFT JOIN hs_qw_applyer m ON a.id = m.apply_id AND m.APPLY_ROLE = '0' -- 主申请人
		LEFT JOIN hs_qw_management_check hqmc ON a.id = hqmc.apply_id AND hqmc.status = '4' AND hqmc.check_type = '0'
		WHERE a.status = '0' 
		AND (hqmc.status != '4' OR hqmc.id IS NULL)
		AND a.apply_matter IN ('0', '1', '2', '4')
		AND NOT EXISTS (
			SELECT 1 FROM hs_qw_rental_fee f 
			WHERE f.compact_id = c.id 
			AND f.fee_month = #{feeMonth}
			AND f.fee_type = #{feeType}
		)
	</select>

	<!-- 批量插入租金账单 -->
	<insert id="batchInsert">
		INSERT INTO hs_qw_rental_fee (
			id, fee_month, expect_fee_date, compact_id, fee_type, 
			user_id, rental_fee, create_by, create_date, update_by, 
			update_date, remarks, status
		)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(
				#{item.id}, #{item.feeMonth}, #{item.expectFeeDate}, #{item.compactId}, #{item.feeType},
				#{item.userId}, #{item.rentalFee}, #{item.createBy}, #{item.createDate}, #{item.updateBy},
				#{item.updateDate}, #{item.remarks}, #{item.status}
			)
		</foreach>
	</insert>
</mapper>