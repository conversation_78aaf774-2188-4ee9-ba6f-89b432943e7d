<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.publicapply.dao.HsPublicApplyDao">
	
	<!-- 查询数据
	<select id="findList" resultType="HsPublicApply">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<select id="checkHouse" resultType="map">
		select * from HS_PUBLIC_APPLY where status in (0,4,88) and house_id='${houseId}' and buy_status!='2' and id != '${applyId}'
	</select>

	<select id="selectHouseId" resultType="map">
		select a.HOUSE_ID as HOUSE_ID, house.type as HOUSE_TYPE from HS_PUBLIC_APPLY a
		LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE house ON a.house_id = house.id
		where a.id = '${applyId}'
	</select>

</mapper>