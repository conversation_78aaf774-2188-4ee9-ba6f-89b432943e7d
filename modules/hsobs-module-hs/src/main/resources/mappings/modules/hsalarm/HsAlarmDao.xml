<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.hsalarm.dao.HsAlarmDao">

	<!-- 查询数据
	<select id="findList" resultType="HsAlarm">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<!-- 未签合同
	<select id="countUnsignedContract" resultType="map">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<!-- 拖欠租金-->
	<select id="countRentArrears" resultType="map">
		SELECT er.ORGANIZATION as APPLYER_ORGANIZATION, er.NAME as APPLYER_NAME, er.PHONE as APPLYER_PHONE,a.CREATE_BY as APPLY_CREATE_BY,a.CREATE_DATE as APPLY_CREATE_DATE,a.OFFICE_CODE as OFFICE_CODE,
			   TIMESTAMPDIFF(MONTH, f.FEE_DATE, NOW) AS DIFF_MONTH, c.MONTH_FEE as MONTH_FEE
		FROM HS_QW_RENTAL_FEE f
				 LEFT JOIN HS_QW_COMPACT c on f.COMPACT_ID=c.ID
				 LEFT JOIN HS_QW_APPLY a on c.APPLY_ID=a.ID
				 LEFT JOIN HS_QW_APPLYER er on er.APPLY_ID=a.Id and er.apply_role = 0
		WHERE f.status = '0';
	</select>

	<!-- 时间异常-->
	<select id="countTimeAnomaly" resultType="map">
		SELECT er.ORGANIZATION as APPLYER_ORGANIZATION, er.NAME as APPLYER_NAME, er.PHONE as APPLYER_PHONE,a.CREATE_BY as APPLY_CREATE_BY,a.CREATE_DATE as APPLY_CREATE_DATE,a.OFFICE_CODE as OFFICE_CODE
		FROM HS_QW_COMPACT c
				 LEFT JOIN HS_QW_APPLY a on c.APPLY_ID=a.ID
				 LEFT JOIN HS_QW_APPLYER er on er.APPLY_ID=a.Id and er.apply_role = 0
		WHERE c.end_date <![CDATA[<]]> now() and c.status = '0';
	</select>

	<!-- 资格异常 -->
	<select id="countAbnormalQualification" resultType="map">
		SELECT er.ORGANIZATION as APPLYER_ORGANIZATION, er.NAME as APPLYER_NAME, er.PHONE as APPLYER_PHONE,a.CREATE_BY as APPLY_CREATE_BY,a.CREATE_DATE as APPLY_CREATE_DATE,a.OFFICE_CODE as OFFICE_CODE
		FROM HS_QW_APPLYER er
				 LEFT JOIN HS_QW_APPLY a on a.id=er.APPLY_ID
		WHERE er.status = '0';
	</select>

	<!-- 维修资金 -->
	<select id="countInsufficientCreditLimit" resultType="map">
		SELECT t.CREATE_BY as userid,jse.EMP_NAME as username,jse.office_code as office_code,jso.office_name as office_name,t2.NAME as estate,t.INPUT_FUNDS as total_amount, (t.INPUT_FUNDS - t.USED_FUNDS) as remaining_amount
		FROM HS_MAINTENANCE_FUNDS t
				 LEFT JOIN hs_qw_public_rental_estate t2 on t.HOUSE_ID = t2.ID
				 LEFT JOIN js_sys_employee jse ON t.CREATE_BY = jse.emp_code
				 LEFT JOIN js_sys_office jso ON jso.office_code = jse.office_code
		WHERE a.apply_status <![CDATA[<]]> 12 and ((t.INPUT_FUNDS-t.USED_FUNDS) <![CDATA[<=]]> ${remainingAmount}
		   or t.INPUT_FUNDS <![CDATA[>]]> ${limitAmount})
	</select>

</mapper>