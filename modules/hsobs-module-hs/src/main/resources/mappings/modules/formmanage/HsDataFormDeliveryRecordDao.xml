<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.formmanage.dao.HsDataFormDeliveryRecordDao">
	
	<!-- 查询数据
	<select id="findList" resultType="HsDataFormDeliveryRecord">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->


    <update id="updateReadStatus">
        UPDATE HS_DATA_FORM_DELIVERY_RECORD SET
        read_status = #{readStatus},
        read_date = #{readDate}
        WHERE delivery_id = #{deliveryId}
        AND fill_status = #{fillStatus}
    </update>

</mapper>