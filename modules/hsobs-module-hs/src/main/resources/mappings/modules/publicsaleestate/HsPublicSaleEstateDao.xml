<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.publicsaleestate.dao.HsPublicSaleEstateDao">
	
	<!-- 查询数据
	<select id="findList" resultType="HsPublicSaleEstate">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<select id="countApplyHouseCount" resultType="java.lang.Integer">
		SELECT COUNT(1) as TOTAL_COUNT
		FROM hs_public_sale_estate hr
		LEFT JOIN hs_public_sale_apply a ON a.id=hr.apply_id
		where hr.house_id = '${houseId}' and a.status in ('0', '4', '88')
	</select>

</mapper>