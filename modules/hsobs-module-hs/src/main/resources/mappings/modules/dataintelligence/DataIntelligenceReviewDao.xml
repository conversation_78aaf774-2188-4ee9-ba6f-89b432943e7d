<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceReviewDao">
	
	<!-- 公租房资格年审统计  -->
	<select id="countReviewArea" resultType="map">
		SELECT estate.id as ESTATE_ID, estate.name as ESTATE_NAME,
		COUNT(1) as TOTAL_COUNT,
		sum(CASE WHEN a.VIOLATION = 0 then 1 ELSE 0 END) as NORMAL_COUNT,
		sum(CASE WHEN a.VIOLATION = 1 then 1 ELSE 0 END) as ABNORMAL_COUNT
		FROM HS_QW_CHECK_RECORD a
		LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE house ON a.HOUSE_ID=house.ID
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate ON house.ESTATE_ID=estate.ID
		LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
		WHERE a.STATUS =0 ${sqlOtherWhere}
		GROUP BY estate.id, estate.name
		${sqlOrderBy}
	</select>

	<select id="countReviewCompare" resultType="map">
		SELECT
		count(*) as TOTAL_COUNT,
		sum(CASE WHEN a.VIOLATION = 0 then 1 ELSE 0 END) as NORMAL_COUNT,
		sum(CASE WHEN a.VIOLATION = 1 then 1 ELSE 0 END) as ABNORMAL_COUNT
		FROM HS_QW_CHECK_RECORD a
		LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE house ON a.HOUSE_ID=house.ID
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate ON house.ESTATE_ID=estate.ID
		LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
		WHERE a.STATUS =0 ${sqlOtherWhere}
	</select>

	<!-- 公租房资格年审统计  -->
	<select id="countReviewType" resultType="map">
		SELECT a.VIOLATION_TYPE as ABNORMAL_TYPE,
		count(1) as ABNORMAL_COUNT
		FROM HS_QW_CHECK_RECORD a
		LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN JS_SYS_OFFICE jso on a.office_code = jso.OFFICE_CODE
		WHERE a.VIOLATION = '1' ${sqlOtherWhere}
		GROUP BY a.VIOLATION_TYPE
		ORDER by ABNORMAL_COUNT desc
	</select>
	
</mapper>