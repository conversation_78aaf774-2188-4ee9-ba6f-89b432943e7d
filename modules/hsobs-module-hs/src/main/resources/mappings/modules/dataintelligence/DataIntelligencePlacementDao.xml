<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.dataintelligence.dao.DataIntelligencePlacementDao">
	
	<!-- 公有住房/限价房配售统计 -->
	<select id="countTotalInfo" resultType="map">
		SELECT jso.OFFICE_CODE as OFFICE_CODE, jso.OFFICE_NAME as OFFICE_NAME,
		sum(CASE WHEN a.status in ('4','88') then 1 ELSE 0 END) as APPLY_TOTAL_COUNT,
		sum(CASE WHEN a.status = '4' then 1 ELSE 0 END) as APPLYING_COUNT,
		sum(CASE WHEN a.status = '88' then 1 ELSE 0 END) as APPROVED_COUNT,
		sum(CASE WHEN a.status in ('4','88') then house.building_area ELSE 0 END) as TOTAL_BUILDING_AREA,
		sum(CASE WHEN a.status = '4' then house.building_area ELSE 0 END) as APPLYING_BUILDING_AREA,
		sum(CASE WHEN a.status = '88' then house.building_area ELSE 0 END) as APPROVED_BUILDING_AREA,
		sum(CASE WHEN a.status in ('4','88') then a.price ELSE 0 END) as TOTAL_BUILDING_PRICE,
		sum(CASE WHEN a.status = '4' then a.price ELSE 0 END) as APPLYING_BUILDING_PRICE,
		sum(CASE WHEN a.status = '88' then a.price ELSE 0 END) as APPROVED_BUILDING_PRICE
		FROM ${sqlTable} a
		LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
		${sqlWhere}
		GROUP BY jso.office_code, jso.office_name
		${sqlOrderBy}
	</select>

	<!-- 公有住房/限价房统计 -->
	<select id="countAreaInfo" resultType="map">
		SELECT estate.id as ESTATE_ID, estate.name as ESTATE_NAME,
		sum(CASE WHEN a.status in ('4','88') then 1 ELSE 0 END) as APPLY_TOTAL_COUNT,
		sum(CASE WHEN a.status = '4' then 1 ELSE 0 END) as APPLYING_COUNT,
		sum(CASE WHEN a.status = '88' then 1 ELSE 0 END) as APPROVED_COUNT,
		sum(CASE WHEN a.status in ('4','88') then house.building_area ELSE 0 END) as TOTAL_BUILDING_AREA,
		sum(CASE WHEN a.status = '4' then house.building_area ELSE 0 END) as APPLYING_BUILDING_AREA,
		sum(CASE WHEN a.status = '88' then house.building_area ELSE 0 END) as APPROVED_BUILDING_AREA,
		sum(CASE WHEN a.status in ('4','88') then a.price ELSE 0 END) as TOTAL_BUILDING_PRICE,
		sum(CASE WHEN a.status = '4' then a.price ELSE 0 END) as APPLYING_BUILDING_PRICE,
		sum(CASE WHEN a.status = '88' then a.price ELSE 0 END) as APPROVED_BUILDING_PRICE
		FROM ${sqlTable} a
		LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
		${sqlWhere}
		GROUP BY estate.id,estate.name
		${sqlOrderBy}
	</select>

</mapper>