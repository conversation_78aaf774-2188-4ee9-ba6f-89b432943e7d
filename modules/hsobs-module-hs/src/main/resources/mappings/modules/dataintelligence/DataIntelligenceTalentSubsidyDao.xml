<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTalentSubsidyDao">

	<!-- 人才住房补助统计 -->
	<select id="countTalentSubsidy" resultType="map">
		SELECT a.TALENT_LEVEL as TALENT_LEVEL,
		count(*) as APPLY_TOTAL_COUNT,
		sum(CASE WHEN a.apply_status != 9 then 1 ELSE 0 END) as APPLYING_COUNT,
	    sum(CASE WHEN a.apply_status = 9 then 1 ELSE 0 END) as APPROVED_COUNT,
		sum(CASE WHEN a.apply_status != 9 then a.subsidy_fund ELSE 0 END) as APPLYING_SUBSIDY_FUND,
		sum(CASE WHEN a.apply_status = 9 then a.subsidy_fund ELSE 0 END) as APPROVED_SUBSIDY_FUND,
		sum(a.subsidy_fund) as APPLY_TOTAL_SUBSIDY_FUND
		from HS_TALENT_INTRODUCTION_APPLY a
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		<where>
			1=1 ${sqlOtherWhere}
			<if test="talentType != null and talentType != ''">
				AND a.TALENT_TYPE LIKE CONCAT('%', #{talentType}, '%')
			</if>
			<if test="talentLevel != null and talentLevel != ''">
				AND a.TALENT_LEVEL LIKE CONCAT('%', #{talentLevel}, '%')
			</if>
		</where>
		group BY a.TALENT_LEVEL
		${sqlOrderBy}
	</select>

	<select id="countTalentSubsidyArea" resultType="map">
		SELECT sa.area_code AS AREA_CODE,sa.area_name AS AREA_NAME,
		count(*) as APPLY_TOTAL_COUNT,
		sum(CASE WHEN a.apply_status != 9 then 1 ELSE 0 END) as APPLYING_COUNT,
		sum(CASE WHEN a.apply_status = 9 then 1 ELSE 0 END) as APPROVED_COUNT,
		sum(CASE WHEN a.apply_status != 9 then a.subsidy_fund ELSE 0 END) as APPLYING_SUBSIDY_FUND,
		sum(CASE WHEN a.apply_status = 9 then a.subsidy_fund ELSE 0 END) as APPROVED_SUBSIDY_FUND,
		sum(a.subsidy_fund) as APPLY_TOTAL_SUBSIDY_FUND
		from HS_TALENT_INTRODUCTION_APPLY a
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		LEFT JOIN  JS_SYS_AREA sa on a.area = sa.area_code
		<where>
			1=1 ${sqlOtherWhere}
			<if test="talentType != null and talentType != ''">
				AND a.TALENT_TYPE LIKE CONCAT('%', #{talentType}, '%')
			</if>
			<if test="talentLevel != null and talentLevel != ''">
				AND a.TALENT_LEVEL LIKE CONCAT('%', #{talentLevel}, '%')
			</if>
		</where>
		group BY sa.area_code,sa.area_name
		${sqlOrderBy}
	</select>

	<!-- 人才住房补助统计 -->
	<select id="countTalentSubsidyCompare" resultType="map">
	SELECT count(*) as APPLY_TOTAL_COUNT,
	sum(CASE WHEN a.apply_status != 9 then 1 ELSE 0 END) as APPLYING_COUNT,
	sum(CASE WHEN a.apply_status = 9 then 1 ELSE 0 END) as APPROVED_COUNT,
	sum(CASE WHEN a.apply_status != 9 then a.subsidy_fund ELSE 0 END) as APPLYING_SUBSIDY_FUND,
	sum(CASE WHEN a.apply_status = 9 then a.subsidy_fund ELSE 0 END) as APPROVED_SUBSIDY_FUND,
	sum(a.subsidy_fund) as APPLY_TOTAL_SUBSIDY_FUND
	from HS_TALENT_INTRODUCTION_APPLY a
	LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		<where>
			1=1 ${sqlOtherWhere}
			<if test="talentType != null and talentType != ''">
				AND a.TALENT_TYPE LIKE CONCAT('%', #{talentType}, '%')
			</if>
			<if test="talentLevel != null and talentLevel != ''">
				AND a.TALENT_LEVEL LIKE CONCAT('%', #{talentLevel}, '%')
			</if>
		</where>
	</select>

</mapper>