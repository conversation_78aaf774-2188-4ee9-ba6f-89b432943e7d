<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTotalDao">

	
<!-- 总房源信息统计 -->
	<select id="countHouseTotal" resultType="map">
	SELECT house.office_code as OFFICE_CODE,jso.office_name as OFFICE_NAME,jso.office_type as OFFICE_TYPE,
	count(*) as TOTAL_ROOMS, sum(house.building_area) as TOTAL_AREAS,
	sum(CASE WHEN house.type='0' then 1 ELSE 0 END) as RENTAL_ROOMS,
	sum(CASE WHEN house.type='0' then house.building_area ELSE 0 END) as RENTAL_AREAS,
	sum(CASE WHEN house.type='1' then 1 ELSE 0 END) as PRICELIMIT_ROOMS,
	sum(CASE WHEN house.type='1' then house.building_area ELSE 0 END) as PRICELIMIT_AREAS,
	sum(CASE WHEN house.type='2' then 1 ELSE 0 END) as PUBLIC_ROOMS,
	sum(CASE WHEN house.type='2' then house.building_area ELSE 0 END) as PUBLIC_AREAS
	FROM hs_qw_public_rental_house house
	LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
	LEFT JOIN js_sys_office jso ON jso.office_code = house.office_code
	WHERE house.is_public='1' and house.status='0' ${otherWhere}
	GROUP BY house.office_code,jso.office_name,jso.office_type
	${sqlOrderBy}
	</select>

	<select id="countHouseTotal2" resultType="map">
		SELECT house.office_code as OFFICE_CODE,jso.office_name as OFFICE_NAME,jso.office_type as OFFICE_TYPE,
			   count(*) as TOTAL_ROOMS, sum(house.building_area) as TOTAL_AREAS,
			   sum(CASE WHEN house.type='0' then 1 ELSE 0 END) as RENTAL_ROOMS,
			   sum(CASE WHEN house.type='0' then house.building_area ELSE 0 END) as RENTAL_AREAS,
			   sum(CASE WHEN house.type='1' then 1 ELSE 0 END) as PRICELIMIT_ROOMS,
			   sum(CASE WHEN house.type='1' then house.building_area ELSE 0 END) as PRICELIMIT_AREAS,
			   sum(CASE WHEN house.type='2' then 1 ELSE 0 END) as PUBLIC_ROOMS,
			   sum(CASE WHEN house.type='2' then house.building_area ELSE 0 END) as PUBLIC_AREAS
		FROM hs_qw_public_rental_house house
				 LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
				 LEFT JOIN js_sys_office jso ON jso.office_code = house.office_code
		WHERE house.is_public='1' and house.status='0' ${otherWhere}
		GROUP BY house.office_code,jso.office_name,jso.office_type
			${sqlOrderBy}
	</select>

	<select id="countHouseTotalByCity" resultType="map">
		SELECT estate.city as CITY_CODE,aa.area_name as CITY_NAME,
			   count(*) as TOTAL_ROOMS, sum(house.building_area) as TOTAL_AREAS,
			   sum(CASE WHEN house.type='0' then 1 ELSE 0 END) as RENTAL_ROOMS,
			   sum(CASE WHEN house.type='0' then house.building_area ELSE 0 END) as RENTAL_AREAS,
			   sum(CASE WHEN house.type='1' then 1 ELSE 0 END) as PRICELIMIT_ROOMS,
			   sum(CASE WHEN house.type='1' then house.building_area ELSE 0 END) as PRICELIMIT_AREAS,
			   sum(CASE WHEN house.type='2' then 1 ELSE 0 END) as PUBLIC_ROOMS,
			   sum(CASE WHEN house.type='2' then house.building_area ELSE 0 END) as PUBLIC_AREAS
		FROM hs_qw_public_rental_house house
				 LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
				 LEFT JOIN js_sys_office jso ON jso.office_code = house.office_code
		Left Join js_sys_area aa on estate.city=aa.area_code
		WHERE house.is_public='1' and house.status='0' ${otherWhere}
		GROUP BY estate.city,aa.area_name
			${sqlOrderBy}
	</select>
</mapper>