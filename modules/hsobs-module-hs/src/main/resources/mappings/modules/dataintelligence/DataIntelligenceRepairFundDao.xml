<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceRepairFundDao">

	<!-- 维修资金情况统计  -->
	<select id="countRepairFund" resultType="map">
		SELECT a.REPAIR_TYPE,
		count(*) as DISBURSEMENT_COUNT,
		sum(DISBURSEMENT_FUND) as DISBURSEMENT_FUND
		FROM HS_MAINTENANCE_APPLY a
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = a.house_id
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		WHERE APPLY_STATUS = 12 ${otherWhere}
		GROUP BY a.REPAIR_TYPE
		${sqlOrderBy}
	</select>

	<!-- 维修资金情况统计  -->
	<select id="countRepairFundTotal" resultType="map">
		SELECT count(*) as DISBURSEMENT_COUNT,
		sum(APPLY_FUND) as APPLY_FUND,
		sum(DISBURSEMENT_FUND) as DISBURSEMENT_FUND,
		sum(APPLY_FUND-DISBURSEMENT_FUND) as UNUSED_FUND
		FROM HS_MAINTENANCE_APPLY a
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = a.house_id
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		WHERE APPLY_STATUS = 12 ${otherWhere}
	</select>

	<!-- 维修资金情况统计  -->
	<select id="countRepairFundArea" resultType="map">
		SELECT estate.id as ESTATE_ID, estate.name as ESTATE_NAME,
		count(*) as DISBURSEMENT_COUNT,
		sum(APPLY_FUND) as APPLY_FUND,
		sum(DISBURSEMENT_FUND) as DISBURSEMENT_FUND,
		sum(APPLY_FUND-DISBURSEMENT_FUND) as UNUSED_FUND
		FROM HS_MAINTENANCE_APPLY a
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = a.house_id
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		WHERE APPLY_STATUS = 12 ${otherWhere}
		GROUP BY estate.id, estate.name
		${sqlOrderBy}
	</select>

	<!-- 维修资金情况统计  -->
	<select id="countRepairFundOffice" resultType="map">
		SELECT jso.OFFICE_CODE as OFFICE_CODE, jso.FULL_NAME as OFFICE_NAME,
		count(*) as DISBURSEMENT_COUNT,
		sum(APPLY_FUND) as APPLY_FUND,
		sum(DISBURSEMENT_FUND) as DISBURSEMENT_FUND,
		sum(APPLY_FUND-DISBURSEMENT_FUND) as UNUSED_FUND
		FROM HS_MAINTENANCE_APPLY a
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = a.house_id
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		WHERE APPLY_STATUS = 12 ${otherWhere}
		GROUP BY jso.OFFICE_CODE, jso.FULL_NAME
		${sqlOrderBy}
	</select>
	
</mapper>