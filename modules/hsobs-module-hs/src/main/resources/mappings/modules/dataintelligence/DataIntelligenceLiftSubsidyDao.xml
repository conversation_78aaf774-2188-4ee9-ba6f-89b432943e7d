<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceLiftSubsidyDao">

	<!-- 加装电梯补助统计 -->
	<select id="countLiftSubsidyTotal" resultType="map">
		SELECT count(*) as APPLY_TOTAL_COUNT,
		sum(CASE WHEN a.apply_status != 9 then 1 ELSE 0 END) as APPLYING_COUNT,
		sum(CASE WHEN a.apply_status = 9 then 1 ELSE 0 END) as APPROVED_COUNT,
  	    sum(CASE WHEN a.apply_status != 9 then a.APPLY_FUND ELSE 0 END) as APPLYING_SUBSIDY_FUND,
		sum(CASE WHEN a.apply_status = 9 then a.DISBURSEMENT_FUND ELSE 0 END) as APPROVED_SUBSIDY_FUND,
		sum(a.DISBURSEMENT_FUND) as APPLY_TOTAL_SUBSIDY_FUND
		FROM HS_ELEVATOR_APPLY a
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		${sqlWhere}
	</select>

	<select id="countLiftSubsidy" resultType="map">
		SELECT jso.OFFICE_CODE as OFFICE_CODE, jso.OFFICE_NAME as OFFICE_NAME,
		count(*) as APPLY_TOTAL_COUNT,
		sum(CASE WHEN a.apply_status != 9 then 1 ELSE 0 END) as APPLYING_COUNT,
		sum(CASE WHEN a.apply_status = 9 then 1 ELSE 0 END) as APPROVED_COUNT,
		sum(CASE WHEN a.apply_status != 9 then a.APPLY_FUND ELSE 0 END) as APPLYING_SUBSIDY_FUND,
		sum(CASE WHEN a.apply_status = 9 then a.DISBURSEMENT_FUND ELSE 0 END) as APPROVED_SUBSIDY_FUND,
		sum(a.DISBURSEMENT_FUND) as APPLY_TOTAL_SUBSIDY_FUND
		FROM HS_ELEVATOR_APPLY a
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		${sqlWhere}
		GROUP BY jso.OFFICE_CODE,jso.OFFICE_NAME
		${sqlOrderBy}
	</select>

	<select id="countLiftSubsidyByEstate" resultType="map">
		SELECT sa.AREA_CODE AS AREA_CODE,sa.AREA_NAME AS AREA_NAME,
		count(*) as APPLY_TOTAL_COUNT,
		sum(CASE WHEN a.apply_status != 9 then 1 ELSE 0 END) as APPLYING_COUNT,
		sum(CASE WHEN a.apply_status = 9 then 1 ELSE 0 END) as APPROVED_COUNT,
		sum(CASE WHEN a.apply_status != 9 then a.APPLY_FUND ELSE 0 END) as APPLYING_SUBSIDY_FUND,
		sum(CASE WHEN a.apply_status = 9 then a.DISBURSEMENT_FUND ELSE 0 END) as APPROVED_SUBSIDY_FUND,
		sum(a.DISBURSEMENT_FUND) as APPLY_TOTAL_SUBSIDY_FUND
		FROM HS_ELEVATOR_APPLY a
		LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
		LEFT JOIN  JS_SYS_AREA sa on sa.AREA_CODE=a.AREA
		${sqlWhere}
		GROUP BY sa.AREA_CODE,sa.AREA_NAME
		${sqlOrderBy}
	</select>

	<!-- 加装电梯补助统计 -->
	<select id="countLiftSubsidyCompare" resultType="map">
	SELECT count(*) as APPLY_TOTAL_COUNT,
	sum(CASE WHEN a.apply_status != 9 then 1 ELSE 0 END) as APPLYING_COUNT,
	sum(CASE WHEN a.apply_status = 9 then 1 ELSE 0 END) as APPROVED_COUNT,
	sum(CASE WHEN a.apply_status != 9 then a.APPLY_FUND ELSE 0 END) as APPLYING_SUBSIDY_FUND,
	sum(CASE WHEN a.apply_status = 9 then a.DISBURSEMENT_FUND ELSE 0 END) as APPROVED_SUBSIDY_FUND,
	sum(a.DISBURSEMENT_FUND) as APPLY_TOTAL_SUBSIDY_FUND
	FROM HS_ELEVATOR_APPLY a
	LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
	LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
	LEFT JOIN JS_SYS_OFFICE jso on a.UNIT_ID = jso.OFFICE_CODE
	${sqlWhere}
	</select>

</mapper>