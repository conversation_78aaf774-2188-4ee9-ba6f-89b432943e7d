<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceLeaseDao">
	
	<!-- 公租房租赁情况统计 -->
	<select id="countTotalStat" resultType="map">
		SELECT jso.OFFICE_CODE AS OFFICE_CODE, jso.OFFICE_NAME as OFFICE_NAME,
		sum(CASE WHEN a.status IN ('0', '4') then 1 ELSE 0 END) as APPLIED_FRO_COUNT,
		sum(CASE WHEN a.status IN ('4') then 1 ELSE 0 END) as PENDING_APPROVAL_COUNT,
		sum(CASE WHEN a.status IN ('0') then 1 ELSE 0 END) as APPROVED_COUNT
		FROM HS_QW_APPLY a
		LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
		WHERE 1=1 ${sqlOtherWhere}
		GROUP BY jso.OFFICE_CODE,jso.OFFICE_NAME
	</select>

	<select id="countAreaStat" resultType="map">
		SELECT * FROM (
			SELECT estate.id as ESTATE_ID,estate.name as ESTATE_NAME,
			sum(CASE WHEN a.status IN ('0') then 1 ELSE 0 END) as APPROVED_COUNT,
			sum(CASE WHEN (a.status IN ('0', '4') and c.id IS NOT NULL) then 1 ELSE 0 END) as SIGNED_CONTRACT_COUNT,
			sum(CASE WHEN (a.status IN ('0', '4') and c.id IS NULL) then 1 ELSE 0 END) as UNSIGNED_CONTRACT_COUNT,
			sum(house.BUILDING_AREA) as BUILDING_AREA,
			sum(CASE WHEN (a.house_id is not null and house.house_type = '0') then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_1,
			sum(CASE WHEN (a.house_id is not null and house.house_type = '1') then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_2,
			sum(CASE WHEN (a.house_id is not null and house.house_type = '2') then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_3
			FROM HS_QW_APPLY a
			LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
			LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
			LEFT JOIN HS_QW_COMPACT c ON c.APPLY_ID = a.ID AND c.STATUS = '0'
			LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
			WHERE a.house_id is not null ${sqlOtherWhere}
			GROUP BY estate.id,estate.name) aa
		LEFT JOIN (
			SELECT estate.ID as ESTATE_ID2,
			sum(f.RENTAL_FEE) as RECEIVABLES,
			sum(CASE WHEN f.STATUS = '0' then f.RENTAL_FEE ELSE 0 END) as UNPAID,	-- 未缴费
			sum(CASE WHEN f.STATUS = '1' then f.RENTAL_FEE ELSE 0 END) as PAID		-- 已缴费
			FROM HS_QW_RENTAL_FEE f
			LEFT JOIN HS_QW_COMPACT c ON c.ID = f.COMPACT_ID
			LEFT JOIN HS_QW_APPLY a ON a.ID = c.APPLY_ID
			LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE house ON a.HOUSE_ID = house.ID
			LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate ON house.ESTATE_ID = estate.ID
			LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
			${sqlWhere}
			GROUP BY estate.id) bb on aa.ESTATE_ID=bb.ESTATE_ID2
		${sqlOrderBy}
	</select>

	<select id="countLeaseAreaOfficeStat" resultType="map">
		SELECT jso.office_code AS OFFICE_CODE, jso.office_name AS OFFICE_NAME,
		sum(house.BUILDING_AREA) as BUILDING_AREA,
		sum(CASE WHEN (a.house_id is not null and house.house_type = '0') then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_1,
		sum(CASE WHEN (a.house_id is not null and house.house_type = '1') then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_2,
		sum(CASE WHEN (a.house_id is not null and house.house_type = '2') then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_3
		FROM HS_QW_APPLY a
		LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
		WHERE a.house_id is not null ${sqlOtherWhere}
		GROUP BY jso.office_code, jso.office_name
	</select>

	<select id="countLeasePaidStatByOffice" resultType="map">
		SELECT * FROM (
			SELECT jso.office_code AS OFFICE_CODE, jso.office_name AS OFFICE_NAME,
			sum(CASE WHEN (a.status IN ('0', '4') and c.id IS NOT NULL) then 1 ELSE 0 END) as SIGNED_CONTRACT_COUNT,
			sum(CASE WHEN (a.status IN ('0', '4') and c.id IS NULL) then 1 ELSE 0 END) as UNSIGNED_CONTRACT_COUNT
			FROM HS_QW_APPLY a
			LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
			LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
			LEFT JOIN HS_QW_COMPACT c ON c.APPLY_ID = a.ID AND c.STATUS = '0'
			LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
			WHERE a.house_id is not null ${sqlOtherWhere}
			GROUP BY jso.office_code,jso.office_name) aa
		LEFT JOIN (
			SELECT jso.office_code AS OFFICE_CODE2,
			sum(f.RENTAL_FEE) as RECEIVABLES,
			sum(CASE WHEN f.STATUS = '0' then f.RENTAL_FEE ELSE 0 END) as UNPAID,	-- 未缴费
			sum(CASE WHEN f.STATUS = '1' then f.RENTAL_FEE ELSE 0 END) as PAID		-- 已缴费
			FROM HS_QW_RENTAL_FEE f
			LEFT JOIN HS_QW_COMPACT c ON c.ID = f.COMPACT_ID
			LEFT JOIN HS_QW_APPLY a ON a.ID = c.APPLY_ID
			LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE house ON a.HOUSE_ID = house.ID
			LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate ON house.ESTATE_ID = estate.ID
			LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
			${sqlWhere}
			GROUP BY jso.office_code) bb on aa.OFFICE_CODE=bb.OFFICE_CODE2
		${sqlOrderBy}
	</select>

	<select id="countUnpaidOffice" resultType="map">
		SELECT
			jso.office_code AS OFFICE_CODE,
			jso.office_name AS OFFICE_NAME,
			SUM(CEIL(MONTHS_BETWEEN(SYSDATE, a.expect_fee_date))) AS DurationMonth,	-- 拖欠月数总和
			SUM(a.rental_fee) AS UNPAID, 	-- 拖欠租金总额
			COUNT(a.id) AS UNPAID_COUNT			-- 拖欠账单数量
		FROM hs_qw_rental_fee a
		LEFT JOIN hs_qw_compact c ON a.compact_id = c.id
		LEFT JOIN hs_qw_apply ap ON c.apply_id = ap.id
		LEFT JOIN hs_qw_public_rental_house house on house.id = ap.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN js_sys_office jso ON ap.office_code = jso.office_code
		WHERE a.fee_type = '1' AND a.fee_date IS NULL ${sqlOtherWhere}
		GROUP BY jso.office_code, jso.office_name
	</select>

	<select id="countLeaseOfficeStat" resultType="map">
	SELECT a.OFFICE_CODE as OFFICE_CODE, jso.OFFICE_NAME as OFFICE_NAME,
	sum(CASE WHEN a.STATUS IN ('0') then 1 ELSE 0 END) as CHECK_IN_COUNT
	FROM HS_QW_APPLY a
	LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
	LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
	LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
	WHERE 1=1 ${sqlOtherWhere}
	GROUP BY a.OFFICE_CODE, jso.OFFICE_NAME
	</select>


	<select id="countLeaseFEEStat" resultType="map">
	SELECT estate.ID as ESTATE_ID, estate.name as ESTATE_NAME,
	sum(f.RENTAL_FEE) as RECEIVABLES,
	sum(CASE WHEN f.STATUS = '0' then f.RENTAL_FEE ELSE 0 END) as UNPAID,	-- 未缴费
	sum(CASE WHEN f.STATUS = '1' then f.RENTAL_FEE ELSE 0 END) as PAID		-- 已缴费
	FROM HS_QW_RENTAL_FEE f
	LEFT JOIN HS_QW_COMPACT c ON c.ID = f.COMPACT_ID
	LEFT JOIN HS_QW_APPLY a ON a.ID = c.APPLY_ID
	LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE house ON a.HOUSE_ID = house.ID
	LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate ON house.ESTATE_ID = estate.ID
	LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
	${sqlWhere}
	GROUP BY estate.id, estate.name
	</select>


	<select id="countLeaseBuildingAreaStat" resultType="map">
	SELECT estate.id as ESTATE_ID,estate.name as ESTATE_NAME,
	sum(house.BUILDING_AREA) as BUILDING_AREA,
	sum(CASE WHEN house.house_type = '0' then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_1,
	sum(CASE WHEN house.house_type = '1' then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_2,
	sum(CASE WHEN house.house_type = '2' then house.BUILDING_AREA ELSE 0 END) as BUILDING_AREA_3
	FROM HS_QW_APPLY a
	LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
	LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
	LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
	WHERE a.house_id is not null ${sqlOtherWhere}
	GROUP BY estate.id,estate.name
	${sqlOrderBy}
	</select>

	<select id="countVacantAreaStat" resultType="map">
	SELECT house.ESTATE_ID as ESTATE_ID, estate.name as ESTATE_NAME,
	COUNT(1) as VACANT_COUNT,sum(house.SHARED_AREA) as VACANT_AREA,
	sum(CASE WHEN house.type = 0 then 1 ELSE 0 END) as VACANT0_COUNT,
	sum(CASE WHEN house.type = 0 then house.SHARED_AREA ELSE 0 END) as VACANT0_AREA,
	sum(CASE WHEN house.type = 3 then 1 ELSE 0 END) as VACANT3_COUNT,
	sum(CASE WHEN house.type = 3 then house.SHARED_AREA ELSE 0 END) as VACANT3_AREA,
	sum(CASE WHEN house.type = 4 then 1 ELSE 0 END) as VACANT4_COUNT,
	sum(CASE WHEN house.type = 4 then house.SHARED_AREA ELSE 0 END) as VACANT4_AREA
	FROM HS_QW_PUBLIC_RENTAL_HOUSE house
	LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate ON house.ESTATE_ID=estate.ID
	LEFT JOIN JS_SYS_OFFICE jso on house.OFFICE_CODE = jso.OFFICE_CODE
	WHERE house.STATUS =0 AND (house.HOUSE_STATUS = '0' OR house.HOUSE_STATUS = '2') and house.TYPE IN ('0','3','4') ${sqlOtherWhere}
	GROUP BY house.ESTATE_ID,estate.name
	${sqlOrderBy}
	</select>

</mapper>