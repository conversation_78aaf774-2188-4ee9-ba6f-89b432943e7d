<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceClearanceDao">
	
	<!-- 公租房清退统计 -->
	<select id="countClearanceStat" resultType="map">
		SELECT estate.id as ESTATE_ID,estate.NAME as ESTATE_NAME,
		count(1) as TOTAL_COUNT,
		sum(CASE WHEN c."TYPE" IN ('0') then 1 ELSE 0 END) as ARREARS_COUNT,
		sum(CASE WHEN c."TYPE" IN ('1') then 1 ELSE 0 END) as MATURITY_COUNT,
		sum(CASE WHEN c."TYPE" IN ('2','3') then 1 ELSE 0 END) as VIOLATIONS_COUNT
		FROM HS_QW_APPLY a
		LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN HS_QW_CLEARANCE c on c.APPLY_ID=a.id and c.STATUS = '0'
		LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
		WHERE a.STATUS IN ('0') AND a.APPLY_MATTER IN ('0', '1', '2', '4') ${sqlOtherWhere}
		GROUP BY estate.id,estate.NAME
		${sqlOrderBy}
	</select>

	<!-- 公租房清退同比 -->
	<select id="countClearanceCompare" resultType="map">
		SELECT count(1) as TOTAL_COUNT,
		sum(CASE WHEN c."TYPE" IN ('0') then 1 ELSE 0 END) as ARREARS_COUNT,
		sum(CASE WHEN c."TYPE" IN ('1') then 1 ELSE 0 END) as MATURITY_COUNT,
		sum(CASE WHEN c."TYPE" IN ('2','3') then 1 ELSE 0 END) as VIOLATIONS_COUNT
		FROM HS_QW_APPLY a
		LEFT JOIN hs_qw_public_rental_house house on house.id = a.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN HS_QW_CLEARANCE c on c.APPLY_ID=a.id and c.STATUS = '0'
		LEFT JOIN JS_SYS_OFFICE jso on a.OFFICE_CODE = jso.OFFICE_CODE
		WHERE a.STATUS IN ('0') AND a.APPLY_MATTER IN ('0', '1', '2', '4') ${sqlOtherWhere}
	</select>

	<!-- 公租房清退违规类型分析 -->
	<select id="countClearanceTypeStat" resultType="map">
		SELECT a.TYPE as CLEARANCE_TYPE,
		count(*) as CLEARANCE_COUNT
		FROM HS_QW_CLEARANCE a
		left join HS_QW_APPLY app on app.id = a.APPLY_ID
		LEFT JOIN hs_qw_public_rental_house house on house.id = app.house_id
		LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE estate on estate.id = house.estate_id
		LEFT JOIN JS_SYS_OFFICE jso on app.office_code = jso.OFFICE_CODE
		WHERE a.status = '0' ${otherWhere}
		GROUP BY a.TYPE
		ORDER BY CLEARANCE_COUNT desc
	</select>
</mapper>