<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.applypublic.dao.HsQwApplyPublicDao">
	
	<!-- 查询数据
	<select id="findList" resultType="HsQwApplyPublic">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<select id="getHouseInfoByIds" resultType="String" weight="100">
		SELECT
			LISTAGG(CONCAT(CONCAT(CONCAT(HQPRE.NAME , '小区'), CONCAT(HQPRH.BUILDING_NUM, '楼')), HQPRH.UNIT_NUM),
					',') as simpleInfos
		FROM
			HS_QW_PUBLIC_RENTAL_HOUSE hqprh
				LEFT JOIN HS_QW_PUBLIC_RENTAL_ESTATE hqpre ON
				HQPRE.ID = HQPRH.ESTATE_ID
		where
			hqprh.id in
		<foreach collection="idArray" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

</mapper>