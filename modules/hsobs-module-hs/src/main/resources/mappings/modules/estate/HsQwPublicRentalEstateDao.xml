<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.estate.dao.HsQwPublicRentalEstateDao">

    <!-- 查询数据
    <select id="findList" resultType="HsQwPublicRentalEstate">
        SELECT ${sqlMap.column.toSql()}
        FROM ${sqlMap.table.toSql()}
        <where>
            ${sqlMap.where.toSql()}
        </where>
        ORDER BY ${sqlMap.order.toSql()}
    </select> -->
<!-- 分页查询 -->
<select id="findStaticEstate" resultType="com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate">
    SELECT
    A.id AS id,
    A.name AS name,
    A.address,
    A.area,
    A.surrounding_plan,
    A.remarks,
    A.latitude,
    A.longitude
    FROM HS_QW_PUBLIC_RENTAL_ESTATE A
    LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE B ON A.id = B.ESTATE_ID and B.status=0 and B.is_public=1 and B.type = 0
    LEFT JOIN JS_SYS_AREA sa on sa.area_code=a.area
    WHERE 1=1 and A.status=0
    <if test="estate.name != null and estate.name != ''">
        AND (A.name LIKE CONCAT('%', #{estate.name}, '%') or a.address LIKE CONCAT('%', #{estate.name}, '%') or sa.area_name LIKE CONCAT('%', #{estate.name}, '%'))
    </if>
    <if test="estate.fylx != null and estate.fylx != ''">
        AND EXISTS (
        SELECT 1 FROM HS_QW_PUBLIC_RENTAL_HOUSE AS B2
        WHERE B2.ESTATE_ID = A.id
        AND FIND_IN_SET(B2.HOUSE_TYPE, #{estate.fylx})
        and B2.status=0 and B2.is_public=1 and B2.type = 0
        )
    </if>
    GROUP BY A.id, A.name,    A.address, A.area,
    A.surrounding_plan,
    A.remarks,A.latitude,
    A.longitude
    <if test="estate.page != null and estate.page.orderBy != null and estate.page.orderBy != ''">
        ORDER BY ${estate.page.orderBy}
    </if>
    <if test="estate.page != null and estate.page.pageSize >= 0">
        LIMIT #{estate.page.firstResult}, #{estate.page.pageSize}
    </if>
</select>

<!-- 查询总数 -->
<select id="findStaticEstateCount" resultType="int">
    SELECT COUNT(1) FROM (
        SELECT A.id
        FROM HS_QW_PUBLIC_RENTAL_ESTATE A
        LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE B ON A.id = B.ESTATE_ID and B.status=0 and B.is_public=1 and B.type = 0
        LEFT JOIN JS_SYS_AREA sa on sa.area_code=a.area
        WHERE 1=1 and A.status=0
        <if test="estate.name != null and estate.name != ''">
            AND (A.name LIKE CONCAT('%', #{estate.name}, '%') or a.address LIKE CONCAT('%', #{estate.name}, '%') or sa.area_name LIKE CONCAT('%', #{estate.name}, '%'))
        </if>
        <if test="estate.fylx != null and estate.fylx != ''">
            AND EXISTS (
            SELECT 1 FROM HS_QW_PUBLIC_RENTAL_HOUSE AS B2
            WHERE B2.ESTATE_ID = A.id
            AND FIND_IN_SET(B2.HOUSE_TYPE, #{estate.fylx})
            and B2.status=0 and B2.is_public=1 and B2.type = 0
            )
        </if>
        GROUP BY A.id
    ) t
</select>


    <select id="findPriceLimitStaticEstate" resultType="com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate">
        SELECT
        A.id AS id,
        A.name AS name,
        A.address,
        A.area AS area
        FROM HS_QW_PUBLIC_RENTAL_ESTATE A
        LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE B ON A.id = B.ESTATE_ID and B.status=0 and B.is_public=1 and B.type = 1 and b.house_status=2
        LEFT JOIN JS_SYS_AREA sa on sa.area_code=a.area
        WHERE b.id IS NOT NULL and A.status=0
        <if test="estate.name != null and estate.name != ''">
            AND (A.name LIKE CONCAT('%', #{estate.name}, '%') or a.address LIKE CONCAT('%', #{estate.name}, '%') or sa.area_name LIKE CONCAT('%', #{estate.name}, '%'))
        </if>
        GROUP BY A.id, A.name, A.address, A.area
        <if test="estate.page != null and estate.page.orderBy != null and estate.page.orderBy != ''">
            ORDER BY ${estate.page.orderBy}
        </if>
        <if test="estate.page != null and estate.page.pageSize >= 0">
            LIMIT #{estate.page.firstResult}, #{estate.page.pageSize}
        </if>
    </select>

    <!-- 查询总数 -->
    <select id="findPriceLimitStaticEstateCount" resultType="int">
        SELECT COUNT(1) FROM (
        SELECT A.id
        FROM HS_QW_PUBLIC_RENTAL_ESTATE A
        LEFT JOIN HS_QW_PUBLIC_RENTAL_HOUSE B ON A.id = B.ESTATE_ID and B.status=0 and B.is_public=1 and B.type = 1 and b.house_status=2
        LEFT JOIN JS_SYS_AREA sa on sa.area_code=a.area
        WHERE b.id IS NOT NULL and A.status=0
        <if test="estate.name != null and estate.name != ''">
            AND (A.name LIKE CONCAT('%', #{estate.name}, '%') or a.address LIKE CONCAT('%', #{estate.name}, '%') or sa.area_name LIKE CONCAT('%', #{estate.name}, '%'))
        </if>
        GROUP BY A.id
        ) t
    </select>

</mapper>