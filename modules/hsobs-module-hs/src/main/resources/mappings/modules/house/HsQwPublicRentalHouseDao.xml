<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.house.dao.HsQwPublicRentalHouseDao">
	
	<!-- 查询数据
	<select id="findList" resultType="HsQwPublicRentalHouse">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

    <select id="findHouseTemplateList" resultType="com.hsobs.hs.modules.external.entity.ApiHsHouseTemplate">
        SELECT
            max(h.id) AS fybh,
            max(v.vr_url) as vrUrl,
            h.BUILDING_NUM AS lh,
            h.FLOOR AS lc,
            h.UNIT_NUM AS dyh,
            h.BUILDING_AREA AS jzmj,
            max(h.HOUSE_TYPE) As fwlx
        FROM
            HS_QW_PUBLIC_RENTAL_HOUSE h
            LEFT JOIN HS_QW_HOUSE_VR v ON h.vr_info = v.id
        where h.estate_id = #{id}
              and h.status = 0
              and h.is_public = 1
              and h.type = 0
        GROUP BY
            h.BUILDING_NUM ,
            h.FLOOR ,
            h.UNIT_NUM ,
            h.BUILDING_AREA
    </select>


    <select id="findPriceLimitHouseTemplateList" resultType="com.hsobs.hs.modules.external.entity.ApiHsHouseTemplate">
        SELECT
            max(h.id) AS fybh,
            h.BUILDING_NUM AS lh,
            h.FLOOR AS lc,
            h.UNIT_NUM AS dyh,
            h.BUILDING_AREA AS jzmj,
            max(h.HOUSE_TYPE) As fwlx
        FROM
            HS_QW_PUBLIC_RENTAL_HOUSE h
        where h.estate_id = #{id}
          and h.status = 0
          and h.is_public = 1
          and h.type = 1
          and h.house_status=2
        GROUP BY
            h.BUILDING_NUM ,
            h.FLOOR ,
            h.UNIT_NUM ,
            h.BUILDING_AREA
    </select>
</mapper>