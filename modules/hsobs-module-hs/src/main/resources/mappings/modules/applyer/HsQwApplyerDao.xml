<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.applyer.dao.HsQwApplyerDao">
	
	<!-- 查询数据
	<select id="findList" resultType="HsQwApplyer">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

    <!-- 第一步：先查询符合条件的 userId 分组 -->
    <select id="findGroupUserIds" resultType="String">
        SELECT a.user_id
        FROM hs_qw_applyer a
        LEFT JOIN hs_qw_apply b ON a.apply_id = b.id
        <where>
            a.status = '0'
            AND a.apply_role = '0'
            <!-- HsQwApplyer 条件 -->
            <if test="applyer.userId != null and applyer.userId != ''">
                AND a.user_id = #{applyer.userId}
            </if>
            <if test="applyer.name != null and applyer.name != ''">
                AND a.name LIKE concat('%', #{applyer.name}, '%')
            </if>
            <if test="applyer.idNum != null and applyer.idNum != ''">
                AND a.id_num = #{applyer.idNum}
            </if>
            <!-- HsQwApply 条件 -->
            <if test="apply.applyMatter != null and apply.applyMatter != ''">
                AND b.apply_matter = #{apply.applyMatter}
            </if>
            <if test="apply.officeCode != null and apply.officeCode != ''">
                AND b.office_code = #{apply.officeCode}
            </if>
        </where>
        GROUP BY a.user_id
    </select>

</mapper>