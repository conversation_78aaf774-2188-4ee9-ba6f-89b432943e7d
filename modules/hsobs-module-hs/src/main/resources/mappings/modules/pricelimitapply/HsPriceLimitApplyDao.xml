<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsobs.hs.modules.pricelimitapply.dao.HsPriceLimitApplyDao">
	
	<!-- 查询数据
	<select id="findList" resultType="HsPriceLimitApply">
		SELECT ${sqlMap.column.toSql()}
		FROM ${sqlMap.table.toSql()}
		<where>
			${sqlMap.where.toSql()}
		</where>
		ORDER BY ${sqlMap.order.toSql()}
	</select> -->

	<select id="checkHouse" resultType="map">
		select * from HS_PRICE_LIMIT_APPLY where status in (0,4,88) and house_id='${houseId}' and buy_status!='2' and id != '${applyId}'
	</select>

	<select id="selectHouseId" resultType="map">
		select HOUSE_ID from HS_PRICE_LIMIT_APPLY where id = '${applyId}'
	</select>

</mapper>