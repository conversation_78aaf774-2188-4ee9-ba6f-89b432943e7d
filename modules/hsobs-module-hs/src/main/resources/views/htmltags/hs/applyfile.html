<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */

/**
* 配租申请单-申请材料
* <AUTHOR>
* @version 2025-02-11
*/
var p = {
	// 业务流程实体对象
	entity: entity!,
	// 内置参数
	thisTag: thisTag,
	readonly: readonly!'false'
};

// 编译属性参数
form.attrs(p);

%>

<div class="hs-table-div">
	<table class="table-form hs-table-form">
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('身份证明图片上传')}：
			</td>
			<td>
				<#form:fileupload id="uploadImage2" bizKey="${entity.id}" bizType="hsQwApply_idnum"
				uploadType="image" class="" readonly="${p.readonly}" preview="true" dataMap="true"/>
			</td>
		</tr>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('婚姻证明图片上传')}：
			</td>
			<td>
				<#form:fileupload id="uploadImage3" bizKey="${entity.id}" bizType="hsQwApply_marry"
				uploadType="image" class="" readonly="${p.readonly}" preview="true" dataMap="true"/>
			</td>
		</tr>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('人员类型证明图片上传')}：
			</td>
			<td>
				<#form:fileupload id="uploadImage4" bizKey="${entity.id}" bizType="hsQwApply_userType"
				uploadType="image" class="" readonly="${p.readonly}" preview="true" dataMap="true"/>
			</td>
		</tr>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('住房公积金证明图片上传')}：
			</td>
			<td>
				<#form:fileupload id="uploadImage5" bizKey="${entity.id}" bizType="hsQwApply_fund"
				uploadType="image" class="" readonly="${p.readonly}" preview="true" dataMap="true"/>
			</td>
		</tr>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('承诺证明图片上传')}：
			</td>
			<td>
				<#form:fileupload id="uploadImage6" bizKey="${entity.id}" bizType="hsQwApply_promise"
				uploadType="image" class="" readonly="${p.readonly}" preview="true" dataMap="true"/>
			</td>
		</tr>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('收入证明图片上传')}：
			</td>
			<td>
				<#form:fileupload id="uploadImage7" bizKey="${entity.id}" bizType="hsQwApply_income"
				uploadType="image" class="" readonly="${p.readonly}" preview="true" dataMap="true"/>
			</td>
		</tr>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('其他证明图片上传')}：
			</td>
			<td>
				<#form:fileupload id="uploadImage8" bizKey="${entity.id}" bizType="hsQwApply_others"
				uploadType="image" class="" readonly="${p.readonly}" preview="true" dataMap="true"/>
			</td>
		</tr>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('房屋证明图片上传')}：
			</td>
			<td>
				<#form:fileupload id="uploadImage1" bizKey="${entity.id}" bizType="hsQwApply_house"
				uploadType="image" class="" readonly="${p.readonly}" preview="true" dataMap="true"/>
			</td>
		</tr>
	</table>
</div>