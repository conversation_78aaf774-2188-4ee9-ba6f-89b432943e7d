<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */

/**
 * 配租申请单-选择主申请人
 * <AUTHOR>
 * @version 2025-03-14
 */
var p = {

	// 标签参数
	path: path!,
	value: value![],				// 元素值
	// 内置参数
	thisTag: thisTag,
	readonly: readonly!'false',
	editable: readonly!'false'=='false'
};


// 编译属性参数
form.attrs(p);

%>

<div class="form-unit-wrap table-form" id="hsQwApplyerWrapper">
	<#form:btnlistselect id="userGridAddRowListselectBtn" title="关联用户"
	allowClear="true"
	btnLabel="选择主申请人"
	setSelectDataFuncName="hsQwApplyUserSelect"
	url="${ctx}/sys/empUser/empUserSelect"
	allowClear="false" btnClass="btn btn-primary btn-sm mt10 mb10"
	itemCode="id" itemName="name" />
</div>
<table id="hsQwApplyerDataGrid"></table>
<% if (hasPermi('apply:hsQwApply:edit') && p.editable){ %>
<a href="#" id="hsQwApplyerDataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10" ><i class="fa fa-plus"></i> ${text('增行')}</a>
<% } %>
<script src="/hsobs/static/bootstrap/js/bootstrap.min.js"></script>
<script src="/hsobs/static/select2/4.0/select2.js?V5.9-03111521"></script>
<script src="/hsobs/static/select2/4.0/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/layer/3.5/layer.js?V5.9-03111521"></script>
<script src="/hsobs/static/laydate/5.3/laydate.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery/jquery-ui-sortable-1.13.2.min.js"></script>
<script src="/hsobs/static/jqGrid/4.7/js/jquery.jqGrid.js?V5.9-03111521"></script>
<script src="/hsobs/static/jqGrid/4.7/js/jquery.jqGrid.extend.js?V5.9-03111521"></script>
<script src="/hsobs/static/jqGrid/4.7/js/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/jquery.validate.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/localization/messages_zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/jquery.validate.extend.js?V5.9-03111521"></script>
<script src="/hsobs/static/webuploader/0.1/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/jeesite.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/i18n/jeesite_zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/common.js?V5.9-03111521"></script>
<script>

	let gridData = []
	//# // 初始化租赁资格轮候申请人DataGrid对象
	$('#hsQwApplyerDataGrid').dataGrid({
		data: ${p.value},
		datatype: 'local', // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度

		//# // 设置数据表格列
		columnModel: [
			{header:'状态', name:'status', editable:true, hidden:true},
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'用户id', name:'userId', editable:true, hidden:true},
			{header:'${text("申请人姓名")}', name:'name', width:150, editable:${p.editable}, edittype:'text', editoptions:{
					'maxlength':'100', 'class':'form-control required realName'}},
			{header:'${text("参加工作时间")}', name:'workTime', width:150,
				formatter:'date', formatoptions:{srcformat:'Y-m',newformat:'Y-m'},
				editable:true, edittype:'text', editoptions:{
					${p.editable ? '':'\'disabled\': \'disabled\','} 'class':'form-control laydate required', 'readonly':'true',
					dataInit: function(element){
						laydate.render({elem:element, type:'datetime', format:'yyyy-MM'});
					}
				}
			},
			{header:'${text("工龄")}', name:'workAge', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control'}},
			{header:'${text("工作单位")}', name:'organization', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
			{header:'${text("身份证号")}', name:'idNum', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required idcard'}},
			{header:'${text("手机号")}', name:'phone', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required mobile'}},
			{header:'${text("职务")}', name:'workPosition', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
			{header:'${text("年收入")}', name:'annualIncome', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required'}},
			{header:'${text("人员类型")}', name:'userType', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control'}},
			{header:'${text("备注信息")}', name:'remarks', width:150, editable:${p.editable}, edittype:'textarea', editoptions:{'maxlength':'500', 'class':'form-control', 'rows':'1'}},
			{header:'${text("婚姻状况")}', name:'marryStatus', width:100,editable:true, edittype:'select',
				editoptions:{${p.editable ? '':'\'disabled\': \'disabled\','}'class':'form-control',
					items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('hs_applyer_marry')}"),
					itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
						js.select2(element).on("change",function(){
							$(this).resetValid()});
					}
				}},
			{header:'${text("死亡时间")}', name:'deathDate', width:150,formatter:'date', formatoptions:{srcformat:'Y-m-d',newformat:'Y-m-d'},
				editable:true, edittype:'text', editoptions:{'disabled': 'disabled','class':'form-control laydate', 'readonly':'true',
					dataInit: function(element){
						laydate.render({elem:element, type:'datetime', format:'yyyy-MM-dd'});
					}
				},
				hidden: true, // 默认隐藏
				hidedlg: false // 允许在列选择器中显示
			},
			{header:'${text("申请人角色")}', name:'applyRole', width:100,
				editable:true, edittype:'select', editoptions:{${p.editable ? '':'\'disabled\': \'disabled\','}'class':'form-control',
					items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('hs_applyer_role')}"),
					itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
						js.select2(element).on("change",function(){
							$(this).resetValid()});
					}
				}
			},
			{header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
					var actions = [];
					if (${p.editable}){
						if (val == 'new'){
							actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyerDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
						}else{
							actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyerDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');});return false;">删除</a>&nbsp;');
						}
					}
					return actions.join('');
				}, editoptions: {defaultValue: 'new'}}
		],

		//# // 编辑表格参数
		editGrid: ${p.editable},				// 是否是编辑表格
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
		editGridAddRowBtn: $('#hsQwApplyerDataGridAddRowBtn'),	// 子表增行按钮
		editGridAddRowBtnToHeader: ${p.editable},	// 子表增行按钮是否显示到表头上
		editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

		//# // 编辑表格的提交数据参数
		editGridInputFormListName: 'hsQwApplyerList', // 提交的数据列表名
		editGridInputFormListAttrs: 'status,id,userId,name,workAge,workTime,organization,idNum,phone,marryStatus, userType, deathDate,workPosition,workTitle,annualIncome,createBy,createDate,updateBy,updateDate,remarks,applyId,applyRole,', // 提交数据列表的属性字段

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){
			setTimeout(bindMarryStatusChange, 500); // 延迟执行，确保数据已加载
		}
	});

	function hsQwApplyUserSelect(id, selectData) {
		if (id === 'userGridAddRowListselectBtn') {
			// 确保 selectData 不为空
			if ($.isEmptyObject(selectData)) {
				return;
			}
			let selectUserId;
			let selectObject;
			$.each(selectData, (key, value) => {
				selectUserId = key.toString();
				selectObject = value;
			});

			let grid = $('#hsQwApplyerDataGrid');
			// 确保 selectData 是数组

			let selectedArray = grid.dataGrid('getRowData');
			// 获取当前表格所有行的 ID

			let rowIds = grid.jqGrid('getDataIDs');
			// 遍历现有行数据，只删除 applyRole=0 且 userId 存在于 selectData 中的行
			rowIds.forEach(rowId => {
				// 获取 applyRole 的值，通过查找该行中的 <select> 元素
				let applyRoleValue = $('#' + rowId + '_applyRole').val(); // 根据行 ID 获取 select 元素的值

				// 获取 userId 的值，通过查找该行中的 <input> 元素
				let userId = $('#' + rowId + '_userId').val(); // 根据行 ID 获取 input 元素的值
				let name = $('#' + rowId + '_name').val(); // 根据行 ID 获取 input 元素的值
				let phone = $('#' + rowId + '_phone').val(); // 根据行 ID 获取 input 元素的值
				let applyRole = $('#' + rowId + '_applyRole').val(); // 根据行 ID 获取 input 元素的值

				// 如果 applyRole 是 0 且 userId 在 selectData 中，删除该行
				if (applyRoleValue === "0") {
					grid.jqGrid('delRowData', rowId); // 删除该行
				}
			});

			grid.jqGrid('addRow', {
				position: 'first',
				addRowParams: { keys: false, focusField: true },
				initdata: {
					userId: selectObject.id || "",
					phone: selectObject.mobile || "",
					name: selectObject.userName || "Unknown",
					idNum: selectObject.idNum || "Unknown",
					organization: selectObject.employee.office.officeName || "Unknown",
					applyRole: "0", // 设置 applyRole 为 0
					status: Global.STATUS_NORMAL
				}
			});

			// 触发表格刷新
			// grid.trigger("reloadGrid");
		}
	}


	function bindMarryStatusChange() {
	    // 先解绑之前可能存在的事件处理程序，避免重复绑定
	    $('#hsQwApplyerDataGrid').off('change', 'select[name="marryStatus"]');
	    
	    // 监听婚姻状况变化
	    $('#hsQwApplyerDataGrid').on('change', 'select[name="marryStatus"]', function () {
	        var selectedValue = $(this).val();
	        var grid = $('#hsQwApplyerDataGrid');
	        var rowId = $(this).closest('tr').attr('id');
	        
	        console.log('婚姻状况变更:', selectedValue); // 添加调试日志
	        
	        if (selectedValue === '3') { // 丧偶状态
	            // 显示死亡日期列
	            grid.jqGrid('showCol', 'deathDate');
	            
	            // 解除当前行死亡日期控件的禁用状态
	            var deathDateInput = $('#' + rowId + '_deathDate');
	            deathDateInput.prop('disabled', false).addClass('required');
	        } else {
	            // 隐藏死亡日期列
	            grid.jqGrid('hideCol', 'deathDate');
	            
	            // 禁用当前行死亡日期控件并清空值
	            var deathDateInput = $('#' + rowId + '_deathDate');
	            deathDateInput.prop('disabled', true).val('').removeClass('required');
	        }
	    });
	    
	    // 初始化时检查已有行的婚姻状况
	    setTimeout(function() {
	        var grid = $('#hsQwApplyerDataGrid');
	        var rows = grid.jqGrid('getDataIDs');
	        var hasWidowStatus = false;
	        
	        for (var i = 0; i < rows.length; i++) {
	            var rowId = rows[i];
	            var marryStatus = grid.jqGrid('getCell', rowId, 'marryStatus');
	            console.log('检查行:', rowId, '婚姻状况:', marryStatus);
	            
	            if (marryStatus === '3') {
	                // 显示死亡日期列
	                grid.jqGrid('showCol', 'deathDate');
	                
	                // 解除该行死亡日期控件的禁用状态
	                var deathDateInput = $('#' + rowId + '_deathDate');
	                deathDateInput.prop('disabled', false).addClass('required');
	                
	                hasWidowStatus = true;
	            }
	        }
	        
	        // 如果没有丧偶状态的行，确保死亡日期列隐藏
	        if (!hasWidowStatus) {
	            grid.jqGrid('hideCol', 'deathDate');
	        }
	    }, 100);
	}
	
	// 在文档加载完成时绑定事件
	$(document).ready(function() {
	    // 在表格编辑完成后也重新绑定事件
	    $('#hsQwApplyerDataGrid').on('jqGridAfterEditCell jqGridAfterRestoreCell jqGridAddEditAfterSubmit', function() {
	        bindMarryStatusChange();
	    });
	});
	</script>