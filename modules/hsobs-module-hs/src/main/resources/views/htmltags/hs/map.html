<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */

/**
 * 配租申请单-申请单
 * <AUTHOR>
 * @version 2025-02-11
 */
var p = {

	// 业务流程实体对象
	name: name,
	address: address,
	nameValue: nameValue,
	addressValue: addressValue,
	lat: lat,
	lng: lng,
	latValue: latValue,
	lngValue: lngValue,
	// 内置参数
	thisTag: thisTag,
	readonly: readonly!'false'
};


// 编译属性参数
form.attrs(p);

%>


<!-- 地图弹窗 -->
<div class="modal fade" id="mapModal" tabindex="-1" role="dialog">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">${text('地图标注')}</h4>
			</div>
			<div class="modal-body">
				<div id="mapContainer" style="height: 500px; width: 100%;"></div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript" src="${@Global.getConfig('tianditu.baseUrl')}"></script>
<style>
    .map-search-box {
        position: absolute;
        top: 10px;
        left: 50px;
        z-index: 1000;
        width: 300px;
        background: rgba(255,255,255,0.9);
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    .map-search-box input {
        width: 65% !important;
        display: inline-block !important;
    }
    .map-search-box button {
        width: 28%;
        margin-left: 2%;
    }
</style>
<script>
    $(function () {
        let map, geocoder, currentMarker, localsearch;
        function initMap() {
            // 清理之前的实例
            $('#mapContainer').empty();
            if(map) map = null;

            // 创建新地图实例
            map = new T.Map('mapContainer', {projection: 'EPSG:4326'});
            // 启用滚轮缩放
            map.enableScrollWheelZoom();

            // 添加缩放控件
            var zoom = new T.Control.Zoom();
            map.addControl(zoom);

            geocoder = new T.Geocoder();

            let lng = parseFloat($('#${p.lng}').val());
            let lat = parseFloat($('#${p.lat}').val());
            let hasValidCoords = !isNaN(lng) && !isNaN(lat);

            let centerLng, centerLat;
            if (hasValidCoords) {
                centerLng = lng;
                centerLat = lat;
            } else {
                // 默认中心点（例如福州）
                centerLng = 119.291320;
                centerLat = 26.077380;
            }
            map.centerAndZoom(new T.LngLat(centerLng, centerLat), hasValidCoords ? 15 : 15);
            if(hasValidCoords) addMarker(centerLng, centerLat);

            const config = {
                pageCapacity: 10,
                onSearchComplete: localSearchResult
            };
            localsearch = new T.LocalSearch(map, config);
            // 添加地图点击监听
            map.addEventListener('click', handleMapClick);

            // 初始化搜索功能
            initSearch();
        }
        function localSearchResult(result) {
            map.clearOverLays();
            if (parseInt(result.getResultType()) !== 1) {
                return;
            }
            let zoomArr = [];

            let obj = result.getPois();
            for (var i = 0; i < obj.length; i++) {
                //闭包
                (function (i) {
                    var lnglatArr = obj[i].lonlat.split(",");
                    var lnglat = new T.LngLat(lnglatArr[0], lnglatArr[1]);

                    //创建标注对象
                    var marker = new T.Marker(lnglat);
                    //地图上添加标注点
                    map.addOverLay(marker);
                    zoomArr.push(lnglat);
                })(i);
            }
            //显示地图的最佳级别
            map.setViewport(zoomArr);
        }

        // 更新表单字段
        function updateFormFields({lng, lat, address, name}) {
            $('#${p.lng}').val(lng);
            $('#${p.lat}').val(lat);
            $('#${p.address}').val(address);
            if (!name) return;
            $('#${p.name}').val(name);
        }
        // 处理地图点击事件
        async function handleMapClick(e) {
            const lng = e.lnglat.getLng();
            const lat = e.lnglat.getLat();

            try {
                // 反向地理编码获取地址
                const result = await new Promise((resolve, reject) => {
                    geocoder.getLocation(e.lnglat, resolve, reject);
                });

                updateFormFields({
                    lng: lng.toFixed(6),
                    lat: lat.toFixed(6),
                    address: result.addressComponent.address || '',
                    name: result.addressComponent?.poi || $('#name').val()
                });

                addMarker(lng, lat);
                map.panTo(new T.LngLat(lng, lat));
            } catch (error) {
                js.showMessage('无法获取该位置的地址信息');
            }
        }

        // 添加/更新标记
        function addMarker(lng, lat) {
            if(currentMarker) map.removeOverLay(currentMarker);
            // 创建标记并设置为可拖动
            currentMarker = new T.Marker(new T.LngLat(lng, lat));
            // currentMarker.setDraggable(true);

            // 添加标记拖动结束事件
            currentMarker.addEventListener("dragend", function(e) {
                const newLng = e.lnglat.getLng();
                const newLat = e.lnglat.getLat();

                // 更新表单字段
                geocoder.getLocation(e.lnglat, function(result) {
                    updateFormFields({
                        lng: newLng.toFixed(6),
                        lat: newLat.toFixed(6),
                        address: result.addressComponent.address || '',
                        name: result.addressComponent?.poi || $('#name').val()
                    });
                });
            });

            map.addOverLay(currentMarker);
            bindMarkerClick(lng, lat);
        }

        // 绑定标记点击事件
        function bindMarkerClick(lng, lat) {
            currentMarker.addEventListener('click', () => {
                showInfoWindow(lng, lat);
            });
        }

        // 显示信息窗口
        function showInfoWindow(lng, lat) {
            const content = `【` + ($('#${p.name}').val() || '未命名') + `】<br>
                        ` + ($('#${p.address}').val() || '无地址信息') + `<br>
                        经度：` + lng.toFixed(6) + `<br>
                        纬度：` + lat.toFixed(6) + ``;
            const infoWindow = new T.InfoWindow(content, {offset: new T.Point(0, -30)});
            map.openInfoWindow(infoWindow, new T.LngLat(lng, lat));
        }

        // 显示地图弹窗
        $('#btnShowMap').click(function(){
            $('#mapModal').modal('show').on('shown.bs.modal', initMap);
        });

        // 清理地图资源
        $('#mapModal').on('hidden.bs.modal', () => {
            if(map) map = null;
            currentMarker = null;
        });

        // 初始化搜索功能
        function initSearch() {
            const $searchBox = $(`
                <div class="map-search-box">
                    <input type="text" placeholder="输入地址进行搜索" class="form-control">
                    <button type="button" class="btn btn-primary">搜索</button>
                </div>
            `).prependTo('#mapContainer');

            // 阻止搜索区域的点击事件冒泡
            $searchBox.on('mousedown mousemove mouseup click', function(event) {
                event.stopPropagation();
            });

            $searchBox.find('button').click(async (event) => {
                event.preventDefault();
                const address = $searchBox.find('input').val().trim();
                if (!address) return;
                localsearch.search(address);
            });

            // 添加回车键搜索
            $searchBox.find('input').keypress(function(event) {
                if(event.which == 13) {
                    event.preventDefault();
                    $searchBox.find('button').click();
                }
            });
        }
    })
</script>