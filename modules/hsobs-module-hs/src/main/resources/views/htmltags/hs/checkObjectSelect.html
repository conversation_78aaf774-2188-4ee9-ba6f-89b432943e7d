<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
* No deletion without permission, or be held responsible to law. */

/**
* 物业核查事项列表
* <AUTHOR>
* @version 2025-04-27
*/
var p = {

	// 标签参数
	path: path!,
	value: value![],				// 元素值
	// 内置参数
	thisTag: thisTag,
	readonly: readonly!'false',
	editable: readonly!'false'=='false',
	dataUrl: dataUrl!,
	title: title!'选择核查事项',
	multiple: multiple!'false'
};


// 编译属性参数
form.attrs(p);

%>
<div class="form-unit-wrap table-form" id="hsQwApplyerWrapper">
	<#form:btnlistselect id="objectGridAddRowListselectBtn" title="${p.title}"
	allowClear="true"
	btnLabel="选择核查事项"
	setSelectDataFuncName="hsObjectSelect"
	url="${ctx}/checkobject/hsQwManagementCheckObject/hsQwManagementCheckObjectSelect"
	checkbox="${p.multiple}"
	btnClass="btn btn-primary btn-sm mt10 mb10"
	itemCode="id" itemName="name" />
</div>

<input type="hidden" id="objectIdStr" name="objectIdStr"  />
<table id="hsObjectListDataGrid"></table>
<script src="/hsobs/static/bootstrap/js/bootstrap.min.js"></script>
<script src="/hsobs/static/select2/4.0/select2.js?V5.9-03111521"></script>
<script src="/hsobs/static/select2/4.0/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/layer/3.5/layer.js?V5.9-03111521"></script>
<script src="/hsobs/static/laydate/5.3/laydate.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery/jquery-ui-sortable-1.13.2.min.js"></script>
<script src="/hsobs/static/jqGrid/4.7/js/jquery.jqGrid.js?V5.9-03111521"></script>
<script src="/hsobs/static/jqGrid/4.7/js/jquery.jqGrid.extend.js?V5.9-03111521"></script>
<script src="/hsobs/static/jqGrid/4.7/js/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/jquery.validate.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/localization/messages_zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/jquery.validate.extend.js?V5.9-03111521"></script>
<script src="/hsobs/static/webuploader/0.1/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/jeesite.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/i18n/jeesite_zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/common.js?V5.9-03111521"></script>
<script>
	$('#hsObjectListDataGrid').dataGrid({
		data: ${p.value},
		datatype: 'local',
		autoGridHeight: function(){return 'auto'},
		columnModel: [
			{header:'${text("物品编号")}', name:'id',  sortable:false, width:150, align:"left"},
			{header:'${text("物品名称")}', name:'name',  sortable:false, width:150, align:"left"},
			{header:'${text("状态")}', name:'status',  sortable:false, width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
				}},
			{header:'${text("更新时间")}', name:'updateDate',  sortable:false, width:150, align:"center"},
			{header:'${text("备注信息")}', name:'remarks',  sortable:false, width:150, align:"left"},
			{header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
					var actions = [];
					if (${p.editable}){
						if (val == 'new'){
							actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsObjectListDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
						}else{
							actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsObjectListDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');});return false;">删除</a>&nbsp;');
						}
					}
					return actions.join('');
				}, editoptions: {defaultValue: 'new'}}
		],
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});

	// 更新隐藏字段中的事项ID字符串
	function updateObjectIdStr(objectId, action) {
	    let objectIdStrField = $('#objectIdStr');
	    let currentValue = objectIdStrField.val() || '';
	    let idArray = currentValue ? currentValue.split(',') : [];
	    
	    if (action === 'add') {
	        // 如果ID不存在，则添加
	        if (idArray.indexOf(objectId) === -1) {
	            idArray.push(objectId);
	        }
	    } else if (action === 'remove') {
	        // 如果ID存在，则删除
	        let index = idArray.indexOf(objectId);
	        if (index !== -1) {
	            idArray.splice(index, 1);
	        }
	    }
	    
	    // 更新隐藏字段的值
	    objectIdStrField.val(idArray.join(','));
	}

	function hsObjectSelect(id, selectData) {
	    if (id === 'objectGridAddRowListselectBtn') {
	        // 确保 selectData 不为空
	        if ($.isEmptyObject(selectData)) {
	            return;
	        }
	        
	        let grid = $('#hsObjectListDataGrid');
	        // 获取当前表格中的所有数据
	        let existingData = grid.jqGrid('getRowData');
	        let addedCount = 0;
	        let duplicateCount = 0;
	        
	        // 遍历所有选择的事项
	        $.each(selectData, (key, value) => {
	            let selectId = key.toString();
	            let selectObject = value;
	            
	            // 检查是否已存在相同ID的事项
	            let isDuplicate = false;
	            for (let i = 0; i < existingData.length; i++) {
	                if (existingData[i].id === selectId) {
	                    isDuplicate = true;
	                    duplicateCount++;
	                    break;
	                }
	            }
	            
	            // 如果不是重复的事项，则添加到表格中
	            if (!isDuplicate) {
	                grid.jqGrid('addRow', {
	                    position: 'first',
	                    addRowParams: { keys: false, focusField: true },
	                    initdata: {
	                        id: selectObject.id,
	                        name: selectObject.name || "",
	                        status: selectObject.status || "",
	                        updateDate: selectObject.updateDate || "",
	                        remarks: selectObject.remarks || "",
	                        actions: 'new'
	                    }
	                });
	                addedCount++;
	                
	                // 更新隐藏字段的值，添加新的ID
	                updateObjectIdStr(selectObject.id, 'add');
	            }
	        });
	        
	        // 提示用户添加结果
	        if (addedCount > 0) {
	            if (duplicateCount > 0) {
	                js.showMessage("成功添加" + addedCount + "个事项，" + duplicateCount + "个事项已存在，不再重复添加");
	            } else {
	                js.showMessage("成功添加" + addedCount + "个事项");
	            }
	        } else if (duplicateCount > 0) {
	            js.showMessage("所选事项已全部存在，不再重复添加");
	        }
	    }
	}

	// 修改删除操作，在删除事项时同时更新隐藏字段
	$(document).ready(function() {
	    // 处理原有数据，初始化隐藏字段
	    let grid = $('#hsObjectListDataGrid');
	    let existingData = grid.jqGrid('getRowData');
	    let initialIds = [];
	    
	    for (let i = 0; i < existingData.length; i++) {
	        if (existingData[i].id) {
	            initialIds.push(existingData[i].id);
	        }
	    }
	    
	    // 设置初始值
	    $('#objectIdStr').val(initialIds.join(','));
	    
	    // 监听删除事件
	    $(document).on('click', '#hsObjectListDataGrid a[onclick*="delRowData"]', function(e) {
	        // 阻止默认事件
	        e.preventDefault();
	        
	        // 获取行ID
	        let rowId = $(this).closest('tr').attr('id');
	        let rowData = $('#hsObjectListDataGrid').jqGrid('getRowData', rowId);
	        
	        // 确认删除
	        js.confirm('${text("你确认要删除这条数据吗？")}', function() {
	            // 删除行
	            $('#hsObjectListDataGrid').dataGrid('delRowData', rowId);
	            
	            // 更新隐藏字段
	            if (rowData && rowData.id) {
	                updateObjectIdStr(rowData.id, 'remove');
	            }
	        });
	        
	        return false;
	    });
	    
	    // 监听另一种删除方式
	    $(document).on('click', '#hsObjectListDataGrid a[onclick*="setRowData"]', function(e) {
	        // 阻止默认事件
	        e.preventDefault();
	        
	        // 获取行ID
	        let rowId = $(this).closest('tr').attr('id');
	        let rowData = $('#hsObjectListDataGrid').jqGrid('getRowData', rowId);
	        
	        // 确认删除
	        js.confirm('${text("你确认要删除这条数据吗？")}', function() {
	            // 隐藏行
	            $('#hsObjectListDataGrid').dataGrid('setRowData', rowId, null, {display: 'none'});
	            $('#' + rowId + '_status').val(Global.STATUS_DELETE);
	            
	            // 更新隐藏字段
	            if (rowData && rowData.id) {
	                updateObjectIdStr(rowData.id, 'remove');
	            }
	        });
	        
	        return false;
	    });
	});
</script>