<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */

/**
 * 配租申请单-申请房源
 * <AUTHOR>
 * @version 2025-02-11
 */
var p = {

	// 标签参数
	value: value!'',				// 元素值
	// 内置参数
	thisTag: thisTag,
	readonly: readonly!'false',
	editable: readonly!'false'=='false'
};


// 编译属性参数
form.attrs(p);

%>

<table id="hsQwApplyHouseDataGrid" class="table-form hs-table-form"></table>
<% if (hasPermi('apply:hsQwApply:edit') && p.editable){ %>
	<a href="#" id="hsQwApplyHouseDataGridBtn" class="btn btn-primary btn-sm mt10 mb10" ><i class="fa fa-plus"></i> ${text('增行')}</a>
<% } %>
<script src="/hsobs/static/bootstrap/js/bootstrap.min.js"></script>
<script src="/hsobs/static/select2/4.0/select2.js?V5.9-03111521"></script>
<script src="/hsobs/static/select2/4.0/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/layer/3.5/layer.js?V5.9-03111521"></script>
<script src="/hsobs/static/laydate/5.3/laydate.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery/jquery-ui-sortable-1.13.2.min.js"></script>
<script src="/hsobs/static/jqGrid/4.7/js/jquery.jqGrid.js?V5.9-03111521"></script>
<script src="/hsobs/static/jqGrid/4.7/js/jquery.jqGrid.extend.js?V5.9-03111521"></script>
<script src="/hsobs/static/jqGrid/4.7/js/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/jquery.validate.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/localization/messages_zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/jquery-validation/1.16/jquery.validate.extend.js?V5.9-03111521"></script>
<script src="/hsobs/static/webuploader/0.1/i18n/zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/jeesite.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/i18n/jeesite_zh_CN.js?V5.9-03111521"></script>
<script src="/hsobs/static/common/common.js?V5.9-03111521"></script>
<script>
	//# // 初始化租赁资格轮候申请人DataGrid对象
	$('#hsQwApplyHouseDataGrid').dataGrid({
		data: ${p.value},
		datatype: 'local', // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度

		//# // 设置数据表格列
		columnModel: [
			{header:'状态', name:'status', editable:true, hidden:true},
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'${text("房屋坐落信息")}', name:'address', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required'}},
			{header:'${text("建筑面积")}', name:'floorArea', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required'}},
			{header:'${text("产权性质")}', name:'propertyRight', width:150, editable:${p.editable}, edittype:'text', editoptions:{'maxlength':'10', 'class':'form-control required'}},
			{header:'${text("备注信息")}', name:'remarks', width:150, editable:${p.editable}, edittype:'textarea', editoptions:{'maxlength':'500', 'class':'form-control', 'rows':'1'}},
			{header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
				var actions = [];
				if (${p.editable}) {
					if (val == 'new') {
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyHouseList\').dataGrid(\'delRowData\',\'' + obj.rowId + '\')});return false;">删除</a>&nbsp;');
					} else {
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyHouseList\').dataGrid(\'setRowData\',\'' + obj.rowId + '\',null,{display:\'none\'});$(\'#' + obj.rowId + '_status\').val(\'' + Global.STATUS_DELETE + '\');});return false;">删除</a>&nbsp;');
					}
				}
				return actions.join('');
			}, editoptions: {defaultValue: 'new'}}
		],

		//# // 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
		editGridAddRowBtn: $('#hsQwApplyHouseDataGridBtn'),	// 子表增行按钮
		editGridAddRowBtnToHeader: ${p.editable},	// 子表增行按钮是否显示到表头上
		editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

		//# // 编辑表格的提交数据参数
	//# // 编辑表格的提交数据参数
		editGridInputFormListName: 'hsQwApplyHouseList', // 提交的数据列表名
		editGridInputFormListAttrs: 'status,id,applyId,address,floorArea,propertyRight,createBy,createDate,updateBy,updateDate,remarks,', // 提交数据列表的属性字段
			//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>