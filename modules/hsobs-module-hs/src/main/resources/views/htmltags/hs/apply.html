<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
   * No deletion without permission, or be held responsible to law. */

/**
 * 配租申请单-申请单
 * <AUTHOR>
 * @version 2025-02-11
 */
var p = {

	// 业务流程实体对象
	entity: entity!,
	// 内置参数
	thisTag: thisTag,
	readonly: readonly!'false'
};


// 编译属性参数
form.attrs(p);

%>
<tr>
	<td class="form-label hs-form-label">
		<span class="required ">*</span> ${text('归属机构')}：<i class="fa icon-question hide"></i>
	</td>
	<td>
		<#form:treeselect id="officeCode" title="${text('机构选择')}"
		path="officeCode" labelPath="office.officeName"
		url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi!}"
		class="required" allowClear="false" canSelectRoot="true" readonly="true" canSelectParent="false"/>
	</td>
	<% if (p.entity.applyMatter!'0'!='0') {%>
	<td class="form-label hs-form-label">
		<span class="required ">*</span> ${text('申请单信息')}：<i class="fa icon-question hide"></i>
	</td>
	<td>
		<a class="hsBtnList"  title="申请单信息" href="${ctx}/apply/hsQwApply/form?redirectPage=hsQwApplyFormCompactConfirm&id=${entity.applyedId}&isRead=true" > ${entity.applyedId} </a>
	</td>
	<% } else { %>
	<td class="form-label hs-form-label"></td>
	<td></td>
	<% } %>
</tr>
<tr>
	<td class="form-label hs-form-label">
		<span class="required ">*</span> ${text('人口数量')}：<i class="fa icon-question hide"></i>
	</td>
	<td>
		<#form:input path="familyPeoples" dataFormat="number" maxlength="20" readonly="${p.readonly}" class="form-control required"/>
	</td>
	<td class="form-label hs-form-label">
		<span class="required ">*</span> ${text('家庭年收入（元）')}：<i class="fa icon-question hide"></i>
	</td>
	<td>
		<#form:input path="familyIncome" maxlength="20" readonly="${p.readonly}" class="form-control required"/>
	</td>
</tr>
<tr>
	<td class="form-label hs-form-label">
		<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
	</td>
	<td colspan="3">
		<#form:textarea path="remarks" rows="4" maxlength="500" readonly="${p.readonly}" class="form-control"/>
	</td>
</tr>