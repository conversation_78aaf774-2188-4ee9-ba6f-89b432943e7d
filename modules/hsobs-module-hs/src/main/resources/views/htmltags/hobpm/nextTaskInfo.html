<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
* No deletion without permission, or be held responsible to law. */

/**
* 下一步流程信息控件
* <AUTHOR>
* @version 2019-11-3
*/
var p = {

// 业务流程实体对象
bpmEntity: bpmEntity!,

// 内置参数
thisTag: thisTag
};
%><div class="form-unit">${text('下一步流程信息')}</div>
<div class="row">
    <div class="col-xs-6">
        <div class="form-group">
            <label class="control-label col-xs-4" title="${text('下一步任务的要求完成期限，提醒作用')}">
                <span class="required hide">*</span> ${text('要求完成时间')}：<i class="fa icon-question "></i></label>
            <div class="col-xs-8">
                <#form:input path="bpm.dueDate" readonly="readonly" maxlength="20" class="form-control laydate"
                dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
            </div>
        </div>
    </div>
    <div class="col-xs-12">
        <div class="form-group">
            <label class="control-label col-xs-2" title="${text('若不填，下一步处理人由流程自动分配')}">
                <span class="required hide">*</span> ${text('下一步处理人')}：<i class="fa icon-question "></i></label>
            <div class="col-xs-10">
                <#form:listselect id="userSelect" title="${text('用户选择')}" path="bpm.nextUserCodes"
                url="${ctx}/sys/empUser/empUserSelect" allowClear="true"
                checkbox="true" itemCode="userCode" itemName="userName"/>
            </div>
        </div>
    </div>
</div>