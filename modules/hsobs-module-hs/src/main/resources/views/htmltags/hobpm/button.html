<%/* Copyright (c) 2013-Now http://jeesite.com All rights reserved.
* No deletion without permission, or be held responsible to law. */

/**
* 流程控件：流程按钮
* <AUTHOR>
* @version 2019-11-3
*/
var p = {

// 业务流程实体对象
bpmEntity: bpmEntity!,

// 业务表单Key
formKey: formKey!,

// 按钮的标签，设置为空，则不显示按钮
claimText: claimText!text('签 收'),
completeText: completeText!text('提 交'),
backText: backText!text('退 回'),			// 退回到之前办理过的任务环节
turnText: turnText!text('转 办'),			// 将任务转交他人办理，办理完成后继续流转
delegateText: delegateText!text('委 托'),	// 任务交由被委托人办理，办理完成后返回委托人
modifySignText: modifySignText!text('加减签'),// 任务加签或减签操作
stopText: stopText!text('终 止'),			// 单据作废，结束流程实例
moveText: moveText!text('自由流'), 			// 特事特办，自由流，随意跳转
traceText: traceText!text('流程图'),			// 流程状态，流程图，流程记录
rollbackText: moveText!text('撤回任务'), 	// 下一环节未审批的可撤回重做
restartText: restartText!text('重启流程'),	// 已经结束的流程，重现启动流程

// 内置参数
thisTag: thisTag
};
p.params = p.bpmEntity.bpm!;
if (isEmpty(p.params!) || type.fullName(p.params!) != 'com.jeesite.modules.bpm.entity.BpmParams'){
print('参数 bpmEntity 不是  BpmEntity 的超类！');
return;
}
p.bizKey = p.bpmEntity.id!;
p.currentUser = user();
%><span id="bpmButton" data-task-id="${p.params.taskId}" data-proc-ins-id="${p.params.procInsId}" data-form-key="${p.formKey}" data-biz-key="${p.bizKey}"></span>
<script src="${ctxStatic}/htmltags/bpm/button.js"></script>
<script id="bpmButtonTpl" type="text/template">
  <input type="hidden" id="bpmTaskId" name="bpm.taskId" value="{{d.task.id}}"/>
  <input type="hidden" id="processName" name="processName" value="{{d.task.name}}"/>
  <input type="hidden" id="bpmProcInsId" name="bpm.procInsId" value="{{d.task.procIns.id}}"/>
  <input type="hidden" id="bpmActivityId" name="bpm.activityId" value="{{d.task.activityId}}"/>
  {{# if(d.task.procIns.id == ''){ }}
  <button type="submit" class="btn btn-sm btn-primary mr3" data-type="complete"><i class="fa fa-check"></i> ${p.completeText}</button>
  {{# } else if(d.task.id != '' && (d.task.endTime == null || d.task.endTime == '')){ }}
  <span class="taskClaim {{d.task.assignee && d.task.assignee != '' && d.task.assignee == '${p.currentUser.userCode!}' ? 'hide' : ''}}">
		<button type="button" class="btn btn-sm btn-success mr3" data-type="claim"><i class="fa fa-hand-paper-o"></i> ${p.claimText}</button>
	</span>
  <span class="needClaim {{d.task.assignee && d.task.assignee != '' ? '' : 'hide'}}">
	    {{# if(${isNotBlank(p.completeText)}){ }}
		<button type="submit" class="btn btn-sm btn-primary mr3" data-type="complete"><i class="fa fa-check"></i> ${p.completeText}</button>
	    {{# } }}
		{{# if(${isNotBlank(p.backText)} && js.val(d.task, 'procIns.procDef.form.optionMap.allowBackTask') != Global.NO){ }}
			<button type="button" class="btn btn-sm btn-danger mr3" data-type="back" data-layer="true" data-layer-width="800" data-layer-height="475"><i class="fa fa-reply"></i> ${p.backText}</button>
		{{# } }}
		{{# if(${isNotBlank(p.turnText)} && js.val(d.task, 'procIns.procDef.form.optionMap.allowTurnTask') != Global.NO){ }}
			<button type="button" class="btn btn-sm btn-warning mr3" data-type="turn" data-layer="true" data-layer-width="600" data-layer-height="330"><i class="fa fa-share"></i> ${p.turnText}</button>
		{{# } }}
		{{# if(${isNotBlank(p.delegateText)} && js.val(d.task, 'procIns.procDef.form.optionMap.allowDelegateTask') != Global.NO){ }}
			<button type="button" class="btn btn-sm btn-success mr3" data-type="delegate" data-layer="true" data-layer-width="600" data-layer-height="330"><i class="fa fa-retweet"></i> ${p.delegateText}</button>
		{{# } }}
		{{# if(${isNotBlank(p.modifySignText)} && d.task.hasMultiInstance && js.val(d.task, 'procIns.procDef.form.optionMap.allowModifySignTask') != Global.NO){ }}
			<button type="button" class="btn btn-sm bg-purple mr3" data-type="modifySign" data-layer="true" data-layer-width="600" data-layer-height="390"><i class="fa fa-tasks"></i> ${p.modifySignText}</button>
		{{# } }}
		{{# if(${isNotBlank(p.stopText)} && (d.task.procIns.startUserId == '${p.currentUser.userCode!}' || '${p.currentUser.admin!}' == 'true') && js.val(d.task, 'procIns.procDef.form.optionMap.allowStop') != Global.NO){ }}
			<button type="button" class="btn btn-sm btn-danger mr3" data-type="stop" data-layer="true" data-layer-width="500" data-layer-height="220"><i class="fa fa-power-off"></i> ${p.stopText}</button>
		{{# } }}
		{{# if(${isNotBlank(p.moveText)} && ('${p.currentUser.admin!}' == 'true' || js.val(d.task, 'procIns.procDef.form.optionMap.allowMoveTask') == Global.YES)){ }}
			<button type="button" class="btn btn-sm btn-primary mr3" data-type="move" data-layer="true" data-layer-width="600" data-layer-height="375"><i class="fa fa-paper-plane-o"></i> ${p.moveText}</button>
		{{# } }}
	</span>
  {{# } }}
  {{# if(${isNotBlank(p.traceText)} && d.task.procIns.id != ''){ }}
  <button type="button" class="btn btn-sm btn-info mr3" data-type="trace" data-layer="true">流程追踪 ${p.traceText}</button>
  {{# } }}
  {{# if(${isNotBlank(p.rollbackText)} && js.val(d.task, 'procIns.procDef.form.optionMap.allowRollbackTask') != Global.NO && d.task.procIns.id != '' && (d.task.procIns.endTime == null || d.task.procIns.endTime == '') && d.task.id != '' && !(d.task.endTime == null || d.task.endTime == '') && d.task.assignee == '${p.currentUser.userCode!}'){ }}
  <button type="button" class="btn btn-sm bg-purple mr3" data-type="rollback" data-confirm="${text("如果该任务的下一环节未被处理，则可以尝试撤回该任务的办理。您确认要尝试撤回吗？")}"><i class="fa icon-action-undo"></i> ${p.rollbackText}</button>
  {{# } }}
  {{# if(${isNotBlank(p.restartText)} && d.task.procIns.ended && (d.task.procIns.startUserId == '${p.currentUser.userCode!}' || '${p.currentUser.admin!}' == 'true') && js.val(d.task, 'procIns.procDef.form.optionMap.allowRestart') == Global.YES){ }}
  <button type="button" class="btn btn-sm btn-default mr3" data-type="restart"><i class="fa fa-rotate-left"></i> ${p.restartText}</button>
  {{# } }}
</script>