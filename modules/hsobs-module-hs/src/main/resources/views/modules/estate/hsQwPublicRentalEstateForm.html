<% layout('/layouts/default.html', {title: '房源楼盘信息表管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa icon-note"></i> ${text(hsQwPublicRentalEstate.isNewRecord ? '新增房源楼盘信息表' : '编辑房源楼盘信息表')}
            </div>
            <div class="box-tools pull-right hide">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
        </div>
        <#form:form id="inputForm" model="${hsQwPublicRentalEstate}" action="${ctx}/estate/hsQwPublicRentalEstate/save" method="post" class="form-horizontal">
        <div class="box-body hs-box-body-bpm">
            <div class="form-unit">${text('基本信息')}</div>
            <#form:hidden path="id"/>
            <div class="hs-table-div">
                <table class="table-form hs-table-form">
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('楼盘名称')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="name" maxlength="255" class="form-control required"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('楼盘地址')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <div class="input-group">
                                <#form:input path="address" maxlength="255" class="form-control"/>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-primary" id="btnShowMap" title="查看标注">
                                        <i class="fa fa-map-marker"></i>
                                    </button>
                                </span>
                            </div>
                        </td>
                        <#hs:map name="name" address="address" lat="latitude" lng="longitude"
                        latValue="${hsQwPublicRentalEstate.latitude}" lngValue="${hsQwPublicRentalEstate.longitude}"
                        nameValue="${hsQwPublicRentalEstate.name}" addressValue="${hsQwPublicRentalEstate.address}" />
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('经度')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="longitude" maxlength="20" class="form-control required" readonly="true"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('纬度')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="latitude" maxlength="20" class="form-control required" readonly="true"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('地市')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:select path="city" id="citySelect" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" class="form-control required" />
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('区域')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:select path="area" id="areaSelect" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('开发商')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="propertyDeveloper" maxlength="255" class="form-control required"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('建设单位')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="developer" maxlength="255" class="form-control required"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('建造年代')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="year" readonly="true" maxlength="20" class="form-control laydate"
                            dataFormat="date" data-type="date" data-format="yyyy-MM"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('产权年限')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="term" class="form-control digits"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('容积率(%)')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="plotRatio" maxlength="20" class="form-control number required"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('绿化率(%)')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="greenRate" maxlength="20" class="form-control number required"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('小区户数')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="households" class="form-control digits"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('车位数')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="parkNum" class="form-control digits"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('物业公司')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="managementCompany" maxlength="255" class="form-control"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required">*</span> ${text('物业费（每平方）')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="managementFee" maxlength="20" class="form-control required number"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('公摊水费（每平方）')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="waterFee" class="form-control required number"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('公摊电费（每平方）')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="electricFee" class="form-control required number"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('公摊总面积')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="publicArea" class="form-control required number"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('资产编码')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:listselect id="assetSelect" title="资产信息查询" path="assetCode"
                            url="${ctx}/szgz/assets/assetSelect" allowClear="false" class="form-control required"
                            checkbox="true" itemCode="assetCode" itemName="assetName" />
                        </td>
                    </tr>
                </table>
            </div>

            <div class="form-unit">${text('配套信息')}</div>
            <div class="hs-table-div">
                <table class="table-form hs-table-form">
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('供电方式')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="powerSupply" maxlength="100" class="form-control"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('供水方式')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="waterSupply" maxlength="100" class="form-control"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('楼盘状态')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="estateStatus" maxlength="255" class="form-control"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('周边规划')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="surroundingPlan" maxlength="500" class="form-control"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td colspan="3">
                            <#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('图片上传')}：
                        </td>
                        <td colspan="3">
                            <#form:fileupload id="uploadImage" bizKey="${hsQwPublicRentalEstate.id}" bizType="hsQwPublicRentalEstate_image"
                            uploadType="image" class="" readonly="false" preview="true" dataMap="true"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('附件上传')}：
                        </td>
                        <td colspan="3">
                            <#form:fileupload id="uploadFile" bizKey="${hsQwPublicRentalEstate.id}" bizType="hsQwPublicRentalEstate_file"
                            uploadType="all" class="" readonly="false" preview="true" dataMap="true"/>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="box-footer">
                <div class="row">
                    <div class="col-sm-offset-2 col-sm-10">
                        <% if (hasPermi('estate:hsQwPublicRentalEstate:edit')){ %>
                        <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
                        <% } %>
                        <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
                    </div>
                </div>
            </div>
        </#form:form>
    </div>
</div>
<% } %>
<script>
    $('#inputForm').validate({
        submitHandler: function(form){
            js.ajaxSubmitForm($(form), function(data){
                js.showMessage(data.message);
                if(data.result == Global.TRUE){
                    js.closeCurrentTabPage(function(contentWindow){
                        contentWindow.page();
                    });
                }
            }, "json");
        }
    });
</script>

<script>
    $(document).ready(function() {

        let cityCode = $('#citySelect').val();
        showAreaInfo(cityCode);
        let areaSelCode = "${hsQwPublicRentalEstate.area!}";
        if(areaSelCode){
            $("#areaSelect").val(areaSelCode);
        }

        $('#citySelect').change(function() {
            let cityCode = $('#citySelect').val();
            showAreaInfo(cityCode);
        });

        function showAreaInfo(cityCode) {

            let areaSelect = $('#areaSelect');
            areaSelect.empty();
            if (cityCode) {
                $.ajax({
                    url: '${ctx}/dataintelligencetotal/getAreaInfo',
                    type: 'GET',
                    data: {areaCode: cityCode},
                    dataType: 'json',
                    async: false,
                    error: function(data){
                        js.showErrorMessage(data.responseText);
                    },
                    success: function(data, status, xhr){
                        $.each(data, function(index, item) {
                            let option = $("<option>")
                                .val(item.areaCode)
                                .text(item.areaName);
                            $("#areaSelect").append(option);
                        });
                    }
                });
            }
        }
    });
</script>

