<% layout('/layouts/default.html', {title: '公租房房源楼盘信息表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('房源楼盘信息表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('estate:hsQwPublicRentalEstate:edit')){ %>
					<a href="${ctx}/estate/hsQwPublicRentalEstate/form" class="btn btn-default btnTool" title="${text('新增房源楼盘信息表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwPublicRentalEstate}" action="${ctx}/estate/hsQwPublicRentalEstate/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('楼盘名称')}：</label>
							<div class="control-inline">
								<#form:input path="name" maxlength="255" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('建造年代')}：</label>
							<div class="control-inline">
								<#form:input path="year_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="date" data-type="date" data-format="yyyy-MM" data-done="year_lte.click()"/>
								&nbsp;-&nbsp;
								<#form:input path="year_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="date" data-type="date" data-format="yyyy-MM" />
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('建设单位')}：</label>
							<div class="control-inline">
								<#form:input path="developer" maxlength="255" class="form-control width-120"/>
							</div>
						</div>
					</div>

					<!-- 第二行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('物业公司')}：</label>
							<div class="control-inline">
								<#form:input path="managementCompany" maxlength="255" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('楼盘地址')}：</label>
							<div class="control-inline">
								<#form:input path="address" maxlength="255" class="form-control width-120"/>
							</div>
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("楼盘编号")}', name:'id',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return '<a href="${ctx}/estate/hsQwPublicRentalEstate/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑房源楼盘信息表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("楼盘名称")}', name:'name',  sortable:false, width:150, align:"left"},
		{header:'${text("建造年代")}', name:'year',  sortable:false, width:120, align:"left"},
		{header:'${text("产权年限")}', name:'term',  sortable:false, width:120, align:"left"},
		{header:'${text("建设单位")}', name:'developer',  sortable:false, width:150, align:"left"},
		{header:'${text("物业公司")}', name:'managementCompany',  sortable:false, width:150, align:"left"},
		{header:'${text("状态")}', name:'status',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate',  sortable:false, width:150, align:"left"},
		{header:'${text("楼盘地址")}', name:'address',  sortable:false, width:200, align:"left"},
		{header:'${text("操作")}', name:'actions', align:"left", width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('estate:hsQwPublicRentalEstate:edit')){
				actions.push('<a href="${ctx}/estate/hsQwPublicRentalEstate/form?id='+row.id+'" class="hsBtnList" title="${text("编辑房源楼盘信息表")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/estate/hsQwPublicRentalEstate/disable?id='+row.id+'" class="btnList" title="${text("停用房源楼盘信息表")}" data-confirm="${text("确认要停用该房源楼盘信息表吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/estate/hsQwPublicRentalEstate/enable?id='+row.id+'" class="btnList" title="${text("启用房源楼盘信息表")}" data-confirm="${text("确认要启用该房源楼盘信息表吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/estate/hsQwPublicRentalEstate/delete?id='+row.id+'" class="btnList" title="${text("删除房源楼盘信息表")}" data-confirm="${text("确认要删除该房源楼盘信息表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	ajaxSuccess: function() {
		            requestAnimationFrame(function() {
                CommonTable.setupFixedColumns('dataGrid');
            });
		// js.closeLoading(0, true);
	}
});
</script>
