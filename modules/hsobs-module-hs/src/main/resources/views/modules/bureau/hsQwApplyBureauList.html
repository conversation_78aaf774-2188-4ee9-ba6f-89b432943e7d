<% layout('/layouts/default.html', {title: '局直管公房申请表管理', libs: ['dataGrid']}){ %>
<div class="box box-main">
    <div class="box-header">
        <div class="box-title">
            <i class="fa icon-notebook"></i> ${text('局直管公房申请表管理')}
        </div>
        <div class="box-tools pull-right">
            <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i>
                ${text('查询')}</a>
            <a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i
                    class="fa fa-navicon"></i></a>
        </div>
    </div>
    <div class="box-body">
        <#form:form id="searchForm" model="${hsQwApplyBureau}" action="${ctx}/bureau/hsQwApplyBureau/listData" method="post"
        class="form-inline hide"
        data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
        <div class="form-group">
            <label class="control-label">${text('房源选择')}：</label>
            <div class="control-inline ">
                <#form:listselect id="houseIdSelect" title="房源选择选择"
                path="houseId" labelPath="11"
                url="${ctx}/house/hsQwPublicRentalHouse/houseSelect?isPublic=1&type=3" allowClear="false"
                checkbox="false" itemCode="id" itemName="simpleInfo"/>
            </div>
        </div>

        <div class="form-group">
            <label class="control-label">${text('承租人')}：</label>
            <div class="control-inline width-60">
                <#form:input path="mainApplyer.name" class="form-control"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label">${text('状态')}：</label>
            <div class="control-inline ">
                <#form:select path="status" dictType="sys_status" blankOption="true" class="form-control isQuick"/>
            </div>
        </div>
        <div class="form-group">
            <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i>
                ${text('查询')}
            </button>
            <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i>
                ${text('重置')}
            </button>
        </div>
    </
    #form:form>
    <table id="dataGrid"></table>
    <div id="dataGridPage"></div>
</div>
</div>
</div>
<% } %>
<script>
    //# // 初始化DataGrid对象
    $('#dataGrid').dataGrid({
        searchForm: $('#searchForm'),
        columnModel: [
            {
                header: '${text("申请单编号")}',
                name: 'id',
                index: 'a.id',
                width: 80,
                align: "center",
                frozen: true,
                formatter: function (val, obj, row, act) {
                    return '<a href="${ctx}/bureau/hsQwApplyBureau/form?id=' + row.id + '" class="hsBtnList" title="${text("编辑局直公房变更")}">' + (val || row.id) + '</a>';
                }
            },
            {header: '${text("房源信息")}', name: 'house.simpleInfo', index: 'h.id', width: 60, align: "center"},
            {
                header: '${text("主申请人身份证")}',
                name: 'mainApplyer.idNum',
                index: 'ha.id_num',
                width: 100,
                align: "center"
            },
            {
                header: '${text("主申请人手机号")}',
                name: 'mainApplyer.phone',
                index: 'ha.phone',
                width: 60,
                align: "center"
            },
            {
                header: '${text("审核状态")}',
                name: 'status',
                index: 'a.status',
                width: 60,
                align: "center",
                formatter: function (val, obj, row, act) {
                    return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
                }
            },
            {header:'${text("智能核验时间")}', name:'checkDate',  sortable:false, width:150, align:"center"},
            {header:'${text("智能校验")}', name:'autoCheck',  sortable:false, width:150, align:"left"},
            {header: '${text("备注信息")}', name: 'remarks', index: 'a.remarks', width: 130, align: "center"},
            {
                header: '${text("操作")}', name: 'actions', width: 120, formatter: function (val, obj, row, act) {
                    var actions = [];
                    //# if(hasPermi('apply:hsQwApply:edit')){
                    actions.push('<a href="${ctx}/bureau/hsQwApplyBureau/form?id=' + row.id + '" class="hsBtnList" title="${text("编辑局直公房变更")}"><i class="fa fa-check"></i></a>&nbsp;');
                    actions.push('<a href="${ctx}/bureau/hsQwApplyBureau/form?id=' + row.id + '" class="hsBtnList" title="${text("编辑局直公房变更")}"><i class="fa fa-check"></i></a>&nbsp;');
                    // actions.push('<a href="${ctx}/apply/hsQwApply/delete?id='+row.id+'" class="btnList" title="${text("删除局直公房变更")}" data-confirm="${text("确认要删除该局直公房变更吗？")}">删除</a>&nbsp;');
                    //# }
                    return actions.join('');
                }
            }
        ],
        //# // 加载成功后执行事件
        ajaxSuccess: function (data) {

        }
    });
</script>