<% layout('/layouts/default.html', {title: '局直公房历史承租信息管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('局直公房历史承租信息管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwApplyBureau}" action="${ctx}/bureau/hsQwApplyBureau/listBureauData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<#form:hidden path="houseId" />

			<div class="form-group">
					<label class="control-label">${text('申请人姓名')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.name" maxlength="100" class="form-control width-120"/>
					</div>
				</div>

				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.organization" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('身份证号')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('手机号')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("申请人姓名")}', name:'mainApplyer.name',  sortable:false, width:150, align:"left", frozen:true},
		{header:'${text("参加工作时间")}', name:'mainApplyer.workTime',  sortable:false, width:150, align:"center"},
		{header:'${text("工作单位")}', name:'mainApplyer.organization',  sortable:false, width:150, align:"left"},
		{header:'${text("身份证号")}', name:'mainApplyer.idNum',  sortable:false, width:150, align:"left"},
		{header:'${text("手机号")}', name:'mainApplyer.phone',  sortable:false, width:150, align:"left"},
		{header:'${text("开始承租时间")}', name:'startDate',  sortable:false, width:150, align:"left"},
		{header:'${text("结束承租时间")}', name:'endDate.phone',  sortable:false, width:150, align:"left"},
		{header:'${text("承租楼盘")}', name:'house.estate.name',  sortable:false, width:150, align:"left"},
		{header:'${text("承租地址")}', name:'house.estate.address',  sortable:false, width:150, align:"left"},
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});

</script>