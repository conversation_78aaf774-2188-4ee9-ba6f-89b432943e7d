<% layout('/layouts/default.html', {title: '局直管公房申请表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="nav-tabs-custom nav-main">
		<ul class="nav nav-tabs">
			<% if ( first ){ %>
			<li  class="active" >
				<% } else {%>
			<li>
				<% } %>
				<a href="${ctx}/bureau/hsQwApplyBureau/listAudit"><i class="fa icon-energy"></i> ${text('审批待办')}</a></li>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('bureau:hsQwApplyBureau:add')){ %>
					<a href="${ctx}/bureau/hsQwApplyBureau/form" class="btn btn-default btnTool" title="${text('新增租赁配房')}"><i class="fa fa-plus"></i> ${text('新增配房')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
			<% if ( !first ){ %>
			<li  class="active" >
				<% } else {%>
			<li>
				<% } %>
				<a href="${ctx}/bureau/hsQwApplyBureau/listAuditDone"><i class="fa icon-book-open"></i> ${text('已办信息')}</a></li>
		</ul>
		<div class="box-body">
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwApplyBureau}" action="${ctx}${listDataUrl}" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('房源选择')}：</label>
							<div class="control-inline width-120">
								<#form:listselect id="publicHouse" title="房源选择选择"
								path="houseId" labelPath="11" class="required"
								url="${ctx}/house/hsQwPublicRentalHouse/houseSelectByType?dataType=5" allowClear="true"
								checkbox="false" itemCode="id" itemName="simpleInfo"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('承租人')}：</label>
							<div class="control-inline width-120">
								<#form:input path="mainApplyer.name" class="form-control"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('状态')}：</label>
							<div class="control-inline width-120">
								<#form:select path="status" dictType="sys_status" blankOption="true" class="form-control isQuick"/>
							</div>
						</div>
					</div>
					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false, // 禁用自动调整列宽
    autowidth: false,  // 禁用自动宽度
    scroll: true,      // 启用滚动条
    scrollOffset: 18,
    width: '100%',     // 表格宽度
    height: 'auto',    // 表格高度自适应
    columnModel: [
        {header:'${text("申请单编号")}', name:'id', width:80, align:"left", frozen:true, formatter: function(val, obj, row, act){
            return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑局直公房变更")}">' + (val || row.id) + '</a>';
        }},
        {header:'${text("申请单内容")}', name:'applyTitle', width:130, align:"left", formatter: function(val, obj, row, act){
            return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑局直公房变更")}">' + (val || row.id) + '</a>';
        }},
        {header:'${text("房源信息")}', name:'house.simpleInfo', width:150, align:"left"},
        {header:'${text("主申请人身份证")}', name:'mainApplyer.idNum', width:150, align:"left"},
        {header:'${text("主申请人手机号")}', name:'mainApplyer.phone', width:150, align:"left"},
        {header:'${text("流程环节")}', name:'processName', width:150, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_bureau_status')}", val, '${text("未知")}', true);
        }},
        {header:'${text("备注信息")}', name:'remarks', width:150, align:"left"},
        {header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
            var actions = [];
            //# if(hasPermi('apply:hsQwApply:edit')){
            actions.push('<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑局直公房变更")}"><i class="fa fa-check"></i></a>&nbsp;');
            // actions.push('<a href="${ctx}/apply/hsQwApply/delete?id='+row.id+'" class="btnList" title="${text("删除局直公房变更")}" data-confirm="${text("确认要删除该局直公房变更吗？")}">删除</a>&nbsp;');
            //# }
            if (row.status != Global.STATUS_DRAFT){
                actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=rent_apply_bureau&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
            }
            return actions.join('');
        }}
    ],
    loadComplete: function() {
        requestAnimationFrame(function() {
            CommonTable.setupFixedColumns('dataGrid');
        });
        js.closeLoading(0, true);
    }
}).on('click', '.bpmButton', function(){
	var $this = $(this).addClass('hsBtnList');
	js.ajaxSubmit($this.attr('href'), function(data){
		if (data.result == Global.TRUE){
			// 检查 pcUrl 是否可访问
			$.ajax({
				url: data.pcUrl,
				type: 'get',
				success: function(response) {
					// 如果请求成功，使用 js.addTabPage 打开新标签页
					js.addTabPage($this, $this.attr('title'), data.pcUrl);
				},
				error: function(xhr, status, error) {
					// 如果请求失败，显示错误信息
					var errorMsg = '${text("加载失败")}';
					if (xhr.responseJSON && xhr.responseJSON.message) {
						errorMsg = xhr.responseJSON.message;
					}
					js.showErrorMessage(errorMsg);
				}
			});
		} else {
			js.showErrorMessage(data.message);
		}
	});
	return false;
});
</script>