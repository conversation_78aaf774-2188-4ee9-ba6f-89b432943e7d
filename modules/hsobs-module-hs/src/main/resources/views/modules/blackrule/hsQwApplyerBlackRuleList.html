<% layout('/layouts/default.html', {title: '租赁资格轮候租户黑名单规则表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租赁资格轮候租户黑名单规则表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('blackrule:hsQwApplyerBlackRule:edit')){ %>
					<a href="${ctx}/blackrule/hsQwApplyerBlackRule/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候租户黑名单规则表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwApplyerBlackRule}" action="${ctx}/blackrule/hsQwApplyerBlackRule/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('规则名称')}：</label>
							<div class="control-inline">
								<#form:input path="name" maxlength="100" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('规则类型')}：</label>
							<div class="control-inline">
								<#form:select path="ruleType" dictType="hs_apply_black_type" blankOption="true" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('黑名单期限')}：</label>
							<div class="control-inline">
								<#form:input path="timeNum" class="form-control width-120"/>
							</div>
						</div>
					</div>

					<!-- 第二行：2个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('备注信息')}：</label>
							<div class="control-inline">
								<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("规则名称")}', name:'name',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return '<a href="${ctx}/blackrule/hsQwApplyerBlackRule/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候租户黑名单规则表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("规则类型")}', name:'ruleType',  sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_black_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("黑名单期限")}', name:'timeNum',  sortable:false, width:120, align:"left"},
		{header:'${text("状态")}', name:'status',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate',  sortable:false, width:150, align:"left"},
		{header:'${text("备注信息")}', name:'remarks',  sortable:false, width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('blackrule:hsQwApplyerBlackRule:edit')){
				actions.push('<a href="${ctx}/blackrule/hsQwApplyerBlackRule/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁资格轮候租户黑名单规则表")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/blackrule/hsQwApplyerBlackRule/delete?id='+row.id+'" class="btnList" title="${text("删除租赁资格轮候租户黑名单规则表")}" data-confirm="${text("确认要删除该黑名单规则吗？")}">删除</a>&nbsp;');

			//# }
			return actions.join('');
		}}
	],
	ajaxSuccess: function() {
		            requestAnimationFrame(function() {
                CommonTable.setupFixedColumns('dataGrid');
            });
	}
});
</script>

