<% layout('/layouts/default.html', {title: '租赁资格轮候租户黑名单规则表管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwApplyerBlackRule.isNewRecord ? '新增租赁资格轮候租户黑名单规则表' : '编辑租赁资格轮候租户黑名单规则表')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwApplyerBlackRule}" action="${ctx}/blackrule/hsQwApplyerBlackRule/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-12">
						<div class="hs-table-div">
							<table class="table-form hs-table-form">
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('规则名称')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="name" maxlength="100" class="form-control required"/>
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('规则类型')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:select path="ruleType" dictType="hs_apply_black_type" class="form-control required" />
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('黑名单期限')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="timeNum" class="form-control digits"/>
									</td>
									<td class="form-label hs-form-label"></td>
									<td></td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('blackrule:hsQwApplyerBlackRule:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>