<% layout('/layouts/default.html', {title: '租赁公告管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租赁公告管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('notice:hsQwNotice:edit')){ %>
					<a href="${ctx}/notice/hsQwNotice/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候公告')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwNotice}" action="${ctx}/notice/hsQwNotice/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('公告主题')}：</label>
							<div class="control-inline">
								<#form:input path="noticeTitle" maxlength="100" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('发布时间')}：</label>
							<div class="control-inline">
								<#form:input path="publicDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="publicDate_lte.click()"/>
								&nbsp;-&nbsp;
								<#form:input path="publicDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('发布单位')}：</label>
							<div class="control-inline">
								<#form:treeselect id="publicOrg" title="${text('机构选择')}"
									path="publicOrg" labelPath=""
									url="${ctx}/sys/office/treeData" allowClear="true" class="width-120"/>
							</div>
						</div>
					</div>

					<!-- 第二行：1个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('公告状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="hs_notice_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("公告主题")}', name:'noticeTitle', index:'a.notice_title', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return '<a href="${ctx}/notice/hsQwNotice/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候公告")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("公共内容")}', name:'noticeContent', index:'a.notice_content', sortable:false, width:150, align:"left"},
		{header:'${text("发布时间")}', name:'publicDate', index:'a.public_date', sortable:false, width:150, align:"left"},
		{header:'${text("发布单位")}', name:'office.officeName', index:'a.public_org', sortable:false, width:150, align:"left"},
		{header:'${text("公告状态")}', name:'status', index:'a.status', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_notice_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', sortable:false, width:150, align:"left"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', sortable:false, width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
			var actions = [];
			actions.push('<a href="${ctx}/notice/hsQwNotice/form?id='+row.id+'" class="hsBtnList" title="${text("编辑公告")}">编辑</a>&nbsp;');
			if(row.status=='0'){
				actions.push('<a href="${ctx}/notice/hsQwNotice/reback?id='+row.id+'" class="hsBtnList" title="${text("撤回公告")}" data-confirm="${text("确认要撤回该公告吗？")}"><i class="fa fa-reply"></i></a>&nbsp;');
			} else if(row.status=='2'){
				actions.push('<a href="${ctx}/notice/hsQwNotice/delete?id='+row.id+'" class="btnList" title="${text("删除公告")}" data-confirm="${text("确认要删除该公告吗？")}">删除</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	loadComplete: function() {
		// 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
		requestAnimationFrame(function() {
			CommonTable.setupFixedColumns('dataGrid');
		});
		js.closeLoading(0, true);
	}
});
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/notice/hsQwNotice/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>

<script>
	// 初始化公共表格功能
	CommonTable.init('dataGrid');
</script>