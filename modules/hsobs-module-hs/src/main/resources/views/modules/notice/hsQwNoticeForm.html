<% layout('/layouts/default.html', {title: '租赁公告管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwNotice.isNewRecord ? '新增租赁公告' : '编辑租赁公告')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwNotice}" action="${ctx}/notice/hsQwNotice/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<#form:hidden path="status" defaultValue="0"/>
				<div class="row">
					<div class="col-xs-12">
						<div class="hs-table-div">
							<table class="table-form hs-table-form">
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('公告主题')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="noticeTitle" maxlength="100" class="form-control required"/>
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('发布时间')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="publicDate" readonly="true" maxlength="20" class="form-control laydate required"
											dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('发布单位')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:treeselect id="publicOrg" title="${text('机构选择')}"
											path="publicOrg" labelPath="office.officeName"
											url="${ctx}/sys/office/treeData"
											class=" required" allowClear="true"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('公告内容')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:textarea path="noticeContent" rows="4" maxlength="500" class="form-control"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('notice:hsQwNotice:edit')
							&& hsQwNotice.status!'2'=='2'){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('提 交')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>