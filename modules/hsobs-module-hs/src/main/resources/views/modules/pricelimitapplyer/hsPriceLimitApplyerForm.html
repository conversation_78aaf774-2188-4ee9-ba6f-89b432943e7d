<% layout('/layouts/default.html', {title: '限价房-购房申请人管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsPriceLimitApplyer.isNewRecord ? '新增限价房-购房申请人' : '编辑限价房-购房申请人')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsPriceLimitApplyer}" action="${ctx}/pricelimitapplyer/hsPriceLimitApplyer/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('用户编号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="userId" maxlength="64" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('申请人角色')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applyRole" maxlength="1" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('申请表id')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applyId" maxlength="64" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请人姓名')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="64" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('工作单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="organization" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('身份证号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="idNum" maxlength="20" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('联系电话')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="phone" maxlength="32" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('户籍')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="census" maxlength="64" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('婚姻状况')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="maritalStatus" maxlength="3" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('年收入')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="annualIncome" class="form-control required digits"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('住房')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="area" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required hide">*</span> ${text('图片上传')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadImage" bizKey="${hsPriceLimitApplyer.id}" bizType="hsPriceLimitApplyer_image"
									uploadType="image" class="" readonly="false" preview="true" dataMap="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required hide">*</span> ${text('附件上传')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${hsPriceLimitApplyer.id}" bizType="hsPriceLimitApplyer_file"
									uploadType="all" class="" readonly="false" preview="true" dataMap="true"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('pricelimitapplyer:hsPriceLimitApplyer:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>