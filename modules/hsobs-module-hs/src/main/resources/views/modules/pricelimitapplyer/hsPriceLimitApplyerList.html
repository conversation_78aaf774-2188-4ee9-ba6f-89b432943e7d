<% layout('/layouts/default.html', {title: '限价房-购房申请人管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('限价房-购房申请人管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('pricelimitapplyer:hsPriceLimitApplyer:edit')){ %>
					<a href="${ctx}/pricelimitapplyer/hsPriceLimitApplyer/form" class="btn btn-default btnTool" title="${text('新增限价房-购房申请人')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsPriceLimitApplyer}" action="${ctx}/pricelimitapplyer/hsPriceLimitApplyer/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('用户编号')}：</label>
					<div class="control-inline">
						<#form:input path="userId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人角色')}：</label>
					<div class="control-inline">
						<#form:input path="applyRole" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请表id')}：</label>
					<div class="control-inline">
						<#form:input path="applyId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人姓名')}：</label>
					<div class="control-inline">
						<#form:input path="name" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline">
						<#form:input path="organization" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('身份证号')}：</label>
					<div class="control-inline">
						<#form:input path="idNum" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('联系电话')}：</label>
					<div class="control-inline">
						<#form:input path="phone" maxlength="32" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('户籍')}：</label>
					<div class="control-inline">
						<#form:input path="census" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('婚姻状况')}：</label>
					<div class="control-inline">
						<#form:input path="maritalStatus" maxlength="3" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('年收入')}：</label>
					<div class="control-inline">
						<#form:input path="annualIncome" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('住房')}：</label>
					<div class="control-inline">
						<#form:input path="area" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('审核状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注信息')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("用户编号")}', name:'userId', index:'a.user_id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/pricelimitapplyer/hsPriceLimitApplyer/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑限价房-购房申请人")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请人角色")}', name:'applyRole', index:'a.apply_role', width:150, align:"left"},
		{header:'${text("申请表id")}', name:'applyId', index:'a.apply_id', width:150, align:"left"},
		{header:'${text("申请人姓名")}', name:'name', index:'a.name', width:150, align:"left"},
		{header:'${text("工作单位")}', name:'organization', index:'a.organization', width:150, align:"left"},
		{header:'${text("身份证号")}', name:'idNum', index:'a.id_num', width:150, align:"left"},
		{header:'${text("联系电话")}', name:'phone', index:'a.phone', width:150, align:"left"},
		{header:'${text("户籍")}', name:'census', index:'a.census', width:150, align:"left"},
		{header:'${text("婚姻状况")}', name:'maritalStatus', index:'a.marital_status', width:150, align:"left"},
		{header:'${text("年收入")}', name:'annualIncome', index:'a.annual_income', width:150, align:"left"},
		{header:'${text("住房")}', name:'area', index:'a.area', width:150, align:"left"},
		{header:'${text("审核状态")}', name:'status', index:'a.status', width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('pricelimitapplyer:hsPriceLimitApplyer:edit')){
				actions.push('<a href="${ctx}/pricelimitapplyer/hsPriceLimitApplyer/form?id='+row.id+'" class="hsBtnList" title="${text("编辑限价房-购房申请人")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/pricelimitapplyer/hsPriceLimitApplyer/disable?id='+row.id+'" class="btnList" title="${text("停用限价房-购房申请人")}" data-confirm="${text("确认要停用该限价房-购房申请人吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/pricelimitapplyer/hsPriceLimitApplyer/enable?id='+row.id+'" class="btnList" title="${text("启用限价房-购房申请人")}" data-confirm="${text("确认要启用该限价房-购房申请人吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/pricelimitapplyer/hsPriceLimitApplyer/delete?id='+row.id+'" class="btnList" title="${text("删除限价房-购房申请人")}" data-confirm="${text("确认要删除该限价房-购房申请人吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>