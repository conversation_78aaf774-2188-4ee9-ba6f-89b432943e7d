<% layout('/layouts/default.html', {title: '租赁资格轮候申请人管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租户信息列表')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwApplyer}" action="${ctx}/applyer/hsQwApplyer/listData" method="post" class="form-inline hide">
					<!-- 第一行条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('申请人姓名')}：</label>
							<div class="control-inline">
								<#form:input path="name" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('身份证号')}：</label>
							<div class="control-inline">
								<#form:input path="idNum" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('手机号')}：</label>
							<div class="control-inline">
								<#form:input path="phone" class="form-control width-120"/>
							</div>
						</div>
					</div>
					
					<!-- 第二行条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('参加工作时间')}：</label>
							<div class="control-inline">
								<#form:input path="workTime_gte" class="form-control laydate width-datetime"/>
								&nbsp;-&nbsp;
								<#form:input path="workTime_lte" class="form-control laydate width-datetime"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('工作单位')}：</label>
							<div class="control-inline">
								<#form:input path="organization" class="form-control width-120"/>
							</div>
						</div>
					</div>
					
					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm">查询</button>
						<button type="reset" class="btn btn-default btn-sm">重置</button>
					</div>
				</#form:form>
			</div>
			
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false,
	autowidth: false,
	scrollOffset: 18,
	width: '100%',
	height: 'auto',
	columnModel: [
		{header:'${text("申请人姓名")}', name:'name',  sortable:false, width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/applyer/hsQwApplyer/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候申请人")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("参加工作时间")}', name:'workTime',  sortable:false, width:150, align:"center"},
		{header:'${text("工作单位")}', name:'organization',  sortable:false, width:150, align:"left"},
		{header:'${text("身份证号")}', name:'idNum',  sortable:false, width:150, align:"left"},
		{header:'${text("手机号")}', name:'phone',  sortable:false, width:150, align:"left"},
		{header:'${text("性别")}', name:'sexName',  sortable:false, width:150, align:"left"},
		{header:'${text("婚姻状况")}', name:'marryStatus',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_marital_status')}", val, '未知', true);
			}},
		{header:'${text("承租楼盘")}', name:'hsQwApply.hsQwApplyHouse.estate.name',  sortable:false, width:150, align:"left"},
		{header:'${text("承租地址")}', name:'hsQwApply.hsQwApplyHouse.estate.address',  sortable:false, width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('applyer:hsQwApplyer:edit')){
				actions.push('<a href="${ctx}/applyer/hsQwApplyer/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁资格轮候申请人")}">编辑</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function() {
	    requestAnimationFrame(function() {
	        CommonTable.setupFixedColumns('dataGrid');
	    });
	    // js.closeLoading(0, true);
	}
});

$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/applyer/hsQwApplyer/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>