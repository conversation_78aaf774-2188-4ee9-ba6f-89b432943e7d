<% layout('/layouts/default.html', {title: '租赁资格轮候申请人管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwApplyer.isNewRecord ? '新增租赁资格轮候申请人' : '编辑租赁资格轮候申请人')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwApplyer}" action="${ctx}/applyer/hsQwApplyer/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('人员编号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="userId" maxlength="64" readonly="true" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请人姓名')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="name" maxlength="100" readonly="true" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('参加工作时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="workTime" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" readonly="true" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('工作单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="organization" maxlength="255" readonly="true" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('身份证号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="idNum" maxlength="20" readonly="true" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('手机号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="phone" maxlength="20" readonly="true" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('职务')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="workPosition" maxlength="255" readonly="true" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('职位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="workTitle" maxlength="20" readonly="true" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('年收入')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="annualIncome" maxlength="20" readonly="true" class="form-control required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" readonly="true" maxlength="500" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('租房信息')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('房号信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<a href="${ctx}/house/hsQwPublicRentalHouse/form?id=${hsQwApplyer.hsQwApply.hsQwApplyHouse.id}" class="hsBtnList" data-title="${text("查看租赁公租房房源房源信息表")}">
								${hsQwApplyer.hsQwApply.hsQwApplyHouse.estate.name}${hsQwApplyer.hsQwApply.hsQwApplyHouse.unitNum}
								</a>
							</div>
						</div>
					</div>
				</div>
			</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>