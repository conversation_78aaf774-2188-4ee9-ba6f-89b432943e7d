<% layout('/layouts/default.html', {title: '省自管公房申请表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('省自管公房申请表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('province:hsQwApplyProvince:edit')){ %>
					<a href="${ctx}/province/hsQwApplyProvince/form" class="btn btn-default btnTool" title="${text('新增省自管公房申请表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwApplyProvince}" action="${ctx}/province/hsQwApplyProvince/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('房源编号')}：</label>
							<div class="control-inline">
								<#form:treeselect id="houseId" title="${text('机构选择')}"
									path="houseId" labelPath=""
									url="${ctx}/sys/company/treeData" allowClear="true" class="width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="sys_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('备注信息')}：</label>
							<div class="control-inline">
								<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
							</div>
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("房源编号")}', name:'houseId', index:'a.house_id', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return '<a href="${ctx}/province/hsQwApplyProvince/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑省自管公房申请表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("房源信息")}', name:'house.simpleInfo', index:'h.id', sortable:false, width:150, align:"left"},
		{header:'${text("主申请人身份证")}', name:'mainApplyer.idNum', index:'ha.id_num', sortable:false, width:150, align:"left"},
		{header:'${text("主申请人手机号")}', name:'mainApplyer.phone', index:'ha.phone', sortable:false, width:120, align:"left"},
		{header:'${text("申请状态")}', name:'status', index:'a.status', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', sortable:false, width:150, align:"left"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', sortable:false, width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('province:hsQwApplyProvince:edit')){
				actions.push('<a href="${ctx}/province/hsQwApplyProvince/delete?id='+row.id+'" class="btnList" title="${text("删除省自管公房申请表")}" data-confirm="${text("确认要删除该省自管公房申请表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	ajaxSuccess: function() {
		            requestAnimationFrame(function() {
                CommonTable.setupFixedColumns('dataGrid');
            });
		// js.closeLoading(0, true);
	}
});
</script>

