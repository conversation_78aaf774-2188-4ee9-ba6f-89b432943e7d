<% layout('/layouts/default.html', {title: '省直自管公房申请表管理', libs: ['validate','dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa icon-note"></i> ${text(hsQwApplyProvince.isNewRecord ? '新增省直自管公房申请表' : '查看省直自管公房申请表')}
            </div>
            <div class="box-tools pull-right hide">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
        </div>
        <#form:form id="inputForm" model="${hsQwApplyProvince}" action="${ctx}/province/hsQwApplyProvince/save" method="post" class="form-horizontal">
        <div class="box-body hs-box-body-bpm">
            <div class="form-unit">${text('基本信息')}</div>
            <#form:hidden path="id"/>
            <div class="hs-table-div">
                <table class="table-form hs-table-form">
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('房源选择')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td colspan="3">
                            <#form:listselect id="houseId" title="房源选择选择"
                            path="houseId" labelPath="house.simpleInfo"
                            url="${ctx}/house/hsQwPublicRentalHouse/houseSelectByType?dataType=8" allowClear="false"
                            checkbox="false" itemCode="id" itemName="simpleInfo"/>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="form-unit">${text('省直自管公房申请人')}</div>
            <div class="form-unit-wrap table-form">
                <table id="hsQwApplyerDataGrid"></table>
                <% if (hasPermi('apply:hsQwApply:edit')){ %>
                <a href="#" id="hsQwApplyerDataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> ${text('增行')}</a>
                <% } %>
            </div>

            <div class="hs-table-div">
                <table class="table-form hs-table-form">
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('住房情况')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td colspan="3">
                            <#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
                        </td>
                    </tr>
                </table>
            </div>
            <!--			<#bpm:nextTaskInfo bpmEntity="${hsQwApplyProvince}" />-->
        </div>
        <div class="box-footer">
            <div class="row">
                <div class="col-sm-offset-2 col-sm-10">
                    <% if (hasPermi('province:hsQwApplyProvince:edit') && hsQwApplyProvince.isNewRecord){ %>
                    <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
                    <% } %>
                    <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
                </div>
            </div>
        </div>
    </#form:form>
</div>
</div>
<% } %>
<script>
    //# // 初始化省直自管公房申请申请人DataGrid对象
    $('#hsQwApplyerDataGrid').dataGrid({

        data: "#{toJson(hsQwApplyProvince.hsQwApplyerList)}",
        datatype: 'local', // 设置本地数据
        autoGridHeight: function(){return 'auto'}, // 设置自动高度

        //# // 设置数据表格列
        columnModel: [
            {header:'状态', name:'status', editable:true, hidden:true},
            {header:'主键', name:'id', editable:true, hidden:true},
            {header:'${text("申请人姓名")}', name:'name', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required realName'}},
            {header:'${text("工作单位")}', name:'organization', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
            {header:'${text("身份证号")}', name:'idNum', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required idcard'}},
            {header:'${text("职务")}', name:'workPosition', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
            {header:'${text("婚姻状况")}', name:'marryStatus', width:100,editable:true, edittype:'select', editoptions:{'class':'form-control',
                    items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('hs_applyer_marry')}"),
                    itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
                        js.select2(element).on("change",function(){
                            $(this).resetValid()});
                    }
                }},
            {header:'${text("死亡时间")}', name:'deathDate', width:150,formatter:'date', formatoptions:{srcformat:'Y-m',newformat:'Y-m'},
                editable:true, edittype:'text', editoptions:{'class':'form-control laydate', 'readonly':'true',
                    dataInit: function(element){
                        laydate.render({elem:element, type:'datetime', format:'yyyy-MM-dd'});
                    }
                }
            },
            {header:'${text("人员类型")}', name:'userType', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control'}},
            {header:'${text("申请人角色")}', name:'applyRole', width:100,
                editable:true, edittype:'select', editoptions:{'class':'form-control',
                    items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('hs_applyer_role_bureau')}"),
                    itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
                        js.select2(element).on("change",function(){
                            $(this).resetValid()});
                    }
                }
            },
            {header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
                    var actions = [];
                    if (val == 'new'){
                        actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyerDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
                    }else{
                        actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyerDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');});return false;">删除</a>&nbsp;');
                    }
                    return actions.join('');
                }, editoptions: {defaultValue: 'new'}}
        ],

        //# // 编辑表格参数
        editGrid: true,				// 是否是编辑表格
        editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
        editGridAddRowBtn: $('#hsQwApplyerDataGridAddRowBtn'),	// 子表增行按钮
        editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
        editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL , applyRole: '0'},	// 新增行的时候初始化的数据

        //# // 编辑表格的提交数据参数
        editGridInputFormListName: 'hsQwApplyerList', // 提交的数据列表名
        editGridInputFormListAttrs: 'status,id,name,organization,idNum,workPosition,applyRole,', // 提交数据列表的属性字段

        //# // 加载成功后执行事件
        ajaxSuccess: function(data){

        }
    });
</script>
<script>
    // 业务实现草稿按钮
    $('#btnDraft').click(function(){
        $('#status').val(Global.STATUS_DRAFT);
    });
    // 流程按钮操作事件
    BpmButton = window.BpmButton || {};
    BpmButton.init = function(task){
        if (task.status != '2') {
            $('.taskComment').removeClass('hide');
        }
    }
    BpmButton.complete = function($this, task){
        $('#status').val(Global.STATUS_AUDIT);
    };
    // 表单验证提交事件
    $('#inputForm').validate({
        submitHandler: function(form){
            js.ajaxSubmitForm($(form), function(data){
                js.showMessage(data.message);
                if(data.result == Global.TRUE){
                    js.closeCurrentTabPage(function(contentWindow){
                        contentWindow.page();
                    });
                }
            }, "json");
        }
    });
</script>