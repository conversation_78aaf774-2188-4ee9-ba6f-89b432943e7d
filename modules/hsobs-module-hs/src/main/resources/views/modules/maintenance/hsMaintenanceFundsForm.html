<% layout('/layouts/default.html', {title: '维修资金', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsMaintenanceFunds.isNewRecord ? '新增维修资金' : '编辑维修资金')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsMaintenanceFunds}" action="${ctx}/maintenance/hsMaintenanceFunds/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">
								<span class="required ">*</span>${text('房屋信息')}：</label>
							<div class="col-sm-8">
								<#form:select path="houseId" items="${@com.hsobs.hs.modules.estate.util.EstateUtils.getEstateList()}" itemLabel="name" itemValue="id" blankOption="true" class="form-control required" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('维修资金总额')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="inputFunds" disabled="" dataFormat="number" class="form-control required number"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('帐户名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="fundName" maxlength="120" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('变动金额')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="changeFund" dataFormat="number" class="form-control required number"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('资金来源简述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remark" rows="5" maxlength="900" class="form-control"/>
							</div>
						</div>
					</div>
<!--					<div class="col-xs-6">-->
<!--						<div class="form-group">-->
<!--							<label class="control-label col-sm-4" title="">-->
<!--								<span class="required hide">*</span> ${text('录入年份')}：<i class="fa icon-question hide"></i></label>-->
<!--							<div class="col-sm-8">-->
<!--								<#form:input path="inputYear" maxlength="4" class="form-control"/>-->
<!--							</div>-->
<!--						</div>-->
<!--					</div>-->
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('maintenance:hsMaintenanceFunds:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>