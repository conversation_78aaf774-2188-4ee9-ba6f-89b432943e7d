<% layout('/layouts/default.html', {title: '维修资金', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-body">
			<#form:form id="searchForm" model="${hsMaintenanceFunds}" action="${ctx}/maintenance/hsMaintenanceFunds/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('主识别ID')}：</label>
					<div class="control-inline">
						<#form:input path="id" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('房屋信息')}：</label>
					<div class="control-inline">
						<#form:input path="houseId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修资金总额')}：</label>
					<div class="control-inline">
						<#form:input path="inputFunds" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('已使用资金额度')}：</label>
					<div class="control-inline">
						<#form:input path="usedFunds" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请中资金额度')}：</label>
					<div class="control-inline">
						<#form:input path="applyFunds" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('资金状态-预留')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('资金来源简述')}：</label>
					<div class="control-inline">
						<#form:input path="remark" maxlength="900" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('录入年份')}：</label>
					<div class="control-inline">
						<#form:input path="inputYear" maxlength="4" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('录入月份')}：</label>
					<div class="control-inline">
						<#form:input path="inputMonth" maxlength="4" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('是否有效;1-有效 0-无效')}：</label>
					<div class="control-inline">
						<#form:input path="validTag" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="row">
				<div class="col-xs-10 pr10">
					<table id="dataGrid"></table>
					<div id="dataGridPage"></div>
				</div>
				<div class="col-xs-2 pl0">
					<div id="selectData" class="tags-input"></div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("主识别ID")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return (val||row.id);
		}},
		{header:'${text("房屋信息")}', name:'houseId', index:'a.house_id', width:150, align:"left"},
		{header:'${text("维修资金总额")}', name:'inputFunds', index:'a.input_funds', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("已使用资金额度")}', name:'usedFunds', index:'a.used_funds', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("申请中资金额度")}', name:'applyFunds', index:'a.apply_funds', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("资金状态-预留")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('')}", val, '${text("未知")}', true);
		}},
		{header:'${text("资金来源简述")}', name:'remark', index:'a.remark', width:150, align:"left"},
		{header:'${text("录入年份")}', name:'inputYear', index:'a.input_year', width:150, align:"left"},
		{header:'${text("录入月份")}', name:'inputMonth', index:'a.input_month', width:150, align:"left"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("是否有效;1-有效 0-无效")}', name:'validTag', index:'a.valid_tag', width:150, align:"left"},
		{header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
			return JSON.stringify(row);
		}}
	],
	autoGridHeight: function(){
		var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
		$('.tags-input').height($('.ui-jqgrid').height() - 10);
		return height;
	},
	showCheckbox: '${parameter.checkbox}' == 'true',
	multiboxonly: false, // 单击复选框时再多选
	ajaxSuccess: function(data){
		$.each(selectData, function(key, value){
			dataGrid.dataGrid('setSelectRow', key);
		});
		initSelectTag();
	},
	onSelectRow: function(id, isSelect, event){
		if ('${parameter.checkbox}' == 'true'){
			if(isSelect){
				selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
			}else{
				delete selectData[id];
			}
		}else{
			selectData = {};
			selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
		}
		initSelectTag();
	},
	onSelectAll: function(ids, isSelect){
		if ('${parameter.checkbox}' == 'true'){
			for (var i=0; i<ids.length; i++){
				if(isSelect){
					selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
				}else{
					delete selectData[ids[i]];
				}
			}
		}
		initSelectTag();
	},
	ondblClickRow: function(id, rownum, colnum, event){
		if ('${parameter.checkbox}' != 'true'){
			js.layer.$('#' + window.name).closest('.layui-layer')
				.find(".layui-layer-btn0").trigger("click");
		}
		initSelectTag();
	}
});
function initSelectTag(){
	selectNum = 0;
	var html = [];
	$.each(selectData, function(key, value){
		selectNum ++;
		html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.id||value.id)+' </span>'
			+ '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
	});
	html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
	$('#selectData').empty().append(html.join(''));
}
function removeSelectTag(key){
	delete selectData[key];
	dataGrid.dataGrid('resetSelection', key);
	$('#selectNum').html(--selectNum);
	$('#'+key+'_tags-input').remove();
}
function getSelectData(){
	return selectData;
}
</script>