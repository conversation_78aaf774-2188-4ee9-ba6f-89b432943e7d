<% layout('/layouts/default.html', {title: '维修资金变动记录', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('维修资金变动记录')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsMaintenanceFundsHis}" action="${ctx}/maintenance/hsMaintenanceFundsHis/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			    <#form:hidden path="fundsId" maxlength="64" class="form-control width-120"/>
<!--				<div class="form-group">-->
<!--					<label class="control-label">${text('录入年份')}：</label>-->
<!--					<div class="control-inline">-->
<!--						<#form:input path="inputYear" maxlength="4" class="form-control width-120"/>-->
<!--					</div>-->
<!--				</div>-->
<!--				<div class="form-group">-->
<!--					<label class="control-label">${text('录入月份')}：</label>-->
<!--					<div class="control-inline">-->
<!--						<#form:input path="inputMonth" maxlength="4" class="form-control width-120"/>-->
<!--					</div>-->
<!--				</div>-->
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("ID")}', name:'id', index:'a.id', width:150, align:"left", frozen:true},
		{header:'${text("变动前维修资金总额")}', name:'inputFunds', index:'a.input_funds', width:150, align:"center"},
		{header:'${text("变动资金额度")}', name:'changeFunds', index:'a.change_funds', width:150, align:"center"},
		{header:'${text("录入年份")}', name:'inputYear', index:'a.input_year', width:150, align:"left"},
		{header:'${text("录入月份")}', name:'inputMonth', index:'a.input_month', width:150, align:"left"},
		{header:'${text("录入人名称")}', name:'createByName', index:'a.create_by_name', width:150, align:"left"},
		{header:'${text("录入人账号")}', name:'createBy', index:'a.create_by', width:150, align:"left"},
		{header:'${text("录入时间")}', name:'createDate', index:'a.create_date', width:150, align:"center"},
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>