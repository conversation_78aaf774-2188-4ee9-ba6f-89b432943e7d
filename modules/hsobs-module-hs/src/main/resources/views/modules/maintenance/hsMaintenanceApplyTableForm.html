<% layout('/layouts/default.html', {title: '维修申请管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<% if(!hsMaintenanceApply.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsMaintenanceApply}" title="房屋维修资金申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsMaintenanceApply}" formKey="maintenance_apply" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsMaintenanceApply.isNewRecord ? '维修申请' : '维修申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsMaintenanceApply}" action="${ctx}/maintenance/hsMaintenanceApply/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基础信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('申请单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:treeselect id="unitId" title="${text('机构选择')}"
								path="unitId" labelPath="applyOffice.officeName"
								readonly="${isRead}"
								url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
								class="required" allowClear="true" canSelectRoot="true" canSelectParent="false"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span>${text('楼盘')}：
							</td>
							<td>
								<#form:select path="houseId" readonly="${isRead}"
								items="${@com.hsobs.hs.modules.estate.util.EstateUtils.getEstateList()}"
								itemLabel="name" itemValue="id" blankOption="true" class="form-control required" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('资金帐户')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:select id="fundIdSelect" path="fundId" readonly="${isRead}" items="${fundList}" itemLabel="fundName" itemValue="id" blankOption="true" blankOptionLabel="请选择" class="form-control required" />
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('房屋坐落')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="houseAddress" readonly="${isRead}" maxlength="30" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('联系人')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="contactName" readonly="${isRead}" maxlength="30" class="form-control required"/>
							</td>

							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('联系人电话')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="contactTel" readonly="${isRead}" maxlength="20" class="form-control required mobile" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('现存资金')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="existFund" readonly="${isRead}" class="form-control number" />
							</td>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('￥申请金额(元)')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="applyFund" readonly="${isRead}" min="0.01" class="form-control required number minValue"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('总套数')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="houseTotal" readonly="${isRead}" class="form-control digits" />
							</td>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('已售套数')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="houseSold" readonly="${isRead}" class="form-control digits" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('维修类别')}：<i class="fa icon-question hide"></i>
							</td>
							<td >
								<#form:input path="repairType" readonly="${isRead}" maxlength="32" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('竣工年份')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="completedTime" maxlength="20" class="form-control laydate" dataFormat="datetime" data-type="datetime" readonly="${isRead}" data-format="yyyy-MM-dd HH:mm" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('维修项目')}：<i class="fa icon-question hide"></i>
							</td>
							<td colspan="3">
								<#form:textarea path="applyReason" readonly="${isRead}" rows="5" maxlength="900" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required">*</span> ${text('证明材料')}：
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile" bizKey="${hsMaintenanceApply.id}"
								bizType="hsMaintenanceApply_file"
								uploadType="all" class="required" readonly="${isRead}" preview="true" dataMap="true"/>
							</td>
						</tr>

					</table>
				</div>

				<% if(!hsMaintenanceApply.isNewRecord){ %>

				<% if(hsMaintenanceApply.applyStatus >= 1){ %>
				<div class="form-unit">${text('现场勘测')}</div>
				<% } %>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
				<% if(hsMaintenanceApply.applyStatus >= 1){ %>
						<tr>
							<td class="form-label hs-form-label">
								<span id="surveyFlagDivSpan" class="required">*</span> ${text('是否需要勘测')}：<i class="fa icon-question hide"></i>
							</td>
							<td colspan="3">
								<#form:select path="surveyFlag" dictType="sys_yes_no" readonly="${hsMaintenanceApply.applyStatus == 1 ? false : true}" blankOption="true" blankOptionLabel="请选择"  class="form-control required" />
							</td>
						</tr>
				<% } %>

				<% if(hsMaintenanceApply.applyStatus >= 2 && hsMaintenanceApply.surveyFlag == '1'){ %>
						<tr>
							<td colspan="4" class="form-label hs-form-label" style="color: red;">
								<span  class="required">*</span> ${text('提示')}：请上传现场勘查的照片、勘查员的分析、结论及记录等材料。
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span id="surveyAnalysisSpan" class="required">*</span> ${text('勘测分析')}：
							</td>
							<td colspan="3">
								<#form:textarea path="surveyAnalysis" readonly="false" rows="3" maxlength="400" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span id="surveyConclusionSpan" class="required">*</span> ${text('勘测结论')}：
							</td>
							<td colspan="3">
								<#form:textarea path="surveyConclusion" readonly="false" rows="3" maxlength="400" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span id="investigationDivSpan" class="required">*</span> ${text('勘测材料')}：
							</td>
							<td colspan="3">
								<#form:fileupload id="investigationUploadFile" bizKey="${hsMaintenanceApply.id}" bizType="hsMaintenanceApply_investigationFile"
								uploadType="all" class="required" readonly="${hsMaintenanceApply.applyStatus > 2 ? true : false}" preview="true" dataMap="true"/>
							</td>
						</tr>
				<% } %>
					</table>
				</div>

				<% if(hsMaintenanceApply.applyStatus >= 7){ %>
				<div class="form-unit">${text('工程施工')}</div>
				<div id="engineerDiv" class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span id="engineerDivSpan" class="required">*</span> ${text('施工材料')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="engineerUploadFile" bizKey="${hsMaintenanceApply.id}" bizType="hsMaintenanceApply_engineerFile"
								uploadType="all" class="required" readonly="${hsMaintenanceApply.applyStatus > 8 ? true : false}" preview="true" dataMap="true"/>
							</div>
						</div>
					</div>
				</div>
				<% } %>

				<% if(hsMaintenanceApply.applyStatus >= 8){ %>
				<div class="form-unit">${text('核检意见')}</div>
				<div id="reviewDiv" class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span id="reviewDivSpan" class="required">*</span> ${text('核检意见')}：</label>
							<div class="col-sm-10">
								<#form:textarea path="reviewMsg" readonly="false" rows="5" maxlength="500" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span id="reviewFileDivSpan" class="required hide">*</span> ${text('核检材料')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="reviewUploadFile" bizKey="${hsMaintenanceApply.id}" bizType="hsMaintenanceApply_reviewFile"
								uploadType="all" class="" readonly="${hsMaintenanceApply.applyStatus > 8 ? true : false}" preview="true" dataMap="true"/>
							</div>
						</div>
					</div>
				</div>
				<% } %>

				<div id="fundFlipLabelDiv" class="form-unit">${text('资金拨付单信息')}</div>
				<div id="fundFlipDiv" class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('批文号数量')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="adnNum" readonly="false" maxlength="20" class="form-control digits required"/>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('资金信息')}</div>

				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('维修资金总额')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input  path="inputFunds" disabled="" maxlength="30" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('已使用金额')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="usedFunds" disabled="" maxlength="30" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('申请中金额')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applyFunds" disabled="" maxlength="30" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('可用金额')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="otherFunds" disabled="" maxlength="30" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
<!--				<#bpm:nextTaskInfo bpmEntity="${hsMaintenanceApply}" />-->

				<% } %>
			</div>
			<div class="box-footer hs-footer-block">
				<% if(!(hsMaintenanceApply.isNewRecord || hsMaintenanceApply.applyStatus == 0 || hsMaintenanceApply.applyStatus == 12)){ %>
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group" style="margin-bottom: 0;">
							<label class="control-label col-xs-2">审批意见：</label>
							<div class="col-xs-10">
								<#hobpm:comment bpmEntity="${hsMaintenanceApply}" rows="2" required="true" showCommWords="false" />
							</div>
						</div>
					</div>
				</div>
				<% } %>
				<div class="row">
					<div class="col-sm-offset-4 col-sm-8">
						<% if (hasPermi('maintenance:hsMaintenanceApply:edit')){ %>
							<#form:hidden path="status"/>
<!--							<% if (hsMaintenanceApply.isNewRecord || hsMaintenanceApply.status == '9'){ %>-->
<!--								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;-->
<!--							<% } %>-->
							<#bpm:button bpmEntity="${hsMaintenanceApply}" formKey="maintenance_apply" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>


// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.activityId === undefined) {
		$('#fundFlipLabelDiv').remove();
		$('#fundFlipDiv').remove();

		<% if (hsMaintenanceApply.applyStatus > 2) { %>
		$('#investigationDivSpan').hide();
		$('#investigationUploadFile').prop("readonly", true).removeClass("required");
		$('#surveyConclusionSpan').hide();
		$('#surveyConclusion').prop("readonly", true).removeClass("required");
		$('#surveyAnalysisSpan').hide();
		$('#surveyAnalysis').prop("readonly", true).removeClass("required");
		<% } %>

	    <% if (hsMaintenanceApply.applyStatus > 7) { %>
		$('#engineerDivSpan').hide();
		$('#engineerUploadFile').prop("readonly", true).removeClass("required");
		<% } %>
		<% if (hsMaintenanceApply.applyStatus > 8) { %>
		$('#reviewDivSpan').hide();
		$('#reviewFileDivSpan').hide();
		$('#reviewMsg').prop("readonly", true).removeClass("required");
		<% } %>

	} else {
		// 资金拨付单
		if (task.activityId != 'maintenanceApply0012') {
			$('#fundFlipLabelDiv').remove();
			$('#fundFlipDiv').remove();
		}
		// activityId 节点ID控制
		// maintenanceApply0002  控制 surveyFlag == 1 需要勘察  0 不需要勘察
		if (task.activityId != 'maintenanceApply0002') {
		}
		// 现场勘察材料
		if (task.activityId != 'maintenanceApply0004') {
			$('#investigationDivSpan').hide();
			$('#investigationUploadFile').prop("readonly", true).removeClass("required");
			$('#surveyConclusionSpan').hide();
			$('#surveyConclusion').prop("readonly", true).removeClass("required");
			$('#surveyAnalysisSpan').hide();
			$('#surveyAnalysis').prop("readonly", true).removeClass("required");
		}
		// 工程材料
		if (task.activityId != 'maintenanceApply0008') {
			$('#engineerDivSpan').hide();
			$('#engineerUploadFile').prop("readonly", true).removeClass("required");
		}
		// 核检
		if (task.activityId != 'maintenanceApply0010') {
			$('#reviewDivSpan').hide();
			$('#reviewMsg').prop("readonly", true).removeClass("required");
		}
	}

	if (task.status !== undefined && task.status != '2') {
		$('.taskComment').removeClass('hide');
	}

}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};

BpmButton.callback = function(){
	let idValue = $('#id').val();
	$.ajax({
		type: 'POST',
		url: "${ctx}/maintenance/hsMaintenanceApply/flushTaskStatus?___t=" + new Date().getTime(),
		data: {id: idValue},
		dataType: 'json',
		async: false,
		error: function(data){
			js.showErrorMessage(data.responseText);
		},
		success: function(data, status, xhr){
		}
	});
};
</script>

<script>
	$(function () {

		$('#houseId').on('change', function() {
			let selectedValue = $(this).val();
			flushHouseSelect(selectedValue);
			changeFundSelect(selectedValue);
		});

		function flushHouseSelect(houseId) {
			$.ajax({
				type: 'POST',
				url: "${ctx}/estate/hsQwPublicRentalEstate/listData?___t=" + new Date().getTime(),
				data: {id: houseId},
				dataType: 'json',
				async: false,
				error: function(data){
					js.showErrorMessage(data.responseText);
				},
				success: function(data, status, xhr){
					if (data != null && data.list.length > 0) {
						$('#houseTotal').val(data.list[0].households);
						if (data.list[0].year && data.list[0].year.length == 7) {
							$('#completedTime').val(data.list[0].year + '-01 00:00');
						}
						$('#houseAddress').val(data.list[0].address);
					}
				}
			});
		}

		function changeFundSelect(houseId) {
			$.ajax({
				type: 'POST',
				url: "${ctx}/maintenance/hsMaintenanceFunds/fullListData?___t=" + new Date().getTime(),
				data: {houseId: houseId},
				dataType: 'json',
				async: false,
				error: function(data){
					js.showErrorMessage(data.responseText);
				},
				success: function(data, status, xhr){
					console.log(data)
					$('#fundIdSelect').empty();
					if (data != null) {
						$('#fundIdSelect').select2({
							data: data.map(item => {
								return {id: item.id, text: item.fundName};
							})
						});
					} else {
						$('#fundIdSelect').select2({
							data: []
						});
					}
					$('#fundIdSelect').val('').trigger('change');
				}
			});
		}

		$('#fundIdSelect').on ('change' , function(e, data) {
			let selectedValue = $(this).val();
			$.ajax({
				type: 'POST',
				url: "${ctx}/maintenance/hsMaintenanceFunds/singleData?___t=" + new Date().getTime(),
				data: {id: selectedValue},
				dataType: 'json',
				async: false,
				error: function(data){
					js.showErrorMessage(data.responseText);
				},
				success: function(data, status, xhr){
					if (data != null) {
						if ($('#inputFunds').length > 0) {
							$('#inputFunds').val(data.inputFunds);
							$('#usedFunds').val(data.usedFunds);
							$('#applyFunds').val(data.applyFunds);
							$('#otherFunds').val(data.inputFunds - data.usedFunds);
						}
					}
				}
			});
		});

		<% if(!hsMaintenanceApply.isNewRecord){ %>
			$('#fundIdSelect').val($('#fundIdSelect').val()).change();
			<% } %>

        // 表单验证提交事件
		$('#inputForm').validate({
			submitHandler: function(form){
				js.ajaxSubmitForm($(form), function(data){
					js.showMessage(data.message);
					if(data.result == Global.TRUE){
						js.closeCurrentTabPage(function(contentWindow){
							contentWindow.page();
						});
					}
				}, "json");
			}
		});

	});
</script>
