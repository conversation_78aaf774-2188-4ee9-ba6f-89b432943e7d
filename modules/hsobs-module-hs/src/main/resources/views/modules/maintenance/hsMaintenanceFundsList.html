<% layout('/layouts/default.html', {title: '维修资金', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('维修资金')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('maintenance:hsMaintenanceFunds:edit')){ %>
					<a href="${ctx}/maintenance/hsMaintenanceFunds/form" class="btn btn-default btnTool" title="${text('新增维修资金信息表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsMaintenanceFunds}" action="${ctx}/maintenance/hsMaintenanceFunds/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('房屋信息')}：</label>
					<div class="control-inline width-120">
						<#form:select path="houseId" items="${@com.hsobs.hs.modules.estate.util.EstateUtils.getEstateList()}" itemLabel="name" itemValue="id" blankOption="true" class="form-control " />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('录入年份')}：</label>
					<div class="control-inline">
						<#form:input path="inputYear" maxlength="4" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>

$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
	    js.ajaxSubmitForm($('#searchForm'), {
	    	url: '${ctx}/maintenance/hsMaintenanceFunds/exportData',
	    	clearParams: 'pageNo,pageSize',
	    	downloadFile: true
	    });
	});
});

//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("ID")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/maintenance/hsMaintenanceFunds/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑维修资金")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("房屋信息")}', name:'publicRentalEstate.name', index:'a.public_rental_estate.name', width:150, align:"left"},
		{header:'${text("帐户名称")}', name:'fundName', index:'a.fund_name', width:150, align:"left"},
		{header:'${text("资金总额")}', name:'inputFunds', index:'a.input_funds', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("已使用额度")}', name:'usedFunds', index:'a.used_funds', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("申请中资金额度")}', name:'applyFunds', index:'a.apply_funds', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("资金来源简述")}', name:'remark', index:'a.remark', width:150, align:"left"},
		{header:'${text("录入时间")}', name:'createDate', index:'a.create_date', width:150, align:"left"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('maintenance:hsMaintenanceFunds:edit')){
				actions.push('<a href="${ctx}/maintenance/hsMaintenanceFunds/form?id='+row.id+'" class="hsBtnList" title="${text("编辑维修资金")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/maintenance/hsMaintenanceFundsHis/list?fundsId='+row.id+'" class="hsBtnList" title="${text("变更历史")}">历史</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/maintenance/hsMaintenanceFunds/disable?id='+row.id+'" class="btnList" title="${text("停用维修资金")}" data-confirm="${text("确认要停用该维修资金信息表吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/maintenance/hsMaintenanceFunds/enable?id='+row.id+'" class="btnList" title="${text("启用维修资金")}" data-confirm="${text("确认要启用该维修资金信息表吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/maintenance/hsMaintenanceFunds/delete?id='+row.id+'" class="btnList" title="${text("删除维修资金")}" data-confirm="${text("确认要删除该维修资金信息表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>