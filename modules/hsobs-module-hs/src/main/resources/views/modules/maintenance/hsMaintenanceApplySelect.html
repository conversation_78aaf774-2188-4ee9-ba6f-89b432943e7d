<% layout('/layouts/default.html', {title: '维修申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-body">
			<#form:form id="searchForm" model="${hsMaintenanceApply}" action="${ctx}/maintenance/hsMaintenanceApply/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('主识别ID')}：</label>
					<div class="control-inline">
						<#form:input path="id" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('房屋信息')}：</label>
					<div class="control-inline">
						<#form:input path="houseId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请单位')}：</label>
					<div class="control-inline">
						<#form:input path="unitId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('联系人')}：</label>
					<div class="control-inline">
						<#form:input path="contactName" maxlength="30" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('联系人电话')}：</label>
					<div class="control-inline">
						<#form:input path="contactTel" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('竣工年份')}：</label>
					<div class="control-inline">
						<#form:input path="completedTime" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('总套数')}：</label>
					<div class="control-inline">
						<#form:input path="houseTotal" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('已售套数')}：</label>
					<div class="control-inline">
						<#form:input path="houseSold" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('现存资金')}：</label>
					<div class="control-inline">
						<#form:input path="existFund" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请金额')}：</label>
					<div class="control-inline">
						<#form:input path="applyFund" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('拨付金额')}：</label>
					<div class="control-inline">
						<#form:input path="disbursementFund" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('维修类别')}：</label>
					<div class="control-inline">
						<#form:input path="repairType" maxlength="32" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请原由')}：</label>
					<div class="control-inline">
						<#form:input path="applyReason" maxlength="900" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('资金拨付单编号')}：</label>
					<div class="control-inline">
						<#form:input path="favNo" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('资金拨付单批文号起')}：</label>
					<div class="control-inline">
						<#form:input path="adnStart" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('资金拨付单批文号止')}：</label>
					<div class="control-inline">
						<#form:input path="adnEnd" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请状态')}：</label>
					<div class="control-inline">
						<#form:input path="applyStatus" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('是否有效;1-有效 0-无效')}：</label>
					<div class="control-inline">
						<#form:input path="validTag" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="row">
				<div class="col-xs-10 pr10">
					<table id="dataGrid"></table>
					<div id="dataGridPage"></div>
				</div>
				<div class="col-xs-2 pl0">
					<div id="selectData" class="tags-input"></div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("主识别ID")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return (val||row.id);
		}},
		{header:'${text("房屋信息")}', name:'houseId', index:'a.house_id', width:150, align:"left"},
		{header:'${text("申请单位")}', name:'unitId', index:'a.unit_id', width:150, align:"left"},
		{header:'${text("联系人")}', name:'contactName', index:'a.contact_name', width:150, align:"left"},
		{header:'${text("联系人电话")}', name:'contactTel', index:'a.contact_tel', width:150, align:"left"},
		{header:'${text("竣工年份")}', name:'completedTime', index:'a.completed_time', width:150, align:"center"},
		{header:'${text("总套数")}', name:'houseTotal', index:'a.house_total', width:150, align:"center"},
		{header:'${text("已售套数")}', name:'houseSold', index:'a.house_sold', width:150, align:"center"},
		{header:'${text("现存资金")}', name:'existFund', index:'a.exist_fund', width:150, align:"center"},
		{header:'${text("申请金额")}', name:'applyFund', index:'a.apply_fund', width:150, align:"center"},
		{header:'${text("拨付金额")}', name:'disbursementFund', index:'a.disbursement_fund', width:150, align:"center"},
		{header:'${text("维修类别")}', name:'repairType', index:'a.repair_type', width:150, align:"left"},
		{header:'${text("申请原由")}', name:'applyReason', index:'a.apply_reason', width:150, align:"left"},
		{header:'${text("资金拨付单编号")}', name:'favNo', index:'a.fav_no', width:150, align:"center"},
		{header:'${text("资金拨付单批文号起")}', name:'adnStart', index:'a.adn_start', width:150, align:"center"},
		{header:'${text("资金拨付单批文号止")}', name:'adnEnd', index:'a.adn_end', width:150, align:"center"},
		{header:'${text("申请状态")}', name:'applyStatus', index:'a.apply_status', width:150, align:"center"},
		{header:'${text("申请状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("是否有效;1-有效 0-无效")}', name:'validTag', index:'a.valid_tag', width:150, align:"left"},
		{header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
			return JSON.stringify(row);
		}}
	],
	autoGridHeight: function(){
		var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
		$('.tags-input').height($('.ui-jqgrid').height() - 10);
		return height;
	},
	showCheckbox: '${parameter.checkbox}' == 'true',
	multiboxonly: false, // 单击复选框时再多选
	ajaxSuccess: function(data){
		$.each(selectData, function(key, value){
			dataGrid.dataGrid('setSelectRow', key);
		});
		initSelectTag();
	},
	onSelectRow: function(id, isSelect, event){
		if ('${parameter.checkbox}' == 'true'){
			if(isSelect){
				selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
			}else{
				delete selectData[id];
			}
		}else{
			selectData = {};
			selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
		}
		initSelectTag();
	},
	onSelectAll: function(ids, isSelect){
		if ('${parameter.checkbox}' == 'true'){
			for (var i=0; i<ids.length; i++){
				if(isSelect){
					selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
				}else{
					delete selectData[ids[i]];
				}
			}
		}
		initSelectTag();
	},
	ondblClickRow: function(id, rownum, colnum, event){
		if ('${parameter.checkbox}' != 'true'){
			js.layer.$('#' + window.name).closest('.layui-layer')
				.find(".layui-layer-btn0").trigger("click");
		}
		initSelectTag();
	}
});
function initSelectTag(){
	selectNum = 0;
	var html = [];
	$.each(selectData, function(key, value){
		selectNum ++;
		html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.id||value.id)+' </span>'
			+ '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
	});
	html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
	$('#selectData').empty().append(html.join(''));
}
function removeSelectTag(key){
	delete selectData[key];
	dataGrid.dataGrid('resetSelection', key);
	$('#selectNum').html(--selectNum);
	$('#'+key+'_tags-input').remove();
}
function getSelectData(){
	return selectData;
}
</script>