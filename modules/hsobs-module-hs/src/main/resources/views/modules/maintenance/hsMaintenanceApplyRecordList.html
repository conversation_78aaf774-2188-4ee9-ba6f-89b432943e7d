<% layout('/layouts/default.html', {title: '维修申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text('维修申请')}
            </div>
            <div class="box-tools pull-right">
                <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
                <a href="#" class="btn btn-default" id="btnExport" title="${text('导出')}"><i class="glyphicon glyphicon-export"></i>导出</a>
                <a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
            </div>
        </div>
        <div id="maintenanceCompletedListId" ></div>
        <div class="box-body">
            <div class="hide">
                <#form:form id="exportFundSlipForm" model="${hsMaintenanceApply}" action="${ctx}/maintenance/hsMaintenanceApply/exportFundSlip" method="post" class="form-inline hide"
                data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
                <#form:hidden id="exportId" path="id"/>
            </#form:form>
        </div>
        <div class="search-form-container">
        <#form:form id="searchForm" model="${hsMaintenanceApply}" action="${ctx}/maintenance/hsMaintenanceApply/fullListData" method="post" class="form-inline hide"
        data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
            <div class="search-form-row">
        <div class="form-group">
            <label class="control-label">${text('申请单位')}：</label>
            <div class="control-inline">
                <#form:treeselect id="unitId" title="${text('机构选择')}"
                path="unitId" labelPath="office.officeName"
                url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
                class="required" allowClear="true" canSelectRoot="true" canSelectParent="false"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label">${text('联系人')}：</label>
            <div class="control-inline">
                <#form:input path="contactName" maxlength="30" class="form-control width-120"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label">${text('联系人电话')}：</label>
            <div class="control-inline">
                <#form:input path="contactTel" maxlength="20" class="form-control width-120"/>
            </div>
        </div>
            </div>
            <div class="search-form-row">
        <div class="form-group">
            <label class="control-label">${text('维修类别')}：</label>
            <div class="control-inline">
                <#form:input path="repairType" maxlength="32" class="form-control width-120"/>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label">${text('房屋坐落')}：</label>
            <div class="control-inline">
                <#form:input path="houseAddress" maxlength="200" class="form-control width-120"/>
            </div>
        </div>
            </div>
        <div class="search-button-row">
            <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
            <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
        </div>
    </#form:form>
    </div>
    <table id="dataGrid"></table>
    <div id="dataGridPage"></div>
</div>
</div>
</div>
<script>
    //# // 初始化DataGrid对象
$(function () {
    $('#dataGrid').dataGrid({
        searchForm: $('#searchForm'),
        sortableColumn: false,
        columnModel: [
            {header:'${text("申请单号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
                    return '<a href="${ctx}/maintenance/hsMaintenanceApply/form?id='+row.id+'" class="hsBtnList" data-title="${text("查看维修申请")}">'+(val||row.id)+'</a>';
                }},
            {header:'${text("房屋信息")}', name:'publicRentalEstate.name', index:'a.public_rental_estate.name', width:150, align:"left"},
            {header:'${text("房屋坐落")}', name:'houseAddress', index:'a.house_address', width:150, align:"left"},
            {header:'${text("申请单位")}', name:'applyOffice.treeNames', index:'a.apply_office.tree_names', width:150, align:"left"},
            {header:'${text("联系人")}', name:'contactName', index:'a.contact_name', width:150, align:"left"},
            {header:'${text("联系人电话")}', name:'contactTel', index:'a.contact_tel', width:150, align:"left"},
            // {header:'${text("现存资金")}', name:'existFund', index:'a.exist_fund', width:150, align:"center"},
            {header:'${text("申请金额")}', name:'applyFund', index:'a.apply_fund', width:150, align:"center"},
            {header:'${text("维修类别")}', name:'repairType', index:'a.repair_type', width:150, align:"left"},
            {header:'${text("申请状态")}', name:'applyStatus', index:'a.apply_status', width:150, align:"center", formatter: function(val, obj, row, act){
                    return js.getDictLabel("#{@DictUtils.getDictListJson('maintenance_apply_status_f')}", val + '', '${text("未知")}', true);
                }},
            {header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
            {header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
                    var actions = [];
                    if (row.status != Global.STATUS_DRAFT){
                        actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=maintenance_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
                    }
                    <% if(hasPermi('maintenance:hsMaintenanceApply:edit')){ %>
                        if (row.applyStatus === 12) {
                            actions.push('<a href="#" onclick="js.confirm(\'是否导出？\', function(){exportData(\''+row.id+'\');});return false;" class="" title="${text("导出资金拨付单")}">资金拨付单</a>&nbsp;');
                        }
                        <% } %>
                    return actions.join('');
                }}
        ],
        //# // 加载成功后执行事件
        ajaxSuccess: function(data){

        }
    })
});
    function exportData(id) {
        $('#exportId').val(id);
        js.ajaxSubmitForm( $('#exportFundSlipForm'), {
            url: '${ctx}/maintenance/hsMaintenanceApply/exportFundSlip',
            clearParams: '',
            downloadFile: true
        });
        return false;
    }
</script>
<script>
    $('#btnExport').click(function(){
        js.confirm('${text("是否导出数据？")}', function(){
            js.ajaxSubmitForm($('#searchForm'), {
                url: '${ctx}/maintenance/hsMaintenanceApply/exportData',
                clearParams: 'pageNo,pageSize',
                downloadFile: true
            });
        });
    })
</script>
<% } %>
