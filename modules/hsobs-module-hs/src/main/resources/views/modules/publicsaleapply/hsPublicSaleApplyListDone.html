<% layout('/layouts/default.html', {title: '公有住房配售申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公有住房配售申请管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('publicsaleapply:hsPublicSaleApply:edit')){ %>
					<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsPublicSaleApply}" action="${ctx}/publicsaleapply/hsPublicSaleApply/listAuditDoneData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('申请编号')}：</label>
					<div class="control-inline">
						<#form:input path="id" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('产权单位')}：</label>
					<div class="control-inline">
						<#form:input path="propertyUnit" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
			<div class="form-group">
				<label class="control-label">${text('联系方式')}：</label>
				<div class="control-inline">
					<#form:input path="propertyPhone" maxlength="64" class="form-control width-120"/>
				</div>
			</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn : false,
	columnModel: [
		{header:'${text("申请编号")}', name:'id', index:'a.id', width:60, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/publicsaleapply/hsPublicSaleApply/archives/form?id='+row.id+'" class="hsBtnList" data-title="${text("查看公有住房配售申请")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("配售主题")}', name:'title', index:'title', width:120, align:"left"},
		{header:'${text("申请单位")}', name:'office.officeName', index:'a.office.office_name', width:120, align:"left"},
		{header:'${text("产权单位")}', name:'propertyUnit', index:'a.property_unit', width:120, align:"left"},
		{header:'${text("联系方式")}', name:'propertyPhone', index:'a.property_phone', width:120, align:"left"},
		{header:'${text("审批状态")}', name:'flowStatus', index:'a.flowStatus', width:60, align:"left"},
		{header:'${text("申请理由")}', name:'remarks', index:'a.remarks', width:180, align:"left"},
		{header:'${text("申请时间")}', name:'createDate', index:'a.create_date', width:80, align:"left"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('publicsaleapply:hsPublicSaleApply:edit')){
				actions.push('<a href="${ctx}/publicsaleapply/hsPublicSaleApply/archives/form?id='+row.id+'" class="hsBtnList" title="${text("查看公有住房配售申请")}">查看</a>&nbsp;');
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=public_sale_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/publicsaleapply/hsPublicSaleApply/exportAuditDoneData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>