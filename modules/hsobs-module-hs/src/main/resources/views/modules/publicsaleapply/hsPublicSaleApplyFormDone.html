<% layout('/layouts/default.html', {title: '公有住房配售申请管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsPublicSaleApply.isNewRecord ? '查看公有住房配售申请' : '查看公有住房配售申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsPublicSaleApply}" action="${ctx}/publicsaleapply/hsPublicSaleApply/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('配售主题')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:input path="title" maxlength="200" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('产权单位')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="propertyUnit" maxlength="200" readonly="true" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('联系方式')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="propertyPhone" maxlength="200" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('申请理由')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:textarea path="remarks" rows="4" maxlength="500" readonly="true" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('申请材料')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile" bizKey="${hsPublicSaleApply.id}" bizType="hsPublicSaleApply_file"
									uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('审批结果')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:textarea path="result" rows="4" maxlength="500" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required "></span> ${text('审批材料')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="resultFile" bizKey="${hsPublicSaleApply.id}" bizType="hsPublicSaleApply_result_file"
								uploadType="all" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>

				<div class="form-unit">${text('公有住房配售')}</div>
				<div class="form-unit-wrap table-form">
					<table id="hsPublicSaleEstateDataGrid"></table>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('publicsaleapply:hsPublicSaleApply:edit')){ %>
							<#form:hidden path="status"/>
							<% if (hsPublicSaleApply.isNewRecord || hsPublicSaleApply.status == '9'){ %>
							<% } %>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
//# // 初始化公有住房配售房屋DataGrid对象
$('#hsPublicSaleEstateDataGrid').dataGrid({
	data: "#{toJson(hsPublicSaleApply.hsPublicSaleEstateList)}",
	datatype: 'local', // 设置本地数据
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	//# // 设置数据表格列
	columnModel: [
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'申请id', name:'applyId', editable:true, hidden:true},
		{header:'${text("房源Id")}', name:'houseId', width:150, editable:false, hidden: true},
		{header:'${text("楼盘名称")}', name:'house.estate.name', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'128', 'class':'form-control required'}},
		{header:'${text("坐落地址")}', name:'house.estate.address', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'128', 'class':'form-control required'}},
		{header:'${text("房源信息")}', name:'house.simpleInfo', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'128', 'class':'form-control required'}},
		{header:'${text("结构类型")}', name:'structureType', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
	],
	//# // 编辑表格参数
	editGrid: false,				// 是否是编辑表格
	editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsPublicSaleEstateDataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据
	//# // 编辑表格的提交数据参数
	editGridInputFormListName: 'hsPublicSaleEstateList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,createBy,createDate,updateBy,updateDate,structureType,applyId,houseId,', // 提交数据列表的属性字段
		//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});
</script>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};

BpmButton.callback = function(){
	let idValue = $('#id').val();
	$.ajax({
		type: 'POST',
		url: "${ctx}/publicsaleapply/hsPublicSaleApply/flushTaskStatus?___t=" + new Date().getTime(),
		data: {id: idValue},
		dataType: 'json',
		async: false,
		error: function(data){
			js.showErrorMessage(data.responseText);
		},
		success: function(data, status, xhr){
		}
	});
};

// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>