<% layout('/layouts/default.html', {title: '房源VR信息表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('房源VR信息表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('housrvr:hsQwHouseVr:edit')){ %>
					<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
					<a href="${ctx}/housrvr/hsQwHouseVr/form" class="btn btn-default btnTool" title="${text('新增房源VR信息表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwHouseVr}" action="${ctx}/housrvr/hsQwHouseVr/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('小区名称')}：</label>
							<div class="control-inline">
								<#form:input path="estateName" maxlength="100" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('楼栋单元')}：</label>
							<div class="control-inline">
								<#form:input path="houseUnit" maxlength="100" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('面积')}：</label>
							<div class="control-inline">
								<#form:input path="houseArea" class="form-control width-120"/>
							</div>
						</div>
					</div>

					<!-- 第二行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('vr地址')}：</label>
							<div class="control-inline">
								<#form:input path="vrUrl" maxlength="200" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('备注信息')}：</label>
							<div class="control-inline">
								<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
							</div>
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/housrvr/hsQwHouseVr/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("小区名称")}', name:'estateName', index:'a.estate_name', width:150, align:"left", sortable:false, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/housrvr/hsQwHouseVr/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑房源VR信息表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("楼栋单元")}', name:'houseUnit', index:'a.house_unit', width:150, align:"left", sortable:false},
		{header:'${text("面积")}', name:'houseArea', index:'a.house_area', width:150, align:"left", sortable:false},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"left", sortable:false, formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("vr地址")}', name:'vrUrl', index:'a.vr_url', width:150, align:"left", sortable:false, formatter: function(val, obj, row, act){
				if (val && val.trim() !== '') {
					return '<a href="' + val + '" target="_blank" class="btn btn-link btn-xs"> ${text("'+val+'")}</a>';
				} else {
					return '${text("无")}';
				}
			}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"left", sortable:false},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:150, align:"left", sortable:false},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('housrvr:hsQwHouseVr:edit')){
				actions.push('<a href="${ctx}/housrvr/hsQwHouseVr/form?id='+row.id+'" class="hsBtnList" title="${text("编辑房源VR信息表")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/housrvr/hsQwHouseVr/delete?id='+row.id+'" class="btnList" title="${text("删除房源VR信息表")}" data-confirm="${text("确认要删除该房源VR信息表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	loadComplete: function() {
		// 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
		requestAnimationFrame(function() {
			CommonTable.setupFixedColumns('dataGrid');
		});
		js.closeLoading(0, true);
	}
});
</script>

<script>
	// 初始化公共表格功能
	CommonTable.init('dataGrid');

	// 可选：添加调试信息
	$(document).ready(function() {
		setTimeout(function() {
			console.log('检查固定列样式应用情况:');
			console.log('checkbox列数量:', $('.fixed-checkbox-column').length);
			console.log('左固定列数量:', $('.fixed-left-column').length);
			console.log('右固定列数量:', $('.fixed-right-column').length);
			console.log('CommonTable对象:', typeof CommonTable);
			console.log('CommonTable方法:', Object.keys(CommonTable || {}));

			// 如果固定列未生效，手动触发
			if ($('.fixed-checkbox-column').length === 0) {
				console.log('固定列未自动应用，手动触发...');
				CommonTable.setupFixedColumns('dataGrid');
			}
		}, 3000);
	});
</script>