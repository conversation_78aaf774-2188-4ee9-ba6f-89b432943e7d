<% layout('/layouts/default.html', {title: '预警信息管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsAlarm.isNewRecord ? '预警信息' : '预警信息')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsAlarm}" action="${ctx}/hsalarm//save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('处理人员')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="userId" title="用户选择"
								path="userId" labelPath="employee.empName"
								url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
								checkbox="false" itemCode="userCode" itemName="userName" readonly="true"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('工作单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="officeId" title="${text('工作单位')}"
								path="officeCode" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
								class="required" allowClear="false" canSelectRoot="true" canSelectParent="false" readonly="true" />
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('预警类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="alarmType" dictType="hs_alarm_type" class="form-control required" readonly="true" />
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('预警信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="alarmInfo" rows="4" maxlength="500" class="form-control" readonly="true" />
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('预警处理')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('预警状态')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
<% if(hsAlarm.alarmStatus == "2"){ %>
								<#form:select path="alarmStatus" dictType="early_warn_state" class="form-control required" readonly="true" />
<% } else { %>
								<#form:select path="alarmStatus" dictType="early_warn_state" class="form-control required" />
<% } %>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
<% if(hsAlarm.alarmStatus == "2"){ %>
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control" readonly="true" />
<% } else { %>
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
<% } %>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('hsalarm::edit')){ %>
						<% if(hsAlarm.alarmStatus != "2"){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('处 理')}</button>&nbsp;
						<% } %>
						<% } %>
						<a href="#" class="btn btn-default" id="btnPublic"><i class="glyphicon glyphicon-export"></i> ${text('发 布')}</a>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});

$('#btnPublic').click(function(){
	$.ajax({
		url: '${ctx}/hsalarm/publicAlarm',
		type: 'POST',
		data: $('#inputForm').serialize(),  // 序列化表单数据
		success: function(data) {
			js.showMessage(data.message);
			//page();
		},
		error: function(error) {
			js.showErrorMessage(error.responseJSON.message);
		}
	});
});
</script>