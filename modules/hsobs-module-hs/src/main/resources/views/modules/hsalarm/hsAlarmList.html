<% layout('/layouts/default.html', {title: '预警信息管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('智能预警管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsAlarm}" action="${ctx}/hsalarm//listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline width-250" >
						<#form:treeselect id="officeId" title="${text('工作单位')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData${hsAlarm.parentCodeTree}" allowClear="true"/>
					</div>
				</div>
					<div class="form-group">
						<label class="control-label">${text('预警类型')}：</label>
						<div class="control-inline width-160">
							<#form:select path="alarmType" dictType="hs_alarm_type" blankOption="true" class="form-control"/>
						</div>
					</div>
				<div class="form-group">
					<label class="control-label">${text('预警状态')}：</label>
					<div class="control-inline width-160">
						<#form:select path="alarmStatus" dictType="early_warn_state" blankOption="true" class="form-control"/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('预警日期')}：</label>
					<div class="control-inline width-160">
						<#form:input path="startDate" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
						-
					<div class="control-inline width-160">
						<#form:input path="endDate" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("处理人员")}', name:'employee.empName', index:'a.user_id', width:80, align:"left"},
		{header:'${text("所属单位")}', name:'office.officeName', index:'a.office_code', width:150, align:"left"},
		{header:'${text("预警类型")}', name:'alarmType', index:'a.alarm_type', width:60, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_alarm_type')}", val, '${text("未知")}', true);
			}},
		{header:'${text("预警时间")}', name:'createDate', index:'a.create_date', width:60, align:"left"},
		{header:'${text("预警信息")}', name:'alarmInfo', index:'a.alarm_info', width:300, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/hsalarm//form?id='+row.id+'" class="hsBtnList" data-title="${text("处理预警信息")}">'+(val||row.id)+'</a>';
			}},
		{header:'${text("预警状态")}', name:'alarmStatus', index:'a.alarm_status', width:40, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('early_warn_state')}", val, '${text("未知")}', true);
			}},
		{header:'${text("发布状态")}', name:'publicStatus', index:'a.public_status', width:60, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_alarm_public_status')}", val, '${text("未知")}', true);
			}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('hsalarm::edit')){
				actions.push('<a href="${ctx}/hsalarm//form?id='+row.id+'" class="hsBtnList" title="${text("处理预警信息")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/hsalarm//disable?id='+row.id+'" class="btnList" title="${text("停用预警信息")}" data-confirm="${text("确认要停用该预警信息吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/hsalarm//enable?id='+row.id+'" class="btnList" title="${text("启用预警信息")}" data-confirm="${text("确认要启用该预警信息吗？")}">启用</a>&nbsp;');
				}
				//actions.push('<a href="${ctx}/hsalarm//delete?id='+row.id+'" class="btnList" title="${text("删除预警信息")}" data-confirm="${text("确认要删除该预警信息吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});

$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/hsalarm//exportAlarmData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>