<% layout('/layouts/default.html', {title: '局直公房管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwPublicRentalHouse.isNewRecord ? '新增' : '编辑')}${text(suffix?'')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwPublicRentalHouse}" action="${ctx}/house/hsQwPublicRentalBureauHouse/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('楼盘')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:select path="estateId" items="${@com.hsobs.hs.modules.estate.util.EstateUtils.getEstateList()}" itemLabel="name" itemValue="id" class="form-control required" />
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('楼号')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="buildingNum" maxlength="10" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('所属单位')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:treeselect id="publicOrg" title="${text('机构选择')}"
								path="officeCode" labelPath="office.officeName"
								url="${ctx}/sys/office/treeData"
								class=" required" allowClear="true"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('住房编号')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="houseNum" maxlength="50" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('所处楼层')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="floor" class="form-control required digits"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('公摊面积')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="sharedArea" class="form-control required number"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('建筑面积')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="buildingArea" maxlength="10" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('户型')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:select path="houseType" dictType="hs_house_house_type" blankOption="true" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('单元号')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="unitNum" maxlength="10" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('平面图图片文件编号')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="filePlan" maxlength="64" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('VR信息')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="vrInfo" maxlength="255" class="form-control"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('是否发布')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:select path="isPublic" dictType="hs_house_public" class="form-control required" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
							</td>
							<td colspan="3">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('平面图上传')}：
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadImage" bizKey="${hsQwPublicRentalHouse.id}" bizType="hsQwHouse_plane"
								uploadType="image" class="" readonly="false" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('house:hsQwPublicRentalHouse:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>