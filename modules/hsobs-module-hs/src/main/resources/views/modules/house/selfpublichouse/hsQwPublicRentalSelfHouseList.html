<% layout('/layouts/default.html', {title: '省直自管公房管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('省直自管公房管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('house:hsQwPublicRentalHouse:edit')){ %>
				    <a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入</a>
					<a href="${ctx}/house/hsQwPublicRentalSelfHouse/form" class="btn btn-default btnTool" title="${text('新增省直自管公房')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwPublicRentalHouse}" action="${ctx}/house/hsQwPublicRentalSelfHouse/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('楼盘')}：</label>
					<div class="control-inline width-120">
						<#form:select path="estateId" items="${@com.hsobs.hs.modules.estate.util.EstateUtils.getEstateList()}" itemLabel="name" itemValue="id" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('楼号')}：</label>
					<div class="control-inline">
						<#form:input path="buildingNum" maxlength="10" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('单元号')}：</label>
					<div class="control-inline">
						<#form:input path="unitNum" maxlength="10" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('是否发布')}：</label>
					<div class="control-inline width-120">
						<#form:select path="isPublic" dictType="hs_house_public" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('创建时间')}：</label>
					<div class="control-inline">
						<#form:input path="createDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="createDate_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="createDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('所属单位')}：</label>
					<div class="control-inline width-120" >
						<#form:treeselect id="publicOrg" title="${text('机构选择')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('房源类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="type" dictType="hs_house_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('房屋状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="houseStatus" dictType="hs_house_status" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("楼盘")}', name:'estate.name',  sortable:false, width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/estate/hsQwPublicRentalEstate/form?id='+row.estateId+'" class="hsBtnList" data-title="${text("查看楼盘信息")}">'+(val||row.estateId)+'</a>';
		}},
		{header:'${text("楼盘位置")}', name:'estate.address',  sortable:false, width:150, align:"left"},
		{header:'${text("楼号")}', name:'buildingNum',  sortable:false, width:150, align:"left"},
		{header:'${text("单元号")}', name:'unitNum',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/house/hsQwPublicRentalHouse/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑省直自管公房")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("所属单位")}', name:'office.officeName',  sortable:false, width:150, align:"left"},
		{header:'${text("是否发布")}', name:'isPublic',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_public')}", val, '${text("未知")}', true);
		}},
		{header:'${text("创建时间")}', name:'createDate',  sortable:false, width:150, align:"left"},
		{header:'${text("户型")}', name:'houseType',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_house_type')}", val, '${text("未知")}', true);
			}},
		{header:'${text("房源类型")}', name:'type',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("房屋状态")}', name:'houseStatus',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('house:hsQwPublicRentalHouse:edit')){
				actions.push('<a href="${ctx}/house/hsQwPublicRentalSelfHouse/form?id='+row.id+'" class="hsBtnList" title="${text("编辑省直自管公房")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/house/hsQwPublicRentalHouse/delete?id='+row.id+'" class="btnList" title="${text("删除省直自管公房")}" data-confirm="${text("确认要删除该省直自管公房吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script><script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/house/hsQwPublicRentalSelfHouse/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入省直自管公房")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">
<form id="inputForm" action="${ctx}/house/hsQwPublicRentalSelfHouse/importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/house/hsQwPublicRentalSelfHouse/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
</script>