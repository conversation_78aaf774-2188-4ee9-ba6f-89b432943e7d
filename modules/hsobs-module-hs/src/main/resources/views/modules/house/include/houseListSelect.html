<% layout('/layouts/default.html', {title: '房源房源信息表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-body">
            <#form:form id="searchForm" model="${hsQwPublicRentalHouse}" action="${ctx}${listDataUrl}" method="post" class="form-inline"
            data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
            <div class="form-group">
                <label class="control-label">${text('楼盘')}：</label>
                <div class="control-inline width-120">
                    <#form:listselect id="houseId" title="楼盘选择"
                    path="estateId"
                    url="${ctx}/estate/hsQwPublicRentalEstate/hsQwPublicRentalEstateSelect" allowClear="false"
                    checkbox="false" itemCode="id" itemName="name"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('楼号')}：</label>
                <div class="control-inline">
                    <#form:input path="buildingNum" maxlength="10" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('户型')}：</label>
                <div class="control-inline width-120">
                    <#form:select path="houseType" dictType="hs_house_house_type" blankOption="true" class="form-control"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('单元号')}：</label>
                <div class="control-inline">
                    <#form:input path="unitNum" maxlength="10" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('承租人')}：</label>
                <div class="control-inline">
                    <#form:input path="hsQwApply.mainApplyer.name" maxlength="10" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('创建时间')}：</label>
                <div class="control-inline">
                    <#form:input path="createDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                    dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="createDate_lte.click()"/>
                    &nbsp;-&nbsp;
                    <#form:input path="createDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                    dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
                </div>
            </div>
            <#form:hidden path="type" />
            <#form:hidden path="isPublic" defaultValue="1"/>
            <#form:hidden path="houseStatus" dictType="hs_house_status" blankOption="true" class="form-control"/>
            <#form:hidden path="dataType" defaultValue="${hsQwPublicRentalHouse.dataType}"/>
            <#form:hidden path="applyId" defaultValue="${hsQwPublicRentalHouse.applyId}"/>
            <#form:hidden path="selectId" defaultValue="${hsQwPublicRentalHouse.selectId}"/>
            ${layoutContent!}
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
            </div>
        </#form:form>
        <div class="row">
            <div class="col-xs-10 pr10">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
            <div class="col-xs-2 pl0">
                <div id="selectData" class="tags-input"></div>
            </div>
        </div>
    </div>
</div>
</div>
<% } %>
<script>
    var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
        selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
            searchForm: $('#searchForm'),
            columnModel: [
                {header:'${text("楼盘")}', name:'estate.name',  sortable:false, width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
                        return '<a href="${ctx}/estate/hsQwPublicRentalEstate/form?id='+row.estateId+'" class="hsBtnList" data-title="${text("查看楼盘信息")}">'+(val||row.estateId)+'</a>';
                    }},
                {header:'${text("楼盘位置")}', name:'estate.address',  sortable:false, width:150, align:"left"},
                {header:'${text("楼号")}', name:'buildingNum',  sortable:false, width:150, align:"left"},
                {header:'${text("单元号")}', name:'unitNum',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
                        return '<a href="${ctx}/house/hsQwPublicRentalHouse/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁公租房房源房源信息表")}">'+(val||row.id)+'</a>';
                    }},

                {header:'${text("创建时间")}', name:'createDate',  sortable:false, width:150, align:"left"},
                {header:'${text("户型")}', name:'houseType',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
                        return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_house_type')}", val, '${text("未知")}', true);
                    }},
                {header:'${text("房源类型")}', name:'type',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
                        return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_type')}", val, '${text("未知")}', true);
                    }},
                {header:'${text("房屋状态")}', name:'houseStatus',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
                        return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_status')}", val, '${text("未知")}', true);
                    }},
                {header:'${text("承租人")}', name:'hsQwApply.mainApplyer.name',  sortable:false, width:150, align:"left"},
                {header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
                        return JSON.stringify(row);
                    }}
            ],
            autoGridHeight: function(){
                var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
                $('.tags-input').height($('.ui-jqgrid').height() - 10);
                return height;
            },
            showCheckbox: '${parameter.checkbox}' == 'true',
            multiboxonly: false, // 单击复选框时再多选
            ajaxSuccess: function(data){
                $.each(selectData, function(key, value){
                    dataGrid.dataGrid('setSelectRow', key);
                });
                initSelectTag();
            },
            onSelectRow: function(id, isSelect, event){
                if ('${parameter.checkbox}' == 'true'){
                    if(isSelect){
                        selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
                    }else{
                        delete selectData[id];
                    }
                }else{
                    selectData = {};
                    selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
                }
                initSelectTag();
            },
            onSelectAll: function(ids, isSelect){
                if ('${parameter.checkbox}' == 'true'){
                    for (var i=0; i<ids.length; i++){
                        if(isSelect){
                            selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
                        }else{
                            delete selectData[ids[i]];
                        }
                    }
                }
                initSelectTag();
            },
            ondblClickRow: function(id, rownum, colnum, event){
                if ('${parameter.checkbox}' != 'true'){
                    js.layer.$('#' + window.name).closest('.layui-layer')
                        .find(".layui-layer-btn0").trigger("click");
                }
                initSelectTag();
            }
        });
    function initSelectTag(){
        selectNum = 0;
        var html = [];
        $.each(selectData, function(key, value){
            selectNum ++;
            html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.simpleInfo)+' </span>'
                + '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
        });
        html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
        $('#selectData').empty().append(html.join(''));
    }
    function removeSelectTag(key){
        delete selectData[key];
        dataGrid.dataGrid('resetSelection', key);
        $('#selectNum').html(--selectNum);
        $('#'+key+'_tags-input').remove();
    }
    function getSelectData(){
        return selectData;
    }
</script>