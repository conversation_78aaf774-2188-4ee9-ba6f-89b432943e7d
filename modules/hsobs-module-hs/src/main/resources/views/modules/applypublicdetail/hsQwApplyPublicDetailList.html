<% layout('/layouts/default.html', {title: '租赁资格轮候公示复查详情表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租赁资格轮候公示复查详情表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('applypublicdetail:hsQwApplyPublicDetail:edit')){ %>
					<a href="${ctx}/applypublicdetail/hsQwApplyPublicDetail/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候公示复查详情表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwApplyPublicDetail}" action="${ctx}/applypublicdetail/hsQwApplyPublicDetail/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('公示id')}：</label>
					<div class="control-inline">
						<#form:input path="publicId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请单id')}：</label>
					<div class="control-inline">
						<#form:input path="applyId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('流程id')}：</label>
					<div class="control-inline">
						<#form:input path="processId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("公示id")}', name:'publicId', index:'a.public_id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/applypublicdetail/hsQwApplyPublicDetail/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候公示复查详情表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请单id")}', name:'applyId', index:'a.apply_id', width:150, align:"left"},
		{header:'${text("流程id")}', name:'processId', index:'a.process_id', width:150, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('applypublicdetail:hsQwApplyPublicDetail:edit')){
				actions.push('<a href="${ctx}/applypublicdetail/hsQwApplyPublicDetail/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁资格轮候公示复查详情表")}">编辑</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>