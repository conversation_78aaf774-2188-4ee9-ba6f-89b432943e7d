<% layout('/layouts/default.html', {title: '档案目录管理', libs: ['layout','zTree']}){ %>
<div class="ui-layout-west">
	<div class="main-content">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title">
					<i class="fa icon-grid"></i> ${text('档案目录')}
				</div>
				<div class="box-tools pull-right">
					<button type="button" class="btn btn-box-tool" id="btnExpand" title="${text('展开')}" style="display:none;"><i class="fa fa-chevron-up"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnCollapse" title="${text('折叠')}"><i class="fa fa-chevron-down"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnRefresh" title="${text('刷新')}"><i class="fa fa-refresh"></i></button>
				</div>
			</div>
			<div class="ui-layout-content">
				<div id="tree" class="ztree"></div>
			</div>
		</div>
	</div>
</div>
<div class="ui-layout-center">
	<iframe id="mainFrame" name="mainFrame" class="ui-layout-content p0"
		src="${ctx}/datamanage/hsFileClass/list"></iframe>
</div>
<% } %>
<script>
$('body').layout({
	west__initClosed: $(window).width() <= 767, // 是否默认关闭
	west__size: 200
});
var win = $("#mainFrame")[0].contentWindow;
var setting = {view:{selectedMulti:false},data:{key:{title:"title"},simpleData:{enable:true}},
	async:{enable:true,autoParam:["id=parentCode"],url:"${ctx}/datamanage/hsFileClass/treeData"},
	callback:{onClick:function(event, treeId, treeNode){
		tree.expandNode(treeNode);
		//win.$('button[type=reset]').click();
		win.$('#id').val(treeNode.id);
		win.page();
	}}
}, tree, loadTree = function(){
	js.ajaxSubmit(setting.async.url+"?___t="+new Date().getTime(), {
			parentCode:'${parameter.parentCode!}'}, function(data){
		tree = $.fn.zTree.init($("#tree"), setting, data);
		var level = -1, nodes;
		while (++level <= 1) {
			nodes = tree.getNodesByParam("level", level);
			if (nodes.length > 10) { break; }
			for(var i=0; i<nodes.length; i++) {
	 			tree.expandNode(nodes[i], true, false, false);
			}
		}
	}, null, null, js.text('loading.message'));
};loadTree();
$('#btnExpand').click(function(){
	tree.expandAll(true);
	$(this).hide();
	$('#btnCollapse').show();
});
$('#btnCollapse').click(function(){
	tree.expandAll(false);
	$(this).hide();
	$('#btnExpand').show();
});
$('#btnRefresh').click(function(){
	loadTree();
});
function page(){
	win.page();
}
</script>
