<% layout('/layouts/default.html', {title: '档案目录管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('档案目录管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnRefreshTree" title="${text('刷新')}"><i class="fa fa-refresh"></i> ${text('刷新')}</a>
				<a href="#" class="btn btn-default" id="btnExpandTreeNode" title="${text('展开一级')}"><i class="fa fa-angle-double-down"></i> ${text('展开')}</a>
				<a href="#" class="btn btn-default" id="btnCollapseTreeNode" title="${text('折叠全部')}"><i class="fa fa-angle-double-up"></i> ${text('折叠')}</a>
				<% if(hasPermi('datamanage:hsFileClass:edit')){ %>
					<a href="${ctx}/datamanage/hsFileClass/form" class="btn btn-default btnTool" title="${text('新增档案分类')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsFileClass}" action="${ctx}/datamanage/hsFileClass/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<#form:hidden path="id"/>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('分类名称')}：</label>
					<div class="control-inline">
						<#form:input path="className" maxlength="100" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('业务类别')}：</label>
					<div class="control-inline">
						<#form:input path="businessType" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('分类编码')}：</label>
					<div class="control-inline">
						<#form:input path="classCode" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("分类名称")}', name:'className', index:'a.class_name', width:250, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/datamanage/hsFileClass/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑档案分类")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("分类编码")}', name:'classCode', index:'a.class_code', width:150, align:"left"},
		{header:'${text("业务类别")}', name:'businessType', index:'a.business_type', width:150, align:"left"},
		{header:'${text("排序号")}', name:'treeSort', index:'a.tree_sort', width:150, align:"center"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('datamanage:hsFileClass:edit')){
				actions.push('<a href="${ctx}/datamanage/hsFileClass/form?id='+row.id+'" class="hsBtnList" title="${text("编辑档案分类")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/datamanage/hsFileClass/delete?id='+row.id+'" class="btnList" title="${text("删除档案分类")}" data-confirm="${text("确认要删除该档案目录及所有子档案目录吗？")}" data-deltreenode="'+row.id+'">删除</a>&nbsp;');
				actions.push('<a href="${ctx}/datamanage/hsFileClass/form?parentCode='+row.id+'" class="hsBtnList" title="${text("新增下级档案分类")}"><i class="fa fa-plus-square"></i></a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	treeGrid: true,			// 启用树结构表格
	defaultExpandLevel: 0,	// 默认展开的层次
	expandNodeClearPostData: 'className,groupType,businessType,classCode,remarks,', // 展开节点清理请求参数数据（一般设置查询条件的字段属性，否则在查询后，不能展开子节点数据）
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>