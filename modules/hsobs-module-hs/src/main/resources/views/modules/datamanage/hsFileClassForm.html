<% layout('/layouts/default.html', {title: '档案分类管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsFileClass.isNewRecord ? '新增档案分类' : '编辑档案分类')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsFileClass}" action="${ctx}/datamanage/hsFileClass/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('上级档案分类')}：</label>
							<div class="col-sm-8">
								<#form:treeselect id="parent" title="${text('上级档案分类')}"
									path="parent.id" labelPath="parent.className"
									url="${ctx}/datamanage/hsFileClass/treeData?excludeCode=${hsFileClass.id}"
									class="" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
							</div>
						</div>
					</div>
				</div>
				<#form:hidden path="id"/>
				<#form:hidden path="groupType" value="global"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('分类名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="className" maxlength="100" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('业务类别')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="businessType" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('分类编码')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="classCode" maxlength="255" readonly="${!hsFileClass.isNewRecord}" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('排序号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="treeSort" class="form-control required digits"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('datamanage:hsFileClass:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					(contentWindow.win||contentWindow).$('#dataGrid').dataGrid('refreshTreeChildren',
							$('#parentCode').val(), '${hsFileClass.id}');
				});
			}
		}, "json");
    }
});

// 选择上级节点回调方法
function treeselectCallback(id, act, index, layero){
	if (id == 'parent' && (act == 'ok' || act == 'clear')){
		// 创建并初始化下一个节点信息，如：排序号、默认值
		$.get('${ctx}/datamanage/hsFileClass/createNextNode?parentCode='
				+$('#parentCode').val(), function(data){
			$('#treeSort').val(data.treeSort);
		});
	}
}
</script>