<% layout('/layouts/default.html', {title: '档案查询', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('档案查询')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport" title="${text('导出')}"><i class="glyphicon glyphicon-export"></i>导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsFileScan}" action="${ctx}/datamanage/hsFileScan/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('档案名称')}：</label>
					<div class="control-inline">
						<#form:input path="fileName" maxlength="90" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('业务类别')}：</label>
					<div class="control-inline">
						<#form:input path="businessType" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('档案分类')}：</label>
					<div class="control-inline">
						<#form:treeselect id="classId" title="${text('档案分类')}"
						path="hsFileClass.id" labelPath="hsFileClass.className"
						url="${ctx}/datamanage/hsFileClass/treeData"
						class="" allowClear="true" canSelectRoot="true" canSelectParent="true"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$(function () {
	$('#btnExport').click(function(){
		js.confirm('${text("是否导出数据？")}', function(){
			js.ajaxSubmitForm($('#searchForm'), {
				url: '${ctx}/datamanage/hsFileScan/exportData',
				clearParams: 'pageNo,pageSize',
				downloadFile: true
			});
		});
	})
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("档案编号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/datamanage/hsFileScan/form?id='+row.id+'&isRead=true" class="hsBtnList" data-title="${text("查看档案信息")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("文档名称")}', name:'fileName', index:'a.file_name', width:150, align:"left"},
		{header:'${text("档案分类")}', name:'hsFileClass.className', index:'a.hs_file_class_class_name', width:150, align:"left"},
		{header:'${text("分类编码")}', name:'hsFileClass.classCode', index:'a.hs_file_class_class_code', width:150, align:"left"},
		{header:'${text("业务类别")}', name:'businessType', index:'a.business_type', width:150, align:"left"},
		{header:'${text("文档备注信息")}', name:'fileRemark', index:'a.file_remark', width:150, align:"left"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('datamanage:hsFileScan:edit')){
				actions.push('<a href="${ctx}/datamanage/hsFileScan/form?id='+row.id+'&isRead=true" class="hsBtnList" title="${text("查看档案信息")}">编辑</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
});
</script>