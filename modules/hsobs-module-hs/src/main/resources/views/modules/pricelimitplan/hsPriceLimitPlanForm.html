<% layout('/layouts/default.html', {title: '限价房方案管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsPriceLimitPlan.isNewRecord ? '新增限价房方案' : '编辑限价房方案')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsPriceLimitPlan}" action="${ctx}/pricelimitplan/hsPriceLimitPlan/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('主题')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<% if(hasPermi('pricelimitplan:hsPriceLimitPlan:add')){ %>
								<#form:input path="title" maxlength="64" class="form-control required"/>
								<% } else { %>
								<#form:input path="title" maxlength="64" readonly="true" class="form-control required"/>
								<% } %>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">

								<span class="required ">*</span> ${text('方案')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<% if(hasPermi('pricelimitplan:hsPriceLimitPlan:add')){ %>
								<#form:textarea path="remarks" rows="10" maxlength="1028" class="form-control required"/>
								<% } else { %>
								<#form:textarea path="remarks" rows="10" readonly="true" maxlength="1028" class="form-control required"/>
								<% } %>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('pricelimitplan:hsPriceLimitPlan:add')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>