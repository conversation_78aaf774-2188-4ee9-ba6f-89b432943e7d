<% layout('/layouts/default.html', {title: '限价房方案管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('限价房方案管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('pricelimitplan:hsPriceLimitPlan:add')){ %>
					<a href="${ctx}/pricelimitplan/hsPriceLimitPlan/form" class="btn btn-default btnTool" title="${text('新增限价房方案')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsPriceLimitPlan}" action="${ctx}/pricelimitplan/hsPriceLimitPlan/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('主题')}：</label>
						<div class="control-inline">
							<#form:input path="title" maxlength="128" class="form-control width-120"/>
						</div>
					</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			</div>
			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn : false,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("编号")}', name:'id', index:'a.id', width:160, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/pricelimitplan/hsPriceLimitPlan/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑拟定限价房方案")}">'+(val||row.id)+'</a>';
			}},
		{header:'${text("主题")}', name:'title', index:'a.title', width:160, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/pricelimitplan/hsPriceLimitPlan/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑拟定限价房方案")}">'+(val||row.id)+'</a>';
			}},
		{header:'${text("方案")}', name:'remarks', index:'a.remarks', width:200, align:"left"},
		{header:'${text("发布时间")}', name:'createDate', index:'a.create_date', width:100, align:"left"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('pricelimitplan:hsPriceLimitPlan:edit')){
				actions.push('<a href="${ctx}/pricelimitplan/hsPriceLimitPlan/form?id='+row.id+'" class="hsBtnList" title="${text("编辑拟定限价房方案")}">编辑</a>&nbsp;');
				/*if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/pricelimitplan/hsPriceLimitPlan/disable?id='+row.id+'" class="btnList" title="${text("停用拟定限价房方案")}" data-confirm="${text("确认要停用该拟定限价房方案吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/pricelimitplan/hsPriceLimitPlan/enable?id='+row.id+'" class="btnList" title="${text("启用拟定限价房方案")}" data-confirm="${text("确认要启用该拟定限价房方案吗？")}">启用</a>&nbsp;');
				}*/
				//actions.push('<a href="${ctx}/pricelimitplan/hsPriceLimitPlan/delete?id='+row.id+'" class="btnList" title="${text("删除拟定限价房方案")}" data-confirm="${text("确认要删除该拟定限价房方案吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		requestAnimationFrame(function() {
			CommonTable.setupFixedColumns('dataGrid');
		});
	}
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/pricelimitplan/hsPriceLimitPlan/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>