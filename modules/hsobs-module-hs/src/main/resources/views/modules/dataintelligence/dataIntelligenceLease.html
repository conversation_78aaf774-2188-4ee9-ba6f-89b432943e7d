<% layout('/layouts/default.html', {title: '公租房租赁情况统计', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公租房租赁情况统计')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<!--<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>-->
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataIntelligenceLease}" action="${ctx}/dataintelligencelease/countAreaStat" method="post" class="form-inline hide" >
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('地市')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="city" id="citySelect" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="area" id="areaSelect" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('小区')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="estateId" id="estateIdSelect" itemLabel="name" itemValue="id" blankOption="true" class="form-control required" />
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline width-250" >
						<#form:treeselect id="officeId" title="${text('工作单位')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData${dataIntelligenceLease.parentCodeTree}" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('开始日期')}：</label>
					<div class="control-inline">
						<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('结束日期')}：</label>
					<div class="control-inline">
						<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</div>
			</#form:form>
			<#form:form id="OfficeStatForm" model="${dataIntelligenceLease}" action="${ctx}/dataintelligencelease/countLeaseOfficeStat" method="post" class="form-inline hide"
				data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			</#form:form>
			<#form:form id="VacantStatForm" model="${dataIntelligenceLease}" action="${ctx}/dataintelligencelease/countVacantAreaStat" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">公租房租赁总体统计</h3>
							</div>
							<table id="dataGridTotal"></table>
							<div id="dataGridTotalPage"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">公租房租赁小区统计</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body" style="height: 140px;">
							<div class="box-header with-border">
								<h3 class="box-title">公租房租赁单位统计</h3>
							</div>
							<table id="dataGridUnpaid"></table>
							<div id="dataGridUnpaidPage"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="col-md-6">
								<div class="chart">
									<div id="pieChartDiv2" style="height:320px;width:100%"></div><script>
									$(function(){
										pieChart2 = echarts.init(document.getElementById('pieChartDiv2'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'拖欠时长'
											},
											tooltip: {
												trigger: 'item',
												formatter: '{b} {c}次'
											},
											legend: {
												top: '25%',
												right: '20%',
												orient: 'vertical'
											},
											series: [
												{
													name: '',
													type: 'pie',
													center: ['30%', '50%'],
													radius: ['40%', '70%'],
													avoidLabelOverlap: false,
													label: {
														show: false,
														position: 'center',
														formatter: '{d}%'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
													]
												}
											]
										};
										pieChart2.setOption(option);
									});
								</script>
								</div>
							</div>
							<div class="col-md-6">
								<div class="chart">
									<div id="pieChartDiv3" style="height:320px;width:100%"></div><script>
									$(function(){
										pieChart3 = echarts.init(document.getElementById('pieChartDiv3'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'拖欠金额'
											},
											tooltip: {
												trigger: 'item',
												formatter: '{b} {c}元'
											},
											legend: {
												top: '25%',
												right: '20%',
												orient: 'vertical'
											},
											series: [
												{
													name: '',
													type: 'pie',
													center: ['30%', '50%'],
													radius: ['40%', '70%'],
													avoidLabelOverlap: false,
													label: {
														show: false,
														position: 'center',
														formatter: '{d}%'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
													]
												}
											]
										};
										pieChart3.setOption(option);
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">公租房空置房源分析</h3>
							</div>
							<table id="dataGrid5"></table>
							<div id="dataGrid5Page"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">公租房入住分析</h3>
							</div>
							<table id="dataGrid2"></table>
							<div id="dataGrid2Page"></div>
						</div>
					</div>
				</div>

			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>

var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(pieChart2) pieChart2.resize();
		if(pieChart3) pieChart3.resize();
	}).resize();
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		$(window).resize();
	});
	$('.ui-sortable').sortable({
		connectWith : '.ui-sortable',
		handle      : '.box-header, .nav-tabs',
		placeholder : 'sort-highlight',
		forcePlaceholderSize: true,
		zIndex : 999999
	}).find('.box-header, .nav-tabs').css('cursor', 'move');
});

$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("小区名称")}', name:'estateName', index:'ESTATE_NAME', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/hsestateaddress/estateAddress?id='+row.estateId+'" class="btnList" data-title="${text("小区位置")}">'+(val)+'</a>';
			}},
		{header:'${text("审批通过数")}', name:'approvedCount', index:'APPROVED_COUNT', width:150, align:"left"},
		{header:'${text("已签订合同数")}', name:'signedContractCount', index:'SIGNED_CONTRACT_COUNT', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/listAllUrl?compactSign=1&status=0,4&createDate_gte='+row.startDate+'&createDate_lte='+row.endDate+'&officeCode='+row.officeCode+'&hsQwApplyHouse.estate.name='+row.estateName+'" class="btnList" data-title="${text("租赁申请单管理")}">'+(val)+'</a>';
			}},
		{header:'${text("未签订合同数")}', name:'unsignedContractCount', index:'UNSIGNED_CONTRACT_COUNT', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/listAllUrl?compactSign=0&status=0,4&createDate_gte='+row.startDate+'&createDate_lte='+row.endDate+'&officeCode='+row.officeCode+'&hsQwApplyHouse.estate.name='+row.estateName+'" class="btnList" data-title="${text("租赁申请单管理")}">'+(val)+'</a>';
			}},
		{header:'${text("应收租金(元)")}', name:'receivables', index:'RECEIVABLES', width:150, align:"left"},
		{header:'${text("已收租金(元)")}', name:'paid', index:'PAID', width:150, align:"left"},
		{header:'${text("拖欠租金(元)")}', name:'unpaid', index:'UNPAID', width:150, align:"left"},
		{header:'${text("总面积m²")}', name:'buildingArea', index:'BUILDING_AREA', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/dataintelligencelease/dataIntelligenceLeaseArea?startDate='+row.startDate+'&endDate='+row.endDate+'&officeCode='+row.officeCode+'&estateId='+row.estateId+'&estateName='+row.estateName+'" class="btnList" data-title="${text("面积分析")}">'+(val)+'</a>';
			}},
		{header:'${text("35-45m²")}', name:'buildingArea1', index:'BUILDING_AREA_1', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/dataintelligencelease/dataIntelligenceLeaseArea?startDate='+row.startDate+'&endDate='+row.endDate+'&officeCode='+row.officeCode+'&estateId='+row.estateId+'&estateName='+row.estateName+'" class="btnList" data-title="${text("面积分析")}">'+(val)+'</a>';
			}},
		{header:'${text("60-65m²")}', name:'buildingArea2', index:'BUILDING_AREA_2', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/dataintelligencelease/dataIntelligenceLeaseArea?startDate='+row.startDate+'&endDate='+row.endDate+'&officeCode='+row.officeCode+'&estateId='+row.estateId+'&estateName='+row.estateName+'" class="btnList" data-title="${text("面积分析")}">'+(val)+'</a>';
			}},
		{header:'${text("70m²")}', name:'buildingArea3', index:'BUILDING_AREA_3', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/dataintelligencelease/dataIntelligenceLeaseArea?startDate='+row.startDate+'&endDate='+row.endDate+'&officeCode='+row.officeCode+'&estateId='+row.estateId+'&estateName='+row.estateName+'" class="btnList" data-title="${text("面积分析")}">'+(val)+'</a>';
			}},
		],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		$(document).ready(function () {
			// 使用 jQuery 动态获取数据并更新图表
			function fetchUnpaidDurationOffice(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencelease/countUnpaidDurationOffice", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						pieChart2.setOption({
							title:{
								text:data.title
							},
							series: [{
								data: data.data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			function fetchUnpadByOffice(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencelease/countUnpadByOffice", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						pieChart3.setOption({
							title:{
								text:data.title
							},
							series: [{
								data: data.data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}

			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchUnpadByOffice(formData);
				fetchUnpaidDurationOffice(formData);
				// 定时获取数据（例如每秒获取一次数据）
				// setInterval(fetchData, 1000); // 可根据需要调整时间间隔
			});
		});
	}
});

$('#dataGridTotal').dataGrid({
	url: '${ctx}/dataintelligencelease/countLeaseTotalStat',
	dataGridPage: $('#dataGridTotalPage'),
	columnModel: [
		{header:'${text("工作单位")}', name:'officeName', index:'OFFICE_NAME', width:150, align:"left"},
		{header:'${text("已申请数")}', name:'appliedForCount', index:'APPLIED_FRO_COUNT', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/listAllUrl?status=0,4&createDate_gte='+row.startDate+'&createDate_lte='+row.endDate+'&officeCode='+row.officeCode+'&city='+row.city+'&area='+row.area+'" class="btnList" data-title="${text("租赁申请单管理")}">'+(val)+'</a>';
			}},
		{header:'${text("待审批数")}', name:'pendingApprovalCount', index:'PENDING_APPROVAL_COUNT', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/listAllUrl?status=4&createDate_gte='+row.startDate+'&createDate_lte='+row.endDate+'&officeCode='+row.officeCode+'&city='+row.city+'&area='+row.area+'" class="btnList" data-title="${text("租赁申请单管理")}">'+(val)+'</a>';
			}},
		{header:'${text("审批通过数")}', name:'approvedCount', index:'APPROVED_COUNT', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/listAllUrl?status=0&createDate_gte='+row.startDate+'&createDate_lte='+row.endDate+'&officeCode='+row.officeCode+'&city='+row.city+'&area='+row.area+'" class="btnList" data-title="${text("租赁申请单管理")}">'+(val)+'</a>';
			}},
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});

$('#dataGridUnpaid').dataGrid({
	url: '${ctx}/dataintelligencelease/countLeasePaidStatByOffice',
	dataGridPage: $('#dataGridUnpaidPage'),
	columnModel: [
		{header:'${text("工作单位")}', name:'officeName', index:'OFFICE_CODE', width:150, align:"left"},
		{header:'${text("已签订合同数")}', name:'signedContractCount', index:'SIGNED_CONTRACT_COUNT', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/listAllUrl?compactSign=1&status=0,4&createDate_gte='+row.startDate+'&createDate_lte='+row.endDate+'&officeCode='+row.officeCode+'&city='+row.city+'&area='+row.area+'" class="btnList" data-title="${text("租赁申请单管理")}">'+(val)+'</a>';
			}},
		{header:'${text("未签订合同数")}', name:'unsignedContractCount', index:'UNSIGNED_CONTRACT_COUNT', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/listAllUrl?compactSign=0&status=0,4&createDate_gte='+row.startDate+'&createDate_lte='+row.endDate+'&officeCode='+row.officeCode+'&city='+row.city+'&area='+row.area+'" class="btnList" data-title="${text("租赁申请单管理")}">'+(val)+'</a>';
			}},
		{header:'${text("应收租金(元)")}', name:'receivables', index:'RECEIVABLES', width:150, align:"left"},
		{header:'${text("已收租金(元)")}', name:'paid', index:'PAID', width:150, align:"left"},
		{header:'${text("拖欠租金(元)")}', name:'unpaid', index:'UNPAID', width:150, align:"left"},
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});

//# //公租房租赁情况统计2 初始化DataGrid对象
$('#dataGrid2').dataGrid({
	url: '${ctx}/dataintelligencelease/countLeaseOfficeStat',
	dataGridPage: $('#dataGrid2Page'),
	columnModel: [
		{header:'${text("工作单位")}', name:'officeName', index:'OFFICE_NAME', width:150, align:"left"},
		{header:'${text("已入住数")}', name:'checkInCount', index:'CHECK_IN_COUNT', width:150, align:"left",
			formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/listAllUrl?compactSign=1&status=0,4&createDate_gte='+row.startDate+'&createDate_lte='+row.endDate+'&officeCode='+row.officeCode+'&city='+row.city+'&area='+row.area+'" class="btnList" data-title="${text("租赁申请单管理")}">'+(val)+'</a>';
			}},
		],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});

$('#dataGrid5').dataGrid({
	url: '${ctx}/dataintelligencelease/countVacantAreaStat',
	dataGridPage: $('#dataGrid5Page'),
	columnModel: [
		{header:'${text("小区名称")}', name:'estateName', index:'ESTATE_NAME', width:150, align:"left"},
		{header:'${text("总空置数")}', name:'vacantCount', index:'VACANT_COUNT', width:150, align:"left"},
		{header:'${text("公租房空置数")}', name:'vacant0Count', index:'VACANT0_COUNT', width:150, align:"left"},
		{header:'${text("局直公房空置数")}', name:'vacant3Count', index:'VACANT3_COUNT', width:150, align:"left"},
		{header:'${text("自管公房空置数")}', name:'vacant4Count', index:'VACANT4_COUNT', width:150, align:"left"},
		{header:'${text("总空置面积(m²)")}', name:'vacantArea', index:'VACANT_AREA', width:150, align:"left"},
		{header:'${text("公租房空置面积(m²)")}', name:'vacant0Area', index:'VACANT0_AREA', width:150, align:"left"},
		{header:'${text("局直公房空置面积(m²)")}', name:'vacant3Area', index:'VACANT3_AREA', width:150, align:"left"},
		{header:'${text("自管公房空置面积(m²)")}', name:'vacant4Area', index:'VACANT4_AREA', width:150, align:"left"},
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});

$("#searchForm").unbind('submit').submit(function(e){
	// 添加成立再执行查询
	if (true) {
		var formData = $(this).serializeArray();
		console.log(formData)

		$("#dataGrid").dataGrid('refresh', 1);

		$('#dataGridTotal').jqGrid('setGridParam', { postData: formData });
		$('#dataGridTotal').trigger("reloadGrid");
		$("#dataGridTotal").dataGrid('refresh', 1);

		$('#dataGrid2').jqGrid('setGridParam', { postData: formData });
		$('#dataGrid2').trigger("reloadGrid");
		$("#dataGrid2").dataGrid('refresh', 1);

		$('#dataGrid5').jqGrid('setGridParam', { postData: formData });
		$('#dataGrid5').trigger("reloadGrid");
		$("#dataGrid5").dataGrid('refresh', 1);
	}
	return false;
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/dataintelligencelease/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>

<script>
	$(document).ready(function() {

		$('#citySelect').change(function() {
			showAreaInfo();
		});
		function showAreaInfo() {

			let cityCode = $('#citySelect').val();

			let areaSelect = $('#areaSelect');
			areaSelect.empty();

			let estateIdSelect = $('#estateIdSelect');
			estateIdSelect.empty();
			let option = $("<option>")
					.val('')
					.text('所有小区');
			$("#estateIdSelect").append(option);

			if (cityCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getAreaInfo',
					type: 'GET',
					data: {areaCode: cityCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						let option = $("<option>")
								.val('')
								.text('所有区域');
						$("#areaSelect").append(option);

						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.areaCode)
									.text(item.areaName);
							$("#areaSelect").append(option);
						});
					}
				});
			}
		}

		$('#areaSelect').change(function() {

			let estateIdSelect = $('#estateIdSelect');
			estateIdSelect.empty();

			let option = $("<option>")
					.val('')
					.text('所有小区');
			$("#estateIdSelect").append(option);

			let areaCode = $('#areaSelect').val();
			if (areaCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getEstateInfo',
					type: 'GET',
					data: {areaCode: areaCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.id)
									.text(item.name);
							$("#estateIdSelect").append(option);
						});
					}
				});
			}
		});
	});
</script>