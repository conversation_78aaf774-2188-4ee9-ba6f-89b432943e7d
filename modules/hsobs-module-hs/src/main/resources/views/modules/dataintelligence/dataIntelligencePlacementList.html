<% layout('/layouts/default.html', {title: '交易金额', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<!--<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
			</div>-->
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataIntelligencePlacement}" action="${ctx}/dataintelligenceplace/countPlacementPayInfo" method="post" class="form-inline hide" >
			<#form:hidden path="stype"/>
			<#form:hidden path="estateId"/>
			<#form:hidden path="estateName"/>
			<#form:hidden path="officeCode"/>
			<div class="form-group">
				<label class="control-label">${text('开始日期')}：</label>
				<div class="control-inline">
					<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('结束日期')}：</label>
				<div class="control-inline">
					<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
				</div>
			</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">${dataIntelligencePlacement.estateName}-交易金额统计</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
//# //加装电梯补助统计 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("工作单位")}', name:'officeName', index:'OFFICE_NAME', width:150, align:"left"},
		{header:'${text("交易金额(元)")}', name:'approvedRoomPrice', index:'APPROVED_BUILDING_PRICE', width:150, align:"left"}
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 页面加载完成后首次获取数据
			$(document).ready(function () {
						});
		});
	}
});
</script>