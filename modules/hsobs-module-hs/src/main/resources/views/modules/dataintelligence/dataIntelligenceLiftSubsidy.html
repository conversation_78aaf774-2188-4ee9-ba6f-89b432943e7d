<% layout('/layouts/default.html', {title: '加装电梯补助统计', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('加装电梯补助统计')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<!--<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>-->
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
			<#form:form id="searchForm" model="${dataIntelligenceLiftSubsidy}" action="${ctx}/dataintelligencelift/countLiftSubsidyStat" method="post" class="form-inline hide" >
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('地市')}：</label>
						<div class="control-inline">
							<#form:select path="city" id="citySelect" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control width-120 required" />
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('区域')}：</label>
						<div class="control-inline">
							<#form:select path="area" id="areaSelect" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control width-120 required" />
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('工作单位')}：</label>
						<div class="control-inline" >
							<#form:treeselect id="officeId" title="${text('工作单位')}"
							path="officeCode" labelPath=""
							url="${ctx}/sys/office/treeData${dataIntelligenceLiftSubsidy.parentCodeTree}" allowClear="true"/>
						</div>
					</div>
				</div>
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('开始日期')}：</label>
						<div class="control-inline">
							<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('结束日期')}：</label>
						<div class="control-inline">
							<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
						</div>
					</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		<#form:form id="LiftSubsidyTotal" model="${dataIntelligenceLiftSubsidy}" action="${ctx}/dataintelligencelift/countLiftSubsidyStatTotal" method="post" class="form-inline hide"
		data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
		</#form:form>
		</div>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body" style="height: 140px">
							<div class="box-header with-border">
								<h3 class="box-title">加装电梯总体补助统计</h3>
							</div>
							<table id="dataGridTotal"></table>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">加装电梯补助统计</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
//# //加装电梯补助统计 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("区域")}', name:'area', index:'AREA_CODE', width:150, align:"left"},
		{header:'${text("申请数")}', name:'applyTotalCount', index:'APPLY_TOTAL_COUNT', width:150, align:"left"},
		{header:'${text("审批通过数")}', name:'approvedCount', index:'APPROVED_COUNT', width:150, align:"left"},
		{header:'${text("申请资金(元)")}', name:'applyTotalSubsidyFund', index:'APPLY_TOTAL_SUBSIDY_FUND', width:150, align:"left"},
		{header:'${text("审批通过资金(元)")}', name:'approvedSubsidyFund', index:'APPROVED_SUBSIDY_FUND', width:150, align:"left"}
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 页面加载完成后首次获取数据
			$(document).ready(function () {
						});
		});
	}
});
$('#dataGridTotal').dataGrid({
	url: '${ctx}/dataintelligencelift/countLiftSubsidyStatTotal',
	columnModel: [
		{header:'${text("申请数")}', name:'applyTotalCount', index:'APPLY_TOTAL_COUNT', width:150, align:"left"},
		{header:'${text("审批通过数")}', name:'approvedCount', index:'APPROVED_COUNT', width:150, align:"left"},
		{header:'${text("申请资金(元)")}', name:'applyTotalSubsidyFund', index:'APPLY_TOTAL_SUBSIDY_FUND', width:150, align:"left"},
		{header:'${text("审批通过资金(元)")}', name:'approvedSubsidyFund', index:'APPROVED_SUBSIDY_FUND', width:150, align:"left"}
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	showRownum: false,
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});

$("#searchForm").unbind('submit').submit(function(e){
	// 添加成立再执行查询
	if (true) {
		var formData = $(this).serializeArray();
		console.log(formData)

		$("#dataGrid").dataGrid('refresh', 1);

		$('#dataGridTotal').jqGrid('setGridParam', { postData: formData });
		$('#dataGridTotal').trigger("reloadGrid");
		$("#dataGridTotal").dataGrid('refresh', 1);
	}
	return false;
});

$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/dataintelligencelift/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>

<script>
	$(document).ready(function() {

		$('#citySelect').change(function() {
			showAreaInfo();
		});
		function showAreaInfo() {

			let cityCode = $('#citySelect').val();

			let areaSelect = $('#areaSelect');
			areaSelect.empty();

			if (cityCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getAreaInfo',
					type: 'GET',
					data: {areaCode: cityCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						let option = $("<option>")
								.val('')
								.text('所有区域');
						$("#areaSelect").append(option);

						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.areaCode)
									.text(item.areaName);
							$("#areaSelect").append(option);
						});
					}
				});
			}
		}
	});
</script>