<% layout('/layouts/default.html', {title: '小区位置', libs: ['validate', 'fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text('小区位置')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsEstateAddress}" action="${ctx}/hsestateaddress/save" method="post" class="form-horizontal">
		<div class="box-body">
				<div class="row">
					<div id="mapContainer" class="col-sm-12" style="height: 600px; position: relative;">
						<!-- 悬浮搜索框 -->
<!--						<div class="map-search-box">-->
<!--							<div class="input-group">-->
<!--								<input type="text" id="searchAddress" class="form-control" placeholder="输入地址进行搜索">-->
<!--								<span class="input-group-btn">-->
<!--									<button class="btn btn-primary" type="button" id="btnSearch">-->
<!--										<i class="fa fa-search"></i>-->
<!--									</button>-->
<!--								</span>-->
<!--							</div>-->
<!--						</div>-->
						<div id="map" style="height: 100%;"></div>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<style>
	/* 地图搜索框样式 */
	.map-search-box {
		position: absolute;
		top: 10px;
		right: 20px;
		z-index: 999;
		width: 300px;
		box-shadow: 0 2px 6px rgba(0,0,0,0.3);
	}

	.map-search-box .input-group {
		width: 100%;
	}

	.map-search-box .form-control {
		border-radius: 2px 0 0 2px;
		border-right: none;
	}

	.map-search-box .btn {
		border-radius: 0 2px 2px 0;
		padding: 6px 12px;
	}

	/* 移动端适配 */
	@media (max-width: 768px) {
		.map-search-box {
			width: calc(100% - 40px);
			right: 10px;
			left: 10px;
		}
	}
</style>

<!--<script type="text/javascript" src="http://api.tianditu.gov.cn/api?v=4.0&tk=18fa918fd89912421adc846feab670ad"></script>-->

<link rel="stylesheet" href="${ctxStatic}/leaflet/leaflet.css" />
<link rel="stylesheet" href="${ctxStatic}/leaflet/L.Control.Zoomslider.css" />
<link rel="stylesheet" href="${ctxStatic}/leaflet/iconLayers.css">
<link rel="stylesheet" href="${ctxStatic}/leaflet/leaflet.draw.css" />
<script src="${ctxStatic}/leaflet/leaflet.js"></script>
<script src="${ctxStatic}/leaflet/leaflet-tilelayer-wmts.js"></script>
<script src="${ctxStatic}/leaflet/L.Control.Zoomslider.js"></script>
<script src="${ctxStatic}/leaflet/iconLayers.js"></script>
<script src="${ctxStatic}/leaflet/leaflet.draw.js"></script>
<script src="${ctxStatic}/leaflet/leaflet.geometryutil.js"></script>
<script src="${ctxStatic}/leaflet/leaflet.snap.js"></script>
<script>
let vec_fj_url = "${@Global.getConfig('tianditu.url')}/vec_fj/wmts";
let cva_fj_url = "${@Global.getConfig('tianditu.url')}/cva_fj/wmts";
let img_fj_url = "${@Global.getConfig('tianditu.url')}/img_fj/wmts";
let cia_fj_url = "${@Global.getConfig('tianditu.url')}/cia_fj/wmts";
let defaultLng = 119.291320;
let defaultLat = 26.077380;

$(function () {

	const initMap = () => {
		var vec_fj = new L.TileLayer.WMTS(vec_fj_url, {
			layer: "vec_fj",
			style: "default",
			tilematrixSet: "Matrix_0",
			format: "image/png",
			minZoom: 10,
			maxZoom: 18,
			attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
		});
		var cva_fj = new L.TileLayer.WMTS(cva_fj_url, {
			layer: "cva_fj",
			style: "default",
			tilematrixSet: "Matrix_0",
			format: "image/png",
			minZoom: 10,
			maxZoom: 18,
			attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
		});
		var img_fj = new L.TileLayer.WMTS(img_fj_url, {
			layer: "img_fj",
			style: "default",
			tilematrixSet: "Matrix_0",
			format: "image/png",
			minZoom: 10,
			maxZoom: 18,
			attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
		});
		var cia_fj = new L.TileLayer.WMTS(cia_fj_url, {
			layer: "cia_fj",
			style: "default",
			tilematrixSet: "Matrix_0",
			format: "image/png",
			minZoom: 10,
			maxZoom: 18,
			attribution: "<a href='http://www.fjmap.net'>天地图福建</a>&copy;"
		});
		var TDTLayers = new L.Control.IconLayers([{
			title: '矢量', // use any string
			layer: L.layerGroup([vec_fj, cva_fj]), // any ILayer
			icon: '${ctxStatic}/leaflet/LayerIcons/Vectory.png' // 80x80 icon
		}, {
			title: '影像',
			layer: L.layerGroup([img_fj, cia_fj]),
			icon: '${ctxStatic}/leaflet/LayerIcons/Satellite.png'
		}], {
			position: 'topright',
			maxLayersInRow: 5
		});

		var centerpoint = new L.LatLng(defaultLat, defaultLng);
		var mapOptions = {
			center: centerpoint,
			zoom: 7,
			tms: false,
			crs: L.CRS.EPSG4326,
			zoomsliderControl: false,
			zoomControl: false,
			attributionControl: false
		};
		var map = L.map('map', mapOptions).setView(centerpoint, 14);
		TDTLayers.addTo(map);

		if (${hsEstateAddress.latitude_d != null && hsEstateAddress.longitude_d != null}) {
			let lng, lat;
			lng = `${hsEstateAddress.longitude_d}`;
			lat = `${hsEstateAddress.latitude_d}`;
			const markersLayer = L.layerGroup().addTo(map);
			const marker = L.marker([Number(lat), Number(lng)]).addTo(markersLayer);
			marker.bindPopup(`【${hsEstateAddress.name}】` + '</br>' + `${hsEstateAddress.address}`);
			map.panTo([lat, lng], {
				animate: true,
				duration: 1.5,    // 过渡时间（秒）
				easeLinearity: 0.25 // 缓动系数
			});
		}
		L.control.scale().addTo(map);

		// var map = L.map('map', mapOptions).setView(centerpoint, 14);
		// TDTLayers.addTo(map);
		// if (${hsEstateAddress.latitude_d != null && hsEstateAddress.longitude_d != null}) {
		// 	let lng, lat;
		// 	lng = `${hsEstateAddress.longitude_d}`;
		// 	lat = `${hsEstateAddress.latitude_d}`;
		// 	const markersLayer = L.layerGroup().addTo(map);
		// 	const marker = L.marker([26.10377, 119.2904]).addTo(markersLayer);
		// 	// marker.bindPopup(`【${hsEstateAddress.name}】` + '</br>' + `${hsEstateAddress.address}`);
		// }
		// L.control.scale().addTo(map);

	};

	initMap();
});
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>