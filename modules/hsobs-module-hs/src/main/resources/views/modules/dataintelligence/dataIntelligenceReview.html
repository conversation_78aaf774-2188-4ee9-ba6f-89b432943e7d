<% layout('/layouts/default.html', {title: '公租房资格年审统计', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公租房资格年审统计')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<!--<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>-->
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataIntelligenceReview}" action="${ctx}/dataintelligencereview/resourceData" method="post" class="form-inline hide" >
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('地市')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="city" id="citySelect" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="area" id="areaSelect" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('小区')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="estateId" id="estateIdSelect" itemLabel="name" itemValue="id" blankOption="true" class="form-control required" />
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('开始日期')}：</label>
					<div class="control-inline">
						<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('结束日期')}：</label>
					<div class="control-inline">
						<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">资格年审统计情况</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="col-md-6">
								<div class="chart">
									<div id="pieChart" style="height:230px;width:100%"></div><script>
									$(function(){
										pieChart1 = echarts.init(document.getElementById('pieChart'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'资格异常类型分析'
											},
											tooltip: {
												trigger: 'item',
												formatter: '{b} {c}次'
											},
											legend: {
												top: '25%',
												right: '20%',
												orient: 'vertical'
											},
											series: [
												{
													name: '资格异常类型分析',
													type: 'pie',
													center: ['30%', '50%'],
													radius: ['40%', '70%'],
													avoidLabelOverlap: false,
													label: {
														show: false,
														position: 'center',
														formatter: '{d}%'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
													]
												}
											]
										};
										pieChart1.setOption(option);
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(pieChart1) pieChart1.resize();
	}).resize();
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		$(window).resize();
	});
	$('.ui-sortable').sortable({
		connectWith : '.ui-sortable',
		handle      : '.box-header, .nav-tabs',
		placeholder : 'sort-highlight',
		forcePlaceholderSize: true,
		zIndex : 999999
	}).find('.box-header, .nav-tabs').css('cursor', 'move');
});
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("小区名称")}', name:'estateName', index:'ESTATE_NAME', width:150, align:"left"},
		{header:'${text("总量")}', name:'totalCount', index:'TOTAL_COUNT', width:150, align:"left"},
		{header:'${text("正常量")}', name:'normalCount', index:'NORMAL_COUNT', width:150, align:"left"},
		{header:'${text("异常量")}', name:'abnormalCount', index:'ABNORMAL_COUNT', width:150, align:"left"},
		],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){

		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			function fetchReviewTypeCompare(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencereview/reviewCompare", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {

						// 更新图表数据
						pieChart1.setOption({
							series: [{
								data: data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchReviewTypeCompare(formData);
				// 定时获取数据（例如每秒获取一次数据）
				// setInterval(fetchData, 1000); // 可根据需要调整时间间隔
			});
		});

	}
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/dataintelligencereview/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>

<script>
	$(document).ready(function() {

		$('#citySelect').change(function() {
			showAreaInfo();
		});
		function showAreaInfo() {

			let cityCode = $('#citySelect').val();

			let areaSelect = $('#areaSelect');
			areaSelect.empty();

			let estateIdSelect = $('#estateIdSelect');
			estateIdSelect.empty();
			let option = $("<option>")
					.val('')
					.text('所有小区');
			$("#estateIdSelect").append(option);

			if (cityCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getAreaInfo',
					type: 'GET',
					data: {areaCode: cityCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						let option = $("<option>")
								.val('')
								.text('所有区域');
						$("#areaSelect").append(option);

						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.areaCode)
									.text(item.areaName);
							$("#areaSelect").append(option);
						});
					}
				});
			}
		}

		$('#areaSelect').change(function() {

			let estateIdSelect = $('#estateIdSelect');
			estateIdSelect.empty();

			let option = $("<option>")
					.val('')
					.text('所有小区');
			$("#estateIdSelect").append(option);

			let areaCode = $('#areaSelect').val();
			if (areaCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getEstateInfo',
					type: 'GET',
					data: {areaCode: areaCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.id)
									.text(item.name);
							$("#estateIdSelect").append(option);
						});
					}
				});
			}
		});
	});
</script>