<% layout('/layouts/default.html', {title: '公租房清退统计', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公租房清退统计')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<!--<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>-->
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
			<#form:form id="searchForm" model="${dataIntelligenceClearance}" action="${ctx}/dataintelligenceclearance/countClearanceStat" method="post" class="form-inline hide" >
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('地市')}：</label>
						<div class="control-inline">
							<#form:select path="city" id="citySelect" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control width-120 required" />
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('区域')}：</label>
						<div class="control-inline">
							<#form:select path="area" id="areaSelect" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control width-120 required" />
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('小区')}：</label>
						<div class="control-inline">
							<#form:select path="estateId" id="estateIdSelect" itemLabel="name" itemValue="id" blankOption="true" class="form-control width-120 required" />
						</div>
					</div>
				</div>
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('工作单位')}：</label>
						<div class="control-inline">
							<#form:treeselect id="officeId" title="${text('工作单位')}"
							path="officeCode" labelPath=""
							url="${ctx}/sys/office/treeData${dataIntelligenceClearance.parentCodeTree}" allowClear="true"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('开始日期')}：</label>
						<div class="control-inline">
							<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('结束日期')}：</label>
						<div class="control-inline">
							<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
						</div>
					</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		</div>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">公租房清退统计</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart1 = echarts.init(document.getElementById('barChart'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'公租房违规类型排名'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['违规户数']
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '公租房户数',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value} 户'
												}
											}
										],
										series: [
											{
												name: '违规户数',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 户';
													}
												},
												data: []
											}
										]
									};
									barChart1.setOption(option);
								});
							</script>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="col-md-6">
								<div class="chart">
									<div id="pieChartDiv1" style="height:320px;width:100%"></div><script>
									$(function(){
										pieChart1 = echarts.init(document.getElementById('pieChartDiv1'), chartTheme);
										var option = {
											title:{
												show:true,
												text:'违规类型分析'
											},
											tooltip: {
												trigger: 'item',
												formatter: '{b} {c}次'
											},
											legend: {
												top: '25%',
												right: '20%',
												orient: 'vertical'
											},
											series: [
												{
													name: '',
													type: 'pie',
													center: ['30%', '50%'],
													radius: ['40%', '70%'],
													avoidLabelOverlap: false,
													label: {
														show: false,
														position: 'center',
														formatter: '{d}%'
													},
													emphasis: {
														label: {
															show: true,
															fontSize: 14,
															fontWeight: 'bold'
														}
													},
													labelLine: {
														show: false
													},
													data: [
													]
												}
											]
										};
										pieChart1.setOption(option);

										pieChart1.on('click', function(params) {
											if (params.componentType === 'series' && params.seriesType === 'pie') {
											}
										});
									});
								</script>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
	var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
	$(function(){
		$(window).resize(function(){
			var footerHeight = $('.main-footer').outerHeight() || 0;
			var windowHeight = $(window).height();
			$('.content').css('min-height', windowHeight - footerHeight);
			if(barChart1) barChart1.resize();
			if(pieChart1) pieChart1.resize();
		}).resize();
		$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
			$(window).resize();
		});
		$('.ui-sortable').sortable({
			connectWith : '.ui-sortable',
			handle      : '.box-header, .nav-tabs',
			placeholder : 'sort-highlight',
			forcePlaceholderSize: true,
			zIndex : 999999
		}).find('.box-header, .nav-tabs').css('cursor', 'move');
	});
//# //公租房清退统计 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("小区名称")}', name:'estateName', index:'ESTATE_NAME', width:150, align:"left"},
		{header:'${text("公租房总数")}', name:'totalCount', index:'TOTAL_COUNT', width:150, align:"left"},
		{header:'${text("租赁到期户数")}', name:'arrearsCount', index:'ARREARS_COUNT', width:150, align:"left"},
		{header:'${text("租金拖欠户数")}', name:'maturityCount', index:'MATURITY_COUNT', width:150, align:"left"},
		{header:'${text("违规户数")}', name:'violationsCount', index:'VIOLATIONS_COUNT', width:150, align:"left"},
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 使用 jQuery 动态获取数据并更新图表
			function fetchPriceLimitPriceData(formData) {
				$.ajax({
					url: "${ctx}/dataintelligenceclearance/countClearanceTypeStat", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						pieChart1.setOption({
							series: [{
								data: data.data
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}

			function fetchClearanceType(formData) {
				$.ajax({
					url: "${ctx}/dataintelligenceclearance/countClearanceTypeTop", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart1.setOption({
							xAxis: [{//分类
								data: data.class
							}],
							yAxis: [{
								max: data.max,
								interval: data.interval
							}],
							//['违规户数']
							series: [{//违规户数
								name:data.type1.name,
								data: data.type1.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}

			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchPriceLimitPriceData(formData);
				fetchClearanceType(formData);
				// 定时获取数据（例如每秒获取一次数据）
				// setInterval(fetchData, 1000); // 可根据需要调整时间间隔
			});
		});
	}
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/dataintelligenceclearance/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>

<script>
	$(document).ready(function() {

		$('#citySelect').change(function() {
			showAreaInfo();
		});
		function showAreaInfo() {

			let cityCode = $('#citySelect').val();

			let areaSelect = $('#areaSelect');
			areaSelect.empty();

			let estateIdSelect = $('#estateIdSelect');
			estateIdSelect.empty();
			let option = $("<option>")
					.val('')
					.text('所有小区');
			$("#estateIdSelect").append(option);

			if (cityCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getAreaInfo',
					type: 'GET',
					data: {areaCode: cityCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						let option = $("<option>")
								.val('')
								.text('所有区域');
						$("#areaSelect").append(option);

						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.areaCode)
									.text(item.areaName);
							$("#areaSelect").append(option);
						});
					}
				});
			}
		}

		$('#areaSelect').change(function() {

			let estateIdSelect = $('#estateIdSelect');
			estateIdSelect.empty();

			let option = $("<option>")
					.val('')
					.text('所有小区');
			$("#estateIdSelect").append(option);

			let areaCode = $('#areaSelect').val();
			if (areaCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getEstateInfo',
					type: 'GET',
					data: {areaCode: areaCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.id)
									.text(item.name);
							$("#estateIdSelect").append(option);
						});
					}
				});
			}
		});
	});
</script>