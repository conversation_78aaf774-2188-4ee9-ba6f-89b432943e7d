<% layout('/layouts/default.html', {title: '总房源信息统计', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('总房源信息统计')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataIntelligenceTotal}" action="${ctx}/dataintelligencetotal/totalStatList" method="post" class="form-inline hide" >
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('地市')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="city" id="citySelect" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="area" id="areaSelect" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('小区')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="estateId" id="estateIdSelect" itemLabel="name" itemValue="id" blankOption="true" class="form-control required" />
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline width-250" >
						<#form:treeselect id="officeId" title="${text('工作单位')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData${dataIntelligenceTotal.parentCodeTree}" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</div>
			</#form:form>
			<div class="content pb0">
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart1 = echarts.init(document.getElementById('barChart'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'总体房源地市套数统计'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['公租房','限价房','公有住房']
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '总体房源套数统计',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value}'
												}
											}
										],
										series: [
											{
												name: '公租房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 套';
													}
												},
												data: []
											},
											{
												name: '限价房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 套';
													}
												},
												data: []
											},
											{
												name: '公有住房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 套';
													}
												},
												data: []
											}
										]
									};
									barChart1.setOption(option);
								});
							</script>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChartId2" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart2 = echarts.init(document.getElementById('barChartId2'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'总体房源地市面积统计'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['公租房','限价房','公有住房']
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '总体房源面积统计',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value}'
												}
											}
										],
										series: [
											{
												name: '公租房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: []
											},
											{
												name: '限价房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: []
											},
											{
												name: '公有住房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: []
											}
										]
									};
									barChart2.setOption(option);
								});
							</script>
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart3" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart3 = echarts.init(document.getElementById('barChart3'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'总体房源单位套数统计'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['公租房','限价房','公有住房']
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '总体房源套数统计',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value}'
												}
											}
										],
										series: [
											{
												name: '公租房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 套';
													}
												},
												data: []
											},
											{
												name: '限价房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 套';
													}
												},
												data: []
											},
											{
												name: '公有住房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 套';
													}
												},
												data: []
											}
										]
									};
									barChart3.setOption(option);
								});
							</script>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChartId4" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart4 = echarts.init(document.getElementById('barChartId4'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'总体房源单位面积统计'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['公租房','限价房','公有住房']
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '总体房源面积统计',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value}'
												}
											}
										],
										series: [
											{
												name: '公租房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: []
											},
											{
												name: '限价房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: []
											},
											{
												name: '公有住房',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' m²';
													}
												},
												data: []
											}
										]
									};
									barChart4.setOption(option);
								});
							</script>
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">总体房源信息表</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(barChart1) barChart1.resize();
		if(barChart2) barChart2.resize();
		if(barChart3) barChart3.resize();
		if(barChart4) barChart4.resize();
	}).resize();
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		$(window).resize();
	});
	$('.ui-sortable').sortable({
		connectWith : '.ui-sortable',
		handle      : '.box-header, .nav-tabs',
		placeholder : 'sort-highlight',
		forcePlaceholderSize: true,
		zIndex : 999999
	}).find('.box-header, .nav-tabs').css('cursor', 'move');
});

//# // 公租房
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("工作单位")}', name:'officeName', index:'OFFICE_NAME', width:200, align:"left"},
		{header:'${text("总套数")}', name:'roomTotalNumber', index:'TOTAL_ROOMS', width:150, align:"left"},
		{header:'${text("总面积(m²)")}', name:'roomTotalArea', index:'TOTAL_AREAS', width:150, align:"left"},
		{header:'${text("公租房套数")}', name:'roomRentalNumber', index:'RENTAL_ROOMS', width:150, align:"left"},
		{header:'${text("公租房面积(m²)")}', name:'roomRentalArea', index:'RENTAL_AREAS', width:150, align:"left"},
		{header:'${text("限价房套数")}', name:'roomPriceLimitNumber', index:'PRICELIMIT_ROOMS', width:150, align:"left"},
		{header:'${text("限价房面积(m²)")}', name:'roomPriceLimitArea', index:'PRICELIMIT_AREAS', width:150, align:"left"},
		{header:'${text("公有住房套数")}', name:'roomPublicNumber', index:'PUBLIC_ROOMS', width:150, align:"left"},
		{header:'${text("公有住房面积(m²)")}', name:'roomPublicArea', index:'PUBLIC_AREAS', width:150, align:"left"}
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){

		$(document).ready(function () {
			function fetchTotalRoomsStatByCity(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencetotal/totalRoomsStatByCity", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart1.setOption({
							xAxis: [{//分类
								data: data.class
							}],
							yAxis: [{
								max: data.max,
								interval: data.interval
							}],
							series: [{//公租房
								name:data.type1.name,
								data:data.type1.value
							},{//限价房
								name:data.type2.name,
								data:data.type2.value
							},{//公有住房
								name:data.type3.name,
								data:data.type3.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			function fetchTotalAreasStatByCity(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencetotal/totalAreasStatByCity", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart2.setOption({
							xAxis: [{//分类
								data: data.class
							}],
							yAxis: [{
								max: data.max,
								interval: data.interval
							}],
							series: [{//公租房
								name:data.type1.name,
								data:data.type1.value
							},{//限价房
								name:data.type2.name,
								data:data.type2.value
							},{//公有住房
								name:data.type3.name,
								data:data.type3.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}

			function fetchTotalRoomsStat(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencetotal/totalRoomsStat", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart3.setOption({
							xAxis: [{//分类
								data: data.class
							}],
							yAxis: [{
								max: data.max,
								interval: data.interval
							}],
							series: [{//公租房
								name:data.type1.name,
								data:data.type1.value
							},{//限价房
								name:data.type2.name,
								data:data.type2.value
							},{//公有住房
								name:data.type3.name,
								data:data.type3.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			function fetchTotalAreasStat(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencetotal/totalAreasStat", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart4.setOption({
							xAxis: [{//分类
								data: data.class
							}],
							yAxis: [{
								max: data.max,
								interval: data.interval
							}],
							series: [{//公租房
								name:data.type1.name,
								data:data.type1.value
							},{//限价房
								name:data.type2.name,
								data:data.type2.value
							},{//公有住房
								name:data.type3.name,
								data:data.type3.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}

			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				fetchTotalRoomsStatByCity(formData);
				fetchTotalAreasStatByCity(formData);
				fetchTotalRoomsStat(formData);
				fetchTotalAreasStat(formData);
				// 定时获取数据（例如每秒获取一次数据）
				// setInterval(fetchData, 1000); // 可根据需要调整时间间隔
			});
		});
	}
});

$("#searchForm").unbind('submit').submit(function(e){
	// 添加成立再执行查询
	if (true) {
		var formData = $(this).serializeArray();
		console.log(formData)

		$("#dataGrid").dataGrid('refresh', 1);
	}
	return false;
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/dataintelligencetotal/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>

<script>
	$(document).ready(function() {

		$('#citySelect').change(function() {
			showAreaInfo();
		});
		function showAreaInfo() {

			let cityCode = $('#citySelect').val();

			let areaSelect = $('#areaSelect');
			areaSelect.empty();

			let estateIdSelect = $('#estateIdSelect');
			estateIdSelect.empty();
			let option = $("<option>")
					.val('')
					.text('所有小区');
			$("#estateIdSelect").append(option);

			if (cityCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getAreaInfo',
					type: 'GET',
					data: {areaCode: cityCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						let option = $("<option>")
								.val('')
								.text('所有区域');
						$("#areaSelect").append(option);

						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.areaCode)
									.text(item.areaName);
							$("#areaSelect").append(option);
						});
					}
				});
			}
		}

		$('#areaSelect').change(function() {

			let estateIdSelect = $('#estateIdSelect');
			estateIdSelect.empty();

			let option = $("<option>")
					.val('')
					.text('所有小区');
			$("#estateIdSelect").append(option);

			let areaCode = $('#areaSelect').val();
			if (areaCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getEstateInfo',
					type: 'GET',
					data: {areaCode: areaCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.id)
									.text(item.name);
							$("#estateIdSelect").append(option);
						});
					}
				});
			}
		});
	});
</script>