<% layout('/layouts/default.html', {title: '加装电梯补助同比', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('加装电梯补助同比')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${dataIntelligenceLiftSubsidy}" action="${ctx}/dataintelligencelift/countLiftSubsidyStatEmpty" method="post" class="form-inline hide" >
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('地市')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="city" id="citySelect" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('区域')}：</label>
					<div class="control-inline" style="width:150px;">
						<#form:select path="area" id="areaSelect" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline width-250" >
						<#form:treeselect id="officeId" title="${text('工作单位')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData${dataIntelligenceLiftSubsidy.parentCodeTree}" allowClear="true"/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('同比类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="compareType" dictType="hs_compare_type" blankOption="false" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</div>
			</#form:form>
			<div class="content pb0">
				<div hidden="hidden" class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="box-header with-border">
								<h3 class="box-title">加装电梯补助统计</h3>
							</div>
							<table id="dataGrid"></table>
							<div id="dataGridPage"></div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChart" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart1 = echarts.init(document.getElementById('barChart'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'加装电梯申请同比'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['申请数','审批通过数']
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '加装电梯申请同比',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value}'
												}
											}
										],
										series: [
											{
												name: '申请数',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 申请';
													}
												},
												data: []
											},
											{
												name: '审批通过数',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 通过';
													}
												},
												data: []
											}
										]
									};
									barChart1.setOption(option);
								});
							</script>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="box box-widget">
						<div class="box-body">
							<div class="chart">
								<div id="barChartId2" style="height:430px;width:100%"></div><script>
								$(function(){
									barChart2 = echarts.init(document.getElementById('barChartId2'), chartTheme);
									var option = {
										tooltip: {
											trigger: 'axis',
											axisPointer: {
												type: 'cross',
												crossStyle: {
													color: '#999'
												}
											}
										},
										title:{
											show:true,
											text:'加装电梯补助费用同比'
										},
										toolbox: {
											feature: {
												restore: { show: true },
												saveAsImage: { show: true }
											}
										},
										legend: {
											data: ['补助费用']
										},
										xAxis: [
											{
												type: 'category',
												data: [],
												axisPointer: {
													type: 'shadow'
												}
											}
										],
										yAxis: [
											{
												type: 'value',
												name: '加装电梯补助费用同比',
												min: 0,
												max: 250,
												interval: 50,
												axisLabel: {
													formatter: '{value}'
												}
											}
										],
										series: [
											{
												name: '补助费用',
												type: 'bar',
												tooltip: {
													valueFormatter: function (value) {
														return value + ' 元';
													}
												},
												data: []
											}
										]
									};
									barChart2.setOption(option);
								});
							</script>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/echarts/5.5.1/echarts.min.js"></script>
<script>
var chartTheme = $('html').hasClass('skin-dark') > 0 ? 'dark' : 'light';
$(function(){
	$(window).resize(function(){
		var footerHeight = $('.main-footer').outerHeight() || 0;
		var windowHeight = $(window).height();
		$('.content').css('min-height', windowHeight - footerHeight);
		if(barChart1) barChart1.resize();
		if(barChart2) barChart2.resize();
	}).resize();
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		$(window).resize();
	});
	$('.ui-sortable').sortable({
		connectWith : '.ui-sortable',
		handle      : '.box-header, .nav-tabs',
		placeholder : 'sort-highlight',
		forcePlaceholderSize: true,
		zIndex : 999999
	}).find('.box-header, .nav-tabs').css('cursor', 'move');
});
//# //加装电梯补助统计 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	dataGridPage: $('#dataGridPage'),
	columnModel: [
		{header:'${text("工作单位")}', name:'officeName', index:'OFFICE_NAME', width:150, align:"left"},
		{header:'${text("申请总人数")}', name:'applyTotalCount', index:'APPLY_TOTAL_COUNT', width:150, align:"left"},
		{header:'${text("申请中人数")}', name:'applyingCount', index:'APPLYING_COUNT', width:150, align:"left"},
		{header:'${text("审核通过人数")}', name:'approvedCount', index:'APPROVED_COUNT', width:150, align:"left"},
		{header:'${text("申请总资金(元)")}', name:'applyTotalSubsidyFund', index:'APPLY_TOTAL_SUBSIDY_FUND', width:150, align:"left"},
		{header:'${text("申请中资金(元)")}', name:'applyingSubsidyFund', index:'APPLYING_SUBSIDY_FUND', width:150, align:"left"},
		{header:'${text("审核通过资金(元)")}', name:'approvedSubsidyFund', index:'APPROVED_SUBSIDY_FUND', width:150, align:"left"}
	],
	// 表格大小设置参数，autoGridHeight 和 autoGridWidth 可以是个函数，函数的返回值就是该表格的高度或宽度。
	autoGridHeight: true,   // 自动表格高度（设置为false后，不自动调整表格高度）为函数时返回'100%',则自动高度。
	autoGridHeightFix: 0,   // 自动表格高度宽度（自动调整高度时修正的高度值）
	//frozenCols: true, 	// 冻结列，锁定列，固定列，在 colModel 指定 frozen: true
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		// 页面加载完成后首次获取数据
		$(document).ready(function () {
			// 使用 jQuery 动态获取数据并更新图表
			function countClearanceCompare(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencelift/countLiftSubsidyCompare", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart1.setOption({
							xAxis: [{//分类
								data: data.class
							}],
							yAxis: [{
								max: data.max,
								interval: data.interval
							}],
							//['申请数','审批通过数']
							series: [{//申请数
								name:data.type1.name,
								data: data.type1.value
							},{//审批通过数
								name:data.type2.name,
								data: data.type2.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			function countClearancePayCompare(formData) {
				$.ajax({
					url: "${ctx}/dataintelligencelift/countLiftSubsidyPayCompare", // 替换为你的数据接口 URL
					type: 'POST',
					data: formData,
					dataType: 'json',
					success: function (data) {
						// 更新图表数据
						barChart2.setOption({
							xAxis: [{//分类
								data: data.class
							}],
							yAxis: [{
								max: data.max,
								interval: data.interval
							}],
							//['补助费用']
							series: [{//补助费用
								name:data.type1.name,
								data: data.type1.value
							}]
						});
					},
					error: function (error) {
						console.error('获取数据失败:', error);
					}
				});
			}
			// 页面加载完成后首次获取数据
			$(document).ready(function () {
				var formData = $('#searchForm').serialize();
				countClearanceCompare(formData);
				countClearancePayCompare(formData);
			});
		});
	}
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/dataintelligencelift/exportData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>

<script>
	$(document).ready(function() {

		$('#citySelect').change(function() {
			showAreaInfo();
		});
		function showAreaInfo() {

			let cityCode = $('#citySelect').val();

			let areaSelect = $('#areaSelect');
			areaSelect.empty();

			if (cityCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getAreaInfo',
					type: 'GET',
					data: {areaCode: cityCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						let option = $("<option>")
								.val('')
								.text('所有区域');
						$("#areaSelect").append(option);

						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.areaCode)
									.text(item.areaName);
							$("#areaSelect").append(option);
						});
					}
				});
			}
		}
	});
</script>