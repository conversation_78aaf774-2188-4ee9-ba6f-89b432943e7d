<% layout('/layouts/default.html', {title: '租赁资格轮候申请管理', libs: ['dataGrid']}){ %>
<style>
/* 筛选条件区域样式调整 */
.search-form-container {
    max-height: 200px; /* 4行的高度，每行约50px */
    overflow-y: auto;
    padding: 15px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    margin-bottom: 15px;
    background-color: #fafafa;
}

.search-form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    align-items: center;
}

.search-form-row .form-group {
    flex: 0 0 33.333333%; /* 每行3个条件 */
    max-width: 33.333333%;
    padding-right: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.search-form-row .form-group:nth-child(3n) {
    padding-right: 0;
}

.search-form-row .form-group .control-label {
    width: 120px;
    text-align: right;
    padding-right: 10px;
    margin-bottom: 0;
    white-space: nowrap;
    font-weight: normal;
}

.search-form-row .form-group .control-inline {
    flex: 1;
    min-width: 0;
}

.search-form-row .form-group .control-inline .form-control {
    width: 100%;
}

/* 按钮行单独处理 */
.search-button-row {
    display: flex;
    justify-content: flex-start;
    padding-top: 15px;
    border-top: 1px solid #e5e5e5;
    margin-top: 10px;
}

.search-button-row .btn {
    margin-right: 10px;
}

/* 表格列文字左对齐 */
.ui-jqgrid tr.jqgrow td {
    text-align: left !important;
}

/* 表格固定列样式 */
.fixed-table-container {
    position: relative;
    overflow: hidden;
}

/* 表格水平滚动条样式 */
.ui-jqgrid .ui-jqgrid-bdiv {
    overflow-x: auto;
}

/* 优化表格显示 */
.ui-jqgrid {
    border: 1px solid #ddd;
}

.ui-jqgrid .ui-jqgrid-htable th {
    background-color: #f5f5f5;
    font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .search-form-row .form-group {
        flex: 0 0 50%; /* 小屏幕时每行2个条件 */
        max-width: 50%;
    }
}

@media (max-width: 768px) {
    .search-form-row .form-group {
        flex: 0 0 100%; /* 更小屏幕时每行1个条件 */
        max-width: 100%;
        padding-right: 0;
    }

    .search-form-row .form-group .control-label {
        width: 100px;
    }
}
</style>
<div class="main-content">
    ${layoutContent!}
    <div class="box-body">
        <div class="search-form-container">
            <#form:form id="searchForm" model="${hsQwApply}" action="${ctx}/apply/hsQwApply/listQueryData" method="post" class="form-inline hide"
            data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
            <#form:hidden path="orderBy" defaultValue="a.update_date desc"/>

            <!-- 第一行：3个查询条件 -->
            <div class="search-form-row">
                <div class="form-group">
                    <label class="control-label">${text('申请单号')}：</label>
                    <div class="control-inline">
                        <#form:input path="idq" maxlength="20" class="form-control width-120"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">${text('小区名称')}：</label>
                    <div class="control-inline">
                        <#form:input path="hsQwApplyHouse.estate.name" maxlength="20" class="form-control width-120"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">${text('所属单位')}：</label>
                    <div class="control-inline">
                        <#form:treeselect id="office" title="${text('机构选择')}"
                        path="officeCode"
                        url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi!}"
                        class="required" allowClear="false" canSelectRoot="true" canSelectParent="false"/>
                    </div>
                </div>
            </div>

            <!-- 第二行：3个查询条件 -->
            <div class="search-form-row">
                <div class="form-group">
                    <label class="control-label">${text('主申请人')}：</label>
                    <div class="control-inline">
                        <#form:input path="mainApplyer.name" maxlength="20" class="form-control width-120"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">${text('主申请人身份证')}：</label>
                    <div class="control-inline">
                        <#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">${text('主申请人手机号')}：</label>
                    <div class="control-inline">
                        <#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
                    </div>
                </div>
            </div>

            <!-- 第三行：3个查询条件 -->
            <div class="search-form-row">
                <div class="form-group">
                    <label class="control-label">${text('申请单状态')}：</label>
                    <div class="control-inline width-120">
                        <#form:select path="status" dictType="sys_status" multiple="true" blankOption="true" class="form-control isQuick"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">${text('合同签约状态')}：</label>
                    <div class="control-inline width-120">
                        <#form:select path="compactSign" dictType="hs_qw_compact_sign" blankOption="true" class="form-control isQuick"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">${text('申请日期')}：</label>
                    <div class="control-inline">
                        <#form:input path="createDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                        dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="rentTime_lte.click()"/>
                        &nbsp;-&nbsp;
                        <#form:input path="createDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                        dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
                    </div>
                </div>
            </div>

            <#form:hidden path="applyMatter"/>
            <#form:hidden path="isQuery" defaultValue="true"/>

            <!-- 按钮行 -->
            <div class="search-button-row">
                <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
            </div>
        </#form:form>
        <div class="fixed-table-container">
            <table id="dataGrid"></table>
            <div id="dataGridPage"></div>
        </div>
    </div>
</div>
</div>
<% } %>
<script>
    //# // 初始化DataGrid对象
    $('#dataGrid').dataGrid({
        searchForm: $('#searchForm'),
        showCheckbox: true,
        shrinkToFit: false, // 禁用自动调整列宽
        autowidth: true,
        scroll: true, // 启用滚动条
        scrollOffset: 18,
        columnModel: [
            {header:'${text("申请单编号")}', name:'id',  sortable:false,  align:"left", frozen:true, width:150, formatter: function(val, obj, row, act){
                    return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑租赁资格轮候申请")}">' + (val || row.taskId) + '</a>';
                }},
            {header:'${text("小区名称")}', name:'hsQwApplyHouse.estate.name',  sortable:false, align:"left", width:120},
            {header:'${text("所属单位")}', name:'office.officeName', sortable:false, align:"left", width:150},
            {header:'${text("主申请人")}', name:'mainApplyer.name', sortable:false, align:"left", width:100},
            {header:'${text("主申请人身份证")}', name:'mainApplyer.idNum', sortable:false, align:"left", width:180},
            {header:'${text("主申请人手机号")}', name:'mainApplyer.phone',  sortable:false, align:"left", width:130},
            {header:'${text("申请单类型")}', name:'applyMatter',  sortable:false, align:"left", width:120, formatter: function(val, obj, row, act){
                    return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_matter')}", val, '${text("未知")}', true);
                }},
            {header:'${text("申请单状态")}', name:'status',  sortable:false, align:"left", width:120, formatter: function(val, obj, row, act){
                    return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
                }},
            {header:'${text("合同签约状态")}', name:'compactSign', sortable:false, align:"left", width:130, formatter: function(val, obj, row, act){
                    if (val==undefined || val==''){
                        return "未签约"
                    }
                    return js.getDictLabel("#{@DictUtils.getDictListJson('hs_qw_compact_sign')}", val, '${text("未知")}', true);
                }},
            <% if(hsQwApply.applyMatter=='0' || hsQwApply.applyMatter=='2') { %>
                {header:'${text("轮候评分")}', name:'applyScore',  sortable:false, align:"left", width:100, formatter: function(val, obj, row, act){
                    return '<a href="javascript:void(0);" onclick="showScoreDetail(\'' + row.id + '\');" title="查看评分详情">' + val + '</a>';
                }},
            <% } %>
            {header:'${text("申请时间")}', name:'createDate',  sortable:false, align:"left", width:150},
            {header:'${text("当前流程环节")}', name:'processName',  sortable:false, align:"left", width:150},
            // {header:'${text("备注信息")}', name:'remarks',  sortable:false,width:60, align:"left"},
            {header:'${text("操作")}', name:'actions', align:"left", width:150, frozen:true, frozenPosition:'right', formatter: function(val, obj, row, act){
                    var actions = [];
                    if(row.applyMatter != "3"){
                        actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=' + row.formKey + '&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程</a>&nbsp;');
                    }
                    if (row.processName == '房管机构确认' || row.processName == '申请人确认' || row.processName == '签订租赁合同' || row.processName == '确认合同信息' ) {
                        actions.push('<a href="${ctx}/apply/hsQwApply/genHouseSelectOrder?id=' + row.id + '" data-layer="true">选房确认单</a>&nbsp;');
                    }
                    return actions.join('');
                }}
        ],
        //# // 加载成功后执行事件
        ajaxSuccess: function(data){

        }
    }).on('click', '.bpmButton', function(){
        var $this = $(this).addClass('hsBtnList');
        js.ajaxSubmit($this.attr('href'), function(data){
            if (data.result == Global.TRUE){
                // 检查 pcUrl 是否可访问
                $.ajax({
                    url: data.pcUrl,
                    type: 'get',
                    success: function(response) {
                        // 如果请求成功，使用 js.addTabPage 打开新标签页
                        js.addTabPage($this, $this.attr('title'), data.pcUrl);
                    },
                    error: function(xhr, status, error) {
                        // 如果请求失败，显示错误信息
                        var errorMsg = '${text("加载失败")}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        }
                        js.showErrorMessage(errorMsg);
                    }
                });
            } else {
                js.showErrorMessage(data.message);
            }
        });
        return false;
    });
</script>

<script>
    function showScoreDetail(id) {
        $.ajax({
            url: '${ctx}/apply/hsQwApply/getScoreDetail',
            type: 'GET',
            data: {applyId: id.toString()},
            success: function(data) {
                if (data.length === 0) {
                    js.showMessage('暂无评分详情数据！');
                    return;
                }
                var html = '<table class="table table-bordered table-hover table-striped">';
                html += '<thead><tr><th style="width:60px">序号</th><th>规则类型</th><th>规则得分</th><th>得分明细</th></tr></thead>';
                html += '<tbody>';
                var totalScore = 0;
                for (var i = 0; i < data.length; i++) {
                    html += '<tr><td>' + (i+1) + '</td>' +
                        '<td>' + js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_score_type')}", data[i].ruleType, '未知', true) + '</td>' +
                        '<td>' + data[i].score + '</td>' +
                        '<td style="max-width: 300px; white-space: normal; overflow-wrap: break-word;" title="' + data[i].remarks + '">' + data[i].remarks + '</td>' +
                        '</tr>';
                    totalScore += parseFloat(data[i].score);
                }
                html += '<tr><td colspan="4" style="text-align: right; font-weight: bold;">汇总总计：' + totalScore + ' 分</td></tr>';
                if (data.length > 0) {
                    html += '<tr><td colspan="4">打分时间：' + data[0].createDate + '</td></tr>';
                }
                html += '</tbody></table>';
                layer.open({
                    type: 1,
                    title: '<i class="fa fa-list-alt"></i> 评分详情',
                    content: '<div style="max-width: 800px; overflow-x: auto;">' + html + '</div>',
                    area: ['800px', '500px'],
                    time: 0, // 添加默认time参数
                    skin: 'layui-layer-admin',
                    btn: ['关闭'],
                    success: function(layer){
                        $('.layui-layer-content').css({'padding':'15px','font-size':'14px'});
                    }
                });
            },
            error: function() {
                layer.msg('获取评分详情失败，请稍后重试！');
            }
        });
    }
    $('#btnExport').click(function(){
        // 保存当前页码和页大小
        var currentPageNo = $('#pageNo').val();
        var currentPageSize = $('#pageSize').val();

        // 设置最大页大小
        $('#pageSize').val(999999);

        js.ajaxSubmitForm($('#searchForm'), {
            url: '${ctx}/apply/hsQwApply/exportAllData',
            clearParams: '',  // 不清除任何参数
            downloadFile: true,
            success: function(data) {
                // 导出完成后恢复原来的页码和页大小
                $('#pageNo').val(currentPageNo);
                $('#pageSize').val(currentPageSize || 10); // 使用保存的值，如果没有则使用默认值10
            }
        });
    });
</script>