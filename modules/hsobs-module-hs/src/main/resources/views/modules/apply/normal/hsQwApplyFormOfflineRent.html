<% layout('/modules/apply/normal/hsQwApplyFormRecheckAudit.html', { isRead: hsQwApply.isRead , isHouse : '', canAudit: true}){ %>
<div class="form-unit">${text('线下配租')}</div>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('是否参与配租')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:radio path="offlineRent" value="${hsQwApply.offlineRent}" dictType="hs_apply_offline_rent_status" class="form-control required" readonly="${isRead!}"/>
            </td>
        </tr>
    </table>
</div>
${layoutContent!}
<% } %>
