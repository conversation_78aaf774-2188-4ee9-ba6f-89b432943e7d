<% layout('/modules/apply/normal/hsQwApplyFormCheckRead.html', { isRead: hsQwApply.isRead, isHouse : '', canAudit: true}){ %>
<div class="form-unit">${text('复查确认')}</div>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('确认信息')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:radio path="recheckStatus" value = "${hsQwApply.recheckStatus}" dictType="hs_apply_recheck_status" class="form-control required" readonly="${isRead!false}"/>
            </td>
        </tr>
    </table>
</div>
${layoutContent!}
<% } %>
