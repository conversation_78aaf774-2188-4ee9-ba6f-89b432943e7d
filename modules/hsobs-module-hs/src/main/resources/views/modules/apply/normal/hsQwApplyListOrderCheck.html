<% include('/modules/apply/include/applyOrderList.html',{first: false, applyStatusPage: @HsQwApply.APPLY_STATUS_AUDIT_CHECK_COMPLETE}){} %>

<script>
//# // 初始化DataGrid对象
$('#publicBtn').click(function () {
	var pids = $("#dataGrid").dataGrid('getSelectRows');
	if (pids == ''){
		js.showMessage("请至少选择一个申请单！", "公示单新增错误", "warning", 3000);
		return false;
	}
	console.log(pids);
	js.addTabPage(null, "复查公示", "${ctx}/applypublic/hsQwApplyPublic/form?pids="+ pids + "&publicType=1" );
});
</script>