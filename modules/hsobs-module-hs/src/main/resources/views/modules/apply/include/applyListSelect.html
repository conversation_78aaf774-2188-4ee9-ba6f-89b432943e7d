<% layout('/layouts/default.html', {title: '租赁资格轮候申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-body">
            <#form:form id="searchForm" model="${hsQwApply}" action="${ctx}${actionPath!}" method="post" class="form-inline"
            data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}" >
            <div class="form-group">
                <label class="control-label">${text('主申请人')}：</label>
                <div class="control-inline">
                    <#form:input path="mainApplyer.name" maxlength="20" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('主申请人身份证')}：</label>
                <div class="control-inline">
                    <#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('主申请人手机号')}：</label>
                <div class="control-inline">
                    <#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('备注信息')}：</label>
                <div class="control-inline">
                    <#form:input path="remarks" maxlength="500" class="form-control width-120"/>
                </div>
            </div>
            <#form:hidden path="status" defaultValue="0"/>
            <#form:hidden path="dataType"/>
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
            </div>
        </#form:form>
        <div class="row">
            <div class="col-xs-10 pr10">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
            <div class="col-xs-2 pl0">
                <div id="selectData" class="tags-input"></div>
            </div>
        </div>
    </div>
</div>
</div>
<% } %>
<script>
    var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
        selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
            searchForm: $('#searchForm'),
            columnModel: [
                {header:'${text("申请单编号")}', name:'id',  sortable:false, width:80, align:"center", frozen:true, formatter: function(val, obj, row, act){
                        return (val || row.id);
                    }},
                {header:'${text("申请单内容")}', name:'applyTitle',  sortable:false, width:130, align:"center", frozen:true, formatter: function(val, obj, row, act){
                        return (val || row.id) ;
                    }},
                {header:'${text("主申请人")}', name:'mainApplyer.name',  sortable:false, width:100, align:"center"},
                {header:'${text("主申请人身份证")}', name:'mainApplyer.idNum',  sortable:false, width:100, align:"center"},
                {header:'${text("主申请人手机号")}', name:'mainApplyer.phone',  sortable:false, width:60, align:"center"},
                {header:'${text("房源信息")}', name:'hsQwApplyHouse.simpleInfo',  width:80, align:"center"},
                {header:'${text("人均家庭收入")}', name:'avgIncome',  sortable:false, width:30, align:"left"},
                {header:'${text("轮候评分")}', name:'applyScore',  sortable:false, width:30, align:"center"},
                {header:'${text("申请单状态")}', name:'status', sortable:false, width:60, fixed:true, align:"center", formatter: function(val, obj, row, act){
                        return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '未知', true);
                    }},
                {header:'${text("备注信息")}', name:'remarks',  sortable:false, width:130, align:"center"},
                {header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
                        return JSON.stringify(row);
                    }}
            ],

            autoGridHeight: function(){
                var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
                $('.tags-input').height($('.ui-jqgrid').height() - 10);
                return height;
            },
            showCheckbox: '${parameter.checkbox}' == 'true',
            multiboxonly: false, // 单击复选框时再多选
            ajaxSuccess: function(data){
                $.each(selectData, function(key, value){
                    dataGrid.dataGrid('setSelectRow', key);
                });
                initSelectTag();
            },
            onSelectRow: function(id, isSelect, event){
                if ('${parameter.checkbox}' == 'true'){
                    if(isSelect){
                        selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
                    }else{
                        delete selectData[id];
                    }
                }else{
                    selectData = {};
                    selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
                }
                initSelectTag();
            },
            onSelectAll: function(ids, isSelect){
                if ('${parameter.checkbox}' == 'true'){
                    for (var i=0; i<ids.length; i++){
                        if(isSelect){
                            selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
                        }else{
                            delete selectData[ids[i]];
                        }
                    }
                }
                initSelectTag();
            },
            ondblClickRow: function(id, rownum, colnum, event){
                if ('${parameter.checkbox}' != 'true'){
                    js.layer.$('#' + window.name).closest('.layui-layer')
                        .find(".layui-layer-btn0").trigger("click");
                }
                initSelectTag();
            }
        });
    function initSelectTag(){
        selectNum = 0;
        var html = [];
        $.each(selectData, function(key, value){
            selectNum ++;
            html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.applyTitle||value.id)+' </span>'
                + '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
        });
        html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
        $('#selectData').empty().append(html.join(''));
    }
    function removeSelectTag(key){
        delete selectData[key];
        dataGrid.dataGrid('resetSelection', key);
        $('#selectNum').html(--selectNum);
        $('#'+key+'_tags-input').remove();
    }
    function getSelectData(){
        return selectData;
    }
</script>