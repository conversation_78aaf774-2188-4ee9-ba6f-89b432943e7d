<% layout('/modules/apply/info/hsQwApplyInfoForm.html', { isRead: hsQwApply.isRead , isHouse : '',canAudit: true}){ %>
    <div class="form-unit">${text('申请资格判定')}</div>
    <div class="hs-table-div">
        <table class="table-form hs-table-form">
            <tr>
                <td class="form-label hs-form-label">
                    <span class="required ">*</span> ${text('是否满足资格')}：<i class="fa icon-question hide"></i>
                </td>
                <td>
                    <#form:radio path="eligible" dictType="hs_qw_apply_check" class="form-control required" readonly="${isRead!}" value ="${hsQwApply.eligible}"/>
                </td>
            </tr>
        </table>
    </div>
    ${layoutContent!}
<% } %>
