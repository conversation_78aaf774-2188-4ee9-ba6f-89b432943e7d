<% layout('/layouts/default.html', {title: '租赁绿色通道申请管理' , libs: ['validate','fileupload','dataGrid']}){ %>
    <div class="main-content">
        <div class="box box-main">
            <div class="box-header with-border">
                <div class="box-title">
                    <i class="fa icon-note"></i> ${text(hsQwApply.isNewRecord ? '新增租赁绿色通道申请' : '编辑租赁绿色通道申请')}
                </div>
                <div class="box-tools pull-right hide">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse"><i
                            class="fa fa-minus"></i></button>
                </div>
            </div>
            <#form:form id="inputForm" model="${hsQwApply}" action="${ctx}/apply/hsQwApplyGreen/save" method="post"
                class="form-horizontal">
                <div class="box-body hs-box-body-bpm">
                    <div class="form-unit">${text('基本信息')}</div>
                    <#form:hidden path="id" />
                    <div class="hs-table-div" >
                        <table class="table-form hs-table-form" >
                            <tr>
                                <td class="form-label hs-form-label">
                                    <span class="required ">*</span> ${text('归属机构')}：<i
                                    class="fa icon-question hide"></i>
                                </td>
                                <td>
                                    <#form:treeselect id="officeCode" title="${text('机构选择')}" path="officeCode"
                                    labelPath="office.officeName"
                                    url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}" class="required"
                                    allowClear="false" canSelectRoot="true"
                                    readonly="${hsQwApply.isNewRecord?'false':'true'}" canSelectParent="false" />
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="form-unit">${text('申请人信息')}</div>
                    <div class="form-unit-wrap table-form">
                        <div class="hs-table-div" >
                            <table class="table-form hs-table-form" >
                                <#hs:applyerSelect path="hsQwApplyerList" value="${toJson(hsQwApply.hsQwApplyerList)}" />
                            </table>
                        </div>
                    </div>

                    <div class="form-unit">${text('房租配租信息')}</div>
                    <div class="form-unit-wrap table-form"></div>
                        <div class="hs-table-div" >
                            <table class="table-form hs-table-form" >
                                <tr>
                                    <td class="form-label hs-form-label">
                                        <span class="required ">*</span> ${text('选择房源')}：<i
                                        class="fa icon-question hide"></i>
                                    </td>
                                    <td>
                                        <% if (hsQwApply.isNewRecord || hsQwApply.status == '9') {%>
                                            <#form:listselect id="publicHouse" title="房源选择选择"
                                                    path="houseId" labelPath="hsQwApplyHouse.simpleInfo" class="required"
                                                    url="${ctx}/house/hsQwPublicRentalHouse/houseSelectByType?dataType=1" allowClear="true"
                                                    checkbox="false" itemCode="id" itemName="simpleInfo" readonly="${isRead!false}"/>
                                        <% } else {%>
                                                <a href="${ctx}/house/hsQwPublicRentalHouse/form?id=${hsQwApply.hsQwApplyHouse.id}"
                                                    class="hsBtnList" data-title="${text(" 查看租赁公租房房源房源信息表")}">
                                                    ${hsQwApply.hsQwApplyHouse.simpleInfo}
                                                </a>
                                        <% } %>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    </div>
                    ${layoutContent!}
                </div>
                <div class="box-footer">
                    <div class="row">
                        <div class="col-sm-offset-2 col-sm-10">
                            <% if (hasPermi('apply:hsQwApply:edit')){ %>
                                <#form:hidden path="status" />
                                <#form:hidden path="applyMatter" defaultValue="3" />
                                <#form:hidden path="familyIncome" defaultValue="0" />
                                <#form:hidden path="familyPeoples" defaultValue="0" />
                                <#form:hidden path="processName" defaultValue="租赁绿色通道" />

                                <% if (hsQwApply.isNewRecord ){ %>
                                    <button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i
                                            class="fa fa-save"></i> 暂 存</button>&nbsp;

                                    <% } %>
                            <button type="submit" class="btn btn-sm btn-primary mr3" id="btnSubmit"><i
                                    class="fa fa-check"></i> 提 交</button>&nbsp;
                                        <% } %>
                                            <button type="button" class="btn btn-sm btn-default" id="btnCancel"
                                                onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i>
                                                ${text('关 闭')}</button>
                        </div>
                    </div>
                </div>
            </#form:form>
        </div>
    </div>
    <% } %>
        <script>
            //# // 初始化租赁资格轮候申请人DataGrid对象
            $('#hsQwApplyerDataGrid').dataGrid({

                data: "#{toJson(hsQwApply.hsQwApplyerList)}",
                datatype: 'local', // 设置本地数据
                autoGridHeight: function () { return 'auto' }, // 设置自动高度

                //# // 设置数据表格列
                columnModel: [
                    { header: '状态', name: 'status', editable: true, hidden: true },
                    { header: '主键', name: 'id', editable: true, hidden: true },
                    { header: '${text("申请人姓名")}', name: 'name', width: 150, editable: true, edittype: 'text', editoptions: { 'maxlength': '100', 'class': 'form-control required realName' } },
                    {
                        header: '${text("参加工作时间")}', name: 'workTime', width: 150,
                        formatter: 'date', formatoptions: { srcformat: 'Y-m', newformat: 'Y-m' },
                        editable: true, edittype: 'text', editoptions: {
                            'class': 'form-control laydate required', 'readonly': 'true',
                            dataInit: function (element) {
                                laydate.render({ elem: element, type: 'datetime', format: 'yyyy-MM' });
                            }
                        }
                    },
                    { header: '${text("工龄")}', name: 'workAge', width: 150, editable: true, edittype: 'text', editoptions: { 'maxlength': '255', 'class': 'form-control' } },
                    { header: '${text("工作单位")}', name: 'organization', width: 150, editable: true, edittype: 'text', editoptions: { 'maxlength': '255', 'class': 'form-control required' } },
                    { header: '${text("身份证号")}', name: 'idNum', width: 150, editable: true, edittype: 'text', editoptions: { 'maxlength': '20', 'class': 'form-control required idcard' } },
                    { header: '${text("手机号")}', name: 'phone', width: 150, editable: true, edittype: 'text', editoptions: { 'maxlength': '20', 'class': 'form-control required mobile' } },
                    { header: '${text("职务")}', name: 'workPosition', width: 150, editable: true, edittype: 'text', editoptions: { 'maxlength': '255', 'class': 'form-control required' } },
                    { header: '${text("年收入")}', name: 'annualIncome', width: 150, editable: true, edittype: 'text', editoptions: { 'maxlength': '20', 'class': 'form-control required' } },
                    { header: '${text("备注信息")}', name: 'remarks', width: 150, editable: true, edittype: 'textarea', editoptions: { 'maxlength': '500', 'class': 'form-control', 'rows': '1' } },
                    {
                        header: '${text("婚姻状况")}', name: 'marryStatus', width: 100, editable: true, edittype: 'select', editoptions: {
                            'class': 'form-control',
                            items: $.merge([{ dictLabel: '&nbsp;', dictValue: '' }], "#{@DictUtils.getDictListJson('hs_applyer_marry')}"),
                            itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function (element) {
                                js.select2(element).on("change", function () {
                                    $(this).resetValid()
                                });
                            }
                        }
                    },
                    {
                        header: '${text("死亡时间")}', name: 'deathDate', width: 150, formatter: 'date', formatoptions: { srcformat: 'Y-m', newformat: 'Y-m' },
                        editable: true, edittype: 'text', editoptions: {
                            'class': 'form-control laydate', 'readonly': 'true',
                            dataInit: function (element) {
                                laydate.render({ elem: element, type: 'datetime', format: 'yyyy-MM-dd' });
                            }
                        }
                    },
                    { header: '${text("人员类型")}', name: 'userType', width: 150, editable: true, edittype: 'text', editoptions: { 'maxlength': '20', 'class': 'form-control' } },
                    {
                        header: '${text("申请人角色")}', name: 'applyRole', width: 100,
                        editable: true, edittype: 'select', editoptions: {
                            'class': 'form-control',
                            items: $.merge([{ dictLabel: '&nbsp;', dictValue: '' }], "#{@DictUtils.getDictListJson('hs_applyer_role')}"),
                            itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function (element) {
                                js.select2(element).on("change", function () {
                                    $(this).resetValid()
                                });
                            }
                        }
                    },
                    {
                        header: '${text("操作")}', name: 'actions', width: 80, align: 'center', formatter: function (val, obj, row, act) {
                            var actions = [];
                            if (val == 'new') {
                                actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyerDataGrid\').dataGrid(\'delRowData\',\'' + obj.rowId + '\')});return false;">删除</a>&nbsp;');
                            } else {
                                actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyerDataGrid\').dataGrid(\'setRowData\',\'' + obj.rowId + '\',null,{display:\'none\'});$(\'#' + obj.rowId + '_status\').val(\'' + Global.STATUS_DELETE + '\');});return false;">删除</a>&nbsp;');
                            }
                            return actions.join('');
                        }, editoptions: { defaultValue: 'new' }
                    }
                ],

                //# // 编辑表格参数
                editGrid: ${ hsQwApply.isNewRecord },				// 是否是编辑表格
                editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
                editGridAddRowBtn: $('#hsQwApplyerDataGridAddRowBtn'),	// 子表增行按钮
                editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
                editGridAddRowInitData: { id: '', status: Global.STATUS_NORMAL, applyRole: '0' },	// 新增行的时候初始化的数据

                //# // 编辑表格的提交数据参数
                editGridInputFormListName: 'hsQwApplyerList', // 提交的数据列表名
                editGridInputFormListAttrs: 'status,id,userId,name,workTime,organization,idNum,phone,workPosition,workTitle,annualIncome,createBy,createDate,updateBy,updateDate,remarks,applyId,applyRole,', // 提交数据列表的属性字段

                //# // 加载成功后执行事件
                ajaxSuccess: function (data) {

                }
	});
        </script>
        <script>
            // 业务实现草稿按钮
            $('#btnDraft').click(function () {
                $('#status').val(Global.STATUS_DRAFT);
            });

            // 业务实现草稿按钮
            $('#btnSubmit').click(function () {
                $('#status').val(Global.STATUS_NORMAL);
            });
            // 表单验证提交事件
            $('#inputForm').validate({
                submitHandler: function (form) {
                    js.ajaxSubmitForm($(form), function (data) {
                        js.showMessage(data.message);
                        if (data.result == Global.TRUE) {
                            js.closeCurrentTabPage(function (contentWindow) {
                                contentWindow.page();
                            });
                        }
                    }, "json");
                }
            });

            // function onTabPageClose(tabId, title){
            // 	console.log("ffffffff");
            // 	js.getPrevTabPage(function(contentWindow){
            // 		contentWindow.page();
            // 	}, true);
            // }

        </script>