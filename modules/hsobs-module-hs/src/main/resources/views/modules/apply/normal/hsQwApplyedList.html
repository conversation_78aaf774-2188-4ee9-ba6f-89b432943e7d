<% layout('/layouts/default.html', {title: '资格核查管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> 资格核查列表
            </div>
            <div class="box-tools pull-right">
                <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
                <% if(hasPermi('checkrecord:hsQwCheckRecord:add')){ %>
                    <button type="button" id="checkBtn"  onclick="return false;" class="btn"><i class="fa fa-plus"></i> ${text('新增资格核查')}</button>
                <% } %>
                <a href="#"  class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
            </div>
        </div>
        <div class="box-body">
            <#form:form id="searchForm" model="${hsQwApply}" action="${ctx}/apply/hsQwApply/listApplyedData" method="post" class="form-inline hide"
            data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
            <div class="form-group">
                <label class="control-label">${text('主申请人')}：</label>
                <div class="control-inline">
                    <#form:input path="mainApplyer.name" maxlength="20" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('主申请人身份证')}：</label>
                <div class="control-inline">
                    <#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('主申请人手机号')}：</label>
                <div class="control-inline">
                    <#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label">${text('小区')}：</label>
                <div class="control-inline">
                    <#form:select path="hsQwApplyHouse.estate.id" items="${@com.hsobs.hs.modules.estate.util.EstateUtils.getEstateList()}" itemLabel="name" itemValue="id" blankOption="true" class="form-control required" />
                </div>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
            </div>
        </#form:form>
        <table id="dataGrid"></table>
        <div id="dataGridPage"></div>
    </div>
</div>
</div>
<% } %>
<script>
    //# // 初始化DataGrid对象
    $('#dataGrid').dataGrid({
        searchForm: $('#searchForm'),
        showCheckbox: true,
        columnModel: [
            {header:'${text("申请单编号")}', name:'id',  sortable:false, width:80, align:"center", frozen:true, formatter: function(val, obj, row, act){
                return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑租赁资格轮候申请")}">' + (val || row.id) + '</a>';
            }},
            {header:'${text("主申请人姓名")}', name:'mainApplyer.name',  sortable:false, width:100, align:"center"},
            {header:'${text("主申请人身份证")}', name:'mainApplyer.idNum',  sortable:false, width:100, align:"center"},
            {header:'${text("主申请人手机号")}', name:'mainApplyer.phone',  sortable:false, width:60, align:"center"},
            {header:'${text("小区")}', name:'hsQwApplyHouse.estate.name',  width:130, align:"center"},
            {header:'${text("房号")}', name:'hsQwApplyHouse.unitNum', width:130, align:"center"},
            {header:'${text("合同到期时间")}', name:'compact.endDate',width:130, align:"center"},
        ],
        //# // 加载成功后执行事件
        ajaxSuccess: function(data){

        }
    })
</script>

<script>
    //# // 初始化DataGrid对象
    $('#checkBtn').click(function () {
        var pids = $("#dataGrid").dataGrid('getSelectRows');
        if (pids == ''){
            js.showMessage("请至少选择一个申请单！", "资格核查新增错误", "warning", 3000);
            return false;
        }
        console.log(pids);
        js.addTabPage(null, "资格核查清单", "${ctx}/applypublic/hsQwApplyPublic/form?pids="+ pids + "&publicType=0" );
    });
</script>