<% layout('/layouts/default.html', {title: '租赁资格轮候申请管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<% if(!hsQwApply.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsQwApply}" title="公租房申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsQwApply}" formKey="${formKey!'rent_apply'}" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i>
				<% if (hsQwApply.applyMatter=='0') {%>
					${text('赁资格轮候申请')}
				<% } else if(hsQwApply.applyMatter=='1'){%>
					${text('个人承租变更申请')}
				<% } else if(hsQwApply.applyMatter=='2'){%>
					${text('个人承租置换申请')}
				<% } %>
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwApply}" action="${ctx}/apply/hsQwApply/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
                <div class="hs-table-div" >
                    <table class="table-form hs-table-form" >
                        <#form:hidden path="id"/>
                        <#hs:apply readonly="${isRead}" entity="${hsQwApply}"/>
                        <% if (hsQwApply.applyMatter!='0') {%>
                            <tr>
                                <td class="form-label hs-form-label">
                                    <span class="required ">*</span> ${text('申请变更理由')}：<i class="fa icon-question hide"></i>
                                </td>
                                <td colspan="3">
                                    <#form:textarea path="changeReason" rows="4" readonly="${isRead}" maxlength="500" class="form-control"/>
                                </td>
                            </tr>
                        <% } %>
                    </table>
                </div>

				<div class="form-unit">${text('租赁资格轮候申请人')}</div>
				<#hs:applyer path="hsQwApplyerList" value="${toJson(hsQwApply.hsQwApplyerList)}" readonly="${isRead}" />
				<div class="form-unit">${text('租赁资格轮候申请人房屋情况表')}</div>
				<#hs:applyhouse value="${toJson(hsQwApply.hsQwApplyHouseList)}" readonly="${isRead}" />
				<div class="form-unit">${text('证明材料')}</div>
				<#hs:applyfile readonly="${isRead}" entity="${hsQwApply}" />
				${layoutContent}
				<% if (canAudit!false) {%>
				<div class="form-unit">${text('审核操作')}</div>
				<div class="hs-table-div" >
                        <table class="table-form hs-table-form" >
                            <tr>
                                <td class="form-label hs-form-label">
                                    ${text('审批意见：')}：
                                </td>
                                <td colspan="3">
                                    <#bpm:comment bpmEntity="${hsQwApply}" showCommWords="true" />
                                </td>
                            </tr>
                        </table>
                    </div>
				<% } %>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (canAudit!false) {%>
							<% if (hasPermi('apply:hsQwApply:edit')){ %>
								<#form:hidden path="status"/>
<!--								<#form:hidden path="processName" defaultValue = "${applyStatusPage!}"/>-->
								<% if (hsQwApply.isNewRecord || hsQwApply.status == '0'){ %>
									<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
								<% } %>
								<#hobpm:button bpmEntity="${hsQwApply}" formKey="rent_apply" completeText="提 交"/>
							<% } %>
						<% } else {%>
							<a href="${ctx}/bpm/bpmRuntime/trace?formKey=rent_apply&bizKey=${hsQwApply.id}" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪流程图</a>
						<% } %>

						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script src="${ctxStatic}/form-compare/form-compare.js"></script>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
	$('#processName').val('草稿');
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
	$("#houseIdName").val('');
};
// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});


</script>
<script>
// 初始化表单比对
$(document).ready(function() {
    // 获取后台传递的变更信息，并进行判空处理
    var changeMap = {};
    <% if (hsQwApply != null && hsQwApply.changeMap != null) { %>
        try {
            changeMap = ${toJson(hsQwApply.changeMap)};
        } catch (e) {
            console.log('变更信息解析失败', e);
        }
    <% } %>
    
    // 初始化表单比对
    $('#inputForm').formCompare({
        changeMap: changeMap,
		onDismiss: function(params) {
			// params.element: 发生变更的元素
			// params.message: 变更信息
			// params.success: 成功回调函数
			// params.error: 错误回调函数

			// 调用后台API删除记录
			$.ajax({
				url: '${ctx}/formalarm/hsQwFormAlarm/dismiss',
				type: 'POST',
				data: {
					objectId: '${hsQwApply.id}',
					attrKey: params.key,
					message: params.message
				},
				success: function(response) {
					if (response.result) {
						params.success();
                        // 刷新页面
                        location.reload();
					} else {
						params.error(response.message);
					}
				},
				error: function(xhr) {
					params.error(xhr.responseText);
				}
			});
		},
		confirmMessage: '确定要忽略这条提醒吗？' // 可选，自定义确认提示文字

	});
});
    </script>