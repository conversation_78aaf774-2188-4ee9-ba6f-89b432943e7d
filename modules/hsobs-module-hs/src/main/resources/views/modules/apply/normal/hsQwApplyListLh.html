<% include('/modules/apply/include/applyList.html', {formUrl: '/apply/hsQwApply/listAuditData',
isQueryStatus: false, applyStatusPage: @HsQwApply.APPLY_STATUS_FIRST_PUBLIC_COMPLETE,
title: '租赁资格轮候表'  , isAdd: false, isLh: true,orderBy:'a.apply_score desc, a.PRIORITY_ORDER desc' }){} %>

<script>
    //# // 初始化DataGrid对象
    $('#btnExportBySelect').click(function () {
        // 保存当前页码和页大小
        var currentPageNo = $('#pageNo').val();
        var currentPageSize = $('#pageSize').val();
        var pids = $("#dataGrid").dataGrid('getSelectRows');
        if (pids == ''){
            js.showMessage("请至少选择一个申请单！", "资格轮候名单导出错误", "warning", 3000);
            return false;
        }
        console.log(pids);
        js.ajaxSubmitForm($('#searchForm'), {
            url: '${ctx}/apply/hsQwApply/exportDataBySelected?pids=' + pids,
            clearParams: '',  // 不清除任何参数
            downloadFile: true,
            success: function(data) {
                // 导出完成后恢复原来的页码和页大小
                $('#pageNo').val(currentPageNo);
                $('#pageSize').val(currentPageSize || 10); // 使用保存的值，如果没有则使用默认值10
            }
        });
    });
</script>
