<% layout('/layouts/default.html', {title: '租赁资格轮候申请管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwApply.isNewRecord ? '新增租赁资格轮候申请' : '编辑租赁资格轮候申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwApply}" action="${ctx}/apply/hsQwApply/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
					<#hs:apply entity="${hsQwApply}"/>
					</table>
				</div>
				<div class="form-unit">${text('租赁资格轮候申请人')}</div>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<#hs:applyer path="hsQwApplyerList" value="${toJson(hsQwApply.hsQwApplyerList)}"  />
					</table>
				</div>
				<div class="form-unit">${text('租赁资格轮候申请人房屋情况表')}</div>
				<div class="form-unit-wrap table-form">
					<div class="hs-table-div" >
						<table class="table-form hs-table-form" >
							<#hs:applyhouse path="hsQwApplyHouseList" value="${toJson(hsQwApply.hsQwApplyHouseList)}" />
						</table>
					</div>
				</div>
				<div class="form-unit">${text('证明材料')}</div>
				<#hs:applyfile  entity="${hsQwApply}" />
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-xs-2">审批意见：</label>
							<div class="col-xs-10">
								<#bpm:comment bpmEntity="${hsQwApply}" showCommWords="true" />
							</div>
						</div>
					</div>
				</div>
<!--				<#bpm:nextTaskInfo bpmEntity="${hsQwApply}" />-->

			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('apply:hsQwApply:edit')){ %>
							<#form:hidden path="status"/>
						    <#form:hidden path="applyMatter" defaultValue="0"/>
							<% if (hsQwApply.isNewRecord || hsQwApply.status == '9'){ %>
								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
							<% } %>
							<#hobpm:button bpmEntity="${hsQwApply}" formKey="rent_apply" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>

<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
	$('#processName').val('草稿');
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};
// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>
<script src="${ctxStatic}/form-compare/form-compare.js"></script>

<script>
	// 初始化表单比对
	$(document).ready(function() {
		// 获取后台传递的变更信息，并进行判空处理
		var changeMap = {};
		<% if (hsQwApply != null && hsQwApply.changeMap != null) { %>
			try {
				changeMap = ${toJson(hsQwApply.changeMap)};
			} catch (e) {
				console.log('变更信息解析失败', e);
			}
			<% } %>

		// 初始化表单比对
		$('#inputForm').formCompare({
			changeMap: changeMap,
			onDismiss: function(params) {
				// params.element: 发生变更的元素
			},
			confirmMessage: '您无权限执行该操作？' // 可选，自定义确认提示文字

		});
	});
    </script>
