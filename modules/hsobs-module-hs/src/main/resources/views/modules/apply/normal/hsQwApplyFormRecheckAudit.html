<% layout('/modules/apply/normal/hsQwApplyFormRecheck.html', { isRead: hsQwApply.isRead , isHouse : '', canAudit: true}){ %>
<div class="form-unit">${text('复查审核')}</div>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('审核信息')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:radio path="recheckAudit" value = "${hsQwApply.recheckAudit}" dictType="hs_apply_recheck_audit" class="form-control required" readonly="${isRead!false}"/>
            </td>
        </tr>
    </table>
</div>
${layoutContent!}
<% } %>