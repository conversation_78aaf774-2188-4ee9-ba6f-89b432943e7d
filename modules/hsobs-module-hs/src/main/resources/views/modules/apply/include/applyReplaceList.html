<% layout('/layouts/default.html', {title: '个人住房变更管理', isAdd: false, libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="nav-tabs-custom nav-main">
		<ul class="nav nav-tabs">
			<% if ( first ){ %>
			<li  class="active" >
				<% } else {%>
			<li>
				<% } %>
				<a href="${ctx}/apply/hsQwApplyReplace/listReplace?isAdd=true"><i class="fa icon-energy"></i> ${text('审批待办')}</a></li>
			<% if ( !first ){ %>
			<li  class="active" >
				<% } else {%>
			<li>
				<% } %>
				<a href="${ctx}/apply/hsQwApplyReplace/listReplaceDone"><i class="fa icon-book-open"></i> ${text('已办信息')}</a></li>
		</ul>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwApply}" action="${ctx}${listDataUrl}" method="post" class="form-inline"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('主申请人')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.name" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('主申请人身份证')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('主申请人手机号')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
					</div>
				</div>

				<div class="form-group">
					<label class="control-label">${text('流程环节')}：</label>
					<div class="control-inline width-120">
						<#form:select path="processName" dictType="hs_apply_personal_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>

				<#form:hidden path="applyMatter" defaultValue="4" />
				<div class="form-group">
					<label class="control-label">${text('备注信息')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<% if(isAdd!false){ %>
						<a href="${ctx}/apply/hsQwApplyReplace/formReplace" id="btnFormReplace" class="btn btn-default" title="${text('新增承租信息变更申请')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
					<% } %>
					<% if(hasPermi('apply:hsQwApplyReplace:proxyAdd')){ %>
						<a href="#" class="btn btn-default" id="btnProxyReplace" title="${text('代理新增承租信息变更申请')}"><i class="fa fa-plus"></i> ${text('代理新增')}</a>
					<% } %>
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<div class="hide">
	<#form:listselect id="applySelect" title="配租申请单"
	path="applyId"
	url="${ctx}/apply/hsQwApply/applyedSelect?dataType=1"  allowClear="false"
	checkbox="false" itemCode="mainApplyer.userId"  itemName="mainApplyer.name" />
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("申请单编号")}', name:'id',  sortable:false, width:80, align:"center", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑个人住房变更")}">' + (val || row.id) + '</a>';
			}},
		{header:'${text("申请单内容")}', name:'applyTitle',  sortable:false, width:130, align:"center", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑个人住房变更")}">' + (val || row.id) + '</a>';
		}},
		// {header:'${text("家庭人数")}', name:'familyPeoples',  sortable:false, width:60, align:"center"},
		// {header:'${text("家庭年收入")}', name:'familyIncome',  sortable:false, width:60, align:"center"},
		{header:'${text("主申请人身份证")}', name:'mainApplyer.idNum',  sortable:false, width:100, align:"center"},
		{header:'${text("主申请人手机号")}', name:'mainApplyer.phone',  sortable:false, width:60, align:"center"},
		{header:'${text("流程环节")}', name:'processName',  sortable:false, width:60, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_rent_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("备注信息")}', name:'remarks',  sortable:false, width:130, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('apply:hsQwApply:edit')){
				actions.push('<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑个人住房变更")}"><i class="fa fa-check"></i></a>&nbsp;');
				// actions.push('<a href="${ctx}/apply/hsQwApply/delete?id='+row.id+'" class="btnList" title="${text("删除个人住房变更")}" data-confirm="${text("确认要删除该个人住房变更吗？")}">删除</a>&nbsp;');
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=rent_apply_house&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
// 在现有的bpmButton点击事件后添加新增按钮的处理逻辑
}).on('click', '.bpmButton', function(){
    var $this = $(this).addClass('hsBtnList');
    js.ajaxSubmit($this.attr('href'), function(data){
        if (data.result == Global.TRUE){
            // 检查 pcUrl 是否可访问
            $.ajax({
                url: data.pcUrl,
                type: 'get',
                success: function(response) {
                    // 如果请求成功，使用 js.addTabPage 打开新标签页
                    js.addTabPage($this, $this.attr('title'), data.pcUrl);
                },
                error: function(xhr, status, error) {
                    // 如果请求失败，显示错误信息
                    var errorMsg = '${text("加载失败")}';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    js.showErrorMessage(errorMsg);
                }
            });
        } else {
            js.showErrorMessage(data.message);
        }
    });
    return false;
})
$('#btnFormReplace').click(function(e){
    e.preventDefault();
    var $this = $(this).addClass('hsBtnList');
    js.ajaxSubmit($this.attr('href'));
    return false;
});
$('#btnProxyReplace').click(function(){
	$('#applySelectDiv').attr('data-url', '${ctx}/apply/hsQwApply/applySelect?dataType=1');
	$('#applySelectCode').val('');
	$('#applySelectName').val('').click();
});

function listselectCallback(id, action, index, layero){
	if (id == 'applySelect' && action == 'ok'){
		if ($('#applySelectCode').val() != ''){
			js.confirm('是否为该申请单用户进行承租信息变更申请‘' + $('#applySelectName').val() + '’',function(){
				$.ajax({
					url: "${ctx}/apply/hsQwApplyReplace/formReplace?proxyUserId="+ $('#applySelectCode').val(),
					type: 'get',
					success: function(response) {
						// 如果请求成功，使用 js.addTabPage 打开新标签页
						js.addTabPage(null, "代理申请承租信息变更", "${ctx}/apply/hsQwApplyReplace/formReplace?proxyUserId="+ $('#applySelectCode').val() );
					},
					error: function(xhr, status, error) {
						// 如果请求失败，显示错误信息
						var errorMsg = '${text("加载失败")}';
						if (xhr.responseJSON && xhr.responseJSON.message) {
							errorMsg = xhr.responseJSON.message;
						}
						js.showErrorMessage(errorMsg);
					}
				});
			});
		}else{
			js.showMessage('${text("没有选择要代理的申请单！")}');
		}
	}
}
</script>