<% layout('/layouts/default.html', {title: '租赁绿色通道', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text('新增租赁绿色通道申请')}
            </div>
            <div class="box-tools pull-right">
                <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
                <a href="${ctx}/apply/hsQwApplyGreen/form" class="btn btn-default btnTool" title="${text('新增租赁绿色通道申请')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
                <a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
            </div>
        </div>
        <div class="box-body">
            <div class="search-form-container">
                <#form:form id="searchForm" model="${hsQwApply}" action="${ctx}/apply/hsQwApplyGreen/listData" method="post" class="form-inline hide"
                        data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('主申请人')}：</label>
                            <div class="control-inline">
                                <#form:input path="mainApplyer.name" maxlength="20" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('主申请人身份证')}：</label>
                            <div class="control-inline">
                                <#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('主申请人手机号')}：</label>
                            <div class="control-inline">
                                <#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('户型')}：</label>
                            <div class="control-inline ">
                                <#form:select path="hsQwApplyHouse.houseType" dictType="hs_house_house_type" blankOption="true" class="form-control"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('租赁日期')}：</label>
                            <div class="control-inline">
                                <#form:input path="rentTime_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                                dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="rentTime_lte.click()"/>
                                &nbsp;-&nbsp;
                                <#form:input path="rentTime_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                                dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
                            </div>
                        </div>
                    </div>
                    <#form:hidden path="applyMatter" defaultValue="3" />
                    <div class="search-button-row">
                        <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                        <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
                    </div>
                </#form:form>
            </div>
            <div class="fixed-table-container">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
        </div>
    </div>
</div>
<% } %>
<script>
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false,
    autowidth: false,
    //scroll: true,
    scrollOffset: 18,
    width: '100%',
    height: 'auto',
    columnModel: [
        {header:'${text("申请单编号")}', name:'id', sortable:false, width:80, align:"left", frozen:true, formatter: function(val, obj, row, act){
            return '<a href="${ctx}/apply/hsQwApplyGreen/form?id=' + row.id + '" class="hsBtnList" title="${text("编辑租赁绿色通道申请")}">' + (val || row.id) + '</a>';
        }},
        {header:'${text("主申请人")}', name:'mainApplyer.name', sortable:false, width:60, align:"left"},
        {header:'${text("主申请人身份证")}', name:'mainApplyer.idNum', sortable:false, width:100, align:"left"},
        {header:'${text("主申请人手机号")}', name:'mainApplyer.phone', sortable:false, width:60, align:"left"},
        {header:'${text("楼盘")}', name:'hsQwApplyHouse.estate.name', sortable:false, width:150, align:"left"},
        {header:'${text("楼号")}', name:'hsQwApplyHouse.buildingNum', sortable:false, width:150, align:"left"},
        {header:'${text("单元号")}', name:'hsQwApplyHouse.unitNum', sortable:false, width:150, align:"left"},
        {header:'${text("户型")}', name:'hsQwApplyHouse.houseType', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_house_type')}", val, '${text("未知")}', true);
        }},
        {header:'${text("租赁时间")}', name:'rentTime', sortable:false, width:150, align:"left"},
        {header:'${text("状态")}', name:'status', sortable:false, width:60, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
        }},
        {header:'${text("备注信息")}', name:'remarks', sortable:false, width:130, align:"left"},
        {header:'${text("操作")}', name:'actions', width:150, align:"left", frozen:"right", formatter: function(val, obj, row, act){
            var actions = [];
            //# if(hasPermi('apply:hsQwApply:edit')){
                if (row.status == Global.STATUS_DRAFT){
                    actions.push('<a href="${ctx}/apply/hsQwApplyGreen/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁绿色通道申请")}" >编辑</a>&nbsp;');
                }
                actions.push('<a href="${ctx}/apply/hsQwApplyGreen/delete?id='+row.id+'" class="btnList" title="${text("删除租赁绿色通道申请")}" data-confirm="${text("确认要删除该租赁绿色通道申请吗？")}">删除</a>&nbsp;');
            //# }
                if (row.status == Global.STATUS_NORMAL){
                    actions.push('<a href="${ctx}/apply/hsQwApplyGreen/disable?id='+row.id+'" class="btnList" title="${text("停用公租房房源楼盘信息表")}" data-confirm="${text("确认要停用该公租房房源楼盘信息表吗？")}">停用</a>&nbsp;');
                } else if (row.status == Global.STATUS_DISABLE){
                    actions.push('<a href="${ctx}/apply/hsQwApplyGreen/enable?id='+row.id+'" class="btnList" title="${text("启用公租房房源楼盘信息表")}" data-confirm="${text("确认要启用该公租房房源楼盘信息表吗？")}">启用</a>&nbsp;');
                }
            return actions.join('');
        }}
    ],
    ajaxSuccess: function() {
        requestAnimationFrame(function() {
            CommonTable.setupFixedColumns('dataGrid');
        });
    }
});

// CommonTable.init('dataGrid');
</script>