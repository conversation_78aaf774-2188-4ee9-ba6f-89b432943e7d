<% layout('/layouts/default.html', {title: '局直公房申请申请管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<% if(!hsQwApply.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsQwApply}" title="局直公房申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsQwApply}" formKey="rent_apply_bureau" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwApply.isNewRecord ? '新增局直公房申请申请' : '编辑局直公房申请申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwApply}" action="${ctx}/apply/hsQwApply/save" method="post" class="form-horizontal">
		<div class="box-body hs-box-body-bpm">
			<div class="form-unit">${text('基础信息')}</div>
			<#form:hidden path="id"/>
			<#form:hidden path="officeCode"/>
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('人口数量')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input path="familyPeoples" dataFormat="number" maxlength="20" class="form-control required"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('家庭年收入（元）')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input path="familyIncome" maxlength="20" class="form-control required"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
						</td>
					</tr>
				</table>
			</div>

			<div class="form-unit">${text('局直公房申请人')}</div>
			<div class="form-unit-wrap table-form hs-table-div">
				<table id="hsQwApplyerDataGrid"></table>
				<% if (hasPermi('apply:hsQwApply:edit')){ %>
				<a href="#" id="hsQwApplyerDataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> ${text('增行')}</a>
				<% } %>
			</div>
			
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('收入证明图片上传')}：
						</td>
						<td colspan="3">
							<#form:fileupload id="uploadImage" bizKey="${hsQwApply.id}" bizType="hsQwApply_income"
							uploadType="image" class="" readonly="false" preview="true" dataMap="true"/>
						</td>
					</tr>
				</table>
			</div>
			
			<div class="form-unit">${text('局直公房申请人房屋情况表')}</div>
			<div class="form-unit-wrap table-form hs-table-div">
				<table id="hsQwApplyHouseDataGrid"></table>
				<% if (hasPermi('apply:hsQwApply:edit')){ %>
				<a href="#" id="hsQwApplyHouseDataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> ${text('增行')}</a>
				<% } %>
			</div>
			
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('房屋证明图片上传')}：
						</td>
						<td colspan="3">
							<#form:fileupload id="uploadImage1" bizKey="${hsQwApply.id}" bizType="hsQwApply_house"
							uploadType="image" class="" readonly="false" preview="true" dataMap="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('申请变更理由')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:textarea path="changeReason" rows="4" maxlength="500" class="form-control required"/>
						</td>
					</tr>
				</table>
			</div>
			
			<div class="hs-table-div taskComment hide">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label" width="15%">审批意见：</td>
						<td width="85%">
							<#bpm:comment bpmEntity="${hsQwApply}" showCommWords="true" />
						</td>
					</tr>
				</table>
			</div>
			<!--				<#bpm:nextTaskInfo bpmEntity="${hsQwApply}" />-->
		</div>
		<div class="box-footer">
			<div class="row">
				<div class="col-sm-offset-2 col-sm-10">
					<% if (hasPermi('apply:hsQwApply:edit')){ %>
					<#form:hidden path="status"/>
					<#form:hidden path="processName"/>
					<#form:hidden path="applyMatter" defaultValue="5"/>
					<% if (hsQwApply.isNewRecord || hsQwApply.status == '9'){ %>
					<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
					<% } %>
					<#bpm:button bpmEntity="${hsQwApply}" formKey="rent_apply_bureau" completeText="提 交"/>
					<% } %>
					<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
				</div>
			</div>
		</div>
	</#form:form>
</div>
</div>
<% } %>
<script>
	//# // 初始化局直公房申请申请人DataGrid对象
	$('#hsQwApplyerDataGrid').dataGrid({

		data: "#{toJson(hsQwApply.hsQwApplyerList)}",
		datatype: 'local', // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度

		//# // 设置数据表格列
		columnModel: [
			{header:'状态', name:'status', editable:true, hidden:true},
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'${text("人员编号")}', name:'userId', width:150,
				formatter: function(val, obj, row, act){
					return js.val(row, 'userId')+'|'+js.val(row, '');
				}, editable: true, edittype: "custom", editoptions: {
					custom_element: function(val, editOptions) {
						return js.template('treeselectTpl', {
							id: 'user_'+editOptions.id, title: '用户选择',
							name: 'userId', value: val.split('|')[0],
							labelName: '', labelValue: val.split('|')[1],
							url: '${ctx}/sys/office/treeData?isLoadUser=true&isAll=true', cssClass: ' required'
						});
					}
				}
			},
			{header:'${text("申请人姓名")}', name:'name', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required realName'}},
			{header:'${text("参加工作时间")}', name:'workTime', width:150,
				formatter:'date', formatoptions:{srcformat:'Y-m',newformat:'Y-m'},
				editable:true, edittype:'text', editoptions:{'class':'form-control laydate required', 'readonly':'true',
					dataInit: function(element){
						laydate.render({elem:element, type:'datetime', format:'yyyy-MM'});
					}
				}
			},
			{header:'${text("工龄")}', name:'workAge', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control'}},
			{header:'${text("工作单位")}', name:'organization', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
			{header:'${text("身份证号")}', name:'idNum', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required idcard'}},
			{header:'${text("手机号")}', name:'phone', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required mobile'}},
			{header:'${text("职务")}', name:'workPosition', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
			{header:'${text("年收入")}', name:'annualIncome', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required'}},
			{header:'${text("备注信息")}', name:'remarks', width:150, editable:true, edittype:'textarea', editoptions:{'maxlength':'500', 'class':'form-control', 'rows':'1'}},
			{header:'${text("婚姻状况")}', name:'marryStatus', width:100,editable:true, edittype:'select', editoptions:{'class':'form-control',
					items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('hs_applyer_marry')}"),
					itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
						js.select2(element).on("change",function(){
							$(this).resetValid()});
					}
				}},
			{header:'${text("死亡时间")}', name:'deathDate', width:150,formatter:'date', formatoptions:{srcformat:'Y-m',newformat:'Y-m'},
				editable:true, edittype:'text', editoptions:{'class':'form-control laydate', 'readonly':'true',
					dataInit: function(element){
						laydate.render({elem:element, type:'datetime', format:'yyyy-MM-dd'});
					}
				}
			},
			{header:'${text("人员类型")}', name:'userType', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control'}},
			{header:'${text("申请人角色")}', name:'applyRole', width:100,
				editable:true, edittype:'select', editoptions:{'class':'form-control',
					items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('hs_applyer_role')}"),
					itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
						js.select2(element).on("change",function(){
							$(this).resetValid()});
					}
				}
			},
			{header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
					var actions = [];
					if (val == 'new'){
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyerDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
					}else{
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyerDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');});return false;">删除</a>&nbsp;');
					}
					return actions.join('');
				}, editoptions: {defaultValue: 'new'}}
		],

		//# // 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
		editGridAddRowBtn: $('#hsQwApplyerDataGridAddRowBtn'),	// 子表增行按钮
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
		editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL , applyRole: '0'},	// 新增行的时候初始化的数据

		//# // 编辑表格的提交数据参数
		editGridInputFormListName: 'hsQwApplyerList', // 提交的数据列表名
		editGridInputFormListAttrs: 'status,id,userId,name,workTime,organization,idNum,phone,workPosition,workTitle,annualIncome,createBy,createDate,updateBy,updateDate,remarks,applyId,applyRole,', // 提交数据列表的属性字段

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
	//# // 初始化局直公房申请申请人房屋情况表DataGrid对象
	$('#hsQwApplyHouseDataGrid').dataGrid({

		data: "#{toJson(hsQwApply.hsQwApplyHouseList)}",
		datatype: 'local', // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度

		//# // 设置数据表格列
		columnModel: [
			{header:'状态', name:'status', editable:true, hidden:true},
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'${text("房屋坐落信息")}', name:'address', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required'}},
			{header:'${text("建筑面积")}', name:'floorArea', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required'}},
			{header:'${text("产权性质")}', name:'propertyRight', width:150, editable:true, edittype:'text', editoptions:{'maxlength':'10', 'class':'form-control required'}},
			{header:'${text("备注信息")}', name:'remarks', width:150, editable:true, edittype:'textarea', editoptions:{'maxlength':'500', 'class':'form-control', 'rows':'1'}},
			{header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
					var actions = [];
					if (val == 'new'){
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyHouseDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
					}else{
						actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyHouseDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');});return false;">删除</a>&nbsp;');
					}
					return actions.join('');
				}, editoptions: {defaultValue: 'new'}}
		],

		//# // 编辑表格参数
		editGrid: true,				// 是否是编辑表格
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
		editGridAddRowBtn: $('#hsQwApplyHouseDataGridAddRowBtn'),	// 子表增行按钮
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
		editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

		//# // 编辑表格的提交数据参数
		editGridInputFormListName: 'hsQwApplyHouseList', // 提交的数据列表名
		editGridInputFormListAttrs: 'status,id,applyId,address,floorArea,propertyRight,createBy,createDate,updateBy,updateDate,remarks,', // 提交数据列表的属性字段

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>
<script id="treeselectTpl" type="text/template">//<!--<div>
<#form:treeselect id="{{d.id}}" title="{{d.title}}" name="{{d.name}}" value="{{d.value}}"
	labelName="{{d.labelName}}" labelValue="{{d.labelValue}}" url="{{d.url}}"
	class="{{d.cssClass}}" btnClass="btn-sm" allowClear="true"/>
</div>//--></script>
<script>
	// 业务实现草稿按钮
	$('#btnDraft').click(function(){
		$('#status').val(Global.STATUS_DRAFT);
		$('#applyStatus').val('草稿');
	});
	// 流程按钮操作事件
	BpmButton = window.BpmButton || {};
	BpmButton.init = function(task){
		if (task.status != '2') {
			$('.taskComment').removeClass('hide');
		}
	}
	BpmButton.complete = function($this, task){
		$('#status').val(Global.STATUS_AUDIT);
	};
	// 表单验证提交事件
	$('#inputForm').validate({
		submitHandler: function(form){
			js.ajaxSubmitForm($(form), function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.closeCurrentTabPage(function(contentWindow){
						contentWindow.page();
					});
				}
			}, "json");
		}
	});
</script>