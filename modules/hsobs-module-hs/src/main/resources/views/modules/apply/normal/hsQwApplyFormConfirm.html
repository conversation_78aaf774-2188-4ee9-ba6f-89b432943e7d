<% layout('/modules/apply/normal/hsQwApplyFormManagement.html', { isRead: hsQwApply.isRead , isHouse : '', canAudit: true}){ %>
<div class="form-unit">${text('配租房源承租确认')}</div>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('确认承租')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:radio path="applyAccepted" value="${hsQwApply.applyAccepted}" dictType="hs_qw_apply_confirm" class="form-control required" readonly="${isRead!}"/>
            </td>
        </tr>
    </table>
</div>
${layoutContent!}
<% } %>
