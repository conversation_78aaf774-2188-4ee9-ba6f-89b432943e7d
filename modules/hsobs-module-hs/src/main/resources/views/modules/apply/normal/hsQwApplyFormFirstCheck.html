<% layout('/modules/apply/include/applyForm.html', { isRead: hsQwApply.isRead, isHouse : '', canAudit: true}){ %>
<div class="form-unit">${text('资格初审')}</div>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required hide">*</span> ${text('初审证明材料上传')}：
            </td>
            <td>
                <#form:fileupload id="uploadImage11" bizKey="${hsQwApply.id}" bizType="hsQwApply_first_check"
                uploadType="all" class="required" readonly="${isRead!false}" preview="true" dataMap="true"/>
            </td>
        </tr>
    </table>
</div>
${layoutContent!}
<% } %>
