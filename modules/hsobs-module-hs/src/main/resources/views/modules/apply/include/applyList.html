<% layout('/layouts/default.html', {title: '租赁资格轮候申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${title}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(isAdd){ %>
					<a href="${ctx}/apply/hsQwApplyReplaceUp/formReplaceUp" id="btnFormReplaceUp" class="btn btn-default" title="${text('新增居室变更申请')}"><i class="fa fa-plus"></i> ${text('新增置换')}</a>
					<a href="${ctx}/apply/hsQwApplyMzt/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候申请')}"><i class="fa fa-plus"></i> ${text('新增租房')}</a>
				<% } %>
				<% if(hasPermi('apply:hsQwApplyReplaceUp:proxyAdd')){ %>
					<a href="#" class="btn btn-default" id="btnProxyInfo" title="${text('代理新增居室变更申请')}"><i class="fa fa-plus"></i> ${text('代理新增')}</a>
				<% } %>
				<% if(isLh!false){ %>
	                <a href="#" class="btn btn-default" id="btnExportBySelect"><i class="glyphicon glyphicon-export"></i> 导出资格轮候名单</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwApply}" action="${ctx}${formUrl}" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('申请单号')}：</label>
					<div class="control-inline">
						<#form:input path="idq" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('主申请人')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.name" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('主申请人单位')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.organization" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('主申请人身份证')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('主申请人手机号')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('资格轮候得分区间')}：</label>
					<div class="control-inline">
						<#form:input path="applyScoreGte" maxlength="20" class="form-control width-120"/>
						-
						<#form:input path="applyScoreLte" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<% if(isQueryStatus) {%>
				<div class="form-group">
					<label class="control-label">${text('流程环节')}：</label>
					<div class="control-inline width-120">
						<#form:select path="processName" dictType="hs_apply_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<% } else {%>
				<#form:hidden path="processName" defaultValue="${applyStatusPage}" />
			<!--				<#form:hidden path="applyMatter" defaultValue="0" />-->
				<% }%>
			<#form:hidden path="orderBy" defaultValue="${orderBy!'a.apply_score desc'}" />
			<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<div class="hide">
	<#form:listselect id="applySelect" title="配租申请单"
	path="applyId"
	url="${ctx}/apply/hsQwApply/applyedSelect?dataType=2"  allowClear="false"
	checkbox="false" itemCode="mainApplyer.userId"  itemName="mainApplyer.name" />
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	columnModel: [
		{header:'${text("申请单编号")}', name:'id',  sortable:false, width:60, align:"center", frozen:true, formatter: function(val, obj, row, act){
				if(row.status != Global.STATUS_DRAFT) {
					return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑租赁资格轮候申请")}">' + (val || row.taskId) + '</a>';
				} else {
					return '<a href="${ctx}/apply/hsQwApplyMzt/form?id=' + row.id + '&status=1" class="hsBtnList" title="${text("编辑租赁资格轮候申请")}">' + (val || row.taskId) + '</a>';
				}
			}},
		// {header:'${text("申请单内容")}', name:'applyTitle',  sortable:false, width:130, align:"center", frozen:true, formatter: function(val, obj, row, act){
		// 		if(row.status != Global.STATUS_DRAFT) {
		// 			return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑租赁资格轮候申请")}">' + (val || row.id) + '</a>';
		// 		} else {
		// 			return '<a href="${ctx}/apply/hsQwApplyMzt/form?id=' + row.id +  '&status=1" class="hsBtnList" title="${text("编辑租赁资格轮候申请")}">' + (val || row.id) + '</a>';
		// 		}
		// }},
		// {header:'${text("家庭人数")}', name:'familyPeoples',  sortable:false, width:60, align:"center"},
		// {header:'${text("家庭年收入")}', name:'familyIncome',  sortable:false, width:60, align:"center"},
		{header:'${text("主申请人")}', name:'mainApplyer.name',  sortable:false, width:40, align:"center"},
		{header:'${text("主申请人单位")}', name:'mainApplyer.organization',  sortable:false, width:40, align:"center"},
		{header:'${text("主申请人身份证")}', name:'mainApplyer.idNum',  sortable:false, width:40, align:"center"},
		{header:'${text("主申请人手机号")}', name:'mainApplyer.phone',  sortable:false, width:40, align:"center"},
		{header:'${text("申请单状态")}', name:'status',  sortable:false, width:40, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
			}},
		{header:'${text("流程环节")}', name:'processName',  width:60, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("轮候评分")}', name:'applyScore',  sortable:false, width:60, align:"center", formatter: function(val, obj, row, act){
				return '<a href="javascript:void(0);"  onclick="showScoreDetail(\'' + row.id + '\',\'' + row.scoreUpdate + '\');" title="查看评分详情">' + val + '</a>';
		}},
		{header:'${text("优先对象")}', name:'priorityOrder',  sortable:false, width:60, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('sys_no_yes')}", val, '未知', true);
			}},
		{header:'${text("任务状态")}', name:'bpmStatus', sortable:false, width:60, fixed:true, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_task_status')}", val, '未知', true);
			}},
		{header:'${text("处理时间")}', name:'taskDueDate', sortable:false, width:100, fixed:true, align:"center"},
		// {header:'${text("备注信息")}', name:'remarks',  sortable:false, width:130, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			if(row.status != Global.STATUS_DRAFT) {
				actions.push('<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑租赁资格轮候申请")}"><i class="fa fa-check"></i></a>&nbsp;');
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=rent_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
				actions.push('<a href="${ctx}/apply/hsQwApply/refresh?id='+row.id+'" class="btnList" title="${text("刷新资格轮候得分")}" data-confirm="${text("确认要刷新资格轮候得分？刷新后手动修改评分将恢复自动轮候计算！")}"><i class="glyphicon glyphicon-refresh"></i></a>&nbsp;');
			} else {
				actions.push('<a href="${ctx}/apply/hsQwApplyMzt/form?id=' + row.id + '&status=1" class="hsBtnList" title="${text("编辑租赁资格轮候申请")}"><i class="fa fa-check"></i></a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
}).on('click', '.bpmButton', function(){
	var $this = $(this).addClass('hsBtnList');
	js.ajaxSubmit($this.attr('href'), function(data){
		if (data.result == Global.TRUE){
			// 检查 pcUrl 是否可访问
			$.ajax({
				url: data.pcUrl,
				type: 'get',
				success: function(response) {
					// 如果请求成功，使用 js.addTabPage 打开新标签页
					js.addTabPage($this, $this.attr('title'), data.pcUrl);
				},
				error: function(xhr, status, error) {
					// 如果请求失败，显示错误信息
					var errorMsg = '${text("加载失败")}';
					if (xhr.responseJSON && xhr.responseJSON.message) {
						errorMsg = xhr.responseJSON.message;
					}
					js.showErrorMessage(errorMsg);
				}
			});
		} else {
			js.showErrorMessage(data.message);
		}
	});
	return false;
});

$('#btnProxyInfo').click(function(){
	$('#applySelectDiv').attr('data-url', '${ctx}/apply/hsQwApply/applySelect?dataType=2');
	$('#applySelectCode').val('');
	$('#applySelectName').val('').click();
});
$('#btnFormReplaceUp').click(function(e){
	e.preventDefault();
	var $this = $(this).addClass('hsBtnList');
	js.ajaxSubmit($this.attr('href'));
	return false;
});
function listselectCallback(id, action, index, layero){
	if (id == 'applySelect' && action == 'ok'){
		if ($('#applySelectCode').val() != ''){
			js.confirm('是否为该申请单用户进行居室变更申请' + $('#applySelectName').val() + '',function(){
				$.ajax({
					url: "${ctx}/apply/hsQwApplyReplaceUp/formReplaceUp?proxyUserId="+ $('#applySelectCode').val(),
					type: 'get',
					success: function(response) {
						// 如果请求成功，使用 js.addTabPage 打开新标签页
						js.addTabPage(null, "代理申请居室变更", "${ctx}/apply/hsQwApplyReplaceUp/formReplaceUp?proxyUserId="+ $('#applySelectCode').val() );
					},
					error: function(xhr, status, error) {
						// 如果请求失败，显示错误信息
						var errorMsg = '${text("加载失败")}';
						if (xhr.responseJSON && xhr.responseJSON.message) {
							errorMsg = xhr.responseJSON.message;
						}
						js.showErrorMessage(errorMsg);
					}
				});
			});
		}else{
			js.showMessage('${text("没有选择要代理的申请单！")}');
		}
	}
}
</script>

<script>
function showScoreDetail(id, scoreUpdate) {
    // 防止重复弹窗
    if ($('.layui-layer-admin').length > 0) {
        return;
    }
    
    $.ajax({
        url: '${ctx}/apply/hsQwApply/getScoreDetail',
        type: 'GET',
        data: {applyId: id.toString()},
        success: function(data) {
            if (data.length === 0) {
                js.showMessage('暂无评分详情数据！');
                return;
            }
            
            // 确保scoreUpdate是布尔值或数字
            var isManuallyAdjusted = scoreUpdate === "1" || scoreUpdate === 1 || scoreUpdate === true;
            
            var html = '<table class="table table-bordered table-hover table-striped">';
            html += '<thead><tr><th style="width:60px">序号</th><th>规则类型</th><th>规则得分</th><th>得分明细</th><th style="width:80px">操作</th></tr></thead>';
            html += '<tbody>';
            var totalScore = 0;
            var hasManualModification = false;
            
            for (var i = 0; i < data.length; i++) {
                var isModified = data[i].isManualModified === true || data[i].isManualModified === 'true' || data[i].isManualModified === 1;
                if (isModified) {
                    hasManualModification = true;
                }
                
                html += '<tr data-id="' + data[i].id + '" data-rule-type="' + data[i].ruleType + '" class="' + (isModified ? 'modified-score' : '') + '">' +
                        '<td>' + (i+1) + (isModified ? ' <i class="fa fa-pencil text-warning" title="手动修改"></i>' : '') + '</td>' +
                        '<td>' + js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_score_type')}", data[i].ruleType, '未知', true) + '</td>' +
                        '<td class="score-value">' + data[i].score + '</td>' +
                        '<td style="max-width: 300px; white-space: normal; overflow-wrap: break-word;" title="' + data[i].remarks + '" class="score-remarks">' + data[i].remarks + '</td>' +
                        '<td><button class="btn btn-xs btn-primary edit-score-btn">修改</button></td>' +
                        '</tr>';
                totalScore += parseFloat(data[i].score);
            }
            
            html += '<tr><td colspan="5" style="text-align: right; font-weight: bold;">汇总总计：<span id="totalScoreValue">' + totalScore + '</span> 分</td></tr>';
            
            // 添加人工调整警告信息
            if (isManuallyAdjusted) {
                html += '<tr><td colspan="5" class="bg-warning" style="font-weight: bold; color: #8a6d3b;">' +
                       '<i class="fa fa-exclamation-triangle"></i> 注意：该申请已标记为人工调整分数，不参与自动轮候计算</td></tr>';
            }
            
            if (data.length > 0) {
                html += '<tr><td colspan="5">打分时间：' + data[0].createDate + 
                       (hasManualModification && !isManuallyAdjusted ? ' <span class="label label-warning">编辑 包含手动修改项</span>' : '') +
                       '</td></tr>';
            }
            html += '</tbody></table>';
            
            // 使用setTimeout确保之前的弹窗已关闭
            setTimeout(function() {
                var layerIndex = layer.open({
                    type: 1,
                    title: '<i class="fa fa-list-alt"></i> 评分详情' + 
                           (isManuallyAdjusted ? ' <span class="label label-danger"><i class="fa fa-ban"></i> 人工调整 - 不参与自动轮候</span>' : '') +
                           (hasManualModification && !isManuallyAdjusted ? ' <span class="label label-warning">编辑 包含手动修改项</span>' : ''),
                    content: '<div style="max-width: 800px; overflow-x: auto;">' + html + '</div>',
                    area: ['800px', '500px'],
                    time: 0,
                    skin: 'layui-layer-admin',
                    btn: ['保存', '关闭'],
                    yes: function(index) {
                        // 收集所有修改过的分数，使用对象来避免重复
                        var updatedScoresMap = {};
                        
                        // 首先检查所有带有编辑表单的行
                        $('.score-edit-form').each(function() {
                            var $row = $(this).closest('tr');
                            var scoreId = $row.data('id');
                            
                            // 如果这个ID还没有被处理过
                            if (!updatedScoresMap[scoreId]) {
                                var newScore = $(this).find('.score-input').val();
                                var newRemarks = $(this).find('.remarks-input').val();
                                
                                // 如果找不到输入框，可能是因为有多个编辑表单在同一行
                                if (newScore === undefined) {
                                    newScore = $row.find('.score-input').val();
                                }
                                if (newRemarks === undefined) {
                                    newRemarks = $row.find('.remarks-input').val();
                                }
                                
                                updatedScoresMap[scoreId] = {
                                    id: scoreId,
                                    score: newScore,
                                    remarks: newRemarks,
                                    isManualModified: true
                                };
                            }
                        });
                        
                        // 然后检查所有已标记为修改但当前没有编辑表单的行
                        $('.modified-score').each(function() {
                            var scoreId = $(this).data('id');
                            
                            // 如果这个ID还没有被处理过（没有活动的编辑表单）
                            if (!updatedScoresMap[scoreId] && !$(this).find('.score-edit-form').length) {
                                updatedScoresMap[scoreId] = {
                                    id: scoreId,
                                    score: $(this).find('.score-value').text(),
                                    remarks: $(this).find('.score-remarks').text(),
                                    isManualModified: true
                                };
                            }
                        });
                        
                        // 转换为数组
                        var updatedScores = Object.values(updatedScoresMap);
                        
                        if (updatedScores.length > 0) {
                            // 发送AJAX请求保存修改后的分数
                            $.ajax({
                                url: '${ctx}/apply/hsQwApply/updateScoreDetail',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    id: id.toString(),
                                    scoreDetails: updatedScores,
                                    scoreUpdate: "1"  // 标记为人工调整
                                }),
                                success: function(result) {
                                    if (result.result == Global.TRUE) {
                                        js.showMessage('分数更新成功！');
                                        layer.close(layerIndex);
                                        
                                        // 刷新数据表格 - 使用更可靠的方法
                                        $('#dataGrid').dataGrid('reload');
                                        
                                        // 如果需要完全刷新页面，使用以下代码
                                        setTimeout(function() {
                                            window.location.reload();
                                        }, 500);
                                    } else {
                                        js.showMessage(result.message || '分数更新失败！');
                                    }
                                },
                                error: function() {
                                    js.showMessage('分数更新失败，请稍后重试！');
                                }
                            });
                        } else {
                            layer.close(layerIndex);
                        }
                    },
                    success: function(layero) {
                        $('.layui-layer-content').css({'padding':'15px','font-size':'14px'});
                        
                        // 为手动修改过的行添加样式
                        $('.modified-score').css('background-color', '#ffffd9');
                        
                        // 如果是人工调整，添加特殊样式
                        if (isManuallyAdjusted) {
                            $(layero).find('.layui-layer-title').css('background-color', '#f2dede');
                        }
                        
                        // 绑定修改按钮点击事件
                        function bindEditButtonEvents() {
                            $(layero).find('.edit-score-btn').off('click').on('click', function() {
                                var $row = $(this).closest('tr');
                                var currentScore = $row.find('.score-value').text();
                                var currentRemarks = $row.find('.score-remarks').text();
                                
                                // 替换为编辑表单
                                $row.find('.score-value').html('<div class="score-edit-form"><input type="number" class="form-control score-input" value="' + currentScore + '" step="0.01" min="0"></div>');
                                $row.find('.score-remarks').html('<div class="score-edit-form"><textarea class="form-control remarks-input" rows="2">' + currentRemarks + '</textarea></div>');
                                $(this).removeClass('btn-primary edit-score-btn').addClass('btn-success save-score-btn').text('确认');
                                
                                // 绑定确认按钮点击事件
                                $(this).off('click').on('click', function() {
                                    var newScore = parseFloat($row.find('.score-input').val()) || 0;
                                    var newRemarks = $row.find('.remarks-input').val();
                                    
                                    // 更新显示
                                    $row.find('.score-value').text(newScore);
                                    $row.find('.score-remarks').text(newRemarks).attr('title', newRemarks);
                                    $(this).removeClass('btn-success save-score-btn').addClass('btn-primary edit-score-btn').text('修改');
                                    
                                    // 标记为已修改
                                    $row.addClass('modified-score').css('background-color', '#ffffd9');
                                    if (!$row.find('td:first i.fa-pencil').length) {
                                        $row.find('td:first').append(' <i class="fa fa-pencil text-warning" title="手动修改"></i>');
                                    }
                                    
                                    // 重新计算总分
                                    var newTotal = 0;
                                    $('.score-value').each(function() {
                                        if (!$(this).find('.score-input').length) {
                                            newTotal += parseFloat($(this).text()) || 0;
                                        }
                                    });
                                    $('#totalScoreValue').text(newTotal.toFixed(2));
                                    
                                    // 重新绑定修改按钮事件
                                    bindEditButtonEvents();
                                });
                            });
                        }
                        
                        // 初始绑定所有修改按钮
                        bindEditButtonEvents();
                    }
                });
            }, 100);
        },
        error: function() {
            layer.msg('获取评分详情失败，请稍后重试！');
        }
    });
}

$('#btnExport').click(function(){
    // 保存当前页码和页大小
    var currentPageNo = $('#pageNo').val();
    var currentPageSize = $('#pageSize').val();
    
    // 设置最大页大小
    $('#pageSize').val(999999);
    
    js.ajaxSubmitForm($('#searchForm'), {
        url: '${ctx}/apply/hsQwApply/exportData',
        clearParams: '',  // 不清除任何参数
        downloadFile: true,
        success: function(data) {
            // 导出完成后恢复原来的页码和页大小
            $('#pageNo').val(currentPageNo);
            $('#pageSize').val(currentPageSize || 10); // 使用保存的值，如果没有则使用默认值10
        }
    });
});
</script>