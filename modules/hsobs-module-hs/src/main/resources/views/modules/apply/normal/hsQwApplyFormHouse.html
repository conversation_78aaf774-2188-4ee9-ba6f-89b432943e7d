<% layout('/modules/apply/normal/hsQwApplyFormOfflineRent.html', { isRead: hsQwApply.isRead , isHouse : '', canAudit: true}){ %>
<div class="form-unit">${text('房租配租信息')}</div>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('选择房源')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <% if (isRead!'false' == 'false') {%>
                    <#form:listselect id="publicHouse" title="房源选择选择"
                    path="houseId" labelPath="hsQwApplyHouse.simpleInfo" class="required"
                    url="${ctx}/house/hsQwPublicRentalHouse/houseSelectByType?dataType=3&applyId=${hsQwApply.id}&selectId=${hsQwApply.houseId}" allowClear="true"
                    checkbox="false" itemCode="id" itemName="simpleInfo" readonly="${isRead!false}"/>
                <% } else if (hsQwApply.hsQwApplyHouse!=null){%>
                    <a href="${ctx}/house/hsQwPublicRentalHouse/form?id=${hsQwApply.hsQwApplyHouse.id}" class="hsBtnList" data-title="${text("查看租赁公租房房源房源信息表")}">
                        ${hsQwApply.hsQwApplyHouse.simpleInfo}
                    </a>
                <% } %>
            </td>
        </tr>
    </table>
</div>
${layoutContent!}
<% } %>
