<% layout('/modules/apply/normal/hsQwApplyFormFirstCheck.html', { isRead: hsQwApply.isRead, isHouse : '', canAudit: true}){ %>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('是否优先对象')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:radio path="priorityOrder" value = "${hsQwApply.priorityOrder}" dictType="sys_no_yes"
                class="form-control required" readonly="${isRead!'false'}" />
            </td>
        </tr>
    </table>
</div>
${layoutContent!}
<% } %>
