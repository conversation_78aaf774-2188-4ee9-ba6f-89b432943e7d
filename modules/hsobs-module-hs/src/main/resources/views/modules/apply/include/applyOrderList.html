<% layout('/layouts/default.html', {title: '租赁资格轮候申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="nav-tabs-custom nav-main">
        <ul class="nav nav-tabs">
            <% if ( first ){ %>
            <li  class="active" >
                <% } else {%>
            <li>
                <% } %>
            <a href="${ctx}/apply/hsQwApply/orderListFirst"><i class="fa icon-energy"></i> ${text('初审评分排序')}</a></li>
            <% if ( !first ){ %>
            <li  class="active" >
                <% } else {%>
            <li>
                <% } %>
            <a href="${ctx}/apply/hsQwApply/orderListCheck"><i class="fa icon-book-open"></i> ${text('复查评分排序')}</a></li>
        </ul>
        <div class="box-body">
            <div class="search-form-container">
                <#form:form id="searchForm" model="${hsQwApply}" action="${ctx}/apply/hsQwApply/listAuditData" method="post" class="form-inline"
                        data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
                    <!-- 第一行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('主申请人')}：</label>
                            <div class="control-inline">
                                <#form:input path="mainApplyer.name" maxlength="20" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('主申请人身份证')}：</label>
                            <div class="control-inline">
                                <#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('主申请人手机号')}：</label>
                            <div class="control-inline">
                                <#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第二行：1个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('备注信息')}：</label>
                            <div class="control-inline">
                                <#form:input path="remarks" maxlength="500" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>

                    <#form:hidden path="processName" defaultValue="${applyStatusPage}" />
                    <#form:hidden path="orderBy" defaultValue="a.apply_score desc, a.priority_order desc" />
                    
                    <!-- 按钮行 -->
                    <div class="search-button-row">
                        <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                        <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
                        <% if(hasPermi('apply:hsQwApply:order')){ %>
                            <button type="button" id="publicBtn" class="btn btn-default btn-sm" onclick="return false;"><i class="fa fa-plus"></i> ${text('公示')}</button>
                        <% } %>
                    </div>
                </#form:form>
            </div>
            <div class="fixed-table-container">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
        </div>
    </div>
</div>
<% } %>
<script>
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false,
    autowidth: false,
    //scroll: true,
    scrollOffset: 18,
    width: '100%',
    height: 'auto',
    columnModel: [
        {header:'${text("申请单编号")}', name:'id', sortable:false, width:80, align:"left", frozen:true, formatter: function(val, obj, row, act){
            return '<a href="${ctx}/apply/hsQwApply/formRead?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候申请")}">'+(val||row.id)+'</a>';
        }},
        {header:'${text("申请人")}', name:'mainApplyer.name', sortable:false, width:60, align:"left"},
        {header:'${text("工龄")}', name:'mainApplyer.workAge', sortable:false, width:60, align:"left"},
        {header:'${text("工作单位")}', name:'mainApplyer.organization', sortable:false, width:60, align:"left"},
        {header:'${text("职务")}', name:'mainApplyer.workPosition', sortable:false, width:60, align:"left"},
        {header:'${text("住房情况")}', name:'totalhouseInfo', sortable:false, width:60, align:"left"},
        {header:'${text("收入情况")}', name:'mainApplyer.annualIncome', sortable:false, width:60, align:"left"},
        {header:'${text("轮候评分")}', name:'applyScore', sortable:false, width:50, align:"left"},
        {header:'${text("优先对象")}', name:'priorityOrder', sortable:false, width:50, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('sys_no_yes')}", val, '未知', true);
        }},
        {header:'${text("任务状态")}', name:'bpmStatus', sortable:false, width:60, align:"left", frozen:'right', formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('bpm_task_status')}", val, '未知', true);
        }}
    ],
    ajaxSuccess: function() {
        requestAnimationFrame(function() {
            CommonTable.setupFixedColumns('dataGrid');
        });
    }
});
</script>