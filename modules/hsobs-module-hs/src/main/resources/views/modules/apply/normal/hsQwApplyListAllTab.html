<% layout('/modules/apply/include/applyListAll.html', {title: '租赁资格轮候申请管理', libs: ['dataGrid']}){ %>
        <div class="nav-tabs-custom nav-main">
            <ul class="nav nav-tabs">
                <% if ( hsQwApply.applyMatter=='0'){ %>
                <li  class="active" >
                    <% } else {%>
                <li>
                <% } %>
                    <a href="${ctx}/apply/hsQwApply/listAll?applyMatter=0"><i class="fa icon-energy"></i> ${text('公租房申请')}</a></li>
                <% if (  hsQwApply.applyMatter=='1'){ %>
                <li  class="active" >
                    <% } else {%>
                <li>
                    <% } %>
                    <a href="${ctx}/apply/hsQwApply/listAll?applyMatter=1"><i class="fa icon-book-open"></i> ${text('个人信息变更申请')}</a></li>
                <% if (  hsQwApply.applyMatter=='2'){ %>
                <li  class="active" >
                    <% } else {%>
                <li>
                    <% } %>
                    <a href="${ctx}/apply/hsQwApply/listAll?applyMatter=2"><i class="fa icon-book-open"></i> ${text('承租置换变更申请')}</a></li>
                <% if (  hsQwApply.applyMatter=='3'){ %>
                <li  class="active" >
                    <% } else {%>
                <li>
                    <% } %>
                    <a href="${ctx}/apply/hsQwApply/listAll?applyMatter=3"><i class="fa icon-book-open"></i> ${text('绿色通道申请')}</a></li>
                <% if (  hsQwApply.applyMatter=='4'){ %>
                <li  class="active" >
                    <% } else {%>
                <li>
                    <% } %>
                    <a href="${ctx}/apply/hsQwApply/listAll?applyMatter=4"><i class="fa icon-book-open"></i> ${text('承租信息变更申请')}</a></li>
                <div class="box-tools pull-right">
                    <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
                    <a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
                    <a href="#" class="btn btn-default" id="btnExportRent" title="${text('批量导出配租确认单')}"><i class="fa fa-plus"></i> ${text('批量导出配租确认单')}</a>
                    <a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
                </div>
                <div class="hide">
                    <#form:listselect id="applySelect" title="配租确认单选择"
                    url="${ctx}/checkrecord/hsQwCheckRecord/houseSelect" allowClear="false"
                    checkbox="true" itemCode="id" itemName="simpleInfo" />
                </div>
            </ul>
<%}%>
<script>
    $('#btnExportRent').click(function(){
        $('#applySelectDiv').attr('data-url', '${ctx}/apply/hsQwApply/applySelect?dataType=6');
        $('#applySelectCode').val('');
        $('#applySelectName').val('').click();
    });
    function listselectCallback(id, action, index, layero){
    if (id == 'applySelect' && action == 'ok'){
        if ($('#applySelectCode').val() != ''){
            js.confirm('是否批量导出这些配租确认单？', function(){
                // 显示提示信息
                js.showMessage('正在生成配租确认单，请稍候...');
                
                // 打开下载窗口
                window.open("${ctx}/apply/hsQwApply/genHouseSelectOrderZip?applyIds=" + $('#applySelectCode').val());
            });
        } else {
            js.showMessage('${text("没有选择要导出配租确认单！")}');
        }
    }
}
</script>