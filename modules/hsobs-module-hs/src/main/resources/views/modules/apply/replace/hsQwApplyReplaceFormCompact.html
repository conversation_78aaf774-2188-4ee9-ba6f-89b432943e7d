<% layout('/modules/apply/replace/hsQwApplyRePlaceFormHouse.html', { isRead: false , isHouse : ''}){ %>
<div class="form-unit">${text('房源合同信息')}</div>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('合同编码')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="compact.compactCode" readonly="${hsQwApply.compactRead!''=='1'}" maxlength="255" class="form-control required" value="${hsQwApply.compact.compactCode!}"/>
            </td>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('月租金')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="compact.monthFee" readonly="${hsQwApply.compactRead!''=='1'}" class="form-control required" value="${hsQwApply.compact.monthFee!}"/>
            </td>
        </tr>
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('合同开始时间')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="compact.startDate" readonly="${hsQwApply.compactRead!''=='1'}" maxlength="20" class="form-control laydate required"
                dataFormat="date" data-type="date" data-format="yyyy-MM-dd" value="${hsQwApply.compact.startDate!}"/>
            </td>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('合同结束时间')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="compact.endDate" readonly="${hsQwApply.compactRead!''=='1'}" maxlength="20" class="form-control laydate required"
                dataFormat="date" data-type="date" data-format="yyyy-MM-dd" value="${hsQwApply.compact.endDate!}"/>
            </td>
        </tr>
        <tr>
            <td class="form-label hs-form-label">
                <span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
            </td>
            <td colspan="3">
                <#form:textarea path="compact.remarks" readonly="${hsQwApply.compactRead!''=='1'}" rows="4" maxlength="500" class="form-control" value="${hsQwApply.compact.remarks!}"/>
            </td>
        </tr>
        <tr>
            <td class="form-label hs-form-label">
                <span class="required hide">*</span> ${text('合同文件上传')}：
            </td>
            <td colspan="3">
                <#form:fileupload id="uploadCompact" bizKey="${hsQwApply.compact.id!}" bizType="hsQwApply_compact"
                uploadType="file" class="" readonly="${hsQwApply.compactRead!'false'}" preview="true" dataMap="true"/>
            </td>
        </tr>
    </table>
</div>
    ${layoutContent!}
<% } %>

