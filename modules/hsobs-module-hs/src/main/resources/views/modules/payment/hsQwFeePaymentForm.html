<% layout('/layouts/default.html', {title: '租赁资格轮候物业费催单管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwFeePayment.isNewRecord ? '新增租赁资格轮候物业费催单' : '编辑租赁资格轮候物业费催单')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwFeePayment}" action="${ctx}/payment/hsQwFeePayment/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-12">
						<div class="hs-table-div">
							<table class="table-form hs-table-form">
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('账单编号')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="feeId" maxlength="64" readonly="true" class="form-control required"/>
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('催收类型')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:select path="noticeType" dictType="hs_payment_type" class="form-control required" />
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('催收信息')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('payment:hsQwFeePayment:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>