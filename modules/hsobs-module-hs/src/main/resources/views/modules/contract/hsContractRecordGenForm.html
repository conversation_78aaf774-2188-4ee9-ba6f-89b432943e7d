<% layout('/layouts/default.html', {title: '合同管理', libs: ['validate','ueditor','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsContractRecord.isView == 1 ? '查看合同' : hsContractRecord.isNewRecord ? '新增合同' : '编辑合同')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsContractRecord}" action="${ctx}/contract/hsContractRecord/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<#form:hidden name="contractSource" value="0" />
				<div class="row">
					<% if (hsContractRecord.isNewRecord){ %>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同模板')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select id="templateIdSelect" path="templateId" items="${templateList}" itemLabel="viewName" itemValue="id" blankOption="true" blankOptionLabel="请选择" class="form-control required" />
							</div>
						</div>
					</div>
					<% } %>
				</div>
				<div class="row">

					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="contractName" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('合同编号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="contractNo" maxlength="255" class="form-control "/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="contractType" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请人名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applyName" maxlength="20" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请人身份证')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applySfz" maxlength="18" class="form-control idcard required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('联系电话')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applyTel" maxlength="15" class="form-control mobile required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('工作单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="workUnit" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('房屋地址')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="houseAddr" maxlength="512" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('单元号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="unitNo" maxlength="64" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('面积')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="houseArea" maxlength="64" class="form-control number required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('户型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="houseType" maxlength="128" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('租金')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="houseRent" maxlength="16" class="form-control number required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同签订开始时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="startTime" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同签订结束时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="endTime" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('合同简述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea rows="3" path="contractDesc" maxlength="255" class="form-control "/>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('补充属性')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-1" title="">
								<span class="required hide">*</span> ${text('')}<i class="fa icon-question hide"></i></label>
							<div class="col-sm-11 table-form">
								<table id="hsContractFieldGrid"></table>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('合同内容')}
					<button type="button" class="btn btn-sm btn-default" id="refreshBtn" onclick="refreshContent()"><i class="fa fa-refresh"></i> ${text('刷新')}</button>
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-1">${text('正文')}：</label>
							<div class="col-sm-11">
								<#form:ueditor id="content" path="hsContractRecordData.content" maxlength="10000" height="500" class="required" outline="${parameter.outline}"/>
							</div>
						</div>
					</div>
				</div>

			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hsContractRecord.isView != 1 && hasPermi('contract:hsContractRecord:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('生 成')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>

<% if (hsContractRecord.isNewRecord){ %>
$('#templateIdSelect').on ('change' , function(e, data) {
	let selectedValue = $(this).val();
	$.ajax({
		type: 'POST',
		url: "${ctx}/contract/hsContractTemplate/singleData?___t=" + new Date().getTime(),
		data: {id: selectedValue},
		dataType: 'json',
		async: false,
		error: function(data){
			js.showErrorMessage(data.responseText);
		},
		success: function(data, status, xhr){
			if (data != null) {
				if (data.hsContractTemplateData) {

					console.log($('#contentUE'));
					console.log(contentUE);

					contentUE.setContent(data.hsContractTemplateData.content);
				}
				$('#hsContractFieldGrid').dataGrid('setParam', {data:data.hsContractTemplateFieldList, page: 1, rowNum: 5000}, true);
				$('#hsContractFieldGrid').dataGrid('refresh');
			}
		}
	});
});
<% } %>

function refreshContent() {
	let msg = contentUE.getContent();
	if (msg != null && msg !== '') {
		msg = msg.replace(/\$\{contractName\}/g, $('#contractName').val());
		msg = msg.replace(/\$\{contractNo\}/g, $('#contractNo').val());
		msg = msg.replace(/\$\{applyName\}/g, $('#applyName').val());
		msg = msg.replace(/\$\{applySfz\}/g, $('#applySfz').val());
		msg = msg.replace(/\$\{applyTel\}/g, $('#applyTel').val());
		msg = msg.replace(/\$\{workUnit\}/g, $('#workUnit').val());
		msg = msg.replace(/\$\{houseAddr\}/g, $('#houseAddr').val());
		msg = msg.replace(/\$\{unitNo\}/g, $('#unitNo').val());
		msg = msg.replace(/\$\{houseArea\}/g, $('#houseArea').val());
		msg = msg.replace(/\$\{houseType\}/g, $('#houseType').val());
		msg = msg.replace(/\$\{houseRent\}/g, $('#houseRent').val());
		msg = msg.replace(/\$\{startTime\}/g, $('#startTime').val());
		msg = msg.replace(/\$\{endTime\}/g, $('#endTime').val());
		msg = msg.replace(/\$\{contractDesc\}/g, $('#contractDesc').val());

		let rows = $('#hsContractFieldGrid').dataGrid('getDataIDs');
		rows.forEach((item, index) => {
			let codeTmp = $('#' + item + '_fieldCode').val();
			let valueTmp = $('#' + item + '_fieldValue').val();

			if (valueTmp != null && valueTmp !== '') {
				let regex = new RegExp('\\$\\{' + codeTmp + '\\}', 'g');
				msg = msg.replace(regex, valueTmp);
			}
		});
		contentUE.setContent(msg);

	}
}

$("#hsContractFieldGrid").dataGrid({
	data: "#{toJson(hsContractRecord.fieldList)}",
	datatype: 'local', // 设置本地数据
	sortableColumn: false,
	columnModel: [
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'${text("字段名称")}', name:'fieldName', sortable:false, width:100,
			editable: true, edittype: "text", editoptions: {'maxlength':'100', 'class':'form-control'}
		},
		{header:'${text("字段属性编码")}', name:'fieldCode', sortable:false, width:100,
			editable:true, edittype:'text', editoptions:{'maxlength':'50', 'class':'form-control'}
		},
		{header:'${text("填写值")}', name:'fieldValue', sortable:false, width:100,
			editable:true, edittype:'text', editoptions:{'maxlength':'50', 'class':'form-control'}
		},
	],
	autoGridHeight: function(){return 'auto'}, // 设置自动高度

	// 编辑表格参数
	editGrid: true,				// 是否是编辑表格
	editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsContractTemplateFieldGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

	// 编辑表格的提交数据参数
	editGridInputFormListName: 'fieldList', // 提交的数据列表名
	editGridInputFormListAttrs: 'fieldName,fieldCode,fieldValue,id,status,createBy,createDate,updateBy,updateDate,', // 提交数据列表的属性字段

	ajaxSuccess: function(){

	}
});

$('#inputForm').validate({
	submitHandler: function(form){

		refreshContent();

		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
	}
});

</script>