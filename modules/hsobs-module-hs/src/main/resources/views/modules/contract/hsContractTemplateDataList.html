<% layout('/layouts/default.html', {title: '合同模板详情表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('合同模板详情表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('contract:hsContractTemplateData:edit')){ %>
					<a href="${ctx}/contract/hsContractTemplateData/form" class="btn btn-default btnTool" title="${text('新增合同模板详情表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsContractTemplateData}" action="${ctx}/contract/hsContractTemplateData/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('合同模板内容')}：</label>
					<div class="control-inline">
						<#form:input path="content" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("合同模板内容")}', name:'content', index:'a.content', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/contract/hsContractTemplateData/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑合同模板详情表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('contract:hsContractTemplateData:edit')){
				actions.push('<a href="${ctx}/contract/hsContractTemplateData/form?id='+row.id+'" class="hsBtnList" title="${text("编辑合同模板详情表")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/contract/hsContractTemplateData/delete?id='+row.id+'" class="btnList" title="${text("删除合同模板详情表")}" data-confirm="${text("确认要删除该合同模板详情表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>