<% layout('/layouts/default.html', {title: '合同字段管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('合同字段管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('contract:hsContractRecordField:edit')){ %>
					<a href="${ctx}/contract/hsContractRecordField/form" class="btn btn-default btnTool" title="${text('新增合同字段')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsContractRecordField}" action="${ctx}/contract/hsContractRecordField/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('主识别ID')}：</label>
					<div class="control-inline">
						<#form:input path="id" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字段归属合同ID')}：</label>
					<div class="control-inline">
						<#form:input path="recordId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字段类型;')}：</label>
					<div class="control-inline">
						<#form:input path="fieldType" maxlength="32" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字段名称')}：</label>
					<div class="control-inline">
						<#form:input path="fieldName" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同字段填写值')}：</label>
					<div class="control-inline">
						<#form:input path="fieldCode" maxlength="128" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字段填写值')}：</label>
					<div class="control-inline">
						<#form:input path="fieldValue" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字段子项数据;单选、多选等选择类数据')}：</label>
					<div class="control-inline">
						<#form:input path="fieldSubkey" maxlength="2000" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字段填写描述')}：</label>
					<div class="control-inline">
						<#form:input path="fieldDesc" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('字段排序编号')}：</label>
					<div class="control-inline">
						<#form:input path="orderNum" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('是否有效')}：</label>
					<div class="control-inline">
						<#form:input path="validTag" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("主识别ID")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/contract/hsContractRecordField/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑合同字段")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("字段归属合同ID")}', name:'recordId', index:'a.record_id', width:150, align:"left"},
		{header:'${text("字段类型;")}', name:'fieldType', index:'a.field_type', width:150, align:"left"},
		{header:'${text("字段名称")}', name:'fieldName', index:'a.field_name', width:150, align:"left"},
		{header:'${text("合同字段填写值")}', name:'fieldCode', index:'a.field_code', width:150, align:"left"},
		{header:'${text("字段填写值")}', name:'fieldValue', index:'a.field_value', width:150, align:"left"},
		{header:'${text("字段子项数据;单选、多选等选择类数据")}', name:'fieldSubkey', index:'a.field_subkey', width:150, align:"left"},
		{header:'${text("字段填写描述")}', name:'fieldDesc', index:'a.field_desc', width:150, align:"left"},
		{header:'${text("字段排序编号")}', name:'orderNum', index:'a.order_num', width:150, align:"center"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("是否有效")}', name:'validTag', index:'a.valid_tag', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('contract:hsContractRecordField:edit')){
				actions.push('<a href="${ctx}/contract/hsContractRecordField/form?id='+row.id+'" class="hsBtnList" title="${text("编辑合同字段")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/contract/hsContractRecordField/delete?id='+row.id+'" class="btnList" title="${text("删除合同字段")}" data-confirm="${text("确认要删除该合同字段吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>