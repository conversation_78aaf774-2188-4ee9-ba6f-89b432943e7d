<% layout('/layouts/default.html', {title: '合同管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsContractRecord.isView == 1 ? '查看合同' : hsContractRecord.isNewRecord ? '合同导入' : '合同导入')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsContractRecord}" action="${ctx}/contract/hsContractRecord/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<#form:hidden name="contractSource" value="1" />
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="contractName" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同编号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="contractNo" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同类型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="contractType" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请人名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applyName" maxlength="20" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('申请人身份证')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applySfz" maxlength="18" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('联系电话')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="applyTel" maxlength="15" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同签订开始时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="startTime" readonly="true" maxlength="20" class="form-control laydate required"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同签订结束时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="endTime" readonly="true" maxlength="20" class="form-control laydate required"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('选填信息')}</div>
				<div class="row">

					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('工作单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="workUnit" maxlength="255" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('房屋地址')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="houseAddr" maxlength="512" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('单元号')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="unitNo" maxlength="64" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('面积')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="houseArea" maxlength="64" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('户型')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="houseType" maxlength="128" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('租金')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="houseRent" maxlength="16" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('合同描述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea rows="3" path="contractDesc" maxlength="255" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('文档材料')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2">
								<span class="required ">*</span> ${text('材料上传')}：</label>
							<div class="col-sm-10">
								<#form:fileupload id="uploadFile" bizKey="${hsContractRecord.id}" bizType="hsContractRecord_file"
									uploadType="all" class="required" readonly="false" preview="true" dataMap="true"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hsContractRecord.isView != 1 && hasPermi('contract:hsContractRecord:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>