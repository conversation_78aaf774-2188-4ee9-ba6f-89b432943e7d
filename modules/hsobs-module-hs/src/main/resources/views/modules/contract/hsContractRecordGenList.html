<% layout('/layouts/default.html', {title: '合同管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('合同管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('contract:hsContractRecord:edit')){ %>
					<a href="${ctx}/contract/hsContractRecord/genForm" class="btn btn-default btnTool" title="${text('新增合同')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsContractRecord}" action="${ctx}/contract/hsContractRecord/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			    <#form:hidden name="contractSource" value="0" />
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('合同名称')}：</label>
					<div class="control-inline">
						<#form:input path="contractName" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同编号')}：</label>
					<div class="control-inline">
						<#form:input path="contractNo" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同类型')}：</label>
					<div class="control-inline">
						<#form:input path="contractType" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('申请人姓名')}：</label>
					<div class="control-inline">
						<#form:input path="applyName" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('身份证')}：</label>
					<div class="control-inline">
						<#form:input path="applySfz" maxlength="18" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('联系电话')}：</label>
					<div class="control-inline">
						<#form:input path="applyTel" maxlength="15" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('签订时间')}：</label>
					<div class="control-inline">
						<#form:input path="startTime_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="endTime_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="endTime_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("ID")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/contract/hsContractRecord/genForm?id='+row.id+'" class="btnList" data-title="${text("编辑合同")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("合同名称")}', name:'contractName', index:'a.contract_name', width:150, align:"left"},
		{header:'${text("合同编号")}', name:'contractNo', index:'a.contract_no', width:150, align:"left"},
		{header:'${text("合同类型")}', name:'contractType', index:'a.contract_type', width:150, align:"left"},
		{header:'${text("申请人姓名")}', name:'applyName', index:'a.apply_name', width:150, align:"left"},
		{header:'${text("身份证")}', name:'applySfz', index:'a.apply_sfz', width:150, align:"left"},
		{header:'${text("联系电话")}', name:'applyTel', index:'a.apply_tel', width:150, align:"left"},
		{header:'${text("签订开始时间")}', name:'startTime', index:'a.start_time', width:150, align:"center"},
		{header:'${text("签订结束时间")}', name:'endTime', index:'a.end_time', width:150, align:"center"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('contract:hsContractRecord:edit')){
				actions.push('<a href="${ctx}/contract/hsContractRecord/genForm?id='+row.id+'" class="btnList" title="${text("编辑合同")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/contract/hsContractRecord/genFile?id='+row.id+'" class="btnList" title="${text("合同文件")}">合同</a>&nbsp;');
				actions.push('<a href="${ctx}/contract/hsContractRecord/delete?id='+row.id+'" class="btnList" title="${text("删除合同")}" data-confirm="${text("确认要删除该合同吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>