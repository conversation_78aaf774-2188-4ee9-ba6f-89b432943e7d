<% layout('/layouts/default.html', {title: '合同管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-body">
			<#form:form id="searchForm" model="${hsContractRecord}" action="${ctx}/contract/hsContractRecord/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('合同模板ID')}：</label>
					<div class="control-inline">
						<#form:input path="templateId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同名称')}：</label>
					<div class="control-inline">
						<#form:input path="contractName" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同编号')}：</label>
					<div class="control-inline">
						<#form:input path="contractNo" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同类型')}：</label>
					<div class="control-inline">
						<#form:input path="contractType" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同来源 0-生成 1-导入')}：</label>
					<div class="control-inline">
						<#form:input path="contractSource" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人名称')}：</label>
					<div class="control-inline">
						<#form:input path="applyName" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人身份证')}：</label>
					<div class="control-inline">
						<#form:input path="applySfz" maxlength="18" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('联系电话')}：</label>
					<div class="control-inline">
						<#form:input path="applyTel" maxlength="15" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline">
						<#form:input path="workUnit" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('房屋地址')}：</label>
					<div class="control-inline">
						<#form:input path="houseAddr" maxlength="512" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('单元号')}：</label>
					<div class="control-inline">
						<#form:input path="unitNo" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('面积')}：</label>
					<div class="control-inline">
						<#form:input path="houseArea" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('户型')}：</label>
					<div class="control-inline">
						<#form:input path="houseType" maxlength="128" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('租金')}：</label>
					<div class="control-inline">
						<#form:input path="houseRent" maxlength="16" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同签订开始时间')}：</label>
					<div class="control-inline">
						<#form:input path="startTime" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同签订结束时间')}：</label>
					<div class="control-inline">
						<#form:input path="endTime" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同描述')}：</label>
					<div class="control-inline">
						<#form:input path="contractDesc" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('是否有效;1-有效 0-无效')}：</label>
					<div class="control-inline">
						<#form:input path="validTag" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="row">
				<div class="col-xs-10 pr10">
					<table id="dataGrid"></table>
					<div id="dataGridPage"></div>
				</div>
				<div class="col-xs-2 pl0">
					<div id="selectData" class="tags-input"></div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("合同模板ID")}', name:'templateId', index:'a.template_id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return (val||row.id);
		}},
		{header:'${text("合同名称")}', name:'contractName', index:'a.contract_name', width:150, align:"left"},
		{header:'${text("合同编号")}', name:'contractNo', index:'a.contract_no', width:150, align:"left"},
		{header:'${text("合同类型")}', name:'contractType', index:'a.contract_type', width:150, align:"left"},
		{header:'${text("合同来源 0-生成 1-导入")}', name:'contractSource', index:'a.contract_source', width:150, align:"left"},
		{header:'${text("申请人名称")}', name:'applyName', index:'a.apply_name', width:150, align:"left"},
		{header:'${text("申请人身份证")}', name:'applySfz', index:'a.apply_sfz', width:150, align:"left"},
		{header:'${text("联系电话")}', name:'applyTel', index:'a.apply_tel', width:150, align:"left"},
		{header:'${text("工作单位")}', name:'workUnit', index:'a.work_unit', width:150, align:"left"},
		{header:'${text("房屋地址")}', name:'houseAddr', index:'a.house_addr', width:150, align:"left"},
		{header:'${text("单元号")}', name:'unitNo', index:'a.unit_no', width:150, align:"left"},
		{header:'${text("面积")}', name:'houseArea', index:'a.house_area', width:150, align:"left"},
		{header:'${text("户型")}', name:'houseType', index:'a.house_type', width:150, align:"left"},
		{header:'${text("租金")}', name:'houseRent', index:'a.house_rent', width:150, align:"left"},
		{header:'${text("合同签订开始时间")}', name:'startTime', index:'a.start_time', width:150, align:"center"},
		{header:'${text("合同签订结束时间")}', name:'endTime', index:'a.end_time', width:150, align:"center"},
		{header:'${text("合同描述")}', name:'contractDesc', index:'a.contract_desc', width:150, align:"left"},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("是否有效;1-有效 0-无效")}', name:'validTag', index:'a.valid_tag', width:150, align:"left"},
		{header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
			return JSON.stringify(row);
		}}
	],
	autoGridHeight: function(){
		var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
		$('.tags-input').height($('.ui-jqgrid').height() - 10);
		return height;
	},
	showCheckbox: '${parameter.checkbox}' == 'true',
	multiboxonly: false, // 单击复选框时再多选
	ajaxSuccess: function(data){
		$.each(selectData, function(key, value){
			dataGrid.dataGrid('setSelectRow', key);
		});
		initSelectTag();
	},
	onSelectRow: function(id, isSelect, event){
		if ('${parameter.checkbox}' == 'true'){
			if(isSelect){
				selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
			}else{
				delete selectData[id];
			}
		}else{
			selectData = {};
			selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
		}
		initSelectTag();
	},
	onSelectAll: function(ids, isSelect){
		if ('${parameter.checkbox}' == 'true'){
			for (var i=0; i<ids.length; i++){
				if(isSelect){
					selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
				}else{
					delete selectData[ids[i]];
				}
			}
		}
		initSelectTag();
	},
	ondblClickRow: function(id, rownum, colnum, event){
		if ('${parameter.checkbox}' != 'true'){
			js.layer.$('#' + window.name).closest('.layui-layer')
				.find(".layui-layer-btn0").trigger("click");
		}
		initSelectTag();
	}
});
function initSelectTag(){
	selectNum = 0;
	var html = [];
	$.each(selectData, function(key, value){
		selectNum ++;
		html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.templateId||value.id)+' </span>'
			+ '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
	});
	html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
	$('#selectData').empty().append(html.join(''));
}
function removeSelectTag(key){
	delete selectData[key];
	dataGrid.dataGrid('resetSelection', key);
	$('#selectNum').html(--selectNum);
	$('#'+key+'_tags-input').remove();
}
function getSelectData(){
	return selectData;
}
</script>