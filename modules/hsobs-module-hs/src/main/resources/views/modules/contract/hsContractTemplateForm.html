<% layout('/layouts/default.html', {title: '合同模板管理', libs: ['validate','ueditor','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsContractTemplate.isNewRecord ? '新增合同模板' : '编辑合同模板')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsContractTemplate}" action="${ctx}/contract/hsContractTemplate/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同模板名称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="contractName" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('合同模板版本')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="contractVersion" maxlength="255" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('合同模板描述')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea rows="4" path="contractDesc" maxlength="255" class="form-control"/>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('基础属性')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-1" title="">
								<span class="required hide">*</span> ${text('')}<i class="fa icon-question hide"></i></label>
							<div class="col-sm-11 table-form">
								<table id="baseFieldGrid"></table>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('补充属性')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-1" title="">
								<span class="required hide">*</span> ${text('')}<i class="fa icon-question hide"></i></label>
							<div class="col-sm-11 table-form">
								<table id="hsContractTemplateFieldGrid"></table>
								<% if (hasPermi('contract:hsContractTemplate:edit')){ %>
								<a href="#" id="hsContractTemplateFieldGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> ${text('增行')}</a>
								<% } %>
							</div>
						</div>
					</div>
				</div>


				<div class="form-unit">${text('合同模板')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-1">${text('正文')}：</label>
							<div class="col-sm-11">
								<#form:ueditor id="content" path="hsContractTemplateData.content" maxlength="10000" height="500" class="required" outline="${parameter.outline}"/>
							</div>
						</div>
					</div>
				</div>

			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('contract:hsContractTemplate:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>

$("#hsContractTemplateFieldGrid").dataGrid({
	data: "#{toJson(hsContractTemplate.hsContractTemplateFieldList)}",
	datatype: 'local', // 设置本地数据
	sortableColumn: false,
	columnModel: [
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'${text("字段名称")}', name:'fieldName', sortable:false, width:100,
			editable: true, edittype: "text", editoptions: {'maxlength':'100', 'class':'form-control'}
		},
		{header:'${text("字段属性编码")}', name:'fieldCode', sortable:false, width:100,
			editable:true, edittype:'text', editoptions:{'maxlength':'50', 'class':'form-control'}
		},
		{header:'${text("操作")}', name:'actions', width:80, sortable:false, fixed:true, formatter: function(val, obj, row, act){
				var actions = [];
				actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsContractTemplateFieldGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
				return actions.join('');
			}, editoptions: {defaultValue: 'new'}}
	],
	autoGridHeight: function(){return 'auto'}, // 设置自动高度

	// 编辑表格参数
	editGrid: true,				// 是否是编辑表格
	editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsContractTemplateFieldGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

	// 编辑表格的提交数据参数
	editGridInputFormListName: 'hsContractTemplateFieldList', // 提交的数据列表名
	editGridInputFormListAttrs: 'fieldName,fieldCode,id,status,createBy,createDate,updateBy,updateDate,', // 提交数据列表的属性字段

	ajaxSuccess: function(){

	}
});

// {header:'主键', name:'id', editable:true, hidden:true},

$("#baseFieldGrid").dataGrid({
	data: "#{toJson(baseFieldList)}",
	datatype: 'local', // 设置本地数据
	sortableColumn: false,
	columnModel: [
		{header:'${text("字段名称")}', name:'fieldName', sortable:false, width:100,
			editable: true, edittype: "text", editoptions: {'maxlength':'100', 'class':'form-control'}
		},
		{header:'${text("字段属性编码")}', name:'fieldCode', sortable:false, width:100,
			editable:true, edittype:'text', editoptions:{'maxlength':'50', 'class':'form-control'}
		}
	],
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	// 编辑表格参数
	editGrid: false,				// 是否是编辑表格
	ajaxSuccess: function(){

	}
});

$('#inputForm').validate({
	submitHandler: function(form){
		// $("#hsContractTemplateFieldGrid").dataGrid('reloadGrid');
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
	}
});

</script>