<% layout('/layouts/default.html', {title: '合同模板管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('合同模板管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('contract:hsContractTemplate:edit')){ %>
					<a href="${ctx}/contract/hsContractTemplate/form" class="btn btn-default btnTool" title="${text('新增合同模板')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsContractTemplate}" action="${ctx}/contract/hsContractTemplate/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('合同模板名称')}：</label>
					<div class="control-inline">
						<#form:input path="contractName" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同模板版本')}：</label>
					<div class="control-inline">
						<#form:input path="contractVersion" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("模板名称")}', name:'contractName', index:'a.contract_name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/contract/hsContractTemplate/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑合同模板")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("模板版本")}', name:'contractVersion', index:'a.contract_version', width:150, align:"left"},
		{header:'${text("模板描述")}', name:'contractDesc', index:'a.contract_desc', width:150, align:"left"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('contract:hsContractTemplate:edit')){
				actions.push('<a href="${ctx}/contract/hsContractTemplate/form?id='+row.id+'" class="hsBtnList" title="${text("编辑合同模板")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/contract/hsContractTemplate/delete?id='+row.id+'" class="btnList" title="${text("删除合同模板")}" data-confirm="${text("确认要删除该合同模板吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>