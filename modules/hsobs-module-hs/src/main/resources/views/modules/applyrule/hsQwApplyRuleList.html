<% layout('/layouts/default.html', {title: '租赁资格轮候规则配置管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text('租赁资格轮候规则配置管理')}
            </div>
            <div class="box-tools pull-right">
                <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
                <% if(hasPermi('applyrule:hsQwApplyRule:edit')){ %>
                    <a href="${ctx}/applyrule/hsQwApplyRule/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候规则配置')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
                <% } %>
                <a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
            </div>
        </div>
        <div class="box-body">
            <div class="search-form-container">
                <#form:form id="searchForm" model="${hsQwApplyRule}" action="${ctx}/applyrule/hsQwApplyRule/listData" method="post" class="form-inline hide"
                        data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
                    <!-- 第一行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('规则名称')}：</label>
                            <div class="control-inline">
                                <#form:input path="ruleName" maxlength="100" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('规则类型')}：</label>
                            <div class="control-inline width-120">
                                <#form:select path="ruleType" dictType="hs_apply_score_type" blankOption="true" class="form-control"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('规则配置')}：</label>
                            <div class="control-inline width-120">
                                <#form:select path="ruleConfig" dictType="hs_apply_score_config" blankOption="true" class="form-control"/>
                            </div>
                        </div>
                    </div>
                    <!-- 第二行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('规则编码')}：</label>
                            <div class="control-inline">
                                <#form:input path="ruleCode" maxlength="100" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('规则内容')}：</label>
                            <div class="control-inline">
                                <#form:input path="ruleContent" maxlength="100" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('规则结果')}：</label>
                            <div class="control-inline">
                                <#form:input path="ruleResult" maxlength="100" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>
                    <!-- 第三行：2个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('状态')}：</label>
                            <div class="control-inline width-120">
                                <#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('备注信息')}：</label>
                            <div class="control-inline">
                                <#form:input path="remarks" maxlength="500" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>
                    <!-- 按钮行 -->
                    <div class="search-button-row">
                        <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                        <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
                    </div>
                </#form:form>
            </div>
            <div class="fixed-table-container">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
        </div>
    </div>
</div>
<% } %>
<script>
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false, // 禁用自动调整列宽
    autowidth: false,  // 禁用自动宽度
    scroll: true,      // 启用滚动条
    scrollOffset: 18,
    width: '100%',     // 表格宽度
    height: 'auto',    // 表格高度自适应
    columnModel: [
        {header:'${text("规则名称")}', name:'ruleName', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
            return '<a href="${ctx}/applyrule/hsQwApplyRule/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候规则配置")}">'+(val||row.id)+'</a>';
        }},
        {header:'${text("规则类型")}', name:'ruleType', width:150, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_score_type')}", val, '${text("未知")}', true);
        }},
        {header:'${text("规则配置")}', name:'ruleConfig', width:150, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_score_config')}", val, '${text("未知")}', true);
        }},
        {header:'${text("规则编码")}', name:'ruleCode', width:150, align:"left"},
        {header:'${text("规则内容")}', name:'ruleContent', width:150, align:"left"},
        {header:'${text("规则结果")}', name:'ruleResult', width:150, align:"left"},
        {header:'${text("状态")}', name:'status', width:150, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
        }},
        {header:'${text("更新时间")}', name:'updateDate', width:150, align:"left"},
        {header:'${text("备注信息")}', name:'remarks', width:150, align:"left"},
        {header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
            var actions = [];
            //# if(hasPermi('applyrule:hsQwApplyRule:edit')){
                actions.push('<a href="${ctx}/applyrule/hsQwApplyRule/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁资格轮候规则配置")}">编辑</a>&nbsp;');
                if (row.status == Global.STATUS_NORMAL){
                    actions.push('<a href="${ctx}/applyrule/hsQwApplyRule/disable?id='+row.id+'" class="btnList" title="${text("停用租赁资格轮候规则配置")}" data-confirm="${text("确认要停用该租赁资格轮候规则配置吗？")}">停用</a>&nbsp;');
                } else if (row.status == Global.STATUS_DISABLE){
                    actions.push('<a href="${ctx}/applyrule/hsQwApplyRule/enable?id='+row.id+'" class="btnList" title="${text("启用租赁资格轮候规则配置")}" data-confirm="${text("确认要启用该租赁资格轮候规则配置吗？")}">启用</a>&nbsp;');
                }
                actions.push('<a href="${ctx}/applyrule/hsQwApplyRule/delete?id='+row.id+'" class="btnList" title="${text("删除租赁资格轮候规则配置")}" data-confirm="${text("确认要删除该租赁资格轮候规则配置吗？")}">删除</a>&nbsp;');
            //# }
            return actions.join('');
        }}
    ],
    loadComplete: function() {
        requestAnimationFrame(function() {
            CommonTable.setupFixedColumns('dataGrid');
        });
        js.closeLoading(0, true);
    }
});
</script>