<% layout('/modules/checkrecord/include/readForm.html', {title: '资格核查信息确认', isRead: true, submitUrl: '/checkrecord/hsQwCheckRecord/updateCompact', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('是否涉及违规')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:select path="violation" dictType="hs_qw_check_violation"  blankOptionLabel="" value="${hsQwCheckRecord.violation!}" readonly="true" class="form-control required" />
            </td>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('是否合同续签')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:select path="renewal" dictType="hs_qw_check_renewal"  blankOptionLabel="" value="${hsQwCheckRecord.renewal!}" readonly="true" class="form-control required" />
            </td>
        </tr>
    </table>
</div>

<div class="form-unit">${text('房源合同信息')}</div>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('合同编码')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="compact.compactCode" readonly="true" maxlength="255" class="form-control" value="${hsQwApply.compact.compactCode!}"/>
            </td>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('月租金')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="compact.monthFee"  class="form-control required number" value="${hsQwApply.compact.monthFee!}"/>
            </td>
        </tr>
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('合同开始时间')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="compact.startDate"  maxlength="20" class="form-control laydate"
                dataFormat="date" data-type="date" data-format="yyyy-MM-dd" value="${hsQwApply.compact.startDate!}"/>
            </td>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('合同结束时间')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="compact.endDate" maxlength="20" class="form-control laydate"
                dataFormat="date" data-type="date" data-format="yyyy-MM-dd" value="${hsQwApply.compact.endDate!}"/>
            </td>
        </tr>
        <tr>
            <td class="form-label hs-form-label">
                <span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
            </td>
            <td colspan="3">
                <#form:textarea path="compact.remarks"  rows="4" maxlength="500" class="form-control" value="${hsQwApply.compact.remarks!}"/>
            </td>
        </tr>
        <tr>
            <td class="form-label hs-form-label">
                <span class="required hide">*</span> ${text('合同文件上传')}：
            </td>
            <td colspan="3">
                <#form:fileupload id="uploadCompact" bizKey="${hsQwApply.compact.id!}" bizType="hsQwApply_compact"
                uploadType="file" class=""  preview="true" dataMap="true"/>
            </td>
        </tr>
    </table>
</div>
<% } %>