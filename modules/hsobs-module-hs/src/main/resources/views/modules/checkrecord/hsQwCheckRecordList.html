<% layout('/layouts/default.html', {title: '租赁资格核查台账表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租赁资格核查台账表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('checkrecord:hsQwCheckRecord:add')){ %>
<!--					<a href="${ctx}/checkrecord/hsQwCheckRecord/form" class="btn btn-default btnTool" title="${text('新增租赁资格核查')}"><i class="fa fa-plus"></i> ${text('新增')}</a>-->
					<a href="#" class="btn btn-default" id="btnAddRecord" title="${text('新增租赁资格核查')}"><i class="fa fa-plus"></i> ${text('新增核查')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwCheckRecord}" action="${ctx}/checkrecord/hsQwCheckRecord/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('处理状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="sys_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('核查状态')}：</label>
							<div class="control-inline">
								<#form:select path="violation" dictType="hs_qw_check_violation" blankOption="true" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('核查日期')}：</label>
							<div class="control-inline">
								<#form:input path="createDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="rentTime_lte.click()"/>
								&nbsp;-&nbsp;
								<#form:input path="createDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<div class="hide">
	<#form:listselect id="recordSelect" title="房源选择选择"
	url="${ctx}/checkrecord/hsQwCheckRecord/houseSelect" allowClear="false"
	checkbox="true" itemCode="id" itemName="simpleInfo" />
</div>

<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("id")}', name:'id',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/checkrecord/hsQwCheckRecord/formAll?id=' + row.id +'" class="btnList" title="${text("资格核查信息")}">'+(val||row.id)+'</a>';
			}},
		{header:'${text("申请单信息")}', name:'applyId',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/form?id='+row.applyId+'" class="hsBtnList" data-title="${text("申请单信息")}">'+row.applyId+'</a>';
			}},
		{header:'${text("承租人")}', name:'hsQwApply.mainApplyer.name',  sortable:false, width:120, align:"left"},
		{header:'${text("身份证号")}', name:'hsQwApply.mainApplyer.idNum',  sortable:false, width:150, align:"left"},
		{header:'${text("联系方式")}', name:'hsQwApply.mainApplyer.phone',  sortable:false, width:120, align:"left"},
		{header:'${text("工作单位")}', name:'hsQwApply.mainApplyer.organization',  sortable:false, width:150, align:"left"},
		{header:'${text("承租楼盘")}', name:'house.estate.name',  sortable:false, width:150, align:"left"},
		{header:'${text("户型")}', name:'house.houseType',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_house_type')}", val, '${text("未知")}', true);
			}},
		{header:'${text("租金")}', name:'compact.monthFee',  sortable:false, width:100, align:"left"},
		{header:'${text("核查状态")}', name:'violation',  sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_qw_check_violation')}", val, '${text("未知")}', true);
			}},
		{header:'${text("处理状态")}', name:'status',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
			}},
		{header:'${text("核查日期")}', name:'createDate',  sortable:false, width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
				var actions = [];
				//# if(hasPermi('checkrecord:hsQwCheckRecord:edit')){
				actions.push('<a href="${ctx}/checkrecord/hsQwCheckRecord/formAll?id=' + row.id+'" class="btnList" title="${text("编辑租赁资格核查台账表")}">编辑</a>&nbsp;');
				// actions.push('<a href="${ctx}/checkrecord/hsQwCheckRecord/delete?id='+row.id+'" class="btnList" title="${text("删除租赁资格核查台账表")}" data-confirm="${text("确认要删除该租赁资格核查台账表吗？")}">删除</a>&nbsp;');
				//# }
				if (row.status != Global.STATUS_DRAFT){
					actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=rent_apply_check&bizKey='+row.id+'" class="hsBtnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
				}
				return actions.join('');
			}}
	],
	loadComplete: function() {
		// 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
		requestAnimationFrame(function() {
			CommonTable.setupFixedColumns('dataGrid');
		});
		js.closeLoading(0, true);
	}
});
$('#btnAddRecord').click(function(){
	$('#recordSelectDiv').attr('data-url', '${ctx}/checkrecord/hsQwCheckRecord/houseSelect');
	$('#recordSelectCode').val('');
	$('#recordSelectName').val('').click();
});
function listselectCallback(id, action, index, layero){
	if (id == 'recordSelect' && action == 'ok'){
		if ($('#recordSelectCode').val() != ''){
			js.confirm('是否选择这些房屋进行资格核验‘' + $('#recordSelectName').val() + '’',
					'${ctx}/checkrecord/hsQwCheckRecord/recordSave?houseIdStr=' + $('#recordSelectCode').val(),
					function(data){
						js.showMessage(data.message);
					},'json', true, '正在生成资格核验单...');
			$('#dataGrid').refresh();
		}else{
			js.showMessage('${text("没有选择要核验的申请单！")}');
		}
	}
}

$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/checkrecord/hsQwCheckRecord/exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
</script>

<script>
	// 初始化公共表格功能
	CommonTable.init('dataGrid');
</script>