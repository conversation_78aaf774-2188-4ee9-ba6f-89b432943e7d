<% layout('/modules/checkrecord/include/readForm.html', {title: '资格核查信息确认', isRead: true, submitUrl: '/checkrecord/hsQwCheckRecord/update', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('是否涉及违规')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:select path="violation" dictType="hs_qw_check_violation"  blankOptionLabel="" value="${hsQwCheckRecord.violation!}" readonly="true" class="form-control required" />
            </td>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('是否申诉')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:select path="complaint" dictType="hs_qw_check_complaint"  blankOptionLabel="" value="${hsQwCheckRecord.complaint!}" readonly="true" class="form-control required" />
            </td>
        </tr>
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('申诉是否合理')}：<i class="fa icon-question hide"></i>
            </td>
            <td colspan="3">
                <#form:select path="reasonable" dictType="hs_qw_check_reasonable"  class="form-control required" />
            </td>
        </tr>
    </table>
</div>
<% } %>