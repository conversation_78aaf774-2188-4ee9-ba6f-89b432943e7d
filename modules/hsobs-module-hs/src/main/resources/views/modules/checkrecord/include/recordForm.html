<% layout('/layouts/default.html', {title: '租赁资格核查台账表管理', libs: ['validate']}){ %>
<div class="main-content">
	<% if(!hsQwCheckRecord.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsQwCheckRecord}" title="租赁资格核查" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsQwCheckRecord}" formKey="rent_apply_check" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${title}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwCheckRecord}" action="${ctx}/checkrecord/hsQwCheckRecord/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('申请单id')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="applyId" maxlength="64" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('合同id')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="compactId" maxlength="64" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('房源id')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="houseId" maxlength="64" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('核查状态')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:select path="violation" dictType="hs_qw_check_violation" class="form-control required" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('是否合同续签')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:select path="renewal" dictType="hs_qw_check_renewal" class="form-control required" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
							</td>
							<td colspan="3">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
							</td>
						</tr>
					</table>
				</div>
				${layoutContent!}
				<div class="hs-table-div taskComment hide">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">审批意见：</td>
							<td>
								<#bpm:comment bpmEntity="${hsQwCheckRecord}" showCommWords="true" />
							</td>
						</tr>
					</table>
				</div>
				<#bpm:nextTaskInfo bpmEntity="${hsQwCheckRecord}" />
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('checkrecord:hsQwCheckRecord:edit')){ %>
							<#form:hidden path="status"/>
							<% if (hsQwCheckRecord.isNewRecord || hsQwCheckRecord.status == '9'){ %>
								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
							<% } %>
							<#bpm:button bpmEntity="${hsQwCheckRecord}" formKey="rent_apply_check" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};
// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>