<% layout('/layouts/default.html', {title: '个人信息变更管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
    <% if(!hsQwApply.isNewRecord){ %>
    <div class="box box-main" style="margin-bottom: 10px;">
        <div class="box-header with-border">
            <div class="hide" style="display: flex;justify-content: space-between;">
                <div class="box-title">
                </div>
                <div class="box-tools pull-right ">
                    <button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
                </div>
            </div>
            <div class="row">
                <#common:viewport entity="${hsQwCheckRecord}" title="资格核验申请" />
            </div>
        </div>
        <div class="box-body">
            <#common:steps entity="${hsQwCheckRecord}" formKey="rent_apply_check" />
        </div>
    </div>
    <% } %>
    <div class="box box-main">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa icon-note"></i> ${text('资格核验个人信息核验')}
            </div>
            <div class="box-tools pull-right hide">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
        </div>
        <#form:form id="inputForm" model="${hsQwApply}" action="${ctx}${submitUrl}" method="post" class="form-horizontal">
        <#form:hidden name="id" value="${hsQwCheckRecord.id}"/>
        <div class="box-body hs-box-body-bpm">
            <div class="form-unit">${text('基本信息')}</div>
            <div class="hs-table-div" >
                <table class="table-form hs-table-form" >
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('核查时间')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="checkDate"  maxlength="20" class="form-control disabled laydate required"
                            dataFormat="date" data-type="date" data-format="yyyy-MM-dd" defaultValue="${hsQwCheckRecord.checkDate}"/>
                        </td>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('核查类型')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:select path="violationType" readonly="true" dictType="hs_qw_check_violation_type" class="form-control required" defaultValue="${hsQwCheckRecord.violationType}" />
                        </td>
                    </tr>
                    <#hs:apply readonly="${isRead}" entity="${hsQwApply}"/>
                </table>
            </div>
            <div class="form-unit">${text('个人住房变更人')}</div>
                <#hs:applyer path="hsQwApplyerList" value="${toJson(hsQwApply.hsQwApplyerList)}" readonly="${isRead}" />
            <div class="form-unit">${text('个人住房变更人房屋情况表')}</div>
                <#hs:applyhouse value="${toJson(hsQwApply.hsQwApplyHouseList)}" readonly="${isRead}" />
            <div class="form-unit">${text('证明材料')}</div>
            <#hs:applyfile readonly="${isRead}" entity="${hsQwApply}" />
            <div class="form-unit">${text('租金信息')}</div>
            <#form:hidden path="applyId"/>
            <#form:hidden path="checkType"/>
            <#form:hidden path="checkResult"/>
            <div class="hs-table-div">
                <table class="table-form hs-table-form">
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('租金拖欠')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            ${hsQwManagementCheck.hasRentalFee!'否'}
                        </td>
                    </tr>
                </table>
            </div>
            <div class="form-unit-wrap table-form">
                <table id="hsQwApplyRecordRentalFeeDataGrid" class="table-form hs-table-form"></table>
            </div>
            <div class="form-unit">${text('入户核验信息')}</div>
            <div class="form-unit-wrap table-form">
                <table id="hsQwApplyRecordObjectCheckDataGrid" class="table-form hs-table-form"></table>
            </div>
            <div class="hs-table-div">
                <table class="table-form hs-table-form">
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('入户核验证明图片上传')}：
                        </td>
                        <td>
                            <#form:fileupload id="uploadFile" bizKey="${hsQwManagementCheck.id}" bizType="hsQwManagementCheck_file"
                            uploadType="all" class=""  preview="true" dataMap="true" />
                        </td>
                    </tr>
                </table>
            </div>
            ${layoutContent!}
            <div class="form-unit">${text('审核操作')}</div>
            <div class="hs-table-div" >
                <table class="table-form hs-table-form" >
                    <tr>
                        <td class="form-label hs-form-label">
                            ${text('审批意见：')}：
                        </td>
                        <td colspan="3">
                            <#bpm:comment bpmEntity="${hsQwApply}" showCommWords="true" />
                        </td>
                    </tr>
                </table>
            </div>

        </div>
        <div class="box-footer">
            <div class="row">
                <div class="col-sm-offset-2 col-sm-10">
                    <% if (hasPermi('apply:hsQwApply:edit')){ %>
                    <#form:hidden path="status"/>
                    <#form:hidden path="applyStatus" defaultValue = "${applyStatusPage!}"/>
                    <#form:hidden path="applyedId" defaultValue = "${hsQwApply.id}"/>
                    <#form:hidden path="applyMatter" defaultValue="9"/>
                    <#bpm:button bpmEntity="${hsQwCheckRecord}" formKey="rent_apply_check" completeText="提 交"/>
                    <% } %>
                    <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
                </div>
            </div>
        </div>
    </#form:form>
</div>
</div>
<% } %>
<script src="${ctxStatic}/form-compare/form-compare.js"></script>
<script>
    //# // 初始化租金欠缴信息DataGrid对象
    $('#hsQwApplyRecordRentalFeeDataGrid').dataGrid({

        data: "#{toJson(hsQwManagementCheck.hsQwRentalFeeList)}",
        datatype: 'local', // 设置本地数据
        autoGridHeight: function(){return 'auto'}, // 设置自动高度

        //# // 设置数据表格列
        columnModel: [
            {header:'主键', name:'id', editable:true, hidden:true},
            {header:'${text("费用")}', name:'rentalFee', width:150, editable:false, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required realName'}},
            {header:'${text("缴费周期")}', name:'feeMonth', width:150, editable:false, edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
            {header:'${text("预期缴费时间")}', name:'expectFeeDate', width:150, editable:false, edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required idcard'}},
            {
                header: '${text("缴费状态")}', name: 'status', sortable: false, width: 150, align: "center",
                formatter: function (val, obj, row, act) {
                    var label = js.getDictLabel("#{@DictUtils.getDictListJson('hs_rental_fee_status')}", val, '${text("未知")}', true);
                    var style = '';
                    if (val == '0') { // 未缴费
                        style = 'background-color:#ffcccc;'; // 红色背景
                    } else if (val == '1') { // 已缴费
                        style = 'background-color:#ccffcc;'; // 绿色背景
                    }
                    return '<div style="' + style + 'padding:5px;">' + label + '</div>';
                }
            },
            {header:'${text("租金类型")}', name:'feeType', width:100,formatter: function(val, obj, row, act){
                    return js.getDictLabel("#{@DictUtils.getDictListJson('hs_rental_fee_type')}", val, '${text("未知")}', true);
                }},
        ],

        //# // 编辑表格参数
        editGrid: true,				// 是否是编辑表格
        editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
        editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
        editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

        //# // 编辑表格的提交数据参数
        editGridInputFormListName: 'hsQwRentalFeeList', // 提交的数据列表名
        editGridInputFormListAttrs: 'id,rentalFee,feeMonth,expectFeeDate,status,feeType,', // 提交数据列表的属性字段

        //# // 加载成功后执行事件
        ajaxSuccess: function(data){

        }
    });
    //# // 初始化房屋核查物品DataGrid对象
    $('#hsQwApplyRecordObjectCheckDataGrid').dataGrid({

        data: "#{toJson(hsQwManagementCheck.hsQwObjectCheckList)}",
        datatype: 'local', // 设置本地数据
        autoGridHeight: function(){return 'auto'}, // 设置自动高度

        //# // 设置数据表格列
        columnModel: [
            {header:'主键', name:'id', editable:true, hidden:true},
            {header:'${text("核验物品id")}', name:'objectId', width:60, editable:false, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required'}},
            {header:'${text("核验物品名称")}', name:'objectName', width:100, editable:false, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required'}},
            {header:'${text("是否损坏")}', name:'damage', width:135, fixed: true,
                editable: true, edittype:'radio', editoptions:{'class':'form-control icheck',
                    items: $.merge([], "#{@DictUtils.getDictListJson('hs_management_check_damage')}"),
                    itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
                        js.iCheck(element).on("ifChanged",function(){$(this).resetValid()});
                    }
                }
            },
            {header:'${text("是否维修")}', name:'maintenance', width:135, fixed: true,
                editable: true, edittype:'radio', editoptions:{'class':'form-control icheck',
                    items: $.merge([], "#{@DictUtils.getDictListJson('hs_management_check_maintenance')}"),
                    itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
                        js.iCheck(element).on("ifChanged",function(){$(this).resetValid()});
                    }
                }
            },
        ],

        //# // 编辑表格参数
        editGrid: true,					// 是否是编辑表格
        editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
        editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
        editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

        //# // 编辑表格的提交数据参数
        editGridInputFormListName: 'hsQwObjectCheckList', // 提交的数据列表名
        editGridInputFormListAttrs: 'id,objectId,objectName,damage,maintenance', // 提交数据列表的属性字段

        //# // 加载成功后执行事件
        ajaxSuccess: function(data){

        }
    });
</script>
<script>
    // 业务实现草稿按钮
    $('#btnDraft').click(function(){
        $('#status').val(Global.STATUS_DRAFT);
        $('#applyStatus').val('草稿');
    });
    // 流程按钮操作事件
    BpmButton = window.BpmButton || {};
    BpmButton.init = function(task){
        if (task.status != '2') {
            $('.taskComment').removeClass('hide');
        }
    }
    BpmButton.complete = function($this, task){
        $('#status').val(Global.STATUS_AUDIT);
        $("#houseIdName").val('');
    };
    // 表单验证提交事件
    $('#inputForm').validate({
        submitHandler: function(form){
            js.ajaxSubmitForm($(form), function(data){
                js.showMessage(data.message);
                if(data.result == Global.TRUE){
                    js.closeCurrentTabPage(function(contentWindow){
                        contentWindow.page();
                    });
                }
            }, "json");
        }
    });

</script>

<script>
    // 初始化表单比对
    $(document).ready(function() {
        // 获取后台传递的变更信息，并进行判空处理
        var changeMap = {};
        <% if (hsQwApply != null && hsQwApply.changeMap != null) { %>
            try {
                changeMap = ${toJson(hsQwApply.changeMap)};
            } catch (e) {
                console.log('变更信息解析失败', e);
            }
            <% } %>

        // 初始化表单比对
        $('#inputForm').formCompare({
            changeMap: changeMap,
            onDismiss: function(params) {
                // params.element: 发生变更的元素
                // params.message: 变更信息
                // params.success: 成功回调函数
                // params.error: 错误回调函数

                // 调用后台API删除记录
                $.ajax({
                    url: '${ctx}/formalarm/hsQwFormAlarm/dismiss',
                    type: 'POST',
                    data: {
                        objectId: '${hsQwApply.id}',
                        attrKey: params.key,
                        message: params.message
                    },
                    success: function(response) {
                        if (response.result) {
                            params.success();
                            // 刷新页面
                            location.reload();
                        } else {
                            params.error(response.message);
                        }
                    },
                    error: function(xhr) {
                        params.error(xhr.responseText);
                    }
                });
            },
            confirmMessage: '确定要忽略这条提醒吗？' // 可选，自定义确认提示文字

        });
    });
</script>