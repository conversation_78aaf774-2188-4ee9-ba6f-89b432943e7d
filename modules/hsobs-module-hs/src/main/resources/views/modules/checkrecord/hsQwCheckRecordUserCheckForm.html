<% layout('/modules/checkrecord/include/readForm.html', {title: '资格核查信息确认', isRead: true, submitUrl: '/checkrecord/hsQwCheckRecord/update', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('是否涉及违规')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:select path="violation" dictType="hs_qw_check_violation"  blankOptionLabel="" value="${hsQwCheckRecord.violation!}" readonly="true" class="form-control required" />
            </td>
            <td class="form-label hs-form-label">
                <span class="required ">*</span> ${text('是否进行申诉')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:select path="complaint" dictType="hs_qw_check_complaint"  class="form-control required" />
            </td>
        </tr>
    </table>
</div>
<% } %>