<% layout('/layouts/default.html', {title: '数据表单下发发送记录表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-body">
			<#form:form id="searchForm" model="${hsDataFormDeliveryRecord}" action="${ctx}/formmanage/hsDataFormDeliveryRecord/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('所属消息')}：</label>
					<div class="control-inline">
						<#form:input path="deliveryId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('接受者用户编码')}：</label>
					<div class="control-inline">
						<#form:input path="receiveUserCode" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('接受者用户姓名')}：</label>
					<div class="control-inline">
						<#form:input path="receiveUserName" maxlength="100" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('填写状态 ')}：</label>
					<div class="control-inline">
						<#form:input path="fillStatus" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('填写时间')}：</label>
					<div class="control-inline">
						<#form:input path="fillDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('读取状态')}：</label>
					<div class="control-inline">
						<#form:input path="readStatus" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('阅读时间')}：</label>
					<div class="control-inline">
						<#form:input path="readDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('是否标星')}：</label>
					<div class="control-inline">
						<#form:input path="isStar" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="row">
				<div class="col-xs-10 pr10">
					<table id="dataGrid"></table>
					<div id="dataGridPage"></div>
				</div>
				<div class="col-xs-2 pl0">
					<div id="selectData" class="tags-input"></div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("所属消息")}', name:'deliveryId', index:'a.delivery_id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return (val||row.id);
		}},
		{header:'${text("接受者用户编码")}', name:'receiveUserCode', index:'a.receive_user_code', width:150, align:"left"},
		{header:'${text("接受者用户姓名")}', name:'receiveUserName', index:'a.receive_user_name', width:150, align:"left"},
		{header:'${text("填写状态 ")}', name:'fillStatus', index:'a.fill_status', width:150, align:"left"},
		{header:'${text("填写时间")}', name:'fillDate', index:'a.fill_date', width:150, align:"center"},
		{header:'${text("读取状态")}', name:'readStatus', index:'a.read_status', width:150, align:"left"},
		{header:'${text("阅读时间")}', name:'readDate', index:'a.read_date', width:150, align:"center"},
		{header:'${text("是否标星")}', name:'isStar', index:'a.is_star', width:150, align:"left"},
		{header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
			return JSON.stringify(row);
		}}
	],
	autoGridHeight: function(){
		var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
		$('.tags-input').height($('.ui-jqgrid').height() - 10);
		return height;
	},
	showCheckbox: '${parameter.checkbox}' == 'true',
	multiboxonly: false, // 单击复选框时再多选
	ajaxSuccess: function(data){
		$.each(selectData, function(key, value){
			dataGrid.dataGrid('setSelectRow', key);
		});
		initSelectTag();
	},
	onSelectRow: function(id, isSelect, event){
		if ('${parameter.checkbox}' == 'true'){
			if(isSelect){
				selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
			}else{
				delete selectData[id];
			}
		}else{
			selectData = {};
			selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
		}
		initSelectTag();
	},
	onSelectAll: function(ids, isSelect){
		if ('${parameter.checkbox}' == 'true'){
			for (var i=0; i<ids.length; i++){
				if(isSelect){
					selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
				}else{
					delete selectData[ids[i]];
				}
			}
		}
		initSelectTag();
	},
	ondblClickRow: function(id, rownum, colnum, event){
		if ('${parameter.checkbox}' != 'true'){
			js.layer.$('#' + window.name).closest('.layui-layer')
				.find(".layui-layer-btn0").trigger("click");
		}
		initSelectTag();
	}
});
function initSelectTag(){
	selectNum = 0;
	var html = [];
	$.each(selectData, function(key, value){
		selectNum ++;
		html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.deliveryId||value.id)+' </span>'
			+ '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
	});
	html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
	$('#selectData').empty().append(html.join(''));
}
function removeSelectTag(key){
	delete selectData[key];
	dataGrid.dataGrid('resetSelection', key);
	$('#selectNum').html(--selectNum);
	$('#'+key+'_tags-input').remove();
}
function getSelectData(){
	return selectData;
}
</script>