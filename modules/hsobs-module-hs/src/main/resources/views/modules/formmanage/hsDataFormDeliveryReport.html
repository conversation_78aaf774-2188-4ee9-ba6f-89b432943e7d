<% layout('/layouts/default.html', {title: '数据报送', libs: ['validate']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa icon-note"></i> ${text(hsDataFormDelivery.isNewRecord ? '数据报送' : '数据报送')}
            </div>
            <div class="box-tools pull-right hide">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
        </div>
        <#form:form id="inputForm" model="${hsDataFormDelivery}" action="${ctx}/formmanage/hsDataFormDelivery/saveReport" method="post" class="form-horizontal">
        <div class="box-body">
            <#form:hidden path="ids"/>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="control-label col-sm-4" title="">
                            <span class="required ">*</span> ${text('接收单位')}：<i class="fa icon-question hide"></i></label>
                        <div class="col-sm-8">
                            <#form:treeselect id="receive" title="${text('接收单位选择')}"
                            path="receiveCodes" labelPath="receiveNames"
                            url="${ctx}/sys/office/treeData?isAll=true"
                            class=" required" allowClear="true" checkbox="true"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="box-footer">
            <div class="row">
                <div class="col-sm-offset-2 col-sm-10">
                    <% if (hsDataFormDelivery.isNewRecord && hasPermi('formmanage:hsDataFormDelivery:edit')){ %>
                    <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
                    <% } %>
                    <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
                </div>
            </div>
        </div>
    </#form:form>
</div>
</div>
<% } %>
<script>

    $('#inputForm').validate({
        submitHandler: function(form){
            js.ajaxSubmitForm($(form), function(data){
                js.showMessage(data.message);
                if(data.result == Global.TRUE){
                    js.closeCurrentTabPage(function(contentWindow){
                        contentWindow.page();
                    });
                }
            }, "json");
        }
    });
</script>