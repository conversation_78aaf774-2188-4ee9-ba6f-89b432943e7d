<% layout('/layouts/default.html', {title: '数据表单下发管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('数据表单下发管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('formmanage:hsDataFormDelivery:edit')){ %>
					<a href="${ctx}/formmanage/hsDataFormDelivery/form" class="btn btn-default btnTool" title="${text('新增数据表单下发')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsDataFormDelivery}" action="${ctx}/formmanage/hsDataFormDelivery/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('标题')}：</label>
					<div class="control-inline">
						<#form:input path="msgTitle" maxlength="200" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('填写说明')}：</label>
					<div class="control-inline">
						<#form:input path="msgContent" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('模板名称')}：</label>
					<div class="control-inline">
						<#form:input path="formTemplate.formName" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
			    <div class="form-group">
				    <label class="control-label">${text('表单类型')}：</label>
				    <div class="control-inline ">
					    <#form:select path="formTemplate.formType" dictType="data_form_type" blankOption="true" class="form-control " />
				    </div>
			    </div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("ID")}', hidden: true, name:'id', index:'a.id', width:150, align:"left"},
		{header:'${text("标题")}', name:'msgTitle', index:'a.msg_title', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/formmanage/hsDataFormDelivery/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑数据表单下发")}">'+(val||row.id)+'</a>';
		}},

		{header:'${text("模板名称")}', name:'formTemplate.formName', index:'a.form_template.form_name', width:150, align:"left"},
		{header:'${text("表单类型")}', name:'formTemplate.formType', index:'a.form_template.form_type', width:90, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('data_form_type')}", val, '${text("无")}', true);
			}},
		{header:'${text("填写说明")}', name:'msgContent', index:'a.msg_content', width:150, align:"left"},
		{header:'${text("发送者用户编码")}', name:'sendUserCode', index:'a.send_user_code', width:150, align:"left"},
		{header:'${text("发送者用户姓名")}', name:'sendUserName', index:'a.send_user_name', width:150, align:"left"},
		{header:'${text("截止时间")}', name:'limitDate', index:'a.limit_date', width:150, align:"center"},
		{header:'${text("发送时间")}', name:'sendDate', index:'a.send_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('formmanage:hsDataFormDelivery:edit')){
				actions.push('<a href="${ctx}/formmanage/hsDataFormDelivery/form?id='+row.id+'" class="hsBtnList" title="${text("查看数据表单下发")}">查看</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/formmanage/hsDataFormDelivery/disable?id='+row.id+'" class="btnList" title="${text("停用数据表单下发")}" data-confirm="${text("确认要停用该数据表单下发吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/formmanage/hsDataFormDelivery/enable?id='+row.id+'" class="btnList" title="${text("启用数据表单下发")}" data-confirm="${text("确认要启用该数据表单下发吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/formmanage/hsDataFormDelivery/delete?id='+row.id+'" class="btnList" title="${text("删除数据表单下发")}" data-confirm="${text("确认要删除该数据表单下发吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>