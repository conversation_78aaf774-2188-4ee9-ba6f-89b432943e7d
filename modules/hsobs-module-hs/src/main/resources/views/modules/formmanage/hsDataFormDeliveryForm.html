<% layout('/layouts/default.html', {title: '数据表单下发管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsDataFormDelivery.isNewRecord ? '新增数据表单下发' : '查看数据表单下发')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsDataFormDelivery}" action="${ctx}/formmanage/hsDataFormDelivery/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('标题')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="msgTitle" maxlength="200" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('表单模板')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:listselect id="template" title="表单模板选择"
								path="templateCode" labelPath="templateName"
								url="${ctx}/formmanage/hsDataFormTemplate/hsDataFormTemplateSelect?isPublic=1&type=0" allowClear="true"
								checkbox="false" class=" required" itemCode="id" itemName="formName"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('截止时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="limitDate" readonly="true" maxlength="20" class="form-control laydate required"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('填写说明')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea rows="3" path="msgContent" class="form-control "/>
							</div>
						</div>
					</div>

				</div>
				<div class="form-unit">${text('接受者信息')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required ">*</span> ${text('接受者')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:radio path="receiveType" dictType="msg_inner_receiver_type" class="form-control required" />
								<#form:treeselect id="receive" title="${text('接受者选择')}"
								path="receiveCodes" labelPath="receiveNames"
								url="${ctx}/sys/office/treeData?isLoadUser=true"
								class=" required" allowClear="true" checkbox="true"/>
							</div>
						</div>
					</div>
				</div>
				<% if(!hsDataFormDelivery.isNewRecord){ %>
				<div class="form-unit">${text('发送者信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('发送者')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="sendUserName" readonly="true" maxlength="100" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('发送时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="sendDate" dataFormat="datetime" readonly="true" maxlength="20" class="form-control required"/>
							</div>
						</div>
					</div>
				</div>
				<% } %>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hsDataFormDelivery.isNewRecord && hasPermi('formmanage:hsDataFormDelivery:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>

$('#receiveType input').on('ifCreated ifChecked', function(event){
	if ($(this).is(':checked')){
		var url, val = $(this).val(); // 接受者类型（1用户 2部门 3角色 4岗位）
		if (val == '0'){
			$('#receiveDiv').hide();
			$('#receiveName').removeClass('required');
		}else{
			$('#receiveDiv').show();
			$('#receiveName').addClass('required');
			if (val == '1'){
				url = '${ctx}/sys/office/treeData?isLoadUser=true&isAll=true';
			}else if (val == '2'){
				url = '${ctx}/sys/office/treeData?isAll=true';
			}else if (val == '3'){
				url = '${ctx}/sys/role/treeData?isAll=true';
			}else if (val == '4'){
				url = '${ctx}/sys/post/treeData?isAll=true';
			}
			$('#receiveDiv').attr('data-url', url);
		}
		if (event.type != 'ifCreated'){
			$('#receiveCode,#receiveName').val('');
		}
	}
});

$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>