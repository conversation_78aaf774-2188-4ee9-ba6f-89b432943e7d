<% layout('/layouts/default.html', {title: '数据表单填写', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsDataFormFill.isNewRecord ? '数据表单填写' : '数据表单填写')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsDataFormFill}" action="${ctx}/formmanage/hsDataFormFill/save" method="post" class="form-horizontal">
			<div class="box-body">

				<div class="row">
					<div class="col-xs-12">
						<div class="col-sm-offset-2 col-sm-8 ">
							<p class="text-center">
								<strong>填写情况统计</strong>
							</p>
							<div class="progress-group">
								<span class="progress-text">已填写量</span>
								<span class="progress-number"><b>${hsDataFormFill.filledTotal}</b>/${hsDataFormFill.deliveryTotal}</span>
								<div class="progress sm">
									<div class="progress-bar progress-bar-aqua" style="width: ${hsDataFormFill.filledTotal * 100 / hsDataFormFill.deliveryTotal}%"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<#form:hidden path="deliveryId"/>
				<#form:hidden path="deliveryRecordId"/>
				<#form:hidden path="templateId"/>
				<#form:hidden path="opType"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required ">*</span> ${text('标题')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="delivery.msgTitle" readonly="true" maxlength="200" class="form-control required"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('填写说明')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea rows="3"  readonly="true" path="delivery.msgContent" class="form-control "/>
							</div>
						</div>
					</div>
				</div>


				<div class="form-unit">${text('填写信息')}</div>

				<% var fieldDataList = hsDataFormFill.recordList;
				   if(hsDataFormDeliveryRecord.opType == 'upload'){fieldDataList=hsDataFormFill.recordFnlList;}
				for(var field in fieldDataList){ %>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required <% if (field.templateField.requiredTag != '1'){ %>hide<% } %>">*</span> ${field.templateField.fieldName}(${field.templateField.fieldKey})：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<% if (field.templateField.fieldType == '1'){ %>
								<#form:input name="recordList[${field.templateField.index}].fieldValue" value="${field.fieldValue}" maxlength="255" class="form-control ${field.templateField.requiredTag != '1' ? '': 'required'} "/>
								<% } else if (field.templateField.fieldType == '2'){ %>
								<#form:textarea rows="3" name="recordList[${field.templateField.index}].fieldValue" value="${field.fieldValue}" maxlength="255" class="form-control ${field.templateField.requiredTag != '1' ? '': 'required'} "/>
								<% } else if (field.templateField.fieldType == '3'){ %>
								<#form:radio name="recordList[${field.templateField.index}].fieldValue" value="${field.fieldValue}" items="${field.templateField.items}" itemLabel="label" itemValue="value" maxlength="255" class="form-control ${field.templateField.requiredTag != '1' ? '': 'required'} "/>
								<% } else if (field.templateField.fieldType == '4'){ %>
								<#form:checkbox name="recordList[${field.templateField.index}].fieldValue" value="${field.fieldValue}" items="${field.templateField.items}" itemLabel="label" itemValue="value" maxlength="255" class="form-control ${field.templateField.requiredTag != '1' ? '': 'required'} "/>
								<% } else if (field.templateField.fieldType == '5'){ %>
								<#form:select name="recordList[${field.templateField.index}].fieldValue" value="${field.fieldValue}" items="${field.templateField.items}" itemLabel="label" itemValue="value" maxlength="255" class="form-control ${field.templateField.requiredTag != '1' ? '': 'required'} "/>
								<% } %>
								<% if (isNotBlank(field.templateField.fieldDesc)) { %>
								<div style="padding: 4px 6px 4px 6px;">${field.templateField.fieldDesc}</div>
								<% } %>

								<input type="text" id="recordList_${field.templateField.index}id" name="recordList[${field.templateField.index}].id" value="${field.id}" maxLength="255" class="form-control hide"/>
								<input type="text" id="recordList_${field.templateField.index}fieldId" name="recordList[${field.templateField.index}].templateField.id" value="${field.templateField.id}" maxLength="255" class="form-control hide"/>
								<input type="text" id="recordList_${field.templateField.index}fieldType" name="recordList[${field.templateField.index}].templateField.fieldType" value="${field.templateField.fieldType}" maxLength="255" class="form-control hide"/>
								<input type="text" id="recordList_${field.templateField.index}fieldName" name="recordList[${field.templateField.index}].templateField.fieldName" value="${field.templateField.fieldName}" maxLength="255" class="form-control hide"/>
								<input type="text" id="recordList_${field.templateField.index}fieldKey" name="recordList[${field.templateField.index}].templateField.fieldKey" value="${field.templateField.fieldKey}" maxLength="255" class="form-control hide"/>
								<input type="text" id="recordList_${field.templateField.index}fieldSubkey" name="recordList[${field.templateField.index}].templateField.fieldSubkey" value="${field.templateField.fieldSubkey}" maxLength="255" class="form-control hide"/>
								<input type="text" id="recordList_${field.templateField.index}fieldDesc" name="recordList[${field.templateField.index}].templateField.fieldDesc" value="${field.templateField.fieldDesc}" maxLength="255" class="form-control hide"/>
								<input type="text" id="recordList_${field.templateField.index}requiredTag" name="recordList[${field.templateField.index}].templateField.requiredTag" value="${field.templateField.requiredTag}" maxLength="255" class="form-control hide"/>

							</div>
						</div>
					</div>
				</div>
				<% } %>

			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
<!--						<% if (hasPermi('formmanage:hsDataFormFill:edit')){ %>-->
<!--						<% } %>-->
						<% if (hsDataFormDeliveryRecord.opType != 'upload'){ %>
						<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<button type="submit" class="btn btn-sm btn-primary" id="btnSubmitUpload"><i class="fa fa-check"></i> ${text('上 传')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
	$(function() {
		$('#btnSubmitUpload').click(function(){
			$('#opType').val('upload');
		});
		$('#btnSubmit').click(function(){
			$('#opType').val('save');
		});

		$('#inputForm').validate({
			submitHandler: function(form){
				js.ajaxSubmitForm($(form), function(data){
					js.showMessage(data.message);
					if(data.result == Global.TRUE){
						js.closeCurrentTabPage(function(contentWindow){
							contentWindow.page();
						});
					}
				}, "json");
			}
		});
	});
</script>