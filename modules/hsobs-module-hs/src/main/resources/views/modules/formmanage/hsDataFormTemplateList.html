<% layout('/layouts/default.html', {title: '表单配置', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('表单配置')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('formmanage:hsDataFormTemplate:edit')){ %>
					<a href="${ctx}/formmanage/hsDataFormTemplate/form" class="btn btn-default btnTool" title="${text('新增数据表单')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="exportForm" model="${hsDataFormTemplate}" action="${ctx}/formmanage/hsDataFormTemplate/exportFormData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<#form:hidden id="exportId" path="id"/>
			</#form:form>
		    <div class="search-form-container">
			<#form:form id="searchForm" model="${hsDataFormTemplate}" action="${ctx}/formmanage/hsDataFormTemplate/listData" method="post" class="form-inline "
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
			    <div class="form-group">
					<label class="control-label">${text('表单名称')}：</label>
					<div class="control-inline">
						<#form:input path="formName" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
			    <div class="form-group">
			    	<label class="control-label">${text('表单类型')}：</label>
			    	<div class="control-inline ">
			    		<#form:select path="formType" dictType="data_form_type" blankOption="true" class="form-control " />
			    	</div>
			    </div>
				<div class="form-group">
					<label class="control-label">${text('表单描述')}：</label>
					<div class="control-inline">
						<#form:input path="formDesc" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('创建时间')}：</label>
					<div class="control-inline">
						<#form:input path="createDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="createDate_lte.click()"/> -
						<#form:input path="createDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
			    <div class="form-group">
			    	<label class="control-label">${text('创建人')}：</label>
			    	<div class="control-inline">
			    		<#form:listselect id="createBy" title="用户选择"
			    		path="createBy" labelPath="user.userName"
			    		url="${ctx}/sys/empUser/empUserSelect" allowClear="true"
			    		checkbox="false" itemCode="userCode" itemName="userName"/>
			    	</div>
			    </div>
			    <div class="form-group">
			    	<label class="control-label">${text('创建单位')}：</label>
			    	<div class="control-inline">
			    		<#form:treeselect id="office" title="${text('机构选择')}"
			    		path="office.officeCode" labelPath="office.officeName"
			    		url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
			    		class="required" allowClear="true" canSelectRoot="true" canSelectParent="false"/>
			    	</div>
			    </div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
	        </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("编号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/formmanage/hsDataFormTemplate/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑数据表单")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("表单名称")}', name:'formName', index:'a.form_name', width:150, align:"left"},
		{header:'${text("表单类型")}', name:'formType', index:'a.form_type', width:90, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('data_form_type')}", val, '${text("无")}', true);
			}},
		{header:'${text("表单描述")}', name:'formDesc', index:'a.form_desc', width:150, align:"left"},
		{header:'${text("创建人")}', name:'user.userName', index:'a.user.user_name', width:150, align:"left"},
		{header:'${text("创建单位")}', name:'office.officeName', index:'a.office.office_name', width:150, align:"left"},
		{header:'${text("创建时间")}', name:'createDate', index:'a.create_date', width:150, align:"left"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('formmanage:hsDataFormTemplate:edit')){
				actions.push('<a href="${ctx}/formmanage/hsDataFormTemplate/form?id='+row.id+'" class="hsBtnList" title="${text("编辑数据表单")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/formmanage/hsDataFormTemplate/fieldConfigForm?id='+row.id+'" class="btnList" title="${text("表单字段")}">配置</a>&nbsp;');
				actions.push('<a href="#" onclick="js.confirm(\'是否导出？\', function(){exportFormFile(\''+row.id+'\');});return false;" class="" title="${text("表单导出")}">导出</a>&nbsp;');
				actions.push('<a href="${ctx}/formmanage/hsDataFormTemplate/delete?id='+row.id+'" class="btnList" title="${text("删除数据表单")}" data-confirm="${text("确认要删除该数据表单吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});


function exportFormFile(id) {
	$('#exportId').val(id);
	js.ajaxSubmitForm( $('#exportForm'), {
		url: '${ctx}/formmanage/hsDataFormTemplate/exportFormData',
		clearParams: '',
		downloadFile: true
	});
	return false;
}

</script>