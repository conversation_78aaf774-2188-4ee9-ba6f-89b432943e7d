<% layout('/layouts/default.html', {title: '数据表单', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text(hsDataFormDelivery.pageType == '2' ? '专项台账' : '数据报送')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('formmanage:hsDataFormDelivery:edit') && hsDataFormDelivery.pageType != '2'){ %>
				<a href="#" id="reportBtn" class="btn btn-default " title="${text('数据报送')}" data-layer="true" data-layer-width="700" data-layer-height="400"><i class="fa fa-plus"></i> ${text('报送')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsDataFormDelivery}" action="${ctx}/formmanage/hsDataFormDelivery/reportListData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="search-form-row">
			<div class="form-group">
				<label class="control-label">${text(hsDataFormDelivery.pageType == '2' ? '专项名称' : '标题')}：</label>
				<div class="control-inline">
					<#form:input path="msgTitle" maxlength="200" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('填写说明')}：</label>
				<div class="control-inline">
					<#form:input path="msgContent" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('下发日期')}：</label>
				<div class="control-inline">
					<#form:input path="sendDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="sendDate_lte.click()"/>
					&nbsp;-&nbsp;
					<#form:input path="sendDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
					dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
				</div>
			</div>
			</div>
			<div class="search-form-row">
			<div class="form-group ${hsDataFormDelivery.pageType == '2' ? 'hide' : ''}">
				<label class="control-label">${text('表单类型')}：</label>
				<div class="control-inline ">
					<#form:select path="formTemplate.formType" dictType="data_form_type" blankOption="true" class="form-control " />
				</div>
			</div>
			</div>
			<div class="search-button-row">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		</div>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		showCheckbox: true,
		sortableColumn: false,
		columnModel: [
			{header:'${text("ID")}', hidden: true, name:'id', index:'a.id', width:150, align:"left"},
			{header:'${text(hsDataFormDelivery.pageType == "2" ? "专项名称" : "标题")}', name:'msgTitle', index:'a.msg_title', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
					return '<a href="${ctx}/formmanage/hsDataFormDelivery/form?id='+row.id+'" class="hsBtnList" data-title="${text("表单信息")}">'+(val||row.id)+'</a>';
				}},
			{header:'${text("表单类型")}', name:'formTemplate.formType', index:'a.form_template.form_type', width:90, align:"left", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('data_form_type')}", val, '${text("无")}', true);
				}},
			{header:'${text("填写说明")}', name:'msgContent', index:'a.msg_content', width:150, align:"left"},
			{header:'${text("截止时间")}', name:'limitDate', index:'a.limit_date', width:150, align:"left"},
			{header:'${text("下发日期")}', name:'sendDate', index:'a.send_date', width:150, align:"left"},
			{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
					var actions = [];
					//# if(hasPermi('formmanage:hsDataFormDelivery:edit')){
					actions.push('<a href="${ctx}/formmanage/hsDataFormDelivery/form?id='+row.id+'" class="hsBtnList" title="${text("表单信息")}">编辑</a>&nbsp;');
					actions.push('<a href="${ctx}/formmanage/hsDataFormDeliveryRecord/fillList?deliveryId='+row.id+'&opType=upload" class="btnList" title="${text("填写信息")}">数据</a>&nbsp;');
					//# }
					return actions.join('');
				}}
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>

<script>
	//# // 初始化DataGrid对象
	$('#reportBtn').click(function () {
		var ids = $("#dataGrid").dataGrid('getSelectRows');
		if (ids == ''){
			js.showMessage("请至少选择一条数据！", "数据报送错误", "warning", 3000);
			return false;
		}
		console.log(ids);
		js.addTabPage(null, "数据报送", "${ctx}/formmanage/hsDataFormDelivery/report?ids="+ ids + "&publicType=0" );
	});
</script>