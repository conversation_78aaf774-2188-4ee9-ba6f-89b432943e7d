<% layout('/layouts/default.html', {title: '数据填报', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('数据填报')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsDataFormDeliveryRecord}" action="${ctx}/formmanage/hsDataFormDeliveryRecord/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('标题')}：</label>
					<div class="control-inline">
						<#form:input path="hsDataFormDelivery.msgTitle" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('填写状态')}：</label>
					<div class="control-inline ">
						<#form:select path="fillStatus" dictType="data_form_fill_status" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('上报状态')}：</label>
					<div class="control-inline ">
						<#form:select path="readStatus" dictType="data_form_report_status" blankOption="true" class="form-control"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		    </div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("标题")}', name:'hsDataFormDelivery.msgTitle', index:'a.hs_data_form_delivery.msg_title', width:150, align:"left"},
		{header:'${text("类型")}', name:'formTemplate.formType', index:'a.form_template.form_type', width:90, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('data_form_type')}", val, '${text("无")}', true);
			}},
		{header:'${text("填写人名称")}', name:'receiveUserName', index:'a.receive_user_name', width:150, align:"left"},
		{header:'${text("填写状态")}', name:'fillStatus', index:'a.fill_status', width:120, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('data_form_fill_status')}", val, '${text("未填写")}', true);
			}},
		{header:'${text("填写时间")}', name:'fillDate', index:'a.fill_date', width:200, align:"left"},
		{header:'${text("上传状态")}', name:'uploadStatus', index:'a.upload_status', width:120, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('data_form_upload_status')}", val, '${text("未上传")}', true);
			}},
		{header:'${text("上传时间")}', name:'uploadDate', index:'a.upload_date', width:200, align:"left"},
		{header:'${text("报送状态")}', name:'readStatus', index:'a.readStatus', width:120, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('data_form_report_status')}", val, '${text("未报送")}', true);
			}},
		{header:'${text("报送时间")}', name:'readDate', index:'a.read_date', width:200, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('formmanage:hsDataFormDeliveryRecord:edit')){
				actions.push('<a href="${ctx}/formmanage/hsDataFormDeliveryRecord/form?id='+row.id+'" class="hsBtnList" title="${text("表单填写")}">填写</a>&nbsp;');
				// actions.push('<a href="${ctx}/formmanage/hsDataFormDeliveryRecord/delete?id='+row.id+'" class="btnList" title="${text("删除数据表单下发发送记录表")}" data-confirm="${text("确认要删除该数据表单下发发送记录表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>