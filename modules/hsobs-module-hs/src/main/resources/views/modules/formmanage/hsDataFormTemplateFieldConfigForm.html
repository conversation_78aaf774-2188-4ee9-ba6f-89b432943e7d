<% layout('/layouts/default.html', {title: '数据表单模板字段管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsDataFormTemplate.isNewRecord ? '编辑表单字段' : '编辑表单字段')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<div class="box-body">

			<div class="row">
				<div class="col-sm-6 col-xs-6">
					<#form:form id="dynamicForm" model="${hsDataFormTemplate}" action="#" method="post" class="form-horizontal">
					<div class="form-unit">${text('新增字段')}</div>
					<div class="row">
						<div class="col-xs-12">
							<div class="form-group">
								<label class="control-label col-sm-4" title="">
									<span class="required ">*</span> ${text('字段类型')}：<i class="fa icon-question hide"></i></label>
								<div class="col-sm-8">
									<#form:select path="fieldType" dictType="form_field_type" class="form-control required " />
								</div>
							</div>
						</div>
						<div class="col-xs-12">
							<div class="form-group">
								<label class="control-label col-sm-4" title="">
									<span class="required ">*</span> ${text('是否必填')}：<i class="fa icon-question hide"></i></label>
								<div class="col-sm-8">
									<#form:select path="requiredTag" dictType="sys_yes_no" class="form-control required " />
								</div>
							</div>
						</div>
						<div class="col-xs-12">
							<div class="form-group">
								<label class="control-label col-sm-4" title="">
									<span class="required ">*</span> ${text('字段名称')}：<i class="fa icon-question hide"></i></label>
								<div class="col-sm-8">
									<#form:input path="fieldName" maxlength="255" class="form-control required"/>
								</div>
							</div>
						</div>
						<div class="col-xs-12">
							<div class="form-group">
								<label class="control-label col-sm-4" title="">
									<span class="required ">*</span> ${text('字段标识')}：<i class="fa icon-question hide"></i></label>
								<div class="col-sm-8">
									<#form:input path="fieldKey" maxlength="255" class="form-control abc required"/>
								</div>
							</div>
						</div>
						<div class="col-xs-12">
							<div class="form-group">
								<label class="control-label col-sm-4" title="">
									<span class="required hide">*</span> ${text('单选、多选、下拉数据，多个用逗号隔开')}：<i class="fa icon-question hide"></i></label>
								<div class="col-sm-8">
									<#form:textarea rows="3" path="fieldSubkey" maxlength="2000" class="form-control"/>
								</div>
							</div>
						</div>
						<div class="col-xs-12">
							<div class="form-group">
								<label class="control-label col-sm-4" title="">
									<span class="required hide">*</span> ${text('字段填写描述')}：<i class="fa icon-question hide"></i></label>
								<div class="col-sm-8">
									<#form:textarea rows="3" path="fieldDesc" maxlength="255" class="form-control"/>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-offset-4 col-sm-8">
							<button type="submit" class="btn btn-info" onclick="addDynamicField()">新增</button>
						</div>
					</div>
				</#form:form>

				</div>
				<div class="col-sm-6 col-xs-6">
					<#form:form id="inputForm" model="${hsDataFormTemplate}" action="${ctx}/formmanage/hsDataFormTemplate/saveField" method="post" class="form-horizontal">
					<div class="form-unit">${text('表单信息')}</div>
					<#form:hidden path="isNewRecord"/>
					<#form:hidden path="id"/>

					<% for(var field in hsDataFormTemplate.fieldList){ %>
					<div class="row">
						<div class="col-xs-12">
							<div class="form-group">
								<label class="control-label col-sm-4" title="">
									<span class="required <% if (field.requiredTag != '1'){ %>hide<% } %>">*</span> ${field.fieldName}(${field.fieldKey})：<i class="fa icon-question hide"></i></label>
								<div class="col-sm-8">
									<% if (field.fieldType == '1'){ %>
									<#form:input path="${field.fieldKey}" maxlength="255" class="form-control "/>
									<% } else if (field.fieldType == '2'){ %>
									<#form:textarea rows="3" path="${field.fieldKey}" maxlength="255" class="form-control "/>
									<% } else if (field.fieldType == '3'){ %>
									<#form:radio path="${field.fieldKey}" items="${field.items}" itemLabel="label" itemValue="value" maxlength="255" class="form-control "/>
									<% } else if (field.fieldType == '4'){ %>
									<#form:checkbox path="${field.fieldKey}" items="${field.items}" itemLabel="label" itemValue="value" maxlength="255" class="form-control "/>
									<% } else if (field.fieldType == '5'){ %>
									<#form:select path="${field.fieldKey}" items="${field.items}" itemLabel="label" itemValue="value" maxlength="255" class="form-control "/>
									<% } %>
									<% if (isNotBlank(field.fieldDesc)) { %>
									<div style="padding: 4px 6px 4px 6px;">${field.fieldDesc}</div>
									<% } %>

									<input type="text" id="fieldList_${field.index}id" name="fieldList[${field.index}].id" value="${field.id}" maxLength="255" class="form-control hide"/>
									<input type="text" id="fieldList_${field.index}fieldType" name="fieldList[${field.index}].fieldType" value="${field.fieldType}" maxLength="255" class="form-control hide"/>
									<input type="text" id="fieldList_${field.index}fieldName" name="fieldList[${field.index}].fieldName" value="${field.fieldName}" maxLength="255" class="form-control hide"/>
									<input type="text" id="fieldList_${field.index}fieldKey" name="fieldList[${field.index}].fieldKey" value="${field.fieldKey}" maxLength="255" class="form-control hide"/>
									<input type="text" id="fieldList_${field.index}fieldSubkey" name="fieldList[${field.index}].fieldSubkey" value="${field.fieldSubkey}" maxLength="255" class="form-control hide"/>
									<input type="text" id="fieldList_${field.index}fieldDesc" name="fieldList[${field.index}].fieldDesc" value="${field.fieldDesc}" maxLength="255" class="form-control hide"/>
									<input type="text" id="fieldList_${field.index}requiredTag" name="fieldList[${field.index}].requiredTag" value="${field.requiredTag}" maxLength="255" class="form-control hide"/>

								</div>
							</div>
						</div>
					</div>
					<% } %>

					<div id="formFields" ></div>
					<div class="row">
						<div class="col-sm-offset-2 col-sm-10">
							<% if (hasPermi('formmanage:hsDataFormTemplate:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
							<% } %>
							<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
						</div>
					</div>
				</#form:form>

				</div>
			</div>




		</div>
		<div class="box-footer">
		</div>
	</div>
</div>
<% } %>
<script>

	let fieldIndex = ${fieldIndex};
	let fieldListTmp = JSON.parse('${fieldArrStr}');

	const dataArray = [];
	const dataMap = new Map();

	if (fieldListTmp) {
		fieldListTmp.forEach((item, index, array) => {
			dataArray.push(item.fieldKey);
			dataMap.set('wrapper-fieldDiv-' + item.index, item.fieldKey);
		})
	}

	// 添加数据到数据集合和 Map 映射集合
	function addData(key, value) {
		dataArray.push(value);
		dataMap.set(key, value);
	}
	// 删除指定数据
	function removeData(key) {
		if (dataMap.has(key)) {
			let value = dataMap.get(key);
			// 从数组中删除对应的值
			let index = dataArray.indexOf(value);
			if (index!== -1) {
				dataArray.splice(index, 1);
			}
			// 从 Map 中删除对应键值对
			dataMap.delete(key);
		}
	}

	function getFormValues() {
		return {
			fieldDesc: $('#fieldDesc').val(),
			fieldSubkey: $('#fieldSubkey').val(),
			fieldKey: $('#fieldKey').val(),
			fieldName: $('#fieldName').val(),
			requiredTag: $('#requiredTag').val(),
			fieldType: $('#fieldType').val()
		};
	}

	function addDynamicField() {

		$('#dynamicForm').validate({
			submitHandler: function(form, e){
				let { fieldDesc, fieldSubkey, fieldKey, fieldName, requiredTag, fieldType } = getFormValues();

				let index = dataArray.indexOf(fieldKey);
				if (index!== -1) {
					js.showMessage('fieldKey => ' + fieldKey + ' 已存在');
					e.preventDefault();
					return;
				}

				addField(fieldType, fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag);
				fieldIndex++;
			}
		});
	}

	function addField(fieldType, fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag) {

		let requiredSpan = requiredTag === '1'
				? '<span class="required ">*</span>'
				: '<span class="required hide ">*</span>';

		// 构建最外层的 div
		let html = '<div class="row" id="wrapper-fieldDiv-' + fieldIndex + '">';
		// 构建列 div
		html += '<div class="col-xs-12">';
		// 构建表单组 div
		html += '<div class="form-group">';
		// 构建 label 标签
		html += '<label class="control-label col-sm-4" title="' + fieldName + '">';
		html += requiredSpan + fieldName + '(' + fieldKey + ')：<a href="#" class="" title="${text("删除表单字段")}" onclick="js.confirm(\'是否删除该表单字段？\', function(){delFieldDiv(\'wrapper-fieldDiv-'+fieldIndex+'\');});return false;" ><i class="fa fa-trash-o " style="padding: 0px 5px 0px 5px;"></i></a></label>';
		// 构建输入框所在的列 div
		html += '<div class="col-sm-8">';

		// 1 单行文本  2-多行文本 3-单选框 4-多选框 5-下拉框
		if (fieldType === '1') {
			html += addInputField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag);
		} else if (fieldType === '2') {
			html += addMultipleInputField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag);
		} else if (fieldType === '3') {
			html += addRadioField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag);
		} else if (fieldType === '4') {
			html += addCheckboxField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag);
		} else if (fieldType === '5') {
			html += addSelectField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag);
		}

		if (fieldDesc.length > 0) {
			html += '<div style="padding: 4px 6px 4px 6px;">' + fieldDesc + '</div>';
		}

		html += '<input type="text" id="fieldList_' + fieldIndex + 'fieldType" name="fieldList[' + fieldIndex + '].fieldType" value="' + fieldType + '" maxLength="255" class="form-control hide"/>';
		html += '<input type="text" id="fieldList_' + fieldIndex + 'fieldName" name="fieldList[' + fieldIndex + '].fieldName" value="' + fieldName + '" maxLength="255" class="form-control hide"/>';
		html += '<input type="text" id="fieldList_' + fieldIndex + 'fieldKey" name="fieldList[' + fieldIndex + '].fieldKey" value="' + fieldKey + '" maxLength="255" class="form-control hide"/>';
		html += '<input type="text" id="fieldList_' + fieldIndex + 'fieldSubkey" name="fieldList[' + fieldIndex + '].fieldSubkey" value="' + fieldSubkey + '" maxLength="255" class="form-control hide"/>';
		html += '<input type="text" id="fieldList_' + fieldIndex + 'fieldDesc" name="fieldList[' + fieldIndex + '].fieldDesc" value="' + fieldDesc + '" maxLength="255" class="form-control hide"/>';
		html += '<input type="text" id="fieldList_' + fieldIndex + 'requiredTag" name="fieldList[' + fieldIndex + '].requiredTag" value="' + requiredTag + '" maxLength="255" class="form-control hide"/>';

		// 关闭所有打开的 div 标签
		html += '</div></div></div></div>';

		$('#formFields').before(html);
		addData('wrapper-fieldDiv-' + fieldIndex, fieldKey)
	}

	function addInputField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag) {
		return '<input type="text" id="' + fieldKey + '" name="' + fieldKey + '" value="" maxLength="255" class="form-control "/>';
	}

	function addMultipleInputField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag) {
		return '<textarea type="text" id="' + fieldKey + '" name="' + fieldKey + '" rows="3" maxLength="255" value="" maxLength="255" class="form-control "></textarea>';
	}

	// 添加单选框
	function addRadioField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag) {
		let html = '<input type="hidden" name="!'+ fieldKey +'" value="">';
		html += '    <span id="'+fieldKey+'" class="icheck">';

		if (fieldSubkey.length > 0) {
			let nameArray = fieldSubkey.split(',');
			let result = nameArray.map((name, index) => {
				html += '        <label class="">' ;
				html += '            <div class="iradio_minimal-grey" aria-checked="false" aria-disabled="false" style="position: relative;">' ;
				html += '                <input type="radio" id="radioM1" name="radioM" value="1" class="form-control " style="position: absolute; opacity: 0;">' ;
				html += '                <ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins>' ;
				html += '            </div>' ;
				html += name ;
				html += '        </label>' ;
			});
		}
		html += '    </span>';
		return html;
	}

	// 添加多选框
	function addCheckboxField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag) {
		let html = '<span id="'+ fieldKey +'" class="icheck">';

		if (fieldSubkey.length > 0) {
			let nameArray = fieldSubkey.split(',');
			let result = nameArray.map((name, index) => {
				html += '        <label class="">';
				html += '            <div class="icheckbox_minimal-grey" aria-checked="false" aria-disabled="false" style="position: relative;">';
				html += '                <input type="checkbox" id="' + fieldKey + index + '" name="' + fieldKey + '" value="' + name + '" class="form-control " style="position: absolute; opacity: 0;">';
				html += '                <ins class="iCheck-helper" style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"></ins>';
				html += '            </div>';
				html += name;
				html += '        </label>';
			});
		}

		html += '        <input type="hidden" name="!'+ fieldKey +'" value="">';
		html += '    </span>';

		return html;
	}

	// 添加下拉框
	function addSelectField(fieldName, fieldKey, fieldSubkey, fieldDesc, requiredTag) {
		let selectValue = null;
		let html = '<select id="'+fieldKey+'" name="'+fieldKey+'" class="form-control select2-hidden-accessible" tabindex="-1" aria-hidden="true">';
		if (fieldSubkey.length > 0) {
			let nameArray = fieldSubkey.split(',');
			nameArray.map((name, index) => {
				html += '<option value="' + name + '">' + name + '</option>';
				if (selectValue == null) {
					selectValue = name;
				}
			});
		}
		html += '</select>';

		html += '<span class="select2 select2-container select2-container--default select2-container--below" dir="ltr" style="width: 100%;">';
		html += '    <span class="selection">';
		html += '        <span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-labelledby="select2-' + fieldKey + '-container">';
		html += '            <span class="select2-selection__rendered" id="select2-' + fieldKey + '-container" title="' + selectValue + '">' + selectValue + '</span>';
		html += '            <span class="select2-selection__arrow" role="presentation">';
		html += '                <b role="presentation"></b>';
		html += '            </span>';
		html += '        </span>';
		html += '    </span>';
		html += '    <span class="dropdown-wrapper" aria-hidden="true"></span>';
		html += '</span>';

		return html;
	}

	function delFieldDiv(id) {
		let obj = $('#' + id)
		obj.remove();
		removeData(id);
	}

	$('#inputForm').validate({
		submitHandler: function (form) {
			js.ajaxSubmitForm($(form), function (data) {
				js.showMessage(data.message);
				if (data.result == Global.TRUE) {
					js.closeCurrentTabPage(function (contentWindow) {
						contentWindow.page();
					});
				}
			}, "json");
		}
	});
</script>