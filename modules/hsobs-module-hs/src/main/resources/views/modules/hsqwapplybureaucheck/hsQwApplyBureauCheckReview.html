<% layout('/layouts/default.html', {title: '局直公房智能核验复核', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-check"></i> ${text('局直公房智能核验复核')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${check}" action="${ctx}/hsqwapplybureaucheck/saveReview" method="post" class="form-horizontal">
			<div class="box-body">
				<#form:hidden path="id"/>
				
				<div class="form-unit">${text('申请单信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('申请单编号')}：</label>
							<div class="col-sm-8">
								<p class="form-control-static">${bureau.id!}</p>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('房源信息')}：</label>
							<div class="col-sm-8">
								<p class="form-control-static">${bureau.house.simpleInfo!}</p>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('主申请人')}：</label>
							<div class="col-sm-8">
								<p class="form-control-static">${bureau.mainApplyer.name!}</p>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('身份证号')}：</label>
							<div class="col-sm-8">
								<p class="form-control-static">${bureau.mainApplyer.idNum!}</p>
							</div>
						</div>
					</div>
				</div>
				
				<div class="form-unit">${text('核验信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('核验时间')}：</label>
							<div class="col-sm-8">
								<p class="form-control-static">${check.checkDate!}</p>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4">${text('核验结果')}：</label>
							<div class="col-sm-8">
								<p class="form-control-static">
									<% if(check.checkResult == '0'){ %>
										<span class="badge badge-success">正常</span>
									<% }else if(check.checkResult == '1'){ %>
										<span class="badge badge-danger">异常</span>
									<% }else{ %>
										${check.checkResult!}
									<% } %>
								</p>
							</div>
						</div>
					</div>
				</div>
				
				<% if(isNotBlank(check.propertyInfo)){ %>
				<div class="form-unit">${text('不动产信息')}</div>
				<div class="row">
					<div class="col-xs-12">
						<pre class="json-viewer">${check.propertyInfo!}</pre>
					</div>
				</div>
				<% } %>
				
				<% if(isNotBlank(check.verifyDetail)){ %>
				<div class="form-unit">${text('核验详情')}</div>
				<div class="row">
					<div class="col-xs-12">
						<pre class="json-viewer">${check.verifyDetail!}</pre>
					</div>
				</div>
				<% } %>
				
				<div class="form-unit">${text('复核信息')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('复核状态')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="reviewStatus" dictType="hs_qw_apply_check_review_status" class="form-control required" />
							</div>
						</div>
					</div>
					<div class="col-xs-6 clearance-date-group" >
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required">*</span> ${text('应清退时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="clearanceDate" readonly="true" maxlength="20" class="form-control laydate"
									dataFormat="date" data-type="date" data-format="yyyy-MM-dd"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								${text('复核备注')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="reviewRemark" rows="4" maxlength="500" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('提 交')}</button>&nbsp;
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$(function(){
	// 格式化JSON显示
	$('.json-viewer').each(function(){
		try {
			var jsonObj = JSON.parse($(this).text());
			$(this).html(JSON.stringify(jsonObj, null, 2));
		} catch(e) {
			console.error("JSON解析错误", e);
		}
	});
	
	// 复核状态变更事件
	$('#reviewStatus').change(function(){
		if($(this).val() == '3'){ // 复核不通过
			$('.clearance-date-group').show();
			$('#clearanceDate').addClass('required');
		} else {
			$('.clearance-date-group').hide();
			$('#clearanceDate').removeClass('required');
		}
	});
	
	// 初始化表单验证
	$('#inputForm').validate({
		submitHandler: function(form){
			js.ajaxSubmitForm($(form), function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.closeCurrentTabPage(function(contentWindow){
						contentWindow.page();
					});
				}
			}, "json");
		}
	});
});
</script>
