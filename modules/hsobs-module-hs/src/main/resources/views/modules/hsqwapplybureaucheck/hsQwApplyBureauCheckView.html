<% layout('/layouts/default.html', {title: '局直公房智能核验详情', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-eye"></i> ${text('局直公房智能核验详情')}
			</div>
			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<div class="box-body">
			<div class="form-unit">${text('申请单信息')}</div>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('申请单编号')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${bureau.id!}</p>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('房源信息')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${bureau.house.simpleInfo!}</p>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('主申请人')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${bureau.mainApplyer.name!}</p>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('身份证号')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${bureau.mainApplyer.idNum!}</p>
						</div>
					</div>
				</div>
			</div>
			
			<div class="form-unit">${text('核验信息')}</div>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('核验时间')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${check.checkDate!}</p>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('核验结果')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">
								<% if(check.checkResult == '0'){ %>
									<span class="badge badge-success">正常</span>
								<% }else if(check.checkResult == '1'){ %>
									<span class="badge badge-danger">异常</span>
								<% }else{ %>
									${check.checkResult!}
								<% } %>
							</p>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('通知状态')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">
								<% if(check.noticed == '0'){ %>
									<span class="badge">未通知</span>
								<% }else if(check.noticed == '1'){ %>
									<span class="badge badge-info">已通知</span>
								<% }else{ %>
									${check.noticed!}
								<% } %>
							</p>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('复核状态')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">
								<% if(check.reviewStatus == '0'){ %>
									<span class="badge">未复核</span>
								<% }else if(check.reviewStatus == '1'){ %>
									<span class="badge badge-info">已复核</span>
								<% }else if(check.reviewStatus == '2'){ %>
									<span class="badge badge-success">复核通过</span>
								<% }else if(check.reviewStatus == '3'){ %>
									<span class="badge badge-danger">复核不通过</span>
								<% }else{ %>
									${check.reviewStatus!}
								<% } %>
							</p>
						</div>
					</div>
				</div>
			</div>
			
			<% if(isNotBlank(check.reviewStatus) && check.reviewStatus != '0'){ %>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('复核人')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${check.reviewUser!}</p>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('复核时间')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${check.reviewDate!}</p>
						</div>
					</div>
				</div>
				<div class="col-xs-12">
					<div class="form-group">
						<label class="control-label col-sm-2">${text('复核备注')}：</label>
						<div class="col-sm-10">
							<p class="form-control-static">${check.reviewRemark!}</p>
						</div>
					</div>
				</div>
			</div>
			<% } %>
			
			<% if(check.clearanceStatus == '1' || check.clearanceStatus == '2'){ %>
			<div class="form-unit">${text('清退信息')}</div>
			<div class="row">
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('清退状态')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">
								<% if(check.clearanceStatus == '0'){ %>
									<span class="badge">未清退</span>
								<% }else if(check.clearanceStatus == '1'){ %>
									<span class="badge badge-warning">已通知</span>
								<% }else if(check.clearanceStatus == '2'){ %>
									<span class="badge badge-success">已清退</span>
								<% }else{ %>
									${check.clearanceStatus!}
								<% } %>
							</p>
						</div>
					</div>
				</div>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('应清退时间')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${check.clearanceDate!}</p>
						</div>
					</div>
				</div>
				<% if(check.clearanceStatus == '2'){ %>
				<div class="col-xs-6">
					<div class="form-group">
						<label class="control-label col-sm-4">${text('实际清退时间')}：</label>
						<div class="col-sm-8">
							<p class="form-control-static">${check.actualClearanceDate!}</p>
						</div>
					</div>
				</div>
				<% } %>
			</div>
			<% } %>
			
			<% if(isNotBlank(check.propertyInfo)){ %>
			<div class="form-unit">${text('不动产信息')}</div>
			<div class="row">
				<div class="col-xs-12">
					<pre class="json-viewer">${check.propertyInfo!}</pre>
				</div>
			</div>
			<% } %>
			
			<% if(isNotBlank(check.verifyDetail)){ %>
			<div class="form-unit">${text('核验详情')}</div>
			<div class="row">
				<div class="col-xs-12">
					<pre class="json-viewer">${check.verifyDetail!}</pre>
				</div>
			</div>
			<% } %>
			
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('hsqwapplybureaucheck::edit') && (check.reviewStatus == '0' || isBlank(check.reviewStatus))){ %>
							<a href="${ctx}/hsqwapplybureaucheck/review?id=${check.id}" class="btn btn-sm btn-primary"><i class="fa fa-check"></i> ${text('复核')}</a>&nbsp;
						<% } %>
						<% if (hasPermi('hsqwapplybureaucheck::edit') && check.reviewStatus == '3' && check.clearanceStatus == '1'){ %>
							<button type="button" class="btn btn-sm btn-success" id="btnCompleteClearance"><i class="fa fa-check"></i> ${text('清退完成')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
$(function(){
	// 格式化JSON显示
	$('.json-viewer').each(function(){
		try {
			var jsonObj = JSON.parse($(this).text());
			$(this).html(JSON.stringify(jsonObj, null, 2));
		} catch(e) {
			console.error("JSON解析错误", e);
		}
	});
	
	// 清退完成按钮点击事件
	$('#btnCompleteClearance').click(function(){
		js.confirm('确认已完成清退？', function(){
			js.ajaxSubmit({
				url: '${ctx}/hsqwapplybureaucheck/completeClearance',
				data: {id: '${check.id}'},
				type: 'POST',
				dataType: 'json',
				success: function(data){
					js.showMessage(data.message);
					if(data.result == Global.TRUE){
						js.closeCurrentTabPage(function(contentWindow){
							contentWindow.page();
						});
					}
				}
			});
		});
	});
});
</script>
