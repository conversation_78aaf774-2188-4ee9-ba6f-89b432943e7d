<% layout('/layouts/default.html', {title: '公房智能核验管理', libs: ['dataGrid']}){ %>
<style>
.danger-row {
    background-color: #ffeeee !important;
}
.danger-row:hover {
    background-color: #ffdddd !important;
}
</style>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公房智能核验管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="$r{text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwApplyBureauCheck}" action="${ctx}/hsqwapplybureaucheck//listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('核验时间')}：</label>
							<div class="control-inline">
								<#form:input path="checkDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="checkDate_lte.click()"/>
								&nbsp;-&nbsp;
								<#form:input path="checkDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('核验结果')}：</label>
							<div class="control-inline">
								<#form:select path="checkResult" dictType="hs_qw_apply_check_result" class="form-control width-120" blankOption="true" />
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('复核状态')}：</label>
							<div class="control-inline">
								<#form:select path="reviewStatus" dictType="hs_qw_apply_check_review_status" class="form-control width-120" blankOption="true" />
							</div>
						</div>
					</div>

					<!-- 第二行：1个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('清退状态')}：</label>
							<div class="control-inline">
								<#form:select path="clearanceStatus" dictType="hs_qw_apply_check_clearance_status" class="form-control width-120" blankOption="true" />
							</div>
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("核验时间")}', name:'checkDate', index:'a.check_date', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return '<a href="${ctx}/hsqwapplybureaucheck/view?id='+row.id+'" class="hsBtnList" data-title="${text("查看局直公房智能核验")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("局直公房申请单ID")}', name:'bureauId', index:'a.bureau_id', sortable:false, width:150, align:"left"},
		{header:'${text("核验结果")}', name:'checkResult', index:'a.check_result', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			if (val == '0') {
				return '<span class="badge badge-success">正常</span>';
			} else if (val == '1') {
				return '<span class="badge badge-danger" style="font-weight:bold;font-size:14px;background-color:#ff0000;">异常</span>';
			} else {
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_qw_apply_check_result')}", val, '${text("未知")}', true);
			}
		}},
		{header:'${text("复核状态")}', name:'reviewStatus', index:'a.review_status', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			if (val == '0') {
				return '<span class="badge">未复核</span>';
			} else if (val == '1') {
				return '<span class="badge badge-info">已复核</span>';
			} else if (val == '2') {
				return '<span class="badge badge-success">复核通过</span>';
			} else if (val == '3') {
				return '<span class="badge badge-danger">复核不通过</span>';
			} else {
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_qw_apply_check_review_status')}", val, '${text("未知")}', true);
			}
		}},
		{header:'${text("清退状态")}', name:'clearanceStatus', index:'a.clearance_status', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			if (val == '0') {
				return '<span class="badge">未清退</span>';
			} else if (val == '1') {
				return '<span class="badge badge-warning">已通知</span>';
			} else if (val == '2') {
				return '<span class="badge badge-success">已清退</span>';
			} else {
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_qw_apply_check_clearance_status')}", val, '${text("未知")}', true);
			}
		}},
		{header:'${text("通知状态")}', name:'noticed', index:'a.noticed', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			if (val == '0') {
				return '<span class="badge">未通知</span>';
			} else if (val == '1') {
				return '<span class="badge badge-info">已通知</span>';
			} else {
				return val;
			}
		}},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('hsqwapplybureaucheck::view')){
				actions.push('<a href="${ctx}/hsqwapplybureaucheck/view?id='+row.id+'" class="hsBtnList" title="${text("查看详情")}"><i class="fa fa-eye"></i> 查看</a>&nbsp;');
			//# }
			//# if(hasPermi('hsqwapplybureaucheck::edit')){
				if (row.reviewStatus == '0' || !row.reviewStatus) {
					actions.push('<a href="${ctx}/hsqwapplybureaucheck/review?id='+row.id+'" class="hsBtnList" title="${text("复核")}"><i class="fa fa-check"></i> 复核</a>&nbsp;');
				}
				if (row.reviewStatus == '3' && row.clearanceStatus == '1') {
					actions.push('<a href="javascript:completeClearance(\''+row.id+'\')" class="btnList" title="${text("清退完成")}"><i class="fa fa-check-circle"></i> 清退完成</a>&nbsp;');
				}
				if (row.noticed == '0') {
					//新增通知按钮
					actions.push('<a href="${ctx}/hsqwapplybureaucheck/sendNotification?id='+row.id+'" class="btnList" title="${text("通知")}" data-confirm="${text("确认要发送通知？")}">通知</a>&nbsp;');		}
			//# }
			return actions.join('');
		}}
	],
	ajaxSuccess: function() {
		// 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
		requestAnimationFrame(function() {
			CommonTable.setupFixedColumns('dataGrid');

			// 高亮显示异常记录
			$("#dataGrid tbody tr").each(function(){
				var $row = $(this);
				var rowData = $row.data("jqGridRowData");
				if(rowData && rowData.checkResult === '1'){
					$row.addClass("danger-row");
				}
			});
		});
		// js.closeLoading(0, true);
	}
});
</script><script>
$('#btnExport').click(function(){
	js.ajaxSubmitForm($('#searchForm'), {
		url: '${ctx}/hsqwapplybureaucheck//exportData',
		clearParams: 'pageNo,pageSize',
		downloadFile: true
	});
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入公房房源智能核验")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/hsqwapplybureaucheck//importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/hsqwapplybureaucheck//importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>

<script>
// 手动核验按钮点击事件
$('#btnVerify').click(function(){
	js.confirm('确认要执行手动核验吗？这将会对所有需要核验的局直公房申请进行智能核验。', function(){
		js.ajaxSubmit({
			url: '${ctx}/hsqwapplybureaucheck/executeVerification',
			type: 'POST',
			dataType: 'json',
			success: function(data){
				js.showMessage(data.message);
				if(data.success){
					$('#dataGrid').refresh();
				}
			}
		});
	});
});

// 清退完成函数
function completeClearance(id){
	js.confirm('确认已完成清退？', function(){
		js.ajaxSubmit({
			url: '${ctx}/hsqwapplybureaucheck/completeClearance',
			data: {id: id},
			type: 'POST',
			dataType: 'json',
			success: function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					$('#dataGrid').refresh();
				}
			}
		});
	});
}

</script>



