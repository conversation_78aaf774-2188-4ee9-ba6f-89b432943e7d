<% layout('/layouts/default.html', {title: '公示管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsPriceLimitPublic.isNewRecord ? '公示管理' : '公示管理')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsPriceLimitPublic}" action="${ctx}/pricelimitpublic/hsPriceLimitPublic/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('公示名称')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:input path="publicName" maxlength="200" class="form-control required "/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('公示信息')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
									<#form:textarea path="publicInfo" rows="4" maxlength="500" class="form-control required "/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('公示开始时间')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="startDate" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('公示结束时间')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="endDate" maxlength="20" class="form-control laydate required"
										dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('附件上传')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile" bizKey="${hsPriceLimitPublic.id}" bizType="hsPriceLimitPublic_file"
										uploadType="all" class="required" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
				<div class="form-unit">${text('申请人公示表')}</div>
				<div class="form-unit-wrap table-form">
					<table id="hsPriceLimitPublicDetailDataGrid"></table>
				</div>
			</div>
        <#form:hidden id="pids" path="pids"/>
        <#form:hidden id="submitType" path="submitType"/>
        <#form:hidden id="status" path="status"/>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if ( hsPriceLimitPublic.status == null || hsPriceLimitPublic.status == 0){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('公 示')}</button>&nbsp;
						<% } else if ( hsPriceLimitPublic.status=='1' ){%>
							<!--<% if ( hsPriceLimitPublic.commit =='1' ){ %>  -->
								<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit3"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
								<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit1"><i class="fa fa-check"></i> ${text('批量提交')}</button>&nbsp;
								<button type="submit" class="btn btn-sm btn-danger" id="btnSubmit2"><i class="fa fa-reply-all"></i> ${text('批量拒绝')}</button>&nbsp;
							<!-- <% } %> -->
							<% if ( hsPriceLimitPublic.isPublic=='0' ){%>
								<a href="#" class="btn btn-default" id="btnPublic"><i class="glyphicon glyphicon-export"></i> ${text('发 布')}</a>
							<% } %>
							<a href="#" class="btn btn-default" id="btnOutput"><i class="glyphicon glyphicon-export"></i> ${text('导出公示')}</a>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
//# // 初始化公示关联批量申请DataGrid对象
$('#hsPriceLimitPublicDetailDataGrid').dataGrid({

	data: "#{toJson(hsPriceLimitPublic.hsPriceLimitPublicDetailList)}",
	datatype: 'local', // 设置本地数据
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	
	//# // 设置数据表格列
	columnModel: [
		{header:'${text("申请单编号")}', name:'applyId', editable:false, width:80, align:"left"},
		{header:'${text("申请人姓名")}', name:'hsPriceLimitApply.mainApplyer.name', width:100, align:"left"},
		{header:'${text("身份证号")}', name:'hsPriceLimitApply.mainApplyer.idNum', width:100, align:"left"},
		{header:'${text("工作单位")}', name:'hsPriceLimitApply.mainApplyer.organization', width:100, align:"left"},
	],
	
	//# // 编辑表格参数
	editGrid: false,				// 是否是编辑表格
	editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsPriceLimitPublicDetailDataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

	//# // 编辑表格的提交数据参数
	editGridInputFormListName: 'hsPriceLimitPublicDetailList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,createBy,createDate,updateBy,updateDate,applyId,publicId,', // 提交数据列表的属性字段
	
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
$('#btnSubmit').click(function(){
	$('#submitType').val('0');
	$('#status').val('1');
});
$('#btnSubmit1').click(function(){
	$('#submitType').val('1');
});
$('#btnSubmit2').click(function(){
	$('#submitType').val('2');
});
$('#btnSubmit3').click(function(){
	$('#submitType').val('3');
});
$('#btnPublic').click(function(){
	$.ajax({
		url: '${ctx}/pricelimitpublic/hsPriceLimitPublic/sendPublic',
		type: 'POST',
		data: $('#inputForm').serialize(),  // 序列化表单数据
		success: function(data) {
			js.showMessage(data.message);
			page();
		},
		error: function(error) {
			js.showErrorMessage(error.responseJSON.message);
		}
	});
});

$('#btnOutput').click(function(){
	js.ajaxSubmitForm($('#inputForm'), {
		url: '${ctx}/pricelimitpublic/hsPriceLimitPublic/outputPublic',
		clearParams: 'pageNo,pageSize',
		downloadFile: true,
		success: function(response, status, xhr) {
		}
	});
});
</script>