<% layout('/layouts/default.html', {title: '公示管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公示管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsPriceLimitPublic}" action="${ctx}/pricelimitpublic/hsPriceLimitPublic/listOwnerData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('公示名称')}：</label>
						<div class="control-inline">
							<#form:input path="publicName" maxlength="200" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('公示信息')}：</label>
						<div class="control-inline">
							<#form:input path="publicInfo" maxlength="500" class="form-control width-120"/>
						</div>
					</div>
				</div>
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('公示开始时间')}：</label>
						<div class="control-inline">
							<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('公示结束时间')}：</label>
						<div class="control-inline">
							<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
						</div>
					</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			</div>
			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn : false,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("公示名称")}', name:'publicName', index:'a.public_name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/pricelimitpublic/hsPriceLimitPublic/form?id='+row.id+'" class="hsBtnList" data-title="${text("管理网上公示")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("公示信息")}', name:'publicInfo', index:'a.public_info', width:150, align:"left"},
		{header:'${text("公示开始时间")}', name:'startDate', index:'a.start_date', width:150, align:"left"},
		{header:'${text("公示结束时间")}', name:'endDate', index:'a.end_date', width:150, align:"left"},
		{header:'${text("公示状态")}', name:'status', index:'a.status', width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_public_status')}", val, '${text("未知")}', true);
			}},
		{header:'${text("发布状态")}', name:'isPublic', index:'a.is_public', width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_pricelimit_public_status')}", val, '${text("未知")}', true);
			}},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
				actions.push('<a href="${ctx}/pricelimitpublic/hsPriceLimitPublic/form?id='+row.id+'" class="hsBtnList" title="${text("管理网上公示")}">编辑</a>&nbsp;');
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		requestAnimationFrame(function() {
			CommonTable.setupFixedColumns('dataGrid');
		});
	}
});
</script>