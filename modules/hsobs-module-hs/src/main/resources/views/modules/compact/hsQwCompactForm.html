<% layout('/layouts/default.html', {title: '租赁资格轮候合同管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwCompact.isNewRecord ? '新增租赁资格轮候合同' : '编辑租赁资格轮候合同')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwCompact}" action="${ctx}/compact/hsQwCompact/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div">
					<table class="table-form hs-table-form">
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('合同编码')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="compactCode" maxlength="255" class="form-control"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('合同开始日期')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="startDate" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('合同结束日期')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="endDate" readonly="true" maxlength="20" class="form-control laydate required"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('申请单信息')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<a class="hsBtnList"  title="申请单信息" href="${ctx}/apply/hsQwApply/form?redirectPage=hsQwApplyFormCompactConfirm&id=${hsQwCompact.applyId}&isRead=true" > ${hsQwCompact.applyId} </a>

							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('月租金')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:input path="monthFee" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
							</td>
							<td>
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('compact:hsQwCompact:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>