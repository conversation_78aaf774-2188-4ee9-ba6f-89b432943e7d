<% layout('/layouts/default.html', {title: '租赁资格轮候合同管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租赁资格轮候合同管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('compact:hsQwCompact:edit')){ %>
					<a href="${ctx}/compact/hsQwCompact/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候合同')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwCompact}" action="${ctx}/compact/hsQwCompact/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('合同编码')}：</label>
					<div class="control-inline">
						<#form:input path="compactCode" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同开始日期')}：</label>
					<div class="control-inline">
						<#form:input path="startDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="startDate_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="startDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('合同结束日期')}：</label>
					<div class="control-inline">
						<#form:input path="endDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="endDate_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="endDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注信息')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请表id')}：</label>
					<div class="control-inline">
						<#form:input path="applyId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('月租金')}：</label>
					<div class="control-inline">
						<#form:input path="monthFee" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("合同编码")}', name:'compactCode',  sortable:false, width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/compact/hsQwCompact/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候合同")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("合同开始日期")}', name:'startDate',  sortable:false, width:150, align:"center"},
		{header:'${text("合同结束日期")}', name:'endDate',  sortable:false, width:150, align:"center"},
		{header:'${text("状态")}', name:'status',  sortable:false, width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate',  sortable:false, width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks',  sortable:false, width:150, align:"left"},
		{header:'${text("申请表id")}', name:'applyId',  sortable:false, width:150, align:"left"},
		{header:'${text("月租金")}', name:'monthFee',  sortable:false, width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('compact:hsQwCompact:edit')){
				actions.push('<a href="${ctx}/compact/hsQwCompact/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁资格轮候合同")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/compact/hsQwCompact/delete?id='+row.id+'" class="btnList" title="${text("删除租赁资格轮候合同")}" data-confirm="${text("确认要删除该租赁资格轮候合同吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>