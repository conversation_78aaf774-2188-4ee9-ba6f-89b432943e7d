<% layout('/modules/clearance/include/hsQwClearanceFormBase.html', {isRead:'true'}){ %>
<div class="hs-table-div">
	<table class="table-form hs-table-form">
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('实施说明')}：<i class="fa icon-question hide"></i>
			</td>
			<td colspan="3">
				<#form:textarea rows="4" path="impleDesc" maxlength="255" class="form-control required" value="${hsQwClearance.impleDesc}"/>
			</td>
		</tr>
		<% if (hsQwClearance.type!=1){ %>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required ">*</span> ${text('是否列入黑名单')}：<i class="fa icon-question hide"></i>
			</td>
			<td colspan="3">
				<#form:radio path="blackUser" dictType="sys_yes_no" class="form-control required" value="${hsQwClearance.blackUser}"/>
			</td>
		</tr>
		<%} else {%>
			<#form:hidden path="blackUser" defaultValue="0"/>
		<%}%>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('图片上传')}：
			</td>
			<td colspan="3">
				<#form:fileupload id="uploadImage" bizKey="${hsQwClearance.id}" bizType="hsQwClearance_image"
				uploadType="image" class="required" readonly="false" preview="true" dataMap="true"/>
			</td>
		</tr>
		<tr>
			<td class="form-label hs-form-label">
				<span class="required hide">*</span> ${text('附件上传')}：
			</td>
			<td colspan="3">
				<#form:fileupload id="uploadFile" bizKey="${hsQwClearance.id}" bizType="hsQwClearance_file"
				uploadType="all" class="required" readonly="false" preview="true" dataMap="true"/>
			</td>
		</tr>
	</table>
</div>
<% } %>
