<% layout('/layouts/default.html', {title: '租赁资格轮候清退申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text('租赁资格轮候清退申请管理')}
            </div>
            <div class="box-tools pull-right">
                <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
                <a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
                <% if(hasPermi('clearance:hsQwClearance:add1')){ %>
                <a href="${ctx}/clearance/hsQwClearance/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候清退申请')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
                <% } %>
                <a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
            </div>
        </div>
        <div class="box-body">
            <!-- 筛选条件区域 -->
            <div class="search-form-container">
                <#form:form id="searchForm" model="${hsQwClearance}" action="${ctx}/clearance/hsQwClearance/listData" method="post" class="form-inline hide"
                data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

                    <!-- 第一行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('申请单号')}：</label>
                            <div class="control-inline">
                                <#form:input path="id" maxlength="255" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('清退类型')}：</label>
                            <div class="control-inline">
                                <#form:select path="type" dictType="hs_qw_clearance_type" blankOption="true" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('清退原因')}：</label>
                            <div class="control-inline">
                                <#form:input path="reason" maxlength="255" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>

                    <!-- 第二行：2个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('状态')}：</label>
                            <div class="control-inline">
                                <#form:select path="status" dictType="sys_status" blankOption="true" class="form-control width-120 isQuick"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('审核意见')}：</label>
                            <div class="control-inline">
                                <#form:input path="remarks" maxlength="500" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <!-- 占位，保持布局 -->
                        </div>
                    </div>

                    <!-- 按钮行 -->
                    <div class="search-button-row">
                        <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                        <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
                    </div>
                </#form:form>
            </div>

            <!-- 表格区域 -->
            <div class="fixed-table-container">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
        </div>
</div>
</div>
<% } %>
<script>
    //# // 初始化DataGrid对象
    $('#dataGrid').dataGrid({
        searchForm: $('#searchForm'),
        showCheckbox: true,
        shrinkToFit: false, // 禁用自动调整列宽
        autowidth: false, // 禁用自动宽度
        //scroll: true, // 启用滚动条
        scrollOffset: 18,
        width: '100%', // 表格宽度
        height: 'auto', // 表格高度自适应
        columnModel: [
            {header:'${text("申请编号")}', name:'id',  sortable:false, width:160, align:"left", formatter: function(val, obj, row, act){
                    return '<a href="${ctx}/clearance/hsQwClearance/form?id=' + row.id + '&status=1" class="hsBtnList" title="${text("编辑租赁资格轮候清退申请")}">' + (val || row.id) + '</a>';
                }},
            {header:'${text("主申请人")}', name:'hsQwApply.mainApplyer.name', sortable:false,  width:120, align:"left"},
            {header:'${text("清退类型")}', name:'type',  sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
                    return js.getDictLabel("#{@DictUtils.getDictListJson('hs_qw_clearance_type')}", val, '${text("未知")}', true);
                }},
            {header:'${text("清退原因")}', name:'reason',  sortable:false, width:150, align:"left"},
            {header:'${text("审核状态")}', name:'status',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
                    return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
                }},
            {header:'${text("流程环节")}', name:'processName', sortable:false,  width:120, align:"left"},
            {header:'${text("审核意见")}', name:'remarks',  sortable:false, width:150, align:"left"},
            {header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
                    var actions = [];
                    //# if(hasPermi('clearance:hsQwClearance:add1')){
                    actions.push('<a href="${ctx}/clearance/hsQwClearance/form?id=' + row.id + '&status=1" class="hsBtnList" title="${text("编辑租赁资格轮候清退申请")}">编辑</a>&nbsp;');
                    //# }
                    return actions.join('');
                }}
        ],
        ajaxSuccess: function() {
            // 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
            requestAnimationFrame(function() {
                CommonTable.setupFixedColumns('dataGrid');
            });
            // js.closeLoading(0, true);
        }
    }).on('click', '.bpmButton', function(){
        var $this = $(this).addClass('hsBtnList');
        js.ajaxSubmit($this.attr('href'), function(data){
            if (data.result == Global.TRUE){
                // 检查 pcUrl 是否可访问
                $.ajax({
                    url: data.pcUrl,
                    type: 'get',
                    success: function(response) {
                        // 如果请求成功，使用 js.addTabPage 打开新标签页
                        js.addTabPage($this, $this.attr('title'), data.pcUrl);
                    },
                    error: function(xhr, status, error) {
                        // 如果请求失败，显示错误信息
                        var errorMsg = '${text("加载失败")}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        }
                        js.showErrorMessage(errorMsg);
                    }
                });
            } else {
                js.showErrorMessage(data.message);
            }
        });
        return false;
    });
    $('#btnExport').click(function(){
        js.ajaxSubmitForm($('#searchForm'), {
            url: '${ctx}/clearance/hsQwClearance/exportData',
            clearParams: 'pageNo,pageSize',
            downloadFile: true
        });
    });
</script>