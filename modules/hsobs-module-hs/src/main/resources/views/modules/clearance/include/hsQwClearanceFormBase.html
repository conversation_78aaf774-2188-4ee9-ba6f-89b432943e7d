<% layout('/layouts/default.html', {title: '租赁资格轮候清退申请管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<% if(!hsQwClearance.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsQwClearance}" title="租赁资格轮候清退申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsQwClearance}" formKey="rent_clear" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwClearance.isNewRecord ? '新增租赁资格轮候清退申请' : '编辑租赁资格轮候清退申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwClearance}" action="${ctx}/clearance/hsQwClearance/save" method="post" class="form-horizontal">
		<div class="box-body hs-box-body-bpm">
			<div class="form-unit">${text('基本信息')}</div>
			<#form:hidden path="id"/>
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('配租信息查询')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:listselect id="applyId" title="已配租申请单"
							path="applyIdL" labelPath="hsQwApply.applyTitle"
							url="${ctx}/apply/hsQwApply/applySelect?dataType=3" allowClear="false"
							checkbox="false" itemCode="applyIdC" readonly="${isRead}" itemName="applyTitle" callbackFuncName = "getApplyInfo"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('配租合同编号')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="compactCode" path="hsQwApply.compact.compactCode" maxlength="64" class="form-control" readonly="true" />
							<#form:hidden id="compactId" path="compactId" maxlength="64" class="form-control" readonly="true" />
						</td>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('主申请人')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="applyerName" path="hsQwApply.mainApplyer.name" maxlength="64" class="form-control" readonly="true"/>
							<#form:hidden id="applyerId" path="applyerId" maxlength="64" class="form-control" readonly="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('主申请身份证')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="applyerIdNum" path="hsQwApply.mainApplyer.idNum" maxlength="64" class="form-control" readonly="true"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('联系方式')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="applyerMobile" path="hsQwApply.mainApplyer.phone" maxlength="64" class="form-control" readonly="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('工作单位')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="applyerOrg" path="hsQwApply.mainApplyer.organization" maxlength="64" class="form-control" readonly="true"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('房屋地址')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="applyerAddress" path="hsQwApply.hsQwApplyHouse.estate.address" maxlength="64" class="form-control" readonly="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('面积')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="applyerSize" path="hsQwApply.hsQwApplyHouse.buildingArea" maxlength="64" class="form-control" readonly="true"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('清退类型')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:select path="type" readonly="${isRead}" dictType="hs_qw_clearance_type" class="form-control required" />
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('清退原因')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:textarea path="reason" readonly="${isRead}" rows="4" maxlength="255" class="form-control required"/>
						</td>
					</tr>
				</table>
			</div>
			${layoutContent!}
			<div class="form-unit">${text('审核操作')}</div>
			<div class="hs-table-div" >
				<table class="table-form hs-table-form" >
					<tr>
						<td class="form-label hs-form-label">
							${text('审批意见：')}：
						</td>
						<td colspan="3">
							<#bpm:comment bpmEntity="${hsQwClearance}" showCommWords="true" />
						</td>
					</tr>
				</table>
			</div>
			<!--			<#bpm:nextTaskInfo bpmEntity="${hsQwClearance}" />-->
		</div>
		<#form:hidden id="applyId" path="applyId" maxlength="64" class="form-control" readonly="true"/>
		<div class="box-footer">
			<div class="row">
				<div class="col-sm-offset-2 col-sm-10">
					<% if (hasPermi('clearance:hsQwClearance:edit')){ %>
					<#form:hidden path="status"/>
					<% if (hsQwClearance.isNewRecord || hsQwClearance.status == '9'){ %>
					<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;
					<% } %>
					<#bpm:button bpmEntity="${hsQwClearance}" formKey="rent_clear" completeText="提 交"/>
					<% } %>
					<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
				</div>
			</div>
		</div>
	</#form:form>
</div>
</div>
<% } %>
<script>
	// 业务实现草稿按钮
	$('#btnDraft').click(function(){
		$('#status').val(Global.STATUS_DRAFT);
	});
	// 流程按钮操作事件
	BpmButton = window.BpmButton || {};
	BpmButton.init = function(task){
		if (task.status != '2') {
			$('.taskComment').removeClass('hide');
		}
	}
	BpmButton.complete = function($this, task){
		$('#status').val(Global.STATUS_AUDIT);
	};
	// 表单验证提交事件
	$('#inputForm').validate({
		submitHandler: function(form){
			js.ajaxSubmitForm($(form), function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.closeCurrentTabPage(function(contentWindow){
						contentWindow.page();
					});
				}
			}, "json");
		}
	});

	function getApplyInfo(id, act, index, layero, nodes){
		if (nodes==null){
			return;
		}
		Object.keys(nodes).forEach(key => {
			let jsonObject = nodes[key]; // 获取 JSON 对象
			$('#applyId').val(jsonObject.id);
			$('#applyerId').val(jsonObject.mainApplyer.id);
			$('#applyerName').val(jsonObject.mainApplyer.name);
			$('#applyerIdNum').val(jsonObject.mainApplyer.idNum);
			$('#applyerMobile').val(jsonObject.mainApplyer.mobile);
			$('#applyerOrg').val(jsonObject.mainApplyer.organization);
			$('#applyerAddress').val(jsonObject.hsQwApplyHouse.estate.address);
			$('#applyerSize').val(jsonObject.hsQwApplyHouse.buildingArea);
			if (jsonObject.compact!=null){
				$('#compactId').val(jsonObject.compact.id);
				$('#compactCode').val(jsonObject.compact.compactCode);
			} else {
				$('#compactId').val('');
				$('#compactCode').val('');
			}
			console.log(jsonObject.id);
		});
	}
</script>