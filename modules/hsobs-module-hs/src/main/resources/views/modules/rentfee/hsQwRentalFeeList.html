<% layout('/layouts/default.html', {title: '租赁账单表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租赁账单表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwRentalFee}" action="${ctx}/rentfee/hsQwRentalFee/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('合同编号')}：</label>
							<div class="control-inline">
								<#form:input path="compactId" maxlength="64" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('缴费周期')}：</label>
							<div class="control-inline">
								<#form:input path="feeMonth" readonly="true" maxlength="20" class="form-control laydate width-date"
									dataFormat="date" data-type="date" data-format="yyyy-MM"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="hs_rental_fee_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
					</div>

					<!-- 第二行：2个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('缴费时间')}：</label>
							<div class="control-inline">
								<#form:input path="feeDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="feeDate_lte.click()"/>
								&nbsp;-&nbsp;
								<#form:input path="feeDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('预期缴费时间')}：</label>
							<div class="control-inline">
								<#form:input path="expectFeeDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="expectFeeDate_lte.click()"/>
								&nbsp;-&nbsp;
								<#form:input path="expectFeeDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
									dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('租金类型')}：</label>
							<div class="control-inline">
								<#form:select path="feeType" dictType="hs_rental_fee_type" blankOption="true" class="form-control width-120"/>
							</div>
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("编号")}', name:'id', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return '<a href="${ctx}/rentfee/hsQwRentalFee/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁账单表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("合同")}', name:'compactId', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/compact/hsQwCompact/form?id='+row.compactId+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候合同")}">'+(val||row.compactId)+'</a>';
			}},
		{header:'${text("金额")}', name:'rentalFee', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("房源信息")}', name:'hsQwApply.hsQwApplyHouse.estate.name', sortable:false, width:150, align:"left"},
		{header:'${text("租户姓名")}', name:'hsQwApply.mainApplyer.name',sortable:false,  width:120, align:"left"},
		{header:'${text("身份证号")}', name:'hsQwApply.mainApplyer.idNum', sortable:false, width:150, align:"left"},
		{header:'${text("工作单位")}', name:'hsQwApply.mainApplyer.organization', sortable:false, width:150, align:"left"},
		{header:'${text("缴费周期")}', name:'feeMonth', sortable:false, width:120, align:"left"},
		{header:'${text("缴费时间")}', name:'feeDate', sortable:false, width:150, align:"left"},
		{header:'${text("预期缴费时间")}', name:'expectFeeDate', sortable:false, width:150, align:"left"},
		{header:'${text("租金类型")}', name:'feeType', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_rental_fee_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("缴费状态")}', name:'status', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_rental_fee_status')}", val, '${text("未知")}', true);
			}},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", sortable:false, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('rentfee:hsQwRentalFee:edit')){
				if(row.status=='0'){
					actions.push('<a href="${ctx}/payment/hsQwFeePayment/form?feeId='+row.id+'" class="hsBtnList" title="${text("新增催收单")}"><i class="fa fa-plus"></i></a>&nbsp;');
				}
				actions.push('<a href="${ctx}/rentfee/hsQwRentalFee/form?id='+row.id+'" class="hsBtnList" title="${text("查看租赁账单表")}">编辑</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	ajaxSuccess: function() {
		            requestAnimationFrame(function() {
                CommonTable.setupFixedColumns('dataGrid');
            });
		// js.closeLoading(0, true);
	}
});
</script>

