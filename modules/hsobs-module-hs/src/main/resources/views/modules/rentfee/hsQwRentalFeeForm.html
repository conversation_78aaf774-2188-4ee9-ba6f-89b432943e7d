<% layout('/layouts/default.html', {title: '租赁账单表管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwRentalFee.isNewRecord ? '新增租赁账单表' : '编辑租赁账单表')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwRentalFee}" action="${ctx}/rentfee/hsQwRentalFee/saveFee" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-12">
						<div class="hs-table-div">
							<table class="table-form hs-table-form">
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('合同编号')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="compactId" maxlength="64" readonly="true" class="form-control required"/>
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('租金费')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="rentalFee" readonly="true" class="form-control required"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('租金类型')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:select path="feeType" dictType="hs_rental_fee_type" readonly="true" class="form-control required" />
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('缴费周期')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="feeMonth" readonly="true" class="form-control required"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('预期缴费时间')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:input path="expectFeeDate" readonly="true" maxlength="20" class="form-control laydate required"
										dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('租赁资格轮候物业费催单')}</div>
				<div class="form-unit-wrap table-form">
					<table id="hsQwFeePaymentDataGrid" class="table-form hs-table-form"></table>
				</div>

				<div class="form-unit">${text('缴费操作')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="hs-table-div">
							<table class="table-form hs-table-form">
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('确认缴费')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:radio path="status" dictType="hs_rental_fee_status" readonly = "${hsQwRentalFee.status!='0'}" class="form-control" />
									</td>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('缴费证明上传')}：
									</td>
									<td>
										<#form:fileupload id="uploadFile" bizKey="${hsQwRentalFee.id}" bizType="hsQwRentalFee_file"
										uploadType="all" class="required" readonly="false" preview="true" dataMap="true"/>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('rentfee:hsQwRentalFee:edit') && hsQwRentalFee.status=='0'){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('缴 费')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
//# // 初始化租赁资格轮候物业费催单DataGrid对象
$('#hsQwFeePaymentDataGrid').dataGrid({

	data: "#{toJson(hsQwRentalFee.hsQwFeePaymentList)}",
	datatype: 'local', // 设置本地数据
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	
	//# // 设置数据表格列
	columnModel: [
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'${text("账单编号")}', name:'hsQwRentalFee.id', editable:true, hidden:true},
		{header:'${text("催收明细说明")}', name:'remarks', width:150, editable:true, edittype:'textarea', editoptions:{'maxlength':'500', 'class':'form-control', 'rows':'1'}},
		{header:'${text("催收时间")}', name:'updateDate',  width:150, align:"center"},
		{header:'${text("催收类型")}', name:'noticeType', width:100, formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('hs_payment_type')}", val, '${text("未知")}', true);
			}
		}
	],
	
	//# // 编辑表格参数
	editGrid: false,				// 是否是编辑表格
	editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsQwFeePaymentDataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

	//# // 编辑表格的提交数据参数
	editGridInputFormListName: 'hsQwFeePaymentList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,feeId,createBy,createDate,updateBy,updateDate,remarks,noticeType,', // 提交数据列表的属性字段
	
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});
</script>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>