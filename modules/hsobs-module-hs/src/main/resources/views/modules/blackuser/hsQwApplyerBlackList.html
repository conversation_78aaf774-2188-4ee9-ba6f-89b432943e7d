<% layout('/layouts/default.html', {title: '租赁资格轮候租户黑名单管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租赁资格轮候租户黑名单管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('blackrule:hsQwApplyerBlackRule:view')){ %>
					<a href="${ctx}/blackrule/hsQwApplyerBlackRule/list" class="btn btn-default btnTool" title="${text('租户黑名单规则配置')}"><i class="icon-settings"></i> ${text('规则配置')}</a>
				<% } %>
				<% if(hasPermi('blackuser:hsQwApplyerBlack:edit')){ %>
					<a href="${ctx}/blackuser/hsQwApplyerBlack/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候租户黑名单')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwApplyerBlack}" action="${ctx}/blackuser/hsQwApplyerBlack/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：3个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('黑名单状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="hs_apply_black_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('用户姓名')}：</label>
							<div class="control-inline">
								<#form:input path="user.userName" maxlength="64" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('工作单位')}：</label>
							<div class="control-inline">
								<#form:input path="office.officeName" maxlength="255" class="form-control width-120"/>
							</div>
						</div>
					</div>

					<!-- 第二行：1个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('黑名单结束时间')}：</label>
							<div class="control-inline">
								<#form:input path="endTime_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="endTime_lte.click()"/>
								&nbsp;-&nbsp;
								<#form:input path="endTime_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	// //scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("用户编号")}', name:'userId',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return '<a href="${ctx}/blackuser/hsQwApplyerBlack/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候租户黑名单")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("用户姓名")}', name:'user.userName', sortable:false, width:120, align:"left"},
		{header:'${text("用户单位")}', name:'office.officeName', sortable:false, width:150, align:"left"},
		{header:'${text("纳入黑名单原因")}', name:'reasonType',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_black_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("黑名单状态")}', name:'status',  sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_black_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("纳入原因")}', name:'reason',  sortable:false, width:150, align:"left"},
		{header:'${text("黑名单结束时间")}', name:'endTime',  sortable:false, width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('blackuser:hsQwApplyerBlack:edit')){
				actions.push('<a href="${ctx}/blackuser/hsQwApplyerBlack/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁资格轮候租户黑名单")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/blackuser/hsQwApplyerBlack/disable?id='+row.id+'" class="btnList" title="${text("停用租赁资格轮候租户黑名单")}" data-confirm="${text("确认要停用该租赁资格轮候租户黑名单吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/blackuser/hsQwApplyerBlack/enable?id='+row.id+'" class="btnList" title="${text("启用租赁资格轮候租户黑名单")}" data-confirm="${text("确认要启用该租赁资格轮候租户黑名单吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/blackuser/hsQwApplyerBlack/delete?id='+row.id+'" class="btnList" title="${text("删除租赁资格轮候租户黑名单")}" data-confirm="${text("确认要删除该租赁资格轮候租户黑名单吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	ajaxSuccess: function() {
		            requestAnimationFrame(function() {
                CommonTable.setupFixedColumns('dataGrid');
            });
		// js.closeLoading(0, true);
	}
});
</script>

