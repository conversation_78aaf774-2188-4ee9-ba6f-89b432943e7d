<% layout('/layouts/default.html', {title: '租赁资格轮候租户黑名单管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwApplyerBlack.isNewRecord ? '新增租赁资格轮候租户黑名单' : '编辑租赁资格轮候租户黑名单')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwApplyerBlack}" action="${ctx}/blackuser/hsQwApplyerBlack/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-12">
						<div class="hs-table-div">
							<table class="table-form hs-table-form">
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('用户')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:listselect id="userId" title="用户选择"
										path="userId" labelPath="user.userName"
										url="${ctx}/sys/empUser/empUserSelect" allowClear="false"
										checkbox="false" itemCode="userCode" itemName="userName" callbackFuncName = "getUserInfo"/>
									</td>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('工作单位')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input id="officeName" path="office.officeName" maxlength="255" readonly="true" class="form-control"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('纳入原因')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="reason" maxlength="255" class="form-control"/>
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('黑名单结束时间')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:input path="endTime" readonly="true" maxlength="20" class="form-control laydate required"
											dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('黑名单状态')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:select path="status" dictType="hs_apply_black_status" defaultValue="0" class="form-control required" />
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
				<#form:hidden path="reasonType" defaultValue="3"/>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('blackuser:hsQwApplyerBlack:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});

function getUserInfo(id, act, index, layero, nodes){
	if (nodes==null){
		return;
	}
	Object.keys(nodes).forEach(key => {
		let jsonObject = nodes[key]; // 获取 JSON 对象
		$('#officeName').val(jsonObject.employee.office.officeName);
	});
}
</script>