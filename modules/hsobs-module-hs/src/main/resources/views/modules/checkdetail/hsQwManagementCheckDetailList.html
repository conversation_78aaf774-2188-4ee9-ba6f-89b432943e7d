<% layout('/layouts/default.html', {title: '租赁资格轮候物业核验详细表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('租赁资格轮候物业核验详细表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('checkdetail:hsQwManagementCheckDetail:edit')){ %>
					<a href="${ctx}/checkdetail/hsQwManagementCheckDetail/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候物业核验详细表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwManagementCheckDetail}" action="${ctx}/checkdetail/hsQwManagementCheckDetail/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('资格审查id')}：</label>
					<div class="control-inline">
						<#form:input path="checkId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('物品id')}：</label>
					<div class="control-inline">
						<#form:input path="objectId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('是否损坏')}：</label>
					<div class="control-inline ">
						<#form:select path="damage" dictType="hs_management_check_damage" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('是否维修')}：</label>
					<div class="control-inline ">
						<#form:select path="maintenance" dictType="hs_management_check_maintenance" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注信息')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("资格审查id")}', name:'checkId', index:'a.check_id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/checkdetail/hsQwManagementCheckDetail/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候物业核验详细表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("物品id")}', name:'objectId', index:'a.object_id', width:150, align:"left"},
		{header:'${text("是否损坏")}', name:'damage', index:'a.damage', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_management_check_damage')}", val, '${text("未知")}', true);
		}},
		{header:'${text("是否维修")}', name:'maintenance', index:'a.maintenance', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_management_check_maintenance')}", val, '${text("未知")}', true);
		}},
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('checkdetail:hsQwManagementCheckDetail:edit')){
				actions.push('<a href="${ctx}/checkdetail/hsQwManagementCheckDetail/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁资格轮候物业核验详细表")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/checkdetail/hsQwManagementCheckDetail/delete?id='+row.id+'" class="btnList" title="${text("删除租赁资格轮候物业核验详细表")}" data-confirm="${text("确认要删除该租赁资格轮候物业核验详细表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>