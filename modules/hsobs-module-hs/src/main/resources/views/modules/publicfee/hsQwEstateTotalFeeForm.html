<% layout('/layouts/default.html', {title: '公摊物业费用表管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwEstateTotalFee.isNewRecord ? '新增公摊物业费用表' : '编辑公摊物业费用表')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwEstateTotalFee}" action="${ctx}/publicfee/hsQwEstateTotalFee/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-12">
						<div class="hs-table-div">
							<table class="table-form hs-table-form">
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('楼盘')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:select path="estateId" items="${@com.hsobs.hs.modules.estate.util.EstateUtils.getEstateList()}" itemLabel="name" itemValue="id" class="form-control required" />
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('缴费周期')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="feeMonth" maxlength="8" class="form-control required"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('总水费')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="waterFee" class="form-control required number"/>
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('总电费')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="electriFee" class="form-control required number"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('相关证明材料')}：
									</td>
									<td colspan="3">
										<#form:fileupload id="uploadFile" bizKey="${hsQwEstateTotalFee.id}" bizType="hsQwEstateTotalFee_file"
											uploadType="all" class="" readonly="false" preview="true" dataMap="true"/>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('publicfee:hsQwEstateTotalFee:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>