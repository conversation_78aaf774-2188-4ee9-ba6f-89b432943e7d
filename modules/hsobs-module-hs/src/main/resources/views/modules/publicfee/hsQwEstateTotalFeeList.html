<% layout('/layouts/default.html', {title: '公摊物业费用表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公摊物业费用表管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('publicfee:hsQwEstateTotalFee:edit')){ %>
					<a href="${ctx}/publicfee/hsQwEstateTotalFee/form" class="btn btn-default btnTool" title="${text('新增公摊物业费用')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwEstateTotalFee}" action="${ctx}/publicfee/hsQwEstateTotalFee/listData" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：2个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('楼盘')}：</label>
							<div class="control-inline">
								<#form:select path="estateId" blankOption="true" blankOptionLabel="请选择" items="${@com.hsobs.hs.modules.estate.util.EstateUtils.getEstateList()}" itemLabel="name" itemValue="id" class="form-control width-120 required" />
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('缴费周期')}：</label>
							<div class="control-inline">
								<#form:input path="feeMonth" maxlength="8" class="form-control width-120 required"/>
							</div>
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("编号")}', name:'id', index:'a.id', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/publicfee/hsQwEstateTotalFee/form?id='+row.id+'" class="hsBtnList" data-title="${text("查看费用信息")}">'+(val||row.id)+'</a>';
			}},
		{header:'${text("楼盘")}', name:'estateId', index:'a.estate_id', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/estate/hsQwPublicRentalEstate/form?id='+row.estateId+'" class="hsBtnList" data-title="${text("查看楼盘信息")}">'+(row.estate.name)+'</a>';
			}},
		{header:'${text("总水费")}', name:'waterFee', index:'a.water_fee', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("总电费")}', name:'electriFee', index:'a.electri_fee', sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("缴费周期")}', name:'feeMonth', index:'a.fee_month', sortable:false, width:120, align:"left"},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', sortable:false, width:150, align:"left"},
		{header:'${text("备注信息")}', name:'remarks', index:'a.remarks', sortable:false, width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('publicfee:hsQwEstateTotalFee:edit')){
				actions.push('<a href="${ctx}/publicfee/hsQwEstateTotalFee/form?id='+row.id+'" class="hsBtnList" title="${text("编辑公摊物业费用表")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/publicfee/hsQwEstateTotalFee/delete?id='+row.id+'" class="btnList" title="${text("删除公摊物业费用表")}" data-confirm="${text("确认要删除该公摊物业费用表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	ajaxSuccess: function() {
		            requestAnimationFrame(function() {
                CommonTable.setupFixedColumns('dataGrid');
            });
		// js.closeLoading(0, true);
	}
});

</script>

