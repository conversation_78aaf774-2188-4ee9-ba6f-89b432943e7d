<% layout('/layouts/default.html', {title: '公有住房配售房屋管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公有住房配售房屋管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('publicsaleestate:hsPublicSaleEstate:edit')){ %>
					<a href="${ctx}/publicsaleestate/hsPublicSaleEstate/form" class="btn btn-default btnTool" title="${text('新增公有住房配售房屋')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsPublicSaleEstate}" action="${ctx}/publicsaleestate/hsPublicSaleEstate/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('status')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('结构类型')}：</label>
					<div class="control-inline">
						<#form:input path="structureType" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请id')}：</label>
					<div class="control-inline">
						<#form:input path="applyId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("status")}', name:'status', index:'a.status', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/publicsaleestate/hsPublicSaleEstate/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑公有住房配售房屋")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("update_date")}', name:'updateDate', index:'a.update_date', width:150, align:"left"},
		{header:'${text("结构类型")}', name:'structureType', index:'a.structure_type', width:150, align:"left"},
		{header:'${text("申请id")}', name:'applyId', index:'a.apply_id', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('publicsaleestate:hsPublicSaleEstate:edit')){
				actions.push('<a href="${ctx}/publicsaleestate/hsPublicSaleEstate/form?id='+row.id+'" class="hsBtnList" title="${text("编辑公有住房配售房屋")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/publicsaleestate/hsPublicSaleEstate/disable?id='+row.id+'" class="btnList" title="${text("停用公有住房配售房屋")}" data-confirm="${text("确认要停用该公有住房配售房屋吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/publicsaleestate/hsPublicSaleEstate/enable?id='+row.id+'" class="btnList" title="${text("启用公有住房配售房屋")}" data-confirm="${text("确认要启用该公有住房配售房屋吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/publicsaleestate/hsPublicSaleEstate/delete?id='+row.id+'" class="btnList" title="${text("删除公有住房配售房屋")}" data-confirm="${text("确认要删除该公有住房配售房屋吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>