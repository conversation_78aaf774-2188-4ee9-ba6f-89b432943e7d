<% layout('/layouts/default.html', {title: '人才补助停发报备管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('人才补助停发报备管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsTalentRecordStop}" action="${ctx}/talent/hsTalentRecordStop/auditListData" method="post" class="form-inline hide"
			data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="search-form-row">
			<div class="form-group">
				<label class="control-label">${text('姓名')}：</label>
				<div class="control-inline">
					<#form:input path="talentRecord.userName" maxlength="64" class="form-control width-120"/>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label">${text('身份证号码')}：</label>
				<div class="control-inline">
					<#form:input path="userNo" maxlength="255" class="form-control width-120"/>
				</div>
			</div>
			</div>
			<div class="search-button-row">
				<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
				<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
			</div>
		</#form:form>
		</div>
		<table id="dataGrid"></table>
		<div id="dataGridPage"></div>
	</div>
</div>
</div>
<% } %>
<script>
	//# // 初始化DataGrid对象
	$('#dataGrid').dataGrid({
		searchForm: $('#searchForm'),
		sortableColumn: false,
		columnModel: [
			{header:'${text("申请单号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
					return '<a href="${ctx}/talent/hsTalentRecordStop/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑人才补助停发报备")}">'+(val||row.id)+'</a>';
				}},
			{header:'${text("申请单位")}', name:'applyOffice.treeNames', index:'a.apply_office.tree_names', width:150, align:"left"},
			{header:'${text("身份证号码")}', name:'userNo', index:'a.user_no', width:150, align:"left"},
			{header:'${text("姓名")}', name:'talentRecord.userName', index:'a.talent_record.user_name', width:150, align:"left"},
			{header:'${text("停发原因")}', name:'stopReason', index:'a.stop_reason', width:150, align:"left"},
			{header:'${text("申请状态")}', name:'flowStatus', index:'a.flow_status', width:150, align:"center", formatter: function(val, obj, row, act){
					return js.getDictLabel("#{@DictUtils.getDictListJson('talent_stop_flow_status')}", val + '', '${text("未知")}', true);
				}},
			{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
			{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
					var actions = [];
					//# if(hasPermi('talent:hsTalentRecordStop:edit')){
					actions.push('<a href="${ctx}/talent/hsTalentRecordStop/form?id='+row.id+'" class="hsBtnList" title="${text("编辑人才补助停发报备")}">编辑</a>&nbsp;');
					// if (row.status == Global.STATUS_NORMAL){
					// 	actions.push('<a href="${ctx}/talent/hsTalentRecordStop/disable?id='+row.id+'" class="btnList" title="${text("停用人才补助停发报备")}" data-confirm="${text("确认要停用该人才补助停发报备吗？")}">停用</a>&nbsp;');
					// } else if (row.status == Global.STATUS_DISABLE){
					// 	actions.push('<a href="${ctx}/talent/hsTalentRecordStop/enable?id='+row.id+'" class="btnList" title="${text("启用人才补助停发报备")}" data-confirm="${text("确认要启用该人才补助停发报备吗？")}">启用</a>&nbsp;');
					// }
					if (row.applyStatus < 1) {
						actions.push('<a href="${ctx}/talent/hsTalentRecordStop/delete?id=' + row.id + '" class="btnList" title="${text("删除人才补助停发报备")}" data-confirm="${text("确认要删除该人才补助停发报备吗？")}">删除</a>&nbsp;');
					}
					//# }
					if (row.status != Global.STATUS_DRAFT){
						actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=talent_subsidy_stop_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
					}
					return actions.join('');
				}}
		],
		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>