<% layout('/layouts/default.html', {title: '人才补助停发报备管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<% if(!hsTalentRecordStop.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsTalentRecordStop}" title="人才补助停发报备申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsTalentRecordStop}" formKey="talent_subsidy_stop_apply" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsTalentRecordStop.isView == 1 ? '人才补助停发报备申请信息' : hsTalentRecordStop.isNewRecord ? '新增人才补助停发报备' : '编辑人才补助停发报备')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsTalentRecordStop}" action="${ctx}/talent/hsTalentRecordStop/save" method="post" class="form-horizontal">
			<div class="box-body  hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('人才选择')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:listselect id="talentId" title="人才选择"
								path="talentId" labelPath="talentId" readonly="${isRead}"
								url="${ctx}/talent/hsTalentRecord/hsTalentRecordSelect?isPublic=1&type=0" allowClear="true"
								checkbox="false" itemCode="id" itemName="userName" class="required" />
							</td>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('人才身份证号码')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="userNo" readonly="true" maxlength="255" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('停发原因')}：<i class="fa icon-question hide"></i></td>
							<td colspan="3">
								<#form:textarea rows="5" path="stopReason" readonly="${isRead}" maxlength="900" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class=""></span> ${text('附件上传')}：</td>
							<td colspan="3">
								<#form:fileupload id="stopUploadFile" bizKey="${hsTalentRecordStop.id}" bizType="hsTalentRecordStop_file"
								uploadType="all" readonly="${isRead}" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="box-footer hs-footer-block">
				<% if(hsTalentRecordStop.isView == 0) { if(!(hsTalentRecordStop.isNewRecord || hsTalentRecordStop.applyStatus <= 0 || hsTalentRecordStop.applyStatus == 5)) { %>
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group" style="margin-bottom: 0;">
							<label class="control-label col-xs-2">审批意见：</label>
							<div class="col-xs-10">
								<#hobpm:comment bpmEntity="${hsTalentRecordStop}" required="true" rows="2" showCommWords="false" />
							</div>
						</div>
					</div>
				</div>
				<!--				<#bpm:nextTaskInfo bpmEntity="${hsTalentRecordStop}" />-->
				<% }} %>
				<div class="row">
					<div class="col-sm-offset-4 col-sm-8">
						<% if (hasPermi('talent:hsTalentRecordStop:edit') && hsTalentRecordStop.isView == 0){ %>
							<#form:hidden path="status"/>
							<% if (hsTalentRecordStop.isNewRecord || hsTalentRecordStop.status == '9'){ %>
<!--								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;-->
							<% } %>
							<#bpm:button bpmEntity="${hsTalentRecordStop}" formKey="talent_subsidy_stop_apply" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};

BpmButton.callback = function(){
	let idValue = $('#id').val();
	$.ajax({
		type: 'POST',
		url: "${ctx}/talent/hsTalentRecordStop/flushTaskStatus?___t=" + new Date().getTime(),
		data: {id: idValue},
		dataType: 'json',
		async: false,
		error: function(data){
			js.showErrorMessage(data.responseText);
		},
		success: function(data, status, xhr){
		}
	});
};


function listselectCallback(id, act, index, layero, selectData){
	if (act == 'ok') {
		let innerObject = Object.values(selectData)[0];
		if (innerObject) {
			$('#userNo').val(innerObject.userNo);
		} else {
			$('#userNo').val('');
		}
	} else if (act == 'clear') {
		$('#userNo').val('');
	}
}

// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>