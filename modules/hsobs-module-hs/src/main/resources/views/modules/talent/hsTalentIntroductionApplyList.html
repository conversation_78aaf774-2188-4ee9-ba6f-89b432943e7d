<% layout('/layouts/default.html', {title: '人才补助申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('人才补助申请管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport" title="${text('导出')}"><i class="glyphicon glyphicon-export"></i>导出</a>
				<% if(hasPermi('talent:hsTalentIntroductionApply:edit')){ %>
					<a href="${ctx}/talent/hsTalentIntroductionApply/form" class="btn btn-default btnTool" title="${text('新增人才补助申请')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsTalentIntroductionApply}" action="${ctx}/talent/hsTalentIntroductionApply/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('申请单位')}：</label>
					<div class="control-inline">
						<#form:treeselect id="unitId" title="${text('机构选择')}"
						path="unitId" labelPath="office.officeName"
						url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
						class="required" allowClear="true" canSelectRoot="true" canSelectParent="false"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人姓名')}：</label>
					<div class="control-inline">
						<#form:input path="applyName" maxlength="30" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人电话')}：</label>
					<div class="control-inline">
						<#form:input path="applyTel" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('身份证号码')}：</label>
					<div class="control-inline">
						<#form:input path="applyNo" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('引进时间')}：</label>
					<div class="control-inline">
						<#form:input path="arrivalTime_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="arrivalTime_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="arrivalTime_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		</div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
$(function () {
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("申请单号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/talent/hsTalentIntroductionApply/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑人才补助申请表")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请单位")}', name:'applyOffice.treeNames', index:'a.apply_office.tree_names', width:150, align:"left"},
		{header:'${text("申请人姓名")}', name:'applyName', index:'a.apply_name', width:150, align:"left"},
		{header:'${text("申请人电话")}', name:'applyTel', index:'a.apply_tel', width:150, align:"left"},
		{header:'${text("身份证号码")}', name:'applyNo', index:'a.apply_no', width:150, align:"left"},
		{header:'${text("职称")}', name:'titleId', index:'a.title_id', width:90, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('talent_title')}", val, '${text("无")}', true);
			}},
		{header:'${text("学历")}', name:'eduBack', index:'a.edu_back', width:90, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('talent_edu_type')}", val, '${text("无")}', true);
			}},
		{header:'${text("引进时间")}', name:'arrivalTime', index:'a.arrival_time', width:150, align:"center"},
		{header:'${text("备注")}', name:'remark', index:'a.remark', width:150, align:"left"},
		{header:'${text("申请状态")}', name:'applyStatus', index:'a.apply_status', width:150, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('talent_apply_status')}", val + '', '${text("未知")}', true);
			}},
		// {header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
		// 		return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
		// 	}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('talent:hsTalentIntroductionApply:edit')){
				actions.push('<a href="${ctx}/talent/hsTalentIntroductionApply/form?id='+row.id+'" class="hsBtnList" title="${text("编辑人才补助申请")}">编辑</a>&nbsp;');
				// if (row.status == Global.STATUS_NORMAL){
				// 	actions.push('<a href="${ctx}/talent/hsTalentIntroductionApply/disable?id='+row.id+'" class="btnList" title="${text("停用人才补助申请表")}" data-confirm="${text("确认要停用该人才补助申请表吗？")}">停用</a>&nbsp;');
				// } else if (row.status == Global.STATUS_DISABLE){
				// 	actions.push('<a href="${ctx}/talent/hsTalentIntroductionApply/enable?id='+row.id+'" class="btnList" title="${text("启用人才补助申请表")}" data-confirm="${text("确认要启用该人才补助申请表吗？")}">启用</a>&nbsp;');
				// }
				if (row.applyStatus < 1 || row.applyStatus == 100) {
					actions.push('<a href="${ctx}/talent/hsTalentIntroductionApply/delete?id=' + row.id + '" class="btnList" title="${text("删除人才补助申请")}" data-confirm="${text("确认要删除该人才补助申请吗？")}">删除</a>&nbsp;');
				}
			//# }
			if (row.createBy == '${currentUser.userCode}' && row.applyStatus < 9) {
				actions.push('<a href="${ctx}/talent/hsTalentIntroductionApply/form?id=' + row.id + '&opType=update" class="hsBtnList" title="${text("更新人才补助申请")}">更新</a>&nbsp;');
			}
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=talent_subsidy_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});

	$('#btnExport').click(function(){
		js.confirm('${text("是否导出数据？")}', function(){
			js.ajaxSubmitForm($('#searchForm'), {
				url: '${ctx}/talent/hsTalentIntroductionApply/exportData',
				clearParams: 'pageNo,pageSize',
				downloadFile: true
			});
		});
	})

});
</script>