<% layout('/layouts/default.html', {title: '人才补助档案信息', libs: ['validate', 'dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsTalentRecord.isNewRecord ? '人才补助档案信息' : '人才补助档案信息')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsTalentRecord}" action="${ctx}/talent/hsTalentRecord/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<div class="row">
					<#form:hidden path="id"/>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('工作单位')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:treeselect id="unitId" title="${text('机构选择')}"
								path="unitId" labelPath="applyOffice.officeName"
								url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
								class="required" allowClear="true" readonly="${isRead}" canSelectRoot="true" canSelectParent="false"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('申请人姓名')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="userName" maxlength="30" readonly="${isRead}" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('申请人电话')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="userTel" maxlength="20" readonly="${isRead}" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('申请人身份证号码')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="userNo" maxlength="255" readonly="${isRead}" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('学历')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="eduBack" dictType="talent_edu_type" readonly="${isRead}" blankOption="true" class="form-control" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('职称')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="titleId" dictType="talent_title" readonly="${isRead}" blankOption="true" class="form-control" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('引进时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="arrivalTime" readonly="true" maxlength="20" class="form-control laydate"
									dataFormat="datetime" data-type="datetime" readonly="${isRead}" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>

					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('审批时间')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="checkTime" readonly="true" maxlength="20" class="form-control laydate"
									dataFormat="datetime" data-type="datetime" readonly="${isRead}" data-format="yyyy-MM-dd HH:mm"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('审批状态')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="checkStatus" dictType="talent_status" readonly="${isRead}" blankOption="true" class="form-control" />
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('补助总金额')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="subsidyFund" readonly="${isRead}" class="form-control number"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('补助周期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="subsidyPeriod" readonly="${isRead}" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('已发放周期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="issuedPeriod" readonly="${isRead}" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea rows="3" path="remark" readonly="${isRead}" maxlength="900" class="form-control"/>
							</div>
						</div>
					</div>
				</div>

				<div class="form-unit">${text('申请信息')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span><i class="fa icon-question hide"></i></label>
							<div class="col-sm-10 table-form">
								<table id="applyGrid"></table>
							</div>
						</div>
					</div>
				</div>

			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('talent:hsTalentRecord:edit')){ %>
<!--							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;-->
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>

$("#applyGrid").dataGrid({
	data: "#{toJson(hsTalentRecord.mergeApplyList)}",
	datatype: 'local', // 设置本地数据
	sortableColumn: false,
	columnModel: [
		{header:'${text("申请类型")}', name:'type', index:'a.type', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			    if (row.type == 1) {
					return '补助申请';
				} else if (row.type == 2) {
					return '停发报备';
				}
			}},
		{header:'${text("申请单号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			    if (row.type == 1) {
					return '<a href="${ctx}/talent/hsTalentIntroductionApply/form?id='+row.id+'&isView=' + row.isView + '" class="hsBtnList" data-title="${text("人才补助申请信息")}">'+(val||row.id)+'</a>';
				} else if (row.type == 2) {
					return '<a href="${ctx}/talent/hsTalentRecordStop/form?id='+row.id+'&isView=' + row.isView + '" class="hsBtnList" data-title="${text("人才补助停发报备申请信息")}">'+(val||row.id)+'</a>';
				}
			}},
		{header:'${text("申请状态")}', name:'applyStatus', index:'a.apply_status', width:150, align:"left", formatter: function(val, obj, row, act){
			    if (row.type == 1) {
					return js.getDictLabel("#{@DictUtils.getDictListJson('talent_apply_status')}", val + '', '${text("未知")}', true);
				} else if (row.type == 2) {
					return js.getDictLabel("#{@DictUtils.getDictListJson('talent_stop_apply_status')}", val + '', '${text("未知")}', true);
				}
			}},
		{header:'${text("创建时间")}', name:'createDate', index:'a.create_date', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			    var actions = [];
			    if (row.status != Global.STATUS_DRAFT){
					if (row.type == 1) {
						actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=talent_subsidy_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
					} else if (row.type == 2) {
						actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=talent_subsidy_stop_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
					}
			    }
			    return actions.join('');
		    }}
	],
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	// 编辑表格参数
	editGrid: false,				// 是否是编辑表格
	ajaxSuccess: function(){
	}
});

$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>