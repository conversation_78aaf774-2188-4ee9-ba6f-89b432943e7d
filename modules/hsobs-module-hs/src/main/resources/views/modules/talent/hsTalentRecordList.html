<% layout('/layouts/default.html', {title: '人才补助档案', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('人才补助档案')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport" title="${text('导出')}"><i class="glyphicon glyphicon-export"></i>导出</a>
				<% if(hasPermi('talent:hsTalentRecord:edit')){ %>
<!--					<a href="${ctx}/talent/hsTalentRecord/form" class="btn btn-default btnTool" title="${text('新增人才补助档案表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>-->
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsTalentRecord}" action="${ctx}/talent/hsTalentRecord/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline">
						<#form:treeselect id="unitId" title="${text('机构选择')}"
						path="unitId" labelPath="office.officeName"
						url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
						class="required" allowClear="true" canSelectRoot="true" canSelectParent="false"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人姓名')}：</label>
					<div class="control-inline">
						<#form:input path="userName" maxlength="30" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人电话')}：</label>
					<div class="control-inline">
						<#form:input path="userTel" maxlength="20" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-form-row">
				<div class="form-group">
					<label class="control-label">${text('身份证号码')}：</label>
					<div class="control-inline">
						<#form:input path="userNo" maxlength="255" class="form-control width-120"/>
					</div>
				</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		</div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
$(function () {
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("ID")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/talent/hsTalentRecord/form?id='+row.id+'" class="hsBtnList" data-title="${text("人才补助档案信息")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("工作单位")}', name:'applyOffice.treeNames', index:'a.apply_office.tree_names', width:150, align:"left"},
		{header:'${text("姓名")}', name:'userName', index:'a.user_name', width:150, align:"left"},
		{header:'${text("电话")}', name:'userTel', index:'a.user_tel', width:150, align:"left"},
		{header:'${text("身份证")}', name:'userNo', index:'a.user_no', width:150, align:"left"},
		// {header:'${text("学历")}', name:'eduBack', index:'a.edu_back', width:150, align:"left"},
		// {header:'${text("职称")}', name:'titleId', index:'a.title_id', width:150, align:"left"},
		{header:'${text("引进时间")}', name:'arrivalTime', index:'a.arrival_time', width:150, align:"center"},
		{header:'${text("审批时间")}', name:'checkTime', index:'a.check_time', width:150, align:"center"},
		{header:'${text("补助总额")}', name:'subsidyFund', index:'a.subsidy_fund', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("补助周期")}', name:'subsidyPeriod', index:'a.subsidy_period', width:150, align:"center"},
		{header:'${text("已发放周期")}', name:'issuedPeriod', index:'a.issued_period', width:150, align:"center"},
		{header:'${text("审批状态")}', name:'checkStatus', index:'a.check_status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('talent_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('talent:hsTalentRecord:edit')){
				actions.push('<a href="${ctx}/talent/hsTalentRecord/form?id='+row.id+'" class="hsBtnList" title="${text("编辑人才补助档案表")}">编辑</a>&nbsp;');
				// if (row.status == Global.STATUS_NORMAL){
				// 	actions.push('<a href="${ctx}/talent/hsTalentRecord/disable?id='+row.id+'" class="btnList" title="${text("停用人才补助档案表")}" data-confirm="${text("确认要停用该人才补助档案表吗？")}">停用</a>&nbsp;');
				// } else if (row.status == Global.STATUS_DISABLE){
				// 	actions.push('<a href="${ctx}/talent/hsTalentRecord/enable?id='+row.id+'" class="btnList" title="${text("启用人才补助档案表")}" data-confirm="${text("确认要启用该人才补助档案表吗？")}">启用</a>&nbsp;');
				// }
				// actions.push('<a href="${ctx}/talent/hsTalentRecord/delete?id='+row.id+'" class="btnList" title="${text("删除人才补助档案表")}" data-confirm="${text("确认要删除该人才补助档案表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
});
</script>
<script>
	$('#btnExport').click(function(){
		js.confirm('${text("是否导出数据？")}', function(){
			js.ajaxSubmitForm($('#searchForm'), {
				url: '${ctx}/talent/hsTalentRecord/exportData',
				clearParams: 'pageNo,pageSize',
				downloadFile: true
			});
		});
	})
</script>