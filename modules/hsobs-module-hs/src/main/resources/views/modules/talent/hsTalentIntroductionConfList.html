<% layout('/layouts/default.html', {title: '人才引进补助配置管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('人才引进补助配置管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
				<% if(hasPermi('talent:hsTalentIntroductionConf:edit')){ %>
					<a href="${ctx}/talent/hsTalentIntroductionConf/form" class="btn btn-default btnTool" title="${text('新增人才引进补助配置')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsTalentIntroductionConf}" action="${ctx}/talent/hsTalentIntroductionConf/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('引进时间')}：</label>
					<div class="control-inline">
						<#form:input path="arrivalTime_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="arrivalTime_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="arrivalTime_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
			    <div class="form-group">
			    	<label class="control-label">${text('人才级别')}：</label>
			    	<div class="control-inline">
			    		<#form:input path="talentLevel" maxlength="32" class="form-control width-120"/>
			    	</div>
			    </div>
				<div class="form-group">
					<label class="control-label">${text('职称')}：</label>
					<div class="control-inline width-120">
						<#form:select path="titleId" dictType="talent_title" blankOption="true" class="form-control" />
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('学历')}：</label>
					<div class="control-inline  width-120">
						<#form:select path="eduBackId" dictType="talent_edu_type" blankOption="true" class="form-control" />
					</div>
				</div>
			    <div class="form-group">
			    	<label class="control-label">${text('补助名称')}：</label>
			    	<div class="control-inline">
			    		<#form:input path="subsidyName" maxlength="255" class="form-control width-120"/>
			    	</div>
			    </div>
				<div class="form-group">
					<label class="control-label">${text('补助依据')}：</label>
					<div class="control-inline">
						<#form:input path="subsidyRemark" maxlength="900" class="form-control width-120"/>
					</div>
				</div>
			    <div class="form-group">
					<label class="control-label">${text('创建时间')}：</label>
					<div class="control-inline">
						<#form:input path="createDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="createDate_lte.click()"/>
						&nbsp;-&nbsp;
						<#form:input path="createDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
						dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
			    </div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>

$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
	    js.ajaxSubmitForm($('#searchForm'), {
	    	url: '${ctx}/talent/hsTalentIntroductionConf/exportData',
	    	clearParams: 'pageNo,pageSize',
	    	downloadFile: true
	    });
	});
});

//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("ID")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/talent/hsTalentIntroductionConf/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑人才引进补助配置")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("补助名称")}', name:'subsidyName', index:'a.subsidy_name', width:150, align:"left"},
		{header:'${text("引进时间")}', name:'arrivalTime', index:'a.arrival_time', width:150, align:"center"},
		{header:'${text("职称")}', name:'titleId', index:'a.title_id', width:90, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('talent_title')}", val, '${text("无")}', true);
			}},
		{header:'${text("学历")}', name:'eduBackId', index:'a.edu_back_id', width:90, align:"center", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('talent_edu_type')}", val, '${text("无")}', true);
			}},
		{header:'${text("人才级别")}', name:'talentLevel', index:'a.talent_level', width:120, align:"center"},
		{header:'${text("补助总金额")}', name:'subsidyFund', index:'a.subsidy_fund', width:120, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("补助周期")}', name:'subsidyPeriod', index:'a.subsidy_period', width:90, align:"center"},
		{header:'${text("补助依据")}', name:'subsidyRemark', index:'a.subsidy_remark', width:150, align:"left"},
		{header:'${text("创建时间")}', name:'createDate', index:'a.create_date', width:150, align:"center"},
		{header:'${text("创建人")}', name:'user.userName', index:'a.user.user_name', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('talent:hsTalentIntroductionConf:edit')){
				actions.push('<a href="${ctx}/talent/hsTalentIntroductionConf/form?id='+row.id+'" class="hsBtnList" title="${text("编辑人才引进补助配置表")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/talent/hsTalentIntroductionConf/disable?id='+row.id+'" class="btnList" title="${text("停用人才引进补助配置表")}" data-confirm="${text("确认要停用该人才引进补助配置表吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/talent/hsTalentIntroductionConf/enable?id='+row.id+'" class="btnList" title="${text("启用人才引进补助配置表")}" data-confirm="${text("确认要启用该人才引进补助配置表吗？")}">启用</a>&nbsp;');
				}
				actions.push('<a href="${ctx}/talent/hsTalentIntroductionConf/delete?id='+row.id+'" class="btnList" title="${text("删除人才引进补助配置表")}" data-confirm="${text("确认要删除该人才引进补助配置表吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>