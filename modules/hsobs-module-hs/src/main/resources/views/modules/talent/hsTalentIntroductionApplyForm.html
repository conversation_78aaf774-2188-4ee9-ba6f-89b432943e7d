<% layout('/layouts/default.html', {title: '人才补助申请表管理', libs: ['validate','fileupload']}){ %>
<div class="main-content">
	<% if(!hsTalentIntroductionApply.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsTalentIntroductionApply}" title="人才补助申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsTalentIntroductionApply}" formKey="talent_subsidy_apply" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text( hsTalentIntroductionApply.isView == 1 ? '人才补助申请信息' : hsTalentIntroductionApply.isNewRecord ? '新增人才补助申请表' : '编辑人才补助申请表')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsTalentIntroductionApply}" action="${ctx}/talent/hsTalentIntroductionApply/save" method="post" class="form-horizontal">
			<div class="box-body  hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<#form:hidden path="opType"/>

				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('申请单位')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:treeselect id="unitId" title="${text('机构选择')}"
								path="unitId" labelPath="applyOffice.officeName" readonly="${isRead}"
								url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
								class="required" allowClear="true" canSelectRoot="true" canSelectParent="false"/>
							</td>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('身份证号码')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="applyNo" maxlength="255" readonly="${isRead}" class="form-control idcard required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('申请人姓名')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="applyName" maxlength="30" readonly="${isRead}" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('申请人电话')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="applyTel" maxlength="20" readonly="${isRead}" class="form-control mobile required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('学位')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:select path="eduBack" dictType="talent_edu_type" readonly="${isRead}" blankOption="true" class="form-control required" />
							</td>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('职称')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:select path="titleId" dictType="talent_title" readonly="${isRead}" blankOption="true" class="form-control required" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('人才类型')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="talentType" maxlength="255" readonly="${isRead}" class="form-control"/>
							</td>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('人才等级')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="talentLevel" maxlength="255" readonly="${isRead}" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('引进时间')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="arrivalTime" readonly="${isRead}" maxlength="20" class="form-control required laydate"
								dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd"/>
							</td>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('补助标准')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:select id="confId" path="confId" readonly="${isRead}" items="${confList}" itemLabel="subsidyName" itemValue="id" blankOption="true" blankOptionLabel="请选择" class="form-control required" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('补助总金额')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="subsidyFund" readonly="${isRead}" class="form-control number"/>
							</td>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('补助周期')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="subsidyPeriod" readonly="${isRead}" class="form-control digits"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('地市')}：<i class="fa icon-question hide"></i></td>
							<td>
<!--								<#form:input path="city" readonly="${isRead}" class="form-control "/>-->
								<#form:select path="city" id="citySelect" readonly="${isRead}" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
							</td>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('区域')}：<i class="fa icon-question hide"></i></td>
							<td>
<!--								<#form:input path="area" readonly="${isRead}" class="form-control "/>-->
								<#form:select path="area" id="areaSelect" readonly="${isRead}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></td>
							<td colspan="3">
								<#form:textarea path="remark" rows="4" readonly="${isRead}" maxlength="900" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('证明材料')}：</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile" bizKey="${hsTalentIntroductionApply.id}" bizType="hsTalentIntroductionApply_file"
								uploadType="all" class="" readonly="${isRead}" preview="true" dataMap="true"/>
							</td>
						</tr>

						<% if (hsTalentIntroductionApply.opType != 'update' && hsTalentIntroductionApply.applyStatus >= 5){ %>
						<tr>
							<td class="form-label hs-form-label" colspan="4">${text('财务处审批信息')}</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('意见')}：<i class="fa icon-question hide"></i></td>
							<td colspan="3">
								<#form:textarea path="approvalComment" rows="4" readonly="${hsTalentIntroductionApply.applyStatus > 5 ? true : false}" maxlength="900" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('材料')}：</td>
							<td colspan="3">
								<#form:fileupload id="uploadCwFile" bizKey="${hsTalentIntroductionApply.id}" bizType="hsTalentIntroductionApply_cwFile"
								uploadType="all" class="" readonly="${hsTalentIntroductionApply.applyStatus > 5 ? true : false}" preview="true" dataMap="true"/>
							</td>
						</tr>
						<% } %>

					</table>

<!--					<#bpm:nextTaskInfo bpmEntity="${hsTalentIntroductionApply}" />-->

				</div>
			</div>
			<div class="box-footer hs-footer-block">
				<% if (hsTalentIntroductionApply.isView == 0 && hsTalentIntroductionApply.opType != 'update') { if(!(hsTalentIntroductionApply.isNewRecord || hsTalentIntroductionApply.applyStatus == 0 || hsTalentIntroductionApply.applyStatus == 9)) { %>
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group" style="margin-bottom: 0;">
							<label class="control-label col-xs-2">审批意见：</label>
							<div class="col-xs-10">
								<#hobpm:comment bpmEntity="${hsTalentIntroductionApply}" required="true" rows="2" showCommWords="false" />
							</div>
						</div>
					</div>
				</div>
				<% }} %>
				<div class="row">
					<div class="col-sm-offset-4 col-sm-8">
						<% if (hasPermi('talent:hsTalentIntroductionApply:edit') && hsTalentIntroductionApply.isView == 0){ %>
							<#form:hidden path="status"/>
						    <% if (hsTalentIntroductionApply.isNewRecord || hsTalentIntroductionApply.applyStatus == -1){ %>
<!--								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;-->
							<% } %>
						    <% if (hsTalentIntroductionApply.opType == 'update') { %>
						    <button type="submit" class="btn btn-sm btn-primary mr3" data-type="complete"><i class="fa fa-check"></i> 提 交</button>
                            <% } else { %>
							<#bpm:button bpmEntity="${hsTalentIntroductionApply}" formKey="talent_subsidy_apply" completeText="提 交"/>
						    <% } %>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
	var confList = "#{toJson(confList)}";
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};

BpmButton.callback = function(){
	let idValue = $('#id').val();
	$.ajax({
		type: 'POST',
		url: "${ctx}/talent/hsTalentIntroductionApply/flushTaskStatus?___t=" + new Date().getTime(),
		data: {id: idValue},
		dataType: 'json',
		async: false,
		error: function(data){
			js.showErrorMessage(data.responseText);
		},
		success: function(data, status, xhr){
		}
	});
};

// 学位 职称  入职时间
$('#titleId').on ('change' , function(e, data) {
	changeConf();
});
$('#eduBack').on ('change' , function(e, data) {
	changeConf();
});
$('#arrivalTime').on ('change' , function(e, data) {
	changeConf();
});

function changeConf() {
	let titleId = $('#titleId').val();
	let eduBack = $('#eduBack').val();
	let arrivalTime = $('#arrivalTime').val();

	let filterConditions = {
		titleId: titleId,
		eduBackId: eduBack,
		arrivalTime: arrivalTime
	};

	let filteredList = confList.filter(item => {
		return Object.keys(filterConditions).every(key => {
			if (key === 'arrivalTime') {
				let flag = new Date(item[key]) >= new Date(filterConditions[key]);
				return flag;
			}
			let flag = item[key] === filterConditions[key];
			return flag;
		});
	});

	let selectedItem = filteredList.length > 0 ? filteredList[0] : null;
	if (selectedItem) {
		$('#confId').val(selectedItem.id).trigger('change');
		$('#subsidyFund').val(selectedItem.subsidyFund);
		$('#subsidyPeriod').val(selectedItem.subsidyPeriod);
	}
}

$('#confId').on ('change' , function(e, data) {
	let selectedValue = $(this).val();
	$.ajax({
		type: 'POST',
		url: "${ctx}/talent/hsTalentIntroductionConf/singleData?___t=" + new Date().getTime(),
		data: {id: selectedValue},
		dataType: 'json',
		async: false,
		error: function(data){
			js.showErrorMessage(data.responseText);
		},
		success: function(data, status, xhr){
			if (data != null) {
				if ($('#subsidyFund').length > 0) {
					$('#subsidyFund').val(data.subsidyFund);
					$('#subsidyPeriod').val(data.subsidyPeriod);
				}
			}
		}
	});
});


// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>
<script>
	$(document).ready(function() {

		<% if(!hsTalentIntroductionApply.isNewRecord){ %>
			showAreaInfo(${hsTalentIntroductionApply.area});
		<% } %>

		$('#citySelect').change(function() {
			showAreaInfo();
		});
		function showAreaInfo(defaultAreaCode) {

			let cityCode = $('#citySelect').val();

			let areaSelect = $('#areaSelect');
			areaSelect.empty();

			if (cityCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getAreaInfo',
					type: 'GET',
					data: {areaCode: cityCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						let option = $("<option>")
								.val('')
								.text('所有区域');
						$("#areaSelect").append(option);

						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.areaCode)
									.text(item.areaName);
							$("#areaSelect").append(option);
						});

						if(defaultAreaCode) {
							$("#areaSelect").val(defaultAreaCode).trigger('change');
						}
					}
				});
			}
		}
	});
</script>