<% layout('/layouts/default.html', {title: 'hs_qw_apply_score_detail管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('hs_qw_apply_score_detail管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<% if(hasPermi('applyscore:hsQwApplyScoreDetail:edit')){ %>
					<a href="${ctx}/applyscore/hsQwApplyScoreDetail/form" class="btn btn-default btnTool" title="${text('新增hs_qw_apply_score_detail')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwApplyScoreDetail}" action="${ctx}/applyscore/hsQwApplyScoreDetail/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('申请单ID')}：</label>
					<div class="control-inline">
						<#form:input path="applyId" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('规则得分类型')}：</label>
					<div class="control-inline">
						<#form:input path="ruleType" maxlength="1" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('轮候类型得分')}：</label>
					<div class="control-inline">
						<#form:input path="score" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('status')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('remarks')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("申请单ID")}', name:'applyId', index:'a.apply_id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/applyscore/hsQwApplyScoreDetail/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑hs_qw_apply_score_detail")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("规则得分类型")}', name:'ruleType', index:'a.rule_type', width:150, align:"left"},
		{header:'${text("轮候类型得分")}', name:'score', index:'a.score', width:150, align:"center"},
		{header:'${text("status")}', name:'status', index:'a.status', width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("update_date")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("remarks")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('applyscore:hsQwApplyScoreDetail:edit')){
				actions.push('<a href="${ctx}/applyscore/hsQwApplyScoreDetail/form?id='+row.id+'" class="hsBtnList" title="${text("编辑hs_qw_apply_score_detail")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/applyscore/hsQwApplyScoreDetail/delete?id='+row.id+'" class="btnList" title="${text("删除hs_qw_apply_score_detail")}" data-confirm="${text("确认要删除该hs_qw_apply_score_detail吗？")}">删除</a>&nbsp;');
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>