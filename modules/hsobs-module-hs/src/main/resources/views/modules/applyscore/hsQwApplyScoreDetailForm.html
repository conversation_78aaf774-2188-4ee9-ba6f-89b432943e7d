<% layout('/layouts/default.html', {title: 'hs_qw_apply_score_detail管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwApplyScoreDetail.isNewRecord ? '新增hs_qw_apply_score_detail' : '编辑hs_qw_apply_score_detail')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwApplyScoreDetail}" action="${ctx}/applyscore/hsQwApplyScoreDetail/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-12">
						<div class="hs-table-div">
							<table class="table-form hs-table-form">
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('申请单ID')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="applyId" maxlength="64" class="form-control required"/>
									</td>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('规则得分类型')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="ruleType" maxlength="1" class="form-control required"/>
									</td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required ">*</span> ${text('轮候类型得分')}：<i class="fa icon-question hide"></i>
									</td>
									<td>
										<#form:input path="score" class="form-control required digits"/>
									</td>
									<td class="form-label hs-form-label"></td>
									<td></td>
								</tr>
								<tr>
									<td class="form-label hs-form-label">
										<span class="required hide">*</span> ${text('remarks')}：<i class="fa icon-question hide"></i>
									</td>
									<td colspan="3">
										<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('applyscore:hsQwApplyScoreDetail:edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>