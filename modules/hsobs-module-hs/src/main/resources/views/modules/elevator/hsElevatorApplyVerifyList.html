<% layout('/layouts/default.html', {title: '加装电梯补助申请审批', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('加装电梯补助申请审批')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsElevatorApply}" action="${ctx}/elevator/hsElevatorApply/verifyListData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
			    <div class="form-group">
			    	<label class="control-label">${text('房屋信息')}：</label>
			    	<div class="control-inline">
			    		<#form:input path="houseId" maxlength="64" class="form-control width-120"/>
			    	</div>
			    </div>
			    <div class="form-group">
			    	<label class="control-label">${text('申请单位')}：</label>
			    	<div class="control-inline">
			    		<#form:treeselect id="unitId" title="${text('机构选择')}"
			    		path="unitId" labelPath="office.officeName"
			    		url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
			    		class="required" allowClear="true" canSelectRoot="true" canSelectParent="false"/>
			    	</div>
			    </div>
			    <div class="form-group">
			    	<label class="control-label">${text('联系人')}：</label>
			    	<div class="control-inline">
			    		<#form:input path="contactName" maxlength="30" class="form-control width-120"/>
			    	</div>
			    </div>
				</div>
				<div class="search-form-row">
			    <div class="form-group">
			    	<label class="control-label">${text('联系人电话')}：</label>
			    	<div class="control-inline">
			    		<#form:input path="contactTel" maxlength="20" class="form-control width-120"/>
			    	</div>
			    </div>
			    <div class="form-group">
			    	<label class="control-label">${text('申请原由')}：</label>
			    	<div class="control-inline">
			    		<#form:input path="applyReason" maxlength="900" class="form-control width-120"/>
			    	</div>
			    </div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
		</div>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn: false,
	columnModel: [
		{header:'${text("申请单号")}', name:'id', index:'a.id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/elevator/hsElevatorApply/form?id='+row.id+'" class="hsBtnList" data-title="${text("审批加装电梯补助申请")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("房屋信息")}', name:'houseId', index:'a.house_id', width:150, align:"left"},
		{header:'${text("申请单位")}', name:'applyOffice.treeNames', index:'a.apply_office.tree_names', width:150, align:"left"},
		{header:'${text("联系人")}', name:'contactName', index:'a.contact_name', width:150, align:"left"},
		{header:'${text("联系人电话")}', name:'contactTel', index:'a.contact_tel', width:150, align:"left"},
		{header:'${text("项目总费用")}', name:'totalFund', index:'a.total_fund', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("申请金额")}', name:'applyFund', index:'a.apply_fund', width:150, align:"right", formatter: function(val, obj, row, act){
			return js.formatNumber(val, 2, false, ''); // 数值类型格式化 (原始数值, 小数位数, 是否千分位, 默认值，金额情况下设置0.00);
		}},
		{header:'${text("申请原由")}', name:'applyReason', index:'a.apply_reason', width:150, align:"left"},
		{header:'${text("流程状态")}', name:'flowStatus', index:'a.flow_status', width:150, align:"center", formatter: function(val, obj, row, act){
			if (row.applyStatus == 9) {
				return '已结束';
			} else {
				return js.getDictLabel("#{@DictUtils.getDictListJson('elevator_flow_status')}", val + '', '${text("未知")}', true);
			}
			}},
		{header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"center"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('elevator:hsElevatorApply:edit')){
				actions.push('<a href="${ctx}/elevator/hsElevatorApply/form?id='+row.id+'" class="hsBtnList" title="${text("审批加装电梯补助申请")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/elevator/hsElevatorApply/disable?id='+row.id+'" class="btnList" title="${text("停用加装电梯补助申请表")}" data-confirm="${text("确认要停用该加装电梯补助申请表吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/elevator/hsElevatorApply/enable?id='+row.id+'" class="btnList" title="${text("启用加装电梯补助申请表")}" data-confirm="${text("确认要启用该加装电梯补助申请表吗？")}">启用</a>&nbsp;');
				}
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=elevator_subsidy_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>