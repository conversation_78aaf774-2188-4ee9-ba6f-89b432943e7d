<% layout('/layouts/default.html', {title: '加装电梯补助申请表管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<% if(!hsElevatorApply.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsElevatorApply}" title="加装电梯补助申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsElevatorApply}" formKey="elevator_subsidy_apply" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsElevatorApply.isNewRecord ? '新增加装电梯补助申请表' : '编辑加装电梯补助申请表')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsElevatorApply}" action="${ctx}/elevator/hsElevatorApply/save" method="post" class="form-horizontal">
			<div class="box-body hs-box-body-bpm" >
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('申请单位')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:treeselect id="unitId" title="${text('机构选择')}"
								path="unitId" labelPath="applyOffice.officeName" readonly="${isRead}"
								url="${ctx}/sys/office/treeData?ctrlPermi=${ctrlPermi}"
								class="required" allowClear="true" canSelectRoot="true" canSelectParent="false"/>
							</td>
							<td class="form-label hs-form-label"><span class="required">*</span> ${text('房屋信息')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="houseId" readonly="${isRead}" maxlength="64" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required">*</span> ${text('联系人')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="contactName" readonly="${isRead}" maxlength="30" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label"><span class="required">*</span> ${text('联系人电话')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="contactTel" readonly="${isRead}" maxlength="20" class="form-control mobile required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required">*</span> ${text('项目总费用')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="totalFund" readonly="${isRead}" class="form-control required number"/>
							</td>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('电梯服务户数')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="households" readonly="${isRead}" class="form-control digits"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('申请金额')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="applyFund" readonly="${isRead}" class="form-control required number"/>
							</td>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('已筹资金总额')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="existFund" readonly="${isRead}" class="form-control number"/>
							</td>
						</tr>

						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('已筹资金(业主)')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="existFundOwner" readonly="${isRead}" dataFormat="number" class="form-control required number"/>
							</td>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('已筹资金(住宅维修金)')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="existFundHouse" readonly="${isRead}" dataFormat="number" class="form-control required number"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('已筹资金(单位补助)')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="existFundUnit" readonly="${isRead}" dataFormat="number" class="form-control required number"/>
							</td>
							<td class="form-label hs-form-label"><span class="required ">*</span> ${text('已筹资金(其他)')}：<i class="fa icon-question hide"></i></td>
							<td>
								<#form:input path="existFundOther" readonly="${isRead}" dataFormat="number" class="form-control required number"/>
							</td>
						</tr>

						<tr>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('地市')}：<i class="fa icon-question hide"></i></td>
							<td>
								<!--								<#form:input path="city" readonly="${isRead}" class="form-control "/>-->
								<#form:select path="city" id="citySelect" readonly="${isRead}" items="${@com.hsobs.hs.modules.dataintelligence.util.AreaUtils.getList('350000')}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
							</td>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('区域')}：<i class="fa icon-question hide"></i></td>
							<td>
								<!--								<#form:input path="area" readonly="${isRead}" class="form-control "/>-->
								<#form:select path="area" id="areaSelect" readonly="${isRead}" itemLabel="areaName" itemValue="areaCode" blankOption="true" class="form-control required" />
							</td>
						</tr>

						<tr>
							<td class="form-label hs-form-label"><span class="required hide">*</span> ${text('申请原由')}：<i class="fa icon-question hide"></i></td>
							<td colspan="3">
								<#form:textarea path="applyReason" readonly="${isRead}" rows="5" maxlength="900" class="form-control"/>
							</td>
						</tr>
						<tr>
						<td rowspan="2" class="form-label hs-form-label"><span class="required">*</span> ${text('申请材料')}：</td>
						<td style="color: red;" colspan="3">
							<div >需提交工程立项书、项目预算书、工程施工合同、项目结算书、工程建安发票（复印件）等</div>
						</td>
						</tr>
						<tr>
						<td colspan="3">
							<#form:fileupload id="uploadFile" bizKey="${hsElevatorApply.id}" bizType="hsElevatorApply_file"
							uploadType="all" class="required" readonly="${isRead}" preview="true" dataMap="true"/>
						</td>
						</tr>
					</table>
				</div>
				<% if(hsElevatorApply.applyStatus >= 1){ %>
				<div class="form-unit">${text('申请受理信息')}</div>
				<div class="hs-table-div">
				    <table id="applyFirstDataGrid" class="table-form hs-table-form" ></table>
				</div>
				<% } %>
				<% if(hsElevatorApply.applyStatus >= 2){ %>
				<div class="form-unit">${text('现场勘察材料')}</div>
				<div id="investigationDiv" class="hs-table-div">
					<table class="table-form hs-table-form" >
						<tr>
							<td colspan="4" class="form-label hs-form-label" style="color: red;"><span  class="required">*</span> ${text('提示')}：请提交勘测结果、勘测意见、现场记录等材料。</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span id="surveyAnalysisSpan" class="required">*</span> ${text('勘测结果')}：</td>
							<td colspan="3">
								<#form:textarea path="surveyAnalysis" readonly="false" rows="3" maxlength="400" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span id="surveyConclusionSpan" class="required">*</span> ${text('勘测意见')}：</td>
							<td colspan="3">
								<#form:textarea path="surveyConclusion" readonly="false" rows="3" maxlength="400" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label"><span id="investigationDivSpan" class="required">*</span> ${text('勘察材料')}：</td>
							<td colspan="3">
								<#form:fileupload id="investigationUploadFile" bizKey="${hsElevatorApply.id}" bizType="hsElevatorApply_investigationFile"
								uploadType="all" class="required" readonly="${hsElevatorApply.applyStatus > 2 ? true : false}" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
				<% } %>
			</div>
			<div class="box-footer hs-footer-block">
				<% if(!(hsElevatorApply.isNewRecord || hsElevatorApply.applyStatus == 0 || hsElevatorApply.applyStatus == 9)) { %>
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group" style="margin-bottom: 0;">
							<label class="control-label col-xs-2">审批意见：</label>
							<div class="col-xs-10">
								<#hobpm:comment bpmEntity="${hsElevatorApply}" required="true" rows="2" showCommWords="false" />
							</div>
						</div>
					</div>
				</div>
				<!--				<#bpm:nextTaskInfo bpmEntity="${hsElevatorApply}" />-->
				<% } %>
				<div class="row">
					<div class="col-sm-offset-4 col-sm-8">
						<% if (hasPermi('elevator:hsElevatorApply:edit')){ %>
							<#form:hidden path="status"/>
							<% if (hsElevatorApply.isNewRecord || hsElevatorApply.applyStatus == -1){ %>
<!--								<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;-->
							<% } %>
							<#bpm:button bpmEntity="${hsElevatorApply}" formKey="elevator_subsidy_apply" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}

	<% if(hsElevatorApply.applyStatus > 2){ %>
		$('#investigationUploadFile').prop("readonly", true).removeClass("required");
		$('#surveyConclusionSpan').hide();
		$('#surveyConclusion').prop("readonly", true).removeClass("required");
		$('#surveyAnalysisSpan').hide();
		$('#surveyAnalysis').prop("readonly", true).removeClass("required");
	<% } %>

}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
};

BpmButton.callback = function(){
	let idValue = $('#id').val();
	$.ajax({
		type: 'POST',
		url: "${ctx}/elevator/hsElevatorApply/flushTaskStatus?___t=" + new Date().getTime(),
		data: {id: idValue},
		dataType: 'json',
		async: false,
		error: function(data){
			js.showErrorMessage(data.responseText);
		},
		success: function(data, status, xhr){
		}
	});
};


function existFundChange() {
	let ownerValue = $('#existFundOwner').val();
	let houseValue = $('#existFundHouse').val();
	let unitValue = $('#existFundUnit').val();
	let otherValue = $('#existFundOther').val();

	let ownerValue1 = parseFloat(ownerValue);
	let houseValue1 = parseFloat(houseValue);
	let unitValue1 = parseFloat(unitValue);
	let otherValue1 = parseFloat(otherValue);

	let fundTotal = 0;

	if (!isNaN(ownerValue1)) {
		fundTotal = fundTotal + ownerValue1;
	}
	if (!isNaN(houseValue1)) {
		fundTotal = fundTotal + houseValue1;
	}
	if (!isNaN(unitValue1)) {
		fundTotal = fundTotal + unitValue1;
	}
	if (!isNaN(otherValue1)) {
		fundTotal = fundTotal + otherValue1;
	}
	$('#existFund').val(fundTotal);
}

$('#existFundOwner').on ('change' , function(e, data) {
	existFundChange();
});
$('#existFundHouse').on ('change' , function(e, data) {
	existFundChange();
});
$('#existFundUnit').on ('change' , function(e, data) {
	existFundChange();
});
$('#existFundOther').on ('change' , function(e, data) {
	existFundChange();
});

existFundChange();


// 表单验证提交事件
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});
</script>
<script>
	<% if(hsElevatorApply.applyStatus >= 1){ %>
	$(function(){
		// applyFirstDataGrid
		$("#applyFirstDataGrid").dataGrid({
			data: [],
			datatype: 'local',
			autoGridHeight: function(){return 'auto';}, // 设置自动高度
			// 设置数据表格列
			columnModel: [
				{header:'${text("环节名称")}', name:'name', width:150, align:"center", formatter: function(val, obj, row, act){
						return (val||'${text("未设置名称")}');
					}},
				{header:'${text("受理日期")}', name:'createTime', width:100, align:"center"},
				{header:'${text("完成日期")}', name:'endTime', width:100, align:"center"},
				{header:'${text("任务历时")}', name:'durationFormat', width:80, align:"center"},
				{header:'${text("受理人")}', name:'assigneeInfo', width:150, align:"left"},
				{header:'${text("受理意见")}', name:'comment', width:200, align:"left", classes:"textarea"}
			],
			sortableColumn: false,
			//# // 加载成功后执行事件
			ajaxSuccess: function(data){
			}
		});
		let procIns = getProcIns();
		if(procIns){
			$.ajax({
				type: 'POST',
				url: "${ctx}/bpm/display/app/rest/process-instances/" + procIns.id + "/trace-json?t=" + new Date().getTime(),
				data: {},
				dataType: 'json',
				async: false,
				error: function(data){
					// js.showErrorMessage(data.responseText);
				},
				success: function(data, status, xhr){
					if (data != null) {
						let filteredArray = data.filter(item => item.activityId === 'elevatorSubsidyApply0002');
						$("#applyFirstDataGrid").dataGrid('setParam', {data: filteredArray, page: 1, rowNum: 5000}, true).dataGrid('reloadGrid');
					}
				}
			})
		}
	})
	<% } %>
</script>
<script>
	$(document).ready(function() {

		<% if(!hsElevatorApply.isNewRecord){ %>
			showAreaInfo(${hsElevatorApply.area});
		<% } %>

		$('#citySelect').change(function() {
			showAreaInfo();
		});
		function showAreaInfo(defaultAreaCode) {

			let cityCode = $('#citySelect').val();

			let areaSelect = $('#areaSelect');
			areaSelect.empty();

			if (cityCode) {
				$.ajax({
					url: '${ctx}/dataintelligencetotal/getAreaInfo',
					type: 'GET',
					data: {areaCode: cityCode},
					dataType: 'json',
					async: false,
					error: function(data){
						js.showErrorMessage(data.responseText);
					},
					success: function(data, status, xhr){
						let option = $("<option>")
								.val('')
								.text('所有区域');
						$("#areaSelect").append(option);

						$.each(data, function(index, item) {
							let option = $("<option>")
									.val(item.areaCode)
									.text(item.areaName);
							$("#areaSelect").append(option);
						});

						if(defaultAreaCode) {
							$("#areaSelect").val(defaultAreaCode).trigger('change');
						}

					}
				});
			}
		}
	});
</script>