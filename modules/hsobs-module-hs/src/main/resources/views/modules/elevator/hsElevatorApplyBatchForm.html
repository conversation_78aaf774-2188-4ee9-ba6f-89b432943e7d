<% layout('/layouts/default.html', {title: '一键审批', libs: ['validate','dataGrid']}){ %>
<div class="box box-main">
    <#form:form id="inputForm" action="${ctx}/elevator/hsElevatorApply/batchSave" method="post" class="form-horizontal">
    <#form:hidden name="idsStr" value="${hsElevatorApply.ids}" class="form-control"/>
    <div class="box-body" style="padding-top:20px">
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="control-label col-xs-3">${text('审批动作')}：</label>
                    <div class="col-xs-9">
                        <#form:radio path="approvalType" dictType="sys_approval_status" defaultValue="0" class="form-control required" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <style>.col-xs-3 { width: 21% }</style>
                    <label class="control-label col-xs-3">${text('当前环节')}：</label>
                    <div class="col-xs-9">
                        <label class="control-label">${isNotBlank(task.name)?task.name:text("未设置环节名")}</label>
                    </div>
                </div>
            </div>
        </div>

        <div id="noPassDiv" class="row hide">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="control-label col-xs-3">${text('退回到哪')}：</label>
                    <div class="col-xs-9">
                        <% var items = [{label:text('退回到发起人'),value:'1'},{label:text('退回到上一步'),value:'2'},{label:text('退回任意环节'),value:'3'}]; %>
                        <#form:radio name="action" value="1" items="${items}" itemLabel="label" itemValue="value" class="form-control required" />
                        <table id="backActivity"></table>
                        <#form:hidden path="bpm.activityId" />
                        <#form:hidden path="bpm.nextUserCodes" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="control-label col-xs-3">${text('审批意见')}：</label>
                    <div class="col-xs-9">
                        <#form:textarea path="bpm.comment" rows="2" maxlength="200" class="form-control"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="box-footer">
        <div class="row">
            <div class="col-sm-offset-3 col-sm-10">
                <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('确 定')}</button>&nbsp;
<!--                <button type="button" class="btn btn-sm btn-default addTabPage" data-href="${ctx}/bpm/bpmRuntime/trace?id=${task.procIns.id!}&status=${task.procIns.status!}" data-title="${text('流程图')}" data-layer="true"><i class="fa fa-file-picture-o"></i> ${text('流程图')}</button>&nbsp;-->
                <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
            </div>
        </div>
    </div>
</#form:form>
</div>
<% } %>
<script>
    var approvalTypeValue = '0';
    var backActivity = "#{toJson(backActivity!)}";
    $("#backActivity").dataGrid({
        data: backActivity,
        datatype: 'local', // 设置本地数据
        autoGridHeight: function(){return 150;}, // 设置自动高度
// 	autoGridWidth: function(){return 450;}, // 设置自动高度
        // 设置数据表格列
        columnModel: [
            {header:'${text("编号")}', name:'activityId', hidden:true},
            {header:'${text("环节名称")}', name:'activityName', width:160, align:"left", formatter: function(val, obj, row, act){
                    return (val||'${text("未设置环节名")}');
                }},
            {header:'${text("处理人编号")}', name:'assignee', hidden:true},
            {header:'${text("处理人名称")}', name:'assigneeName', width:180, align:"left"},
            {header:'${text("处理时间")}', name:'startTime', width:140, align:"left"}
        ],
        onSelectRow: function(id, isSelect, event) {
            var rowId = parseInt(id);
            if (rowId == 1){
                $('#action1').closest('label').click();
            } else if (rowId == backActivity.length){
                $('#action2').closest('label').click();
            } else {
                $('#action3').closest('label').click();
            }
        },
        sortableColumn: false,
        //# // 加载成功后执行事件
        ajaxSuccess: function(data){
            $('#backActivity').dataGrid('setSelectRow', '1');
            $('#action1').closest('label').click();
        }
    });
    $('#action input').on('ifCreated ifChecked', function(){
        if ($(this).is(':checked')){
            if (backActivity.length <= 1){
                return;
            }
            var div = $("#backActivity").parent();
            if ($(this).val() == '1'){
                $("#backActivity tr:eq(1)").click();
                div.scrollTop(0);
            }else if ($(this).val() == '2'){
                $("#backActivity tr:last").click();
                div.scrollTop(div[0].scrollHeight);
            }
        }
    });
    $('#inputForm').validate({
        submitHandler: function(form){
            if (approvalTypeValue === '0') {
            } else {
                var backActivityDataGrid = $('#backActivity'),
                    rowId = backActivityDataGrid.dataGrid('getSelectRow');
                if (!rowId){
                    js.showMessage('${text("请选择退回到哪")}？');
                    return;
                }
                var rowData = backActivityDataGrid.dataGrid('getRowData', rowId);
                $('#bpm_activityId').val(rowData.activityId);
                $('#bpm_nextUserCodes').val(rowData.assignee);
            }
            js.ajaxSubmitForm($(form), function(data){
                js.showMessage(data.message);
                if(data.result == Global.TRUE){
                    js.closeCurrentTabPage(function(contentWindow){
                        if (contentWindow.BpmButton){
                            contentWindow.BpmButton.callback();
                        }else{
                            contentWindow.page();
                        }
                    });
                }
            }, "json");
        }
    });

    $('#approvalType input').on('ifCreated ifChecked', function(event){
        if ($(this).is(':checked')){
            var approvalType = $(this).val(); // 审批动作（0同意 1驳回）
            approvalTypeValue = approvalType;
            if (approvalType === '0'){
                $('#noPassDiv').addClass('hide');
                $('#bpm_comment').val('同意');
            }else{
                $('#noPassDiv').removeClass('hide');
                $('#bpm_comment').val('不同意');
            }
        }
    });

</script>