<% layout('/layouts/default.html', {title: '租赁资格轮候资格配置表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text('租赁资格轮候资格配置表管理')}
            </div>
            <div class="box-tools pull-right">
                <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
                <% if(hasPermi('qualification:hsQwApplyQualification:edit')){ %>
                    <a href="${ctx}/qualification/hsQwApplyQualification/form" class="btn btn-default btnTool" title="${text('新增租赁资格轮候资格配置表')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
                <% } %>
                <a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
            </div>
        </div>
        <div class="box-body">
            <div class="search-form-container">
                <#form:form id="searchForm" model="${hsQwApplyQualification}" action="${ctx}/qualification/hsQwApplyQualification/listData" method="post" class="form-inline hide"
                        data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('资格名称')}：</label>
                            <div class="control-inline">
                                <#form:input path="quaName" maxlength="100" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('状态')}：</label>
                            <div class="control-inline ">
                                <#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('备注信息')}：</label>
                            <div class="control-inline">
                                <#form:input path="remarks" maxlength="500" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>
                    <div class="search-button-row">
                        <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                        <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
                    </div>
                </#form:form>
            </div>
            <div class="fixed-table-container">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
        </div>
    </div>
</div>
<% } %>
<script>
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false,
    autowidth: false,
    //scroll: true,
    scrollOffset: 18,
    width: '100%',
    height: 'auto',
    columnModel: [
        {header:'${text("资格名称")}', name:'quaName', index:'a.qua_name', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
            return '<a href="${ctx}/qualification/hsQwApplyQualification/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候资格配置表")}">'+(val||row.id)+'</a>';
        }},
        {header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
        }},
        {header:'${text("更新时间")}', name:'updateDate', index:'a.update_date', width:150, align:"left"},
        {header:'${text("备注信息")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
        {header:'${text("操作")}', name:'actions', width:150, align:"left", frozen:"right", formatter: function(val, obj, row, act){
            var actions = [];
            //# if(hasPermi('qualification:hsQwApplyQualification:edit')){
                actions.push('<a href="${ctx}/qualification/hsQwApplyQualification/form?id='+row.id+'" class="hsBtnList" title="${text("编辑租赁资格轮候资格配置表")}">编辑</a>&nbsp;');
                if (row.status == Global.STATUS_NORMAL){
                    actions.push('<a href="${ctx}/qualification/hsQwApplyQualification/disable?id='+row.id+'" class="btnList" title="${text("停用租赁资格轮候资格配置表")}" data-confirm="${text("确认要停用该租赁资格轮候资格配置表吗？")}">停用</a>&nbsp;');
                } else if (row.status == Global.STATUS_DISABLE){
                    actions.push('<a href="${ctx}/qualification/hsQwApplyQualification/enable?id='+row.id+'" class="btnList" title="${text("启用租赁资格轮候资格配置表")}" data-confirm="${text("确认要启用该租赁资格轮候资格配置表吗？")}">启用</a>&nbsp;');
                }
                actions.push('<a href="${ctx}/qualification/hsQwApplyQualification/delete?id='+row.id+'" class="btnList" title="${text("删除租赁资格轮候资格配置表")}" data-confirm="${text("确认要删除该租赁资格轮候资格配置表吗？")}">删除</a>&nbsp;');
            //# }
            return actions.join('');
        }}
    ],
    ajaxSuccess: function() {
        requestAnimationFrame(function() {
            CommonTable.setupFixedColumns('dataGrid');
        });
        // js.closeLoading(0, true);
    }
});

</script>