<% layout('/layouts/default.html', {title: '限价房购房申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('限价房购房申请管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsPriceLimitApply}" action="${ctx}/pricelimitapply/hsPriceLimitApply/listAuditDoneData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('申请编号')}：</label>
					<div class="control-inline">
						<#form:input path="id" maxlength="20" class="form-control width-250"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.name" maxlength="20" class="form-control width-250"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('身份证号')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-250"/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('联系电话')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-250"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline width-250" >
						<#form:treeselect id="officeId" title="${text('工作单位')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
			<div class="form-group">
				<label class="control-label">${text('审批状态')}：</label>
				<div class="control-inline width-120">
					<#form:select path="flowStatus" dictType="pricelimit_apply_status" blankOption="true" class="form-control"/>
				</div>
			</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn : false,
	columnModel: [
		{header:'${text("申请编号")}', name:'id', index:'a.id', width:60, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/pricelimitapply/hsPriceLimitApply/archives/form?type=0&id='+row.id+'" class="hsBtnList" data-title="${text("查看限价房购房申请")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请单内容")}', name:'applyTitle', index:'a.apply_title', width:130, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/pricelimitapply/hsPriceLimitApply/archives/form?type=0&id='+row.id+'" class="hsBtnList" data-title="${text("查看限价房购房申请")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请人")}', name:'mainApplyer.name', index:'a.main_applyer.name', width:60, align:"left"},
		{header:'${text("身份证号")}', name:'mainApplyer.idNum', index:'a.main_applyer.id_num', width:60, align:"left"},
		{header:'${text("联系电话")}', name:'mainApplyer.phone', index:'a.main_applyer.phone', width:60, align:"left"},
		{header:'${text("工作单位")}', name:'office.officeName', index:'a.office.office_name', width:100, align:"left"},
		{header:'${text("审批状态")}', name:'flowStatus', index:'a.flowStatus', width:60, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('pricelimit_apply_status')}", val, '${text("")}', true);
			}},
		{header:'${text("申请理由")}', name:'remarks', index:'a.remarks', width:130, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('pricelimitapply:hsPriceLimitApply:edit')){
				actions.push('<a href="${ctx}/pricelimitapply/hsPriceLimitApply/archives/form?type=0&id='+row.id+'" class="hsBtnList" title="${text("查看限价房购房申请")}">编辑</a>&nbsp;');
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=pricelimit_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/pricelimitapply/hsPriceLimitApply/exportAuditDoneData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>