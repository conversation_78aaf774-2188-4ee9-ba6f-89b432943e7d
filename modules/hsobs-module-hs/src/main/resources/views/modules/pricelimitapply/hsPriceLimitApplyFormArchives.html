<% layout('/layouts/default.html', {title: '限价房购房申请管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsPriceLimitApply.isNewRecord ? '查看限价房购房申请' : '查看限价房购房申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsPriceLimitApply}" action="${ctx}/pricelimitapply/hsPriceLimitApply/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('限价房方案')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:input path="plan.title" maxlength="200" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('房源信息')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
									<a href="${ctx}/house/hsQwPublicRentalHouse/form?id=${hsPriceLimitApply.houseId}" class="btnList" data-title="${text('查看限价房房房源房源信息表')}">
									${hsPriceLimitApply.houseInfo}
									</a>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('合同编号')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="contractId" maxlength="200" readonly="true" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('限价房金额(元)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="Price" maxlength="200" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('申请理由')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:textarea path="remarks" rows="4" maxlength="500" readonly="true" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('申请附件')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFileApply" bizKey="${hsPriceLimitApply.id}" bizType="hsPriceLimitApplyApply_file" uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('资格确认单')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFileExamine" bizKey="${hsPriceLimitApply.id}" bizType="hsPriceLimitApplyExamine_file" uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('合同材料')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFileContract" bizKey="${hsPriceLimitApply.id}" bizType="hsPriceLimitApplyContract_file" uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>

				<div class="form-unit">${text('限价房购房申请人')}</div>
				<div class="form-unit-wrap table-form">
					<table id="hsPriceLimitApplyerDataGrid"></table>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if(hsPriceLimitApply.type=="0"){ %>
							<#hobpm:button bpmEntity="${hsPriceLimitApply}" formKey="pricelimit_apply" completeText=""/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
//# // 初始化限价房-购房申请人DataGrid对象
$('#hsPriceLimitApplyerDataGrid').dataGrid({

	data: "#{toJson(hsPriceLimitApply.hsPriceLimitApplyerList)}",
	datatype: 'local', // 设置本地数据
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	
	//# // 设置数据表格列
	columnModel: [
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'${text("申请人角色")}', name:'applyRole', width:100,formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_applyer_role')}", val, '${text("未知")}', true);
			}
		},
		{header:'${text("申请人姓名")}', name:'name', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
		{header:'${text("身份证号")}', name:'idNum', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required idcard'}},
		{header:'${text("联系电话")}', name:'phone', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'32', 'class':'form-control required mobile'}},
		{header:'${text("工作单位")}', name:'organization', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
		{header:'${text("户籍")}', name:'census', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
		{header:'${text("婚姻状况")}', name:'maritalStatus', width:100,align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_marital_status')}", val, '${text("未知")}', true);
			}
		},
		{header:'${text("年收入(元)")}', name:'annualIncome', width:150, editable:false, align:"left", edittype:'text', editoptions:{'class':'form-control required digits'}},
		{header:'${text("住房")}', name:'area', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
	],
	
	//# // 编辑表格参数
	editGrid: false,				// 是否是编辑表格
	editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsPriceLimitApplyerDataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

	//# // 编辑表格的提交数据参数
	editGridInputFormListName: 'hsPriceLimitApplyerList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,userId,applyRole,applyId,name,organization,idNum,phone,census,maritalStatus,annualIncome,area,createBy,createDate,updateBy,updateDate,remarks,', // 提交数据列表的属性字段
	
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
		$("#houseIdName").val('');
	}
}
</script>