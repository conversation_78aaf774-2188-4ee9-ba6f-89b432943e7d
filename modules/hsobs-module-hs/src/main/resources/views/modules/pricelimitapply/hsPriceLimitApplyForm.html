<% layout('/layouts/default.html', {title: '限价房购房申请管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<% if(!hsPriceLimitApply.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsPriceLimitApply}" title="限价房购房申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsPriceLimitApply}" formKey="pricelimit_apply" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsPriceLimitApply.isNewRecord ? '新增限价房购房申请' : '编辑限价房购房申请')}
			</div>
			<div class="box-tools pull-right">
				<% if(hasPermi('pricelimitapply:hsPriceLimitApply:edit')){ %>
					<% } %>
				<button type="button" class="btn btn-box-tool hide" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsPriceLimitApply}" action="${ctx}/pricelimitapply/hsPriceLimitApply/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('限价房方案')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<% if(hsPriceLimitApply.isNewRecord || hsPriceLimitApply.applyStatus <= 2){ %>
								<#form:select path="planId" labelPath="plan.title" items="${@com.hsobs.hs.modules.pricelimitplan.utils.HsPriceLimitPlanUtils.getPriceLimitPlanList()}" itemLabel="title" itemValue="id" class="form-control" />
								<% } else { %>
								<#form:input path="plan.title" maxlength="200" readonly="true" class="form-control required"/>
								<% } %>
							</td>
						</tr>

				<% if(!hsPriceLimitApply.isNewRecord && hsPriceLimitApply.applyStatus >= 25){ %>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('房源信息')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<% if(hsPriceLimitApply.applyStatus == 25){ %>
									<#form:listselect id="houseIdSelect" title="房源选择"
									path="houseId" labelPath="house.simpleInfo"
									url="${ctx}/house/hsQwPublicRentalHouse/housePlacementSelect?isPublic=1&type=1" allowClear="false"
									checkbox="false" itemCode="id" readonly="false" itemName="simpleInfo"/>
								<% } else { %>
									<a href="${ctx}/house/hsQwPublicRentalHouse/form?id=${hsPriceLimitApply.houseId}" class="btnList" data-title="${text('查看限价房房房源房源信息表')}">
									${hsPriceLimitApply.houseInfo}
									</a>
								<% } %>
							</td>
						</tr>
				<% } %>

				<% if(!hsPriceLimitApply.isNewRecord && hsPriceLimitApply.applyStatus >= 35){ %>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('合同编号')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="contractId" maxlength="200" readonly="${text(hsPriceLimitApply.applyStatus==35?'false':'true')}" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('限价房金额(元)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="Price" maxlength="200" readonly="${text(hsPriceLimitApply.applyStatus==35?'false':'true')}" class="form-control number required"/>
							</td>
						</tr>
				<% } %>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('申请理由')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:textarea path="remarks" rows="4" maxlength="500" readonly="${hsPriceLimitApply.readOnly}" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('申请附件')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFileApply" bizKey="${hsPriceLimitApply.id}" bizType="hsPriceLimitApplyApply_file" uploadType="all" class="required" readonly="${hsPriceLimitApply.readOnly}" preview="true" dataMap="true"/>
							</td>
						</tr>
				<% if(!hsPriceLimitApply.isNewRecord){ %>
				<% if(!hsPriceLimitApply.isNewRecord && hsPriceLimitApply.applyStatus >= 25){ %>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('资格确认单')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFileExamine" bizKey="${hsPriceLimitApply.id}" bizType="hsPriceLimitApplyExamine_file" uploadType="all" class="required" readonly="${text(hsPriceLimitApply.applyStatus==25?'false':'true')}" preview="true" dataMap="true"/>
							</td>
						</tr>
				<% } %>

				<% if(!hsPriceLimitApply.isNewRecord && hsPriceLimitApply.applyStatus >= 35){ %>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('合同材料')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFileContract" bizKey="${hsPriceLimitApply.id}" bizType="hsPriceLimitApplyContract_file" uploadType="all" class="required" readonly="${text(hsPriceLimitApply.applyStatus==35?'false':'true')}" preview="true" dataMap="true"/>
							</td>
						</tr>
				<% } %>
				<% } %>
					</table>
				</div>

				<div class="form-unit">${text('限价房购房申请人')}</div>
				<div class="form-unit-wrap table-form">
					<table id="hsPriceLimitApplyerDataGrid"></table>
<% if(hsPriceLimitApply.isNewRecord || hsPriceLimitApply.applyStatus <= 2){ %>
					<% if (hasPermi('pricelimitapply:hsPriceLimitApply:edit')){ %>
						<a href="#" id="hsPriceLimitApplyerDataGridAddRowBtn" class="btn btn-primary btn-sm mt10 mb10"><i class="fa fa-plus"></i> ${text('增行')}</a>
					<% } %>
<% } %>
				</div>

				<div class="form-unit">${text('审批')}</div>
				<div class="row taskComment hide">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-xs-2">审批意见：</label>
							<div class="col-xs-10">
								<#bpm:comment bpmEntity="${hsPriceLimitApply}" showCommWords="true" />
							</div>
						</div>
					</div>
				</div>
<!--				<#bpm:nextTaskInfo bpmEntity="${hsPriceLimitApply}" />-->
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('pricelimitapply:hsPriceLimitApply:edit')){ %>
							<#form:hidden path="status"/>
						    <#form:hidden path="applyStatus"/>
							<% if (hsPriceLimitApply.isNewRecord || hsPriceLimitApply.status == '9'){ %>
						<!---	<button type="submit" class="btn btn-sm btn-info" id="btnDraft"><i class="fa fa-save"></i> 暂 存</button>&nbsp;  --->
                        	<% } %>
                        <#bpm:button bpmEntity="${hsPriceLimitApply}" formKey="pricelimit_apply" completeText="提 交"/>
                    	<% } %>
						<% if (!hsPriceLimitApply.isNewRecord && hsPriceLimitApply.applyStatus == 25){ %>
						<a href="#" class="btn btn-default" id="btnExamine"><i class="glyphicon glyphicon-export"></i> ${text('生成确认单')}</a>
						<% } %>
                    <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
                </div>
            </div>
        </div>
    </#form:form>
</div>
</div>
<% } %>
<script>
<% if(hsPriceLimitApply.isNewRecord || hsPriceLimitApply.applyStatus <= 2){ %>

$('#hsPriceLimitApplyerDataGrid').dataGrid({
	data: "#{toJson(hsPriceLimitApply.hsPriceLimitApplyerList)}",
	datatype: 'local', // 设置本地数据
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	//# // 设置数据表格列
	columnModel: [
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'${text("申请人角色")}', name:'applyRole', width:100,
			editable:true, edittype:'select', editoptions:{'class':'form-control',
				items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('hs_applyer_role')}"),
				itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
					js.select2(element).on("change",function(){
						$(this).resetValid()});
				}
			}
		},
		{header:'${text("申请人姓名")}', name:'name', width:150, editable:true, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
		{header:'${text("身份证号")}', name:'idNum', width:150, editable:true, align:"left", edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required idcard'}},
		{header:'${text("联系电话")}', name:'phone', width:150, editable:true, align:"left", edittype:'text', editoptions:{'maxlength':'32', 'class':'form-control required mobile'}},
		{header:'${text("工作单位")}', name:'organization', width:150, editable:true, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
		{header:'${text("户籍")}', name:'census', width:150, editable:true, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
		{header:'${text("婚姻状况")}', name:'maritalStatus', width:150, editable:true, align:"left",
			edittype:'select', editoptions:{'class':'form-control',
				items: $.merge([{dictLabel:'&nbsp;',dictValue:''}], "#{@DictUtils.getDictListJson('hs_marital_status')}"),
				itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
					js.select2(element).on("change",function(){
						$(this).resetValid()});
				}
			}
		},
		{header:'${text("年收入(元)")}', name:'annualIncome', width:150, editable:true, align:"left", edittype:'text', editoptions:{'class':'form-control required digits'}},
		{header:'${text("住房")}', name:'area', width:150, editable:true, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
		{header:'${text("操作")}', name:'actions', width:80, align:'left', formatter: function(val, obj, row, act){
				var actions = [];
				if (val == 'new'){
					actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsPriceLimitApplyerDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\')});return false;">删除</a>&nbsp;');
				}else{
					actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsPriceLimitApplyerDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');});return false;">删除</a>&nbsp;');
				}
				return actions.join('');
			}, editoptions: {defaultValue: 'new'}}
	],
	//# // 编辑表格参数
	editGrid: true,				// 是否是编辑表格
	editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsPriceLimitApplyerDataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL , applyRole: '0', maritalStatus: '0'},	// 新增行的时候初始化的数据
	//# // 编辑表格的提交数据参数
	editGridInputFormListName: 'hsPriceLimitApplyerList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,userId,applyRole,applyId,name,organization,idNum,phone,census,maritalStatus,annualIncome,area,createBy,createDate,updateBy,updateDate,remarks,', // 提交数据列表的属性字段
	//# // 加载成功后执行事件
		ajaxSuccess: function(data){
	}
});

<% } else { %>

$('#hsPriceLimitApplyerDataGrid').dataGrid({
	data: "#{toJson(hsPriceLimitApply.hsPriceLimitApplyerList)}",
	datatype: 'local', // 设置本地数据
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	//# // 设置数据表格列
	columnModel: [
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'${text("申请人角色")}', name:'applyRole', width:100,formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_applyer_role')}", val, '${text("未知")}', true);
			}
		},
		{header:'${text("申请人姓名")}', name:'name', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
		{header:'${text("身份证号")}', name:'idNum', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required idcard'}},
		{header:'${text("联系电话")}', name:'phone', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'32', 'class':'form-control required mobile'}},
		{header:'${text("工作单位")}', name:'organization', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
		{header:'${text("户籍")}', name:'census', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
		{header:'${text("婚姻状况")}', name:'maritalStatus', width:100,align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_marital_status')}", val, '${text("未知")}', true);
			}
		},
		{header:'${text("年收入(元)")}', name:'annualIncome', width:150, editable:false, align:"left", edittype:'text', editoptions:{'class':'form-control required digits'}},
		{header:'${text("住房")}', name:'area', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
	],
	//# // 编辑表格参数
	editGrid: false,				// 是否是编辑表格
	editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsPriceLimitApplyerDataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL , applyRole: '0', maritalStatus: '0'},	// 新增行的时候初始化的数据
	//# // 编辑表格的提交数据参数
	editGridInputFormListName: 'hsPriceLimitApplyerList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,userId,applyRole,applyId,name,organization,idNum,phone,census,maritalStatus,annualIncome,area,createBy,createDate,updateBy,updateDate,remarks,', // 提交数据列表的属性字段
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});
<% } %>
</script>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
if (task.status != '2') {
    $('.taskComment').removeClass('hide');
}
}
BpmButton.complete = function($this, task){
$('#status').val(Global.STATUS_AUDIT);
$("#houseIdName").val('');
};

BpmButton.callback = function(){
let idValue = $('#id').val();
$.ajax({
    type: 'POST',
    url: "${ctx}/pricelimitapply/hsPriceLimitApply/flushTaskStatus?___t=" + new Date().getTime(),
    data: {id: idValue},
    dataType: 'json',
    async: false,
    error: function(data){
        js.showErrorMessage(data.responseText);
    },
    success: function(data, status, xhr){
    }
});
};

// 表单验证提交事件
$('#inputForm').validate({
submitHandler: function(form){
    js.ajaxSubmitForm($(form), function(data){
        js.showMessage(data.message);
        if(data.result == Global.TRUE){
            js.closeCurrentTabPage(function(contentWindow){
                contentWindow.page();
            });
        }
    }, "json");
}
});
$('#btnExamine').click(function(){
	js.ajaxSubmitForm($('#inputForm'), {
		url: '${ctx}/pricelimitapply/hsPriceLimitApply/createExamine',
		clearParams: 'pageNo,pageSize',
		downloadFile: true,
		success: function(response, status, xhr) {
		}
	});
});
</script>
