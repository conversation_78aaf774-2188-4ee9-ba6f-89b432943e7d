<% layout('/layouts/default.html', {title: '限价房购房申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('限价房购房申请管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<% if(hasPermi('pricelimitapply:hsPriceLimitApply:add')){ %>
					<a href="${ctx}/pricelimitapply/hsPriceLimitApply/form" class="btn btn-default btnTool" title="${text('新增限价房购房申请')}"><i class="fa fa-plus"></i> ${text('新增')}</a>
				<% } %>
				<% if(hasPermi('pricelimitapply:hsPriceLimitApply:input')){ %>
				<a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入房源</a>
				<% } %>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsPriceLimitApply}" action="${ctx}/pricelimitapply/hsPriceLimitApply/listAuditData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('申请编号')}：</label>
					<div class="control-inline">
						<#form:input path="id" maxlength="20" class="form-control width-250"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('申请人')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.name" maxlength="20" class="form-control width-250"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('身份证号')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-250"/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="form-group">
					<label class="control-label">${text('联系电话')}：</label>
					<div class="control-inline">
						<#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-250"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('工作单位')}：</label>
					<div class="control-inline width-250" >
						<#form:treeselect id="officeId" title="${text('工作单位')}"
						path="officeCode" labelPath=""
						url="${ctx}/sys/office/treeData" allowClear="true"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('审批状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="flowStatus" dictType="pricelimit_apply_status" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn : false,
	columnModel: [
		{header:'${text("申请编号")}', name:'id', index:'a.id', width:60, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/pricelimitapply/hsPriceLimitApply/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑限价房购房申请")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请单内容")}', name:'applyTitle', index:'a.apply_title', width:130, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/pricelimitapply/hsPriceLimitApply/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑限价房购房申请")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请人")}', name:'mainApplyer.name', index:'a.main_applyer.name', width:60, align:"left"},
		{header:'${text("身份证号")}', name:'mainApplyer.idNum', index:'a.main_applyer.id_num', width:60, align:"left"},
		{header:'${text("联系电话")}', name:'mainApplyer.phone', index:'a.main_applyer.phone', width:60, align:"left"},
		{header:'${text("工作单位")}', name:'office.officeName', index:'a.office.office_name', width:100, align:"left"},
		{header:'${text("审批状态")}', name:'flowStatus', index:'a.flowStatus', width:60, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('pricelimit_apply_status')}", val, '${text("")}', true);
			}},
		{header:'${text("申请理由")}', name:'remarks', index:'a.remarks', width:130, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('pricelimitapply:hsPriceLimitApply:edit')){
				actions.push('<a href="${ctx}/pricelimitapply/hsPriceLimitApply/form?id='+row.id+'" class="hsBtnList" title="${text("编辑限价房购房申请")}">编辑</a>&nbsp;');
				/*if(hasPermi('pricelimitapply:hsPriceLimitApply:add')) {
					actions.push('<a href="${ctx}/pricelimitapply/hsPriceLimitApply/disable?id=' + row.id + '" class="btnList" title="${text("停用限价房购房申请")}" data-confirm="${text("确认要停用该限价房购房申请吗？")}">停用</a>&nbsp;');
				}*/
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=pricelimit_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/pricelimitapply/hsPriceLimitApply/exportAuditData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});

$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入选择房源")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val()
			};
			if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
				js.showMessage("${text('文件不正确，请选择后缀为“xls”或“xlsx”的文件。')}", null, 'warning');
				return false;
			}
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">
	<form id="inputForm" action="${ctx}/pricelimitapply/hsPriceLimitApply/importData" method="post" enctype="multipart/form-data"
		  class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
		<div class="row">
			<div class="col-xs-12 col-xs-offset-1">
				<input type="file" id="file" name="file" class="form-file"/>
				<div class="mt10 pt5" style="color:red">
					${text('提示：仅允许导入“xls”或“xlsx”格式文件！')}
				</div>
				<div class="mt10 pt5">
					<a href="${ctx}/pricelimitapply/hsPriceLimitApply/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
				</div>
			</div>
		</div>
	</form>
</script>
