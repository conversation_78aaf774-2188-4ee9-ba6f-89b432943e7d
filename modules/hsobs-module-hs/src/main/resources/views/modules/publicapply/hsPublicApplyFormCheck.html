<% layout('/layouts/default.html', {title: '公有住房购房申请管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsPublicApply.isNewRecord ? '新增公有住房购房申请' : '编辑公有住房购房申请')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsPublicApply}" action="${ctx}/publicapply/hsPublicApply/save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('基本信息')}</div>
				<#form:hidden path="id"/>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('公有住房配售方案')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="publicSaleApply.title" maxlength="200" readonly="true" class="form-control required"/>
							</td>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('房源信息')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<a href="${ctx}/house/hsQwPublicRentalHouse/form?id=${hsPublicApply.houseId}" class="hsBtnList" data-title="${text('查看公有住房房源房源信息表')}">
								${hsPublicApply.houseInfo}
								</a>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('是否有住房')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:select path="haveHouse" dictType="hs_have_house" readonly="true" class="form-control required"/>
							</td>
							<td>
							</td>
							<td>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('当前住址')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="houseAddress" maxlength="200" readonly="true" class="form-control required"/>
							</td>
							<td>
								<span class="required hide">*</span> ${text('住址类型')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:select path="houseType" dictType="hs_address_house_type" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('住宅建筑面积(m²)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="house.buildingArea" maxlength="200" readonly="true" class="form-control required"/>
							</td>
							<td>
								<span class="required hide">*</span> ${text('附属间建筑面积(m²)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="house.supportArea" maxlength="200" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('标准内部分单价(元/m²)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="unitPrice" maxlength="200" readonly="true" class="form-control digits required"/>
							</td>
							<td>
								<span class="required hide">*</span> ${text('超标准部分单价(元/m²)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="exceedUnitPrice" maxlength="200" readonly="true" class="form-control digits required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('附属间单价(元/m²)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="otherUnitPrice" maxlength="200" readonly="true" class="form-control digits required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('估算价格(元)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="estimatedPrice" maxlength="200" readonly="true" class="form-control digits required"/>
							</td>
							<td>
								<span class="required hide">*</span> ${text('评估报告ID')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="estinmateId" maxlength="200" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('核算价格(元)')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="price" maxlength="200" readonly="true" class="form-control digits required"/>
							</td>
							<td>
								<span class="required hide">*</span> ${text('合同编号')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:input path="contractId" maxlength="200" readonly="true" class="form-control required"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('购房状态')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td>
								<#form:select path="buyStatus" dictType="hs_buy_status" readonly="true" class="form-control required"/>
							</td>
							<td>
							</td>
							<td>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('申请理由')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:textarea path="remarks" rows="4" maxlength="500" readonly="true" class="form-control"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('审核意见')}：<i class="fa icon-question hide"></i></label>
							</td>
							<td colspan="3">
								<#form:textarea path="result" rows="4" maxlength="500" readonly="true" class="form-control"/>
							</td>
						</tr>
					</table>
				</div>
				<div class="form-unit">${text('公有住房购房申请人')}</div>
				<div class="form-unit-wrap table-form">
					<table id="hsPublicApplyerDataGrid"></table>
				</div>
				<div class="form-unit">${text('相关材料')}</div>
				<div class="hs-table-div" >
					<table class="table-form hs-table-form" >
						<tr>
							<td class="form-label hs-form-label" style="width: 400px;">
								<span class="required hide">*</span> ${text('所有申请人户口簿复印件')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_hr" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_hr_file"
									uploadType="image" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('所有申请人身份证复印件')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_id" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_id_file"
								uploadType="image" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('申请人未婚声明书/结婚证/离婚证和离婚协议或离婚判决书/配偶死亡证明复印件')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_marital" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_marital_file"
								uploadType="image" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('子女身份证或相关户籍证明材料复印件')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_child" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_child_file"
								uploadType="image" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('申请人离退休证复印件')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_old" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_old_file"
								uploadType="image" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('不动产登记信息查询结果')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_real_estate" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_real_estate_file"
								uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('其他相关材料')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_other" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_other_file"
								uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('申请确认表')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_apply" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_apply_file"
								uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('下发批文')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_approve" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_approve_file"
								uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('评估业务委托书')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_estimated" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_estimated_file"
								uploadType="all" class="" preview="true" readonly="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required ">*</span> ${text('房地产估价报告书')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_report" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_report_file"
								uploadType="all" class="" preview="true" readonly="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('备案材料')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_putonfile" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_putonfile_file"
								uploadType="all" class="" preview="true" readonly="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('核准材料')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_approval" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_approval_file"
								uploadType="all" class="" preview="true" readonly="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('合同材料')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFileContract" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_contract_file" uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
						<tr>
							<td class="form-label hs-form-label">
								<span class="required hide">*</span> ${text('交易确认书')}：</label>
							</td>
							<td colspan="3">
								<#form:fileupload id="uploadFile_Transaction" bizKey="${hsPublicApply.id}" bizType="hsPublicApply_Transaction_file"
								uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
							</td>
						</tr>
					</table>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if(hsPublicApply.type=="0"){ %>
							<#form:hidden path="status"/>
							<#hobpm:button bpmEntity="${hsPublicApply}" formKey="public_apply" completeText=""/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
//# // 初始化公有住房-购房申请人DataGrid对象
$('#hsPublicApplyerDataGrid').dataGrid({

	data: "#{toJson(hsPublicApply.hsPublicApplyerList)}",
	datatype: 'local', // 设置本地数据
	autoGridHeight: function(){return 'auto'}, // 设置自动高度
	//# // 设置数据表格列
	columnModel: [
		{header:'状态', name:'status', editable:true, hidden:true},
		{header:'主键', name:'id', editable:true, hidden:true},
		{header:'${text("申请人角色")}', name:'applyRole', width:100,formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_applyer_role')}", val, '${text("未知")}', true);
			}
		},
		{header:'${text("申请人姓名")}', name:'name', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
		{header:'${text("身份证号")}', name:'idNum', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'20', 'class':'form-control required idcard'}},
		{header:'${text("联系电话")}', name:'phone', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'32', 'class':'form-control required mobile'}},
		{header:'${text("工作单位")}', name:'organization', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
			{header:'${text("参加工作时间")}', name:'workDate', width:150, align:"left", formatter:'date', formatoptions:{srcformat:'Y-m',newformat:'Y-m'},
				editable:false, edittype:'text', editoptions:{'class':'form-control laydate required', 'readonly':'true',
					dataInit: function(element){
						laydate.render({elem:element, type:'datetime', format:'yyyy-MM'});
					}
				}
			},
			{header:'${text("本单位工作时间")}', name:'workDateL', width:150, align:"left", formatter:'date', formatoptions:{srcformat:'Y-m',newformat:'Y-m'},
				editable:false, edittype:'text', editoptions:{'class':'form-control laydate required', 'readonly':'true',
					dataInit: function(element){
						laydate.render({elem:element, type:'datetime', format:'yyyy-MM'});
					}
				}
			},
			{header:'${text("职级")}', name:'workLevel', width:60, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
			{header:'${text("工龄")}', name:'workingYear', width:60, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control digits required'}},
		{header:'${text("户籍")}', name:'census', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'64', 'class':'form-control required'}},
		{header:'${text("婚姻状况")}', name:'maritalStatus', width:100,align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_marital_status')}", val, '${text("未知")}', true);
			}
		},
		{header:'${text("年收入(元)")}', name:'annualIncome', width:150, editable:false, align:"left", edittype:'text', editoptions:{'class':'form-control required digits'}},
		{header:'${text("住房")}', name:'area', width:150, editable:false, align:"left", edittype:'text', editoptions:{'maxlength':'255', 'class':'form-control required'}},
	],
	//# // 编辑表格参数
	editGrid: false,				// 是否是编辑表格
	editGridInitRowNum: 1,		// 编辑表格的初始化新增行数
	editGridAddRowBtn: $('#hsPublicApplyerDataGridAddRowBtn'),	// 子表增行按钮
	editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
	editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL , applyRole: '0'},	// 新增行的时候初始化的数据

	//# // 编辑表格的提交数据参数
	editGridInputFormListName: 'hsPublicApplyerList', // 提交的数据列表名
	editGridInputFormListAttrs: 'status,id,userId,applyRole,applyId,name,organization,idNum,phone,census,maritalStatus,annualIncome,area,createBy,createDate,updateBy,updateDate,remarks,', // 提交数据列表的属性字段
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
	}
});
</script>
<script>
// 业务实现草稿按钮
$('#btnDraft').click(function(){
	$('#status').val(Global.STATUS_DRAFT);
});
// 流程按钮操作事件
BpmButton = window.BpmButton || {};
BpmButton.init = function(task){
	if (task.status != '2') {
		$('.taskComment').removeClass('hide');
	}
}
BpmButton.complete = function($this, task){
	$('#status').val(Global.STATUS_AUDIT);
	$("#houseIdName").val('');
};
</script>