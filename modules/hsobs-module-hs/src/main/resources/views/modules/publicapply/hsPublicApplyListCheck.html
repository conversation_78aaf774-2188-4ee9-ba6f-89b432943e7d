<% layout('/layouts/default.html', {title: '公有住房购房申请管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('公有住房购房申请管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="checkBtn"><i class="glyphicon glyphicon-export"></i> ${text(hsPublicApply.title)}</a>
				<a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> ${text('导出')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
			<#form:form id="searchForm" model="${hsPublicApply}" action="${ctx}/publicapply/hsPublicApply/listAuditCheckData?type=${hsPublicApply.type}" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('申请编号')}：</label>
						<div class="control-inline">
							<#form:input path="id" maxlength="20" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('申请人')}：</label>
						<div class="control-inline">
								<#form:input path="mainApplyer.name" maxlength="20" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('身份证号')}：</label>
						<div class="control-inline">
								<#form:input path="mainApplyer.idNum" maxlength="20" class="form-control width-120"/>
						</div>
					</div>
				</div>
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('联系电话')}：</label>
						<div class="control-inline">
								<#form:input path="mainApplyer.phone" maxlength="20" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('工作单位')}：</label>
						<div class="control-inline" >
							<#form:treeselect id="officeId" title="${text('工作单位')}"
							path="officeCode" labelPath=""
							url="${ctx}/sys/office/treeData" allowClear="true"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('审批状态')}：</label>
						<div class="control-inline">
							<#form:select path="flowStatus" dictType="public_apply_status" blankOption="true" class="form-control width-120"/>
						</div>
					</div>
				</div>
				<div class="search-form-row">
					<div class="form-group">
						<label class="control-label">${text('下发状态')}：</label>
						<div class="control-inline">
							<#form:select path="approvalStatus" dictType="hs_approval_status" blankOption="true" class="form-control width-120"/>
						</div>
					</div>
					<div class="form-group">
						<label class="control-label">${text('合同状态')}：</label>
						<div class="control-inline">
							<#form:select path="contractStatus" dictType="hs_contract_status" blankOption="true" class="form-control width-120"/>
						</div>
					</div>
				</div>
				<div class="search-button-row">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			</div>
			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	sortableColumn : false,
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("申请编号")}', name:'id', index:'a.id', width:160, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return '<a href="${ctx}/publicapply/hsPublicApply/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑公有住房购房申请")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请单内容")}', name:'applyTitle', index:'a.apply_title', width:180, align:"left", frozen:true, formatter: function(val, obj, row, act){
				return '<a href="${ctx}/publicapply/hsPublicApply/form?id='+row.id+'" class="hsBtnList" data-title="${text("编辑公有住房购房申请")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("申请人")}', name:'mainApplyer.name', index:'a.main_applyer.name', width:100, align:"left"},
		{header:'${text("身份证号")}', name:'mainApplyer.idNum', index:'a.main_applyer.id_num', width:100, align:"left"},
		{header:'${text("联系电话")}', name:'mainApplyer.phone', index:'a.main_applyer.phone', width:80, align:"left"},
		{header:'${text("工作单位")}', name:'office.officeName', index:'a.office.office_name', width:160, align:"left"},
		{header:'${text("审批状态")}', name:'flowStatus', index:'a.flowStatus', width:80, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('public_apply_status')}", val, '${text("")}', true);
			}},
		{header:'${text("批文状态")}', name:'approvalStatus', index:'a.approval_status', width:80, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_approval_status')}", val, '${text("未下发")}', true);
			}},
		{header:'${text("合同状态")}', name:'contractStatus', index:'a.contract_status', width:80, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_contract_status')}", val, '${text("未签订")}', true);
			}},
		{header:'${text("申请理由")}', name:'remarks', index:'a.remarks', width:160, align:"left"},
		{header:'${text("操作")}', name:'actions', width:120, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('publicapply:hsPublicApply:edit')){
				actions.push('<a href="${ctx}/publicapply/hsPublicApply/form?id='+row.id+'" class="hsBtnList" title="${text("编辑公有住房购房申请")}">编辑</a>&nbsp;');
				/*if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/publicapply/hsPublicApply/disable?id='+row.id+'" class="btnList" title="${text("停用公有住房购房申请")}" data-confirm="${text("确认要停用该公有住房购房申请吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/publicapply/hsPublicApply/enable?id='+row.id+'" class="btnList" title="${text("启用公有住房购房申请")}" data-confirm="${text("确认要启用该公有住房购房申请吗？")}">启用</a>&nbsp;');
				}*/
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=public_apply&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		requestAnimationFrame(function() {
			CommonTable.setupFixedColumns('dataGrid');
		});
	}
});
$('#btnExport').click(function(){
	js.confirm('${text("是否导出数据？")}', function(){
		js.ajaxSubmitForm($('#searchForm'), {
			url: '${ctx}/publicapply/hsPublicApply/exportAuditData',
			clearParams: 'pageNo,pageSize',
			downloadFile: true
		});
	})
});
</script>

<script>
	//# // 初始化DataGrid对象
	$('#checkBtn').click(function () {
		var pids = $("#dataGrid").dataGrid('getSelectRows');
		if (pids == ''){
			js.showMessage("请至少选择一个申请单！", "新增错误", "warning", 3000);
			return false;
		}
		fetch("${ctx}/publicapply/hsPublicApply/createOutput?type="+${hsPublicApply.type}+"&pids="+ pids, {
			method: "GET", // 或 POST、PUT 等
			headers: {
				"Content-Type": "application/json",
			},
		})
			.then(response => response.json())
			.then(response => {
				if(response.result == "true")
					js.showMessage(response.message);
				else
					js.showMessage(response.message, "导出错误", "error");
				page();
			})
	});
</script>