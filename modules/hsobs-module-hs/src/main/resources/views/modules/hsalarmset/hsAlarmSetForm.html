<% layout('/layouts/default.html', {title: '住房保障告警设置管理', libs: ['validate']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsAlarmSet.isNewRecord ? '住房保障告警设置' : '住房保障告警设置')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsAlarmSet}" action="${ctx}/hsalarmset//save" method="post" class="form-horizontal">
			<div class="box-body">
				<div class="form-unit">${text('公租房租金拖欠预警设置')}</div>
				<#form:hidden path="id"/>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('拖欠月份阈值(月数)')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="rentArrearsTime" class="form-control digits required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('拖欠金额阈值(元)')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="rentArrearsPrice" class="form-control digits required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('提醒周期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="rentArrearsCycle" dictType="hs_alarm_cycle_type" class="form-control required" />
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('资格核查异常预警设置')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('提醒周期')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:select path="anomalyCycle" dictType="hs_alarm_cycle_type" class="form-control required" />
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('维修补助额度不足预警设置')}</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('维修资金总额(元)')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="limitUndrawn" class="form-control digits required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('剩余额度(元)')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="remainingUndrawn" class="form-control digits required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<div class="form-group">
							<label class="control-label col-sm-4" title="">
								<span class="required hide">*</span> ${text('提醒周期(天)')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-8">
								<#form:input path="reminderCycle" class="form-control digits required"/>
							</div>
						</div>
					</div>
				</div>
				<div class="form-unit">${text('')}</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="form-group">
							<label class="control-label col-sm-2" title="">
								<span class="required hide">*</span> ${text('备注')}：<i class="fa icon-question hide"></i></label>
							<div class="col-sm-10">
								<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control"/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('hsalarmset::edit')){ %>
							<button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			/*if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}*/
		}, "json");
    }
});
</script>