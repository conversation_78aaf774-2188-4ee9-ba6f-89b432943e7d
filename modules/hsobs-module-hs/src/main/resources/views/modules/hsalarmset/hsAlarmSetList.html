<% layout('/layouts/default.html', {title: '告警设置管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-header">
			<div class="box-title">
				<i class="fa icon-notebook"></i> ${text('告警设置管理')}
			</div>
			<div class="box-tools pull-right">
				<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
				<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
			</div>
		</div>
		<div class="box-body">
			<#form:form id="searchForm" model="${hsAlarmSet}" action="${ctx}/hsalarmset//listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline ">
						<#form:select path="status" dictType="sys_search_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('剩余额度')}：</label>
					<div class="control-inline">
						<#form:input path="remainingUndrawn" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('提醒周期')}：</label>
					<div class="control-inline">
						<#form:input path="reminderCycle" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<table id="dataGrid"></table>
			<div id="dataGridPage"></div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("状态")}', name:'status', index:'a.status', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			//return '<a href="${ctx}/hsalarmset//form?id='+row.id+'" class="hsBtnList" data-title="${text("告警设置")}">'+(val||row.id)+'</a>';
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_search_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("剩余额度(元)")}', name:'remainingUndrawn', index:'a.remaining_undrawn', width:150, align:"center"},
		{header:'${text("提醒周期(天)")}', name:'reminderCycle', index:'a.reminder_cycle', width:150, align:"center"},
		{header:'${text("上次提醒时间")}', name:'reminderDate', index:'a.reminder_date', width:150, align:"center"},
		{header:'${text("备注")}', name:'remarks', index:'a.remarks', width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('hsalarmset::edit')){
				actions.push('<a href="${ctx}/hsalarmset//form?id='+row.id+'" class="hsBtnList" title="${text("告警设置")}">编辑</a>&nbsp;');
				if (row.status == Global.STATUS_NORMAL){
					actions.push('<a href="${ctx}/hsalarmset//disable?id='+row.id+'" class="btnList" title="${text("停用告警设置")}" data-confirm="${text("确认要停用该告警设置吗？")}">停用</a>&nbsp;');
				} else if (row.status == Global.STATUS_DISABLE){
					actions.push('<a href="${ctx}/hsalarmset//enable?id='+row.id+'" class="btnList" title="${text("启用告警设置")}" data-confirm="${text("确认要启用该告警设置吗？")}">启用</a>&nbsp;');
				}
			//# }
			return actions.join('');
		}}
	],
	//# // 加载成功后执行事件
	ajaxSuccess: function(data){
		
	}
});
</script>