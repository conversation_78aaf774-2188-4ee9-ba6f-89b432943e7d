<% layout('/layouts/default.html', {title: '租赁资格轮候公示复查表管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <ul class="nav nav-tabs">

            <% if ( publicType!'0' == "0"){ %>
            <li  class="active" >
                <% } else {%>
            <li>
                <% } %>
                <a href="${ctx}/applypublic/hsQwApplyPublic/list?publicType=0"><i class="fa icon-energy"></i> ${text('初审公示')}</a></li>
            <% if ( publicType =="1"){ %>
            <li  class="active" >
                <% } else {%>
            <li>
                <% } %>
                <a href="${ctx}/applypublic/hsQwApplyPublic/list?publicType=1"><i class="fa icon-book-open"></i> ${text('复查公示')}</a></li>
        </ul>
        <div class="box-body">
            <div class="search-form-container">
                <#form:form id="searchForm" model="${hsQwApplyPublic}" action="${ctx}/applypublic/hsQwApplyPublic/listData" method="post" class="form-inline"
                data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
                    <!-- 第一行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('公示时间')}：</label>
                            <div class="control-inline">
                                <#form:input path="publicDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                                dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('公示状态')}：</label>
                            <div class="control-inline width-120">
                                <#form:select path="status" dictType="hs_apply_public_status" blankOption="true" class="form-control isQuick"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('公示内容')}：</label>
                            <div class="control-inline">
                                <#form:input path="remarks" maxlength="500" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第二行：1个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('公示名称')}：</label>
                            <div class="control-inline">
                                <#form:input path="publicName" maxlength="200" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>

                    <#form:hidden path="publicType" />
                    
                    <!-- 按钮行 -->
                    <div class="search-button-row">
                        <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                        <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
                    </div>
                </#form:form>
            </div>
            <div class="fixed-table-container">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
        </div>
    </div>
</div>
<% } %>
<script>
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false,
    autowidth: false,
    scroll: true,
    scrollOffset: 18,
    width: '100%',
    height: 'auto',
    columnModel: [
        {header:'${text("公示单号")}', name:'id', sortable:false, width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
            if(row.publicType == '0') {
                return '<a href="${ctx}/applypublic/hsQwApplyPublic/formUpload?id='+row.id+'" class="hsBtnList" data-title="${text("资格轮候公示")}">'+(val||row.id)+'</a>';
            } else {
                return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("资格轮候复查公示")}">' + (val || row.id) + '</a>';
            }
        }},
        {header:'${text("公示名称")}', name:'publicName', sortable:false, width:150, align:"left"},
        {header:'${text("公示状态")}', name:'status', sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('hs_apply_public_status')}", val, '${text("未知")}', true);
        }},
        {header:'${text("公示时间")}', name:'publicDate', sortable:false, width:150, align:"left"},
        {header:'${text("公示内容")}', name:'remarks', sortable:false, width:150, align:"left"},
        {header:'${text("操作")}', name:'actions', width:150, align:"left", frozen:'right', formatter: function(val, obj, row, act){
            var actions = [];
            if (row.status == Global.STATUS_NORMAL){
                if(row.publicType == '0') {
                    actions.push('<a href="${ctx}/applypublic/hsQwApplyPublic/formUpload?id='+row.id+'" class="hsBtnList" data-title="${text("资格轮候公示")}">编辑</a>&nbsp;');
                } else {
                    actions.push('<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("资格轮候复查公示")}">编辑</a> &nbsp;');
                }
            }
            return actions.join('');
        }}
    ],
    loadComplete: function() {
        requestAnimationFrame(function() {
            CommonTable.setupFixedColumns('dataGrid');
        });
        js.closeLoading(0, true);
    }
}).on('click', '.bpmButton', function(){
    var $this = $(this).addClass('hsBtnList');
    js.ajaxSubmit($this.attr('href'), function(data){
        if (data.result == Global.TRUE){
            // 检查 pcUrl 是否可访问
            $.ajax({
                url: data.pcUrl,
                type: 'get',
                success: function(response) {
                    // 如果请求成功，使用 js.addTabPage 打开新标签页
                    js.addTabPage($this, $this.attr('title'), data.pcUrl);
                },
                error: function(xhr, status, error) {
                    // 如果请求失败，显示错误信息
                    var errorMsg = '${text("加载失败")}';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    js.showErrorMessage(errorMsg);
                }
            });
        } else {
            js.showErrorMessage(data.message);
        }
    });
    return false;
});
</script>