<% layout('/modules/applypublic/hsQwApplyPublicRentForm.html', {isRead: true , canAudit: true, title: '租赁资格轮候公示复查表管理', libs: ['validate','fileupload','dataGrid']}){ %>

<div class="hs-table-div">
    <table class="table-form hs-table-form">
        <tr>
            <td class="form-label hs-form-label">
                <span class="required hide">*</span> ${text('公示通知文件')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <a href="${ctx}/applypublic/hsQwApplyPublic/exporRecheckNames?id=${hsQwApplyPublic.id}" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 下载</a>
            </td>
            <td class="form-label hs-form-label">
                <span class="required hide">*</span> ${text('公示时间')}：<i class="fa icon-question hide"></i>
            </td>
            <td>
                <#form:input path="publicDate" value="${hsQwApplyPublic.publicDate}" maxlength="20" class="form-control laydate"
                dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" readonly="${isRead!false}"/>
            </td>
        </tr>
        <tr>
            <td class="form-label hs-form-label">
                <span class="required hide">*</span> ${text('公示材料')}：
            </td>
            <td colspan="3">
                <#form:fileupload id="uploadFile" bizKey="${hsQwApplyPublic.id}" bizType="hsQwApplyPublic_file" uploadType="all" class="" readonly="${isRead!false}"  preview="true" dataMap="true"/>
            </td>
        </tr>
    </table>
</div>

${layoutContent!}
<% } %>
