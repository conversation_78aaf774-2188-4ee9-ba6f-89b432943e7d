<% layout('/layouts/default.html', {title: '配租管理', libs: ['dataGrid']}){ %>
<div class="main-content">
    <div class="nav-tabs-custom nav-main">
        <ul class="nav nav-tabs">
            <% if ( first ){ %>
            <li  class="active" >
                <% } else {%>
            <li>
                <% } %>
                <a href="${ctx}/applypublic/hsQwApplyPublic/listRent"><i class="fa icon-energy"></i> ${text('审批待办')}</a></li>
            <% if ( !first ){ %>
            <li  class="active" >
                <% } else {%>
            <li>
                <% } %>
                <a href="${ctx}/applypublic/hsQwApplyPublic/listRentDone"><i class="fa icon-book-open"></i> ${text('已办信息')}</a></li>
        </ul>
        <div class="box-body">
            <div class="search-form-container">
                <#form:form id="searchForm" model="${hsQwApplyPublic}" action="${ctx}${listDataUrl}" method="post" class="form-inline"
                data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
                    <!-- 第一行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('公示时间')}：</label>
                            <div class="control-inline">
                                <#form:input path="publicDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                                dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('公示状态')}：</label>
                            <div class="control-inline ">
                                <#form:select path="status" dictType="hs_apply_public_status" blankOption="true" class="form-control isQuick"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('备注信息')}：</label>
                            <div class="control-inline">
                                <#form:input path="remarks" maxlength="500" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>
                    <!-- 第二行：1个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('公示名称')}：</label>
                            <div class="control-inline">
                                <#form:input path="publicName" maxlength="200" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>
                    <#form:hidden path="publicType" />
                    <!-- 按钮行 -->
                    <div class="search-button-row">
                        <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                        <button type="button" id="oneStepBtn" class="btn btn-default" onclick="return false;"><i class="fa fa-plus"></i> ${text('一键审批')}</button>
                        <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
                    </div>
                </#form:form>
            </div>
            <div class="fixed-table-container">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
        </div>
    </div>
</div>
</div>
<% } %>
<script>
$('#dataGrid').dataGrid({
    searchForm: $('#searchForm'),
    showCheckbox: true,
    shrinkToFit: false, // 禁用自动调整列宽
    autowidth: false,  // 禁用自动宽度
    //scroll: true,      // 启用滚动条
    scrollOffset: 18,
    width: '100%',     // 表格宽度
    height: 'auto',    // 表格高度自适应
    columnModel: [
        {header:'${text("配租公示单号")}', name:'id', width:150, align:"left", frozen:true, formatter: function(val, obj, row, act) {
            return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '&status=1" class="bpmButton" title="${text("编辑租赁资格轮候公示复查表")}">' + (val || row.id) + '</a>';
        }},
        {header: '${text("配租公示名称")}', name: 'publicName', width: 150, align: "left"},
        {header: '${text("配租公示内容")}', name:'applyTitle', width:150, align:"left"},
        {header:'${text("流程环节")}', name:'processName', width:150, align:"left"},
        {header:'${text("处理时间")}', name:'taskDueDate', width:150, align:"left"},
        {header:'${text("备注信息")}', name:'remarks', width:150, align:"left"},
        {header:'${text("发布状态")}', name:'publicStatus', width:150, align:"left", formatter: function(val, obj, row, act){
            return val === '1' ? '${text("已发布")}' : '${text("未发布")}';
        }},
        {header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
            var actions = [];
            actions.push('<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId + '" class="bpmButton" title="${text("编辑租赁资格轮候公示复查表")}">编辑</a>&nbsp;');
            actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey=rent_apply_public&bizKey='+row.id+'" class="btnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');

            // 检查 processName 的值
            if (row.processName === '配租名单网上公示' || row.processName === '草拟配租方案' || row.processName === '处室领导配租方案审核' || row.processName === '局级领导配租方案审核') {
                if (row.publicStatus === '0' ){
                    actions.push('<a href="${ctx}/applypublic/hsQwApplyPublic/publicStatus?id='+row.id+'" class="btnList" title="${text("一键发布")}" data-confirm="${text("确认发布资格轮候名单吗？")}">发布</a>&nbsp;');
                }
                actions.push('<a href="javascript:void(0);" class="download-btn" onclick="showDownloadPanel(\''+row.id+'\',\''+row.processName+'\')">下载</a>');
            }

            return actions.join('');
        }}
    ],
    ajaxSuccess: function() {
        requestAnimationFrame(function() {
            CommonTable.setupFixedColumns('dataGrid');
        });
    }
}).on('click', '.bpmButton', function(){
    var $this = $(this).addClass('hsBtnList');
    js.ajaxSubmit($this.attr('href'), function(data){
        if (data.result == Global.TRUE){
            // 检查 pcUrl 是否可访问
            $.ajax({
                url: data.pcUrl,
                type: 'get',
                success: function(response) {
                    // 如果请求成功，使用 js.addTabPage 打开新标签页
                    js.addTabPage($this, $this.attr('title'), data.pcUrl);
                },
                error: function(xhr, status, error) {
                    // 如果请求失败，显示错误信息
                    var errorMsg = '${text("加载失败")}';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    js.showErrorMessage(errorMsg);
                }
            });
        } else {
            js.showErrorMessage(data.message);
        }
    });
    return false;
});
$('#oneStepBtn').click(function(){
    var hids = $('#dataGrid').dataGrid('getSelectRows');
    if (hids != null && hids.length > 0){
        js.confirm('${text("确认要对选中的申请单进行一键审批吗？")}', function(){
            js.ajaxSubmit('${ctx}/applypublic/hsQwApplyPublic/saveRentBatch', {
                pids: hids.join(',')
            }, function(data){
                js.showMessage(data.message);
                page();
            });
        });
    }else{
        js.showMessage('${text("请在列表选中要进行一键发布的申请单！")}');
    }
    return false;
});

function showDownloadPanel(id, processName) {
    // 动态拼接 HTML 内容，将 id 直接插入到 onclick 事件中
    let content = `
            <div style="padding: 20px; display: flex; justify-content: space-between;">
                <a href="${ctx}/applypublic/hsQwApplyPublic/exporRecheckNames?id=`+id+`"><i class="fa icon-people"></i> ${text('配租公示人员名单')}</a>
            `;
    // 判断是否为处室领导配租方案审核或局级领导配租方案审核
    if (processName === '处室领导配租方案审核' || processName === '局级领导配租方案审核') {
        content += `<a href="${ctx}/applypublic/hsQwApplyPublic/exporRecheckHouses?id=`+id+`"><i class="fa icon-home"></i> ${text('配租公示房源名单')}</a>`;
    }
    content += `
                </div>
            `;

    js.layer.open({
        type: 1,
        area: ['400px'],
        // 假设服务器端已经将国际化表达式替换为实际文本
        title: '下载配租公示材料',
        content: content,
        resize: false,
        scrollbar: true
    });
}
    function downloadFile(id, type) {
        let url = '';
        if (type === 'person') {
            url = '${ctx}/applypublic/hsQwApplyPublic/downloadPersonList?id=' + id;
        } else if (type === 'house') {
            url = '${ctx}/applypublic/hsQwApplyPublic/downloadHouseList?id=' + id;
        }
        window.location.href = url;
    }
</script>
