<% layout('/layouts/default.html', {title: '租赁资格轮候公示复查表管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
    <% if(!hsQwApplyPublic.isNewRecord){ %>
    <div class="box box-main" style="margin-bottom: 10px;">
        <div class="box-header with-border">
            <div class="hide" style="display: flex;justify-content: space-between;">
                <div class="box-title">
                </div>
                <div class="box-tools pull-right ">
                    <button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
                </div>
            </div>
            <div class="row">
                <#common:viewport entity="${hsQwApplyPublic}" title="租赁资格轮候公示复查申请" />
            </div>
        </div>
        <div class="box-body">
            <#common:steps entity="${hsQwApplyPublic}" formKey="rent_apply_public" />
        </div>
    </div>
    <% } %>
    <div class="box box-main">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa icon-note"></i> ${text(hsQwApplyPublic.isNewRecord ? '新增租赁资格轮候公示表' : '编辑租赁资格轮候公示表')}
            </div>
            <div class="box-tools pull-right hide">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
        </div>
        <#form:form id="inputForm" model="${hsQwApplyPublic}" action="${ctx}/applypublic/hsQwApplyPublic/saveRent" method="post" class="form-horizontal">
        <div class="box-body hs-box-body-bpm">
            <div class="form-unit">${text('租赁资格轮候公示表')}</div>
            <div class="form-unit-wrap table-form">
                <table id="hsQwApplyPublicDetailDataGrid" class="table-form hs-table-form"></table>
            </div>
            <div class="form-unit">${text('公示信息')}</div>
            <#form:hidden path="id"/>
            <div class="hs-table-div">
                <table class="table-form hs-table-form">
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('公示名称')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="publicName" maxlength="200" readonly="${isRead}" class="form-control required"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:textarea path="remarks" rows="4" maxlength="500" readonly="${isRead}" class="form-control"/>
                        </td>
                    </tr>
                </table>
            </div>
            ${layoutContent!}
            <% if (canAudit!false) {%>
            <div class="form-unit">${text('审核操作')}</div>
            <div class="hs-table-div">
                    <table class="table-form hs-table-form">
                        <tr>
                            <td class="form-label hs-form-label">审批意见：</td>
                            <td>
                                <#bpm:comment bpmEntity="${hsQwApplyPublic}" showCommWords="true" />
                            </td>
                        </tr>
                    </table>
                </div>
            <% } %>
        </div>
        <#form:hidden path="publicType"/>
        <#form:hidden id="pids" path="pids"/>
        <#form:hidden id="submitType" path="submitType"/>
        <#form:hidden id="status" path="status"/>
        <div class="box-footer">
            <div class="row">
                <div class="col-sm-offset-2 col-sm-10">
                    <% if (canAudit!false) {%>
                        <#bpm:button bpmEntity="${hsQwApplyPublic}" formKey="rent_apply_public" completeText="提 交"/>
                        <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
                    <% } else {%>
                        <a href="${ctx}/bpm/bpmRuntime/trace?formKey=rent_apply_public&bizKey=${hsQwApplyPublic.id}" class="hsBtnList" title="${text("流程追踪")}" data-layer="true">流程追踪流程图</a>
                    <% } %>
                </div>
            </div>
        </div>
    </#form:form>
</div>
</div>
<% } %>
<script>
    //# // 初始化租赁资格轮候公示复查详情表DataGrid对象
    $('#hsQwApplyPublicDetailDataGrid').dataGrid({

        data: "#{toJson(hsQwApplyPublic.hsQwApplyPublicDetailList)}",
        datatype: 'local', // 设置本地数据
        autoGridHeight: function(){return 'auto'}, // 设置自动高度

        //# // 设置数据表格列
        columnModel: [
            {header:'${text("申请单编号")}', name:'applyId',  sortable:false, width:80, align:"center", frozen:true, formatter: function(val, obj, row, act){
                        return '<a href="${ctx}/apply/hsQwApply/formRead?id='+row.hsQwApply.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候申请")}">'+(val||row.id)+'</a>';
                }},
            {header:'${text("申请单内容")}', name:'hsQwApply.applyTitle',  sortable:false, width:130, align:"center", frozen:true, formatter: function(val, obj, row, act){
                        return '<a href="${ctx}/apply/hsQwApply/formRead?id='+row.hsQwApply.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候申请")}">'+(val||row.id)+'</a>';
                }},
            {header:'${text("主申请人身份证")}', name:'hsQwApply.mainApplyer.idNum',  sortable:false, width:100, align:"center"},
            {header:'${text("主申请人手机号")}', name:'hsQwApply.mainApplyer.phone',  sortable:false, width:60, align:"center"},
            {header:'${text("主申请人单位")}', name:'hsQwApply.mainApplyer.organization',  sortable:false, width:60, align:"center"},
            {header:'${text("家庭成员姓名")}', name:'familyNames',  sortable:false, width:60, align:"center"},
            {header:'${text("轮候评分")}', name:'hsQwApply.applyScore',  sortable:false, width:130, align:"center"},
            {header:'${text("备注信息")}', name:'hsQwApply.remarks',  sortable:false, width:130, align:"center"},
        ],

        //# // 编辑表格参数
        editGrid: true,				// 是否是编辑表格
        editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
        editGridAddRowBtn: $('#hsQwApplyPublicDetailDataGridAddRowBtn'),	// 子表增行按钮
        editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
        editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

        //# // 编辑表格的提交数据参数
        editGridInputFormListName: 'hsQwApplyPublicDetailList', // 提交的数据列表名
        editGridInputFormListAttrs: 'status,id,publicId,applyId,processId,', // 提交数据列表的属性字段

        //# // 加载成功后执行事件
        ajaxSuccess: function(data){

        }
    });
</script>
<script>
    $('#inputForm').validate({
        submitHandler: function(form){
            js.ajaxSubmitForm($(form), function(data){
                js.showMessage(data.message);
                if(data.result == Global.TRUE){
                    js.closeCurrentTabPage(function(contentWindow){
                        contentWindow.page();
                    });
                }
            }, "json");
        }
    });

</script>