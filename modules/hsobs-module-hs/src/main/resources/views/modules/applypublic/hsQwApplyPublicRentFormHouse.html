<% layout('/modules/applypublic/hsQwApplyPublicRentFormUpload.html', {isRead: true , canAudit: true, title: '租赁资格轮候公示复查表管理', libs: ['validate','fileupload','dataGrid']}){ %>
    <div class="form-unit">${text('租赁资格轮候公示房源名单')}</div>
    <div class="hs-table-div">
        <table class="table-form hs-table-form">
            <tr>
                <td class="form-label hs-form-label">
                    <span class="required ">*</span> ${text('选择房源')}：<i class="fa icon-question hide"></i>
                </td>
                <td>
                    <% if (isRead!'false' == 'false') {%>
                        <#form:listselect id="publicHouse1" title="房源选择选择"
                        path="publicHouseIds" labelPath="publicHouseNames" class="required"
                        url="${ctx}/house/hsQwPublicRentalHouse/houseSelectByType?dataType=6" allowClear="true"
                        checkbox="true" itemCode="id" itemName="simpleInfo" readonly="${isRead!false}"/>
                    <% } else {%>
                            ${hsQwApplyPublic.publicHouseNames}
                    <% } %>
                </td>
                <td>
                    <% if (isRead!'false' == 'false') {%>
                        <a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入</a>
                    <% } %>
                </td>
            </tr>
        </table>
    </div>
${layoutContent!}
<% } %>
<script>
    $('#btnImport').click(function(){
        js.layer.open({
            type: 1,
            area: ['400px'],
            title: '${text("导入空闲房源表")}',
            resize: false,
            scrollbar: true,
            content: js.template('importTpl'),
            btn: ['<i class="fa fa-check"></i> ${text("导入")}',
                '<i class="fa fa-remove"></i> ${text("关闭")}'],
            btn1: function(index, layero){
                var form = {
                    inputForm: layero.find('#inputForm'),
                    file: layero.find('#file').val(),
                    type: "0"
                };
                if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
                    js.showMessage("${text('文件不正确，请选择后缀为xls或xlsx的文件。')}", null, 'warning');
                    return false;
                }
                js.ajaxSubmitForm(form.inputForm, function(data){
                    js.showMessage(data.message);
                    if(data.result == Global.TRUE){
                        js.layer.closeAll();
                    }
                    page();
                }, "json");
                return true;
            }
        });
    });
</script>

<!-- ===== 添加导入弹窗的 HTML 模板 ===== -->
<script type="text/template" id="importTpl">
    <form id="inputForm" action="${ctx}/applypublic/hsQwApplyPublic/importDataIdle" method="post" enctype="multipart/form-data" class="form-horizontal mt10"
            style="max-height:400px;overflow:auto;">
        <div class="form-group">
            <label class="col-sm-3 control-label">${text('选择文件')}：</label>
            <div class="col-sm-8">
                <input type="file" id="file" name="file" class="form-control required" accept=".xls,.xlsx">
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-offset-3 col-sm-8">
                <a href="${ctx}/house/hsQwPublicRentalHouse/importTemplateIdle" class="btn btn-default btn-sm">${text('下载模板')}</a>
                <p class="help-block">${text('仅允许导入“xls”或“xlsx”格式文件！')}</p>
            </div>
        </div>
    </form>
</script>
<!-- ===== 模板结束 ===== -->