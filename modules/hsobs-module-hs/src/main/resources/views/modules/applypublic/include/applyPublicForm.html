<% layout('/layouts/default.html', {title: '租赁资格轮候公示复查表管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa icon-note"></i> ${text(hsQwApplyPublic.isNewRecord ? '新增租赁资格轮候公示表' : '编辑租赁资格轮候公示表')}
            </div>
            <div class="box-tools pull-right hide">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
        </div>
        <#form:form id="inputForm" model="${hsQwApplyPublic}" action="${ctx}/applypublic/hsQwApplyPublic/save" method="post" class="form-horizontal">
        <div class="box-body hs-box-body-bpm">
            <div class="form-unit">${text('租赁资格轮候公示表')}</div>
            <div class="form-unit-wrap table-form">
                <table id="hsQwApplyPublicDetailDataGrid" class="table-form hs-table-form"></table>
            </div>
            <div class="form-unit">${text('公示信息')}</div>
            <#form:hidden path="id"/>
            <div class="hs-table-div">
                <table class="table-form hs-table-form">
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required ">*</span> ${text('公示名称')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="publicName" maxlength="200" readonly="${readonly}" class="form-control required"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('公示内容')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:textarea path="remarks" rows="4" maxlength="500" readonly="${readonly}" class="form-control"/>
                        </td>
                    </tr>
                    <% if ( hsQwApplyPublic.status == '0' || upload){ %>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('公示时间')}：<i class="fa icon-question hide"></i>
                        </td>
                        <td>
                            <#form:input path="publicDate" readonly="true" maxlength="20" class="form-control laydate"
                            dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="form-label hs-form-label">
                            <span class="required hide">*</span> ${text('公示材料')}：
                        </td>
                        <td>
                            <% if (hsQwApplyPublic.status == '0'){ %>
                            <#form:fileupload id="uploadFile" bizKey="${hsQwApplyPublic.id}" bizType="hsQwApplyPublic_file" uploadType="all" class="" readonly="false" preview="true" dataMap="true"/>
                            <% } else {%>
                            <#form:fileupload id="uploadFile" bizKey="${hsQwApplyPublic.id}" bizType="hsQwApplyPublic_file" uploadType="all" class="" readonly="true" preview="true" dataMap="true"/>
                            <% } %>
                        </td>
                    </tr>
                    <% } %>
                </table>
            </div>
        </div>
        <#form:hidden path="publicType"/>
        <#form:hidden id="pids" path="pids"/>
        <#form:hidden id="submitType" path="submitType"/>
        <#form:hidden id="status" path="status"/>
        <div class="box-footer">
            <div class="row">
                <div class="col-sm-offset-2 col-sm-10">
                <% if ( hsQwApplyPublic.status == null ){ %>
                    <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('保 存')}</button>&nbsp;
                <% } else if ( hsQwApplyPublic.status=='0' ){%>
                    <button type="submit" class="btn btn-sm btn-primary" id="btnSubmi1"><i class="fa fa-check"></i> ${text('公 示')}</button>&nbsp;
                <% } else if ( hsQwApplyPublic.status=='3'){%>
                    <% if ( hsQwApplyPublic.isHasAudit ){ %>
                        <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit2"><i class="fa fa-check"></i> ${text('批量提交')}</button>&nbsp;
                        <button type="submit" class="btn btn-sm btn-danger" id="btnSubmit3"><i class="fa fa-reply-all"></i> ${text('批量拒绝')}</button>&nbsp;
                    <% } %>
                <% } %>
                    <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
                </div>
            </div>
        </div>
    </#form:form>
</div>
</div>
<% } %>
<script>
    //# // 初始化租赁资格轮候公示复查详情表DataGrid对象
    $('#hsQwApplyPublicDetailDataGrid').dataGrid({

        data: "#{toJson(hsQwApplyPublic.hsQwApplyPublicDetailList)}",
        datatype: 'local', // 设置本地数据
        autoGridHeight: function(){return 'auto'}, // 设置自动高度

        //# // 设置数据表格列
        columnModel: [
            {header:'${text("申请单编号")}', name:'applyId',  sortable:false, width:80, align:"center", frozen:true, formatter: function(val, obj, row, act){
                    <% if (hsQwApplyPublic.status == '3') {%>
                        return '<a href="${ctx}/apply/hsQwApply/form?id='+row.hsQwApply.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候申请")}">'+(val||row.id)+'</a>';
                        <% } else {%>
                        return '<a href="${ctx}/apply/hsQwApply/formRead?id='+row.hsQwApply.id+'" class="hsBtnList" data-title="${text("编辑租赁资格轮候申请")}">'+(val||row.id)+'</a>';
                        <% } %>
                }},
            {header:'${text("姓名")}', name:'hsQwApply.mainApplyer.name',  sortable:false, width:100, align:"center"},
            {header:'${text("工作单位")}', name:'hsQwApply.mainApplyer.organization',  sortable:false, width:100, align:"center"},
            {header:'${text("家庭成员姓名")}', name:'familyNames',  sortable:false, width:60, align:"center"},
            {header:'${text("家庭成员工作单位")}', name:'familyOrganization',  sortable:false, width:60, align:"center"},
            {header:'${text("轮候起算日")}', name:'hsQwApply.applyTime',  sortable:false, width:60, align:"center"},
            {header:'${text("轮候评分")}', name:'hsQwApply.applyScore',  sortable:false, width:130, align:"center"},
            <% if ( delop =="true"){ %>
            {header:'${text("操作")}', name:'actions', width:80, align:'center', formatter: function(val, obj, row, act){
                    var actions = [];
                    if (val == 'new'){
                        actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyPublicDetailDataGrid\').dataGrid(\'delRowData\',\''+obj.rowId+'\');let idArray = $(\'#pids\').val().split(\',\');idArray = idArray.filter(id => id !== '+obj.applyId+');$(\'#pids\').val(idArray.join(\',\');});return false;">删除</a>&nbsp;');
                    }else{
                        actions.push('<a href="#" onclick="js.confirm(\'${text("你确认要删除这条数据吗？")}\', function(){$(\'#hsQwApplyPublicDetailDataGrid\').dataGrid(\'setRowData\',\''+obj.rowId+'\',null,{display:\'none\'});$(\'#'+obj.rowId+'_status\').val(\''+Global.STATUS_DELETE+'\');let idArray = $(\'#pids\').val().split(\',\');idArray = idArray.filter(id => id !== \''+row.applyId+'\');$(\'#pids\').val(idArray.join(\',\'));});return false;">删除</a>&nbsp;');
                    }
                    return actions.join('');
                }, editoptions: {defaultValue: 'new'}}
            <% } %>
        ],

        //# // 编辑表格参数
        editGrid: true,				// 是否是编辑表格
        editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
        editGridAddRowBtn: $('#hsQwApplyPublicDetailDataGridAddRowBtn'),	// 子表增行按钮
        editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
        editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

        //# // 编辑表格的提交数据参数
        editGridInputFormListName: 'hsQwApplyPublicDetailList', // 提交的数据列表名
        editGridInputFormListAttrs: 'status,id,publicId,applyId,processId,', // 提交数据列表的属性字段

        //# // 加载成功后执行事件
        ajaxSuccess: function(data){

        }
    });
</script>
<script>
    $('#inputForm').validate({
        submitHandler: function(form){
            js.ajaxSubmitForm($(form), function(data){
                js.showMessage(data.message);
                if(data.result == Global.TRUE){
                    js.closeCurrentTabPage(function(contentWindow){
                        contentWindow.page();
                    });
                }
            }, "json");
        }
    });
    // 业务实现公示按钮
    $('#btnSubmit1').click(function(){
        console.log("erewrweqrewr")
        $('#status').val('1');
    });
    // 业务实现草稿按钮
    $('#btnSubmit2').click(function(){
        $('#submitType').val('0');
    });
    // 业务实现草稿按钮
    $('#btnSubmit3').click(function(){
        $('#submitType').val('1');
    });
</script>