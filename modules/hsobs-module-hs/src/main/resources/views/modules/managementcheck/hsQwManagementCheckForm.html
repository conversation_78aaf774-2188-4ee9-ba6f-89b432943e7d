<% layout('/layouts/default.html', {title: '租赁资格轮候物业核验管理', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
	<% if(!hsQwManagementCheck.isNewRecord){ %>
	<div class="box box-main" style="margin-bottom: 10px;">
		<div class="box-header with-border">
			<div class="hide" style="display: flex;justify-content: space-between;">
				<div class="box-title">
				</div>
				<div class="box-tools pull-right ">
					<button type="button" style="width: 120px;" class="btn btn-sm btn-primary" data-widget="collapse"><i class="glyphicon glyphicon-export"></i> 导出申请单 </button>
				</div>
			</div>
			<div class="row">
				<#common:viewport entity="${hsQwManagementCheck}" title="租赁资格物业核验申请" />
			</div>
		</div>
		<div class="box-body">
			<#common:steps entity="${hsQwManagementCheck}" formKey="${hsQwManagementCheck.formKey}" />
		</div>
	</div>
	<% } %>
	<div class="box box-main">
		<div class="box-header with-border">
			<div class="box-title">
				<i class="fa icon-note"></i> ${text(hsQwManagementCheck.isNewRecord ? '新增租赁资格轮候物业核验' : '编辑租赁资格轮候物业核验')}
			</div>
			<div class="box-tools pull-right hide">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
			</div>
		</div>
		<#form:form id="inputForm" model="${hsQwManagementCheck}" action="${ctx}/managementcheck/hsQwManagementCheck/save" method="post" class="form-horizontal">
		<div class="box-body hs-box-body-bpm">
			<div class="form-unit">${text('基本信息')}</div>
			<#form:hidden path="id"/>
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('配租信息查询')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:listselect id="applyId" title="已配租申请单"
							path="applyIdL" labelPath="hsQwApply.applyTitle" class="required"
							url="${ctx}/apply/hsQwApply/applyedSelect?dataType=${hsQwManagementCheck.dataType}" allowClear="false"
							checkbox="false" itemCode="applyIdC" readonly="${hsQwManagementCheck.status!'0'!='0'}" itemName="applyTitle" callbackFuncName = "getApplyInfo"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('配租合同编号')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="compactCode" path="hsQwApply.compact.compactCode" maxlength="64" class="form-control required" readonly="true" />
							<#form:hidden id="compactId" path="compactId" maxlength="64" class="form-control" readonly="true" />
						</td>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('主申请人')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="applyerName" path="hsQwApply.mainApplyer.name" maxlength="64" class="form-control required" readonly="true"/>
							<#form:hidden id="applyerId" path="applyerId" maxlength="64" class="form-control" readonly="true"/>
							<#form:hidden id="applyerUserId" path="hsQwApply.mainApplyer.userId" maxlength="64" class="form-control" readonly="true"/>
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('备注信息')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:textarea path="remarks" rows="4" maxlength="500" class="form-control" readonly="${hsQwManagementCheck.status!'0'!='0'}"/>
						</td>
					</tr>
				</table>
			</div>

			<div class="form-unit">${text('读表信息')}</div>
			<div class="hs-table-div">
				<table class="table-form hs-table-form">
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('水表')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="waterFee" path="waterFee" maxlength="64" class="form-control required" readonly="${hsQwManagementCheck.isRead!'false'}"/>
						</td>
						<td class="form-label hs-form-label">
							<span class="required hide">*</span> ${text('电表')}：<i class="fa icon-question hide"></i>
						</td>
						<td>
							<#form:input id="eletricFee" path="eletricFee" maxlength="64" class="form-control required" readonly="${hsQwManagementCheck.isRead!'false'}" />
						</td>
					</tr>
					<tr>
						<td class="form-label hs-form-label">
							<span class="required ">*</span> ${text('燃气表')}：<i class="fa icon-question hide"></i>
						</td>
						<td colspan="3">
							<#form:input id="gasFee" path="gasFee" maxlength="64" class="form-control required" readonly="${hsQwManagementCheck.isRead!'false'}" />
						</td>
					</tr>
				</table>
			</div>

			<div class="form-unit">${text('物品核验记录')}</div>
			<div class="row">
				<div class="form-unit-wrap table-form">
					<table id="hsQwApplyRecordObjectCheckDataGrid"></table>
				</div>
			</div>
			<#form:hidden id="applyId" path="applyId" maxlength="64" class="form-control" readonly="true"/>
			<#form:hidden path="formKey"/>
			<#form:hidden path="checkType" class="form-control required" />
			<div class="box-footer">
				<div class="row">
					<div class="col-sm-offset-2 col-sm-10">
						<% if (hasPermi('apply:hsQwApply:edit')){ %>
							<#form:hidden path="status"/>
							<#bpm:button bpmEntity="${hsQwManagementCheck}" formKey="${hsQwManagementCheck.formKey}" completeText="提 交"/>
						<% } %>
						<button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
					</div>
				</div>
			</div>
		</#form:form>
	</div>
</div>
<% } %>
<script>
	//# // 初始化房屋核查物品DataGrid对象
	$('#hsQwApplyRecordObjectCheckDataGrid').dataGrid({

		data: "#{toJson(hsQwManagementCheck.hsQwObjectCheckList)}",
		datatype: 'local', // 设置本地数据
		autoGridHeight: function(){return 'auto'}, // 设置自动高度

		//# // 设置数据表格列
		columnModel: [
			{header:'主键', name:'id', editable:true, hidden:true},
			{header:'${text("核验物品id")}', name:'objectId', width:60, editable: false, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required'}},
			{header:'核验物品id', name:'objectId', editable:true, hidden:true},
			{header:'${text("核验物品名称")}', name:'objectName', width:100, editable:false, edittype:'text', editoptions:{'maxlength':'100', 'class':'form-control required'}},
			{header:'${text("是否损坏")}', name:'damage', width:135, fixed: true,
				editable: true, edittype:'radio', editoptions:{${hsQwManagementCheck.isRead!'false' == 'true'? '':'\'disabled\': \'disabled\','}'class':'form-control icheck',
					items: $.merge([], "#{@DictUtils.getDictListJson('hs_management_check_damage')}"),
					itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
						js.iCheck(element).on("ifChanged",function(){$(this).resetValid()});
					}
				}
			},
			{header:'${text("是否维修")}', name:'maintenance', width:135, fixed: true,
				editable: true, edittype:'radio', editoptions:{'class':'form-control icheck',
					items: $.merge([], "#{@DictUtils.getDictListJson('hs_management_check_maintenance')}"),
					itemLabel: 'dictLabel', itemValue: 'dictValue', dataInit: function(element){
						js.iCheck(element).on("ifChanged",function(){$(this).resetValid()});
					}
				}
			},
			{header:'${text("备注")}', name:'remarks', width:100, editable:true, edittype:'text', editoptions:{'maxlength':'200', 'class':'form-control'}},
		],

		//# // 编辑表格参数
		editGrid: true,					// 是否是编辑表格
		editGridInitRowNum: 0,		// 编辑表格的初始化新增行数
		editGridAddRowBtnToHeader: true,	// 子表增行按钮是否显示到表头上
		editGridAddRowInitData: {id: '', status: Global.STATUS_NORMAL},	// 新增行的时候初始化的数据

		//# // 编辑表格的提交数据参数
		editGridInputFormListName: 'hsQwObjectCheckList', // 提交的数据列表名
		editGridInputFormListAttrs: 'id,objectId,objectName,damage,maintenance,remarks', // 提交数据列表的属性字段

		//# // 加载成功后执行事件
		ajaxSuccess: function(data){

		}
	});
</script>
<script>
	// 业务实现草稿按钮
	$('#btnDraft').click(function(){
		$('#status').val(Global.STATUS_DRAFT);
	});
	// 流程按钮操作事件
	BpmButton = window.BpmButton || {};
	BpmButton.init = function(task){
		if (task.status != '2') {
			$('.taskComment').removeClass('hide');
		}
	}
	BpmButton.complete = function($this, task){
		$('#status').val(Global.STATUS_AUDIT);
	};
$('#inputForm').validate({
	submitHandler: function(form){
		js.ajaxSubmitForm($(form), function(data){
			js.showMessage(data.message);
			if(data.result == Global.TRUE){
				js.closeCurrentTabPage(function(contentWindow){
					contentWindow.page();
				});
			}
		}, "json");
    }
});

function getApplyInfo(id, act, index, layero, nodes){
	if (nodes==null){
		return;
	}
	Object.keys(nodes).forEach(key => {
		let jsonObject = nodes[key]; // 获取 JSON 对象
		$('#applyId').val(jsonObject.id);
		$('#applyerId').val(jsonObject.mainApplyer.id);
		$('#applyerUserId').val(jsonObject.mainApplyer.userId);
		$('#applyerName').val(jsonObject.mainApplyer.name);
		if (jsonObject.compact!=null){
			$('#compactId').val(jsonObject.compact.id);
			$('#compactCode').val(jsonObject.compact.compactCode);
		} else {
			$('#compactId').val('');
			$('#compactCode').val('');
		}
		console.log(jsonObject.id);
	});
}
</script>