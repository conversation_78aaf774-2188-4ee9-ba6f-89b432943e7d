<% layout('/layouts/default.html', {title: '租赁资格轮候物业核验管理', libs: ['dataGrid']}){ %>
<div class="main-content">
	<div class="box box-main">
		<div class="box-body">
			<#form:form id="searchForm" model="${hsQwManagementCheck}" action="${ctx}/managementcheck/hsQwManagementCheck/listData" method="post" class="form-inline hide"
					data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">
				<div class="form-group">
					<label class="control-label">${text('核验类型')}：</label>
					<div class="control-inline width-120">
						<#form:select path="checkType" dictType="hs_management_check_type" blankOption="true" class="form-control"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('反馈时间')}：</label>
					<div class="control-inline">
						<#form:input path="backDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('核验时间')}：</label>
					<div class="control-inline">
						<#form:input path="checkDate" readonly="true" maxlength="20" class="form-control laydate width-datetime"
							dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('状态')}：</label>
					<div class="control-inline width-120">
						<#form:select path="status" dictType="hs_management_check_status" blankOption="true" class="form-control isQuick"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('核验人员')}：</label>
					<div class="control-inline">
						<#form:input path="checkUser" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('反馈人员')}：</label>
					<div class="control-inline">
						<#form:input path="backUser" maxlength="64" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label">${text('备注信息')}：</label>
					<div class="control-inline">
						<#form:input path="remarks" maxlength="500" class="form-control width-120"/>
					</div>
				</div>
				<div class="form-group">
					<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
					<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
				</div>
			</#form:form>
			<div class="row">
				<div class="col-xs-10 pr10">
					<table id="dataGrid"></table>
					<div id="dataGridPage"></div>
				</div>
				<div class="col-xs-2 pl0">
					<div id="selectData" class="tags-input"></div>
				</div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
var selectData = "#{isNotBlank(selectData!) ? selectData! : '{}'}",
selectNum = 0, dataGrid = $('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	columnModel: [
		{header:'${text("申请单编号")}', name:'applyId',  sortable:false, width:150, align:"left", frozen:true, formatter: function(val, obj, row, act){
			return (val||row.id);
		}},
		{header:'${text("核验反馈")}', name:'checkBack',  sortable:false, width:150, align:"left"},
		{header:'${text("核验结果")}', name:'checkResult',  sortable:false, width:150, align:"left"},
		{header:'${text("核验类型")}', name:'checkType',  sortable:false, width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_management_check_type')}", val, '${text("未知")}', true);
		}},
		{header:'${text("反馈时间")}', name:'backDate',  sortable:false, width:150, align:"center"},
		{header:'${text("核验时间")}', name:'checkDate',  sortable:false, width:150, align:"center"},
		{header:'${text("状态")}', name:'status',  sortable:false, width:150, align:"center", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_management_check_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("核验人员")}', name:'checkUser',  sortable:false, width:150, align:"left"},
		{header:'${text("反馈人员")}', name:'backUser',  sortable:false, width:150, align:"left"},
		{header:'${text("更新时间")}', name:'updateDate',  sortable:false, width:150, align:"center"},
		{header:'${text("备注信息")}', name:'remarks',  sortable:false, width:150, align:"left"},
		{header:'行数据', name:'rowData', hidden:true, formatter: function(val, obj, row, act){
			return JSON.stringify(row);
		}}
	],
	autoGridHeight: function(){
		var height = $(window).height() - $('#searchForm').height() - $('#dataGridPage').height() - 76;
		$('.tags-input').height($('.ui-jqgrid').height() - 10);
		return height;
	},
	showCheckbox: '${parameter.checkbox}' == 'true',
	multiboxonly: false, // 单击复选框时再多选
	ajaxSuccess: function(data){
		$.each(selectData, function(key, value){
			dataGrid.dataGrid('setSelectRow', key);
		});
		initSelectTag();
	},
	onSelectRow: function(id, isSelect, event){
		if ('${parameter.checkbox}' == 'true'){
			if(isSelect){
				selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
			}else{
				delete selectData[id];
			}
		}else{
			selectData = {};
			selectData[id] = JSON.parse(dataGrid.dataGrid('getRowData', id).rowData);
		}
		initSelectTag();
	},
	onSelectAll: function(ids, isSelect){
		if ('${parameter.checkbox}' == 'true'){
			for (var i=0; i<ids.length; i++){
				if(isSelect){
					selectData[ids[i]] = JSON.parse(dataGrid.dataGrid('getRowData', ids[i]).rowData);
				}else{
					delete selectData[ids[i]];
				}
			}
		}
		initSelectTag();
	},
	ondblClickRow: function(id, rownum, colnum, event){
		if ('${parameter.checkbox}' != 'true'){
			js.layer.$('#' + window.name).closest('.layui-layer')
				.find(".layui-layer-btn0").trigger("click");
		}
		initSelectTag();
	}
});
function initSelectTag(){
	selectNum = 0;
	var html = [];
	$.each(selectData, function(key, value){
		selectNum ++;
		html.push('<span class="tag" id="'+key+'_tags-input"><span>'+(value.applyId||value.id)+' </span>'
			+ '<a href="#" onclick="removeSelectTag(\''+key+'\');" title="${text("取消选择")}">x</a></span>');
	});
	html.unshift('<div class="title">${text("当前已选择 {0\} 项", "<span id=\"selectNum\">'+selectNum+'</span>")}：</div>');
	$('#selectData').empty().append(html.join(''));
}
function removeSelectTag(key){
	delete selectData[key];
	dataGrid.dataGrid('resetSelection', key);
	$('#selectNum').html(--selectNum);
	$('#'+key+'_tags-input').remove();
}
function getSelectData(){
	return selectData;
}
</script>