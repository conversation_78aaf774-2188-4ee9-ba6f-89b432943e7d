<% layout('/layouts/default.html', {title: '租赁资格轮候物业核验管理', libs: ['dataGrid']}){ %>
<div class="main-content">
		<div class="nav-tabs-custom nav-main">
			<ul class="nav nav-tabs">
				<% if ( first ){ %>
				<li  class="active" >
					<% } else {%>
				<li>
					<% } %>
					<a href="${ctx}/managementcheck/hsQwManagementCheck/listAudit"><i class="fa icon-energy"></i> ${text('审批待办')}</a></li>
				<div class="box-tools pull-right">
					<a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
					<% if(hasPermi('managementcheck:hsQwManagementCheck:addIn')){ %>
						<a href="${ctx}/managementcheck/hsQwManagementCheck/form?checkType=0" class="btn btn-default btnTool" title="${text('新增租赁资格轮候物业核验')}"><i class="fa fa-plus"></i> ${text('新增入户核验')}</a>
					<% } %>
					<% if(hasPermi('managementcheck:hsQwManagementCheck:addOut')){ %>
						<a href="${ctx}/managementcheck/hsQwManagementCheck/form?checkType=1" class="btn btn-default btnTool" title="${text('新增租赁资格轮候物业核验')}"><i class="fa fa-plus"></i> ${text('新增退租核验')}</a>
					<% } %>
					<a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
				</div>
				<% if ( !first ){ %>
				<li  class="active" >
					<% } else {%>
				<li>
					<% } %>
					<a href="${ctx}/managementcheck/hsQwManagementCheck/listDone"><i class="fa icon-book-open"></i> ${text('已办信息')}</a></li>
			</ul>
		<div class="box-body">
			<!-- 筛选条件区域 -->
			<div class="search-form-container">
				<#form:form id="searchForm" model="${hsQwManagementCheck}" action="${ctx}${listDataUrl}" method="post" class="form-inline hide"
						data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

					<!-- 第一行：2个查询条件 -->
					<div class="search-form-row">
						<div class="form-group">
							<label class="control-label">${text('核验类型')}：</label>
							<div class="control-inline">
								<#form:select path="checkType" dictType="hs_management_check_type" blankOption="true" class="form-control width-120"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label">${text('状态')}：</label>
							<div class="control-inline">
								<#form:select path="status" dictType="sys_status" blankOption="true" class="form-control width-120 isQuick"/>
							</div>
						</div>
						<div class="form-group">
							<!-- 占位，保持布局 -->
						</div>
					</div>

					<!-- 按钮行 -->
					<div class="search-button-row">
						<button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
						<button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
					</div>
				</#form:form>
			</div>

			<!-- 表格区域 -->
			<div class="fixed-table-container">
				<table id="dataGrid"></table>
				<div id="dataGridPage"></div>
			</div>
		</div>
	</div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
		{header:'${text("编号")}', name:'applyId',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return '<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId +'" class="bpmButton" title="${text("物业核验")}">'+(val||row.id)+'</a>';
		}},
		{header:'${text("核验类型")}', name:'checkType',  sortable:false, width:120, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_management_check_type')}", val, '${text("未知")}', true);
			}},
		{header:'${text("申请单信息")}', name:'applyId',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/apply/hsQwApply/formCompact?compactRead=1&houseRead=1&id='+row.applyId+'" class="hsBtnList" data-title="${text("查看申请单信息")}">'+(val||row.applyId)+'</a>';
		}},

		{header:'${text("流程环节")}', name:'processName',sortable:false,  width:120, align:"left"},
		{header:'${text("房源信息")}', name:'hsQwApply.hsQwApplyHouse.simpleInfo', sortable:false, width:150, align:"left"},
		{header:'${text("主申请人")}', name:'hsQwApply.mainApplyer.name',  sortable:false, width:120, align:"left"},
		{header:'${text("主申请人身份证")}', name:'hsQwApply.mainApplyer.idNum',  sortable:false, width:150, align:"left"},
		{header:'${text("主申请人手机号")}', name:'hsQwApply.mainApplyer.phone',  sortable:false, width:120, align:"left"},
		{header:'${text("状态")}', name:'status',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('sys_status')}", val, '${text("未知")}', true);
		}},
		{header:'${text("操作")}', name:'actions', width:150, align:"left", formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('managementcheck:hsQwManagementCheck:edit')){
				actions.push('<a href="${ctx}/bpm/bpmMyTask/form?id=' + row.taskId +'" class="bpmButton" title="${text("物业核验")}">编辑</a>&nbsp;');
			//# }
			if (row.status != Global.STATUS_DRAFT){
				actions.push('<a href="${ctx}/bpm/bpmRuntime/trace?formKey='+ row.formKey + '&bizKey='+row.id+'" class="hsBtnList" title="${text("流程追踪")}" data-layer="true">流程追踪</a>&nbsp;');
			}
			return actions.join('');
		}}
	],
	loadComplete: function() {
		// 使用requestAnimationFrame确保在下一帧执行，不阻塞加载框关闭
		requestAnimationFrame(function() {
			CommonTable.setupFixedColumns('dataGrid');
		});
		js.closeLoading(0, true);
	}
}).on('click', '.bpmButton', function(){
	var $this = $(this).addClass('hsBtnList');
	js.ajaxSubmit($this.attr('href'), function(data){
		if (data.result == Global.TRUE){
			// 检查 pcUrl 是否可访问
			$.ajax({
				url: data.pcUrl,
				type: 'get',
				success: function(response) {
					// 如果请求成功，使用 js.addTabPage 打开新标签页
					js.addTabPage($this, $this.attr('title'), data.pcUrl);
				},
				error: function(xhr, status, error) {
					// 如果请求失败，显示错误信息
					var errorMsg = '${text("加载失败")}';
					if (xhr.responseJSON && xhr.responseJSON.message) {
						errorMsg = xhr.responseJSON.message;
					}
					js.showErrorMessage(errorMsg);
				}
			});
		} else {
			js.showErrorMessage(data.message);
		}
	});
	return false;
});
</script>

<script>
	// 初始化公共表格功能
	CommonTable.init('dataGrid');
</script>