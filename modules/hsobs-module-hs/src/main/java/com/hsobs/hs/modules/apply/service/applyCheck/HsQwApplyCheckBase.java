package com.hsobs.hs.modules.apply.service.applyCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.service.HsQwApplyerBlackService;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HsQwApplyCheckBase  {

    @Autowired
    private HsQwApplyerBlackService hsQwApplyerBlackService;

    @Autowired
    protected HsQwApplyDao hsQwApplyDao;

    /**
     * 黑名单用户校验
     * @param hsQwApply
     */
    public void blackCheck(HsQwApply hsQwApply){
        HsQwApplyerBlack entity = new HsQwApplyerBlack();
        entity.setUserId(this.getRealApplyUser(hsQwApply));
        entity.sqlMap().getWhere().and("status", QueryType.EQ, "0");
        entity.setEndTime_gte(new Date());
        long count = hsQwApplyerBlackService.findCount(entity);
        if (count > 0) {
            throw new ServiceException("申请资格验证失败，条件不满足！");
        }
    }

    /**
     * 过程中的申请
     * @param hsQwApply
     * @param applyMatters
     */
    public boolean processCheck(HsQwApply hsQwApply, String[] applyMatters, String[] status, String allowMatter, String extWhere){
        //申请单校验
        HsQwApply entity = new HsQwApply();
        entity.sqlMap().getWhere().disableAutoAddStatusWhere();
        entity.setMainApplyer(new HsQwApplyer());
        entity.getMainApplyer().setUserId(this.getRealApplyUser(hsQwApply));//主申请人是申请者
        //是否存在信息变更和房屋置换变更申请单判定
        if (applyMatters != null){
            entity.sqlMap().getWhere().and("apply_matter", QueryType.IN, applyMatters);
        }
        if (applyMatters != null){
            entity.setStatus_in(status);
        }
        if (extWhere != null){
            entity.sqlMap().put("extWhere", extWhere);
        }
        if (!StringUtils.isBlank(hsQwApply.getId())){
            //校验去除自身的id
            entity.sqlMap().getWhere().and("id", QueryType.NE, hsQwApply.getId());
        }
        List<HsQwApply> list = hsQwApplyDao.findList(entity);
        if (list.size() >= 1) {
            return false;
        }
        return true;
    }

    public boolean normalCheck(String[] status, HsQwApply hsQwApply) {
        //是否存在已有的公租房申请单判定
        HsQwApply entity = new HsQwApply();
        entity.sqlMap().getWhere().disableAutoAddStatusWhere();
        entity.setMainApplyer(new HsQwApplyer());
        entity.getMainApplyer().setUserId(this.getRealApplyUser(hsQwApply));//主申请人是申请者
        entity.setStatus_in(status);
        entity.setApplyMatter(HsQwApply.APPLY_MATTER_RENTAL);
        long count1 = hsQwApplyDao.findCount(entity);
        if (count1 <= 0) {
            //若不存在公租申请单，则查询是否做了信息修改、居室变更、承租变更
            entity.setStatus("0");
            entity.setApplyMatter(null);
            entity.sqlMap().getWhere().and("apply_matter", QueryType.IN, new String[]{"1","2","4"});
            count1 = hsQwApplyDao.findCount(entity);
            if (count1 <= 0) {
                return false;
            }
        }
        return true;
    }

    protected String getRealApplyUser(HsQwApply hsQwApply) {
        if (hsQwApply.getProxyUserId() != null) {
            return hsQwApply.getProxyUserId();
        }
        return UserUtils.getUser().getUserCode();
    }
}
