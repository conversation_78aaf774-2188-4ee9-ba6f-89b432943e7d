package com.hsobs.hs.modules.utils;

import com.jeesite.common.lang.DateUtils;
import freemarker.cache.FileTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Map;

@Slf4j
public class WordUtils {

    private static Configuration configuration = null;
    private static final String templateFolder1 = WordUtils.class.getClassLoader().getResource("").getPath()+"classes/file.template/";
    private static final String templateFolder2 = WordUtils.class.getClassLoader().getResource("").getPath()+"file.template/";
    private static final String templateFolder3 = WordUtils.class.getClassLoader().getResource("").getPath()+"/WEB-INF/classes/file.template/";

    private static final String[] numerals = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] units = {"", "十", "百", "千", "万", "十万", "百万", "千万", "亿"};

    static {
        configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        configuration.setDefaultEncoding("utf-8");

        try {
            String path = templateFolder3;
            File f = new File(path);
            if (!f.exists()) {
                path = templateFolder1;
                f = new File(path);
            }
            if (!f.exists()) {
                path = templateFolder2;
            }
            log.info(path);

            FileTemplateLoader fileTemplateLoader = new FileTemplateLoader(new File(path), true);
            configuration.setTemplateLoader(fileTemplateLoader);
        } catch (IOException e) {
            log.debug("", e);
        }
    }
    private WordUtils() {
        throw new AssertionError();
    }

    /**
     * 导出excel
     * @param response 响应对象
     * @param map word文档中参数
     * @param wordName 为模板的名字  例如xxx.ftl
     * @param fileName 是word 文件的名字 格式为："xxxx.doc"
     * @param name 是临时的文件夹米名称 string类型 可随意定义
     * @throws IOException
     */
    public static void exportMillCertificateWord(HttpServletResponse response, Map map, String wordName, String fileName, String name) throws IOException {

        Template freemarkerTemplate = configuration.getTemplate(wordName);
        File file = null;
        InputStream fin = null;
        ServletOutputStream out = null;
        try {
            // 调用工具类的createDoc方法生成Word文档
            file = createDoc(map, freemarkerTemplate, name);
            fin = new FileInputStream(file);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/x-download");
            fileName = new String(fileName.getBytes(), "ISO-8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(fileName)));
            out = response.getOutputStream();
            byte[] buffer = new byte[512];// 缓冲区
            int bytesToRead = -1;
            // 通过循环将读入的Word文件的内容输出到浏览器中
            while ((bytesToRead = fin.read(buffer)) != -1) {
                out.write(buffer, 0, bytesToRead);
            }
        } finally {
            if(fin != null) {
                fin.close();
            }
            if(out != null) {
                out.close();
            }
            if(file != null) {
                file.delete();// 删除临时文件
            }
        }
    }

    public static void exportWord(Map map, String wordName, String fileName, String name) throws IOException {

        Template freemarkerTemplate = configuration.getTemplate(wordName);
        File file = null;
        InputStream fin = null;
        OutputStream out = null;
        try {
            // 调用工具类的createDoc方法生成Word文档
            file = createDoc(map, freemarkerTemplate, name);
            fin = new FileInputStream(file);
            out = new FileOutputStream(fileName);
            byte[] buffer = new byte[512];// 缓冲区
            int bytesToRead = -1;
            // 通过循环将读入的Word文件的内容输出到浏览器中
            while ((bytesToRead = fin.read(buffer)) != -1) {
                out.write(buffer, 0, bytesToRead);
            }
        } finally {
            if(fin != null) {
                fin.close();
            }
            if(out != null) {
                out.close();
            }
            if(file != null) {
                file.delete();// 删除临时文件
            }
        }
    }

    private static File createDoc(Map<?, ?> dataMap, Template template, String name) {

        File f = new File(DateUtils.getDate("yyyyMMddHHmmss")+name);
        try {
            // 这个地方不能使用FileWriter因为需要指定编码类型否则生成的Word文档会因为有无法识别的编码而无法打开
            Writer w = new OutputStreamWriter(new FileOutputStream(f), "utf-8");
            template.process(dataMap, w);
            w.close();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
        return f;
    }

    public static String toChineseNumeral(int num) {
        if (num == 0) {
            return numerals[0];
        }

        StringBuilder sb = new StringBuilder();
        String numStr = String.valueOf(Math.abs(num));
        int length = numStr.length();

        for (int i = 0; i < length; i++) {
            int digit = numStr.charAt(length - 1 - i) - '0';
            if (digit != 0) {
                sb.insert(0, numerals[digit] + units[i]);
            } else if (i % 4 == 0 && length - 1 - i >= 0) { // 处理万、亿等单位前的零
                if (sb.length() > 0 && !sb.substring(0, 1).equals("零")) {
                    sb.insert(0, units[i]);
                }
            } else if (sb.length() > 0 && sb.charAt(0) != '零' && i % 4 != 0 && length - 1 - i >= 0) {
                sb.insert(0, numerals[digit]); // 避免连续的零，但保留单位前的零
            }
        }

        // 处理特殊情况：十
        if (sb.length() > 1 && sb.charAt(0) == '一' && sb.charAt(1) == '十') {
            sb.deleteCharAt(0);
        }

        // 处理负数
        if (num < 0) {
            sb.insert(0, "负");
        }

        // 移除末尾多余的零
        while (sb.length() > 1 && sb.charAt(sb.length() - 1) == '零') {
            sb.deleteCharAt(sb.length() - 1);
        }

        return sb.toString();
    }

}