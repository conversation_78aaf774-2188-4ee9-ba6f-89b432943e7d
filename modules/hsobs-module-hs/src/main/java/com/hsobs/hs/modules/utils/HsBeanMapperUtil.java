package com.hsobs.hs.modules.utils;

import com.hsobs.hs.modules.external.entity.ApiFile;
import com.hsobs.hs.modules.external.entity.HsMapFile;
import com.hsobs.hs.modules.external.entity.HsMapTo;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.modules.bpm.entity.BpmEntity;

import java.lang.reflect.*;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class HsBeanMapperUtil {


    /**
     * source转换为targetType的bean
     * @param source
     * @param targetType
     * @return 转换后的目标对象
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     */
    public static <S, T> T convertBean(S source, Class<T> targetType) {
        return convertBean(source, targetType, null);
    }
    /**
     * source转换为targetType的bean
     * @param source
     * @param targetType
     * @param targetInstance 可选的目标实体对象，如果传入则使用该对象，否则创建新对象
     * @return 转换后的目标对象
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     */
    public static <S, T> T convertBean(S source, Class<T> targetType, T targetInstance) {
        try {
            // 如果 targetInstance 为 null，则创建一个新的目标实体
            if (targetInstance == null) {
                targetInstance = targetType.getDeclaredConstructor().newInstance();
            }

            // 遍历 source 对象的所有字段，包括继承的字段
            for (Field sourceField : getAllFields(source.getClass())) {
                sourceField.setAccessible(true);
                HsMapTo annotation = sourceField.getAnnotation(HsMapTo.class);

                if (annotation != null) {
                    String targetFieldName = annotation.value();
                    Object sourceValue = sourceField.get(source);

                    if (sourceValue == null) continue; // 跳过空值

                    setFieldValue(targetInstance, targetFieldName, sourceValue);
                }

                HsMapFile fileano = sourceField.getAnnotation(HsMapFile.class);
                if (fileano !=null){
                    // 这里可以添加对 HsMapFile 的处理逻辑
                    setFileFieldValue(targetInstance, (List<ApiFile>) sourceField.get(source));
                }
            }

            //设置文件
            Field fileMap = getFieldRecursively(source.getClass(), "dataMap");
            if (fileMap !=null){
                fileMap.setAccessible(true);
                Map dataMap = (Map) fileMap.get(source);
                if (dataMap !=null){
                    setFileFieldValue(targetInstance, dataMap);
                }
            }

            //设置通用id
            Field fieldId = getFieldRecursively(source.getClass(), "id");
            if (fieldId !=null){
                fieldId.setAccessible(true);
                Object id = fieldId.get(source);
                if (id !=null){
                    Field targetField = getFieldRecursively(targetInstance.getClass(), "id");
                    targetField.setAccessible(true);
                    targetField.set(targetInstance, id.toString());
                }
            }
            return targetInstance;
        } catch (Exception e) {
            throw new RuntimeException("Bean 映射失败: " + e.getMessage(), e);
        }
    }

    /**
     * 反向文件列表写入，接收参数为固定的fileList
     * @param targetInstance
     * @param dataMap
     * @param <T>
     */
    private static <T> void setFileFieldValue(T targetInstance, Map dataMap) throws Exception {
        List<ApiFile> files = new ArrayList<>();
        dataMap.forEach((k,v)->{
            String[] ids = v.toString().split(",");
            for (String id : ids) {
                ApiFile file = new ApiFile();
                file.setFileId(id);
                file.setFileType(k.toString());
                files.add(file);
            }
        });
        Field targetField = getFieldRecursively(targetInstance.getClass(), "fileList");
        if (targetField==null){
            return;
        }
        targetField.setAccessible(true);
        targetField.set(targetInstance, files);
    }

    /**
     * 文件类处理
     * @param targetInstance
     * @param apiFiles
     * @param <T>
     */
    public static <T> void setFileFieldValue(T targetInstance, List<ApiFile> apiFiles) throws Exception {
        if (apiFiles == null){
            return;
        }
        try {
            Map dataMap = new HashMap();
            apiFiles.forEach(fileUpload -> {
                if (dataMap.containsKey(fileUpload.getFileType())) {
                    String value = dataMap.get(fileUpload.getFileType()).toString();
                    dataMap.put(fileUpload.getFileType(), value + "," + fileUpload.getFileId());
                } else {
                    dataMap.put(fileUpload.getFileType(), fileUpload.getFileId());
                }
            });
            setFieldValue(targetInstance, "dataMap", dataMap);
        } catch (Exception e) {
            throw new Exception("文件信息处理失败" );
        }

    }

    /**
     * 获取所有属性
     * @param clazz
     * @return
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) { // 确保一直遍历到 Object 之前
            Collections.addAll(fields, clazz.getDeclaredFields());
            clazz = clazz.getSuperclass(); // 获取父类字段
        }
        return fields;
    }

    /**
     * 递归查找字段（支持继承层级）
     * @param clazz
     * @param fieldName
     * @return
     * @throws NoSuchFieldException
     */
    private static Field getFieldRecursively(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        while (clazz != null && clazz != Object.class) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
//        throw new NoSuchFieldException("字段未找到: " + fieldName);
        return null;
    }

    /**
     * 设置字段值，支持集合、嵌套对象、时间格式转换
     * @param targetInstance
     * @param fieldPath
     * @param sourceValue
     * @throws Exception
     */
    private static void setFieldValue(Object targetInstance, String fieldPath, Object sourceValue) throws Exception {
        String[] fieldNames = fieldPath.split("\\.");
        Object currentObject = targetInstance;

        for (int i = 0; i < fieldNames.length - 1; i++) {
            Field field = getFieldRecursively(currentObject.getClass(), fieldNames[i]);
            if (field == null){
                continue;
            }
            field.setAccessible(true);

            if (field.get(currentObject) == null) {
                Object nestedObject = field.getType().getDeclaredConstructor().newInstance();
                field.set(currentObject, nestedObject);
            }
            currentObject = field.get(currentObject);
        }

        Field targetField = getFieldRecursively(currentObject.getClass(), fieldNames[fieldNames.length - 1]);
        if (targetField == null){
            return;
        }
        targetField.setAccessible(true);

        Object convertedValue = convertValue(targetField.getType(), targetField, sourceValue);
        targetField.set(currentObject, convertedValue);
    }

    /**
     * 类型转换（支持 String → Date、LocalDateTime、Long）
     * @param targetType
     * @param field
     * @param sourceValue
     * @return
     * @throws Exception
     */
    private static <S extends DataEntity> Object convertValue(Class<?> targetType, Field field, Object sourceValue) throws Exception {
        if (sourceValue == null) return null;

        // 处理集合类型
        if (sourceValue instanceof List<?> && Collection.class.isAssignableFrom(targetType)) {
            ParameterizedType listType = (ParameterizedType) field.getGenericType();
            Class<?> targetItemType = (Class<?>) listType.getActualTypeArguments()[0];
            List<S> sourceList = (List<S>) sourceValue;
            List<Object> targetList = new ArrayList<>();

            for (Object item : sourceList) {
                targetList.add(convertBean(item, targetItemType));
            }
            return targetList;
        }

        // 直接赋值相同类型的数据
        if (targetType.isAssignableFrom(sourceValue.getClass())) {
            return sourceValue;
        }

        // 处理 String → Date 转换
        if (targetType == Date.class && sourceValue instanceof String) {
            return DateUtils.parseDate(sourceValue);
        }

        // 处理 Date → String 转换
        if (targetType == String.class && sourceValue instanceof Date) {
            return DateUtils.formatDate((Date) sourceValue);
        }

        // 处理 Date → String 转换
        if (targetType == double.class && sourceValue instanceof String) {
            return Double.parseDouble(sourceValue.toString());
        }

        return sourceValue; // 直接返回原值
    }
}
