package com.hsobs.hs.modules.pricelimitapply.service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.dao.HsQwPublicRentalHouseDao;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitImport;
import com.hsobs.hs.modules.pricelimitplan.entity.HsPriceLimitPlan;
import com.hsobs.hs.modules.pricelimitplan.service.HsPriceLimitPlanService;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.hsobs.hs.modules.utils.WordUtils;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.mapper.query.QueryWhere;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.ApiHjptService;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.pricelimitapply.dao.HsPriceLimitApplyDao;

import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.hsobs.hs.modules.pricelimitapplyer.dao.HsPriceLimitApplyerDao;

import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.service.OfficeService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.constraints.NotBlank;


/**
 * 限价房-购房申请Service
 * <AUTHOR>
 * @version 2024-12-10
 */
@Service
public class HsPriceLimitApplyService extends CrudService<HsPriceLimitApplyDao, HsPriceLimitApply> {

	@Autowired
	private HsPriceLimitApplyDao hsPriceLimitApplyDao;

	@Autowired
	private HsPriceLimitApplyerDao hsPriceLimitApplyerDao;

	@Autowired
	private HsQwPublicRentalHouseDao hsPublicRentalHouseDao;

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

	@Autowired
	private HsPriceLimitPlanService hsPriceLimitPlanService;

	@Autowired
	private EmpUserService empUserService;

	@Autowired
	private OfficeService officeService;

	@Autowired
	private BpmTaskService bpmTaskService;
	@Autowired
	private CommonBpmService commonBpmService;

	@Autowired
	ResourceLoader resourceLoader;

	@Autowired
	ApiHjptService apiHjptService;

	private void findAndSetActivityId(HsPriceLimitApply hsPriceLimitApply){

		HsBpmTask params = new HsBpmTask();
		params.setStatus(BpmTask.STATUS_UNFINISHED);
		params.getProcIns().setFormKey("pricelimit_apply");
		params.getProcIns().setBizKey(hsPriceLimitApply.getId());
		Page<BpmTask> myHsTaskPage = this.bpmTaskService.findTaskPage(params);

		int activityIdInt = 1;

		BpmTask bpmTask = !myHsTaskPage.getList().isEmpty() ? (BpmTask) myHsTaskPage.getList().get(0) : null;
		if (bpmTask != null) {

			hsPriceLimitApply.setFlowStatus(bpmTask.getName());
			String activityId = bpmTask.getActivityId();
			activityId = activityId.substring(Math.max(0, activityId.length() - 3));
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				activityIdInt = Integer.parseInt(activityId);
			}
		}

		hsPriceLimitApply.setApplyStatus(activityIdInt);
	}

	/**
	 * 获取单条数据
	 * @param hsPriceLimitApply
	 * @return
	 */
	@Override
	public HsPriceLimitApply get(HsPriceLimitApply hsPriceLimitApply) {
		HsPriceLimitApply entity = super.get(hsPriceLimitApply);
		if (entity != null){
			Office office = new Office();
			office.setOfficeCode(entity.getOfficeCode());
			entity.setOffice(officeService.get(office));

			HsPriceLimitApplyer hsPriceLimitApplyer = new HsPriceLimitApplyer();
			hsPriceLimitApplyer.setApplyId(entity.getId());
			hsPriceLimitApplyer.sqlMap().getWhere().disableAutoAddStatusWhere();
			List<HsPriceLimitApplyer> lstApplyer = hsPriceLimitApplyerDao.findList(hsPriceLimitApplyer);
			entity.setHsPriceLimitApplyerList(lstApplyer);

			for(HsPriceLimitApplyer applyer : lstApplyer){
				if(applyer.getApplyRole().equals("0")){
					hsPriceLimitApply.setMainApplyer(applyer);
					break;
				}
			}

			if(entity.getPlanId() != null && !entity.getPlanId().equals("")){
				HsPriceLimitPlan plan = new HsPriceLimitPlan();
				plan.setId(entity.getPlanId());
				plan.sqlMap().getWhere().disableAutoAddStatusWhere();
				entity.setPlan(hsPriceLimitPlanService.get(plan));
			}

			if(entity.getHouseId() != null && !entity.getHouseId().equals("")) {
				HsQwPublicRentalHouse house = new HsQwPublicRentalHouse();
				house.setId(entity.getHouseId());
				house.sqlMap().getWhere().disableAutoAddStatusWhere();
				entity.setHouse(hsPublicRentalHouseDao.get(house));

				if(entity.getHouse() != null){
					entity.setHouseInfo(entity.getHouse().getSimpleInfo());
				}
			}

			findAndSetActivityId(entity);

			if(entity.getIsNewRecord() || entity.getApplyStatus() <= 2)
				entity.setReadOnly("false");
			else
				entity.setReadOnly("true");
		}
		return entity;
	}

	/**
	 * 查询分页数据
	 * @param hsPriceLimitApply 查询条件
	 * @param hsPriceLimitApply page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPriceLimitApply> findPage(HsPriceLimitApply hsPriceLimitApply) {
		hsPriceLimitApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findPage(hsPriceLimitApply);
	}
	
	/**
	 * 查询列表数据
	 * @param hsPriceLimitApply
	 * @return
	 */
	@Override
	public List<HsPriceLimitApply> findList(HsPriceLimitApply hsPriceLimitApply) {
		hsPriceLimitApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findList(hsPriceLimitApply);
	}

	@Override
	public void addDataScopeFilter(HsPriceLimitApply hsPriceLimitApply) {
		hsPriceLimitApply.sqlMap().getDataScope().addFilter("dsf", "Office", "a.office_code", DataScope.CTRL_PERMI_HAVE);
	}
	
	/**
	 * 查询子表分页数据
	 * @param hsPriceLimitApplyer
	 * @param hsPriceLimitApplyer page 分页对象
	 * @return
	 */
	public Page<HsPriceLimitApplyer> findSubPage(HsPriceLimitApplyer hsPriceLimitApplyer) {
		Page<HsPriceLimitApplyer> page = hsPriceLimitApplyer.getPage();
		page.setList(hsPriceLimitApplyerDao.findList(hsPriceLimitApplyer));
		return page;
	}

	/**
	 * 更新住房状态  2待售 3已售
	 *
	 * @param houseId
	 * @param status		2待售 3已售
	 */
	public void updateHouseStatus(String houseId, String status) {
		HsQwPublicRentalHouse hsQwApplyHouse = hsQwPublicRentalHouseService.get(houseId);
		hsQwApplyHouse.setHouseStatus(status);
		hsQwPublicRentalHouseService.update(hsQwApplyHouse);
	}

	public boolean checkHouseId(HsPriceLimitApply hsApply, String houseId){
		List<Map<String,Object>> list = hsPriceLimitApplyDao.checkHouse(hsApply.getId(), houseId);
		return (list.size() == 0)?true:false;
	}

	/**
	 * 保存数据（插入或更新）
	 * @param hsPriceLimitApply
	 */
	@Override
	@Transactional
	public void save(HsPriceLimitApply hsPriceLimitApply) {
		final int[] mainNum = {0};
		hsPriceLimitApply.getHsPriceLimitApplyerList().forEach(k -> {
			if (k.getApplyRole().equals("0")){
				mainNum[0]++;
			}
		});
		if (mainNum[0] == 0) {
			throw new ServiceException("未添加主申请人信息");
		}
		if (mainNum[0] > 1) {
			throw new ServiceException("主申请人只能有一个");
		}

		if(hsPriceLimitApply.getPlanId() == null) {
			throw new ServiceException("需要选择一个限价房方案");
		}
		hsPriceLimitApply.setPlanId(hsPriceLimitApply.getPlanId().replaceAll(",", ""));

		if(hsPriceLimitApply.getFlowStatus() != null && hsPriceLimitApply.getFlowStatus().equals(hsPriceLimitApply.APPLY_STATUS_AUDIT_QUALIFICATION)){
			if(hsPriceLimitApply.getHouseId() == null || hsPriceLimitApply.getHouseId().equals("")){
				throw new ServiceException(text("没有选择房源"));
			}
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsPriceLimitApply.STATUS_NORMAL.equals(hsPriceLimitApply.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		String houseId = hsPriceLimitApply.getHouseId();
		if(houseId != null && !houseId.equals("")) {
			int index = houseId.indexOf(',');
			if (index > 0) {
				houseId = houseId.substring(index + 1);
				hsPriceLimitApply.setHouseId(houseId);
			}
			if(!checkHouseId(hsPriceLimitApply, hsPriceLimitApply.getHouseId())){
				throw new ServiceException(text("房源选择错误，该房源已被占用！"));
			}
		}

		if(hsPriceLimitApply.getPublicStatus() == null){
			hsPriceLimitApply.setPublicStatus("0");
		}

		if(hsPriceLimitApply.getOfficeCode() == null) {
			if (UserUtils.getUser().isAdmin()) {
				throw new ServiceException(text("获取不到工作单位，不能以管理员身份发起！"));
			}
			String officeCode = EmpUtils.getCurrentOfficeCode();
			hsPriceLimitApply.setOfficeCode(officeCode);
		}

		// 判断houseid是否正确
		if(hsPriceLimitApply.getHouseId() != null && !hsPriceLimitApply.getHouseId().equals("")) {
			try {
				List<Map<String,Object>> list = hsPriceLimitApplyDao.selectHouseId(hsPriceLimitApply.getId());
				if(list != null && list.size() > 0) {
					Map<String, Object> map = list.get(0);
					Object ob = map.get("HOUSE_ID");
					if (ob != null) {
						String oldHouseId = ob.toString();
						if (!oldHouseId.equals(hsPriceLimitApply.getHouseId())) {
							this.updateHouseStatus(oldHouseId, "2");
						}
					}
				}
			}
			catch (Exception e) {
			}
		}

		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsPriceLimitApply.getStatus())){
			hsPriceLimitApply.setStatus(HsPriceLimitApply.STATUS_AUDIT);
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsPriceLimitApply.STATUS_DRAFT.equals(hsPriceLimitApply.getStatus())
				|| HsPriceLimitApply.STATUS_AUDIT.equals(hsPriceLimitApply.getStatus())){
			this.updateStatus(hsPriceLimitApply);
			super.save(hsPriceLimitApply);
		}

		// 保存申请材料
		FileUploadUtils.saveFileUpload(hsPriceLimitApply, hsPriceLimitApply.getId(), "hsPriceLimitApplyApply_file");
		// 资格确认单
		FileUploadUtils.saveFileUpload(hsPriceLimitApply, hsPriceLimitApply.getId(), "hsPriceLimitApplyExamine_file");
		// 保存公示材料
		FileUploadUtils.saveFileUpload(hsPriceLimitApply, hsPriceLimitApply.getId(), "hsPriceLimitApplyPublic_file");
		// 保存合同材料
		FileUploadUtils.saveFileUpload(hsPriceLimitApply, hsPriceLimitApply.getId(), "hsPriceLimitApplyContract_file");

		if (hsPriceLimitApply.getHouseId() != null && StringUtils.isNotBlank(hsPriceLimitApply.getHouseId())){
			this.updateHouseStatus(hsPriceLimitApply.getHouseId(), "3");//已售
		}

		// 如果为审核状态，则进行审批流操作
		if (HsPriceLimitApply.STATUS_AUDIT.equals(hsPriceLimitApply.getStatus())){
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", hsPriceLimitApply.getLeaveDays());

			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsPriceLimitApply.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsPriceLimitApply.getBpm().getTaskId())){
				BpmUtils.start(hsPriceLimitApply, "pricelimit_apply", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsPriceLimitApply, variables, null);
			}
		}

		// 保存 HsPriceLimitApply子表
		for (HsPriceLimitApplyer hsPriceLimitApplyer : hsPriceLimitApply.getHsPriceLimitApplyerList()){
			if (!HsPriceLimitApplyer.STATUS_DELETE.equals(hsPriceLimitApplyer.getStatus())){
				hsPriceLimitApplyer.setApplyId(hsPriceLimitApply.getId());
				hsPriceLimitApplyer.setUserId(hsPriceLimitApply.getId());
				if (hsPriceLimitApplyer.getIsNewRecord()){
					hsPriceLimitApplyerDao.insert(hsPriceLimitApplyer);
				}else{
					hsPriceLimitApplyerDao.update(hsPriceLimitApplyer);
				}
			}else{
				hsPriceLimitApplyerDao.delete(hsPriceLimitApplyer);
			}
		}
		if(hsPriceLimitApply.getFlowStatus() != null && hsPriceLimitApply.getFlowStatus().equals(HsPriceLimitApply.APPLY_STATUS_AUDIT_CONTRACT)){// 流程结束
			hsPriceLimitApply.setStatus("88");
			this.updateStatus(hsPriceLimitApply);
		}
	}

	private void initBpm(HsPriceLimitApply hsPriceLimitApply, BpmTask task) {
		BpmParams bpm = hsPriceLimitApply.getBpm();
		bpm.setTaskId(task.getId());
		bpm.setProcInsId(task.getProcIns().getId());
		bpm.setNextUserCodes(task.getNextUserCodes());
		bpm.setActivityId(task.getActivityId());
		bpm.setDueDate(task.getDueDate());
		bpm.setComment(task.getComment());
	}

	public void submitTask(HsPriceLimitApply hsPriceLimitApply, String userCode){

		BpmTask task = BpmUtils.getTask(hsPriceLimitApply, "pricelimit_apply", StringUtils.isBlank(userCode)?UserUtils.getUser().getUserCode():userCode);
		this.initBpm(hsPriceLimitApply, task);
		// 指定流程变量，作为流程条件，决定流转方向
		Map<String, Object> variables = MapUtils.newHashMap();
		variables.put("officeCode", hsPriceLimitApply.getOfficeCode());
		BpmUtils.getBpmTaskService().claimTask(task);
		BpmUtils.complete(hsPriceLimitApply, variables, null);
	}

	public void submitPubicTasks(List<HsPriceLimitApply> lstApply) {

		Page<BpmTask> myHsTaskPage = findBpmTaskPage(HsPriceLimitApply.APPLY_STATUS_AUDIT_SHOW_PUBLIC, null, BpmTask.STATUS_UNFINISHED);

		for (HsPriceLimitApply hsPriceLimitApply : lstApply) {
			String applyId = hsPriceLimitApply.getId();

			// 确认一下是否还是公示流程，在跳下一步
			for (BpmTask bpmTask : myHsTaskPage.getList()) {
				if(bpmTask.getProcIns().getBizKey().equals(applyId)) {

					submitTask(hsPriceLimitApply, null);
					break;
				}

			}
		}

	}
	
	/**
	 * 更新状态
	 * @param hsPriceLimitApply
	 */
	@Override
	@Transactional
	public void updateStatus(HsPriceLimitApply hsPriceLimitApply) {
		super.updateStatus(hsPriceLimitApply);
	}

	/**
	 * 更新状态
	 * @param hsPriceLimitApply
	 */
	public void updatePublicStatus(HsPriceLimitApply hsPriceLimitApply, String publicStatus) {
		hsPriceLimitApply.setPublicStatus(publicStatus);
		super.update(hsPriceLimitApply);
	}
	
	/**
	 * 删除数据
	 * @param hsPriceLimitApply
	 */
	@Override
	@Transactional
	public void delete(HsPriceLimitApply hsPriceLimitApply) {
		super.delete(hsPriceLimitApply);
		HsPriceLimitApplyer hsPriceLimitApplyer = new HsPriceLimitApplyer();
		hsPriceLimitApplyer.setApplyId(hsPriceLimitApply.getId());
		hsPriceLimitApplyerDao.deleteByEntity(hsPriceLimitApplyer);
	}

	public List<HsPriceLimitApply> findAuditListByTask(HsPriceLimitApply hsApply, String flowStatus, String[] flowStatuss, String status) {

		Page<HsPriceLimitApply> page = findAuditPageByTask(hsApply, flowStatus, flowStatuss, status);
		return page.getList();
	}

	public List<HsPriceLimitApply> findAuditListByTasks(HsPriceLimitApply hsApply, String status) {

		Page<HsPriceLimitApply> page = findAuditPageByTasks(hsApply, status);
		return page.getList();
	}

	public Page<HsPriceLimitApply> findAuditPageByTasks(HsPriceLimitApply hsApply, String status) {

		String[] flowStatuss = new String[]{
				HsPriceLimitApply.APPLY_STATUS_DRAFT, // "限价房申请";//0
				HsPriceLimitApply.APPLY_STATUS_AUDIT_ORGHAND_FIRST, // "经办审核";//1
				HsPriceLimitApply.APPLY_STATUS_AUDIT_ORGOFFICE_FIRST, // "处室领导审核";//2
				HsPriceLimitApply.APPLY_STATUS_AUDIT_LEADER, // "分管领导审核";//2
				//HsPriceLimitApply.APPLY_STATUS_AUDIT_SHOW_PUBLIC, // "网上公示";//4
				HsPriceLimitApply.APPLY_STATUS_AUDIT_QUALIFICATION, // "资格确认";//5
				HsPriceLimitApply.APPLY_STATUS_AUDIT_CONFIRMATION, // "接收确认单";//6
				HsPriceLimitApply.APPLY_STATUS_AUDIT_CONTRACT // "签订购房合同";//7
		};
		return this.findAuditPageByTask(hsApply, "", flowStatuss, status);
	}

	public Page<BpmTask> findBpmTaskPage(String flowStatus, String[] flowStatuss, String status){

		HsBpmTask params = new HsBpmTask();
		if (StringUtils.isNotBlank(status)) {
			params.setStatus(status);
		}
		if(!UserUtils.getUser().isAdmin()) {// 是管理员不设置usercode
			params.setUserCode(params.currentUser().getUserCode());
		}
		params.getProcIns().setFormKey("pricelimit_apply");//限价房申请key
		if (StringUtils.isNotBlank(flowStatus)) {
			params.setName(flowStatus);
		}
		if (flowStatuss != null && flowStatuss.length > 0) {
			params.setNames(Arrays.stream(flowStatuss).collect(Collectors.toList()));
		}
		return this.bpmTaskService.findTaskPage(params);
	}

	public Page<HsPriceLimitApply> findAuditPageByTaskOld(HsPriceLimitApply hsApply, String flowStatus, String[] flowStatuss, String status){

		if(hsApply.getFlowStatus() != null && !"".equals(hsApply.getFlowStatus())){
			flowStatus = hsApply.getFlowStatus();
			flowStatuss = null;
		}
		Page<BpmTask> myHsTaskPage = findBpmTaskPage(flowStatus, flowStatuss, status);

		Page<HsPriceLimitApply> page = hsApply.getPage();
		//获取所有待办任务的申请单id
		List<String> hsIds = new ArrayList<>();
		myHsTaskPage.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		hsApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
		if (!hsIds.isEmpty()) {
			page = this.findPage(hsApply);
			page.getList().forEach(k -> this.setTaskInfo(k,k.getId(),myHsTaskPage));
		}
		return page;
	}
	public Page<HsPriceLimitApply> findAuditPageByTask(HsPriceLimitApply hsApply, String flowStatus, String[] flowStatuss, String status){

		if(hsApply.getFlowStatus() != null && !"".equals(hsApply.getFlowStatus())){
			flowStatus = hsApply.getFlowStatus();
		}
		if(flowStatus != null && !"".equals(flowStatus)){
			flowStatuss = new String[]{flowStatus};
		}
		return commonBpmService.findTaskList(flowStatuss, "pricelimit_apply", hsApply, status);
	}

	private void setTaskInfo(HsPriceLimitApply k, String id, Page<BpmTask> myHsTaskPage) {
		for (BpmTask bpmTask : myHsTaskPage.getList()) {
			if (bpmTask.getProcIns().getBizKey().equals(id)) {

				k.setFlowStatus(bpmTask.getName());
				return;
			}
		}
	}

    public Page<HsPriceLimitApply> findMztPage(HsPriceLimitApply hsApply) {
		BpmTask params = new BpmTask();
		hsApply.setCreateBy(params.currentUser().getUserCode());
		hsApply.setStatus(HsPriceLimitApply.STATUS_DRAFT);
		Page<HsPriceLimitApply> hsApplyPage =  this.findPage(hsApply);
		return hsApplyPage;
    }

	public Page<HsPriceLimitApply> findOnlyArchivesPage(HsPriceLimitApply hsApply) {

		hsApply.sqlMap().getWhere().and("status", QueryType.IN, new String[]{"88"});
		hsApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsPriceLimitApply> hsApplyPage = this.findPage(hsApply);

		List<HsPriceLimitApply> lstApply = hsApplyPage.getList();
		for(HsPriceLimitApply apply : lstApply){
			if(apply.getStatus().equals("88")){
				apply.setFlowStatus("配售完成");
			}
		}
		return hsApplyPage;
	}

	public Page<HsPriceLimitApply> findArchivesPage(HsPriceLimitApply hsApply) {

		String officeCode = hsApply.getOfficeCode();
        if(officeCode != null && !"".equals(officeCode)) {
			Office office = new Office();
			office.setOfficeCode(officeCode);
			office = officeService.get(office);
			String officeParentCodes = office.getParentCodes();

			Consumer<QueryWhere> nestQueryTypeConsumer = (queryWhere) -> {
				queryWhere.or("office.parent_codes", QueryType.LIKE, officeParentCodes+officeCode+",%").or("office.office_code", QueryType.EQ, officeCode);
			};
			hsApply.sqlMap().getWhere().and(nestQueryTypeConsumer);
		}
		addDataScopeFilter(hsApply);

		if(hsApply.getFlowStatus() != null && !hsApply.getFlowStatus().equals("")) {
			if(hsApply.getFlowStatus().equals("配售完成")){
				return findOnlyArchivesPage(hsApply);
			}
			else{
				return findAuditPageByTask(hsApply, hsApply.getFlowStatus(), null, BpmTask.STATUS_UNFINISHED);
			}
		}

		hsApply.sqlMap().getWhere().and("status", QueryType.IN, new String[]{"4","88"});
		hsApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsPriceLimitApply> hsApplyPage = this.findPage(hsApply);

		Page<BpmTask> myHsTaskPage = findBpmTaskPage("", null, BpmTask.STATUS_UNFINISHED);

		List<HsPriceLimitApply> lstApply = hsApplyPage.getList();
		for(HsPriceLimitApply apply : lstApply){
			if(apply.getStatus().equals("88")){
				apply.setFlowStatus("配售完成");
			}
			else{
				setTaskInfo(apply, apply.getId(), myHsTaskPage);
			}
		}

		return hsApplyPage;
	}

	public Page<HsPriceLimitApply> findPublicPage(HsPriceLimitApply hsApply) {

		hsApply.sqlMap().getWhere().and("public_status", QueryType.NOT_IN, new String[]{"2"});
		return findAuditPageByTask(hsApply, HsPriceLimitApply.APPLY_STATUS_AUDIT_SHOW_PUBLIC, null, BpmTask.STATUS_UNFINISHED);
	}

	private void putMap(Map<String, String> map, String key, String val){
		map.put(key, String.format(" %s ", val));
	}

	List<HsPriceLimitApplyer> getApplyerList(HsPriceLimitApply hsPriceLimitApply, String role){
		List<HsPriceLimitApplyer> applyerList = new ArrayList<>();

		for(HsPriceLimitApplyer applyer : hsPriceLimitApply.getHsPriceLimitApplyerList()){
			if(applyer.getApplyRole().equals(role)){

				applyerList.add(applyer);
			}
		}
		return applyerList;
	}

	public void getSM4(HsPriceLimitApply hsApply, HttpServletResponse response) {

		apiHjptService.getSM4Key();
	}
	public void createExamine(HsPriceLimitApply hsApply, HttpServletResponse response) {

		if(hsApply.getHouseId() == null || hsApply.getHouseId().equals("")){
			throw new ServiceException(text("没有选择房源！"));
		}

		// 生成确认单，并且电子签章
		Map<String, String> map = new HashMap<>();
		// 购房人
		putMap(map,"main_name", hsApply.getMainApplyer().getName());
		putMap(map,"main_phone", hsApply.getMainApplyer().getPhone());
		putMap(map,"main_rank", (hsApply.getMainApplyer().getWorkLevel()!=null)?hsApply.getMainApplyer().getWorkLevel():"");
		// 配偶
		putMap(map,"spouse_name", "");
		putMap(map,"spouse_phone", "");
		putMap(map,"spouse_rank", "");
		List<HsPriceLimitApplyer> lstApplyer = getApplyerList(hsApply, "1");	// 配偶
		if(lstApplyer.size()>0) {
			HsPriceLimitApplyer hsPriceLimitApplyer = lstApplyer.get(0);
			putMap(map,"spouse_name", hsPriceLimitApplyer.getName());
			putMap(map,"spouse_phone", hsPriceLimitApplyer.getPhone());
			putMap(map,"spouse_rank", (hsPriceLimitApplyer.getWorkLevel()!=null)?hsPriceLimitApplyer.getWorkLevel():"");
		}

		//putMap(map,"house_level", "");			// 购房标准
		if(hsApply.getHouseInfo() == null){

			HsQwPublicRentalHouse house = new HsQwPublicRentalHouse();
			house.setId(hsApply.getHouseId());
			house.sqlMap().getWhere().disableAutoAddStatusWhere();
			house = hsQwPublicRentalHouseService.get(house);
			if(house == null){
				throw new ServiceException(text("获取房源信息失败！"));
			}
			hsApply.setHouse(house);
			hsApply.setHouseInfo(house.getSimpleInfo());
		}
		putMap(map,"house_info", hsApply.getHouseInfo());		// 房屋座落及单元号

		Resource resource = resourceLoader.getResource("classpath:file.template/PriceLimitConfirm.docx");
		if (!resource.exists()) {
			return;
		}
		ServletOutputStream outputStream = null;
		try {
			response.setCharacterEncoding("utf-8");
			response.setContentType("application/x-download");
			String fileName = "选房资格确认单"+ DateUtils.getDate("yyyyMMddHHmmss")+".docx";
			fileName = new String(fileName.getBytes(), "ISO-8859-1");
			response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(fileName)));
			outputStream = response.getOutputStream();

		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
		ConfigureBuilder configureBuilder = Configure.builder();
		Configure config = configureBuilder.build();

		try (InputStream templateStream = resource.getInputStream();
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
			template.render(map);
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	public void flushTaskStatus(HsPriceLimitApply hsApply) {
		if (hsApply.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		hsApply = this.get(hsApply.getId());
		if (hsApply == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		HsBpmTask params = new HsBpmTask();
		params.setStatus(BpmTask.STATUS_UNFINISHED);
		params.getProcIns().setFormKey("pricelimit_apply");
		params.getProcIns().setBizKey(hsApply.getId());

		Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);

		BpmTask bpmTask = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
		if (bpmTask != null) {

			String activityId = bpmTask.getActivityId();
			activityId = activityId.substring(Math.max(0, activityId.length() - 3));
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				int activityIdInt = Integer.parseInt(activityId);

				if(activityIdInt <= 20){// 跳到网上公示环节之前
					this.updatePublicStatus(hsApply, "0");
				}
			}

		}
	}

	public void importTemplate(HttpServletResponse response){
		HsPriceLimitApply hsPriceLimitExcel = new HsPriceLimitApply();
		List<HsPriceLimitApply> list = ListUtils.newArrayList(hsPriceLimitExcel);
		String fileName = "限价房导入房源模板.xlsx";
		try(ExcelExport ee = new ExcelExport("限价房选房结果导入模板(身份证号单元格式请设置成文本格式)", HsPriceLimitImport.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	public List<HsQwPublicRentalHouse> findHouseList(HsPriceLimitImport hsImp){

		HsQwPublicRentalHouse house = new HsQwPublicRentalHouse();
		house.setBuildingNum(hsImp.getBuildingNum());
		house.setFloor(Long.valueOf(hsImp.getFloor()));
		house.setUnitNum(hsImp.getUnitNum());
		house.setHouseNum(hsImp.getHouseNum());
		house.sqlMap().getWhere().and("o.address", QueryType.EQ, hsImp.getAddress());

		return hsQwPublicRentalHouseService.findList(house);
	}

	public List<HsPriceLimitApply> findApplyList(HsPriceLimitImport hsImp){
		HsPriceLimitApply hsApply = new HsPriceLimitApply();
		hsApply.sqlMap().getWhere().and("o.name", QueryType.EQ, hsImp.getName());
		hsApply.sqlMap().getWhere().and("o.id_num", QueryType.EQ, hsImp.getIdNum());
		return findList(hsApply);
	}

	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){

			List<HsPriceLimitImport> list = ei.getDataList(HsPriceLimitImport.class);
			for (HsPriceLimitImport hsApplyIm : list) {
				try{
					ValidatorUtils.validateWithException(hsApplyIm);

					List<HsQwPublicRentalHouse> lstHouse = findHouseList(hsApplyIm);
					if(lstHouse.size() == 0){
						throw new ServiceException(text("没有匹配到房源！"));
					}
					if(lstHouse.size() > 1){
						throw new ServiceException(text("房源存在重复！"));
					}
					HsQwPublicRentalHouse house = lstHouse.get(0);

					if(house.getId() == null){
						throw new ServiceException(text("房源编号不能为空！"));
					}

					List<HsPriceLimitApply> lstApply = findApplyList(hsApplyIm);
					if(lstApply.size() == 0){
						throw new ServiceException(text("没有匹配到申请人！"));
					}
					if(lstApply.size() > 1){
						throw new ServiceException(text("房源存在存在重复！"));
					}

					HsPriceLimitApply hsApply = lstApply.get(0);
					if(hsApply == null) {
						throw new ServiceException(text("申请编号不存在，输入错误！"));
					}
					HsPriceLimitApply hsApplyGet = new HsPriceLimitApply();
					hsApplyGet.setId(hsApply.getId());
					hsApplyGet = this.get(hsApplyGet);
					if(hsApplyGet.getFlowStatus() == null && !hsApplyGet.getFlowStatus().equals(HsPriceLimitApply.APPLY_STATUS_AUDIT_QUALIFICATION)){
						throw new ServiceException(text("该申请还没有到资格确认审批状态"));
					}

					if(!checkHouseId(hsApply, house.getId())){
						throw new ServiceException(text("房源选择错误，已经被占用！"));
					}

					// 如果之前已存在，修改之前的房源 id，，  或者报异常
					if(hsApply.getHouseId() != null && !hsApply.getHouseId().isEmpty() && !hsApply.getHouseId().equals(house.getId())){
						//updateHouseStatus(hsApply.getHouseId(), "2");//未售
						throw new ServiceException(text("房源选择错误，该申请人已经选择过房源！"));
					}
					hsApply.setHouseId(house.getId());
					this.updateHouseStatus(hsApply.getHouseId(), "3");//已售
					super.save(hsApply);
					successNum++;
					successMsg.append("<br/>" + successNum + "、申请人 " + hsApplyIm.getName() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、申请人 " + hsApplyIm.getName() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
}