package com.hsobs.hs.modules.hsqwapplybureaucheck.web;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.bureau.service.HsQwApplyBureauService;
import com.hsobs.hs.modules.hsqwapplybureaucheck.service.HsQwApplyBureauTask;
import com.jeesite.common.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.hsqwapplybureaucheck.entity.HsQwApplyBureauCheck;
import com.hsobs.hs.modules.hsqwapplybureaucheck.service.HsQwApplyBureauCheckService;
import com.jeesite.modules.msg.entity.content.PcMsgContent;
import com.jeesite.modules.msg.utils.MsgPushUtils;
import com.jeesite.modules.sys.utils.UserUtils;
/**
 * 局直公房智能核验Controller
 * <AUTHOR>
 * @version 2025-05-03
 */
@Controller
@RequestMapping(value = "${adminPath}/hsqwapplybureaucheck/")
public class HsQwApplyBureauCheckController extends BaseController {

	@Autowired
	private HsQwApplyBureauCheckService hsQwApplyBureauCheckService;

	@Autowired
	private HsQwApplyBureauService hsQwApplyBureauService;

	@Autowired
	private HsQwApplyBureauTask hsQwApplyBureauTask;

	// 注意：以下外部API服务需要在实际项目中实现
	// @Autowired
	// private RealEstateApiService realEstateApiService;
	//
	// @Autowired
	// private BureauVerificationApiService bureauVerificationApiService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyBureauCheck get(String id, boolean isNewRecord) {
		return hsQwApplyBureauCheckService.get(id, isNewRecord);
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("hsqwapplybureaucheck::view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyBureauCheck hsQwApplyBureauCheck, Model model) {
		model.addAttribute("hsQwApplyBureauCheck", hsQwApplyBureauCheck);
		return "modules/hsqwapplybureaucheck/hsQwApplyBureauCheckList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("hsqwapplybureaucheck::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyBureauCheck> listData(HsQwApplyBureauCheck hsQwApplyBureauCheck, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyBureauCheck.setPage(new Page<>(request, response));
		Page<HsQwApplyBureauCheck> page = hsQwApplyBureauCheckService.findPage(hsQwApplyBureauCheck);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("hsqwapplybureaucheck::view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyBureauCheck hsQwApplyBureauCheck, Model model) {
		model.addAttribute("hsQwApplyBureauCheck", hsQwApplyBureauCheck);
		return "modules/hsqwapplybureaucheck/hsQwApplyBureauCheckForm";
	}

	/**
	 * 查看核验详情
	 */
	@RequiresPermissions("hsqwapplybureaucheck::view")
	@RequestMapping(value = "view")
	public String view(HsQwApplyBureauCheck hsQwApplyBureauCheck, Model model) {
		// 获取申请单信息
		HsQwApplyBureau bureau = hsQwApplyBureauService.get(hsQwApplyBureauCheck.getBureauId());
		model.addAttribute("bureau", bureau);
		model.addAttribute("check", hsQwApplyBureauCheck);
		return "modules/hsqwapplybureaucheck/hsQwApplyBureauCheckView";
	}

	/**
	 * 复核页面
	 */
	@RequiresPermissions("hsqwapplybureaucheck::edit")
	@RequestMapping(value = "review")
	public String review(HsQwApplyBureauCheck hsQwApplyBureauCheck, Model model) {
		// 获取申请单信息
		HsQwApplyBureau bureau = hsQwApplyBureauService.get(hsQwApplyBureauCheck.getBureauId());
		model.addAttribute("bureau", bureau);
		model.addAttribute("check", hsQwApplyBureauCheck);
		return "modules/hsqwapplybureaucheck/hsQwApplyBureauCheckReview";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("hsqwapplybureaucheck::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		hsQwApplyBureauCheckService.save(hsQwApplyBureauCheck);
		return renderResult(Global.TRUE, text("保存公房房源智能核验成功！"));
	}

	/**
	 * 保存复核结果
	 */
	@RequiresPermissions("hsqwapplybureaucheck::edit")
	@PostMapping(value = "saveReview")
	@ResponseBody
	public String saveReview(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		// 设置复核信息
		hsQwApplyBureauCheck.setReviewDate(new Date());
		hsQwApplyBureauCheck.setReviewUser(UserUtils.getUser().getUserCode());

		// 如果复核不通过，设置清退信息
		if ("3".equals(hsQwApplyBureauCheck.getReviewStatus())) {
			hsQwApplyBureauCheck.setClearanceStatus("1"); // 1-已通知

			// 如果未设置清退时间，默认设置为30天后
			if (hsQwApplyBureauCheck.getClearanceDate() == null) {
				hsQwApplyBureauCheck.setClearanceDate(new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000));
			}
		}

		hsQwApplyBureauCheckService.save(hsQwApplyBureauCheck);
		return renderResult(Global.TRUE, text("保存复核结果成功"));
	}

	/**
	 * 清退完成
	 */
	@RequiresPermissions("hsqwapplybureaucheck::edit")
	@PostMapping(value = "completeClearance")
	@ResponseBody
	public String completeClearance(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		// 设置清退完成信息
		hsQwApplyBureauCheck.setClearanceStatus("2"); // 2-已清退
		hsQwApplyBureauCheck.setActualClearanceDate(new Date());

		hsQwApplyBureauCheckService.save(hsQwApplyBureauCheck);
		return renderResult(Global.TRUE, text("清退完成"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("hsqwapplybureaucheck::view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwApplyBureauCheck hsQwApplyBureauCheck, HttpServletResponse response) {
		List<HsQwApplyBureauCheck> list = hsQwApplyBureauCheckService.findList(hsQwApplyBureauCheck);
		String fileName = "局直公房智能核验" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("局直公房智能核验", HsQwApplyBureauCheck.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("hsqwapplybureaucheck::view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		HsQwApplyBureauCheck hsQwApplyBureauCheck = new HsQwApplyBureauCheck();
		List<HsQwApplyBureauCheck> list = ListUtils.newArrayList(hsQwApplyBureauCheck);
		String fileName = "局直公房智能核验模板.xlsx";
		try(ExcelExport ee = new ExcelExport("局直公房智能核验", HsQwApplyBureauCheck.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("hsqwapplybureaucheck::edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = hsQwApplyBureauCheckService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("hsqwapplybureaucheck::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		hsQwApplyBureauCheckService.delete(hsQwApplyBureauCheck);
		return renderResult(Global.TRUE, text("删除公房房源智能核验成功！"));
	}

	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("hsqwapplybureaucheck::view")
	@RequestMapping(value = "hsQwApplyBureauCheckSelect")
	public String hsQwApplyBureauCheckSelect(HsQwApplyBureauCheck hsQwApplyBureauCheck, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwApplyBureauCheck", hsQwApplyBureauCheck);
		return "modules/hsqwapplybureaucheck/hsQwApplyBureauCheckSelect";
	}

	/**
	 * 手动执行核验
	 */
	@RequiresPermissions("hsqwapplybureaucheck::edit")
	@PostMapping(value = "executeVerification")
	@ResponseBody
	public Map<String, Object> executeVerification() {
		Map<String, Object> result = new HashMap<>();

		try {
			// 执行核验任务
			hsQwApplyBureauTask.manualExecuteVerification();

			result.put("success", true);
			result.put("message", "智能核验任务已执行");
		} catch (Exception e) {
			logger.error("执行智能核验任务失败", e);
			result.put("success", false);
			result.put("message", "执行智能核验任务失败：" + e.getMessage());
		}

		return result;
	}

	/**
	 * 手动核验单个申请
	 */
	@RequiresPermissions("hsqwapplybureaucheck::edit")
	@PostMapping(value = "verifyApplication")
	@ResponseBody
	public Map<String, Object> verifyApplication(String bureauId) {
		Map<String, Object> result = new HashMap<>();

		try {
			// 获取申请信息
			HsQwApplyBureau bureau = hsQwApplyBureauService.get(bureauId);
			if (bureau == null) {
				result.put("success", false);
				result.put("message", "未找到指定的申请记录");
				return result;
			}

			// 创建核验记录
			HsQwApplyBureauCheck check = new HsQwApplyBureauCheck();
			check.setBureauId(bureau.getId());
			check.setCheckDate(new Date());
			check.setCheckType("0"); // 0-厅局公房
			check.setNoticed("0"); // 0-未通知
			check.setReviewStatus("0"); // 0-未复核
			check.setClearanceStatus("0"); // 0-未清退

			// 模拟不动产登记部门API获取不动产信息
			if (bureau.getMainApplyer() != null && StringUtils.isNotBlank(bureau.getMainApplyer().getIdNum())) {
				// 模拟不动产信息
				JSONObject propertyInfo = new JSONObject();
				propertyInfo.put("idCard", bureau.getMainApplyer().getIdNum());
				propertyInfo.put("name", bureau.getMainApplyer().getName());
				propertyInfo.put("hasProperty", false);
				propertyInfo.put("checkTime", new Date());

				check.setPropertyInfo(propertyInfo.toJSONString());
			}

			// 模拟核验服务进行综合核验
			JSONObject verificationResult = new JSONObject();
			verificationResult.put("verified", true);
			verificationResult.put("applyId", bureau.getId());
			verificationResult.put("checkTime", new Date());

			// 添加核验项目
			JSONObject items = new JSONObject();
			items.put("idCardCheck", "pass");
			items.put("propertyCheck", "pass");
			items.put("housingFundCheck", "pass");
			verificationResult.put("items", items);

			// 保存核验详情
			check.setVerifyDetail(verificationResult.toJSONString());

			// 判断核验结果
			boolean isValid = verificationResult.getBooleanValue("verified");

			// 设置核验结果
			check.setCheckResult(isValid ? "0" : "1"); // 0-正常 1-异常

			// 如果核验结果为异常，设置应清退时间为30天后
			if (!isValid) {
				check.setClearanceDate(new Date(System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000));
			}

			// 保存核验记录
			hsQwApplyBureauCheckService.save(check);

			// 更新申请单核验状态
			bureau.setCheckDate(new Date());
			bureau.setAutoCheck(isValid ? "1" : "0"); // 1-有效 0-无效
			hsQwApplyBureauService.update(bureau);

			// 如果核验结果为无效，则发送通知
			if (!isValid) {
				hsQwApplyBureauTask.sendInvalidNotification(bureau, check);
				check.setNoticed("1"); // 1-已通知
				hsQwApplyBureauCheckService.update(check);
			}

			result.put("success", true);
			result.put("valid", isValid);
			result.put("message", "核验完成，结果：" + (isValid ? "有效" : "无效"));
		} catch (Exception e) {
			logger.error("核验申请失败", e);
			result.put("success", false);
			result.put("message", "核验申请失败：" + e.getMessage());
		}

		return result;
	}

	/**
	 * 发送通知给申请人并更新通知状态
	 * @param bureauId 核验单ID
	 * @return 处理结果
	 */
	@RequestMapping(value = "sendNotification")
	@ResponseBody
	public String sendNotification(String id) {
		if (StringUtils.isBlank(id)) {
			return renderResult(Global.FALSE, text("核验单ID不能为空"));
		}
		
		try {
			// 获取核验单信息
			HsQwApplyBureauCheck bureau = hsQwApplyBureauCheckService.get(id);
			if (bureau == null) {
				return renderResult(Global.FALSE, text("核验单不存在"));
			}
			
			// 检查是否已通知
			if ("1".equals(bureau.getNoticed())) {
				return renderResult(Global.FALSE, text("已经通知过申请人，无需重复通知"));
			}
			
			
			String receiveUserCode = bureau.getCreateBy();
			if (StringUtils.isBlank(receiveUserCode)) {
				return renderResult(Global.FALSE, text("申请人用户编码不存在"));
			}
			
			
			// 创建PC消息内容
            PcMsgContent msgContent = new PcMsgContent();
            msgContent.setTitle("【局直公房智能核验】申请无效通知");
            msgContent.setContent("您有一份核验单需要查看，核验单编号：" + bureau.getId());
			
			// 发送消息
			MsgPushUtils.push(msgContent, bureau.getId(), HsQwApplyBureauCheck.class.getSimpleName(), receiveUserCode);
			
			// 更新通知状态为已通知
			bureau.setNoticed("1"); // 1 表示已通知
			hsQwApplyBureauCheckService.save(bureau);
			
			return renderResult(Global.TRUE, text("通知发送成功"));
		} catch (Exception e) {
			logger.error("发送通知失败", e);
			return renderResult(Global.FALSE, text("发送通知失败：") + e.getMessage());
		}
	}
}