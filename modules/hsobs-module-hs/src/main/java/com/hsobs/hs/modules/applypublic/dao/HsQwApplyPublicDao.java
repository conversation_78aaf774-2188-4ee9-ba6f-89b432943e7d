package com.hsobs.hs.modules.applypublic.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.applypublic.entity.HsQwApplyPublic;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * 租赁资格轮候公示复查表DAO接口
 * <AUTHOR>
 * @version 2024-12-05
 */
@MyBatisDao
public interface HsQwApplyPublicDao extends CrudDao<HsQwApplyPublic> {

    String getHouseInfoByIds(@Param("idArray") String[] idArray);

}