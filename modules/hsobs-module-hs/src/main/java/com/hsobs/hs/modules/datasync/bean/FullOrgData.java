package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取全部单位信息响应实体
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FullOrgData implements java.io.Serializable {

    public static final long serialVersionUID = 1L;

    private String orgId;
    private String orgPid;
    private String orgCode;
    private String orgName;
    private String orgType;
    private String code;
    private String administrativeLevel;
    private Integer level;
    private Integer sort;
    private String address;
    private String createTime;
    private String updateTime;
    private String extendField;
    private String socialCreditCode;

}
