package com.hsobs.hs.modules.apply.service.applyCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 房屋置换申请（以小换大）校验
 */
@Service
public class HsQwApplyCheckReplaceUp extends HsQwApplyCheckBase implements HsQwApplyCheck {
    @Autowired
    private HsQwApplyDao hsQwApplyDao;

    @Override
    public String getApplyType() {
        return "2";//承租置换
    }

    @Override
    public void execute(HsQwApply hsQwApply) {
        //黑名单校验
        super.blackCheck(hsQwApply);
        //流程中的申请单校验
        if (!super.processCheck(hsQwApply, new String[]{"0","1","2","4"}, new String[]{"4"}, this.getApplyType(), null)){
            throw new ServiceException("已存在流程中的申请单，请先完成再申请！");
        }

        //流程中的必须有正常状态的申请单
        if (!super.normalCheck(new String[]{"0"}, hsQwApply)){
            throw new ServiceException("申请住房信息置换资格验证失败，不存在已有的申请单！");
        }

        //todo 已入住2.5居室的不可发起申请

    }

}
