package com.hsobs.hs.modules.apply.service.serviceTask;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.blackrule.entity.HsQwApplyerBlackRule;
import com.hsobs.hs.modules.blackrule.service.HsQwApplyerBlackRuleService;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.service.HsQwApplyerBlackService;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.hsobs.hs.modules.clearance.service.HsQwClearanceService;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.utils.BpmUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 申请资格审核-已租赁的审核不符合租赁资格，需要执行退租
 */
@Component
public class HsQwApplyPersonClearServiceTask implements JavaDelegate {
    @Autowired
    private HsQwApplyService hsQwApplyService;
    @Autowired
    private HsQwClearanceService hsQwClearanceService;
    @Override
    public void execute(DelegateExecution delegateExecution) {
        HsQwApply hsQwApply = hsQwApplyService.get(BpmUtils.getBizKey(delegateExecution));
        HsQwApply hsQwApplyOld = hsQwApplyService.get(hsQwApply.getApplyedId());
        this.clearanceApply(hsQwApplyOld);
    }

    private void clearanceApply(HsQwApply hsQwApply){
        HsQwClearance hsQwClearance = new HsQwClearance();
        hsQwClearance.setApplyId(hsQwApply.getId());
        hsQwClearance.setCompactId(hsQwApply.getCompact().getId());
        hsQwClearance.setApplyerId(hsQwApply.getMainApplyer().getId());
        hsQwClearance.setType("4");//核查清退
        hsQwClearance.setReason("个人申请资格核查执行清退");
        hsQwClearance.setBlackUser("0");
        hsQwClearanceService.save(hsQwClearance);
//        BpmUtils.start(hsQwClearance, "rent_clear", null, null);
    }
}
