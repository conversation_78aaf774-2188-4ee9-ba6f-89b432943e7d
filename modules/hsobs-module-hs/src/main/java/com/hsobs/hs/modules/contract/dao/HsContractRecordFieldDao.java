package com.hsobs.hs.modules.contract.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.contract.entity.HsContractRecordField;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

/**
 * 合同字段DAO接口
 * <AUTHOR>
 * @version 2025-01-22
 */
@MyBatisDao
public interface HsContractRecordFieldDao extends CrudDao<HsContractRecordField> {

    @Delete("delete from hs_contract_record_field where record_id = #{recordId}")
    void deleteByRecordId(@Param("recordId") String recordId);
}