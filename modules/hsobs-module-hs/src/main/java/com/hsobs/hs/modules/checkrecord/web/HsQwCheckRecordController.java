package com.hsobs.hs.modules.checkrecord.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.managementcheck.entity.HsQwManagementCheck;
import com.hsobs.hs.modules.managementcheck.service.HsQwManagementCheckService;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.checkrecord.entity.HsQwCheckRecord;
import com.hsobs.hs.modules.checkrecord.service.HsQwCheckRecordService;

/**
 * 租赁资格核查台账表Controller
 * <AUTHOR>
 * @version 2025-02-15
 */
@Controller
@RequestMapping(value = "${adminPath}/checkrecord/hsQwCheckRecord")
public class HsQwCheckRecordController extends BaseController {

	@Autowired
	private HsQwCheckRecordService hsQwCheckRecordService;

	@Autowired
	private HsQwManagementCheckService hsQwManagementCheckService;
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwCheckRecord get(String id, boolean isNewRecord) {
		return hsQwCheckRecordService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		return "modules/checkrecord/hsQwCheckRecordList";
	}


	/**
	 * 查询列表-审批列表
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = {"listAudit", ""})
	public String listAudit(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		return "modules/checkrecord/hsQwCheckRecordListAudit";
	}

	/**
	 * 查询列表-审批已办列表
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = {"listAuditDone", ""})
	public String listAuditDone(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		return "modules/checkrecord/hsQwCheckRecordListDone";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwCheckRecord> listData(HsQwCheckRecord hsQwCheckRecord, HttpServletRequest request, HttpServletResponse response) {
		hsQwCheckRecord.setPage(new Page<>(request, response));
		Page<HsQwCheckRecord> page = hsQwCheckRecordService.findPage(hsQwCheckRecord);
		return page;
	}


	/**
	 * 查询列表数据-待办
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "listAuditData")
	@ResponseBody
	public Page<HsQwCheckRecord> listAuditData(HsQwCheckRecord hsQwCheckRecord, HttpServletRequest request, HttpServletResponse response) {
		hsQwCheckRecord.setPage(new Page<>(request, response));
		Page<HsQwCheckRecord> page = hsQwCheckRecordService.findPageByTask(hsQwCheckRecord, null, "1");
		return page;
	}


	/**
	 * 查询列表数据-已办
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "listAuditDoneData")
	@ResponseBody
	public Page<HsQwCheckRecord> listAuditDoneData(HsQwCheckRecord hsQwCheckRecord, HttpServletRequest request, HttpServletResponse response) {
		hsQwCheckRecord.setPage(new Page<>(request, response));
		Page<HsQwCheckRecord> page = hsQwCheckRecordService.findPageByTask(hsQwCheckRecord, null, "2");
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "form")
	public String form(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		return "modules/checkrecord/hsQwCheckRecordForm";
	}


	/**
	 * 查看编辑表单-省直单位核查表
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "formApply")
	public String formApply(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("recordId", hsQwCheckRecord.getId());
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		model.addAttribute("hsQwApply", hsQwCheckRecordService.getApplyForm(hsQwCheckRecord));
		return "modules/checkrecord/hsQwCheckRecordApplyForm";
	}

	/**
	 * 查看编辑表单-房管机构核查表
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "formHouse")
	public String formHouse(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("recordId", hsQwCheckRecord.getId());
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheckService.getHouseCheckDetail(hsQwCheckRecord));
		return "modules/checkrecord/hsQwCheckRecordHouseCheckForm";
	}

	/**
	 * 查看编辑表单-机构经办核查
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "formCheck")
	public String formCheck(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		model.addAttribute("hsQwApply", hsQwCheckRecordService.getApplyForm(hsQwCheckRecord));
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheckService.getHouseCheckDetail(hsQwCheckRecord));
		return "modules/checkrecord/hsQwCheckRecordFormCheck";
	}

	/**
	 * 查看编辑表单-用户续签确认
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "formConfirm")
	public String formConfirm(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		model.addAttribute("hsQwApply", hsQwCheckRecordService.getApplyForm(hsQwCheckRecord));
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheckService.getHouseCheckDetail(hsQwCheckRecord));
		return "modules/checkrecord/hsQwCheckRecordFormConfirm";
	}

	/**
	 * 查看编辑表单-机关经办合同续签
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "formCompact")
	public String formCompact(HsQwCheckRecord hsQwCheckRecord, Model model) {
		hsQwCheckRecord.setCompact(null);//
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		model.addAttribute("hsQwApply", hsQwCheckRecordService.getApplyForm(hsQwCheckRecord));
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheckService.getHouseCheckDetail(hsQwCheckRecord));
		return "modules/checkrecord/hsQwCheckRecordFormCompact";
	}

	/**
	 * 查看编辑表单-用户核查结果确认
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "formUserCheck")
	public String formUserCheck(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		model.addAttribute("hsQwApply", hsQwCheckRecordService.getApplyForm(hsQwCheckRecord));
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheckService.getHouseCheckDetail(hsQwCheckRecord));
		return "modules/checkrecord/hsQwCheckRecordUserCheckForm";
	}


	/**
	 * 查看编辑表单-申诉知悉
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "formComplaint")
	public String formComplaint(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		model.addAttribute("hsQwApply", hsQwCheckRecordService.getApplyForm(hsQwCheckRecord));
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheckService.getHouseCheckDetail(hsQwCheckRecord));
		return "modules/checkrecord/hsQwCheckRecordComplaintConfirmForm";
	}

	/**
	 * 查看编辑表单-所有信息查看
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "formAll")
	public String formAll(HsQwCheckRecord hsQwCheckRecord, Model model) {
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		model.addAttribute("hsQwApply", hsQwCheckRecordService.getApplyForm(hsQwCheckRecord));
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheckService.getHouseCheckDetail(hsQwCheckRecord));
		return "modules/checkrecord/hsQwCheckRecordAllForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwCheckRecord hsQwCheckRecord) {
		hsQwCheckRecordService.save(hsQwCheckRecord);
		return renderResult(Global.TRUE, text("保存租赁资格核查台账表成功！"));
	}

	/**
	 * 保存数据-更新续签状态、是否违规
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:edit")
	@PostMapping(value = "update")
	@ResponseBody
	public String update(HsQwCheckRecord hsQwCheckRecord) {
		HsQwCheckRecord checkRecord = hsQwCheckRecordService.get(hsQwCheckRecord.getId());
		if (StringUtils.isNotBlank(hsQwCheckRecord.getViolation())){
			checkRecord.setViolation(hsQwCheckRecord.getViolation());
		}
		if (StringUtils.isNotBlank(hsQwCheckRecord.getRenewal())){
			checkRecord.setRenewal(hsQwCheckRecord.getRenewal());
		}
		if (StringUtils.isNotBlank(hsQwCheckRecord.getComplaint())){
			checkRecord.setComplaint(hsQwCheckRecord.getComplaint());
		}
		if (StringUtils.isNotBlank(hsQwCheckRecord.getReasonable())){
			checkRecord.setReasonable(hsQwCheckRecord.getReasonable());
		}
		checkRecord.setBpm(hsQwCheckRecord.getBpm());
		hsQwCheckRecordService.save(checkRecord);
		return renderResult(Global.TRUE, text("保存租赁资格核查台账表成功！"));
	}


	/**
	 * 保存数据-更新合同信息
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:edit")
	@PostMapping(value = "updateCompact")
	@ResponseBody
	public String updateCompact(HsQwCheckRecord hsQwCheckRecord) {
		HsQwCheckRecord checkRecord = hsQwCheckRecordService.get(hsQwCheckRecord.getId());
		checkRecord.setCompact(hsQwCheckRecord.getCompact());
		checkRecord.setBpm(hsQwCheckRecord.getBpm());
		hsQwCheckRecordService.saveByCompact(checkRecord);
		return renderResult(Global.TRUE, text("保存租赁资格核查台账表成功！"));
	}

	/**
	 * 保存数据-省直单位提交保存数据
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:edit")
	@PostMapping(value = "checkSave")
	@ResponseBody
	public String checkSave(@Validated HsQwApply hsQwApply, HsQwCheckRecord hsQwCheckRecord) {
		hsQwCheckRecordService.save(hsQwApply, hsQwCheckRecord);
		return renderResult(Global.TRUE, text("省直单位保存租赁资格核查成功！"));
	}

	/**
	 * 保存数据-房管机构提交保存数据
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:edit")
	@PostMapping(value = "houseSave")
	@ResponseBody
	public String houseSave(@Validated HsQwManagementCheck hsQwManagementCheck) {
		hsQwCheckRecordService.save(hsQwManagementCheck);
		return renderResult(Global.TRUE, text("房管机构保存租赁资格核查成功！"));
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwCheckRecord hsQwCheckRecord) {
		if (!HsQwCheckRecord.STATUS_DRAFT.equals(hsQwCheckRecord.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		hsQwCheckRecordService.delete(hsQwCheckRecord);
		return renderResult(Global.TRUE, text("删除租赁资格核查台账表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "hsQwCheckRecordSelect")
	public String hsQwCheckRecordSelect(HsQwCheckRecord hsQwCheckRecord, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		return "modules/checkrecord/hsQwCheckRecordSelect";
	}

	/**
	 * 选择房源对话框
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "houseSelect")
	public String houseSelect(HsQwPublicRentalHouse hsQwPublicRentlHouse, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
			model.addAttribute("selectData", selectDataJson);
		}
		hsQwPublicRentlHouse.setHouseStatus("1");
		model.addAttribute("hsQwPublicReplacealHouse", hsQwPublicRentlHouse);
		return "modules/house/hsQwPublicRentalHouseListSelectCheckRecord";
	}

	/**
	 * 资格核验表单申请页面
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:edit")
	@RequestMapping(value = "recordForm")
	public String recordForm(HsQwCheckRecord hsQwCheckRecord, Model model) {
		hsQwCheckRecord = new HsQwCheckRecord();
		model.addAttribute("hsQwCheckRecord", hsQwCheckRecord);
		return "modules/checkrecord/hsQwCheckRecordApplySelectForm";
	}

	/**
	 * 提交资格核验清单
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:edit")
	@RequestMapping(value = "recordSave")
	@ResponseBody
	public String recordSave(String houseIdStr, String objectIdStr, Date checkDate, String violationType) {
		hsQwCheckRecordService.recordSave(houseIdStr, objectIdStr, checkDate, violationType);
		return renderResult(Global.TRUE, text("提交资格核查清单成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("checkrecord:hsQwCheckRecord:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwCheckRecord hsQwCheckRecord, HttpServletResponse response) {
		List<HsQwCheckRecord> list = hsQwCheckRecordService.findList(hsQwCheckRecord);
		String fileName = "租赁资格核查台账表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("租赁资格核查台账表", HsQwCheckRecord.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}
