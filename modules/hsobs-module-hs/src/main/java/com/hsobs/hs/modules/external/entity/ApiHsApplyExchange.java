package com.hsobs.hs.modules.external.entity;

import javax.validation.constraints.NotBlank;
import java.util.List;

public class ApiHsApplyExchange extends ApiBody {
    
    @NotBlank(message = "申请人名称不能为空")
    private String sqrmc;
    
    @NotBlank(message = "手机号不能为空")
    private String sjh;
    
    @NotBlank(message = "变更理由不能为空")
    private String bgly;
    
    private List<ApiFile> fileList;
    
    public String getSqrmc() {
        return sqrmc;
    }
    
    public void setSqrmc(String sqrmc) {
        this.sqrmc = sqrmc;
    }
    
    public String getSjh() {
        return sjh;
    }
    
    public void setSjh(String sjh) {
        this.sjh = sjh;
    }
    
    public String getBgly() {
        return bgly;
    }
    
    public void setBgly(String bgly) {
        this.bgly = bgly;
    }
    
    public List<ApiFile> getFileList() {
        return fileList;
    }
    
    public void setFileList(List<ApiFile> fileList) {
        this.fileList = fileList;
    }
}