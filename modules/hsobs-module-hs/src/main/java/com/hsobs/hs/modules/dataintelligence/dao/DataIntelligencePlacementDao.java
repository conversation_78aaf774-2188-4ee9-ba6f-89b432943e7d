package com.hsobs.hs.modules.dataintelligence.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligencePlacement;

import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计DAO接口  公有住房配售统计
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataIntelligencePlacementDao extends CrudDao<DataIntelligencePlacement> {
    // a、d 公有住房/限价房总体情况
    // String sqlTable, String sqlWhere, String sqlOrderBy
    List<Map<String, Object>> countTotalInfo(Map<String, Object> map);

    // b、c、e、f 公有住房/限价房面积、交易金额情况
    List<Map<String, Object>> countAreaInfo(String sqlTable, String sqlWhere, String sqlOrderBy);
}