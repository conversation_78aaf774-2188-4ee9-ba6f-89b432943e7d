package com.hsobs.hs.modules.checkobject.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候物业核验物品表Entity
 * <AUTHOR>
 * @version 2025-02-13
 */
@Table(name="hs_qw_management_check_object", alias="a", label="租赁资格轮候物业核验物品表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="name", attrName="name", label="物品名称", queryType=QueryType.LIKE),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsQwManagementCheckObject extends DataEntity<HsQwManagementCheckObject> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 物品名称

	public HsQwManagementCheckObject() {
		this(null);
	}
	
	public HsQwManagementCheckObject(String id){
		super(id);
	}
	
	@NotBlank(message="物品名称不能为空")
	@Size(min=0, max=64, message="物品名称长度不能超过 64 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
}