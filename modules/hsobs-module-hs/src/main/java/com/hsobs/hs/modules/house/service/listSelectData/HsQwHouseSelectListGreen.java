package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;
import org.springframework.stereotype.Service;

/**
 * 公租房绿色配租房源：选择待配租的、没有在流程中的房源1，进行配租
 */
@Service
public class HsQwHouseSelectListGreen implements HsQwHouseSelectList {
    public String getDataType() {
        return "1";
    }

    public Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse,
            HsQwPublicRentalHouseService hsQwPublicRentalHouseService) {
        hsQwPublicRentalHouse.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_RENTAL);
        hsQwPublicRentalHouse.setIsPublic("1");
        hsQwPublicRentalHouse.setHouseStatus("0");
        hsQwPublicRentalHouse.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwPublicRentalHouse.sqlMap().add("extWhere", " and a.id not in " +
                "(select house_id from hs_qw_apply where " +
                "status = 4 AND HOUSE_ID  IS NOT NULL )");
        return hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
    }
}
