package com.hsobs.hs.modules.pricelimitplan.service;

import java.util.List;

import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.estate.util.EstateUtils;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.pricelimitplan.entity.HsPriceLimitPlan;
import com.hsobs.hs.modules.pricelimitplan.dao.HsPriceLimitPlanDao;

/**
 * 拟定限价房方案Service
 * <AUTHOR>
 * @version 2025-02-13
 */
@Service
public class HsPriceLimitPlanService extends CrudService<HsPriceLimitPlanDao, HsPriceLimitPlan> {
	
	/**
	 * 获取单条数据
	 * @param hsPriceLimitPlan
	 * @return
	 */
	@Override
	public HsPriceLimitPlan get(HsPriceLimitPlan hsPriceLimitPlan) {
		return super.get(hsPriceLimitPlan);
	}
	
	/**
	 * 查询分页数据
	 * @param hsPriceLimitPlan 查询条件
	 * @param hsPriceLimitPlan page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPriceLimitPlan> findPage(HsPriceLimitPlan hsPriceLimitPlan) {
		return super.findPage(hsPriceLimitPlan);
	}
	
	/**
	 * 查询列表数据
	 * @param hsPriceLimitPlan
	 * @return
	 */
	@Override
	public List<HsPriceLimitPlan> findList(HsPriceLimitPlan hsPriceLimitPlan) {
		return super.findList(hsPriceLimitPlan);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsPriceLimitPlan
	 */
	@Override
	@Transactional
	public void save(HsPriceLimitPlan hsPriceLimitPlan) {

		if(hsPriceLimitPlan.getOfficeCode() == null) {
			if (!UserUtils.getUser().isAdmin()) {
				String officeCode = EmpUtils.getCurrentOfficeCode();
				hsPriceLimitPlan.setOfficeCode(officeCode);
			}
		}

		super.save(hsPriceLimitPlan);
	}
	
	/**
	 * 更新状态
	 * @param hsPriceLimitPlan
	 */
	@Override
	@Transactional
	public void updateStatus(HsPriceLimitPlan hsPriceLimitPlan) {
		super.updateStatus(hsPriceLimitPlan);
	}
	
	/**
	 * 删除数据
	 * @param hsPriceLimitPlan
	 */
	@Override
	@Transactional
	public void delete(HsPriceLimitPlan hsPriceLimitPlan) {
		super.delete(hsPriceLimitPlan);
	}
	
}