package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 获取角色的授权资源响应实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleResourceAuthData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String name;
    private String resourceId;
    private String resourcePid;
    private String url;
    private String component;
    private String icon;
    private Integer openWay;
    private Integer hidden;
    private Integer noCache;
    private String code;
    private Integer sort;
    private Integer type;
    private String remark;

}
