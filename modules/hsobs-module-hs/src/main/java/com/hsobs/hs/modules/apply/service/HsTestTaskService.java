package com.hsobs.hs.modules.apply.service;

import com.jeesite.common.beetl.ext.fn.DictUtil;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.utils.DictUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.engine.ProcessEngines;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HsTestTaskService {

    public static void test(ExecutionEntityImpl execution){
        RepositoryService repositoryService = ProcessEngines.getDefaultProcessEngine().getRepositoryService();

        String processDefinitionId = execution.getProcessDefinitionId();
        String currentActivityId = execution.getCurrentActivityId();

        // 1. 获取流程定义模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(currentActivityId);

        // 2. 获取下一条连线（默认只取第一个）
        List<SequenceFlow> outgoingFlows = currentFlowNode.getOutgoingFlows();

        SequenceFlow nextFlow = outgoingFlows.get(0);
        String targetRef = nextFlow.getTargetRef();
        FlowElement nextElement = bpmnModel.getMainProcess().getFlowElement(targetRef);
        if (BpmUtils.isCurrentCmd(execution, "start")){
            System.out.println("启动");
        }
        else if (BpmUtils.isCurrentCmd(execution, "claim")){
            System.out.println("签收");
        }
        else if (BpmUtils.isCurrentCmd(execution, "complete")){
            System.out.println("提交");
        }
        else if (BpmUtils.isCurrentCmd(execution, "back")){
            System.out.println("退回");
        }
        else if (BpmUtils.isCurrentCmd(execution, "turn")){
            System.out.println("转办");
        }
        else if (BpmUtils.isCurrentCmd(execution, "delegate")){
            System.out.println("委托");
        }
        else if (BpmUtils.isCurrentCmd(execution, "stop")){
            System.out.println("终止");
        }
        else if (BpmUtils.isCurrentCmd(execution, "move")){
            System.out.println("自由跳转");
        }
    }
}
