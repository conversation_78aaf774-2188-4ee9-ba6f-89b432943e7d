package com.hsobs.hs.modules.applypublic.web;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applypublic.entity.HsQwApplyPublic;
import com.hsobs.hs.modules.applypublic.service.HsQwApplyPublicService;
import com.hsobs.hs.modules.applypublicdetail.entity.HsQwApplyPublicDetail;
import com.hsobs.hs.modules.applypublichouse.entity.HsQwApplyPublicHouse;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.word.WordExport;
import com.jeesite.common.web.BaseController;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 配租公示复核表Controller
 *
 * <AUTHOR>
 * @version 2024-12-05
 */
@Controller
@RequestMapping(value = "${adminPath}/applypublic/hsQwApplyPublic")
public class HsQwApplyPublicController extends BaseController {

    @Autowired
    private HsQwApplyPublicService hsQwApplyPublicService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public HsQwApplyPublic get(String id, boolean isNewRecord) {
        return hsQwApplyPublicService.get(id, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = { "list", "" })
    public String list(HsQwApplyPublic hsQwApplyPublic, Model model) {
        model.addAttribute("hsQwApplyPublic", hsQwApplyPublic);
        if (hsQwApplyPublic.getPublicType().equals("0")) {
            return "modules/applypublic/hsQwApplyPublicList";
        } else {
            return "modules/applypublic/hsQwApplyPublicListCheck";
        }
    }

    /**
     * 查询列表-待办信息
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = { "listRent", "" })
    public String listRent(HsQwApplyPublic hsQwApplyPublic, Model model) {
        model.addAttribute("hsQwApplyPublic", hsQwApplyPublic);
        return "modules/applypublic/hsQwApplyPublicRentList";
    }

    /**
     * 查询列表-已办信息
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = { "listRentDone", "" })
    public String listRentDone(HsQwApplyPublic hsQwApplyPublic, Model model) {
        model.addAttribute("hsQwApplyPublic", hsQwApplyPublic);
        return "modules/applypublic/hsQwApplyPublicRentDoneList";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<HsQwApplyPublic> listData(HsQwApplyPublic hsQwApplyPublic, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApplyPublic.setPage(new Page<>(request, response));
        hsQwApplyPublic.sqlMap().getWhere().disableAutoAddStatusWhere();
        Page<HsQwApplyPublic> page = hsQwApplyPublicService.findPage(hsQwApplyPublic);
        return page;
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "listRentData")
    @ResponseBody
    public Page<HsQwApplyPublic> listRentData(HsQwApplyPublic hsQwApplyPublic, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApplyPublic.setPage(new Page<>(request, response));
        hsQwApplyPublic.sqlMap().getWhere().disableAutoAddStatusWhere();
        Page<HsQwApplyPublic> page = hsQwApplyPublicService.findAuditPageByTask(hsQwApplyPublic, "1");
        return page;
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "listRentedData")
    @ResponseBody
    public Page<HsQwApplyPublic> listRentedData(HsQwApplyPublic hsQwApplyPublic, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApplyPublic.setPage(new Page<>(request, response));
        hsQwApplyPublic.sqlMap().getWhere().disableAutoAddStatusWhere();
        Page<HsQwApplyPublic> page = hsQwApplyPublicService.findAuditPageByTask(hsQwApplyPublic, "2");
        return page;
    }

    /**
     * 查询子表数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "hsQwApplyPublicHouseListData")
    @ResponseBody
    public Page<HsQwApplyPublicHouse> subListData(HsQwApplyPublicHouse hsQwApplyPublicHouse, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApplyPublicHouse.setPage(new Page<>(request, response));
        Page<HsQwApplyPublicHouse> page = hsQwApplyPublicService.findSubPage(hsQwApplyPublicHouse);
        return page;
    }

    /**
     * 查询子表数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "hsQwApplyPublicDetailListData")
    @ResponseBody
    public Page<HsQwApplyPublicDetail> subListData(HsQwApplyPublicDetail hsQwApplyPublicDetail,
            HttpServletRequest request, HttpServletResponse response) {
        hsQwApplyPublicDetail.setPage(new Page<>(request, response));
        Page<HsQwApplyPublicDetail> page = hsQwApplyPublicService.findSubPage(hsQwApplyPublicDetail);
        return page;
    }

    /**
     * 查看编辑表单-选择名单
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "form")
    public String form(HsQwApplyPublic hsQwApplyPublic, Model model, String pids) {
        hsQwApplyPublicService.initPublic(hsQwApplyPublic, pids);
        model.addAttribute("hsQwApplyPublic", hsQwApplyPublic);
        return "modules/applypublic/hsQwApplyPublicForm";
    }

    /**
     * 查看编辑表单-配租名单
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "formRent")
    public String formRent(HsQwApplyPublic hsQwApplyPublic, Model model) {
        this.initForm(hsQwApplyPublic, model);
        if (hsQwApplyPublic.getRedirectPage() != null) {
            return "modules/applypublic/" + hsQwApplyPublic.getRedirectPage();
        }
        return "modules/applypublic/hsQwApplyPublicRentForm";
    }

    /**
     * 查看编辑表单-配租名单
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "formRentUpload")
    public String formRentUpload(HsQwApplyPublic hsQwApplyPublic, Model model) {
        this.initForm(hsQwApplyPublic, model);
        return "modules/applypublic/hsQwApplyPublicRentFormUpload";
    }

    private void initForm(HsQwApplyPublic hsQwApplyPublic, Model model) {
        StringBuffer sbIds = new StringBuffer();
        hsQwApplyPublic.getHsQwApplyPublicDetailList().forEach(k -> {
            sbIds.append(k.getApplyId()).append(",");
        });
        String pids = sbIds.toString().substring(0, sbIds.toString().lastIndexOf(","));
        hsQwApplyPublicService.initPublic(hsQwApplyPublic, pids);
        hsQwApplyPublicService.initHouses(hsQwApplyPublic);
        model.addAttribute("hsQwApplyPublic", hsQwApplyPublic);
    }

    /**
     * 查看编辑表单--公示表单
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "formUpload")
    public String formUpload(HsQwApplyPublic hsQwApplyPublic, Model model) {
        this.initForm(hsQwApplyPublic, model);
        return "modules/applypublic/hsQwApplyPublicFormUpload";
    }

    /**
     * 查看编辑表单--公示表单
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:view")
    @RequestMapping(value = "formHouse")
    public String formHouse(HsQwApplyPublic hsQwApplyPublic, Model model) {
        this.initForm(hsQwApplyPublic, model);
        return "modules/applypublic/hsQwApplyPublicRentFormHouse";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated HsQwApplyPublic hsQwApplyPublic) {
        hsQwApplyPublicService.save(hsQwApplyPublic);
        return this.returnMsg("", hsQwApplyPublic.getPublicType());
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:edit")
    @PostMapping(value = "saveRent")
    @ResponseBody
    public String saveRent(@Validated HsQwApplyPublic hsQwApplyPublic) {
        hsQwApplyPublicService.saveRent(hsQwApplyPublic);
        return this.returnMsg("", hsQwApplyPublic.getPublicType());
    }

    /**
     * 保存数据-批量
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:edit")
    @PostMapping(value = "saveRentBatch")
    @ResponseBody
    public String saveRentBatch(String pids) {
        hsQwApplyPublicService.saveRentBatch(pids);
        return renderResult(Global.TRUE, text("一键审批成功"));
    }

    /**
     * 保存数据-执行公示完成
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:edit")
    @PostMapping(value = "savePublic")
    @ResponseBody
    public String savePublic(@Validated HsQwApplyPublic hsQwApplyPublic) {
        hsQwApplyPublicService.save(hsQwApplyPublic);
        return this.returnMsg("", hsQwApplyPublic.getPublicType());
    }

    /**
     * 停用数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:edit")
    @RequestMapping(value = "disable")
    @ResponseBody
    public String disable(HsQwApplyPublic hsQwApplyPublic) {
        hsQwApplyPublic.setStatus("2");
        hsQwApplyPublicService.updateStatus(hsQwApplyPublic);
        return this.returnMsg("停用", hsQwApplyPublic.getPublicType());
    }

    /**
     * 启用数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:edit")
    @RequestMapping(value = "enable")
    @ResponseBody
    public String enable(HsQwApplyPublic hsQwApplyPublic) {
        hsQwApplyPublic.setStatus("0");
        hsQwApplyPublicService.updateStatus(hsQwApplyPublic);
        return this.returnMsg("启用", hsQwApplyPublic.getPublicType());
    }

    /**
     * 启用数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:edit")
    @RequestMapping(value = "publicStatus")
    @ResponseBody
    public String publicStatus(HsQwApplyPublic hsQwApplyPublic) {
        hsQwApplyPublicService.publicStatus(hsQwApplyPublic);
        return renderResult(Global.TRUE, text("发布资格轮候名单成功"));
    }

    /**
     * 启用数据
     */
    @RequiresPermissions("applypublic:hsQwApplyPublic:edit")
    @RequestMapping(value = "test")
    @ResponseBody
    public List<HsQwApplyPublicHouse> test(HsQwApplyPublic hsQwApplyPublic) {
        List<HsQwApplyPublicHouse> list = new ArrayList<>();
        HsQwApplyPublicHouse p1 = new HsQwApplyPublicHouse();
        p1.setStatus("1");
        p1.setPublicId("22");
        p1.setHouseId("111");
        list.add(p1);
        if (true)
            throw new ServiceException(text("用户姓名为空！"));
        return list;
    }

    private String returnMsg(String prefixMsg, String publicType) {
        String prefix = "";
        if (publicType.equals(HsQwApplyPublic.PUBLIC_TYPE_FIRST)) {
            prefix = "初审公示";
        } else if (publicType.equals(HsQwApplyPublic.PUBLIC_TYPE_CHECK)) {
            prefix = "配租公示复核";
        }
        return renderResult(Global.TRUE, text(prefixMsg + prefix + "成功"));
    }

    /**
     * 导出数据-配租名单公示
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "exporRecheckNames")
    public void exporRecheckNames(HsQwApplyPublic hsQwApplyPublic, HttpServletRequest request, HttpServletResponse response) {
        hsQwApplyPublicService.exporRecheckNames(hsQwApplyPublic, response);
    }

    /**
     * 导出数据-配租房源清单公示
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "exporRecheckHouses")
    public void exporRecheckHouses(HsQwApplyPublic hsQwApplyPublic, HttpServletRequest request, HttpServletResponse response) {
        hsQwApplyPublicService.exporRecheckHouses(hsQwApplyPublic, response);
    }

    /**
     * 导入数据-空闲房源
     */
    @ResponseBody
    @RequiresPermissions("house:hsQwPublicRentalHouse:edit")
    @PostMapping(value = "importDataIdle")
    public String importDataIdle(MultipartFile file, String type) {
        try {
            return renderResult(Global.TRUE, "posfull:");
        } catch (Exception ex) {
            return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
        }
    }
}