package com.hsobs.hs.modules.apply.web.adjust;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.config.Global;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 个人承租变更申请Controller-以小换大
 *
 * <AUTHOR>
 * @version 2025-1-6
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/hsQwApplyReplaceUp")
public class HsQwApplyReplaceUpController extends BaseController {

    @Autowired
    private HsQwApplyService hsQwApplyService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public HsQwApply get(String id, boolean isNewRecord) {
        return hsQwApplyService.get(id, isNewRecord);
    }


    /**
     * 租房居室变更申请单（以小换大）
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formReplaceUp")
    public String formReplace(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApplyService.getUserApplyInfo("2", hsQwApply));
        return "modules/apply/replaceUp/hsQwApplyReplaceUpForm";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(HsQwApply hsQwApply) {
        hsQwApplyService.save(hsQwApply);
        return renderResult(Global.TRUE, text("保存个人调租申请成功！"));
    }


}