package com.hsobs.hs.modules.checkobject.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.checkobject.entity.HsQwManagementCheckObject;
import com.hsobs.hs.modules.checkobject.service.HsQwManagementCheckObjectService;

/**
 * 租赁资格轮候物业核验物品表Controller
 * <AUTHOR>
 * @version 2025-02-13
 */
@Controller
@RequestMapping(value = "${adminPath}/checkobject/hsQwManagementCheckObject")
public class HsQwManagementCheckObjectController extends BaseController {

	@Autowired
	private HsQwManagementCheckObjectService hsQwManagementCheckObjectService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwManagementCheckObject get(String id, boolean isNewRecord) {
		return hsQwManagementCheckObjectService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("checkobject:hsQwManagementCheckObject:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwManagementCheckObject hsQwManagementCheckObject, Model model) {
		model.addAttribute("hsQwManagementCheckObject", hsQwManagementCheckObject);
		return "modules/checkobject/hsQwManagementCheckObjectList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("checkobject:hsQwManagementCheckObject:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwManagementCheckObject> listData(HsQwManagementCheckObject hsQwManagementCheckObject, HttpServletRequest request, HttpServletResponse response) {
		hsQwManagementCheckObject.setPage(new Page<>(request, response));
		Page<HsQwManagementCheckObject> page = hsQwManagementCheckObjectService.findPage(hsQwManagementCheckObject);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("checkobject:hsQwManagementCheckObject:view")
	@RequestMapping(value = "form")
	public String form(HsQwManagementCheckObject hsQwManagementCheckObject, Model model) {
		model.addAttribute("hsQwManagementCheckObject", hsQwManagementCheckObject);
		return "modules/checkobject/hsQwManagementCheckObjectForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("checkobject:hsQwManagementCheckObject:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwManagementCheckObject hsQwManagementCheckObject) {
		hsQwManagementCheckObjectService.save(hsQwManagementCheckObject);
		return renderResult(Global.TRUE, text("保存租赁资格轮候物业核验物品表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("checkobject:hsQwManagementCheckObject:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwManagementCheckObject hsQwManagementCheckObject) {
		hsQwManagementCheckObjectService.delete(hsQwManagementCheckObject);
		return renderResult(Global.TRUE, text("删除租赁资格轮候物业核验物品表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("checkobject:hsQwManagementCheckObject:view")
	@RequestMapping(value = "hsQwManagementCheckObjectSelect")
	public String hsQwManagementCheckObjectSelect(HsQwManagementCheckObject hsQwManagementCheckObject, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwManagementCheckObject", hsQwManagementCheckObject);
		return "modules/checkobject/hsQwManagementCheckObjectSelect";
	}
	
}