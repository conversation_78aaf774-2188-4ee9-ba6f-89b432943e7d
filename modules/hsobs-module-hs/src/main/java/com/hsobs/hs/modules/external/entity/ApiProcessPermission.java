package com.hsobs.hs.modules.external.entity;

import javax.validation.constraints.NotBlank;

public class ApiProcessPermission extends ApiBody{


    public static String PROCESS_TYPE_RENTAL = "0"; //公租房首次申请
    public static String PROCESS_TYPE_INFO= "1"; //个人信息变更申请
    public static String PROCESS_TYPE_REPLACEUP = "2"; //租房居室变更申请,以小换大
    public static String PROCESS_TYPE_REPLACE  = "3"; //承租信息变更申请

    private String processType; //环节类型（0：公租房首次申请 1：个人信息变更申请  2：租房居室变更申请承租信息变更申请）

    private String isHandle;

    private String reason;

    private String hasRented;

    private String hasApplying;


    @NotBlank(message = "环节类型不能为空")
    public String getProcessType() {
        return processType;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public String getIsHandle() {
        return isHandle;
    }

    public void setIsHandle(String isHandle) {
        this.isHandle = isHandle;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getHasRented() {
        return hasRented;
    }

    public void setHasRented(String hasRented) {
        this.hasRented = hasRented;
    }

    public String getHasApplying() {
        return hasApplying;
    }

    public void setHasApplying(String hasApplying) {
        this.hasApplying = hasApplying;
    }
}
