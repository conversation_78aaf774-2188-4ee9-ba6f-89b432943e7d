package com.hsobs.hs.modules.publicapply.service;

import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;

import com.hsobs.hs.modules.publicapply.dao.HsPublicApplyHouseDao;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApplyHouse;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.stream.Collectors;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicapply.service.HsPublicApplyService;

import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;


/**
 * 公有住房-购房申请Service
 * <AUTHOR>
 * @version 2024-12-10
 */
@Service
public class HsPublicApplyHouseService extends CrudService<HsPublicApplyHouseDao, HsPublicApplyHouse> {

	@Autowired
	private HsPublicApplyHouseDao hsPublicApplyHouseDao;

	@Autowired
	private BpmTaskService bpmTaskService;

	@Autowired
	private HsPublicApplyService hsPublicApplyService;

	/**
	 * 获取单条数据
	 * @param hsPublicHouse
	 * @return
	 */
	@Override
	public HsPublicApplyHouse get(HsPublicApplyHouse hsPublicHouse) {
		return super.get(hsPublicHouse);
	}

	/**
	 * 查询分页数据
	 * @param hsPublicHouse 查询条件
	 * @param hsPublicHouse page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPublicApplyHouse> findPage(HsPublicApplyHouse hsPublicHouse) {
		if (StringUtils.isNotBlank(hsPublicHouse.getApplyedIdStr())){
			hsPublicHouse.sqlMap().getWhere().and("id", QueryType.NOT_IN, hsPublicHouse.getApplyedIdStr().split(","));
		}
		return super.findPage(hsPublicHouse);
	}

	/**
	 * 查询列表数据
	 * @param hsPublicHouse
	 * @return
	 */
	@Override
	public List<HsPublicApplyHouse> findList(HsPublicApplyHouse hsPublicHouse) {
		return super.findList(hsPublicHouse);
	}

	public Page<HsPublicApplyHouse> findAuditHousePageByTasks(HsPublicApplyHouse hsPublicApplyHouse) {
		//String[] flowStatuss = new String[]{
		//		HsPublicApply.APPLY_STATUS_AUDIT_ESTIMATED // "估算价格";//6
		//};

		return findAuditHousePageByTask(hsPublicApplyHouse, HsPublicApply.APPLY_STATUS_AUDIT_APPRAISE, null);
	}
	public Page<HsPublicApplyHouse> findAuditHousePageByTask(HsPublicApplyHouse hsPublicApplyHouse, String flowStatus, String[] flowStatuss) {
		List<HsPublicApplyHouse> list = findAuditHouseListByTask(hsPublicApplyHouse, flowStatus, flowStatuss);
		Page<HsPublicApplyHouse> page = hsPublicApplyHouse.getPage();
		page.setList(list);
		return page;
	}

	public List<HsPublicApplyHouse> findAuditHouseListByTask(HsPublicApplyHouse hsPublicApplyHouse, String flowStatus, String[] flowStatuss) {

		List<HsPublicApplyHouse> list = new ArrayList<>();

		HsBpmTask params = new HsBpmTask();
		params.setStatus(BpmTask.STATUS_UNFINISHED);
		if(!UserUtils.getUser().isAdmin()) {// 是管理员不设置usercode
			params.setUserCode(params.currentUser().getUserCode());
		}
		params.getProcIns().setFormKey("public_apply");//公有住房申请key
		if (StringUtils.isNotBlank(flowStatus)) {
			params.setName(flowStatus);
		}
		if (flowStatuss != null && flowStatuss.length > 0) {
			params.setNames(Arrays.stream(flowStatuss).collect(Collectors.toList()));
		}
		Page<BpmTask> myHsTaskPage = this.bpmTaskService.findTaskPage(params);
		List<String> hsIds = new ArrayList<>();
		myHsTaskPage.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		if (hsIds.isEmpty())
			return list;

		HsPublicApply hsApply = new HsPublicApply();
		hsApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
		List<HsPublicApply> hsApplyList = hsPublicApplyService.findList(hsApply);

		List<String> hsHouseIds = new ArrayList<>();
		hsApplyList.forEach((k -> hsHouseIds.add(k.getHouseId())));
		if (hsHouseIds.isEmpty())
			return list;

		hsPublicApplyHouse.sqlMap().getWhere().and("id", QueryType.IN, hsHouseIds.toArray());
		list = this.findList(hsPublicApplyHouse);
		list.forEach(k -> this.setTaskInfo(k, k.getId(), hsApplyList, myHsTaskPage));
		return list;
	}

	private void setTaskInfo(HsPublicApplyHouse k, String id, List<HsPublicApply> hsApplyList, Page<BpmTask> myHsTaskPage) {
		for (HsPublicApply hsApply : hsApplyList) {
			if (hsApply.getHouseId().equals(id)) {

				k.setRecordId(hsApply.getId());

				for (BpmTask bpmTask : myHsTaskPage.getList()) {
					if (bpmTask.getProcIns().getBizKey().equals(hsApply.getId())) {

						k.setFlowStatus(bpmTask.getName());
						break;
					}
				}
				return;
			}
		}
	}

}