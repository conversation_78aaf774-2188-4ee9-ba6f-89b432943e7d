package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;
import org.springframework.stereotype.Service;

/**
 * 省直公房，配租房源：选择待配租的房源4，进行核查
 */
@Service
public class HsQwHouseSelectListProvince implements HsQwHouseSelectList {
    public String getDataType() {
        return "8";
    }

    public Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse,
            HsQwPublicRentalHouseService hsQwPublicRentalHouseService) {
        hsQwPublicRentalHouse.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);
        hsQwPublicRentalHouse.setIsPublic("1");
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_PROVINCE);
        hsQwPublicRentalHouse.setHouseStatus("0");
        hsQwPublicRentalHouse.sqlMap().getWhere().disableAutoAddStatusWhere();
        return hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
    }
}
