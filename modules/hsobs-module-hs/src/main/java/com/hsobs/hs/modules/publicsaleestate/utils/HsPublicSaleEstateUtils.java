/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.hsobs.hs.modules.publicsaleestate.utils;

import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.hsobs.hs.modules.publicsaleapply.service.HsPublicSaleApplyService;
import com.hsobs.hs.modules.publicsaleestate.entity.HsPublicSaleEstate;
import com.hsobs.hs.modules.publicsaleestate.service.HsPublicSaleEstateService;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.SpringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * CmsUtils
 *
 * <AUTHOR>
 * @version 2020-7-24
 */
public class HsPublicSaleEstateUtils {

    private static final class Static {
        private static final HsPublicSaleEstateService estateService = SpringUtils.getBean(HsPublicSaleEstateService.class);
    }

    /**
     * 获得站点列表
     */
    public static List<HsPublicSaleEstate> getPublicSaleEstateList(String salePlanId, String houseId) {

        if(salePlanId == null){
            return new ArrayList<>();
        }
        HsPublicSaleEstate hsPublicSaleEstate = new HsPublicSaleEstate();
        hsPublicSaleEstate.setApplyId(salePlanId);
        hsPublicSaleEstate.sqlMap().getWhere().and("h.house_status", QueryType.EQ, "2");
        if(houseId != null && !"".equals(houseId)){
            hsPublicSaleEstate.sqlMap().getWhere().or("a.house_id", QueryType.EQ, houseId);
        }
        return Static.estateService.findList(hsPublicSaleEstate);
    }
}