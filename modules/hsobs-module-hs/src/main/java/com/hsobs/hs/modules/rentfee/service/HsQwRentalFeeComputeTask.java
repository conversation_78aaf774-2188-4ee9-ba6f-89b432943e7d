//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.hsobs.hs.modules.rentfee.service;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.publicfee.entity.HsQwEstateTotalFee;
import com.hsobs.hs.modules.publicfee.service.HsQwEstateTotalFeeService;
import com.hsobs.hs.modules.rentfee.dao.HsQwRentalFeeDao;
import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.hsobs.hs.modules.utils.HsDateUtil;
import com.jeesite.common.config.Global;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.common.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

@Service
public class HsQwRentalFeeComputeTask extends BaseService {

    @Autowired
    private HsQwCompactService hsQwCompactService;

    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private HsQwRentalFeeDao hsQwRentalFeeDao;

    @Autowired
    private HsQwRentalFeeService hsQwRentalFeeService;

    @Autowired
    private HsQwEstateTotalFeeService hsQwEstateTotalFeeService;

    Map<String, Double> mapEstateArea;

   @Transactional
    public void execute() {
        mapEstateArea = new HashMap<>();
        logger.info("进入物业费和租金生成任务");

        // 1. 生成物业费账单
        generatePropertyFees();

        // 2. 生成租金账单
        generateRentalFees();
    }

    /**
     * 批量生成物业费和水电公摊费账单
     */
    private void generatePropertyFees() {
        String currentMonth = HsDateUtil.getCurrentMonthDate();

        // 1. 查询所有需要生成物业费账单的申请单
        List<HsQwApply> appliesNeedPropertyFee = hsQwRentalFeeDao.findAppliesNeedGenerateFee(currentMonth, "0");
        logger.info("需要生成物业费账单的申请单数量: {}", appliesNeedPropertyFee.size());

        if (appliesNeedPropertyFee.isEmpty()) {
            return;
        }

        // 2. 批量生成物业费账单和水电公摊费账单
        List<HsQwRentalFee> propertyFeeList = new ArrayList<>();
        List<HsQwRentalFee> utilityFeeList = new ArrayList<>();
        Date expectFeeDate = DateUtils.addDays(new Date(), Integer.parseInt(Global.getConfig("property.expect.fee.duration", "15")));

        // 3. 预先加载所有小区的公摊费用信息
        Map<String, HsQwEstateTotalFee> estateFeeMap = loadEstateTotalFees(currentMonth);

        for (HsQwApply apply : appliesNeedPropertyFee) {
            try {
                if (apply.getHsQwApplyHouse() == null || apply.getHsQwApplyHouse().getEstate() == null ||
                    apply.getCompact() == null || apply.getMainApplyer() == null) {
                    logger.warn("申请单 {} 缺少必要信息，跳过物业费计算", apply.getId());
                    continue;
                }

                HsQwPublicRentalEstate estate = apply.getHsQwApplyHouse().getEstate();
                HsQwPublicRentalHouse house = apply.getHsQwApplyHouse();
                HsQwCompact compact = apply.getCompact();
                String userId = apply.getMainApplyer().getUserId();

                // 1. 计算物业费（不依赖公摊总数）
                HsQwRentalFee propertyFee = calculateManagementFee(house, estate, compact, userId, currentMonth, expectFeeDate);
                if (propertyFee != null) {
                    propertyFeeList.add(propertyFee);
                }

                // 2. 计算水费（依赖公摊总数）
                HsQwEstateTotalFee estateTotalFee = estateFeeMap.get(estate.getId());
                if (estateTotalFee != null) {
                    // 计算水费
                    HsQwRentalFee waterFee = calculateWaterFee(estateTotalFee, house, estate, compact, userId, currentMonth, expectFeeDate);
                    if (waterFee != null) {
                        utilityFeeList.add(waterFee);
                    }

                    // 计算电费
                    HsQwRentalFee electricityFee = calculateElectricityFee(estateTotalFee, house, estate, compact, userId, currentMonth, expectFeeDate);
                    if (electricityFee != null) {
                        utilityFeeList.add(electricityFee);
                    }
                }
            } catch (Exception e) {
                logger.error(apply.getApplyTitle() + "物业费生成异常：" + e.getMessage(), e);
            }
        }

        // 4. 批量插入物业费账单
        if (!propertyFeeList.isEmpty()) {
            hsQwRentalFeeDao.batchInsert(propertyFeeList);
            logger.info("成功生成{}条物业费账单", propertyFeeList.size());
        }

        // 5. 批量插入水电公摊费账单
        if (!utilityFeeList.isEmpty()) {
            hsQwRentalFeeDao.batchInsert(utilityFeeList);
            logger.info("成功生成{}条水电公摊费账单", utilityFeeList.size());
        }
    }

    /**
     * 批量生成租金账单
     */
    private void generateRentalFees() {
        String currentMonth = HsDateUtil.getCurrentMonthDate();

        // 1. 查询所有需要生成租金账单的申请单
        List<HsQwApply> appliesNeedRentalFee = hsQwRentalFeeDao.findAppliesNeedGenerateFee(currentMonth, "1");
        logger.info("需要生成租金账单的申请单数量: {}", appliesNeedRentalFee.size());

        if (appliesNeedRentalFee.isEmpty()) {
            return;
        }

        // 2. 批量生成租金账单
        List<HsQwRentalFee> rentalFeeList = new ArrayList<>();
        Date expectFeeDate = DateUtils.addDays(new Date(), Integer.parseInt(Global.getConfig("property.expect.fee.duration", "15")));

        for (HsQwApply apply : appliesNeedRentalFee) {
            try {
                HsQwCompact compact = apply.getCompact();

                // 创建租金账单
                HsQwRentalFee rentalFee = new HsQwRentalFee();
                // 设置ID字段
                rentalFee.setId(IdGen.nextId());
                rentalFee.setFeeMonth(currentMonth);
                rentalFee.setExpectFeeDate(expectFeeDate);
                rentalFee.setCompactId(compact.getId());
                rentalFee.setFeeType("1"); // 租金类型（0物业费 1租金 2水费 3电费 4燃气费）
                rentalFee.setUserId(apply.getMainApplyer().getUserId());
                rentalFee.setRentalFee(compact.getMonthFee());
                rentalFee.setStatus("0");
                rentalFee.setCreateBy("system");
                rentalFee.setCreateDate(new Date());
                rentalFee.setUpdateBy("system");
                rentalFee.setUpdateDate(new Date());

                rentalFeeList.add(rentalFee);
            } catch (Exception e) {
                logger.error(apply.getApplyTitle() + "租金生成异常：" + e.getMessage(), e);
            }
        }

        // 3. 批量插入租金账单
        if (!rentalFeeList.isEmpty()) {
            hsQwRentalFeeDao.batchInsert(rentalFeeList);
            logger.info("成功生成{}条租金账单", rentalFeeList.size());
        }
    }

    /**
     * 预先加载所有小区的公摊费用信息
     */
    private Map<String, HsQwEstateTotalFee> loadEstateTotalFees(String currentMonth) {
        Map<String, HsQwEstateTotalFee> result = new HashMap<>();

        HsQwEstateTotalFee query = new HsQwEstateTotalFee();
        query.setFeeMonth(currentMonth);
        List<HsQwEstateTotalFee> allEstateFees = hsQwEstateTotalFeeService.findList(query);

        for (HsQwEstateTotalFee fee : allEstateFees) {
            result.put(fee.getEstateId(), fee);
        }

        return result;
    }

    /**
     * 计算物业管理费（不依赖公摊总数）
     */
    private HsQwRentalFee calculateManagementFee(HsQwPublicRentalHouse house,
                                            HsQwPublicRentalEstate estate, HsQwCompact compact,
                                            String userId, String currentMonth, Date expectFeeDate) {
        // 物业费 = 建筑面积 * 物业管理费单价
        Double area = Double.parseDouble(house.getBuildingArea());
        Double areaFee = estate.getManagementFee();
        Double managementFee = areaFee * area;

        if (managementFee == null || managementFee.isNaN()) {
            managementFee = 0.0;
        }

        // 创建物业费账单
        HsQwRentalFee propertyFee = new HsQwRentalFee();
        // 设置ID字段
        propertyFee.setId(IdGen.nextId());
        propertyFee.setFeeMonth(currentMonth);
        propertyFee.setExpectFeeDate(expectFeeDate);
        propertyFee.setCompactId(compact.getId());
        propertyFee.setFeeType("0"); // 租金类型（0物业费 1租金 2水费 3电费 4燃气费）
        propertyFee.setUserId(userId);
        propertyFee.setRentalFee(managementFee);
        propertyFee.setRemarks("物业管理费");
        propertyFee.setStatus("0");
        propertyFee.setCreateBy("system");
        propertyFee.setCreateDate(new Date());
        propertyFee.setUpdateBy("system");
        propertyFee.setUpdateDate(new Date());

        return propertyFee;
    }

    /**
     * 计算水费（依赖公摊总数）
     */
    private HsQwRentalFee calculateWaterFee(HsQwEstateTotalFee estateTotalFee, HsQwPublicRentalHouse house,
                                            HsQwPublicRentalEstate estate, HsQwCompact compact,
                                            String userId, String currentMonth, Date expectFeeDate) {
        // 计算当前用户的水公摊费
        Double waterPublicFee = estateTotalFee.getWaterFee();
        Double publicArea = estate.getPublicArea();
        Double houseArea = house.getSharedArea();

        // 水费 = 共享面积 / 公共面积 * 水公摊总费用
        Double houseWaterFee = houseArea / publicArea * waterPublicFee;

        if (houseWaterFee == null || houseWaterFee.isNaN()) {
            houseWaterFee = 0.0;
        }

        // 如果水费为0，则不创建账单
        if (houseWaterFee <= 0) {
            return null;
        }

        // 创建水费账单
        HsQwRentalFee waterFee = new HsQwRentalFee();
        // 设置ID字段
        waterFee.setId(IdGen.nextId());
        waterFee.setFeeMonth(currentMonth);
        waterFee.setExpectFeeDate(expectFeeDate);
        waterFee.setCompactId(compact.getId());
        waterFee.setFeeType("2"); // 租金类型（0物业费 1租金 2水费 3电费 4燃气费）
        waterFee.setUserId(userId);
        waterFee.setRentalFee(houseWaterFee);
        waterFee.setRemarks("水费");
        waterFee.setStatus("0");
        waterFee.setCreateBy("system");
        waterFee.setCreateDate(new Date());
        waterFee.setUpdateBy("system");
        waterFee.setUpdateDate(new Date());

        return waterFee;
    }

    /**
     * 计算电费（依赖公摊总数）
     */
    private HsQwRentalFee calculateElectricityFee(HsQwEstateTotalFee estateTotalFee, HsQwPublicRentalHouse house,
                                            HsQwPublicRentalEstate estate, HsQwCompact compact,
                                            String userId, String currentMonth, Date expectFeeDate) {
        // 计算当前用户的电公摊费
        Double elecPublicFee = estateTotalFee.getElectriFee();
        Double publicArea = estate.getPublicArea();
        Double houseArea = house.getSharedArea();

        // 电费 = 共享面积 / 公共面积 * 电公摊总费用
        Double houseElecFee = houseArea / publicArea * elecPublicFee;

        if (houseElecFee == null || houseElecFee.isNaN()) {
            houseElecFee = 0.0;
        }

        // 如果电费为0，则不创建账单
        if (houseElecFee <= 0) {
            return null;
        }

        // 创建电费账单
        HsQwRentalFee electricityFee = new HsQwRentalFee();
        // 设置ID字段
        electricityFee.setId(IdGen.nextId());
        electricityFee.setFeeMonth(currentMonth);
        electricityFee.setExpectFeeDate(expectFeeDate);
        electricityFee.setCompactId(compact.getId());
        electricityFee.setFeeType("3"); // 租金类型（0物业费 1租金 2水费 3电费 4燃气费）
        electricityFee.setUserId(userId);
        electricityFee.setRentalFee(houseElecFee);
        electricityFee.setRemarks("电费");
        electricityFee.setStatus("0");
        electricityFee.setCreateBy("system");
        electricityFee.setCreateDate(new Date());
        electricityFee.setUpdateBy("system");
        electricityFee.setUpdateDate(new Date());

        return electricityFee;
    }
}
