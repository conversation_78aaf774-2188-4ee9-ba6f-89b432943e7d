package com.hsobs.hs.modules.rentfee.service;

import java.util.Date;
import java.util.List;

import com.hsobs.hs.modules.payment.dao.HsQwFeePaymentDao;
import com.hsobs.hs.modules.payment.entity.HsQwFeePayment;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.hsobs.hs.modules.rentfee.dao.HsQwRentalFeeDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 租赁账单表Service
 * <AUTHOR>
 * @version 2025-01-20
 */
@Service
public class HsQwRentalFeeService extends CrudService<HsQwRentalFeeDao, HsQwRentalFee> {
	
	@Autowired
	private HsQwFeePaymentDao hsQwFeePaymentDao;
	
	/**
	 * 获取单条数据
	 * @param hsQwRentalFee
	 * @return
	 */
	@Override
	public HsQwRentalFee get(HsQwRentalFee hsQwRentalFee) {
		HsQwRentalFee entity = super.get(hsQwRentalFee);
		if (entity != null){
			HsQwFeePayment hsQwFeePayment = new HsQwFeePayment();
			hsQwFeePayment.setFeeId(entity.getId());
			hsQwFeePayment.setStatus(HsQwFeePayment.STATUS_NORMAL);
			entity.setHsQwFeePaymentList(hsQwFeePaymentDao.findList(hsQwFeePayment));
		}
		return entity;
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwRentalFee 查询条件
	 * @param hsQwRentalFee page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwRentalFee> findPage(HsQwRentalFee hsQwRentalFee) {
		hsQwRentalFee.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findPage(hsQwRentalFee);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwRentalFee
	 * @return
	 */
	@Override
	public List<HsQwRentalFee> findList(HsQwRentalFee hsQwRentalFee) {
		return super.findList(hsQwRentalFee);
	}
	
	/**
	 * 查询子表分页数据
	 * @param hsQwFeePayment
	 * @param hsQwFeePayment page 分页对象
	 * @return
	 */
	public Page<HsQwFeePayment> findSubPage(HsQwFeePayment hsQwFeePayment) {
		Page<HsQwFeePayment> page = hsQwFeePayment.getPage();
		page.setList(hsQwFeePaymentDao.findList(hsQwFeePayment));
		return page;
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwRentalFee
	 */
	@Override
	@Transactional
	public void save(HsQwRentalFee hsQwRentalFee) {
		super.save(hsQwRentalFee);
	}
	
	/**
	 * 更新状态
	 * @param hsQwRentalFee
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwRentalFee hsQwRentalFee) {
		super.updateStatus(hsQwRentalFee);
	}
	
	/**
	 * 删除数据
	 * @param hsQwRentalFee
	 */
	@Override
	@Transactional
	public void delete(HsQwRentalFee hsQwRentalFee) {
		super.delete(hsQwRentalFee);
		HsQwFeePayment hsQwFeePayment = new HsQwFeePayment();
		hsQwFeePayment.setFeeId(hsQwRentalFee.getId());
		hsQwFeePaymentDao.deleteByEntity(hsQwFeePayment);
	}

	@Transactional
	public void saveFee(HsQwRentalFee hsQwRentalFee) {
		if (hsQwRentalFee.getStatus().equals("0")){
			throw new ServiceException("缴费状态未选择");
		}
		hsQwRentalFee.setFeeDate(new Date());
		this.update(hsQwRentalFee);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsQwRentalFee, hsQwRentalFee.getId(), "hsQwRentalFee_file");
		this.updateStatus(hsQwRentalFee);
	}

	/**
	 * 根据合同编码获取逾期未缴费的租金列表
	 * @param compactId
	 * @return
	 */
    public List<HsQwRentalFee> getPassedRentalFeeList(String compactId) {
		HsQwRentalFee query = new HsQwRentalFee();
		query.setCompactId(compactId);
		return super.findList(query);
    }
}