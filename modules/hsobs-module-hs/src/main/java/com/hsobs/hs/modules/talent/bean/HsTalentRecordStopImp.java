package com.hsobs.hs.modules.talent.bean;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 人才住房补助导入模板
 * <AUTHOR>
 */
public class HsTalentRecordStopImp implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String userName;		// 用户名称
    private String mobile;		// 手机号码
    private String userNo;		// 身份证号码
    private String stopReason;		// 停发原因

    @ExcelFields({
            @ExcelField(title="姓名", attrName="userName", align= ExcelField.Align.CENTER, sort=40),
            @ExcelField(title="手机号码", attrName="mobile", align= ExcelField.Align.CENTER, sort=50),
            @ExcelField(title="身份证号码", attrName="userNo", words=25, align= ExcelField.Align.CENTER, sort=60),
            @ExcelField(title="停发原因", attrName="stopReason", words=40, align= ExcelField.Align.CENTER, sort=70),
    })

    public HsTalentRecordStopImp() {
        super();
    }

    @NotBlank(message = "姓名不能为空")
    @Size(min = 1, max = 30, message = "姓名长度不能超过 30 个字符")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @NotBlank(message = "手机号码不能为空")
    @Size(min = 1, max = 20, message = "手机号码长度不能超过 20 个字符")
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @NotBlank(message = "身份证号码不能为空")
    @Size(min = 1, max = 20, message = "身份证号码长度不能超过 20 个字符")
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    @NotBlank(message = "停发原因不能为空")
    @Size(min = 1, max = 500, message = "停发原因长度不能超过 500 个字符")
    public String getStopReason() {
        return stopReason;
    }

    public void setStopReason(String stopReason) {
        this.stopReason = stopReason;
    }
}
