package com.hsobs.hs.modules.apply.web.adjust;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 个人变更申请Controller
 *
 * <AUTHOR>
 * @version 2025-1-6
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/hsQwApplyInfo")
public class HsQwApplyInfoController extends BaseController {

    @Autowired
    private HsQwApplyService hsQwApplyService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public HsQwApply get(String id, boolean isNewRecord) {
        return hsQwApplyService.get(id, isNewRecord);
    }


    /**
     * 查询列表-个人信息修改代办
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = {"listInfo", ""})
    public String listInfo(HsQwApply hsQwApply, Model model, boolean isAdd) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("isAdd", isAdd);
        return "modules/apply/info/hsQwApplyInfoList";
    }

    /**
     * 查询列表-个人信息修改代办已处理
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = {"listInfoDone", ""})
    public String listInfoDone(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/info/hsQwApplyInfoDoneList";
    }


    /**
     * 查询代办的审批任务-个人信息修改代办
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listAuditInfoData")
    @ResponseBody
    public Page<HsQwApply> listAuditInfoData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTaskNew(hsQwApply, null, "1", "rent_apply_info");
        return page;
    }

    /**
     * 查询已办的审批任务-个人信息修改已办
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listAuditedInfoData")
    @ResponseBody
    public Page<HsQwApply> listAuditedInfoData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTaskNew(hsQwApply, null, "2", "rent_apply_info");
        return page;
    }


    /**
     * 租房个人信息变更申请单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formInfo")
    public String formInfo(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApplyService.getUserApplyInfo("1", hsQwApply));
        return "modules/apply/info/hsQwApplyInfoForm";
    }

    /**
     * 租房个人信息变更申请单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "form")
    public String form(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/info/hsQwApplyInfoForm";
    }

    /**
     * 租房居室变更申请单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formInfoCheck")
    public String formInfoCheck(HsQwApply hsQwApply, Model model, String isRead) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        return "modules/apply/info/hsQwApplyInfoFormAudit";
    }

    /**
     * 租房居室变更申请单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formInfoConfirm")
    public String formInfoConfirm(HsQwApply hsQwApply, Model model, String isRead) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        return "modules/apply/info/hsQwApplyInfoFormConfirm";
    }

    /**
     * 选择代理申请的申请单列表
     */
    @RequiresPermissions("checkrecord:hsQwCheckRecord:view")
    @RequestMapping(value = "applySelect")
    public String applySelect(HsQwPublicRentalHouse hsQwPublicRentlHouse, String selectData, Model model) {
        String selectDataJson = EncodeUtils.decodeUrl(selectData);
        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
            model.addAttribute("selectData", selectDataJson);
        }
        model.addAttribute("hsQwPublicReplacealHouse", hsQwPublicRentlHouse);
        return "modules/house/hsQwPublicRentalHouseListSelectProxyInfo";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(HsQwApply hsQwApply) {
        hsQwApplyService.save(hsQwApply);
        return renderResult(Global.TRUE, text("保存个人调租申请成功！"));
    }

    /**
     * 选择房源对话框
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "houseSelect")
    public String houseSelect(HsQwPublicRentalHouse hsQwPublicRentalHouse, String selectData, Model model, String applyId) {
        String selectDataJson = EncodeUtils.decodeUrl(selectData);
        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
            model.addAttribute("selectData", selectDataJson);
        }
        model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
        return "modules/house/hsQwPublicRentalHouseListSelectApply";
    }

}