package com.hsobs.hs.modules.talent.web;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.hsobs.hs.modules.talent.entity.HsTalentRecord;
import com.hsobs.hs.modules.talent.service.HsTalentIntroductionApplyService;
import com.hsobs.hs.modules.talent.service.HsTalentRecordService;
import com.hsobs.hs.modules.talent.service.HsTalentServiceSupport;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 人才补助档案表Controller
 * <AUTHOR>
 * @version 2025-01-03
 */
@Controller
@RequestMapping(value = "${adminPath}/talent/hsTalentRecord")
public class HsTalentRecordController extends BaseController {

	@Autowired
	private HsTalentRecordService hsTalentRecordService;
	@Autowired
	private HsTalentServiceSupport hsTalentServiceSupport;

	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsTalentRecord get(String id, boolean isNewRecord) {
		return hsTalentRecordService.get(id, isNewRecord);
	}

	@RequiresPermissions("talent:hsTalentRecord:view")
	@RequestMapping(value = {"index", ""})
	public String index(HsTalentRecord hsTalentRecord, Model model) {
		model.addAttribute("hsTalentRecord", hsTalentRecord);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentRecordIndex";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("talent:hsTalentRecord:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsTalentRecord hsTalentRecord, Model model) {
		model.addAttribute("hsTalentRecord", hsTalentRecord);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentRecordList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("talent:hsTalentRecord:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsTalentRecord> listData(HsTalentRecord hsTalentRecord, HttpServletRequest request, HttpServletResponse response) {
		hsTalentRecord.setPage(new Page<>(request, response));
		hsTalentRecordService.addDataScopeFilter(hsTalentRecord);
		Page<HsTalentRecord> page = hsTalentRecordService.findPage(hsTalentRecord);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("talent:hsTalentRecord:view")
	@RequestMapping(value = "form")
	public String form(HsTalentRecord hsTalentRecord, Model model) {
		model.addAttribute("hsTalentRecord", hsTalentRecord);
		hsTalentServiceSupport.loadApplyData(hsTalentRecord);
		model.addAttribute("isRead", "true");
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentRecordForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("talent:hsTalentRecord:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsTalentRecord hsTalentRecord) {
		hsTalentRecordService.save(hsTalentRecord);
		return renderResult(Global.TRUE, text("保存人才补助档案表成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("talent:hsTalentRecord:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsTalentRecord hsTalentRecord) {
		hsTalentRecord.setStatus(HsTalentRecord.STATUS_DISABLE);
		hsTalentRecordService.updateStatus(hsTalentRecord);
		return renderResult(Global.TRUE, text("停用人才补助档案表成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("talent:hsTalentRecord:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsTalentRecord hsTalentRecord) {
		hsTalentRecord.setStatus(HsTalentRecord.STATUS_NORMAL);
		hsTalentRecordService.updateStatus(hsTalentRecord);
		return renderResult(Global.TRUE, text("启用人才补助档案表成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("talent:hsTalentRecord:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsTalentRecord hsTalentRecord) {
		hsTalentRecordService.delete(hsTalentRecord);
		return renderResult(Global.TRUE, text("删除人才补助档案表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("talent:hsTalentRecord:view")
	@RequestMapping(value = "hsTalentRecordSelect")
	public String hsTalentRecordSelect(HsTalentRecord hsTalentRecord, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsTalentRecord", hsTalentRecord);
		return "modules/talent/hsTalentRecordSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("talent:hsTalentRecord:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsTalentRecord hsTalentRecord, HttpServletResponse response) {
		hsTalentRecordService.addDataScopeFilter(hsTalentRecord);
		List<HsTalentRecord> list = hsTalentRecordService.findList(hsTalentRecord);
		String fileName = "人才住房补助信息" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("人才住房补助信息", HsTalentRecord.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	
}