package com.hsobs.hs.modules.datasync.service;

import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.datasync.bean.RoleResourceAuthData;
import com.hsobs.hs.modules.datasync.bean.UserRoleAndDataAuthData;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.Role;
import com.jeesite.modules.sys.entity.RoleDataScope;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.RoleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class DataSyncRoleService {

    @Autowired
    private RoleService roleService;


    public Role syncRole(UserRoleAndDataAuthData roleData, User systemUser) {
        Role role = roleService.get(roleData.getCode());

        if (role == null) {
            role = new Role();

            role.setRoleName(roleData.getRoleName());
            role.setViewCode(roleData.getCode());
            role.setRoleCode(roleData.getCode());

            role.setRoleSort(1000);
            role.setUserType("employee");
            role.setRoleType("4");
            if (roleData.getCopyFlag()) {
                role.setIsShow("0");
            } else {
                role.setIsShow("1");
            }
            role.setIsSys("1");
            role.setStatus("0");

            role.currentUser(systemUser);
            roleService.save(role);
        }

        return role;
    }

    public void syncRoleAuth(Role role, User systemUser, Api2ResponseBody<List<RoleResourceAuthData>> roleResourceResp) {
        List<String> menuCodeList = new ArrayList<>();
        if (roleResourceResp.getData() != null && !roleResourceResp.getData().isEmpty()) {
            for (RoleResourceAuthData authData : roleResourceResp.getData()) {
                if (StringUtils.isNotEmpty(authData.getCode())) {
                    menuCodeList.add(authData.getCode());
                }
            }
        }
        role.setRoleMenuListJson(StringUtils.join(menuCodeList, ","));
        role.currentUser(systemUser);
        roleService.saveAuth(role);
    }

    public void syncRoleAuthDataScope(Role role, User systemUser, UserRoleAndDataAuthData roleData, Map<String, Office> officeMap) {
        // roleService.saveAuthDataScope(role);
        role.currentUser(systemUser);
        // 1-本单位  2-本单位及下级 3-指定单位
        List<RoleDataScope>  scopeList = new ArrayList<>();
        RoleDataScope scope = null;

        if (!officeMap.containsKey(roleData.getOrgId())) {
            return;
        }

        if ("1".equals(roleData.getPriType())) {
            role.setDataScope("6");
        } else if ("2".equals(roleData.getPriType())) {
            role.setDataScope("3");
        } else if ("3".equals(roleData.getPriType())) {
            role.setDataScope("2");

            scope = new RoleDataScope();
            scope.setCtrlType("Office");
            scope.setCtrlData(officeMap.get(roleData.getOrgId()).getOfficeCode());
            scopeList.add(scope);

            if (StringUtils.isNotBlank(roleData.getPriData())) {
                String[] priArr = StringUtils.split(roleData.getPriData(), ",");
                for (String pri : priArr) {
                    if (officeMap.containsKey(pri)) {
                        scope = new RoleDataScope();
                        scope.setCtrlType("Office");
                        scope.setCtrlData(officeMap.get(pri).getOfficeCode());
                        scopeList.add(scope);
                    }
                }
            }
            // roleDataScopeListJson: [{"ctrlType":"Office","ctrlData":"0000000"},{"ctrlType":"Office","ctrlData":"0000064"},{"ctrlType":"Office","ctrlData":"0000735"},{"ctrlType":"Office","ctrlData":"0001180"},{"ctrlType":"Office","ctrlData":"0001181"}]
        } else {
            role.setDataScope("6");
        }
        role.setRoleDataScopeListJson(JSONObject.toJSONString(scopeList));
        roleService.saveAuthDataScope(role);
    }
}
