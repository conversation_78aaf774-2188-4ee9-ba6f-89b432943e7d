package com.hsobs.hs.modules.contract.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

import java.util.List;

/**
 * 合同模板Entity
 * <AUTHOR>
 * @version 2025-01-21
 */
@Table(name="hs_contract_template", alias="a", label="合同模板信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="contract_name", attrName="contractName", label="合同模板名称", queryType=QueryType.LIKE),
		@Column(name="contract_desc", attrName="contractDesc", label="合同模板描述"),
		@Column(name="contract_version", attrName="contractVersion", label="合同模板版本" ,queryType=QueryType.LIKE),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, orderBy="a.update_date DESC"
)
public class HsContractTemplate extends DataEntity<HsContractTemplate> {
	
	private static final long serialVersionUID = 1L;
	private String contractName;		// 合同模板名称
	private String contractDesc;		// 合同模板描述
	private String contractVersion;		// 合同模板版本
	private String validTag;		// 是否有效;1-有效 0-无效

	private String viewName;
	private HsContractTemplateData hsContractTemplateData;
	private List<HsContractTemplateField> hsContractTemplateFieldList = ListUtils.newArrayList();;

	public HsContractTemplate() {
		this(null);
	}
	
	public HsContractTemplate(String id){
		super(id);
	}
	
	@Size(min=0, max=255, message="合同模板名称长度不能超过 255 个字符")
	public String getContractName() {
		return contractName;
	}

	public void setContractName(String contractName) {
		this.contractName = contractName;
	}
	
	@Size(min=0, max=255, message="合同模板描述长度不能超过 255 个字符")
	public String getContractDesc() {
		return contractDesc;
	}

	public void setContractDesc(String contractDesc) {
		this.contractDesc = contractDesc;
	}
	
	@Size(min=0, max=255, message="合同模板版本长度不能超过 255 个字符")
	public String getContractVersion() {
		return contractVersion;
	}

	public void setContractVersion(String contractVersion) {
		this.contractVersion = contractVersion;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public HsContractTemplateData getHsContractTemplateData() {
		return hsContractTemplateData;
	}

	public void setHsContractTemplateData(HsContractTemplateData hsContractTemplateData) {
		this.hsContractTemplateData = hsContractTemplateData;
	}

	public List<HsContractTemplateField> getHsContractTemplateFieldList() {
		return hsContractTemplateFieldList;
	}

	public void setHsContractTemplateFieldList(List<HsContractTemplateField> hsContractTemplateFieldList) {
		this.hsContractTemplateFieldList = hsContractTemplateFieldList;
	}

	public String getViewName() {
		return viewName;
	}

	public void setViewName(String viewName) {
		this.viewName = viewName;
	}
}