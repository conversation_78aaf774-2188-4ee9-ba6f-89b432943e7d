package com.hsobs.hs.modules.applypublichouse.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.applypublichouse.entity.HsQwApplyPublicHouse;
import com.hsobs.hs.modules.applypublichouse.service.HsQwApplyPublicHouseService;

/**
 * 租赁资格轮候公示房源表Controller
 * <AUTHOR>
 * @version 2024-12-05
 */
@Controller
@RequestMapping(value = "${adminPath}/applypublichouse/hsQwApplyPublicHouse")
public class HsQwApplyPublicHouseController extends BaseController {

	@Autowired
	private HsQwApplyPublicHouseService hsQwApplyPublicHouseService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyPublicHouse get(String id, boolean isNewRecord) {
		return hsQwApplyPublicHouseService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("applypublichouse:hsQwApplyPublicHouse:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyPublicHouse hsQwApplyPublicHouse, Model model) {
		model.addAttribute("hsQwApplyPublicHouse", hsQwApplyPublicHouse);
		return "modules/applypublichouse/hsQwApplyPublicHouseList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("applypublichouse:hsQwApplyPublicHouse:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyPublicHouse> listData(HsQwApplyPublicHouse hsQwApplyPublicHouse, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyPublicHouse.setPage(new Page<>(request, response));
		Page<HsQwApplyPublicHouse> page = hsQwApplyPublicHouseService.findPage(hsQwApplyPublicHouse);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("applypublichouse:hsQwApplyPublicHouse:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyPublicHouse hsQwApplyPublicHouse, Model model) {
		model.addAttribute("hsQwApplyPublicHouse", hsQwApplyPublicHouse);
		return "modules/applypublichouse/hsQwApplyPublicHouseForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("applypublichouse:hsQwApplyPublicHouse:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyPublicHouse hsQwApplyPublicHouse) {
		hsQwApplyPublicHouseService.save(hsQwApplyPublicHouse);
		return renderResult(Global.TRUE, text("保存租赁资格轮候公示房源表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("applypublichouse:hsQwApplyPublicHouse:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyPublicHouse hsQwApplyPublicHouse) {
		hsQwApplyPublicHouseService.delete(hsQwApplyPublicHouse);
		return renderResult(Global.TRUE, text("删除租赁资格轮候公示房源表成功！"));
	}
	
}