package com.hsobs.hs.modules.publicapplyer.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.hsobs.hs.modules.publicapplyer.service.HsPublicApplyerService;

/**
 * 公有住房-购房申请人Controller
 * <AUTHOR>
 * @version 2024-12-10
 */
@Controller
@RequestMapping(value = "${adminPath}/publicapplyer/hsPublicApplyer")
public class HsPublicApplyerController extends BaseController {

	@Autowired
	private HsPublicApplyerService hsPublicApplyerService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPublicApplyer get(String id, boolean isNewRecord) {
		return hsPublicApplyerService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicapplyer:hsPublicApplyer:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPublicApplyer hsPublicApplyer, Model model) {
		model.addAttribute("hsPublicApplyer", hsPublicApplyer);
		return "modules/publicapplyer/hsPublicApplyerList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("publicapplyer:hsPublicApplyer:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsPublicApplyer> listData(HsPublicApplyer hsPublicApplyer, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApplyer.setPage(new Page<>(request, response));
		Page<HsPublicApplyer> page = hsPublicApplyerService.findPage(hsPublicApplyer);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("publicapplyer:hsPublicApplyer:view")
	@RequestMapping(value = "form")
	public String form(HsPublicApplyer hsPublicApplyer, Model model) {
		model.addAttribute("hsPublicApplyer", hsPublicApplyer);
		return "modules/publicapplyer/hsPublicApplyerForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("publicapplyer:hsPublicApplyer:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsPublicApplyer hsPublicApplyer) {
		hsPublicApplyerService.save(hsPublicApplyer);
		return renderResult(Global.TRUE, text("保存公有住房-购房申请人成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("publicapplyer:hsPublicApplyer:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsPublicApplyer hsPublicApplyer) {
		hsPublicApplyer.setStatus(HsPublicApplyer.STATUS_DISABLE);
		hsPublicApplyerService.updateStatus(hsPublicApplyer);
		return renderResult(Global.TRUE, text("停用公有住房-购房申请人成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("publicapplyer:hsPublicApplyer:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsPublicApplyer hsPublicApplyer) {
		hsPublicApplyer.setStatus(HsPublicApplyer.STATUS_NORMAL);
		hsPublicApplyerService.updateStatus(hsPublicApplyer);
		return renderResult(Global.TRUE, text("启用公有住房-购房申请人成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("publicapplyer:hsPublicApplyer:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsPublicApplyer hsPublicApplyer) {
		hsPublicApplyerService.delete(hsPublicApplyer);
		return renderResult(Global.TRUE, text("删除公有住房-购房申请人成功！"));
	}
	
}