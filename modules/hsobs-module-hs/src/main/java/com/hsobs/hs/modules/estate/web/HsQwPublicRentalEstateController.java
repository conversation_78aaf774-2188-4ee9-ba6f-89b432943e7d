package com.hsobs.hs.modules.estate.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.service.ServiceException;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.estate.service.HsQwPublicRentalEstateService;

/**
 * 公租房房源楼盘信息表Controller
 * <AUTHOR>
 * @version 2024-11-20
 */
@Controller
@RequestMapping(value = "${adminPath}/estate/hsQwPublicRentalEstate")
public class HsQwPublicRentalEstateController extends BaseController {

	@Autowired
	private HsQwPublicRentalEstateService hsQwPublicRentalEstateService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwPublicRentalEstate get(String id, boolean isNewRecord) {
		return hsQwPublicRentalEstateService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("estate:hsQwPublicRentalEstate:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwPublicRentalEstate hsQwPublicRentalEstate, Model model) {
		model.addAttribute("hsQwPublicRentalEstate", hsQwPublicRentalEstate);
		return "modules/estate/hsQwPublicRentalEstateList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("estate:hsQwPublicRentalEstate:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwPublicRentalEstate> listData(HsQwPublicRentalEstate hsQwPublicRentalEstate, HttpServletRequest request, HttpServletResponse response) {
		hsQwPublicRentalEstate.setPage(new Page<>(request, response));
		Page<HsQwPublicRentalEstate> page = hsQwPublicRentalEstateService.findPage(hsQwPublicRentalEstate);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("estate:hsQwPublicRentalEstate:view")
	@RequestMapping(value = "form")
	public String form(HsQwPublicRentalEstate hsQwPublicRentalEstate, Model model) {
		model.addAttribute("hsQwPublicRentalEstate", hsQwPublicRentalEstate);
		return "modules/estate/hsQwPublicRentalEstateForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("estate:hsQwPublicRentalEstate:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwPublicRentalEstate hsQwPublicRentalEstate) {
		hsQwPublicRentalEstateService.save(hsQwPublicRentalEstate);
		return renderResult(Global.TRUE, text("保存房源楼盘信息表成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("estate:hsQwPublicRentalEstate:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
		hsQwPublicRentalEstate.setStatus(HsQwPublicRentalEstate.STATUS_DISABLE);
		hsQwPublicRentalEstateService.updateStatus(hsQwPublicRentalEstate);
		return renderResult(Global.TRUE, text("停用房源楼盘信息表成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("estate:hsQwPublicRentalEstate:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
		hsQwPublicRentalEstate.setStatus(HsQwPublicRentalEstate.STATUS_NORMAL);
		hsQwPublicRentalEstateService.updateStatus(hsQwPublicRentalEstate);
		return renderResult(Global.TRUE, text("启用房源楼盘信息表成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("estate:hsQwPublicRentalEstate:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
		hsQwPublicRentalEstateService.delete(hsQwPublicRentalEstate);
		return renderResult(Global.TRUE, text("删除公租房房源楼盘信息表成功！"));
	}

	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("estate:hsQwPublicRentalEstate:view")
	@RequestMapping(value = "hsQwPublicRentalEstateSelect")
	public String hsQwPublicRentalEstateSelect(HsQwPublicRentalEstate hsQwPublicRentalEstate, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwPublicRentalEstate", hsQwPublicRentalEstate);
		return "modules/estate/hsQwPublicRentalEstateSelect";
	}
	
}