package com.hsobs.hs.modules.applyer.service;

import java.util.List;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.dao.HsQwApplyerDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 租赁资格轮候申请人Service
 * <AUTHOR>
 * @version 2024-11-21
 */
@Service
public class HsQwApplyerService extends CrudService<HsQwApplyerDao, HsQwApplyer> {

	@Autowired
	private HsQwApplyService hsQwApplyService;
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyer
	 * @return
	 */
	@Override
	public HsQwApplyer get(HsQwApplyer hsQwApplyer) {
		HsQwApplyer qwApplyer = super.get(hsQwApplyer);
		if (qwApplyer == null){
			return hsQwApplyer;
		}
		qwApplyer.setHsQwApply(hsQwApplyService.get(qwApplyer.getApplyId()));
		return qwApplyer;
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyer 查询条件
	 * @param hsQwApplyer page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyer> findPage(HsQwApplyer hsQwApplyer) {
		Page<HsQwApplyer> page = super.findPage(hsQwApplyer);
//		page.getList().forEach(k -> {
//			k.setHsQwApply(hsQwApplyService.get(k.getApplyId()));
//		});
		return page;
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyer
	 * @return
	 */
	@Override
	public List<HsQwApplyer> findList(HsQwApplyer hsQwApplyer) {
		return super.findList(hsQwApplyer);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyer
	 */
	@Override
	@Transactional
	public void save(HsQwApplyer hsQwApplyer) {
		super.save(hsQwApplyer);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(hsQwApplyer, hsQwApplyer.getId(), "hsQwApplyer_image");
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsQwApplyer, hsQwApplyer.getId(), "hsQwApplyer_file");
	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyer
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyer hsQwApplyer) {
		super.updateStatus(hsQwApplyer);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyer
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyer hsQwApplyer) {
		super.delete(hsQwApplyer);
	}

    public HsQwApplyer getMainApplyer(String applyId) {
		HsQwApplyer query = new HsQwApplyer();
		query.setApplyId(applyId);
		query.setApplyRole("0");
		query.setStatus(HsQwApplyer.STATUS_NORMAL);
		return this.findList(query).stream().findFirst().orElse(null);
    }


}