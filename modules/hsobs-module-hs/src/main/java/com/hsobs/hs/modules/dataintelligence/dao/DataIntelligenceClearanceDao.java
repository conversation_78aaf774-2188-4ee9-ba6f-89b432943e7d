package com.hsobs.hs.modules.dataintelligence.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceClearance;

import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计DAO接口  公租房清退统计
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataIntelligenceClearanceDao extends CrudDao<DataIntelligenceClearance> {

    // String sqlOtherWhere, String sqlOrderBy
    List<Map<String, Object>> countClearanceStat(Map<String, Object> map);

    List<Map<String, Object>> countClearanceCompare(String sqlOtherWhere);

    List<Map<String, Object>> countClearanceTypeStat(String sqlOtherWhere);
}