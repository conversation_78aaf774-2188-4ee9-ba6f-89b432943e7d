package com.hsobs.hs.modules.applyrule.service.HsQwApplyRule;

import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRuleResult;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.ServiceException;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@Service
public class HsQwApplyRuleLh implements IHsQwApplyRule {
    @Override
    public String getRuleConfig() {
        return "10";
    }

    @Override
    public HsQwApplyRuleResult execute(String content, HsQwApplyRule rule) {
        HsQwApplyRuleResult ruleResult = new HsQwApplyRuleResult();
        try {
            LocalDate startDate = LocalDate.parse(content);
            LocalDate currentDate = LocalDate.now();
            
            // 校验未来日期
            if (startDate.isAfter(currentDate)) {
                ruleResult.setData("0");
                ruleResult.setResult(true);
                return ruleResult;
            }
            
            // 计算完整月份差
            long totalMonths = ChronoUnit.MONTHS.between(startDate, currentDate);
            
            // 转换配置月份数
            double configMonths = Double.parseDouble(rule.getRuleContent());
            double scorePerMonth = Double.parseDouble(rule.getRuleResult());
            
            // 校验配置数值有效性
            if(configMonths <= 0 || scorePerMonth <= 0) {
                throw new ServiceException("配置月份数和得分值必须大于0");
            }
            
            // 计算实际月份数
            long actualMonths = totalMonths;
            
            // 计算得分（总月份数/配置月份数 * 每月得分）
            double score = actualMonths >= 1 ? (actualMonths / configMonths * scorePerMonth) : 0;
            
            ruleResult.setData(String.format("%.2f", score));
            ruleResult.setResult(true);
        } catch (DateTimeParseException e) {
            throw new ServiceException("轮候开始时间格式错误，请输入yyyy-MM-dd格式日期");
        } catch (NumberFormatException e) {
            throw new ServiceException("规则配置月份数或得分值格式错误，请输入有效正数");
        }
        return ruleResult;
    }
}
