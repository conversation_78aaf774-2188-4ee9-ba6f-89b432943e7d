package com.hsobs.hs.modules.apply.entity;

import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;

/**
 * 租赁资格轮候申请导出实体
 *
 * <AUTHOR>
 * @version 2024-11-21
 */
public class HsQwApplyExport extends HsQwApply {

    private String order;

    private String preOrder;

    private HsQwApplyer secondApplyer;

    private String other1Name;

    private String other2Name;

    private String other3Name;

    @ExcelFields({
            @ExcelField(title = "轮候排名", attrName = "order", align = Align.CENTER, sort = 10),
            @ExcelField(title = "申请人姓名", attrName = "mainApplyer.name", align = Align.CENTER, sort = 20),
            @ExcelField(title = "工作单位", attrName = "mainApplyer.organization", align = Align.CENTER, sort = 30),
            @ExcelField(title = "家庭成员姓名", attrName = "secondApplyer.name", align = Align.CENTER, sort = 110),
            @ExcelField(title = "家庭成员工作单位", attrName = "secondApplyer.organization", align = Align.CENTER, sort = 120),
            @ExcelField(title = "家庭成员姓名", attrName = "other1Name", align = Align.CENTER, sort = 130),
            @ExcelField(title = "家庭成员姓名", attrName = "other2Name", align = Align.CENTER, sort = 140),
            @ExcelField(title = "家庭成员姓名", attrName = "other3Name", align = Align.CENTER, sort = 150),
            @ExcelField(title = "轮候起算日", attrName = "applyTime", align = Align.CENTER, sort = 150, dataFormat = "yyyy-MM-dd"),
            @ExcelField(title = "申请编号", attrName = "id", align = Align.CENTER, sort = 10),
            @ExcelField(title = "截止当前日期得分", attrName = "applyScore", align = Align.CENTER, sort = 150),
            @ExcelField(title = "备注", attrName = "remarks", align = Align.CENTER, sort = 160),
    })
    public HsQwApplyExport() {
        this(null);
    }

    public HsQwApplyExport(String id) {
        this.id = id;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public HsQwApplyer getSecondApplyer() {
        return secondApplyer;
    }

    public void setSecondApplyer(HsQwApplyer secondApplyer) {
        this.secondApplyer = secondApplyer;
    }

    public String getOther1Name() {
        return other1Name;
    }

    public void setOther1Name(String other1Name) {
        this.other1Name = other1Name;
    }

    public String getOther2Name() {
        return other2Name;
    }

    public void setOther2Name(String other2Name) {
        this.other2Name = other2Name;
    }

    public String getOther3Name() {
        return other3Name;
    }

    public void setOther3Name(String other3Name) {
        this.other3Name = other3Name;
    }

    public String getPreOrder() {
        return preOrder;
    }

    public void setPreOrder(String preOrder) {
        this.preOrder = preOrder;
    }
}