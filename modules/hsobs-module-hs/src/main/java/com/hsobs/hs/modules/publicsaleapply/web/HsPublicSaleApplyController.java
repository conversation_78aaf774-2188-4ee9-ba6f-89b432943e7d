package com.hsobs.hs.modules.publicsaleapply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.publicsaleapply.utils.HsPublicSaleApplyUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.bpm.entity.BpmTask;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.hsobs.hs.modules.publicsaleestate.entity.HsPublicSaleEstate;
import com.hsobs.hs.modules.publicsaleapply.service.HsPublicSaleApplyService;

/**
 * 公有住房配售申请Controller
 * <AUTHOR>
 * @version 2025-02-12
 */
@Controller
@RequestMapping(value = "${adminPath}/publicsaleapply/hsPublicSaleApply")
public class HsPublicSaleApplyController extends BaseController {

	@Autowired
	private HsPublicSaleApplyService hsPublicSaleApplyService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPublicSaleApply get(String id, boolean isNewRecord) {
		return hsPublicSaleApplyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPublicSaleApply hsPublicSaleApply, Model model) {
		model.addAttribute("hsPublicSaleApply", hsPublicSaleApply);
		return "modules/publicsaleapply/hsPublicSaleApplyList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = {"listDone", ""})
	public String listDone(HsPublicSaleApply hsPublicSaleApply, Model model) {
		model.addAttribute("hsPublicSaleApply", hsPublicSaleApply);
		return "modules/publicsaleapply/hsPublicSaleApplyListDone";
	}


	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = {"archives", ""})
	public String archives(HsPublicSaleApply hsPublicSaleApply, Model model) {
		model.addAttribute("hsPublicSaleApply", hsPublicSaleApply);
		return "modules/publicsaleapply/hsPublicSaleApplyListArchives";
	}

    /**
     * 查询代办的审批任务
     */
    @RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
    @RequestMapping(value = "listAuditData")
    @ResponseBody
    public Page<HsPublicSaleApply> listAuditData(HsPublicSaleApply hsPublicSaleApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleApply.setPage(new Page<>(request, response));
        Page<HsPublicSaleApply> page = hsPublicSaleApplyService.findAuditPageByTasks(hsPublicSaleApply, BpmTask.STATUS_UNFINISHED);
        return page;
    }

	/**
	 * 查询代办的审批任务
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "listAuditDoneData")
	@ResponseBody
	public Page<HsPublicSaleApply> listAuditDoneData(HsPublicSaleApply hsPublicSaleApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleApply.setPage(new Page<>(request, response));
		Page<HsPublicSaleApply> page = hsPublicSaleApplyService.findAuditPageByTasks(hsPublicSaleApply, BpmTask.STATUS_FINISHED);
		return page;
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsPublicSaleApply> listData(HsPublicSaleApply hsPublicSaleApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleApply.setPage(new Page<>(request, response));
		Page<HsPublicSaleApply> page = hsPublicSaleApplyService.findPage(hsPublicSaleApply);
		return page;
	}
	
	/**
	 * 查询子表数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "hsPublicSaleEstateListData")
	@ResponseBody
	public Page<HsPublicSaleEstate> subListData(HsPublicSaleEstate hsPublicSaleEstate, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleEstate.setPage(new Page<>(request, response));
		Page<HsPublicSaleEstate> page = hsPublicSaleApplyService.findSubPage(hsPublicSaleEstate);
		return page;
	}

	/**
	 * 查询代办的审批任务
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "listArchivesData")
	@ResponseBody
	public Page<HsPublicSaleApply> listArchivesData(HsPublicSaleApply hsPublicSaleApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleApply.setPage(new Page<>(request, response));
		Page<HsPublicSaleApply> page = hsPublicSaleApplyService.findArchivesPage(hsPublicSaleApply);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "form")
	public String form(HsPublicSaleApply hsPublicSaleApply, Model model) {
		model.addAttribute("hsPublicSaleApply", hsPublicSaleApply);
model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/publicsaleapply/hsPublicSaleApplyForm";
	}

	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "archives/form")
	public String form_archives(HsPublicSaleApply hsPublicSaleApply, Model model) {
		model.addAttribute("hsPublicSaleApply", hsPublicSaleApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/publicsaleapply/hsPublicSaleApplyFormDone";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsPublicSaleApply hsPublicSaleApply) {
		hsPublicSaleApplyService.save(hsPublicSaleApply);
		if (hsPublicSaleApply.getIsNewRecord()) {
			return renderResult(Global.TRUE, text("新增公有住房配售申请成功！"));
		}
		return renderResult(Global.TRUE, text("审核成功！"));
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:edit")
	@PostMapping(value = "flushTaskStatus")
	@ResponseBody
	public String flushTaskStatus(@Validated HsPublicSaleApply hsPublicSaleApply) {
		hsPublicSaleApplyService.flushTaskStatus(hsPublicSaleApply);
		return renderResult(Global.TRUE, text("刷新公有住房配售申请状态成功！"));
	}
	
	/**
	 * 导出数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "exportAuditData")
	public void exportData(HsPublicSaleApply hsPublicSaleApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleApply.setPage(new Page<>(request, response));
		Page<HsPublicSaleApply> page = hsPublicSaleApplyService.findAuditPageByTasks(hsPublicSaleApply, BpmTask.STATUS_UNFINISHED);
		String fileName = "公有住房配售申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("公有住房配售申请信息表", HsPublicSaleApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "exportAuditDoneData")
	public void exportAuditDoneData(HsPublicSaleApply hsPublicSaleApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleApply.setPage(new Page<>(request, response));
		Page<HsPublicSaleApply> page = hsPublicSaleApplyService.findAuditPageByTasks(hsPublicSaleApply, BpmTask.STATUS_FINISHED);
		String fileName = "公有住房配售申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("公有住房配售申请信息表", HsPublicSaleApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:view")
	@RequestMapping(value = "exportArchivesData")
	public void exportArchivesData(HsPublicSaleApply hsPublicSaleApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleApply.setPage(new Page<>(request, response));
		Page<HsPublicSaleApply> page = hsPublicSaleApplyService.findArchivesPage(hsPublicSaleApply);
		String fileName = "公有住房配售档案" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("公有住房配售档案", HsPublicSaleApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	/**
	 * 停用数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsPublicSaleApply hsPublicSaleApply) {
		hsPublicSaleApply.setStatus(HsPublicSaleApply.STATUS_DISABLE);
		hsPublicSaleApplyService.updateStatus(hsPublicSaleApply);
		return renderResult(Global.TRUE, text("停用公有住房配售申请成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsPublicSaleApply hsPublicSaleApply) {
		hsPublicSaleApply.setStatus(HsPublicSaleApply.STATUS_NORMAL);
		hsPublicSaleApplyService.updateStatus(hsPublicSaleApply);
		return renderResult(Global.TRUE, text("启用公有住房配售申请成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("publicsaleapply:hsPublicSaleApply:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsPublicSaleApply hsPublicSaleApply) {
		if (!HsPublicSaleApply.STATUS_DRAFT.equals(hsPublicSaleApply.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		hsPublicSaleApplyService.delete(hsPublicSaleApply);
		return renderResult(Global.TRUE, text("删除公有住房配售申请成功！"));
	}
	
}