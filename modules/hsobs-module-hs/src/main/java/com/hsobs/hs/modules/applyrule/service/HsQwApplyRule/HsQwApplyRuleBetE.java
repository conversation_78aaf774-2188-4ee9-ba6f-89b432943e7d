package com.hsobs.hs.modules.applyrule.service.HsQwApplyRule;

import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRuleResult;
import com.jeesite.common.service.ServiceException;
import org.springframework.stereotype.Service;

@Service
public class HsQwApplyRuleBetE implements IHsQwApplyRule {
    @Override
    public String getRuleConfig() {
        return "4";
    }

    @Override
    public HsQwApplyRuleResult execute(String content, HsQwApplyRule rule) {
        HsQwApplyRuleResult ruleResult = new HsQwApplyRuleResult();
        try {
            // 将字符串转换为浮动数字
            double num1 = Double.parseDouble(content);
            String[] strNum = rule.getRuleContent().split(",");
            double num2 = Double.parseDouble(strNum[0]);
            double num3 = Double.parseDouble(strNum[1]);

            // 比较两个数字
            if ( num2 <= num1  && (num1 <= num3)) {
                ruleResult.setData(rule.getRuleResult());
                ruleResult.setResult(true);
            }
        } catch (NumberFormatException e) {
            throw new ServiceException("资格轮候评分规则配置【=<<=】错误，影响评分计算，请及时纠正！");
        }
        return ruleResult;
    }
}
