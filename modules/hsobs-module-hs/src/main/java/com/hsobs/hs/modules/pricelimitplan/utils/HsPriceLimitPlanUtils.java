/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.hsobs.hs.modules.pricelimitplan.utils;

import com.hsobs.hs.modules.pricelimitplan.entity.HsPriceLimitPlan;
import com.hsobs.hs.modules.pricelimitplan.service.HsPriceLimitPlanService;
import com.jeesite.common.utils.SpringUtils;

import java.util.List;

/**
 * CmsUtils
 *
 * <AUTHOR>
 * @version 2020-7-24
 */
public class HsPriceLimitPlanUtils {

    private static final class Static {
        private static final HsPriceLimitPlanService planService = SpringUtils.getBean(HsPriceLimitPlanService.class);
    }

    /**
     * 获得站点列表
     */
    public static List<HsPriceLimitPlan> getPriceLimitPlanList() {

        return Static.planService.findList(new HsPriceLimitPlan());
    }
}