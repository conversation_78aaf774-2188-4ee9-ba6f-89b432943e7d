package com.hsobs.hs.modules.formalarm.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 表单信息预警表Entity
 * <AUTHOR>
 * @version 2025-03-22
 */
@Table(name="hs_qw_form_alarm", alias="a", label="表单信息预警表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="object_id", attrName="objectId", label="对象编号"),
		@Column(name="attr_key", attrName="attrKey", label="属性主键"),
		@Column(name="alarm_type", attrName="alarmType", label="预警类型", comment="预警类型(0不符合规则 1信息变更)"),
		@Column(name="alarm_info", attrName="alarmInfo", label="预警信息"),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsQwFormAlarm extends DataEntity<HsQwFormAlarm> {
	
	private static final long serialVersionUID = 1L;
	private String objectId;		// 对象编号
	private String attrKey;		// 属性主键
	private String alarmType;		// 预警类型(0不符合规则 1信息变更)
	private String alarmInfo;		// 预警信息

	public HsQwFormAlarm() {
		this(null);
	}
	
	public HsQwFormAlarm(String id){
		super(id);
	}
	
	@NotBlank(message="对象编号不能为空")
	@Size(min=0, max=64, message="对象编号长度不能超过 64 个字符")
	public String getObjectId() {
		return objectId;
	}

	public void setObjectId(String objectId) {
		this.objectId = objectId;
	}
	
	@NotBlank(message="属性主键不能为空")
	@Size(min=0, max=10, message="属性主键长度不能超过 10 个字符")
	public String getAttrKey() {
		return attrKey;
	}

	public void setAttrKey(String attrKey) {
		this.attrKey = attrKey;
	}
	
	@Size(min=0, max=1, message="预警类型长度不能超过 1 个字符")
	public String getAlarmType() {
		return alarmType;
	}

	public void setAlarmType(String alarmType) {
		this.alarmType = alarmType;
	}
	
	@NotBlank(message="预警信息不能为空")
	@Size(min=0, max=500, message="预警信息长度不能超过 500 个字符")
	public String getAlarmInfo() {
		return alarmInfo;
	}

	public void setAlarmInfo(String alarmInfo) {
		this.alarmInfo = alarmInfo;
	}
	
}