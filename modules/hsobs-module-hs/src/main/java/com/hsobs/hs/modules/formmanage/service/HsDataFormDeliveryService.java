package com.hsobs.hs.modules.formmanage.service;

import com.hsobs.hs.modules.formmanage.dao.HsDataFormDeliveryDao;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormDelivery;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormDeliveryRecord;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormReport;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.web.http.ServletUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.EmpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 数据表单下发Service
 * <AUTHOR>
 * @version 2025-02-16
 */
@Service
public class HsDataFormDeliveryService extends CrudService<HsDataFormDeliveryDao, HsDataFormDelivery> {

	@Autowired
	private EmpUserService empUserService;
	@Autowired
	private HsDataFormDeliveryRecordService hsDataFormDeliveryRecordService;
	@Autowired
	private HsDataFormReportService hsDataFormReportService;

	/**
	 * 获取单条数据
	 * @param hsDataFormDelivery
	 * @return
	 */
	@Override
	public HsDataFormDelivery get(HsDataFormDelivery hsDataFormDelivery) {
		return super.get(hsDataFormDelivery);
	}
	
	/**
	 * 查询分页数据
	 * @param hsDataFormDelivery 查询条件
	 * @param hsDataFormDelivery page 分页对象
	 * @return
	 */
	@Override
	public Page<HsDataFormDelivery> findPage(HsDataFormDelivery hsDataFormDelivery) {
		return super.findPage(hsDataFormDelivery);
	}

	public Page<HsDataFormDelivery> reportListPage(HsDataFormDelivery hsDataFormDelivery) {
		//TODO 数据过滤
		// 数据过滤权限  过滤自由用户相关权限的数据
		User user = hsDataFormDelivery.currentUser();
		if (!user.isAdmin()){
			String currentOfficeCode = EmpUtils.getCurrentOfficeCode();
			hsDataFormDelivery.sqlMap().add("extForm", " LEFT JOIN (SELECT t2.DELIVERY_ID from HS_DATA_FORM_REPORT t2 WHERE t2.REPORT_OFFICE_CODE = '" + currentOfficeCode + "' GROUP BY t2.DELIVERY_ID) hdfr on a.ID = hdfr.DELIVERY_ID ");
			hsDataFormDelivery.sqlMap().getWhere().andBracket("a.send_user_code", QueryType.EQ, user.getUserCode())
					.or("hdfr.DELIVERY_ID", QueryType.IS_NOT_NULL, null).endBracket();
		}
		return super.findPage(hsDataFormDelivery);
	}

	
	/**
	 * 查询列表数据
	 * @param hsDataFormDelivery
	 * @return
	 */
	@Override
	public List<HsDataFormDelivery> findList(HsDataFormDelivery hsDataFormDelivery) {
		return super.findList(hsDataFormDelivery);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsDataFormDelivery
	 */
	@Override
	@Transactional
	public void save(HsDataFormDelivery hsDataFormDelivery) {

		if (StringUtils.isEmpty(hsDataFormDelivery.getTemplateCode()) || StringUtils.isEmpty(hsDataFormDelivery.getReceiveCodes())) {
			throw new ServiceException("无效请求.");
		}

		if (hsDataFormDelivery.getIsNewRecord()){
			User user = hsDataFormDelivery.currentUser();
			hsDataFormDelivery.setSendUserCode(user.getUserCode());
			hsDataFormDelivery.setSendUserName(user.getUserName());
			// 没有设置状态，则默认新增后是草稿状态
			if (StringUtils.isBlank(hsDataFormDelivery.getStatus())){
				hsDataFormDelivery.setStatus(HsDataFormDelivery.STATUS_NORMAL);
			}
		}
		hsDataFormDelivery.setSendDate(new Date());
		hsDataFormDelivery.setIsAttac(hsDataFormDelivery.getDataMap() != null && StringUtils.isNotBlank((String)hsDataFormDelivery.getDataMap().get("hsDataFormDelivery_file"))
				|| StringUtils.isNotBlank(ServletUtils.getParameter("hsDataFormDelivery_file")) ? Global.YES : Global.NO);
		super.save(hsDataFormDelivery);
		// 发送内部消息
		if (HsDataFormDelivery.STATUS_NORMAL.equals(hsDataFormDelivery.getStatus())){
			this.updateStatus(hsDataFormDelivery); // 更新状态
			List<EmpUser> empUserList = null;
			if (HsDataFormDelivery.RECEIVE_TYPE_ALL.equals(hsDataFormDelivery.getReceiveType())){
				EmpUser empUser = new EmpUser();
				empUser.setCodes(new String[]{});
				empUserList = empUserService.findUserList(empUser);
			}else{
				String[] codes = StringUtils.splitComma(hsDataFormDelivery.getReceiveCodes());
				String[] names = StringUtils.splitComma(hsDataFormDelivery.getReceiveNames());
				if (codes != null && names != null && codes.length > 0 && codes.length == names.length){
					EmpUser empUser = new EmpUser();
					empUser.setCodes(codes);
					switch(hsDataFormDelivery.getReceiveType()){
						case HsDataFormDelivery.RECEIVE_TYPE_USER:
							empUserList = ListUtils.newArrayList();
							for (int i=0; i<codes.length; i++){
								EmpUser e = new EmpUser();
								e.setUserCode(codes[i]);
								e.setUserName(names[i]);
								empUserList.add(e);
							}
							break;
						case HsDataFormDelivery.RECEIVE_TYPE_OFFICE:
							empUserList = empUserService.findUserListByOfficeCodes(empUser);
							break;
						case HsDataFormDelivery.RECEIVE_TYPE_ROLE:
							empUserList = empUserService.findUserListByRoleCodes(empUser);
							break;
						case HsDataFormDelivery.RECEIVE_TYPE_POST:
							empUserList = empUserService.findUserListByPostCodes(empUser);
							break;
					}
				}
			}
			this.saveHsDataFormDeliveryRecord(hsDataFormDelivery, empUserList);
		}
	}

	private void saveHsDataFormDeliveryRecord(HsDataFormDelivery hsDataFormDelivery, List<EmpUser> empUserList) {
		if (empUserList == null || empUserList.size() <= 0){
			return;
		}
		List<HsDataFormDeliveryRecord> recordList = ListUtils.newArrayList();
		empUserList.forEach(user -> {
			HsDataFormDeliveryRecord r = new HsDataFormDeliveryRecord();
			r.setDeliveryId(hsDataFormDelivery.getId());
			r.setReceiveUserCode(user.getUserCode());
			r.setReceiveUserName(user.getUserName());
			r.setReadStatus(HsDataFormDeliveryRecord.READ_STATUS_UNREAD);
			r.setFillStatus(HsDataFormDeliveryRecord.FILL_STATUS_UNFILL);
			r.setUploadStatus(HsDataFormDeliveryRecord.UPLOAD_STATUS_UN);
			recordList.add(r);
		});
		hsDataFormDeliveryRecordService.insertBatch(recordList, 100);
	}

	/**
	 * 更新状态
	 * @param hsDataFormDelivery
	 */
	@Override
	@Transactional
	public void updateStatus(HsDataFormDelivery hsDataFormDelivery) {
		super.updateStatus(hsDataFormDelivery);
	}
	
	/**
	 * 删除数据
	 * @param hsDataFormDelivery
	 */
	@Override
	@Transactional
	public void delete(HsDataFormDelivery hsDataFormDelivery) {
		super.delete(hsDataFormDelivery);
	}


	public void saveReport(HsDataFormDelivery hsDataFormDelivery) {

		User user = hsDataFormDelivery.currentUser();
		if (StringUtils.isEmpty(hsDataFormDelivery.getIds())
				|| StringUtils.isEmpty(hsDataFormDelivery.getReceiveCodes())
				|| user == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		String[] ids = StringUtils.splitComma(hsDataFormDelivery.getIds());
		String[] receiveCodes = StringUtils.splitComma(hsDataFormDelivery.getReceiveCodes());

		List<HsDataFormReport> reportList = ListUtils.newArrayList();
		HsDataFormReport report;
		for (String id : ids) {
			HsDataFormDelivery delivery = super.get(id);
			if (delivery == null) {
				continue;
			}
			for (String receiveCode : receiveCodes) {
				Office office = EmpUtils.getOffice(receiveCode);
				if (office == null) {
					continue;
				}
				report = new HsDataFormReport();
				report.setDeliveryId(id);
				report.setTemplateId(delivery.getTemplateCode());
				report.setReportOfficeCode(office.getOfficeCode());
				report.setReportOfficeName(office.getOfficeName());
				report.setReportDate(new Date());
				report.setReportUserCode(user.getUserCode());
				report.setReportUserName(user.getUserName());
				report.setAuditStatus("1");
				report.setAuditComment("");
				report.setAuditTime(new Date());
				report.setValidTag("1");
				report.setRemarks("");
				report.setStatus("0");
				reportList.add(report);
			}
		}
		if (!reportList.isEmpty()) {
			hsDataFormReportService.insertBatch(reportList, 100);
		}

		for (String id : ids) {
			HsDataFormDeliveryRecord record = new HsDataFormDeliveryRecord();
			record.setDeliveryId(id);
			record.setFillStatus(HsDataFormDeliveryRecord.FILL_STATUS_FILL);
			record.setReadDate(new Date());
			record.setReadStatus(HsDataFormDeliveryRecord.READ_STATUS_READ);
			hsDataFormDeliveryRecordService.updateReadStatus(record);
		}

	}
}