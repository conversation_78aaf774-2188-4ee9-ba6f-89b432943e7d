package com.hsobs.hs.modules.maintenance.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFundsHis;
import com.hsobs.hs.modules.maintenance.service.HsMaintenanceFundsHisService;

/**
 * 维修资金变动记录表Controller
 * <AUTHOR>
 * @version 2025-03-15
 */
@Controller
@RequestMapping(value = "${adminPath}/maintenance/hsMaintenanceFundsHis")
public class HsMaintenanceFundsHisController extends BaseController {

	@Autowired
	private HsMaintenanceFundsHisService hsMaintenanceFundsHisService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsMaintenanceFundsHis get(String id, boolean isNewRecord) {
		return hsMaintenanceFundsHisService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFundsHis:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsMaintenanceFundsHis hsMaintenanceFundsHis, Model model) {
		model.addAttribute("hsMaintenanceFundsHis", hsMaintenanceFundsHis);
		return "modules/maintenance/hsMaintenanceFundsHisList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFundsHis:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsMaintenanceFundsHis> listData(HsMaintenanceFundsHis hsMaintenanceFundsHis, HttpServletRequest request, HttpServletResponse response) {
		hsMaintenanceFundsHis.setPage(new Page<>(request, response));
		Page<HsMaintenanceFundsHis> page = hsMaintenanceFundsHisService.findPage(hsMaintenanceFundsHis);
		return page;
	}

}