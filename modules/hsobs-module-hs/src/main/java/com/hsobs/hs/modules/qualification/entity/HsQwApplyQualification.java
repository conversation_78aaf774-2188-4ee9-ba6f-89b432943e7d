package com.hsobs.hs.modules.qualification.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候资格配置表Entity
 * <AUTHOR>
 * @version 2025-03-13
 */
@Table(name="hs_qw_apply_qualification", alias="a", label="租赁资格轮候资格配置表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="qua_name", attrName="quaName", label="资格名称", queryType=QueryType.LIKE),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyQualification extends DataEntity<HsQwApplyQualification> {
	
	private static final long serialVersionUID = 1L;
	private String quaName;		// 资格名称

	public HsQwApplyQualification() {
		this(null);
	}
	
	public HsQwApplyQualification(String id){
		super(id);
	}
	
	@NotBlank(message="资格名称不能为空")
	@Size(min=0, max=100, message="资格名称长度不能超过 100 个字符")
	public String getQuaName() {
		return quaName;
	}

	public void setQuaName(String quaName) {
		this.quaName = quaName;
	}
	
}