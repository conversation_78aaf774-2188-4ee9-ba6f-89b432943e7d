package com.hsobs.hs.modules.external.entity;

public class ApiAccountPermission extends ApiBody{

    private String xm; //姓名

    private String mkbm; //模块编码

    private String mkmc; //模块名称

    private String qxjp; //权限结果

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getMkbm() {
        return mkbm;
    }

    public void setMkbm(String mkbm) {
        this.mkbm = mkbm;
    }

    public String getMkmc() {
        return mkmc;
    }

    public void setMkmc(String mkmc) {
        this.mkmc = mkmc;
    }

    public String getQxjp() {
        return qxjp;
    }

    public void setQxjp(String qxjp) {
        this.qxjp = qxjp;
    }
}
