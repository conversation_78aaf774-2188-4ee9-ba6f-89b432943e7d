package com.hsobs.hs.modules.dataintelligence.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.dataintelligence.service.DataIntelligenceLeaseService;

import java.util.List;

class DataIntelligenceLeaseExcel1 extends DataEntity<DataIntelligenceLeaseExcel1> {

	@ExcelFields({
			@ExcelField(title = "小区名称", attrName = "estateName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "总空置数", attrName = "vacantCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "公租房空置数", attrName = "vacant0Count", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "局直公房空置数", attrName = "vacant3Count", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "自管公房空置数", attrName = "vacant4Count", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "总空置面积(m²)", attrName = "vacantArea", align = ExcelField.Align.CENTER, sort = 40),
			@ExcelField(title = "公租房空置面积(m²)", attrName = "vacant0Area", align = ExcelField.Align.CENTER, sort = 50),
			@ExcelField(title = "局直公房空置面积(m²)", attrName = "vacant3Area", align = ExcelField.Align.CENTER, sort = 60),
			@ExcelField(title = "自管公房空置面积(m²)", attrName = "vacant4Area", align = ExcelField.Align.CENTER, sort = 70),
	})
	public DataIntelligenceLeaseExcel1(){
	}
}
class DataIntelligenceLeaseExcel2 extends DataEntity<DataIntelligenceLeaseExcel2> {

	@ExcelFields({
			@ExcelField(title = "工作单位", attrName = "officeName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "已入住数", attrName = "checkInCount", align = ExcelField.Align.CENTER, sort = 30),
	})
	public DataIntelligenceLeaseExcel2(){
	}
}

/**
 * 住房保障数据统计Controller  公租房租赁情况统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Controller
@RequestMapping(value = "${adminPath}/dataintelligencelease/")
public class DataIntelligenceLeaseController extends BaseController {

	@Autowired
	private DataIntelligenceLeaseService dataIntelligenceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataIntelligenceLease get(Integer type, boolean isNewRecord) {
		return dataIntelligenceService.get(type, isNewRecord);
	}

	/**
	 * 公租房租赁情况统计
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = {"dataIntelligenceLease", ""})
	public String dataIntelligenceLease(DataIntelligenceLease dataIntelligenceLease, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceLease);
		return "modules/dataintelligence/dataIntelligenceLease";
	}

	/**
	 * 公租房租赁情况统计
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = {"dataIntelligenceLeaseArea", ""})
	public String dataIntelligenceLeaseArea(DataIntelligenceLease dataIntelligenceLease, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceLease);
		return "modules/dataintelligence/dataIntelligenceLeaseArea";
	}

	/**
	 * 查询
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countAreaStat")
	@ResponseBody
	public Page<DataIntelligenceLease> countAreaStat(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLease.setPage(new Page<>(request, response));
		Page<DataIntelligenceLease> page = dataIntelligenceService.findAreaStatDataPage(dataIntelligenceLease, true);
		return page;
	}

	/**
	 * 查询
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countLeasePaidStatByOffice")
	@ResponseBody
	public Page<DataIntelligenceLease> countLeasePaidStatByOffice(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLease.setPage(new Page<>(request, response));
		Page<DataIntelligenceLease> page = dataIntelligenceService.findAreaStatDataByOfficePage(dataIntelligenceLease, true);
		return page;
	}

	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countLeaseAreaStatByOffice")
	@ResponseBody
	public Page<DataIntelligenceLease> countLeaseAreaStatByOffice(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLease.setPage(new Page<>(request, response));
		Page<DataIntelligenceLease> page = dataIntelligenceService.findLeaseAreaStatDataPage(dataIntelligenceLease, true);
		return page;
	}


	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countLeaseTotalStat")
	@ResponseBody
	public Page<DataIntelligenceLease> countLeaseTotalStat(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLease.setPage(new Page<>(request, response));
		Page<DataIntelligenceLease> page = dataIntelligenceService.findTotalStat(dataIntelligenceLease);
		return page;
	}

	/**
	 * 查询
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countLeaseOfficeStat")
	@ResponseBody
	public Page<DataIntelligenceLease> countLeaseOfficeStat(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLease.setPage(new Page<>(request, response));
		Page<DataIntelligenceLease> page = dataIntelligenceService.findLeaseOfficeStatDataPage(dataIntelligenceLease, true);
		return page;
	}

	/**
	 * 查询
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countLeaseFeeStat")
	@ResponseBody
	public Page<DataIntelligenceLease> countLeaseFeeStat(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLease.setPage(new Page<>(request, response));
		Page<DataIntelligenceLease> page = dataIntelligenceService.findLeaseAreaStatDataPage(dataIntelligenceLease, true);
		return page;
	}

	/**
	 * 查询
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countBuildingAreaStat")
	@ResponseBody
	public Page<DataIntelligenceLease> countBuildingAreaStat(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLease.setPage(new Page<>(request, response));
		Page<DataIntelligenceLease> page = dataIntelligenceService.findLeaseBuildingAreaPage(dataIntelligenceLease, true);
		return page;
	}

	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countVacantAreaStat")
	@ResponseBody
	public Page<DataIntelligenceLease> countVacantAreaStat(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLease.setPage(new Page<>(request, response));
		Page<DataIntelligenceLease> page = dataIntelligenceService.findVacantAreaPage(dataIntelligenceLease, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countUnpadByOffice")
	@ResponseBody
	public String countUnpadByOffice(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.countUnpadByOffice(dataIntelligenceLease, 1);
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "countUnpaidDurationOffice")
	@ResponseBody
	public String countUnpaidDurationOffice(DataIntelligenceLease dataIntelligenceLease, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.countUnpadByOffice(dataIntelligenceLease, 2);
	}
	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligencelease::view")
	@RequestMapping(value = "exportData")
	public void exportData(DataIntelligenceLease dataIntelligenceLease, HttpServletResponse response) {
		String fileName = "公租房租赁情况统计表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("公租房租赁统计", DataIntelligenceLease.class);
			List<DataIntelligenceLease> list = dataIntelligenceService.countAreaStat(dataIntelligenceLease);
			ee.setDataList(list);

			ee.createSheet("公租房空置房源分析", "公租房空置房源分析", DataIntelligenceLeaseExcel1.class, ExcelField.Type.EXPORT);
			list = dataIntelligenceService.countVacantAreaStat(dataIntelligenceLease);
			ee.setDataList(list);

			ee.createSheet("公租房入住分析", "公租房入住分析", DataIntelligenceLeaseExcel2.class, ExcelField.Type.EXPORT);
			list = dataIntelligenceService.countLeaseOfficeStat(dataIntelligenceLease);
			ee.setDataList(list);

			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}
}