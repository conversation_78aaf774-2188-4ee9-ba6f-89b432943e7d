package com.hsobs.hs.modules.applyscore.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * hs_qw_apply_score_detailEntity
 * <AUTHOR>
 * @version 2025-03-22
 */
@Table(name="hs_qw_apply_score_detail", alias="a", label="hs_qw_apply_score_detail信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="apply_id", attrName="applyId", label="申请单ID"),
		@Column(name="rule_type", attrName="ruleType", label="规则得分类型", comment="规则得分类型（0家庭人均住房建筑面积 1共同申请人数 2家庭人均年收入 3申请人累计工龄 4轮候时间累计）"),
		@Column(name="score", attrName="score", label="轮候类型得分"),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyScoreDetail extends DataEntity<HsQwApplyScoreDetail> {
	
	private static final long serialVersionUID = 1L;
	private String applyId;		// 申请单ID
	private String ruleType;		// 规则得分类型（0家庭人均住房建筑面积 1共同申请人数 2家庭人均年收入 3申请人累计工龄 4轮候时间累计）
	private Double score;		// 轮候类型得分

	public HsQwApplyScoreDetail() {
		this(null);
	}
	
	public HsQwApplyScoreDetail(String id){
		super(id);
	}
	
	@NotBlank(message="申请单ID不能为空")
	@Size(min=0, max=64, message="申请单ID长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@NotBlank(message="规则得分类型不能为空")
	@Size(min=0, max=1, message="规则得分类型长度不能超过 1 个字符")
	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}
	
	@NotNull(message="轮候类型得分不能为空")
	public Double getScore() {
		return score;
	}

	public void setScore(Double score) {
		this.score = score;
	}
	
}