package com.hsobs.hs.modules.apply.service.applyCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公租房申请校验
 */
@Service
public class HsQwApplyCheckApply extends HsQwApplyCheckBase implements HsQwApplyCheck {

    @Override
    public String getApplyType() {
        return "0";//公租申请
    }

    @Override
    public void execute(HsQwApply hsQwApply) {
        //黑名单校验
        super.blackCheck(hsQwApply);

        //申请单校验
        HsQwApply entity = new HsQwApply();
        entity.sqlMap().getWhere().disableAutoAddStatusWhere();
        entity.setMainApplyer(new HsQwApplyer());
        entity.getMainApplyer().setUserId(this.getRealApplyUser(hsQwApply));//主申请人是申请者
        //是否存在信息变更和房屋置换变更申请单判定
        String extWhere = " and a.APPLY_MATTER IN (  0,1, 2, 4 ) AND a.STATUS IN (0,4)";
        //流程中的申请单校验
        if (!super.processCheck(hsQwApply, null, null, this.getApplyType(), extWhere)){
            throw new ServiceException("申请资格验证失败，存在流程中或者已有的申请单！");
        }
    }



}
