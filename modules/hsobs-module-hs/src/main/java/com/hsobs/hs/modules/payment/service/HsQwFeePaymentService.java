package com.hsobs.hs.modules.payment.service;

import java.util.Date;
import java.util.List;

import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.hsobs.hs.modules.rentfee.service.HsQwRentalFeeService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.payment.entity.HsQwFeePayment;
import com.hsobs.hs.modules.payment.dao.HsQwFeePaymentDao;

/**
 * 租赁资格轮候物业费催单Service
 * <AUTHOR>
 * @version 2025-01-20
 */
@Service
public class HsQwFeePaymentService extends CrudService<HsQwFeePaymentDao, HsQwFeePayment> {
	@Autowired
	private EmpUserService empUserService;

	@Autowired
	private ApiSzkjService apiSzkjService;

	@Autowired
	private HsQwRentalFeeService hsQwRentalFeeService;

	/**
	 * 获取单条数据
	 * @param hsQwFeePayment
	 * @return
	 */
	@Override
	public HsQwFeePayment get(HsQwFeePayment hsQwFeePayment) {
		return super.get(hsQwFeePayment);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwFeePayment 查询条件
	 * @param hsQwFeePayment page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwFeePayment> findPage(HsQwFeePayment hsQwFeePayment) {
		return super.findPage(hsQwFeePayment);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwFeePayment
	 * @return
	 */
	@Override
	public List<HsQwFeePayment> findList(HsQwFeePayment hsQwFeePayment) {
		return super.findList(hsQwFeePayment);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwFeePayment
	 */
	@Override
	@Transactional
	public void save(HsQwFeePayment hsQwFeePayment) {
		//保存催收单
		super.save(hsQwFeePayment);
		try {
			HsQwRentalFee query = new HsQwRentalFee();
			query.setId(hsQwFeePayment.getFeeId());
			HsQwRentalFee hsQwRentalFee = hsQwRentalFeeService.get(query);
			// 下发消息到数字空间
			Api2NoticeBody body = new Api2NoticeBody();
			body.setMsgId(hsQwFeePayment.getId());
			body.setMsgType("1");		// 公告
			body.setMsgSource("租金催收单");
			body.setTopic("租金催收单");
			body.setContent(hsQwFeePayment.getRemarks());
			body.setFileIds("");
			body.setPublishUnit(EmpUtils.getOffice().getOfficeName());
			body.setPublishUnitId(EmpUtils.getOffice().getOfficeCode());
			EmpUser queryUser = new EmpUser();
			queryUser.setUserCode(UserUtils.getUser().getUserCode());
			body.setPublishTime(new Date());
//			EmpUser empUser = empUserService.get(queryUser);
//			String receiveUserIds = empUser.getExtAspId();
			body.setReceiveUserIds("all"); //临时改为all
//			body.setReceiveUserIds(receiveUserIds);
			Api2ResponseBody result2 = apiSzkjService.uploadNotice(body);
			if(result2.getCode() != 1) {
				throw new ServiceException(result2.getMessage());
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}
	}
	
	/**
	 * 更新状态
	 * @param hsQwFeePayment
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwFeePayment hsQwFeePayment) {
		super.updateStatus(hsQwFeePayment);
	}
	
	/**
	 * 删除数据
	 * @param hsQwFeePayment
	 */
	@Override
	@Transactional
	public void delete(HsQwFeePayment hsQwFeePayment) {
		super.delete(hsQwFeePayment);
	}
	
}