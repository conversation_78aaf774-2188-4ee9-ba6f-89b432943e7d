package com.hsobs.hs.modules.maintenance.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFundsHis;
import com.hsobs.hs.modules.maintenance.dao.HsMaintenanceFundsHisDao;

/**
 * 维修资金变动记录表Service
 * <AUTHOR>
 * @version 2025-03-15
 */
@Service
public class HsMaintenanceFundsHisService extends CrudService<HsMaintenanceFundsHisDao, HsMaintenanceFundsHis> {
	
	/**
	 * 获取单条数据
	 * @param hsMaintenanceFundsHis
	 * @return
	 */
	@Override
	public HsMaintenanceFundsHis get(HsMaintenanceFundsHis hsMaintenanceFundsHis) {
		return super.get(hsMaintenanceFundsHis);
	}
	
	/**
	 * 查询分页数据
	 * @param hsMaintenanceFundsHis 查询条件
	 * @param hsMaintenanceFundsHis page 分页对象
	 * @return
	 */
	@Override
	public Page<HsMaintenanceFundsHis> findPage(HsMaintenanceFundsHis hsMaintenanceFundsHis) {
		if (hsMaintenanceFundsHis.getFundsId() == null) {
			return hsMaintenanceFundsHis.getPage();
		}
		return super.findPage(hsMaintenanceFundsHis);
	}
	
	/**
	 * 查询列表数据
	 * @param hsMaintenanceFundsHis
	 * @return
	 */
	@Override
	public List<HsMaintenanceFundsHis> findList(HsMaintenanceFundsHis hsMaintenanceFundsHis) {
		return super.findList(hsMaintenanceFundsHis);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsMaintenanceFundsHis
	 */
	@Override
	@Transactional
	public void save(HsMaintenanceFundsHis hsMaintenanceFundsHis) {
		super.save(hsMaintenanceFundsHis);
	}
	
	/**
	 * 更新状态
	 * @param hsMaintenanceFundsHis
	 */
	@Override
	@Transactional
	public void updateStatus(HsMaintenanceFundsHis hsMaintenanceFundsHis) {
		super.updateStatus(hsMaintenanceFundsHis);
	}
	
	/**
	 * 删除数据
	 * @param hsMaintenanceFundsHis
	 */
	@Override
	@Transactional
	public void delete(HsMaintenanceFundsHis hsMaintenanceFundsHis) {
		hsMaintenanceFundsHis.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsMaintenanceFundsHis);
	}
	
}