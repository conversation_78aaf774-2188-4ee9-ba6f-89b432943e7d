package com.hsobs.hs.modules.contract.web;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.contract.entity.HsContractRecord;
import com.hsobs.hs.modules.contract.entity.HsContractRecordData;
import com.hsobs.hs.modules.contract.entity.HsContractRecordField;
import com.hsobs.hs.modules.contract.entity.HsContractTemplate;
import com.hsobs.hs.modules.contract.service.HsContractRecordDataService;
import com.hsobs.hs.modules.contract.service.HsContractRecordFieldService;
import com.hsobs.hs.modules.contract.service.HsContractRecordService;
import com.hsobs.hs.modules.contract.service.HsContractTemplateService;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 合同Controller
 * <AUTHOR>
 * @version 2025-01-22
 */
@Controller
@RequestMapping(value = "${adminPath}/contract/hsContractRecord")
public class HsContractRecordController extends BaseController {

	@Autowired
	private HsContractRecordService hsContractRecordService;
	@Autowired
	private HsContractTemplateService hsContractTemplateService;
	@Autowired
	private HsContractRecordFieldService hsContractRecordFieldService;
	@Autowired
	private HsContractRecordDataService hsContractRecordDataService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsContractRecord get(String id, boolean isNewRecord) {
		return hsContractRecordService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsContractRecord hsContractRecord, Model model) {
		model.addAttribute("hsContractRecord", hsContractRecord);
		return "modules/contract/hsContractRecordList";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = {"genList", ""})
	public String genList(HsContractRecord hsContractRecord, Model model) {
		model.addAttribute("hsContractRecord", hsContractRecord);
		return "modules/contract/hsContractRecordGenList";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = {"impList", ""})
	public String impList(HsContractRecord hsContractRecord, Model model) {
		model.addAttribute("hsContractRecord", hsContractRecord);
		return "modules/contract/hsContractRecordImpList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsContractRecord> listData(HsContractRecord hsContractRecord, HttpServletRequest request, HttpServletResponse response) {
		hsContractRecord.setPage(new Page<>(request, response));
		Page<HsContractRecord> page = hsContractRecordService.findPage(hsContractRecord);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = "form")
	public String form(HsContractRecord hsContractRecord, Model model) {
		model.addAttribute("hsContractRecord", hsContractRecord);
		return "modules/contract/hsContractRecordForm";
	}
	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = "genForm")
	public String genForm(HsContractRecord hsContractRecord, Model model) {
		List<HsContractTemplate> templateList = hsContractTemplateService.findList(new HsContractTemplate());
		if (templateList != null && !templateList.isEmpty()) {
			templateList.forEach(template -> {
				template.setViewName(String.format("%s (%s)", template.getContractName(), template.getContractVersion()));
			});
		}
		if (hsContractRecord.getId() != null) {
			HsContractRecordField query = new HsContractRecordField();
			query.setRecordId(hsContractRecord.getId());
			hsContractRecord.setFieldList(hsContractRecordFieldService.findList(query));

			HsContractRecordData data = hsContractRecordDataService.get(hsContractRecord.getId());
			hsContractRecord.setHsContractRecordData(data);
		}

		if (hsContractRecord.getFieldList() ==null) {
			hsContractRecord.setFieldList(ListUtils.newArrayList());
		}

		model.addAttribute("hsContractRecord", hsContractRecord);
		model.addAttribute("templateList", templateList);
		return "modules/contract/hsContractRecordGenForm";
	}
	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = "genFile")
	public String genFile(HsContractRecord hsContractRecord, Model model) {
		model.addAttribute("hsContractRecord", hsContractRecord);
		return "modules/contract/hsContractRecordGenFile";
	}

	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = "impForm")
	public String impForm(HsContractRecord hsContractRecord, Model model) {
		model.addAttribute("hsContractRecord", hsContractRecord);
		return "modules/contract/hsContractRecordImpForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("contract:hsContractRecord:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsContractRecord hsContractRecord) {
		hsContractRecordService.save(hsContractRecord);
		return renderResult(Global.TRUE, text("保存合同成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("contract:hsContractRecord:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsContractRecord hsContractRecord) {
		hsContractRecordService.delete(hsContractRecord);
		return renderResult(Global.TRUE, text("删除合同成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("contract:hsContractRecord:view")
	@RequestMapping(value = "hsContractRecordSelect")
	public String hsContractRecordSelect(HsContractRecord hsContractRecord, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsContractRecord", hsContractRecord);
		return "modules/contract/hsContractRecordSelect";
	}
	
}