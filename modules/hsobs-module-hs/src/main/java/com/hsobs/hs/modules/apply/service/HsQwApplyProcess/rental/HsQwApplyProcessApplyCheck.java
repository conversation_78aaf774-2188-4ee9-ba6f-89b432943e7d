package com.hsobs.hs.modules.apply.service.HsQwApplyProcess.rental;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.EmpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeesite.modules.sys.service.ApiSzkjService;

import java.util.Date;
import java.util.UUID;

@Service
public class HsQwApplyProcessApplyCheck extends HsQwApplyProcessDefault {

    @Autowired
    HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

    @Autowired
    ApiSzkjService apiSzkjService;

    @Autowired
    private EmpUserService empUserService;

    @Autowired
    private HsQwApplyerService hsQwApplyerService;

    @Override
    public String getStatus() {
        return "发起配租复查";
    }

    @Override
    public void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        // 记录配租复查前的轮候分
        hsQwApply.setApplyScorePre(hsQwApply.getApplyScore());
        this.process(hsQwApply, hsQwApplyService);
        // 下一个环节是用户复查确认，将进行通知操作
        Api2NoticeBody body = new Api2NoticeBody();
        body.setMsgId(UUID.randomUUID().toString());
        body.setMsgType("1");		// 公告
        body.setMsgSource("租房申请复查确认单");
        body.setTopic("租房申请复查确认单");
        body.setContent("您的租房申请需要进行复查确认，请及时进行确认！");
        body.setFileIds("");
        body.setPublishUnit(EmpUtils.getOffice().getOfficeName());
        body.setPublishUnitId(EmpUtils.getOffice().getOfficeCode());
        EmpUser queryUser = new EmpUser();
        queryUser.setUserCode(this.getUserCode(hsQwApply));
        EmpUser empUser = empUserService.get(queryUser);
        String receiveUserIds = empUser.getExtAspId();
        body.setPublishTime(new Date());
//        body.setReceiveUserIds("all");//临时改为all
        body.setReceiveUserIds(receiveUserIds);
        Api2ResponseBody result2 = apiSzkjService.uploadNotice(body);
        if(result2.getCode() != 1) {
            throw new ServiceException(result2.getMessage());
        }
    }

    private String getUserCode(HsQwApply hsQwApply) {
        HsQwApplyer queryUser = new HsQwApplyer();
        queryUser.setApplyId(hsQwApply.getId());
        queryUser.setApplyRole("0");
        return hsQwApplyerService.findList(queryUser)
                .stream()
                .findFirst()
                .orElseThrow(() -> new ServiceException("主申请用户信息不存在"))
                .getUserId();
    }
}
