package com.hsobs.hs.modules.contract.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.contract.entity.HsContractRecordField;
import com.hsobs.hs.modules.contract.dao.HsContractRecordFieldDao;

/**
 * 合同字段Service
 * <AUTHOR>
 * @version 2025-01-22
 */
@Service
public class HsContractRecordFieldService extends CrudService<HsContractRecordFieldDao, HsContractRecordField> {
	
	/**
	 * 获取单条数据
	 * @param hsContractRecordField
	 * @return
	 */
	@Override
	public HsContractRecordField get(HsContractRecordField hsContractRecordField) {
		return super.get(hsContractRecordField);
	}
	
	/**
	 * 查询分页数据
	 * @param hsContractRecordField 查询条件
	 * @param hsContractRecordField page 分页对象
	 * @return
	 */
	@Override
	public Page<HsContractRecordField> findPage(HsContractRecordField hsContractRecordField) {
		return super.findPage(hsContractRecordField);
	}
	
	/**
	 * 查询列表数据
	 * @param hsContractRecordField
	 * @return
	 */
	@Override
	public List<HsContractRecordField> findList(HsContractRecordField hsContractRecordField) {
		return super.findList(hsContractRecordField);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsContractRecordField
	 */
	@Override
	@Transactional
	public void save(HsContractRecordField hsContractRecordField) {
		super.save(hsContractRecordField);
	}
	
	/**
	 * 更新状态
	 * @param hsContractRecordField
	 */
	@Override
	@Transactional
	public void updateStatus(HsContractRecordField hsContractRecordField) {
		super.updateStatus(hsContractRecordField);
	}
	
	/**
	 * 删除数据
	 * @param hsContractRecordField
	 */
	@Override
	@Transactional
	public void delete(HsContractRecordField hsContractRecordField) {
		hsContractRecordField.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsContractRecordField);
	}

	public void deleteByRecordId(String recordId) {
		this.dao.deleteByRecordId(recordId);
	}
}