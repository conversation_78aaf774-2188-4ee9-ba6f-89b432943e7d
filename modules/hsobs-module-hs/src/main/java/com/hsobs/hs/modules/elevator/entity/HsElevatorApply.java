package com.hsobs.hs.modules.elevator.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

import java.util.List;

/**
 * 加装电梯补助申请表Entity
 * <AUTHOR>
 * @version 2024-12-24
 */
@Table(name="hs_elevator_apply", alias="a", label="加装电梯补助申请表信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="house_id", attrName="houseId", label="房屋信息", queryType=QueryType.LIKE),
		@Column(name="unit_id", attrName="unitId", label="申请单位"),
		@Column(name="contact_name", attrName="contactName", label="联系人", queryType=QueryType.LIKE),
		@Column(name="contact_tel", attrName="contactTel", label="联系人电话", queryType=QueryType.LIKE),
		@Column(name="households", attrName="households", label="电梯服务户数", isUpdateForce=true),
		@Column(name="total_fund", attrName="totalFund", label="项目总费用", isUpdateForce=true),
		@Column(name="exist_fund", attrName="existFund", label="已筹资金", isUpdateForce=true),
		@Column(name="exist_fund_owner", attrName="existFundOwner", label="已筹资金-业主", isUpdateForce=true),
		@Column(name="exist_fund_house", attrName="existFundHouse", label="已筹资金-住宅维修金", isUpdateForce=true),
		@Column(name="exist_fund_unit", attrName="existFundUnit", label="已筹资金-单位补助", isUpdateForce=true),
		@Column(name="exist_fund_other", attrName="existFundOther", label="已筹资金-其他", isUpdateForce=true),
		@Column(name="apply_fund", attrName="applyFund", label="申请金额", isUpdateForce=true),
		@Column(name="disbursement_fund", attrName="disbursementFund", label="拨付金额", isUpdateForce=true),
		@Column(name="apply_reason", attrName="applyReason", label="申请原由", queryType=QueryType.LIKE),
		@Column(name="apply_status", attrName="applyStatus", label="申请状态", isUpdateForce=true),
		@Column(name="survey_analysis", attrName="surveyAnalysis", label="勘察分析"),
		@Column(name="survey_conclusion", attrName="surveyConclusion", label="勘察结论"),
		@Column(name="status", attrName="status", label="状态", isUpdate=true),
		@Column(name="create_by", attrName="createBy", label="申请人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="申请日期", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效"),
		@Column(name="city", attrName="city", label="城市"),
		@Column(name="area", attrName="area", label="区域"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o",
				on = "o.office_code = a.unit_id", attrName = "applyOffice",
				columns = {
					@Column(includeEntity = Office.class)
		        }
		)
    }, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere", orderBy="a.update_date DESC"
)
public class HsElevatorApply extends BpmEntity<HsElevatorApply> {

	public static final String APPLY_STATUS_DEFAULT = "-1"; //默认、删除的申请单
	public static final String APPLY_STATUS_DRAFT = "加装电梯申请";//0:省直单位申请草稿
	public static final String APPLY_STATUS_AUDIT_ORGHAND_FIRST = "经办申请受理";//1:待机关经办受理
	public static final String APPLY_STATUS_AUDIT_SITE_INVESTIG = "现场勘察";//2:待机关经办现场勘察
	public static final String APPLY_STATUS_AUDIT_ORGHAND_SECOND = "初审意见";//3:待机关经办初审
	public static final String APPLY_STATUS_AUDIT_ORGHAND_THIRD = "经办复核";//4:待机关经办复核
	public static final String APPLY_STATUS_AUDIT_ORGOFFICE_FIRST = "处室领导审核";//5:待机关处室领导审核
	public static final String APPLY_STATUS_AUDIT_ORGBUREAU_FIRST = "局领导审批";//6:待机关局领导审批
	public static final String APPLY_STATUS_AUDIT_ORGFINANCE_FIRST = "财政厅审批";//7:待财政厅审批
	public static final String APPLY_STATUS_AUDIT_ORGFINANCE_VER = "财务部门核验拨付";//8:待机关财务部门核验拨付


	private static final long serialVersionUID = 1L;
	private String houseId;		// 房屋信息
	private String unitId;		// 申请单位
	private String contactName;		// 联系人
	private String contactTel;		// 联系人电话
	private Integer households;		// 电梯服务户数
	private Double totalFund;		// 项目总费用
	private Double existFund;		// 已筹资金
	private Double existFundOwner;		// 已筹资金-业主
	private Double existFundHouse;		// 已筹资金-住宅维修金
	private Double existFundUnit;		// 已筹资金-单位补助
	private Double existFundOther;		// 已筹资金-其他
	private Double applyFund;		// 申请金额
	private Double disbursementFund;		// 拨付金额
	private String applyReason;		// 申请原由
	private Integer applyStatus;		// 申请状态
	private String validTag;		// 是否有效
	private String surveyAnalysis;  // 勘察分析
	private String surveyConclusion;  // 勘察结论
	private String city;
	private String area;

	private String flowStatus;
	private Office applyOffice;

	private List<String> ids;
	private String idsStr;
	private String approvalType;

	@ExcelFields({
			@ExcelField(title="申请单号", attrName="id", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="房屋信息", attrName="houseId", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="申请单位", attrName="applyOffice.treeNames", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="联系人", attrName="contactName", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="联系人电话", attrName="contactTel", align= ExcelField.Align.CENTER, sort=60),
			@ExcelField(title="项目总费用", attrName="totalFund", align= ExcelField.Align.CENTER, sort=70),
			@ExcelField(title="申请金额", attrName="applyFund", align= ExcelField.Align.LEFT, sort=80),
			@ExcelField(title="申请原由", attrName="applyReason", align= ExcelField.Align.LEFT, sort=90),
			@ExcelField(title="申请状态", attrName="applyStatus", dictType="elevator_apply_status",  align= ExcelField.Align.LEFT, sort=100),
			@ExcelField(title="创建日期", attrName="createDate", align= ExcelField.Align.CENTER, words=20, sort=110, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="更新日期", attrName="updateDate", align= ExcelField.Align.CENTER, words=20, sort=120, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
	})


	public HsElevatorApply() {
		this(null);
	}
	
	public HsElevatorApply(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="房屋信息长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}
	
	@Size(min=0, max=64, message="申请单位长度不能超过 64 个字符")
	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}
	
	@Size(min=0, max=30, message="联系人长度不能超过 30 个字符")
	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}
	
	@Size(min=0, max=20, message="联系人电话长度不能超过 20 个字符")
	public String getContactTel() {
		return contactTel;
	}

	public void setContactTel(String contactTel) {
		this.contactTel = contactTel;
	}
	
	public Integer getHouseholds() {
		return households;
	}

	public void setHouseholds(Integer households) {
		this.households = households;
	}
	
	public Double getTotalFund() {
		return totalFund;
	}

	public void setTotalFund(Double totalFund) {
		this.totalFund = totalFund;
	}
	
	public Double getExistFund() {
		return existFund;
	}

	public void setExistFund(Double existFund) {
		this.existFund = existFund;
	}
	
	public Double getExistFundOwner() {
		return existFundOwner;
	}

	public void setExistFundOwner(Double existFundOwner) {
		this.existFundOwner = existFundOwner;
	}
	
	public Double getExistFundHouse() {
		return existFundHouse;
	}

	public void setExistFundHouse(Double existFundHouse) {
		this.existFundHouse = existFundHouse;
	}
	
	public Double getExistFundUnit() {
		return existFundUnit;
	}

	public void setExistFundUnit(Double existFundUnit) {
		this.existFundUnit = existFundUnit;
	}
	
	public Double getExistFundOther() {
		return existFundOther;
	}

	public void setExistFundOther(Double existFundOther) {
		this.existFundOther = existFundOther;
	}
	
	public Double getApplyFund() {
		return applyFund;
	}

	public void setApplyFund(Double applyFund) {
		this.applyFund = applyFund;
	}
	
	public Double getDisbursementFund() {
		return disbursementFund;
	}

	public void setDisbursementFund(Double disbursementFund) {
		this.disbursementFund = disbursementFund;
	}
	
	@Size(min=0, max=900, message="申请原由长度不能超过 900 个字符")
	public String getApplyReason() {
		return applyReason;
	}

	public void setApplyReason(String applyReason) {
		this.applyReason = applyReason;
	}
	
	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}
	
	@Size(min=0, max=1, message="是否有效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public String getFlowStatus() {
		return flowStatus;
	}

	public void setFlowStatus(String flowStatus) {
		this.flowStatus = flowStatus;
	}

	public Office getApplyOffice() {
		return applyOffice;
	}

	public void setApplyOffice(Office applyOffice) {
		this.applyOffice = applyOffice;
	}

	@Size(min=0, max=500, message="勘察分析长度不能超过 500 个字符")
	public String getSurveyAnalysis() {
		return surveyAnalysis;
	}

	public void setSurveyAnalysis(String surveyAnalysis) {
		this.surveyAnalysis = surveyAnalysis;
	}

	@Size(min=0, max=500, message="勘察结论长度不能超过 500 个字符")
	public String getSurveyConclusion() {
		return surveyConclusion;
	}

	public void setSurveyConclusion(String surveyConclusion) {
		this.surveyConclusion = surveyConclusion;
	}

	public List<String> getIds() {
		return ids;
	}

	public void setIds(List<String> ids) {
		this.ids = ids;
	}

	public String getIdsStr() {
		return idsStr;
	}

	public void setIdsStr(String idsStr) {
		this.idsStr = idsStr;
	}

	public String getApprovalType() {
		return approvalType;
	}

	public void setApprovalType(String approvalType) {
		this.approvalType = approvalType;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}
}