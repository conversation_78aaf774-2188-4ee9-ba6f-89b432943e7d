package com.hsobs.hs.modules.dataintelligence.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.dataintelligence.service.DataIntelligenceReviewService;

import java.util.ArrayList;
import java.util.List;

/**
 * 住房保障数据统计Controller  公租房资格年审统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Controller
@RequestMapping(value = "${adminPath}/dataintelligencereview/")
public class DataIntelligenceReviewController extends BaseController {

	@Autowired
	private DataIntelligenceReviewService dataIntelligenceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataIntelligenceReview get(Integer type, boolean isNewRecord) {
		return dataIntelligenceService.get(type, isNewRecord);
	}

	/**
	 * 公租房资格年审统计
	 */
	@RequiresPermissions("dataintelligencereview::view")
	@RequestMapping(value = {"dataIntelligenceReview", ""})
	public String dataIntelligenceReview(DataIntelligenceReview dataIntelligenceReview, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceReview);
		return "modules/dataintelligence/dataIntelligenceReview";
	}

	/**
	 * 公租房资格年审统计
	 */
	@RequiresPermissions("dataintelligencereview::view")
	@RequestMapping(value = {"dataIntelligenceReviewCompare", ""})
	public String dataIntelligenceReviewCompare(DataIntelligenceReview dataIntelligenceReview, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceReview);
		return "modules/dataintelligence/dataIntelligenceReviewCompare";
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencereview::view")
	@RequestMapping(value = "resourceData")
	@ResponseBody
	public Page<DataIntelligenceReview> resourceData(DataIntelligenceReview dataIntelligenceReview, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceReview.setPage(new Page<>(request, response));
		Page<DataIntelligenceReview> page = dataIntelligenceService.findResourceDataPage(dataIntelligenceReview, true);
		return page;
	}

	@RequiresPermissions("dataintelligencereview::view")
	@RequestMapping(value = "resourceDataEmpty")
	@ResponseBody
	public Page<DataIntelligenceReview> resourceDataEmpty(DataIntelligenceReview dataIntelligenceReview, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceReview.setPage(new Page<>(request, response));
		List<DataIntelligenceReview> statList = new ArrayList<>();
		Page<DataIntelligenceReview> page = (Page<DataIntelligenceReview>) dataIntelligenceReview.getPage();
		page.setList(statList);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencereview::view")
	@RequestMapping(value = "reviewStatTotal")
	@ResponseBody
	public String reviewStatTotal(DataIntelligenceReview dataIntelligenceReview, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findReviewStatTotal(dataIntelligenceReview);
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencereview::view")
	@RequestMapping(value = "reviewCompare")
	@ResponseBody
	public String reviewCompare(DataIntelligenceReview dataIntelligenceReview, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findReviewCmpare(dataIntelligenceReview);
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligencereview::view")
	@RequestMapping(value = "exportData")
	public void exportData(DataIntelligenceReview dataIntelligenceReview, HttpServletResponse response) {
		String fileName = "公租房资格年审统计表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("公租房资格年审统计表", DataIntelligenceReview.class);
			List<DataIntelligenceReview> list = dataIntelligenceService.countReviewAreaStat(dataIntelligenceReview);
			ee.setDataList(list);

			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}
}