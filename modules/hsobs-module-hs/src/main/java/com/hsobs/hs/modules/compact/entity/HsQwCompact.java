package com.hsobs.hs.modules.compact.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候合同Entity
 * <AUTHOR>
 * @version 2025-01-02
 */
@Table(name="hs_qw_compact", alias="a", label="租赁资格轮候合同信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="compact_code", attrName="compactCode", label="合同编码"),
		@Column(name="start_date", attrName="startDate", label="合同开始日期"),
		@Column(name="end_date", attrName="endDate", label="合同结束日期"),
		@Column(includeEntity=DataEntity.class),
		@Column(name="apply_id", attrName="applyId", label="申请表id"),
		@Column(name="month_fee", attrName="monthFee", label="月租金"),
	}, orderBy="a.update_date DESC"
)
public class HsQwCompact extends DataEntity<HsQwCompact> {
	
	private static final long serialVersionUID = 1L;
	private String compactCode;		// 合同编码
	private Date startDate;		// 合同开始日期
	private Date endDate;		// 合同结束日期
	private String applyId;		// 申请表id
	private Double monthFee;		// 月租金

	public HsQwCompact() {
		this(null);
	}
	
	public HsQwCompact(String id){
		super(id);
	}
	
	@Size(min=0, max=255, message="合同编码长度不能超过 255 个字符")
	public String getCompactCode() {
		return compactCode;
	}

	public void setCompactCode(String compactCode) {
		this.compactCode = compactCode;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="合同开始日期不能为空")
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="合同结束日期不能为空")
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	@NotBlank(message="申请表id不能为空")
	@Size(min=0, max=64, message="申请表id长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@NotNull(message="月租金不能为空")
	public Double getMonthFee() {
		return monthFee;
	}

	public void setMonthFee(Double monthFee) {
		this.monthFee = monthFee;
	}
	
	public Date getStartDate_gte() {
		return sqlMap.getWhere().getValue("start_date", QueryType.GTE);
	}

	public void setStartDate_gte(Date startDate) {
		sqlMap.getWhere().and("start_date", QueryType.GTE, startDate);
	}
	
	public Date getStartDate_lte() {
		return sqlMap.getWhere().getValue("start_date", QueryType.LTE);
	}

	public void setStartDate_lte(Date startDate) {
		sqlMap.getWhere().and("start_date", QueryType.LTE, startDate);
	}
	
	public Date getEndDate_gte() {
		return sqlMap.getWhere().getValue("end_date", QueryType.GTE);
	}

	public void setEndDate_gte(Date endDate) {
		sqlMap.getWhere().and("end_date", QueryType.GTE, endDate);
	}
	
	public Date getEndDate_lte() {
		return sqlMap.getWhere().getValue("end_date", QueryType.LTE);
	}

	public void setEndDate_lte(Date endDate) {
		sqlMap.getWhere().and("end_date", QueryType.LTE, endDate);
	}
	
}