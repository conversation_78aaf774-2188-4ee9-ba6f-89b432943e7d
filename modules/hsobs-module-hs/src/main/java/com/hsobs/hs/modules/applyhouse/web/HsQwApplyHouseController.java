package com.hsobs.hs.modules.applyhouse.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyhouse.service.HsQwApplyHouseService;

/**
 * 租赁资格轮候申请人房屋情况表Controller
 * <AUTHOR>
 * @version 2024-11-21
 */
@Controller
@RequestMapping(value = "${adminPath}/applyhouse/hsQwApplyHouse")
public class HsQwApplyHouseController extends BaseController {

	@Autowired
	private HsQwApplyHouseService hsQwApplyHouseService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyHouse get(String id, boolean isNewRecord) {
		return hsQwApplyHouseService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("applyhouse:hsQwApplyHouse:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyHouse hsQwApplyHouse, Model model) {
		model.addAttribute("hsQwApplyHouse", hsQwApplyHouse);
		return "modules/applyhouse/hsQwApplyHouseList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("applyhouse:hsQwApplyHouse:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyHouse> listData(HsQwApplyHouse hsQwApplyHouse, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyHouse.setPage(new Page<>(request, response));
		Page<HsQwApplyHouse> page = hsQwApplyHouseService.findPage(hsQwApplyHouse);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("applyhouse:hsQwApplyHouse:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyHouse hsQwApplyHouse, Model model) {
		model.addAttribute("hsQwApplyHouse", hsQwApplyHouse);
		return "modules/applyhouse/hsQwApplyHouseForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("applyhouse:hsQwApplyHouse:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyHouse hsQwApplyHouse) {
		hsQwApplyHouseService.save(hsQwApplyHouse);
		return renderResult(Global.TRUE, text("保存租赁资格轮候申请人房屋情况表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("applyhouse:hsQwApplyHouse:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyHouse hsQwApplyHouse) {
		hsQwApplyHouseService.delete(hsQwApplyHouse);
		return renderResult(Global.TRUE, text("删除租赁资格轮候申请人房屋情况表成功！"));
	}
	
}