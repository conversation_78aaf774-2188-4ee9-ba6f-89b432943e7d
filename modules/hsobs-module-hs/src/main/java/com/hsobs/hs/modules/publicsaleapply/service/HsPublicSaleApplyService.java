package com.hsobs.hs.modules.publicsaleapply.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicsaleestate.dao.HsPublicSaleEstateDao;
import com.hsobs.hs.modules.publicsaleestate.entity.HsPublicSaleEstate;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.mybatis.mapper.query.QueryWhere;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.hsobs.hs.modules.publicsaleapply.dao.HsPublicSaleApplyDao;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 公有住房配售申请Service
 * <AUTHOR>
 * @version 2025-02-12
 */
@Service
public class HsPublicSaleApplyService extends CrudService<HsPublicSaleApplyDao, HsPublicSaleApply> {
	
	@Autowired
	private EmpUserService empUserService;

	@Autowired
	private OfficeService officeService;

	@Autowired
	private BpmTaskService bpmTaskService;

	@Autowired
	private CommonBpmService commonBpmService;

    @Autowired
    private HsPublicSaleEstateDao hsPublicSaleEstateDao;

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

	private void findAndSetActivityId(HsPublicSaleApply hsPublicSaleApply){

		HsBpmTask params = new HsBpmTask();
		params.setStatus(BpmTask.STATUS_UNFINISHED);
		params.getProcIns().setFormKey("public_sale_apply");
		params.getProcIns().setBizKey(hsPublicSaleApply.getId());
		Page<BpmTask> myHsTaskPage = this.bpmTaskService.findTaskPage(params);

		int activityIdInt = 1;

		BpmTask bpmTask = !myHsTaskPage.getList().isEmpty() ? (BpmTask) myHsTaskPage.getList().get(0) : null;
		if (bpmTask != null) {

			hsPublicSaleApply.setFlowStatus(bpmTask.getName());
			String activityId = bpmTask.getActivityId();
			activityId = activityId.substring(Math.max(0, activityId.length() - 3));
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				activityIdInt = Integer.parseInt(activityId);
			}
		}

		hsPublicSaleApply.setApplyStatus(activityIdInt);
	}

	/**
	 * 获取单条数据
	 * @param hsPublicSaleApply
	 * @return
	 */
	@Override
	public HsPublicSaleApply get(HsPublicSaleApply hsPublicSaleApply) {

		HsPublicSaleApply entity = super.get(hsPublicSaleApply);
		if (entity != null){
			Office office = new Office();
			office.setOfficeCode(entity.getOfficeCode());
			entity.setOffice(officeService.get(office));

			HsPublicSaleEstate hsPublicSaleEstate = new HsPublicSaleEstate();
			hsPublicSaleEstate.setApplyId(hsPublicSaleApply.getId());
			entity.setHsPublicSaleEstateList(hsPublicSaleEstateDao.findList(hsPublicSaleEstate));

			findAndSetActivityId(entity);

			if(entity.getIsNewRecord() || entity.getApplyStatus() <= 2)
				entity.setReadOnly("false");
			else
				entity.setReadOnly("true");
		}
		return entity;
	}
	
	/**
	 * 查询分页数据
	 * @param hsPublicSaleApply 查询条件
	 * @param hsPublicSaleApply page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPublicSaleApply> findPage(HsPublicSaleApply hsPublicSaleApply) {
		return super.findPage(hsPublicSaleApply);
	}
		
	/**
	 * 查询列表数据
	 * @param hsPublicSaleApply
	 * @return
	 */
	@Override
	public List<HsPublicSaleApply> findList(HsPublicSaleApply hsPublicSaleApply) {
		hsPublicSaleApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		List<HsPublicSaleApply> lstApply = super.findList(hsPublicSaleApply);
		return lstApply;
	}

	@Override
	public void addDataScopeFilter(HsPublicSaleApply hsPublicSaleApply) {
		hsPublicSaleApply.sqlMap().getDataScope().addFilter("dsf", "Office", "a.office_code", DataScope.CTRL_PERMI_HAVE);
	}

	/**
	 * 查询子表分页数据
	 * @param hsPublicSaleEstate
	 * @param hsPublicSaleEstate page 分页对象
	 * @return
	 */
	public Page<HsPublicSaleEstate> findSubPage(HsPublicSaleEstate hsPublicSaleEstate) {
		Page<HsPublicSaleEstate> page = hsPublicSaleEstate.getPage();
		page.setList(hsPublicSaleEstateDao.findList(hsPublicSaleEstate));
		return page;
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsPublicSaleApply
	 */
	@Override
	@Transactional
	public void save(HsPublicSaleApply hsPublicSaleApply) {

		if(hsPublicSaleApply.getHsPublicSaleEstateList().size() == 0){
			throw new ServiceException("至少选择一个房源");
		}

		if(hsPublicSaleApply.getOfficeCode() == null) {
			if (UserUtils.getUser().isAdmin()) {
				throw new ServiceException(text("获取不到工作单位，不能以管理员身份发起！"));
			}
			String officeCode = EmpUtils.getCurrentOfficeCode();
			hsPublicSaleApply.setOfficeCode(officeCode);
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsPublicSaleApply.STATUS_NORMAL.equals(hsPublicSaleApply.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsPublicSaleApply.getStatus())){
			hsPublicSaleApply.setStatus(HsPublicSaleApply.STATUS_AUDIT);
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsPublicSaleApply.STATUS_DRAFT.equals(hsPublicSaleApply.getStatus())
				|| HsPublicSaleApply.STATUS_AUDIT.equals(hsPublicSaleApply.getStatus())){
			this.updateStatus(hsPublicSaleApply);
			if(hsPublicSaleApply.getApplyStatus() == null)
				hsPublicSaleApply.setApplyStatus(0);
			super.save(hsPublicSaleApply);
		}

		// 如果为审核状态，则进行审批流操作
		if (HsPublicSaleApply.STATUS_AUDIT.equals(hsPublicSaleApply.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", hsPublicSaleApply.getLeaveDays());
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsPublicSaleApply.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsPublicSaleApply.getBpm().getTaskId())){
				BpmUtils.start(hsPublicSaleApply, "public_sale_apply", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsPublicSaleApply, variables, null);
			}
		}

		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsPublicSaleApply, hsPublicSaleApply.getId(), "hsPublicSaleApply_file");

		// 审批材料
		FileUploadUtils.saveFileUpload(hsPublicSaleApply, hsPublicSaleApply.getId(), "hsPublicSaleApply_result_file");

		// 保存 HsPublicSaleEstate
		for (HsPublicSaleEstate hsPublicSaleEstate : hsPublicSaleApply.getHsPublicSaleEstateList()) {
			if (!HsPublicSaleEstate.STATUS_DELETE.equals(hsPublicSaleEstate.getStatus())){
				hsPublicSaleEstate.setApplyId(hsPublicSaleApply.getId());
				if (hsPublicSaleEstate.getIsNewRecord()){
					hsPublicSaleEstateDao.insert(hsPublicSaleEstate);
				}else{
					hsPublicSaleEstateDao.update(hsPublicSaleEstate);
				}
			}else{
				hsPublicSaleEstateDao.delete(hsPublicSaleEstate);
			}
		}
		for (HsPublicSaleEstate hsPublicSaleEstate : hsPublicSaleApply.getHsPublicSaleEstateList()) {
			HsQwPublicRentalHouse house = new HsQwPublicRentalHouse(hsPublicSaleEstate.getHouseId());
			hsQwPublicRentalHouseService.UpdateHouseSaleStatus(house, "1");
		}
		if(hsPublicSaleApply.getFlowStatus() != null && hsPublicSaleApply.getFlowStatus().equals(HsPublicSaleApply.APPLY_STATUS_AUDIT_ORGHAND)){// 流程结束
			hsPublicSaleApply.setStatus("88");
			this.updateStatus(hsPublicSaleApply);
		}
	}
	
	/**
	 * 更新状态
	 * @param hsPublicSaleApply
	 */
	@Override
	@Transactional
	public void updateStatus(HsPublicSaleApply hsPublicSaleApply) {
		super.updateStatus(hsPublicSaleApply);
	}
	
	/**
	 * 删除数据
	 * @param hsPublicSaleApply
	 */
	@Override
	@Transactional
	public void delete(HsPublicSaleApply hsPublicSaleApply) {
		super.delete(hsPublicSaleApply);
		HsPublicSaleEstate hsPublicSaleEstate = new HsPublicSaleEstate();
		hsPublicSaleEstate.setApplyId(hsPublicSaleApply.getId());
		hsPublicSaleEstateDao.deleteByEntity(hsPublicSaleEstate);
	}

	public List<HsPublicSaleApply> findAuditListByTask(HsPublicSaleApply hsApply, String flowStatus, String[] flowStatuss, String status){

		Page<HsPublicSaleApply> page = findAuditPageByTask(hsApply, flowStatus, flowStatuss, status);
		return page.getList();
	}
	public List<HsPublicSaleApply> findAuditListByTasks(HsPublicSaleApply hsApply, String status) {

		Page<HsPublicSaleApply> page = findAuditPageByTasks(hsApply, status);
		return page.getList();
	}

	public Page<HsPublicSaleApply> findAuditPageByTasks(HsPublicSaleApply hsApply, String status) {

		return this.findAuditPageByTask(hsApply, "", null, status);
	}

	public Page<BpmTask> findBpmTaskPage(String flowStatus, String[] flowStatuss, String status){

		HsBpmTask params = new HsBpmTask();
		//params.setStatus(BpmTask.STATUS_UNFINISHED);
		if (StringUtils.isNotBlank(status)) {
			params.setStatus(status);
		}
		if(!UserUtils.getUser().isAdmin()) {// 是管理员不设置usercode
			params.setUserCode(params.currentUser().getUserCode());
		}
		params.getProcIns().setFormKey("public_sale_apply");
		if (StringUtils.isNotBlank(flowStatus)) {
			params.setName(flowStatus);
		}
		if (flowStatuss != null && flowStatuss.length > 0) {
			params.setNames(Arrays.stream(flowStatuss).collect(Collectors.toList()));
		}
		return this.bpmTaskService.findTaskPage(params);
	}

	public List<HsPublicSaleApply> findAuditListByTaskOld(HsPublicSaleApply hsApply, String flowStatus, String[] flowStatuss, String status){

		Page<BpmTask> myHsTaskPage = findBpmTaskPage(flowStatus, flowStatuss, status);

		List<HsPublicSaleApply> list = new ArrayList<>();
		//获取所有待办任务的申请单id
		List<String> hsIds = new ArrayList<>();
		myHsTaskPage.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		hsApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
		if (!hsIds.isEmpty()) {
			list = this.findList(hsApply);
			list.forEach(k -> this.setTaskInfo(k,k.getId(),myHsTaskPage));
		}
		return list;
	}

	public Page<HsPublicSaleApply> findAuditPageByTask(HsPublicSaleApply hsApply, String flowStatus, String[] flowStatuss, String status) {
		if(hsApply.getFlowStatus() != null && !"".equals(hsApply.getFlowStatus())){
			flowStatus = hsApply.getFlowStatus();
		}
		if(flowStatus != null && !"".equals(flowStatus)){
			flowStatuss = new String[]{flowStatus};
		}
		return commonBpmService.findTaskList(flowStatuss, "public_sale_apply", hsApply, status);
	}

	private void setTaskInfo(HsPublicSaleApply k, String id, Page<BpmTask> myHsTaskPage) {
		for (BpmTask bpmTask : myHsTaskPage.getList()) {
			if (bpmTask.getProcIns().getBizKey().equals(id)) {

				k.setFlowStatus(bpmTask.getName());
				return;
			}
		}
	}

    public Page<HsPublicSaleApply> findMztPage(HsPublicSaleApply hsApply) {
		BpmTask params = new BpmTask();
		hsApply.setCreateBy(params.currentUser().getUserCode());
		hsApply.setStatus(HsPublicSaleApply.STATUS_DRAFT);
		Page<HsPublicSaleApply> hsApplyPage =  this.findPage(hsApply);
		return hsApplyPage;
    }

	public Page<HsPublicSaleApply> findOnlyArchivesPage(HsPublicSaleApply hsApply) {

		hsApply.sqlMap().getWhere().and("status", QueryType.IN, new String[]{"88"});
		hsApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsPublicSaleApply> hsApplyPage = this.findPage(hsApply);

		List<HsPublicSaleApply> lstApply = hsApplyPage.getList();
		for(HsPublicSaleApply apply : lstApply){
			if(apply.getStatus().equals("88")){
				apply.setFlowStatus("配售完成");
			}
		}
		return hsApplyPage;
	}

	public Page<HsPublicSaleApply> findArchivesPage(HsPublicSaleApply hsApply) {

		String officeCode = hsApply.getOfficeCode();
		if(officeCode != null && !"".equals(officeCode)) {
			Office office = new Office();
			office.setOfficeCode(officeCode);
			office = officeService.get(office);
			String officeParentCodes = office.getParentCodes();

			Consumer<QueryWhere> nestQueryTypeConsumer = (queryWhere) -> {
				queryWhere.or("oi.parent_codes", QueryType.LIKE, officeParentCodes+officeCode+",%").or("oi.office_code", QueryType.EQ, officeCode);
			};
			hsApply.sqlMap().getWhere().and(nestQueryTypeConsumer);
		}
		addDataScopeFilter(hsApply);

		if(hsApply.getFlowStatus() != null && !hsApply.getFlowStatus().equals("")) {
			if(hsApply.getFlowStatus().equals("配售完成")){
				return findOnlyArchivesPage(hsApply);
			}
			else{
				return findAuditPageByTask(hsApply, hsApply.getFlowStatus(), null, BpmTask.STATUS_UNFINISHED);
			}
		}

		hsApply.sqlMap().getWhere().and("status", QueryType.IN, new String[]{"4","88"});
		hsApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsPublicSaleApply> hsApplyPage = this.findPage(hsApply);

		Page<BpmTask> myHsTaskPage = findBpmTaskPage("", null, BpmTask.STATUS_UNFINISHED);

		List<HsPublicSaleApply> lstApply = hsApplyPage.getList();
		for(HsPublicSaleApply apply : lstApply){
			if(apply.getStatus().equals("88")){
				apply.setFlowStatus("配售完成");
			}
			else{
				setTaskInfo(apply, apply.getId(), myHsTaskPage);
			}
		}

		return hsApplyPage;
	}

	public void flushTaskStatus(HsPublicSaleApply hsApply) {
		if (hsApply.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		hsApply = this.get(hsApply.getId());
		if (hsApply == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		HsBpmTask params = new HsBpmTask();
		params.setStatus(BpmTask.STATUS_UNFINISHED);
		params.getProcIns().setFormKey("public_sale_apply");
		params.getProcIns().setBizKey(hsApply.getId());

		Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);

		BpmTask bpmTask = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
		if (bpmTask != null) {


		}
	}
}