//package com.hsobs.hs.modules.apply.web;
//
//import com.alibaba.fastjson.JSONValidator;
//import com.hsobs.hs.modules.apply.entity.HsQwApply;
//import com.hsobs.hs.modules.apply.service.HsQwApplyService;
//import com.hsobs.hs.modules.clearance.service.HsQwClearanceService;
//import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
//import com.jeesite.common.codec.EncodeUtils;
//import com.jeesite.common.config.Global;
//import com.jeesite.common.entity.Page;
//import com.jeesite.common.lang.StringUtils;
//import com.jeesite.common.mybatis.mapper.query.QueryType;
//import com.jeesite.common.web.BaseController;
//import com.jeesite.modules.bpm.service.BpmTaskService;
//import com.jeesite.modules.sys.utils.EmpUtils;
//import com.jeesite.modules.sys.utils.UserUtils;
//import org.apache.shiro.authz.annotation.RequiresPermissions;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.Model;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.ModelAttribute;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
///**
// * 局直公房申请管理Controller
// *
// * <AUTHOR>
// * @version 2025-1-6
// */
//@Controller
//@RequestMapping(value = "${adminPath}/apply/hsQwApplyBureau")
//public class HsQwApplyBureauController extends BaseController {
//
//    @Autowired
//    private HsQwApplyService hsQwApplyService;
//
//    /**
//     * 获取数据
//     */
//    @ModelAttribute
//    public HsQwApply get(String id, boolean isNewRecord) {
//        return hsQwApplyService.get(id, isNewRecord);
//    }
//
//
//    /**
//     * 查询列表-局直公房申请代办
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = {"listBureau", ""})
//    public String listBureau(HsQwApply hsQwApply, Model model, boolean isAdd) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        model.addAttribute("isAdd", isAdd);
//        return "modules/apply/bureau/hsQwApplyBureauList";
//    }
//
//    /**
//     * 查询列表-局直公房申请已处理
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = {"listBureauDone", ""})
//    public String listBureauDone(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/bureau/hsQwApplyBureauDoneList";
//    }
//
//
//    /**
//     * 局直公房申请单
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formBureau")
//    public String formBureau(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        hsQwApply.setOfficeCode(EmpUtils.getCurrentOfficeCode());
//        return "modules/apply/bureau/hsQwApplyBureauForm";
//    }
//
//    /**
//     * 局直公房申请-房源选择
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formBureauHouse")
//    public String formBureauHouse(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/bureau/hsQwApplyBureauFormHouse";
//    }
//
//
//    /**
//     * 局直公房申请-合同签订
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formBureauCompact")
//    public String formBureauCompact(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/bureau/hsQwApplyBureauFormCompact";
//    }
//
//    /**
//     * 查询代办的审批任务-局直公房申请
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "listAuditBureauData")
//    @ResponseBody
//    public Page<HsQwApply> listAuditBureauData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
//        hsQwApply.setPage(new Page<>(request, response));
//        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTask(hsQwApply, null, "1", "rent_apply_bureau");
//        return page;
//    }
//
//    /**
//     * 查询已办的审批任务-局直公房申请
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "listAuditedBureauData")
//    @ResponseBody
//    public Page<HsQwApply> listAuditedChangeData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
//        hsQwApply.setPage(new Page<>(request, response));
//        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTask(hsQwApply, null, "2", "rent_apply_bureau");
//        return page;
//    }
//
//    /**
//     * 保存数据
//     */
//    @RequiresPermissions("apply:hsQwApply:edit")
//    @PostMapping(value = "save")
//    @ResponseBody
//    public String save(HsQwApply hsQwApply) {
//        hsQwApplyService.save(hsQwApply);
//        return renderResult(Global.TRUE, text("保存局直公房申请成功！"));
//    }
//
//    /**
//     * 选择房源对话框
//     */
//    @RequiresPermissions("apply:hsQwApply:edit")
//    @RequestMapping(value = "houseSelect")
//    public String houseSelect(HsQwPublicRentalHouse hsQwPublicRentlHouse, String selectData, Model model, String applyId) {
//        String selectDataJson = EncodeUtils.decodeUrl(selectData);
//        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
//            model.addAttribute("selectData", selectDataJson);
//        }
//        model.addAttribute("hsQwPublicBureaualHouse", hsQwPublicRentlHouse);
//        return "modules/house/hsQwPublicRentalHouseListSelectApply";
//    }
//
//}