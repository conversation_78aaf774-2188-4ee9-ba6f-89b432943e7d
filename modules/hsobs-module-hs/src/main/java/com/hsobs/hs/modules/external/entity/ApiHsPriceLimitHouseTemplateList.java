package com.hsobs.hs.modules.external.entity;

import io.swagger.annotations.Api;

import java.util.List;

public class ApiHsPriceLimitHouseTemplateList extends ApiBody {
    
    private String keyWord;

    private String lpbh;
    private String lpmc;
    private String qhbm;
    private String lpdz;
    private List<ApiHsPriceLimitHouseTemplate> fyxxList;

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getLpbh() {
        return lpbh;
    }

    public void setLpbh(String lpbh) {
        this.lpbh = lpbh;
    }

    public String getLpmc() {
        return lpmc;
    }

    public void setLpmc(String lpmc) {
        this.lpmc = lpmc;
    }

    public String getQhbm() {
        return qhbm;
    }

    public void setQhbm(String qhbm) {
        this.qhbm = qhbm;
    }

    public String getLpdz() {
        return lpdz;
    }

    public void setLpdz(String lpdz) {
        this.lpdz = lpdz;
    }

    public List<ApiHsPriceLimitHouseTemplate> getFyxxList() {
        return fyxxList;
    }

    public void setFyxxList(List<ApiHsPriceLimitHouseTemplate> fyxxList) {
        this.fyxxList = fyxxList;
    }
}
