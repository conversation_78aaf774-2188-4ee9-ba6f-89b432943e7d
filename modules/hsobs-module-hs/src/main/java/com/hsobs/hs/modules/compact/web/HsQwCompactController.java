package com.hsobs.hs.modules.compact.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;

/**
 * 租赁资格轮候合同Controller
 * <AUTHOR>
 * @version 2025-01-02
 */
@Controller
@RequestMapping(value = "${adminPath}/compact/hsQwCompact")
public class HsQwCompactController extends BaseController {

	@Autowired
	private HsQwCompactService hsQwCompactService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwCompact get(String id, boolean isNewRecord) {
		return hsQwCompactService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("compact:hsQwCompact:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwCompact hsQwCompact, Model model) {
		model.addAttribute("hsQwCompact", hsQwCompact);
		return "modules/compact/hsQwCompactList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("compact:hsQwCompact:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwCompact> listData(HsQwCompact hsQwCompact, HttpServletRequest request, HttpServletResponse response) {
		hsQwCompact.setPage(new Page<>(request, response));
		Page<HsQwCompact> page = hsQwCompactService.findPage(hsQwCompact);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("compact:hsQwCompact:view")
	@RequestMapping(value = "form")
	public String form(HsQwCompact hsQwCompact, Model model) {
		model.addAttribute("hsQwCompact", hsQwCompact);
		return "modules/compact/hsQwCompactForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("compact:hsQwCompact:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwCompact hsQwCompact) {
		hsQwCompactService.save(hsQwCompact);
		return renderResult(Global.TRUE, text("保存租赁资格轮候合同成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("compact:hsQwCompact:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwCompact hsQwCompact) {
		hsQwCompactService.delete(hsQwCompact);
		return renderResult(Global.TRUE, text("删除租赁资格轮候合同成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("compact:hsQwCompact:view")
	@RequestMapping(value = "hsQwCompactSelect")
	public String hsQwCompactSelect(HsQwCompact hsQwCompact, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwCompact", hsQwCompact);
		return "modules/compact/hsQwCompactSelect";
	}
	
}