/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.hsobs.hs.modules.publicsaleapply.utils;

import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.hsobs.hs.modules.publicsaleapply.service.HsPublicSaleApplyService;
import com.jeesite.common.utils.SpringUtils;

import java.util.List;

/**
 * CmsUtils
 *
 * <AUTHOR>
 * @version 2020-7-24
 */
public class HsPublicSaleApplyUtils {

    private static final class Static {
        private static final HsPublicSaleApplyService saleService = SpringUtils.getBean(HsPublicSaleApplyService.class);
    }

    /**
     * 获得站点列表
     */
    public static List<HsPublicSaleApply> getPublicSaleApplyList() {

        HsPublicSaleApply hsApply = new HsPublicSaleApply();
        Static.saleService.addDataScopeFilter(hsApply);
        return Static.saleService.findList(hsApply);
    }
}