package com.hsobs.hs.modules.datasync.bean;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

public class TalentDoneImp {

    private String name;
    private String idNumber;
    private String introductionUnit;
    private String number;
    private String degree;
    private String title;
    private String introductionTime;
    private String distributionStandard;
    private String alreadyDistributedAmount;
    private String currentYearDistributionAmount;
    private String bank;
    private String bankAccount;

    @ExcelFields({
            @ExcelField(title = "姓名", attrName = "name", align = ExcelField.Align.CENTER, sort = 10),
            @ExcelField(title = "身份证号码", attrName = "idNumber", align = ExcelField.Align.CENTER, sort = 20),
            @ExcelField(title = "引进单位", attrName = "introductionUnit", align = ExcelField.Align.CENTER, sort = 30),
            @ExcelField(title = "编号", attrName = "number", align = ExcelField.Align.CENTER, sort = 40),
            @ExcelField(title = "学位", attrName = "degree", align = ExcelField.Align.CENTER, sort = 50),
            @ExcelField(title = "引进时间", attrName = "introductionTime", align = ExcelField.Align.CENTER, sort = 70),
            @ExcelField(title = "发放标准(万元)", attrName = "distributionStandard", align = ExcelField.Align.CENTER, sort = 80),
            @ExcelField(title = "已发金额(万元)", attrName = "alreadyDistributedAmount", align = ExcelField.Align.CENTER, sort = 90),
            @ExcelField(title = "本年度发放金额(万元)", attrName = "currentYearDistributionAmount", align = ExcelField.Align.CENTER, sort = 100),
            @ExcelField(title = "开户银行", attrName = "bank", align = ExcelField.Align.CENTER, sort = 110),
            @ExcelField(title = "个人银行账号或卡号", attrName = "bankAccount", align = ExcelField.Align.CENTER, sort = 120)
    })

    public TalentDoneImp() {
        super();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getIntroductionUnit() {
        return introductionUnit;
    }

    public void setIntroductionUnit(String introductionUnit) {
        this.introductionUnit = introductionUnit;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIntroductionTime() {
        return introductionTime;
    }

    public void setIntroductionTime(String introductionTime) {
        this.introductionTime = introductionTime;
    }

    public String getDistributionStandard() {
        return distributionStandard;
    }

    public void setDistributionStandard(String distributionStandard) {
        this.distributionStandard = distributionStandard;
    }

    public String getAlreadyDistributedAmount() {
        return alreadyDistributedAmount;
    }

    public void setAlreadyDistributedAmount(String alreadyDistributedAmount) {
        this.alreadyDistributedAmount = alreadyDistributedAmount;
    }

    public String getCurrentYearDistributionAmount() {
        return currentYearDistributionAmount;
    }

    public void setCurrentYearDistributionAmount(String currentYearDistributionAmount) {
        this.currentYearDistributionAmount = currentYearDistributionAmount;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }
}
