package com.hsobs.hs.modules.publicsaleestate.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.publicsaleapply.utils.HsPublicSaleApplyUtils;
import com.hsobs.hs.modules.publicsaleestate.utils.HsPublicSaleEstateUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.publicsaleestate.entity.HsPublicSaleEstate;
import com.hsobs.hs.modules.publicsaleestate.service.HsPublicSaleEstateService;

/**
 * 公有住房配售房屋Controller
 * <AUTHOR>
 * @version 2025-02-26
 */
@Controller
@RequestMapping(value = "${adminPath}/publicsaleestate/hsPublicSaleEstate")
public class HsPublicSaleEstateController extends BaseController {

	@Autowired
	private HsPublicSaleEstateService hsPublicSaleEstateService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPublicSaleEstate get(String id, boolean isNewRecord) {
		return hsPublicSaleEstateService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicsaleestate:hsPublicSaleEstate:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPublicSaleEstate hsPublicSaleEstate, Model model) {
		model.addAttribute("hsPublicSaleEstate", hsPublicSaleEstate);
		return "modules/publicsaleestate/hsPublicSaleEstateList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("publicsaleestate:hsPublicSaleEstate:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsPublicSaleEstate> listData(HsPublicSaleEstate hsPublicSaleEstate, HttpServletRequest request, HttpServletResponse response) {
		hsPublicSaleEstate.setPage(new Page<>(request, response));
		Page<HsPublicSaleEstate> page = hsPublicSaleEstateService.findPage(hsPublicSaleEstate);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("publicsaleestate:hsPublicSaleEstate:view")
	@RequestMapping(value = "form")
	public String form(HsPublicSaleEstate hsPublicSaleEstate, Model model) {
		model.addAttribute("hsPublicSaleEstate", hsPublicSaleEstate);
		return "modules/publicsaleestate/hsPublicSaleEstateForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("publicsaleestate:hsPublicSaleEstate:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsPublicSaleEstate hsPublicSaleEstate) {
		hsPublicSaleEstateService.save(hsPublicSaleEstate);
		return renderResult(Global.TRUE, text("保存公有住房配售房屋成功！"));
	}

	/**
	 * 获取house信息
	 */
	@RequestMapping(value = "getHouseInfo")
	@ResponseBody
	public String getHouseInfo(String planId, String houseId) {

		List<HsPublicSaleEstate> listSale = HsPublicSaleEstateUtils.getPublicSaleEstateList(planId, houseId);
		List<Map<String,String>> mapList = new ArrayList<Map<String,String>>();
		for(HsPublicSaleEstate saleEstate:listSale){
			Map<String, String> map = new HashMap<>();
			map.put("houseId", saleEstate.getHouseId());
			map.put("houseInfo", saleEstate.getHouse().getSimpleInfo());
			mapList.add(map);
		}
		return JSON.toJSONString(mapList);
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("publicsaleestate:hsPublicSaleEstate:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsPublicSaleEstate hsPublicSaleEstate) {
		hsPublicSaleEstate.setStatus(HsPublicSaleEstate.STATUS_DISABLE);
		hsPublicSaleEstateService.updateStatus(hsPublicSaleEstate);
		return renderResult(Global.TRUE, text("停用公有住房配售房屋成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("publicsaleestate:hsPublicSaleEstate:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsPublicSaleEstate hsPublicSaleEstate) {
		hsPublicSaleEstate.setStatus(HsPublicSaleEstate.STATUS_NORMAL);
		hsPublicSaleEstateService.updateStatus(hsPublicSaleEstate);
		return renderResult(Global.TRUE, text("启用公有住房配售房屋成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("publicsaleestate:hsPublicSaleEstate:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsPublicSaleEstate hsPublicSaleEstate) {
		hsPublicSaleEstateService.delete(hsPublicSaleEstate);
		return renderResult(Global.TRUE, text("删除公有住房配售房屋成功！"));
	}
	
}