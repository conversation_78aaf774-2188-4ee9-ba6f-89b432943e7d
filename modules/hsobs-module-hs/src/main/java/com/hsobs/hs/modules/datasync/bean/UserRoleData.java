package com.hsobs.hs.modules.datasync.bean;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取用户的关联角色响应实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserRoleData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String roleId;
    private String roleName;
    private String roleCode;
    private String intro;

}
