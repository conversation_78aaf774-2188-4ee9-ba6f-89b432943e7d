package com.hsobs.hs.modules.talent.web;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.talent.bean.HsTalentRecordStopImp;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.hsobs.hs.modules.talent.entity.HsTalentRecordStop;
import com.hsobs.hs.modules.talent.service.HsTalentRecordStopService;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 人才补助停发报备Controller
 * <AUTHOR>
 * @version 2025-01-03
 */
@Controller
@RequestMapping(value = "${adminPath}/talent/hsTalentRecordStop")
public class HsTalentRecordStopController extends BaseController {

	@Autowired
	private HsTalentRecordStopService hsTalentRecordStopService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsTalentRecordStop get(String id, boolean isNewRecord) {
		return hsTalentRecordStopService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsTalentRecordStop hsTalentRecordStop, Model model) {
		model.addAttribute("hsTalentRecordStop", hsTalentRecordStop);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentRecordStopList";
	}
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = {"applyList", ""})
	public String applyList(HsTalentRecordStop hsTalentRecordStop, Model model) {
		model.addAttribute("hsTalentRecordStop", hsTalentRecordStop);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentRecordStopList";
	}
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = {"auditList", ""})
	public String auditList(HsTalentRecordStop hsTalentRecordStop, Model model) {
		model.addAttribute("hsTalentRecordStop", hsTalentRecordStop);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentRecordStopAuditList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsTalentRecordStop> listData(HsTalentRecordStop hsTalentRecordStop, HttpServletRequest request, HttpServletResponse response) {
		hsTalentRecordStop.setPage(new Page<>(request, response));
		hsTalentRecordStopService.addDataScopeFilter(hsTalentRecordStop);
		Page<HsTalentRecordStop> page = hsTalentRecordStopService.findPage(hsTalentRecordStop);
		return page;
	}
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = "auditListData")
	@ResponseBody
	public Page<HsTalentRecordStop> auditListData(HsTalentRecordStop hsTalentRecordStop, HttpServletRequest request, HttpServletResponse response) {
		hsTalentRecordStop.setPage(new Page<>(request, response));
		Page<HsTalentRecordStop> page = hsTalentRecordStopService.findAuditPageByTask(hsTalentRecordStop);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = "form")
	public String form(HsTalentRecordStop hsTalentRecordStop, Model model) {
		model.addAttribute("hsTalentRecordStop", hsTalentRecordStop);
		if (hsTalentRecordStop.getApplyStatus() == null) {
			hsTalentRecordStop.setApplyStatus(-1);
		}
		String isRead = "false";
		if (hsTalentRecordStop.getApplyStatus() >= 1) {
			isRead = "true";
		}
		model.addAttribute("isRead", isRead);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentRecordStopForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsTalentRecordStop hsTalentRecordStop) {
		hsTalentRecordStopService.save(hsTalentRecordStop);
		if (hsTalentRecordStop.getApplyStatus() == null || hsTalentRecordStop.getApplyStatus() <= 1) {
			return renderResult(Global.TRUE, text("保存人才住房补助停发报备成功！"));
		} else {
			return renderResult(Global.TRUE, text("提交成功！"));
		}
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:edit")
	@PostMapping(value = "flushTaskStatus")
	@ResponseBody
	public String flushTaskStatus(@Validated HsTalentRecordStop hsTalentRecordStop) {
		hsTalentRecordStopService.flushTaskStatus(hsTalentRecordStop);
		return renderResult(Global.TRUE, text("刷新人才住房补助停发报备状态成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsTalentRecordStop hsTalentRecordStop) {
		hsTalentRecordStop.setStatus(HsTalentRecordStop.STATUS_DISABLE);
		hsTalentRecordStopService.updateStatus(hsTalentRecordStop);
		return renderResult(Global.TRUE, text("停用人才住房补助停发报备成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsTalentRecordStop hsTalentRecordStop) {
		hsTalentRecordStop.setStatus(HsTalentRecordStop.STATUS_NORMAL);
		hsTalentRecordStopService.updateStatus(hsTalentRecordStop);
		return renderResult(Global.TRUE, text("启用人才住房补助停发报备成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsTalentRecordStop hsTalentRecordStop) {
		if (!HsTalentRecordStop.STATUS_DRAFT.equals(hsTalentRecordStop.getStatus())){
			if (hsTalentRecordStop.getApplyStatus() > 0) {
				return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
			}
		}
		hsTalentRecordStopService.delete(hsTalentRecordStop);
		return renderResult(Global.TRUE, text("删除人才住房补助停发报备成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = "hsTalentRecordStopSelect")
	public String hsTalentRecordStopSelect(HsTalentRecordStop hsTalentRecordStop, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsTalentRecordStop", hsTalentRecordStop);
		return "modules/talent/hsTalentRecordStopSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsTalentRecordStop hsTalentRecordStop, HttpServletResponse response) {
		hsTalentRecordStop.sqlMap().getWhere().disableAutoAddStatusWhere();
		List<HsTalentRecordStop> list = hsTalentRecordStopService.findList(hsTalentRecordStop);
		String fileName = "人才住房补助停发报备信息" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("人才住房补助停发报备信息", HsTalentRecordStop.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("talent:hsTalentRecordStop:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		HsTalentRecordStopImp hsTalentRecordStopImp = new HsTalentRecordStopImp();
		List<HsTalentRecordStopImp> list = ListUtils.newArrayList(hsTalentRecordStopImp);
		String fileName = "人才住房补助停发报备名单导入模板.xlsx";
		try(ExcelExport ee = new ExcelExport("人才住房补助停发报备名单", HsTalentRecordStopImp.class, ExcelField.Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("talent:hsTalentRecordStop:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = hsTalentRecordStopService.importData(file, "1");
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}

	
}