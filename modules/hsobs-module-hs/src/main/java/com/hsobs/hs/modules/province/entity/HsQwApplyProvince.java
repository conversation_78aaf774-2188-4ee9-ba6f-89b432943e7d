package com.hsobs.hs.modules.province.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

import java.util.Date;
import java.util.List;

/**
 * 省直单位自管公房申请表Entity
 * <AUTHOR>
 * @version 2025-02-19
 */
@Table(name="hs_qw_apply_province", alias="a", label="省直单位自管公房申请表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="house_id", attrName="houseId", label="房源编号"),
		@Column(name = "auto_check", attrName = "authCheck", label = "智能校验"),
		@Column(name = "check_date", attrName = "checkDate", label = "核验时间"),
		@Column(includeEntity=DataEntity.class),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "h",
                on = "a.house_id = h.id", attrName = "house",
                columns = {@Column(includeEntity = HsQwPublicRentalHouse.class)}),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "o",
                on = "o.id = h.estate_id", attrName = "house.estate",
                columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)}),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "ha",
                on = "ha.apply_id = a.id and ha.apply_role = 0 and ha.status=0", attrName = "mainApplyer",
                columns = {@Column(includeEntity = HsQwApplyer.class)}),
}, orderBy = "a.update_date DESC"
)
public class HsQwApplyProvince extends DataEntity<HsQwApplyProvince> {
	
	private static final long serialVersionUID = 1L;
	private String houseId;		// 房源编号


	private HsQwPublicRentalHouse house;

	private HsQwApplyer mainApplyer;

	private String authCheck;

	private Date checkDate;



	private List<HsQwApplyer> hsQwApplyerList = ListUtils.newArrayList();        // 子表列表


	public HsQwPublicRentalHouse getHouse() {
		return house;
	}

	public void setHouse(HsQwPublicRentalHouse house) {
		this.house = house;
	}

	public HsQwApplyer getMainApplyer() {
		return mainApplyer;
	}

	public void setMainApplyer(HsQwApplyer mainApplyer) {
		this.mainApplyer = mainApplyer;
	}

	public String getAuthCheck() {
		return authCheck;
	}

	public void setAuthCheck(String authCheck) {
		this.authCheck = authCheck;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public HsQwApplyProvince() {
		this(null);
	}
	
	public HsQwApplyProvince(String id){
		super(id);
	}
	
	@NotBlank(message="房源编号不能为空")
	@Size(min=0, max=64, message="房源编号长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}

	public List<HsQwApplyer> getHsQwApplyerList() {
		return hsQwApplyerList;
	}

	public void setHsQwApplyerList(List<HsQwApplyer> hsQwApplyerList) {
		this.hsQwApplyerList = hsQwApplyerList;
	}
}