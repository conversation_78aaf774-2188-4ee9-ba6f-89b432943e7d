package com.hsobs.hs.modules.datasync.bean;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * <AUTHOR>
 */
public class ElevatorImp implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String houseId;		// 房屋信息
    private String unitId;		// 申请单位
    private String contactName;		// 联系人
    private String contactTel;		// 联系人电话
    private Integer households;		// 电梯服务户数
    private Double totalFund;		// 项目总费用
    private Double existFund;		// 已筹资金
    private Double existFundOwner;		// 已筹资金-业主
    private Double existFundHouse;		// 已筹资金-住宅维修金
    private Double existFundUnit;		// 已筹资金-单位补助
    private Double existFundOther;		// 已筹资金-其他
    private Double applyFund;		// 申请金额
    private Double disbursementFund;		// 拨付金额
    private String applyReason;		// 申请原由
    private Integer applyStatus;		// 申请状态
    private String validTag;		// 是否有效
    private String surveyAnalysis;  // 勘察分析
    private String surveyConclusion;  // 勘察结论


    private String unitName;
    private String flowStatus;

    //    申请单位	房屋座落	加装部数	申请户数	项目总费用	业主出资	业主出资占比	住宅维修金
//    单位补助	其他	资金缺口	申请金额	勘察日期	处理阶段	审核金额		备注

    @ExcelFields({

        @ExcelField(title="单位编号", attrName="unitId", align= ExcelField.Align.CENTER, sort=20),
        @ExcelField(title="申请单位", attrName="unitName", align= ExcelField.Align.CENTER, sort=21),
            @ExcelField(title="房屋座落", attrName="houseId", align= ExcelField.Align.CENTER, sort=22),
            @ExcelField(title="加装部数", attrName="contactName", align= ExcelField.Align.CENTER, sort=30),
        @ExcelField(title="申请户数", attrName="households", align= ExcelField.Align.CENTER, sort=50),
        @ExcelField(title="项目总费用", attrName="totalFund", align= ExcelField.Align.CENTER, sort=60),
        @ExcelField(title="业主出资", attrName="existFundOwner", align= ExcelField.Align.CENTER, sort=80),
        @ExcelField(title="住宅维修金", attrName="existFundHouse", align= ExcelField.Align.CENTER, sort=90),
        @ExcelField(title="单位补助", attrName="existFundUnit", align= ExcelField.Align.CENTER, sort=100),
        @ExcelField(title="其他", attrName="existFundOther", align= ExcelField.Align.CENTER, sort=110),
        @ExcelField(title="申请金额", attrName="applyFund", align= ExcelField.Align.CENTER, sort=120),
            @ExcelField(title="处理阶段", attrName="flowStatus", align= ExcelField.Align.CENTER, sort=121),
            @ExcelField(title="审核金额", attrName="disbursementFund", align= ExcelField.Align.CENTER, sort=130),
        @ExcelField(title="备注", attrName="applyReason", words=40, align= ExcelField.Align.CENTER, sort=140),

    })

    public ElevatorImp() {
        super();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactTel() {
        return contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = contactTel;
    }

    public Integer getHouseholds() {
        return households;
    }

    public void setHouseholds(Integer households) {
        this.households = households;
    }

    public Double getTotalFund() {
        return totalFund;
    }

    public void setTotalFund(Double totalFund) {
        this.totalFund = totalFund;
    }

    public Double getExistFund() {
        return existFund;
    }

    public void setExistFund(Double existFund) {
        this.existFund = existFund;
    }

    public Double getExistFundOwner() {
        return existFundOwner;
    }

    public void setExistFundOwner(Double existFundOwner) {
        this.existFundOwner = existFundOwner;
    }

    public Double getExistFundHouse() {
        return existFundHouse;
    }

    public void setExistFundHouse(Double existFundHouse) {
        this.existFundHouse = existFundHouse;
    }

    public Double getExistFundUnit() {
        return existFundUnit;
    }

    public void setExistFundUnit(Double existFundUnit) {
        this.existFundUnit = existFundUnit;
    }

    public Double getExistFundOther() {
        return existFundOther;
    }

    public void setExistFundOther(Double existFundOther) {
        this.existFundOther = existFundOther;
    }

    public Double getApplyFund() {
        return applyFund;
    }

    public void setApplyFund(Double applyFund) {
        this.applyFund = applyFund;
    }

    public Double getDisbursementFund() {
        return disbursementFund;
    }

    public void setDisbursementFund(Double disbursementFund) {
        this.disbursementFund = disbursementFund;
    }

    public String getApplyReason() {
        return applyReason;
    }

    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    public Integer getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getValidTag() {
        return validTag;
    }

    public void setValidTag(String validTag) {
        this.validTag = validTag;
    }

    public String getSurveyAnalysis() {
        return surveyAnalysis;
    }

    public void setSurveyAnalysis(String surveyAnalysis) {
        this.surveyAnalysis = surveyAnalysis;
    }

    public String getSurveyConclusion() {
        return surveyConclusion;
    }

    public void setSurveyConclusion(String surveyConclusion) {
        this.surveyConclusion = surveyConclusion;
    }
}
