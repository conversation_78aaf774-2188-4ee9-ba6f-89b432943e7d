package com.hsobs.hs.modules.formmanage.service;

import com.hsobs.hs.modules.formmanage.bean.FormItemBean;
import com.hsobs.hs.modules.formmanage.dao.HsDataFormFillDao;
import com.hsobs.hs.modules.formmanage.entity.*;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.entity.User;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 数据表单填写记录Service
 * <AUTHOR>
 * @version 2025-02-23
 */
@Service
public class HsDataFormFillService extends CrudService<HsDataFormFillDao, HsDataFormFill> {

	@Autowired
	private HsDataFormDeliveryRecordService hsDataFormDeliveryRecordService;
	@Autowired
	private HsDataFormFillRecordService hsDataFormFillRecordService;
	@Autowired
	private HsDataFormTemplateFieldService hsDataFormTemplateFieldService;
	@Autowired
	private HsDataFormDeliveryService hsDataFormDeliveryService;
	@Autowired
	private HsDataFormIntelligentFillService hsDataFormIntelligentFillService;
	@Autowired
	private HsDataFormFillRecordFnlService hsDataFormFillRecordFnlService;

	/**
	 * 获取单条数据
	 * @param hsDataFormFill
	 * @return
	 */
	@Override
	public HsDataFormFill get(HsDataFormFill hsDataFormFill) {
		return super.get(hsDataFormFill);
	}
	
	/**
	 * 查询分页数据
	 * @param hsDataFormFill 查询条件
	 * @param hsDataFormFill page 分页对象
	 * @return
	 */
	@Override
	public Page<HsDataFormFill> findPage(HsDataFormFill hsDataFormFill) {
		return super.findPage(hsDataFormFill);
	}
	
	/**
	 * 查询列表数据
	 * @param hsDataFormFill
	 * @return
	 */
	@Override
	public List<HsDataFormFill> findList(HsDataFormFill hsDataFormFill) {
		return super.findList(hsDataFormFill);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsDataFormFill
	 */
	@Override
	@Transactional
	public void save(HsDataFormFill hsDataFormFill) {

		HsDataFormDeliveryRecord deliveryRecord = hsDataFormDeliveryRecordService.get(hsDataFormFill.getDeliveryRecordId());
		if (deliveryRecord == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		HsDataFormDelivery delivery =  hsDataFormDeliveryService.get(deliveryRecord.getDeliveryId());
		if (delivery == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		if (delivery.getLimitDate().getTime() < (new Date()).getTime()) {
			throw new ServiceException(text("当前填写表单已超过限制时间！ " + DateUtils.formatDate(delivery.getLimitDate(), "yyyy-MM-dd HH:mm:ss")));
		}

		deliveryRecord.setFillStatus(HsDataFormDeliveryRecord.FILL_STATUS_FILL);
		deliveryRecord.setFillDate(new Date());

		boolean uploadFlag = "upload".equals(hsDataFormFill.getOpType());

		if (uploadFlag) {
			deliveryRecord.setUploadStatus(HsDataFormDeliveryRecord.UPLOAD_STATUS_ED);
			deliveryRecord.setUploadDate(new Date());
		} else {
			if (StringUtils.isBlank(deliveryRecord.getUploadStatus()) || HsDataFormDeliveryRecord.UPLOAD_STATUS_UN.equals(deliveryRecord.getUploadStatus())) {
				deliveryRecord.setUploadStatus(HsDataFormDeliveryRecord.UPLOAD_STATUS_UN);
			} else {
				deliveryRecord.setUploadStatus(HsDataFormDeliveryRecord.UPLOAD_STATUS_RE);
			}
		}
		hsDataFormDeliveryRecordService.save(deliveryRecord);

		//TODO 待限制 是否允许重复提交 或 未报送前可重新提交
		User user = hsDataFormFill.currentUser();
		hsDataFormFill.setFillTime(new Date());
		hsDataFormFill.setFillUserCode(user.getUserCode());
		hsDataFormFill.setFillUserName(user.getUserName());
		super.save(hsDataFormFill);

		HsDataFormFillRecordFnl fnlQuery = new HsDataFormFillRecordFnl();
		fnlQuery.setFillId(hsDataFormFill.getId());
		List<HsDataFormFillRecordFnl> fnlList = hsDataFormFillRecordFnlService.findList(fnlQuery);
		Map<String, HsDataFormFillRecordFnl> fnlDbMap = new HashMap<String, HsDataFormFillRecordFnl>();
		if (fnlList != null && !fnlList.isEmpty()) {
			for (HsDataFormFillRecordFnl fnl : fnlList) {
				fnlDbMap.put(fnl.getFieldId(), fnl);
			}
		}

		hsDataFormFill.getRecordList().forEach(record -> {
			record.setFillId(hsDataFormFill.getId());
			record.setTemplateId(hsDataFormFill.getTemplateId());
			record.setFieldId(record.getTemplateField().getId());
			record.setFieldType(record.getTemplateField().getFieldType());
			record.setFieldName(record.getTemplateField().getFieldName());
			record.setFieldKey(record.getTemplateField().getFieldKey());
			record.setValidTag("1");

			hsDataFormFillRecordService.save(record);

			if (uploadFlag) {
				HsDataFormFillRecordFnl fnlRecord = new HsDataFormFillRecordFnl();
				BeanUtils.copyProperties(record, fnlRecord);
				if (fnlDbMap.containsKey(record.getFieldId())) {
					fnlRecord.setIsNewRecord(false);
				} else {
					fnlRecord.setIsNewRecord(true);
				}
				hsDataFormFillRecordFnlService.save(fnlRecord);
			}
		});

	}
	
	/**
	 * 更新状态
	 * @param hsDataFormFill
	 */
	@Override
	@Transactional
	public void updateStatus(HsDataFormFill hsDataFormFill) {
		super.updateStatus(hsDataFormFill);
	}
	
	/**
	 * 删除数据
	 * @param hsDataFormFill
	 */
	@Override
	@Transactional
	public void delete(HsDataFormFill hsDataFormFill) {
		super.delete(hsDataFormFill);
	}

	public HsDataFormFill getFillByDeliveryRecordId(String deliveryRecordId) {

		HsDataFormDeliveryRecord hsDataFormDeliveryRecord = hsDataFormDeliveryRecordService.get(deliveryRecordId);
		HsDataFormDelivery delivery = hsDataFormDeliveryService.get(hsDataFormDeliveryRecord.getDeliveryId());
		if (delivery == null || delivery.getTemplateCode() == null) {
			throw new ServiceException("无效数据！");
		}

		// 获取填写记录
		HsDataFormFill fill = new HsDataFormFill();
		fill.setDeliveryRecordId(hsDataFormDeliveryRecord.getId());
		List<HsDataFormFill> fillList = this.findList(fill);
		if (fillList != null && !fillList.isEmpty()) {
			fill = fillList.get(0);
		} else {
			fill = new HsDataFormFill();
		}
		fill.setTemplateId(delivery.getTemplateCode());
		fill.setDeliveryId(delivery.getId());
		fill.setDeliveryRecordId(deliveryRecordId);
		fill.setDelivery(delivery);

		String deliveryId = delivery.getId();

		//TODO 是否智能获取
		boolean newFlag = fill.getId() == null;

		Map<String, HsDataFormFillRecord> fillRecordMap = new HashMap<String, HsDataFormFillRecord>();
		if (fill.getId() != null) {
			HsDataFormFillRecord tmpQuery = new HsDataFormFillRecord();
			tmpQuery.setFillId(fill.getId());
			List<HsDataFormFillRecord> tmpList = hsDataFormFillRecordService.findList(tmpQuery);
			if (tmpList != null && !tmpList.isEmpty()) {
				tmpList.forEach(record -> {
					fillRecordMap.put(record.getFieldId(), record);
				});
			}
		}

		List<HsDataFormFillRecord> recordList = getFieldFillRecordList(fill, fillRecordMap, newFlag, deliveryId);
		fill.setRecordList(recordList);

		List<HsDataFormFillRecordFnl> recordFnlList = getFieldFillRecordFnlList(fill, fillRecordMap, newFlag, deliveryId, recordList);
		fill.setRecordFnlList(recordFnlList);

		//TODO 专项表单展示已经统计的数据

		HsDataFormDeliveryRecord recordQuery = new HsDataFormDeliveryRecord();
		recordQuery.setDeliveryId(fill.getDeliveryId());
		Long deliveryTotal = hsDataFormDeliveryRecordService.findCount(recordQuery);

		recordQuery.setFillStatus("1");
		Long filledTotal = hsDataFormDeliveryRecordService.findCount(recordQuery);

		fill.setFilledTotal(filledTotal);
		fill.setDeliveryTotal(deliveryTotal);

		return fill;
	}

	private List<HsDataFormFillRecordFnl> getFieldFillRecordFnlList(HsDataFormFill fill, Map<String, HsDataFormFillRecord> fillRecordMap, boolean newFlag, String deliveryId, List<HsDataFormFillRecord> recordList) {
		List<HsDataFormFillRecordFnl> recordFnlList = new ArrayList<>();

		Map<String, HsDataFormFillRecordFnl> fillRecordFnlMap = new HashMap<String, HsDataFormFillRecordFnl>();
		if (fill.getId() != null) {
			HsDataFormFillRecordFnl tmpQuery = new HsDataFormFillRecordFnl();
			tmpQuery.setFillId(fill.getId());
			List<HsDataFormFillRecordFnl> tmpList = hsDataFormFillRecordFnlService.findList(tmpQuery);
			if (tmpList != null && !tmpList.isEmpty()) {
				tmpList.forEach(record -> {
					fillRecordFnlMap.put(record.getFieldId(), record);
				});
			}
		}

		HsDataFormFillRecordFnl recordFnl = null;

		for (HsDataFormFillRecord fillRecord : recordList) {
			if (fillRecordFnlMap.containsKey(fillRecord.getTemplateField().getId())) {
				recordFnl = fillRecordFnlMap.get(fillRecord.getTemplateField().getId());
				recordFnl.setFieldId(fillRecord.getTemplateField().getId());
			} else {
				recordFnl = new HsDataFormFillRecordFnl();
			}
			recordFnl.setTemplateField(fillRecord.getTemplateField());
			recordFnlList.add(recordFnl);
		}
		return recordFnlList;
	}

	private List<HsDataFormFillRecord> getFieldFillRecordList(HsDataFormFill fill, Map<String, HsDataFormFillRecord> fillRecordMap, boolean newFlag, String deliveryId) {
		AtomicInteger fileIndex = new AtomicInteger(0);
		HsDataFormTemplateField query = new HsDataFormTemplateField();
		query.setTemplateId(fill.getTemplateId());
		List<HsDataFormTemplateField> fieldList = hsDataFormTemplateFieldService.findList(query);

		List<HsDataFormFillRecord> recordList = new ArrayList<>();

		if (fieldList != null && !fieldList.isEmpty()) {
			fieldList.forEach(field -> {
				field.setIndex(fileIndex.getAndIncrement());
				if ("3".equals(field.getFieldType()) || "4".equals(field.getFieldType()) || "5".equals(field.getFieldType())) {
					List<FormItemBean> itemList = new ArrayList<>();
					if (field.getFieldSubkey() != null) {
						String[] subKeyArr = field.getFieldSubkey().split(",");
						for (String subKey : subKeyArr) {
							itemList.add(new FormItemBean(subKey, subKey));
						}
					}
					field.setItems(itemList);
				}
				HsDataFormFillRecord fillRecord = null;
				if (fillRecordMap.containsKey(field.getId())) {
					fillRecord = fillRecordMap.get(field.getId());
					fillRecord.setFieldId(field.getId());
				} else {
					fillRecord = new HsDataFormFillRecord();
					if (newFlag && ("1".equals(field.getFieldType()) || "2".equals(field.getFieldType()))) {
						// 智能填报值
						hsDataFormIntelligentFillService.fill(fillRecord, field, deliveryId);
					}
				}
				fillRecord.setTemplateField(field);
				recordList.add(fillRecord);
			});
		}
		return recordList;
	}
}