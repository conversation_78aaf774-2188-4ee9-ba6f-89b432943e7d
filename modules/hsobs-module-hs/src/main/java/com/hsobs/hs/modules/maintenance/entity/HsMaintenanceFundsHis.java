package com.hsobs.hs.modules.maintenance.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 维修资金变动记录表Entity
 * <AUTHOR>
 * @version 2025-03-15
 */
@Table(name="hs_maintenance_funds_his", alias="a", label="维修资金变动记录表信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="funds_id", attrName="fundsId", label="归属资金账号ID"),
		@Column(name="input_funds", attrName="inputFunds", label="维修资金总额", isUpdateForce=true),
		@Column(name="change_funds", attrName="changeFunds", label="变动资金额度", isUpdateForce=true),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="remark", attrName="remark", label="简述"),
		@Column(name="input_year", attrName="inputYear", label="录入年份"),
		@Column(name="input_month", attrName="inputMonth", label="录入月份"),
		@Column(name="create_by", attrName="createBy", label="录入人", isUpdate=false, isQuery=false),
		@Column(name="create_by_name", attrName="createByName", label="录入人名称", queryType=QueryType.LIKE),
		@Column(name="create_date", attrName="createDate", label="录入日期", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, orderBy="a.create_date DESC"
)
public class HsMaintenanceFundsHis extends DataEntity<HsMaintenanceFundsHis> {
	
	private static final long serialVersionUID = 1L;
	private String fundsId;		// 归属资金账号ID
	private Double inputFunds;		// 维修资金总额
	private Double changeFunds;		// 变动资金额度
	private String remark;		// 简述
	private String inputYear;		// 录入年份
	private String inputMonth;		// 录入月份
	private String createByName;		// 录入人名称
	private String validTag;		// 是否有效;1-有效 0-无效

	public HsMaintenanceFundsHis() {
		this(null);
	}
	
	public HsMaintenanceFundsHis(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="归属资金账号ID长度不能超过 64 个字符")
	public String getFundsId() {
		return fundsId;
	}

	public void setFundsId(String fundsId) {
		this.fundsId = fundsId;
	}
	
	public Double getInputFunds() {
		return inputFunds;
	}

	public void setInputFunds(Double inputFunds) {
		this.inputFunds = inputFunds;
	}
	
	public Double getChangeFunds() {
		return changeFunds;
	}

	public void setChangeFunds(Double changeFunds) {
		this.changeFunds = changeFunds;
	}
	
	@Size(min=0, max=900, message="简述长度不能超过 900 个字符")
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	@Size(min=0, max=4, message="录入年份长度不能超过 4 个字符")
	public String getInputYear() {
		return inputYear;
	}

	public void setInputYear(String inputYear) {
		this.inputYear = inputYear;
	}
	
	@Size(min=0, max=4, message="录入月份长度不能超过 4 个字符")
	public String getInputMonth() {
		return inputMonth;
	}

	public void setInputMonth(String inputMonth) {
		this.inputMonth = inputMonth;
	}
	
	@Size(min=0, max=100, message="录入人名称长度不能超过 100 个字符")
	public String getCreateByName() {
		return createByName;
	}

	public void setCreateByName(String createByName) {
		this.createByName = createByName;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}
	
}