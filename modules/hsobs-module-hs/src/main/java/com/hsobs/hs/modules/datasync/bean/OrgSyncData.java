package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 单位数据同步请求体实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgSyncData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String orgId;
    private String orgPid;
    private String orgCode;
    private String orgType;
    private String orgName;
    private String code;
    private String administrativeLevel;
    private Integer level;
    private Integer sort;
    private String address;
    private String createTime;
    private String updateTime;
    private Integer changeType;
    private String extendField;
    private String socialCreditCode;

}
