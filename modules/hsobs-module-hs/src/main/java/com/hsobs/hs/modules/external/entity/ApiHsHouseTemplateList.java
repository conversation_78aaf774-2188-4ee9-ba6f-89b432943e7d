package com.hsobs.hs.modules.external.entity;

import io.swagger.annotations.Api;

import java.util.List;

public class ApiHsHouseTemplateList extends ApiBody {
    
    private String keyWord;
    private String fwlx;

    private String lpbh;
    private String lpmc;
    private String qhbm;
    private String lpdz;
    private String xqgk;
    private String zbpt;
    private String longitude;
    private String latitude;
    private List<ApiHsHouseTemplate> fyxxList;

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getFwlx() {
        return fwlx;
    }

    public void setFwlx(String fwlx) {
        this.fwlx = fwlx;
    }
    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getLpbh() {
        return lpbh;
    }

    public void setLpbh(String lpbh) {
        this.lpbh = lpbh;
    }

    public String getLpmc() {
        return lpmc;
    }

    public void setLpmc(String lpmc) {
        this.lpmc = lpmc;
    }

    public String getQhbm() {
        return qhbm;
    }

    public void setQhbm(String qhbm) {
        this.qhbm = qhbm;
    }

    public String getLpdz() {
        return lpdz;
    }

    public void setLpdz(String lpdz) {
        this.lpdz = lpdz;
    }

    public String getXqgk() {
        return xqgk;
    }

    public void setXqgk(String xqgk) {
        this.xqgk = xqgk;
    }

    public String getZbpt() {
        return zbpt;
    }

    public void setZbpt(String zbpt) {
        this.zbpt = zbpt;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public List<ApiHsHouseTemplate> getFyxxList() {
        return fyxxList;
    }

    public void setFyxxList(List<ApiHsHouseTemplate> fyxxList) {
        this.fyxxList = fyxxList;
    }
}
