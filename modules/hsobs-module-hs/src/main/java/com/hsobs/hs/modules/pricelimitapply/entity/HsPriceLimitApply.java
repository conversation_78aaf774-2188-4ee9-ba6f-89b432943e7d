package com.hsobs.hs.modules.pricelimitapply.entity;

import javax.validation.Valid;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import javax.validation.constraints.Size;
import java.util.Date;

import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.pricelimitplan.entity.HsPriceLimitPlan;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

/**
 * 限价房-购房申请Entity
 * <AUTHOR>
 * @version 2024-12-10
 */
@Table(name="hs_price_limit_apply", alias="a", label="限价房-购房申请信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true, queryType=QueryType.LIKE),
		@Column(name="house_id", attrName="houseId", label="房源id"),
		@Column(name="signature", attrName="signature", label="资格确认签章"),
		@Column(name="contract_id", attrName="contractId", label="合同编号", queryType=QueryType.LIKE),
		@Column(name="price", attrName="price", label="限价房金额"),
		@Column(name="office_code", attrName="officeCode", label="归属机构"),
		@Column(name="public_name", attrName="publicName", label="公示名称", queryType=QueryType.LIKE),
		@Column(name="public_info", attrName="publicInfo", label="公示内容", queryType=QueryType.LIKE),
		@Column(name="public_date", attrName="publicDate", label="公示时间"),
		@Column(name="planid", attrName="planId", label="方案id"),
		@Column(name="public_status", attrName="publicStatus", label="公示状态"),
		@Column(name="buy_status", attrName="buyStatus", label="购买状态"),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsPriceLimitApplyer.class, alias = "o",
				on = "o.apply_id = a.id and o.apply_role = 0", attrName="mainApplyer",
				columns = {@Column(includeEntity = HsPriceLimitApplyer.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "h",
				on = "h.id = a.house_id and h.status=0", attrName = "house",
				columns = {@Column(includeEntity = HsQwPublicRentalHouse.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "e",
				on = "e.id = h.estate_id and e.status=0", attrName="house.estate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "office",
				on = "office.office_code = a.office_code", attrName="office",
				columns = {@Column(includeEntity = Office.class)}),
	},
		extWhereKeys="dsf",
		orderBy="a.update_date DESC"
)
public class HsPriceLimitApply extends BpmEntity<HsPriceLimitApply> {

	public static final String APPLY_STATUS_DEFAULT = "-1"; //默认、删除的申请单
	public static final String APPLY_STATUS_DRAFT = "限价房申请";//0
	public static final String APPLY_STATUS_AUDIT_ORGHAND_FIRST = "经办审核";//1
	public static final String APPLY_STATUS_AUDIT_ORGOFFICE_FIRST = "处室领导审核";//2
	public static final String APPLY_STATUS_AUDIT_LEADER = "分管领导审核";//3
	public static final String APPLY_STATUS_AUDIT_SHOW_PUBLIC = "网上公示";//5
	public static final String APPLY_STATUS_AUDIT_QUALIFICATION = "资格确认";//6
	public static final String APPLY_STATUS_AUDIT_CONFIRMATION = "接收确认单";//7
	public static final String APPLY_STATUS_AUDIT_CONTRACT = "合同上传";//8

	@ExcelFields({
			@ExcelField(title = "申请编号", attrName = "id", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "申请人", attrName = "mainApplyer.name", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "身份证号", attrName = "mainApplyer.idNum", align = ExcelField.Align.CENTER, sort = 40),
			@ExcelField(title = "联系电话", attrName = "mainApplyer.phone", align = ExcelField.Align.CENTER, sort = 50),
			@ExcelField(title = "工作单位", attrName = "office.name", align = ExcelField.Align.CENTER, sort = 60),
			@ExcelField(title = "楼盘地址", attrName = "house.estate.address", align = ExcelField.Align.CENTER, sort = 70),
			@ExcelField(title = "户型", attrName = "house.houseType", align = ExcelField.Align.CENTER, sort = 80),
			@ExcelField(title = "合同编号", attrName = "contractId", align = ExcelField.Align.CENTER, sort = 90),
			@ExcelField(title = "申请理由", attrName = "remarks", align = ExcelField.Align.CENTER, sort = 100),
	})

	private static final long serialVersionUID = 1L;
	private String houseId;		// 房源id
	private String signature;		// 资格确认签章
	private String contractId;		// 合同编号
	private Long price;
	private String officeCode;		// 归属机构
	private String publicName;		// 公示名称
	private String publicInfo;		// 公示内容
	private Date publicDate;		// 公示时间
	private String planId;			// 限价房方案
	private String publicStatus;	// 公示状态
	private Integer applyStatus;		// 申请状态
	private Office office;
	private String buyStatus;		// 购买状态（0,未购买(购买流程中)，1已购买，2放弃购买）
	private String applyTitle;
	private String flowStatus;		// 申请状态(字符串)
	private HsPriceLimitApplyer mainApplyer;
	private HsQwPublicRentalHouse house;
	private HsPriceLimitPlan plan;
	private List<HsPriceLimitApplyer> hsPriceLimitApplyerList = ListUtils.newArrayList();		// 子表列表

	private String readOnly;
	private String houseInfo;

	private Date startDate;
	private Date endDate;

	private String type;	// 仅用于传递参数

	public HsPriceLimitApply() {
		this(null);
		readOnly = "false";
	}
	
	public HsPriceLimitApply(String id){
		super(id);
	}

	@Size(min=0, max=64, message="房源长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		if (!StringUtils.isBlank(houseId)){
			houseId = houseId.replaceAll("^,+|,+$", "");;
		}
		this.houseId = houseId;
	}
	
	@Size(min=0, max=64, message="资格确认签章长度不能超过 64 个字符")
	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}
	
	@Size(min=0, max=64, message="合同编号长度不能超过 64 个字符")
	public String getContractId() {
		return contractId;
	}

	public void setContractId(String contractId) {
		this.contractId = contractId;
	}

	public Long getPrice() {
		return price;
	}

	public void setPrice(Long price) {
		this.price = price;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	@Size(min=0, max=200, message="公示名称长度不能超过 200 个字符")
	public String getPublicName() {
		return publicName;
	}

	public void setPublicName(String publicName) {
		this.publicName = publicName;
	}

	@Size(min=0, max=200, message="公示内容长度不能超过 500 个字符")
	public String getPublicInfo() {
		return publicInfo;
	}

	public void setPublicInfo(String publicInfo) {
		this.publicInfo = publicInfo;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getPublicDate() {
		return publicDate;
	}

	public void setPublicDate(Date publicDate) {
		this.publicDate = publicDate;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}

	public String getPublicStatus() {
		return publicStatus;
	}

	public void setPublicStatus(String publicStatus) {
		this.publicStatus = publicStatus;
	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}
	
	@Valid
	public List<HsPriceLimitApplyer> getHsPriceLimitApplyerList() {
		return hsPriceLimitApplyerList;
	}

	public void setHsPriceLimitApplyerList(List<HsPriceLimitApplyer> hsPriceLimitApplyerList) {
		this.hsPriceLimitApplyerList = hsPriceLimitApplyerList;
	}

    @Override
    public String getStatus() {
        return status;
    }

    @Override
    public void setStatus(String status) {
        this.status = status;
    }

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

    public HsPriceLimitApplyer getMainApplyer() {
        return mainApplyer;
    }

    public void setMainApplyer(HsPriceLimitApplyer mainApplyer) {
        this.mainApplyer = mainApplyer;
    }

	public HsQwPublicRentalHouse getHouse() {
		return house;
	}

	public void setHouse(HsQwPublicRentalHouse house) {
		this.house = house;
	}

	public String getBuyStatus() {
		return buyStatus;
	}

	public void setBuyStatus(String buyStatus) {
		this.buyStatus = buyStatus;
	}

	public HsPriceLimitPlan getPlan() {
		return plan;
	}

	public void setPlan(HsPriceLimitPlan plan) {
		this.plan = plan;
	}

	public String getApplyTitle() {
		if(this.mainApplyer != null)
        	return this.mainApplyer.getName() + " 于" + DateUtils.formatDateTime(this.getCreateDate()) + " 发起了限价房申请";
		return "";
    }
    public void setApplyTitle(String applyTitle) {
        this.applyTitle = applyTitle;
    }

	public String getFlowStatus() {
		return this.flowStatus;
	}

	public void setFlowStatus(String flowStatus) {
		this.flowStatus = flowStatus;
	}

	public String getReadOnly() {
		return this.readOnly;
	}

	public void setReadOnly(String readOnly) {
		this.readOnly = readOnly;
	}

	public String getHouseInfo() {
		return houseInfo;
	}

	public void setHouseInfo(String houseInfo) {
		this.houseInfo = houseInfo;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}