package com.hsobs.hs.modules.formmanage.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormFill;
import com.hsobs.hs.modules.formmanage.service.HsDataFormFillService;

/**
 * 数据表单填写记录Controller
 * <AUTHOR>
 * @version 2025-02-23
 */
@Controller
@RequestMapping(value = "${adminPath}/formmanage/hsDataFormFill")
public class HsDataFormFillController extends BaseController {

	@Autowired
	private HsDataFormFillService hsDataFormFillService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsDataFormFill get(String id, boolean isNewRecord) {
		return hsDataFormFillService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormFill:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsDataFormFill hsDataFormFill, Model model) {
		model.addAttribute("hsDataFormFill", hsDataFormFill);
		return "modules/formmanage/hsDataFormFillList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormFill:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsDataFormFill> listData(HsDataFormFill hsDataFormFill, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormFill.setPage(new Page<>(request, response));
		Page<HsDataFormFill> page = hsDataFormFillService.findPage(hsDataFormFill);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormFill:view")
	@RequestMapping(value = "form")
	public String form(HsDataFormFill hsDataFormFill, Model model) {
		model.addAttribute("hsDataFormFill", hsDataFormFill);
		return "modules/formmanage/hsDataFormFillForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormFill:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsDataFormFill hsDataFormFill) {
		hsDataFormFillService.save(hsDataFormFill);
		return renderResult(Global.TRUE, text("保存数据表单填写记录成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("formmanage:hsDataFormFill:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsDataFormFill hsDataFormFill) {
		hsDataFormFillService.delete(hsDataFormFill);
		return renderResult(Global.TRUE, text("删除数据表单填写记录成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("formmanage:hsDataFormFill:view")
	@RequestMapping(value = "hsDataFormFillSelect")
	public String hsDataFormFillSelect(HsDataFormFill hsDataFormFill, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsDataFormFill", hsDataFormFill);
		return "modules/formmanage/hsDataFormFillSelect";
	}
	
}