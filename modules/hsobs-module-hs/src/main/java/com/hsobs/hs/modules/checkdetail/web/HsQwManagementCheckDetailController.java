package com.hsobs.hs.modules.checkdetail.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.checkdetail.entity.HsQwManagementCheckDetail;
import com.hsobs.hs.modules.checkdetail.service.HsQwManagementCheckDetailService;

/**
 * 租赁资格轮候物业核验详细表Controller
 * <AUTHOR>
 * @version 2025-02-14
 */
@Controller
@RequestMapping(value = "${adminPath}/checkdetail/hsQwManagementCheckDetail")
public class HsQwManagementCheckDetailController extends BaseController {

	@Autowired
	private HsQwManagementCheckDetailService hsQwManagementCheckDetailService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwManagementCheckDetail get(String id, boolean isNewRecord) {
		return hsQwManagementCheckDetailService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("checkdetail:hsQwManagementCheckDetail:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwManagementCheckDetail hsQwManagementCheckDetail, Model model) {
		model.addAttribute("hsQwManagementCheckDetail", hsQwManagementCheckDetail);
		return "modules/checkdetail/hsQwManagementCheckDetailList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("checkdetail:hsQwManagementCheckDetail:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwManagementCheckDetail> listData(HsQwManagementCheckDetail hsQwManagementCheckDetail, HttpServletRequest request, HttpServletResponse response) {
		hsQwManagementCheckDetail.setPage(new Page<>(request, response));
		Page<HsQwManagementCheckDetail> page = hsQwManagementCheckDetailService.findPage(hsQwManagementCheckDetail);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("checkdetail:hsQwManagementCheckDetail:view")
	@RequestMapping(value = "form")
	public String form(HsQwManagementCheckDetail hsQwManagementCheckDetail, Model model) {
		model.addAttribute("hsQwManagementCheckDetail", hsQwManagementCheckDetail);
		return "modules/checkdetail/hsQwManagementCheckDetailForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("checkdetail:hsQwManagementCheckDetail:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwManagementCheckDetail hsQwManagementCheckDetail) {
		hsQwManagementCheckDetailService.save(hsQwManagementCheckDetail);
		return renderResult(Global.TRUE, text("保存租赁资格轮候物业核验详细表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("checkdetail:hsQwManagementCheckDetail:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwManagementCheckDetail hsQwManagementCheckDetail) {
		hsQwManagementCheckDetailService.delete(hsQwManagementCheckDetail);
		return renderResult(Global.TRUE, text("删除租赁资格轮候物业核验详细表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("checkdetail:hsQwManagementCheckDetail:view")
	@RequestMapping(value = "hsQwManagementCheckDetailSelect")
	public String hsQwManagementCheckDetailSelect(HsQwManagementCheckDetail hsQwManagementCheckDetail, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwManagementCheckDetail", hsQwManagementCheckDetail);
		return "modules/checkdetail/hsQwManagementCheckDetailSelect";
	}
	
}