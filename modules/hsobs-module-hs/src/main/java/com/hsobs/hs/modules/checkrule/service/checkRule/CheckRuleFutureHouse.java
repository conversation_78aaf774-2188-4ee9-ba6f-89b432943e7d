package com.hsobs.hs.modules.checkrule.service.checkRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;
import org.springframework.stereotype.Service;

/**
 * 未来拥有住房
 * 申请人及共同申请人在申请之日前3年内将拥有的福州市城区和单位主体所在县（市、区）范围内的私有住房产权转移的（不含该房产合并计算后家庭人均住房建筑面积在15平方米以下和配偶婚前转移非政策性住房的情形）
 */
@Service
public class CheckRuleFutureHouse implements ICheckRule {
    @Override
    public CheckRuleResult execute(HsQwApply hsQwApply, HsQwCheckRule rule) {
        return CheckRuleResult.success("成功");
    }
}
