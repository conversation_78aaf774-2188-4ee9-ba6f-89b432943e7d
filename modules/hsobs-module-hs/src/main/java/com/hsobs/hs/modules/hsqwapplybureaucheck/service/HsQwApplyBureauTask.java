package com.hsobs.hs.modules.hsqwapplybureaucheck.service;

import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.bureau.service.HsQwApplyBureauService;
import com.hsobs.hs.modules.hsqwapplybureaucheck.entity.HsQwApplyBureauCheck;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.BaseService;
import com.jeesite.modules.msg.entity.content.PcMsgContent;
import com.jeesite.modules.msg.utils.MsgPushUtils;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.ApiSzkjService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 局直公房智能核验定时任务
 *
 * <AUTHOR>
 * @version 2025-05-03
 */
@Service
public class HsQwApplyBureauTask extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(HsQwApplyBureauTask.class);

    @Autowired
    private HsQwApplyBureauService hsQwApplyBureauService;

    @Autowired
    private HsQwApplyBureauCheckService hsQwApplyBureauCheckService;

    @Autowired
    private ApiSzkjService apiSzkjService;

    // 注意：以下外部API服务需要在实际项目中实现
    // @Autowired
    // private BureauVerificationApiService bureauVerificationApiService;
    //
    // @Autowired
    // private RealEstateApiService realEstateApiService;

    /**
     * 定时执行局直公房智能核验
     * 每天凌晨2点执行
     */
    @Transactional
    public void execute() {
        logger.info("开始执行局直公房智能核验任务");
        try {
            // 查询所有需要核验的局直公房申请
            List<HsQwApplyBureau> bureauList = findNeedVerificationList();

            if (bureauList != null && !bureauList.isEmpty()) {
                logger.info("找到{}条需要核验的局直公房申请", bureauList.size());

                for (HsQwApplyBureau bureau : bureauList) {
                    try {
                        // 创建核验记录
                        HsQwApplyBureauCheck check = new HsQwApplyBureauCheck();
                        check.setBureauId(bureau.getId());
                        check.setCheckDate(new Date());
                        check.setCheckType("0"); // 0-厅局公房
                        check.setNoticed("0"); // 0-未通知
                        check.setReviewStatus("0"); // 0-未复核
                        check.setClearanceStatus("0"); // 0-未清退

                        // 模拟不动产登记部门API获取不动产信息
                        if (bureau.getMainApplyer() != null && StringUtils.isNotBlank(bureau.getMainApplyer().getIdNum())) {
                            // 模拟不动产信息
                            JSONObject propertyInfo = new JSONObject();
                            propertyInfo.put("idCard", bureau.getMainApplyer().getIdNum());
                            propertyInfo.put("name", bureau.getMainApplyer().getName());
                            propertyInfo.put("hasProperty", false);
                            propertyInfo.put("checkTime", new Date());

                            check.setPropertyInfo(propertyInfo.toJSONString());
                        }

                        // 模拟核验服务进行综合核验
                        JSONObject verificationResult = new JSONObject();
                        verificationResult.put("verified", true);
                        verificationResult.put("applyId", bureau.getId());
                        verificationResult.put("checkTime", new Date());

                        // 添加核验项目
                        JSONObject items = new JSONObject();
                        items.put("idCardCheck", "pass");
                        items.put("propertyCheck", "pass");
                        items.put("housingFundCheck", "pass");
                        verificationResult.put("items", items);

                        // 保存核验详情
                        check.setVerifyDetail(verificationResult.toJSONString());

                        // 判断核验结果
                        boolean isValid = verificationResult.getBooleanValue("verified");

                        // 设置核验结果
                        check.setCheckResult(isValid ? "0" : "1"); // 0-正常 1-异常

                        // 如果核验结果为异常，设置应清退时间为30天后
                        if (!isValid) {
                            check.setClearanceDate(DateUtils.addDays(new Date(), 30));
                        }

                        // 保存核验记录
                        hsQwApplyBureauCheckService.save(check);

                        // 更新申请单核验状态
                        bureau.setCheckDate(new Date());
                        bureau.setAutoCheck(isValid ? "1" : "0"); // 1-有效 0-无效
                        hsQwApplyBureauService.update(bureau);

                        // 如果核验结果为无效，则发送通知
                        if (!isValid) {
                            sendInvalidNotification(bureau, check);
                            check.setNoticed("1"); // 1-已通知
                            hsQwApplyBureauCheckService.update(check);
                        }

                    } catch (Exception e) {
                        logger.error("核验局直公房申请失败，ID: " + bureau.getId(), e);
                    }
                }
            } else {
                logger.info("没有找到需要核验的局直公房申请");
            }
        } catch (Exception e) {
            logger.error("执行局直公房智能核验任务失败", e);
        }
        logger.info("局直公房智能核验任务执行完成");
    }

    /**
     * 查询需要进行智能核验的局直公房申请列表
     */
    private List<HsQwApplyBureau> findNeedVerificationList() {
        HsQwApplyBureau query = new HsQwApplyBureau();
        // 只查询正常状态的申请
        query.setStatus(HsQwApplyBureau.STATUS_NORMAL);
        // 禁用自动添加状态条件
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        // 查询条件：未核验或者核验时间超过30天的记录
        String extWhere = "AND ((a.auto_check IS NULL OR a.auto_check = '') " +
                "OR (a.check_date IS NOT NULL AND a.check_date < " + DateUtils.addDays(new Date(), -30) + "))";
        query.sqlMap().add("extWhere", extWhere);
        return hsQwApplyBureauService.findList(query);
    }


    /**
     * 发送无效通知
     */
    public void sendInvalidNotification(HsQwApplyBureau bureau, HsQwApplyBureauCheck check) {
        try {
            logger.info("发送局直公房申请无效通知，ID: {}", bureau.getId());

            // 构建通知内容
            String houseInfo = bureau.getHouse() != null ? bureau.getHouse().getEstate().getAddress() : bureau.getHouseId();
            String applyerName = bureau.getMainApplyer() != null ? bureau.getMainApplyer().getName() : "";
            String clearanceDate = check.getClearanceDate() != null ? DateUtils.formatDate(check.getClearanceDate(), "yyyy-MM-dd") : "";

            // 构建清退通知内容
            String noticeContent = "您申请的局直公房(" + houseInfo + ")未通过智能核验";
            if (StringUtils.isNotBlank(clearanceDate)) {
                noticeContent += "，请于" + clearanceDate + "前完成清退";
            }
            noticeContent += "。";

            // 发送系统内部消息通知
            sendInternalNotification(bureau, houseInfo, applyerName, noticeContent);

            // 发送数字空间通知
            sendDigitalSpaceNotification(bureau, houseInfo, applyerName, noticeContent);

            // 更新清退状态
            check.setClearanceStatus("1"); // 1-已通知

            logger.info("局直公房申请无效通知发送完成，ID: {}", bureau.getId());
        } catch (Exception e) {
            logger.error("发送局直公房申请无效通知异常，ID: " + bureau.getId(), e);
        }
    }

    /**
     * 发送系统内部通知
     */
    private void sendInternalNotification(HsQwApplyBureau bureau, String houseInfo, String applyerName, String noticeContent) {
        // 获取接收人
        String receiveUserCode = bureau.getCreateBy();
        if (StringUtils.isBlank(receiveUserCode) && bureau.getMainApplyer() != null) {
            receiveUserCode = bureau.getMainApplyer().getUserId();
        }

        // 获取申请单位管理员
        String unitAdminCode = bureau.getCreateBy();

        if (StringUtils.isNotBlank(receiveUserCode)) {
            // 创建PC消息内容
            PcMsgContent msgContent = new PcMsgContent();
            msgContent.setTitle("【局直公房智能核验】申请无效通知");
            msgContent.setContent(noticeContent);

            // 发送消息给申请人
            MsgPushUtils.push(msgContent, bureau.getId(), HsQwApplyBureau.class.getSimpleName(), receiveUserCode);

            // 发送消息给申请单位管理员
            if (StringUtils.isNotBlank(unitAdminCode) && !unitAdminCode.equals(receiveUserCode)) {
                MsgPushUtils.push(msgContent, bureau.getId(), HsQwApplyBureau.class.getSimpleName(), unitAdminCode);
            }
        }
    }

    /**
     * 发送数字空间通知
     */
    private void sendDigitalSpaceNotification(HsQwApplyBureau bureau, String houseInfo, String applyerName, String noticeContent) {
        // 获取接收人ID
        String receiveUserId = bureau.getCreateBy();
        if (StringUtils.isBlank(receiveUserId) && bureau.getMainApplyer() != null) {
            receiveUserId = bureau.getMainApplyer().getUserId();
        }

        // 获取申请单位管理员
        String unitAdminId = bureau.getCreateBy();
        String receiveUserIds = receiveUserId;
        if (StringUtils.isNotBlank(unitAdminId) && !unitAdminId.equals(receiveUserId)) {
            receiveUserIds += "," + unitAdminId;
        }

        if (StringUtils.isNotBlank(receiveUserIds)) {
            // 创建通知对象
            Api2NoticeBody noticeBody = new Api2NoticeBody();
            noticeBody.setMsgId(bureau.getId());
            noticeBody.setMsgType("3"); // 公告
            noticeBody.setMsgSource("局直公房智能核验");
            noticeBody.setTopic("申请无效通知");
            noticeBody.setContent(noticeContent);
            noticeBody.setFileIds("");
            noticeBody.setPublishUnit("住房保障中心");
            noticeBody.setPublishUnitId(bureau.getCreateBy());
            noticeBody.setPublishTime(new Date());
            noticeBody.setReceiveUserIds(receiveUserIds);

            // 发送通知
            try {
                Api2ResponseBody response = apiSzkjService.uploadNotice(noticeBody);
                if (response != null && response.getCode() == 1) {
                    logger.info("数字空间通知发送成功，ID: {}", bureau.getId());
                } else {
                    logger.warn("数字空间通知发送失败，ID: {}", bureau.getId());
                }
            } catch (Exception e) {
                logger.error("发送数字空间通知异常，ID: " + bureau.getId(), e);
            }
        }
    }


    /**
     * 手动触发核验任务
     */
    @Transactional
    public void manualExecuteVerification() {
        execute();
    }
}
