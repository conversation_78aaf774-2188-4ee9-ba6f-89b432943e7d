package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 获取单位下用户列表响应实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserUnderOrgData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String name;
    private String userId;
    private String account;
    private String mobile;
    private String orgId;
    private List<RoleData> roleList;

}
