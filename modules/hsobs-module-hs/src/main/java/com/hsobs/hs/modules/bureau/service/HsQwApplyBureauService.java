package com.hsobs.hs.modules.bureau.service;

import java.util.Date;
import java.util.List;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.utils.HsBpmService;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.bureau.dao.HsQwApplyBureauDao;
import java.util.Map;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;

import javax.annotation.PostConstruct;

/**
 * 局直管公房申请表Service
 * <AUTHOR>
 * @version 2025-02-19
 */
@Service
public class HsQwApplyBureauService extends CrudService<HsQwApplyBureauDao, HsQwApplyBureau> {

	@Autowired
	private HsQwApplyerService hsQwApplyerService;

	private HsBpmService<HsQwApplyBureau> hsBpmService;

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

	@Autowired
	private CommonBpmService commonBpmService;

	@PostConstruct
	public void init() {
		// 在注入后对 hsBpmService 的属性进行设置
		hsBpmService = new HsBpmService<>(HsQwApplyBureau.class);
		hsBpmService.setCrudService(this);
	}
	/**
	 * 获取单条数据
	 * @param hsQwApplyBureau
	 * @return
	 */
	@Override
	public HsQwApplyBureau get(HsQwApplyBureau hsQwApplyBureau) {
		HsQwApplyBureau entity =  super.get(hsQwApplyBureau);
		if (entity!=null){
			HsQwApplyer hsQwApplyer = new HsQwApplyer();
			hsQwApplyer.setApplyId(entity.getId());
			hsQwApplyer.setStatus(HsQwApplyer.STATUS_NORMAL);
			entity.setHsQwApplyerList(hsQwApplyerService.findList(hsQwApplyer));
		}
		return entity;
	}

	/**
	 * 查询分页数据
	 * @param hsQwApplyBureau 查询条件
	 * @param hsQwApplyBureau page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyBureau> findPage(HsQwApplyBureau hsQwApplyBureau) {
		return super.findPage(hsQwApplyBureau);
	}

	/**
	 * 查询列表数据
	 * @param hsQwApplyBureau
	 * @return
	 */
	@Override
	public List<HsQwApplyBureau> findList(HsQwApplyBureau hsQwApplyBureau) {
		return super.findList(hsQwApplyBureau);
	}

	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyBureau
	 */
	@Override
	@Transactional
	public void save(HsQwApplyBureau hsQwApplyBureau) {
		// 检查申请人信息是否提交
		this.checkApplyer(hsQwApplyBureau.getHsQwApplyerList());
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsQwApplyBureau.getStatus())){
			hsQwApplyBureau.setStatus(HsQwApplyBureau.STATUS_AUDIT);
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsQwApplyBureau.STATUS_NORMAL.equals(hsQwApplyBureau.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsQwApplyBureau.STATUS_DRAFT.equals(hsQwApplyBureau.getStatus())
				|| HsQwApplyBureau.STATUS_AUDIT.equals(hsQwApplyBureau.getStatus())){
			this.updateHouseStatus(hsQwApplyBureau.getHouseId(), HsQwPublicRentalHouse.STATUS_AUDIT);//锁定房源
			super.save(hsQwApplyBureau);
		}

		// 保存 HsQwApply子表
		for (HsQwApplyer hsQwApplyer : hsQwApplyBureau.getHsQwApplyerList()) {
			if (!HsQwApplyer.STATUS_DELETE.equals(hsQwApplyer.getStatus())) {
				hsQwApplyer.setApplyId(hsQwApplyBureau.getId());
				if (StringUtils.isBlank(hsQwApplyer.getUserId())){
					hsQwApplyer.setUserId("0");//先默认值
				}
				if (hsQwApplyer.getIsNewRecord()) {
					hsQwApplyerService.insert(hsQwApplyer);
				} else {
					hsQwApplyerService.update(hsQwApplyer);
				}
			} else {
				hsQwApplyerService.delete(hsQwApplyer);
			}
		}

		// 如果为审核状态，则进行审批流操作
		if (HsQwApplyBureau.STATUS_AUDIT.equals(hsQwApplyBureau.getStatus())){

			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", hsQwApplyBureau.getLeaveDays());

			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsQwApplyBureau.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsQwApplyBureau.getBpm().getTaskId())){
				BpmUtils.start(hsQwApplyBureau, "rent_apply_bureau", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsQwApplyBureau, variables, null);
			}
		}
	}

	private void checkApplyer(List<HsQwApplyer> hsQwApplyerList) {
		//判断是否含有主申请人信息
		final boolean[] hasMain = {false, false};
		hsQwApplyerList.forEach(k -> {
			hasMain[0] = hasMain[0] || k.getApplyRole().equals("0");
			hasMain[1] = hasMain[1] || k.getApplyRole().equals("1");
		});
		if (!hasMain[0]) {
			throw new ServiceException("未添加主申请人信息");
		}
		if (!hasMain[1]) {
			throw new ServiceException("未添加配偶信息");
		}

	}

	/**
	 * 更新状态
	 * @param hsQwApplyBureau
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyBureau hsQwApplyBureau) {
		HsQwApplyBureau realBean = this.get(hsQwApplyBureau.getId());
		if (hsQwApplyBureau.getStatus().equals(HsQwApplyBureau.STATUS_NORMAL)){
			realBean.setStartDate(new Date());
		} else {
			realBean.setEndDate(new Date());
		}
		//更新承租时间
		this.update(realBean);
		//状态更新
		super.updateStatus(hsQwApplyBureau);

		//若已完成配租，则需要更新房源配租状态
		if (hsQwApplyBureau.getStatus().equals(HsQwApply.STATUS_NORMAL)){
			this.updateHouseStatus(realBean.getHouseId(), "1");//流程结束，已配租
		} else if (StringUtils.isNotBlank(hsQwApplyBureau.getHouseId())){
			this.updateHouseStatus(realBean.getHouseId(), "0");//流程异常，恢复待配租
		}
	}

	public void updateHouseStatus(String houseId, String status) {
		HsQwPublicRentalHouse hsQwApplyHouse = hsQwPublicRentalHouseService.get(houseId);
		hsQwApplyHouse.setHouseStatus(status);
		hsQwPublicRentalHouseService.update(hsQwApplyHouse);
		hsQwApplyHouse.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);
		hsQwPublicRentalHouseService.updateStatus(hsQwApplyHouse);
	}
	/**
	 * 删除数据
	 * @param hsQwApplyBureau
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyBureau hsQwApplyBureau) {
		super.delete(hsQwApplyBureau);
	}

	public Page<HsQwApplyBureau> findPageByTask(HsQwApplyBureau hsQwApplyBureau, String[] status, String bpmStatus) {
		return commonBpmService.findTaskList(status, "rent_apply_bureau",hsQwApplyBureau, bpmStatus);
	}

	/**
	 * 执行用户腾退操作
	 * @param hsQwApplyBureau
	 */
	@Transactional
	public void clear(HsQwApplyBureau hsQwApplyBureau) {
		//停止任务
		if (hsQwApplyBureau.getStatus().equals(HsQwApplyBureau.STATUS_AUDIT)){
			this.disableBpm(hsQwApplyBureau);
		}
		//承租信息消除
		hsQwApplyBureau.setStatus("2");
		HsQwApplyBureau where = new HsQwApplyBureau();
		HsQwApplyBureau entity  = new HsQwApplyBureau();
		entity.setEndDate(new Date());
		entity.setStatus(HsQwApplyBureau.STATUS_DISABLE);
		where.setId(hsQwApplyBureau.getId());
		this.dao.updateByEntity(entity, where);
		this.updateStatus(hsQwApplyBureau);
		//房源重置为待配租
		this.updateHouseStatus(hsQwApplyBureau.getHouseId(), "0");
	}

	private void disableBpm(HsQwApplyBureau hsQwApplyBureau) {
		BpmProcIns procIns = BpmUtils.getBpmRuntimeService().getProcessInstanceByBusinessKey("rent_apply_bureau", hsQwApplyBureau.getId());
		BpmUtils.getBpmRuntimeService().stopProcessInstance(procIns);
	}

	public Page<HsQwApplyer> findHisApplyerPage(HsQwApplyer hsQwApplyer) {
		return hsQwApplyerService.findPage(hsQwApplyer);
	}

	public Page<HsQwApplyBureau> findBureauPage(HsQwApplyBureau hsQwApplyBureau) {
		hsQwApplyBureau.sqlMap().getWhere().disableAutoAddStatusWhere();
		hsQwApplyBureau.sqlMap().getWhere().and("status" , QueryType.NE , "1");
		return this.findPage(hsQwApplyBureau);
	}
}