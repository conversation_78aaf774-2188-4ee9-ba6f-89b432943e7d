package com.hsobs.hs.modules.formmanage.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 数据表单报送记录Entity
 * <AUTHOR>
 * @version 2025-02-21
 */
@Table(name="hs_data_form_report", alias="a", label="数据表单报送记录信息", columns={
		@Column(name="id", attrName="id", label="报送ID", isPK=true),
		@Column(name="delivery_id", attrName="deliveryId", label="表单下发消息ID"),
		@Column(name="template_id", attrName="templateId", label="表单模板ID"),
		@Column(name="report_office_code", attrName="reportOfficeCode", label="接收单位编码"),
		@Column(name="report_office_name", attrName="reportOfficeName", label="接收单位名称", queryType=QueryType.LIKE),
		@Column(name="report_date", attrName="reportDate", label="报送时间", isUpdateForce=true),
		@Column(name="report_user_code", attrName="reportUserCode", label="报送人用户编码"),
		@Column(name="report_user_name", attrName="reportUserName", label="报送人用户姓名", queryType=QueryType.LIKE),
		@Column(name="audit_status", attrName="auditStatus", label="审核状态", comment="审核状态(0-待审核 1-通过 2-退回)"),
		@Column(name="audit_comment", attrName="auditComment", label="审核意见"),
		@Column(name="audit_time", attrName="auditTime", label="审核时间", isUpdateForce=true),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, orderBy="a.update_date DESC"
)
public class HsDataFormReport extends DataEntity<HsDataFormReport> {
	
	private static final long serialVersionUID = 1L;
	private String deliveryId;		// 表单下发消息ID
	private String templateId;		// 表单模板ID
	private String reportOfficeCode;		// 接收单位编码
	private String reportOfficeName;		// 接收单位名称
	private Date reportDate;		// 报送时间
	private String reportUserCode;		// 报送人用户编码
	private String reportUserName;		// 报送人用户姓名
	private String auditStatus;		// 审核状态(0-待审核 1-通过 2-退回)
	private String auditComment;		// 审核意见
	private Date auditTime;		// 审核时间
	private String validTag;		// 是否有效;1-有效 0-无效

	public HsDataFormReport() {
		this(null);
	}
	
	public HsDataFormReport(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="表单下发消息ID长度不能超过 64 个字符")
	public String getDeliveryId() {
		return deliveryId;
	}

	public void setDeliveryId(String deliveryId) {
		this.deliveryId = deliveryId;
	}

	@Size(min=0, max=64, message="表单模板ID长度不能超过 64 个字符")
	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	
	@NotBlank(message="接收单位编码不能为空")
	@Size(min=0, max=64, message="接收单位编码长度不能超过 64 个字符")
	public String getReportOfficeCode() {
		return reportOfficeCode;
	}

	public void setReportOfficeCode(String reportOfficeCode) {
		this.reportOfficeCode = reportOfficeCode;
	}
	
	@NotBlank(message="接收单位名称不能为空")
	@Size(min=0, max=100, message="接收单位名称长度不能超过 100 个字符")
	public String getReportOfficeName() {
		return reportOfficeName;
	}

	public void setReportOfficeName(String reportOfficeName) {
		this.reportOfficeName = reportOfficeName;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getReportDate() {
		return reportDate;
	}

	public void setReportDate(Date reportDate) {
		this.reportDate = reportDate;
	}
	
	@NotBlank(message="报送人用户编码不能为空")
	@Size(min=0, max=64, message="报送人用户编码长度不能超过 64 个字符")
	public String getReportUserCode() {
		return reportUserCode;
	}

	public void setReportUserCode(String reportUserCode) {
		this.reportUserCode = reportUserCode;
	}
	
	@NotBlank(message="报送人用户姓名不能为空")
	@Size(min=0, max=100, message="报送人用户姓名长度不能超过 100 个字符")
	public String getReportUserName() {
		return reportUserName;
	}

	public void setReportUserName(String reportUserName) {
		this.reportUserName = reportUserName;
	}
	
	@Size(min=0, max=10, message="审核状态长度不能超过 10 个字符")
	public String getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(String auditStatus) {
		this.auditStatus = auditStatus;
	}
	
	@Size(min=0, max=500, message="审核意见长度不能超过 500 个字符")
	public String getAuditComment() {
		return auditComment;
	}

	public void setAuditComment(String auditComment) {
		this.auditComment = auditComment;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(Date auditTime) {
		this.auditTime = auditTime;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}
	
}