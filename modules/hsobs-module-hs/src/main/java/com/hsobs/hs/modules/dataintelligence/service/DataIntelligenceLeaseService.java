package com.hsobs.hs.modules.dataintelligence.service;

import java.util.*;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceLiftSubsidyDao;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceLeaseDao;


/**
 * 住房保障数据统计Service  公租房租赁情况统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class DataIntelligenceLeaseService extends CrudService<DataIntelligenceLeaseDao, DataIntelligenceLease> {

	@Autowired
	private DataIntelligenceLeaseDao dataIntelligenceLeaseDao;
	@Autowired
	private DataIntelligenceTotalService dataIntelligenceTotalService;

	/**
	 * 获取单条数据
	 * @param dataIntelligenceLease
	 * @return
	 */
	@Override
	public DataIntelligenceLease get(DataIntelligenceLease dataIntelligenceLease) {
		return super.get(dataIntelligenceLease);
	}
	
	/**
	 * 查询分页数据
	 * @param dataIntelligenceLease 查询条件
	 * @param dataIntelligenceLease page 分页对象
	 * @return
	 */
	@Override
	public Page<DataIntelligenceLease> findPage(DataIntelligenceLease dataIntelligenceLease) {
		return super.findPage(dataIntelligenceLease);
	}
	
	/**
	 * 查询列表数据
	 * @param dataIntelligenceLease
	 * @return
	 */
	@Override
	public List<DataIntelligenceLease> findList(DataIntelligenceLease dataIntelligenceLease) {
		return super.findList(dataIntelligenceLease);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param dataIntelligenceLease
	 */
	@Override
	@Transactional
	public void save(DataIntelligenceLease dataIntelligenceLease) {
		super.save(dataIntelligenceLease);
	}
	
	/**
	 * 更新状态
	 * @param dataIntelligenceLease
	 */
	@Override
	@Transactional
	public void updateStatus(DataIntelligenceLease dataIntelligenceLease) {
		super.updateStatus(dataIntelligenceLease);
	}
	
	/**
	 * 删除数据
	 * @param dataIntelligenceLease
	 */
	@Override
	@Transactional
	public void delete(DataIntelligenceLease dataIntelligenceLease) {
		dataIntelligenceLease.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataIntelligenceLease);
	}

	private  String getSqlOtherWhere(DataIntelligenceLease dataIntelligenceLease, Date startDate, Date endDate){
		String sqlOtherWhere = "";
		if (startDate != null || dataIntelligenceLease.getStartDate() != null) {
			Date date = (startDate!=null)?startDate:dataIntelligenceLease.getStartDate();
			sqlOtherWhere += " AND a.create_date >= '" + String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}
		if (endDate != null || dataIntelligenceLease.getEndDate() != null) {
			Date date = (endDate!=null)?endDate:dataIntelligenceLease.getEndDate();
			sqlOtherWhere += " AND a.create_date <= '" + String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}

		/*if(dataIntelligenceLease.getEstateName() != null && dataIntelligenceLease.getEstateName().length() > 0) {
			sqlOtherWhere += " AND estate.name like '%" + dataIntelligenceLease.getEstateName() + "%'";
		}*/
		if(dataIntelligenceLease.getEstateId() != null && dataIntelligenceLease.getEstateId().length() > 0) {
			sqlOtherWhere += " AND estate.id = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceLease.getEstateId()) + "'";
		}
		if(dataIntelligenceLease.getCity() != null && !"".equals(dataIntelligenceLease.getCity())){
			sqlOtherWhere += " AND estate.city = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceLease.getCity()) + "'";
		}
		if(dataIntelligenceLease.getArea() != null && !"".equals(dataIntelligenceLease.getArea())){
			sqlOtherWhere += " AND estate.area = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceLease.getArea()) + "'";
		}

		dataIntelligenceLease.sqlMap().getDataScope().addFilter("dsf", "Office", "jso.office_code", DataScope.CTRL_PERMI_HAVE);
		SqlMap sqlMap = dataIntelligenceLease.sqlMap();
		Object obWhere = sqlMap.get("dsf");
		if(obWhere != null){
			sqlOtherWhere += obWhere.toString();
		}
		String officeCode = null;
		if(dataIntelligenceLease.getOfficeCode() != null && dataIntelligenceLease.getOfficeCode().length() > 0) {
			officeCode = dataIntelligenceLease.getOfficeCode();
		}
		sqlOtherWhere += dataIntelligenceTotalService.getOfficePermission(officeCode);

		return sqlOtherWhere;
	}

	public Page<DataIntelligenceLease> findTotalStat(DataIntelligenceLease dataIntelligenceLease){

		Page<DataIntelligenceLease> pageMap = new Page<>();
		pageMap.setPageNo(dataIntelligenceLease.getPageNo());
		pageMap.setPageSize(dataIntelligenceLease.getPageSize());

		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("page", pageMap);
		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countTotalStat(mapPara);


		List<DataIntelligenceLease> statList = new ArrayList<>();
		pageMap.setList(statList);

		for(Map<String, Object> map: list) {

			DataIntelligenceLease leaseStat = new DataIntelligenceLease();
			leaseStat.setCity(dataIntelligenceLease.getCity());
			leaseStat.setArea(dataIntelligenceLease.getArea());

			Object ob = map.get("OFFICE_CODE");
			leaseStat.setOfficeCode((ob!=null)?ob.toString():"");
			ob = map.get("OFFICE_NAME");
			leaseStat.setOfficeName((ob!=null)?ob.toString():"");

			// 已申请数
			ob = map.get("APPLIED_FRO_COUNT");
			leaseStat.setAppliedForCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 待审批数
			ob = map.get("PENDING_APPROVAL_COUNT");
			leaseStat.setPendingApprovalCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 审批通过数
			ob = map.get("APPROVED_COUNT");
			leaseStat.setApprovedCount((ob!=null)?Long.valueOf(ob.toString()):0);

			statList.add(leaseStat);
		}

		return pageMap;
	}

	public Page<DataIntelligenceLease> findAreaStatDataPage(DataIntelligenceLease dataIntelligenceLease, boolean findpage){

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		String sqlWhere = "";
		if(!sqlOtherWhere.isEmpty()){
			sqlWhere = "WHERE " + sqlOtherWhere.substring(5);
		}

		//String sqlOrderBy = (dataIntelligenceLease.getOrderBy()!=null&&dataIntelligenceLease.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceLease.getOrderBy()):"";
		String sqlOrderBy = "";

				Page<DataIntelligenceLease> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlWhere", sqlWhere);
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceLease.getPageNo());
			pageMap.setPageSize(dataIntelligenceLease.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countAreaStat(mapPara);
		List<DataIntelligenceLease> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceLease leaseStat = new DataIntelligenceLease();

			leaseStat.setOfficeCode(dataIntelligenceLease.getOfficeCode());
			leaseStat.setStartDate(dataIntelligenceLease.getStartDate());
			leaseStat.setEndDate(dataIntelligenceLease.getEndDate());

			Object ob = map.get("ESTATE_ID");
			leaseStat.setEstateId((ob!=null)?ob.toString():"");
			ob = map.get("ESTATE_NAME");
			leaseStat.setEstateName((ob!=null)?ob.toString():"");

			// 审核通过
			ob = map.get("APPROVED_COUNT");
			leaseStat.setApprovedCount((ob!=null)?Long.valueOf(ob.toString()):0);

			// 已签订合同数
			ob = map.get("SIGNED_CONTRACT_COUNT");
			leaseStat.setSignedContractCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 未签订合同数
			ob = map.get("UNSIGNED_CONTRACT_COUNT");
			leaseStat.setUnsignedContractCount((ob!=null)?Long.valueOf(ob.toString()):0);

			ob = map.get("RECEIVABLES");
			double dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setReceivables(Long.valueOf((long)dV));
			ob = map.get("UNPAID");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setUnpaid(Long.valueOf((long)dV));
			ob = map.get("PAID");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setPaid(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_1");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea1(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_2");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea2(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_3");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea3(Long.valueOf((long)dV));

			statList.add(leaseStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

	public Page<DataIntelligenceLease> findAreaStatDataByOfficePage(DataIntelligenceLease dataIntelligenceLease, boolean findpage){

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		String sqlWhere = "";
		if(!sqlOtherWhere.isEmpty()){
			sqlWhere = "WHERE " + sqlOtherWhere.substring(5);
		}

		//String sqlOrderBy = (dataIntelligenceLease.getOrderBy()!=null&&dataIntelligenceLease.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceLease.getOrderBy()):"";
		String sqlOrderBy = "";

		Page<DataIntelligenceLease> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlWhere", sqlWhere);
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceLease.getPageNo());
			pageMap.setPageSize(dataIntelligenceLease.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countLeasePaidStatByOffice(mapPara);
		List<DataIntelligenceLease> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceLease leaseStat = new DataIntelligenceLease();

			leaseStat.setStartDate(dataIntelligenceLease.getStartDate());
			leaseStat.setEndDate(dataIntelligenceLease.getEndDate());

			Object ob = map.get("OFFICE_CODE");
			leaseStat.setOfficeCode((ob!=null)?ob.toString():"");
			ob = map.get("OFFICE_NAME");
			leaseStat.setOfficeName((ob!=null)?ob.toString():"");

			// 已签订合同数
			ob = map.get("SIGNED_CONTRACT_COUNT");
			leaseStat.setSignedContractCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 未签订合同数
			ob = map.get("UNSIGNED_CONTRACT_COUNT");
			leaseStat.setUnsignedContractCount((ob!=null)?Long.valueOf(ob.toString()):0);

			ob = map.get("RECEIVABLES");
			double dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setReceivables(Long.valueOf((long)dV));
			ob = map.get("UNPAID");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setUnpaid(Long.valueOf((long)dV));
			ob = map.get("PAID");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setPaid(Long.valueOf((long)dV));

			statList.add(leaseStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

	public List<DataIntelligenceLease> countAreaStat(DataIntelligenceLease dataIntelligenceLease) {

		return findAreaStatDataPage(dataIntelligenceLease, false).getList();
	}

	public Page<DataIntelligenceLease> findLeaseOfficeStatDataPage(DataIntelligenceLease dataIntelligenceLease, boolean findpage){
		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		//String sqlOrderBy = (dataIntelligenceLease.getOrderBy()!=null&&dataIntelligenceLease.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceLease.getOrderBy()):"";
		String sqlOrderBy = "";

		Page<DataIntelligenceLease> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceLease.getPageNo());
			pageMap.setPageSize(dataIntelligenceLease.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countLeaseOfficeStat(mapPara);
		List<DataIntelligenceLease> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceLease leaseStat = new DataIntelligenceLease();

			leaseStat.setEstateName(dataIntelligenceLease.getEstateName());
			leaseStat.setStartDate(dataIntelligenceLease.getStartDate());
			leaseStat.setEndDate(dataIntelligenceLease.getEndDate());
			leaseStat.setCity(dataIntelligenceLease.getCity());
			leaseStat.setArea(dataIntelligenceLease.getArea());

			Object ob = map.get("OFFICE_CODE");
			leaseStat.setOfficeCode((ob!=null)?ob.toString():"");
			ob = map.get("OFFICE_NAME");
			leaseStat.setOfficeName((ob!=null)?ob.toString():"");

			// 已入住数
			ob = map.get("CHECK_IN_COUNT");
			leaseStat.setCheckInCount((ob!=null)?Long.valueOf(ob.toString()):0);

			statList.add(leaseStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

	public Page<DataIntelligenceLease> findLeaseAreaStatDataPage(DataIntelligenceLease dataIntelligenceLease, boolean findpage){
		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		//String sqlOrderBy = (dataIntelligenceLease.getOrderBy()!=null&&dataIntelligenceLease.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceLease.getOrderBy()):"";
		String sqlOrderBy = "";

		Page<DataIntelligenceLease> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceLease.getPageNo());
			pageMap.setPageSize(dataIntelligenceLease.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countLeaseAreaOfficeStat(mapPara);
		List<DataIntelligenceLease> statList = new ArrayList<>();

		double dV;
		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceLease leaseStat = new DataIntelligenceLease();

			leaseStat.setEstateName(dataIntelligenceLease.getEstateName());
			leaseStat.setStartDate(dataIntelligenceLease.getStartDate());
			leaseStat.setEndDate(dataIntelligenceLease.getEndDate());
			leaseStat.setCity(dataIntelligenceLease.getCity());
			leaseStat.setArea(dataIntelligenceLease.getArea());

			Object ob = map.get("OFFICE_CODE");
			leaseStat.setOfficeCode((ob!=null)?ob.toString():"");
			ob = map.get("OFFICE_NAME");
			leaseStat.setOfficeName((ob!=null)?ob.toString():"");

			ob = map.get("BUILDING_AREA");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_1");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea1(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_2");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea2(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_3");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea3(Long.valueOf((long)dV));

			statList.add(leaseStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

	public List<DataIntelligenceLease> countLeaseOfficeStat(DataIntelligenceLease dataIntelligenceLease) {

		return findLeaseOfficeStatDataPage(dataIntelligenceLease, false).getList();
	}

	public Page<DataIntelligenceLease> findLeaseFeeStatDataPage(DataIntelligenceLease dataIntelligenceLease, boolean findpage){
		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		String sqlWhere = "";
		if(sqlOtherWhere.length() > 5) {
			sqlWhere =  "WHERE " + sqlOtherWhere.substring(5);
			sqlWhere = sqlWhere.replace("a.create_date", "f.FEE_MONTH");
		}
		//String sqlOrderBy = (dataIntelligenceLease.getOrderBy()!=null&&dataIntelligenceLease.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceLease.getOrderBy()):"";
		String sqlOrderBy = "";

		Page<DataIntelligenceLease> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlWhere", sqlWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceLease.getPageNo());
			pageMap.setPageSize(dataIntelligenceLease.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countLeaseFEEStat(mapPara);
		List<DataIntelligenceLease> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceLease leaseStat = new DataIntelligenceLease();

			leaseStat.setOfficeCode(dataIntelligenceLease.getOfficeCode());
			leaseStat.setStartDate(dataIntelligenceLease.getStartDate());
			leaseStat.setEndDate(dataIntelligenceLease.getEndDate());

			Object ob = map.get("ESTATE_ID");
			leaseStat.setEstateId((ob!=null)?ob.toString():"");
			ob = map.get("ESTATE_NAME");
			leaseStat.setEstateName((ob!=null)?ob.toString():"");

			ob = map.get("RECEIVABLES");
			double dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setReceivables(Long.valueOf((long)dV));
			ob = map.get("UNPAID");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setUnpaid(Long.valueOf((long)dV));
			ob = map.get("PAID");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setPaid(Long.valueOf((long)dV));

			statList.add(leaseStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

	public List<DataIntelligenceLease> countLeaseFeeStat(DataIntelligenceLease dataIntelligenceLease) {

		return findLeaseFeeStatDataPage(dataIntelligenceLease, false).getList();
	}


	public Page<DataIntelligenceLease> findLeaseBuildingAreaPage(DataIntelligenceLease dataIntelligenceLease, boolean findpage){
		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		//String sqlOrderBy = (dataIntelligenceLease.getOrderBy()!=null&&dataIntelligenceLease.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceLease.getOrderBy()):"";
		String sqlOrderBy = "";

				Page<DataIntelligenceLease> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceLease.getPageNo());
			pageMap.setPageSize(dataIntelligenceLease.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countLeaseBuildingAreaStat(mapPara);
		List<DataIntelligenceLease> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceLease leaseStat = new DataIntelligenceLease();

			leaseStat.setOfficeCode(dataIntelligenceLease.getOfficeCode());
			leaseStat.setStartDate(dataIntelligenceLease.getStartDate());
			leaseStat.setEndDate(dataIntelligenceLease.getEndDate());

			Object ob = map.get("ESTATE_ID");
			leaseStat.setEstateId((ob!=null)?ob.toString():"");
			ob = map.get("ESTATE_NAME");
			leaseStat.setEstateName((ob!=null)?ob.toString():"");

			ob = map.get("BUILDING_AREA");
			double dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_1");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea1(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_2");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea2(Long.valueOf((long)dV));

			ob = map.get("BUILDING_AREA_3");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setBuildingArea3(Long.valueOf((long)dV));

			statList.add(leaseStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}
	public List<DataIntelligenceLease> countLeaseBuildingAreaStat(DataIntelligenceLease dataIntelligenceLease) {

		return findLeaseBuildingAreaPage(dataIntelligenceLease, false).getList();
	}



	public Page<DataIntelligenceLease> findVacantAreaPage(DataIntelligenceLease dataIntelligenceLease, boolean findpage){
		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		sqlOtherWhere = sqlOtherWhere.replace("a.create_date", "house.update_date");

		//String sqlOrderBy = (dataIntelligenceLease.getOrderBy()!=null&&dataIntelligenceLease.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceLease.getOrderBy()):"";
		String sqlOrderBy = "";

				Page<DataIntelligenceLease> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceLease.getPageNo());
			pageMap.setPageSize(dataIntelligenceLease.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countVacantAreaStat(mapPara);
		List<DataIntelligenceLease> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceLease leaseStat = new DataIntelligenceLease();

			leaseStat.setOfficeCode(dataIntelligenceLease.getOfficeCode());
			leaseStat.setStartDate(dataIntelligenceLease.getStartDate());
			leaseStat.setEndDate(dataIntelligenceLease.getEndDate());

			Object ob = map.get("ESTATE_ID");
			leaseStat.setEstateId((ob!=null)?ob.toString():"");
			ob = map.get("ESTATE_NAME");
			leaseStat.setEstateName((ob!=null)?ob.toString():"");

			ob = map.get("VACANT_COUNT");
			leaseStat.setVacantCount((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("VACANT_AREA");
			double dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setVacantArea(Long.valueOf((long)dV));

			ob = map.get("VACANT0_COUNT");
			leaseStat.setVacant0Count((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("VACANT0_AREA");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setVacant0Area(Long.valueOf((long)dV));

			ob = map.get("VACANT3_COUNT");
			leaseStat.setVacant3Count((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("VACANT3_AREA");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setVacant3Area(Long.valueOf((long)dV));

			ob = map.get("VACANT4_COUNT");
			leaseStat.setVacant4Count((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("VACANT4_AREA");
			dV = (ob!=null)?Double.valueOf(ob.toString()):0;
			leaseStat.setVacant4Area(Long.valueOf((long)dV));

			statList.add(leaseStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}
	public List<DataIntelligenceLease> countVacantAreaStat(DataIntelligenceLease dataIntelligenceLease) {

		return findVacantAreaPage(dataIntelligenceLease, false).getList();
	}


	public String countUnpadByOffice(DataIntelligenceLease dataIntelligenceLease, int type) {

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceLease, null, null);
		List<Map<String,Object>> list = dataIntelligenceLeaseDao.countUnpaidOffice(sqlOtherWhere);

		Object ob;
		List<Map<String, Object>> newList = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> unitMap = list.get(i);
			Map<String, Object> map = new HashMap<>();

			if(type == 1) {
				ob = unitMap.get("UNPAID");			// 拖欠金额
			}else{
				ob = unitMap.get("UNPAID_COUNT");	// 拖欠账单
			}
			if(ob == null || "0".equals(ob.toString()))
				continue;
			map.put("value", ob.toString());
			ob = unitMap.get("OFFICE_NAME");
			map.put("name", (ob!=null)?ob.toString():"");

			newList.add(map);
		}

		Map<String, Object> map = new HashMap<>();
		map.put("data", newList);
		if(type == 1) {
			map.put("title", "拖欠金额占比");
		}else{
			map.put("title", "拖欠时长占比");
		}
		return JSON.toJSONString(map);
	}
}