package com.hsobs.hs.modules.house.service.deleteCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.bureau.dao.HsQwApplyBureauDao;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 局直公房申请单，查询所有房源的申请单
 */
@Service
public class HsQwHouseDeleteCheckMethodQwApplyBureau implements HsQwHouseDeleteCheckMethod {
    @Autowired
    private HsQwApplyBureauDao hsQwApplyBureauDao;

    @Override
    public void execute(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        HsQwApplyBureau query = new HsQwApplyBureau();
        query.setHouseId(hsQwPublicRentalHouse.getId());
        query.setStatus_in(new String[]{HsQwApply.STATUS_NORMAL, HsQwApply.STATUS_AUDIT});
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        long count = hsQwApplyBureauDao.findCount(query);
        if (count>0){
            throw new ServiceException("该房源存在关联的局直公房申请单，请先取消申请单后再重新操作！");
        }
    }
}
