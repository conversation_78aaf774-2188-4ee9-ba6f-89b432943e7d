package com.hsobs.hs.modules.external.entity;

import com.hsobs.hs.modules.external.entity.validated.NotNullApplyMatterGroup;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

public class ApiHsQwApplyer extends ApiBody{

    @HsMapTo("name")
    @NotBlank(message = "姓名不可为空" )
    private String xm;

    @HsMapTo("idNum")
    @NotBlank(message = "身份证不可为空" )
    private String sfzh;

    @HsMapTo("workPosition")
    private String sqrzj;

    @HsMapTo("workTime")
    private String cjgzsj;

    @HsMapTo("phone")
    private String sjh;

    @HsMapTo("organization")
    private String gzdw;

    @HsMapTo("annualIncome")
    private String ndzsr;

    @HsMapTo("status")
    private String delete;

    @HsMapTo("workAge")
    private String gl;

    @HsMapTo("marryStatus")
    private String hyzk;

    @HsMapTo("deathDate")
    private String qssj;

    @HsMapTo("userType")
    private String rylx;

    @HsMapTo("applyRole")
    @NotBlank(message = "申请角色不能为空")
    private String sqrlx;

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public void setSfzh(String sfzh) {
        this.sfzh = sfzh;
    }

    public String getSqrzj() {
        return sqrzj;
    }

    public void setSqrzj(String sqrzj) {
        this.sqrzj = sqrzj;
    }

    public String getCjgzsj() {
        return cjgzsj;
    }

    public void setCjgzsj(String cjgzsj) {
        this.cjgzsj = cjgzsj;
    }

    public String getSjh() {
        return sjh;
    }

    public void setSjh(String sjh) {
        this.sjh = sjh;
    }

    public String getGzdw() {
        return gzdw;
    }

    public void setGzdw(String gzdw) {
        this.gzdw = gzdw;
    }

    public String getNdzsr() {
        return ndzsr;
    }

    public void setNdzsr(String ndzsr) {
        this.ndzsr = ndzsr;
    }

    public String getGl() {
        return gl;
    }

    public void setGl(String gl) {
        this.gl = gl;
    }

    public String getHyzk() {
        return hyzk;
    }

    public void setHyzk(String hyzk) {
        this.hyzk = hyzk;
    }

    public String getQssj() {
        return qssj;
    }

    public void setQssj(String qssj) {
        this.qssj = qssj;
    }

    public String getRylx() {
        return rylx;
    }

    public void setRylx(String rylx) {
        this.rylx = rylx;
    }

    public String getSqrlx() {
        return sqrlx;
    }

    public void setSqrlx(String sqrlx) {
        this.sqrlx = sqrlx;
    }

    public String getDelete() {
        return delete;
    }

    public void setDelete(String delete) {
        this.delete = delete;
    }

    public String getSfzh() {
        return sfzh;
    }
}
