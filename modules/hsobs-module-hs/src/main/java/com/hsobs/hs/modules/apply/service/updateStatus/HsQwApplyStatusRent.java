package com.hsobs.hs.modules.apply.service.updateStatus;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.jeesite.common.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class HsQwApplyStatusRent implements HsQwApplyStatus{


    @Override
    public String getApplyMater() {
        return "0";
    }

    @Override
    public void execute(HsQwApply realBean, HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        //执行清退
        if (hsQwApply.isClear()) {
            hsQwApplyService.invalidApplyInfo(realBean);
        } else {
            //状态更新
            hsQwApplyService.realUpdateStatus(hsQwApply);
            //更新房源状态
            //若已完成配租，则需要更新房源配租状态
            if (hsQwApply.getStatus().equals(HsQwApply.STATUS_NORMAL)) {
                hsQwApplyService.updateHouseStatus(realBean.getHouseId(), "1");//流程结束，已配租
            } else if (StringUtils.isNotBlank(hsQwApply.getHouseId())) {
                hsQwApplyService.updateHouseStatus(realBean.getHouseId(), "0");//流程异常，恢复待配租
            }

            //其他流程中的申请单也一并消亡
            if (hsQwApply.getStatus().equals(HsQwApply.STATUS_DISABLE)){
                hsQwApplyService.invalidBatchRelateApply(hsQwApply.getId());
            }
        }
    }
}
