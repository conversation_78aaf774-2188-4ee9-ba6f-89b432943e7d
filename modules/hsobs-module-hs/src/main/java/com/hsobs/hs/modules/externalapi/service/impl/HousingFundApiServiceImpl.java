package com.hsobs.hs.modules.externalapi.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.externalapi.config.ExternalApiConfig;
import com.hsobs.hs.modules.externalapi.service.AbstractExternalApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 公积金部门API服务实现类
 * 
 * <AUTHOR>
 * @version 2024-05-20
 */
@Service
public class HousingFundApiServiceImpl extends AbstractExternalApiService {

    @Autowired
    private ExternalApiConfig externalApiConfig;
    
    @Override
    protected ExternalApiConfig.DepartmentConfig getDepartmentConfig() {
        return externalApiConfig.getHousingFund();
    }
    
    @Override
    public String getDepartmentName() {
        return "housingFund";
    }
    
    /**
     * 公积金账户信息查询
     * 
     * @param idCard 身份证号
     * @return 账户信息
     */
    public JSONObject queryAccountInfo(String idCard) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        
        return callApi("queryAccountInfo", params);
    }
    
    /**
     * 公积金缴存记录查询
     * 
     * @param idCard 身份证号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 缴存记录
     */
    public JSONObject queryDepositRecords(String idCard, String startDate, String endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        
        return callApi("queryDepositRecords", params);
    }
    
    /**
     * 公积金贷款信息查询
     * 
     * @param idCard 身份证号
     * @return 贷款信息
     */
    public JSONObject queryLoanInfo(String idCard) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        
        return callApi("queryLoanInfo", params);
    }
    
    /**
     * 公积金提取记录查询
     * 
     * @param idCard 身份证号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 提取记录
     */
    public JSONObject queryWithdrawalRecords(String idCard, String startDate, String endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        
        return callApi("queryWithdrawalRecords", params);
    }
    
    @Override
    protected JSONObject createMockResponse(String apiName, Map<String, Object> params) {
        JSONObject response = super.createMockResponse(apiName, params);
        JSONObject data = response.getJSONObject("data");
        
        // 根据不同的API创建不同的模拟数据
        switch (apiName) {
            case "queryAccountInfo":
                data.put("accountId", "HF123456");
                data.put("idCard", params.get("idCard"));
                data.put("name", "模拟姓名");
                data.put("balance", 85000.50);
                data.put("status", "正常");
                data.put("openDate", "2010-01-01");
                data.put("monthlyDeposit", 1500);
                data.put("companyName", "模拟公司名称");
                break;
                
            case "queryDepositRecords":
                JSONArray depositRecords = new JSONArray();
                for (int i = 1; i <= 12; i++) {
                    JSONObject record = new JSONObject();
                    record.put("recordId", "DR" + i);
                    record.put("accountId", "HF123456");
                    record.put("depositDate", "2023-" + (i < 10 ? "0" + i : i) + "-15");
                    record.put("amount", 1500);
                    record.put("personalAmount", 750);
                    record.put("companyAmount", 750);
                    record.put("companyName", "模拟公司名称");
                    depositRecords.add(record);
                }
                data.put("records", depositRecords);
                data.put("total", depositRecords.size());
                break;
                
            case "queryLoanInfo":
                JSONArray loans = new JSONArray();
                JSONObject loan = new JSONObject();
                loan.put("loanId", "L123456");
                loan.put("accountId", "HF123456");
                loan.put("idCard", params.get("idCard"));
                loan.put("loanAmount", 500000);
                loan.put("loanDate", "2020-01-01");
                loan.put("loanTerm", 240);
                loan.put("interestRate", 3.25);
                loan.put("monthlyPayment", 2800.75);
                loan.put("remainingPrincipal", 420000);
                loan.put("status", "正常");
                loan.put("propertyAddress", "模拟房产地址");
                loans.add(loan);
                data.put("loans", loans);
                data.put("total", loans.size());
                break;
                
            case "queryWithdrawalRecords":
                JSONArray withdrawalRecords = new JSONArray();
                JSONObject withdrawal = new JSONObject();
                withdrawal.put("recordId", "WR123456");
                withdrawal.put("accountId", "HF123456");
                withdrawal.put("idCard", params.get("idCard"));
                withdrawal.put("withdrawalDate", "2022-06-15");
                withdrawal.put("amount", 50000);
                withdrawal.put("purpose", "购房");
                withdrawal.put("status", "已完成");
                withdrawalRecords.add(withdrawal);
                data.put("records", withdrawalRecords);
                data.put("total", withdrawalRecords.size());
                break;
        }
        
        return response;
    }
}
