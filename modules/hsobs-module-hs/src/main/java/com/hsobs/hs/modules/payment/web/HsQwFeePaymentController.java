package com.hsobs.hs.modules.payment.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.payment.entity.HsQwFeePayment;
import com.hsobs.hs.modules.payment.service.HsQwFeePaymentService;

/**
 * 租赁资格轮候物业费催单Controller
 * <AUTHOR>
 * @version 2025-01-20
 */
@Controller
@RequestMapping(value = "${adminPath}/payment/hsQwFeePayment")
public class HsQwFeePaymentController extends BaseController {

	@Autowired
	private HsQwFeePaymentService hsQwFeePaymentService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwFeePayment get(String id, boolean isNewRecord) {
		return hsQwFeePaymentService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("payment:hsQwFeePayment:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwFeePayment hsQwFeePayment, Model model) {
		model.addAttribute("hsQwFeePayment", hsQwFeePayment);
		return "modules/payment/hsQwFeePaymentList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("payment:hsQwFeePayment:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwFeePayment> listData(HsQwFeePayment hsQwFeePayment, HttpServletRequest request, HttpServletResponse response) {
		hsQwFeePayment.setPage(new Page<>(request, response));
		Page<HsQwFeePayment> page = hsQwFeePaymentService.findPage(hsQwFeePayment);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("payment:hsQwFeePayment:view")
	@RequestMapping(value = "form")
	public String form(HsQwFeePayment hsQwFeePayment, Model model) {
		model.addAttribute("hsQwFeePayment", hsQwFeePayment);
		return "modules/payment/hsQwFeePaymentForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("payment:hsQwFeePayment:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwFeePayment hsQwFeePayment) {
		hsQwFeePaymentService.save(hsQwFeePayment);
		return renderResult(Global.TRUE, text("保存租赁资格轮候物业费催单成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("payment:hsQwFeePayment:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwFeePayment hsQwFeePayment) {
		hsQwFeePaymentService.delete(hsQwFeePayment);
		return renderResult(Global.TRUE, text("删除租赁资格轮候物业费催单成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("payment:hsQwFeePayment:view")
	@RequestMapping(value = "hsQwFeePaymentSelect")
	public String hsQwFeePaymentSelect(HsQwFeePayment hsQwFeePayment, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwFeePayment", hsQwFeePayment);
		return "modules/payment/hsQwFeePaymentSelect";
	}
	
}