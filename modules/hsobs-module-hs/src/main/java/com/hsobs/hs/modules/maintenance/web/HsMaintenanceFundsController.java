package com.hsobs.hs.modules.maintenance.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFunds;
import com.hsobs.hs.modules.maintenance.service.HsMaintenanceFundsService;

/**
 * 维修资金信息Controller
 * <AUTHOR>
 * @version 2024-11-28
 */
@Controller
@RequestMapping(value = "${adminPath}/maintenance/hsMaintenanceFunds")
public class HsMaintenanceFundsController extends BaseController {

	@Autowired
	private HsMaintenanceFundsService hsMaintenanceFundsService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsMaintenanceFunds get(String id, boolean isNewRecord) {
		return hsMaintenanceFundsService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsMaintenanceFunds hsMaintenanceFunds, Model model) {
		model.addAttribute("hsMaintenanceFunds", hsMaintenanceFunds);
		return "modules/maintenance/hsMaintenanceFundsList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsMaintenanceFunds> listData(HsMaintenanceFunds hsMaintenanceFunds, HttpServletRequest request, HttpServletResponse response) {
		hsMaintenanceFunds.setPage(new Page<>(request, response));
		Page<HsMaintenanceFunds> page = hsMaintenanceFundsService.findPage(hsMaintenanceFunds);
		return page;
	}
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:view")
	@RequestMapping(value = "fullListData")
	@ResponseBody
	public List<HsMaintenanceFunds> fullListData(HsMaintenanceFunds hsMaintenanceFunds, HttpServletRequest request, HttpServletResponse response) {
        return hsMaintenanceFundsService.findList(hsMaintenanceFunds);
	}

	@RequiresPermissions("maintenance:hsMaintenanceFunds:view")
	@RequestMapping(value = "singleData")
	@ResponseBody
	public HsMaintenanceFunds singleData(HsMaintenanceFunds hsMaintenanceFunds, HttpServletRequest request, HttpServletResponse response) {
		if (hsMaintenanceFunds.getId() == null || "".equals(hsMaintenanceFunds.getId())) {
			return null;
		} else {
			return hsMaintenanceFundsService.get(hsMaintenanceFunds.getId());
		}
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:view")
	@RequestMapping(value = "form")
	public String form(HsMaintenanceFunds hsMaintenanceFunds, Model model) {
		model.addAttribute("hsMaintenanceFunds", hsMaintenanceFunds);
		if (hsMaintenanceFunds.getInputFunds() == null) {
			hsMaintenanceFunds.setInputFunds(0.0d);
		}
		return "modules/maintenance/hsMaintenanceFundsForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsMaintenanceFunds hsMaintenanceFunds) {
		String code = hsMaintenanceFunds.getIsNewRecord() ? "保存维修资金信息成功" : "更新维修资金信息成功！";
		hsMaintenanceFundsService.save(hsMaintenanceFunds);
		return renderResult(Global.TRUE, text(code));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsMaintenanceFunds hsMaintenanceFunds) {
		hsMaintenanceFunds.setStatus(HsMaintenanceFunds.STATUS_DISABLE);
		hsMaintenanceFundsService.updateStatus(hsMaintenanceFunds);
		return renderResult(Global.TRUE, text("停用维修资金信息表成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsMaintenanceFunds hsMaintenanceFunds) {
		hsMaintenanceFunds.setStatus(HsMaintenanceFunds.STATUS_NORMAL);
		hsMaintenanceFundsService.updateStatus(hsMaintenanceFunds);
		return renderResult(Global.TRUE, text("启用维修资金信息表成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsMaintenanceFunds hsMaintenanceFunds) {
		hsMaintenanceFundsService.delete(hsMaintenanceFunds);
		return renderResult(Global.TRUE, text("删除维修资金信息表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:view")
	@RequestMapping(value = "hsMaintenanceFundsSelect")
	public String hsMaintenanceFundsSelect(HsMaintenanceFunds hsMaintenanceFunds, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsMaintenanceFunds", hsMaintenanceFunds);
		return "modules/maintenance/hsMaintenanceFundsSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceFunds:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsMaintenanceFunds hsMaintenanceFunds, HttpServletResponse response) {
		List<HsMaintenanceFunds> list = hsMaintenanceFundsService.findList(hsMaintenanceFunds);
		String fileName = "维修资金" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("维修资金", HsMaintenanceFunds.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	
}