package com.hsobs.hs.modules.qualification.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.qualification.entity.HsQwApplyQualification;
import com.hsobs.hs.modules.qualification.service.HsQwApplyQualificationService;

/**
 * 租赁资格轮候资格配置表Controller
 * <AUTHOR>
 * @version 2025-03-13
 */
@Controller
@RequestMapping(value = "${adminPath}/qualification/hsQwApplyQualification")
public class HsQwApplyQualificationController extends BaseController {

	@Autowired
	private HsQwApplyQualificationService hsQwApplyQualificationService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyQualification get(String id, boolean isNewRecord) {
		return hsQwApplyQualificationService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("qualification:hsQwApplyQualification:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyQualification hsQwApplyQualification, Model model) {
		model.addAttribute("hsQwApplyQualification", hsQwApplyQualification);
		return "modules/qualification/hsQwApplyQualificationList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("qualification:hsQwApplyQualification:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyQualification> listData(HsQwApplyQualification hsQwApplyQualification, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyQualification.setPage(new Page<>(request, response));
		Page<HsQwApplyQualification> page = hsQwApplyQualificationService.findPage(hsQwApplyQualification);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("qualification:hsQwApplyQualification:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyQualification hsQwApplyQualification, Model model) {
		model.addAttribute("hsQwApplyQualification", hsQwApplyQualification);
		return "modules/qualification/hsQwApplyQualificationForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("qualification:hsQwApplyQualification:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyQualification hsQwApplyQualification) {
		hsQwApplyQualificationService.save(hsQwApplyQualification);
		return renderResult(Global.TRUE, text("保存租赁资格轮候资格配置表成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("qualification:hsQwApplyQualification:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsQwApplyQualification hsQwApplyQualification) {
		hsQwApplyQualification.setStatus(HsQwApplyQualification.STATUS_DISABLE);
		hsQwApplyQualificationService.updateStatus(hsQwApplyQualification);
		return renderResult(Global.TRUE, text("停用租赁资格轮候资格配置表成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("qualification:hsQwApplyQualification:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsQwApplyQualification hsQwApplyQualification) {
		hsQwApplyQualification.setStatus(HsQwApplyQualification.STATUS_NORMAL);
		hsQwApplyQualificationService.updateStatus(hsQwApplyQualification);
		return renderResult(Global.TRUE, text("启用租赁资格轮候资格配置表成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("qualification:hsQwApplyQualification:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyQualification hsQwApplyQualification) {
		hsQwApplyQualificationService.delete(hsQwApplyQualification);
		return renderResult(Global.TRUE, text("删除租赁资格轮候资格配置表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("qualification:hsQwApplyQualification:view")
	@RequestMapping(value = "hsQwApplyQualificationSelect")
	public String hsQwApplyQualificationSelect(HsQwApplyQualification hsQwApplyQualification, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwApplyQualification", hsQwApplyQualification);
		return "modules/qualification/hsQwApplyQualificationSelect";
	}
	
}