package com.hsobs.hs.modules.apply.service.applyRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 申请人及其家庭成员未曾购买政策性住房。
 * 若申请人再婚且前配偶曾拥有政策性住房但已分割或转移，离婚时间需超过3年。
 */
@Service
public class HsQwApplyRulePolicy implements HsQwApplyRuleMethod {

    @Override
    public void execute(HsQwApply hsQwApply, Map<String, HsQwFormAlarm> hsQwFormAlarmMap) {
        // todo 申请人及其家庭成员未曾购买政策性住房,调用外部接口
        //  todo 若申请人再婚且前配偶曾拥有政策性住房但已分割或转移，离婚时间需超过3年。
    }
}
