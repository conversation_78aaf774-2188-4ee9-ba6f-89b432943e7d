package com.hsobs.hs.modules.bureau.entity;


import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 局直管公房申请表Entity
 *
 * <AUTHOR>
 * @version 2025-02-19
 */
@Table(name = "hs_qw_apply_bureau", alias = "a", label = "局直管公房申请表信息", columns = {
        @Column(name = "id", attrName = "id", label = "编号", isPK = true),
        @Column(name = "house_id", attrName = "houseId", label = "房源编号"),
        @Column(name = "auto_check", attrName = "authCheck", label = "智能校验"),
        @Column(name = "start_date", attrName = "startDate", label = "开始承租时间"),
        @Column(name = "end_date", attrName = "endDate", label = "结束承租时间"),
        @Column(name = "check_date", attrName = "checkDate", label = "核验时间"),
        @Column(includeEntity = DataEntity.class),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "h",
                on = "a.house_id = h.id", attrName = "house",
                columns = {@Column(includeEntity = HsQwPublicRentalHouse.class)}),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "o",
                on = "o.id = h.estate_id", attrName = "house.estate",
                columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)}),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "ha",
                on = "ha.apply_id = a.id and ha.apply_role = 0 and ha.status=0", attrName = "mainApplyer",
                columns = {@Column(includeEntity = HsQwApplyer.class)}),
},extWhereKeys = "extWhere", orderBy = "a.update_date DESC"
)
public class HsQwApplyBureau extends BpmEntity<HsQwApplyBureau> {

    private static final long serialVersionUID = 1L;
    private String houseId;        // 房源编号

    private HsQwPublicRentalHouse house;

    private HsQwApplyer mainApplyer;

    private String autoCheck;

    private Date checkDate;

    private Date startDate;

    private Date endDate;

    private List<HsQwApplyer> hsQwApplyerList = ListUtils.newArrayList();        // 子表列表

    @ExcelFields({
            @ExcelField(title="编号", attrName="id", align=Align.CENTER, sort=10),
            @ExcelField(title="房源编号", attrName="houseId", align=Align.CENTER, sort=20),
            @ExcelField(title="房源信息", attrName="house.simpleInfo", align=Align.CENTER, sort=20),
            @ExcelField(title="状态", attrName="status", dictType="sys_status", align=Align.CENTER, sort=30),
            @ExcelField(title="智能校验", attrName="autoCheck", align=Align.CENTER, sort=90),
            @ExcelField(title="智能核验时间", attrName="checkDate", align=Align.CENTER, sort=100, dataFormat="yyyy-MM-dd hh:mm"),
    })
    public HsQwApplyBureau() {
        this(null);
    }

    public HsQwApplyBureau(String id) {
        super(id);
    }

    @NotBlank(message = "房源编号不能为空")
    @Size(min = 0, max = 64, message = "房源编号长度不能超过 64 个字符")
    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public HsQwPublicRentalHouse getHouse() {
        return house;
    }

    public void setHouse(HsQwPublicRentalHouse house) {
        this.house = house;
    }

    public HsQwApplyer getMainApplyer() {
        return mainApplyer;
    }

    public void setMainApplyer(HsQwApplyer mainApplyer) {
        this.mainApplyer = mainApplyer;
    }

    public List<HsQwApplyer> getHsQwApplyerList() {
        return hsQwApplyerList;
    }

    public void setHsQwApplyerList(List<HsQwApplyer> hsQwApplyerList) {
        this.hsQwApplyerList = hsQwApplyerList;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getAutoCheck() {
        return autoCheck;
    }

    public void setAutoCheck(String autoCheck) {
        this.autoCheck = autoCheck;
    }
}