package com.hsobs.hs.modules.formmanage.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormFillRecordFnl;
import com.hsobs.hs.modules.formmanage.service.HsDataFormFillRecordFnlService;

/**
 * 数据表单填写详情记录表Controller
 * <AUTHOR>
 * @version 2025-05-05
 */
@Controller
@RequestMapping(value = "${adminPath}/formmanage/hsDataFormFillRecordFnl")
public class HsDataFormFillRecordFnlController extends BaseController {

	@Autowired
	private HsDataFormFillRecordFnlService hsDataFormFillRecordFnlService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsDataFormFillRecordFnl get(String id, boolean isNewRecord) {
		return hsDataFormFillRecordFnlService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecordFnl:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsDataFormFillRecordFnl hsDataFormFillRecordFnl, Model model) {
		model.addAttribute("hsDataFormFillRecordFnl", hsDataFormFillRecordFnl);
		return "modules/formmanage/hsDataFormFillRecordFnlList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecordFnl:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsDataFormFillRecordFnl> listData(HsDataFormFillRecordFnl hsDataFormFillRecordFnl, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormFillRecordFnl.setPage(new Page<>(request, response));
		Page<HsDataFormFillRecordFnl> page = hsDataFormFillRecordFnlService.findPage(hsDataFormFillRecordFnl);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecordFnl:view")
	@RequestMapping(value = "form")
	public String form(HsDataFormFillRecordFnl hsDataFormFillRecordFnl, Model model) {
		model.addAttribute("hsDataFormFillRecordFnl", hsDataFormFillRecordFnl);
		return "modules/formmanage/hsDataFormFillRecordFnlForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecordFnl:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsDataFormFillRecordFnl hsDataFormFillRecordFnl) {
		hsDataFormFillRecordFnlService.save(hsDataFormFillRecordFnl);
		return renderResult(Global.TRUE, text("保存数据表单填写详情记录表成功！"));
	}
	
}