package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.mybatis.mapper.query.QueryType;

import org.springframework.stereotype.Service;

/**
 * 局直公房，查询房源：选择所有局直公房3，进行检索操作
 */
@Service
public class HsQwHouseSelectListBreauList implements HsQwHouseSelectList {
    public String getDataType() {
        return "5";
    }

    public Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse,
            HsQwPublicRentalHouseService hsQwPublicRentalHouseService) {
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_BUREAU);
        hsQwPublicRentalHouse.sqlMap().getWhere().disableAutoAddStatusWhere();
        return hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
    }
}
