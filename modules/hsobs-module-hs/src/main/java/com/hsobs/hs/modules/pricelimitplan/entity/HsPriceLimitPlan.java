package com.hsobs.hs.modules.pricelimitplan.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 拟定限价房方案Entity
 * <AUTHOR>
 * @version 2025-02-13
 */
@Table(name="hs_price_limit_plan", alias="a", label="拟定限价房方案信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true, queryType=QueryType.LIKE),
		@Column(name="title", attrName="title", label="主题", queryType=QueryType.LIKE),
		@Column(name="office_code", attrName="officeCode", label="office_code"),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsPriceLimitPlan extends DataEntity<HsPriceLimitPlan> {

	@ExcelFields({
		@ExcelField(title = "编号", attrName = "id", align = ExcelField.Align.CENTER, sort = 20),
		@ExcelField(title = "主题", attrName = "title", align = ExcelField.Align.CENTER, sort = 40),
		@ExcelField(title = "方案", attrName = "remarks", align = ExcelField.Align.CENTER, sort = 50),
		@ExcelField(title = "发布时间", attrName = "createDate", align = ExcelField.Align.CENTER, sort = 60),
	})

	private static final long serialVersionUID = 1L;
	private String officeCode;		// office_code
	private String title;		// 主题

	private String type;	// 用于传参

	public HsPriceLimitPlan() {
		this(null);
	}
	
	public HsPriceLimitPlan(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="office_code长度不能超过 64 个字符")
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	@Size(min=0, max=128, message="主题长度不能超过 128 个字符")
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}