package com.hsobs.hs.modules.managementcheck.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.checkdetail.entity.HsQwManagementCheckDetail;
import com.hsobs.hs.modules.checkdetail.service.HsQwManagementCheckDetailService;
import com.hsobs.hs.modules.checkobject.entity.HsQwManagementCheckObject;
import com.hsobs.hs.modules.checkobject.service.HsQwManagementCheckObjectService;
import com.hsobs.hs.modules.checkrecord.entity.HsQwCheckRecord;
import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.hsobs.hs.modules.rentfee.service.HsQwRentalFeeService;
import com.hsobs.hs.modules.utils.HsBpmService;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.entity.Extend;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.managementcheck.entity.HsQwManagementCheck;
import com.hsobs.hs.modules.managementcheck.dao.HsQwManagementCheckDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

import javax.annotation.PostConstruct;

/**
 * 租赁资格轮候物业核验Service
 * <AUTHOR>
 * @version 2025-01-22
 */
@Service
@RestController
public class HsQwManagementCheckService extends CrudService<HsQwManagementCheckDao, HsQwManagementCheck>{

	@Autowired
	private HsQwManagementCheckDetailService hsQwManagementCheckDetailService;

	@Autowired
	private HsQwManagementCheckObjectService hsQwManagementCheckObjectService;

	@Autowired
	private HsQwRentalFeeService hsQwRentalFeeService;

	private HsBpmService<HsQwManagementCheck> hsBpmService;

	@Autowired
	private CommonBpmService commonBpmService;
    @Autowired
    private FileUploadService fileUploadService;

	@PostConstruct
	public void init() {
		// 在注入后对 hsBpmService 的属性进行设置
		hsBpmService = new HsBpmService<>(HsQwManagementCheck.class);
		hsBpmService.setCrudService(this);
	}

	/**
	 * 获取单条数据
	 * @param hsQwManagementCheck
	 * @return
	 */
	@Override
	public HsQwManagementCheck get(HsQwManagementCheck hsQwManagementCheck) {
		HsQwManagementCheck result = super.get(hsQwManagementCheck);
		if (result!=null){
			//查询过期缴费租金信息
			List<HsQwRentalFee> hsQwRentalFeeList = hsQwRentalFeeService.getPassedRentalFeeList(result.getHsQwApply().getCompact().getId());
			result.setHsQwRentalFeeList(hsQwRentalFeeList);
			//查询所有核查详单
			HsQwManagementCheckDetail query = new HsQwManagementCheckDetail();
			query.setCheckId(result.getId());
			List<HsQwManagementCheckDetail> detailList = hsQwManagementCheckDetailService.findList(query);
			for (HsQwManagementCheckDetail detail : detailList) {
				detail.setObjectName(detail.getCheckObject().getName());
				detail.setManagementCheck(null);
				detail.setCheckObject(null);
			}
			result.setHsQwObjectCheckList(detailList);
		}
		return result;
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwManagementCheck 查询条件
	 * @param hsQwManagementCheck page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwManagementCheck> findPage(HsQwManagementCheck hsQwManagementCheck) {
		hsQwManagementCheck.sqlMap().getWhere().and("a.check_type", QueryType.NE, HsQwManagementCheck.CHECK_TYPE_QUALIFICATION);
		return super.findPage(hsQwManagementCheck);
	}

	public Page<HsQwManagementCheck> findApplyPageByTask(HsQwManagementCheck hsQwManagementCheck, String[] status, String bpmStatus) {
		return commonBpmService.findTaskList(status, hsQwManagementCheck.getFormKey(),hsQwManagementCheck, bpmStatus);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwManagementCheck
	 * @return
	 */
	@Override
	public List<HsQwManagementCheck> findList(HsQwManagementCheck hsQwManagementCheck) {
		return super.findList(hsQwManagementCheck);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwManagementCheck
	 */
	@Override
	@Transactional
	public void save(HsQwManagementCheck hsQwManagementCheck) {
		//核验是否存在的申请单
		this.checkExist(hsQwManagementCheck);
		this.saveBase(hsQwManagementCheck);
		// 如果为审核状态，则进行审批流操作
		if (!HsQwApply.STATUS_DRAFT.equals(hsQwManagementCheck.getStatus())) {
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			variables.put("applyer", hsQwManagementCheck.getHsQwApply().getMainApplyer().getUserId());
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsQwManagementCheck.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsQwManagementCheck.getBpm().getTaskId())) {
				BpmUtils.start(hsQwManagementCheck, hsQwManagementCheck.getFormKey(), variables, null);
			}
			// 如果有任务信息，则：提交任务
			else {
				BpmUtils.complete(hsQwManagementCheck, variables, null);
			}
		}
	}

	private void checkExist(HsQwManagementCheck hsQwManagementCheck) {
		HsQwManagementCheck query = new HsQwManagementCheck();
		query.setApplyId(hsQwManagementCheck.getApplyId());
//		query.setCheckType(hsQwManagementCheck.getCheckType());
		query.setStatus(HsQwManagementCheck.STATUS_AUDIT);
		if (hsQwManagementCheck.getId()!=null){
			//排除自身
			query.sqlMap().getWhere().and("id", QueryType.NE, hsQwManagementCheck.getId());
		}
		if (this.findCount(query)> 0){
			throw new ServiceException("已存在核验单，请先完成后再操作");
		}
	}

	/**
	 * 基本保存信息
	 * @param hsQwManagementCheck
	 */
	public void saveBase(HsQwManagementCheck hsQwManagementCheck){
		super.save(hsQwManagementCheck);
		// 保存核验详情信息
		if (hsQwManagementCheck.getHsQwObjectCheckList()!=null){
			hsQwManagementCheck.getHsQwObjectCheckList().forEach(k -> {
				k.setCheckId(hsQwManagementCheck.getId());
				hsQwManagementCheckDetailService.save(k);
			});
		}
		this.saveFileUpload(hsQwManagementCheck, hsQwManagementCheck.getId(), "hsQwManagementCheck_file");
		this.saveFileUpload(hsQwManagementCheck, hsQwManagementCheck.getId(), "hsManagerCheckIn_reply_0");
		this.saveFileUpload(hsQwManagementCheck, hsQwManagementCheck.getId(), "hsManagerCheckIn_reply_1");
		this.saveFileUpload(hsQwManagementCheck, hsQwManagementCheck.getId(), "hsManagerCheckIn_reply_2");
		this.saveFileUpload(hsQwManagementCheck, hsQwManagementCheck.getId(), "hsManagerCheckOut_reply_3");
		this.saveFileUpload(hsQwManagementCheck, hsQwManagementCheck.getId(), "hsManagerCheckOut_reply_4");
		this.saveFileUpload(hsQwManagementCheck, hsQwManagementCheck.getId(), "hsManagerCheckOut_reply_5");
		this.saveFileUpload(hsQwManagementCheck, hsQwManagementCheck.getId(), "hsQwManagementCheck_back_file");
	}

	private void saveFileUpload(HsQwManagementCheck hsQwManagementCheck, String bizKey, String bizType) {
		if (hsQwManagementCheck.getDataMap()==null){
			return;
		}
		FileUpload fileUpload = new FileUpload();
		fileUpload.setId(Optional.ofNullable(hsQwManagementCheck.getDataMap().get(bizType)).orElse("").toString());
		fileUpload.setExtend(new Extend());
		fileUpload.getExtend().setExtendS1(hsQwManagementCheck.getTmpTaskId());//获取临时寄存的过程id
		// 修改文件扩展id
		fileUploadService.update(fileUpload);
		// 保存文件信息
		FileUploadUtils.saveFileUpload(hsQwManagementCheck, bizKey, bizType);
	}


	/**
	 * 更新状态
	 * @param hsQwManagementCheck
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwManagementCheck hsQwManagementCheck) {
		super.updateStatus(hsQwManagementCheck);
	}
	
	/**
	 * 删除数据
	 * @param hsQwManagementCheck
	 */
	@Override
	@Transactional
	public void delete(HsQwManagementCheck hsQwManagementCheck) {
		super.delete(hsQwManagementCheck);
	}

	/**
	 * 根据资格核验信息生成并返回相应的核验单信息
	 * @param hsQwCheckRecord
	 * @return
	 */
	@Transactional
	public HsQwManagementCheck getHouseCheckDetail(HsQwCheckRecord hsQwCheckRecord) {
		HsQwManagementCheck hsQwManagementCheck = new HsQwManagementCheck();
		//若存在则直接返回
		hsQwManagementCheck = this.getByRecord(hsQwCheckRecord.getId());
		if (hsQwManagementCheck != null) {
			return this.get(hsQwManagementCheck.getId());
		}
		//生成核验单并返回
		return this.generateByRecord(hsQwCheckRecord);
	}

	/**
	 * 根据资格审查信息生成核验单
	 * @param hsQwCheckRecord
	 * @return
	 */
	private HsQwManagementCheck generateByRecord(HsQwCheckRecord hsQwCheckRecord) {
		//保存核验单
		HsQwManagementCheck hsQwManagementCheck = new HsQwManagementCheck();
		hsQwManagementCheck.setRecordId(hsQwCheckRecord.getId());
		hsQwManagementCheck.setApplyId(hsQwCheckRecord.getApplyId());
		hsQwManagementCheck.setCheckType("2");//资格核验
		hsQwManagementCheck.setCheckUser(UserUtils.getUser().getUserCode());//当前用户为核验人员
		hsQwManagementCheck.setHsQwApply(hsQwCheckRecord.getHsQwApply());
		this.saveBase(hsQwManagementCheck);
		//保存核验详单
		this.saveCheckObjects(hsQwManagementCheck, hsQwCheckRecord.getCheckObjects());
		//查询租金欠费信息
		hsQwManagementCheck.setHsQwRentalFeeList(hsQwRentalFeeService.getPassedRentalFeeList(hsQwCheckRecord.getCompact().getId()));
		return hsQwManagementCheck;
	}

	/**
	 * 批量生成并保存入户核验物品详情信息
	 *
	 * @param hsQwManagementCheck
	 * @param checkObjects
	 */
	private void saveCheckObjects(HsQwManagementCheck hsQwManagementCheck, String checkObjects) {
		HsQwManagementCheckObject query = new HsQwManagementCheckObject();
		query.setStatus(HsQwManagementCheckObject.STATUS_NORMAL);
		query.setId_in(checkObjects.split(","));
		List<HsQwManagementCheckObject> checkObjectList = hsQwManagementCheckObjectService.findList(query);
		List<HsQwManagementCheckDetail> detailList = new ArrayList<HsQwManagementCheckDetail>();
		checkObjectList.forEach(checkObject -> {
			HsQwManagementCheckDetail checkDetail = new HsQwManagementCheckDetail();
			checkDetail.setCheckObject(checkObject);
			checkDetail.setStatus(HsQwManagementCheckObject.STATUS_NORMAL);
			checkDetail.setCheckId(hsQwManagementCheck.getId());
			checkDetail.setObjectId(checkObject.getId());
			checkDetail.setDamage("0");
			checkDetail.setMaintenance("0");
			checkDetail.setObjectName(checkObject.getName());
			detailList.add(checkDetail);
			hsQwManagementCheckDetailService.insert(checkDetail);
		});
		hsQwManagementCheck.setHsQwObjectCheckList(detailList);

//		hsQwManagementCheckDetailDao.insertBatch(detailList);
	}

	/**
	 * 根据资格编号查询核验单
	 * @param recordId
	 * @return
	 */
	private HsQwManagementCheck getByRecord(String recordId) {
		HsQwManagementCheck query = new HsQwManagementCheck();
		query.setRecordId(recordId);
		return this.findList(query).stream().findFirst().orElse(null);
	}

	public HsQwManagementCheck getCheckInfo(HsQwManagementCheck hsQwManagementCheck) {
		if (hsQwManagementCheck.getCheckType().equals(HsQwManagementCheck.CHECK_TYPE_IN)){
			hsQwManagementCheck.setFormKey("management_check_in");
			hsQwManagementCheck.setDataType("4");
		} else if (hsQwManagementCheck.getCheckType().equals(HsQwManagementCheck.CHECK_TYPE_OUT)){
			hsQwManagementCheck.setFormKey("management_check_out");
			hsQwManagementCheck.setDataType("5");
		}

		if (hsQwManagementCheck.getId() != null){
			return hsQwManagementCheck;
		}
		HsQwManagementCheckObject query = new HsQwManagementCheckObject();
		query.setStatus(HsQwManagementCheckObject.STATUS_NORMAL);
		List<HsQwManagementCheckObject> checkObjectList = hsQwManagementCheckObjectService.findList(query);
		List<HsQwManagementCheckDetail> detailList = new ArrayList<HsQwManagementCheckDetail>();
		checkObjectList.forEach(checkObject -> {
			HsQwManagementCheckDetail checkDetail = new HsQwManagementCheckDetail();
			checkDetail.setCheckObject(checkObject);
			checkDetail.setStatus(HsQwManagementCheckObject.STATUS_NORMAL);
			checkDetail.setObjectId(checkObject.getId());
			checkDetail.setDamage("0");
			checkDetail.setMaintenance("0");
			checkDetail.setObjectName(checkObject.getName());
			detailList.add(checkDetail);
		});
		hsQwManagementCheck.setHsQwObjectCheckList(detailList);
		return hsQwManagementCheck;
	}
}