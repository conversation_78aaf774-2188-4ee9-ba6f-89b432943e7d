package com.hsobs.hs.modules.formmanage.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplateField;
import com.hsobs.hs.modules.formmanage.dao.HsDataFormTemplateFieldDao;

/**
 * 数据表单模板字段Service
 * <AUTHOR>
 * @version 2025-02-06
 */
@Service
public class HsDataFormTemplateFieldService extends CrudService<HsDataFormTemplateFieldDao, HsDataFormTemplateField> {
	
	/**
	 * 获取单条数据
	 * @param hsDataFormTemplateField
	 * @return
	 */
	@Override
	public HsDataFormTemplateField get(HsDataFormTemplateField hsDataFormTemplateField) {
		return super.get(hsDataFormTemplateField);
	}
	
	/**
	 * 查询分页数据
	 * @param hsDataFormTemplateField 查询条件
	 * @param hsDataFormTemplateField page 分页对象
	 * @return
	 */
	@Override
	public Page<HsDataFormTemplateField> findPage(HsDataFormTemplateField hsDataFormTemplateField) {
		return super.findPage(hsDataFormTemplateField);
	}
	
	/**
	 * 查询列表数据
	 * @param hsDataFormTemplateField
	 * @return
	 */
	@Override
	public List<HsDataFormTemplateField> findList(HsDataFormTemplateField hsDataFormTemplateField) {
		return super.findList(hsDataFormTemplateField);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsDataFormTemplateField
	 */
	@Override
	@Transactional
	public void save(HsDataFormTemplateField hsDataFormTemplateField) {
		super.save(hsDataFormTemplateField);
	}
	
	/**
	 * 更新状态
	 * @param hsDataFormTemplateField
	 */
	@Override
	@Transactional
	public void updateStatus(HsDataFormTemplateField hsDataFormTemplateField) {
		super.updateStatus(hsDataFormTemplateField);
	}
	
	/**
	 * 删除数据
	 * @param hsDataFormTemplateField
	 */
	@Override
	@Transactional
	public void delete(HsDataFormTemplateField hsDataFormTemplateField) {
		hsDataFormTemplateField.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsDataFormTemplateField);
	}
	
}