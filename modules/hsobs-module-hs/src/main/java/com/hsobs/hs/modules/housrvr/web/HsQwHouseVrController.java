package com.hsobs.hs.modules.housrvr.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.jeesite.common.lang.DateUtils;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.housrvr.entity.HsQwHouseVr;
import com.hsobs.hs.modules.housrvr.service.HsQwHouseVrService;

/**
 * 房源VR信息表Controller
 * <AUTHOR>
 * @version 2025-03-20
 */
@Controller
@RequestMapping(value = "${adminPath}/housrvr/hsQwHouseVr")
public class HsQwHouseVrController extends BaseController {

	@Autowired
	private HsQwHouseVrService hsQwHouseVrService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwHouseVr get(String id, boolean isNewRecord) {
		return hsQwHouseVrService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("housrvr:hsQwHouseVr:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwHouseVr hsQwHouseVr, Model model) {
		model.addAttribute("hsQwHouseVr", hsQwHouseVr);
		return "modules/housrvr/hsQwHouseVrList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("housrvr:hsQwHouseVr:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwHouseVr> listData(HsQwHouseVr hsQwHouseVr, HttpServletRequest request, HttpServletResponse response) {
		hsQwHouseVr.setPage(new Page<>(request, response));
		Page<HsQwHouseVr> page = hsQwHouseVrService.findPage(hsQwHouseVr);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("housrvr:hsQwHouseVr:view")
	@RequestMapping(value = "form")
	public String form(HsQwHouseVr hsQwHouseVr, Model model) {
		model.addAttribute("hsQwHouseVr", hsQwHouseVr);
		return "modules/housrvr/hsQwHouseVrForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("housrvr:hsQwHouseVr:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwHouseVr hsQwHouseVr) {
		hsQwHouseVrService.save(hsQwHouseVr);
		return renderResult(Global.TRUE, text("保存房源VR信息表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("housrvr:hsQwHouseVr:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwHouseVr hsQwHouseVr) {
		hsQwHouseVrService.delete(hsQwHouseVr);
		return renderResult(Global.TRUE, text("删除房源VR信息表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequestMapping(value = "hsQwHouseVrSelect")
	public String hsQwHouseVrSelect(HsQwHouseVr hsQwHouseVr, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwHouseVr", hsQwHouseVr);
		return "modules/housrvr/hsQwHouseVrSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("housrvr:hsQwHouseVr:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwHouseVr hsQwHouseVr, HttpServletResponse response) {
		List<HsQwHouseVr> list = hsQwHouseVrService.findList(hsQwHouseVr);
		String fileName = "房源VR信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("房源VR信息表", HsQwHouseVr.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}