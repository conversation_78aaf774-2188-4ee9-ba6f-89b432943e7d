package com.hsobs.hs.modules.elevator.service;

import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.elevator.dao.HsElevatorApplyDao;
import com.hsobs.hs.modules.elevator.entity.HsElevatorApply;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.NumberUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.mybatis.mapper.query.QueryWhere;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 加装电梯补助申请表Service
 * <AUTHOR>
 * @version 2024-12-24
 */
@Service
public class HsElevatorApplyService extends CrudService<HsElevatorApplyDao, HsElevatorApply> {

	@Autowired
	private BpmTaskService bpmTaskService;
	@Autowired
	private CommonBpmService commonBpmService;

	/**
	 * 获取单条数据
	 * @param hsElevatorApply
	 * @return
	 */
	@Override
	public HsElevatorApply get(HsElevatorApply hsElevatorApply) {
		return super.get(hsElevatorApply);
	}


	@Override
	public void addDataScopeFilter(HsElevatorApply entity) {
		SqlMap sqlMap = entity.sqlMap(); // v5.3.0+ 及之后版本
		// 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
		sqlMap.getDataScope().addFilter("extWhere", "Office",
				"a.unit_id", "a.create_by", DataScope.CTRL_PERMI_HAVE, "hsElevatorApply");
	}

	/**
	 * 查询分页数据
	 * @param hsElevatorApply 查询条件
	 * @param hsElevatorApply page 分页对象
	 * @return
	 */
	@Override
	public Page<HsElevatorApply> findPage(HsElevatorApply hsElevatorApply) {
		hsElevatorApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsElevatorApply> hsElevatorApplyPage = super.findPage(hsElevatorApply);
		return hsElevatorApplyPage;
	}


	private String[] getAuditStatusArray() {
		return new String[]{
			HsElevatorApply.APPLY_STATUS_DEFAULT,
			HsElevatorApply.APPLY_STATUS_DRAFT,
			HsElevatorApply.APPLY_STATUS_AUDIT_ORGHAND_FIRST,
			HsElevatorApply.APPLY_STATUS_AUDIT_SITE_INVESTIG,

			HsElevatorApply.APPLY_STATUS_AUDIT_ORGHAND_SECOND,
			HsElevatorApply.APPLY_STATUS_AUDIT_ORGOFFICE_FIRST,
			HsElevatorApply.APPLY_STATUS_AUDIT_ORGHAND_THIRD,
			HsElevatorApply.APPLY_STATUS_AUDIT_ORGBUREAU_FIRST,
			HsElevatorApply.APPLY_STATUS_AUDIT_ORGFINANCE_FIRST,
		};
	}

	public List<HsElevatorApply> findAuditByTask(HsElevatorApply hsElevatorApply) {
		String[] status = getAuditStatusArray();
		return commonBpmService.findTaskListNoPage(status, "elevator_subsidy_apply",hsElevatorApply, "1");
	}

	public Page<HsElevatorApply> findAuditPageByTask(HsElevatorApply hsElevatorApply) {
		//审批待办中的状态过滤
		String[] status = getAuditStatusArray();
//		HsBpmTask params = new HsBpmTask();
//		params.setStatus("1");
//		Page<BpmTask> myHsTaskPage = this.getHsTask(params, status, hsElevatorApply.getFlowStatus());
//		//获取所有待办任务的申请单id
//		if (!this.getIdsByTask(myHsTaskPage, hsElevatorApply)) {
//			Page<HsElevatorApply> hsElevatorApplyPage = this.findPage(hsElevatorApply);
//			return this.getTaskResult(hsElevatorApplyPage, myHsTaskPage);
//		} else {
//			return hsElevatorApply.getPage();
//		}
		return this.findApplyPageByTask(hsElevatorApply, status, "1", "elevator_subsidy_apply");
	}

	public Page<HsElevatorApply> findApplyPageByTask(HsElevatorApply hsElevatorApply, String[] status, String bpmStatus, String formKey) {
		return commonBpmService.findTaskList(status, formKey,hsElevatorApply, bpmStatus);
	}




	public Page<HsElevatorApply> findVerifyPageByTask(HsElevatorApply hsElevatorApply) {
		//审批待办中的状态过滤
		String[] status = new String[]{
				HsElevatorApply.APPLY_STATUS_AUDIT_ORGFINANCE_VER
		};
		HsBpmTask params = new HsBpmTask();
		params.setStatus("1");
		Page<BpmTask> myHsTaskPage = this.getHsTask(params, status, hsElevatorApply.getFlowStatus());
		//获取所有待办任务的申请单id
		List<String> hsIds = new ArrayList<>();
		myHsTaskPage.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		if (hsIds.isEmpty()) {
			hsElevatorApply.sqlMap().getWhere().and("apply_status", QueryType.EQ, "9");
		} else {
			hsElevatorApply.sqlMap().getWhere().andBracket("apply_status", QueryType.EQ, "9")
					.or("id", QueryType.IN, hsIds.toArray())
					.endBracket();
		}
		Page<HsElevatorApply> hsElevatorApplyPage = this.findPage(hsElevatorApply);
		return this.getTaskResult(hsElevatorApplyPage, myHsTaskPage);
	}

	private String getStatusFromTask(String id, Page<BpmTask> myHsTaskPage) {
		for (BpmTask bpmTask : myHsTaskPage.getList()) {
			if (bpmTask.getProcIns().getBizKey().equals(id)) {
				return bpmTask.getName();
			}
		}
		return null;
	}

	private Page<HsElevatorApply> getTaskResult(Page<HsElevatorApply> hsElevatorApplyPage, Page<BpmTask> myHsTaskPage) {
		hsElevatorApplyPage.getList().forEach(k -> k.setFlowStatus(this.getStatusFromTask(k.getId(), myHsTaskPage)));
		return hsElevatorApplyPage;
	}

	private boolean getIdsByTask(Page<BpmTask> pageTask, HsElevatorApply hsElevatorApply) {
		List<String> hsIds = new ArrayList<>();
		pageTask.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		hsElevatorApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
		return hsIds.isEmpty();
	}

	private Page<BpmTask> getHsTask(HsBpmTask params, String[] resNames, String applyStatus) {
		//查询我的代办rent_apply任务
		params.setUserCode(params.currentUser().getUserCode());
		// 维修资金申请流程key
		params.getProcIns().setFormKey("elevator_subsidy_apply");
		if (resNames != null && resNames.length > 0) {
			params.setNames(Arrays.stream(resNames).collect(Collectors.toList()));
		}
		if (StringUtils.isNotBlank(applyStatus)) {
			params.setName(applyStatus);
		}
		return this.bpmTaskService.findTaskPage(params);
	}


	/**
	 * 查询列表数据
	 * @param hsElevatorApply
	 * @return
	 */
	@Override
	public List<HsElevatorApply> findList(HsElevatorApply hsElevatorApply) {
		return super.findList(hsElevatorApply);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsElevatorApply
	 */
	@Override
	@Transactional
	public void save(HsElevatorApply hsElevatorApply) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsElevatorApply.getStatus())){
			hsElevatorApply.setStatus(HsElevatorApply.STATUS_AUDIT);
		}
		
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsElevatorApply.STATUS_NORMAL.equals(hsElevatorApply.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 记录流程节点值
		int activityIdInt = -1;
		if (hsElevatorApply.getBpm() != null && StringUtils.isNotEmpty(hsElevatorApply.getBpm().getActivityId())) {
			String activityId = hsElevatorApply.getBpm().getActivityId();
			if ("undefined".equals(activityId)) {
				if (HsElevatorApply.STATUS_AUDIT.equals(hsElevatorApply.getStatus())) {
					activityIdInt = 1;
				}
			} else {
				// maintenanceApply0010
				activityId = activityId.replace("elevatorSubsidyApply", "");
				while (activityId.startsWith("0")) {
					activityId = StringUtils.removeStart(activityId, "0");
				}
				if (StringUtils.isNumeric(activityId)) {
					activityIdInt = Integer.parseInt(activityId);
				}
			}
		}
		hsElevatorApply.setApplyStatus(activityIdInt);
		if (activityIdInt == -1 || activityIdInt == 0 || activityIdInt == 1) {
			double existFund = 0d;
			if (hsElevatorApply.getExistFundHouse() != null) {
				existFund += hsElevatorApply.getExistFundHouse();
			}
			if (hsElevatorApply.getExistFundOther() != null) {
				existFund += hsElevatorApply.getExistFundOther();
			}
			if (hsElevatorApply.getExistFundOwner() != null) {
				existFund += hsElevatorApply.getExistFundOwner();
			}
			if (hsElevatorApply.getExistFundUnit() != null) {
				existFund += hsElevatorApply.getExistFundUnit();
			}
			hsElevatorApply.setExistFund(existFund);
		} else if (activityIdInt == 2) {
			// 经办申请受理更新拨付金额
			//TODO 防篡改
			hsElevatorApply.setDisbursementFund(hsElevatorApply.getApplyFund());
		}

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsElevatorApply.STATUS_DRAFT.equals(hsElevatorApply.getStatus())
				|| HsElevatorApply.STATUS_AUDIT.equals(hsElevatorApply.getStatus())){
			super.save(hsElevatorApply);
		}

		// 如果为审核状态，则进行审批流操作
		if (HsElevatorApply.STATUS_AUDIT.equals(hsElevatorApply.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", hsElevatorApply.getLeaveDays());
			
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsElevatorApply.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsElevatorApply.getBpm().getTaskId())){
				BpmUtils.start(hsElevatorApply, "elevator_subsidy_apply", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsElevatorApply, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsElevatorApply, hsElevatorApply.getId(), "hsElevatorApply_file");
		FileUploadUtils.saveFileUpload(hsElevatorApply, hsElevatorApply.getId(), "hsElevatorApply_investigationFile");
	}
	
	/**
	 * 更新状态
	 * @param hsElevatorApply
	 */
	@Override
	@Transactional
	public void updateStatus(HsElevatorApply hsElevatorApply) {
		super.updateStatus(hsElevatorApply);
	}
	
	/**
	 * 删除数据
	 * @param hsElevatorApply
	 */
	@Override
	@Transactional
	public void delete(HsElevatorApply hsElevatorApply) {
		hsElevatorApply.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsElevatorApply);
	}

	public void flushTaskStatus(HsElevatorApply hsElevatorApply) {
		if (hsElevatorApply.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		hsElevatorApply = this.get(hsElevatorApply.getId());
		if (hsElevatorApply == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		HsBpmTask params = new HsBpmTask();
		params.setStatus("1");
		// 维修资金申请流程key
		params.getProcIns().setFormKey("elevator_subsidy_apply");
		params.getProcIns().setBizKey(hsElevatorApply.getId());

		Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);

		BpmTask bpmTask = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
		if (bpmTask != null) {
			String activityId = bpmTask.getActivityId().replace("elevatorSubsidyApply", "");
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				int activityIdInt = Integer.parseInt(activityId);

				if (activityIdInt == 1) {
					activityIdInt = 0;
				} else {
					activityIdInt = getPrevApplyStatus(activityIdInt);
				}

				hsElevatorApply.setApplyStatus(activityIdInt);
				super.save(hsElevatorApply);
			}
		}
	}

	public Integer getPrevApplyStatus(Integer activityIdInt) {
		return PREV_MAP.get(activityIdInt);
	}

	private static Map<Integer, Integer> PREV_MAP = new HashMap<>();
	static {
		// 电梯  1 2 5 6 7 8 9
		PREV_MAP.put(1, 0);
		PREV_MAP.put(2, 1);
		PREV_MAP.put(5, 2);
		PREV_MAP.put(6, 5);
		PREV_MAP.put(7, 6);
		PREV_MAP.put(8, 7);
		PREV_MAP.put(9, 8);
	}


}