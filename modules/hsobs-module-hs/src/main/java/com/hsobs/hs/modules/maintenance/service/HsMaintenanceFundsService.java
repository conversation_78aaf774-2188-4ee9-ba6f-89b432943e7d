package com.hsobs.hs.modules.maintenance.service;

import com.hsobs.hs.modules.maintenance.dao.HsMaintenanceFundsDao;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFunds;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFundsHis;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 维修资金信息Service
 * <AUTHOR>
 * @version 2024-11-28
 */
@Service
public class HsMaintenanceFundsService extends CrudService<HsMaintenanceFundsDao, HsMaintenanceFunds> {

	@Autowired
	private HsMaintenanceFundsHisService hsMaintenanceFundsHisService;

	/**
	 * 获取单条数据
	 * @param hsMaintenanceFunds
	 * @return
	 */
	@Override
	public HsMaintenanceFunds get(HsMaintenanceFunds hsMaintenanceFunds) {
		return super.get(hsMaintenanceFunds);
	}
	
	/**
	 * 查询分页数据
	 * @param hsMaintenanceFunds 查询条件
	 * @param hsMaintenanceFunds page 分页对象
	 * @return
	 */
	@Override
	public Page<HsMaintenanceFunds> findPage(HsMaintenanceFunds hsMaintenanceFunds) {
		return super.findPage(hsMaintenanceFunds);
	}
	
	/**
	 * 查询列表数据
	 * @param hsMaintenanceFunds
	 * @return
	 */
	@Override
	public List<HsMaintenanceFunds> findList(HsMaintenanceFunds hsMaintenanceFunds) {
		return super.findList(hsMaintenanceFunds);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsMaintenanceFunds
	 */
	@Override
	@Transactional
	public void save(HsMaintenanceFunds hsMaintenanceFunds) {

		if (hsMaintenanceFunds.getChangeFund() == null || hsMaintenanceFunds.getChangeFund() <= 0) {
			throw new ServiceException(text("维修资金变动金额不能为负值！"));
		}

		Double inputFund = hsMaintenanceFunds.getInputFunds();

		// 单位 + 楼盘 + 账户名 构成唯一值
		HsMaintenanceFunds query = new HsMaintenanceFunds();
		query.setHouseId(hsMaintenanceFunds.getHouseId());
		query.setFundName(hsMaintenanceFunds.getFundName());
		List<HsMaintenanceFunds> fundDbList = findList(query);
		if (fundDbList != null && !fundDbList.isEmpty()) {
			inputFund = fundDbList.get(0).getInputFunds();

			double changeFund = hsMaintenanceFunds.getChangeFund();
			hsMaintenanceFunds = fundDbList.get(0);
			hsMaintenanceFunds.setChangeFund(changeFund);

			this.dao.updateInputFunds(fundDbList.get(0).getId(), hsMaintenanceFunds.getChangeFund());

		} else {
			inputFund = 0.0d;
			if (hsMaintenanceFunds.getId() == null) {
				hsMaintenanceFunds.setUsedFunds(0.0D);
				hsMaintenanceFunds.setApplyFunds(0.0D);
				hsMaintenanceFunds.setInputFunds(hsMaintenanceFunds.getChangeFund());
				hsMaintenanceFunds.setInputYear(DateUtils.getYear());
				hsMaintenanceFunds.setInputMonth(DateUtils.getMonth());
				hsMaintenanceFunds.setValidTag("1");
			} else {
				this.dao.updateInputFunds(hsMaintenanceFunds.getId(), hsMaintenanceFunds.getChangeFund());
			}
		}
		super.save(hsMaintenanceFunds);

		// 变更记录
		HsMaintenanceFundsHis his = new HsMaintenanceFundsHis();
		his.setFundsId(hsMaintenanceFunds.getId());
		his.setInputFunds(inputFund);
		his.setChangeFunds(hsMaintenanceFunds.getChangeFund());
		his.setRemark("");
		his.setInputYear(DateUtils.getYear());
		his.setInputMonth(DateUtils.getMonth());
		his.setCreateByName(hsMaintenanceFunds.currentUser().getUserName());
		his.setValidTag("1");
		his.setStatus(HsMaintenanceFundsHis.STATUS_NORMAL);
		hsMaintenanceFundsHisService.save(his);
	}
	
	/**
	 * 更新状态
	 * @param hsMaintenanceFunds
	 */
	@Override
	@Transactional
	public void updateStatus(HsMaintenanceFunds hsMaintenanceFunds) {
		if (HsMaintenanceFunds.STATUS_DISABLE.equals(hsMaintenanceFunds.getStatus())) {
			// 停用限制
			if (hsMaintenanceFunds.getApplyFunds() != null && hsMaintenanceFunds.getApplyFunds() > 0) {
				throw new ServiceException(text("维修资金不能停用，存在申请中资金！"));
			}
		}
		super.updateStatus(hsMaintenanceFunds);
	}
	
	/**
	 * 删除数据
	 * @param hsMaintenanceFunds
	 */
	@Override
	@Transactional
	public void delete(HsMaintenanceFunds hsMaintenanceFunds) {
		HsMaintenanceFunds fundDb = this.get(hsMaintenanceFunds.getId());
		if (fundDb == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		if ((fundDb.getUsedFunds() != null && fundDb.getUsedFunds() > 0)
				|| (fundDb.getApplyFunds() != null && fundDb.getApplyFunds() > 0)) {
			throw new ServiceException(text("该资金账户使用中，无法删除！"));
		}
		hsMaintenanceFunds.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsMaintenanceFunds);
	}

	public void decrease(String id, Double fund) {
		this.dao.decreaseInputFund(id, fund);
	}

	public void applyFund(String id, Double fund) {
		this.dao.applyFund(id, fund);
	}
}