package com.hsobs.hs.modules.apply.web;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.clearance.service.HsQwClearanceService;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 绿色通道管理Controller
 *
 * <AUTHOR>
 * @version 2025-1-6
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/hsQwApplyGreen")
public class HsQwApplyGreenController extends BaseController {

    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private BpmTaskService bpmTaskService;

    @Autowired
    private HsQwClearanceService hsQwClearanceService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public HsQwApply get(String id, boolean isNewRecord) {
        return hsQwApplyService.get(id, isNewRecord);
    }


    /**
     * 查询列表-绿色通道
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = {"list", ""})
    public String listGreen(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/greenChannel/hsQwApplyGreenList";
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "form")
    public String form(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        return "modules/apply/greenChannel/hsQwApplyGreenForm";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<HsQwApply> listData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        hsQwApply.sqlMap().getWhere().and("status", QueryType.NE, "1");
        Page<HsQwApply> page = hsQwApplyService.findPage(hsQwApply);
        return page;
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated HsQwApply hsQwApply) {
        hsQwApplyService.save(hsQwApply);
        return renderResult(Global.TRUE, text("保存租赁资格轮候申请成功！"));
    }

    /**
     * 停用数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "disable")
    @ResponseBody
    public String disable(HsQwApply hsQwApply) {
        hsQwApply.setStatus(HsQwApply.STATUS_DISABLE);
        hsQwApplyService.updateStatus(hsQwApply);
        if (StringUtils.isNotBlank(hsQwApply.getHouseId())) {
            hsQwApplyService.updateHouseStatus(hsQwApply.getHouseId(), "0");//恢复待配租
        }
        return renderResult(Global.TRUE, text("停用租赁资格轮候申请成功"));
    }

    /**
     * 启用数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "enable")
    @ResponseBody
    public String enable(HsQwApply hsQwApply) {
        hsQwApply.setStatus(HsQwApply.STATUS_NORMAL);
        hsQwApplyService.updateStatus(hsQwApply);
        if (StringUtils.isNotBlank(hsQwApply.getHouseId())) {
            hsQwApplyService.enableGreenApply(hsQwApply);
            hsQwApplyService.updateHouseStatus(hsQwApply.getHouseId(), "1");//恢复已配租
        }
        return renderResult(Global.TRUE, text("启用租赁资格轮候申请成功"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(HsQwApply hsQwApply) {
        if (!"0".equals(hsQwApply.getStatus())) {
            return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
        }
        hsQwApplyService.delete(hsQwApply);
        if (StringUtils.isNotBlank(hsQwApply.getHouseId())) {
            hsQwApplyService.updateHouseStatus(hsQwApply.getHouseId(), "0");//恢复待配租
        }
        return renderResult(Global.TRUE, text("删除租赁资格轮候申请成功！"));
    }

}