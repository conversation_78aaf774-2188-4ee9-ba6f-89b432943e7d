package com.hsobs.hs.modules.contract.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.contract.entity.HsContractTemplateField;
import com.hsobs.hs.modules.contract.dao.HsContractTemplateFieldDao;

/**
 * 合同模板字段Service
 * <AUTHOR>
 * @version 2025-01-21
 */
@Service
public class HsContractTemplateFieldService extends CrudService<HsContractTemplateFieldDao, HsContractTemplateField> {
	
	/**
	 * 获取单条数据
	 * @param hsContractTemplateField
	 * @return
	 */
	@Override
	public HsContractTemplateField get(HsContractTemplateField hsContractTemplateField) {
		return super.get(hsContractTemplateField);
	}
	
	/**
	 * 查询分页数据
	 * @param hsContractTemplateField 查询条件
	 * @param hsContractTemplateField page 分页对象
	 * @return
	 */
	@Override
	public Page<HsContractTemplateField> findPage(HsContractTemplateField hsContractTemplateField) {
		return super.findPage(hsContractTemplateField);
	}
	
	/**
	 * 查询列表数据
	 * @param hsContractTemplateField
	 * @return
	 */
	@Override
	public List<HsContractTemplateField> findList(HsContractTemplateField hsContractTemplateField) {
		return super.findList(hsContractTemplateField);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsContractTemplateField
	 */
	@Override
	@Transactional
	public void save(HsContractTemplateField hsContractTemplateField) {
		super.save(hsContractTemplateField);
	}
	
	/**
	 * 更新状态
	 * @param hsContractTemplateField
	 */
	@Override
	@Transactional
	public void updateStatus(HsContractTemplateField hsContractTemplateField) {
		super.updateStatus(hsContractTemplateField);
	}
	
	/**
	 * 删除数据
	 * @param hsContractTemplateField
	 */
	@Override
	@Transactional
	public void delete(HsContractTemplateField hsContractTemplateField) {
		hsContractTemplateField.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsContractTemplateField);
	}
	
}