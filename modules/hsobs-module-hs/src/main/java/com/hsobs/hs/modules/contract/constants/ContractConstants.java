package com.hsobs.hs.modules.contract.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ContractConstants {

    public static Map<String, String> BASE_FIELE_MAP = new HashMap<>();


    static {
        BASE_FIELE_MAP.put("contractName", "合同名称");
        BASE_FIELE_MAP.put("contractNo", "合同编号");
        BASE_FIELE_MAP.put("applyName", "申请人名称");
        BASE_FIELE_MAP.put("applySfz", "申请人身份证");
        BASE_FIELE_MAP.put("applyTel", "联系电话");
        BASE_FIELE_MAP.put("workUnit", "工作单位");
        BASE_FIELE_MAP.put("houseAddr", "房屋地址");
        BASE_FIELE_MAP.put("unitNo", "单元号");
        BASE_FIELE_MAP.put("houseArea", "面积");
        BASE_FIELE_MAP.put("houseType", "户型");
        BASE_FIELE_MAP.put("houseRent", "租金");
        BASE_FIELE_MAP.put("startTime", "合同签订开始时间");
        BASE_FIELE_MAP.put("endTime", "合同签订结束时间");
        BASE_FIELE_MAP.put("contractDesc", "合同描述");
    }

}
