package com.hsobs.hs.modules.externalapi.service;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * 外部API服务接口
 * 
 * <AUTHOR>
 * @version 2024-05-20
 */
public interface ExternalApiService {

    /**
     * 调用外部API
     * 
     * @param apiName API名称
     * @param params 请求参数
     * @return 响应结果
     */
    JSONObject callApi(String apiName, Map<String, Object> params);
    
    /**
     * 调用外部API
     * 
     * @param apiName API名称
     * @param params 请求参数
     * @param clazz 返回类型
     * @return 响应结果
     */
    <T> T callApi(String apiName, Map<String, Object> params, Class<T> clazz);
    
    /**
     * 获取部门名称
     * 
     * @return 部门名称
     */
    String getDepartmentName();
    
    /**
     * 是否启用
     * 
     * @return 是否启用
     */
    boolean isEnabled();
}
