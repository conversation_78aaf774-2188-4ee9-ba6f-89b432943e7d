package com.hsobs.hs.modules.maintenance.web;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFunds;
import com.hsobs.hs.modules.maintenance.service.HsMaintenanceApplyService;
import com.hsobs.hs.modules.maintenance.service.HsMaintenanceFundsService;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 维修申请Controller
 * <AUTHOR>
 * @version 2024-12-08
 */
@Controller
@RequestMapping(value = "${adminPath}/maintenance/hsMaintenanceApply")
public class HsMaintenanceApplyController extends BaseController {

	@Autowired
	private HsMaintenanceApplyService hsMaintenanceApplyService;
	@Autowired
	private HsMaintenanceFundsService hsMaintenanceFundsService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsMaintenanceApply get(String id, boolean isNewRecord) {
		return hsMaintenanceApplyService.get(id, isNewRecord);
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = {"fullList", ""})
	public String fullList(HsMaintenanceApply hsMaintenanceApply, Model model) {
		model.addAttribute("hsMaintenanceApply", hsMaintenanceApply);
		// 新增用户组织管理功能的控制权限设置 user.adminCtrlPermi，1拥有的权限 2管理的权限，无限级授权场景使用
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		//TODO 新增按钮权限控制 仅限省直单位角色拥有者可见
		return "modules/maintenance/hsMaintenanceApplyFullList";
	}
	
	/**
	 *
	 * 查询列表
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = {"listDone", ""})
	public String listDone(HsMaintenanceApply hsMaintenanceApply, Model model) {
		model.addAttribute("hsMaintenanceApply", hsMaintenanceApply);
		// 新增用户组织管理功能的控制权限设置 user.adminCtrlPermi，1拥有的权限 2管理的权限，无限级授权场景使用
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));

		//TODO 新增按钮权限控制 仅限省直单位角色拥有者可见

		return "modules/maintenance/hsMaintenanceApplyDoneList";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsMaintenanceApply hsMaintenanceApply, Model model) {
		model.addAttribute("hsMaintenanceApply", hsMaintenanceApply);
		// 新增用户组织管理功能的控制权限设置 user.adminCtrlPermi，1拥有的权限 2管理的权限，无限级授权场景使用
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));

		//TODO 新增按钮权限控制 仅限省直单位角色拥有者可见

		return "modules/maintenance/hsMaintenanceApplyedList";
//		return "modules/maintenance/hsMaintenanceApplyList";
	}

	/**
	 * 查询列表(流程已结束)
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = {"completedList", ""})
	public String completedList(HsMaintenanceApply hsMaintenanceApply, Model model) {
		model.addAttribute("hsMaintenanceApply", hsMaintenanceApply);
		// 新增用户组织管理功能的控制权限设置 user.adminCtrlPermi，1拥有的权限 2管理的权限，无限级授权场景使用
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		//TODO 新增按钮权限控制 仅限省直单位角色拥有者可见
		return "modules/maintenance/hsMaintenanceApplyCompletedList";
	}

	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = {"recordIndex", ""})
	public String recordIndex(HsMaintenanceApply hsMaintenanceApply, Model model) {
		model.addAttribute("hsMaintenanceApply", hsMaintenanceApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/maintenance/hsMaintenanceApplyRecordIndex";
	}

	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = {"recordList", ""})
	public String recordList(HsMaintenanceApply hsMaintenanceApply, Model model) {
		model.addAttribute("hsMaintenanceApply", hsMaintenanceApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/maintenance/hsMaintenanceApplyRecordList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsMaintenanceApply> listData(HsMaintenanceApply hsMaintenanceApply, HttpServletRequest request, HttpServletResponse response) {
		hsMaintenanceApply.setPage(new Page<>(request, response));
		Page<HsMaintenanceApply> page = hsMaintenanceApplyService.findAuditPageByTask(hsMaintenanceApply);
		return page;
	}

	/**
	 * 查询已办的审批任务-维修基金申请已办
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = "listAuditedData")
	@ResponseBody
	public Page<HsMaintenanceApply> listAuditedData(HsMaintenanceApply hsMaintenanceApply, HttpServletRequest request, HttpServletResponse response) {
		hsMaintenanceApply.setPage(new Page<>(request, response));
		String[] status = null;
		if (StringUtils.isNotEmpty(hsMaintenanceApply.getFlowStatus())) {
			status = new String[]{
				hsMaintenanceApply.getFlowStatus()
			};
		}
		Page<HsMaintenanceApply> page = hsMaintenanceApplyService.findApplyPageByTask(hsMaintenanceApply, status, "2", "maintenance_apply");
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = "completedListData")
	@ResponseBody
	public Page<HsMaintenanceApply> completedListData(HsMaintenanceApply hsMaintenanceApply, HttpServletRequest request, HttpServletResponse response) {
		hsMaintenanceApply.setPage(new Page<>(request, response));
		hsMaintenanceApply.setApplyStatus(12);
		Page<HsMaintenanceApply> page = hsMaintenanceApplyService.findPage(hsMaintenanceApply);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = "fullListData")
	@ResponseBody
	public Page<HsMaintenanceApply> fullListData(HsMaintenanceApply hsMaintenanceApply, HttpServletRequest request, HttpServletResponse response) {
		hsMaintenanceApply.setPage(new Page<>(request, response));
		hsMaintenanceApplyService.addDataScopeFilter(hsMaintenanceApply);
		Page<HsMaintenanceApply> page = hsMaintenanceApplyService.findPage(hsMaintenanceApply);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = "form")
	public String form(HsMaintenanceApply hsMaintenanceApply, Model model) {
		model.addAttribute("hsMaintenanceApply", hsMaintenanceApply);
		if (hsMaintenanceApply.getApplyStatus() == null) {
			hsMaintenanceApply.setApplyStatus(-1);
		}
		if (hsMaintenanceApply.getAdnEnd() != null && hsMaintenanceApply.getAdnStart() != null) {
			hsMaintenanceApply.setAdnNum(hsMaintenanceApply.getAdnEnd().intValue() - hsMaintenanceApply.getAdnStart().intValue() + 1);
		}

		String isRead = "false";
		if (hsMaintenanceApply.getApplyStatus() >= 1) {
			isRead = "true";
		}
		model.addAttribute("isRead", isRead);

		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		model.addAttribute("fundList", hsMaintenanceFundsService.findList(new HsMaintenanceFunds()));
//		return "modules/maintenance/hsMaintenanceApplyForm";
		return "modules/maintenance/hsMaintenanceApplyTableForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsMaintenanceApply hsMaintenanceApply) {
		hsMaintenanceApplyService.save(hsMaintenanceApply);
		if (hsMaintenanceApply.getApplyStatus() == null || hsMaintenanceApply.getApplyStatus() <= 1) {
			return renderResult(Global.TRUE, text("保存维修申请成功！"));
		} else {
			return renderResult(Global.TRUE, text("提交成功！"));
		}
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:edit")
	@PostMapping(value = "flushTaskStatus")
	@ResponseBody
	public String flushTaskStatus(@Validated HsMaintenanceApply hsMaintenanceApply) {
		hsMaintenanceApplyService.flushTaskStatus(hsMaintenanceApply);
		return renderResult(Global.TRUE, text("刷新维修申请状态成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsMaintenanceApply hsMaintenanceApply) {
		hsMaintenanceApply.setStatus(HsMaintenanceApply.STATUS_DISABLE);
		hsMaintenanceApplyService.updateStatus(hsMaintenanceApply);
		return renderResult(Global.TRUE, text("停用维修申请成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsMaintenanceApply hsMaintenanceApply) {
		hsMaintenanceApply.setStatus(HsMaintenanceApply.STATUS_NORMAL);
		hsMaintenanceApplyService.updateStatus(hsMaintenanceApply);
		return renderResult(Global.TRUE, text("启用维修申请成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsMaintenanceApply hsMaintenanceApply) {
		if (!HsMaintenanceApply.STATUS_DRAFT.equals(hsMaintenanceApply.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		hsMaintenanceApplyService.delete(hsMaintenanceApply);
		return renderResult(Global.TRUE, text("删除维修申请成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = "hsMaintenanceApplySelect")
	public String hsMaintenanceApplySelect(HsMaintenanceApply hsMaintenanceApply, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsMaintenanceApply", hsMaintenanceApply);
		return "modules/maintenance/hsMaintenanceApplySelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = "exportFundSlip")
	public void exportFundSlip(HsMaintenanceApply hsMaintenanceApply, HttpServletResponse response) {
		hsMaintenanceApplyService.exportFundSlip(hsMaintenanceApply, response);
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("maintenance:hsMaintenanceApply:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsMaintenanceApply hsMaintenanceApply, HttpServletResponse response) {
		hsMaintenanceApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		hsMaintenanceApplyService.addDataScopeFilter(hsMaintenanceApply);
		List<HsMaintenanceApply> list = hsMaintenanceApplyService.findList(hsMaintenanceApply);
		String fileName = "维修资金申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("维修资金申请", HsMaintenanceApply.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	
}