package com.hsobs.hs.modules.contract.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.contract.entity.HsContractRecordData;
import com.hsobs.hs.modules.contract.service.HsContractRecordDataService;

/**
 * 合同详情表Controller
 * <AUTHOR>
 * @version 2025-01-22
 */
@Controller
@RequestMapping(value = "${adminPath}/contract/hsContractRecordData")
public class HsContractRecordDataController extends BaseController {

	@Autowired
	private HsContractRecordDataService hsContractRecordDataService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsContractRecordData get(String id, boolean isNewRecord) {
		return hsContractRecordDataService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("contract:hsContractRecordData:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsContractRecordData hsContractRecordData, Model model) {
		model.addAttribute("hsContractRecordData", hsContractRecordData);
		return "modules/contract/hsContractRecordDataList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("contract:hsContractRecordData:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsContractRecordData> listData(HsContractRecordData hsContractRecordData, HttpServletRequest request, HttpServletResponse response) {
		hsContractRecordData.setPage(new Page<>(request, response));
		Page<HsContractRecordData> page = hsContractRecordDataService.findPage(hsContractRecordData);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("contract:hsContractRecordData:view")
	@RequestMapping(value = "form")
	public String form(HsContractRecordData hsContractRecordData, Model model) {
		model.addAttribute("hsContractRecordData", hsContractRecordData);
		return "modules/contract/hsContractRecordDataForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("contract:hsContractRecordData:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsContractRecordData hsContractRecordData) {
		hsContractRecordDataService.save(hsContractRecordData);
		return renderResult(Global.TRUE, text("保存合同详情表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("contract:hsContractRecordData:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsContractRecordData hsContractRecordData) {
		hsContractRecordDataService.delete(hsContractRecordData);
		return renderResult(Global.TRUE, text("删除合同详情表成功！"));
	}
	
}