package com.hsobs.hs.modules.dataintelligence.service;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceLiftSubsidyDao;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.sys.entity.DictData;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.DictUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceReviewDao;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;

/**
 * 住房保障数据统计Service  公租房资格年审统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class DataIntelligenceReviewService extends CrudService<DataIntelligenceReviewDao, DataIntelligenceReview> {

	@Autowired
	private DataIntelligenceReviewDao dataIntelligenceReviewDao;
	@Autowired
	private DataIntelligenceTotalService dataIntelligenceTotalService;

	/**
	 * 获取单条数据
	 * @param dataIntelligenceReview
	 * @return
	 */
	@Override
	public DataIntelligenceReview get(DataIntelligenceReview dataIntelligenceReview) {
		return super.get(dataIntelligenceReview);
	}
	
	/**
	 * 查询分页数据
	 * @param dataIntelligenceReview 查询条件
	 * @param dataIntelligenceReview page 分页对象
	 * @return
	 */
	@Override
	public Page<DataIntelligenceReview> findPage(DataIntelligenceReview dataIntelligenceReview) {
		return super.findPage(dataIntelligenceReview);
	}
	
	/**
	 * 查询列表数据
	 * @param dataIntelligenceReview
	 * @return
	 */
	@Override
	public List<DataIntelligenceReview> findList(DataIntelligenceReview dataIntelligenceReview) {
		return super.findList(dataIntelligenceReview);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param dataIntelligenceReview
	 */
	@Override
	@Transactional
	public void save(DataIntelligenceReview dataIntelligenceReview) {
		super.save(dataIntelligenceReview);
	}
	
	/**
	 * 更新状态
	 * @param dataIntelligenceReview
	 */
	@Override
	@Transactional
	public void updateStatus(DataIntelligenceReview dataIntelligenceReview) {
		super.updateStatus(dataIntelligenceReview);
	}
	
	/**
	 * 删除数据
	 * @param dataIntelligenceReview
	 */
	@Override
	@Transactional
	public void delete(DataIntelligenceReview dataIntelligenceReview) {
		dataIntelligenceReview.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataIntelligenceReview);
	}

	private  String getSqlOtherWhere(DataIntelligenceReview dataIntelligenceReview, Date startDate, Date endDate){
		String sqlOtherWhere = "";
		if (startDate != null || dataIntelligenceReview.getStartDate() != null) {
			Date date = (startDate!=null)?startDate:dataIntelligenceReview.getStartDate();
			sqlOtherWhere += " AND a.create_date >= '" + String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}
		if (endDate != null || dataIntelligenceReview.getEndDate() != null) {
			Date date = (endDate!=null)?endDate:dataIntelligenceReview.getEndDate();
			sqlOtherWhere += " AND a.create_date <= '" + String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}

		/*if(dataIntelligenceReview.getEstateName() != null && dataIntelligenceReview.getEstateName().length() > 0) {
			sqlOtherWhere += " AND estate.name like '%" + dataIntelligenceReview.getEstateName() + "%'";
		}*/
		if(dataIntelligenceReview.getEstateId() != null && dataIntelligenceReview.getEstateId().length() > 0) {
			sqlOtherWhere += " AND estate.id = '" + (dataIntelligenceReview.getEstateId()) + "'";
		}
		if(dataIntelligenceReview.getCity() != null && !"".equals(dataIntelligenceReview.getCity())){
			sqlOtherWhere += " AND estate.city = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceReview.getCity()) + "'";
		}
		if(dataIntelligenceReview.getArea() != null && !"".equals(dataIntelligenceReview.getArea())){
			sqlOtherWhere += " AND estate.area = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceReview.getArea()) + "'";
		}

		/*if(sqlWhere.length() > 5){
			return "WHERE " + sqlWhere.substring(5);
		}
		return "";*/
		return sqlOtherWhere;
	}

	public Page<DataIntelligenceReview> findResourceDataPage(DataIntelligenceReview dataIntelligenceReview, boolean findpage){

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceReview, null, null);
		String sqlOrderBy = (dataIntelligenceReview.getOrderBy()!=null&&dataIntelligenceReview.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceReview.getOrderBy()):"";

		Page<DataIntelligenceReview> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceReview.getPageNo());
			pageMap.setPageSize(dataIntelligenceReview.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceReviewDao.countReviewArea(mapPara);
		List<DataIntelligenceReview> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceReview review = new DataIntelligenceReview();

			Object ob = map.get("ESTATE_NAME");
			review.setEstateName((ob!=null)?ob.toString():"");


			ob = map.get("TOTAL_COUNT");
			review.setTotalCount((ob!=null)?Integer.valueOf(ob.toString()):0);
			ob = map.get("NORMAL_COUNT");
			review.setNormalCount((ob!=null)?Integer.valueOf(ob.toString()):0);
			ob = map.get("ABNORMAL_COUNT");
			review.setAbnormalCount((ob!=null)?Integer.valueOf(ob.toString()):0);

			statList.add(review);
		}
		pageMap.setList(statList);
		return pageMap;
	}

    public List<DataIntelligenceReview> countReviewAreaStat(DataIntelligenceReview dataIntelligenceReview) {

		return findResourceDataPage(dataIntelligenceReview, false).getList();
    }

	public void SetTypeData(List<Map<String,Object>> value, List<List<String>> dataType){
		if(value.size() == 0) {
			dataType.get(0).add("0");
			dataType.get(1).add("0");
			dataType.get(2).add("0");
		}
		else {
			Map<String,Object> map = value.get(0);

			Object ob = map.get("TOTAL_COUNT");
			dataType.get(0).add((ob!=null)?ob.toString():"0");

			ob = map.get("NORMAL_COUNT");
			dataType.get(1).add((ob!=null)?ob.toString():"0");

			ob = map.get("ABNORMAL_COUNT");
			dataType.get(2).add((ob!=null)?ob.toString():"0");
		}
	}
	public String findReviewStatTotal(DataIntelligenceReview dataIntelligenceReview) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 3; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "总量");break;
				case 1:dataTypeO.put("name", "正常量");break;
				case 2:dataTypeO.put("name", "异常量");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		Map<String, Object> mapPara = dataIntelligenceTotalService.getComparePara(dataIntelligenceReview.getCompareType());

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceReview, (Date)mapPara.get("last_start_date"), (Date)mapPara.get("last_end_date"));
		List<Map<String,Object>> value = dataIntelligenceReviewDao.countReviewCompare(sqlOtherWhere);
		SetTypeData(value, dataType);

		sqlOtherWhere = getSqlOtherWhere(dataIntelligenceReview, (Date)mapPara.get("now_start_date"), (Date)mapPara.get("now_end_date"));
		value = dataIntelligenceReviewDao.countReviewCompare(sqlOtherWhere);
		SetTypeData(value, dataType);

		dataClass.add((String)mapPara.get("last_title"));
		dataClass.add((String)mapPara.get("now_title"));

		Integer max = dataIntelligenceTotalService.calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}

	public String findReviewCmpare(DataIntelligenceReview dataIntelligenceReview) {

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceReview, null, null);
		List<Map<String,Object>> list = dataIntelligenceReviewDao.countReviewType(sqlOtherWhere);

		List<Map<String, Object>> newList = new ArrayList<>();
		DictData dictData;
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> unitMap = list.get(i);
			Map<String, Object> map = new HashMap<>();

			Object ob = unitMap.get("ABNORMAL_TYPE");
			if(ob == null){
				continue;
			}
			dictData = DictUtils.getDictData("hs_qw_check_violation_type", ob.toString());
			if(dictData == null){
				continue;
			}
			map.put("name", dictData.getDictLabel());

			ob = unitMap.get("ABNORMAL_COUNT");
			map.put("value", (ob!=null)?ob.toString():"0");

			newList.add(map);
		}
		return JSON.toJSONString(newList);
	}
}