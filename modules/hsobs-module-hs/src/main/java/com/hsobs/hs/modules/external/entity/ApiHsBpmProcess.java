package com.hsobs.hs.modules.external.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

public class ApiHsBpmProcess extends ApiBody{


    private String nodeName; //环节名称

    private String currentNode; //是否当前节点（0：否 1：是）

    private String nodeStatusDes; //节点状态描述（审核通过、已确认参加、已确认、提交。。。）

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getCurrentNode() {
        return currentNode;
    }

    public void setCurrentNode(String currentNode) {
        this.currentNode = currentNode;
    }

    public String getNodeStatusDes() {
        return nodeStatusDes;
    }

    public void setNodeStatusDes(String nodeStatusDes) {
        this.nodeStatusDes = nodeStatusDes;
    }
}
