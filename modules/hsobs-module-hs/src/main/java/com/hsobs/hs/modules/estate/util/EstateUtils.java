/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.hsobs.hs.modules.estate.util;

import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.estate.service.HsQwPublicRentalEstateService;
import com.jeesite.common.cache.CacheUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.SpringUtils;

import java.util.List;

/**
 * CmsUtils
 *
 * <AUTHOR>
 * @version 2020-7-24
 */
public class EstateUtils {

    private static final String ESTATE_CACHE = "estateCache";

    private static final class Static {
        private static final HsQwPublicRentalEstateService estateService = SpringUtils.getBean(HsQwPublicRentalEstateService.class);
    }


    /**
     * 获得站点列表
     */
    public static List<HsQwPublicRentalEstate> getEstateList() {
        @SuppressWarnings("unchecked")
        List<HsQwPublicRentalEstate> estateList = (List<HsQwPublicRentalEstate>) getCache("estateList");
        if (estateList == null) {
            estateList = Static.estateService.findList(new HsQwPublicRentalEstate());
            putCache("estateList", estateList);
        }
        return estateList;
    }

    /**
     * 获得站点列表
     */
    public static List<HsQwPublicRentalEstate> getEstateList(String areaCode) {

        HsQwPublicRentalEstate estate = new HsQwPublicRentalEstate();
        //estate.setArea(areaCode);
        estate.sqlMap().getWhere().and("area", QueryType.EQ, areaCode);
        List<HsQwPublicRentalEstate> estateList = Static.estateService.findList(estate);
        return estateList;
    }

    public static <V> V getCache(String key) {
        return CacheUtils.get(ESTATE_CACHE, key);
    }

    public static <V> V getCache(String key, V defaultValue) {
        V value = CacheUtils.get(ESTATE_CACHE, key);
        return value != null ? value : defaultValue;
    }

    public static void putCache(String key, Object value) {
        CacheUtils.put(ESTATE_CACHE, key, value);
    }
}