package com.hsobs.hs.modules.house.entity;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.housrvr.entity.HsQwHouseVr;
import com.hsobs.hs.modules.province.entity.HsQwApplyProvince;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;
import org.checkerframework.checker.units.qual.min;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * 租赁公租房房源房源信息表Entity
 *
 * <AUTHOR>
 * @version 2024-11-20
 */
@Table(name = "hs_qw_public_rental_house", alias = "a", label = "租赁公租房房源房源信息表信息", columns = {
        @Column(name = "id", attrName = "id", label = "编号", isPK = true),
        @Column(name = "estate_id", attrName = "estateId", label = "楼盘编号"),
        @Column(name = "location", attrName = "location", label = "楼盘位置"),
        @Column(name = "building_num", attrName = "buildingNum", label = "楼号"),
        @Column(name = "house_num", attrName = "houseNum", label = "住房编号"),
        @Column(name = "floor", attrName = "floor", label = "所处楼层"),
        @Column(name = "building_area", attrName = "buildingArea", label = "建筑面积", isQuery = false),
        @Column(name = "house_type", attrName = "houseType", label = "户型",comment = "户型（0 单居室 1 两居室 2 2.5居室）"),
        @Column(name = "unit_num", attrName = "unitNum", label = "单元号"),
        @Column(name = "file_plan", attrName = "filePlan", label = "平面图图片文件编号", isQuery = false),
        @Column(name = "vr_info", attrName = "vrInfo", label = "VR信息", isQuery = false),
        @Column(name = "is_public", attrName = "isPublic", label = "是否发布", comment = "是否发布（0未发布 1已发布）"),
        @Column(includeEntity = DataEntity.class),
        @Column(name = "type", attrName = "type", label = "房源类型", comment = "房源类型（0公租房 1限价房 2公用住房 3局直公房 4自管公房）"),
        @Column(name = "house_status", attrName = "houseStatus", label = "房屋状态", comment = "房屋状态（0待配租 1已配租 2待售 3已售）"),
        @Column(name="shared_area", attrName="sharedArea", label="公摊面积", isQuery=false),
        @Column(name="support_area", attrName="supportArea", label="附属间面积", isQuery=false),
        @Column(name="office_code", attrName="officeCode", label="所属部门", isQuery=true),
        @Column(name="house_sale_status", attrName="houseSaleStatus", label="配售状态", comment = "配售状态(针对公有住房配售0未配售,1已配售)"),
        @Column(name="asset_code", attrName="assetCode", label="资产编码", comment="资产编码"),
}, joinTable = {
            @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "o",
                    on = "o.id = a.estate_id", attrName="estate",
                    columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)}),
            @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApply.class, alias = "ha",
                    on = "a.id = ha.house_id and ha.status=0 and ha.apply_matter=0", attrName="hsQwApply",
                    columns = {@Column(includeEntity = HsQwApply.class)}),
            @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "oa", on = "oa.apply_id = ha.id and oa.apply_role = 0 and oa.status=0", attrName = "hsQwApply.mainApplyer", columns = {
                @Column(includeEntity = HsQwApplyer.class) }),
            @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "f",
                    on = "a.office_code = f.office_code", attrName="office",
                    columns = {@Column(includeEntity = Office.class)}),
            @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwHouseVr.class, alias = "v",
                    on = "a.vr_info = v.id", attrName="hsQwHouseVr",
                    columns = {@Column(includeEntity = HsQwHouseVr.class)})
    }, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere" , orderBy = "a.update_date DESC"
)
public class HsQwPublicRentalHouse extends DataEntity<HsQwPublicRentalHouse> {

    private static final long serialVersionUID = 1L;
    private String estateId;        // 楼盘编号
    private String location;        // 楼盘位置
    private String buildingNum;        // 楼号
    private String houseNum;        // 住房编号
    private Long floor;        // 所处楼层
    private String buildingArea;        // 建筑面积
    private String houseType;        // 户型
    private String unitNum;        // 单元号
    private String filePlan;        // 平面图图片文件编号
    private String vrInfo;        // VR信息
    private String isPublic;        // 是否发布（0未发布 1已发布）
    private String type;        // 房源类型（0公租房 1限价房 2公用住房 3局直公房 4自管公房）
    private String houseStatus;        // 房屋状态（0待配租 1已配租 2待售 3已售）
    private String applyedIdStr; //已配租的房源ids
    private Double sharedArea;		// 公摊面积
    private Double supportArea;     // 附属间面积
    private HsQwPublicRentalEstate estate;
    private String officeCode;
    private Office office;
    private String houseSaleStatus;     // 配售状态(针对公有住房配售0未配售,1已配售)
    private String assetCode;
    private HsQwHouseVr hsQwHouseVr;

    private HsQwApply hsQwApply;
    private String simpleInfo;

    private String selectId;

    private HsQwApplyBureau bureau;

    private HsQwApplyProvince province;
    private String selectTypes;

    private String applyId;

    private String dataType; // 1-绿色通道 2-
    public static final String HOUSE_TYPE_RENTAL = "0";//房源类型-公租房
    public static final String HOUSE_TYPE_PRICE = "1";//房源类型-限价房
    public static final String HOUSE_TYPE_PUBLIC = "2";//房源类型-公用住房
    public static final String HOUSE_TYPE_BUREAU = "3";//房源类型-局直公房
    public static final String HOUSE_TYPE_PROVINCE = "4";//房源类型-自管公房

    @ExcelFields({
            @ExcelField(title="楼盘编号", attrName="estateId", align=Align.CENTER, sort=20),
            @ExcelField(title="楼盘位置", attrName="location", align=Align.CENTER, sort=30),
            @ExcelField(title="楼号", attrName="buildingNum", align=Align.CENTER, sort=40),
            @ExcelField(title="住房编号", attrName="houseNum", align=Align.CENTER, sort=50),
            @ExcelField(title="所处楼层", attrName="floor", align=Align.CENTER, sort=60),
            @ExcelField(title="建筑面积", attrName="buildingArea", align=Align.CENTER, sort=70),
            @ExcelField(title="户型\n(单居室/两居室/2.5居室)", attrName="houseType", dictType="hs_house_house_type" , align=Align.CENTER, sort=80),
            @ExcelField(title="单元号", attrName="unitNum", align=Align.CENTER, sort=90),
            @ExcelField(title="单位", attrName="officeCode", align=Align.CENTER, sort=200),
            @ExcelField(title="公摊面积", attrName="sharedArea", align=Align.CENTER, sort=220),
    })
    public HsQwPublicRentalHouse() {
        this(null);
    }

    public HsQwPublicRentalHouse(String id) {
        super(id);
    }

    @NotBlank(message = "楼盘编号不能为空")
    @Size(min = 0, max = 64, message = "楼盘编号长度不能超过 64 个字符")
    public String getEstateId() {
        return estateId;
    }

    public void setEstateId(String estateId) {
        this.estateId = estateId;
    }

    @Size(min = 0, max = 50, message = "楼盘位置长度不能超过 50 个字符")
    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    @NotBlank(message = "楼号不能为空")
    @Size(min = 0, max = 10, message = "楼号长度不能超过 10 个字符")
    public String getBuildingNum() {
        return buildingNum;
    }

    public void setBuildingNum(String buildingNum) {
        this.buildingNum = buildingNum;
    }

    @NotBlank(message = "住房编号不能为空")
    @Size(min = 0, max = 50, message = "住房编号长度不能超过 50 个字符")
    public String getHouseNum() {
        return houseNum;
    }

    public void setHouseNum(String houseNum) {
        this.houseNum = houseNum;
    }

    @NotNull(message = "所处楼层不能为空")
    @Min(value = 0, message = "楼层必须为正整数")
    public Long getFloor() {
        return floor;
    }

    public void setFloor(Long floor) {
        this.floor = floor;
    }

    @NotBlank(message = "建筑面积不能为空")
    @Size(min = 0, max = 10, message = "建筑面积长度不能超过 10 个字符")
    @Digits(integer = 3, fraction = 2, message = "建筑面积必须是数字")
    public String getBuildingArea() {
        return buildingArea;
    }

    public void setBuildingArea(String buildingArea) {
        this.buildingArea = buildingArea;
    }

    @Size(min = 0, max = 10, message = "户型长度不能超过 10 个字符")
    @Min(value = 0, message = "户型类型错误")
    @Max(value = 2, message = "户型类型错误")
    public String getHouseType() {
        return houseType;
    }

    public void setHouseType(String houseType) {
        this.houseType = houseType;
    }

    @NotBlank(message = "单元号不能为空")
    @Size(min = 0, max = 10, message = "单元号长度不能超过 10 个字符")
    public String getUnitNum() {
        return unitNum;
    }

    public void setUnitNum(String unitNum) {
        this.unitNum = unitNum;
    }

    @Size(min = 0, max = 64, message = "平面图图片文件编号长度不能超过 64 个字符")
    public String getFilePlan() {
        return filePlan;
    }

    public void setFilePlan(String filePlan) {
        this.filePlan = filePlan;
    }

    @Size(min = 0, max = 255, message = "VR信息长度不能超过 255 个字符")
    public String getVrInfo() {
        return vrInfo;
    }

    public void setVrInfo(String vrInfo) {
        this.vrInfo = vrInfo;
    }

    @NotBlank(message = "是否发布不能为空")
    @Size(min = 0, max = 1, message = "是否发布长度不能超过 1 个字符")
    public String getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(String isPublic) {
        this.isPublic = isPublic;
    }

    @Size(min = 0, max = 255, message = "房源类型长度不能超过 255 个字符")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Size(min = 0, max = 255, message = "房屋状态长度不能超过 255 个字符")
    public String getHouseStatus() {
        return houseStatus;
    }

    public void setHouseStatus(String houseStatus) {
        this.houseStatus = houseStatus;
    }

    public Date getCreateDate_gte() {
        return sqlMap.getWhere().getValue("create_date", QueryType.GTE);
    }

    public void setCreateDate_gte(Date createDate) {
        sqlMap.getWhere().and("create_date", QueryType.GTE, createDate);
    }

    public Date getCreateDate_lte() {
        return sqlMap.getWhere().getValue("create_date", QueryType.LTE);
    }

    public void setCreateDate_lte(Date createDate) {
        sqlMap.getWhere().and("create_date", QueryType.LTE, createDate);
    }

    public HsQwPublicRentalEstate getEstate() {
        return estate;
    }

    public void setEstate(HsQwPublicRentalEstate estate) {
        this.estate = estate;
    }

    public String getApplyedIdStr() {
        return applyedIdStr;
    }

    public void setApplyedIdStr(String applyedIdStr) {
        this.applyedIdStr = applyedIdStr;
    }

    @NotNull(message="公摊面积不能为空")
    public Double getSharedArea() {
        return sharedArea;
    }

    public void setSharedArea(Double sharedArea) {
        this.sharedArea = sharedArea;
    }

    public Double getSupportArea() {
        return supportArea;
    }

    public void setSupportArea(Double supportArea) {
        this.supportArea = supportArea;
    }

    public Office getOffice() {
        return office;
    }

    public void setOffice(Office office) {
        this.office = office;
    }

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    public String getHouseSaleStatus() {
        return houseSaleStatus;
    }

    public void setHouseSaleStatus(String houseSaleStatus) {
        this.houseSaleStatus = houseSaleStatus;
    }

    public HsQwApply getHsQwApply() {
        return hsQwApply;
    }

    public void setHsQwApply(HsQwApply hsQwApply) {
        this.hsQwApply = hsQwApply;
    }

    public String getSimpleInfo() {
        if (estate!=null && this.buildingNum !=null && this.unitNum!=null){
            return estate.getName() + "小区" + this.buildingNum + "楼" + this.unitNum;
        }
        return "";
    }

    public void setSimpleInfo(String simpleInfo) {
        this.simpleInfo = simpleInfo;
    }

    public HsQwApplyBureau getBureau() {
        return bureau;
    }

    public void setBureau(HsQwApplyBureau bureau) {
        this.bureau = bureau;
    }

    public HsQwApplyProvince getProvince() {
        return province;
    }

    public void setProvince(HsQwApplyProvince province) {
        this.province = province;
    }

    public String getSelectTypes() {
        return selectTypes;
    }

    public void setSelectTypes(String selectTypes) {
        this.selectTypes = selectTypes;
    }

    public HsQwHouseVr getHsQwHouseVr() {
        return hsQwHouseVr;
    }

    public void setHsQwHouseVr(HsQwHouseVr hsQwHouseVr) {
        this.hsQwHouseVr = hsQwHouseVr;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getSelectId() {
        return selectId;
    }

    public void setSelectId(String selectId) {
        this.selectId = selectId;
    }

    public String getAssetCode() {
        return assetCode;
    }

    public void setAssetCode(String assetCode) {
        this.assetCode = assetCode;
    }
}