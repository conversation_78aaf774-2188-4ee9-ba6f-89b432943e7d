package com.hsobs.hs.modules.external.entity;

public class ApiHsManagementFee extends ApiBody{

    private String jflx;//	否	缴费类型（0：物业费 1：租金 2：水费 3：电费 4：燃气费）
    private String  jfkssj;//	否	缴费时间（开始）yyyy-MM-dd HH:mm:ss格式
    private String  jfjssj;//	否	缴费时间（结束） yyyy-MM-dd HH:mm:ss格式
    private String jfzq;//缴费周期
    private String   jfsj;//	缴费时间 yyyy-MM-dd HH:mm:ss格式
    private String   jfje;//	缴费金额
    private String   jffw;//	缴费房屋
    private String    jfzt;//	缴费状态（0：成功 1：失败）

    public String getJflx() {
        return jflx;
    }

    public void setJflx(String jflx) {
        this.jflx = jflx;
    }

    public String getJfkssj() {
        return jfkssj;
    }

    public void setJfkssj(String jfkssj) {
        this.jfkssj = jfkssj;
    }

    public String getJfjssj() {
        return jfjssj;
    }

    public void setJfjssj(String jfjssj) {
        this.jfjssj = jfjssj;
    }

    public String getJfsj() {
        return jfsj;
    }

    public void setJfsj(String jfsj) {
        this.jfsj = jfsj;
    }

    public String getJfje() {
        return jfje;
    }

    public void setJfje(String jfje) {
        this.jfje = jfje;
    }

    public String getJffw() {
        return jffw;
    }

    public void setJffw(String jffw) {
        this.jffw = jffw;
    }

    public String getJfzt() {
        return jfzt;
    }

    public void setJfzt(String jfzt) {
        this.jfzt = jfzt;
    }

    public String getJfzq() {
        return jfzq;
    }

    public void setJfzq(String jfzq) {
        this.jfzq = jfzq;
    }
}
