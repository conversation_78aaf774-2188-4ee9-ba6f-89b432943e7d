package com.hsobs.hs.modules.contract.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 合同Entity
 * <AUTHOR>
 * @version 2025-01-22
 */
@Table(name="hs_contract_record", alias="a", label="合同信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="template_id", attrName="templateId", label="合同模板ID"),
		@Column(name="contract_name", attrName="contractName", label="合同名称", queryType=QueryType.LIKE),
		@Column(name="contract_no", attrName="contractNo", label="合同编号", queryType=QueryType.LIKE),
		@Column(name="contract_type", attrName="contractType", label="合同类型", queryType=QueryType.LIKE),
		@Column(name="contract_source", attrName="contractSource", label="合同来源 0-生成 1-导入"),
		@Column(name="apply_name", attrName="applyName", label="申请人名称", queryType=QueryType.LIKE),
		@Column(name="apply_sfz", attrName="applySfz", label="申请人身份证", queryType=QueryType.LIKE),
		@Column(name="apply_tel", attrName="applyTel", label="联系电话"),
		@Column(name="work_unit", attrName="workUnit", label="工作单位"),
		@Column(name="house_addr", attrName="houseAddr", label="房屋地址"),
		@Column(name="unit_no", attrName="unitNo", label="单元号"),
		@Column(name="house_area", attrName="houseArea", label="面积"),
		@Column(name="house_type", attrName="houseType", label="户型"),
		@Column(name="house_rent", attrName="houseRent", label="租金"),
		@Column(name="start_time", attrName="startTime", label="合同签订开始时间", isUpdateForce=true),
		@Column(name="end_time", attrName="endTime", label="合同签订结束时间", isUpdateForce=true),
		@Column(name="contract_desc", attrName="contractDesc", label="合同描述"),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, orderBy="a.update_date DESC"
)
public class HsContractRecord extends DataEntity<HsContractRecord> {
	
	private static final long serialVersionUID = 1L;
	private String templateId;		// 合同模板ID
	private String contractName;		// 合同名称
	private String contractNo;		// 合同编号
	private String contractType;		// 合同类型
	private String contractSource;		// 合同来源 0-生成 1-导入
	private String applyName;		// 申请人名称
	private String applySfz;		// 申请人身份证
	private String applyTel;		// 联系电话
	private String workUnit;		// 工作单位
	private String houseAddr;		// 房屋地址
	private String unitNo;		// 单元号
	private String houseArea;		// 面积
	private String houseType;		// 户型
	private String houseRent;		// 租金
	private Date startTime;		// 合同签订开始时间
	private Date endTime;		// 合同签订结束时间
	private String contractDesc;		// 合同描述
	private String validTag;		// 是否有效;1-有效 0-无效

	private Integer isView = 0;

	private List<HsContractRecordField> fieldList;
	private HsContractRecordData hsContractRecordData; // 合同内容

	public HsContractRecord() {
		this(null);
	}
	
	public HsContractRecord(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="合同模板ID长度不能超过 64 个字符")
	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	
	@Size(min=0, max=255, message="合同名称长度不能超过 255 个字符")
	public String getContractName() {
		return contractName;
	}

	public void setContractName(String contractName) {
		this.contractName = contractName;
	}
	
	@Size(min=0, max=255, message="合同编号长度不能超过 255 个字符")
	public String getContractNo() {
		return contractNo;
	}

	public void setContractNo(String contractNo) {
		this.contractNo = contractNo;
	}
	
	@Size(min=0, max=255, message="合同类型长度不能超过 255 个字符")
	public String getContractType() {
		return contractType;
	}

	public void setContractType(String contractType) {
		this.contractType = contractType;
	}
	
	@NotBlank(message="合同来源 0-生成 1-导入不能为空")
	@Size(min=0, max=1, message="合同来源 0-生成 1-导入长度不能超过 1 个字符")
	public String getContractSource() {
		return contractSource;
	}

	public void setContractSource(String contractSource) {
		this.contractSource = contractSource;
	}
	
	@Size(min=0, max=20, message="申请人名称长度不能超过 20 个字符")
	public String getApplyName() {
		return applyName;
	}

	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	
	@Size(min=0, max=18, message="申请人身份证长度不能超过 18 个字符")
	public String getApplySfz() {
		return applySfz;
	}

	public void setApplySfz(String applySfz) {
		this.applySfz = applySfz;
	}
	
	@Size(min=0, max=15, message="联系电话长度不能超过 15 个字符")
	public String getApplyTel() {
		return applyTel;
	}

	public void setApplyTel(String applyTel) {
		this.applyTel = applyTel;
	}
	
	@Size(min=0, max=255, message="工作单位长度不能超过 255 个字符")
	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}
	
	@Size(min=0, max=512, message="房屋地址长度不能超过 512 个字符")
	public String getHouseAddr() {
		return houseAddr;
	}

	public void setHouseAddr(String houseAddr) {
		this.houseAddr = houseAddr;
	}
	
	@Size(min=0, max=64, message="单元号长度不能超过 64 个字符")
	public String getUnitNo() {
		return unitNo;
	}

	public void setUnitNo(String unitNo) {
		this.unitNo = unitNo;
	}
	
	@Size(min=0, max=64, message="面积长度不能超过 64 个字符")
	public String getHouseArea() {
		return houseArea;
	}

	public void setHouseArea(String houseArea) {
		this.houseArea = houseArea;
	}
	
	@Size(min=0, max=128, message="户型长度不能超过 128 个字符")
	public String getHouseType() {
		return houseType;
	}

	public void setHouseType(String houseType) {
		this.houseType = houseType;
	}
	
	@Size(min=0, max=16, message="租金长度不能超过 16 个字符")
	public String getHouseRent() {
		return houseRent;
	}

	public void setHouseRent(String houseRent) {
		this.houseRent = houseRent;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}


	public Date getStartTime_gte() {
		return sqlMap.getWhere().getValue("start_time", QueryType.GTE);
	}

	public void setStartTime_gte(Date startTime) {
		sqlMap.getWhere().and("start_time", QueryType.GTE, startTime);
	}

	public Date getEndTime_lte() {
		return sqlMap.getWhere().getValue("end_time", QueryType.LTE);
	}

	public void setEndTime_lte(Date endTime) {
		sqlMap.getWhere().and("end_time", QueryType.LTE, endTime);
	}

	@Size(min=0, max=255, message="合同描述长度不能超过 255 个字符")
	public String getContractDesc() {
		return contractDesc;
	}

	public void setContractDesc(String contractDesc) {
		this.contractDesc = contractDesc;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public List<HsContractRecordField> getFieldList() {
		return fieldList;
	}

	public void setFieldList(List<HsContractRecordField> fieldList) {
		this.fieldList = fieldList;
	}

	public HsContractRecordData getHsContractRecordData() {
		return hsContractRecordData;
	}

	public void setHsContractRecordData(HsContractRecordData hsContractRecordData) {
		this.hsContractRecordData = hsContractRecordData;
	}

	public Integer getIsView() {
		return isView;
	}

	public void setIsView(Integer isView) {
		this.isView = isView;
	}
}