package com.hsobs.hs.modules.apply.web;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.entity.HsQwApplyAllExport;
import com.hsobs.hs.modules.apply.entity.HsQwApplyExport;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applypublicdetail.service.HsQwApplyPublicDetailService;
import com.hsobs.hs.modules.applyscore.entity.HsQwApplyScoreDetail;
import com.hsobs.hs.modules.applyscore.service.HsQwApplyScoreDetailService;
import com.hsobs.hs.modules.clearance.service.HsQwClearanceService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 租赁资格轮候申请Controller
 *
 * <AUTHOR>
 * @version 2024-11-21
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/hsQwApply")
@Api(tags = "Apply - 租房服务")
public class HsQwApplyController extends BaseController {

    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private BpmTaskService bpmTaskService;

    @Autowired
    private HsQwClearanceService hsQwClearanceService;

    @Autowired
    private HsQwApplyPublicDetailService hsQwApplyPublicDetailService;

    @Autowired
    private HsQwApplyScoreDetailService hsQwApplyScoreDetailService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public HsQwApply get(String id, boolean isNewRecord) {
        return hsQwApplyService.get(id, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = { "list", "" })
    public String list(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyList";
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = { "listAll", "" })
    public String listAll(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyListAllTab";
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = { "listAllUrl", "" })
    public String listAllUrl(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyListAllJump";
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = { "applyedList", "" })
    public String applyedList(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyedList";
    }

    /**
     * 查询列表-已处理
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = { "listDone", "" })
    public String listDone(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyDoneList";
    }

    /**
     * 查询列表-绿色通道
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = { "listGreen", "" })
    public String listGreen(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/greenChannel/hsQwApplyGreenList";
    }

    /**
     * 查询列表-初审排序
     */
    @RequiresPermissions("apply:hsQwApply:order")
    @RequestMapping(value = { "orderListFirst", "" })
    public String orderListFirst(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyListOrder";
    }

    /**
     * 查询列表-复查排序
     */
    @RequiresPermissions("apply:hsQwApply:orderCheck")
    @RequestMapping(value = { "orderListCheck", "" })
    public String orderListCheck(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyListOrderCheck";
    }

    /**
     * 查询列表-资格轮候
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = { "lhList", "" })
    public String lhList(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyListLh";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<HsQwApply> listData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        // todo 控制数据权限
        Page<HsQwApply> page = hsQwApplyService.findPage(hsQwApply);
        return page;
    }

    /**
     * 查询列表数据-多维度检索数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listQueryData")
    @ResponseBody
    public Page<HsQwApply> listQueryData(HsQwApply hsQwApply, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        // todo 控制数据权限
        hsQwApplyService.addDataScopeFilter(hsQwApply);
        Page<HsQwApply> page = hsQwApplyService.findPageByQuery(hsQwApply);
        return page;
    }

    /**
     * 查询列表数据-获取所有已经承租的列表
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listApplyedData")
    @ResponseBody
    public Page<HsQwApply> listApplyedData(HsQwApply hsQwApply, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        Page<HsQwApply> page = hsQwApplyService.getApplyedPage(hsQwApply);
        return page;
    }

    /**
     * 查询代办的审批任务-租房申请代办
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listAuditData")
    @ResponseBody
    public Page<HsQwApply> listAuditData(HsQwApply hsQwApply, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        Page<HsQwApply> page = hsQwApplyService.findAuditPageByTask(hsQwApply);
        return page;
    }

    /**
     * 查询已办的审批任务-租房申请已办
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listAuditedData")
    @ResponseBody
    public Page<HsQwApply> listAuditedData(HsQwApply hsQwApply, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        hsQwApply.setOrderBy("a.id desc");
        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTaskNew(hsQwApply, null, "2", "rent_apply");
        return page;
    }

    /**
     * 查询子表数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "hsQwApplyerListData")
    @ResponseBody
    public Page<HsQwApplyer> subListData(HsQwApplyer hsQwApplyer, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApplyer.setPage(new Page<>(request, response));
        Page<HsQwApplyer> page = hsQwApplyService.findSubPage(hsQwApplyer);
        return page;
    }

    /**
     * 查询子表数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "hsQwApplyHouseListData")
    @ResponseBody
    public Page<HsQwApplyHouse> subListData(HsQwApplyHouse hsQwApplyHouse, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApplyHouse.setPage(new Page<>(request, response));
        Page<HsQwApplyHouse> page = hsQwApplyService.findSubPage(hsQwApplyHouse);
        return page;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "form")
    public String form(HsQwApply hsQwApply, Model model) {
        if (hsQwApply.getIsNewRecord()) {
            hsQwApply.setOfficeCode(EmpUtils.getOffice().getOfficeCode());
            hsQwApply.setOffice(EmpUtils.getOffice());
        }
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        if (hsQwApply.getRedirectPage() != null) {
            return "modules/apply/normal/" + hsQwApply.getRedirectPage();
        }
        return "modules/apply/normal/hsQwApplyForm";
    }

    private BpmParams getBpm(BpmTask task) {
        BpmParams params = new BpmParams();
        params.setTaskId(task.getId());
        params.setProcInsId(task.getProcIns().getId());
        params.setActivityId(task.getActivityId());
        return params;
    }

    /**
     * 查看编辑表单-只读表单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formRead")
    public String formRead(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        return "modules/apply/normal/hsQwApplyFormRead";
    }

    /**
     * 查看编辑表单-房源配租
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formHouse")
    public String formHouse(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        return "modules/apply/normal/hsQwApplyFormHouse";
    }

    /**
     * 查看编辑表单-房源配租
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formConfirm")
    public String formConfirm(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        return "modules/apply/normal/hsQwApplyFormConfirm";
    }

    /**
     * 查看编辑表单-合同
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formCompact")
    public String formCompact(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        return "modules/apply/normal/hsQwApplyFormCompact";
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(@Validated HsQwApply hsQwApply) {
        hsQwApplyService.save(hsQwApply);
        if (hsQwApply.getIsNewRecord()) {
            return renderResult(Global.TRUE, text("保存租赁资格轮候申请成功！"));
        }
        return renderResult(Global.TRUE, text("审核成功！"));
    }

    /**
     * 停用数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "disable")
    @ResponseBody
    public String disable(HsQwApply hsQwApply) {
        hsQwApply.setStatus(HsQwApply.STATUS_DISABLE);
        hsQwApplyService.updateStatus(hsQwApply);
        return renderResult(Global.TRUE, text("停用租赁资格轮候申请成功"));
    }

    /**
     * 启用数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "enable")
    @ResponseBody
    public String enable(HsQwApply hsQwApply) {
        hsQwApply.setStatus(HsQwApply.STATUS_NORMAL);
        hsQwApplyService.updateStatus(hsQwApply);
        return renderResult(Global.TRUE, text("启用租赁资格轮候申请成功"));
    }

    /**
     * 删除数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(HsQwApply hsQwApply) {
        if (!"0".equals(hsQwApply.getStatus())) {
            return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
        }
        hsQwApplyService.delete(hsQwApply);
        return renderResult(Global.TRUE, text("删除租赁资格轮候申请成功！"));
    }

    /**
     * 选择员工对话框
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "houseSelect")
    public String houseSelect(HsQwPublicRentalHouse hsQwPublicRentalHouse, String selectData, Model model,
            String applyId) {
        String selectDataJson = EncodeUtils.decodeUrl(selectData);
        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
            model.addAttribute("selectData", selectDataJson);
        }
        model.addAttribute("applyedIdStr", hsQwApplyPublicDetailService.getHasApplyIdStr(applyId));
        model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
        return "modules/house/hsQwPublicRentalHouseListSelectApply";
    }

    /**
     * 选择申请单对话框
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "applySelect")
    public String applySelect(HsQwApply hsQwApply, String selectData, Model model) {
        String selectDataJson = EncodeUtils.decodeUrl(selectData);
        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
            model.addAttribute("selectData", selectDataJson);
        }
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyedListSelect";
    }

    /**
     * 选择员工对话框-已完成申请的申请单
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "applyedSelect")
    public String applyedSelect(HsQwApply hsQwApply, String selectData, Model model) {
        String selectDataJson = EncodeUtils.decodeUrl(selectData);
        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
            model.addAttribute("selectData", selectDataJson);
        }
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/normal/hsQwApplyedListSelect";
    }

    /**
     * 查询代办的审批任务
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listAuditSelectData")
    @ResponseBody
    public Page<HsQwApply> listAuditSelectData(HsQwApply hsQwApply, HttpServletRequest request,
            HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        Page<HsQwApply> page = hsQwClearanceService.findClearancePage(hsQwApply);
        return page;
    }

    /**
     * 申请资格检查
     */
    @PostMapping(value = "applyCheck")
    @ResponseBody
    public String applyCheck(HsQwApply hsQwApply) {
        hsQwApplyService.applyCheck(hsQwApply);
        return renderResult(Global.TRUE, text("资格审核成功！"));
    }

    /**
     * 根据 apply_id 查询 hs_qw_apply_score_detail 表数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "getScoreDetail")
    @ResponseBody
    public List<HsQwApplyScoreDetail> getScoreDetail(String applyId, HttpServletRequest request,
            HttpServletResponse response) {
        HsQwApplyScoreDetail hsQwApplyScoreDetail = new HsQwApplyScoreDetail();
        hsQwApplyScoreDetail.setApplyId(applyId);
        List<HsQwApplyScoreDetail> page = hsQwApplyScoreDetailService.findList(hsQwApplyScoreDetail);
        return page;
    }

    /**
     * 刷新轮候得分
     */
    @RequiresPermissions("applyrule:hsQwApplyRule:edit")
    @RequestMapping(value = "refresh")
    @ResponseBody
    public String refresh(HsQwApply hsQwApply) {
        hsQwApplyService.refreshByApply(hsQwApply);
        return renderResult(Global.TRUE, text("刷新资格轮候得分成功！"));
    }

    /**
     * 修改轮候得分明细
     */
    @RequiresPermissions("applyrule:hsQwApplyRule:edit")
    @RequestMapping(value = "updateScoreDetail")
    @ResponseBody
    public String updateScoreDetail(HsQwApply hsQwApply) {
        hsQwApplyService.updateScoreDetail(hsQwApply);
        return renderResult(Global.TRUE, text("修改资格轮候得分成功！"));
    }

    /**
     * 导出数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "exportData")
    public void exportData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        List<HsQwApplyExport> list = hsQwApplyService.findExportList(hsQwApply);
        String fileName = "租赁资格轮候申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        try (ExcelExport ee = new ExcelExport("租赁资格轮候申请", HsQwApplyExport.class)) {
            ee.setDataList(list).write(response, fileName);
        }
    }


    /**
     * 导出数据-根据选择导出数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "exportDataBySelected")
    public void exportDataBySelected(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        hsQwApply.setId_in(hsQwApply.getPids().split(","));
        List<HsQwApplyExport> list = hsQwApplyService.findExportListSelected(hsQwApply);
        String fileName = "租赁资格轮候申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        try (ExcelExport ee = new ExcelExport("租赁资格轮候申请", HsQwApplyExport.class)) {
            ee.setDataList(list).write(response, fileName);
        }
    }

    /**
     * 导出数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "exportAllData")
    public void exportAllData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        List<HsQwApplyAllExport> list = hsQwApplyService.findExportAllList(hsQwApply);
        String fileName = "租赁资格轮候申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        try (ExcelExport ee = new ExcelExport("租赁资格轮候申请", HsQwApplyAllExport.class)) {
            ee.setDataList(list).write(response, fileName);
        }
    }

    /**
     * 生成选房确认单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "genHouseSelectOrder")
    public void genHouseSelectOrder(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApplyService.genHouseSelectOrder(hsQwApply, response);
    }

    /**
     * 批量生成选房确认单，结果文件用zip压缩包下载
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "genHouseSelectOrderZip")
    public void genHouseSelectOrderZip(String applyIds, HttpServletRequest request, HttpServletResponse response) {
        hsQwApplyService.genHouseSelectOrderZip(applyIds, response);
    }


}
