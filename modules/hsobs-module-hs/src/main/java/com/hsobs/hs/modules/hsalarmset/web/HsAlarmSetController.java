package com.hsobs.hs.modules.hsalarmset.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.hsalarmset.entity.HsAlarmSet;
import com.hsobs.hs.modules.hsalarmset.service.HsAlarmSetService;

/**
 * 告警设置Controller
 * <AUTHOR>
 * @version 2025-01-04
 */
@Controller
@RequestMapping(value = "${adminPath}/hsalarmset/")
public class HsAlarmSetController extends BaseController {

	@Autowired
	private HsAlarmSetService hsAlarmSetService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsAlarmSet get(String id, boolean isNewRecord) {
		return hsAlarmSetService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("hsalarmset::view")
	@RequestMapping(value = {"list", ""})
	public String list(HsAlarmSet hsAlarmSet, Model model) {
		model.addAttribute("hsAlarmSet", hsAlarmSet);
		return "modules/hsalarmset/hsAlarmSetList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("hsalarmset::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsAlarmSet> listData(HsAlarmSet hsAlarmSet, HttpServletRequest request, HttpServletResponse response) {
		hsAlarmSet.setPage(new Page<>(request, response));
		Page<HsAlarmSet> page = hsAlarmSetService.findPage(hsAlarmSet);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("hsalarmset::view")
	@RequestMapping(value = "form")
	public String form(HsAlarmSet hsAlarmSet, Model model) {
		model.addAttribute("hsAlarmSet", hsAlarmSet);
		return "modules/hsalarmset/hsAlarmSetForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("hsalarmset::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsAlarmSet hsAlarmSet) {
		hsAlarmSetService.save(hsAlarmSet);
		return renderResult(Global.TRUE, text("保存告警设置成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("hsalarmset::edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsAlarmSet hsAlarmSet) {
		hsAlarmSet.setStatus(HsAlarmSet.STATUS_DISABLE);
		hsAlarmSetService.updateStatus(hsAlarmSet);
		return renderResult(Global.TRUE, text("停用告警设置成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("hsalarmset::edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsAlarmSet hsAlarmSet) {
		hsAlarmSet.setStatus(HsAlarmSet.STATUS_NORMAL);
		hsAlarmSetService.updateStatus(hsAlarmSet);
		return renderResult(Global.TRUE, text("启用告警设置成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("hsalarmset::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsAlarmSet hsAlarmSet) {
		hsAlarmSetService.delete(hsAlarmSet);
		return renderResult(Global.TRUE, text("删除告警设置成功！"));
	}
	
}