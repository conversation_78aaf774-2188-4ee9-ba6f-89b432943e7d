package com.hsobs.hs.modules.apply.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 租赁资格轮候申请导出实体-申请单信息
 *
 * <AUTHOR>
 * @version 2025-04-10
 */
public class HsQwApplyAllExport extends HsQwApply {

    @ExcelFields({
            @ExcelField(title = "申请单编号", attrName = "id", align = Align.CENTER, sort = 10),
            @ExcelField(title = "主申请人", attrName = "mainApplyer.name", align = Align.CENTER, sort = 20),
            @ExcelField(title = "主申请人工作单位", attrName = "mainApplyer.organization", align = Align.CENTER, sort = 30),
            @ExcelField(title = "主申请人身份证", attrName = "mainApplyer.idNum", align = Align.CENTER, sort = 110),
            @ExcelField(title = "主申请人手机号", attrName = "mainApplyer.phone", align = Align.CENTER, sort = 120),
            @ExcelField(title = "申请单类型", attrName = "applyMatter", dictType = "hs_apply_matter", align = Align.CENTER, sort = 130),
            @ExcelField(title = "申请单状态", attrName = "status", dictType = "sys_status", align = Align.CENTER, sort = 140),
            @ExcelField(title = "轮候评分", attrName = "applyScore", align = Align.CENTER, sort = 150),
            @ExcelField(title = "备注", attrName = "remarks", align = Align.CENTER, sort = 160),
    })
    public HsQwApplyAllExport() {
        this(null);
    }

    public HsQwApplyAllExport(String id) {
        this.id = id;
    }


}