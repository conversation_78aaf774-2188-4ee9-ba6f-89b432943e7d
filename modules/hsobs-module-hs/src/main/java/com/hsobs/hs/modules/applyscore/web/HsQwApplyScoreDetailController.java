package com.hsobs.hs.modules.applyscore.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.applyscore.entity.HsQwApplyScoreDetail;
import com.hsobs.hs.modules.applyscore.service.HsQwApplyScoreDetailService;

/**
 * hs_qw_apply_score_detailController
 * <AUTHOR>
 * @version 2025-03-22
 */
@Controller
@RequestMapping(value = "${adminPath}/applyscore/hsQwApplyScoreDetail")
public class HsQwApplyScoreDetailController extends BaseController {

	@Autowired
	private HsQwApplyScoreDetailService hsQwApplyScoreDetailService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyScoreDetail get(String id, boolean isNewRecord) {
		return hsQwApplyScoreDetailService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("applyscore:hsQwApplyScoreDetail:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyScoreDetail hsQwApplyScoreDetail, Model model) {
		model.addAttribute("hsQwApplyScoreDetail", hsQwApplyScoreDetail);
		return "modules/applyscore/hsQwApplyScoreDetailList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("applyscore:hsQwApplyScoreDetail:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyScoreDetail> listData(HsQwApplyScoreDetail hsQwApplyScoreDetail, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyScoreDetail.setPage(new Page<>(request, response));
		Page<HsQwApplyScoreDetail> page = hsQwApplyScoreDetailService.findPage(hsQwApplyScoreDetail);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("applyscore:hsQwApplyScoreDetail:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyScoreDetail hsQwApplyScoreDetail, Model model) {
		model.addAttribute("hsQwApplyScoreDetail", hsQwApplyScoreDetail);
		return "modules/applyscore/hsQwApplyScoreDetailForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("applyscore:hsQwApplyScoreDetail:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyScoreDetail hsQwApplyScoreDetail) {
		hsQwApplyScoreDetailService.save(hsQwApplyScoreDetail);
		return renderResult(Global.TRUE, text("保存hs_qw_apply_score_detail成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("applyscore:hsQwApplyScoreDetail:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyScoreDetail hsQwApplyScoreDetail) {
		hsQwApplyScoreDetailService.delete(hsQwApplyScoreDetail);
		return renderResult(Global.TRUE, text("删除hs_qw_apply_score_detail成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("applyscore:hsQwApplyScoreDetail:view")
	@RequestMapping(value = "hsQwApplyScoreDetailSelect")
	public String hsQwApplyScoreDetailSelect(HsQwApplyScoreDetail hsQwApplyScoreDetail, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwApplyScoreDetail", hsQwApplyScoreDetail);
		return "modules/applyscore/hsQwApplyScoreDetailSelect";
	}
	
}