package com.hsobs.hs.modules.apply.service.updateStatus;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.jeesite.common.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class HsQwApplyStatusInfo implements HsQwApplyStatus {

    @Autowired
    HsQwCompactService hsQwCompactService;

    @Override
    public String getApplyMater() {
        return "1";
    }

    @Override
    public void execute(HsQwApply realBean, HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        //执行清退
        if (hsQwApply.isClear()){
            hsQwApplyService.invalidApplyInfo(realBean);
        } else {
            // 状态更新
            hsQwApplyService.realUpdateStatus(hsQwApply);
        }
    }
}
