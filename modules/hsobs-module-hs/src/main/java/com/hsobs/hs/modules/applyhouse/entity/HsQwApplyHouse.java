package com.hsobs.hs.modules.applyhouse.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.hsobs.hs.modules.external.entity.HsMapTo;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候申请人房屋情况表Entity
 * <AUTHOR>
 * @version 2024-11-21
 */
@Table(name="hs_qw_apply_house", alias="a", label="租赁资格轮候申请人房屋情况表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="apply_id", attrName="applyId", label="申请表id", queryType=QueryType.EQ),
		@Column(name="address", attrName="address", label="房屋坐落信息", queryType=QueryType.LIKE),
		@Column(name="floor_area", attrName="floorArea", label="建筑面积", isQuery=false),
		@Column(name="property_right", attrName="propertyRight", label="产权性质", isQuery=false),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyHouse extends DataEntity<HsQwApplyHouse> {
	
	private static final long serialVersionUID = 1L;
	private String applyId;		// 申请表id
	@HsMapTo("fwzlxx")
	private String address;		// 房屋坐落信息
	@HsMapTo("jzmj")
	private String floorArea;		// 建筑面积
	@HsMapTo("cqxz")
	private String propertyRight;		// 产权性质

	public HsQwApplyHouse() {
		this(null);
	}
	
	public HsQwApplyHouse(String id){
		super(id);
	}
	
//	@NotBlank(message="申请表id不能为空")
	@Size(min=0, max=64, message="申请表id长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@NotBlank(message="房屋坐落信息不能为空")
	@Size(min=0, max=100, message="房屋坐落信息长度不能超过 100 个字符")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	
	@NotBlank(message="建筑面积不能为空")
	@Size(min=0, max=20, message="建筑面积长度不能超过 20 个字符")
	public String getFloorArea() {
		return floorArea;
	}

	public void setFloorArea(String floorArea) {
		this.floorArea = floorArea;
	}
	
	@NotBlank(message="产权性质不能为空")
	@Size(min=0, max=10, message="产权性质长度不能超过 10 个字符")
	public String getPropertyRight() {
		return propertyRight;
	}

	public void setPropertyRight(String propertyRight) {
		this.propertyRight = propertyRight;
	}
	
}