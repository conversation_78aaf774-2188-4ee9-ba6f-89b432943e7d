package com.hsobs.hs.modules.apply.web.adjust;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 个人变更申请Controller
 *
 * <AUTHOR>
 * @version 2025-1-6
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/hsQwApplyReplace")
public class HsQwApplyReplaceController extends BaseController {

    @Autowired
    private HsQwApplyService hsQwApplyService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public HsQwApply get(String id, boolean isNewRecord) {
        return hsQwApplyService.get(id, isNewRecord);
    }


    /**
     * 查询列表-承租房信息修改代办
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = {"listReplace", ""})
    public String listReplace(HsQwApply hsQwApply, Model model, boolean isAdd) {
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("isAdd", isAdd);
        return "modules/apply/replace/hsQwApplyReplaceList";
    }

    /**
     * 查询列表-承租房信息已处理
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = {"listReplaceDone", ""})
    public String listReplaceDone(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/replace/hsQwApplyReplaceDoneList";
    }


//    /**
//     * 租房个人承租信息变更申请单(平换)
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formReplaceMzt")
//    public String formReplaceMzt(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApplyService.getUserApplyInfo( "4"));
//        return "modules/apply/replace/hsQwApplyReplaceForm";
//    }

    /**
     * 租房个人承租信息变更申请单(平换)
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formReplace")
    public String formReplace(HsQwApply hsQwApply, Model model) {
        if (hsQwApply.getIsNewRecord()){
            model.addAttribute("hsQwApply", hsQwApplyService.getUserApplyInfo( "4", hsQwApply));
        }else {
            model.addAttribute("hsQwApply", hsQwApply);
        }
        return "modules/apply/replace/hsQwApplyReplaceForm";
    }

    /**
     * 租房个人承租信息变更申请单(平换)-核验表单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "form")
    public String form(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/replace/hsQwApplyReplaceForm";
    }

    /**
     * 租房个人承租信息变更申请单(平换)-房源选择
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formReplaceHouse")
    public String formReplaceHouse(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/replace/hsQwApplyReplaceFormHouse";
    }


    /**
     * 租房个人承租信息变更申请单(平换)-合同签订
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "formReplaceCompact")
    public String formReplaceCompact(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/replace/hsQwApplyReplaceFormCompact";
    }

    /**
     * 查询代办的审批任务-承租房信息代办
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listAuditReplaceData")
    @ResponseBody
    public Page<HsQwApply> listAuditReplaceData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTaskNew(hsQwApply, null, "1", "rent_apply_house");
        return page;
    }

    /**
     * 查询已办的审批任务-承租房信息已办
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listAuditedReplaceData")
    @ResponseBody
    public Page<HsQwApply> listAuditedChangeData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTaskNew(hsQwApply, null, "2", "rent_apply_house");
        return page;
    }

    /**
     * 保存数据
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @PostMapping(value = "save")
    @ResponseBody
    public String save(HsQwApply hsQwApply) {
        hsQwApplyService.save(hsQwApply);
        return renderResult(Global.TRUE, text("保存个人调租申请成功！"));
    }

    /**
     * 选择房源对话框
     */
    @RequiresPermissions("apply:hsQwApply:edit")
    @RequestMapping(value = "houseSelect")
    public String houseSelect(HsQwPublicRentalHouse hsQwPublicRentlHouse, String selectData, Model model, String applyId) {
        String selectDataJson = EncodeUtils.decodeUrl(selectData);
        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
            model.addAttribute("selectData", selectDataJson);
        }
        model.addAttribute("hsQwPublicReplacealHouse", hsQwPublicRentlHouse);
        return "modules/house/hsQwPublicRentalHouseListSelectApply";
    }

}