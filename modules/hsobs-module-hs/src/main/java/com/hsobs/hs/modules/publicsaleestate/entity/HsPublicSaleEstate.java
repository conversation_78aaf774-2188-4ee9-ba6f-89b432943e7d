package com.hsobs.hs.modules.publicsaleestate.entity;

import javax.validation.constraints.Size;

import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 公有住房配售房屋Entity
 * <AUTHOR>
 * @version 2025-02-26
 */
@Table(name="hs_public_sale_estate", alias="a", label="公有住房配售房屋信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="status", attrName="status", label="status", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="create_by", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="create_date", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="update_by", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="update_date", isQuery=false, isUpdateForce=true),
		@Column(name="structure_type", attrName="structureType", label="结构类型"),
		@Column(name="apply_id", attrName="applyId", label="申请id", isUpdateForce=true),
		@Column(name="house_id", attrName="houseId", label="房源id", isUpdateForce=true),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "h",
				on = "h.id = a.house_id", attrName="house",
				columns = {@Column(includeEntity = HsQwPublicRentalHouse.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "hre",
				on = "hre.id = h.estate_id ", attrName="house.estate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)})
	}, orderBy="a.update_date DESC"
)
public class HsPublicSaleEstate extends DataEntity<HsPublicSaleEstate> {
	
	private static final long serialVersionUID = 1L;
	private String structureType;		// 结构类型
	private String applyId;		// 申请id
	private String houseId;		// 房源id
	private HsQwPublicRentalHouse house;

	public HsPublicSaleEstate() {
		this(null);
	}

	public HsPublicSaleEstate(String id){
		super(id);
	}

	@Size(min=0, max=64, message="结构类型长度不能超过 64 个字符")
	public String getStructureType() {
		return structureType;
	}

	public void setStructureType(String structureType) {
		this.structureType = structureType;
	}

	@Size(min=0, max=64, message="申请id长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}

	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}

	public HsQwPublicRentalHouse getHouse() {
		return house;
	}

	public void setHouse(HsQwPublicRentalHouse house) {
		this.house = house;
	}
}