package com.hsobs.hs.modules.dataintelligence.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceTotal;
import com.jeesite.common.mybatis.mapper.SqlMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计DAO接口  总房源信息统计
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataIntelligenceTotalDao extends CrudDao<DataIntelligenceTotal> {
    //房源类型（0公租房 1限价房 2公用住房 3局直公房 4自管公房）
    //String otherWhere, String sqlOrderBy
    List<Map<String, Object>> countHouseTotal(Map<String, Object> map);

    List<Map<String, Object>> countHouseTotal2(String otherWhere, String sqlOrderBy);

    List<Map<String, Object>> countHouseTotalByCity(String otherWhere, String sqlOrderBy);
}