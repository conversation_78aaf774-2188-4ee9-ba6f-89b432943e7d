package com.hsobs.hs.modules.publicapply.entity;

import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 租赁公租房房源房源信息表Entity
 *
 * <AUTHOR>
 * @version 2024-11-20
 */
@Table(name = "hs_qw_public_rental_house", alias = "a", label = "租赁公租房房源房源信息表信息", columns = {
		@Column(name = "id", attrName = "id", label = "编号", isPK = true),
		@Column(name = "estate_id", attrName = "estateId", label = "楼盘编号"),
		@Column(name = "location", attrName = "location", label = "楼盘位置"),
		@Column(name = "building_num", attrName = "buildingNum", label = "楼号"),
		@Column(name = "house_num", attrName = "houseNum", label = "住房编号", isQuery = false),
		@Column(name = "floor", attrName = "floor", label = "所处楼层", isQuery = false),
		@Column(name = "building_area", attrName = "buildingArea", label = "建筑面积", isQuery = false),
		@Column(name = "house_type", attrName = "houseType", label = "户型", isQuery = false),
		@Column(name = "unit_num", attrName = "unitNum", label = "单元号"),
		@Column(name = "file_plan", attrName = "filePlan", label = "平面图图片文件编号", isQuery = false),
		@Column(name = "vr_info", attrName = "vrInfo", label = "VR信息", isQuery = false),
		@Column(name = "is_public", attrName = "isPublic", label = "是否发布", comment = "是否发布（0未发布 1已发布）"),
		@Column(includeEntity = DataEntity.class),
		@Column(name = "type", attrName = "type", label = "房源类型", comment = "房源类型（0公租房 1限价房 2公用住房 3局直公房 4自管公房）"),
		@Column(name = "house_status", attrName = "houseStatus", label = "房屋状态", comment = "房屋状态（0待配租 1已配租 2待售 3已售）"),
}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "o",
				on = "o.id = a.estate_id", attrName="estate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)})
}, orderBy = "a.update_date DESC"
)
public class HsPublicApplyHouse extends DataEntity<HsPublicApplyHouse> {

	@ExcelFields({
			@ExcelField(title = "楼盘名称", attrName = "house.estate.name", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "楼盘地址", attrName = "house.estate.address", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "楼号", attrName = "house.buildingNum", align = ExcelField.Align.CENTER, sort = 40),
			@ExcelField(title = "住房编号", attrName = "house.houseNum", align = ExcelField.Align.CENTER, sort = 50),
			@ExcelField(title = "所处楼层", attrName = "house.floor", align = ExcelField.Align.CENTER, sort = 60),
			@ExcelField(title = "单元号", attrName = "house.unitNum", align = ExcelField.Align.CENTER, sort = 70),
			@ExcelField(title = "建筑面积", attrName = "house.buildingArea", align = ExcelField.Align.CENTER, sort = 70),
			@ExcelField(title = "户型", attrName = "house.houseType", align = ExcelField.Align.CENTER, sort = 70),
	})

	private static final long serialVersionUID = 1L;
	private String estateId;        // 楼盘编号
	private String location;        // 楼盘位置
	private String buildingNum;        // 楼号
	private String houseNum;        // 住房编号
	private Long floor;        // 所处楼层
	private String buildingArea;        // 建筑面积
	private String houseType;        // 户型
	private String unitNum;        // 单元号
	private String filePlan;        // 平面图图片文件编号
	private String vrInfo;        // VR信息
	private String isPublic;        // 是否发布（0未发布 1已发布）
	private String type;        // 房源类型（0公租房 1限价房 2公用住房 3局直公房 4自管公房）
	private String houseStatus;        // 房屋状态（0待配租 1已配租 2待售 3已售）
	private String applyedIdStr; //已配租的房源ids
	private HsQwPublicRentalEstate estate;

	private String recordId;
	private String flowStatus;

	public HsPublicApplyHouse() {
		this(null);
	}

	public HsPublicApplyHouse(String id) {
		super(id);
	}

	@NotBlank(message = "楼盘编号不能为空")
	@Size(min = 0, max = 64, message = "楼盘编号长度不能超过 64 个字符")
	public String getEstateId() {
		return estateId;
	}

	public void setEstateId(String estateId) {
		this.estateId = estateId;
	}

	@Size(min = 0, max = 50, message = "楼盘位置长度不能超过 50 个字符")
	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	@NotBlank(message = "楼号不能为空")
	@Size(min = 0, max = 10, message = "楼号长度不能超过 10 个字符")
	public String getBuildingNum() {
		return buildingNum;
	}

	public void setBuildingNum(String buildingNum) {
		this.buildingNum = buildingNum;
	}

	@NotBlank(message = "住房编号不能为空")
	@Size(min = 0, max = 50, message = "住房编号长度不能超过 50 个字符")
	public String getHouseNum() {
		return houseNum;
	}

	public void setHouseNum(String houseNum) {
		this.houseNum = houseNum;
	}

	@NotNull(message = "所处楼层不能为空")
	public Long getFloor() {
		return floor;
	}

	public void setFloor(Long floor) {
		this.floor = floor;
	}

	@NotBlank(message = "建筑面积不能为空")
	@Size(min = 0, max = 10, message = "建筑面积长度不能超过 10 个字符")
	public String getBuildingArea() {
		return buildingArea;
	}

	public void setBuildingArea(String buildingArea) {
		this.buildingArea = buildingArea;
	}

	@Size(min = 0, max = 10, message = "户型长度不能超过 10 个字符")
	public String getHouseType() {
		return houseType;
	}

	public void setHouseType(String houseType) {
		this.houseType = houseType;
	}

	@NotBlank(message = "单元号不能为空")
	@Size(min = 0, max = 10, message = "单元号长度不能超过 10 个字符")
	public String getUnitNum() {
		return unitNum;
	}

	public void setUnitNum(String unitNum) {
		this.unitNum = unitNum;
	}

	@Size(min = 0, max = 64, message = "平面图图片文件编号长度不能超过 64 个字符")
	public String getFilePlan() {
		return filePlan;
	}

	public void setFilePlan(String filePlan) {
		this.filePlan = filePlan;
	}

	@Size(min = 0, max = 255, message = "VR信息长度不能超过 255 个字符")
	public String getVrInfo() {
		return vrInfo;
	}

	public void setVrInfo(String vrInfo) {
		this.vrInfo = vrInfo;
	}

	@NotBlank(message = "是否发布不能为空")
	@Size(min = 0, max = 1, message = "是否发布长度不能超过 1 个字符")
	public String getIsPublic() {
		return isPublic;
	}

	public void setIsPublic(String isPublic) {
		this.isPublic = isPublic;
	}

	@Size(min = 0, max = 255, message = "房源类型长度不能超过 255 个字符")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Size(min = 0, max = 255, message = "房屋状态长度不能超过 255 个字符")
	public String getHouseStatus() {
		return houseStatus;
	}

	public void setHouseStatus(String houseStatus) {
		this.houseStatus = houseStatus;
	}

	public Date getCreateDate_gte() {
		return sqlMap.getWhere().getValue("create_date", QueryType.GTE);
	}

	public void setCreateDate_gte(Date createDate) {
		sqlMap.getWhere().and("create_date", QueryType.GTE, createDate);
	}

	public Date getCreateDate_lte() {
		return sqlMap.getWhere().getValue("create_date", QueryType.LTE);
	}

	public void setCreateDate_lte(Date createDate) {
		sqlMap.getWhere().and("create_date", QueryType.LTE, createDate);
	}

	public HsQwPublicRentalEstate getEstate() {
		return estate;
	}

	public void setEstate(HsQwPublicRentalEstate estate) {
		this.estate = estate;
	}

	public String getApplyedIdStr() {
		return applyedIdStr;
	}

	public void setApplyedIdStr(String applyedIdStr) {
		this.applyedIdStr = applyedIdStr;
	}

	public String getRecordId() {
		return this.recordId;
	}

	public void setRecordId(String recordId) {
		this.recordId = recordId;
	}

	public String getFlowStatus() {
		return this.flowStatus;
	}

	public void setFlowStatus(String flowStatus) {
		this.flowStatus = flowStatus;
	}
}