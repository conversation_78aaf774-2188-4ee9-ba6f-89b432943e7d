package com.hsobs.hs.modules.province.service;

import java.util.List;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.province.entity.HsQwApplyProvince;
import com.hsobs.hs.modules.province.dao.HsQwApplyProvinceDao;

/**
 * 省直单位自管公房申请表Service
 * <AUTHOR>
 * @version 2025-02-19
 */
@Service
public class HsQwApplyProvinceService extends CrudService<HsQwApplyProvinceDao, HsQwApplyProvince> {

	@Autowired
	private HsQwApplyerService hsQwApplyerService;

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;
	/**
	 * 获取单条数据
	 * @param hsQwApplyProvince
	 * @return
	 */
	@Override
	public HsQwApplyProvince get(HsQwApplyProvince hsQwApplyProvince) {
		HsQwApplyProvince entity =  super.get(hsQwApplyProvince);
		HsQwApplyer hsQwApplyer = new HsQwApplyer();
		hsQwApplyer.setApplyId(entity.getId());
		hsQwApplyer.setStatus(HsQwApplyer.STATUS_NORMAL);
		entity.setHsQwApplyerList(hsQwApplyerService.findList(hsQwApplyer));
		return entity;
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyProvince 查询条件
	 * @param hsQwApplyProvince page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyProvince> findPage(HsQwApplyProvince hsQwApplyProvince) {
		return super.findPage(hsQwApplyProvince);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyProvince
	 * @return
	 */
	@Override
	public List<HsQwApplyProvince> findList(HsQwApplyProvince hsQwApplyProvince) {
		return super.findList(hsQwApplyProvince);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyProvince
	 */
	@Override
	@Transactional
	public void save(HsQwApplyProvince hsQwApplyProvince) {
		// 检查申请人信息是否提交
		this.checkApplyer(hsQwApplyProvince.getHsQwApplyerList());
		super.save(hsQwApplyProvince);
		// 保存 HsQwApply子表
		for (HsQwApplyer hsQwApplyer : hsQwApplyProvince.getHsQwApplyerList()) {
			if (!HsQwApplyer.STATUS_DELETE.equals(hsQwApplyer.getStatus())) {
				hsQwApplyer.setApplyId(hsQwApplyProvince.getId());
				if (StringUtils.isBlank(hsQwApplyer.getUserId())){
					hsQwApplyer.setUserId("0");//先默认值
				}
				if (hsQwApplyer.getIsNewRecord()) {
					hsQwApplyerService.insert(hsQwApplyer);
				} else {
					hsQwApplyerService.update(hsQwApplyer);
				}
			} else {
				hsQwApplyerService.delete(hsQwApplyer);
			}
		}
		//更新房源状态
		this.updateHouseStatus(hsQwApplyProvince.getHouseId(), "1");
	}

	private void checkApplyer(List<HsQwApplyer> hsQwApplyerList) {
		//判断是否含有主申请人信息
		final boolean[] hasMain = {false, false};
		hsQwApplyerList.forEach(k -> {
			hasMain[0] = hasMain[0] || k.getApplyRole().equals("0");
			hasMain[1] = hasMain[1] || k.getApplyRole().equals("1");
		});
		if (!hasMain[0]) {
			throw new ServiceException("未添加主申请人信息");
		}
		if (!hasMain[1]) {
			throw new ServiceException("未添加配偶信息");
		}

	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyProvince
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyProvince hsQwApplyProvince) {
		HsQwApplyProvince realBean = this.get(hsQwApplyProvince.getId());
		//状态更新
		super.updateStatus(hsQwApplyProvince);

		//若已完成配租，则需要更新房源配租状态
		if (hsQwApplyProvince.getStatus().equals(HsQwApply.STATUS_NORMAL)){
			this.updateHouseStatus(realBean.getHouseId(), "1");//流程结束，已配租
		} else if (StringUtils.isNotBlank(hsQwApplyProvince.getHouseId())){
			this.updateHouseStatus(realBean.getHouseId(), "0");//流程异常，恢复待配租
		}
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyProvince
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyProvince hsQwApplyProvince) {
		super.delete(hsQwApplyProvince);
	}
	/**
	 * 执行用户腾退操作
	 * @param hsQwApplyProvince
	 */
	@Transactional
	public void clear(HsQwApplyProvince hsQwApplyProvince) {
		hsQwApplyProvince.setStatus("2");
		this.updateStatus(hsQwApplyProvince);
		//房源重置为待配租
		this.updateHouseStatus(hsQwApplyProvince.getHouseId(), "0");
	}

	public void updateHouseStatus(String houseId, String status) {
		HsQwPublicRentalHouse hsQwApplyHouse = hsQwPublicRentalHouseService.get(houseId);
		hsQwApplyHouse.setHouseStatus(status);
		hsQwPublicRentalHouseService.update(hsQwApplyHouse);
	}
}