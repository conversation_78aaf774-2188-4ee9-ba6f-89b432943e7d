package com.hsobs.hs.modules.apply.service.applyRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;

import java.util.Map;

/**
 * 申请人需为在榕省直单位或中央驻榕单位的在职、退休干部职工，或服务满5年的合同制员工。
 * 申请人及其家庭成员在福州市城区及所在县（市、区）无房产，或人均住房面积不超过15平方米。
 * 申请人及其家庭成员未曾购买政策性住房。
 * 若申请人再婚且前配偶曾拥有政策性住房但已分割或转移，离婚时间需超过3年。
 * 存在以下情况之一者不符合申请资格：
 * 申请人及共同申请人在申请之日前3年内将拥有的福州市城区和单位主体所在县（市、区）范围内的私有住房产权转移的（不含该房产合并计算后家庭人均住房建筑面积在15平方米以下和配偶婚前转移非政策性住房的情形）
 * 申请人曾经离异，以前配偶名义购买过政策性住房；或与前配偶共有的非政策性住房在离异时分割给前配偶，但离异时间不足3年的。
 * 申请人及共同申请人已领取征收公有住房货币补偿款的。
 * 申请人及共同申请人已承租单位自管公房，或福州市的公共租赁住房、保障性租赁住房，或已享受租金优惠、租金补助政策的。
 */
public interface HsQwApplyRuleMethod {

    void execute(HsQwApply hsQwApply, Map<String, HsQwFormAlarm> hsQwFormAlarmMap);

    /**
     * 设置告警信息，若存在补充信息，则更新告警信息
     * 
     * @param hsQwFormAlarmMap
     * @param key
     * @param value
     */
    static void putAlarmMap(Map<String, HsQwFormAlarm> hsQwFormAlarmMap, String key, String value, String alarmType, String objectId) {
        HsQwFormAlarm formAlarm = hsQwFormAlarmMap.get(key);
        if (formAlarm == null) {
            formAlarm = new HsQwFormAlarm();
            formAlarm.setObjectId(objectId);
            formAlarm.setAlarmType(alarmType);
            formAlarm.setAttrKey(key);
            formAlarm.setAlarmInfo(getTitle(alarmType) + "• " + value);
            hsQwFormAlarmMap.put(key, formAlarm);
        } else {
            // 获取value值，进行换行补充，前面加上分点符号
            formAlarm.setAlarmInfo(formAlarm.getAlarmInfo() + "\n" + "• " + value);
        }
    }

    static String getTitle(String alarmType) {
        if (alarmType.equals("0")){
            return "合规性校验：\n";
        } else if (alarmType.equals("1")){
            return "变更信息记录：\n";
        }
        return "黑名单用户提醒：\n";
    }
}
