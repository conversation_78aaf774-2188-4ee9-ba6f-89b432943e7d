package com.hsobs.hs.modules.apply.service.HsQwApplyProcess.rental;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.utils.BpmUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class HsQwApplyProcessApply extends HsQwApplyProcessDefault {
    @Override
    public String getStatus() {
        return "申请提交";
    }

    @Override
    public void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        //判断是否含有主申请人信息
        final int[] mainNum = {0};
        hsQwApply.getHsQwApplyerList().forEach(k -> {
            if (k.getApplyRole().equals("0")){
                mainNum[0]++;
            }
        });
        if (mainNum[0] == 0) {
            throw new ServiceException("未添加主申请人信息");
        }
        if (mainNum[0] > 1) {
            throw new ServiceException("主申请人只能有一个");
        }
        this.process(hsQwApply, hsQwApplyService);
//        this.drumpFirst(hsQwApply);
    }

    private HsQwApply initBean(String applyedId, HsQwApplyService hsQwApplyService) {
        HsQwApply hsQwApply = hsQwApplyService.get(applyedId);
        hsQwApply.setId(null);
        hsQwApply.setApplyMatter("2");
        hsQwApply.setApplyTime(null);
        hsQwApply.setApplyAccepted(null);
        hsQwApply.setApplyedId(applyedId);
        hsQwApply.setHouseId(null);
        return hsQwApply;
    }

    public void drumpFirst(HsQwApply hsQwApply){
        // 指定流程变量，作为流程条件，决定流转方向
        Map<String, Object> variables = MapUtils.newHashMap();
        variables.put("officeCode", hsQwApply.getOfficeCode());
        BpmUtils.complete(hsQwApply, variables, null);
    }
}
