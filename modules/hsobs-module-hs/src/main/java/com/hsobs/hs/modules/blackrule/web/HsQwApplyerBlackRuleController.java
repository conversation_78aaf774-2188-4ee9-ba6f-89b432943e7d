package com.hsobs.hs.modules.blackrule.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.blackrule.entity.HsQwApplyerBlackRule;
import com.hsobs.hs.modules.blackrule.service.HsQwApplyerBlackRuleService;

/**
 * 租赁资格轮候租户黑名单规则表Controller
 * <AUTHOR>
 * @version 2024-12-19
 */
@Controller
@RequestMapping(value = "${adminPath}/blackrule/hsQwApplyerBlackRule")
public class HsQwApplyerBlackRuleController extends BaseController {

	@Autowired
	private HsQwApplyerBlackRuleService hsQwApplyerBlackRuleService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyerBlackRule get(String id, boolean isNewRecord) {
		return hsQwApplyerBlackRuleService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("blackrule:hsQwApplyerBlackRule:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyerBlackRule hsQwApplyerBlackRule, Model model) {
		model.addAttribute("hsQwApplyerBlackRule", hsQwApplyerBlackRule);
		return "modules/blackrule/hsQwApplyerBlackRuleList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("blackrule:hsQwApplyerBlackRule:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyerBlackRule> listData(HsQwApplyerBlackRule hsQwApplyerBlackRule, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyerBlackRule.setPage(new Page<>(request, response));
		Page<HsQwApplyerBlackRule> page = hsQwApplyerBlackRuleService.findPage(hsQwApplyerBlackRule);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("blackrule:hsQwApplyerBlackRule:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyerBlackRule hsQwApplyerBlackRule, Model model) {
		model.addAttribute("hsQwApplyerBlackRule", hsQwApplyerBlackRule);
		return "modules/blackrule/hsQwApplyerBlackRuleForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("blackrule:hsQwApplyerBlackRule:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyerBlackRule hsQwApplyerBlackRule) {
		hsQwApplyerBlackRuleService.save(hsQwApplyerBlackRule);
		return renderResult(Global.TRUE, text("保存租赁资格轮候租户黑名单规则表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("blackrule:hsQwApplyerBlackRule:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyerBlackRule hsQwApplyerBlackRule) {
		hsQwApplyerBlackRuleService.delete(hsQwApplyerBlackRule);
		return renderResult(Global.TRUE, text("删除租赁资格轮候租户黑名单规则表成功！"));
	}
	
}