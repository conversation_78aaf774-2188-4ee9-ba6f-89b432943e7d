package com.hsobs.hs.modules.blackuser.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.service.HsQwApplyerBlackService;

/**
 * 租赁资格轮候租户黑名单Controller
 * <AUTHOR>
 * @version 2024-12-19
 */
@Controller
@RequestMapping(value = "${adminPath}/blackuser/hsQwApplyerBlack")
public class HsQwApplyerBlackController extends BaseController {

	@Autowired
	private HsQwApplyerBlackService hsQwApplyerBlackService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyerBlack get(String id, boolean isNewRecord) {
		return hsQwApplyerBlackService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("blackuser:hsQwApplyerBlack:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyerBlack hsQwApplyerBlack, Model model) {
		model.addAttribute("hsQwApplyerBlack", hsQwApplyerBlack);
		return "modules/blackuser/hsQwApplyerBlackList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("blackuser:hsQwApplyerBlack:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyerBlack> listData(HsQwApplyerBlack hsQwApplyerBlack, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyerBlack.setPage(new Page<>(request, response));
		Page<HsQwApplyerBlack> page = hsQwApplyerBlackService.findPage(hsQwApplyerBlack);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("blackuser:hsQwApplyerBlack:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyerBlack hsQwApplyerBlack, Model model) {
		model.addAttribute("hsQwApplyerBlack", hsQwApplyerBlack);
		return "modules/blackuser/hsQwApplyerBlackForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("blackuser:hsQwApplyerBlack:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyerBlack hsQwApplyerBlack) {
		hsQwApplyerBlackService.save(hsQwApplyerBlack);
		return renderResult(Global.TRUE, text("保存租赁资格轮候租户黑名单成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("blackuser:hsQwApplyerBlack:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsQwApplyerBlack hsQwApplyerBlack) {
		hsQwApplyerBlack.setStatus(HsQwApplyerBlack.STATUS_DISABLE);
		hsQwApplyerBlackService.updateStatus(hsQwApplyerBlack);
		return renderResult(Global.TRUE, text("停用租赁资格轮候租户黑名单成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("blackuser:hsQwApplyerBlack:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsQwApplyerBlack hsQwApplyerBlack) {
		hsQwApplyerBlack.setStatus(HsQwApplyerBlack.STATUS_NORMAL);
		hsQwApplyerBlackService.updateStatus(hsQwApplyerBlack);
		return renderResult(Global.TRUE, text("启用租赁资格轮候租户黑名单成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("blackuser:hsQwApplyerBlack:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyerBlack hsQwApplyerBlack) {
		hsQwApplyerBlackService.delete(hsQwApplyerBlack);
		return renderResult(Global.TRUE, text("删除租赁资格轮候租户黑名单成功！"));
	}
	
}