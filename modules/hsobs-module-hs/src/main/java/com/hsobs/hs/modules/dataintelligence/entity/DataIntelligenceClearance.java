package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;

import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 住房保障数据统计Entity  公租房清退统计
 * <AUTHOR>
 * @version 2025-1-2
 */
public class DataIntelligenceClearance extends DataEntity<DataIntelligenceClearance> {

	@ExcelFields({
			@ExcelField(title = "小区名称", attrName = "estateName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "公租房总数", attrName = "totalCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "租赁到期户数", attrName = "arrearsCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "租金拖欠户数", attrName = "maturityCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "违规户数", attrName = "violationsCount", align = ExcelField.Align.CENTER, sort = 40)
	})

	private static final long serialVersionUID = 1L;
	private String parentCodeTree;

	private String city;
	private String area;

	private String officeCode;		// 单位编码
	private Date startDate;		//开始日期
	private Date endDate;	//结束日期
	private Integer compareType;

	private String estateId;	// 小区id
	private String estateName;	// 小区名称
	private Long totalCount;		// 公租房总数
	private Long arrearsCount;		// 租赁到期户数
	private Long maturityCount;		// 租金拖欠户数
	private Long violationsCount;	// 违规数

	public DataIntelligenceClearance() {
		this(null);
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			endDate = new Date();
			startDate = dateFormat.parse(String.format("%04d-01-01", endDate.getYear()+1900));  // 解析指定日期字符串
		} catch (Exception e) {
		}
		parentCodeTree = "";
		if(!UserUtils.getUser().isAdmin()){
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		else{
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		compareType = 19;
	}
	
	public DataIntelligenceClearance(String id){
		super(id);
	}

	public String getParentCodeTree() {
		return parentCodeTree;
	}

	public void setParentCodeTree(String parentCodeTree) {
		this.parentCodeTree = parentCodeTree;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getCompareType() {
		return compareType;
	}

	public void setCompareType(Integer compareType) {
		this.compareType = compareType;
	}

	public String getEstateId() {
		return estateId;
	}

	public void setEstateId(String estateId) {
		this.estateId = estateId;
	}

	public String getEstateName() {
		return estateName;
	}

	public void setEstateName(String estateName) {
		this.estateName = estateName;
	}

	public Long getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}

	public Long getArrearsCount() {
		return arrearsCount;
	}

	public void setArrearsCount(Long arrearsCount) {
		this.arrearsCount = arrearsCount;
	}

	public Long getMaturityCount() {
		return maturityCount;
	}

	public void setMaturityCount(Long maturityCount) {
		this.maturityCount = maturityCount;
	}

	public Long getViolationsCount() {
		return violationsCount;
	}

	public void setViolationsCount(Long violationsCount) {
		this.violationsCount = violationsCount;
	}
}