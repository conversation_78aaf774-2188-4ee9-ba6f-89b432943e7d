//package com.hsobs.hs.modules.utils;
//
//import com.hsobs.hs.modules.apply.entity.HsQwApply;
//import com.jeesite.common.entity.Page;
//import com.jeesite.common.lang.StringUtils;
//import com.jeesite.common.mybatis.mapper.query.QueryType;
//import com.jeesite.common.reflect.ReflectUtils;
//import com.jeesite.common.service.CrudService;
//import com.jeesite.modules.bpm.entity.BpmEntity;
//import com.jeesite.modules.bpm.entity.BpmProcIns;
//import com.jeesite.modules.bpm.entity.BpmTask;
//import com.jeesite.modules.bpm.service.BpmTaskService;
//import com.jeesite.modules.bpm.service.support.HsBpmTask;
//import com.jeesite.modules.bpm.utils.BpmUtils;
//import com.jeesite.modules.sys.utils.UserUtils;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * 公共处理流程任务查询
// * @param <T>
// */
//public class HsBpmProcService<T extends BpmEntity> {
//
//    CrudService crudService;
//
//    public HsBpmProcService setCrudService(CrudService crudService){
//        this.crudService = crudService;
//        return this;
//    }
//
//    BpmTaskService bpmTaskService = BpmUtils.getBpmTaskService();
//
//    public Page<T> findApplyPageByTask(T hsBean, String[] status, String bpmStatus, String formKey) {
//        HsBpmTask params = new HsBpmTask();
//        params.setStatus(bpmStatus);
//        Page<BpmProcIns> myHsTaskPage = this.getAllTask(params, status, ReflectUtils.invokeGetter(hsBean, "applyStatus"), formKey);
//        //获取所有待办任务的申请单id
//        if (!this.getIdsByTask(myHsTaskPage, hsBean, new ArrayList<>())) {
//            Page<T> hsQwApplyPage = crudService.findPage(hsBean);
//            return this.getTaskResult(hsQwApplyPage, myHsTaskPage);
//        } else {
//            return hsBean.getPage();
//        }
//    }
//
//    private Page<T> getTaskResult(Page<T> hsBeanPage, Page<BpmTask> myHsTaskPage) {
//        hsBeanPage.getList().forEach(k -> this.setTaskInfo(k,k.getId(),myHsTaskPage));
//        return hsBeanPage;
//    }
//
//    private void setTaskInfo(T k, String id, Page<BpmTask> myHsTaskPage) {
//        for (BpmTask bpmTask : myHsTaskPage.getList()) {
//            if (bpmTask.getProcIns().getBizKey().equals(id)) {
//                ReflectUtils.invokeSetter(k,"applyTitle", bpmTask.getProcIns().getName());
//                ReflectUtils.invokeSetter(k,"applyStatus", bpmTask.getName());
//                ReflectUtils.invokeSetter(k,"taskId", bpmTask.getId());
//                return;
//            }
//        }
//    }
//
//    private Page<BpmTask> getAllTask(BpmProcIns params, String[] resNames, String applyStatus, String formKey) {
//        //查询我的代办rent_apply任务
//        params.setUserCode(params.currentUser().getUserCode());
//        params.setFormKey(formKey);//动态设置formKey
//        if (resNames != null && resNames.length > 0) {
//            params.setNames(Arrays.stream(resNames).collect(Collectors.toList()));
//        }
//        if (StringUtils.isNotBlank(applyStatus)) {
//            params.setName(applyStatus);
//        }
//        return this.bpmTaskService.findTaskPage(params);
//    }
//
//    private boolean getIdsByTask(Page<BpmTask> pageTask, T hsQwApply, List<String> hsIds) {
//        pageTask.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
//        hsQwApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
//        return hsIds.isEmpty();
//    }
//
//    public Page<T> findMztPage(T hsBean, String formKey) {
//        HsBpmTask params = new HsBpmTask();
////        params.setStatus("1");
//        Page<BpmTask> myHsTaskPage = this.getHsTask(params, null, ReflectUtils.invokeGetter(hsBean, "applyStatus"), formKey);
//        //获取所有待办任务的申请单id
//        List<String> hsIds  = this.getIdsByDraft(hsBean);
//        if (!this.getIdsByTask(myHsTaskPage, hsBean, hsIds)) {
//            Page<T> hsQwApplyPage = crudService.findPage(hsBean);
//            return this.getTaskResult(hsQwApplyPage, myHsTaskPage);
//        } else {
//            return hsBean.getPage();
//        }
//    }
//
//
//    private List<String> getIdsByDraft(T hsQwApply) {
//        HsQwApply query = new HsQwApply();
//        query.setStatus(HsQwApply.STATUS_DRAFT);
//        query.sqlMap().getWhere().disableAutoAddStatusWhere();
//        query.setCreateBy(UserUtils.getUser().getUserCode());
//        List<HsQwApply> list = crudService.findList(query);
//        List<String> hsIds = new ArrayList<>();
//        list.forEach((k -> hsIds.add(k.getId())));
//        return hsIds;
//    }
//
//}
