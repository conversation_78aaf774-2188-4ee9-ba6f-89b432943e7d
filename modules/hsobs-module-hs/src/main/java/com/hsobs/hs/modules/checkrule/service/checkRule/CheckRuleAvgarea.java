package com.hsobs.hs.modules.checkrule.service.checkRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;
import org.springframework.stereotype.Service;

/**
 * 人均面积要求
 * 申请人及其家庭成员在福州市城区及所在县（市、区）无房产，或人均住房面积不超过15平方米。
 */
@Service
public class CheckRuleAvgarea implements ICheckRule {
    @Override
    public CheckRuleResult execute(HsQwApply hsQwApply, HsQwCheckRule rule) {
        return CheckRuleResult.success("成功");
    }
}
