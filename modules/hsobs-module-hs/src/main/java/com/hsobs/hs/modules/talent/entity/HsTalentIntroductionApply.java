package com.hsobs.hs.modules.talent.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

/**
 * 人才补助申请表Entity
 * <AUTHOR>
 * @version 2025-01-03
 */
@Table(name="hs_talent_introduction_apply", alias="a", label="人才补助申请表信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="unit_id", attrName="unitId", label="申请单位"),
		@Column(name="apply_name", attrName="applyName", label="申请人姓名", queryType=QueryType.LIKE),
		@Column(name="apply_tel", attrName="applyTel", label="申请人电话", queryType=QueryType.LIKE),
		@Column(name="apply_no", attrName="applyNo", label="申请人身份证号码", queryType=QueryType.LIKE),
		@Column(name="edu_back", attrName="eduBack", label="学历"),
		@Column(name="title_id", attrName="titleId", label="职称"),
		@Column(name="talent_type", attrName="talentType", label="人才类型"),
		@Column(name="talent_level", attrName="talentLevel", label="人才等级"),
		@Column(name="arrival_time", attrName="arrivalTime", label="引进时间", isUpdateForce=true),
		@Column(name="remark", attrName="remark", label="备注"),
		@Column(name="approval_comment", attrName="approvalComment", label="备注"),
		@Column(name="conf_id", attrName="confId", label="补助标准"),
		@Column(name="subsidy_fund", attrName="subsidyFund", label="补助总金额", isUpdate=true),
		@Column(name="subsidy_period", attrName="subsidyPeriod", label="补助周期", isUpdate=true),
		@Column(name="apply_status", attrName="applyStatus", label="申请状态", isUpdateForce=true),
		@Column(name="status", attrName="status", label="状态", isUpdate=true),
		@Column(name="create_by", attrName="createBy", label="申请日期", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="申请人", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
		@Column(name="city", attrName="city", label="城市"),
		@Column(name="area", attrName="area", label="区域"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o",
				on = "o.office_code = a.unit_id", attrName = "applyOffice",
				columns = {
					@Column(includeEntity = Office.class)
		        }
		)
    }, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere", orderBy="a.update_date DESC"
)
public class HsTalentIntroductionApply extends BpmEntity<HsTalentIntroductionApply> {

	public static final String APPLY_STATUS_DEFAULT = "-1"; //默认、删除的申请单
	public static final String APPLY_STATUS_DRAFT = "人才引进申请";//0:省直单位申请草稿
	// 人才引进申请 主管部门审核   机关经办审核  机关处室领导复核  机关局领导审核  机关财务处经办审核  机关财务处领导复核   机关局领导审核  机关财务拨款
	//  talent_apply_status   talent_flow_status
	public static final String APPLY_STATUS_AUDIT_SUPERUNIT_FIRST = "主管部门审核";//1:待主管部门审核
	public static final String APPLY_STATUS_AUDIT_ORGHAND_FIRST = "机关经办审核";//2:待机关经办审核
	public static final String APPLY_STATUS_AUDIT_ORGOFFICE_FIRST = "机关处室领导复核";//3:待机关处室领导复核
	public static final String APPLY_STATUS_AUDIT_ORGBUREAU_FIRST = "机关局领导审核";//4:待机关局领导审核
	public static final String APPLY_STATUS_AUDIT_JGCWCJBSH_FIRST = "机关财务处经办审核";//5:待机关财务处经办审核
	public static final String APPLY_STATUS_AUDIT_JGCWCLDFH_FIRST = "机关财务处领导复核";//6:待机关财务处领导复核
	public static final String APPLY_STATUS_AUDIT_ORGBUREAU_SECOND = "机关局领导审核";//7:待机关局领导审核
	public static final String APPLY_STATUS_AUDIT_JGCWCJBBK_FIRST = "机关财务拨款";//8:待机关财务拨款

	private static final long serialVersionUID = 1L;
	private String unitId;		// 申请单位
	private String applyName;		// 申请人姓名
	private String applyTel;		// 申请人电话
	private String applyNo;		// 申请人身份证号码
	private String eduBack;		// 学历
	private String titleId;		// 职称
	private String talentType;		// 人才类型
	private String talentLevel;		//
	private Date arrivalTime;		// 引进时间
	private String remark;		// 备注
	private Integer applyStatus;		// 申请状态
	private String validTag;		// 是否有效;1-有效 0-无效
    private String approvalComment; // 审批意见
	private String city;
	private String area;

	private String confId;		// 补助标准
	private Double subsidyFund;		// 补助总金额
	private Integer subsidyPeriod;		//

	private String flowStatus;
	private Office applyOffice;

	private Integer issuedPeriod = 0;

	private Integer isView = 0;
	private String opType = "";


	@ExcelFields({
			@ExcelField(title="申请单号", attrName="id", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="申请单位", attrName="applyOffice.treeNames", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="申请人姓名", attrName="applyName", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="申请人电话", attrName="applyTel", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="身份证号码", attrName="applyNo", align= ExcelField.Align.CENTER, sort=60),
			@ExcelField(title="职称", attrName="titleId", dictType="talent_title", align= ExcelField.Align.CENTER, sort=70),
			@ExcelField(title="学历", attrName="eduBack", dictType="talent_edu_type", align= ExcelField.Align.LEFT, sort=80),
			@ExcelField(title="引进时间", attrName="arrivalTime", align= ExcelField.Align.CENTER, words=20, sort=90, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="备注", attrName="remark", align= ExcelField.Align.CENTER, sort=100),
			@ExcelField(title="申请状态", attrName="applyStatus", dictType="talent_apply_status",  align= ExcelField.Align.LEFT, sort=110),
			@ExcelField(title="创建日期", attrName="createDate", align= ExcelField.Align.CENTER, words=20, sort=120, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="更新日期", attrName="updateDate", align= ExcelField.Align.CENTER, words=20, sort=130, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
	})

	public HsTalentIntroductionApply() {
		this(null);
	}
	
	public HsTalentIntroductionApply(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="申请单位长度不能超过 64 个字符")
	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}
	
	@Size(min=0, max=30, message="申请人姓名长度不能超过 30 个字符")
	public String getApplyName() {
		return applyName;
	}

	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	
	@Size(min=0, max=20, message="申请人电话长度不能超过 20 个字符")
	public String getApplyTel() {
		return applyTel;
	}

	public void setApplyTel(String applyTel) {
		this.applyTel = applyTel;
	}
	
	@Size(min=0, max=255, message="申请人身份证号码长度不能超过 255 个字符")
	public String getApplyNo() {
		return applyNo;
	}

	public void setApplyNo(String applyNo) {
		this.applyNo = applyNo;
	}
	
	@Size(min=0, max=32, message="学历长度不能超过 32 个字符")
	public String getEduBack() {
		return eduBack;
	}

	public void setEduBack(String eduBack) {
		this.eduBack = eduBack;
	}
	
	@Size(min=0, max=64, message="职称长度不能超过 64 个字符")
	public String getTitleId() {
		return titleId;
	}

	public void setTitleId(String titleId) {
		this.titleId = titleId;
	}

	@Size(min=0, max=255, message="人才类型长度不能超过 255 个字符")
	public String getTalentType() {
		return talentType;
	}

	public void setTalentType(String talentType) {
		this.talentType = talentType;
	}

	@Size(min=0, max=255, message="人才等级长度不能超过 255 个字符")
	public String getTalentLevel() {
		return talentLevel;
	}

	public void setTalentLevel(String talentLevel) {
		this.talentLevel = talentLevel;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getArrivalTime() {
		return arrivalTime;
	}

	public void setArrivalTime(Date arrivalTime) {
		this.arrivalTime = arrivalTime;
	}

	public Date getArrivalTime_gte() {
		return sqlMap.getWhere().getValue("arrival_time", QueryType.GTE);
	}

	public void setArrivalTime_gte(Date arrivalTime) {
		sqlMap.getWhere().and("arrival_time", QueryType.GTE, arrivalTime);
	}

	public Date getArrivalTime_lte() {
		return sqlMap.getWhere().getValue("arrival_time", QueryType.LTE);
	}

	public void setArrivalTime_lte(Date arrivalTime) {
		sqlMap.getWhere().and("arrival_time", QueryType.LTE, arrivalTime);
	}

	@Size(min=0, max=900, message="备注长度不能超过 900 个字符")
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public String getFlowStatus() {
		return flowStatus;
	}

	public void setFlowStatus(String flowStatus) {
		this.flowStatus = flowStatus;
	}

	public Office getApplyOffice() {
		return applyOffice;
	}

	public void setApplyOffice(Office applyOffice) {
		this.applyOffice = applyOffice;
	}

	public String getConfId() {
		return confId;
	}

	public void setConfId(String confId) {
		this.confId = confId;
	}

	public Double getSubsidyFund() {
		return subsidyFund;
	}

	public void setSubsidyFund(Double subsidyFund) {
		this.subsidyFund = subsidyFund;
	}

	public Integer getSubsidyPeriod() {
		return subsidyPeriod;
	}

	public void setSubsidyPeriod(Integer subsidyPeriod) {
		this.subsidyPeriod = subsidyPeriod;
	}

	public Integer getIsView() {
		return isView;
	}

	public void setIsView(Integer isView) {
		this.isView = isView;
	}

	@Size(min=0, max=500, message="审批意见长度不能超过 500 个字符")
	public String getApprovalComment() {
		return approvalComment;
	}

	public void setApprovalComment(String approvalComment) {
		this.approvalComment = approvalComment;
	}

	public Integer getIssuedPeriod() {
		return issuedPeriod;
	}

	public void setIssuedPeriod(Integer issuedPeriod) {
		this.issuedPeriod = issuedPeriod;
	}

	@Size(min=0, max=32, message="城市长度不能超过 32 个字符")
	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Size(min=0, max=32, message="区域长度不能超过 32 个字符")
	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getOpType() {
		return opType;
	}

	public void setOpType(String opType) {
		this.opType = opType;
	}
}