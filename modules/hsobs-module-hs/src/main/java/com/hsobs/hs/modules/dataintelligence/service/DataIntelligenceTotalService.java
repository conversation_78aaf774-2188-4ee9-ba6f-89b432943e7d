package com.hsobs.hs.modules.dataintelligence.service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.LongAccumulator;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryDataScope;
import com.jeesite.common.reflect.asm.FieldAccess;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTotalDao;

import com.jeesite.modules.sys.entity.User;

import static org.apache.commons.lang3.StringUtils.isNumeric;

/**
 * 住房保障数据统计Service   总房源信息统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class DataIntelligenceTotalService extends CrudService<DataIntelligenceTotalDao, DataIntelligenceTotal> {

	@Autowired
	private DataIntelligenceTotalDao dataIntelligenceTotalDao;
	@Autowired
	private OfficeService officeService;

	/**
	 * 获取单条数据
	 * @param dataIntelligenceTotal
	 * @return
	 */
	@Override
	public DataIntelligenceTotal get(DataIntelligenceTotal dataIntelligenceTotal) {
		return super.get(dataIntelligenceTotal);
	}
	
	/**
	 * 查询分页数据
	 * @param dataIntelligenceTotal 查询条件
	 * @param dataIntelligenceTotal page 分页对象
	 * @return
	 */
	@Override
	public Page<DataIntelligenceTotal> findPage(DataIntelligenceTotal dataIntelligenceTotal) {
		return super.findPage(dataIntelligenceTotal);
	}
	
	/**
	 * 查询列表数据
	 * @param dataIntelligenceTotal
	 * @return
	 */
	@Override
	public List<DataIntelligenceTotal> findList(DataIntelligenceTotal dataIntelligenceTotal) {
		return super.findList(dataIntelligenceTotal);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param dataIntelligenceTotal
	 */
	@Override
	@Transactional
	public void save(DataIntelligenceTotal dataIntelligenceTotal) {
		super.save(dataIntelligenceTotal);
	}
	
	/**
	 * 更新状态
	 * @param dataIntelligenceTotal
	 */
	@Override
	@Transactional
	public void updateStatus(DataIntelligenceTotal dataIntelligenceTotal) {
		super.updateStatus(dataIntelligenceTotal);
	}
	
	/**
	 * 删除数据
	 * @param dataIntelligenceTotal
	 */
	@Override
	@Transactional
	public void delete(DataIntelligenceTotal dataIntelligenceTotal) {
		dataIntelligenceTotal.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataIntelligenceTotal);
	}

	public Map<String, Object> getComparePara(Integer compareType){
		Map<String, Object> map = new HashMap<>();

		Date now = new Date();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		int year = now.getYear()+1900;
		try {
			switch (compareType) {
				case 1:// 1月
				case 2:// 2月
				case 3:// 3月
				case 4:// 4月
				case 5:// 5月
				case 6:// 6月
				case 7:// 7月
				case 8:// 8月
				case 9:// 9月
				case 10:// 10月
				case 11:// 11月
					map.put("now_start_date", dateFormat.parse(String.format("%04d-%02d-01", year, compareType)));
					Date nDate = dateFormat.parse(String.format("%04d-%02d-01", year, compareType+1));
					map.put("now_title", String.format("%04d年%d月份", year, compareType));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-%02d-01", year-1, compareType)));
					Date lDate = dateFormat.parse(String.format("%04d-%02d-01", year-1, compareType+1));
					map.put("last_title", String.format("%04d年%d月份", year-1, compareType));

					Calendar calendar = Calendar.getInstance();
					calendar.setTime(nDate);
					calendar.add(Calendar.DATE, -1);
					map.put("now_end_date", calendar.getTime());

					calendar.setTime(lDate);
					calendar.add(Calendar.DATE, -1);
					map.put("last_end_date", calendar.getTime());
					break;
				case 12:// 12月
					map.put("now_start_date", dateFormat.parse(String.format("%04d-%02d-01", year, compareType)));
					map.put("now_end_date", dateFormat.parse(String.format("%04d-%02d-31", year, compareType)));
					map.put("now_title", String.format("%04d年%d月份", year, compareType));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-%02d-01", year-1, compareType)));
					map.put("last_end_date", dateFormat.parse(String.format("%04d-%02d-31", year-1, compareType)));
					map.put("last_title", String.format("%04d年%d月份", year-1, compareType));
					break;
				case 13://第1季度
					map.put("now_start_date", dateFormat.parse(String.format("%04d-01-01", year)));
					map.put("now_end_date", dateFormat.parse(String.format("%04d-03-31", year)));
					map.put("now_title", String.format("%04d年%d季度", year, compareType-12));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-01-01", year-1)));
					map.put("last_end_date", dateFormat.parse(String.format("%04d-03-31", year-1)));
					map.put("last_title", String.format("%04d年%d季度", year-1, compareType-12));
					break;
				case 14://第2季度
					map.put("now_start_date", dateFormat.parse(String.format("%04d-04-01", year)));
					map.put("now_end_date", dateFormat.parse(String.format("%04d-06-30", year)));
					map.put("now_title", String.format("%04d年%d季度", year, compareType-12));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-04-01", year-1)));
					map.put("last_end_date", dateFormat.parse(String.format("%04d-06-30", year-1)));
					map.put("last_title", String.format("%04d年%d季度", year-1, compareType-12));
					break;
				case 15://第3季度
					map.put("now_start_date", dateFormat.parse(String.format("%04d-07-01", year)));
					map.put("now_end_date", dateFormat.parse(String.format("%04d-09-30", year)));
					map.put("now_title", String.format("%04d年%d季度", year, compareType-12));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-07-01", year-1)));
					map.put("last_end_date", dateFormat.parse(String.format("%04d-09-30", year-1)));
					map.put("last_title", String.format("%04d年%d季度", year-1, compareType-12));
					break;
				case 16://第4季度
					map.put("now_start_date", dateFormat.parse(String.format("%04d-10-01", year)));
					map.put("now_end_date", dateFormat.parse(String.format("%04d-12-31", year)));
					map.put("now_title", String.format("%04d年%d季度", year, compareType-12));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-10-01", year-1)));
					map.put("last_end_date", dateFormat.parse(String.format("%04d-12-31", year-1)));
					map.put("last_title", String.format("%04d年%d季度", year-1, compareType-12));
					break;
				case 17://上半年
					map.put("now_start_date", dateFormat.parse(String.format("%04d-01-01", year)));
					map.put("now_end_date", dateFormat.parse(String.format("%04d-06-30", year)));
					map.put("now_title", String.format("%04d年上半年", year));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-01-01", year-1)));
					map.put("last_end_date", dateFormat.parse(String.format("%04d-06-30", year-1)));
					map.put("last_title", String.format("%04d年上半年", year-1));
					break;
				case 18://下半年
					map.put("now_start_date", dateFormat.parse(String.format("%04d-07-01", year)));
					map.put("now_end_date", dateFormat.parse(String.format("%04d-12-31", year)));
					map.put("now_title", String.format("%04d年下半年", year));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-07-01", year-1)));
					map.put("last_end_date", dateFormat.parse(String.format("%04d-12-31", year-1)));
					map.put("last_title", String.format("%04d年下半年", year-1));
					break;
				case 19://1年
					map.put("now_start_date", dateFormat.parse(String.format("%04d-01-01", year)));
					map.put("now_end_date", dateFormat.parse(String.format("%04d-12-31", year)));
					map.put("now_title", String.format("%04d年", year));
					map.put("last_start_date", dateFormat.parse(String.format("%04d-01-01", year-1)));
					map.put("last_end_date", dateFormat.parse(String.format("%04d-12-31", year-1)));
					map.put("last_title", String.format("%04d年", year-1));
					break;
			}
		} catch (Exception e) {
		}

		return map;
	}

	public String getOfficePermission(String officeCode){

		String strOtherWhere = "";
		if(officeCode != null) {
			Office office = officeService.get(officeCode);
			String officeParentCodes = office.getParentCodes();
			if(officeParentCodes != null && officeParentCodes.length() > 0)
				strOtherWhere += " AND (jso.parent_codes like '" + officeParentCodes+officeCode + ",%' or jso.office_code='" + officeCode + "')";
			else
				strOtherWhere += String.format(" AND jso.office_code='%s'", officeCode);
		}
		return strOtherWhere;
	}

	public Integer calcMaxValue(List<List<String>> dataType){
		Integer max = 1;
		Integer t;
		for(int i = 0; i < dataType.size(); i ++){
			List<String> data = dataType.get(i);
			for(int j = 0; j < data.size(); j ++){
				t = Integer.valueOf(data.get(j));
				if(max < t){
					max = t;
				}
			}
		}
		return (max>100)?((max+99)/100*100):((max+9)/10*10);
	}

	public String CheckDigitStr(String val){
		if(val != null && !"".equals(val)){
			if(!isNumeric(val)){
				throw new ServiceException("输入格式有错误,有被劫持风险");
			}
		}
		return val;
	}
	public String CheckOrderBy(String val){
		if(val == null || "".equals(val)) {
			return "";
		}
		String[] sqls = val.split(" ");
		if(sqls.length != 2){
			throw new ServiceException("输入格式有错误,有被劫持风险");
		}
		if(!"asc".equals(sqls[1]) && !"desc".equals(sqls[1])){
			throw new ServiceException("输入格式有错误,有被劫持风险");
		}
		if(!sqls[0].matches("^[a-zA-Z0-9_]+$")){
			throw new ServiceException("输入格式有错误,有被劫持风险");
		}
		return val;
	}

	private  String getSqlOtherWhere(DataIntelligenceTotal dataIntelligenceTotal, Date startDate, Date endDate){
		String sqlOtherWhere = "";
		if (startDate != null || dataIntelligenceTotal.getStartDate() != null) {
			Date date = (startDate!=null)?startDate:dataIntelligenceTotal.getStartDate();
			sqlOtherWhere += " AND house.create_date >= '" + String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}
		if (endDate != null || dataIntelligenceTotal.getEndDate() != null) {
			Date date = (endDate!=null)?endDate:dataIntelligenceTotal.getEndDate();
			sqlOtherWhere += " AND house.create_date <= '" + String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}

		/*if(dataIntelligenceTotal.getEstateName() != null && dataIntelligenceTotal.getEstateName().length() > 0) {
			sqlOtherWhere += " AND estate.name like '%" + dataIntelligenceTotal.getEstateName() + "%'";
		}*/
		if(dataIntelligenceTotal.getEstateId() != null && dataIntelligenceTotal.getEstateId().length() > 0) {
			sqlOtherWhere += " AND estate.id = '" + CheckDigitStr(dataIntelligenceTotal.getEstateId()) + "'";
		}
		if(dataIntelligenceTotal.getCity() != null && !"".equals(dataIntelligenceTotal.getCity())){
			sqlOtherWhere += " AND estate.city = '" + CheckDigitStr(dataIntelligenceTotal.getCity()) + "'";
		}
		if(dataIntelligenceTotal.getArea() != null && !"".equals(dataIntelligenceTotal.getArea())){
			sqlOtherWhere += " AND estate.area = '" + CheckDigitStr(dataIntelligenceTotal.getArea()) + "'";
		}

		dataIntelligenceTotal.sqlMap().getDataScope().addFilter("dsf", "Office", "jso.office_code", DataScope.CTRL_PERMI_HAVE);
		SqlMap sqlMap = dataIntelligenceTotal.sqlMap();
		Object obWhere = sqlMap.get("dsf");
		if(obWhere != null){
			sqlOtherWhere += obWhere.toString();
		}
		String officeCode = null;
		if(dataIntelligenceTotal.getOfficeCode() != null && dataIntelligenceTotal.getOfficeCode().length() > 0) {
			officeCode = dataIntelligenceTotal.getOfficeCode();
		}
		sqlOtherWhere += getOfficePermission(officeCode);

		return sqlOtherWhere;
	}

	public Page<DataIntelligenceTotal>findTotalDataPage(DataIntelligenceTotal dataIntelligenceTotal, boolean findpage){

		String otherWhere = getSqlOtherWhere(dataIntelligenceTotal, null, null);
		String sqlOrderBy = (dataIntelligenceTotal.getOrderBy()!=null&&dataIntelligenceTotal.getOrderBy().length()>3)?"ORDER BY "+ CheckOrderBy(dataIntelligenceTotal.getOrderBy()):"";

		Page<DataIntelligenceTotal> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("otherWhere", otherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceTotal.getPageNo());
			pageMap.setPageSize(dataIntelligenceTotal.getPageSize());
			mapPara.put("page", pageMap);
		}
 		List<Map<String,Object>> list = dataIntelligenceTotalDao.countHouseTotal(mapPara);
		List<DataIntelligenceTotal> statList = new ArrayList<>();
		for (int j = 0; j < list.size(); j++) {

			Map<String,Object> map = list.get(j);

			DataIntelligenceTotal houseStat = new DataIntelligenceTotal();
			Object ob = map.get("OFFICE_CODE");
			houseStat.setOfficeCode((ob!=null)?ob.toString():"");
			ob = map.get("OFFICE_NAME");
			houseStat.setOfficeName((ob!=null)?ob.toString():"");

			ob = map.get("TOTAL_ROOMS");
			houseStat.setRoomTotalNumber((ob!=null)?Integer.valueOf(ob.toString()):0);
			ob = map.get("TOTAL_AREAS");
			houseStat.setRoomTotalArea((ob!=null)?Float.valueOf(ob.toString()):0);

			ob = map.get("RENTAL_ROOMS");
			houseStat.setRoomRentalNumber((ob!=null)?Integer.valueOf(ob.toString()):0);
			ob = map.get("RENTAL_AREAS");
			houseStat.setRoomRentalArea((ob!=null)?Float.valueOf(ob.toString()):0);

			ob = map.get("PRICELIMIT_ROOMS");
			houseStat.setRoomPriceLimitNumber((ob!=null)?Integer.valueOf(ob.toString()):0);
			ob = map.get("PRICELIMIT_AREAS");
			houseStat.setRoomPriceLimitArea((ob!=null)?Float.valueOf(ob.toString()):0);

			ob = map.get("PUBLIC_ROOMS");
			houseStat.setRoomPublicNumber((ob!=null)?Integer.valueOf(ob.toString()):0);
			ob = map.get("PUBLIC_AREAS");
			houseStat.setRoomPublicArea((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(houseStat);
		}

		pageMap.setList(statList);
		return pageMap;
	}

	public List<DataIntelligenceTotal> findTotalData(DataIntelligenceTotal dataIntelligenceTotal) {

		// 统计，houseType:房源类型（0公租房 1限价房 2公用住房 3局直公房 4自管公房）
		return findTotalDataPage(dataIntelligenceTotal, false).getList();
	}

	public String totalStat(DataIntelligenceTotal dataIntelligenceTotal, boolean rooms) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 3; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "公租房");break;
				case 1:dataTypeO.put("name", "公有住房");break;
				case 2:dataTypeO.put("name", "限价房");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		String otherWhere = getSqlOtherWhere(dataIntelligenceTotal, null, null);
		List<Map<String,Object>> list = dataIntelligenceTotalDao.countHouseTotal2(otherWhere, "");

		Object ob;
		for(Map<String,Object> unitMap : list){

			if(rooms == true) {
				ob = unitMap.get("RENTAL_ROOMS");
				dataType.get(0).add((ob != null) ? ob.toString() : "0");
				ob = unitMap.get("PRICELIMIT_ROOMS");
				dataType.get(1).add((ob != null) ? ob.toString() : "0");
				ob = unitMap.get("PUBLIC_ROOMS");
				dataType.get(2).add((ob != null) ? ob.toString() : "0");
			}
			else {

				ob = unitMap.get("RENTAL_AREAS");
				dataType.get(0).add((ob != null) ? ob.toString() : "0");
				ob = unitMap.get("PRICELIMIT_AREAS");
				dataType.get(1).add((ob != null) ? ob.toString() : "0");
				ob = unitMap.get("PUBLIC_AREAS");
				dataType.get(2).add((ob != null) ? ob.toString() : "0");
			}
			ob = unitMap.get("OFFICE_NAME");
			dataClass.add((ob!=null)?ob.toString():"");
		}

		Integer max = calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}

	public String totalStatByCity(DataIntelligenceTotal dataIntelligenceTotal, boolean rooms) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 3; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "公租房");break;
				case 1:dataTypeO.put("name", "公有住房");break;
				case 2:dataTypeO.put("name", "限价房");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		String otherWhere = getSqlOtherWhere(dataIntelligenceTotal, null, null);
		List<Map<String,Object>> list = dataIntelligenceTotalDao.countHouseTotalByCity(otherWhere, "");

		Object ob;
		for(Map<String,Object> unitMap : list){

			if(rooms == true) {
				ob = unitMap.get("RENTAL_ROOMS");
				dataType.get(0).add((ob != null) ? ob.toString() : "0");
				ob = unitMap.get("PRICELIMIT_ROOMS");
				dataType.get(1).add((ob != null) ? ob.toString() : "0");
				ob = unitMap.get("PUBLIC_ROOMS");
				dataType.get(2).add((ob != null) ? ob.toString() : "0");
			}
			else {

				ob = unitMap.get("RENTAL_AREAS");
				dataType.get(0).add((ob != null) ? ob.toString() : "0");
				ob = unitMap.get("PRICELIMIT_AREAS");
				dataType.get(1).add((ob != null) ? ob.toString() : "0");
				ob = unitMap.get("PUBLIC_AREAS");
				dataType.get(2).add((ob != null) ? ob.toString() : "0");
			}
			ob = unitMap.get("CITY_NAME");
			dataClass.add((ob!=null)?ob.toString():"");
		}

		Integer max = calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}
}

