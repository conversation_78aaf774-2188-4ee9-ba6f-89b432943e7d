package com.hsobs.hs.modules.hsalarm.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.hsalarm.entity.HsAlarm;

import java.util.List;
import java.util.Map;

/**
 * 告警信息DAO接口
 * <AUTHOR>
 * @version 2025-01-03
 */
@MyBatisDao
public interface HsAlarmDao extends CrudDao<HsAlarm> {
    // 未签合同
    List<Map<String, Object>> countUnsignedContract();
    // 拖欠租金
    List<Map<String, Object>> countRentArrears();
    // 时间异常
    List<Map<String, Object>> countTimeAnomaly();
    // 资格异常
    List<Map<String, Object>> countAbnormalQualification();
    // 维修补助额度不足
    List<Map<String, Object>> countInsufficientCreditLimit(long remainingAmount, long limitAmount);
}