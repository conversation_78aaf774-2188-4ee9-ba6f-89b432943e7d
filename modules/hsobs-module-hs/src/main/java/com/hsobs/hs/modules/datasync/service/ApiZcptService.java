package com.hsobs.hs.modules.datasync.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.hsobs.hs.modules.datasync.bean.*;
//import com.jeesite.common.codec.SM3Utils;
import com.jeesite.modules.sys.utils.SM3Utils;
import com.jeesite.common.config.Global;
import com.jeesite.common.service.BaseService;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class ApiZcptService extends BaseService {

    protected static Logger logger = LoggerFactory.getLogger(ApiZcptService.class);

    private static final String SIGN_HEADER_KEY = "ZC-Access-Sign";
    private static final String APPID_HEADER_KEY = "ZC-Access-App-Id";
    private static final String TIMESTAMP_HEADER_KEY = "ZC-Access-Timestamp";

    private static String systemId; //应用支撑平台分配的应用id
    private static String sm4Key; // 接收接口解密密钥
    private static String sm3Key; // 头部签名密钥
    private static String server; // 服务地址
    private static String syncUserId; // 同步数据用户ID

    static {

        try{
            systemId = Global.getConfig("zcpt.systemId");
            sm4Key = Global.getConfig("zcpt.sm4.prk-my");
            sm3Key = Global.getConfig("zcpt.sm3.pbk-my");
            server = Global.getConfig("zcpt.server");
            syncUserId = Global.getConfig("zcpt.syncUserId");
        } catch (Exception e) {
            logger.error("load init param error", e);
        }
    }

    public String getSystemId() {
        return systemId;
    }

    public String getSm4Key() {
        return sm4Key;
    }

    private Map<String, String> getCommonHeaders(String timeMillis) {
        Map<String, String> headers = new TreeMap<>();
        headers.put(APPID_HEADER_KEY, systemId);
        headers.put(TIMESTAMP_HEADER_KEY, timeMillis);
        return headers;
    }

    public String generateGetSignature(String timeMillis, Map<String, String> params) {
        return generateGetSignature(getCommonHeaders(timeMillis), params);
    }

    public String generateGetSignature(Map<String, String> headers, Map<String, String> params) {
        Map<String, String> allParams = new TreeMap<>(headers);
        allParams.putAll(params);
        allParams.remove(SIGN_HEADER_KEY);
        allParams.values().removeIf(Objects::isNull);

        StringBuilder paramString = new StringBuilder();
        for (Map.Entry<String, String> entry : allParams.entrySet()) {
            if (paramString.length() > 0) {
                paramString.append("&");
            }
            paramString.append(entry.getKey()).append("=").append(entry.getValue());
        }
        String input = paramString.toString();
        logger.debug("generateGetSignature input: {}", input);
        return SM3Utils.sm3(input, sm3Key);
    }
    public String generateGetParam(Map<String, String> params) {
        Map<String, String> allParams = new TreeMap<>(params);
        allParams.values().removeIf(Objects::isNull);

        StringBuilder paramString = new StringBuilder();
        for (Map.Entry<String, String> entry : allParams.entrySet()) {
            if (paramString.length() > 0) {
                paramString.append("&");
            }
            paramString.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return paramString.toString();
    }

    public String generatePostSignature(String timeMillis, Object requestBody) {
        return generatePostSignature(getCommonHeaders(timeMillis), requestBody);
    }

    public String generatePostSignature(Map<String, String> headers, Object requestBody) {
        String jsonBody = JSONObject.toJSONString(requestBody);

        Map<String, String> allParams = new TreeMap<>(headers);
        allParams.put("data", jsonBody);
        allParams.remove(SIGN_HEADER_KEY);

        StringBuilder paramString = new StringBuilder();
        for (Map.Entry<String, String> entry : allParams.entrySet()) {
            if (paramString.length() > 0) {
                paramString.append("&");
            }
            paramString.append(entry.getKey()).append("=").append(entry.getValue());
        }
        String input = paramString.toString();
        logger.debug("generatePostSignature input: {}", input);
        return SM3Utils.sm3(input, sm3Key);
    }

    public static CloseableHttpClient createHttpClient() throws KeyManagementException, NoSuchAlgorithmException, KeyStoreException {
        // 创建一个信任所有证书的 SSLContext
        SSLContext sslContext = new SSLContextBuilder()
                .loadTrustMaterial(null, (certificate, authType) -> true) // 信任所有证书
                .build();

        // 创建一个 SSLConnectionSocketFactory，跳过主机名验证
        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        // 创建 HttpClient
        return HttpClients.custom()
                .setSSLSocketFactory(socketFactory)
                .build();
    }

    public static RestTemplate createRestTemplate() throws Exception {
        // 创建跳过 SSL 验证的 HttpClient
        CloseableHttpClient httpClient = createHttpClient();

        // 配置 RestTemplate 使用自定义的 HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory(httpClient);

        return new RestTemplate(requestFactory);
    }

    /**
     * 数字空间通用请求方法
     *
     * @param body
     * @return
     */
    private <T> Api2ResponseBody<T> request(String url, HttpMethod method, Object body, TypeReference<Api2ResponseBody<T>> typeReference) {
        logger.debug("ZCPT 请求报文: " + (body != null ? body.toString() : ""));
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        String zcAccessTimestamp = String.valueOf(System.currentTimeMillis());
        headers.add(APPID_HEADER_KEY, systemId);
        headers.add(TIMESTAMP_HEADER_KEY, zcAccessTimestamp);

        try {
            HttpEntity<Object> requestEntity = null;
            String sign = null;
            if (HttpMethod.GET.equals(method)) {
                if (body instanceof Map) {
                    sign = generateGetSignature(zcAccessTimestamp, (Map<String, String>) body);
                    url += "?" + generateGetParam((Map<String, String>) body);
                } else if (body == null) {
                    sign = generateGetSignature(zcAccessTimestamp, new HashMap<String, String>());
                }
                body = null;
            } else {
                sign = generatePostSignature(zcAccessTimestamp, body);
            }
            headers.add(SIGN_HEADER_KEY, sign);

            requestEntity = new HttpEntity<>(body, headers);

            // 发送 POST 请求并解析 JSON 响应
            logger.debug("request url: " + url);
            logger.debug("request headers: " + headers.toString());
            ResponseEntity<String> responseEntity = createRestTemplate().exchange(url, method, requestEntity, String.class);
            // 处理返回数据
            String responseBody = responseEntity.getBody();
            if(responseBody != null){
                logger.debug("ZCPT 结果报文: " + responseBody);
            }
            return JSON.parseObject(responseBody, typeReference);
        } catch (Exception e) {
            logger.debug("接口请求失败，请核查！", e);
        }
        return null;
    }

    /**
     * 获取用户的授权单位
     */
    public Api2ResponseBody<List<UserOrgData>> getUserOrg(String userId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getUserOrg";
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<UserOrgData>>>() {});
    }

    /**
     * 获取用户的授权单位树
     */
    public Api2ResponseBody<OrgTreeData> getUserOrgTree(String userId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getUserOrgTree";
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<OrgTreeData>>() {});
    }

    /**
     * 获取用户的授权资源
     */
    public Api2ResponseBody<List<ResourceData>> getUserResource(String userId, String systemId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getUserResource";
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        params.put("systemId", systemId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<ResourceData>>>() {});
    }

    /**
     * 获取用户的关联角色
     */
    public Api2ResponseBody<List<UserRoleData>> getUserRole(String userId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getUserRole";
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<UserRoleData>>>() {});
    }

    /**
     * 获取单位详细信息
     */
    public Api2ResponseBody<List<OrgDetailData>> getOrgInfo(String orgId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getOrgInfo";
        Map<String, String> params = new HashMap<>();
        params.put("orgId", orgId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<OrgDetailData>>>() {});
    }

    /**
     * 获取单位下用户列表
     */
    public Api2ResponseBody<List<UserUnderOrgData>> getUserUnderOrg(String orgId, Integer isSub, Integer isSubRoles) {
        return getUserUnderOrg(orgId, syncUserId, isSub, isSubRoles);
    }

    /**
     * 获取单位下用户列表
     */
    public Api2ResponseBody<List<UserUnderOrgData>> getUserUnderOrg(String orgId, String userId, Integer isSub, Integer isSubRoles) {
        String url = server + "/digit-space/digit-space-main/userAuth/userUnderOrg";
        Map<String, String> params = new HashMap<>();
        params.put("orgId", orgId);
        params.put("userId", userId);
        if (isSub != null) {
            params.put("isSub", String.valueOf(isSub));
        }
        if (isSubRoles != null) {
            params.put("isSubRoles", String.valueOf(isSubRoles));
        }
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<UserUnderOrgData>>>() {});
    }

    /**
     * 获取全部单位信息
     */
    public Api2ResponseBody<List<FullOrgData>> getFullOrg() {
        String url = server + "/digit-space/digit-space-main/userAuth/getFullOrg";
        return this.request(url, HttpMethod.GET, null, new TypeReference<Api2ResponseBody<List<FullOrgData>>>() {});
    }

    /**
     * 7.8 批量获取用户详细信息
     */
    public Api2ResponseBody<List<UserDetailData>> listUserInfo(List<String> userIds) {
        String url = server + "/digit-space/digit-space-main/userAuth/listUserInfo";
        LinkedHashMap<String, Object> requestBody = new LinkedHashMap<>();
        requestBody.put("userIds", userIds);
        return this.request(url, HttpMethod.POST, requestBody, new TypeReference<Api2ResponseBody<List<UserDetailData>>>() {});
    }

    /**
     * 获取单位下拥有资源权限的角色信息
     */
    public Api2ResponseBody<List<RoleResourceData>> getRoleByOrgAndResourceCode(String systemId, String orgId, String resourceCode) {
        String url = server + "/digit-space/digit-space-main/userAuth/getRoleByOrgAndResourceCode";
        Map<String, String> params = new HashMap<>();
        params.put("systemId", systemId);
        params.put("orgId", orgId);
        params.put("resourceCode", resourceCode);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<RoleResourceData>>>() {});
    }

    /**
     * 根据角色ID获取单位下用户列表
     */
    public Api2ResponseBody<List<UserByRoleIdData>> getUserUnderOrgByRoleId(List<String> roleIds, String userId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getUserUnderOrg";
        LinkedHashMap<String, Object> requestBody = new LinkedHashMap<>();
        requestBody.put("roleIds", roleIds);
        requestBody.put("userId", userId);
        return this.request(url, HttpMethod.POST, requestBody, new TypeReference<Api2ResponseBody<List<UserByRoleIdData>>>() {});
    }

    /**
     * 获取用户的关联角色及角色数据权限
     */
    public Api2ResponseBody<List<UserRoleAndDataAuthData>> getUserAndRoleOrg(String userId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getUserAndRoleOrg";
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<UserRoleAndDataAuthData>>>() {});
    }

    /**
     * 根据角色编码获取角色所关联的用户列表
     */
    public Api2ResponseBody<List<UserByRoleCodeData>> getUserByRoleCode(String roleCode) {
        String url = server + "/digit-space/digit-space-main/userAuth/getUserByRoleCode";
        Map<String, String> params = new HashMap<>();
        params.put("roleCode", roleCode);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<UserByRoleCodeData>>>() {});
    }

    /**
     * 获取具有访问对应系统权限的角色列表
     */
    public Api2ResponseBody<List<RoleSystemAccessData>> getRoleBySystemId(String systemId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getRoleBySystemId";
        Map<String, String> params = new HashMap<>();
        params.put("systemId", systemId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<RoleSystemAccessData>>>() {});
    }

    /**
     * 获取用户所在单位下的用户列表
     */
    public Api2ResponseBody<List<UserUnderUserOrgData>> getUserListUnderOrg(String userId) {
        String url = server + "/digit-space/digit-space-main/userAuth/userListUnderOrg";
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<UserUnderUserOrgData>>>() {});
    }

    /**
     * 获取角色的授权资源
     */
    public Api2ResponseBody<List<RoleResourceAuthData>> getRoleResource(String roleId, String roleCode, String systemId) {
        String url = server + "/digit-space/digit-space-main/userAuth/getRoleResource";
        Map<String, String> params = new HashMap<>();
        if (roleId != null) {
            params.put("roleId", roleId);
        }
        if (roleCode != null) {
            params.put("roleCode", roleCode);
        }
        params.put("systemId", systemId);
        return this.request(url, HttpMethod.GET, params, new TypeReference<Api2ResponseBody<List<RoleResourceAuthData>>>() {});
    }

}
