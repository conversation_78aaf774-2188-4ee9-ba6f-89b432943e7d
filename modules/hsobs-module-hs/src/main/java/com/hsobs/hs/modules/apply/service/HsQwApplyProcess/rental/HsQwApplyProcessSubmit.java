package com.hsobs.hs.modules.apply.service.HsQwApplyProcess.rental;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyrule.service.HsQwApplyRuleService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.formalarm.dao.HsQwFormAlarmDao;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import com.hsobs.hs.modules.formalarm.service.HsQwFormAlarmService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class HsQwApplyProcessSubmit extends HsQwApplyProcessDefault {

    @Autowired
    HsQwFormAlarmService hsQwFormAlarmService;

    @Autowired
    HsQwFormAlarmDao hsQwFormAlarmDao;

    @Autowired
    HsQwApplyRuleService hsQwApplyRuleService;

    @Override
    public String getStatus() {
        return "用户申请";
    }

    @Override
    public void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        Map<String, HsQwFormAlarm> hsQwFormAlarmMap = new HashMap<>();
        hsQwApply.setStatus(HsQwApply.STATUS_AUDIT);
        // 主流程
        this.process(hsQwApply, hsQwApplyService);
        // 计算轮候得分
        if (hsQwApply.getScoreUpdate()==null || hsQwApply.getScoreUpdate().equals("0")) {
            hsQwApplyService.refreshByApply(hsQwApply);
        }
        // 资格核验
        hsQwApplyService.applyRuleCheck(hsQwApply, hsQwFormAlarmMap);
        if (hsQwFormAlarmMap.size() > 0) {
            // 先删除
            HsQwFormAlarm where = new HsQwFormAlarm();
            where.setObjectId(hsQwApply.getId());
            HsQwFormAlarm entity = new HsQwFormAlarm();
            entity.setStatus(HsQwFormAlarm.STATUS_DELETE);
            hsQwFormAlarmDao.updateStatusByEntity(entity,where);
            // 解析出map中的key，value，批量插入数据库，整合成list
            List<HsQwFormAlarm> list = new ArrayList<>();
            hsQwFormAlarmMap.forEach((k, v) -> {
                list.add(v);
            });
            hsQwFormAlarmService.saveBatch(list);
        }
        //申请开始阶段保证状态一定是审核中
        hsQwApply.setStatus(HsQwApply.STATUS_AUDIT);
        hsQwApplyService.realUpdateStatus(hsQwApply);
    }

}
