package com.hsobs.hs.modules.publicsaleestate.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.publicsaleestate.entity.HsPublicSaleEstate;
import com.hsobs.hs.modules.publicsaleestate.dao.HsPublicSaleEstateDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 公有住房配售房屋Service
 * <AUTHOR>
 * @version 2025-02-26
 */
@Service
public class HsPublicSaleEstateService extends CrudService<HsPublicSaleEstateDao, HsPublicSaleEstate> {
	
	/**
	 * 获取单条数据
	 * @param hsPublicSaleEstate
	 * @return
	 */
	@Override
	public HsPublicSaleEstate get(HsPublicSaleEstate hsPublicSaleEstate) {
		return super.get(hsPublicSaleEstate);
	}
	
	/**
	 * 查询分页数据
	 * @param hsPublicSaleEstate 查询条件
	 * @param hsPublicSaleEstate page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPublicSaleEstate> findPage(HsPublicSaleEstate hsPublicSaleEstate) {
		return super.findPage(hsPublicSaleEstate);
	}
	
	/**
	 * 查询列表数据
	 * @param hsPublicSaleEstate
	 * @return
	 */
	@Override
	public List<HsPublicSaleEstate> findList(HsPublicSaleEstate hsPublicSaleEstate) {

		List<HsPublicSaleEstate> lstEstate = super.findList(hsPublicSaleEstate);
		return lstEstate;
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsPublicSaleEstate
	 */
	@Override
	@Transactional
	public void save(HsPublicSaleEstate hsPublicSaleEstate) {
		super.save(hsPublicSaleEstate);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(hsPublicSaleEstate, hsPublicSaleEstate.getId(), "hsPublicSaleEstate_image");
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsPublicSaleEstate, hsPublicSaleEstate.getId(), "hsPublicSaleEstate_file");
	}
	
	/**
	 * 更新状态
	 * @param hsPublicSaleEstate
	 */
	@Override
	@Transactional
	public void updateStatus(HsPublicSaleEstate hsPublicSaleEstate) {
		super.updateStatus(hsPublicSaleEstate);
	}
	
	/**
	 * 删除数据
	 * @param hsPublicSaleEstate
	 */
	@Override
	@Transactional
	public void delete(HsPublicSaleEstate hsPublicSaleEstate) {
		super.delete(hsPublicSaleEstate);
	}
	
}