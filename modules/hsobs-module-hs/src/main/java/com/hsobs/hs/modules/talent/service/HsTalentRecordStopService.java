package com.hsobs.hs.modules.talent.service;

import java.util.*;
import java.util.stream.Collectors;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.talent.bean.HsTalentRecordStopImp;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.hsobs.hs.modules.talent.entity.HsTalentRecord;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import groovy.lang.Lazy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.talent.entity.HsTalentRecordStop;
import com.hsobs.hs.modules.talent.dao.HsTalentRecordStopDao;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 人才补助停发报备Service
 * <AUTHOR>
 * @version 2025-01-03
 */
@Service
public class HsTalentRecordStopService extends CrudService<HsTalentRecordStopDao, HsTalentRecordStop> {

	@Autowired
	private BpmTaskService bpmTaskService;
	@Autowired
	private HsTalentRecordService hsTalentRecordService;

	/**
	 * 获取单条数据
	 * @param hsTalentRecordStop
	 * @return
	 */
	@Override
	public HsTalentRecordStop get(HsTalentRecordStop hsTalentRecordStop) {
		return super.get(hsTalentRecordStop);
	}


	@Override
	public void addDataScopeFilter(HsTalentRecordStop entity) {
		SqlMap sqlMap = entity.sqlMap(); // v5.3.0+ 及之后版本
		// 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
		sqlMap.getDataScope().addFilter("extWhere", "Office",
				"h.unit_id", "a.create_by", DataScope.CTRL_PERMI_HAVE, "hsTalentRecord");
	}

	/**
	 * 查询分页数据
	 * @param hsTalentRecordStop 查询条件
	 * @param hsTalentRecordStop page 分页对象
	 * @return
	 */
	@Override
	public Page<HsTalentRecordStop> findPage(HsTalentRecordStop hsTalentRecordStop) {
		// 0 4 5 9
		hsTalentRecordStop.sqlMap().getWhere().disableAutoAddStatusWhere();
		hsTalentRecordStop.setStatus_in(new String[] { "0", "4", "5", "9" });
		return super.findPage(hsTalentRecordStop);
	}
	
	/**
	 * 查询列表数据
	 * @param hsTalentRecordStop
	 * @return
	 */
	@Override
	public List<HsTalentRecordStop> findList(HsTalentRecordStop hsTalentRecordStop) {
		return super.findList(hsTalentRecordStop);
	}

	public Page<HsTalentRecordStop> findAuditPageByTask(HsTalentRecordStop hsTalentRecordStop) {
		//审批待办中的状态过滤
		String[] status = new String[]{
				HsTalentRecordStop.APPLY_STATUS_DEFAULT,
				HsTalentRecordStop.APPLY_STATUS_DRAFT,
				HsTalentRecordStop.APPLY_STATUS_AUDIT_SUPERUNIT_FIRST,
				HsTalentRecordStop.APPLY_STATUS_AUDIT_ORGHAND_FIRST,
				HsTalentRecordStop.APPLY_STATUS_AUDIT_ORGOFFICE_FIRST ,
				HsTalentRecordStop.APPLY_STATUS_AUDIT_ORGBUREAU_FIRST ,
		};

		HsBpmTask params = new HsBpmTask();
		params.setStatus("1");
		Page<BpmTask> myHsTaskPage = this.getHsTask(params, status, hsTalentRecordStop.getFlowStatus());
		//获取所有待办任务的申请单id
		if (!this.getIdsByTask(myHsTaskPage, hsTalentRecordStop)) {
			Page<HsTalentRecordStop> hsTalentRecordStopApplyPage = this.findPage(hsTalentRecordStop);
			return this.getTaskResult(hsTalentRecordStopApplyPage, myHsTaskPage);
		} else {
			return hsTalentRecordStop.getPage();
		}

	}

	private String getStatusFromTask(String id, Page<BpmTask> myHsTaskPage) {
		for (BpmTask bpmTask : myHsTaskPage.getList()) {
			if (bpmTask.getProcIns().getBizKey().equals(id)) {
				return bpmTask.getName();
			}
		}
		return null;
	}

	private Page<HsTalentRecordStop> getTaskResult(Page<HsTalentRecordStop> hsTalentRecordStopPage, Page<BpmTask> myHsTaskPage) {
		hsTalentRecordStopPage.getList().forEach(k -> k.setFlowStatus(this.getStatusFromTask(k.getId(), myHsTaskPage)));
		return hsTalentRecordStopPage;
	}

	private boolean getIdsByTask(Page<BpmTask> pageTask, HsTalentRecordStop hsTalentRecordStop) {
		List<String> hsIds = new ArrayList<>();
		pageTask.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		hsTalentRecordStop.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
		return hsIds.isEmpty();
	}

	private Page<BpmTask> getHsTask(HsBpmTask params, String[] resNames, String applyStatus) {
		params.setUserCode(params.currentUser().getUserCode());
		params.getProcIns().setFormKey("talent_subsidy_stop_apply");
		if (resNames != null && resNames.length > 0) {
			params.setNames(Arrays.stream(resNames).collect(Collectors.toList()));
		}
		if (StringUtils.isNotBlank(applyStatus)) {
			params.setName(applyStatus);
		}
		return this.bpmTaskService.findTaskPage(params);
	}


	/**
	 * 保存数据（插入或更新）
	 * @param hsTalentRecordStop
	 */
	@Override
	@Transactional
	public void save(HsTalentRecordStop hsTalentRecordStop) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsTalentRecordStop.getStatus())){
			hsTalentRecordStop.setStatus(HsTalentRecordStop.STATUS_AUDIT);
		}
		
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsTalentRecordStop.STATUS_NORMAL.equals(hsTalentRecordStop.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		// 记录流程节点值
		int activityIdInt = -1;
		if (hsTalentRecordStop.getBpm() != null && StringUtils.isNotEmpty(hsTalentRecordStop.getBpm().getActivityId())) {
			String activityId = hsTalentRecordStop.getBpm().getActivityId();
			if ("undefined".equals(activityId)) {
				if (HsTalentRecordStop.STATUS_AUDIT.equals(hsTalentRecordStop.getStatus())) {
					activityIdInt = 1;
				}
			} else {
				// talentSubsidyStopApply0001
				activityId = activityId.replace("talentSubsidyStopApply", "");
				while (activityId.startsWith("0")) {
					activityId = StringUtils.removeStart(activityId, "0");
				}
				if (StringUtils.isNumeric(activityId)) {
					activityIdInt = Integer.parseInt(activityId);
				}
			}
		}
		hsTalentRecordStop.setApplyStatus(activityIdInt);
		if (hsTalentRecordStop.getTalentId() != null) {
			hsTalentRecordStop.setTalentId(hsTalentRecordStop.getTalentId().split(",")[1]);
		}


		// 获取人才补助档案
		HsTalentRecord record = hsTalentRecordService.get(hsTalentRecordStop.getTalentId());
		if (record == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		String checkStatusStr = String.valueOf(record.getCheckStatus());
		if ("3".equals(checkStatusStr)) {
			throw new ServiceException(text("该申请人已停发人才补助！"));
		}
		if (activityIdInt == -1 || activityIdInt == 0 || activityIdInt == 1) {
			// 更新人才信息  审批状态  0-无 1-审核中(发放) 2-发放中 3-停发 4-审核中(停发)  talent_status
			if ("1".equals(checkStatusStr)) {
				throw new ServiceException(text("该申请人已经发起申请流程，请先撤回审批中申请！"));
			} else if ("4".equals(checkStatusStr)) {
				throw new ServiceException(text("该申请人已经发起停发申请流程，请先撤回审批中申请！"));
			} else if (!"2".equals(checkStatusStr)) {
				throw new ServiceException(text("该申请人无人才住房补助发放，请先申请人才住房补助！"));
			}
		}

		hsTalentRecordStop.setUserNo(record.getUserNo());

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsTalentRecordStop.STATUS_DRAFT.equals(hsTalentRecordStop.getStatus())
				|| HsTalentRecordStop.STATUS_AUDIT.equals(hsTalentRecordStop.getStatus())){
			super.save(hsTalentRecordStop);
		}

		record.setCheckTime(new Date());

		// activityIdInt == 1
		if (activityIdInt == -1 || activityIdInt == 0) {
			// 更新人才信息  审批状态  0-无 1-审核中(发放) 2-发放中 3-停发 4-审核中(停发)  talent_status
		} else if (activityIdInt == 5) {
			record.setCheckStatus(3);
		} else {
			record.setCheckStatus(4);
		}
		hsTalentRecordService.save(record);

		// 如果为审核状态，则进行审批流操作
		if (HsTalentRecordStop.STATUS_AUDIT.equals(hsTalentRecordStop.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", hsTalentRecordStop.getLeaveDays());
			
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsTalentRecordStop.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsTalentRecordStop.getBpm().getTaskId())){
				BpmUtils.start(hsTalentRecordStop, "talent_subsidy_stop_apply", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsTalentRecordStop, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsTalentRecordStop, hsTalentRecordStop.getId(), "hsTalentRecordStop_file");
	}
	
	/**
	 * 更新状态
	 * @param hsTalentRecordStop
	 */
	@Override
	@Transactional
	public void updateStatus(HsTalentRecordStop hsTalentRecordStop) {
		super.updateStatus(hsTalentRecordStop);
	}

	/**
	 * 删除数据
	 *
	 * @param hsTalentRecordStop
	 */
	@Override
	@Transactional
	public void delete(HsTalentRecordStop hsTalentRecordStop) {
		hsTalentRecordStop.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsTalentRecordStop);
	}


	public void flushTaskStatus(HsTalentRecordStop hsTalentRecordStop) {
		if (hsTalentRecordStop.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		hsTalentRecordStop = this.get(hsTalentRecordStop.getId());
		if (hsTalentRecordStop == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		HsBpmTask params = new HsBpmTask();
		params.setStatus("1");
		// 维修资金申请流程key
		params.getProcIns().setFormKey("talent_subsidy_stop_apply");
		params.getProcIns().setBizKey(hsTalentRecordStop.getId());

		Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);

		BpmTask bpmTask = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
		if (bpmTask != null) {
			String activityId = bpmTask.getActivityId().replace("talentSubsidyStopApply", "");
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				int activityIdInt = Integer.parseInt(activityId);

				if (activityIdInt == 1) {
					activityIdInt = 0;

					// 恢复人员状态为发放状态
					HsTalentRecord record = hsTalentRecordService.get(hsTalentRecordStop.getTalentId());
					record.setCheckStatus(2);
					record.setCheckTime(new Date());
					hsTalentRecordService.update(record);
				} else {
					activityIdInt = getPrevApplyStatus(activityIdInt);
				}

				hsTalentRecordStop.setApplyStatus(activityIdInt);
				super.save(hsTalentRecordStop);
			}
		}
	}

	public Integer getPrevApplyStatus(Integer activityIdInt) {
		return PREV_MAP.get(activityIdInt);
	}

	private static Map<Integer, Integer> PREV_MAP = new HashMap<>();
	static {
		// 停发  1 2 3 4 5 6 7 8 9
		PREV_MAP.put(1, 0);
		PREV_MAP.put(2, 1);
		PREV_MAP.put(3, 2);
		PREV_MAP.put(4, 3);
		PREV_MAP.put(5, 4);
		PREV_MAP.put(6, 5);
		PREV_MAP.put(7, 6);
		PREV_MAP.put(8, 7);
		PREV_MAP.put(9, 8);
	}

    public String importData(MultipartFile file, String type) {
		if (file == null) {
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0;
		int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try (ExcelImport ei = new ExcelImport(file, 2, 0)) {
			List<HsTalentRecordStopImp> list = ei.getDataList(HsTalentRecordStopImp.class);
			int index = 0;
			for (HsTalentRecordStopImp hsTalentRecordStopImp : list) {
				index++;
				try {
					ValidatorUtils.validateWithException(hsTalentRecordStopImp);
					//初始化数据
					this.initData(hsTalentRecordStopImp, type);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + index + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + index + " 导入失败：";
					if (e instanceof ConstraintViolationException) {
						ConstraintViolationException cve = (ConstraintViolationException) e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " (" + violation.getPropertyPath() + ")";
						}
					} else {
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		} else {
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
    }

	private HsTalentRecordStop initData(HsTalentRecordStopImp hsTalentRecordStopImp, String type) {
		// 获取人才补助档案
		HsTalentRecord htrQuery = new HsTalentRecord();
		htrQuery.setUserName(hsTalentRecordStopImp.getUserName());
		htrQuery.setUserTel(hsTalentRecordStopImp.getMobile());
		htrQuery.setUserNo(hsTalentRecordStopImp.getUserNo());
		List<HsTalentRecord> recordList = hsTalentRecordService.findList(htrQuery);

		String msg = String.format("用户:%s 手机:%s 身份证:%s", htrQuery.getUserName(), htrQuery.getUserTel(), htrQuery.getUserNo());

		if (recordList == null || recordList.isEmpty()) {
			throw new ServiceException(text(msg + " 不存在！"));
		}
		if (recordList.size() > 1) {
			throw new ServiceException(text(msg + " 存在多条数据！"));
		}
		HsTalentRecord record = recordList.get(0);
		// 更新人才信息  审批状态  0-无 1-审核中(发放) 2-发放中 3-停发 4-审核中(停发)  talent_status
		String checkStatusStr = String.valueOf(record.getCheckStatus());
		if ("3".equals(checkStatusStr)) {
			throw new ServiceException(text(msg + " 该申请人已停发人才补助！"));
		} else if ("1".equals(checkStatusStr)) {
			throw new ServiceException(text(msg + " 该申请人已经发起申请流程，请先撤回审批中申请！"));
		} else if ("4".equals(checkStatusStr)) {
			throw new ServiceException(text(msg + " 该申请人已经发起停发申请流程，请先撤回审批中申请！"));
		} else if (!"2".equals(checkStatusStr)) {
			throw new ServiceException(text(msg + " 该申请人无人才住房补助发放，请先申请人才住房补助！"));
		}

		HsTalentRecordStop hsTalentRecordStop = new HsTalentRecordStop();
		hsTalentRecordStop.setTalentId(record.getId());
		hsTalentRecordStop.setUserNo(hsTalentRecordStopImp.getUserNo());
		hsTalentRecordStop.setStopReason(hsTalentRecordStopImp.getStopReason());
		hsTalentRecordStop.setStatus(HsTalentRecordStop.STATUS_NORMAL);
		hsTalentRecordStop.setApplyStatus(-1);
		super.save(hsTalentRecordStop);

//		record.setCheckStatus(4);
//		record.setCheckTime(new Date());
//		hsTalentRecordService.save(record);

		return hsTalentRecordStop;
	}

}