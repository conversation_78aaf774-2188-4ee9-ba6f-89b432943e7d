package com.hsobs.hs.modules.dataintelligence.web;


import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTotalDao;
import com.hsobs.hs.modules.dataintelligence.dao.HsKanbanDao;
import com.hsobs.hs.modules.dataintelligence.entity.HsKanban;
import com.hsobs.hs.modules.dataintelligence.service.HsKanbanService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.web.BaseController;
import org.apache.tomcat.util.buf.UriUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

import com.jeesite.modules.sys.service.EmpUserService;
import org.springframework.web.util.UriUtils;

/**
 * 住房保障对位接口
 */
@Controller
@RequestMapping(value = "${adminPath}/kanban/", method = RequestMethod.POST)
public class HsKanbanController extends BaseController {

    @Autowired
    private HsKanbanService hsKanbanService;


    @RequestMapping(value = "queryDataByUser")
    @ResponseBody
    public Map<String, String> queryDataByUser(HsKanban hsKanban, HttpServletRequest request, HttpServletResponse response) {

        return hsKanbanService.countKanbanCount(hsKanban);
    }

}
