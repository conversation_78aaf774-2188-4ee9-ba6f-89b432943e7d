package com.hsobs.hs.modules.apply.service.applyedDataList;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.entity.Page;
import org.springframework.stereotype.Service;

/**
 * 公租房承租信息变更申请-可选择申请单列表
 */
@Service
public class HsQwApplyedListReplace implements HsQwApplyedList{

    @Override
    public String getDataType() {
        return "1";
    }

    @Override
    public Page<HsQwApply> execute(HsQwApply hsQwApply,  HsQwApplyService hsQwApplyService) {
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwApply.setStatus(null);
        hsQwApply.sqlMap().add("extWhere", " and (a.STATUS = 0\n" +
                "\t\tAND a.APPLY_MATTER in (0))");
        return hsQwApplyService.findPage(hsQwApply);
    }
}
