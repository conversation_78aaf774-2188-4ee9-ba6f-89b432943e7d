package com.hsobs.hs.modules.datamanage.service;

import java.util.List;

import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.datamanage.entity.HsFileScan;
import com.hsobs.hs.modules.datamanage.dao.HsFileScanDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 文档扫描信息Service
 * <AUTHOR>
 * @version 2025-01-19
 */
@Service
public class HsFileScanService extends CrudService<HsFileScanDao, HsFileScan> {
	
	/**
	 * 获取单条数据
	 * @param hsFileScan
	 * @return
	 */
	@Override
	public HsFileScan get(HsFileScan hsFileScan) {
		return super.get(hsFileScan);
	}


	@Override
	public void addDataScopeFilter(HsFileScan entity) {
		SqlMap sqlMap = entity.sqlMap(); // v5.3.0+ 及之后版本
		// 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
		sqlMap.getDataScope().addFilter("extWhere", "Office",
				"o.office_code", "a.create_by", DataScope.CTRL_PERMI_HAVE, "hsFileScan");
	}

	/**
	 * 查询分页数据
	 * @param hsFileScan 查询条件
	 * @param hsFileScan page 分页对象
	 * @return
	 */
	@Override
	public Page<HsFileScan> findPage(HsFileScan hsFileScan) {
		return super.findPage(hsFileScan);
	}
	
	/**
	 * 查询列表数据
	 * @param hsFileScan
	 * @return
	 */
	@Override
	public List<HsFileScan> findList(HsFileScan hsFileScan) {
		return super.findList(hsFileScan);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsFileScan
	 */
	@Override
	@Transactional
	public void save(HsFileScan hsFileScan) {
		super.save(hsFileScan);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsFileScan, hsFileScan.getId(), "hsFileScan_file");
	}
	
	/**
	 * 更新状态
	 * @param hsFileScan
	 */
	@Override
	@Transactional
	public void updateStatus(HsFileScan hsFileScan) {
		super.updateStatus(hsFileScan);
	}
	
	/**
	 * 删除数据
	 * @param hsFileScan
	 */
	@Override
	@Transactional
	public void delete(HsFileScan hsFileScan) {
		hsFileScan.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsFileScan);
	}
	
}