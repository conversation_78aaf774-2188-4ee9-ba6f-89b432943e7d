package com.hsobs.hs.modules.maintenance.service;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.maintenance.dao.HsMaintenanceApplyDao;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFunds;
import com.hsobs.hs.modules.maintenance.entity.HsSequence;
import com.hsobs.hs.modules.utils.HsBpmService;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import groovy.lang.Tuple2;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维修申请Service
 * <AUTHOR>
 * @version 2024-12-08
 */
@Service
public class HsMaintenanceApplyService extends CrudService<HsMaintenanceApplyDao, HsMaintenanceApply> {

	@Autowired
	private BpmTaskService bpmTaskService;
	@Autowired
	private HsSequenceService hsSequenceService;
	@Autowired
	private HsMaintenanceFundsService hsMaintenanceFundsService;

	private HsBpmService<HsMaintenanceApply> hsBpmService;
	@PostConstruct
	public void init() {
		// 在注入后对 hsBpmService 的属性进行设置
		hsBpmService = new HsBpmService<>(HsMaintenanceApply.class);
		hsBpmService.setCrudService(this);
	}

	/**
	 * 获取单条数据
	 * @param hsMaintenanceApply
	 * @return
	 */
	@Override
	public HsMaintenanceApply get(HsMaintenanceApply hsMaintenanceApply) {
		return super.get(hsMaintenanceApply);
	}

	@Override
	public void addDataScopeFilter(HsMaintenanceApply entity) {
		SqlMap sqlMap = entity.sqlMap(); // v5.3.0+ 及之后版本
		// 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
		sqlMap.getDataScope().addFilter("extWhere", "Office",
				"a.unit_id", "a.create_by", DataScope.CTRL_PERMI_HAVE, "hsMaintenanceApply");
	}

	/**
	 * 查询分页数据
	 * @param hsMaintenanceApply 查询条件
	 * @param hsMaintenanceApply page 分页对象
	 * @return
	 */
	@Override
	public Page<HsMaintenanceApply> findPage(HsMaintenanceApply hsMaintenanceApply) {
		hsMaintenanceApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findPage(hsMaintenanceApply);
	}

	public Page<HsMaintenanceApply> findAuditPageByTask(HsMaintenanceApply hsMaintenanceApply) {
		//审批待办中的状态过滤
		String[] status = new String[]{
				HsMaintenanceApply.APPLY_STATUS_DEFAULT,
				HsMaintenanceApply.APPLY_STATUS_DRAFT,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_ORGHAND_FIRST,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_SITE_INVESTIG,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_ORGHAND_SECOND,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_ORGOFFICE_FIRST,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_ISSUE_APPROVAL_APPLICATION_FORM,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_ORGANIZE_CONSTRUCTION,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_SUBMIT_CONSTRUCTION_MATERIALS,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_ORGHAND_THIRD,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_SECDEPT_VERIFY,
				HsMaintenanceApply.APPLY_STATUS_AUDIT_ISSUE_FUND_SLIP
		};

		if (StringUtils.isNotEmpty(hsMaintenanceApply.getFlowStatus())) {
			status = new String[]{
					hsMaintenanceApply.getFlowStatus()
			};
		}


//		bpmTaskService.

//		HsBpmTask params = new HsBpmTask();
//		params.setStatus("1");
//		Page<BpmTask> myHsTaskPage = this.getHsTask(params, status, hsMaintenanceApply.getFlowStatus());
//		//获取所有待办任务的申请单id
//		if (!this.getIdsByTask(myHsTaskPage, hsMaintenanceApply)) {
//			Page<HsMaintenanceApply> hsMaintenanceApplyPage = this.findPage(hsMaintenanceApply);
//			return this.getTaskResult(hsMaintenanceApplyPage, myHsTaskPage);
//		} else {
//			return hsMaintenanceApply.getPage();
//		}
		return this.findApplyPageByTask(hsMaintenanceApply, status, "1", "maintenance_apply");
	}

	@Autowired
	private CommonBpmService commonBpmService;

	public Page<HsMaintenanceApply> findApplyPageByTask(HsMaintenanceApply hsMaintenanceApply, String[] status, String bpmStatus, String formKey) {
		return commonBpmService.findTaskList(status, formKey,hsMaintenanceApply, bpmStatus);
	}

	private String getStatusFromTask(String id, Page<BpmTask> myHsTaskPage) {
		for (BpmTask bpmTask : myHsTaskPage.getList()) {
			if (bpmTask.getProcIns().getBizKey().equals(id)) {
				return bpmTask.getName();
			}
		}
		return null;
	}

	private Page<HsMaintenanceApply> getTaskResult(Page<HsMaintenanceApply> hsMaintenanceApplyPage, Page<BpmTask> myHsTaskPage) {
		hsMaintenanceApplyPage.getList().forEach(k -> k.setFlowStatus(this.getStatusFromTask(k.getId(), myHsTaskPage)));
		return hsMaintenanceApplyPage;
	}

	private boolean getIdsByTask(Page<BpmTask> pageTask, HsMaintenanceApply hsMaintenanceApply) {
		List<String> hsIds = new ArrayList<>();
		pageTask.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		hsMaintenanceApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
		return hsIds.isEmpty();
	}

	private Page<BpmTask> getHsTask(HsBpmTask params, String[] resNames, String applyStatus) {
		//查询我的代办rent_apply任务
		params.setUserCode(params.currentUser().getUserCode());
		// 维修资金申请流程key
		params.getProcIns().setFormKey("maintenance_apply");
		if (resNames != null && resNames.length > 0) {
			params.setNames(Arrays.stream(resNames).collect(Collectors.toList()));
		}
		if (StringUtils.isNotBlank(applyStatus)) {
			params.setName(applyStatus);
		}
		return this.bpmTaskService.findTaskPage(params);
	}

	/**
	 * 查询列表数据
	 * @param hsMaintenanceApply
	 * @return
	 */
	@Override
	public List<HsMaintenanceApply> findList(HsMaintenanceApply hsMaintenanceApply) {
		return super.findList(hsMaintenanceApply);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsMaintenanceApply
	 */
	@Override
	@Transactional
	public void save(HsMaintenanceApply hsMaintenanceApply) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsMaintenanceApply.getStatus())){
			hsMaintenanceApply.setStatus(HsMaintenanceApply.STATUS_DRAFT);
		}
		
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsMaintenanceApply.STATUS_NORMAL.equals(hsMaintenanceApply.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}


		// 记录流程节点值
		int activityIdInt = 1;
		if (hsMaintenanceApply.getBpm() != null && StringUtils.isNotEmpty(hsMaintenanceApply.getBpm().getActivityId())) {
			String activityId = hsMaintenanceApply.getBpm().getActivityId();
			// maintenanceApply0010
			activityId = activityId.replace("maintenanceApply", "");
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				activityIdInt = Integer.parseInt(activityId);
			}
		}
		hsMaintenanceApply.setApplyStatus(activityIdInt);

		if (activityIdInt == 2) {
			if (hsMaintenanceApply.getFundId() != null) {
				HsMaintenanceFunds fundObj = hsMaintenanceFundsService.get(hsMaintenanceApply.getFundId());
				if (fundObj == null) {
					throw new ServiceException(text("非法操作，前端数据被劫持！"));
				}
				hsMaintenanceFundsService.applyFund(fundObj.getId(), hsMaintenanceApply.getApplyFund());
			}
		} else if (activityIdInt == 12) {
			// 10 资金拨付单编号生成
			if (hsMaintenanceApply.getFavNo() == null) {
				Long favNo = hsSequenceService.getNextValue(HsSequence.MAINTENANCE_FAV_NO_NAME);
				Tuple2<Long, Long> tuple2 = hsSequenceService.getNextRangeValue(HsSequence.MAINTENANCE_ADN_NAME, hsMaintenanceApply.getAdnNum());

				if (favNo == null || tuple2.getV1() == null || tuple2.getV2() == null) {
					throw new ServiceException(text("系统异常,请联系系统管理员！"));
				}

				hsMaintenanceApply.setFavNo(favNo);
				hsMaintenanceApply.setAdnStart(tuple2.getV1());
				hsMaintenanceApply.setAdnEnd(tuple2.getV2());
				hsMaintenanceApply.setDisbursementFund(hsMaintenanceApply.getApplyFund());

				//TODO 资金帐户变更
				if (hsMaintenanceApply.getFundId() != null) {
					HsMaintenanceFunds fundObj = hsMaintenanceFundsService.get(hsMaintenanceApply.getFundId());
					if (fundObj == null) {
						throw new ServiceException(text("非法操作，前端数据被劫持！"));
					}
					hsMaintenanceFundsService.decrease(fundObj.getId(), hsMaintenanceApply.getDisbursementFund());
				}
			}
		}
		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsMaintenanceApply.STATUS_DRAFT.equals(hsMaintenanceApply.getStatus())
				|| HsMaintenanceApply.STATUS_AUDIT.equals(hsMaintenanceApply.getStatus())){
			super.save(hsMaintenanceApply);
		}

		// 如果为审核状态，则进行审批流操作
		if (HsMaintenanceApply.STATUS_AUDIT.equals(hsMaintenanceApply.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
//			variables.put("leaveDays", hsMaintenanceApply.getLeaveDays());
			// 是否需要现场勘察变量
			variables.put("surveyFlag", hsMaintenanceApply.getSurveyFlag());

			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsMaintenanceApply.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsMaintenanceApply.getBpm().getTaskId())){
				BpmUtils.start(hsMaintenanceApply, "maintenance_apply", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsMaintenanceApply, variables, null);
			}
		}
		// 保存上传附件
		// 申请材料
		FileUploadUtils.saveFileUpload(hsMaintenanceApply, hsMaintenanceApply.getId(), "hsMaintenanceApply_file");
		// 现场勘察材料
		FileUploadUtils.saveFileUpload(hsMaintenanceApply, hsMaintenanceApply.getId(), "hsMaintenanceApply_investigationFile");
		// 工程施工材料
		FileUploadUtils.saveFileUpload(hsMaintenanceApply, hsMaintenanceApply.getId(), "hsMaintenanceApply_engineerFile");
		// 核检材料
		FileUploadUtils.saveFileUpload(hsMaintenanceApply, hsMaintenanceApply.getId(), "hsMaintenanceApply_reviewFile");
	}
	
	/**
	 * 更新状态
	 * @param hsMaintenanceApply
	 */
	@Override
	@Transactional
	public void updateStatus(HsMaintenanceApply hsMaintenanceApply) {
		super.updateStatus(hsMaintenanceApply);
	}
	
	/**
	 * 删除数据
	 * @param hsMaintenanceApply
	 */
	@Override
	@Transactional
	public void delete(HsMaintenanceApply hsMaintenanceApply) {
		hsMaintenanceApply.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsMaintenanceApply);
	}

	public void exportFundSlip(HsMaintenanceApply hsMaintenanceApply, HttpServletResponse response) {

		if (hsMaintenanceApply.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		HsMaintenanceApply hsMaintenanceApply1 = this.get(hsMaintenanceApply.getId());
		if (hsMaintenanceApply1 == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		String fileName = "资金拨付单" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		InputStream inputStream = null;
		try {
			Resource resource = new ClassPathResource("file.template/hsMaintenanceFundSlip.xlsx");
			inputStream = resource.getInputStream();
			XSSFWorkbook wb = new XSSFWorkbook(inputStream);

			Sheet sheet = wb.getSheetAt(0);  // 获取第一个工作表，可根据实际情况修改索引

			// 第二行 3-编号
			// 第三行 1-编号
			// 第四行 1- 2-批文号  5- 6-批文号 8-基金账号名
			// 第五行 1-单位名称 4-拨付金额
			// 第六行 1-拨付金额
			// 第八行 2-日期 8-日期
			
			// 第二行 3-编号
			Row row2 = sheet.getRow(1);
			if (row2 != null) {
				setCellValue(row2, 10, String.valueOf(hsMaintenanceApply1.getFavNo()));
			}
			// 第三行 1-编号
			Row row3 = sheet.getRow(2);
			if (row3 != null) {
				setCellValue(row3, 1, String.valueOf(hsMaintenanceApply1.getFavNo()));
			}
			// 第四行 1- 2-批文号  5- 6-批文号 8-基金账号名
			Row row4 = sheet.getRow(3);
			if (row4 != null) {
				String yearStr = DateUtils.getDate("yyyy");

				String adnStr = String.format("%s-%s", hsMaintenanceApply1.getAdnStart(), hsMaintenanceApply1.getAdnEnd());
				setCellValue(row4, 1, String.format("闽直房改办[%s]", yearStr));
				setCellValue(row4, 3, adnStr);
				setCellValue(row4, 6, String.format("按闽直房改[%s]", yearStr));
				setCellValue(row4, 8, adnStr);

				setCellValue(row4, 10, hsMaintenanceApply1.getHsMaintenanceFunds().getFundName());
			}
			// 第五行 1-单位名称 4-拨付金额
			Row row5 = sheet.getRow(4);
			if (row5 != null) {
				setCellValue(row5, 1, hsMaintenanceApply.getApplyOffice().getOfficeName());
				setCellValue(row5, 8, String.valueOf(hsMaintenanceApply.getDisbursementFund()));
			}
			// 第六行 1-拨付金额
			Row row6 = sheet.getRow(5);
			if (row6 != null) {
				setCellValue(row6, 1, String.valueOf(hsMaintenanceApply.getDisbursementFund()));
			}
			// 第八行 2-日期 8-日期
			Row row8 = sheet.getRow(7);
			if (row8 != null) {
				String dateStr = DateUtils.getDate("yyyy年MM月dd日");
				setCellValue(row8, 2, dateStr);
				setCellValue(row8, 9, dateStr);
			}

			response.reset();
			response.setContentType("application/octet-stream; charset=utf-8");
			response.addHeader("Content-Disposition", "attachment; filename*=utf-8'zh_cn'"+ EncodeUtils.encodeUrl(fileName));
			wb.write(response.getOutputStream());
		} catch (Exception e) {
			logger.error("export-maintenance-fund-slip-exception", e);
		} finally {
			if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException ignore) {
                }
            }
		}
	}

	private void setCellValue(Row row, int index, String value) {
		Cell cell = row.getCell(index);
		if (cell != null) {
			cell.setCellValue(value);
		}
	}

	public void flushTaskStatus(HsMaintenanceApply hsMaintenanceApply) {
		if (hsMaintenanceApply.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		hsMaintenanceApply = this.get(hsMaintenanceApply.getId());
		if (hsMaintenanceApply == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		HsBpmTask params = new HsBpmTask();
		params.setStatus("1");
		// 维修资金申请流程key
		params.getProcIns().setFormKey("maintenance_apply");
		params.getProcIns().setBizKey(hsMaintenanceApply.getId());

		Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);

		BpmTask bpmTask = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
		if (bpmTask != null) {
			String activityId = bpmTask.getActivityId().replace("maintenanceApply", "");
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				int activityIdInt = Integer.parseInt(activityId);

				if (activityIdInt == 1) {
					activityIdInt = 0;
				} else {
					activityIdInt = getPrevApplyStatus(activityIdInt, hsMaintenanceApply.getSurveyFlag());
				}

				hsMaintenanceApply.setApplyStatus(activityIdInt);
				super.save(hsMaintenanceApply);
			}
		}
	}

	public Integer getPrevApplyStatus(Integer activityIdInt, String surveyFlag) {
		if (activityIdInt == 5) {
			if ("1".equals(surveyFlag)) {
				return 4;
			} else {
				return 2;
			}
		} else {
			return PREV_MAP.get(activityIdInt);
		}
	}

	private static Map<Integer, Integer> PREV_MAP = new HashMap<>();
	static {
		// 维修  1 2 4 5 6 7 8 10 12
		PREV_MAP.put(1, 0);
		PREV_MAP.put(2, 1);
		PREV_MAP.put(4, 2);
		PREV_MAP.put(5, 4);
		PREV_MAP.put(6, 5);
		PREV_MAP.put(7, 6);
		PREV_MAP.put(8, 7);
		PREV_MAP.put(10, 8);
		PREV_MAP.put(12, 10);
	}

}