package com.hsobs.hs.modules.pricelimitapply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.hsobs.hs.modules.pricelimitapply.service.HsPriceLimitApplyService;

/**
 * 限价房-购房申请Controller
 * <AUTHOR>
 * @version 2024-12-10
 */
@Controller
@RequestMapping(value = "${adminPath}/pricelimitapply/hsPriceLimitApplyMzt")
public class HsPriceLimitApplyMztController extends BaseController {

	@Autowired
	private HsPriceLimitApplyService hsPriceLimitApplyService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPriceLimitApply get(String id, boolean isNewRecord) {
		return hsPriceLimitApplyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		return "modules/pricelimitapply/hsPriceLimitApplyListMzt";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsPriceLimitApply> listData(HsPriceLimitApply hsPriceLimitApply, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApply.setPage(new Page<>(request, response));
		Page<HsPriceLimitApply> page = hsPriceLimitApplyService.findMztPage(hsPriceLimitApply);
		return page;
	}
	
	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "form")
	public String form(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/pricelimitapply/hsPriceLimitApplyFormMzt";
	}
	
}