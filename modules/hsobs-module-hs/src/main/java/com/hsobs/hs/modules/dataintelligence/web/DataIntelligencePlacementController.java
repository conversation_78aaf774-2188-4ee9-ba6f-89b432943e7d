package com.hsobs.hs.modules.dataintelligence.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.dataintelligence.service.DataIntelligencePlacementService;

import java.util.List;

/**
 * 住房保障数据统计Controller  公有住房配售统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Controller
@RequestMapping(value = "${adminPath}/dataintelligenceplace/")
public class DataIntelligencePlacementController extends BaseController {

	@Autowired
	private DataIntelligencePlacementService dataIntelligenceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataIntelligencePlacement get(Integer type, boolean isNewRecord) {
		return dataIntelligenceService.get(type, isNewRecord);
	}

	/**
	 * 公有住房配售统计
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = {"dataIntelligencePublic", ""})
	public String dataIntelligencePublic(DataIntelligencePlacement dataIntelligencePlacement, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligencePlacement);
		return "modules/dataintelligence/dataIntelligencePublic";
	}
	/**
	 * 限价房配售统计
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = {"dataIntelligencePriceLimit", ""})
	public String dataIntelligencePriceLimit(DataIntelligencePlacement dataIntelligencePlacement, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligencePlacement);
		return "modules/dataintelligence/dataIntelligencePriceLimit";
	}

	/**
	 * 公有住房配售统计
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = {"dataIntelligencePay", ""})
	public String dataIntelligencePay(DataIntelligencePlacement dataIntelligencePlacement, Model model) {
		if(dataIntelligencePlacement.getOfficeCode() != null && "undefined".equals(dataIntelligencePlacement.getOfficeCode())){
			dataIntelligencePlacement.setOfficeCode(null);
		}
		model.addAttribute("dataIntelligence", dataIntelligencePlacement);
		return "modules/dataintelligence/dataIntelligencePlacementList";
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPlacementPayInfo")
	@ResponseBody
	public Page<DataIntelligencePlacement> countPlacementPayInfo(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligencePlacement.setPage(new Page<>(request, response));
		String sqlTable = (dataIntelligencePlacement.getStype()!=null&&"0".equals(dataIntelligencePlacement.getStype()))?"HS_PRICE_LIMIT_APPLY":"HS_PUBLIC_APPLY";
		Page<DataIntelligencePlacement> page = dataIntelligenceService.findcountTotalInfoPage(dataIntelligencePlacement, sqlTable, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPublicTotalInfo")
	@ResponseBody
	public Page<DataIntelligencePlacement> countPublicTotalInfo(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligencePlacement.setPage(new Page<>(request, response));
		Page<DataIntelligencePlacement> page = dataIntelligenceService.findcountTotalInfoPage(dataIntelligencePlacement, "HS_PUBLIC_APPLY", true);
		return page;
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPriceLimitTotalInfo")
	@ResponseBody
	public Page<DataIntelligencePlacement> countPriceLimitTotalInfo(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligencePlacement.setPage(new Page<>(request, response));
		Page<DataIntelligencePlacement> page = dataIntelligenceService.findcountTotalInfoPage(dataIntelligencePlacement, "HS_PRICE_LIMIT_APPLY", true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPublicAreaInfo")
	@ResponseBody
	public Page<DataIntelligencePlacement> countPublicAreaInfo(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligencePlacement.setPage(new Page<>(request, response));
		Page<DataIntelligencePlacement> page = dataIntelligenceService.findcountAreaInfoPage(dataIntelligencePlacement, "HS_PUBLIC_APPLY");
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPublicAreaCompare1")
	@ResponseBody
	public String countPublicAreaCompare1(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountAreaCompare(dataIntelligencePlacement, "HS_PUBLIC_APPLY", 1);
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPublicAreaCompare2")
	@ResponseBody
	public String countPublicAreaCompare2(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountAreaCompare(dataIntelligencePlacement, "HS_PUBLIC_APPLY", 2);
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPublicAreaCompare3")
	@ResponseBody
	public String countPublicAreaCompare3(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountAreaCompare(dataIntelligencePlacement, "HS_PUBLIC_APPLY", 3);
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPublicAreaCompare4")
	@ResponseBody
	public String countPublicAreaCompare4(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountAreaCompare(dataIntelligencePlacement, "HS_PUBLIC_APPLY", 4);
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPublicPriceCompare")
	@ResponseBody
	public String countPublicPriceCompare(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountPriceCompare(dataIntelligencePlacement, "HS_PUBLIC_APPLY");
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPriceLimitAreaInfo")
	@ResponseBody
	public Page<DataIntelligencePlacement> countPriceLimitAreaInfo(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligencePlacement.setPage(new Page<>(request, response));
		Page<DataIntelligencePlacement> page = dataIntelligenceService.findcountAreaInfoPage(dataIntelligencePlacement, "HS_PRICE_LIMIT_APPLY");
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPriceLimitAreaCompare1")
	@ResponseBody
	public String countPriceLimitAreaCompare1(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountAreaCompare(dataIntelligencePlacement, "HS_PRICE_LIMIT_APPLY", 1);
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPriceLimitAreaCompare2")
	@ResponseBody
	public String countPriceLimitAreaCompare2(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountAreaCompare(dataIntelligencePlacement, "HS_PRICE_LIMIT_APPLY", 2);
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPriceLimitAreaCompare3")
	@ResponseBody
	public String countPriceLimitAreaCompare3(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountAreaCompare(dataIntelligencePlacement, "HS_PRICE_LIMIT_APPLY", 3);
	}
	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPriceLimitAreaCompare4")
	@ResponseBody
	public String countPriceLimitAreaCompare4(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountAreaCompare(dataIntelligencePlacement, "HS_PRICE_LIMIT_APPLY", 4);
	}

	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "countPriceLimitPriceCompare")
	@ResponseBody
	public String countPriceLimitPriceCompare(DataIntelligencePlacement dataIntelligencePlacement, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findcountPriceCompare(dataIntelligencePlacement, "HS_PRICE_LIMIT_APPLY");
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "exportDataPublic")
	public void exportDataPublic(DataIntelligencePlacement dataIntelligencePlacement, HttpServletResponse response) {
		String fileName = "公有住房配售统计表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("公有住房配售统计表", DataIntelligencePlacement.class);
			List<DataIntelligencePlacement> list = dataIntelligenceService.findcountTotalInfo(dataIntelligencePlacement, "HS_PUBLIC_APPLY");
			ee.setDataList(list);
			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligenceplace::view")
	@RequestMapping(value = "exportDataPriceLimit")
	public void exportDataPriceLimit(DataIntelligencePlacement dataIntelligencePlacement, HttpServletResponse response) {
		String fileName = "限价房配售统计表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("限价房配售统计表", DataIntelligencePlacement.class);
			List<DataIntelligencePlacement> list = dataIntelligenceService.findcountTotalInfo(dataIntelligencePlacement, "HS_PRICE_LIMIT_APPLY");
			ee.setDataList(list);
			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}
}