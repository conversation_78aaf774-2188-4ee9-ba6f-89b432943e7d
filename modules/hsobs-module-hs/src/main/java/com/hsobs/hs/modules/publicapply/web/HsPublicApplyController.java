package com.hsobs.hs.modules.publicapply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.bpm.entity.BpmTask;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.hsobs.hs.modules.publicapply.service.HsPublicApplyService;

import com.hsobs.hs.modules.publicapply.entity.HsPublicApplyHouse;
import com.hsobs.hs.modules.publicapply.service.HsPublicApplyHouseService;

/**
 * 公有住房-购房申请Controller
 * <AUTHOR>
 * @version 2024-12-10
 */
@Controller
@RequestMapping(value = "${adminPath}/publicapply/hsPublicApply")
public class HsPublicApplyController extends BaseController {

	@Autowired
	private HsPublicApplyService hsPublicApplyService;

	@Autowired
	private HsPublicApplyHouseService hsPublicApplyHouseService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPublicApply get(String id, boolean isNewRecord) {
		return hsPublicApplyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		return "modules/publicapply/hsPublicApplyList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = {"listCheck", ""})
	public String listCheck(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		return "modules/publicapply/hsPublicApplyListCheck";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = {"listDone", ""})
	public String listDone(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		return "modules/publicapply/hsPublicApplyListDone";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = {"listhouse", ""})
	public String listhouse(HsPublicApplyHouse hsPublicApplyHouse, Model model) {
		model.addAttribute("hsPublicApplyHouse", hsPublicApplyHouse);
		return "modules/publicapply/hsPublicApplyListHouse";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = {"archives", ""})
	public String archives(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		return "modules/publicapply/hsPublicApplyListArchives";
	}
	
    /**
     * 查询代办的审批任务
     */
    @RequiresPermissions("publicapply:hsPublicApply:view")
    @RequestMapping(value = "listAuditData")
    @ResponseBody
    public Page<HsPublicApply> listAuditData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
        hsPublicApply.setPage(new Page<>(request, response));
        Page<HsPublicApply> page = hsPublicApplyService.findAuditPageByTasks(hsPublicApply, BpmTask.STATUS_UNFINISHED);
        return page;
    }

	/**
	 * 查询代办的审批任务
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "listAuditCheckData")
	@ResponseBody
	public Page<HsPublicApply> listAuditCheckData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApply.setPage(new Page<>(request, response));
		String flowStatus = "";
		switch (hsPublicApply.getType()) {
			case "1":flowStatus=hsPublicApply.APPLY_STATUS_AUDIT_APPROVAL;break;
			case "2":flowStatus=hsPublicApply.APPLY_STATUS_AUDIT_CONFIRMATION;break;
		}
		Page<HsPublicApply> page = hsPublicApplyService.findAuditPageByTask(hsPublicApply, flowStatus);
		return page;
	}

	/**
	 * 查询代办的审批任务
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "listAuditDoneData")
	@ResponseBody
	public Page<HsPublicApply> listAuditDoneData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApply.setPage(new Page<>(request, response));
		Page<HsPublicApply> page = hsPublicApplyService.findAuditPageByTasks(hsPublicApply, BpmTask.STATUS_FINISHED);
		return page;
	}

	/**
	 * 查询代办的审批任务
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "listAuditHouseData")
	@ResponseBody
	public Page<HsPublicApplyHouse> listAuditHouseData(HsPublicApplyHouse hsPublicApplyHouse, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApplyHouse.setPage(new Page<>(request, response));
		Page<HsPublicApplyHouse> page = hsPublicApplyHouseService.findAuditHousePageByTasks(hsPublicApplyHouse);
		return page;
	}

	/**
	 * 查询代办的审批任务
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "listArchivesData")
	@ResponseBody
	public Page<HsPublicApply> listArchivesData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApply.setPage(new Page<>(request, response));
		Page<HsPublicApply> page = hsPublicApplyService.findArchivesPage(hsPublicApply);
		return page;
	}

	/**
	 * 查询子表数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "hsPublicApplyerListData")
	@ResponseBody
	public Page<HsPublicApplyer> subListData(HsPublicApplyer hsPublicApplyer, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApplyer.setPage(new Page<>(request, response));
		Page<HsPublicApplyer> page = hsPublicApplyService.findSubPage(hsPublicApplyer);
		return page;
	}

	/**
	 * 请求后不能修改，跳转页面
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "form")
	public String form(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/publicapply/hsPublicApplyForm";
	}

	/**
	 * 核准，估价
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "estimated")
	public String estimated(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/publicapply/hsPublicApplyFormEstimated";
	}

	/**
	 * 档案
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "archives/form")
	public String formCheck(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/publicapply/hsPublicApplyFormCheck";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsPublicApply hsPublicApply) {
		hsPublicApplyService.save(hsPublicApply);

		if (hsPublicApply.getIsNewRecord()) {
			return renderResult(Global.TRUE, text("新增公有住房购房申请成功！"));
		}
		return renderResult(Global.TRUE, text("审核成功！"));
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@PostMapping(value = "flushTaskStatus")
	@ResponseBody
	public String flushTaskStatus(@Validated HsPublicApply hsPublicApply) {
		hsPublicApplyService.flushTaskStatus(hsPublicApply);
		return renderResult(Global.TRUE, text("刷新公有住房申请状态成功！"));
	}

	/**
	 * 生成申请表
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "sendApply")
	public void sendApply(HsPublicApply hsPublicApply, HttpServletResponse response) {
		hsPublicApplyService.sendApply(hsPublicApply, response);
	}
	/**
	 * 下发批文，生成批文
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "sendApproval")
	public void sendApproval(HsPublicApply hsPublicApply, HttpServletResponse response) {
		hsPublicApplyService.sendApproval(hsPublicApply, response);
	}

	/**
	 * 备案，向国资备案
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "createRegister")
	@ResponseBody
	public String createRegister(HsPublicApply hsPublicApply, HttpServletResponse response) {
		hsPublicApplyService.createRegister(hsPublicApply, response);
		return renderResult(Global.TRUE, text("创建备案表成功！"));
	}
	/**
	 * 备案，向国资备案
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "sendRegister")
	@ResponseBody
	public String sendRegister(HsPublicApply hsPublicApply, HttpServletResponse response) {
		hsPublicApplyService.sendRegister(hsPublicApply, response);
		return renderResult(Global.TRUE, text("上报备案表成功！"));
	}

	/**
	 * 下发批文，生成批文
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "createOutput")
	@ResponseBody
	public String createOutput(String type, String pids) {
		try {
			if ("1".equals(type)) {
				hsPublicApplyService.sendApprovals(pids);
				return renderResult(Global.TRUE, text("下发批文成功！"));
			}
			else if ("2".equals(type)) {// 生成确认书
				hsPublicApplyService.createCommits(pids);
				return renderResult(Global.TRUE, text("生成交易确认书成功！"));
			}
		}
		catch (Exception e) {
			return renderResult(Global.FALSE, text(e.getMessage()));
		}
		return renderResult(Global.FALSE, text("未知处理"));
	}

	/**
	 * 下发核准表，生成批文
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "createExamine")
	public void createExamine(HsPublicApply hsPublicApply, HttpServletResponse response) {
		hsPublicApplyService.createExamine(hsPublicApply, response);
	}
	/**
	 * 生成通知书
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "createCommit")
	public void createCommit(HsPublicApply hsPublicApply, HttpServletResponse response) {
		hsPublicApplyService.createCommit(hsPublicApply, response);
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "exportAuditData")
	public void exportAuditData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApply.setPage(new Page<>(request, response));
		Page<HsPublicApply> page = hsPublicApplyService.findAuditPageByTasks(hsPublicApply, BpmTask.STATUS_UNFINISHED);
		String fileName = "公有住房申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("公有住房申请信息表", HsPublicApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "exportAuditDoneData")
	public void exportAuditDoneData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApply.setPage(new Page<>(request, response));
		Page<HsPublicApply> page = hsPublicApplyService.findAuditPageByTasks(hsPublicApply, BpmTask.STATUS_FINISHED);
		String fileName = "公有住房申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("公有住房申请信息表", HsPublicApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "exportArchivesData")
	public void exportArchivesData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApply.setPage(new Page<>(request, response));
		Page<HsPublicApply> page = hsPublicApplyService.findArchivesPage(hsPublicApply);
		String fileName = "公有住房申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("公有住房申请信息表", HsPublicApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "exportAuditHouseData")
	public void exportAuditHouseData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApply.setPage(new Page<>(request, response));
		Page<HsPublicApply> page = hsPublicApplyService.findAuditPageByTask(hsPublicApply, "", null, BpmTask.STATUS_UNFINISHED);
		String fileName = "公有住房申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("公有住房申请信息表", HsPublicApplyHouse.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsPublicApply hsPublicApply) {
		hsPublicApply.setStatus(HsPublicApply.STATUS_DISABLE);
		hsPublicApplyService.updateStatus(hsPublicApply);
		return renderResult(Global.TRUE, text("停用公有住房购房申请成功"));
	}
	
	/**
	 * 启用数据，不能直接使用，需要检查房源，房源都有可能被用了
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsPublicApply hsPublicApply) {
		hsPublicApply.setStatus(HsPublicApply.STATUS_NORMAL);
		hsPublicApplyService.updateStatus(hsPublicApply);
		return renderResult(Global.TRUE, text("启用公有住房购房申请成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsPublicApply hsPublicApply) {
		if (!HsPublicApply.STATUS_DRAFT.equals(hsPublicApply.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		hsPublicApply.setBuyStatus(HsPublicApply.BUYSTATUS_FORGO);
		hsPublicApplyService.update(hsPublicApply);
		if (StringUtils.isNotBlank(hsPublicApply.getHouseId())) {
			hsPublicApplyService.updateHouseStatus(hsPublicApply.getHouseId(), "2");//恢复待售
		}
		hsPublicApplyService.delete(hsPublicApply);	// 这个是假删除，还是先设置一下状态，再删除的
		return renderResult(Global.TRUE, text("删除公有住房购房申请成功！"));
	}
	
}