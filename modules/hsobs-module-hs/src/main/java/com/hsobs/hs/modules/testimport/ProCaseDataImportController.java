package com.hsobs.hs.modules.testimport;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.dao.HsQwApplyerDao;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.compact.dao.HsQwCompactDao;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.hsobs.hs.modules.house.dao.HsQwPublicRentalHouseDao;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.rentfee.service.HsQwRentalFeeService;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.File;
import java.text.ParseException;
import java.util.*;

/**
 * 测试数据导入
 */
@Controller
@RequestMapping(value = "/proCase")
public class ProCaseDataImportController extends BaseController {

	@Autowired
	private HsQwRentalFeeService hsQwRentalFeeService;

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

	@Autowired
	private HsQwPublicRentalHouseDao hsQwPublicRentalHouseDao;

	@Autowired
	private HsQwApplyerService hsQwApplyerService;

	@Autowired
	private HsQwApplyService hsQwApplyService;

	@Autowired
	private HsQwApplyDao hsQwApplyDao;

	@Autowired
	private EmpUserService empUserService;

	@Autowired
	private HsQwCompactService hsQwCompactService;

	private BpmTaskService bpmTaskService = BpmUtils.getBpmTaskService();

	// 小区名映射map
	Map<String, String> estMap = new HashMap(){
		{
			put("省直屏西小区公租房", "1944578206420381696");
			put("省直秀峰小区公租房", "1943616542713827328");
		}
	};
    @Autowired
    private HsQwApplyerDao hsQwApplyerDao;
    @Autowired
    private HsQwCompactDao hsQwCompactDao;

	/**
	 * 导入excel数据
	 * @return
	 */
	@RequestMapping(value = "importByExcel")
	public void importByExcel() {
		try {
			File classPath = new File(ProCaseDataImportController.class.getResource("/").getFile());
			String fileName = classPath.getParentFile().getAbsoluteFile() + "/住房保障导入数据-0713.xlsx";
			// 解析EXCEL表单
			ExcelImport ei = new ExcelImport(fileName, 1);
			// 解析并写入房源 | 读取房源信息
			List<HsQwPublicRentalHouse> houseList = new ArrayList<>();
//			houseList = this.insertHouse(ei);
			houseList = this.getAllHouses();

			// 解析并写入订单数据
			List<HsQwApply> hsQwApplyList = new ArrayList<>();
			hsQwApplyList = this.insertApplyInfo(ei, houseList);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("导入失败：" + e.getMessage());
		}
	}

	private List<HsQwPublicRentalHouse> getAllHouses() {
		HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
		return hsQwPublicRentalHouseService.findList(query);
	}

	/**
	 * 导入excel数据-房源
	 * @return
	 */
	private List<HsQwPublicRentalHouse> insertHouse(ExcelImport ei) {
		List<HsQwPublicRentalHouse> houseList = new ArrayList();
		// 存储所有行数据
		List<Map<String, String>> dataList = this.initMap(ei);
		dataList.forEach(k -> {
			HsQwPublicRentalHouse house = this.createHouseEntity(k);
//			hsQwPublicRentalHouseDao.insert(house);
			house.setLocation("");
			house.setFilePlan("");
			house.setVrInfo("");
			house.setRemarks("");
			house.setOfficeCode("");
			house.setAssetCode("");
			houseList.add(house);
		});
		hsQwPublicRentalHouseDao.insertBatch(houseList, 500);
		return houseList;
	}

	private List<HsQwApply> insertApplyInfo(ExcelImport ei, List<HsQwPublicRentalHouse> houseList) {
		// 初始化数据
		List<Map<String, String>> dataList = this.initMap(ei);

		logger.error("开始处理数据，总计 " + dataList.size() + " 条记录");
		long startTime = System.currentTimeMillis();

		// 初始化数据集合对象
		List<HsQwApply> hsQwApplyList = new ArrayList();
		List<HsQwApplyer> hsQwApplyerList = new ArrayList();
		List<HsQwCompact> hsQwCompactList = new ArrayList();
		// 存储所有行数据
		dataList.forEach(k -> {
			// 处理申请单
			HsQwApply hsQwApply = this.createHsQwApply(k, this.getRightHouse(houseList, k));
			hsQwApplyList.add(hsQwApply);

			// 处理申请人
			HsQwApplyer hsQwApplyer = this.createHsQwApplyerEntity(k, hsQwApply);
			hsQwApplyerList.add(hsQwApplyer);

			// 处理合同
            try {
                HsQwCompact hsQwCompact = this.createCompact(k, hsQwApply);
				hsQwCompactList.add(hsQwCompact);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        });

		// 批量写入数据集对象
		hsQwApplyDao.insertBatch(hsQwApplyList, 500);
		hsQwApplyerDao.insertBatch(hsQwApplyerList, 500);
		hsQwCompactDao.insertBatch(hsQwCompactList, 500);

		long endTime = System.currentTimeMillis();
		logger.error("导入成功，共处理 " + dataList.size() + " 条记录，耗时 " + (endTime - startTime) / 1000 + " 秒");
		return hsQwApplyList;
	}

	private HsQwApplyer createHsQwApplyerEntity(Map<String, String> k, HsQwApply hsQwApply) {
		HsQwApplyer hsQwApplyer = new HsQwApplyer();
		String userId = k.get("用户账号");
		if (userId.equals("#N/A")){
			userId = k.get("手机号");
		}
		hsQwApplyer.setUserId(userId);
		hsQwApplyer.setApplyId(hsQwApply.getId());
		hsQwApplyer.setName(k.get("承租人（申请人）"));
		hsQwApplyer.setIdNum(k.get("身份证号"));
		hsQwApplyer.setOrganization("单位名称-需校验");
		hsQwApplyer.setApplyRole("0");
		hsQwApplyer.setWorkTime(new Date());
		hsQwApplyer.setPhone(k.get("手机号"));
		hsQwApplyer.setWorkPosition("");
		hsQwApplyer.setWorkTitle("");
		hsQwApplyer.setAnnualIncome("");
		hsQwApplyer.setStatus("0");
		hsQwApplyer.setCreateBy("system");
		hsQwApplyer.setUpdateBy("system");
		hsQwApplyer.setRemarks("");
		hsQwApplyer.setMarryStatus("3");
		hsQwApplyer.setDeathDate("");
		hsQwApplyer.setUserType("");
		hsQwApplyer.setWorkAge("");
		hsQwApplyer.setCreateDate(new Date());
		hsQwApplyer.setUpdateDate(new Date());
		return hsQwApplyer;
	}

	private HsQwPublicRentalHouse getRightHouse(List<HsQwPublicRentalHouse> houseList, Map<String, String> row) {
		String estateInfo = row.get("楼盘名称");;
		String buildingNum = row.get("楼号");
		String unitInfo = row.get("单元号");
		String houseNum  = buildingNum + "#" + unitInfo;

		for (HsQwPublicRentalHouse house : houseList) {
			if (house.getEstate().getName().equals(estateInfo)
					&& house.getHouseNum().equals(houseNum)){
				return house;
			}
		}
		throw new RuntimeException("未找到指定房源" + row.toString());
	}

	/**
	 * 初始化数据
	 * @param ei
	 * @return
	 */
	private List<Map<String, String>> initMap(ExcelImport ei){
		// 存储所有行数据
		List<Map<String, String>> dataList = new ArrayList<>();

		// 获取表头
		Row headerRow = ei.getRow(ei.getDataRowNum() - 1);
		List<String> headers = new ArrayList<>();
		for (int j = 0; j < ei.getLastCellNum(); j++) {
			Object headerVal = ei.getCellValue(headerRow, j);
			headers.add(headerVal != null ? headerVal.toString() : "");
		}

		// 读取数据行
		for (int i = ei.getDataRowNum(); i < ei.getLastDataRowNum(); i++) {
			Row row = ei.getRow(i);
			if (row == null) {
				continue;
			}

			Map<String, String> rowData = new HashMap<>();
			for (int j = 0; j < ei.getLastCellNum(); j++) {
				Object val = ei.getCellValue(row, j);
				String headerName = j < headers.size() ? headers.get(j) : "Column" + j;
				rowData.put(headerName, val != null ? val.toString() : "");
			}
			dataList.add(rowData);
		}
		return dataList;
	}

	/**
	 * @param k
	 * @param apply
	 * @return
	 */
	private HsQwCompact createCompact(Map<String, String> k, HsQwApply apply) throws ParseException {
		HsQwCompact compact = new HsQwCompact();
		compact.setApplyId(apply.getId());
		// 随机生成合同编号
		Random random = new Random();
		int randomNumber = random.nextInt(500000 - 100000 + 1) + 100000;
		compact.setCompactCode("ZJHT" + DateUtils.getDate("yyyyMMddHHmmss") + randomNumber + "");
		compact.setStartDate(DateUtils.parseDate(k.get("合同开始时间"),"yyyyMMdd"));
		compact.setEndDate(DateUtils.parseDate(k.get("合同结束时间"),"yyyyMMdd"));
		compact.setMonthFee(Double.valueOf(k.get("租金（元/月）")));
		compact.setStatus("0");
		compact.setCreateBy(UserUtils.getUser().getId());
		compact.setUpdateBy(UserUtils.getUser().getId());
		compact.setCreateDate(new Date());
		compact.setUpdateDate(new Date());
		compact.setRemarks("");
		return compact;
	}

	private HsQwApply createHsQwApply(Map<String, String> row, HsQwPublicRentalHouse house) {
		HsQwApply apply = new HsQwApply();
		// 取100000-500000之间随机值
		Random random = new Random();
		int randomNumber = random.nextInt(500000 - 100000 + 1) + 100000;
		apply.setFamilyIncome(randomNumber + "");
		// 取2-5之间的随机数
		apply.setId(IdGen.nextId());
		apply.setFamilyPeoples("1");
		apply.setAvgArea("");
		apply.setAvgIncome("");
		apply.setApplyScore(0D);
		apply.setApplyScorePre(0D);
		apply.setApplyedId("");
		apply.setChangeReason("");
		apply.setPriorityOrder(0);
		apply.setScoreUpdate("");
		apply.setRemarks("");
		apply.setHouseId(house.getId());
		apply.setApplyMatter("0");
		apply.setCreateBy("system");
		apply.setUpdateBy("system");
		apply.setCreateDate(new Date());
		apply.setUpdateDate(new Date());
		apply.setStatus("0");
		apply.setOfficeCode("0000064");
		apply.setApplyTime(new Date());
		apply.setApplyAccepted("0");
		apply.setEligible("0");
		apply.setRecheckStatus("0");
		apply.setRecheckAudit("0");
		apply.setOfflineRent("0");
		apply.setRentTime(new Date());
		return apply;
	}

	/**
	 * 创建房屋实体
	 */
	private HsQwPublicRentalHouse createHouseEntity(Map<String, String> row) {
		HsQwPublicRentalHouse house = new HsQwPublicRentalHouse();
		String estateInfo =row.get("楼盘名称");;
		String buildingNum = row.get("楼号");
		String unitInfo = row.get("单元号");
		String buildingArea = row.get("建筑面积（建造面积修改）");
		String houseType = row.get("户型");
		String floorNum = unitInfo.substring(0, unitInfo.length() - 2);
		String houseNum  = buildingNum + "#" + unitInfo;

		// 初始化房源基本信息
		house.setFloor(Long.valueOf(floorNum));
		house.setUnitNum(unitInfo);
		house.setHouseNum(houseNum);
		house.setBuildingNum(buildingNum);
		// 根据estMap获取estateId
		String estateId = estMap.get(estateInfo);
		house.setEstateId(estateId);
		// 判断居室类型
//		house = this.getExistHouse(house);
		house.setBuildingArea(buildingArea);
		house.setHouseType(this.getHouseType(houseType));
		house.setIsPublic("1");
		house.setStatus("0");
		house.setCreateBy("system");
		house.setUpdateBy("system");
		house.setCreateDate(new Date());
		house.setUpdateDate(new Date());
		house.setType("0");
//		house.setOfficeCode("0000064");
		house.setHouseStatus("1");
		house.setSharedArea(5.0);
		house.setHouseSaleStatus("0");
		house.setSupportArea(0.0);
		return house;
	}

	/**
	 * @param houseType
	 * @return
	 */
	private String getHouseType(String houseType) {
		if (houseType.equals("单居室")) {
			return "0";
		}
		if (houseType.equals("两居室")) {
			return "1";
		}
		throw new RuntimeException("未指定的户型，请分析后重置代码");
	}

}