package com.hsobs.hs.modules.talent.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.hsobs.hs.modules.talent.bean.HsTalentRecordMergeApply;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.hsobs.hs.modules.talent.entity.HsTalentRecordStop;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import groovy.lang.Lazy;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.talent.entity.HsTalentRecord;
import com.hsobs.hs.modules.talent.dao.HsTalentRecordDao;

/**
 * 人才补助档案表Service
 * <AUTHOR>
 * @version 2025-01-03
 */
@Service
public class HsTalentRecordService extends CrudService<HsTalentRecordDao, HsTalentRecord> {


	/**
	 * 获取单条数据
	 * @param hsTalentRecord
	 * @return
	 */
	@Override
	public HsTalentRecord get(HsTalentRecord hsTalentRecord) {
		return super.get(hsTalentRecord);
	}

	@Override
	public void addDataScopeFilter(HsTalentRecord entity) {
		SqlMap sqlMap = entity.sqlMap(); // v5.3.0+ 及之后版本
		// 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
		sqlMap.getDataScope().addFilter("extWhere", "Office",
				"a.unit_id", "a.create_by", DataScope.CTRL_PERMI_HAVE, "hsTalentRecord");
	}

	/**
	 * 查询分页数据
	 * @param hsTalentRecord 查询条件
	 * @param hsTalentRecord page 分页对象
	 * @return
	 */
	@Override
	public Page<HsTalentRecord> findPage(HsTalentRecord hsTalentRecord) {
		return super.findPage(hsTalentRecord);
	}
	
	/**
	 * 查询列表数据
	 * @param hsTalentRecord
	 * @return
	 */
	@Override
	public List<HsTalentRecord> findList(HsTalentRecord hsTalentRecord) {
		return super.findList(hsTalentRecord);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsTalentRecord
	 */
	@Override
	@Transactional
	public void save(HsTalentRecord hsTalentRecord) {
		super.save(hsTalentRecord);
	}
	
	/**
	 * 更新状态
	 * @param hsTalentRecord
	 */
	@Override
	@Transactional
	public void updateStatus(HsTalentRecord hsTalentRecord) {
		super.updateStatus(hsTalentRecord);
	}
	
	/**
	 * 删除数据
	 * @param hsTalentRecord
	 */
	@Override
	@Transactional
	public void delete(HsTalentRecord hsTalentRecord) {
		hsTalentRecord.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsTalentRecord);
	}

}