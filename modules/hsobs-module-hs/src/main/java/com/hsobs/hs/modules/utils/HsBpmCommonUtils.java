package com.hsobs.hs.modules.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hsobs.hs.modules.external.entity.FlowableProcess;
import com.hsobs.hs.modules.external.entity.FlowableTraceProcess;
import com.jeesite.common.config.Global;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Service
public class HsBpmCommonUtils {

    @Autowired
    private RestTemplate restTemplate;

    private ObjectMapper mapper = new ObjectMapper();

    private BpmTaskService bpmTaskService = BpmUtils.getBpmTaskService();


    public FlowableProcess getFlowableProcess(String formKey, String bizKey) {
        BpmTask task = bpmTaskService.getTaskByBusinessKey(formKey, bizKey, UserUtils.getUser().getId());
        StringBuilder sbUrl = new StringBuilder();
        sbUrl.append("/bpm/display/app/rest/process-instances/");
        if (task.getStatus().equals("2")){
            sbUrl.append("history/");
        }
        sbUrl.append(task.getProcIns().getId() + "/model-json?t=" + DateUtils.getDateTime());
        Object result = this.requet(sbUrl.toString(),
                HttpMethod.GET);
        try {
            return mapper.convertValue(result, FlowableProcess.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 请求flowable过程信息接口
     *
     * @return
     */
    public Object requet(String url, HttpMethod httpMethod) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Cookie", "jeesite.session.id=" + UserUtils.getSession().getId());
        // 构建请求
        HttpEntity<MultiValueMap<String, String>> requestEntity =
                new HttpEntity<>(null, headers);
        // 发送 POST 请求并解析 JSON 响应
        ResponseEntity<Object> responseEntity = restTemplate.exchange(
                "http://127.0.0.1:" + Global.getProperty("server.port") + FileUtils.path("/"
                        + Global.getProperty("server.servlet.context-path")) + "/admin" + url,
                httpMethod,
                requestEntity,
                Object.class
        );
        // 处理返回数据
        return responseEntity.getBody();
    }


    public List<FlowableTraceProcess> getProcessList(String formKey, String bizKey) {
        BpmProcIns procIns = BpmUtils.getBpmRuntimeService().getProcessInstanceByBusinessKey(formKey, bizKey);
        if (procIns == null) {
            return null;
        }
        Object result = this.requet("/bpm/display/app/rest/process-instances/" + procIns.getId() + "/trace-json?t=" + DateUtils.getDateTime(), HttpMethod.POST);
        try {
            return mapper.convertValue(result, new TypeReference<List<FlowableTraceProcess>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
