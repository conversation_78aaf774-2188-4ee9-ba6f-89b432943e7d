package com.hsobs.hs.modules.externalapi.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部API接口配置类
 * 
 * <AUTHOR>
 * @version 2024-05-20
 */
@Configuration
@ConfigurationProperties(prefix = "hsobs.external-api")
public class ExternalApiConfig {

    /**
     * 公安部门API配置
     */
    private DepartmentConfig police = new DepartmentConfig();
    
    /**
     * 房产部门API配置
     */
    private DepartmentConfig realEstate = new DepartmentConfig();
    
    /**
     * 公积金部门API配置
     */
    private DepartmentConfig housingFund = new DepartmentConfig();
    
    /**
     * 其他部门API配置
     */
    private Map<String, DepartmentConfig> others = new HashMap<>();

    public DepartmentConfig getPolice() {
        return police;
    }

    public void setPolice(DepartmentConfig police) {
        this.police = police;
    }

    public DepartmentConfig getRealEstate() {
        return realEstate;
    }

    public void setRealEstate(DepartmentConfig realEstate) {
        this.realEstate = realEstate;
    }

    public DepartmentConfig getHousingFund() {
        return housingFund;
    }

    public void setHousingFund(DepartmentConfig housingFund) {
        this.housingFund = housingFund;
    }

    public Map<String, DepartmentConfig> getOthers() {
        return others;
    }

    public void setOthers(Map<String, DepartmentConfig> others) {
        this.others = others;
    }

    /**
     * 部门API配置
     */
    public static class DepartmentConfig {
        /**
         * 基础URL
         */
        private String baseUrl;
        
        /**
         * 认证令牌
         */
        private String token;
        
        /**
         * 应用ID
         */
        private String appId;
        
        /**
         * 应用密钥
         */
        private String appSecret;
        
        /**
         * 是否启用
         */
        private boolean enabled = false;
        
        /**
         * 接口配置
         */
        private Map<String, ApiConfig> apis = new HashMap<>();

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppSecret() {
            return appSecret;
        }

        public void setAppSecret(String appSecret) {
            this.appSecret = appSecret;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public Map<String, ApiConfig> getApis() {
            return apis;
        }

        public void setApis(Map<String, ApiConfig> apis) {
            this.apis = apis;
        }
    }
    
    /**
     * API接口配置
     */
    public static class ApiConfig {
        /**
         * 接口路径
         */
        private String path;
        
        /**
         * 请求方法
         */
        private String method = "POST";
        
        /**
         * 超时时间（毫秒）
         */
        private int timeout = 10000;
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * 其他配置参数
         */
        private Map<String, String> params = new HashMap<>();

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getMethod() {
            return method;
        }

        public void setMethod(String method) {
            this.method = method;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public Map<String, String> getParams() {
            return params;
        }

        public void setParams(Map<String, String> params) {
            this.params = params;
        }
    }
}
