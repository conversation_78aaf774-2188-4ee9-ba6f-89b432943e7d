package com.hsobs.hs.modules.formmanage.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormReport;
import com.hsobs.hs.modules.formmanage.dao.HsDataFormReportDao;

/**
 * 数据表单报送记录Service
 * <AUTHOR>
 * @version 2025-02-21
 */
@Service
public class HsDataFormReportService extends CrudService<HsDataFormReportDao, HsDataFormReport> {
	
	/**
	 * 获取单条数据
	 * @param hsDataFormReport
	 * @return
	 */
	@Override
	public HsDataFormReport get(HsDataFormReport hsDataFormReport) {
		return super.get(hsDataFormReport);
	}
	
	/**
	 * 查询分页数据
	 * @param hsDataFormReport 查询条件
	 * @param hsDataFormReport page 分页对象
	 * @return
	 */
	@Override
	public Page<HsDataFormReport> findPage(HsDataFormReport hsDataFormReport) {
		return super.findPage(hsDataFormReport);
	}
	
	/**
	 * 查询列表数据
	 * @param hsDataFormReport
	 * @return
	 */
	@Override
	public List<HsDataFormReport> findList(HsDataFormReport hsDataFormReport) {
		return super.findList(hsDataFormReport);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsDataFormReport
	 */
	@Override
	@Transactional
	public void save(HsDataFormReport hsDataFormReport) {
		super.save(hsDataFormReport);
	}
	
	/**
	 * 更新状态
	 * @param hsDataFormReport
	 */
	@Override
	@Transactional
	public void updateStatus(HsDataFormReport hsDataFormReport) {
		super.updateStatus(hsDataFormReport);
	}
	
	/**
	 * 删除数据
	 * @param hsDataFormReport
	 */
	@Override
	@Transactional
	public void delete(HsDataFormReport hsDataFormReport) {
		super.delete(hsDataFormReport);
	}

	public void insertBatch(List<HsDataFormReport> reportList, int batchNum) {
		this.dao.insertBatch(reportList, batchNum);
	}
}