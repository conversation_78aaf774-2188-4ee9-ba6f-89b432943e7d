package com.hsobs.hs.modules.contract.service;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

import com.hsobs.hs.modules.contract.entity.HsContractRecordData;
import com.hsobs.hs.modules.contract.entity.HsContractRecordField;
import com.hsobs.hs.modules.utils.UeditorExporter;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.contract.entity.HsContractRecord;
import com.hsobs.hs.modules.contract.dao.HsContractRecordDao;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 合同Service
 * <AUTHOR>
 * @version 2025-01-22
 */
@Service
public class HsContractRecordService extends CrudService<HsContractRecordDao, HsContractRecord> {


	@Autowired
	private HsContractRecordDataService hsContractRecordDataService;
	@Autowired
	private HsContractRecordFieldService hsContractRecordFieldService;
	@Autowired
	FileUploadService fileUploadService;

	/**
	 * 获取单条数据
	 * @param hsContractRecord
	 * @return
	 */
	@Override
	public HsContractRecord get(HsContractRecord hsContractRecord) {
		return super.get(hsContractRecord);
	}
	
	/**
	 * 查询分页数据
	 * @param hsContractRecord 查询条件
	 * @param hsContractRecord page 分页对象
	 * @return
	 */
	@Override
	public Page<HsContractRecord> findPage(HsContractRecord hsContractRecord) {
		return super.findPage(hsContractRecord);
	}
	
	/**
	 * 查询列表数据
	 * @param hsContractRecord
	 * @return
	 */
	@Override
	public List<HsContractRecord> findList(HsContractRecord hsContractRecord) {
		return super.findList(hsContractRecord);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsContractRecord
	 */
	@Override
	@Transactional
	public void save(HsContractRecord hsContractRecord) {

		if (hsContractRecord.getContractSource() == null) {
			hsContractRecord.setContractSource("1");
		}

		HsContractRecord query = new HsContractRecord();
		HsContractRecord recordDb = null;
		if ("1".equals(hsContractRecord.getContractSource())) {
			query.setContractNo(hsContractRecord.getContractNo());
			List<HsContractRecord> dbList = this.findList(query);
			if (dbList != null && !dbList.isEmpty()) {
				recordDb = dbList.get(0);
				if (hsContractRecord.getId() == null || !recordDb.getId().equals(hsContractRecord.getId())) {
					throw new ServiceException(text(String.format("该合同编号(%s)已存在", hsContractRecord.getContractNo())));
				}
			}
		}
		super.save(hsContractRecord);
		if ("0".equals(hsContractRecord.getContractSource()) && StringUtils.isEmpty(hsContractRecord.getContractNo())) {
			hsContractRecord.setContractNo(hsContractRecord.getId());
			super.update(hsContractRecord);
		}

		if ("0".equals(hsContractRecord.getContractSource())) {
			HsContractRecordData recordData = hsContractRecord.getHsContractRecordData();
			String msg = "";
			if (recordData != null) {
				msg = recordData.getContent();
			}

			hsContractRecordFieldService.deleteByRecordId(hsContractRecord.getId());
			if (hsContractRecord.getFieldList() != null) {
				for (HsContractRecordField hsContractRecordField : hsContractRecord.getFieldList()) {
					hsContractRecordField.setId(null);
					hsContractRecordField.setRecordId(hsContractRecord.getId());
					hsContractRecordFieldService.save(hsContractRecordField);

					msg = msg.replaceAll("\\$\\{" + hsContractRecordField.getFieldCode() + "}", hsContractRecordField.getFieldValue());
				}
			}

			msg = msg.replaceAll("\\$\\{contractName}", hsContractRecord.getContractName());
			msg = msg.replaceAll("\\$\\{contractNo}", hsContractRecord.getContractNo());
			msg = msg.replaceAll("\\$\\{applyName}", hsContractRecord.getApplyName());
			msg = msg.replaceAll("\\$\\{applySfz}", hsContractRecord.getApplySfz());
			msg = msg.replaceAll("\\$\\{applyTel}", hsContractRecord.getApplyTel());
			msg = msg.replaceAll("\\$\\{workUnit}", hsContractRecord.getWorkUnit());
			msg = msg.replaceAll("\\$\\{houseAddr}", hsContractRecord.getHouseAddr());
			msg = msg.replaceAll("\\$\\{unitNo}", hsContractRecord.getUnitNo());
			msg = msg.replaceAll("\\$\\{houseArea}", hsContractRecord.getHouseArea());
			msg = msg.replaceAll("\\$\\{houseType}", hsContractRecord.getHouseType());
			msg = msg.replaceAll("\\$\\{houseRent}", hsContractRecord.getHouseRent());
			msg = msg.replaceAll("\\$\\{startTime}", DateUtils.formatDate(hsContractRecord.getStartTime()));
			msg = msg.replaceAll("\\$\\{endTime}", DateUtils.formatDate(hsContractRecord.getEndTime()));

			if (recordData != null) {
				recordData.setContent(msg);
				recordData.setId(hsContractRecord.getId());

				if (hsContractRecordDataService.get(recordData.getId()) == null) {
					hsContractRecordDataService.insert(recordData);
				} else {
					hsContractRecordDataService.update(recordData);
				}
			}
			// 生成word文件
			this.saveGenWord(hsContractRecord, recordData);
		}

		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsContractRecord, hsContractRecord.getId(), "hsContractRecord_file");
	}

	private void saveGenWord(HsContractRecord hsContractRecord, HsContractRecordData recordData) {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		try {
			UeditorExporter.exportToWord(recordData.getContent(), outputStream);
			String md5 = DigestUtils.md5Hex(outputStream.toByteArray());

			String fileName = String.format("%s-合同.docx", hsContractRecord.getContractName());
			String bizType = "hsContractRecord_file_gen";

			FileUploadParams fileParams = new FileUploadParams();
			MultipartFile multipartFile = new MockMultipartFile(
					fileName,
					fileName,
					"application/octet-stream",
					outputStream.toByteArray()
			);
			fileParams.setBizKey(hsContractRecord.getId());
			fileParams.setBizType(bizType);
			fileParams.setFile(multipartFile);
			fileParams.setFileMd5(md5);
			fileParams.setFileName(fileName);
			FileUpload fileUpload = new FileUpload();
			fileUpload.setBizKey(hsContractRecord.getId());
			fileUpload.setBizType(bizType);
			Map<String, Object> uploadedFileMap = fileUploadService.uploadFile(fileUpload, fileParams);
			hsContractRecord.setDataMap(uploadedFileMap);
			FileUploadUtils.saveFileUpload(hsContractRecord, hsContractRecord.getId(), bizType);
		} catch (Exception e) {
			throw new ServiceException(text("合同文件生成失败！"));
		}
	}

	/**
	 * 更新状态
	 * @param hsContractRecord
	 */
	@Override
	@Transactional
	public void updateStatus(HsContractRecord hsContractRecord) {
		super.updateStatus(hsContractRecord);
	}
	
	/**
	 * 删除数据
	 * @param hsContractRecord
	 */
	@Override
	@Transactional
	public void delete(HsContractRecord hsContractRecord) {
		super.delete(hsContractRecord);
	}
	
}