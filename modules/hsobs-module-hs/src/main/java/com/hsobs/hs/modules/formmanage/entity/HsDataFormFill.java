package com.hsobs.hs.modules.formmanage.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 数据表单填写记录Entity
 * <AUTHOR>
 * @version 2025-02-23
 */
@Table(name="hs_data_form_fill", alias="a", label="数据表单填写记录信息", columns={
		@Column(name="id", attrName="id", label="填写ID", isPK=true),
		@Column(name="delivery_id", attrName="deliveryId", label="表单下发消息ID"),
		@Column(name="delivery_record_id", attrName="deliveryRecordId", label="表单下发记录ID"),
		@Column(name="template_id", attrName="templateId", label="表单模板ID"),
		@Column(name="fill_user_code", attrName="fillUserCode", label="填写者用户编码"),
		@Column(name="fill_user_name", attrName="fillUserName", label="填写者用户姓名", queryType=QueryType.LIKE),
		@Column(name="fill_time", attrName="fillTime", label="填报时间", isUpdateForce=true),
		@Column(name="is_submitted", attrName="isSubmitted", label="提交状态"),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, orderBy="a.update_date DESC"
)
public class HsDataFormFill extends DataEntity<HsDataFormFill> {
	
	private static final long serialVersionUID = 1L;
	private String deliveryId;		// 表单下发消息ID
	private String deliveryRecordId;		// 表单下发记录ID
	private String templateId;		// 表单模板ID
	private String fillUserCode;		// 填写者用户编码
	private String fillUserName;		// 填写者用户姓名
	private Date fillTime;		// 填报时间
	private String isSubmitted;		// 提交状态
	private String validTag;		// 是否有效;1-有效 0-无效

	private List<HsDataFormFillRecord> recordList;
	private List<HsDataFormFillRecordFnl> recordFnlList;
	private HsDataFormDelivery delivery;


	private Long deliveryTotal; // 下发总数
	private Long filledTotal; // 已填写总数

	private String opType;

	public HsDataFormFill() {
		this(null);
	}
	
	public HsDataFormFill(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="表单下发消息ID长度不能超过 64 个字符")
	public String getDeliveryId() {
		return deliveryId;
	}

	public void setDeliveryId(String deliveryId) {
		this.deliveryId = deliveryId;
	}
	
	@Size(min=0, max=64, message="表单下发记录ID长度不能超过 64 个字符")
	public String getDeliveryRecordId() {
		return deliveryRecordId;
	}

	public void setDeliveryRecordId(String deliveryRecordId) {
		this.deliveryRecordId = deliveryRecordId;
	}
	
	@Size(min=0, max=64, message="表单模板ID长度不能超过 64 个字符")
	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	
	@Size(min=0, max=64, message="填写者用户编码长度不能超过 64 个字符")
	public String getFillUserCode() {
		return fillUserCode;
	}

	public void setFillUserCode(String fillUserCode) {
		this.fillUserCode = fillUserCode;
	}
	
	@Size(min=0, max=100, message="填写者用户姓名长度不能超过 100 个字符")
	public String getFillUserName() {
		return fillUserName;
	}

	public void setFillUserName(String fillUserName) {
		this.fillUserName = fillUserName;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getFillTime() {
		return fillTime;
	}

	public void setFillTime(Date fillTime) {
		this.fillTime = fillTime;
	}
	
	@Size(min=0, max=10, message="提交状态长度不能超过 10 个字符")
	public String getIsSubmitted() {
		return isSubmitted;
	}

	public void setIsSubmitted(String isSubmitted) {
		this.isSubmitted = isSubmitted;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public List<HsDataFormFillRecord> getRecordList() {
		return recordList;
	}

	public void setRecordList(List<HsDataFormFillRecord> recordList) {
		this.recordList = recordList;
	}

	public List<HsDataFormFillRecordFnl> getRecordFnlList() {
		return recordFnlList;
	}

	public void setRecordFnlList(List<HsDataFormFillRecordFnl> recordFnlList) {
		this.recordFnlList = recordFnlList;
	}

	public HsDataFormDelivery getDelivery() {
		return delivery;
	}

	public void setDelivery(HsDataFormDelivery delivery) {
		this.delivery = delivery;
	}

	public Long getDeliveryTotal() {
		return deliveryTotal;
	}

	public void setDeliveryTotal(Long deliveryTotal) {
		this.deliveryTotal = deliveryTotal;
	}

	public Long getFilledTotal() {
		return filledTotal;
	}

	public void setFilledTotal(Long filledTotal) {
		this.filledTotal = filledTotal;
	}

	public String getOpType() {
		return opType;
	}

	public void setOpType(String opType) {
		this.opType = opType;
	}
}