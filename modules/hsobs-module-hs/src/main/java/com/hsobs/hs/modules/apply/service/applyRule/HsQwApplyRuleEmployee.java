package com.hsobs.hs.modules.apply.service.applyRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 申请人需为在榕省直单位或中央驻榕单位的在职、退休干部职工，或服务满5年的合同制员工。
 */
@Service
public class HsQwApplyRuleEmployee implements HsQwApplyRuleMethod {

    @Override
    public void execute(HsQwApply hsQwApply, Map<String, HsQwFormAlarm> hsQwFormAlarmMap) {
        // 申请人需为在榕省直单位或中央驻榕单位的在职、退休干部职工，或服务满5年的合同制员工。
        HsQwApplyer mainApplyer = null;
        // 获取hsQwApplyerList中applyRole为0的主申请人的List序列号
        String id = "";
        for (int i = 0; i < hsQwApply.getHsQwApplyerList().size(); i++) {
            if (hsQwApply.getHsQwApplyerList().get(i).getApplyRole().equals("0")) {
                mainApplyer = hsQwApply.getHsQwApplyerList().get(i);
                id = mainApplyer.getId();
                break;
            }
        }
        // todo 判断是否是在职员工，调用外部接口

        // 判断工龄是否满5年
        if (mainApplyer.getWorkAge() != null) {
            if (Integer.parseInt(mainApplyer.getWorkAge()) < 5) {
                HsQwApplyRuleMethod.putAlarmMap(hsQwFormAlarmMap,
                        "hsQwApply.hsQwApplyerList[" + id + "].workAge",
                        "员工目前工龄为" + mainApplyer.getWorkAge() + "年，不满5年",
                        "0", hsQwApply.getId());
                return;
            }
        }
    }
}
