package com.hsobs.hs.modules.datamanage.service;

import java.util.List;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.TreeService;
import com.hsobs.hs.modules.datamanage.entity.HsFileClass;
import com.hsobs.hs.modules.datamanage.dao.HsFileClassDao;

/**
 * 档案目录Service
 * <AUTHOR>
 * @version 2025-01-19
 */
@Service
public class HsFileClassService extends TreeService<HsFileClassDao, HsFileClass> {
	
	/**
	 * 获取单条数据
	 * @param hsFileClass
	 * @return
	 */
	@Override
	public HsFileClass get(HsFileClass hsFileClass) {
		return super.get(hsFileClass);
	}
	
	/**
	 * 查询分页数据
	 * @param hsFileClass 查询条件
	 * @param hsFileClass page 分页对象
	 * @return
	 */
	@Override
	public Page<HsFileClass> findPage(HsFileClass hsFileClass) {
		return super.findPage(hsFileClass);
	}
	
	/**
	 * 查询列表数据
	 * @param hsFileClass
	 * @return
	 */
	@Override
	public List<HsFileClass> findList(HsFileClass hsFileClass) {
		return super.findList(hsFileClass);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsFileClass
	 */
	@Override
	@Transactional
	public void save(HsFileClass hsFileClass) {

		HsFileClass query = new HsFileClass();
		query.setClassCode(hsFileClass.getClassCode());
		if (StringUtils.isNotBlank(hsFileClass.getId())) {
			query.sqlMap().getWhere().and("id", QueryType.NOT_IN, hsFileClass.getId());
		}
		List<HsFileClass> list = findList(query);
		if (ListUtils.isNotEmpty(list)) {

			throw new ServiceException(text(String.format("该分类编码(%s)已存在", hsFileClass.getClassCode())));
		}
		query = new HsFileClass();
		query.setParentCode(hsFileClass.getParentCode());
		query.setClassName(hsFileClass.getClassName());
		if (StringUtils.isNotBlank(hsFileClass.getId())) {
			query.sqlMap().getWhere().and("id", QueryType.NOT_IN, hsFileClass.getId());
		}
		list = findList(query);
		if (ListUtils.isNotEmpty(list)) {
			throw new ServiceException(text(String.format("同层级分类名称(%s)已存在", hsFileClass.getClassName())));
		}
		query = new HsFileClass();
		query.setTreeSort(hsFileClass.getTreeSort());
		if (StringUtils.isNotBlank(hsFileClass.getId())) {
			query.sqlMap().getWhere().and("id", QueryType.NOT_IN, hsFileClass.getId());
		}
		list = findList(query);
		if (ListUtils.isNotEmpty(list)) {
			throw new ServiceException(text(String.format("该排序号(%s)已存在", hsFileClass.getTreeSort())));
		}

		super.save(hsFileClass);
	}
	
	/**
	 * 更新状态
	 * @param hsFileClass
	 */
	@Override
	@Transactional
	public void updateStatus(HsFileClass hsFileClass) {
		super.updateStatus(hsFileClass);
	}
	
	/**
	 * 删除数据
	 * @param hsFileClass
	 */
	@Override
	@Transactional
	public void delete(HsFileClass hsFileClass) {
		super.delete(hsFileClass);
	}
	
}