package com.hsobs.hs.modules.dataintelligence.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceReview;

import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计DAO接口   公租房资格年审统计
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataIntelligenceReviewDao extends CrudDao<DataIntelligenceReview> {

    List<Map<String, Object>> countReviewArea(String sqlOtherWhere, String sqlOrderBy);
    List<Map<String, Object>> countReviewArea(Map<String, Object> map);

    List<Map<String, Object>> countReviewCompare(String sqlOtherWhere);

    List<Map<String, Object>> countReviewType(String sqlWhere);
}