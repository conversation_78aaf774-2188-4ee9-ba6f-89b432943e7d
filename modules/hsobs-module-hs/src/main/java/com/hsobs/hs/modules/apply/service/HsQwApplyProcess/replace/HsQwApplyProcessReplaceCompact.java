package com.hsobs.hs.modules.apply.service.HsQwApplyProcess.replace;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class HsQwApplyProcessReplaceCompact extends HsQwPersonalReplaceDefault {
    @Override
    public String getStatus() {
        return "重新签订合同";
    }

    @Override
    public void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        hsQwApply.setRentTime(new Date());//更新租赁时间，默认上传合同时间
        this.process(hsQwApply, hsQwApplyService);
        hsQwApply.getCompact().setApplyId(hsQwApply.getId());
        this.saveCompact(hsQwApply.getCompact(),hsQwApplyService);
        this.saveCompactFile(hsQwApply, hsQwApplyService);
    }

    private void saveCompact(HsQwCompact compact, HsQwApplyService hsQwApplyService) {
        hsQwApplyService.getHsQwCompactService().save(compact);
        compact.setStatus(HsQwCompact.STATUS_AUDIT);
        hsQwApplyService.getHsQwCompactService().updateStatus(compact);
    }

    void saveCompactFile(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService){
        // 保存合同文件
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getCompact().getId(), "hsQwApply_compact");
    }

}
