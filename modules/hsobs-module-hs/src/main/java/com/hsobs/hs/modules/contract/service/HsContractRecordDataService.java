package com.hsobs.hs.modules.contract.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.contract.entity.HsContractRecordData;
import com.hsobs.hs.modules.contract.dao.HsContractRecordDataDao;

/**
 * 合同详情表Service
 * <AUTHOR>
 * @version 2025-01-22
 */
@Service
public class HsContractRecordDataService extends CrudService<HsContractRecordDataDao, HsContractRecordData> {
	
	/**
	 * 获取单条数据
	 * @param hsContractRecordData
	 * @return
	 */
	@Override
	public HsContractRecordData get(HsContractRecordData hsContractRecordData) {
		return super.get(hsContractRecordData);
	}
	
	/**
	 * 查询分页数据
	 * @param hsContractRecordData 查询条件
	 * @param hsContractRecordData page 分页对象
	 * @return
	 */
	@Override
	public Page<HsContractRecordData> findPage(HsContractRecordData hsContractRecordData) {
		return super.findPage(hsContractRecordData);
	}
	
	/**
	 * 查询列表数据
	 * @param hsContractRecordData
	 * @return
	 */
	@Override
	public List<HsContractRecordData> findList(HsContractRecordData hsContractRecordData) {
		return super.findList(hsContractRecordData);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsContractRecordData
	 */
	@Override
	@Transactional
	public void save(HsContractRecordData hsContractRecordData) {
		super.save(hsContractRecordData);
	}
	
	/**
	 * 更新状态
	 * @param hsContractRecordData
	 */
	@Override
	@Transactional
	public void updateStatus(HsContractRecordData hsContractRecordData) {
		super.updateStatus(hsContractRecordData);
	}
	
	/**
	 * 删除数据
	 * @param hsContractRecordData
	 */
	@Override
	@Transactional
	public void delete(HsContractRecordData hsContractRecordData) {
		super.delete(hsContractRecordData);
	}
	
}