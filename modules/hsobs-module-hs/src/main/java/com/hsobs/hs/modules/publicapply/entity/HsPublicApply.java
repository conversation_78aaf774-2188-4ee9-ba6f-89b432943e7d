package com.hsobs.hs.modules.publicapply.entity;

import javax.validation.Valid;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import javax.validation.constraints.Size;
import java.util.Date;

import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

/**
 * 公有住房-购房申请Entity
 * <AUTHOR>
 * @version 2024-12-10
 */
@Table(name="hs_public_apply", alias="a", label="公有住房-购房申请信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true, queryType=QueryType.LIKE),
		@Column(name="sale_plan_id", attrName="salePlanId", label="配售id"),
		@Column(name="house_id", attrName="houseId", label="房源id"),
		@Column(name="reason", attrName="reason", label="申请理由"),
		@Column(name="application_material", attrName="applicationMaterial", label="申请材料"),
		@Column(name="estimated_price", attrName="estimatedPrice", label="房源估价"),
		@Column(name="price", attrName="price", label="房源核价"),
		@Column(name="signature", attrName="signature", label="资格确认签章"),
		@Column(name="contract_id", attrName="contractId", label="合同编号"),
		@Column(name="office_code", attrName="officeCode", label="单位编码"),
		@Column(name="have_house", attrName="haveHouse", label="是否有住房"),
		@Column(name="house_address", attrName="houseAddress", label="当前住址"),
		@Column(name="house_type", attrName="houseType", label="当前房屋类型"),
		@Column(name="unit_price", attrName="unitPrice", label="正常单价"),
		@Column(name="exceed_unit_price", attrName="exceedUnitPrice", label="超出单价"),
		@Column(name="other_unit_price", attrName="otherUnitPrice", label="附属间单价"),
		@Column(name="estinmate_id", attrName="estinmateId", label="评估报告id"),
		@Column(name="approval_status", attrName="approvalStatus", label="下发批文状态"),
		@Column(name="contract_status", attrName="contractStatus", label="合同签订状态"),
		@Column(name="register_status", attrName="registerStatus", label="备案状态"),
		@Column(name="buy_status", attrName="buyStatus", label="购买状态"),
		@Column(name="result", attrName="result", label="审核意见", queryType=QueryType.LIKE),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsPublicApplyer.class, alias = "o",
				on = "o.apply_id = a.id and o.apply_role = 0", attrName="mainApplyer",
				columns = {@Column(includeEntity = HsPublicApplyer.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "h",
				on = "h.id = a.house_id and h.status=0", attrName = "house",
				columns = {@Column(includeEntity = HsQwPublicRentalHouse.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "e",
				on = "e.id = h.estate_id and e.status=0", attrName="house.estate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "office",
				on = "office.office_code = a.office_code", attrName="office",
				columns = {@Column(includeEntity = Office.class)}),
	}, 
		extWhereKeys="dsf",
		orderBy="a.update_date DESC"
)
public class HsPublicApply extends BpmEntity<HsPublicApply> {

	public static final String APPLY_STATUS_DEFAULT = "-1"; //默认、删除的申请单
	public static final String APPLY_STATUS_DRAFT = "公有住房申请";//0
	public static final String APPLY_STATUS_CHECK_APPLY = "生成确认单";//0
	public static final String APPLY_STATUS_AUDIT_ORGHAND_FIRST = "经办初审";//1
	public static final String APPLY_STATUS_AUDIT_ORGOFFICE_FIRST = "处室领导初审";//2
	public static final String APPLY_STATUS_AUDIT_RESPONSIBLE_FIRST = "分管领导初审";//3
	public static final String APPLY_STATUS_AUDIT_BUREAU_FIRST = "局务会初审";//4
	public static final String APPLY_STATUS_AUDIT_APPROVAL = "下发批文";//5
	public static final String APPLY_STATUS_AUDIT_APPRAISE = "估价";//6
	public static final String APPLY_STATUS_AUDIT_RECORD = "备案";//6
	public static final String APPLY_STATUS_AUDIT_RECORD_CHECK = "备案审核";//6
	public static final String APPLY_STATUS_AUDIT_COCOUNT = "核算";//6
	public static final String APPLY_STATUS_AUDIT_ACCOUNTING = "核准价格";//8
	public static final String APPLY_STATUS_AUDIT_CONTRACT = "签订合同";//8
	public static final String APPLY_STATUS_AUDIT_CONFIRMATION = "交易通知书";//13

	public static final String APPROVAL_STATUS_NO = "0";	// 未下发
	public static final String APPROVAL_STATUS_YES = "1";	// 已下发

	public static final String BUYFLAG_NO = "0";	// 不购买
	public static final String BUYFLAG_YES = "1";	// 购买

	public static final String BUYSTATUS_NO = "0";	// 未购买(流程中)
	public static final String BUYSTATUS_YES = "1";	// 已购买(流程结束)
	public static final String BUYSTATUS_FORGO = "2";	// 已购买(流程结束)

	@ExcelFields({
			@ExcelField(title = "申请编号", attrName = "id", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "申请人", attrName = "mainApplyer.name", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "身份证号", attrName = "mainApplyer.idNum", align = ExcelField.Align.CENTER, sort = 40),
			@ExcelField(title = "联系电话", attrName = "mainApplyer.phone", align = ExcelField.Align.CENTER, sort = 50),
			@ExcelField(title = "工作单位", attrName = "office.officeName", align = ExcelField.Align.CENTER, sort = 60),
			@ExcelField(title = "申请理由", attrName = "remarks", align = ExcelField.Align.CENTER, sort = 70),
			@ExcelField(title = "申请时间", attrName = "createDate", align = ExcelField.Align.LEFT, sort = 80, dataFormat = "yyyy-MM-dd"),
	})

	private static final long serialVersionUID = 1L;
	private String salePlanId;
	private String houseId;		// 房源id
	private String reason;		// 申请理由;多个申请材料，怎么处理
	private String applicationMaterial;		// 申请材料
	private String estimatedPrice;		// 房源估价
	private String price;		// 房源核价
	private String signature;		// 资格确认签章
	private String contractId;		// 合同编号
	private String officeCode;		// 单位编码

	private String bugFlag;			// 是否购买 0不购买，1购买
	private String buyStatus;		// 购买状态（0,未购买(购买流程中)，1已购买，2放弃购买）

	private String haveHouse;
	private String houseAddress;
	private String houseType;
	private String unitPrice;
	private String exceedUnitPrice;
	private String otherUnitPrice;
	private String estinmateId;
	private String approvalStatus;
	private String contractStatus;
	private String registerStatus;
	private String result;

	private Integer applyStatus;		// 申请状态
	private Office office;
	private String applyTitle;
	private String flowStatus;
	private HsPublicApplyer mainApplyer;
	private HsQwPublicRentalHouse house;
	private List<HsPublicApplyer> hsPublicApplyerList = ListUtils.newArrayList();		// 子表列表
	private HsPublicSaleApply publicSaleApply;

	private String readOnly;
	private String houseInfo;

	private String type;		// 用于传递参数
	private String title;		// 用于传递参数

	public HsPublicApply() {
		this(null);
		readOnly = "false";
	}
	
	public HsPublicApply(String id){
		super(id);
	}

	public String getSalePlanId() {
		return salePlanId;
	}

	public void setSalePlanId(String salePlanId) {
		this.salePlanId = salePlanId;
	}

	@Size(min=0, max=64, message="房源id长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		if (!StringUtils.isBlank(houseId)){
			houseId = houseId.replaceAll("^,+|,+$", "");;
		}
		this.houseId = houseId;
	}
	
	@Size(min=0, max=900, message="申请理由;多个申请材料，怎么处理长度不能超过 900 个字符")
	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	@Size(min=0, max=64, message="申请材料长度不能超过 64 个字符")
	public String getApplicationMaterial() {
		return applicationMaterial;
	}

	public void setApplicationMaterial(String applicationMaterial) {
		this.applicationMaterial = applicationMaterial;
	}

	@Size(min=0, max=64, message="房源估价长度不能超过 64 个字符")
	public String getEstimatedPrice() {
		return estimatedPrice;
	}

	public void setEstimatedPrice(String estimatedPrice) {
		this.estimatedPrice = estimatedPrice;
	}
	
	@Size(min=0, max=64, message="房源核价长度不能超过 64 个字符")
	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}
	
	@Size(min=0, max=64, message="资格确认签章长度不能超过 64 个字符")
	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}
	
	@Size(min=0, max=64, message="合同编号长度不能超过 64 个字符")
	public String getContractId() {
		return contractId;
	}

	public void setContractId(String contractId) {
		this.contractId = contractId;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getBugFlag() {
		return bugFlag;
	}

	public void setBugFlag(String bugFlag) {
		this.bugFlag = bugFlag;
	}

	public String getBuyStatus() {
		return buyStatus;
	}

	public void setBuyStatus(String buyStatus) {
		this.buyStatus = buyStatus;
	}

	public String getHaveHouse() {
		return haveHouse;
	}

	public void setHaveHouse(String haveHouse) {
		this.haveHouse = haveHouse;
	}

	public String getHouseAddress() {
		return houseAddress;
	}

	public void setHouseAddress(String houseAddress) {
		this.houseAddress = houseAddress;
	}

	public String getHouseType() {
		return houseType;
	}

	public void setHouseType(String houseType) {
		this.houseType = houseType;
	}

	public String getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(String unitPrice) {
		this.unitPrice = unitPrice;
	}

	public String getExceedUnitPrice() {
		return exceedUnitPrice;
	}

	public void setExceedUnitPrice(String exceedUnitPrice) {
		this.exceedUnitPrice = exceedUnitPrice;
	}

	public String getOtherUnitPrice() {
		return otherUnitPrice;
	}

	public void setOtherUnitPrice(String otherUnitPrice) {
		this.otherUnitPrice = otherUnitPrice;
	}

	public String getEstinmateId() {
		return estinmateId;
	}

	public void setEstinmateId(String estinmateId) {
		this.estinmateId = estinmateId;
	}

	public String getApprovalStatus() {
		return approvalStatus;
	}

	public void setApprovalStatus(String approvalStatus) {
		this.approvalStatus = approvalStatus;
	}

	public String getContractStatus() {
		return contractStatus;
	}

	public void setContractStatus(String contractStatus) {
		this.contractStatus = contractStatus;
	}

	public String getRegisterStatus() {
		return registerStatus;
	}

	public void setRegisterStatus(String registerStatus) {
		this.registerStatus = registerStatus;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}

	@Valid
	public List<HsPublicApplyer> getHsPublicApplyerList() {
		return hsPublicApplyerList;
	}

	public void setHsPublicApplyerList(List<HsPublicApplyer> hsPublicApplyerList) {
		this.hsPublicApplyerList = hsPublicApplyerList;
	}

	public HsPublicSaleApply getPublicSaleApply() {
		return publicSaleApply;
	}

	public void setPublicSaleApply(HsPublicSaleApply publicSaleApply) {
		this.publicSaleApply = publicSaleApply;
	}

	@Override
    public String getStatus() {
        return status;
    }

    @Override
    public void setStatus(String status) {
        this.status = status;
    }

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

    public HsPublicApplyer getMainApplyer() {
        return mainApplyer;
    }

    public void setMainApplyer(HsPublicApplyer mainApplyer) {
        this.mainApplyer = mainApplyer;
    }

	public HsQwPublicRentalHouse getHouse() {
		return house;
	}

	public void setHouse(HsQwPublicRentalHouse house) {
		this.house = house;
	}

	public String getApplyTitle() {
		if(this.mainApplyer != null)
        	return this.mainApplyer.getName() + " 于" + DateUtils.formatDateTime(this.getCreateDate()) + " 发起了公有住房申请";
		return "";
    }

    public void setApplyTitle(String applyTitle) {
        this.applyTitle = applyTitle;
    }

	public String getFlowStatus() {
		return this.flowStatus;
	}

	public void setFlowStatus(String flowStatus) {
		this.flowStatus = flowStatus;
	}

	public String getReadOnly() {
		return this.readOnly;
	}

	public void setReadOnly(String readOnly) {
		this.readOnly = readOnly;
	}

	public String getHouseInfo() {
		return houseInfo;
	}

	public void setHouseInfo(String houseInfo) {
		this.houseInfo = houseInfo;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}
}