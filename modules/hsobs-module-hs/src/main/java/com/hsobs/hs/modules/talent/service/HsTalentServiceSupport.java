package com.hsobs.hs.modules.talent.service;

import com.hsobs.hs.modules.talent.bean.HsTalentRecordMergeApply;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.hsobs.hs.modules.talent.entity.HsTalentRecord;
import com.hsobs.hs.modules.talent.entity.HsTalentRecordStop;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class HsTalentServiceSupport {

    @Autowired
    private HsTalentIntroductionApplyService hsTalentIntroductionApplyService;
    @Autowired
    private HsTalentRecordStopService hsTalentRecordStopService;

    public void loadApplyData(HsTalentRecord hsTalentRecord) {

        List<HsTalentRecordMergeApply> mergeApplyList = new ArrayList<>();
        HsTalentRecordMergeApply mergeApply = null;

        HsTalentIntroductionApply query = new HsTalentIntroductionApply();
        query.setApplyNo(hsTalentRecord.getUserNo());
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setStatus_in(new String[] { "0", "4", "5", "9" });
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        List<HsTalentIntroductionApply> applyList = hsTalentIntroductionApplyService.findList(query);
        if (applyList != null && !applyList.isEmpty()) {
            for (HsTalentIntroductionApply apply : applyList) {
                apply.setIsView(1);

                mergeApply = new HsTalentRecordMergeApply();
                BeanUtils.copyProperties(apply, mergeApply);
                mergeApply.setType(1);
                mergeApply.setIsView(1);
                mergeApplyList.add(mergeApply);
            }
        }
        hsTalentRecord.setApplyList(applyList);

        HsTalentRecordStop stopQuery = new HsTalentRecordStop();
        stopQuery.setTalentId(hsTalentRecord.getId());
        stopQuery.sqlMap().getWhere().disableAutoAddStatusWhere();
        stopQuery.setStatus_in(new String[] { "0", "4", "5", "9" });
        List<HsTalentRecordStop> stopList = hsTalentRecordStopService.findList(stopQuery);

        if (stopList != null && !stopList.isEmpty()) {
            for (HsTalentRecordStop stop : stopList) {
                mergeApply = new HsTalentRecordMergeApply();
                BeanUtils.copyProperties(stop, mergeApply);
                mergeApply.setType(2);
                mergeApply.setIsView(1);
                mergeApply.setRemark(stop.getStopReason());
                mergeApplyList.add(mergeApply);
            }
        }

        mergeApplyList.sort(Comparator.comparing(HsTalentRecordMergeApply::getCreateDate).reversed());
        hsTalentRecord.setMergeApplyList(mergeApplyList);
    }

}
