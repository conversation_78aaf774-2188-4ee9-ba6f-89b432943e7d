package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取用户的关联角色及角色数据权限响应实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserRoleAndDataAuthData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String roleId;
    private String roleName;
    private String code;
    private String intro;
    /** 1-本单位  2-本单位及下级 3-指定单位 */
    private String priType;
    /** 角色所属单位ID */
    private String orgId;
    /** 数据授权范围 */
    private String priData;

    private Boolean copyFlag = false;

}
