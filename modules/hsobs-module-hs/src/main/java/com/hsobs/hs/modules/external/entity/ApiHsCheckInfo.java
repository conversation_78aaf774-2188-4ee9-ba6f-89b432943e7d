package com.hsobs.hs.modules.external.entity;

import java.util.List;

public class ApiHsCheckInfo extends ApiHsCheckRecord{

    private String xm;//	是	发起人姓名
    @HsMapTo("waterFee")
    private String sbds;//	是	水表度数（精确至小数点后两位）
    @HsMapTo("eletricFee")
    private String dbds;//	是	电表度数（精确至小数点后两位）
    @HsMapTo("gasFee")
    private String rqds;//	是	燃气度数（精确至小数点后两位）
    private String shlx;//审核类型
    private String shms;
    @HsMapTo("checkBack")
    private String fkms;//反馈描述
    @HsMapTo("hsQwObjectCheckList")
    private List<ApiHsCheckDetai> wpjlList;//	是	物品损毁记录集合

    private List<ApiHsCheckProcess> lcxxList;
    public String getFkms() {
        return fkms;
    }

    public void setFkms(String fkms) {
        this.fkms = fkms;
    }

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getSbds() {
        return sbds;
    }

    public void setSbds(String sbds) {
        this.sbds = sbds;
    }

    public String getDbds() {
        return dbds;
    }

    public void setDbds(String dbds) {
        this.dbds = dbds;
    }

    public String getRqds() {
        return rqds;
    }

    public void setRqds(String rqds) {
        this.rqds = rqds;
    }

    public List<ApiHsCheckDetai> getWpjlList() {
        return wpjlList;
    }

    public void setWpjlList(List<ApiHsCheckDetai> wpjlList) {
        this.wpjlList = wpjlList;
    }

    public String getShms() {
        return shms;
    }

    public void setShms(String shms) {
        this.shms = shms;
    }

   public String getShlx() {
        return shlx;
    }

   public void setShlx(String shlx) {
        this.shlx = shlx;
    }

    public List<ApiHsCheckProcess> getLcxxList() {
        return lcxxList;
    }

    public void setLcxxList(List<ApiHsCheckProcess> lcxxList) {
        this.lcxxList = lcxxList;
    }
}
