package com.hsobs.hs.modules.pricelimitapplyer.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.hsobs.hs.modules.pricelimitapplyer.dao.HsPriceLimitApplyerDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 限价房-购房申请人Service
 * <AUTHOR>
 * @version 2024-12-10
 */
@Service
public class HsPriceLimitApplyerService extends CrudService<HsPriceLimitApplyerDao, HsPriceLimitApplyer> {
	
	/**
	 * 获取单条数据
	 * @param hsPriceLimitApplyer
	 * @return
	 */
	@Override
	public HsPriceLimitApplyer get(HsPriceLimitApplyer hsPriceLimitApplyer) {
		return super.get(hsPriceLimitApplyer);
	}
	
	/**
	 * 查询分页数据
	 * @param hsPriceLimitApplyer 查询条件
	 * @param hsPriceLimitApplyer page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPriceLimitApplyer> findPage(HsPriceLimitApplyer hsPriceLimitApplyer) {
		hsPriceLimitApplyer.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findPage(hsPriceLimitApplyer);
	}
	
	/**
	 * 查询列表数据
	 * @param hsPriceLimitApplyer
	 * @return
	 */
	@Override
	public List<HsPriceLimitApplyer> findList(HsPriceLimitApplyer hsPriceLimitApplyer) {
		hsPriceLimitApplyer.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findList(hsPriceLimitApplyer);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsPriceLimitApplyer
	 */
	@Override
	@Transactional
	public void save(HsPriceLimitApplyer hsPriceLimitApplyer) {
		super.save(hsPriceLimitApplyer);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(hsPriceLimitApplyer, hsPriceLimitApplyer.getId(), "hsPriceLimitApplyer_image");
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsPriceLimitApplyer, hsPriceLimitApplyer.getId(), "hsPriceLimitApplyer_file");
	}
	
	/**
	 * 更新状态
	 * @param hsPriceLimitApplyer
	 */
	@Override
	@Transactional
	public void updateStatus(HsPriceLimitApplyer hsPriceLimitApplyer) {
		super.updateStatus(hsPriceLimitApplyer);
	}
	
	/**
	 * 删除数据
	 * @param hsPriceLimitApplyer
	 */
	@Override
	@Transactional
	public void delete(HsPriceLimitApplyer hsPriceLimitApplyer) {
		super.delete(hsPriceLimitApplyer);
	}
	
}