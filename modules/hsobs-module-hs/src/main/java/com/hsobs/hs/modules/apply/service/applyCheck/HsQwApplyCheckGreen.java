package com.hsobs.hs.modules.apply.service.applyCheck;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import org.springframework.stereotype.Service;

/**
 * 绿色通道申请校验
 */
@Service
public class HsQwApplyCheckGreen extends HsQwApplyCheckBase implements HsQwApplyCheck {

    @Override
    public String getApplyType() {
        return "3";//绿色通道
    }

    @Override
    public void execute(HsQwApply hsQwApply) {
        //黑名单校验
        super.blackCheck(hsQwApply);

    }

}
