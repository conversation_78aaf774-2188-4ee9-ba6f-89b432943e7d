package com.hsobs.hs.modules.applypublicdetail.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.applypublicdetail.entity.HsQwApplyPublicDetail;
import com.hsobs.hs.modules.applypublicdetail.service.HsQwApplyPublicDetailService;

/**
 * 租赁资格轮候公示复查详情表Controller
 * <AUTHOR>
 * @version 2024-12-05
 */
@Controller
@RequestMapping(value = "${adminPath}/applypublicdetail/hsQwApplyPublicDetail")
public class HsQwApplyPublicDetailController extends BaseController {

	@Autowired
	private HsQwApplyPublicDetailService hsQwApplyPublicDetailService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyPublicDetail get(String id, boolean isNewRecord) {
		return hsQwApplyPublicDetailService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("applypublicdetail:hsQwApplyPublicDetail:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyPublicDetail hsQwApplyPublicDetail, Model model) {
		model.addAttribute("hsQwApplyPublicDetail", hsQwApplyPublicDetail);
		return "modules/applypublicdetail/hsQwApplyPublicDetailList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("applypublicdetail:hsQwApplyPublicDetail:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyPublicDetail> listData(HsQwApplyPublicDetail hsQwApplyPublicDetail, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyPublicDetail.setPage(new Page<>(request, response));
		Page<HsQwApplyPublicDetail> page = hsQwApplyPublicDetailService.findPage(hsQwApplyPublicDetail);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("applypublicdetail:hsQwApplyPublicDetail:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyPublicDetail hsQwApplyPublicDetail, Model model) {
		model.addAttribute("hsQwApplyPublicDetail", hsQwApplyPublicDetail);
		return "modules/applypublicdetail/hsQwApplyPublicDetailForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("applypublicdetail:hsQwApplyPublicDetail:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyPublicDetail hsQwApplyPublicDetail) {
		hsQwApplyPublicDetailService.save(hsQwApplyPublicDetail);
		return renderResult(Global.TRUE, text("保存租赁资格轮候公示复查详情表成功！"));
	}
	
}