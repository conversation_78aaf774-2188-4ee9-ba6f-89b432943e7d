package com.hsobs.hs.modules.datasync.service;

import com.hsobs.hs.modules.datasync.bean.ElevatorImp;
import com.hsobs.hs.modules.datasync.bean.TalentApplyImp;
import com.hsobs.hs.modules.datasync.bean.TalentDoneImp;
import com.hsobs.hs.modules.elevator.entity.HsElevatorApply;
import com.hsobs.hs.modules.elevator.service.HsElevatorApplyService;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.hsobs.hs.modules.talent.service.HsTalentIntroductionApplyService;
import com.hsobs.hs.modules.utils.UeditorExporter;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.OfficeService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class DataLoadService {

    @Autowired
    private HsElevatorApplyService hsElevatorApplyService;
    @Autowired
    FileUploadService fileUploadService;
    @Autowired
    private BpmTaskService bpmTaskService;
    @Autowired
    private OfficeService officeService;
    @Autowired
    private HsTalentIntroductionApplyService hsTalentIntroductionApplyService;

    public void loadElevator() {
        File file = new File("H:\\private\\001-202410-住\\文档\\线上数据\\加装电梯收件处理情况表（2018.11.7起）11.22 - 副本 - 副本.xlsx");
        try (ExcelImport ei = new ExcelImport(file, 4, 0)) {
            List<ElevatorImp> list = ei.getDataList(ElevatorImp.class);
            int index = 0;
            for (ElevatorImp elevatorImp : list) {
                String unitId = elevatorImp.getUnitId();
                if (StringUtils.isEmpty(unitId) || "#N/A".equals(unitId) || !unitId.startsWith("000")) {
                    continue;
                }

                HsElevatorApply apply = new HsElevatorApply();
                apply.setHouseId(elevatorImp.getHouseId());
                apply.setUnitId(elevatorImp.getUnitId());
                apply.setContactName("陈铭灏");
                apply.setContactTel("13509314691");
                apply.setHouseholds(elevatorImp.getHouseholds());
                apply.setTotalFund(elevatorImp.getTotalFund());
                apply.setExistFund(0.0D);
                apply.setExistFundOwner(elevatorImp.getExistFundOwner());
                apply.setExistFundHouse(elevatorImp.getExistFundHouse());
                apply.setExistFundUnit(elevatorImp.getExistFundUnit());
                apply.setExistFundOther(elevatorImp.getExistFundOther());
                apply.setApplyFund(elevatorImp.getApplyFund());
                apply.setDisbursementFund(elevatorImp.getDisbursementFund());
                apply.setApplyReason("");
                apply.setApplyStatus(-1);
                apply.setRemarks("");
                apply.setStatus("4");
                hsElevatorApplyService.save(apply);
                saveElevatorFile(apply, "证明材料");

                if ("已办结".equals(elevatorImp.getFlowStatus())) {
                    apply.setIsNewRecord(false);
                    if (elevatorImp.getDisbursementFund() == null) {
                        elevatorImp.setDisbursementFund(apply.getApplyFund());
                    }
                    submitElevatorImp(apply, elevatorImp.getDisbursementFund());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void submitElevatorImp(HsElevatorApply apply, double fund) {

        BpmTask params = new BpmTask();
        params.setStatus("1");
        params.getProcIns().setFormKey("elevator_subsidy_apply");
        params.getProcIns().setBizKey(apply.getId());
        Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);
        BpmTask task = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
        if (task == null) {
            return;
        }
        BpmTask bpmTask = new BpmTask();
        bpmTask.setId(task.getId());

        // 自动签收
        if (StringUtils.isEmpty(task.getAssignee())) {
            bpmTaskService.claimTask(bpmTask);
        }

        BpmParams bpmParams = new BpmParams();
        bpmParams.setTaskId(task.getId());
        bpmParams.setProcInsId(task.getProcIns().getId());
        bpmParams.setActivityId(task.getActivityId());
        bpmParams.setComment("同意");
        apply.setBpm(bpmParams);
        apply.setDisbursementFund(fund);
        hsElevatorApplyService.save(apply);

        submitElevatorImp(apply, fund);
    }

    private void saveElevatorFile(HsElevatorApply hsElevatorApply, String recordData) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            UeditorExporter.exportToWord(recordData, outputStream);
            String md5 = DigestUtils.md5Hex(outputStream.toByteArray());

            String fileName = String.format("辅助材料.docx");
            String bizType = "hsElevatorApply_file";

            FileUploadParams fileParams = new FileUploadParams();
            MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                    "application/octet-stream",
                    outputStream.toByteArray()
            );
            fileParams.setBizKey(hsElevatorApply.getId());
            fileParams.setBizType(bizType);
            fileParams.setFile(multipartFile);
            fileParams.setFileMd5(md5);
            fileParams.setFileName(fileName);
            FileUpload fileUpload = new FileUpload();
            fileUpload.setBizKey(hsElevatorApply.getId());
            fileUpload.setBizType(bizType);
            Map<String, Object> uploadedFileMap = fileUploadService.uploadFile(fileUpload, fileParams);
            hsElevatorApply.setDataMap(uploadedFileMap);
            FileUploadUtils.saveFileUpload(hsElevatorApply, hsElevatorApply.getId(), bizType);
        } catch (Exception e) {
        }
    }


    public void loadFund() {

    }

    /**
     * 申请中
     */
    public void loadTalent() {
        File file = new File("H:\\private\\001-202410-住\\文档\\线上数据\\人才补贴\\人才补贴\\新申请-未发放过\\引进人才住房补助测算(2024)5.xls");
        try (ExcelImport ei = new ExcelImport(file, 2, 0)) {
            List<TalentApplyImp> list = ei.getDataList(TalentApplyImp.class);
            int index = 0;
            for (TalentApplyImp elevatorImp : list) {
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 已发放
     */
    public void loadTalented() {
        File file = new File("H:\\private\\001-202410-住\\文档\\线上数据\\人才补贴已发放.xlsx");
        try (ExcelImport ei = new ExcelImport(file, 2, 0)) {
            List<TalentDoneImp> list = ei.getDataList(TalentDoneImp.class);
            int index = 0;
            Office office = new Office();
            office.setParentCodes("0,0000000,");
            List<Office> officeList = officeService.findList(office);
            Map<String, Office> officeMap = new HashMap<>();
            for (Office office1 : officeList) {
                officeMap.put(office1.getOfficeName(), office1);
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            int total = list.size();
            for (TalentDoneImp talentDoneImp : list) {
                index++;
                if (!officeMap.containsKey(talentDoneImp.getIntroductionUnit())) {
                    continue;
                }
                Office office1 = officeMap.get(talentDoneImp.getIntroductionUnit());

                HsTalentIntroductionApply apply = new HsTalentIntroductionApply();
                apply.setUnitId(office1.getOfficeCode());
                apply.setApplyName(talentDoneImp.getName());
                apply.setApplyTel("18120810000");
                apply.setApplyNo(talentDoneImp.getIdNumber());
                apply.setEduBack("1");
                apply.setTitleId("1");
                apply.setTalentType("");
                apply.setTalentLevel("");

                // 2020.11
                String time = talentDoneImp.getIntroductionTime();
                // 把输入字符串里的点替换成破折号
                String formattedDateStr = time.replace('.', '-');
                // 加上当月第一天
                formattedDateStr += "-01";
                // 定义日期格式
                // 解析日期字符串
                apply.setArrivalTime(sdf.parse(formattedDateStr));

                apply.setRemark("");
                apply.setApplyStatus(-1);

                if (Integer.parseInt(talentDoneImp.getDistributionStandard().trim()) == 18) {
                    apply.setConfId("1878640913352364032");
                    apply.setSubsidyFund(180000.0D);
                } else {
                    apply.setConfId("1878641156357754880");
                    apply.setSubsidyFund(140000.0D);
                }
                apply.setSubsidyPeriod(60);

                apply.setApprovalComment("");
                apply.setStatus("4");
                apply.setIsNewRecord(true);

                BpmParams bpmParams = new BpmParams();
                bpmParams.setActivityId("undefined");
                apply.setBpm(new BpmParams());

                double fund = Double.parseDouble(talentDoneImp.getCurrentYearDistributionAmount().trim()) + Double.parseDouble(talentDoneImp.getAlreadyDistributedAmount().trim());
                double issuedPeriod = fund * 10000 / (apply.getSubsidyFund() / 60);
                apply.setIssuedPeriod((int) Math.ceil(issuedPeriod));
                hsTalentIntroductionApplyService.save(apply);

                if (index < (total - 20)) {
                    apply.setIsNewRecord(false);
                    submitTalentDoneImp(apply);
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void submitTalentDoneImp(HsTalentIntroductionApply apply) {
        BpmTask params = new BpmTask();
        params.setStatus("1");
        params.getProcIns().setFormKey("talent_subsidy_apply");
        params.getProcIns().setBizKey(apply.getId());
        Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);
        BpmTask task = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
        if (task == null) {
            return;
        }
        BpmTask bpmTask = new BpmTask();
        bpmTask.setId(task.getId());

        // 自动签收
        if (StringUtils.isEmpty(task.getAssignee())) {
            bpmTaskService.claimTask(bpmTask);
        }

        BpmParams bpmParams = new BpmParams();
        bpmParams.setTaskId(task.getId());
        bpmParams.setProcInsId(task.getProcIns().getId());
        bpmParams.setActivityId(task.getActivityId());
        bpmParams.setComment("同意");
        apply.setBpm(bpmParams);
        hsTalentIntroductionApplyService.save(apply);

        submitTalentDoneImp(apply);
    }
}
