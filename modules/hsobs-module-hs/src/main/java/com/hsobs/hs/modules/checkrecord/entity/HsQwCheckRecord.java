package com.hsobs.hs.modules.checkrecord.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.checkdetail.entity.HsQwManagementCheckDetail;
import com.hsobs.hs.modules.checkobject.entity.HsQwManagementCheckObject;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.external.entity.HsMapFile;
import com.hsobs.hs.modules.external.entity.HsMapTo;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 租赁资格核查台账表Entity
 * <AUTHOR>
 * @version 2025-02-15
 */
@Table(name="hs_qw_check_record", alias="a", label="租赁资格核查台账表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="apply_id", attrName="applyId", label="申请单id"),
		@Column(name="compact_id", attrName="compactId", label="合同id"),
		@Column(name="house_id", attrName="houseId", label="房源id"),
		@Column(name="office_code", attrName="officeCode", label="机构编号"),
		@Column(includeEntity=DataEntity.class),
		@Column(name="violation", attrName="violation", label="核查状态", comment="核查状态（0合规 1违规）"),
		@Column(name="renewal", attrName="renewal", label="是否合同续签", comment="是否合同续签（0续签 1不续签）"),
		@Column(name="complaint", attrName="complaint", label="是否申诉", comment="是否申诉（0申诉 1不申诉）"),
		@Column(name="reasonable", attrName="reasonable", label="申诉是否合理", comment="申诉是否合理（0合理 1不合理）"),
		@Column(name="complaint_reason", attrName="complaintReason", label="申诉说明", comment="申诉说明"),
		@Column(name="complaint_date", attrName="complaintDate", label="申诉时间", comment="申诉时间"),
		@Column(name="check_date", attrName="checkDate", label="核查时间", comment="核查时间"),
		@Column(name="check_objects", attrName="checkObjects", label="核查事项", comment="核查事项"),
		@Column(name="VIOLATION_TYPE", attrName="violationType", label="核查类型", comment="核查类型")
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "o",
				on = "o.id = a.house_id and o.status=0", attrName="house",
				columns = {@Column(includeEntity = HsQwPublicRentalHouse.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApply.class, alias = "ha",
				on = "a.apply_id = ha.id and ha.status=0 and ha.apply_matter=0", attrName="hsQwApply",
				columns = {@Column(includeEntity = HsQwApply.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwCompact.class, alias = "c",
				on = "a.compact_id = c.id", attrName="compact",
				columns = {@Column(includeEntity = HsQwCompact.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "f",
				on = "a.office_code = f.office_code", attrName="office",
				columns = {@Column(includeEntity = Office.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "e",
				on = "o.estate_id = e.id", attrName="house.estate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "hae",
				on = "hae.apply_id = ha.id and hae.apply_role = 0 and hae.status=0", attrName = "hsQwApply.mainApplyer",
				columns = {@Column(includeEntity = HsQwApplyer.class)}),
	}, orderBy="a.update_date DESC"
)
public class HsQwCheckRecord extends BpmEntity<HsQwCheckRecord> {
	
	private static final long serialVersionUID = 1L;
	private String applyId;		// 申请单id
	private String compactId;		// 合同id
	private String houseId;		// 房源id
	private String violation;		// 核查状态（0合规 1违规）
	private String renewal;		// 是否合同续签（0续签 1不续签）
	private String complaint;
	private String reasonable;
	private String officeCode;
	private HsQwCompact compact;
	private HsQwApply hsQwApply;
	private HsQwPublicRentalHouse house;
	private Office office;

	private Date checkDate;
	private String checkObjects;
	private List<HsQwApply> hsQwApplyList;
	private String violationType;

	private List<HsQwManagementCheckObject> hsObjectList;

	@HsMapTo("ssxx")
	private String complaintReason;
	@HsMapTo("sssj")
	private Date complaintDate;

	public HsQwCompact getCompact() {
		return compact;
	}

	public void setCompact(HsQwCompact compact) {
		this.compact = compact;
	}

	public HsQwApply getHsQwApply() {
		return hsQwApply;
	}

	public void setHsQwApply(HsQwApply hsQwApply) {
		this.hsQwApply = hsQwApply;
	}

	public HsQwPublicRentalHouse getHouse() {
		return house;
	}

	public void setHouse(HsQwPublicRentalHouse house) {
		this.house = house;
	}

	@ExcelFields({
			@ExcelField(title="申请单id", attrName="applyId", align=Align.CENTER, sort=20),
			@ExcelField(title="合同id", attrName="compactId", align=Align.CENTER, sort=30),
			@ExcelField(title="房源id", attrName="houseId", align=Align.CENTER, sort=40),
			@ExcelField(title="状态", attrName="status", dictType="sys_status", align=Align.CENTER, sort=50),
			@ExcelField(title="核查状态", attrName="violation", dictType="hs_qw_check_violation", align=Align.CENTER, sort=60),
			@ExcelField(title="是否合同续签", attrName="renewal", dictType="hs_qw_check_renewal", align=Align.CENTER, sort=70),
			@ExcelField(title="申诉是否合理", attrName="reasonable", dictType="hs_qw_check_reasonable", align=Align.CENTER, sort=140),
			@ExcelField(title="是否申诉", attrName="complaint", dictType="hs_qw_check_complaint", align=Align.CENTER, sort=150),
			@ExcelField(title="申诉理由", attrName="complaintReason", align=Align.CENTER, sort=160),
	})
	public HsQwCheckRecord() {
		this(null);
	}
	
	public HsQwCheckRecord(String id){
		super(id);
		this.hsQwApplyList = new ArrayList<HsQwApply>();
		this.hsObjectList = new ArrayList<HsQwManagementCheckObject>();
	}
	
	@NotBlank(message="申请单id不能为空")
	@Size(min=0, max=64, message="申请单id长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@NotBlank(message="合同id不能为空")
	@Size(min=0, max=64, message="合同id长度不能超过 64 个字符")
	public String getCompactId() {
		return compactId;
	}

	public void setCompactId(String compactId) {
		this.compactId = compactId;
	}
	
	@NotBlank(message="房源id不能为空")
	@Size(min=0, max=64, message="房源id长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}
	
	@NotBlank(message="核查状态不能为空")
	@Size(min=0, max=1, message="核查状态长度不能超过 1 个字符")
	public String getViolation() {
		return violation;
	}

	public void setViolation(String violation) {
		this.violation = violation;
	}
	
	@NotBlank(message="是否合同续签不能为空")
	@Size(min=0, max=1, message="是否合同续签长度不能超过 1 个字符")
	public String getRenewal() {
		return renewal;
	}

	public void setRenewal(String renewal) {
		this.renewal = renewal;
	}

    public Office getOffice() {
        return office;
    }

    public void setOffice(Office office) {
        this.office = office;
    }

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

	public String getReasonable() {
		return reasonable;
	}

	public void setReasonable(String reasonable) {
		this.reasonable = reasonable;
	}

	public String getComplaint() {
		return complaint;
	}

	public void setComplaint(String complaint) {
		this.complaint = complaint;
	}

	public Date getComplaintDate() {
		return complaintDate;
	}

	public void setComplaintDate(Date complaintDate) {
		this.complaintDate = complaintDate;
	}

	public String getComplaintReason() {
		return complaintReason;
	}

	public void setComplaintReason(String complaintReason) {
		this.complaintReason = complaintReason;
	}

	public List<HsQwApply> getHsQwApplyList() {
		return hsQwApplyList;
	}

	public void setHsQwApplyList(List<HsQwApply> hsQwApplyList) {
		this.hsQwApplyList = hsQwApplyList;
	}

	public List<HsQwManagementCheckObject> getHsObjectList() {
		return hsObjectList;
	}

	public void setHsObjectList(List<HsQwManagementCheckObject> hsObjectList) {
		this.hsObjectList = hsObjectList;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public String getCheckObjects() {
		return checkObjects;
	}

	public void setCheckObjects(String checkObjects) {
		this.checkObjects = checkObjects;
	}

	public String getViolationType() {
		return violationType;
	}

	public void setViolationType(String violationType) {
		this.violationType = violationType;
	}
}