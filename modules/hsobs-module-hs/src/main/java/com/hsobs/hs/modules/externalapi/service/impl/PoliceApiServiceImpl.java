package com.hsobs.hs.modules.externalapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.externalapi.config.ExternalApiConfig;
import com.hsobs.hs.modules.externalapi.service.AbstractExternalApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 公安部门API服务实现类
 * 
 * <AUTHOR>
 * @version 2024-05-20
 */
@Service
public class PoliceApiServiceImpl extends AbstractExternalApiService {

    @Autowired
    private ExternalApiConfig externalApiConfig;
    
    @Override
    protected ExternalApiConfig.DepartmentConfig getDepartmentConfig() {
        return externalApiConfig.getPolice();
    }
    
    @Override
    public String getDepartmentName() {
        return "police";
    }
    
    /**
     * 身份证信息核验
     * 
     * @param idCard 身份证号
     * @param name 姓名
     * @return 核验结果
     */
    public JSONObject verifyIdCard(String idCard, String name) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        params.put("name", name);
        
        return callApi("verifyIdCard", params);
    }
    
    /**
     * 户籍信息查询
     * 
     * @param idCard 身份证号
     * @return 户籍信息
     */
    public JSONObject queryHouseholdInfo(String idCard) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        
        return callApi("queryHouseholdInfo", params);
    }
    
    /**
     * 婚姻状况查询
     * 
     * @param idCard 身份证号
     * @param name 姓名
     * @return 婚姻状况
     */
    public JSONObject queryMarriageStatus(String idCard, String name) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        params.put("name", name);
        
        return callApi("queryMarriageStatus", params);
    }
    
    @Override
    protected JSONObject createMockResponse(String apiName, Map<String, Object> params) {
        JSONObject response = super.createMockResponse(apiName, params);
        JSONObject data = response.getJSONObject("data");
        
        // 根据不同的API创建不同的模拟数据
        switch (apiName) {
            case "verifyIdCard":
                data.put("verified", true);
                data.put("idCard", params.get("idCard"));
                data.put("name", params.get("name"));
                break;
                
            case "queryHouseholdInfo":
                data.put("idCard", params.get("idCard"));
                data.put("householdAddress", "模拟户籍地址");
                data.put("householdType", "城镇户口");
                break;
                
            case "queryMarriageStatus":
                data.put("idCard", params.get("idCard"));
                data.put("name", params.get("name"));
                data.put("marriageStatus", "已婚");
                data.put("spouseName", "模拟配偶姓名");
                data.put("spouseIdCard", "模拟配偶身份证号");
                break;
        }
        
        return response;
    }
}
