package com.hsobs.hs.modules.applyer.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;

/**
 * 租户Controller
 * <AUTHOR>
 * @version 2024-11-21
 */
@Controller
@RequestMapping(value = "${adminPath}/applyer/hsQwApplyer")
public class HsQwApplyerController extends BaseController {

	@Autowired
	private HsQwApplyerService hsQwApplyerService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyer get(String id, boolean isNewRecord) {
		return hsQwApplyerService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("applyer:hsQwApplyer:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyer hsQwApplyer, Model model) {
		model.addAttribute("hsQwApplyer", hsQwApplyer);
		return "modules/applyer/hsQwApplyerList";
	}

	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("applyer:hsQwApplyer:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyer> listData(HsQwApplyer hsQwApplyer, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyer.setPage(new Page<>(request, response));
		hsQwApplyer.setHsQwApply(new HsQwApply());
		hsQwApplyer.getHsQwApply().setStatus(HsQwApply.STATUS_NORMAL);
		hsQwApplyer.setApplyRole("0");
		Page<HsQwApplyer> page = hsQwApplyerService.findPage(hsQwApplyer);
		return page;
	}



	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("applyer:hsQwApplyer:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyer hsQwApplyer, Model model) {
		model.addAttribute("hsQwApplyer", hsQwApplyer);
		return "modules/applyer/hsQwApplyerForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("applyer:hsQwApplyer:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyer hsQwApplyer) {
		hsQwApplyerService.save(hsQwApplyer);
		return renderResult(Global.TRUE, text("保存租户成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("applyer:hsQwApplyer:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsQwApplyer hsQwApplyer) {
		hsQwApplyer.setStatus(HsQwApplyer.STATUS_DISABLE);
		hsQwApplyerService.updateStatus(hsQwApplyer);
		return renderResult(Global.TRUE, text("停用租户成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("applyer:hsQwApplyer:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsQwApplyer hsQwApplyer) {
		hsQwApplyer.setStatus(HsQwApplyer.STATUS_NORMAL);
		hsQwApplyerService.updateStatus(hsQwApplyer);
		return renderResult(Global.TRUE, text("启用租户成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("applyer:hsQwApplyer:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyer hsQwApplyer) {
		hsQwApplyerService.delete(hsQwApplyer);
		return renderResult(Global.TRUE, text("删除租户成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("applyer:hsQwApplyer:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwApplyer hsQwApplyer, HttpServletResponse response) {
		hsQwApplyer.setHsQwApply(new HsQwApply());
		hsQwApplyer.getHsQwApply().setStatus(HsQwApply.STATUS_NORMAL);
		hsQwApplyer.setApplyRole("0");
		List<HsQwApplyer> list = hsQwApplyerService.findList(hsQwApplyer);
		String fileName = "租户信息列表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try (ExcelExport ee = new ExcelExport("租户信息列表", HsQwApplyer.class)) {
			ee.setDataList(list).write(response, fileName);
		}

	}
}