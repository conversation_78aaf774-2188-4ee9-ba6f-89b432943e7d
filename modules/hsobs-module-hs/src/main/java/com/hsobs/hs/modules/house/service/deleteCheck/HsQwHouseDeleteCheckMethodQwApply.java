package com.hsobs.hs.modules.house.service.deleteCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 公租申请单核验，查询所有房源的申请单
 */
@Service
public class HsQwHouseDeleteCheckMethodQwApply implements HsQwHouseDeleteCheckMethod {
    @Autowired
    private HsQwApplyDao hsQwApplyDao;

    @Override
    public void execute(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        HsQwApply query = new HsQwApply();
        query.setHouseId(hsQwPublicRentalHouse.getId());
        query.setStatus_in(new String[]{HsQwApply.STATUS_NORMAL, HsQwApply.STATUS_AUDIT});
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        long count = hsQwApplyDao.findCount(query);
        if (count>0){
            throw new ServiceException("该房源存在关联的公租申请单，请先取消申请单后再重新操作！");
        }
    }
}
