package com.hsobs.hs.modules.hsalarmset.entity;

import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 告警设置Entity
 * <AUTHOR>
 * @version 2025-01-04
 */
@Table(name="hs_alarm_set", alias="a", label="告警设置信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(includeEntity=DataEntity.class),
		@Column(name="rent_arrears_time", attrName="rentArrearsTime", label="租金拖欠月份阈值", isUpdateForce=true),
		@Column(name="rent_arrears_price", attrName="rentArrearsPrice", label="租金拖欠金额阈值", isUpdateForce=true),
		@Column(name="rent_arrears_cycle", attrName="rentArrearsCycle", label="租金拖欠提醒周期", isUpdateForce=true),
		@Column(name="anomaly_cycle", attrName="anomalyCycle", label="资格核查异常提醒周期", isUpdateForce=true),
		@Column(name="limit_undrawn", attrName="limitUndrawn", label="维修补助总额度", isUpdateForce=true),
		@Column(name="remaining_undrawn", attrName="remainingUndrawn", label="剩余额度", isUpdateForce=true),
		@Column(name="reminder_cycle", attrName="reminderCycle", label="提醒周期", comment="提醒周期(天)", isUpdateForce=true),
		@Column(name="reminder_date", attrName="reminderDate", label="上次提醒时间", isUpdateForce=true),
	}, orderBy="a.update_date DESC"
)
public class HsAlarmSet extends DataEntity<HsAlarmSet> {
	
	private static final long serialVersionUID = 1L;
	private Long rentArrearsTime;	// 租金拖欠月份阈值
	private Long rentArrearsPrice;	// 租金拖欠金额阈值
	private Long rentArrearsCycle;	// 租金拖欠提醒周期
	private Long anomalyCycle;		// 资格核查异常提醒周期
	private Long limitUndrawn;		// 维修补助总额度
	private Long remainingUndrawn;		// 剩余额度
	private Long reminderCycle;		// 提醒周期(天)
	private Date reminderDate;		// 上次提醒时间

	public HsAlarmSet() {
		this(null);
	}
	
	public HsAlarmSet(String id){
		super(id);
	}

	public Long getRentArrearsTime() {
		return rentArrearsTime;
	}

	public void setRentArrearsTime(Long rentArrearsTime) {
		this.rentArrearsTime = rentArrearsTime;
	}

	public Long getRentArrearsPrice() {
		return rentArrearsPrice;
	}

	public void setRentArrearsPrice(Long rentArrearsPrice) {
		this.rentArrearsPrice = rentArrearsPrice;
	}

	public Long getRentArrearsCycle() {
		return rentArrearsCycle;
	}

	public void setRentArrearsCycle(Long rentArrearsCycle) {
		this.rentArrearsCycle = rentArrearsCycle;
	}

	public Long getAnomalyCycle() {
		return anomalyCycle;
	}

	public void setAnomalyCycle(Long anomalyCycle) {
		this.anomalyCycle = anomalyCycle;
	}

	public Long getLimitUndrawn() {
		return limitUndrawn;
	}

	public void setLimitUndrawn(Long limitUndrawn) {
		this.limitUndrawn = limitUndrawn;
	}

	public Long getRemainingUndrawn() {
		return remainingUndrawn;
	}

	public void setRemainingUndrawn(Long remainingUndrawn) {
		this.remainingUndrawn = remainingUndrawn;
	}
	
	public Long getReminderCycle() {
		return reminderCycle;
	}

	public void setReminderCycle(Long reminderCycle) {
		this.reminderCycle = reminderCycle;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getReminderDate() {
		return reminderDate;
	}

	public void setReminderDate(Date reminderDate) {
		this.reminderDate = reminderDate;
	}

}