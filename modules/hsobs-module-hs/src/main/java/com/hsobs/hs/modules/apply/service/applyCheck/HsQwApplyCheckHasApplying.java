package com.hsobs.hs.modules.apply.service.applyCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 判断当前用户是否有已经有正在申请中的申请单
 */
@Component
public class HsQwApplyCheckHasApplying extends HsQwApplyCheckBase implements HsQwApplyCheck {

    @Override
    public String getApplyType() {
        return "9";
    }

    @Override
    public boolean execute(HsQwApply hsQwApply) {
        //是否存在已有的公租房申请单判定
        HsQwApply entity = new HsQwApply();
        entity.sqlMap().getWhere().disableAutoAddStatusWhere();
        entity.setMainApplyer(new HsQwApplyer());
        entity.getMainApplyer().setUserId(this.getRealApplyUser(hsQwApply));//主申请人是申请者
        entity.setStatus_in(new String[]{"4"});
        entity.setApplyMatter(HsQwApply.APPLY_MATTER_RENTAL);
        long count1 = hsQwApplyDao.findCount(entity);
        if (count1 <= 0) {
            return false;
        }
        return true;
    }

}
