package com.hsobs.hs.modules.publicapplyer.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.hsobs.hs.modules.publicapplyer.dao.HsPublicApplyerDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 公有住房-购房申请人Service
 * <AUTHOR>
 * @version 2024-12-10
 */
@Service
public class HsPublicApplyerService extends CrudService<HsPublicApplyerDao, HsPublicApplyer> {
	
	/**
	 * 获取单条数据
	 * @param hsPublicApplyer
	 * @return
	 */
	@Override
	public HsPublicApplyer get(HsPublicApplyer hsPublicApplyer) {
		return super.get(hsPublicApplyer);
	}
	
	/**
	 * 查询分页数据
	 * @param hsPublicApplyer 查询条件
	 * @param hsPublicApplyer page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPublicApplyer> findPage(HsPublicApplyer hsPublicApplyer) {
		return super.findPage(hsPublicApplyer);
	}
	
	/**
	 * 查询列表数据
	 * @param hsPublicApplyer
	 * @return
	 */
	@Override
	public List<HsPublicApplyer> findList(HsPublicApplyer hsPublicApplyer) {
		return super.findList(hsPublicApplyer);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsPublicApplyer
	 */
	@Override
	@Transactional
	public void save(HsPublicApplyer hsPublicApplyer) {
		super.save(hsPublicApplyer);
		// 保存上传图片
		FileUploadUtils.saveFileUpload(hsPublicApplyer, hsPublicApplyer.getId(), "hsPublicApplyer_image");
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsPublicApplyer, hsPublicApplyer.getId(), "hsPublicApplyer_file");
	}
	
	/**
	 * 更新状态
	 * @param hsPublicApplyer
	 */
	@Override
	@Transactional
	public void updateStatus(HsPublicApplyer hsPublicApplyer) {
		super.updateStatus(hsPublicApplyer);
	}
	
	/**
	 * 删除数据
	 * @param hsPublicApplyer
	 */
	@Override
	@Transactional
	public void delete(HsPublicApplyer hsPublicApplyer) {
		super.delete(hsPublicApplyer);
	}
	
}