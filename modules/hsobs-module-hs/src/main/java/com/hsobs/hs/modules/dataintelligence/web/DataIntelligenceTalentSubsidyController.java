package com.hsobs.hs.modules.dataintelligence.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.dataintelligence.service.DataIntelligenceTalentSubsidyService;

import java.util.ArrayList;
import java.util.List;

/**
 * 住房保障数据统计Controller  人才住房补助统计
 *  * <AUTHOR>
 *  * @version 2025-1-2
 */
@Controller
@RequestMapping(value = "${adminPath}/dataintelligencetalent/")
public class DataIntelligenceTalentSubsidyController extends BaseController {

	@Autowired
	private DataIntelligenceTalentSubsidyService dataIntelligenceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataIntelligenceTalentSubsidy get(Integer type, boolean isNewRecord) {
		return dataIntelligenceService.get(type, isNewRecord);
	}

	/**
	 * 人才住房补助统计
	 */
	@RequiresPermissions("dataintelligencetalent::view")
	@RequestMapping(value = {"dataIntelligenceTalentSubsidy", ""})
	public String dataIntelligenceTalentSubsidy(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceTalentSubsidy);
		return "modules/dataintelligence/dataIntelligenceTalentSubsidy";
	}

	/**
	 * 人才住房补助统计
	 */
	@RequiresPermissions("dataintelligencetalent::view")
	@RequestMapping(value = {"dataIntelligenceTalentSubsidyCompare", ""})
	public String dataIntelligenceTalentSubsidyCompare(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceTalentSubsidy);
		return "modules/dataintelligence/dataIntelligenceTalentSubsidyCompare";
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencetalent::view")
	@RequestMapping(value = "talentSubsidyStatData")
	@ResponseBody
	public Page<DataIntelligenceTalentSubsidy> talentSubsidyStatData(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceTalentSubsidy.setPage(new Page<>(request, response));
		Page<DataIntelligenceTalentSubsidy> page = dataIntelligenceService.findTalentSubsidyStatDataPage(dataIntelligenceTalentSubsidy, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencetalent::view")
	@RequestMapping(value = "talentSubsidyStatAreaData")
	@ResponseBody
	public Page<DataIntelligenceTalentSubsidy> talentSubsidyStatAreaData(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceTalentSubsidy.setPage(new Page<>(request, response));
		Page<DataIntelligenceTalentSubsidy> page = dataIntelligenceService.findTalentSubsidyStatAreaDataPage(dataIntelligenceTalentSubsidy, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencetalent::view")
	@RequestMapping(value = "talentSubsidyStatDataEmpty")
	@ResponseBody
	public Page<DataIntelligenceTalentSubsidy> talentSubsidyStatDataEmpty(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceTalentSubsidy.setPage(new Page<>(request, response));
		List<DataIntelligenceTalentSubsidy> statList = new ArrayList<>();
		Page<DataIntelligenceTalentSubsidy> page = (Page<DataIntelligenceTalentSubsidy>) dataIntelligenceTalentSubsidy.getPage();
		page.setList(statList);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencetalent::view")
	@RequestMapping(value = "talentSubsidyCompare")
	@ResponseBody
	public String talentSubsidyCompare(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findTalentSubsidyCompare(dataIntelligenceTalentSubsidy);
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencetalent::view")
	@RequestMapping(value = "talentSubsidyPayCompare")
	@ResponseBody
	public String talentSubsidyPayCompare(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findTalentSubsidyPayCompare(dataIntelligenceTalentSubsidy);
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligencetalent::view")
	@RequestMapping(value = "exportData")
	public void exportData(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, HttpServletResponse response) {
		String fileName = "人才住房补助统计表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("人才住房补助统计表", DataIntelligenceTalentSubsidy.class);
			List<DataIntelligenceTalentSubsidy> list = dataIntelligenceService.countTalentSubsidy(dataIntelligenceTalentSubsidy);
			ee.setDataList(list);

			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}
}