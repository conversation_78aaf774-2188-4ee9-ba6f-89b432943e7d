package com.hsobs.hs.modules.talent.service;

import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.elevator.entity.HsElevatorApply;
import com.hsobs.hs.modules.talent.dao.HsTalentIntroductionApplyDao;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.hsobs.hs.modules.talent.entity.HsTalentRecord;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import groovy.lang.Lazy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 人才补助申请表Service
 * <AUTHOR>
 * @version 2025-01-03
 */
@Service
public class HsTalentIntroductionApplyService extends CrudService<HsTalentIntroductionApplyDao, HsTalentIntroductionApply> {

	@Autowired
	private BpmTaskService bpmTaskService;
	@Autowired
	private HsTalentRecordService hsTalentRecordService;
	@Autowired
	private CommonBpmService commonBpmService;

	/**
	 * 获取单条数据
	 * @param hsTalentIntroductionApply
	 * @return
	 */
	@Override
	public HsTalentIntroductionApply get(HsTalentIntroductionApply hsTalentIntroductionApply) {
		return super.get(hsTalentIntroductionApply);
	}

	@Override
	public void addDataScopeFilter(HsTalentIntroductionApply entity) {
		SqlMap sqlMap = entity.sqlMap(); // v5.3.0+ 及之后版本
		// 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
		sqlMap.getDataScope().addFilter("extWhere", "Office",
				"a.unit_id", "a.create_by", DataScope.CTRL_PERMI_HAVE, "hsTalentRecord");
	}

	/**
	 * 查询分页数据
	 * @param hsTalentIntroductionApply 查询条件
	 * @param hsTalentIntroductionApply page 分页对象
	 * @return
	 */
	@Override
	public Page<HsTalentIntroductionApply> findPage(HsTalentIntroductionApply hsTalentIntroductionApply) {
		hsTalentIntroductionApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		hsTalentIntroductionApply.setStatus_in(new String[] { "0", "4", "5", "9" });
		return super.findPage(hsTalentIntroductionApply);
	}

	private String[] getAuditStatusArray() {
        return new String[]{
                HsTalentIntroductionApply.APPLY_STATUS_DEFAULT,
                HsTalentIntroductionApply.APPLY_STATUS_DRAFT,
                HsTalentIntroductionApply.APPLY_STATUS_AUDIT_SUPERUNIT_FIRST,
                HsTalentIntroductionApply.APPLY_STATUS_AUDIT_ORGHAND_FIRST,

                HsTalentIntroductionApply.APPLY_STATUS_AUDIT_ORGOFFICE_FIRST ,
                HsTalentIntroductionApply.APPLY_STATUS_AUDIT_ORGBUREAU_FIRST ,
                HsTalentIntroductionApply.APPLY_STATUS_AUDIT_JGCWCJBSH_FIRST ,
                HsTalentIntroductionApply.APPLY_STATUS_AUDIT_JGCWCLDFH_FIRST ,
                HsTalentIntroductionApply.APPLY_STATUS_AUDIT_ORGBUREAU_SECOND,
                HsTalentIntroductionApply.APPLY_STATUS_AUDIT_JGCWCJBBK_FIRST
        };
	}

	public List<HsTalentIntroductionApply> findAuditByTask(HsTalentIntroductionApply hsTalentIntroductionApply) {
		String[] status = getAuditStatusArray();
		return commonBpmService.findTaskListNoPage(status, "talent_subsidy_apply",hsTalentIntroductionApply, "1");
	}

	public Page<HsTalentIntroductionApply> findAuditPageByTask(HsTalentIntroductionApply hsTalentIntroductionApply) {
		//审批待办中的状态过滤
		String[] status = getAuditStatusArray();

//		HsBpmTask params = new HsBpmTask();
//		params.setStatus("1");
//		Page<BpmTask> myHsTaskPage = this.getHsTask(params, status, hsTalentIntroductionApply.getFlowStatus());
//		//获取所有待办任务的申请单id
//		if (!this.getIdsByTask(myHsTaskPage, hsTalentIntroductionApply)) {
//			Page<HsTalentIntroductionApply> hsTalentIntroductionApplyPage = this.findPage(hsTalentIntroductionApply);
//			return this.getTaskResult(hsTalentIntroductionApplyPage, myHsTaskPage);
//		} else {
//			return hsTalentIntroductionApply.getPage();
//		}
		return findApplyPageByTask(hsTalentIntroductionApply, status, "1", "talent_subsidy_apply");
	}

	public Page<HsTalentIntroductionApply> findApplyPageByTask(HsTalentIntroductionApply hsTalentIntroductionApply, String[] status, String bpmStatus, String formKey) {
		return commonBpmService.findTaskList(status, formKey,hsTalentIntroductionApply, bpmStatus);
	}


	/**
	 * 查询列表数据
	 * @param hsTalentIntroductionApply
	 * @return
	 */
	@Override
	public List<HsTalentIntroductionApply> findList(HsTalentIntroductionApply hsTalentIntroductionApply) {
		return super.findList(hsTalentIntroductionApply);
	}

	private String getStatusFromTask(String id, Page<BpmTask> myHsTaskPage) {
		for (BpmTask bpmTask : myHsTaskPage.getList()) {
			if (bpmTask.getProcIns().getBizKey().equals(id)) {
				return bpmTask.getName();
			}
		}
		return null;
	}

	private Page<HsTalentIntroductionApply> getTaskResult(Page<HsTalentIntroductionApply> hsTalentIntroductionApplyPage, Page<BpmTask> myHsTaskPage) {
		hsTalentIntroductionApplyPage.getList().forEach(k -> k.setFlowStatus(this.getStatusFromTask(k.getId(), myHsTaskPage)));
		return hsTalentIntroductionApplyPage;
	}

	private boolean getIdsByTask(Page<BpmTask> pageTask, HsTalentIntroductionApply hsTalentIntroductionApply) {
		List<String> hsIds = new ArrayList<>();
		pageTask.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		hsTalentIntroductionApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
		return hsIds.isEmpty();
	}

	private Page<BpmTask> getHsTask(HsBpmTask params, String[] resNames, String applyStatus) {
		params.setUserCode(params.currentUser().getUserCode());
		params.getProcIns().setFormKey("talent_subsidy_apply");
		if (resNames != null && resNames.length > 0) {
			params.setNames(Arrays.stream(resNames).collect(Collectors.toList()));
		}
		if (StringUtils.isNotBlank(applyStatus)) {
			params.setName(applyStatus);
		}
		return this.bpmTaskService.findTaskPage(params);
	}
	
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsTalentIntroductionApply
	 */
	@Override
	@Transactional
	public void save(HsTalentIntroductionApply hsTalentIntroductionApply) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsTalentIntroductionApply.getStatus())){
			hsTalentIntroductionApply.setStatus(HsTalentIntroductionApply.STATUS_AUDIT);
		}
		
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsTalentIntroductionApply.STATUS_NORMAL.equals(hsTalentIntroductionApply.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		String opType = hsTalentIntroductionApply.getOpType();
		if ("update".equals(opType)) {
			if (!hsTalentIntroductionApply.currentUser().getUserCode().equals(hsTalentIntroductionApply.getCreateBy())) {
				throw new ServiceException(text("非法操作，前端数据被劫持！"));
			}

			hsTalentIntroductionApply.setApplyStatus(100);
			super.save(hsTalentIntroductionApply);
			BpmProcIns procIns = BpmUtils.getBpmRuntimeService().getProcessInstanceByBusinessKey("talent_subsidy_apply", hsTalentIntroductionApply.getId());
			BpmUtils.getBpmRuntimeService().stopProcessInstance(procIns);

			hsTalentIntroductionApply.setId(null);
			hsTalentIntroductionApply.setIsNewRecord(true);
			hsTalentIntroductionApply.setStatus(HsTalentIntroductionApply.STATUS_AUDIT);
			hsTalentIntroductionApply.setBpm(new BpmParams());
		}


		// 记录流程节点值
		int activityIdInt = -1;
		if (hsTalentIntroductionApply.getBpm() != null && StringUtils.isNotEmpty(hsTalentIntroductionApply.getBpm().getActivityId())) {
			String activityId = hsTalentIntroductionApply.getBpm().getActivityId();
			if ("undefined".equals(activityId)) {
				if (HsTalentIntroductionApply.STATUS_AUDIT.equals(hsTalentIntroductionApply.getStatus())) {
					activityIdInt = 1;
				}
			} else {
				// talentSubsidyApply0001
				activityId = activityId.replace("talentSubsidyApply", "");
				while (activityId.startsWith("0")) {
					activityId = StringUtils.removeStart(activityId, "0");
				}
				if (StringUtils.isNumeric(activityId)) {
					activityIdInt = Integer.parseInt(activityId);
				}
			}
		}
		if ("update".equals(opType)) {
			activityIdInt = 1;
		}
		hsTalentIntroductionApply.setApplyStatus(activityIdInt);

		// 获取人才补助档案
		HsTalentRecord record = new HsTalentRecord();
		record.setUserNo(hsTalentIntroductionApply.getApplyNo());
		List<HsTalentRecord> recordList = hsTalentRecordService.findList(record);
		if (recordList != null && !recordList.isEmpty()) {
			record = recordList.get(0);
		} else {
			record = null;
		}

		if (record == null) {
			record = new HsTalentRecord();
			record.setCheckStatus(0);
		}

		copyAttr(hsTalentIntroductionApply, record);
		record.setCheckTime(new Date());

		if ("update".equals(opType)) {
			record.setCheckStatus(1);
		} else {
			if (activityIdInt == -1 || activityIdInt == 0 || activityIdInt == 1) {
				String checkStatusStr = String.valueOf(record.getCheckStatus());
				// 更新人才信息  审批状态  0-无 1-审核中(发放) 2-发放中 3-停发 4-审核中(停发)  talent_status
				if ("1".equals(checkStatusStr)) {
					throw new ServiceException(text("该申请人已经发起申请流程，请先撤回审批中申请！"));
				} else if ("2".equals(checkStatusStr)) {
					throw new ServiceException(text("该申请人已发放人才补助！"));
				} else if ("4".equals(checkStatusStr)) {
					throw new ServiceException(text("该申请人已经发起停发申请流程，请先撤回审批中申请！"));
				} else if (!("0".equals(checkStatusStr) || "3".equals(checkStatusStr))) {
					throw new ServiceException(text("该申请人无法发起人才补助申请！"));
				}
			} else if (activityIdInt == 9) {
				record.setCheckStatus(2);
			} else {
				record.setCheckStatus(1);
			}
		}
		hsTalentRecordService.save(record);

		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsTalentIntroductionApply.STATUS_DRAFT.equals(hsTalentIntroductionApply.getStatus())
				|| HsTalentIntroductionApply.STATUS_AUDIT.equals(hsTalentIntroductionApply.getStatus())){
			//TODO 流程审批中则不做人才基本信息变更
			super.save(hsTalentIntroductionApply);
		}

		// 如果为审核状态，则进行审批流操作
		if (HsTalentIntroductionApply.STATUS_AUDIT.equals(hsTalentIntroductionApply.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			//variables.put("leaveDays", hsTalentIntroductionApply.getLeaveDays());
			
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsTalentIntroductionApply.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsTalentIntroductionApply.getBpm().getTaskId())){
				BpmUtils.start(hsTalentIntroductionApply, "talent_subsidy_apply", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsTalentIntroductionApply, variables, null);
			}
		}
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsTalentIntroductionApply, hsTalentIntroductionApply.getId(), "hsTalentIntroductionApply_file");
		FileUploadUtils.saveFileUpload(hsTalentIntroductionApply, hsTalentIntroductionApply.getId(), "hsTalentIntroductionApply_cwFile");
	}

	private void copyAttr(HsTalentIntroductionApply apply, HsTalentRecord record) {
		record.setUnitId(apply.getUnitId());
		record.setUserName(apply.getApplyName());
		record.setUserTel(apply.getApplyTel());
		record.setUserNo(apply.getApplyNo());
		record.setEduBack(apply.getEduBack());
		record.setTitleId(apply.getTitleId());
		record.setArrivalTime(apply.getArrivalTime());
		record.setCheckTime(new Date());
		record.setSubsidyFund(apply.getSubsidyFund());
		record.setSubsidyPeriod(apply.getSubsidyPeriod());
		record.setIssuedPeriod(0);
		record.setValidTag("1");
		record.setStatus("0");

		record.setTalentLevel(apply.getTalentLevel());
		record.setTalentType(apply.getTalentType());

		if (record.getIssuedPeriod() == null) {
			record.setIssuedPeriod(apply.getIssuedPeriod());
		}

	}

	/**
	 * 更新状态
	 * @param hsTalentIntroductionApply
	 */
	@Override
	@Transactional
	public void updateStatus(HsTalentIntroductionApply hsTalentIntroductionApply) {
		super.updateStatus(hsTalentIntroductionApply);
	}
	
	/**
	 * 删除数据
	 * @param hsTalentIntroductionApply
	 */
	@Override
	@Transactional
	public void delete(HsTalentIntroductionApply hsTalentIntroductionApply) {
		hsTalentIntroductionApply.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsTalentIntroductionApply);
	}

	public void flushTaskStatus(HsTalentIntroductionApply hsTalentIntroductionApply) {
		if (hsTalentIntroductionApply.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		hsTalentIntroductionApply = this.get(hsTalentIntroductionApply.getId());
		if (hsTalentIntroductionApply == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		HsBpmTask params = new HsBpmTask();
		params.setStatus("1");
		// 维修资金申请流程key
		params.getProcIns().setFormKey("talent_subsidy_apply");
		params.getProcIns().setBizKey(hsTalentIntroductionApply.getId());

		Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);

		BpmTask bpmTask = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
		if (bpmTask != null) {
			String activityId = bpmTask.getActivityId().replace("talentSubsidyApply", "");
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				int activityIdInt = Integer.parseInt(activityId);

				if (activityIdInt == 1) {
					activityIdInt = 0;

					// 恢复人员状态为无状态
					HsTalentRecord record = new HsTalentRecord();
					record.setUserNo(hsTalentIntroductionApply.getApplyNo());
					List<HsTalentRecord> recordList = hsTalentRecordService.findList(record);
					if (recordList != null && !recordList.isEmpty()) {
						record = recordList.get(0);
						record.setCheckStatus(0);
						record.setCheckTime(new Date());
						hsTalentRecordService.update(record);
					}
				} else {
					activityIdInt = getPrevApplyStatus(activityIdInt);
				}

				hsTalentIntroductionApply.setApplyStatus(activityIdInt);
				super.save(hsTalentIntroductionApply);
			}
		}
	}

	public Integer getPrevApplyStatus(Integer activityIdInt) {
		return PREV_MAP.get(activityIdInt);
	}

	private static Map<Integer, Integer> PREV_MAP = new HashMap<>();
	static {
		//  人才  1 2 3 4 5 6 7 8 9
		PREV_MAP.put(1, 0);
		PREV_MAP.put(2, 1);
		PREV_MAP.put(3, 2);
		PREV_MAP.put(4, 3);
		PREV_MAP.put(5, 4);
		PREV_MAP.put(6, 5);
		PREV_MAP.put(7, 6);
		PREV_MAP.put(8, 7);
		PREV_MAP.put(9, 8);
	}

}