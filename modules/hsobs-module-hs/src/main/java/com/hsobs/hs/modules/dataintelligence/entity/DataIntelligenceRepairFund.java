package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.text.SimpleDateFormat;

/**
 * 住房保障数据统计Entity   维修资金情况统计
 * <AUTHOR>
 * @version 2025-1-2
 */
public class DataIntelligenceRepairFund extends DataEntity<DataIntelligenceRepairFund> {

	@ExcelFields({
			@ExcelField(title = "小区名称", attrName = "estateName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "维修申请量", attrName = "disbursementCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "维修资金(元)", attrName = "disbursementFund", align = ExcelField.Align.CENTER, sort = 40)
	})

	private static final long serialVersionUID = 1L;
	private String parentCodeTree;

	private String city;
	private String area;

	private String estateId;
	private String estateName;	//区域
	private String officeCode;		// 单位编码
	private  String officeName;
	private Date startDate;		//开始日期
	private Date endDate;	//结束日期

	private String repairType;
	private Long disbursementCount;	   // 维修量
	private float disbursementFund;    //维修资金

	public DataIntelligenceRepairFund() {
		this(null);
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			endDate = new Date();
			startDate = dateFormat.parse(String.format("%04d-01-01", endDate.getYear()+1900));  // 解析指定日期字符串
		} catch (Exception e) {
		}
		officeCode = "";
		parentCodeTree = "";
		if(!UserUtils.getUser().isAdmin()){
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		else{
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
	}
	
	public DataIntelligenceRepairFund(String id){
		super(id);
	}

	public String getParentCodeTree() {
		return parentCodeTree;
	}

	public void setParentCodeTree(String parentCodeTree) {
		this.parentCodeTree = parentCodeTree;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getEstateId() {
		return estateId;
	}

	public void setEstateId(String estateId) {
		this.estateId = estateId;
	}

	public String getEstateName() {
		return estateName;
	}

	public void setEstateName(String estateName) {
		this.estateName = estateName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}



	public String getRepairType() {
		return repairType;
	}

	public void setRepairType(String repairType) {
		this.repairType = repairType;
	}

	public Long getDisbursementCount() {
		return disbursementCount;
	}

	public void setDisbursementCount(Long disbursementCount) {
		this.disbursementCount = disbursementCount;
	}

	public float getDisbursementFund() {
		return disbursementFund;
	}

	public void setDisbursementFund(float disbursementFund) {
		this.disbursementFund = disbursementFund;
	}
}