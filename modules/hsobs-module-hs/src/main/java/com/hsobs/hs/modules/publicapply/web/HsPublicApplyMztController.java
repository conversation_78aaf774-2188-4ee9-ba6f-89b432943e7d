package com.hsobs.hs.modules.publicapply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.hsobs.hs.modules.publicapply.service.HsPublicApplyService;

/**
 * 公有住房-购房申请Controller
 * <AUTHOR>
 * @version 2024-12-10
 */
@Controller
@RequestMapping(value = "${adminPath}/publicapply/hsPublicApplyMzt")
public class HsPublicApplyMztController extends BaseController {

	@Autowired
	private HsPublicApplyService hsPublicApplyService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPublicApply get(String id, boolean isNewRecord) {
		return hsPublicApplyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		return "modules/publicapply/hsPublicApplyListMzt";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsPublicApply> listData(HsPublicApply hsPublicApply, HttpServletRequest request, HttpServletResponse response) {
		hsPublicApply.setPage(new Page<>(request, response));
		Page<HsPublicApply> page = hsPublicApplyService.findMztPage(hsPublicApply);
		return page;
	}
	
	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("publicapply:hsPublicApply:view")
	@RequestMapping(value = "form")
	public String form(HsPublicApply hsPublicApply, Model model) {
		model.addAttribute("hsPublicApply", hsPublicApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/publicapply/hsPublicApplyFormMzt";
	}
	
}