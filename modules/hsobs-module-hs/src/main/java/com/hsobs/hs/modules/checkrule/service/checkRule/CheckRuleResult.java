package com.hsobs.hs.modules.checkrule.service.checkRule;

import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;

public class CheckRuleResult {
    private boolean result;
    private String message;
    private HsQwCheckRule hsQwCheckRule;

    public static CheckRuleResult success(String message) {
        CheckRuleResult result = new CheckRuleResult();
        result.setResult(true);
        result.setMessage(message);
        return result;
    }

    public static CheckRuleResult error(String message) {
        CheckRuleResult result = new CheckRuleResult();
        result.setResult(false);
        result.setMessage(message);
        return result;
    }

    public boolean isResult() {
        return result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public HsQwCheckRule getHsQwCheckRule() {
        return hsQwCheckRule;
    }

    public void setHsQwCheckRule(HsQwCheckRule hsQwCheckRule) {
        this.hsQwCheckRule = hsQwCheckRule;
    }
}
