package com.hsobs.hs.modules.apply.service.applyCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 判断当前用户是否有已经有正在租赁的申请单
 */
@Component
public class HsQwApplyCheckHasRented extends HsQwApplyCheckBase implements HsQwApplyCheck {

    @Override
    public String getApplyType() {
        return "8";
    }

    @Override
    public boolean execute(HsQwApply hsQwApply) {
        return super.normalCheck(new String[]{"0"}, hsQwApply);
    }

}
