package com.hsobs.hs.modules.dataintelligence.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.dataintelligence.service.DataIntelligenceLiftSubsidyService;

import java.util.ArrayList;
import java.util.List;

/**
 * 住房保障数据统计Controller   加装电梯补助统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Controller
@RequestMapping(value = "${adminPath}/dataintelligencelift/")
public class DataIntelligenceLiftSubsidyController extends BaseController {

	@Autowired
	private DataIntelligenceLiftSubsidyService dataIntelligenceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataIntelligenceLiftSubsidy get(Integer type, boolean isNewRecord) {
		return dataIntelligenceService.get(type, isNewRecord);
	}

	/**
	 * 加装电梯补助统计
	 */
	@RequiresPermissions("dataintelligencelift::view")
	@RequestMapping(value = {"dataIntelligenceLiftSubsidy", ""})
	public String dataIntelligenceLiftSubsidy(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceLiftSubsidy);
		return "modules/dataintelligence/dataIntelligenceLiftSubsidy";
	}

	/**
	 * 加装电梯补助统计
	 */
	@RequiresPermissions("dataintelligencelift::view")
	@RequestMapping(value = {"dataIntelligenceLiftSubsidyCompare", ""})
	public String dataIntelligenceLiftSubsidyCompare(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceLiftSubsidy);
		return "modules/dataintelligence/dataIntelligenceLiftSubsidyCompare";
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencelift::view")
	@RequestMapping(value = "countLiftSubsidyStat")
	@ResponseBody
	public Page<DataIntelligenceLiftSubsidy> countLiftSubsidyStat(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLiftSubsidy.setPage(new Page<>(request, response));
		Page<DataIntelligenceLiftSubsidy> page = dataIntelligenceService.findLiftSubsidyStatPage(dataIntelligenceLiftSubsidy, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencelift::view")
	@RequestMapping(value = "countLiftSubsidyStatTotal")
	@ResponseBody
	public Page<DataIntelligenceLiftSubsidy> countLiftSubsidyStatTotal(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLiftSubsidy.setPage(new Page<>(request, response));
		Page<DataIntelligenceLiftSubsidy> page = dataIntelligenceService.findLiftSubsidyStatTotalPage(dataIntelligenceLiftSubsidy, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencelift::view")
	@RequestMapping(value = "countLiftSubsidyStatEmpty")
	@ResponseBody
	public Page<DataIntelligenceLiftSubsidy> countLiftSubsidyStatEmpty(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceLiftSubsidy.setPage(new Page<>(request, response));
		List<DataIntelligenceLiftSubsidy> statList = new ArrayList<>();
		Page<DataIntelligenceLiftSubsidy> page = (Page<DataIntelligenceLiftSubsidy>) dataIntelligenceLiftSubsidy.getPage();
		page.setList(statList);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencelift::view")
	@RequestMapping(value = "countLiftSubsidyCompare")
	@ResponseBody
	public String countLiftSubsidyCompare(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findLiftSubsidyCompare(dataIntelligenceLiftSubsidy);
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencelift::view")
	@RequestMapping(value = "countLiftSubsidyPayCompare")
	@ResponseBody
	public String countLiftSubsidyPayCompare(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findLiftSubsidyPayCompare(dataIntelligenceLiftSubsidy);
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligencelift::view")
	@RequestMapping(value = "exportData")
	public void exportData(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, HttpServletResponse response) {
		String fileName = "加装电梯补助统计表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("加装电梯补助统计表", DataIntelligenceLiftSubsidy.class);
			List<DataIntelligenceLiftSubsidy> list = dataIntelligenceService.countLiftSubsidyStat(dataIntelligenceLiftSubsidy);
			ee.setDataList(list);

			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}
}