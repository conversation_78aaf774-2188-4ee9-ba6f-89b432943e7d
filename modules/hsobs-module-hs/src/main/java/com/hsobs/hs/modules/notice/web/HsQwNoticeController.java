package com.hsobs.hs.modules.notice.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.notice.entity.HsQwNotice;
import com.hsobs.hs.modules.notice.service.HsQwNoticeService;

/**
 * 租赁公告Controller
 * <AUTHOR>
 * @version 2025-01-23
 */
@Controller
@RequestMapping(value = "${adminPath}/notice/hsQwNotice")
public class HsQwNoticeController extends BaseController {

	@Autowired
	private HsQwNoticeService hsQwNoticeService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwNotice get(String id, boolean isNewRecord) {
		return hsQwNoticeService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("notice:hsQwNotice:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwNotice hsQwNotice, Model model) {
		model.addAttribute("hsQwNotice", hsQwNotice);
		return "modules/notice/hsQwNoticeList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("notice:hsQwNotice:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwNotice> listData(HsQwNotice hsQwNotice, HttpServletRequest request, HttpServletResponse response) {
		hsQwNotice.setPage(new Page<>(request, response));
		hsQwNotice.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsQwNotice> page = hsQwNoticeService.findPage(hsQwNotice);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("notice:hsQwNotice:view")
	@RequestMapping(value = "form")
	public String form(HsQwNotice hsQwNotice, Model model) {
		model.addAttribute("hsQwNotice", hsQwNotice);
		return "modules/notice/hsQwNoticeForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("notice:hsQwNotice:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwNotice hsQwNotice) {
		hsQwNoticeService.save(hsQwNotice);
		return renderResult(Global.TRUE, text("保存租赁公告成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("notice:hsQwNotice:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwNotice hsQwNotice) {
		hsQwNoticeService.delete(hsQwNotice);
		return renderResult(Global.TRUE, text("删除租赁公告成功！"));
	}

	/**
	 * 撤回数据
	 */
	@RequiresPermissions("notice:hsQwNotice:edit")
	@RequestMapping(value = "reback")
	@ResponseBody
	public String reback(HsQwNotice hsQwNotice) {
		hsQwNoticeService.reback(hsQwNotice);
		return renderResult(Global.TRUE, text("撤回租赁公告成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("notice:hsQwNotice:view")
	@RequestMapping(value = "hsQwNoticeSelect")
	public String hsQwNoticeSelect(HsQwNotice hsQwNotice, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwNotice", hsQwNotice);
		return "modules/notice/hsQwNoticeSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("notice:hsQwNotice:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwNotice hsQwNotice, HttpServletResponse response) {
		hsQwNotice.sqlMap().getWhere().disableAutoAddStatusWhere();
		List<HsQwNotice> list = hsQwNoticeService.findList(hsQwNotice);
		String fileName = "租赁公告" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("租赁公告", HsQwNotice.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
}