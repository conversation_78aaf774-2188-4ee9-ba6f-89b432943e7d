package com.hsobs.hs.modules.applyer.entity;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.external.entity.HsMapTo;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 租赁资格轮候申请人Entity
 * 
 * <AUTHOR>
 * @version 2024-11-21
 */
@Table(name = "hs_qw_applyer", alias = "a", label = "租赁资格轮候申请人信息", columns = {
        @Column(name = "id", attrName = "id", label = "编号", isPK = true),
        @Column(name = "user_id", attrName = "userId", label = "人员编号", queryType = QueryType.EQ, isUpdate = false),
        @Column(name = "name", attrName = "name", label = "申请人姓名", queryType = QueryType.LIKE),
        @Column(name = "work_time", attrName = "workTime", label = "参加工作时间", comment = "参加工作时间（计算工龄）"),
        @Column(name = "organization", attrName = "organization", label = "工作单位", queryType = QueryType.LIKE),
        @Column(name = "id_num", attrName = "idNum", label = "身份证号", queryType = QueryType.LIKE),
        @Column(name = "phone", attrName = "phone", label = "手机号", queryType = QueryType.LIKE),
        @Column(name = "work_position", attrName = "workPosition", label = "职务", comment = "职务（职称）", isQuery = false),
        @Column(name = "work_title", attrName = "workTitle", label = "职位", isQuery = false),
        @Column(name = "annual_income", attrName = "annualIncome", label = "年收入", isQuery = false),
        @Column(includeEntity = DataEntity.class),
        @Column(name = "apply_id", attrName = "applyId", label = "申请表id", queryType = QueryType.EQ),
        @Column(name = "apply_role", attrName = "applyRole", label = "申请人角色", comment = "申请人角色（0主申请人 1配偶 2父母 3子女 4其他）"),
        @Column(name = "marry_status", attrName = "marryStatus", label = "婚姻状况", comment = "婚姻状况（0：未婚，1：已婚；2：离异；3：丧偶）"),
        @Column(name = "death_date", attrName = "deathDate", label = "死亡时间", comment = "死亡时间（yyyy-MM-dd）"),
        @Column(name = "user_type", attrName = "userType", label = "人员类型", comment = "人员类型"),
        @Column(name = "work_age", attrName = "workAge", label = "工龄", comment = "工龄"),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApply.class, alias = "o", on = "o.id = a.apply_id", attrName = "hsQwApply", columns = {
                @Column(includeEntity = HsQwApply.class) }),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "h", on = "h.id = o.house_id", attrName = "hsQwApply.hsQwApplyHouse", columns = {
                @Column(includeEntity = HsQwPublicRentalHouse.class) }),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "p", on = "p.id = h.estate_id", attrName = "hsQwApply.hsQwApplyHouse.estate", columns = {
                @Column(includeEntity = HsQwPublicRentalEstate.class) }),
}, extFromKeys = "extFrom", extWhereKeys = "extWhere", orderBy = "a.update_date DESC")
public class HsQwApplyer extends DataEntity<HsQwApplyer> {

    private static final long serialVersionUID = 1L;
    private String userId; // 人员编号
    @HsMapTo("xm")
    private String name; // 申请人姓名
    @HsMapTo("cjgzsj")
    private Date workTime; // 参加工作时间（计算工龄）
    @HsMapTo("gzdw")
    private String organization; // 工作单位
    @HsMapTo("sfzh")
    private String idNum; // 身份证号
    @HsMapTo("sjh")
    private String phone; // 手机号
    @HsMapTo("sqrzj")
    private String workPosition; // 职务（职称）
    private String workTitle; // 职位
    @HsMapTo("ndzsr")
    private String annualIncome; // 年收入
    private String applyId; // 申请表id
    @HsMapTo("hyzk")
    private String marryStatus;
    @HsMapTo("qssj")
    private String deathDate;
    @HsMapTo("rylx")
    private String userType;
    @HsMapTo("gl")
    private String workAge;
    @HsMapTo("sqrlx")
    private String applyRole; // 申请人角色（0主申请人 1配偶 2父母 3子女 4其他）
    private HsQwApply hsQwApply;

    private String houseId;

    @ExcelFields({
            @ExcelField(title = "编号", attrName = "id", align = Align.CENTER, sort = 10),
            @ExcelField(title = "人员编号", attrName = "userId", align = Align.CENTER, sort = 20),
            @ExcelField(title = "申请人姓名", attrName = "name", align = Align.CENTER, sort = 30),
            @ExcelField(title = "参加工作时间", attrName = "workTime", align = Align.CENTER, sort = 40, dataFormat = "yyyy-MM-dd hh:mm"),
            @ExcelField(title = "工作单位", attrName = "organization", align = Align.CENTER, sort = 50),
            @ExcelField(title = "身份证号", attrName = "idNum", align = Align.CENTER, sort = 60),
            @ExcelField(title = "手机号", attrName = "phone", align = Align.CENTER, sort = 70),
            @ExcelField(title = "职务", attrName = "workPosition", align = Align.CENTER, sort = 80),
            @ExcelField(title = "职位", attrName = "workTitle", align = Align.CENTER, sort = 90),
            @ExcelField(title = "年收入", attrName = "annualIncome", align = Align.CENTER, sort = 100),
            @ExcelField(title = "婚姻状况", attrName = "marryStatus", dictType = "hs_applyer_marry", align = Align.CENTER, sort = 190),
            @ExcelField(title = "死亡时间", attrName = "deathDate", align = Align.CENTER, sort = 200),
            @ExcelField(title = "人员类型", attrName = "userType", align = Align.CENTER, sort = 210),
            @ExcelField(title = "工龄", attrName = "workAge", align = Align.CENTER, sort = 220),
    })
    public HsQwApplyer() {
        this(null);
    }

    public HsQwApplyer(String id) {
        super(id);
    }

    // @NotBlank(message="人员编号不能为空")
    @Size(min = 0, max = 64, message = "人员编号长度不能超过 64 个字符")
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @NotBlank(message = "申请人姓名不能为空")
    @Size(min = 0, max = 100, message = "申请人姓名长度不能超过 100 个字符")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getWorkTime() {
        return workTime;
    }

    public void setWorkTime(Date workTime) {
        this.workTime = workTime;
    }

    @Size(min = 0, max = 255, message = "工作单位长度不能超过 255 个字符")
    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    @NotBlank(message = "身份证号不能为空")
    @Size(min = 0, max = 20, message = "身份证号长度不能超过 20 个字符")
    public String getIdNum() {
        return idNum;
    }

    public void setIdNum(String idNum) {
        this.idNum = idNum;
    }

    @Size(min = 0, max = 20, message = "手机号长度不能超过 20 个字符")
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Size(min = 0, max = 255, message = "职务长度不能超过 255 个字符")
    public String getWorkPosition() {
        return workPosition;
    }

    public void setWorkPosition(String workPosition) {
        this.workPosition = workPosition;
    }

    @Size(min = 0, max = 20, message = "职位长度不能超过 20 个字符")
    public String getWorkTitle() {
        return workTitle;
    }

    public void setWorkTitle(String workTitle) {
        this.workTitle = workTitle;
    }

    @Size(min = 0, max = 20, message = "年收入长度不能超过 20 个字符")
    public String getAnnualIncome() {
        return annualIncome;
    }

    public void setAnnualIncome(String annualIncome) {
        this.annualIncome = annualIncome;
    }

    // @NotBlank(message="申请表id不能为空")
    @Size(min = 0, max = 64, message = "申请表id长度不能超过 64 个字符")
    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @Size(min = 0, max = 255, message = "申请人角色长度不能超过 255 个字符")
    public String getApplyRole() {
        return applyRole;
    }

    public void setApplyRole(String applyRole) {
        this.applyRole = applyRole;
    }

    public Date getWorkTime_gte() {
        return sqlMap.getWhere().getValue("work_time", QueryType.GTE);
    }

    public void setWorkTime_gte(Date workTime) {
        sqlMap.getWhere().and("work_time", QueryType.GTE, workTime);
    }

    public Date getWorkTime_lte() {
        return sqlMap.getWhere().getValue("work_time", QueryType.LTE);
    }

    public void setWorkTime_lte(Date workTime) {
        sqlMap.getWhere().and("work_time", QueryType.LTE, workTime);
    }

    public HsQwApply getHsQwApply() {
        return hsQwApply;
    }

    public void setHsQwApply(HsQwApply hsQwApply) {
        this.hsQwApply = hsQwApply;
    }

    public String getMarryStatus() {
        return marryStatus;
    }

    public void setMarryStatus(String marryStatus) {
        this.marryStatus = marryStatus;
    }

    public String getDeathDate() {
        return deathDate;
    }

    public void setDeathDate(String deathDate) {
        this.deathDate = deathDate;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getWorkAge() {
        return workAge;
    }

    public void setWorkAge(String workAge) {
        this.workAge = workAge;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getSexName() {
        // 中国的第二代身份证号码是18位数字。其中，倒数第二位数字（第17位）是性别识别码：
        // 奇数 (1, 3, 5, 7, 9) 代表男性。
        // 偶数 (0, 2, 4, 6, 8) 代表女性。
        if (idNum != null && idNum.length() == 18) {
            char genderDigit = idNum.charAt(16);
            int gender = Character.getNumericValue(genderDigit);
            if (gender % 2 == 0) {
                return "女"; 
            } 
            else {
                return "男"; 
            }
        }
        return null; // 无效的身份证号码或无法确定性
    }
}