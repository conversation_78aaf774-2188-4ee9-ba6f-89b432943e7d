package com.hsobs.hs.modules.checkrule.service.checkRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;
import org.springframework.stereotype.Service;

/**
 * 已享受政策性补助
 * 申请人及共同申请人已承租单位自管公房，或福州市的公共租赁住房、保障性租赁住房，或已享受租金优惠、租金补助政策的。
 */
@Service
public class CheckRuleRentFee implements ICheckRule {
    @Override
    public CheckRuleResult execute(HsQwApply hsQwApply, HsQwCheckRule rule) {
        return CheckRuleResult.success("成功");
    }
}
