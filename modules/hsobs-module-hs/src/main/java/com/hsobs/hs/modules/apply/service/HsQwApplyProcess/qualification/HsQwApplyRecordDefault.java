package com.hsobs.hs.modules.apply.service.HsQwApplyProcess.qualification;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyProcess.HsQwApplyProcess;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.ServiceException;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * 资格年审
 */
@Service
public class HsQwApplyRecordDefault implements HsQwApplyProcess {
    @Override
    public String getStatus() {
        return HsQwApply.APPLY_STATUS_DEFAULT;
    }

    @Override
    public String getFormKey() {
        return "rent_apply_check";
    }

    @Override
    public void setBpmParams(Map<String, Object> bpmParams, HsQwApply hsQwApply, HsQwApply hsQwApplyOld) {

    }

    @Override
    public String getNextStatus(HsQwApply hsQwApply) {
        String status = hsQwApply.getStatus();
        if (StringUtils.isBlank(status)){
            status = "-1";
        }
        return String.valueOf(Integer.parseInt(status) + 1);
    }

    @Override
    public String getPreviousStatus(HsQwApply hsQwApply) {
        String status = hsQwApply.getStatus();
        return String.valueOf(Integer.parseInt(status) - 1);
    }

    @Override
    public void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        //判断是否含有主申请人信息
        final boolean[] hasMain = {false};
        hsQwApply.getHsQwApplyerList().forEach(k -> hasMain[0] = hasMain[0] || k.getApplyRole().equals("0"));
        if (!hasMain[0]) {
            throw new ServiceException("未添加主申请人信息");
        }
        hsQwApplyService.applyCheck(hsQwApply);//核验资格
        this.save(hsQwApply, hsQwApplyService);//保存申请单
        this.saveApplyer(hsQwApply,hsQwApplyService);//保存申请人
        this.saveHouse(hsQwApply, hsQwApplyService);//保存配租房源信息
        this.saveFile(hsQwApply, hsQwApplyService);//保存图片材料文件
    }

    public void save(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
//        HsQwApply bean = hsQwApplyService.get(hsQwApply.getId());
//        hsQwApply.setApplyMatter(bean.getApplyMatter());//重置恢复
        hsQwApplyService.saveProcess(hsQwApply);
    }
}
