package com.hsobs.hs.modules.formmanage.web;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.formmanage.bean.FormItemBean;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormDelivery;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormFill;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplateField;
import com.hsobs.hs.modules.formmanage.service.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.sys.entity.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormDeliveryRecord;

/**
 * 数据表单下发发送记录表Controller
 * <AUTHOR>
 * @version 2025-02-16
 */
@Controller
@RequestMapping(value = "${adminPath}/formmanage/hsDataFormDeliveryRecord")
public class HsDataFormDeliveryRecordController extends BaseController {

	@Autowired
	private HsDataFormDeliveryRecordService hsDataFormDeliveryRecordService;
	@Autowired
	private HsDataFormFillService hsDataFormFillService;
	@Autowired
	private HsDataFormFillRecordService hsDataFormFillRecordService;
	@Autowired
	private HsDataFormTemplateFieldService hsDataFormTemplateFieldService;
	@Autowired
	private HsDataFormDeliveryService hsDataFormDeliveryService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsDataFormDeliveryRecord get(String id, boolean isNewRecord) {
		return hsDataFormDeliveryRecordService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsDataFormDeliveryRecord hsDataFormDeliveryRecord, Model model) {
		model.addAttribute("hsDataFormDeliveryRecord", hsDataFormDeliveryRecord);
		return "modules/formmanage/hsDataFormDeliveryRecordList";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:view")
	@RequestMapping(value = {"fillList", ""})
	public String fillList(HsDataFormDeliveryRecord hsDataFormDeliveryRecord, Model model) {
		model.addAttribute("hsDataFormDeliveryRecord", hsDataFormDeliveryRecord);
		return "modules/formmanage/hsDataFormDeliveryRecordFillList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsDataFormDeliveryRecord> listData(HsDataFormDeliveryRecord hsDataFormDeliveryRecord, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormDeliveryRecord.setPage(new Page<>(request, response));
		User user = hsDataFormDeliveryRecord.currentUser();
		if (user == null) {
			throw new ServiceException("无效请求！");
		}
		hsDataFormDeliveryRecord.setReceiveUserCode(user.getUserCode());

		Page<HsDataFormDeliveryRecord> page = hsDataFormDeliveryRecordService.findPage(hsDataFormDeliveryRecord);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:view")
	@RequestMapping(value = "fillListData")
	@ResponseBody
	public Page<HsDataFormDeliveryRecord> fillListData(HsDataFormDeliveryRecord hsDataFormDeliveryRecord, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormDeliveryRecord.setPage(new Page<>(request, response));
//		hsDataFormDeliveryRecord.setFillStatus("1");
//		if ("upload".equals(hsDataFormDeliveryRecord.getOpType())) {
//			hsDataFormDeliveryRecord.sqlMap().getWhere().and("upload_status", QueryType.NE, HsDataFormDeliveryRecord.UPLOAD_STATUS_UN);
//		}
		Page<HsDataFormDeliveryRecord> page = hsDataFormDeliveryRecordService.findPage(hsDataFormDeliveryRecord);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:view")
	@RequestMapping(value = "form")
	public String form(HsDataFormDeliveryRecord hsDataFormDeliveryRecord, Model model) {
		model.addAttribute("hsDataFormDeliveryRecord", hsDataFormDeliveryRecord);
		HsDataFormFill fill = hsDataFormFillService.getFillByDeliveryRecordId(hsDataFormDeliveryRecord.getId());
		model.addAttribute("hsDataFormFill", fill);
		return "modules/formmanage/hsDataFormFillForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsDataFormDeliveryRecord hsDataFormDeliveryRecord) {
//		hsDataFormDeliveryRecordService.save(hsDataFormDeliveryRecord);
		return renderResult(Global.TRUE, text("保存数据表单下发发送记录表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsDataFormDeliveryRecord hsDataFormDeliveryRecord) {
//		hsDataFormDeliveryRecordService.delete(hsDataFormDeliveryRecord);
		return renderResult(Global.TRUE, text("删除数据表单下发发送记录表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:view")
	@RequestMapping(value = "hsDataFormDeliveryRecordSelect")
	public String hsDataFormDeliveryRecordSelect(HsDataFormDeliveryRecord hsDataFormDeliveryRecord, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsDataFormDeliveryRecord", hsDataFormDeliveryRecord);
		return "modules/formmanage/hsDataFormDeliveryRecordSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDeliveryRecord:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsDataFormDeliveryRecord hsDataFormDeliveryRecord, HttpServletResponse response) {
		if (StringUtils.isEmpty(hsDataFormDeliveryRecord.getDeliveryId())) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		HsDataFormDelivery record = hsDataFormDeliveryService.get(hsDataFormDeliveryRecord.getDeliveryId());
		if (record == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		List<HsDataFormDeliveryRecord> list = hsDataFormDeliveryRecordService.findList(hsDataFormDeliveryRecord);
		String fileName = record.getTemplateName() + "-填写情况-" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("填写情况", HsDataFormDeliveryRecord.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	
}