package com.hsobs.hs.modules.applyrule.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候规则配置Entity
 * <AUTHOR>
 * @version 2024-12-06
 */
@Table(name="hs_qw_apply_rule", alias="a", label="租赁资格轮候规则配置信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="rule_name", attrName="ruleName", label="规则名称", queryType=QueryType.LIKE),
		@Column(name="rule_type", attrName="ruleType", label="规则类型", comment="规则类型（0家庭人均住房建筑面积 1共同申请人数 2家庭人均年收入 3申请人累计工龄 4轮候时间累计）"),
		@Column(name="rule_config", attrName="ruleConfig", label="规则配置", comment="规则配置（0等于 1大于 2小于 3之间 4之间（含等）5之间（大等）6之间（小等）7大于（含等）8小于（含等） 9自定义工龄 10自定义轮候）"),
		@Column(name="rule_code", attrName="ruleCode", label="规则编码"),
		@Column(name="rule_content", attrName="ruleContent", label="规则内容"),
		@Column(name="rule_result", attrName="ruleResult", label="规则结果"),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyRule extends DataEntity<HsQwApplyRule> {
	
	private static final long serialVersionUID = 1L;
	private String ruleName;		// 规则名称
	private String ruleType;		// 规则类型（0家庭人均住房建筑面积 1共同申请人数 2家庭人均年收入 3申请人累计工龄 4轮候时间累计）
	private String ruleConfig;		// 规则配置（0等于 1大于 2小于 3之间 4之间（含等）5之间（大等）6之间（小等）7大于（含等）8小于（含等） 9自定义工龄 10自定义轮候）
	private String ruleCode;		// 规则编码
	private String ruleContent;		// 规则内容
	private String ruleResult;		// 规则结果

	public HsQwApplyRule() {
		this(null);
	}
	
	public HsQwApplyRule(String id){
		super(id);
	}
	
	@NotBlank(message="规则名称不能为空")
	@Size(min=0, max=100, message="规则名称长度不能超过 100 个字符")
	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}
	
	@NotBlank(message="规则类型不能为空")
	@Size(min=0, max=1, message="规则类型长度不能超过 1 个字符")
	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}
	
	@NotBlank(message="规则配置不能为空")
	@Size(min=0, max=2, message="规则配置长度不能超过 2 个字符")
	public String getRuleConfig() {
		return ruleConfig;
	}

	public void setRuleConfig(String ruleConfig) {
		this.ruleConfig = ruleConfig;
	}
	
	@Size(min=0, max=100, message="规则编码长度不能超过 100 个字符")
	public String getRuleCode() {
		return ruleCode;
	}

	public void setRuleCode(String ruleCode) {
		this.ruleCode = ruleCode;
	}
	
	@NotBlank(message="规则内容不能为空")
	@Size(min=0, max=100, message="规则内容长度不能超过 100 个字符")
	public String getRuleContent() {
		return ruleContent;
	}

	public void setRuleContent(String ruleContent) {
		this.ruleContent = ruleContent;
	}
	
	@Size(min=0, max=100, message="规则结果长度不能超过 100 个字符")
	public String getRuleResult() {
		return ruleResult;
	}

	public void setRuleResult(String ruleResult) {
		this.ruleResult = ruleResult;
	}
	
}