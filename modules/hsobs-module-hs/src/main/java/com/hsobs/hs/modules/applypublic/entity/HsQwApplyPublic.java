package com.hsobs.hs.modules.applypublic.entity;

import javax.validation.Valid;
import java.util.Date;

import com.hsobs.hs.modules.applypublicdetail.entity.HsQwApplyPublicDetail;
import com.hsobs.hs.modules.applypublichouse.entity.HsQwApplyPublicHouse;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;
import com.jeesite.common.collect.ListUtils;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.entity.BpmEntity;

/**
 * 租赁资格轮候公示复查表Entity
 * <AUTHOR>
 * @version 2024-12-05
 */
@Table(name="hs_qw_apply_public", alias="a", label="租赁资格轮候公示复查表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="public_date", attrName="publicDate", label="公示时间", isUpdateForce=true),
		@Column(name="public_type", attrName="publicType", label="公示类型", comment="公示类型（0初审公示 1复查评分公示 3复查审核）", isUpdate=false, isQuery=true),
		@Column(name="public_house_ids", attrName="publicHouseIds", label="公示房源清单", comment="公示房源清单", isUpdate=true, isQuery=false),
		@Column(includeEntity=DataEntity.class),
		@Column(name="public_name", attrName="publicName", label="公示名称", isUpdate=false, queryType=QueryType.LIKE),
		@Column(name="public_status", attrName="publicStatus", label="发布状态", comment="发布状态(0未发布 1已发布)"),
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyPublic extends BpmEntity<HsQwApplyPublic> {

	public static String PUBLIC_TYPE_FIRST = "0";
	public static String PUBLIC_TYPE_CHECK = "1";
	public static String PUBLIC_TYPE_AUDIT = "2";

	public static String PUBLIC_STATUS_NORMAL = "0";
	public static String PUBLIC_STATUS_COMPLETE = "1";
	public static String PUBLIC_STATUS_CANCLE = "2";
	public static String PUBLIC_STATUS_AUDIT = "3";

	private static final long serialVersionUID = 1L;
	private Date publicDate;		// 公示时间
	private String publicType;		// 公示类型（0初审公示 1复查评分公示 3复查审核）
	private String publicName;		// 公示名称
	private String pids;
	private String publicNum;
	private boolean hasAudit;      //当前用户是否拥有审核权限
	private String submitType;     //提交类型（0批量提交 1批量拒绝）
	private String publicHouseIds;
	private String publicHouseNames;
	private String publicStatus;   //发布状态(0未发布 1已发布)
	private List<HsQwApplyPublicHouse> hsQwApplyPublicHouseList = ListUtils.newArrayList();		// 子表列表
	private List<HsQwApplyPublicDetail> hsQwApplyPublicDetailList = ListUtils.newArrayList();		// 子表列表

	public HsQwApplyPublic() {
		this(null);
	}
	
	public HsQwApplyPublic(String id){
		super(id);
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getPublicDate() {
		return publicDate;
	}

	public void setPublicDate(Date publicDate) {
		this.publicDate = publicDate;
	}
	
	@NotBlank(message="公示类型不能为空")
	@Size(min=0, max=1, message="公示类型长度不能超过 1 个字符")
	public String getPublicType() {
		return publicType;
	}

	public void setPublicType(String publicType) {
		this.publicType = publicType;
	}
	
	@NotBlank(message="公示名称不能为空")
	@Size(min=0, max=200, message="公示名称长度不能超过 200 个字符")
	public String getPublicName() {
		return publicName;
	}

	public void setPublicName(String publicName) {
		this.publicName = publicName;
	}
	
	@Valid
	public List<HsQwApplyPublicHouse> getHsQwApplyPublicHouseList() {
		return hsQwApplyPublicHouseList;
	}

	public void setHsQwApplyPublicHouseList(List<HsQwApplyPublicHouse> hsQwApplyPublicHouseList) {
		this.hsQwApplyPublicHouseList = hsQwApplyPublicHouseList;
	}
	
	@Valid
	public List<HsQwApplyPublicDetail> getHsQwApplyPublicDetailList() {
		return hsQwApplyPublicDetailList;
	}

	public void setHsQwApplyPublicDetailList(List<HsQwApplyPublicDetail> hsQwApplyPublicDetailList) {
		this.hsQwApplyPublicDetailList = hsQwApplyPublicDetailList;
	}

	@NotBlank(message="待公示的申请单列表不能为空")
	public String getPids() {
		return pids;
	}

	public void setPids(String pids) {
		this.pids = pids;
	}

	public String getPublicNum() {
		return this.hsQwApplyPublicDetailList.size() + "";
	}

	public void setPublicNum(String publicNum) {
		this.publicNum = publicNum;
	}

	public boolean isHasAudit() {
		return hasAudit;
	}

	public void setHasAudit(boolean hasAudit) {
		this.hasAudit = hasAudit;
	}

	public String getSubmitType() {
		return submitType;
	}

	public void setSubmitType(String submitType) {
		this.submitType = submitType;
	}


	public String getPublicHouseIds() {
		return publicHouseIds;
	}

	public void setPublicHouseIds(String publicHouseIds) {
		if (!StringUtils.isBlank(publicHouseIds)){
			publicHouseIds = publicHouseIds.replaceAll("^,+|,+$", "");;
		}
		this.publicHouseIds = publicHouseIds;
	}

	public String getPublicHouseNames() {
		return publicHouseNames;
	}

	public void setPublicHouseNames(String publicHouseNames) {
		this.publicHouseNames = publicHouseNames;
	}

    public String getPublicStatus() {
        return publicStatus;
    }

    public void setPublicStatus(String publicStatus) {
        this.publicStatus = publicStatus;
    }
}