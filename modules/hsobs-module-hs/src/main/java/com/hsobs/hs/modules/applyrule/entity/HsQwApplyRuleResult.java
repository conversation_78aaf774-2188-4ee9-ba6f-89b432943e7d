package com.hsobs.hs.modules.applyrule.entity;

import lombok.Getter;
import lombok.Setter;

public class HsQwApplyRuleResult {
    @Setter
    @Getter
    private String data;

    private Double score;

    @Setter
    @Getter
    private boolean result;

    @Setter
    @Getter
    private String content;

    private HsQwApplyRule rule;

    public static HsQwApplyRuleResult empty(String ob) {
        HsQwApplyRuleResult result = new HsQwApplyRuleResult();
        result.setResult(false);
        result.setContent("统计值：" + ob + "，未命中任何规则，得分：0");
        result.setData("0");
        result.setScore(0.0);
        return result;
    }

    public Double getScore() {
        if (score != null) {
            return score;
        }
        if (score == null && result) {
            return Double.parseDouble(data);
        }
        return 0.0;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public HsQwApplyRule getRule() {
        return rule;
    }

    public void setRule(HsQwApplyRule rule) {
        this.rule = rule;
    }
}
