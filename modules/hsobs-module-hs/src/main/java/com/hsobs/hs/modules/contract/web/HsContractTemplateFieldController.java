package com.hsobs.hs.modules.contract.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.contract.entity.HsContractTemplateField;
import com.hsobs.hs.modules.contract.service.HsContractTemplateFieldService;

/**
 * 合同模板字段Controller
 * <AUTHOR>
 * @version 2025-01-21
 */
@Controller
@RequestMapping(value = "${adminPath}/contract/hsContractTemplateField")
public class HsContractTemplateFieldController extends BaseController {

	@Autowired
	private HsContractTemplateFieldService hsContractTemplateFieldService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsContractTemplateField get(String id, boolean isNewRecord) {
		return hsContractTemplateFieldService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("contract:hsContractTemplateField:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsContractTemplateField hsContractTemplateField, Model model) {
		model.addAttribute("hsContractTemplateField", hsContractTemplateField);
		return "modules/contract/hsContractTemplateFieldList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("contract:hsContractTemplateField:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsContractTemplateField> listData(HsContractTemplateField hsContractTemplateField, HttpServletRequest request, HttpServletResponse response) {
		hsContractTemplateField.setPage(new Page<>(request, response));
		Page<HsContractTemplateField> page = hsContractTemplateFieldService.findPage(hsContractTemplateField);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("contract:hsContractTemplateField:view")
	@RequestMapping(value = "form")
	public String form(HsContractTemplateField hsContractTemplateField, Model model) {
		model.addAttribute("hsContractTemplateField", hsContractTemplateField);
		return "modules/contract/hsContractTemplateFieldForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("contract:hsContractTemplateField:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsContractTemplateField hsContractTemplateField) {
		hsContractTemplateFieldService.save(hsContractTemplateField);
		return renderResult(Global.TRUE, text("保存合同模板字段成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("contract:hsContractTemplateField:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsContractTemplateField hsContractTemplateField) {
		hsContractTemplateFieldService.delete(hsContractTemplateField);
		return renderResult(Global.TRUE, text("删除合同模板字段成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("contract:hsContractTemplateField:view")
	@RequestMapping(value = "hsContractTemplateFieldSelect")
	public String hsContractTemplateFieldSelect(HsContractTemplateField hsContractTemplateField, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsContractTemplateField", hsContractTemplateField);
		return "modules/contract/hsContractTemplateFieldSelect";
	}
	
}