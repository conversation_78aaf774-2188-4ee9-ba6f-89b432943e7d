package com.hsobs.hs.modules.hsalarmset.service;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.hsalarmset.entity.HsAlarmSet;
import com.hsobs.hs.modules.hsalarmset.dao.HsAlarmSetDao;

/**
 * 告警设置Service
 * <AUTHOR>
 * @version 2025-01-04
 */
@Service
public class HsAlarmSetService extends CrudService<HsAlarmSetDao, HsAlarmSet> {

	@Autowired
	private HsAlarmSetDao hsAlarmSetDao;

	/**
	 * 获取单条数据
	 * @param hsAlarmSet
	 * @return
	 */
	@Override
	public HsAlarmSet get(HsAlarmSet hsAlarmSet) {
		//hsAlarmSet.setId("01234589");
		HsAlarmSet set = super.get(hsAlarmSet);
		if(set == null){
			set = new HsAlarmSet();
			set.setId(hsAlarmSet.getId());
			hsAlarmSetDao.insert(set);
			set = super.get(hsAlarmSet);
		}

		/*Map<String, Object> map =  hsAlarmSetDao.countRepaireFund();
		if(map != null){
			Object ob = map.get("APPLY_FUND");
			set.setApplyFund((ob!=null)?ob.toString():"0");
		}*/
		return set;
	}
	
	/**
	 * 查询分页数据
	 * @param hsAlarmSet 查询条件
	 * @param hsAlarmSet page 分页对象
	 * @return
	 */
	@Override
	public Page<HsAlarmSet> findPage(HsAlarmSet hsAlarmSet) {
		return super.findPage(hsAlarmSet);
	}
	
	/**
	 * 查询列表数据
	 * @param hsAlarmSet
	 * @return
	 */
	@Override
	public List<HsAlarmSet> findList(HsAlarmSet hsAlarmSet) {
		return super.findList(hsAlarmSet);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsAlarmSet
	 */
	@Override
	@Transactional
	public void save(HsAlarmSet hsAlarmSet) {
		super.save(hsAlarmSet);
	}
	
	/**
	 * 更新状态
	 * @param hsAlarmSet
	 */
	@Override
	@Transactional
	public void updateStatus(HsAlarmSet hsAlarmSet) {
		super.updateStatus(hsAlarmSet);
	}
	
	/**
	 * 删除数据
	 * @param hsAlarmSet
	 */
	@Override
	@Transactional
	public void delete(HsAlarmSet hsAlarmSet) {
		super.delete(hsAlarmSet);
	}
	
}