package com.hsobs.hs.modules.managementcheck.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.managementcheck.entity.HsQwManagementCheck;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.managementcheck.service.HsQwManagementCheckService;

/**
 * 租赁资格轮候物业核验Controller
 * <AUTHOR>
 * @version 2025-01-22
 */
@Controller
@RequestMapping(value = "${adminPath}/managementcheck/hsQwManagementCheck")
public class HsQwManagementCheckController extends BaseController {

	@Autowired
	private HsQwManagementCheckService hsQwManagementCheckService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwManagementCheck get(String id, boolean isNewRecord) {
		return hsQwManagementCheckService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表-待办列表
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwManagementCheck hsQwManagementCheck, Model model) {
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheck);
		return "modules/managementcheck/hsQwManagementCheckList";
	}

	/**
	 * 查询列表-待办列表
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:view")
	@RequestMapping(value = {"listAudit", ""})
	public String listAudit(HsQwManagementCheck hsQwManagementCheck, Model model) {
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheck);
		return "modules/managementcheck/hsQwManagementCheckAuditList";
	}

	/**
	 * 查询列表-已办列表
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:view")
	@RequestMapping(value = {"listDone", ""})
	public String listDone(HsQwManagementCheck hsQwManagementCheck, Model model) {
		model.addAttribute("hsQwManagementCheck", hsQwManagementCheck);
		return "modules/managementcheck/hsQwManagementCheckDoneList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwManagementCheck> listData(HsQwManagementCheck hsQwManagementCheck, HttpServletRequest request, HttpServletResponse response) {
		hsQwManagementCheck.setPage(new Page<>(request, response));
//		hsQwManagementCheck.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsQwManagementCheck> page = hsQwManagementCheckService.findPage(hsQwManagementCheck);
		return page;
	}


	/**
	 * 查询列表数据-待办
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:view")
	@RequestMapping(value = "listCheckData")
	@ResponseBody
	public Page<HsQwManagementCheck> listCheckData(HsQwManagementCheck hsQwManagementCheck, HttpServletRequest request, HttpServletResponse response) {
		hsQwManagementCheck.setPage(new Page<>(request, response));
		hsQwManagementCheck.sqlMap().getWhere().disableAutoAddStatusWhere();
		hsQwManagementCheck.setFormKey("management_check%");
		hsQwManagementCheck.sqlMap().getWhere().and("check_type", QueryType.IN, new String[]{"0","1"});
		Page<HsQwManagementCheck> page = hsQwManagementCheckService.findApplyPageByTask(hsQwManagementCheck,null, "1");
		return page;
	}


	/**
	 * 查询列表数据-已办
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:view")
	@RequestMapping(value = "listCheckDoneData")
	@ResponseBody
	public Page<HsQwManagementCheck> listCheckDoneData(HsQwManagementCheck hsQwManagementCheck, HttpServletRequest request, HttpServletResponse response) {
		hsQwManagementCheck.setPage(new Page<>(request, response));
		hsQwManagementCheck.sqlMap().getWhere().disableAutoAddStatusWhere();
		hsQwManagementCheck.setFormKey("management_check%");
		hsQwManagementCheck.sqlMap().getWhere().and("check_type", QueryType.IN, new String[]{"0","1"});
		Page<HsQwManagementCheck> page = hsQwManagementCheckService.findApplyPageByTask(hsQwManagementCheck,null, "2");
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:view")
	@RequestMapping(value = "form")
	public String form(HsQwManagementCheck hsQwManagementCheck, Model model) {
		HsQwManagementCheck result = hsQwManagementCheckService.getCheckInfo(hsQwManagementCheck);
		result.setIsRead(hsQwManagementCheck.getIsRead());
		model.addAttribute("hsQwManagementCheck", result);
		return "modules/managementcheck/hsQwManagementCheckForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwManagementCheck hsQwManagementCheck) {
		hsQwManagementCheckService.save(hsQwManagementCheck);
		return renderResult(Global.TRUE, text("保存租赁资格轮候物业核验成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("managementcheck:hsQwManagementCheck:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwManagementCheck hsQwManagementCheck) {
		hsQwManagementCheckService.delete(hsQwManagementCheck);
		return renderResult(Global.TRUE, text("删除租赁资格轮候物业核验成功！"));
	}
	
}