package com.hsobs.hs.modules.datasync.web;

import com.hsobs.hs.modules.datasync.service.DataLoadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "${adminPath}/data/load")
public class DataLoadController {

    @Autowired
    private DataLoadService dataLoadService;

//    @RequestMapping(value = "elevator")
//    @ResponseBody
//    public String elevator() {
//        dataLoadService.loadElevator();
//        return "ok";
//    }
//    @RequestMapping(value = "fund")
//    @ResponseBody
//    public String fund() {
//        dataLoadService.loadFund();
//        return "ok";
//    }
//    @RequestMapping(value = "talent")
//    @ResponseBody
//    public String talent() {
//        dataLoadService.loadTalent();
//        return "ok";
//    }
//    @RequestMapping(value = "talented")
//    @ResponseBody
//    public String talented() {
//        dataLoadService.loadTalented();
//        return "ok";
//    }

}
