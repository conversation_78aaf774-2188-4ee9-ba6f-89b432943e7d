package com.hsobs.hs.modules.rentfee.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.payment.entity.HsQwFeePayment;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.hsobs.hs.modules.rentfee.service.HsQwRentalFeeService;

/**
 * 租赁账单表Controller
 * <AUTHOR>
 * @version 2025-01-20
 */
@Controller
@RequestMapping(value = "${adminPath}/rentfee/hsQwRentalFee")
public class HsQwRentalFeeController extends BaseController {

	@Autowired
	private HsQwRentalFeeService hsQwRentalFeeService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwRentalFee get(String id, boolean isNewRecord) {
		return hsQwRentalFeeService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("rentfee:hsQwRentalFee:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwRentalFee hsQwRentalFee, Model model) {
		model.addAttribute("hsQwRentalFee", hsQwRentalFee);
		return "modules/rentfee/hsQwRentalFeeList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("rentfee:hsQwRentalFee:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwRentalFee> listData(HsQwRentalFee hsQwRentalFee, HttpServletRequest request, HttpServletResponse response) {
		hsQwRentalFee.setPage(new Page<>(request, response));
		Page<HsQwRentalFee> page = hsQwRentalFeeService.findPage(hsQwRentalFee);
		return page;
	}
	
	/**
	 * 查询子表数据
	 */
	@RequiresPermissions("rentfee:hsQwRentalFee:view")
	@RequestMapping(value = "hsQwFeePaymentListData")
	@ResponseBody
	public Page<HsQwFeePayment> subListData(HsQwFeePayment hsQwFeePayment, HttpServletRequest request, HttpServletResponse response) {
		hsQwFeePayment.setPage(new Page<>(request, response));
		Page<HsQwFeePayment> page = hsQwRentalFeeService.findSubPage(hsQwFeePayment);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("rentfee:hsQwRentalFee:view")
	@RequestMapping(value = "form")
	public String form(HsQwRentalFee hsQwRentalFee, Model model) {
		model.addAttribute("hsQwRentalFee", hsQwRentalFee);
		return "modules/rentfee/hsQwRentalFeeForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("rentfee:hsQwRentalFee:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwRentalFee hsQwRentalFee) {
		hsQwRentalFeeService.save(hsQwRentalFee);
		return renderResult(Global.TRUE, text("保存租赁账单表成功！"));
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("rentfee:hsQwRentalFee:edit")
	@PostMapping(value = "saveFee")
	@ResponseBody
	public String saveFee(@Validated HsQwRentalFee hsQwRentalFee) {
		hsQwRentalFeeService.saveFee(hsQwRentalFee);
		return renderResult(Global.TRUE, text("保存租赁账单表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("rentfee:hsQwRentalFee:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwRentalFee hsQwRentalFee) {
		hsQwRentalFeeService.delete(hsQwRentalFee);
		return renderResult(Global.TRUE, text("删除租赁账单表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("rentfee:hsQwRentalFee:view")
	@RequestMapping(value = "hsQwRentalFeeSelect")
	public String hsQwRentalFeeSelect(HsQwRentalFee hsQwRentalFee, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwRentalFee", hsQwRentalFee);
		return "modules/rentfee/hsQwRentalFeeSelect";
	}
	
}