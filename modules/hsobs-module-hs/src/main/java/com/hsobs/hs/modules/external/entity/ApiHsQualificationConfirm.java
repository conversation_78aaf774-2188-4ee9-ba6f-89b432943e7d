package com.hsobs.hs.modules.external.entity;

import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

public class ApiHsQualificationConfirm extends ApiBody{
    
    private String sqrmc;

    private String hcbh;
    
    private String isCompact;

    private String qysj;
    private String qydd;
    private String zysx;
    private String ssxx;
    private String complaint;
    private String sssj;
    private List<ApiFile> fileList;
    
    public String getSqrmc() {
        return sqrmc;
    }
    
    public void setSqrmc(String sqrmc) {
        this.sqrmc = sqrmc;
    }
    
    public String getHcbh() {
        return hcbh;
    }
    
    public void setHcbh(String hcbh) {
        this.hcbh = hcbh;
    }

    public String getIsCompact() {
        return isCompact;
    }

    public void setIsCompact(String isCompact) {
        this.isCompact = isCompact;
    }

    public String getQysj() {
        return qysj;
    }

    public void setQysj(String qysj) {
        this.qysj = qysj;
    }

    public String getQydd() {
        return qydd;
    }

    public void setQydd(String qydd) {
        this.qydd = qydd;
    }

    public String getZysx() {
        return zysx;
    }

    public void setZysx(String zysx) {
        this.zysx = zysx;
    }

    public String getComplaint() {
        return complaint;
    }

    public void setComplaint(String complaint) {
        this.complaint = complaint;
    }

    public String getSsxx() {
        return ssxx;
    }

    public void setSsxx(String ssxx) {
        this.ssxx = ssxx;
    }

    @Override
    public List<ApiFile> getFileList() {
        return fileList;
    }

    @Override
    public void setFileList(List<ApiFile> fileList) {
        this.fileList = fileList;
    }

    public String getSssj() {
        return sssj;
    }

    public void setSssj(String sssj) {
        this.sssj = sssj;
    }
}