package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取用户的授权单位树响应实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgTreeData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String orgId;
    private String orgCode;
    private String orgName;
    private String orgPid;
    private boolean authorized;
    private Integer level;

}
