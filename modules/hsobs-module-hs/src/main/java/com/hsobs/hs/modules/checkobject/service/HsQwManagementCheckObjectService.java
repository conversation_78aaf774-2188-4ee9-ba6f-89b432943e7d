package com.hsobs.hs.modules.checkobject.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.checkobject.entity.HsQwManagementCheckObject;
import com.hsobs.hs.modules.checkobject.dao.HsQwManagementCheckObjectDao;

/**
 * 租赁资格轮候物业核验物品表Service
 * <AUTHOR>
 * @version 2025-02-13
 */
@Service
public class HsQwManagementCheckObjectService extends CrudService<HsQwManagementCheckObjectDao, HsQwManagementCheckObject> {
	
	/**
	 * 获取单条数据
	 * @param hsQwManagementCheckObject
	 * @return
	 */
	@Override
	public HsQwManagementCheckObject get(HsQwManagementCheckObject hsQwManagementCheckObject) {
		return super.get(hsQwManagementCheckObject);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwManagementCheckObject 查询条件
	 * @param hsQwManagementCheckObject page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwManagementCheckObject> findPage(HsQwManagementCheckObject hsQwManagementCheckObject) {
		return super.findPage(hsQwManagementCheckObject);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwManagementCheckObject
	 * @return
	 */
	@Override
	public List<HsQwManagementCheckObject> findList(HsQwManagementCheckObject hsQwManagementCheckObject) {
		return super.findList(hsQwManagementCheckObject);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwManagementCheckObject
	 */
	@Override
	@Transactional
	public void save(HsQwManagementCheckObject hsQwManagementCheckObject) {
		super.save(hsQwManagementCheckObject);
	}
	
	/**
	 * 更新状态
	 * @param hsQwManagementCheckObject
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwManagementCheckObject hsQwManagementCheckObject) {
		super.updateStatus(hsQwManagementCheckObject);
	}
	
	/**
	 * 删除数据
	 * @param hsQwManagementCheckObject
	 */
	@Override
	@Transactional
	public void delete(HsQwManagementCheckObject hsQwManagementCheckObject) {
		super.delete(hsQwManagementCheckObject);
	}
	
}