package com.hsobs.hs.modules.notice.service;

import java.util.Date;
import java.util.List;

import com.jeesite.common.lang.DateUtils;
import com.jeesite.modules.sys.service.ApiSzpsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.notice.entity.HsQwNotice;
import com.hsobs.hs.modules.notice.dao.HsQwNoticeDao;

/**
 * 租赁资格轮候公告Service
 * <AUTHOR>
 * @version 2025-01-23
 */
@Service
public class HsQwNoticeService extends CrudService<HsQwNoticeDao, HsQwNotice> {
	@Autowired
	private ApiSzpsService apiSzpsService;

	/**
	 * 获取单条数据
	 * @param hsQwNotice
	 * @return
	 */
	@Override
	public HsQwNotice get(HsQwNotice hsQwNotice) {
		return super.get(hsQwNotice);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwNotice 查询条件
	 * @param hsQwNotice page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwNotice> findPage(HsQwNotice hsQwNotice) {
		return super.findPage(hsQwNotice);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwNotice
	 * @return
	 */
	@Override
	public List<HsQwNotice> findList(HsQwNotice hsQwNotice) {
		return super.findList(hsQwNotice);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwNotice
	 */
	@Override
	@Transactional
	public void save(HsQwNotice hsQwNotice) {
		super.save(hsQwNotice);
		super.updateStatus(hsQwNotice);
		//todo 调用闽政通发布公告接口
		this.publicNotice(hsQwNotice);
	}

	/**
	 * 调用闽政通接口，发布公告
	 * @param hsQwNotice
	 */
	private void publicNotice(HsQwNotice hsQwNotice) {
		//调用数字屏山平接口发布通知
		apiSzpsService.submitPublicNotice("0",
				"通知公告发布",
				DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"),
				hsQwNotice.getNoticeContent(),
				null,
				hsQwNotice.getOffice().getOfficeName());
	}

	/**
	 * 更新状态
	 * @param hsQwNotice
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwNotice hsQwNotice) {
		super.updateStatus(hsQwNotice);
	}
	
	/**
	 * 删除数据
	 * @param hsQwNotice
	 */
	@Override
	@Transactional
	public void delete(HsQwNotice hsQwNotice) {
		super.delete(hsQwNotice);
	}

	/**
	 * 撤回公告信息
	 * @param hsQwNotice
	 */
	public void reback(HsQwNotice hsQwNotice) {
		hsQwNotice.setStatus("2");
		this.updateStatus(hsQwNotice);
		//todo 调用闽政通撤回接口
		this.callReBack(hsQwNotice);
	}

	private void callReBack(HsQwNotice hsQwNotice) {

	}
}