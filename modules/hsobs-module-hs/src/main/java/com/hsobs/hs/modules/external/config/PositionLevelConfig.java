package com.hsobs.hs.modules.external.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 职级配置类
 * 
 * <AUTHOR>
 * @version 2024-06-18
 */
@Component
@ConfigurationProperties(prefix = "position")
public class PositionLevelConfig {

    /**
     * 职级列表
     */
    private List<PositionLevel> levels;

    public List<PositionLevel> getLevels() {
        return levels;
    }

    public void setLevels(List<PositionLevel> levels) {
        this.levels = levels;
    }

    /**
     * 职级内部类
     */
    public static class PositionLevel {
        /**
         * 职级编码
         */
        private String code;
        
        /**
         * 职级名称
         */
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "PositionLevel{" +
                    "code='" + code + '\'' +
                    ", name='" + name + '\'' +
                    '}';
        }
    }
}
