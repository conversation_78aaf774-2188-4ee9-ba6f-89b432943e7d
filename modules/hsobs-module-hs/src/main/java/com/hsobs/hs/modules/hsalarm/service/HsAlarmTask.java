//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.hsobs.hs.modules.hsalarm.service;


import com.jeesite.common.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class HsAlarmTask extends BaseService {

    @Autowired
    private HsAlarmService hsAlarmService;

    @Transactional
    public void execute() {

    }

    @Transactional
    public void executeUnsignedContract() {
        // 未签合同
        hsAlarmService.executeUnsignedContract();
    }

    @Transactional
    public void executeRentArrears() {
        // 拖欠租金
        hsAlarmService.executeRentArrears();
    }

    @Transactional
    public void executeAnomaly() {
        // 时间异常
        hsAlarmService.executeTimeAnomaly();
        // 资格异常
        hsAlarmService.executeAbnormalQualification();
    }

    @Transactional
    public void executeInsufficientCreditLimit() {
        // 维修补助额度不足
        hsAlarmService.executeInsufficientCreditLimit();
    }
}
