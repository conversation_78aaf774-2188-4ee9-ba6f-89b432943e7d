package com.hsobs.hs.modules.house.dao;

import com.hsobs.hs.modules.external.entity.ApiHsHouseTemplate;
import com.hsobs.hs.modules.external.entity.ApiHsPriceLimitHouseTemplate;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * 租赁公租房房源房源信息表DAO接口
 * <AUTHOR>
 * @version 2024-11-20
 */
@MyBatisDao
public interface HsQwPublicRentalHouseDao extends <PERSON>rudDao<HsQwPublicRentalHouse> {


    List<ApiHsHouseTemplate> findHouseTemplateList(@Param("id") String id);

    List<ApiHsPriceLimitHouseTemplate> findPriceLimitHouseTemplateList(@Param("id") String id);
}