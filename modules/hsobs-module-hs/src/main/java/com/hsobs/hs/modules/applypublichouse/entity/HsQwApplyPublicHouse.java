package com.hsobs.hs.modules.applypublichouse.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候公示房源表Entity
 * <AUTHOR>
 * @version 2024-12-05
 */
@Table(name="hs_qw_apply_public_house", alias="a", label="租赁资格轮候公示房源表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="public_id", attrName="publicId", label="公示id"),
		@Column(name="house_id", attrName="houseId", label="房源id"),
	}, orderBy="a.id DESC"
)
public class HsQwApplyPublicHouse extends DataEntity<HsQwApplyPublicHouse> {
	
	private static final long serialVersionUID = 1L;
	private String publicId;		// 公示id
	private String houseId;		// 房源id

	public HsQwApplyPublicHouse() {
		this(null);
	}
	
	public HsQwApplyPublicHouse(String id){
		super(id);
	}
	
	@NotBlank(message="公示id不能为空")
	@Size(min=0, max=64, message="公示id长度不能超过 64 个字符")
	public String getPublicId() {
		return publicId;
	}

	public void setPublicId(String publicId) {
		this.publicId = publicId;
	}
	
	@NotBlank(message="房源id不能为空")
	@Size(min=0, max=64, message="房源id长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}
	
}