package com.hsobs.hs.modules.pricelimitapplyer.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.hsobs.hs.modules.pricelimitapplyer.service.HsPriceLimitApplyerService;

/**
 * 限价房-购房申请人Controller
 * <AUTHOR>
 * @version 2024-12-10
 */
@Controller
@RequestMapping(value = "${adminPath}/pricelimitapplyer/hsPriceLimitApplyer")
public class HsPriceLimitApplyerController extends BaseController {

	@Autowired
	private HsPriceLimitApplyerService hsPriceLimitApplyerService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPriceLimitApplyer get(String id, boolean isNewRecord) {
		return hsPriceLimitApplyerService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitapplyer:hsPriceLimitApplyer:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPriceLimitApplyer hsPriceLimitApplyer, Model model) {
		model.addAttribute("hsPriceLimitApplyer", hsPriceLimitApplyer);
		return "modules/pricelimitapplyer/hsPriceLimitApplyerList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("pricelimitapplyer:hsPriceLimitApplyer:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsPriceLimitApplyer> listData(HsPriceLimitApplyer hsPriceLimitApplyer, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApplyer.setPage(new Page<>(request, response));
		Page<HsPriceLimitApplyer> page = hsPriceLimitApplyerService.findPage(hsPriceLimitApplyer);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("pricelimitapplyer:hsPriceLimitApplyer:view")
	@RequestMapping(value = "form")
	public String form(HsPriceLimitApplyer hsPriceLimitApplyer, Model model) {
		model.addAttribute("hsPriceLimitApplyer", hsPriceLimitApplyer);
		return "modules/pricelimitapplyer/hsPriceLimitApplyerForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("pricelimitapplyer:hsPriceLimitApplyer:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsPriceLimitApplyer hsPriceLimitApplyer) {
		hsPriceLimitApplyerService.save(hsPriceLimitApplyer);
		return renderResult(Global.TRUE, text("保存限价房-购房申请人成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("pricelimitapplyer:hsPriceLimitApplyer:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsPriceLimitApplyer hsPriceLimitApplyer) {
		hsPriceLimitApplyer.setStatus(HsPriceLimitApplyer.STATUS_DISABLE);
		hsPriceLimitApplyerService.updateStatus(hsPriceLimitApplyer);
		return renderResult(Global.TRUE, text("停用限价房-购房申请人成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("pricelimitapplyer:hsPriceLimitApplyer:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsPriceLimitApplyer hsPriceLimitApplyer) {
		hsPriceLimitApplyer.setStatus(HsPriceLimitApplyer.STATUS_NORMAL);
		hsPriceLimitApplyerService.updateStatus(hsPriceLimitApplyer);
		return renderResult(Global.TRUE, text("启用限价房-购房申请人成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("pricelimitapplyer:hsPriceLimitApplyer:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsPriceLimitApplyer hsPriceLimitApplyer) {
		hsPriceLimitApplyerService.delete(hsPriceLimitApplyer);
		return renderResult(Global.TRUE, text("删除限价房-购房申请人成功！"));
	}
	
}