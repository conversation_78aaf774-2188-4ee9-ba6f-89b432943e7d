package com.hsobs.hs.modules.applypublichouse.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.applypublichouse.entity.HsQwApplyPublicHouse;
import com.hsobs.hs.modules.applypublichouse.dao.HsQwApplyPublicHouseDao;

/**
 * 租赁资格轮候公示房源表Service
 * <AUTHOR>
 * @version 2024-12-05
 */
@Service
public class HsQwApplyPublicHouseService extends CrudService<HsQwApplyPublicHouseDao, HsQwApplyPublicHouse> {
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyPublicHouse
	 * @return
	 */
	@Override
	public HsQwApplyPublicHouse get(HsQwApplyPublicHouse hsQwApplyPublicHouse) {
		return super.get(hsQwApplyPublicHouse);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyPublicHouse 查询条件
	 * @param hsQwApplyPublicHouse page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyPublicHouse> findPage(HsQwApplyPublicHouse hsQwApplyPublicHouse) {
		return super.findPage(hsQwApplyPublicHouse);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyPublicHouse
	 * @return
	 */
	@Override
	public List<HsQwApplyPublicHouse> findList(HsQwApplyPublicHouse hsQwApplyPublicHouse) {
		return super.findList(hsQwApplyPublicHouse);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyPublicHouse
	 */
	@Override
	@Transactional
	public void save(HsQwApplyPublicHouse hsQwApplyPublicHouse) {
		super.save(hsQwApplyPublicHouse);
	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyPublicHouse
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyPublicHouse hsQwApplyPublicHouse) {
		super.updateStatus(hsQwApplyPublicHouse);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyPublicHouse
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyPublicHouse hsQwApplyPublicHouse) {
		super.delete(hsQwApplyPublicHouse);
	}
	
}