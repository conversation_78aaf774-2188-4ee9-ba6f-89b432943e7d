package com.hsobs.hs.modules.formalarm.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import com.hsobs.hs.modules.formalarm.service.HsQwFormAlarmService;

/**
 * 表单信息预警表Controller
 * <AUTHOR>
 * @version 2025-03-22
 */
@Controller
@RequestMapping(value = "${adminPath}/formalarm/hsQwFormAlarm")
public class HsQwFormAlarmController extends BaseController {

	@Autowired
	private HsQwFormAlarmService hsQwFormAlarmService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwFormAlarm get(String id, boolean isNewRecord) {
		return hsQwFormAlarmService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formalarm:hsQwFormAlarm:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwFormAlarm hsQwFormAlarm, Model model) {
		model.addAttribute("hsQwFormAlarm", hsQwFormAlarm);
		return "modules/formalarm/hsQwFormAlarmList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formalarm:hsQwFormAlarm:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwFormAlarm> listData(HsQwFormAlarm hsQwFormAlarm, HttpServletRequest request, HttpServletResponse response) {
		hsQwFormAlarm.setPage(new Page<>(request, response));
		Page<HsQwFormAlarm> page = hsQwFormAlarmService.findPage(hsQwFormAlarm);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formalarm:hsQwFormAlarm:view")
	@RequestMapping(value = "form")
	public String form(HsQwFormAlarm hsQwFormAlarm, Model model) {
		model.addAttribute("hsQwFormAlarm", hsQwFormAlarm);
		return "modules/formalarm/hsQwFormAlarmForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formalarm:hsQwFormAlarm:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwFormAlarm hsQwFormAlarm) {
		hsQwFormAlarmService.save(hsQwFormAlarm);
		return renderResult(Global.TRUE, text("保存表单信息预警表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("formalarm:hsQwFormAlarm:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwFormAlarm hsQwFormAlarm) {
		hsQwFormAlarmService.delete(hsQwFormAlarm);
		return renderResult(Global.TRUE, text("删除表单信息预警表成功！"));
	}

	/**
	 * 删除数据
	 */
//	@RequiresPermissions("formalarm:hsQwFormAlarm:edit")
	@RequestMapping(value = "dismiss")
	@ResponseBody
	public String dismiss(HsQwFormAlarm hsQwFormAlarm) {
		hsQwFormAlarmService.dismiss(hsQwFormAlarm);
		return renderResult(Global.TRUE, text("忽略表单信息预警表成功！"));
	}
	
}