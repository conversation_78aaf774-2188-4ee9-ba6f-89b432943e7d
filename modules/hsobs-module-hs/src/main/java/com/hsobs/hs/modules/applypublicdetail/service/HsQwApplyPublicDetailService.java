package com.hsobs.hs.modules.applypublicdetail.service;

import java.util.ArrayList;
import java.util.List;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applypublic.entity.HsQwApplyPublic;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.utils.BpmUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.applypublicdetail.entity.HsQwApplyPublicDetail;
import com.hsobs.hs.modules.applypublicdetail.dao.HsQwApplyPublicDetailDao;

/**
 * 租赁资格轮候公示复查详情表Service
 * <AUTHOR>
 * @version 2024-12-05
 */
@Service
public class HsQwApplyPublicDetailService extends CrudService<HsQwApplyPublicDetailDao, HsQwApplyPublicDetail> {
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyPublicDetail
	 * @return
	 */
	@Override
	public HsQwApplyPublicDetail get(HsQwApplyPublicDetail hsQwApplyPublicDetail) {
		return super.get(hsQwApplyPublicDetail);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyPublicDetail 查询条件
	 * @param hsQwApplyPublicDetail page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyPublicDetail> findPage(HsQwApplyPublicDetail hsQwApplyPublicDetail) {
		return super.findPage(hsQwApplyPublicDetail);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyPublicDetail
	 * @return
	 */
	@Override
	public List<HsQwApplyPublicDetail> findList(HsQwApplyPublicDetail hsQwApplyPublicDetail) {
		return super.findList(hsQwApplyPublicDetail);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyPublicDetail
	 */
	@Override
	@Transactional
	public void save(HsQwApplyPublicDetail hsQwApplyPublicDetail) {
		super.save(hsQwApplyPublicDetail);
	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyPublicDetail
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyPublicDetail hsQwApplyPublicDetail) {
		super.updateStatus(hsQwApplyPublicDetail);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyPublicDetail
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyPublicDetail hsQwApplyPublicDetail) {
		super.delete(hsQwApplyPublicDetail);
	}

	public String getHasApplyIdStr(String applyId) {
		List<String> waitRentHouseIdList = new ArrayList<>();//待分配的申请单房源号
		List<String> hasRentHouseIdList = new ArrayList<>();//已分配的申请单房源号
		HsQwApplyPublic applyPublic;
		//获取申请单对应的公示单号
		HsQwApplyPublicDetail query = new HsQwApplyPublicDetail();
		query.setApplyId(applyId);
		query.setHsQwApplyPublic(new HsQwApplyPublic());
		query.getHsQwApplyPublic().setPublicType("1");
		query.sqlMap().getOrder().setOrderBy("o.public_date,o.id desc");
		List<HsQwApplyPublicDetail> list =  this.findList(query);
		if (list.size()>0){
			applyPublic = list.get(0).getHsQwApplyPublic();
		} else {
			return "";
		}
		//获取公示单所有的申请单
		query.setPublicId(applyPublic.getId());
		query.setApplyId(null);
		query.sqlMap().getWhere().and("apply_id", QueryType.NE, applyId);
		List<HsQwApplyPublicDetail> list1 =  this.findList(query);
		list1.forEach(l-> {
			if (StringUtils.isNotBlank(l.getHsQwApply().getHouseId()) && !BpmUtils.getProcIns(l.getHsQwApply(), "rent_apply").getStatus().equals("房源配租")){
				hasRentHouseIdList.add(l.getHsQwApply().getHouseId());
			}
		});
		//排除已分配的房源号
		String[] houseIds = list.stream().findFirst().get().getHsQwApplyPublic().getPublicHouseIds().split(",");
		for (String houseId : houseIds) {
			if (!hasRentHouseIdList.contains(houseId)){
				waitRentHouseIdList.add(houseId);
			}
		}
		return String.join(",", waitRentHouseIdList);
	}
}