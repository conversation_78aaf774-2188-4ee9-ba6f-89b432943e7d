package com.hsobs.hs.modules.apply.service.serviceTask;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.utils.SpringUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.entity.DictData;
import com.jeesite.modules.sys.utils.DictUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.engine.ProcessEngines;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公租房申请流程节点响应事件
 * 1、流程节点在配租之前的节点，要做房源信息的清理
 * 2、流程节点在签订合同之前的节点，要做合同信息的清理
 */
@Service
public class HsQwApplyInvalidService {

    public static void invalid(ExecutionEntityImpl execution) {
        String processName = execution.getActivityName();
        if (StringUtils.isEmpty(processName)) {
            RepositoryService repositoryService = ProcessEngines.getDefaultProcessEngine().getRepositoryService();
            // 获取流程模型
            BpmnModel bpmnModel = repositoryService.getBpmnModel(execution.getProcessDefinitionId());

            if (bpmnModel != null) {
                // 一般只有一个主流程
                Process process = bpmnModel.getMainProcess();

                // 获取对应的元素（任务、网关、开始节点等等）
                FlowElement flowElement = process.getFlowElement(execution.getActivityId(), true);

                if (flowElement != null) {
                    processName = flowElement.getName();  // 就是你想要的 activityName
                }
            }
        }
        // 获取bizKey
        String bizKey = BpmUtils.getBizKey(execution);
        List<DictData> dl = DictUtils.getDictList("hs_apply_status");
        // 获取字典值为房源配租的字典
        DictData rentData = getDictDataFormList(dl, "房源配租");
        // 获取字典值为签订租赁合同的字典
        DictData compactData = getDictDataFormList(dl, "签订租赁合同");
        // 获取当前流程节点的字典值
        DictData currentData = getDictDataFormList(dl, processName);

        if (currentData == null) {
            return;
        }
        // 通过Spring上下文获取service实例
        HsQwApplyService applyService = SpringUtils.getBean(HsQwApplyService.class);

        // 查询申请单信息
        HsQwApply hsQwApply = applyService.get(bizKey);

        if (hsQwApply == null) {
            return;
        }

        if (currentData.getTreeSort() < compactData.getTreeSort()) {
            // 执行合同失效动作
            applyService.deleteApplyCompact(bizKey);
        }
        if (currentData.getTreeSort() < rentData.getTreeSort()) {
            // 执行房源失效动作
            applyService.invalidApplyRentalHouse(bizKey);
        }
    }

    private static DictData getDictDataFormList(List<DictData> dl, String value) {
        for (DictData dictData : dl) {
            if (dictData.getDictValue().equals(value)) {
                return dictData;
            }
        }
        return null;
    }

}
