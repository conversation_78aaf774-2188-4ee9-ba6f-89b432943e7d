package com.hsobs.hs.modules.elevator.service;

import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.elevator.entity.HsElevatorApply;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmBackActivity;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class HsElevatorApplyBatchService extends BaseService {

    @Autowired
    private HsElevatorApplyService hsElevatorApplyService;
    @Autowired
    private BpmTaskService bpmTaskService;

    public void batchSave(HsElevatorApply hsElevatorApply) {
        if (hsElevatorApply.getIdsStr() == null || hsElevatorApply.getIdsStr().isEmpty()) {
            throw new ServiceException(text("非法操作，前端数据被劫持！"));
        }
        // 0-通过  1-驳回
        String approvalType = hsElevatorApply.getApprovalType();
        String comment = hsElevatorApply.getBpm().getComment();
        BpmTask params = new BpmTask();
        params.setStatus("1");
        params.getProcIns().setFormKey("elevator_subsidy_apply");

        List<String> ids = JSONObject.parseArray(hsElevatorApply.getIdsStr(), String.class);

        for (String id : ids) {
            if (id.startsWith("[")) {
                id = id.substring(1, id.length() - 1);
            } else if (id.endsWith("]")) {
                id = id.substring(0, id.length() - 1);
            }
            HsElevatorApply hsElevatorApplyNew = hsElevatorApplyService.get(id);
            params.getProcIns().setBizKey(id);
            Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);
            BpmTask task = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
            if (task == null) {
                continue;
            }
            BpmTask bpmTask = new BpmTask();
            bpmTask.setId(task.getId());

            // 自动签收
            if (StringUtils.isEmpty(task.getAssignee())) {
                bpmTaskService.claimTask(bpmTask);
            }

            if ("0".equals(approvalType)) {
                BpmParams bpmParams = new BpmParams();
                bpmParams.setTaskId(task.getId());
                bpmParams.setProcInsId(task.getProcIns().getId());
                bpmParams.setActivityId(task.getActivityId());
                bpmParams.setComment(comment);
                hsElevatorApplyNew.setBpm(bpmParams);
				hsElevatorApplyService.save(hsElevatorApplyNew);
            } else if ("1".equals(approvalType)) {
                BpmBackActivity backActivity = null;
                List<BpmBackActivity> backActivityList = this.bpmTaskService.getBackActivity(task);
                if (backActivityList != null && !backActivityList.isEmpty()) {
                    for (BpmBackActivity bpmBackActivity : backActivityList) {
                        if (bpmBackActivity.getActivityId().equals(hsElevatorApply.getBpm().getActivityId())) {
                            backActivity = bpmBackActivity;
                            hsElevatorApply.getBpm().getActivityId();
                            bpmTask.setActivityId(backActivity.getActivityId());
                            bpmTask.setComment(comment);
                            bpmTask.setNextUserCodes(backActivity.getAssignee());
				            bpmTaskService.backTask(bpmTask);
                            // 更新状态
                            String activityIdStr = backActivity.getActivityId().replace("elevatorSubsidyApply", "");
                            while (activityIdStr.startsWith("0")) {
                                activityIdStr = StringUtils.removeStart(activityIdStr, "0");
                            }
                            if (StringUtils.isNumeric(activityIdStr)) {
                                int activityIdInt = Integer.parseInt(activityIdStr);
                                activityIdInt = hsElevatorApplyService.getPrevApplyStatus(activityIdInt);
                                hsElevatorApplyNew.setApplyStatus(activityIdInt);
                                hsElevatorApplyService.update(hsElevatorApplyNew);
                            }
                            break;
                        }
                    }
                }
            } else {
                throw new ServiceException(text("非法操作，前端数据被劫持！"));
            }
        }
    }

}
