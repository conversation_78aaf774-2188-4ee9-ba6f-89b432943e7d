package com.hsobs.hs.modules.contract.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 合同模板字段Entity
 * <AUTHOR>
 * @version 2025-01-21
 */
@Table(name="hs_contract_template_field", alias="a", label="合同模板字段信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="template_id", attrName="templateId", label="字段归属模板ID"),
		@Column(name="field_type", attrName="fieldType", label="字段类型;"),
		@Column(name="field_name", attrName="fieldName", label="字段名称", queryType=QueryType.LIKE),
		@Column(name="field_code", attrName="fieldCode", label="字段标识编码"),
		@Column(name="field_subkey", attrName="fieldSubkey", label="字段子项数据;单选、多选等选择类数据"),
		@Column(name="field_desc", attrName="fieldDesc", label="字段填写描述"),
		@Column(name="order_num", attrName="orderNum", label="字段排序编号", isUpdateForce=true),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;"),
	}, orderBy="a.order_num ASC"
)
public class HsContractTemplateField extends DataEntity<HsContractTemplateField> {
	
	private static final long serialVersionUID = 1L;
	private String templateId;		// 字段归属模板ID
	private String fieldType;		// 字段类型;
	private String fieldName;		// 字段名称
	private String fieldCode;		// 字段标识编码
	private String fieldSubkey;		// 字段子项数据;单选、多选等选择类数据
	private String fieldDesc;		// 字段填写描述
	private Integer orderNum;		// 字段排序编号
	private String validTag;		// 是否有效;

	public HsContractTemplateField() {
		this(null);
	}
	public HsContractTemplateField(String id, String fieldName, String fieldCode) {
		super(id);
		this.setFieldName(fieldName);
		this.setFieldCode(fieldCode);
	}

	public HsContractTemplateField(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="字段归属模板ID长度不能超过 64 个字符")
	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	
	@Size(min=0, max=32, message="字段类型;长度不能超过 32 个字符")
	public String getFieldType() {
		return fieldType;
	}

	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	
	@Size(min=0, max=255, message="字段名称长度不能超过 255 个字符")
	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	
	@Size(min=0, max=128, message="字段标识编码长度不能超过 128 个字符")
	public String getFieldCode() {
		return fieldCode;
	}

	public void setFieldCode(String fieldCode) {
		this.fieldCode = fieldCode;
	}
	
	@Size(min=0, max=2000, message="字段子项数据;单选、多选等选择类数据长度不能超过 2000 个字符")
	public String getFieldSubkey() {
		return fieldSubkey;
	}

	public void setFieldSubkey(String fieldSubkey) {
		this.fieldSubkey = fieldSubkey;
	}
	
	@Size(min=0, max=255, message="字段填写描述长度不能超过 255 个字符")
	public String getFieldDesc() {
		return fieldDesc;
	}

	public void setFieldDesc(String fieldDesc) {
		this.fieldDesc = fieldDesc;
	}
	
	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}
	
	@Size(min=0, max=1, message="是否有效;长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}
	
}