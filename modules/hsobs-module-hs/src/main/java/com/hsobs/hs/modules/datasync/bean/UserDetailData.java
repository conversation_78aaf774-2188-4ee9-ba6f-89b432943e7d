package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量获取用户详细信息响应实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDetailData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String userId;
    private String mobile;
    private String name;
    private String orgId;
    private String orgName;
    /** 0-禁用 1-启用 */
    private String account;
    private Integer status;
    private String certType;
    private String certTypeName;
    private String idCard;
    /** 1-个人 2-企业 */
    private Integer type;

}
