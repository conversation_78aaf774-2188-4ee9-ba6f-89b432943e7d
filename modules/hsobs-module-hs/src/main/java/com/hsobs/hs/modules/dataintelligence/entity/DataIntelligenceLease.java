package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;

import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 住房保障数据统计Entity  公租房租赁情况统计
 * <AUTHOR>
 * @version 2025-1-2
 */
public class DataIntelligenceLease extends DataEntity<DataIntelligenceLease> {

	@ExcelFields({
			@ExcelField(title = "小区名称", attrName = "estateName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "已签订合同数", attrName = "signedContractCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "未签订合同数", attrName = "unsignedContractCount", align = ExcelField.Align.CENTER, sort = 40),
			@ExcelField(title = "应收租金", attrName = "receivables", align = ExcelField.Align.CENTER, sort = 50),
			@ExcelField(title = "已收租金", attrName = "paid", align = ExcelField.Align.CENTER, sort = 60),
			@ExcelField(title = "拖欠租金", attrName = "unpaid", align = ExcelField.Align.CENTER, sort = 70),
			@ExcelField(title = "总面积", attrName = "buildingArea", align = ExcelField.Align.CENTER, sort = 80),
			@ExcelField(title = "35-45m²", attrName = "buildingArea1", align = ExcelField.Align.CENTER, sort = 90),
			@ExcelField(title = "60-65m²", attrName = "buildingArea2", align = ExcelField.Align.CENTER, sort = 100),
			@ExcelField(title = "70m²以上", attrName = "buildingArea3", align = ExcelField.Align.CENTER, sort = 110),
	})

	private static final long serialVersionUID = 1L;
	private String parentCodeTree;

	private String city;
	private String area;

	private String officeCode;		// 单位编码
	private String officeName;		// 单位名称
	private Date startDate;		//开始日期
	private Date endDate;	//结束日期

	private String estateId;	// 小区id
	private String estateName;	// 小区名称
	private Long appliedForCount;
	private Long pendingApprovalCount;
	private Long approvedCount;
	private Long signedContractCount;
	private Long unsignedContractCount;

	private Long checkInCount;		// 入住数

	private Long receivables;		// 应收租金
	private Long paid;				// 已收租金
	private Long unpaid;		// 拖欠租金

	private Long buildingArea;		// 房源面积		使用long，不试用float型
	private Long buildingArea1;		// 房源面积		使用long，不试用float型
	private Long buildingArea2;		// 房源面积		使用long，不试用float型
	private Long buildingArea3;		// 房源面积		使用long，不试用float型
	private Long buildingArea4;		// 房源面积		使用long，不试用float型

	private Long vacantCount;
	private Long vacantArea;
	private Long vacant0Count;
	private Long vacant0Area;
	private Long vacant3Count;
	private Long vacant3Area;
	private Long vacant4Count;
	private Long vacant4Area;

	public DataIntelligenceLease() {
		this(null);
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			endDate = new Date();
			startDate = dateFormat.parse(String.format("%04d-01-01", endDate.getYear()+1900));  // 解析指定日期字符串
		} catch (Exception e) {
		}
		parentCodeTree = "";
		if(!UserUtils.getUser().isAdmin()){
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		else{
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		city = "";
		area = "";
		officeCode = "";
	}
	
	public DataIntelligenceLease(String id){
		super(id);
	}

	public String getParentCodeTree() {
		return parentCodeTree;
	}

	public void setParentCodeTree(String parentCodeTree) {
		this.parentCodeTree = parentCodeTree;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getEstateId() {
		return estateId;
	}

	public void setEstateId(String estateId) {
		this.estateId = estateId;
	}

	public String getEstateName() {
		return estateName;
	}

	public void setEstateName(String estateName) {
		this.estateName = estateName;
	}

	public Long getAppliedForCount() {
		return appliedForCount;
	}

	public void setAppliedForCount(Long appliedForCount) {
		this.appliedForCount = appliedForCount;
	}

	public Long getPendingApprovalCount() {
		return pendingApprovalCount;
	}

	public void setPendingApprovalCount(Long pendingApprovalCount) {
		this.pendingApprovalCount = pendingApprovalCount;
	}

	public Long getApprovedCount() {
		return approvedCount;
	}

	public void setApprovedCount(Long approvedCount) {
		this.approvedCount = approvedCount;
	}

	public Long getSignedContractCount() {
		return signedContractCount;
	}

	public void setSignedContractCount(Long signedContractCount) {
		this.signedContractCount = signedContractCount;
	}

	public Long getUnsignedContractCount() {
		return unsignedContractCount;
	}

	public void setUnsignedContractCount(Long unsignedContractCount) {
		this.unsignedContractCount = unsignedContractCount;
	}

	public Long getCheckInCount() {
		return checkInCount;
	}

	public void setCheckInCount(Long checkInCount) {
		this.checkInCount = checkInCount;
	}

	public Long getReceivables() {
		return receivables;
	}

	public void setReceivables(Long receivables) {
		this.receivables = receivables;
	}

	public Long getPaid() {
		return paid;
	}

	public void setPaid(Long paid) {
		this.paid = paid;
	}

	public Long getUnpaid() {
		return unpaid;
	}

	public void setUnpaid(Long unpaid) {
		this.unpaid = unpaid;
	}

	public Long getBuildingArea() {
		return buildingArea;
	}

	public void setBuildingArea(Long buildingArea) {
		this.buildingArea = buildingArea;
	}

	public Long getBuildingArea1() {
		return buildingArea1;
	}

	public void setBuildingArea1(Long buildingArea1) {
		this.buildingArea1 = buildingArea1;
	}

	public Long getBuildingArea2() {
		return buildingArea2;
	}

	public void setBuildingArea2(Long buildingArea2) {
		this.buildingArea2 = buildingArea2;
	}

	public Long getBuildingArea3() {
		return buildingArea3;
	}

	public void setBuildingArea3(Long buildingArea3) {
		this.buildingArea3 = buildingArea3;
	}

	public Long getBuildingArea4() {
		return buildingArea4;
	}

	public void setBuildingArea4(Long buildingArea4) {
		this.buildingArea4 = buildingArea4;
	}

	public Long getVacantCount() {
		return vacantCount;
	}

	public void setVacantCount(Long vacantCount) {
		this.vacantCount = vacantCount;
	}

	public Long getVacantArea() {
		return vacantArea;
	}

	public void setVacantArea(Long vacantArea) {
		this.vacantArea = vacantArea;
	}

	public Long getVacant0Count() {
		return vacant0Count;
	}

	public void setVacant0Count(Long vacant0Count) {
		this.vacant0Count = vacant0Count;
	}

	public Long getVacant0Area() {
		return vacant0Area;
	}

	public void setVacant0Area(Long vacant0Area) {
		this.vacant0Area = vacant0Area;
	}

	public Long getVacant3Count() {
		return vacant3Count;
	}

	public void setVacant3Count(Long vacant3Count) {
		this.vacant3Count = vacant3Count;
	}

	public Long getVacant3Area() {
		return vacant3Area;
	}

	public void setVacant3Area(Long vacant3Area) {
		this.vacant3Area = vacant3Area;
	}

	public Long getVacant4Count() {
		return vacant4Count;
	}

	public void setVacant4Count(Long vacant4Count) {
		this.vacant4Count = vacant4Count;
	}

	public Long getVacant4Area() {
		return vacant4Area;
	}

	public void setVacant4Area(Long vacant4Area) {
		this.vacant4Area = vacant4Area;
	}
}