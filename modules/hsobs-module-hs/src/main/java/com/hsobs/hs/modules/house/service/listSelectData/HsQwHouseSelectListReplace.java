package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 公租房置换，选择房源：选择待配租的、没有在流程中的房源1，进行选择置换
 */
@Service
public class HsQwHouseSelectListReplace implements HsQwHouseSelectList {
    public String getDataType() {
        return "2";
    }

    public Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse,
            HsQwPublicRentalHouseService hsQwPublicRentalHouseService) {
        hsQwPublicRentalHouse.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_RENTAL);
        hsQwPublicRentalHouse.setHouseStatus("0");
        hsQwPublicRentalHouse.setIsPublic("1");
        hsQwPublicRentalHouse.sqlMap().getWhere().disableAutoAddStatusWhere();
        StringBuffer sb = new StringBuffer(" and a.id not in " +
                " (select hqa1.house_id from hs_qw_apply hqa1 where " +
                "  hqa1.status = 4 AND hqa1.HOUSE_ID  IS NOT NULL ");
        if (StringUtils.isNotBlank(hsQwPublicRentalHouse.getSelectId())){
            sb.append(" and hqa1.house_id != " + hsQwPublicRentalHouse.getSelectId());
        }
        sb.append(")");
        hsQwPublicRentalHouse.sqlMap().add("extWhere", sb.toString());
        return hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
    }
}
