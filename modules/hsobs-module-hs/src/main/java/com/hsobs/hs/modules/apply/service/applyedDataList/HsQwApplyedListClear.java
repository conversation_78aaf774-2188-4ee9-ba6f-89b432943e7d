package com.hsobs.hs.modules.apply.service.applyedDataList;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.clearance.dao.HsQwClearanceDao;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.hsobs.hs.modules.clearance.service.HsQwClearanceService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 现场核验-入户核验
 */
@Service
public class HsQwApplyedListClear implements HsQwApplyedList{

    @Autowired
    private HsQwClearanceDao hsQwClearanceDao;

    @Override
    public String getDataType() {
        return "3";
    }

    @Override
    public Page<HsQwApply> execute(HsQwApply hsQwApply,  HsQwApplyService hsQwApplyService) {
        String[] auditIds = this.getAuditIds();
        hsQwApply.sqlMap().getWhere().and("id", QueryType.NOT_IN, auditIds);
        hsQwApply.sqlMap().getWhere().and("a.apply_matter", QueryType.IN, new String[]{"0","1","2","4"});
        hsQwApply.setStatus(HsQwApply.STATUS_NORMAL);
        return hsQwApplyService.findPage(hsQwApply);
    }

    public String[] getAuditIds() {
        HsQwClearance hsQwClearance = new HsQwClearance();
        hsQwClearance.setStatus(HsQwClearance.STATUS_AUDIT);
        List<HsQwClearance> list = hsQwClearanceDao.findList(hsQwClearance);
        return list.stream().map(HsQwClearance::getApplyId).toArray(String[]::new);
    }

}
