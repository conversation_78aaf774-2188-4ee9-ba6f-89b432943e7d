package com.hsobs.hs.modules.elevator.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.elevator.service.HsElevatorApplyBatchService;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.bpm.entity.BpmBackActivity;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.elevator.entity.HsElevatorApply;
import com.hsobs.hs.modules.elevator.service.HsElevatorApplyService;

/**
 * 加装电梯补助申请表Controller
 * <AUTHOR>
 * @version 2024-12-24
 */
@Controller
@RequestMapping(value = "${adminPath}/elevator/hsElevatorApply")
public class HsElevatorApplyController extends BaseController {

	@Autowired
	private HsElevatorApplyService hsElevatorApplyService;
	@Autowired
	private BpmTaskService bpmTaskService;
	@Autowired
	private HsElevatorApplyBatchService hsElevatorApplyBatchService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsElevatorApply get(String id, boolean isNewRecord) {
		return hsElevatorApplyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsElevatorApply hsElevatorApply, Model model) {
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		return "modules/elevator/hsElevatorApplyList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = {"applyList", ""})
	public String applyList(HsElevatorApply hsElevatorApply, Model model) {
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/elevator/hsElevatorApplyList";
	}
	/**
	 * 查询列表
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = {"auditList", ""})
	public String auditList(HsElevatorApply hsElevatorApply, Model model) {
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/elevator/hsElevatorApplyAuditList";
	}

	/**
	 *
	 * 查询列表
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = {"listDone", ""})
	public String listDone(HsElevatorApply hsElevatorApply, Model model) {
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		// 新增用户组织管理功能的控制权限设置 user.adminCtrlPermi，1拥有的权限 2管理的权限，无限级授权场景使用
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/elevator/hsElevatorApplyDoneList";
	}

	/**
	 *  核验拨付列表
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = {"verifyList", ""})
	public String verifyList(HsElevatorApply hsElevatorApply, Model model) {
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/elevator/hsElevatorApplyVerifyList";
	}

	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = {"recordListIndex", ""})
	public String recordListIndex(HsElevatorApply hsElevatorApply, Model model) {
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/elevator/hsElevatorApplyRecordIndex";
	}

	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = {"recordList", ""})
	public String recordList(HsElevatorApply hsElevatorApply, Model model) {
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/elevator/hsElevatorApplyRecordList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsElevatorApply> listData(HsElevatorApply hsElevatorApply, HttpServletRequest request, HttpServletResponse response) {
		hsElevatorApply.setPage(new Page<>(request, response));
		hsElevatorApplyService.addDataScopeFilter(hsElevatorApply);
		Page<HsElevatorApply> page = hsElevatorApplyService.findPage(hsElevatorApply);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = "auditListData")
	@ResponseBody
	public Page<HsElevatorApply> auditListData(HsElevatorApply hsElevatorApply, HttpServletRequest request, HttpServletResponse response) {
		hsElevatorApply.setPage(new Page<>(request, response));
		Page<HsElevatorApply> page = hsElevatorApplyService.findAuditPageByTask(hsElevatorApply);
		return page;
	}

	/**
	 * 查询已办的审批任务-加装电梯补助申请已办
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = "listAuditedData")
	@ResponseBody
	public Page<HsElevatorApply> listAuditedData(HsElevatorApply hsElevatorApply, HttpServletRequest request, HttpServletResponse response) {
		hsElevatorApply.setPage(new Page<>(request, response));
		Page<HsElevatorApply> page = hsElevatorApplyService.findApplyPageByTask(hsElevatorApply, null, "2", "elevator_subsidy_apply");
		return page;
	}


	/**
	 * 核验拨付列表数据
	 * @param hsElevatorApply
	 * @param request
	 * @param response
	 * @return
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = "verifyListData")
	@ResponseBody
	public Page<HsElevatorApply> verifyListData(HsElevatorApply hsElevatorApply, HttpServletRequest request, HttpServletResponse response) {
		hsElevatorApply.setPage(new Page<>(request, response));
		Page<HsElevatorApply> page = hsElevatorApplyService.findVerifyPageByTask(hsElevatorApply);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = "form")
	public String form(HsElevatorApply hsElevatorApply, Model model) {
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		if (hsElevatorApply.getApplyStatus() == null) {
			hsElevatorApply.setApplyStatus(-1);
		}
		String isRead = "false";
		if (hsElevatorApply.getApplyStatus() >= 1) {
			isRead = "true";
		}
		model.addAttribute("isRead", isRead);
		//TODO 流程状态核验
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/elevator/hsElevatorApplyForm";
	}
	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("elevator:hsElevatorApply:batchApproval")
	@RequestMapping(value = "batchForm")
	public String batchForm(HsElevatorApply hsElevatorApply, Model model) {
		if (hsElevatorApply.getIds() == null || hsElevatorApply.getIds().isEmpty()) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		BpmTask params = new BpmTask();
		params.setStatus("1");
		// 维修资金申请流程key
		params.getProcIns().setFormKey("elevator_subsidy_apply");
		params.getProcIns().setBizKey(hsElevatorApply.getIds().get(0));
		Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);
		BpmTask task = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
		if (task != null) {
			List<BpmBackActivity> backActivity = this.bpmTaskService.getBackActivity(task);
			model.addAttribute("backActivity", backActivity);
			params = task;
		}
		model.addAttribute("task", params);
		model.addAttribute("hsElevatorApply", hsElevatorApply);

		return "modules/elevator/hsElevatorApplyBatchForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:batchApproval")
	@PostMapping(value = "batchSave")
	@ResponseBody
	public String batchSave(@Validated HsElevatorApply hsElevatorApply) {
		hsElevatorApplyBatchService.batchSave(hsElevatorApply);
		if ("1".equals(hsElevatorApply.getApprovalType())) {
			return renderResult(Global.TRUE, text("退回成功！"));
		}
		return renderResult(Global.TRUE, text("提交成功！"));
	}
	/**
	 * 保存数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsElevatorApply hsElevatorApply) {
		hsElevatorApplyService.save(hsElevatorApply);
		if (hsElevatorApply.getApplyStatus() == null || hsElevatorApply.getApplyStatus() <= 1) {
			return renderResult(Global.TRUE, text("保存加装电梯补助申请成功！"));
		} else {
			return renderResult(Global.TRUE, text("提交成功！"));
		}
	}
	/**
	 * 保存数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:edit")
	@PostMapping(value = "flushTaskStatus")
	@ResponseBody
	public String flushTaskStatus(@Validated HsElevatorApply hsElevatorApply) {
		hsElevatorApplyService.flushTaskStatus(hsElevatorApply);
		return renderResult(Global.TRUE, text("刷新加装电梯补助申请状态成功！"));
	}

	/**
	 * 停用数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsElevatorApply hsElevatorApply) {
		hsElevatorApply.setStatus(HsElevatorApply.STATUS_DISABLE);
		hsElevatorApplyService.updateStatus(hsElevatorApply);
		return renderResult(Global.TRUE, text("停用加装电梯补助申请表成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsElevatorApply hsElevatorApply) {
		hsElevatorApply.setStatus(HsElevatorApply.STATUS_NORMAL);
		hsElevatorApplyService.updateStatus(hsElevatorApply);
		return renderResult(Global.TRUE, text("启用加装电梯补助申请表成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsElevatorApply hsElevatorApply) {
		if (!HsElevatorApply.STATUS_DRAFT.equals(hsElevatorApply.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		hsElevatorApplyService.delete(hsElevatorApply);
		return renderResult(Global.TRUE, text("删除加装电梯补助申请表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = "hsElevatorApplySelect")
	public String hsElevatorApplySelect(HsElevatorApply hsElevatorApply, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsElevatorApply", hsElevatorApply);
		return "modules/elevator/hsElevatorApplySelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsElevatorApply hsElevatorApply, HttpServletResponse response) {
		hsElevatorApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		hsElevatorApplyService.addDataScopeFilter(hsElevatorApply);
		List<HsElevatorApply> list = hsElevatorApplyService.findList(hsElevatorApply);
		String fileName = "加装电梯补助申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("加装电梯补助申请", HsElevatorApply.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("elevator:hsElevatorApply:view")
	@RequestMapping(value = "exportAuditData")
	public void exportAuditData(HsElevatorApply hsElevatorApply, HttpServletResponse response) {
		hsElevatorApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		List<HsElevatorApply> list = hsElevatorApplyService.findAuditByTask(hsElevatorApply);
		String fileName = "加装电梯补助申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("加装电梯补助申请", HsElevatorApply.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	
}