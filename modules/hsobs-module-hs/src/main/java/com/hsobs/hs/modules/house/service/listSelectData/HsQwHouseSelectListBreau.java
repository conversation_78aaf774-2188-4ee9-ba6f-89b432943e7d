package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.mybatis.mapper.query.QueryType;

import org.springframework.stereotype.Service;

/**
 * 局直公房，配租房源：选择待配租、没有在流程中的房源3，进行配租
 */
@Service
public class HsQwHouseSelectListBreau implements HsQwHouseSelectList {
    public String getDataType() {
        return "4";
    }

    public Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse,
            HsQwPublicRentalHouseService hsQwPublicRentalHouseService) {
        hsQwPublicRentalHouse.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_BUREAU);
        hsQwPublicRentalHouse.setHouseStatus("0");
        hsQwPublicRentalHouse.setIsPublic("1");
        hsQwPublicRentalHouse.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwPublicRentalHouse.sqlMap().add("extForm",
                " LEFT JOIN (SELECT a.id, a.HOUSE_ID,a.STATUS FROM hs_qw_apply_bureau a WHERE a.STATUS IN (0,4)) hqb ON "
                        + "a.id = hqb.house_id ");
        hsQwPublicRentalHouse.sqlMap().getWhere().andBracket("hqb.status", QueryType.NOT_IN, new String[] { "0", "4" })
                .or("hqb.id", QueryType.IS_NULL, null).endBracket();
        return hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
    }
}
