package com.hsobs.hs.modules.apply.service.applyedDataList;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.springframework.stereotype.Service;

/**
 * 现场核验-入户核验
 */
@Service
public class HsQwApplyedListCheckIn implements HsQwApplyedList{

    @Override
    public String getDataType() {
        return "4";
    }

    @Override
    public Page<HsQwApply> execute(HsQwApply hsQwApply,  HsQwApplyService hsQwApplyService) {
        hsQwApply.setStatus(HsQwApply.STATUS_NORMAL);
        hsQwApply.sqlMap().add("extForm",
                " LEFT JOIN hs_qw_management_check hqmc ON " +
                        "a.id = hqmc.apply_id and hqmc.status = '4' and hqmc.check_type = '0'");
        hsQwApply.sqlMap().getWhere().andBracket("hqmc.status", QueryType.NE, "4")
                .or("hqmc.id", QueryType.IS_NULL, null).endBracket();
        hsQwApply.sqlMap().getWhere().and("a.apply_matter", QueryType.IN, new String[]{"0","1","2","4"});
        return hsQwApplyService.findPage(hsQwApply);
    }
}
