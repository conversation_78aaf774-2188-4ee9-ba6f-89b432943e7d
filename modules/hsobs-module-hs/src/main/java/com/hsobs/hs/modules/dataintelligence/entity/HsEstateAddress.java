package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.Office;

@Table(name="hs_qw_public_rental_estate", alias="a", label="公租房房源楼盘信息表信息", columns={
        @Column(name="id", attrName="id", label="编号", isPK=true),
        @Column(name="name", attrName="name", label="楼盘名称", queryType=QueryType.LIKE),
        @Column(includeEntity=DataEntity.class),
        @Column(name="longitude", attrName="longitude", label="经度", isQuery=false),
        @Column(name="latitude", attrName="latitude", label="纬度", isQuery=false),
        @Column(name="address", attrName="address", label="楼盘地址", queryType=QueryType.LIKE),

}, orderBy="a.update_date DESC"
)
public class HsEstateAddress extends DataEntity<HsEstateAddress> {

    private String id;
    private String name;		// 地址名称
    private String address;		// 详细地址
    private String latitude;		// 纬度
    private String longitude;		// 经度

    private Double latitude_d;		// 纬度
    private Double longitude_d;		// 经度

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude_d() {
        return latitude_d;
    }

    public void setLatitude_d(Double latitude_d) {
        this.latitude_d = latitude_d;
    }

    public Double getLongitude_d() {
        return longitude_d;
    }

    public void setLongitude_d(Double longitude_d) {
        this.longitude_d = longitude_d;
    }
}
