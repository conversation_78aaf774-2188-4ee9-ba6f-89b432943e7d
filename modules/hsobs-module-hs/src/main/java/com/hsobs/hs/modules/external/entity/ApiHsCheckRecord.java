package com.hsobs.hs.modules.external.entity;

import io.swagger.annotations.Api;

import javax.validation.constraints.NotBlank;

public class ApiHsCheckRecord extends ApiBody {

    private String sjh;//	是	手机号

    private String hybh;//核验编号
//    @NotBlank(message = "核验类型不为空！")
    @HsMapTo("checkType")
    private String hylx;//	是	核验类型（0入户核验 1退租核验）
    @HsMapTo("hsQwApply.hsQwApplyHouse.estate.name")
    private String lpmc;//	否	楼盘名称
    @HsMapTo("hsQwApplyHouse.unitNum")
    private String dyh;//	否	单元号

    private String fybh;//	住房编号（唯一）
    private String lpdz;//	楼盘地址
    private String lh;//	楼号
    private String jzmj;//	建筑面积
    private String fwlx; //房屋类型

    private String hyzt;// 核验状态（0：已完成 1：待审核）
    private String hylc;//核验流程（0物业发起入住核验 1待承租人入住核验 2待物业审核入住核验 3承租人发起退租核验 4待物业退租核验 5待承租人退租核验）

    public String getHybh() {
        return hybh;
    }

    public void setHybh(String hybh) {
        this.hybh = hybh;
    }

    public String getHyzt() {
        return hyzt;
    }

    public void setHyzt(String hyzt) {
        this.hyzt = hyzt;
    }

    public String getHylc() {
        return hylc;
    }

    public void setHylc(String hylc) {
        this.hylc = hylc;
    }

    public String getSjh() {
        return sjh;
    }

    public void setSjh(String sjh) {
        this.sjh = sjh;
    }

    public String getHylx() {
        return hylx;
    }

    public void setHylx(String hylx) {
        this.hylx = hylx;
    }

    public String getLpmc() {
        return lpmc;
    }

    public void setLpmc(String lpmc) {
        this.lpmc = lpmc;
    }

    public String getDyh() {
        return dyh;
    }

    public void setDyh(String dyh) {
        this.dyh = dyh;
    }

    public String getFybh() {
        return fybh;
    }

    public void setFybh(String fybh) {
        this.fybh = fybh;
    }

    public String getLpdz() {
        return lpdz;
    }

    public void setLpdz(String lpdz) {
        this.lpdz = lpdz;
    }

    public String getLh() {
        return lh;
    }

    public void setLh(String lh) {
        this.lh = lh;
    }

    public String getJzmj() {
        return jzmj;
    }

    public void setJzmj(String jzmj) {
        this.jzmj = jzmj;
    }

    public String getFwlx() {
        return fwlx;
    }

    public void setFwlx(String fwlx) {
        this.fwlx = fwlx;
    }
}
