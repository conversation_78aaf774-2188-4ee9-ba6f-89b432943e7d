package com.hsobs.hs.modules.checkrecord.service;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.checkrecord.entity.HsQwCheckRecord;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.hsobs.hs.modules.clearance.service.HsQwClearanceService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 申请资格审核-已租赁的审核不符合租赁资格，需要执行退租
 */
@Component
public class HsQwApplyCheckCompactServiceTask implements JavaDelegate {
    @Autowired
    private HsQwApplyService hsQwApplyService;
    @Autowired
    private HsQwCheckRecordService hsQwCheckRecordService;
    @Autowired
    private HsQwClearanceService hsQwClearanceService;

    @Override
    public void execute(DelegateExecution delegateExecution) {
        HsQwCheckRecord hsQwCheckRecord = hsQwCheckRecordService.get(BpmUtils.getBizKey(delegateExecution));
        HsQwApply hsQwApplyOld = hsQwApplyService.get(hsQwCheckRecord.getApplyId());
        this.clearanceApply(hsQwApplyOld);
    }

    private void clearanceApply(HsQwApply hsQwApply){
        HsQwClearance hsQwClearance = new HsQwClearance();
        hsQwClearance.setApplyId(hsQwApply.getId());
        hsQwClearance.setCompactId(hsQwApply.getCompact().getId());
        hsQwClearance.setApplyerId(hsQwApply.getMainApplyer().getId());
        hsQwClearance.setType("4");//核查清退
        hsQwClearance.setReason("个人申请资格核查执行清退");
        hsQwClearance.setBlackUser("0");
        hsQwClearanceService.save(hsQwClearance);
//        BpmUtils.start(hsQwClearance, "rent_clear", null, null);
    }
}
