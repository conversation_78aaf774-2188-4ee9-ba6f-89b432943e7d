package com.hsobs.hs.modules.talent.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFunds;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionConf;
import com.hsobs.hs.modules.talent.service.HsTalentIntroductionConfService;

/**
 * 人才引进补助配置表Controller
 * <AUTHOR>
 * @version 2025-01-03
 */
@Controller
@RequestMapping(value = "${adminPath}/talent/hsTalentIntroductionConf")
public class HsTalentIntroductionConfController extends BaseController {

	@Autowired
	private HsTalentIntroductionConfService hsTalentIntroductionConfService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsTalentIntroductionConf get(String id, boolean isNewRecord) {
		return hsTalentIntroductionConfService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("talent:hsTalentIntroductionConf:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsTalentIntroductionConf hsTalentIntroductionConf, Model model) {
		model.addAttribute("hsTalentIntroductionConf", hsTalentIntroductionConf);
		return "modules/talent/hsTalentIntroductionConfList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionConf:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsTalentIntroductionConf> listData(HsTalentIntroductionConf hsTalentIntroductionConf, HttpServletRequest request, HttpServletResponse response) {
		hsTalentIntroductionConf.setPage(new Page<>(request, response));
		Page<HsTalentIntroductionConf> page = hsTalentIntroductionConfService.findPage(hsTalentIntroductionConf);
		return page;
	}

	@RequiresPermissions("talent:hsTalentIntroductionConf:view")
	@RequestMapping(value = "singleData")
	@ResponseBody
	public HsTalentIntroductionConf singleData(HsTalentIntroductionConf hsTalentIntroductionConf, HttpServletRequest request, HttpServletResponse response) {
		if (hsTalentIntroductionConf.getId() == null || "".equals(hsTalentIntroductionConf.getId())) {
			return null;
		} else {
			return hsTalentIntroductionConfService.get(hsTalentIntroductionConf.getId());
		}
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("talent:hsTalentIntroductionConf:view")
	@RequestMapping(value = "form")
	public String form(HsTalentIntroductionConf hsTalentIntroductionConf, Model model) {
		model.addAttribute("hsTalentIntroductionConf", hsTalentIntroductionConf);
		return "modules/talent/hsTalentIntroductionConfForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionConf:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsTalentIntroductionConf hsTalentIntroductionConf) {
		String code = hsTalentIntroductionConf.getIsNewRecord() ? "新增人才引进补助配置成功！" : "修改人才引进补助配置成功！";
		hsTalentIntroductionConfService.save(hsTalentIntroductionConf);
		return renderResult(Global.TRUE, text(code));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionConf:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsTalentIntroductionConf hsTalentIntroductionConf) {
		hsTalentIntroductionConf.setStatus(HsTalentIntroductionConf.STATUS_DISABLE);
		hsTalentIntroductionConfService.updateStatus(hsTalentIntroductionConf);
		return renderResult(Global.TRUE, text("停用人才引进补助配置表成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionConf:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsTalentIntroductionConf hsTalentIntroductionConf) {
		hsTalentIntroductionConf.setStatus(HsTalentIntroductionConf.STATUS_NORMAL);
		hsTalentIntroductionConfService.updateStatus(hsTalentIntroductionConf);
		return renderResult(Global.TRUE, text("启用人才引进补助配置表成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionConf:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsTalentIntroductionConf hsTalentIntroductionConf) {
		hsTalentIntroductionConfService.delete(hsTalentIntroductionConf);
		return renderResult(Global.TRUE, text("删除人才引进补助配置表成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionConf:edit")
	@RequestMapping(value = "exportData")
	public void exportData(HsTalentIntroductionConf hsTalentIntroductionConf, HttpServletResponse response) {
		List<HsTalentIntroductionConf> list = hsTalentIntroductionConfService.findList(hsTalentIntroductionConf);
		String fileName = "人才补助标准" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("人才补助标准", HsTalentIntroductionConf.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	
}