package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * 用户数据同步请求体实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSyncData implements java.io.Serializable {

    private static final long serialVersionUID = -1L;

    private String userId;
    private String name;
    private String account;
    private String mobil;
    private String orgId;
    private Integer status;
    private Integer type;
    private Date createTime;
    private Date updateTime;
    private Integer changeType;
    private String certType;

}
