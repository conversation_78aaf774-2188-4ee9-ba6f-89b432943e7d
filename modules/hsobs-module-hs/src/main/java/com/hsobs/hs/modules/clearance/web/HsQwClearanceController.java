package com.hsobs.hs.modules.clearance.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.hsobs.hs.modules.clearance.service.HsQwClearanceService;

/**
 * 租赁资格轮候清退申请Controller
 * <AUTHOR>
 * @version 2024-12-24
 */
@Controller
@RequestMapping(value = "${adminPath}/clearance/hsQwClearance")
public class HsQwClearanceController extends BaseController {

	@Autowired
	private HsQwClearanceService hsQwClearanceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute("get")
	public HsQwClearance get(String id, boolean isNewRecord) {
		return hsQwClearanceService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwClearance hsQwClearance, Model model) {
		model.addAttribute("hsQwClearance", hsQwClearance);
		return "modules/clearance/hsQwClearanceList";
	}

	/**
	 * 查询列表-待办
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = {"listAudit", ""})
	public String listAuditedlistAudit(@ModelAttribute("get") HsQwClearance hsQwClearance, Model model) {
		model.addAttribute("hsQwClearance", hsQwClearance);
		return "modules/clearance/hsQwClearanceListAudit";
	}

	/**
	 * 查询列表-已处理
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = {"listAudited", ""})
	public String listAudited(@ModelAttribute("get") HsQwClearance hsQwClearance, Model model) {
		model.addAttribute("hsQwClearance", hsQwClearance);
		return "modules/clearance/hsQwClearanceListAudited";
	}
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwClearance> listData(HsQwClearance hsQwClearance, HttpServletRequest request, HttpServletResponse response) {
		hsQwClearance.setPage(new Page<>(request, response));
		Page<HsQwClearance> page = hsQwClearanceService.findPage(hsQwClearance);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = "listAuditData")
	@ResponseBody
	public Page<HsQwClearance> listAuditData(HsQwClearance hsQwClearance, HttpServletRequest request, HttpServletResponse response) {
		hsQwClearance.setPage(new Page<>(request, response));
		Page<HsQwClearance> page = hsQwClearanceService.findPageByTask(hsQwClearance, null, "1");
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = "listAuditedData")
	@ResponseBody
	public Page<HsQwClearance> listAuditedData(HsQwClearance hsQwClearance, HttpServletRequest request, HttpServletResponse response) {
		hsQwClearance.setPage(new Page<>(request, response));
		Page<HsQwClearance> page = hsQwClearanceService.findPageByTask(hsQwClearance, null, "2");
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = "form")
	public String form(@ModelAttribute("get") HsQwClearance hsQwClearance, Model model) {
		model.addAttribute("hsQwClearance", hsQwClearance);
		return "modules/clearance/hsQwClearanceForm";
	}
	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = "formRead")
	public String formRead(@ModelAttribute("get") HsQwClearance hsQwClearance, Model model) {
		model.addAttribute("hsQwClearance", hsQwClearance);
		return "modules/clearance/hsQwClearanceFormRead";
	}
	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = "formImpl")
	public String formImpl(@ModelAttribute("get") HsQwClearance hsQwClearance, Model model) {
		model.addAttribute("hsQwClearance", hsQwClearance);
		return "modules/clearance/hsQwClearanceFormImpl";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("clearance:hsQwClearance:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@ModelAttribute("get")  @Validated HsQwClearance hsQwClearance) {
		hsQwClearanceService.save(hsQwClearance);
		return renderResult(Global.TRUE, text("保存租赁资格轮候清退申请成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("clearance:hsQwClearance:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(@ModelAttribute("get") HsQwClearance hsQwClearance) {
		if (!HsQwClearance.STATUS_DRAFT.equals(hsQwClearance.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		hsQwClearanceService.delete(hsQwClearance);
		return renderResult(Global.TRUE, text("删除租赁资格轮候清退申请成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("clearance:hsQwClearance:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwClearance hsQwClearance, HttpServletResponse response) {
		List<HsQwClearance> list = hsQwClearanceService.findList(hsQwClearance);
		String fileName = "租赁资格轮候清退申请" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("租赁资格轮候清退申请", HsQwClearance.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}


//	/**
//	 * 选择员工对话框
//	 */
//	@RequiresPermissions("apply:hsQwApply:edit")
//	@RequestMapping(value = "applySelect")
//	public String applySelect(HsQwPublicRentalHouse hsQwPublicRentalHouse, String selectData, Model model) {
//		String selectDataJson = EncodeUtils.decodeUrl(selectData);
//		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
//			model.addAttribute("selectData", selectDataJson);
//		}
//		model.addAttribute("applyedIdStr", hsQwApplyService.getHasApplyIdStr());
//		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
//		return "modules/apply/hsQwApplyListClearanceSelect";
//	}
}