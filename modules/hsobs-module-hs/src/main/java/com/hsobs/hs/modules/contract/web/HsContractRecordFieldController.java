package com.hsobs.hs.modules.contract.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.contract.entity.HsContractRecordField;
import com.hsobs.hs.modules.contract.service.HsContractRecordFieldService;

/**
 * 合同字段Controller
 * <AUTHOR>
 * @version 2025-01-22
 */
@Controller
@RequestMapping(value = "${adminPath}/contract/hsContractRecordField")
public class HsContractRecordFieldController extends BaseController {

	@Autowired
	private HsContractRecordFieldService hsContractRecordFieldService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsContractRecordField get(String id, boolean isNewRecord) {
		return hsContractRecordFieldService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("contract:hsContractRecordField:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsContractRecordField hsContractRecordField, Model model) {
		model.addAttribute("hsContractRecordField", hsContractRecordField);
		return "modules/contract/hsContractRecordFieldList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("contract:hsContractRecordField:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsContractRecordField> listData(HsContractRecordField hsContractRecordField, HttpServletRequest request, HttpServletResponse response) {
		hsContractRecordField.setPage(new Page<>(request, response));
		Page<HsContractRecordField> page = hsContractRecordFieldService.findPage(hsContractRecordField);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("contract:hsContractRecordField:view")
	@RequestMapping(value = "form")
	public String form(HsContractRecordField hsContractRecordField, Model model) {
		model.addAttribute("hsContractRecordField", hsContractRecordField);
		return "modules/contract/hsContractRecordFieldForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("contract:hsContractRecordField:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsContractRecordField hsContractRecordField) {
		hsContractRecordFieldService.save(hsContractRecordField);
		return renderResult(Global.TRUE, text("保存合同字段成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("contract:hsContractRecordField:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsContractRecordField hsContractRecordField) {
		hsContractRecordFieldService.delete(hsContractRecordField);
		return renderResult(Global.TRUE, text("删除合同字段成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("contract:hsContractRecordField:view")
	@RequestMapping(value = "hsContractRecordFieldSelect")
	public String hsContractRecordFieldSelect(HsContractRecordField hsContractRecordField, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsContractRecordField", hsContractRecordField);
		return "modules/contract/hsContractRecordFieldSelect";
	}
	
}