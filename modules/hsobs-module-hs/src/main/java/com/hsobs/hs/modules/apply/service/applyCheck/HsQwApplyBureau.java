package com.hsobs.hs.modules.apply.service.applyCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.service.HsQwApplyerBlackService;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 局直公房申请
 */
@Service
public class HsQwApplyBureau extends HsQwApplyCheckBase implements HsQwApplyCheck {

    @Autowired
    private HsQwApplyDao hsQwApplyDao;

    @Autowired
    private HsQwApplyerBlackService hsQwApplyerBlackService;

    @Override
    public String getApplyType() {
        return "5";//局直公房申请
    }

    @Override
    public boolean execute(HsQwApply hsQwApply) {
        //黑名单校验
        this.blackCheck(hsQwApply);
        //流程中的申请单校验
        if (!super.processCheck(hsQwApply, new String[]{"5"}, new String[]{"4"}, this.getApplyType(), null)){
            throw new ServiceException("已存在流程中的申请单，请先完成再申请！");
        }
        return true;
    }


    /**
     * 黑名单用户校验
     * @param hsQwApply
     */
    public void blackCheck(HsQwApply hsQwApply){
        HsQwApplyerBlack entity = new HsQwApplyerBlack();
        entity.setUserId(hsQwApply.getMainApplyer().getUserId());
        entity.sqlMap().getWhere().and("status", QueryType.EQ, "0");
        entity.setEndTime_gte(new Date());
        long count = hsQwApplyerBlackService.findCount(entity);
        if (count > 0) {
            throw new ServiceException("申请资格验证失败，条件不满足！");
        }
    }

    /**
     * 过程中的申请
     * @param hsQwApply
     * @param applyMatters
     */
    public boolean processCheck(HsQwApply hsQwApply, String[] applyMatters, String[] status, String allowMatter){
        //申请单校验
        HsQwApply entity = new HsQwApply();
        entity.sqlMap().getWhere().disableAutoAddStatusWhere();
        entity.setMainApplyer(new HsQwApplyer());
        entity.getMainApplyer().setUserId(hsQwApply.getMainApplyer().getUserId());
        //是否存在信息变更和房屋置换变更申请单判定
        entity.sqlMap().getWhere().and("apply_matter", QueryType.IN, applyMatters);
        entity.setStatus_in(status);
        if (!StringUtils.isBlank(hsQwApply.getId())){
            //校验去除自身的id
            entity.sqlMap().getWhere().and("id", QueryType.NE, hsQwApply.getId());
        }
        List<HsQwApply> list = hsQwApplyDao.findList(entity);
        if (list.size() >= 1) {
            return false;
        }
        return true;
    }
}
