package com.hsobs.hs.modules.checkrule.service.checkRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;
import org.springframework.stereotype.Service;

/**
 * 离异前购买政策性住房
 * 申请人曾经离异，以前配偶名义购买过政策性住房；或与前配偶共有的非政策性住房在离异时分割给前配偶，但离异时间不足3年的。
 */
@Service
public class CheckRuleHousedYear implements ICheckRule {
    @Override
    public CheckRuleResult execute(HsQwApply hsQwApply, HsQwCheckRule rule) {
        return CheckRuleResult.success("成功");
    }
}
