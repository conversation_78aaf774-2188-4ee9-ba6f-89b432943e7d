package com.hsobs.hs.modules.apply.service.applyCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 房屋申请信息变更申请校验
 */
@Component
public class HsQwApplyCheckInfo extends HsQwApplyCheckBase implements HsQwApplyCheck {

    @Autowired
    private HsQwApplyDao hsQwApplyDao;

    @Override
    public String getApplyType() {
        return "1";//申请信息变更
    }

    @Override
    public void execute(HsQwApply hsQwApply) {

        //黑名单校验
        super.blackCheck(hsQwApply);
        //流程中的申请单校验
        if (!super.processCheck(hsQwApply, new String[]{"1","2","4"}, new String[]{"4"}, this.getApplyType(), null)){
            throw new ServiceException("已存在流程中的申请单，请先完成再申请！");
        }
        //流程中的必须有正常状态的申请单
        if (!super.normalCheck(new String[]{"0", "4"}, hsQwApply)){
            throw new ServiceException("申请住房信息变更资格验证失败，不存在在租或者在等的申请单！");
        }
    }

}
