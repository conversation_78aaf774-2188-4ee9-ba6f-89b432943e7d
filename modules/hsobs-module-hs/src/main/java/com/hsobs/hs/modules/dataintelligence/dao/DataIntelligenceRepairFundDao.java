package com.hsobs.hs.modules.dataintelligence.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceRepairFund;

import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计DAO接口  维修资金情况统计
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataIntelligenceRepairFundDao extends CrudDao<DataIntelligenceRepairFund> {

    // String otherWhere, String sqlOrderBy
    List<Map<String, Object>> countRepairFund(Map<String, Object> map);

    List<Map<String, Object>> countRepairFund(String otherWhere, String sqlOrderBy);

    // 总体统计
    List<Map<String, Object>> countRepairFundTotal(String otherWhere);

    List<Map<String, Object>> countRepairFundArea(String otherWhere, String sqlOrderBy);
    List<Map<String, Object>> countRepairFundArea(Map<String, Object> map);

    // String otherWhere, String sqlOrderBy
    List<Map<String, Object>> countRepairFundOffice(Map<String, Object> map);
}