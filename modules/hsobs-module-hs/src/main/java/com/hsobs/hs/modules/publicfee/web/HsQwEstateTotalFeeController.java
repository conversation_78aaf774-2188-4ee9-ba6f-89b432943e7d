package com.hsobs.hs.modules.publicfee.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.publicfee.entity.HsQwEstateTotalFee;
import com.hsobs.hs.modules.publicfee.service.HsQwEstateTotalFeeService;

/**
 * 公摊物业费用表Controller
 * <AUTHOR>
 * @version 2025-01-21
 */
@Controller
@RequestMapping(value = "${adminPath}/publicfee/hsQwEstateTotalFee")
public class HsQwEstateTotalFeeController extends BaseController {

	@Autowired
	private HsQwEstateTotalFeeService hsQwEstateTotalFeeService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwEstateTotalFee get(String id, boolean isNewRecord) {
		return hsQwEstateTotalFeeService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("publicfee:hsQwEstateTotalFee:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwEstateTotalFee hsQwEstateTotalFee, Model model) {
		model.addAttribute("hsQwEstateTotalFee", hsQwEstateTotalFee);
		return "modules/publicfee/hsQwEstateTotalFeeList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("publicfee:hsQwEstateTotalFee:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwEstateTotalFee> listData(HsQwEstateTotalFee hsQwEstateTotalFee, HttpServletRequest request, HttpServletResponse response) {
		hsQwEstateTotalFee.setPage(new Page<>(request, response));
		Page<HsQwEstateTotalFee> page = hsQwEstateTotalFeeService.findPage(hsQwEstateTotalFee);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("publicfee:hsQwEstateTotalFee:view")
	@RequestMapping(value = "form")
	public String form(HsQwEstateTotalFee hsQwEstateTotalFee, Model model) {
		model.addAttribute("hsQwEstateTotalFee", hsQwEstateTotalFee);
		return "modules/publicfee/hsQwEstateTotalFeeForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("publicfee:hsQwEstateTotalFee:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwEstateTotalFee hsQwEstateTotalFee) {
		hsQwEstateTotalFeeService.save(hsQwEstateTotalFee);
		return renderResult(Global.TRUE, text("保存公摊物业费用表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("publicfee:hsQwEstateTotalFee:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwEstateTotalFee hsQwEstateTotalFee) {
		hsQwEstateTotalFeeService.delete(hsQwEstateTotalFee);
		return renderResult(Global.TRUE, text("删除公摊物业费用表成功！"));
	}
	
}