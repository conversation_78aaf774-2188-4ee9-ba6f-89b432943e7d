package com.hsobs.hs.modules.hsqwapplybureaucheck.entity;

import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 公房房源智能核验Entity
 * <AUTHOR>
 * @version 2025-05-03
 */
@Table(name="hs_qw_apply_bureau_check", alias="a", label="公房房源智能核验信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="check_date", attrName="checkDate", label="核验时间", isUpdateForce=true),
		@Column(name="bureau_id", attrName="bureauId", label="局直公房申请单ID", isQuery=false),
		@Column(name="check_result", attrName="checkResult", label="核验结果", comment="核验结果（0正常 1异常 ）"),
		@Column(name="check_type", attrName="checkType", label="状态", comment="状态（0厅局公房 1省直公房）", isQuery=false),
		@Column(name="noticed", attrName="noticed", label="通知状态", comment="通知状态（0未通知 1已通知 ）", isQuery=false),
		@Column(name="property_info", attrName="propertyInfo", label="不动产信息", isQuery=false),
		@Column(name="verify_detail", attrName="verifyDetail", label="核验详情", isQuery=false),
		@Column(name="review_status", attrName="reviewStatus", label="复核状态", comment="复核状态（0未复核 1已复核 2复核通过 3复核不通过）", isQuery=true),
		@Column(name="review_user", attrName="reviewUser", label="复核人", isQuery=false),
		@Column(name="review_date", attrName="reviewDate", label="复核时间", isQuery=false),
		@Column(name="review_remark", attrName="reviewRemark", label="复核备注", isQuery=false),
		@Column(name="clearance_status", attrName="clearanceStatus", label="清退状态", comment="清退状态（0未清退 1已通知 2已清退）", isQuery=true),
		@Column(name="clearance_date", attrName="clearanceDate", label="应清退时间", isQuery=false),
		@Column(name="actual_clearance_date", attrName="actualClearanceDate", label="实际清退时间", isQuery=false),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyBureau.class, alias = "h",
                on = "a.bureau_id = h.id", attrName = "bureau",
                columns = {@Column(includeEntity = HsQwApplyBureau.class)}),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "ha",
                on = "ha.apply_id = a.bureau_id and ha.apply_role = 0 and ha.status=0", attrName = "mainApplyer",
                columns = {@Column(includeEntity = HsQwApplyer.class)}),
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyBureauCheck extends DataEntity<HsQwApplyBureauCheck> {

	private static final long serialVersionUID = 1L;
	private Date checkDate;		// 核验时间
	private String bureauId;		// 局直公房申请单ID
	private String checkResult;		// 核验结果（0正常 1异常 ）
	private String checkType;		// 状态（0厅局公房 1省直公房）
	private String noticed;		// 通知状态（0未通知 1已通知 ）
	private String propertyInfo;	// 不动产信息
	private String verifyDetail;	// 核验详情
	private String reviewStatus;	// 复核状态（0未复核 1已复核 2复核通过 3复核不通过）
	private String reviewUser;		// 复核人
	private Date reviewDate;		// 复核时间
	private String reviewRemark;	// 复核备注
	private String clearanceStatus;	// 清退状态（0未清退 1已通知 2已清退）
	private Date clearanceDate;	// 应清退时间
	private Date actualClearanceDate;	// 实际清退时间
	private HsQwApplyBureau bureau;
	private HsQwApplyer mainApplyer;


	public HsQwApplyer getMainApplyer() {
		return mainApplyer;
	}

	public void setMainApplyer(HsQwApplyer mainApplyer) {
		this.mainApplyer = mainApplyer;
	}

	public HsQwApplyBureau getBureau() {
		return bureau;
	}

	public void setBureau(HsQwApplyBureau bureau) {
		this.bureau = bureau;
	}

	

	@ExcelFields({
		@ExcelField(title="id", attrName="id", align=Align.CENTER, sort=10),
		@ExcelField(title="核验时间", attrName="checkDate", align=Align.CENTER, sort=20, dataFormat="yyyy-MM-dd hh:mm"),
		@ExcelField(title="局直公房申请单ID", attrName="bureauId", align=Align.CENTER, sort=30),
		@ExcelField(title="核验结果", attrName="checkResult", dictType = "hs_qw_apply_check_result",  align=Align.CENTER, sort=40),
		@ExcelField(title="核验类型", attrName="checkType",dictType = "hs_qw_apply_check_type", align=Align.CENTER, sort=50),
		@ExcelField(title="通知状态", attrName="noticed", dictType = "hs_qw_apply_check_noticed", align=Align.CENTER, sort=60),
		@ExcelField(title="复核状态", attrName="reviewStatus", dictType = "hs_qw_apply_check_review_status", align=Align.CENTER, sort=70),
		@ExcelField(title="复核人", attrName="reviewUser", align=Align.CENTER, sort=80),
		@ExcelField(title="复核时间", attrName="reviewDate", align=Align.CENTER, sort=90, dataFormat="yyyy-MM-dd hh:mm"),
		@ExcelField(title="应清退时间", attrName="clearanceDate", align=Align.CENTER, sort=110, dataFormat="yyyy-MM-dd"),
		@ExcelField(title="备注", attrName="remarks", align=Align.CENTER, sort=120),
	})
	public HsQwApplyBureauCheck() {
		this(null);
	}

	public HsQwApplyBureauCheck(String id){
		super(id);
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	@NotBlank(message="局直公房申请单ID不能为空")
	@Size(min=0, max=64, message="局直公房申请单ID长度不能超过 64 个字符")
	public String getBureauId() {
		return bureauId;
	}

	public void setBureauId(String bureauId) {
		this.bureauId = bureauId;
	}

	@NotBlank(message="核验结果不能为空")
	@Size(min=0, max=1, message="核验结果长度不能超过 1 个字符")
	public String getCheckResult() {
		return checkResult;
	}

	public void setCheckResult(String checkResult) {
		this.checkResult = checkResult;
	}

	@NotBlank(message="状态不能为空")
	@Size(min=0, max=1, message="状态长度不能超过 1 个字符")
	public String getCheckType() {
		return checkType;
	}

	public void setCheckType(String checkType) {
		this.checkType = checkType;
	}

	@NotBlank(message="通知状态不能为空")
	@Size(min=0, max=1, message="通知状态长度不能超过 1 个字符")
	public String getNoticed() {
		return noticed;
	}

	public void setNoticed(String noticed) {
		this.noticed = noticed;
	}

	public String getPropertyInfo() {
		return propertyInfo;
	}

	public void setPropertyInfo(String propertyInfo) {
		this.propertyInfo = propertyInfo;
	}

	public String getVerifyDetail() {
		return verifyDetail;
	}

	public void setVerifyDetail(String verifyDetail) {
		this.verifyDetail = verifyDetail;
	}

	public String getReviewStatus() {
		return reviewStatus;
	}

	public void setReviewStatus(String reviewStatus) {
		this.reviewStatus = reviewStatus;
	}

	public String getReviewUser() {
		return reviewUser;
	}

	public void setReviewUser(String reviewUser) {
		this.reviewUser = reviewUser;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getReviewDate() {
		return reviewDate;
	}

	public void setReviewDate(Date reviewDate) {
		this.reviewDate = reviewDate;
	}

	public String getReviewRemark() {
		return reviewRemark;
	}

	public void setReviewRemark(String reviewRemark) {
		this.reviewRemark = reviewRemark;
	}

	public String getClearanceStatus() {
		return clearanceStatus;
	}

	public void setClearanceStatus(String clearanceStatus) {
		this.clearanceStatus = clearanceStatus;
	}

	@JsonFormat(pattern = "yyyy-MM-dd")
	public Date getClearanceDate() {
		return clearanceDate;
	}

	public void setClearanceDate(Date clearanceDate) {
		this.clearanceDate = clearanceDate;
	}

	@JsonFormat(pattern = "yyyy-MM-dd")
	public Date getActualClearanceDate() {
		return actualClearanceDate;
	}

	public void setActualClearanceDate(Date actualClearanceDate) {
		this.actualClearanceDate = actualClearanceDate;
	}

	public Date getCheckDate_gte() {
		return sqlMap.getWhere().getValue("check_date", QueryType.GTE);
	}

	public void setCheckDate_gte(Date checkDate) {
		sqlMap.getWhere().and("check_date", QueryType.GTE, checkDate);
	}

	public Date getCheckDate_lte() {
		return sqlMap.getWhere().getValue("check_date", QueryType.LTE);
	}

	public void setCheckDate_lte(Date checkDate) {
		sqlMap.getWhere().and("check_date", QueryType.LTE, checkDate);
	}

	public Date getReviewDate_gte() {
		return sqlMap.getWhere().getValue("review_date", QueryType.GTE);
	}

	public void setReviewDate_gte(Date reviewDate) {
		sqlMap.getWhere().and("review_date", QueryType.GTE, reviewDate);
	}

	public Date getReviewDate_lte() {
		return sqlMap.getWhere().getValue("review_date", QueryType.LTE);
	}

	public void setReviewDate_lte(Date reviewDate) {
		sqlMap.getWhere().and("review_date", QueryType.LTE, reviewDate);
	}

	public Date getClearanceDate_gte() {
		return sqlMap.getWhere().getValue("clearance_date", QueryType.GTE);
	}

	public void setClearanceDate_gte(Date clearanceDate) {
		sqlMap.getWhere().and("clearance_date", QueryType.GTE, clearanceDate);
	}

	public Date getClearanceDate_lte() {
		return sqlMap.getWhere().getValue("clearance_date", QueryType.LTE);
	}

	public void setClearanceDate_lte(Date clearanceDate) {
		sqlMap.getWhere().and("clearance_date", QueryType.LTE, clearanceDate);
	}

}