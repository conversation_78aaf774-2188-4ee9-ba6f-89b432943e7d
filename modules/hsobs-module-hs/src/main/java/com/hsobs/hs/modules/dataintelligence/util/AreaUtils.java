/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.hsobs.hs.modules.dataintelligence.util;

import com.jeesite.common.cache.CacheUtils;
import com.jeesite.common.utils.SpringUtils;
import com.jeesite.modules.sys.entity.Area;
import com.jeesite.modules.sys.service.AreaService;

import java.util.List;

public class AreaUtils {

    private static final class Static {
        private static final AreaService areaService = SpringUtils.getBean(AreaService.class);
    }


    /**
     * 获得站点列表
     */
    public static List<Area> getList(String areaCode) {

        Area area = new Area();
        area.setParentCode(areaCode);
        List<Area> lstArea = Static.areaService.findList(area);
        return lstArea;
    }
}