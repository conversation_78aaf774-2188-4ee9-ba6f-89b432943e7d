package com.hsobs.hs.modules.dataintelligence.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.dataintelligence.service.DataIntelligenceClearanceService;

import java.util.ArrayList;
import java.util.List;

/**
 * 住房保障数据统计Controller  公租房清退统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Controller
@RequestMapping(value = "${adminPath}/dataintelligenceclearance/")
public class DataIntelligenceClearanceController extends BaseController {

	@Autowired
	private DataIntelligenceClearanceService dataIntelligenceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataIntelligenceClearance get(Integer type, boolean isNewRecord) {
		return dataIntelligenceService.get(type, isNewRecord);
	}

	/**
	 * 公租房清退统计
	 */
	@RequiresPermissions("dataintelligenceclearance::view")
	@RequestMapping(value = {"dataIntelligenceClearance", ""})
	public String dataIntelligenceClearance(DataIntelligenceClearance dataIntelligenceClearance, Model model) {
		model.addAttribute("dataintelligence", dataIntelligenceClearance);
		return "modules/dataintelligence/dataIntelligenceClearance";
	}

	/**
	 * 公租房清退统计
	 */
	@RequiresPermissions("dataintelligenceclearance::view")
	@RequestMapping(value = {"dataIntelligenceClearanceCompare", ""})
	public String dataIntelligenceClearanceCompare(DataIntelligenceClearance dataIntelligenceClearance, Model model) {
		model.addAttribute("dataintelligence", dataIntelligenceClearance);
		return "modules/dataintelligence/dataIntelligenceClearanceCompare";
	}


	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceclearance::view")
	@RequestMapping(value = "countClearanceStat")
	@ResponseBody
	public Page<DataIntelligenceClearance> countClearanceStat(DataIntelligenceClearance dataIntelligenceClearance, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceClearance.setPage(new Page<>(request, response));
		Page<DataIntelligenceClearance> page = dataIntelligenceService.findClearanceStatPage(dataIntelligenceClearance, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceclearance::view")
	@RequestMapping(value = "countClearanceStatEmpty")
	@ResponseBody
	public Page<DataIntelligenceClearance> countClearanceStatEmpty(DataIntelligenceClearance dataIntelligenceClearance, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceClearance.setPage(new Page<>(request, response));
		List<DataIntelligenceClearance> statList = new ArrayList<>();
		Page<DataIntelligenceClearance> page = (Page<DataIntelligenceClearance>) dataIntelligenceClearance.getPage();
		page.setList(statList);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligenceclearance::view")
	@RequestMapping(value = "countClearanceCompare")
	@ResponseBody
	public String countClearanceCompare(DataIntelligenceClearance dataIntelligenceClearance, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.countClearanceCompare(dataIntelligenceClearance);
	}

	@RequiresPermissions("dataintelligenceclearance::view")
	@RequestMapping(value = "countClearanceTypeStat")
	@ResponseBody
	public String countClearanceTypeStat(DataIntelligenceClearance dataIntelligenceClearance, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.countClearanceTypeStat(dataIntelligenceClearance);
	}

	@RequiresPermissions("dataintelligenceclearance::view")
	@RequestMapping(value = "countClearanceTypeTop")
	@ResponseBody
	public String countClearanceTypeTop(DataIntelligenceClearance dataIntelligenceClearance, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.countClearanceTypeTop(dataIntelligenceClearance);
	}


	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligenceclearance::view")
	@RequestMapping(value = "exportData")
	public void exportData(DataIntelligenceClearance dataIntelligenceClearance, HttpServletResponse response) {
		String fileName = "公租房清退统计统计表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("公租房清退统计统计表", DataIntelligenceClearance.class);
			List<DataIntelligenceClearance> list = dataIntelligenceService.countClearanceStat(dataIntelligenceClearance);
			ee.setDataList(list);

			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}
}