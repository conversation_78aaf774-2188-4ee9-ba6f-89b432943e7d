package com.hsobs.hs.modules.pricelimitpublic.entity;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.List;
import com.jeesite.common.collect.ListUtils;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.Office;

/**
 * 限价房网上公示Entity
 * <AUTHOR>
 * @version 2025-03-10
 */
@Table(name="hs_price_limit_public", alias="a", label="限价房网上公示信息", columns={
		@Column(name="id", attrName="id", label="公示id", isPK=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="public_name", attrName="publicName", label="公示名称", queryType=QueryType.LIKE),
		@Column(name="public_info", attrName="publicInfo", label="公示信息", queryType=QueryType.LIKE),
		@Column(name="start_date", attrName="startDate", label="公示开始时间", isUpdateForce=true),
		@Column(name="end_date", attrName="endDate", label="公示结束时间", isUpdateForce=true),
		@Column(name="office_code", attrName="officeCode", label="归属机构"),
		@Column(name="status", attrName="status", label="状态"),
		@Column(name="is_public", attrName="isPublic", label="是否发布"),
	},
		extWhereKeys="dsf",
		orderBy="a.update_date DESC"
)
public class HsPriceLimitPublic extends DataEntity<HsPriceLimitPublic> {

	public static String PUBLIC_STATUS_NORMAL = "0";
	public static String PUBLIC_STATUS_PUBLIC = "1";	// 正在公示
	public static String PUBLIC_STATUS_CANCLE = "2";	// 撤销
	public static String PUBLIC_STATUS_AUDIT = "3";		// 提交

	private static final long serialVersionUID = 1L;
	private String publicName;		// 公示名称
	private String publicInfo;		// 公示信息
	private Date startDate;			// 公示开始时间
	private Date endDate;			// 公示结束时间
	private String officeCode;		// 归属机构
	private String isPublic;
	private Office office;
	private String pids;
	private String submitType;     //提交类型（0进行公示 1批量提交 2批量拒绝）
	private List<HsPriceLimitPublicDetail> hsPriceLimitPublicDetailList = ListUtils.newArrayList();		// 子表列表

	private String commit;

	private String readOnly;

	public HsPriceLimitPublic() {
		this(null);
		readOnly = "false";
	}
	
	public HsPriceLimitPublic(String id){
		super(id);
	}
	
	@Size(min=0, max=200, message="公示名称长度不能超过 200 个字符")
	public String getPublicName() {
		return publicName;
	}

	public void setPublicName(String publicName) {
		this.publicName = publicName;
	}
	
	@Size(min=0, max=500, message="公示信息长度不能超过 500 个字符")
	public String getPublicInfo() {
		return publicInfo;
	}

	public void setPublicInfo(String publicInfo) {
		this.publicInfo = publicInfo;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	@Size(min=0, max=64, message="归属机构长度不能超过 64 个字符")
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getIsPublic() {
		return isPublic;
	}

	public void setIsPublic(String isPublic) {
		this.isPublic = isPublic;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	public String getPids() {
		return pids;
	}

	public void setPids(String pids) {
		this.pids = pids;
	}

	public String getSubmitType() {
		return submitType;
	}

	public void setSubmitType(String submitType) {
		this.submitType = submitType;
	}

	@Valid
	public List<HsPriceLimitPublicDetail> getHsPriceLimitPublicDetailList() {
		return hsPriceLimitPublicDetailList;
	}

	public void setHsPriceLimitPublicDetailList(List<HsPriceLimitPublicDetail> hsPriceLimitPublicDetailList) {
		this.hsPriceLimitPublicDetailList = hsPriceLimitPublicDetailList;
	}

	public String getReadOnly() {
		return readOnly;
	}

	public void setReadOnly(String readOnly) {
		this.readOnly = readOnly;
	}

	public String getCommit() {
		return commit;
	}

	public void setCommit(String commit) {
		this.commit = commit;
	}
}