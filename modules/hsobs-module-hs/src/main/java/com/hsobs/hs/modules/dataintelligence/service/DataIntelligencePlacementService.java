package com.hsobs.hs.modules.dataintelligence.service;

import java.util.*;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTotalDao;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.modules.sys.entity.DictData;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.DictDataService;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligencePlacementDao;

/**
 * 住房保障数据统计Service  公有住房配售统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class DataIntelligencePlacementService extends CrudService<DataIntelligencePlacementDao, DataIntelligencePlacement> {

	@Autowired
	private DataIntelligencePlacementDao dataIntelligencePlacementDao;
	@Autowired
	private DataIntelligenceTotalService dataIntelligenceTotalService;

	/**
	 * 获取单条数据
	 * @param dataIntelligencePlacement
	 * @return
	 */
	@Override
	public DataIntelligencePlacement get(DataIntelligencePlacement dataIntelligencePlacement) {
		return super.get(dataIntelligencePlacement);
	}
	
	/**
	 * 查询分页数据
	 * @param dataIntelligencePlacement 查询条件
	 * @param dataIntelligencePlacement page 分页对象
	 * @return
	 */
	@Override
	public Page<DataIntelligencePlacement> findPage(DataIntelligencePlacement dataIntelligencePlacement) {
		return super.findPage(dataIntelligencePlacement);
	}
	
	/**
	 * 查询列表数据
	 * @param dataIntelligencePlacement
	 * @return
	 */
	@Override
	public List<DataIntelligencePlacement> findList(DataIntelligencePlacement dataIntelligencePlacement) {
		return super.findList(dataIntelligencePlacement);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param dataIntelligencePlacement
	 */
	@Override
	@Transactional
	public void save(DataIntelligencePlacement dataIntelligencePlacement) {
		super.save(dataIntelligencePlacement);
	}
	
	/**
	 * 更新状态
	 * @param dataIntelligencePlacement
	 */
	@Override
	@Transactional
	public void updateStatus(DataIntelligencePlacement dataIntelligencePlacement) {
		super.updateStatus(dataIntelligencePlacement);
	}
	
	/**
	 * 删除数据
	 * @param dataIntelligencePlacement
	 */
	@Override
	@Transactional
	public void delete(DataIntelligencePlacement dataIntelligencePlacement) {
		dataIntelligencePlacement.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataIntelligencePlacement);
	}

	private  String getSqlWhere(DataIntelligencePlacement dataIntelligencePlacement, Date startDate, Date endDate, String officeCode){
		String sqlWhere = "";
		if (startDate != null || dataIntelligencePlacement.getStartDate() != null) {
			Date date = (startDate!=null)?startDate:dataIntelligencePlacement.getStartDate();
			sqlWhere += " AND a.create_date >= '" + String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}
		if (endDate != null || dataIntelligencePlacement.getEndDate() != null) {
			Date date = (endDate!=null)?endDate:dataIntelligencePlacement.getEndDate();
			sqlWhere += " AND a.create_date <= '" + String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}

		if((dataIntelligencePlacement.getEstateId() == null||dataIntelligencePlacement.getEstateId().isEmpty()) && dataIntelligencePlacement.getEstateName() != null && dataIntelligencePlacement.getEstateName().length() > 0) {
			sqlWhere += " AND estate.name = '" + dataIntelligencePlacement.getEstateName() + "'";
		}
		if(dataIntelligencePlacement.getEstateId() != null && dataIntelligencePlacement.getEstateId().length() > 0) {
			sqlWhere += " AND estate.id = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligencePlacement.getEstateId()) + "'";
		}
		if(dataIntelligencePlacement.getCity() != null && !"".equals(dataIntelligencePlacement.getCity())){
			sqlWhere += " AND estate.city = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligencePlacement.getCity()) + "'";
		}
		if(dataIntelligencePlacement.getArea() != null && !"".equals(dataIntelligencePlacement.getArea())){
			sqlWhere += " AND estate.area = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligencePlacement.getArea()) + "'";
		}

		dataIntelligencePlacement.sqlMap().getDataScope().addFilter("dsf", "Office", "jso.office_code", DataScope.CTRL_PERMI_HAVE);
		SqlMap sqlMap = dataIntelligencePlacement.sqlMap();
		Object obWhere = sqlMap.get("dsf");
		if(obWhere != null){
			sqlWhere += obWhere.toString();
		}
		//String officeCode = null;
		if(officeCode == null && dataIntelligencePlacement.getOfficeCode() != null && dataIntelligencePlacement.getOfficeCode().length() > 0) {
			officeCode = dataIntelligencePlacement.getOfficeCode();
		}
		sqlWhere += dataIntelligenceTotalService.getOfficePermission(officeCode);

		if(sqlWhere.length() > 5){
			return "WHERE " + sqlWhere.substring(5);
		}
		return "";
	}
	private  String getSqlAreaTypeWhere(String sqlWhere, Integer roomAreaType) {

		String sqlType = "";
		switch(roomAreaType){
			case 1:// 60平方以下
				sqlType = String.format(" AND house.BUILDING_AREA < 60");
				break;
			case 2:// 60-90平方
				sqlType = String.format(" AND house.BUILDING_AREA >= 60 AND house.BUILDING_AREA < 90");
				break;
			case 3:// 90-120平方
				sqlType = String.format(" AND house.BUILDING_AREA >= 90 AND house.BUILDING_AREA < 120");
				break;
			case 4:// 120平方以上
				sqlType = String.format(" AND house.BUILDING_AREA >= 120");
				break;
		}
		if(sqlWhere.length() > 0){
			return sqlWhere + sqlType;
		}
		else{
			return "WHERE " + sqlType.substring(5);
		}
	}

	public Page<DataIntelligencePlacement> findcountTotalInfoPage(DataIntelligencePlacement dataIntelligencePlacement, String sqlTable, boolean findpage){

		String sqlWhere = getSqlWhere(dataIntelligencePlacement, null, null, null);
		//String sqlOrderBy = (dataIntelligencePlacement.getOrderBy()!=null&&dataIntelligencePlacement.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligencePlacement.getOrderBy()):"";
		String sqlOrderBy = "";

		Page<DataIntelligencePlacement> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlTable", sqlTable);
		mapPara.put("sqlWhere", sqlWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligencePlacement.getPageNo());
			pageMap.setPageSize(dataIntelligencePlacement.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligencePlacementDao.countTotalInfo(mapPara);
		List<DataIntelligencePlacement> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String,Object> map = list.get(j);

			DataIntelligencePlacement placementStat = new DataIntelligencePlacement();
			Object ob = map.get("OFFICE_CODE");
			placementStat.setOfficeCode((ob!=null)?ob.toString():"");
			ob = map.get("OFFICE_NAME");
			placementStat.setOfficeName((ob!=null)?ob.toString():"");

			ob = map.get("APPLY_TOTAL_COUNT");
			placementStat.setApplyTotalCount((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("APPLYING_COUNT");
			placementStat.setApplyingCount((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("APPROVED_COUNT");
			placementStat.setApprovedCount((ob!=null)?Long.valueOf(ob.toString()):0);

			ob = map.get("TOTAL_BUILDING_AREA");
			placementStat.setTotalRoomArea((ob!=null)?Float.valueOf(ob.toString()):0);
			ob = map.get("APPLYING_BUILDING_AREA");
			placementStat.setApplyingRoomArea((ob!=null)?Float.valueOf(ob.toString()):0);
			ob = map.get("APPROVED_BUILDING_AREA");
			placementStat.setApprovedRoomArea((ob!=null)?Float.valueOf(ob.toString()):0);

			ob = map.get("TOTAL_BUILDING_PRICE");
			placementStat.setTotalRoomPrice((ob!=null)?Float.valueOf(ob.toString()):0);
			ob = map.get("APPLYING_BUILDING_PRICE");
			placementStat.setApplyingRoomPrice((ob!=null)?Float.valueOf(ob.toString()):0);
			ob = map.get("APPROVED_BUILDING_PRICE");
			placementStat.setApprovedRoomPrice((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(placementStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

    public List<DataIntelligencePlacement> findcountTotalInfo(DataIntelligencePlacement dataIntelligencePlacement, String sqlTable) {

		return findcountTotalInfoPage(dataIntelligencePlacement, sqlTable, false).getList();
    }

	public List<DataIntelligencePlacement> findcountAreaInfo(DataIntelligencePlacement dataIntelligencePlacement, String sqlTable, String sqlWhere, String sqlOrderBy){

		List<Map<String,Object>> list = dataIntelligencePlacementDao.countAreaInfo(sqlTable, sqlWhere, sqlOrderBy);

		List<DataIntelligencePlacement> statList = new ArrayList<>();
		for (int j = 0; j < list.size(); j++) {
			Map<String,Object> map = list.get(j);

			DataIntelligencePlacement placementStat = new DataIntelligencePlacement();
			Object ob = map.get("ESTATE_ID");
			placementStat.setEstateId((ob!=null)?ob.toString():"");
			ob = map.get("ESTATE_NAME");
			placementStat.setEstateName((ob!=null)?ob.toString():"");

			ob = map.get("APPLY_TOTAL_COUNT");
			placementStat.setApplyTotalCount((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("APPLYING_COUNT");
			placementStat.setApplyingCount((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("APPROVED_COUNT");
			placementStat.setApprovedCount((ob!=null)?Long.valueOf(ob.toString()):0);

			ob = map.get("TOTAL_BUILDING_AREA");
			placementStat.setTotalRoomArea((ob!=null)?Float.valueOf(ob.toString()):0);
			ob = map.get("APPLYING_BUILDING_AREA");
			placementStat.setApplyingRoomArea((ob!=null)?Float.valueOf(ob.toString()):0);
			ob = map.get("APPROVED_BUILDING_AREA");
			placementStat.setApprovedRoomArea((ob!=null)?Float.valueOf(ob.toString()):0);

			ob = map.get("TOTAL_BUILDING_PRICE");
			placementStat.setTotalRoomPrice((ob!=null)?Float.valueOf(ob.toString()):0);
			ob = map.get("APPLYING_BUILDING_PRICE");
			placementStat.setApplyingRoomPrice((ob!=null)?Float.valueOf(ob.toString()):0);
			ob = map.get("APPROVED_BUILDING_PRICE");
			placementStat.setApprovedRoomPrice((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(placementStat);
		}
		return statList;
	}

	public Page<DataIntelligencePlacement> findcountAreaInfoPage(DataIntelligencePlacement dataIntelligencePlacement, String sqlTable) {

		String sqlWhere = getSqlWhere(dataIntelligencePlacement, null, null, null);
		sqlWhere = getSqlAreaTypeWhere(sqlWhere, dataIntelligencePlacement.getRoomAreaType());
		//String sqlOrderBy = (dataIntelligencePlacement.getOrderBy()!=null&&dataIntelligencePlacement.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligencePlacement.getOrderBy()):"";
		String sqlOrderBy = "";

		List<DataIntelligencePlacement> statList = findcountAreaInfo(dataIntelligencePlacement, sqlTable, sqlWhere, sqlOrderBy);

		Page<DataIntelligencePlacement> page = (Page<DataIntelligencePlacement>) dataIntelligencePlacement.getPage();
		page.setList(statList);
		return page;
	}

	public String findcountAreaCompare(DataIntelligencePlacement dataIntelligencePlacement, String sqlTable, Integer areaType) {

		dataIntelligencePlacement.setRoomAreaType(areaType);

		String sqlWhere = getSqlWhere(dataIntelligencePlacement, null, null, null);
		sqlWhere = getSqlAreaTypeWhere(sqlWhere, dataIntelligencePlacement.getRoomAreaType());
		String sqlOrderBy = "";
		List<Map<String,Object>> list = dataIntelligencePlacementDao.countAreaInfo(sqlTable, sqlWhere, sqlOrderBy);

		List<Map<String, Object>> newList = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> unitMap = list.get(i);
			Map<String, Object> map = new HashMap<>();

			Object ob = unitMap.get("APPROVED_BUILDING_AREA");
			if(ob == null || "0".equals(ob.toString()))
				continue;
			map.put("value", ob.toString());
			ob = unitMap.get("ESTATE_NAME");
			map.put("name", (ob!=null)?ob.toString():"");

			newList.add(map);
		}

		String roomType = "";
		switch(dataIntelligencePlacement.getRoomAreaType())
		{
			case 1:roomType="60m²以下";break;
			case 2:roomType="60-90m²";break;
			case 3:roomType="90-120m²";break;
			case 4:roomType="120m²以上";break;
		}

		Map<String, Object> map = new HashMap<>();
		map.put("data", newList);
		if (sqlTable.equals("HS_PRICE_LIMIT_APPLY")) {
			map.put("title", String.format("限价房配售面积(%s)占比", roomType));
		}
		else{
			map.put("title", String.format("公有住房配售面积(%s)占比", roomType));
		}
		return JSON.toJSONString(map);
	}

	public String findcountPriceCompare(DataIntelligencePlacement dataIntelligencePlacement, String sqlTable) {

		String sqlWhere = getSqlWhere(dataIntelligencePlacement, null, null, null);
		String sqlOrderBy = "";
		List<Map<String,Object>> list = dataIntelligencePlacementDao.countAreaInfo(sqlTable, sqlWhere, sqlOrderBy);

		List<Map<String, Object>> newList = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> unitMap = list.get(i);
			Map<String, Object> map = new HashMap<>();

			Object ob = unitMap.get("APPROVED_BUILDING_PRICE");
			if(ob == null || "0".equals(ob.toString()))
				continue;
			map.put("value", ob.toString());
			ob = unitMap.get("ESTATE_NAME");
			map.put("name", (ob!=null)?ob.toString():"");

			newList.add(map);
		}

		Map<String, Object> map = new HashMap<>();
		map.put("data", newList);
		return JSON.toJSONString(map);
	}
}