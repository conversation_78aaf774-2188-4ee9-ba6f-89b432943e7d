package com.hsobs.hs.modules.pricelimitpublic.service;

import java.io.*;
import java.util.*;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applypublic.entity.HsQwApplyPublic;
import com.hsobs.hs.modules.applypublicdetail.entity.HsQwApplyPublicDetail;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.pricelimitapply.service.HsPriceLimitApplyService;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.hsobs.hs.modules.utils.WordUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.pricelimitpublic.entity.HsPriceLimitPublic;
import com.hsobs.hs.modules.pricelimitpublic.dao.HsPriceLimitPublicDao;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.hsobs.hs.modules.pricelimitpublic.entity.HsPriceLimitPublicDetail;
import com.hsobs.hs.modules.pricelimitpublic.dao.HsPriceLimitPublicDetailDao;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

/**
 * 限价房网上公示Service
 * <AUTHOR>
 * @version 2025-03-10
 */
@Service
public class HsPriceLimitPublicService extends CrudService<HsPriceLimitPublicDao, HsPriceLimitPublic> {
	
	@Autowired
	private HsPriceLimitPublicDetailDao hsPriceLimitPublicDetailDao;
    @Autowired
    private HsPriceLimitApplyService hsPriceLimitApplyService;
	@Autowired
	private ApiSzkjService apiSzkjService;
	@Autowired
	private OfficeService officeService;

	@Autowired
	ResourceLoader resourceLoader;

	/**
	 * 获取单条数据
	 * @param hsPriceLimitPublic
	 * @return
	 */
	@Override
	public HsPriceLimitPublic get(HsPriceLimitPublic hsPriceLimitPublic) {
		HsPriceLimitPublic entity = super.get(hsPriceLimitPublic);
		if (entity != null){
			Office office = new Office();
			office.setOfficeCode(entity.getOfficeCode());
			entity.setOffice(officeService.get(office));

			HsPriceLimitPublicDetail hsPriceLimitPublicDetail = new HsPriceLimitPublicDetail(entity.getId());
			hsPriceLimitPublicDetail.setStatus(HsPriceLimitPublicDetail.STATUS_NORMAL);
			entity.setHsPriceLimitPublicDetailList(hsPriceLimitPublicDetailDao.findList(hsPriceLimitPublicDetail));

			if(entity.getIsNewRecord() || entity.getStatus() == null || entity.getStatus().equals("0"))
				entity.setReadOnly("false");
			else
				entity.setReadOnly("true");

			Date currentDate = new Date();
			if(entity.getEndDate() != null && currentDate.after(entity.getEndDate())){
			//if(entity.getEndDate() != null && currentDate.after(entity.getEndDate()) && entity.getIsPublic().equals("1")){
				entity.setCommit("1");
			}
			else
				entity.setCommit("0");
		}
		return entity;
	}
	
	/**
	 * 查询分页数据
	 * @param hsPriceLimitPublic 查询条件
	 * @param hsPriceLimitPublic page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPriceLimitPublic> findPage(HsPriceLimitPublic hsPriceLimitPublic) {
		hsPriceLimitPublic.sqlMap().getWhere().disableAutoAddStatusWhere();
		if(hsPriceLimitPublic.getStartDate() != null){

			hsPriceLimitPublic.sqlMap().getWhere().and("start_date", QueryType.GTE, hsPriceLimitPublic.getStartDate());
			hsPriceLimitPublic.setStartDate(null);
		}
		if(hsPriceLimitPublic.getEndDate() != null){
			hsPriceLimitPublic.sqlMap().getWhere().and("end_date", QueryType.LTE, hsPriceLimitPublic.getEndDate());
			hsPriceLimitPublic.setEndDate(null);
		}
		return super.findPage(hsPriceLimitPublic);
	}

	/**
	 * 查询分页数据
	 * @param hsPriceLimitPublic 查询条件
	 * @param hsPriceLimitPublic page 分页对象
	 * @return
	 */
	public Page<HsPriceLimitPublic> findOwnerPage(HsPriceLimitPublic hsPriceLimitPublic) {
		hsPriceLimitPublic.sqlMap().getWhere().disableAutoAddStatusWhere();

		String user = EmpUtils.getEmployee().getEmpCode();
		if(user != null && !"".equals(user)){
			hsPriceLimitPublic.sqlMap().getWhere().and("create_by", QueryType.EQ, user);
		}

		if(hsPriceLimitPublic.getStartDate() != null){

			hsPriceLimitPublic.sqlMap().getWhere().and("start_date", QueryType.GTE, hsPriceLimitPublic.getStartDate());
			hsPriceLimitPublic.setStartDate(null);
		}
		if(hsPriceLimitPublic.getEndDate() != null){
			hsPriceLimitPublic.sqlMap().getWhere().and("end_date", QueryType.LTE, hsPriceLimitPublic.getEndDate());
			hsPriceLimitPublic.setEndDate(null);
		}
		return super.findPage(hsPriceLimitPublic);
	}
	
	/**
	 * 查询列表数据
	 * @param hsPriceLimitPublic
	 * @return
	 */
	@Override
	public List<HsPriceLimitPublic> findList(HsPriceLimitPublic hsPriceLimitPublic) {
		hsPriceLimitPublic.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findList(hsPriceLimitPublic);
	}
	
	/**
	 * 查询子表分页数据
	 * @param hsPriceLimitPublicDetail
	 * @param hsPriceLimitPublicDetail page 分页对象
	 * @return
	 */
	public Page<HsPriceLimitPublicDetail> findSubPage(HsPriceLimitPublicDetail hsPriceLimitPublicDetail) {
		Page<HsPriceLimitPublicDetail> page = hsPriceLimitPublicDetail.getPage();
		page.setList(hsPriceLimitPublicDetailDao.findList(hsPriceLimitPublicDetail));
		return page;
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsPriceLimitPublic
	 */
	@Override
	@Transactional
	public void save(HsPriceLimitPublic hsPriceLimitPublic) {

		switch (hsPriceLimitPublic.getSubmitType()){
			case "0":// 提交公示
				SavePublic(hsPriceLimitPublic);
				break;

			case "1":	// 批量提交
				hsPriceLimitPublic.setStatus(HsPriceLimitPublic.PUBLIC_STATUS_AUDIT);	// 提交
				this.updateStatus(hsPriceLimitPublic);

				List<HsPriceLimitApply> lstApply = new ArrayList<>();
				for (HsPriceLimitPublicDetail hsPriceLimitPublicDetail : hsPriceLimitPublic.getHsPriceLimitPublicDetailList()){
					HsPriceLimitApply hsPriceLimitApply = hsPriceLimitPublicDetail.getHsPriceLimitApply();
					lstApply.add(hsPriceLimitApply);
				}
				hsPriceLimitApplyService.submitPubicTasks(lstApply);
				break;

			case "2":// 批量拒绝
				hsPriceLimitPublic.setStatus(HsPriceLimitPublic.PUBLIC_STATUS_CANCLE);	// 撤销
				this.updateStatus(hsPriceLimitPublic);
				String publicStatus = "0"; // 已公示
				for (HsPriceLimitPublicDetail hsPriceLimitPublicDetail : hsPriceLimitPublic.getHsPriceLimitPublicDetailList()){
					HsPriceLimitApply hsPriceLimitApply = hsPriceLimitPublicDetail.getHsPriceLimitApply();
					hsPriceLimitApplyService.updatePublicStatus(hsPriceLimitApply, publicStatus);
				}
				break;
			case "3":
				super.save(hsPriceLimitPublic);
				// 保存上传附件
				FileUploadUtils.saveFileUpload(hsPriceLimitPublic, hsPriceLimitPublic.getId(), "hsPriceLimitPublic_file");
				break;
		}
	}

	public void SavePublic(HsPriceLimitPublic hsPriceLimitPublic){

		if(hsPriceLimitPublic.getOfficeCode() == null) {
			if (UserUtils.getUser().isAdmin()) {
				throw new ServiceException(text("获取不到工作单位，不能以管理员身份发起！"));
			}
			String officeCode = EmpUtils.getCurrentOfficeCode();
			hsPriceLimitPublic.setOfficeCode(officeCode);
		}

		hsPriceLimitPublic.setStatus(HsPriceLimitPublic.PUBLIC_STATUS_PUBLIC);	// 正在公示
		this.updateStatus(hsPriceLimitPublic);
		super.save(hsPriceLimitPublic);

		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsPriceLimitPublic, hsPriceLimitPublic.getId(), "hsPriceLimitPublic_file");

		// 保存 HsPriceLimitPublic子表
		List<HsPriceLimitApply> hsApplyList = this.getPublicDetailList(hsPriceLimitPublic);
		if (hsPriceLimitPublic.getIsNewRecord()){

		}
		for (HsPriceLimitPublicDetail hsPriceLimitPublicDetail : hsPriceLimitPublic.getHsPriceLimitPublicDetailList()){
			if (!HsPriceLimitPublicDetail.STATUS_DELETE.equals(hsPriceLimitPublicDetail.getStatus())){
				hsPriceLimitPublicDetail.setPublicId(hsPriceLimitPublic.getId());
				if (hsPriceLimitPublicDetail.getIsNewRecord()){
					hsPriceLimitPublicDetailDao.insert(hsPriceLimitPublicDetail);
				}else{
					hsPriceLimitPublicDetailDao.update(hsPriceLimitPublicDetail);
				}
			}else{
				hsPriceLimitPublicDetailDao.delete(hsPriceLimitPublicDetail);
			}
		}
		String publicStatus = "2"; // 已公示
		for(HsPriceLimitPublicDetail hsPriceLimitPublicDetail : hsPriceLimitPublic.getHsPriceLimitPublicDetailList()) {
			HsPriceLimitApply hsPriceLimitApply = hsPriceLimitPublicDetail.getHsPriceLimitApply();
			hsPriceLimitApplyService.updatePublicStatus(hsPriceLimitApply, publicStatus);
		}
	}

	public void initPublic(HsPriceLimitPublic hsPriceLimitPublic, String pids) {
		hsPriceLimitPublic.setPids(pids);
		this.getPublicDetailList(hsPriceLimitPublic);
	}

	private List<HsPriceLimitApply> getPublicDetailList(HsPriceLimitPublic hsPriceLimitPublic) {
		List<HsPriceLimitApply> hsApplyList = new ArrayList<>();
		if (hsPriceLimitPublic.getPids() == null) {
			return hsApplyList;
		}

		HsPriceLimitApply hsApply = new HsPriceLimitApply();
		String[] pids = hsPriceLimitPublic.getPids().split(",");
		hsApply.sqlMap().getWhere().and("id", QueryType.IN, pids);
		hsApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		hsApplyList = hsPriceLimitApplyService.findList(hsApply);

		List<HsPriceLimitPublicDetail> detailList = new ArrayList<>();
		hsApplyList.forEach(k -> {
			HsPriceLimitPublicDetail detail = new HsPriceLimitPublicDetail();
			detail.setApplyId(k.getId());
			detail.setPublicId(hsPriceLimitPublic.getId());
			detail.setHsPriceLimitApply(k);
			detailList.add(detail);
		});
		hsPriceLimitPublic.setHsPriceLimitPublicDetailList(detailList);
		return hsApplyList;
	}

	
	/**
	 * 更新状态
	 * @param hsPriceLimitPublic
	 */
	@Override
	@Transactional
	public void updateStatus(HsPriceLimitPublic hsPriceLimitPublic) {
		super.updateStatus(hsPriceLimitPublic);
	}
	
	/**
	 * 删除数据
	 * @param hsPriceLimitPublic
	 */
	@Override
	@Transactional
	public void delete(HsPriceLimitPublic hsPriceLimitPublic) {
		super.delete(hsPriceLimitPublic);
		HsPriceLimitPublicDetail hsPriceLimitPublicDetail = new HsPriceLimitPublicDetail();
		hsPriceLimitPublicDetail.setPublicId(hsPriceLimitPublic.getId());
		hsPriceLimitPublicDetailDao.deleteByEntity(hsPriceLimitPublicDetail);
	}

	public void UpdatePublic2(HsPriceLimitPublic hsPriceLimitPublic, List<FileUpload> listUpload) {

		// 测试文件上传接口
		List<String> files = new ArrayList<>();
		try {
			files.add("e:\\公示附件.txt");
			files.add("e:\\公示附件2.txt");
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		Api2ResponseBody responseBody = apiSzkjService.uploadFile(files, hsPriceLimitPublic.getId());
		if(responseBody.getCode() != 1)
			throw new ServiceException(responseBody.getMessage());
	}
	public void UpdatePublic(HsPriceLimitPublic hsPriceLimitPublic, List<FileUpload> listUpload){

		Api2ResponseBody responseBody;
		if(hsPriceLimitPublic.getIsPublic() != null && hsPriceLimitPublic.getIsPublic().equals("1")){
			throw new ServiceException("该条公示已经发布过，不能重复发布");
		}

		String ids = "";
		/*if(listUpload.size() > 0){
			List<String> files = new ArrayList<>();
			try {
				for (FileUpload fileUpload : listUpload) {
					files.add(fileUpload.getFileEntity().getFileRealPath());
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
			responseBody = apiSzkjService.uploadFile(files, hsPriceLimitPublic.getId());
			if(responseBody.getCode() != 1)
				throw new ServiceException(responseBody.getMessage());
			ids = (String) responseBody.getData();
		}*/

		Api2NoticeBody body = new Api2NoticeBody();
		body.setMsgId(hsPriceLimitPublic.getId());
		body.setMsgType("1");		// 公告
		body.setMsgSource("限价房网上公示");
		body.setTopic(hsPriceLimitPublic.getPublicName());
		body.setContent(hsPriceLimitPublic.getPublicInfo());
		body.setFileIds(ids);
		body.setPublishUnit(hsPriceLimitPublic.getOffice().getFullName());
		body.setPublishUnitId(hsPriceLimitPublic.getOfficeCode());
		body.setPublishTime(hsPriceLimitPublic.getStartDate());
		body.setReceiveUserIds("all");
		responseBody = apiSzkjService.uploadNotice(body);
		if(responseBody.getCode() != 1)
			throw new ServiceException(responseBody.getMessage());

		hsPriceLimitPublic.setIsPublic("1");
		this.update(hsPriceLimitPublic);
	}

	public void sendPublic(HsPriceLimitPublic hsPriceLimitPublic) {
		if (hsPriceLimitPublic.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		if(hsPriceLimitPublic.getPublicName() == null || hsPriceLimitPublic.getPublicName().isEmpty()){
			throw new ServiceException(text("公告名称为空！"));
		}
		if(hsPriceLimitPublic.getPublicInfo() == null || hsPriceLimitPublic.getPublicInfo().isEmpty()){
			throw new ServiceException(text("公告信息为空！"));
		}

		List<FileUpload> listUpload = FileUploadUtils.findFileUpload(hsPriceLimitPublic.getId(), "hsPriceLimitPublic_file");
		UpdatePublic(hsPriceLimitPublic, listUpload);
	}

	private void putMap(Map<String, Object> map, String key, String val){
		map.put(key, String.format(" %s ", val));
	}

	public void outputPublic(HsPriceLimitPublic hsPriceLimitPublic, HttpServletResponse response) {
		// 导出网上公示
		Map<String, Object> map = new HashMap<>();

		map.put("public_title", hsPriceLimitPublic.getPublicName());
		map.put("public_info", hsPriceLimitPublic.getPublicInfo());
		Date date = hsPriceLimitPublic.getStartDate();
		map.put("start_date", String.format("%04d-%02d-%02d %02d:%02d", date.getYear()+1900, date.getMonth()+1, date.getDate(), date.getHours(), date.getMinutes()));
		date = hsPriceLimitPublic.getEndDate();
		map.put("end_date", String.format("%04d-%02d-%02d %02d:%02d", date.getYear()+1900, date.getMonth()+1, date.getDate(), date.getHours(), date.getMinutes()));
		map.put("office_name", hsPriceLimitPublic.getOffice().getFullName());

		List<Map<String, Object>> lstApply = new ArrayList<>();
		List<HsPriceLimitPublicDetail> lstDetail = hsPriceLimitPublic.getHsPriceLimitPublicDetailList();
		for(HsPriceLimitPublicDetail detail : lstDetail){

			Map<String, Object> apply = new HashMap<>();
			apply.put("name", detail.getHsPriceLimitApply().getMainApplyer().getName());
			apply.put("idnum", detail.getHsPriceLimitApply().getMainApplyer().getIdNum());
			apply.put("org", detail.getHsPriceLimitApply().getMainApplyer().getOrganization());

			lstApply.add(apply);
		}
		map.put("lstApply", lstApply);

		Resource resource = resourceLoader.getResource("classpath:file.template/pricelimit_public.docx");
		if (!resource.exists()) {
			return;
		}
		ServletOutputStream outputStream = null;
		try{

			response.setCharacterEncoding("utf-8");
			response.setContentType("application/x-download");
			String fileName = "限价房公示"+ DateUtils.getDate("yyyyMMddHHmmss")+".docx";
			fileName = new String(fileName.getBytes(), "ISO-8859-1");
			response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(fileName)));
			outputStream = response.getOutputStream();
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
		ConfigureBuilder configureBuilder = Configure.builder();
		configureBuilder.bind("lstApply", policy);

		Configure config = configureBuilder.build();

		try (InputStream templateStream = resource.getInputStream();
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
			template.render(map);
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
//		try {
//			WordUtils.exportMillCertificateWord(response, map, "pricelimit_public.ftl", "限价房公示"+ DateUtils.getDate("yyyyMMddHHmmss")+".doc", "price_public");
//
//		} catch (IOException e) {
//			throw new RuntimeException(e);
//		}
	}
}