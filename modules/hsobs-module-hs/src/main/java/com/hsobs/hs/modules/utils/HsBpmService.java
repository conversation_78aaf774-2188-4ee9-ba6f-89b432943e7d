package com.hsobs.hs.modules.utils;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.external.entity.FlowableProcess;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.reflect.ReflectUtils;
import com.jeesite.common.service.CrudService;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.bpm.utils.BpmNextNodeUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公共处理流程任务查询
 *
 * @param <T>
 */
public class HsBpmService<T extends BpmEntity> {

    CrudService crudService;

    private Class<T> clazz;

    public HsBpmService(Class<T> clazz) {
        this.clazz = clazz;
    }

    public T createInstance() {
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException |
                 NoSuchMethodException | InvocationTargetException e) {
            throw new RuntimeException("Cannot create instance", e);
        }
    }

    public HsBpmService setCrudService(CrudService crudService) {
        this.crudService = crudService;
        return this;
    }

    BpmTaskService bpmTaskService = BpmUtils.getBpmTaskService();

    public Page<T> findApplyPageByTask(T hsBean, String[] status, String bpmStatus, String formKey) {
        return findApplyPageByTask(hsBean, status, bpmStatus, formKey, "processName");
    }

    public Page<T> findApplyPageByTask(T hsBean, String[] status, String bpmStatus, String formKey, String propertyName) {
        HsBpmTask params = new HsBpmTask();
        params.setStatus(bpmStatus);
//        params.getProcIns().setName(hsBean.getApplyTitle());
//        params.setPage(new Page<>(hsBean.getPage().getPageNo(), hsBean.getPage().getPageSize()));
        Page<BpmTask> myHsTaskPage = this.getHsTask(params, status, ReflectUtils.invokeGetter(hsBean, propertyName), formKey);
        //获取所有待办任务的申请单id
        if (!this.getIdsByTask(myHsTaskPage, hsBean, new HashSet<>())) {
            hsBean.sqlMap().getWhere().disableAutoAddStatusWhere();
            hsBean.setPage(null);
            List<T> hsQwApplyPage = crudService.findList(hsBean);
            Page<T> result =  this.getTaskResult(hsQwApplyPage, myHsTaskPage);
            result.setCount(myHsTaskPage.getCount());
            result.setPageSize(myHsTaskPage.getPageSize());
            result.setPageNo(myHsTaskPage.getPageNo());
            return result;
        } else {
            return hsBean.getPage();
        }
    }

    private Page<T> getTaskResult(List<T> beanList, Page<BpmTask> myHsTaskPage) {
        Page<T> hsBeanPageResult = new Page<>();
        List<T> hsList = new ArrayList<>();
        for (BpmTask task : myHsTaskPage.getList()) {
            T bean = this.getBusinessBean(task, beanList);
            if (bean == null){
                continue;
            }
            hsList.add(bean);
        }
        hsBeanPageResult.setList(hsList);
        return hsBeanPageResult;
    }

    private T getBusinessBean(BpmTask task, List<T> beanList) {
        for (T t : beanList) {
            if (task.getProcIns().getBizKey().equals(t.getId())) {
                T bean = this.deepCopy(t);
                ReflectUtils.invokeSetter(bean, "applyTitle", task.getProcIns().getName());
                ReflectUtils.invokeSetter(bean, "processName", task.getName());
                ReflectUtils.invokeSetter(bean, "flowStatus", task.getName());
                ReflectUtils.invokeSetter(bean, "taskId", task.getId());
                ReflectUtils.invokeSetter(bean, "bpmStatus", task.getStatus());
                ReflectUtils.invokeSetter(bean, "taskDueDate", DateUtils.formatDate(task.getDueDate(), "yyyy-MM-dd HH:mm:ss"));
                ReflectUtils.invokeSetter(bean, "taskStartDate", DateUtils.formatDate(task.getClaimTime() == null ? task.getCreateTime() : task.getClaimTime(), "yyyy-MM-dd HH:mm:ss"));
                ReflectUtils.invokeSetter(bean, "formKey",task.getProcIns().getFormKey());
                return bean;
            }
        }
        return null;
    }

    private Page<BpmTask> getHsTask(HsBpmTask params, String[] resNames, String applyStatus, String formKey) {
        //查询我的代办rent_apply任务
        params.setUserCode(params.currentUser().getUserCode());
        params.getProcIns().setFormKey(formKey);//动态设置formKey
        if (resNames != null && resNames.length > 0) {
            params.setNames(Arrays.stream(resNames).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(applyStatus)) {
            params.setName(applyStatus);
        }
        return this.bpmTaskService.findTaskPage(params);
    }

    private boolean getIdsByTask(Page<BpmTask> pageTask, T hsQwApply, Set<String> hsIds) {
        pageTask.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
        hsQwApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
        return hsIds.isEmpty();
    }

    public Page<T> findMztPage(T hsBean, String formKey) {
        HsBpmTask params = new HsBpmTask();
//        params.setStatus("1");
        Page<BpmTask> myHsTaskPage = this.getHsTask(params, null, ReflectUtils.invokeGetter(hsBean, "processName"), formKey);
        //获取所有待办任务的申请单id
        Set<String> hsIds = this.getIdsByDraft(hsBean);
        if (!this.getIdsByTask(myHsTaskPage, hsBean, hsIds)) {
            hsBean.sqlMap().getWhere().disableAutoAddStatusWhere();
            List<T> hsQwApplyPage = crudService.findList(hsBean);
            return this.getTaskResult(hsQwApplyPage, myHsTaskPage);
        } else {
            return hsBean.getPage();
        }
    }


    private Set<String> getIdsByDraft(T hsQwApply) {
        HsQwApply query = new HsQwApply();
        query.setStatus(HsQwApply.STATUS_DRAFT);
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setCreateBy(UserUtils.getUser().getUserCode());
        List<HsQwApply> list = crudService.findList(query);
        Set<String> hsIds = new HashSet<>();
        list.forEach((k -> hsIds.add(k.getId())));
        return hsIds;
    }

    private T deepCopy(T instance) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(instance);
            oos.close();

            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bis);
            return (T) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            throw new RuntimeException("Deep copy failed", e);
        }
    }

}
