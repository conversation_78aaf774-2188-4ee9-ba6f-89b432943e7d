package com.hsobs.hs.modules.apply.service.serviceTask;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.blackrule.entity.HsQwApplyerBlackRule;
import com.hsobs.hs.modules.blackrule.service.HsQwApplyerBlackRuleService;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.service.HsQwApplyerBlackService;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.hsobs.hs.modules.clearance.service.HsQwClearanceService;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 申请资格审核-申请中的审核不符合租赁资格，需要执行申请单中止不再轮候
 * 1、当前申请中的执行中止轮候，包含个人申请和换房申请
 * 2、已经申请成功的执行退租
 */
@Component
public class HsQwApplyPersonStopServiceTask implements JavaDelegate {
    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private HsQwApplyerService hsQwApplyerService;

    @Autowired
    private HsQwClearanceService hsQwClearanceService;

    @Override
    public void execute(DelegateExecution delegateExecution) {
        HsQwApply hsQwApply = hsQwApplyService.get(BpmUtils.getBizKey(delegateExecution));
        //从主申请人去获取其所有的申请单
        HsQwApplyer query = new HsQwApplyer();
        query.setUserId(hsQwApply.getMainApplyer().getUserId());
        query.setHsQwApply(new HsQwApply());
        query.getHsQwApply().sqlMap().getWhere().and("status", QueryType.IN, new String[]{HsQwApply.STATUS_NORMAL,HsQwApply.STATUS_AUDIT});
        query.setApplyRole("0");//检索主申请人
//        query.getHsQwApply().sqlMap().getWhere().and("apply_matter", QueryType.IN, new String[]{"0","2"});//只有公租申请和房屋置换流程需要进行信息变更
        query.getHsQwApply().setOrderBy("apply_matter desc");
        List<HsQwApplyer> list = hsQwApplyerService.findList(query);
        for (HsQwApplyer hsQwApplyer : list) {
            HsQwApply apply = hsQwApplyService.get(hsQwApplyer.getApplyId());
            if (apply.getStatus().equals(HsQwApply.STATUS_AUDIT)){
                this.stopApply(apply);
            } else if (apply.getStatus().equals(HsQwApply.STATUS_NORMAL)){
                this.clearanceApply(apply);
            }
        }
    }

    private void clearanceApply(HsQwApply hsQwApply){
        HsQwClearance hsQwClearance = new HsQwClearance();
        hsQwClearance.setApplyId(hsQwApply.getId());
        hsQwClearance.setCompactId(hsQwApply.getCompact().getId());
        hsQwClearance.setApplyerId(hsQwApply.getMainApplyer().getId());
        hsQwClearance.setType("4");//核查清退
        hsQwClearance.setReason("个人申请资格核查执行清退");
        hsQwClearance.setBlackUser("0");
        hsQwClearanceService.save(hsQwClearance);
//        BpmUtils.start(hsQwClearance, "rent_clear", null, null);
    }


    private void stopApply(HsQwApply hsQwApply){
        BpmProcIns procIns = BpmUtils.getBpmRuntimeService().getProcessInstanceByBusinessKey("rent_apply", hsQwApply.getId());
        BpmUtils.getBpmRuntimeService().stopProcessInstance(procIns);
    }
}
