package com.hsobs.hs.modules.maintenance.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import lombok.Setter;

/**
 * 序列表Entity
 * <AUTHOR>
 * @version 2024-12-19
 */
@Setter
@Table(name="hs_sequence", alias="a", label="序列表信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="seq_name", attrName="seqName", label="序列名称"),
		@Column(name="current_val", attrName="currentVal", label="当前值"),
	}, orderBy="a.id DESC"
)
public class HsSequence extends DataEntity<HsSequence> {

	public static final String MAINTENANCE_FAV_NO_NAME = "SEQ_MAINTENANCE_FAV_NO";
	public static final String MAINTENANCE_ADN_NAME = "SEQ_MAINTENANCE_ADN";

	private static final long serialVersionUID = 1L;
	private String seqName;		// 序列名称
	private Long currentVal;		// 当前值

	public HsSequence() {
		this(null);
	}
	
	public HsSequence(String id){
		super(id);
	}
	
	@NotBlank(message="序列名称不能为空")
	@Size(min=0, max=255, message="序列名称长度不能超过 255 个字符")
	public String getSeqName() {
		return seqName;
	}

    @NotNull(message="当前值不能为空")
	public Long getCurrentVal() {
		return currentVal;
	}

}