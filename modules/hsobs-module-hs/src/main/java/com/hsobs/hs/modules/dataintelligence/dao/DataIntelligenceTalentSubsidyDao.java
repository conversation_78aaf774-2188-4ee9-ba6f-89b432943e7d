package com.hsobs.hs.modules.dataintelligence.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceTalentSubsidy;

import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计DAO接口   人才住房补助统计
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataIntelligenceTalentSubsidyDao extends CrudDao<DataIntelligenceTalentSubsidy> {

    List<Map<String, Object>> countTalentSubsidy(String sqlOtherWhere, String talentType, String sqlOrderBy);
    List<Map<String, Object>> countTalentSubsidy(Map<String, Object> map);

    List<Map<String, Object>> countTalentSubsidyArea(Map<String, Object> map);

    List<Map<String, Object>> countTalentSubsidyCompare(String sqlOtherWhere, String talentType, String talentLevel);

}