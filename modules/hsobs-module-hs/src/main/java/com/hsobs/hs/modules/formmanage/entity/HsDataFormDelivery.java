package com.hsobs.hs.modules.formmanage.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.Office;

/**
 * 数据表单下发Entity
 * <AUTHOR>
 * @version 2025-02-16
 */
@Table(name="hs_data_form_delivery", alias="a", label="数据表单下发信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="msg_title", attrName="msgTitle", label="消息标题", queryType=QueryType.LIKE),
		@Column(name="msg_content", attrName="msgContent", label="消息内容", queryType=QueryType.LIKE),
		@Column(name="template_code", attrName="templateCode", label="表单配置模板ID"),
		@Column(name="template_name", attrName="templateName", label="表单配置模板名称"),
		@Column(name="receive_type", attrName="receiveType", label="接受者类型", comment="接受者类型（0全部 1用户 2部门 3角色 4岗位）"),
		@Column(name="receive_codes", attrName="receiveCodes", label="接受者字符串"),
		@Column(name="receive_names", attrName="receiveNames", label="接受者名称字符串", queryType=QueryType.LIKE),
		@Column(name="send_user_code", attrName="sendUserCode", label="发送者用户编码"),
		@Column(name="send_user_name", attrName="sendUserName", label="发送者用户姓名", queryType=QueryType.LIKE),
		@Column(name="limit_date", attrName="limitDate", label="限制时间", isUpdateForce=true),
		@Column(name="send_date", attrName="sendDate", label="发送时间", isUpdateForce=true),
		@Column(name="is_attac", attrName="isAttac", label="是否有附件"),
		@Column(name="notify_types", attrName="notifyTypes", label="通知类型", comment="通知类型（PC APP 短信 邮件 微信）多选"),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsDataFormTemplate.class, alias = "h",
			on = "h.id = a.template_code", attrName = "formTemplate",
			columns = {
				@Column(includeEntity = HsDataFormTemplate.class)
		    }
		)
    }, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys="dsf", orderBy="a.update_date DESC"
)
public class HsDataFormDelivery extends DataEntity<HsDataFormDelivery> {

	// 接受者类型（0所有 1用户 2部门 3角色 4岗位）
	public static final String RECEIVE_TYPE_ALL = "0";
	public static final String RECEIVE_TYPE_USER = "1";
	public static final String RECEIVE_TYPE_OFFICE = "2";
	public static final String RECEIVE_TYPE_ROLE = "3";
	public static final String RECEIVE_TYPE_POST = "4";

	private static final long serialVersionUID = 1L;
	private String msgTitle;		// 消息标题
	private String msgContent;		// 消息内容
	private String templateCode;		// 表单配置模板ID
	private String templateName;		// 表单配置模板名称
	private String receiveType;		// 接受者类型（0全部 1用户 2部门 3角色 4岗位）
	private String receiveCodes;		// 接受者字符串
	private String receiveNames;		// 接受者名称字符串
	private String sendUserCode;		// 发送者用户编码
	private String sendUserName;		// 发送者用户姓名
	private Date limitDate;		// 限制时间
	private Date sendDate;		// 发送时间
	private String isAttac;		// 是否有附件
	private String notifyTypes;		// 通知类型（PC APP 短信 邮件 微信）多选

	private HsDataFormTemplate formTemplate;

	private String pageType;

	private String ids;

	public HsDataFormDelivery() {
		this(null);
	}
	
	public HsDataFormDelivery(String id){
		super(id);
	}
	
	@NotBlank(message="消息标题不能为空")
	@Size(min=0, max=200, message="消息标题长度不能超过 200 个字符")
	public String getMsgTitle() {
		return msgTitle;
	}

	public void setMsgTitle(String msgTitle) {
		this.msgTitle = msgTitle;
	}
	
	public String getMsgContent() {
		return msgContent;
	}

	public void setMsgContent(String msgContent) {
		this.msgContent = msgContent;
	}

	@Size(min=0, max=64, message="表单配置模板ID长度不能超过 64 个字符")
	public String getTemplateCode() {
		return templateCode;
	}

	public void setTemplateCode(String templateCode) {
		this.templateCode = templateCode;
	}

	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	@NotBlank(message="接受者类型不能为空")
	@Size(min=0, max=1, message="接受者类型长度不能超过 1 个字符")
	public String getReceiveType() {
		return receiveType;
	}

	public void setReceiveType(String receiveType) {
		this.receiveType = receiveType;
	}
	
	public String getReceiveCodes() {
		return receiveCodes;
	}

	public void setReceiveCodes(String receiveCodes) {
		this.receiveCodes = receiveCodes;
	}
	
	public String getReceiveNames() {
		return receiveNames;
	}

	public void setReceiveNames(String receiveNames) {
		this.receiveNames = receiveNames;
	}
	
	@Size(min=0, max=64, message="发送者用户编码长度不能超过 64 个字符")
	public String getSendUserCode() {
		return sendUserCode;
	}

	public void setSendUserCode(String sendUserCode) {
		this.sendUserCode = sendUserCode;
	}
	
	@Size(min=0, max=100, message="发送者用户姓名长度不能超过 100 个字符")
	public String getSendUserName() {
		return sendUserName;
	}

	public void setSendUserName(String sendUserName) {
		this.sendUserName = sendUserName;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getSendDate() {
		return sendDate;
	}

	public void setSendDate(Date sendDate) {
		this.sendDate = sendDate;
	}

	public Date getSendDate_gte() {
		return sqlMap.getWhere().getValue("send_date", QueryType.GTE);
	}

	public void setSendDate_gte(Date sendDate) {
		sendDate = DateUtils.getOfDayFirst(sendDate);
		sqlMap.getWhere().and("send_date", QueryType.GTE, sendDate);
	}

	public Date getSendDate_lte() {
		return sqlMap.getWhere().getValue("send_date", QueryType.LTE);
	}

	public void setSendDate_lte(Date sendDate) {
		sendDate = DateUtils.getOfDayLast(sendDate);
		sqlMap.getWhere().and("send_date", QueryType.LTE, sendDate);
	}

	@NotNull(message="截止时间不能为空")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getLimitDate() {
		return limitDate;
	}

	public void setLimitDate(Date limitDate) {
		this.limitDate = limitDate;
	}

	public Date getLimitDate_gte() {
		return sqlMap.getWhere().getValue("limit_date", QueryType.GTE);
	}

	public void setLimitDate_gte(Date limitDate) {
		limitDate = DateUtils.getOfDayFirst(limitDate);
		sqlMap.getWhere().and("limit_date", QueryType.GTE, limitDate);
	}

	public Date getLimitDate_lte() {
		return sqlMap.getWhere().getValue("limit_date", QueryType.LTE);
	}

	public void setLimitDate_lte(Date limitDate) {
		limitDate = DateUtils.getOfDayLast(limitDate);
		sqlMap.getWhere().and("limit_date", QueryType.LTE, limitDate);
	}

	@Size(min=0, max=1, message="是否有附件长度不能超过 1 个字符")
	public String getIsAttac() {
		return isAttac;
	}

	public void setIsAttac(String isAttac) {
		this.isAttac = isAttac;
	}
	
	@Size(min=0, max=100, message="通知类型长度不能超过 100 个字符")
	public String getNotifyTypes() {
		return notifyTypes;
	}

	public void setNotifyTypes(String notifyTypes) {
		this.notifyTypes = notifyTypes;
	}

	public HsDataFormTemplate getFormTemplate() {
		return formTemplate;
	}

	public void setFormTemplate(HsDataFormTemplate formTemplate) {
		this.formTemplate = formTemplate;
	}

	public String getIds() {
		return ids;
	}

	public void setIds(String ids) {
		this.ids = ids;
	}

	public String getPageType() {
		return pageType;
	}

	public void setPageType(String pageType) {
		this.pageType = pageType;
	}
}