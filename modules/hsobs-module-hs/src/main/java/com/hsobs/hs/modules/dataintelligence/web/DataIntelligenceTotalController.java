package com.hsobs.hs.modules.dataintelligence.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.hsobs.hs.modules.dataintelligence.util.AreaUtils;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.estate.util.EstateUtils;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.publicsaleestate.entity.HsPublicSaleEstate;
import com.hsobs.hs.modules.publicsaleestate.utils.HsPublicSaleEstateUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.sys.entity.Area;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.dataintelligence.service.DataIntelligenceTotalService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计Controller   总房源信息统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Controller
@RequestMapping(value = "${adminPath}/dataintelligencetotal/")
public class DataIntelligenceTotalController extends BaseController {

	@Autowired
	private DataIntelligenceTotalService dataIntelligenceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataIntelligenceTotal get(Integer type, boolean isNewRecord) {
		return dataIntelligenceService.get(type, isNewRecord);
	}

	/**
	 * 总房源信息统计
	 */
	@RequiresPermissions("dataintelligencetotal::view")
	@RequestMapping(value = {"dataIntelligenceTotal", ""})
	public String dataIntelligenceTotal(DataIntelligenceTotal dataIntelligenceTotal, Model model) {
		model.addAttribute("dataIntelligenceTotal", dataIntelligenceTotal);
		return "modules/dataintelligence/dataIntelligenceTotal";
	}

	/**
	 *
	 */
	@RequiresPermissions("dataintelligencetotal::view")
	@RequestMapping(value = "totalStatList")
	@ResponseBody
	public Page<DataIntelligenceTotal> totalStatList(DataIntelligenceTotal dataIntelligenceTotal, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceTotal.setPage(new Page<>(request, response));
		Page<DataIntelligenceTotal> page = dataIntelligenceService.findTotalDataPage(dataIntelligenceTotal, true);
		return page;
	}

	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "totalRoomsStat")
	@ResponseBody
	public String totalRoomsStat(DataIntelligenceTotal dataIntelligenceTotal, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.totalStat(dataIntelligenceTotal, true);
	}

	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "totalAreasStat")
	@ResponseBody
	public String totalAreasStat(DataIntelligenceTotal dataIntelligenceTotal, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.totalStat(dataIntelligenceTotal, false);
	}

	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "totalRoomsStatByCity")
	@ResponseBody
	public String totalRoomsStatByCity(DataIntelligenceTotal dataIntelligenceTotal, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.totalStatByCity(dataIntelligenceTotal, true);
	}

	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "totalAreasStatByCity")
	@ResponseBody
	public String totalAreasStatByCity(DataIntelligenceTotal dataIntelligenceTotal, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.totalStatByCity(dataIntelligenceTotal, false);
	}

	/**
	 * 获取house信息
	 */
	@RequestMapping(value = "getAreaInfo")
	@ResponseBody
	public String getAraeInfo(String areaCode) {

		List<Area> listArea = AreaUtils.getList(areaCode);

		List<Map<String,String>> mapList = new ArrayList<Map<String,String>>();
		for(Area area:listArea){
			Map<String, String> map = new HashMap<>();
			map.put("areaCode", area.getAreaCode());
			map.put("areaName", area.getAreaName());
			mapList.add(map);
		}
		return JSON.toJSONString(mapList);
	}

	/**
	 * 获取house信息
	 */
	@RequestMapping(value = "getEstateInfo")
	@ResponseBody
	public String getEstateInfo(String areaCode) {

		List<HsQwPublicRentalEstate> listEsstate = EstateUtils.getEstateList(areaCode);

		List<Map<String,String>> mapList = new ArrayList<Map<String,String>>();
		for(HsQwPublicRentalEstate estate:listEsstate){
			Map<String, String> map = new HashMap<>();
			map.put("id", estate.getId());
			map.put("name", estate.getName());
			mapList.add(map);
		}
		return JSON.toJSONString(mapList);
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligencetotal::view")
	@RequestMapping(value = "exportData")
	public void exportData(DataIntelligenceTotal dataIntelligenceTotal, HttpServletResponse response) {
		String fileName = "总体房源信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("总体房源信息表", DataIntelligenceTotal.class);
			List<DataIntelligenceTotal> list = dataIntelligenceService.findTotalData(dataIntelligenceTotal);
			ee.setDataList(list);

			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}
}