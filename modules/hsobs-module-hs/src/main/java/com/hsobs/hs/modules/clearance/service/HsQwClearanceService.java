package com.hsobs.hs.modules.clearance.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.utils.HsBpmService;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.hsobs.hs.modules.clearance.dao.HsQwClearanceDao;
import java.util.Map;
import java.util.stream.Collectors;

import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;

import javax.annotation.PostConstruct;

/**
 * 租赁资格轮候清退申请Service
 * <AUTHOR>
 * @version 2024-12-24
 */
@Service
public class HsQwClearanceService extends CrudService<HsQwClearanceDao, HsQwClearance> {

	@Autowired
	private BpmTaskService bpmTaskService;

	@Autowired
	private HsQwApplyService hsQwApplyService;

	private HsBpmService<HsQwClearance> hsBpmService;
	@PostConstruct
	public void init() {
		// 在注入后对 hsBpmService 的属性进行设置
		hsBpmService = new HsBpmService<>(HsQwClearance.class);
		hsBpmService.setCrudService(this);
	}
	/**
	 * 获取单条数据
	 * @param hsQwClearance
	 * @return
	 */
	@Override
	public HsQwClearance get(HsQwClearance hsQwClearance) {
		HsQwClearance entity = super.get(hsQwClearance);
		if (entity !=null && StringUtils.isNotBlank(entity.getApplyId())){
			entity.setHsQwApply(hsQwApplyService.get(entity.getApplyId()));
		}
		return entity;
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwClearance 查询条件
	 * @param hsQwClearance page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwClearance> findPage(HsQwClearance hsQwClearance) {
		Page<HsQwClearance> page = super.findPage(hsQwClearance);
//		page.getList().forEach(k -> {
//			if (StringUtils.isNotBlank(k.getApplyId())){
//				k.setHsQwApply(hsQwApplyService.get(k.getApplyId()));
//			}
//		});
		return page;
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwClearance
	 * @return
	 */
	@Override
	public List<HsQwClearance> findList(HsQwClearance hsQwClearance) {
		return super.findList(hsQwClearance);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwClearance
	 */
	@Override
	@Transactional
	public void save(HsQwClearance hsQwClearance) {
		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsQwClearance.getStatus())){
			hsQwClearance.setStatus(HsQwClearance.STATUS_AUDIT);
		}
		
		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsQwClearance.STATUS_NORMAL.equals(hsQwClearance.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		
		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsQwClearance.STATUS_DRAFT.equals(hsQwClearance.getStatus())
				|| HsQwClearance.STATUS_AUDIT.equals(hsQwClearance.getStatus())){
			super.save(hsQwClearance);
		}

		// 如果为审核状态，则进行审批流操作
		if (HsQwClearance.STATUS_AUDIT.equals(hsQwClearance.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();
			variables.put("blackUser", hsQwClearance.getBlackUser());
			
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsQwClearance.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsQwClearance.getBpm().getTaskId())){
				BpmUtils.start(hsQwClearance, "rent_clear", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsQwClearance, variables, null);
			}
		}
		// 保存上传图片
		FileUploadUtils.saveFileUpload(hsQwClearance, hsQwClearance.getId(), "hsQwClearance_image");
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsQwClearance, hsQwClearance.getId(), "hsQwClearance_file");
	}
	
	/**
	 * 更新状态
	 * @param hsQwClearance
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwClearance hsQwClearance) {
		if (hsQwClearance.getStatus().equals(HsQwClearance.STATUS_NORMAL)){
			//流程完成，申请单状态修改为不可用
			HsQwClearance hsQwClearance1 = this.get(hsQwClearance.getId());
			HsQwApply hsQwApply = new HsQwApply();
			hsQwApply.setId(hsQwClearance1.getApplyId());
			hsQwApply.setStatus(HsQwApply.STATUS_DISABLE);
			hsQwApply.setClear(true);
			hsQwApplyService.updateStatus(hsQwApply);
		}
		super.updateStatus(hsQwClearance);
	}
	
	/**
	 * 删除数据
	 * @param hsQwClearance
	 */
	@Override
	@Transactional
	public void delete(HsQwClearance hsQwClearance) {
		super.delete(hsQwClearance);
	}

	@Autowired
	private CommonBpmService commonBpmService;

	public Page<HsQwClearance> findPageByTask(HsQwClearance hsQwClearance, String[] status, String bpmStatus) {
		return commonBpmService.findTaskList(status, "rent_clear",hsQwClearance, bpmStatus);
	}

    public String[] getAuditIds() {
		HsQwClearance hsQwClearance = new HsQwClearance();
		hsQwClearance.setStatus(HsQwClearance.STATUS_AUDIT);
		List<HsQwClearance> list = this.findList(hsQwClearance);
		return list.stream().map(HsQwClearance::getApplyId).toArray(String[]::new);
    }

	public Page<HsQwApply> findClearancePage(HsQwApply hsQwApply) {
		String[] auditIds = this.getAuditIds();
		hsQwApply.sqlMap().getWhere().and("id", QueryType.NOT_IN, auditIds);
		hsQwApply.setStatus(HsQwApply.STATUS_NORMAL);
		return hsQwApplyService.findPage(hsQwApply);
	}
}