package com.hsobs.hs.modules.applyrule.service.HsQwApplyRule;

import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRuleResult;
import com.jeesite.common.service.ServiceException;
import org.springframework.stereotype.Service;

@Service
public class HsQwApplyRuleWork implements IHsQwApplyRule {
    @Override
    public String getRuleConfig() {
        return "9";
    }

    @Override
    public HsQwApplyRuleResult execute(String content, HsQwApplyRule rule) {
        HsQwApplyRuleResult ruleResult = new HsQwApplyRuleResult();
        try {
            // 使用Java 8日期API处理日期
            java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM");
            java.time.YearMonth yearMonth = java.time.YearMonth.parse(content, formatter);
            java.time.LocalDate startDate = yearMonth.atDay(1);
            java.time.LocalDate currentDate = java.time.LocalDate.now();

            // 校验开始时间是否晚于当前时间
            if (startDate.isAfter(currentDate)) {
                ruleResult.setData("0");
                ruleResult.setResult(true);
                return ruleResult;
            }

            // 精确计算年月差
            java.time.Period period = java.time.Period.between(startDate, currentDate);
            int totalMonths = period.getYears() * 12 + period.getMonths();

            // 安全转换配置参数
            int aveYear = Integer.parseInt(rule.getRuleContent());
            int result = Integer.parseInt(rule.getRuleResult());

            // 计算最终得分（总月数/12取整即为年数）
            int workYears = totalMonths / 12;
            if (aveYear <= 0) {
                throw new ServiceException("规则配置错误：平均年限必须大于0");
            }
            ruleResult.setData(String.valueOf((workYears / aveYear) * result));
            ruleResult.setResult(true);
        } catch (NumberFormatException e) {
            throw new ServiceException("资格轮候评分规则配置【自定义工龄】错误，影响评分计算，请及时纠正！");
        }
        return ruleResult;
    }

}
