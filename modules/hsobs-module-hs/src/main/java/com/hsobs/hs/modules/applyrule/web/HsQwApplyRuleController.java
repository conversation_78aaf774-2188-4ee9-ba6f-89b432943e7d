package com.hsobs.hs.modules.applyrule.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.applyrule.service.HsQwApplyRuleService;

/**
 * 租赁资格轮候规则配置Controller
 * <AUTHOR>
 * @version 2024-12-06
 */
@Controller
@RequestMapping(value = "${adminPath}/applyrule/hsQwApplyRule")
public class HsQwApplyRuleController extends BaseController {

	@Autowired
	private HsQwApplyRuleService hsQwApplyRuleService;

	@Autowired
	private HsQwApplyService hsQwApplyService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyRule get(String id, boolean isNewRecord) {
		return hsQwApplyRuleService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("applyrule:hsQwApplyRule:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyRule hsQwApplyRule, Model model) {
		model.addAttribute("hsQwApplyRule", hsQwApplyRule);
		return "modules/applyrule/hsQwApplyRuleList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("applyrule:hsQwApplyRule:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyRule> listData(HsQwApplyRule hsQwApplyRule, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyRule.setPage(new Page<>(request, response));
		Page<HsQwApplyRule> page = hsQwApplyRuleService.findPage(hsQwApplyRule);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("applyrule:hsQwApplyRule:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyRule hsQwApplyRule, Model model) {
		model.addAttribute("hsQwApplyRule", hsQwApplyRule);
		return "modules/applyrule/hsQwApplyRuleForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("applyrule:hsQwApplyRule:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyRule hsQwApplyRule) {
		hsQwApplyRuleService.save(hsQwApplyRule);
		return renderResult(Global.TRUE, text("保存租赁资格轮候规则配置成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("applyrule:hsQwApplyRule:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsQwApplyRule hsQwApplyRule) {
		hsQwApplyRule.setStatus(HsQwApplyRule.STATUS_DISABLE);
		hsQwApplyRuleService.updateStatus(hsQwApplyRule);
		return renderResult(Global.TRUE, text("停用租赁资格轮候规则配置成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("applyrule:hsQwApplyRule:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsQwApplyRule hsQwApplyRule) {
		hsQwApplyRule.setStatus(HsQwApplyRule.STATUS_NORMAL);
		hsQwApplyRuleService.updateStatus(hsQwApplyRule);
		return renderResult(Global.TRUE, text("启用租赁资格轮候规则配置成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("applyrule:hsQwApplyRule:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyRule hsQwApplyRule) {
		hsQwApplyRuleService.delete(hsQwApplyRule);
		return renderResult(Global.TRUE, text("删除租赁资格轮候规则配置成功！"));
	}

}