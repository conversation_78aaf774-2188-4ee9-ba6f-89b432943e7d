package com.hsobs.hs.modules.clearance.service;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.blackrule.entity.HsQwApplyerBlackRule;
import com.hsobs.hs.modules.blackrule.service.HsQwApplyerBlackRuleService;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.service.HsQwApplyerBlackService;
import com.hsobs.hs.modules.clearance.dao.HsQwClearanceDao;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 租赁资格轮候清退申请Service
 * <AUTHOR>
 * @version 2024-12-24
 */
@Component
public class HsQwClearanceBlackService implements JavaDelegate {
	@Autowired
	private HsQwApplyerBlackService hsQwApplyerBlackService;
	@Autowired
	private HsQwApplyerBlackRuleService hsQwApplyerBlackRuleService;

	@Autowired
	private HsQwClearanceService hsQwClearanceService;
	@Autowired
	private HsQwApplyService hsQwApplyService;

	@Override
	public void execute(DelegateExecution delegateExecution) {
		HsQwApplyerBlack hsQwApplyerBlack = new HsQwApplyerBlack();
		ExecutionEntity proins = ((ExecutionEntity) delegateExecution).getProcessInstance();
//		//更新申请单状态
		HsQwClearance hs = hsQwClearanceService.get(BpmUtils.getBizKey(delegateExecution));
//		HsQwApply hsQwApply = new HsQwApply();
//		hsQwApply.setId(hs.getApplyId());
//		hsQwApply.setStatus("2");
//		hsQwApplyService.updateStatus(hsQwApply);
		//添加黑名单
		String userid =  proins.getStartUserId();
		hsQwApplyerBlack.setUserId(userid);
		hsQwApplyerBlack.setReasonType(this.getBlackRuleType(hs.getType()));
		HsQwApplyerBlackRule rule = hsQwApplyerBlackRuleService.getApplyerBlackByReansonType(this.getBlackRuleType(hs.getType()));
		hsQwApplyerBlack.setEndTime(DateUtils.addMonths(new Date(), Math.toIntExact(rule.getTimeNum())));
		hsQwApplyerBlackService.saveAndStatus(hsQwApplyerBlack);
	}

	/**
	 * @description 类型映射
	 * <AUTHOR>
	 * @date 1/3/25 9:55 PM
	 * @param type
	 * @return: java.lang.String
	 */
	private String getBlackRuleType(String type) {
		if (type.equals("0")){
			return "1";//租金拖欠
		} else if (type.equals("2")){
			return "2";
		}
		return null;
	}
}