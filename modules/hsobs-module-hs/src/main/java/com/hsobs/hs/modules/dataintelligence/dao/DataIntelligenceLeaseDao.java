package com.hsobs.hs.modules.dataintelligence.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceLease;

import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计DAO接口  公租房租赁情况统计
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataIntelligenceLeaseDao extends CrudDao<DataIntelligenceLease> {

    // String sqlOtherWhere
    List<Map<String, Object>> countTotalStat(String sqlOtherWhere);
    List<Map<String, Object>> countTotalStat(Map<String, Object> map);

    // String sqlWhere, String sqlOtherWhere, String sqlOrderBy
    List<Map<String, Object>> countAreaStat(Map<String, Object> map);

    List<Map<String, Object>> countLeasePaidStatByOffice(Map<String, Object> map);

    // String sqlOtherWhere, String sqlOrderBy
    List<Map<String, Object>> countLeaseOfficeStat(Map<String, Object> map);

    List<Map<String, Object>> countLeaseAreaOfficeStat(Map<String, Object> map);

    // String sqlWhere, String sqlOrderBy
    List<Map<String, Object>> countLeaseFEEStat(Map<String, Object> map);

    List<Map<String, Object>> countLeaseBuildingAreaStat(String sqlOtherWhere, String sqlOrderBy);
    List<Map<String, Object>> countLeaseBuildingAreaStat(Map<String, Object> map);

    List<Map<String, Object>> countVacantAreaStat(String sqlOtherWhere, String sqlOrderBy);
    List<Map<String, Object>> countVacantAreaStat(Map<String, Object> map);

    List<Map<String, Object>> countUnpaidOffice(String sqlOtherWhere);
}