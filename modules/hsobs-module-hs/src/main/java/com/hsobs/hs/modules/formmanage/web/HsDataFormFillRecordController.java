package com.hsobs.hs.modules.formmanage.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormFillRecord;
import com.hsobs.hs.modules.formmanage.service.HsDataFormFillRecordService;

/**
 * 数据表单填写详情记录表Controller
 * <AUTHOR>
 * @version 2025-02-21
 */
@Controller
@RequestMapping(value = "${adminPath}/formmanage/hsDataFormFillRecord")
public class HsDataFormFillRecordController extends BaseController {

	@Autowired
	private HsDataFormFillRecordService hsDataFormFillRecordService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsDataFormFillRecord get(String id, boolean isNewRecord) {
		return hsDataFormFillRecordService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecord:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsDataFormFillRecord hsDataFormFillRecord, Model model) {
		model.addAttribute("hsDataFormFillRecord", hsDataFormFillRecord);
		return "modules/formmanage/hsDataFormFillRecordList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecord:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsDataFormFillRecord> listData(HsDataFormFillRecord hsDataFormFillRecord, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormFillRecord.setPage(new Page<>(request, response));
		Page<HsDataFormFillRecord> page = hsDataFormFillRecordService.findPage(hsDataFormFillRecord);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecord:view")
	@RequestMapping(value = "form")
	public String form(HsDataFormFillRecord hsDataFormFillRecord, Model model) {
		model.addAttribute("hsDataFormFillRecord", hsDataFormFillRecord);
		return "modules/formmanage/hsDataFormFillRecordForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecord:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsDataFormFillRecord hsDataFormFillRecord) {
		hsDataFormFillRecordService.save(hsDataFormFillRecord);
		return renderResult(Global.TRUE, text("保存数据表单填写详情记录表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecord:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsDataFormFillRecord hsDataFormFillRecord) {
		hsDataFormFillRecordService.delete(hsDataFormFillRecord);
		return renderResult(Global.TRUE, text("删除数据表单填写详情记录表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("formmanage:hsDataFormFillRecord:view")
	@RequestMapping(value = "hsDataFormFillRecordSelect")
	public String hsDataFormFillRecordSelect(HsDataFormFillRecord hsDataFormFillRecord, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsDataFormFillRecord", hsDataFormFillRecord);
		return "modules/formmanage/hsDataFormFillRecordSelect";
	}
	
}