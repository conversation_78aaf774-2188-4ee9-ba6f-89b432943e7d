package com.hsobs.hs.modules.apply.service;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.entity.HsQwApplyAllExport;
import com.hsobs.hs.modules.apply.entity.HsQwApplyExport;
import com.hsobs.hs.modules.apply.service.HsQwApplyProcess.HsQwApplyProcess;
import com.hsobs.hs.modules.apply.service.applyCheck.HsQwApplyCheck;
import com.hsobs.hs.modules.apply.service.applyRule.HsQwApplyRuleMethod;
import com.hsobs.hs.modules.apply.service.applyedDataList.HsQwApplyedList;
import com.hsobs.hs.modules.apply.service.updateStatus.HsQwApplyStatus;
import com.hsobs.hs.modules.applyer.dao.HsQwApplyerDao;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyhouse.dao.HsQwApplyHouseDao;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyrule.service.HsQwApplyRuleService;
import com.hsobs.hs.modules.applyscore.entity.HsQwApplyScoreDetail;
import com.hsobs.hs.modules.applyscore.service.HsQwApplyScoreDetailService;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import com.hsobs.hs.modules.formalarm.service.HsQwFormAlarmService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.utils.HsBpmService;
import com.hsobs.hs.modules.utils.ObjectCompareUtil;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.file.dao.FileUploadDao;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import lombok.Getter;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 租赁资格轮候申请Service
 *
 * <AUTHOR>
 * @version 2024-11-21
 */
@Service
public class HsQwApplyService extends CrudService<HsQwApplyDao, HsQwApply> implements ApplicationContextAware {

    @Getter
    @Autowired
    private HsQwApplyerDao hsQwApplyerDao;

    @Getter
    @Autowired
    private HsQwCompactService hsQwCompactService;

    @Autowired
    private FileUploadDao fileUploadDao;

    @Getter
    @Autowired
    private HsQwApplyHouseDao hsQwApplyHouseDao;

    @Autowired
    private EmpUserService empUserService;

    @Autowired
    private OfficeService officeService;

    @Autowired
    private BpmTaskService bpmTaskService;

    @Autowired
    private HsQwApplyRuleService hsQwApplyRuleService;

    @Autowired
    private CommonBpmService commonBpmService;

    @Autowired
    private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

    @Autowired
    private HsQwFormAlarmService hsQwFormAlarmService;

    private HsBpmService<HsQwApply> hsBpmService;

    @Autowired
    private HsQwApplyScoreDetailService hsQwApplyScoreDetailService;

    @PostConstruct
    public void init() {
        // 在注入后对 hsBpmService 的属性进行设置
        hsBpmService = new HsBpmService<>(HsQwApply.class);
        hsBpmService.setCrudService(this);
    }

    /**
     * 存储所有方法实现-审核过程方法
     **/
    private Map<String, HsQwApplyProcess> processMap = new HashMap<>();

    /**
     * 存储所有方法实现-申请资格检查方法
     **/
    private Map<String, HsQwApplyCheck> checkMap = new HashMap<>();

    /**
     * 存储所有方法实现-已申请单获取方法
     **/
    private Map<String, HsQwApplyedList> applyedListMap = new HashMap<>();

    /**
     * 存储所有方法实现-资格核验方法
     **/
    private List<HsQwApplyRuleMethod> applyRuleMap = new ArrayList<>();

    /**
     * 存储所有方法实现-已申请单获取方法
     **/
    private Map<String, HsQwApplyStatus> applyStatusMap = new HashMap<>();

    /**
     * 获取指定机构和角色的用户，若不存在，则获取上级机构和角色的用户，再没有就指定GZ_JB角色的用户
     * @param officeCode
     * @param roleCode
     * @return
     */
    public List<String> findUserByRoleAndOffice(String officeCode, String roleCode) {
        if (StringUtils.isBlank(officeCode)) {
            officeCode = EmpUtils.getCurrentOfficeCode();
        }
        EmpUser user = new EmpUser();
        user.setRoleCode(roleCode);
        user.getEmployee().getOffice().setOfficeCode(officeCode);
        List<EmpUser> users = empUserService.findList(user);
        if (users.size() > 0) {
            return users.stream().map(a -> a.getUserCode()).collect(Collectors.toList());   
        } else {
            Office office = officeService.get(officeCode);
            if (office!=null && office.getParentCode() != null) {
                return findUserByRoleAndOffice(office.getParentCode(), roleCode);
            } else {
                user.setRoleCode("JD_JG");
                users = empUserService.findList(user);
                return users.stream().map(a -> a.getUserCode()).collect(Collectors.toList());
            }
        }
    }

    @Override
    public void addDataScopeFilter(HsQwApply entity) {
        SqlMap sqlMap = entity.sqlMap(); // v5.3.0+ 及之后版本
        // 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
        sqlMap.getDataScope().addFilter("extWhere", "Office",
                "a.office_code", "a.create_by", DataScope.CTRL_PERMI_HAVE, "hsQwApply");
    }

    public HsQwApply getBase(String id) {
        HsQwApply qwApply = new HsQwApply();
        qwApply.setId(id);
        return this.dao.get(qwApply);
    }

    /**
     * 获取单条数据
     *
     * @param hsQwApply
     * @return
     */
    @Override
    public HsQwApply get(HsQwApply hsQwApply) {
        if (hsQwApply.isQuery()) {
            return hsQwApply;
        }
        HsQwApply entity = super.get(hsQwApply);
        if (entity != null) {
            Office office = new Office();
            office.setOfficeCode(entity.getOfficeCode());
            entity.setOffice(officeService.get(office));
            HsQwApplyer hsQwApplyer = new HsQwApplyer();
            hsQwApplyer.setApplyId(entity.getId());
            hsQwApplyer.setOrderBy("a.create_date");
            hsQwApplyer.setStatus(HsQwApplyer.STATUS_NORMAL);
            entity.setHsQwApplyerList(hsQwApplyerDao.findList(hsQwApplyer));
            HsQwApplyHouse hsQwApplyHouse = new HsQwApplyHouse();
            hsQwApplyHouse.setApplyId(entity.getId());
            hsQwApplyHouse.setStatus(HsQwApplyHouse.STATUS_NORMAL);
            hsQwApplyHouse.setOrderBy("a.create_date");
            entity.setHsQwApplyHouseList(hsQwApplyHouseDao.findList(hsQwApplyHouse));
            // 获取配租房源信息
            if (StringUtils.isNotBlank(entity.getHouseId())) {
                HsQwPublicRentalHouse rentalHouse = new HsQwPublicRentalHouse();
                rentalHouse.setId(entity.getHouseId());
                // rentalHouse.sqlMap().getOrder().setOrderBy("ha.apply_matter,ha.create_date
                // DESC");
                // HsQwPublicRentalHouse rentHouse =
                // hsQwPublicRentalHouseService.findList(rentalHouse).stream().findFirst().orElse(null);
                HsQwPublicRentalHouse rentHouse = hsQwPublicRentalHouseService.get(rentalHouse);
                entity.setHsQwApplyHouse(rentHouse);
            }
            // 查询表单预警信息
            this.setChangeMap(entity);
        }
        return entity;
    }

    private void setChangeMap(HsQwApply hsQwApply) {
        HsQwFormAlarm query = new HsQwFormAlarm();
        query.setObjectId(hsQwApply.getId());
        query.setStatus(HsQwFormAlarm.STATUS_NORMAL);
        List<HsQwFormAlarm> alarms = hsQwFormAlarmService.findList(query);
        // 写入changeMap
        Map<String, String> changeMap = new HashMap<>();
        alarms.forEach(a -> {
            // 如果是重复的key，就做append处理，但是要处理掉alarmInfo中第一行首个:及前面的字符
            if (changeMap.containsKey(a.getAttrKey())) {
                String prefix = a.getAlarmInfo().substring(0, a.getAlarmInfo().indexOf("："));
                if (changeMap.get(a.getAttrKey()).indexOf(prefix) > -1) {
                    changeMap.put(a.getAttrKey(), changeMap.get(a.getAttrKey())
                            + a.getAlarmInfo().substring(a.getAlarmInfo().indexOf("：") + 1)
                            + " " + DateUtils.formatDate(a.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
                } else {
                    changeMap.put(a.getAlarmType() + "-" + a.getAttrKey(),
                            a.getAlarmInfo() + " " + DateUtils.formatDate(a.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
                }

            } else {
                changeMap.put(a.getAttrKey(),
                        a.getAlarmInfo() + " " + DateUtils.formatDate(a.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
            }
        });
        hsQwApply.setChangeMap(changeMap);
    }

    /**
     * 查询分页数据
     *
     * @param hsQwApply 查询条件
     * @param hsQwApply page 分页对象
     * @return
     */
    @Override
    public Page<HsQwApply> findPage(HsQwApply hsQwApply) {
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        Page<HsQwApply> pages = super.findPage(hsQwApply);
        pages.getList().forEach(k -> {
            if (StringUtils.isNotBlank(k.getHouseId()) && k.getHsQwApplyHouse() == null) {
                k.setHsQwApplyHouse(hsQwPublicRentalHouseService.get(k.getHouseId()));
            }
            if (k.getCompact() == null) {
                HsQwCompact compact = hsQwCompactService.getByApplyId(k.getId());
                k.setCompact(compact);
            }
        });
        return pages;
    }

    /**
     * 查询列表数据
     *
     * @param hsQwApply
     * @return
     */
    @Override
    public List<HsQwApply> findList(HsQwApply hsQwApply) {
        return super.findList(hsQwApply);
    }

    /**
     * 查询子表分页数据
     *
     * @param hsQwApplyer
     * @param hsQwApplyer page 分页对象
     * @return
     */
    public Page<HsQwApplyer> findSubPage(HsQwApplyer hsQwApplyer) {
        Page<HsQwApplyer> page = hsQwApplyer.getPage();
        page.setList(hsQwApplyerDao.findList(hsQwApplyer));
        return page;
    }

    /**
     * 查询子表分页数据
     *
     * @param hsQwApplyHouse
     * @param hsQwApplyHouse page 分页对象
     * @return
     */
    public Page<HsQwApplyHouse> findSubPage(HsQwApplyHouse hsQwApplyHouse) {
        Page<HsQwApplyHouse> page = hsQwApplyHouse.getPage();
        page.setList(hsQwApplyHouseDao.findList(hsQwApplyHouse));
        return page;
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param hsQwApply
     */
    @Override
    @Transactional
    public void save(HsQwApply hsQwApply) {
        String formKey = "";
        if (hsQwApply.getApplyMatter().equals("0") || hsQwApply.getApplyMatter().equals("2")
                || hsQwApply.getApplyMatter().equals("3")) {
            formKey = "rent_apply";
        } else if (hsQwApply.getApplyMatter().equals("1")) {
            formKey = "rent_apply_info";
        } else if (hsQwApply.getApplyMatter().equals("4")) {
            formKey = "rent_apply_house";
        } else if (hsQwApply.getApplyMatter().equals("5")) {
            formKey = "rent_apply_bureau";
        }
        this.save(hsQwApply, formKey);
    }

    /**
     * 根据表单key来进行流程更新操作
     *
     * @param hsQwApply
     * @param formKey
     */
    public void save(HsQwApply hsQwApply, String formKey) {
        if (hsQwApply.getIsNewRecord()
                || hsQwApply.getStatus().equals(HsQwApply.STATUS_DRAFT)
                || hsQwApply.getProcessName() == null
                || hsQwApply.getProcessName().equals("undefined")) {
            hsQwApply.setProcessName(HsQwApply.APPLY_STATUS_DRAFT);
        }
        // 自定义流程操作
        HsQwApplyProcess process = processMap.get(formKey + "_" + hsQwApply.getProcessName());
        if (process == null) {
            process = processMap.get(formKey + "_" + HsQwApply.APPLY_STATUS_DEFAULT);
        }
        process.execute(hsQwApply, this);
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param hsQwApply
     */
    @Transactional
    public void saveProcess(HsQwApply hsQwApply) {
        // 获取mainApplyer信息
        if (hsQwApply.getMainApplyer() == null) {
            this.setMainApplyer(hsQwApply);
        }
        // hsQwApplyRuleService.refreshScore(hsQwApply);
        super.save(hsQwApply);
    }

    public void setMainApplyer(HsQwApply hsQwApply) {
        List<HsQwApplyer> applyerList = hsQwApply.getHsQwApplyerList();
        for (HsQwApplyer hsQwApplyer : applyerList) {
            if (hsQwApplyer.getApplyRole().equals("0")) {
                hsQwApply.setMainApplyer(hsQwApplyer);
                return;
            }
        }
    }

    /**
     * 更新状态
     *
     * @param hsQwApply apply_matter:申请事项（0公租申请 1信息变更 2承租置换 3绿色通道 4承租变更）
     */
    @Override
    @Transactional
    public void updateStatus(HsQwApply hsQwApply) {
        HsQwApply realBean = this.get(hsQwApply.getId());
        // 自定义流程操作
        if (realBean == null) {
            return;
        }
        HsQwApplyStatus process = applyStatusMap.get(realBean.getApplyMatter());
        if (process == null) {
            throw new ServiceException("更新申请单状态失败，处理方法为空！" + hsQwApply.getApplyMatter());
        }
        process.execute(realBean, hsQwApply, this);

    }

    public void realUpdateStatus(HsQwApply hsQwApply) {
        super.updateStatus(hsQwApply);
    }

    /**
     * 删除数据
     *
     * @param hsQwApply
     */
    @Override
    @Transactional
    public void delete(HsQwApply hsQwApply) {
        super.delete(hsQwApply);
        HsQwApplyer hsQwApplyer = new HsQwApplyer();
        hsQwApplyer.setApplyId(hsQwApply.getId());
        hsQwApplyerDao.deleteByEntity(hsQwApplyer);
        HsQwApplyHouse hsQwApplyHouse = new HsQwApplyHouse();
        hsQwApplyHouse.setApplyId(hsQwApply.getId());
        hsQwApplyHouseDao.deleteByEntity(hsQwApplyHouse);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 过程方法集合
        Map<String, HsQwApplyProcess> beanMap = applicationContext.getBeansOfType(HsQwApplyProcess.class);
        beanMap.forEach((k, v) -> processMap.put(v.getFormKey() + "_" + v.getStatus(), v));
        // 资格审查方法集合
        Map<String, HsQwApplyCheck> beanCheckMap = applicationContext.getBeansOfType(HsQwApplyCheck.class);
        beanCheckMap.forEach((k, v) -> checkMap.put(v.getApplyType(), v));
        // 已申请单稽核
        Map<String, HsQwApplyedList> beanApplyedMap = applicationContext.getBeansOfType(HsQwApplyedList.class);
        beanApplyedMap.forEach((k, v) -> applyedListMap.put(v.getDataType(), v));
        // 状态更新方法集合
        Map<String, HsQwApplyStatus> beanApplyStatusMap = applicationContext.getBeansOfType(HsQwApplyStatus.class);
        beanApplyStatusMap.forEach((k, v) -> applyStatusMap.put(v.getApplyMater(), v));
        // 资格核验方法集合
        Map<String, HsQwApplyRuleMethod> beanApplyRuleMap = applicationContext
                .getBeansOfType(HsQwApplyRuleMethod.class);
        beanApplyRuleMap.forEach((k, v) -> applyRuleMap.add(v));
    }

    /**
     * 获取"审批待办"界面所有状态的待办任务
     * 申请单状态包含：
     * • 1待单位初审
     * • 2待机关经办初审
     * • 3待机关处室初审
     * • 4初审完成
     * • 8待配租复查单位审批
     * • 9待配租复查机关经办审批
     * • 17配租方案完成
     * • 19待申请人配租确认
     * • 20待签合同
     *
     * @param hsQwApply
     * @return
     */
    public Page<HsQwApply> findAuditPageByTask(HsQwApply hsQwApply) {
        // 审批待办中的状态过滤
        String[] status = new String[] {
                HsQwApply.APPLY_STATUS_AUDIT_UNIT_FIRST,
                HsQwApply.APPLY_STATUS_AUDIT_ORGHAND_FIRST,
                // HsQwApply.APPLY_STATUS_FIRST_PUBLIC_WAIT,
                // HsQwApply.APPLY_STATUS_AUDIT_ORG_FIRST_COMPLETE,
                HsQwApply.APPLY_STATUS_AUDIT_ORGOFFICE_FIRST,
                HsQwApply.APPLY_STATUS_AUDIT_UNIT_CHECK,
                // HsQwApply.APPLY_STATUS_FIRST_PUBLIC_COMPLETE,
                HsQwApply.APPLY_STATUS_AUDIT_ORGHAND_CHECK,
                HsQwApply.APPLY_STATUS_OPTION_COMPLETE,
                // HsQwApply.APPLY_STATUS_AUDIT_CHECK_COMPLETE,
                // HsQwApply.APPLY_STATUS_AUDIT_USER_RENT,
                HsQwApply.APPLY_STATUS_AUDIT_COMPACT,
                HsQwApply.APPLY_STATUS_AUDIT_HOUSE_RENT,
                HsQwApply.APPLY_STATUS_AUDIT_COMPACT_CHECK,
                HsQwApply.APPLY_STATUS_STOP_RENT_AUDIT,
                HsQwApply.APPLY_STATUS_STOP_RENT
        };
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        return this.findApplyPageByTaskNew(hsQwApply, status, "1", "rent_apply");
    }

    public Page<HsQwApply> findApplyPageByTask(HsQwApply hsQwApply, String[] status, String bpmStatus, String formKey) {
        return hsBpmService.findApplyPageByTask(hsQwApply, status, bpmStatus, formKey);
    }

    public Page<HsQwApply> findApplyPageByTaskNew(HsQwApply hsQwApply, String[] status, String bpmStatus,
            String formKey) {
        // 根据applyScoreLte和applyScoreGte来进行过滤
        if (hsQwApply.getOrderBy() == null) {
            hsQwApply.getPage().setOrderBy("a.apply_score desc,a.priority_order desc");
        }
        if (hsQwApply.getApplyScoreGte() != null) {
            hsQwApply.sqlMap().getWhere().and("a.apply_score", QueryType.GTE, hsQwApply.getApplyScoreGte());
        }
        if (hsQwApply.getApplyScoreLte() != null) {
            hsQwApply.sqlMap().getWhere().and("a.apply_score", QueryType.LTE, hsQwApply.getApplyScoreLte());
        }
        // hsQwApply.getPage().setOrderBy("a.apply_score desc,a.priority_order desc");
        return commonBpmService.findTaskList(status, formKey, hsQwApply, bpmStatus);
    }

    public Page<HsQwApply> findMztPage(HsQwApply hsQwApply, String formKey) {
        return hsBpmService.findMztPage(hsQwApply, formKey);
    }

    public void updateApplyTime(HsQwApply hsQwApply) {
        hsQwApply.setApplyTime(new Date());
        this.update(hsQwApply);
    }

    public void updateHouseStatus(String houseId, String status) {
        HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
        query.setId(houseId);
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setStatus_in(new String[] { HsQwApply.STATUS_NORMAL, HsQwApply.STATUS_AUDIT });
        query.sqlMap().getOrder().setOrderBy("ha.apply_matter,ha.create_date DESC");
        HsQwPublicRentalHouse hsQwApplyHouse = hsQwPublicRentalHouseService.findList(query).stream().findFirst()
                .orElse(null);
        if (hsQwApplyHouse == null) {
            return;
        }
        hsQwApplyHouse.setHouseStatus(status);
        hsQwPublicRentalHouseService.update(hsQwApplyHouse);
        // 更新房源状态为正常
        hsQwApplyHouse.setStatus(HsQwApplyHouse.STATUS_NORMAL);
        hsQwPublicRentalHouseService.updateStatus(hsQwApplyHouse);
    }

    public void applyCheck(HsQwApply hsQwApply) {
        if (!hsQwApply.getIsNewRecord() && hsQwApply.getStatus().equals(HsQwApply.STATUS_AUDIT)) {
            return;
        }
        // 自定义流程操作
        HsQwApplyCheck check = checkMap.get(hsQwApply.getApplyMatter());
        if (check == null) {
            throw new ServiceException("申请资格验证失败，存在空的类型检查方法！");
        }
        check.execute(hsQwApply);
    }

    public HsQwApply getUserApplyInfo(String applyMatter, HsQwApply request) {
        // 申请校验
        HsQwApply check = new HsQwApply();
        check.setApplyMatter(applyMatter);
        check.setProxyUserId(request.getProxyUserId());
        this.applyCheck(check);
        // 获取历史单
        return this.getUserApplyInfoNullCheck(applyMatter, request);
    }

    @Transactional
    public HsQwApply getUserApplyInfoNullCheck(String applyMatter, HsQwApply request) {
        // 获取历史单
        return this.generateReplaceForm(this.getOldApplyInfo(applyMatter, request), request, applyMatter);
    }

    public HsQwApply getOldApplyInfo(String applyMatter, HsQwApply hsQwApply) {
        // 获取历史申请单
        HsQwApplyer query = new HsQwApplyer();
        query.setUserId(this.getRealApplyUser(hsQwApply));
        query.setHsQwApply(new HsQwApply());
        if (applyMatter.equals(HsQwApply.APPLY_MATTER_INFO)) {
            query.getHsQwApply().setStatus_in(new String[] { HsQwApply.STATUS_NORMAL, HsQwApply.STATUS_AUDIT });
        } else {
            query.getHsQwApply().setStatus(HsQwApply.STATUS_NORMAL);
        }
        query.setApplyRole("0");// 检索主申请人
        query.getHsQwApply().sqlMap().getWhere().and("apply_matter", QueryType.IN, new String[] { "0" });// 只选择公租房申请单
        return this.get(hsQwApplyerDao.findList(query).stream().findFirst()
                .orElseThrow(() -> new ServiceException("很抱歉，您没有已申请的公租房申请")).getApplyId());
    }

    private String getRealApplyUser(HsQwApply hsQwApply) {
        if(hsQwApply == null) {
            return UserUtils.getUser().getUserCode();
        }
        if (hsQwApply.getProxyUserId() != null) {
            return hsQwApply.getProxyUserId();
        }
        return UserUtils.getUser().getUserCode();
    }

    /**
     * 生成或者查询返回相应申请单的表单信息
     *
     * @param result
     * @param request
     * @param applyMatter
     * @return
     */
    private HsQwApply generateReplaceForm(HsQwApply result, HsQwApply request, String applyMatter) {
        // 检索申请中或者草稿中的申请单
        HsQwApply query = new HsQwApply();
        query.sqlMap().getWhere().and("status", QueryType.IN,
                new String[] { HsQwApply.STATUS_AUDIT, HsQwApply.STATUS_DRAFT });
        query.setApplyMatter(applyMatter);
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.sqlMap().getWhere().and("o.user_id", QueryType.IN, this.getRealApplyUser(request));
        List<HsQwApply> beans = this.findList(query);
        // 若存在申请中或者草稿的申请单，直接返回表单
        HsQwApply hasResult = this.hasStatusBean(beans, HsQwApply.STATUS_AUDIT);
        if (hasResult != null) {
            return this.get(hasResult.getId());
        }
        HsQwApply process = this.hasStatusBean(beans, HsQwApply.STATUS_DRAFT);
        if (process != null) {
            return this.get(process.getId());
        }
        // 不存在则执行深拷贝
        return this.copyExistApplyForm(result, applyMatter);
    }

    /**
     * 进行全量的申请单深拷贝
     *
     * @param result
     * @param applyMatter
     */
    private HsQwApply copyExistApplyForm(HsQwApply result, String applyMatter) {
        // 申请单拷贝
        HsQwApply replaceForm = this.resetForm(result, applyMatter);
        // 申请者拷贝
        this.generateReplaceApplyers(replaceForm, result);
        // 房产信息拷贝
        this.generateReplaceHouses(replaceForm, result);
        // 合同信息拷贝
        if (applyMatter.equals("1")) {
            // 信息变更的合同也要拷贝
            this.generateCompact(replaceForm, result);
        }
        // 申请附件拷贝
        this.generateReplaceFiles(replaceForm, result);
        return replaceForm;
    }

    private void generateCompact(HsQwApply replaceForm, HsQwApply result) {
        HsQwCompact query = new HsQwCompact();
        query.setStatus(HsQwApplyer.STATUS_NORMAL);
        query.setApplyId(result.getId());
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        HsQwCompact compact = hsQwCompactService.findList(query).stream().findFirst().orElse(null);
        if (compact != null) {
            compact.setApplyId(replaceForm.getId());
            compact.setId(null);
            compact.setStatus("4");// 流程中
            hsQwCompactService.save(compact);
        }
    }

    private HsQwApply hasStatusBean(List<HsQwApply> beans, String statusNormal) {
        if (beans == null)
            return null;
        for (int i = 0; i < beans.size(); i++) {
            HsQwApply apply = beans.get(i);
            if (apply.getStatus().equals(statusNormal)) {
                return apply;
            }
        }
        return null;
    }

    private void generateReplaceFiles(HsQwApply replaceForm, HsQwApply result) {
        FileUpload query = new FileUpload();
        query.setBizKey(result.getId());
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        List<FileUpload> list = FileUploadUtils.getFileUploadService().findList(query);
        list.stream().forEach(k -> {
            k.setId(null);
            k.setBizKey(replaceForm.getId());
            k.setCreateBy(null);
            k.setCreateDate(null);
        });
        if (!list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                fileUploadDao.insert(list.get(i));
            }
        }
    }

    private void generateReplaceHouses(HsQwApply replaceForm, HsQwApply result) {
        HsQwApplyHouse query = new HsQwApplyHouse();
        query.setStatus(HsQwApplyer.STATUS_NORMAL);
        query.setApplyId(result.getId());
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        List<HsQwApplyHouse> list = hsQwApplyHouseDao.findList(query);
        list.stream().forEach(k -> {
            k.setId(null);
            k.setApplyId(replaceForm.getId());
            k.setCreateBy(null);
            k.setCreateDate(null);
        });
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                hsQwApplyHouseDao.insert(list.get(i));
            }
            replaceForm.setHsQwApplyHouseList(list);
        }
    }

    private void generateReplaceApplyers(HsQwApply replaceForm, HsQwApply result) {
        HsQwApplyer query = new HsQwApplyer();
        query.setStatus(HsQwApplyer.STATUS_NORMAL);
        query.setApplyId(result.getId());
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        List<HsQwApplyer> list = hsQwApplyerDao.findList(query);
        list.stream().forEach(k -> {
            k.setId(null);
            k.setApplyId(replaceForm.getId());
            k.setCreateBy(null);
            k.setCreateDate(null);
        });
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                hsQwApplyerDao.insert(list.get(i));
            }
            replaceForm.setHsQwApplyerList(list);
        }
    }

    private HsQwApply resetForm(HsQwApply result, String applyMatter) {
        HsQwApply bean = new HsQwApply();
        bean.setApplyedId(result.getId());
        bean.setOfficeCode(result.getOfficeCode());
        bean.setOffice(result.getOffice());
        bean.setFamilyPeoples(result.getFamilyPeoples());
        bean.setFamilyIncome(result.getFamilyIncome());
        bean.setApplyMatter(applyMatter);
        bean.setRemarks(result.getRemarks());
        bean.setStatus(HsQwApply.STATUS_DRAFT);
        bean.setAvgIncome(result.getAvgIncome());
        bean.setAvgArea(result.getAvgArea());
        bean.setApplyScore(result.getApplyScore());
        if (applyMatter.equals("1")) {
            // 个人信息变更，房源不变更
            bean.setHouseId(result.getHouseId());
        }
        super.save(bean);
        return bean;
    }

    /**
     * 获取已经成功配租的申请单
     *
     * @param hsQwApply
     * @return
     */
    public Page<HsQwApply> getApplyedPage(HsQwApply hsQwApply) {
        // 自定义流程操作
        HsQwApplyedList applyedListMethod = applyedListMap.get(hsQwApply.getDataType());
        if (applyedListMethod == null) {
            throw new ServiceException("获取已申请单列表失败，数据类型：" + hsQwApply.getDataType());
        }
        return applyedListMethod.execute(hsQwApply, this);
    }

    public HsQwApply getByApplyedHouse(String houseId) {
        HsQwApply query = new HsQwApply();
        query.setHouseId(houseId);
        query.setStatus(HsQwApply.STATUS_NORMAL);
        return this.findList(query).stream().findFirst().orElse(null);
    }

    public void invalidApplyInfo(HsQwApply history) {
        history.setStatus(HsQwApply.STATUS_DISABLE);
        this.realUpdateStatus(history);
        // //申请人信息失效
        // this.invalidApplyer(history.getId());
        // //申请房源信息失效
        // this.invalidApplyHouse(history.getId());
        // 配租房源状态更新
        this.resetRentalHouse(history.getHouseId());
        // 原合同失效
        this.invalidApplyCompact(history.getId(), HsQwCompact.STATUS_DISABLE);
    }

    /**
     * @param id
     * @description 合同删除动作，不可以作为合同续签失效旧合同操作
     * <AUTHOR>
     * @date 2025/4/12 14:48
     */
    public void deleteApplyCompact(String id) {
        this.invalidApplyCompact(id, HsQwCompact.STATUS_DELETE);
    }

    /**
     * 合同状态变更
     *
     * @param id
     * @param status
     */
    public void invalidApplyCompact(String id, String status) {
        HsQwCompact query = new HsQwCompact();
        query.setApplyId(id);
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        HsQwCompact compact = hsQwCompactService.findList(query).stream().findFirst().orElse(null);
        if (compact == null) {
            return;
        }
        compact.setStatus(status);
        hsQwCompactService.updateStatus(compact);
    }

    private void resetRentalHouse(String houseId) {
        this.updateHouseStatus(houseId, "0");
    }

    public void invalidApplyer(String id) {
        HsQwApply query = new HsQwApply();
        HsQwApplyer entity = new HsQwApplyer();
        HsQwApplyer where = new HsQwApplyer();
        entity.setStatus(HsQwApplyer.STATUS_DISABLE);
        where.setApplyId(id);
        hsQwApplyerDao.updateStatusByEntity(entity, where);
    }

    public void invalidApplyHouse(String id) {
        HsQwApplyHouse entity = new HsQwApplyHouse();
        HsQwApplyHouse where = new HsQwApplyHouse();
        entity.setStatus(HsQwApplyer.STATUS_DISABLE);
        where.setApplyId(id);
        hsQwApplyHouseDao.updateStatusByEntity(entity, where);
    }

    public void invalidBatchRelateApply(String id) {
        HsQwApply where = new HsQwApply();
        HsQwApply entity = new HsQwApply();
        where.setApplyedId(id);
        entity.setStatus(HsQwApply.STATUS_DISABLE);
        this.dao.updateStatusByEntity(entity, where);
    }

    public void applyRuleCheck(HsQwApply hsQwApply, Map<String, HsQwFormAlarm> hsQwFormAlarmMap) {
        for (HsQwApplyRuleMethod rule : applyRuleMap) {
            rule.execute(hsQwApply, hsQwFormAlarmMap);
        }

    }

    public void applyModifyCheck(HsQwApply hsQwApply, Map<String, HsQwFormAlarm> hsQwFormAlarmMap) {
        // 获取历史单
        HsQwApply history = this.get(hsQwApply.getId());
        // 获取所有文件列表，并转换为dataMap
        List<FileUpload> fileUploadList = this.getFileUploadList(hsQwApply.getId(), null);
        Map<String, Object> dataMap = new HashMap<>();
        fileUploadList.stream().forEach(k -> {
            dataMap.put(k.getBizType(), k.getId());
        });
        history.setDataMap(dataMap);
        // 获取历史单和新对象之间不同的属性
        Map<String, String> maps = ObjectCompareUtil.compareObjects(history, hsQwApply, "hsQwApply",
                "sqlMap", "createData", "changeMap", "bpm", "processName");
        // if (true){
        // throw new ServiceException("d");
        // }
        // 将maps中的key，value，生成 List<HsQwFormAlarm>，然后批量插入数据库
        maps.forEach((k, v) -> {
            HsQwApplyRuleMethod.putAlarmMap(hsQwFormAlarmMap, k, v, "1", hsQwApply.getId());
        });
    }

    private List<FileUpload> getFileUploadList(String id, String bizType) {
        FileUpload query = new FileUpload();
        query.setBizKey(id);
        if (bizType != null) {
            query.setBizType(bizType);
        }
        return FileUploadUtils.getFileUploadService().findList(query);
    }

    public void refreshByApply(HsQwApply hsQwApply) {
        hsQwApplyRuleService.refreshScore(hsQwApply);
        hsQwApply.setScoreUpdate("0");
        this.update(hsQwApply);
    }

    public List<HsQwApplyExport> findExportList(HsQwApply hsQwApply) {
        Page<HsQwApply> pageList = this.findApplyPageByTaskNew(hsQwApply, null, "1", "rent_apply");
        return this.findExportList(pageList);
    }

    public List<HsQwApplyExport> findExportListSelected(HsQwApply hsQwApply) {
        Page<HsQwApply> pageList = this.findApplyPageByTaskNew(hsQwApply, null, "1", "rent_apply");
        return this.findExportList(pageList);
    }

    public List<HsQwApplyExport> findExportList(Page<HsQwApply> pageList) {
        if (pageList == null || pageList.getList() == null || pageList.getList().isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 收集所有申请ID
        List<String> applyIds = pageList.getList().stream()
                .map(HsQwApply::getId)
                .collect(Collectors.toList());

        // 3. 批量查询申请人信息
        HsQwApplyer query = new HsQwApplyer();
        query.setStatus(HsQwApplyer.STATUS_NORMAL);
        query.sqlMap().getWhere().and("apply_id", QueryType.IN, applyIds.toArray(new String[0]));
        List<HsQwApplyer> allApplyers = hsQwApplyerDao.findList(query);

        // 4. 按申请ID分组申请人信息
        Map<String, List<HsQwApplyer>> applyerMap = allApplyers.stream()
                .collect(Collectors.groupingBy(HsQwApplyer::getApplyId));

        // 5. 构建导出列表
        List<HsQwApplyExport> exportList = new ArrayList<>();
        for (int i = 0; i < pageList.getList().size(); i++) {
            HsQwApplyExport export = new HsQwApplyExport();
            HsQwApply apply = pageList.getList().get(i);
            export.setId(apply.getId());
            export.setOrder(String.valueOf(i + 1));
            export.setApplyTime(apply.getApplyTime());
            export.setApplyScore(apply.getApplyScore());
            export.setRemarks(apply.getRemarks());
            // 获取当前申请的所有申请人
            List<HsQwApplyer> applyers = applyerMap.getOrDefault(apply.getId(), new ArrayList<>());
            // 处理申请人信息
            for (HsQwApplyer applyer : applyers) {
                if ("0".equals(applyer.getApplyRole())) {
                    export.setMainApplyer(applyer);
                } else if (export.getSecondApplyer() == null) {
                    export.setSecondApplyer(applyer);
                } else if (export.getOther1Name() == null) {
                    export.setOther1Name(applyer.getName());
                } else if (export.getOther2Name() == null) {
                    export.setOther2Name(applyer.getName());
                } else if (export.getOther3Name() == null) {
                    export.setOther3Name(applyer.getName());
                }
            }

            exportList.add(export);
        }
        return exportList;
    }

    public void updateScoreDetail(HsQwApply hsQwApply) {
        // 根据前端传入的applyId，scoreDetails进行更
        // 计算轮候得分
        HsQwApplyScoreDetail query = new HsQwApplyScoreDetail();
        query.setApplyId(hsQwApply.getId());
        List<HsQwApplyScoreDetail> list = hsQwApplyScoreDetailService.findList(query);
        if (list != null && list.size() > 0) {
            // 和scoreDetails进行对比，如果scoreDetails中存在，则更新.重新计算总分
            hsQwApply.getScoreDetails().stream().forEach(k -> {
                HsQwApplyScoreDetail hsQwApplyScoreDetail = list.stream().filter(l -> l.getId().equals(k.getId()))
                        .findFirst().orElse(null);
                if (hsQwApplyScoreDetail != null) {
                    hsQwApplyScoreDetail.setScore(k.getScore());
                    hsQwApplyScoreDetail.setRemarks(k.getRemarks());
                    hsQwApplyScoreDetailService.update(hsQwApplyScoreDetail);
                }
            });
            hsQwApply.setApplyScore(list.stream().filter(k -> k.getScore() != null)
                    .mapToDouble(HsQwApplyScoreDetail::getScore).sum());
        }
        hsQwApply.setScoreUpdate("1");
        this.update(hsQwApply);
    }

    public void invalidApplyRentalHouse(String id) {
        // 查询申请单
        HsQwApply apply = this.get(id);
        if (StringUtils.isNotBlank(apply.getHouseId())) {
            // 申请单中的houseId对应的房源状态修改为待配租
            hsQwPublicRentalHouseService.updateHouseStatus(apply.getHouseId());
            // 申请单中的houseId进行置空
            apply.setHouseId("");
            this.update(apply);
        }
    }

    public List<HsQwApplyAllExport> findExportAllList(HsQwApply hsQwApply) {
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        List<HsQwApply> list = this.findList(hsQwApply);
        List<HsQwApplyAllExport> exportList = new ArrayList<>();
        list.forEach(k -> {
            HsQwApplyAllExport export = new HsQwApplyAllExport();
            BeanUtils.copyProperties(k, export);
            exportList.add(export);
        });
        return exportList;
    }

    public Page<HsQwApply> findPageByQuery(HsQwApply hsQwApply) {
        // 审批状态，多选情况下
        if (hsQwApply.getStatus().indexOf(",") > -1) {
            hsQwApply.setStatus_in(hsQwApply.getStatus().split(","));
            hsQwApply.setStatus("");
            hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        }
        // 合同状态
        if (hsQwApply.getCompactSign().equals("0")) {
            hsQwApply.sqlMap().getWhere().and("m.id", QueryType.IS_NULL, null);
        } else if (hsQwApply.getCompactSign().equals("1")) {
            hsQwApply.sqlMap().getWhere().and("m.id", QueryType.IS_NOT_NULL, null);
        }
        return commonBpmService.findObjectList(null, "rent_apply%", hsQwApply, null);
        // return super.findPage(hsQwApply);
    }

    public void enableGreenApply(HsQwApply hsQwApply) {
        HsQwPublicRentalHouse house = hsQwApply.getHsQwApplyHouse();
        if (!(house.getStatus().equals(HsQwPublicRentalHouse.STATUS_NORMAL)
                || house.getStatus().equals(HsQwPublicRentalHouse.STATUS_AUDIT))) {
            throw new ServiceException("恢复配租失败，该房源已失效！");
        }
        if (house.getStatus().equals(HsQwPublicRentalHouse.STATUS_AUDIT)
                || (house.getStatus().equals(HsQwPublicRentalHouse.STATUS_NORMAL)
                        && house.getHouseStatus().equals("1"))) {
            throw new ServiceException("恢复配租失败，该房源已配租或者处于配租中，请重新发起绿色配置申请！");
        }
        this.updateHouseStatus(hsQwApply.getHouseId(), "1");// 恢复已配租
    }

    public void genHouseSelectOrder(HsQwApply hsQwApply, HttpServletResponse response) {
        HsQwApplyer mainApplyer = hsQwApply.getMainApplyer();
        HsQwPublicRentalHouse house = hsQwApply.getHsQwApplyHouse();

        try (OutputStream out = response.getOutputStream()) {

            // 使用POI方式导出数据到word中文档中
            XWPFDocument document = this.generateWordToFile(hsQwApply);
            // 设置响应头和字符编码
            response.setContentType(
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8");
            String fileName = "公租房选房确认单_" + DateUtils.getDate("yyyyMMddHHmmss") + ".docx";
            // 对文件名进行编码处理
            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 写入文档
            document.write(out);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void insertBlankLine(XWPFDocument document) {
        // 换行
        XWPFParagraph lineParagraph = document.createParagraph();
        lineParagraph.setAlignment(ParagraphAlignment.RIGHT);
        XWPFRun lineRun = lineParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        lineRun.setText("");
        lineRun.setFontFamily("华文细黑");
        lineRun.setFontSize(12);
    }

 
    /**
     * 批量生成配租确认单并打包成ZIP
     * @param applyIdStr 申请ID字符串，多个ID用逗号分隔
     * @param response HTTP响应对象
     */
    public void genHouseSelectOrderZip(String applyIdStr, HttpServletResponse response) {
        if (StringUtils.isBlank(applyIdStr)) {
            throw new ServiceException("申请ID不能为空");
        }
        
        ZipOutputStream zos = null;
        try {
            // 分割申请ID字符串
            String[] applyIds = applyIdStr.split(",");
            if (applyIds.length == 0) {
                throw new ServiceException("申请ID不能为空");
            }
            
            // 创建临时目录
            String tempDir = System.getProperty("java.io.tmpdir") + "/house_select_order_" + System.currentTimeMillis();
            File dir = new File(tempDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // 生成ZIP文件名
            String zipFileName = "配租确认单_" + DateUtils.getDate("yyyyMMddHHmmss") + ".zip";
            
            // 设置响应头
            response.reset(); // 重置响应
            response.setContentType("application/octet-stream"); // 使用通用二进制流类型
            response.setHeader("Content-Disposition", "attachment; filename=" + 
                            URLEncoder.encode(zipFileName, "UTF-8"));
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expires", 0);
            
            // 创建ZIP输出流
            zos = new ZipOutputStream(response.getOutputStream());
            
            // 处理每个申请ID
            for (String applyId : applyIds) {
                if (StringUtils.isBlank(applyId)) {
                    continue;
                }
                
                // 获取申请信息
                HsQwApply apply = get(applyId);
                if (apply == null) {
                    logger.warn("申请信息不存在：" + applyId);
                    continue;
                }
                
                // 生成Word文件名
                String wordFileName = "配租确认单_" + apply.getId();
                if (apply.getMainApplyer() != null) {
                    wordFileName += "_" + apply.getMainApplyer().getName();
                }
                wordFileName += ".docx";
                
                // 创建临时文件
                File wordFile = new File(tempDir, wordFileName);
                
                try (FileOutputStream out = new FileOutputStream(wordFile)) {
                    // 使用POI方式导出数据到word中文档中
                    XWPFDocument document = this.generateWordToFile(apply);
                    
                    // 写入文档
                    document.write(out);
                } catch (IOException e) {
                    logger.error("生成Word文档失败", e);
                    continue; // 继续处理下一个文件
                }
                
                // 添加到ZIP文件
                try (FileInputStream fis = new FileInputStream(wordFile)) {
                    ZipEntry zipEntry = new ZipEntry(wordFileName);
                    zos.putNextEntry(zipEntry);
                    
                    byte[] buffer = new byte[4096]; // 增大缓冲区
                    int length;
                    while ((length = fis.read(buffer)) > 0) {
                        zos.write(buffer, 0, length);
                    }
                    
                    zos.closeEntry();
                } catch (IOException e) {
                    logger.error("添加文件到ZIP失败", e);
                }
                
                // 删除临时文件
                wordFile.delete();
            }
            
            // 确保ZIP输出流正确关闭
            zos.finish();
            zos.flush();
            
            // 删除临时目录
            deleteDirectory(dir);
            
        } catch (Exception e) {
            logger.error("批量生成配租确认单失败", e);
            throw new ServiceException("批量生成配租确认单失败：" + e.getMessage());
        } finally {
            // 确保ZIP输出流关闭
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    logger.error("关闭ZIP输出流失败", e);
                }
            }
        }
    }

    /**
     * 递归删除目录
     * @param directory 要删除的目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

    /**
     * 生成Word文档到文件
     * @param hsQwApply 申请信息
     * @throws IOException IO异常
     */
    private XWPFDocument generateWordToFile(HsQwApply hsQwApply){
        HsQwApplyer mainApplyer = hsQwApply.getMainApplyer();
        HsQwPublicRentalHouse house = hsQwApply.getHsQwApplyHouse();
        XWPFDocument document = new XWPFDocument();
        // 使用POI方式导出数据到word中文档中
        // 创建标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        titleRun.setText("福建省省直单位公共租赁住房");
        titleRun.setFontFamily("黑体");
        titleRun.setFontSize(22);

        // 创建副标题
        XWPFParagraph subtitleParagraph = document.createParagraph();
        subtitleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun subtitleRun = subtitleParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        subtitleRun.setText("选房确认单");
        subtitleRun.setFontFamily("华文细黑");
        subtitleRun.setFontSize(16);

        // 创建副标题
        XWPFParagraph subtitleParagraph1 = document.createParagraph();
        subtitleParagraph1.setAlignment(ParagraphAlignment.RIGHT);
        XWPFRun subtitleRun1 = subtitleParagraph1.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        subtitleRun1.setText("No:" + DateUtils.getDate("yyyyMMddHHmmssms"));
        subtitleRun1.setFontFamily("仿宋");
        subtitleRun1.setFontSize(12);

        // 插入空行
        this.insertBlankLine(document);
        // 插入空行
        this.insertBlankLine(document);

        // 承租人信息
        XWPFParagraph nameParagraph = document.createParagraph();
        nameParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun nameRun = nameParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        nameRun.setText("承租人姓名：" + mainApplyer.getName() + "            身份证号码：" + mainApplyer.getIdNum());
        nameRun.setFontFamily("华文细黑");
        nameRun.setFontSize(12);

        // 插入空行
        this.insertBlankLine(document);

        // 房屋信息
        XWPFParagraph houseParagraph = document.createParagraph();
        houseParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun houseRun = houseParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        houseRun.setText("房号：" + house.getSimpleInfo());
        houseRun.setFontFamily("华文细黑");
        houseRun.setFontSize(12);

        // 插入空行
        this.insertBlankLine(document);
        // 插入空行
        this.insertBlankLine(document);

        // 签字信息
        XWPFParagraph signParagraph = document.createParagraph();
        signParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun signRun = signParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        signRun.setText("承租人（受托人）确认（签字并按指印）");
        signRun.setFontFamily("华文细黑");
        signRun.setFontSize(12);
        // 插入空行
        this.insertBlankLine(document);

        // 注释
        XWPFParagraph commentParagraph = document.createParagraph();
        commentParagraph.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun commentRun = commentParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        commentRun.setText("注：签订租赁合同时需携带本确认单和身份证等其他有关材料");
        commentRun.setFontFamily("华文细黑");
        commentRun.setFontSize(12);

        this.insertBlankLine(document);

        // 盖章
        XWPFParagraph stampParagraph = document.createParagraph();
        stampParagraph.setAlignment(ParagraphAlignment.RIGHT);
        XWPFRun stampRun = stampParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        stampRun.setText("福建省机关事务管理局");
        stampRun.setFontFamily("华文细黑");
        stampRun.setFontSize(12);

        // 换行
        this.insertBlankLine(document);

        // 日期
        XWPFParagraph dateParagraph = document.createParagraph();
        dateParagraph.setAlignment(ParagraphAlignment.RIGHT);
        XWPFRun dateRun = dateParagraph.createRun();
        // 修改字体为方正小标宋体，字号为二号（22 磅）
        dateRun.setText("年    月    日");
        dateRun.setFontFamily("华文细黑");
        dateRun.setFontSize(12);

        return document;
    }

    public List<HsQwApplyer> getCurrentUserInfo(HsQwApply hsQwApply) {
        List<HsQwApplyer> applyerList = new ArrayList<>();
        EmpUser user = this.getCurrentEmpUser();
        Office office = EmpUtils.getOffice();
        HsQwApplyer applyer = new HsQwApplyer();
        applyer.setName(user.getUserName());
        applyer.setIdNum(user.getIdNum());
        applyer.setOrganization(office.getOfficeName());
        applyer.setWorkTime(user.getEmployee().getJoinedDate());
        applyer.setUserId(user.getId());
        applyer.setPhone(user.getMobile());
        applyer.setApplyId(hsQwApply.getId());
        applyer.setApplyRole("0");
        applyerList.add(applyer);
        return applyerList;
    }

    private EmpUser getCurrentEmpUser() {
        EmpUser query = new EmpUser();
        query.setUserCode(UserUtils.getUser().getUserCode());
        EmpUser user = empUserService.get(query);
        return user;
    }
}
