package com.hsobs.hs.modules.applypublic.service;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.entity.HsQwApplyExport;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.applypublic.dao.HsQwApplyPublicDao;
import com.hsobs.hs.modules.applypublic.entity.HsQwApplyPublic;
import com.hsobs.hs.modules.applypublicdetail.dao.HsQwApplyPublicDetailDao;
import com.hsobs.hs.modules.applypublicdetail.entity.HsQwApplyPublicDetail;
import com.hsobs.hs.modules.applypublichouse.dao.HsQwApplyPublicHouseDao;
import com.hsobs.hs.modules.applypublichouse.entity.HsQwApplyPublicHouse;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.utils.WordUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.service.ApiSzpsService;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.DictUtils;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租赁资格轮候公示复查表Service
 *
 * <AUTHOR>
 * @version 2024-12-05
 */
@Service
public class HsQwApplyPublicService extends CrudService<HsQwApplyPublicDao, HsQwApplyPublic> {

    @Autowired
    private HsQwApplyPublicHouseDao hsQwApplyPublicHouseDao;

    @Autowired
    private HsQwApplyPublicDetailDao hsQwApplyPublicDetailDao;

    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private BpmTaskService bpmTaskService;

    @Autowired
    private HsQwApplyerService hsQwApplyerService;

    @Autowired
    private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

    @Autowired
    private CommonBpmService commonBpmService;

    @Autowired
    private ApiSzkjService apiSzkjService;

    @Autowired
    private EmpUserService empUserService;

    /**
     * 获取单条数据
     *
     * @param hsQwApplyPublic
     * @return
     */
    @Override
    public HsQwApplyPublic get(HsQwApplyPublic hsQwApplyPublic) {
        HsQwApplyPublic entity = super.get(hsQwApplyPublic);
        if (entity != null) {
            HsQwApplyPublicHouse hsQwApplyPublicHouse = new HsQwApplyPublicHouse();
            hsQwApplyPublicHouse.setPublicId(hsQwApplyPublic.getId());
            hsQwApplyPublicHouse.setStatus(HsQwApplyPublicHouse.STATUS_NORMAL);
            entity.setHsQwApplyPublicHouseList(hsQwApplyPublicHouseDao.findList(hsQwApplyPublicHouse));
            HsQwApplyPublicDetail hsQwApplyPublicDetail = new HsQwApplyPublicDetail();
            hsQwApplyPublicDetail.setPublicId(hsQwApplyPublic.getId());
            hsQwApplyPublicDetail.setStatus(HsQwApplyPublicDetail.STATUS_NORMAL);
            entity.setHsQwApplyPublicDetailList(hsQwApplyPublicDetailDao.findList(hsQwApplyPublicDetail));
            // 判断当前用户是否有审核权限
            entity.getHsQwApplyPublicDetailList().forEach(k -> {
                HsQwApply hsQwApply = new HsQwApply();
                hsQwApply.setId(k.getApplyId());
                BpmTask task = BpmUtils.getTask(hsQwApply, "rent_apply", UserUtils.getUser().getUserCode());
                if (task != null && task.getStatus().equals(BpmTask.STATUS_UNFINISHED)) {
                    entity.setHasAudit(true);
                }
            });
        }
        return entity;
    }

    /**
     * 查询分页数据
     *
     * @param hsQwApplyPublic 查询条件
     * @param hsQwApplyPublic page 分页对象
     * @return
     */
    @Override
    public Page<HsQwApplyPublic> findPage(HsQwApplyPublic hsQwApplyPublic) {
        hsQwApplyPublic.setOrderBy("a.status asc, a.public_date desc");
        return commonBpmService.findObjectList(null, "rent_apply_public%", hsQwApplyPublic, null);
//        return super.findPage(hsQwApplyPublic);
    }

    /**
     * 查询列表数据
     *
     * @param hsQwApplyPublic
     * @return
     */
    @Override
    public List<HsQwApplyPublic> findList(HsQwApplyPublic hsQwApplyPublic) {
        return super.findList(hsQwApplyPublic);
    }

    /**
     * 查询子表分页数据
     *
     * @param hsQwApplyPublicHouse
     * @param hsQwApplyPublicHouse page 分页对象
     * @return
     */
    public Page<HsQwApplyPublicHouse> findSubPage(HsQwApplyPublicHouse hsQwApplyPublicHouse) {
        Page<HsQwApplyPublicHouse> page = hsQwApplyPublicHouse.getPage();
        page.setList(hsQwApplyPublicHouseDao.findList(hsQwApplyPublicHouse));
        return page;
    }

    /**
     * 查询子表分页数据
     *
     * @param hsQwApplyPublicDetail
     * @param hsQwApplyPublicDetail page 分页对象
     * @return
     */
    public Page<HsQwApplyPublicDetail> findSubPage(HsQwApplyPublicDetail hsQwApplyPublicDetail) {
        Page<HsQwApplyPublicDetail> page = hsQwApplyPublicDetail.getPage();
        page.setList(hsQwApplyPublicDetailDao.findList(hsQwApplyPublicDetail));
        return page;
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param hsQwApplyPublic
     */
    @Override
    @Transactional
    public void save(HsQwApplyPublic hsQwApplyPublic) {
        boolean isStartRentAudit = false;
        if (StringUtils.isBlank(hsQwApplyPublic.getStatus())
                && hsQwApplyPublic.getPublicType().equals(HsQwApplyPublic.PUBLIC_TYPE_CHECK)) {
            hsQwApplyPublic.setStatus(HsQwApplyPublic.PUBLIC_STATUS_AUDIT);
        }
        if (hsQwApplyPublic.getPublicType().equals(HsQwApplyPublic.PUBLIC_TYPE_CHECK)) {
            isStartRentAudit = true;
        }
        super.save(hsQwApplyPublic);
        super.updateStatus(hsQwApplyPublic);
        // 保存公示附件
        this.savePublicFile(hsQwApplyPublic);
        // 保存 HsQwApplyPublic子表
        List<HsQwApply> hsQwApplyList = this.getPublicDetailList(hsQwApplyPublic);
        if (hsQwApplyPublic.getIsNewRecord()) {
            this.savePublicDetails(hsQwApplyPublic);
        }
        this.submitTask(hsQwApplyList, null);
        if (isStartRentAudit) {
            this.processRentApplyTask(hsQwApplyPublic);
        }
    }

    private void processRentApplyTask(HsQwApplyPublic hsQwApplyPublic) {
        // 如果流程实例为空，任务编号也为空，则：启动流程
        if (StringUtils.isBlank(hsQwApplyPublic.getBpm().getProcInsId())
                && StringUtils.isBlank(hsQwApplyPublic.getBpm().getTaskId())) {
            BpmUtils.start(hsQwApplyPublic, "rent_apply_public", null, null);
        }
        // 如果有任务信息，则：提交任务
        else {
            BpmUtils.complete(hsQwApplyPublic, null, null);
        }
    }

    private void cancleTask(List<HsQwApply> hsQwApplyList) {
        // 相关申请单任务自动批量取消
        hsQwApplyList.forEach(k -> {
            // 执行任务提交
            BpmTask task = BpmUtils.getTask(k, "rent_apply", UserUtils.getUser().getUserCode());
            this.initBpm(k, task);
            // 指定流程变量，作为流程条件，决定流转方向
            Map<String, Object> variables = MapUtils.newHashMap();
            variables.put("officeCode", k.getOfficeCode());
            BpmUtils.getBpmTaskService().claimTask(task);
            BpmUtils.complete(k, variables, null);
            // 执行申请单入轮候表时间
            hsQwApplyService.updateApplyTime(k);
        });
    }

    private void submitTask(List<HsQwApply> hsQwApplyList, String userCode) {
        // 相关申请单任务自动批量提交
        hsQwApplyList.forEach(k -> {
            // 执行任务提交
            BpmTask task = BpmUtils.getTask(k, "rent_apply",
                    StringUtils.isBlank(userCode) ? UserUtils.getUser().getUserCode() : userCode);
            this.initBpm(k, task);
            // 指定流程变量，作为流程条件，决定流转方向
            Map<String, Object> variables = MapUtils.newHashMap();
            variables.put("officeCode", k.getOfficeCode());
            BpmUtils.getBpmTaskService().claimTask(task);
            BpmUtils.complete(k, variables, null);
            // 执行申请单入轮候表时间
            hsQwApplyService.updateApplyTime(k);
        });
    }

    private void savePublicFile(HsQwApplyPublic hsQwApplyPublic) {
        // 保存上传附件
        FileUploadUtils.saveFileUpload(hsQwApplyPublic, hsQwApplyPublic.getId(), "hsQwApplyPublic_file");
        // 保存 HsQwApplyPublic子表
        for (HsQwApplyPublicHouse hsQwApplyPublicHouse : hsQwApplyPublic.getHsQwApplyPublicHouseList()) {
            if (!HsQwApplyPublicHouse.STATUS_DELETE.equals(hsQwApplyPublicHouse.getStatus())) {
                if (hsQwApplyPublicHouse.getIsNewRecord()) {
                    hsQwApplyPublicHouseDao.insert(hsQwApplyPublicHouse);
                } else {
                    hsQwApplyPublicHouseDao.update(hsQwApplyPublicHouse);
                }
            } else {
                hsQwApplyPublicHouseDao.delete(hsQwApplyPublicHouse);
            }
        }
    }

    private void savePublicDetails(HsQwApplyPublic hsQwApplyPublic) {
        // 保存 HsQwApplyPublic子表
        for (HsQwApplyPublicDetail hsQwApplyPublicDetail : hsQwApplyPublic.getHsQwApplyPublicDetailList()) {
            if (!HsQwApplyPublicDetail.STATUS_DELETE.equals(hsQwApplyPublicDetail.getStatus())) {
                if (hsQwApplyPublicDetail.getIsNewRecord()) {
                    hsQwApplyPublicDetailDao.insert(hsQwApplyPublicDetail);
                } else {
                    hsQwApplyPublicDetailDao.update(hsQwApplyPublicDetail);
                }
            } else {
                hsQwApplyPublicDetailDao.delete(hsQwApplyPublicDetail);
            }
        }
    }

    private void initBpm(HsQwApply hsQwApply, BpmTask task) {
        BpmParams bpm = hsQwApply.getBpm();
        bpm.setTaskId(task.getId());
        bpm.setProcInsId(task.getProcIns().getId());
        bpm.setNextUserCodes(task.getNextUserCodes());
        bpm.setActivityId(task.getActivityId());
        bpm.setDueDate(task.getDueDate());
        bpm.setComment(task.getComment());
    }

    private List<HsQwApply> getPublicDetailList(HsQwApplyPublic hsQwApplyPublic) {
        List<HsQwApply> hsQwApplyList = new ArrayList<>();
        List<HsQwApplyPublicDetail> detailList = new ArrayList<>();
        if (hsQwApplyPublic.getPids() == null) {
            StringBuffer sb = new StringBuffer();
            hsQwApplyPublic.getHsQwApplyPublicDetailList().forEach(k -> {
                sb.append(k.getApplyId()).append(",");
            });
            String pids = sb.toString().substring(0, sb.toString().lastIndexOf(","));
            hsQwApplyPublic.setPids(pids);
        }
        HsQwApply hsQwApply = new HsQwApply();
        hsQwApply.setId_in(hsQwApplyPublic.getPids().split(","));
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwApplyList = hsQwApplyService.findList(hsQwApply);
        hsQwApplyList.forEach(k -> {
            HsQwApplyPublicDetail detail = new HsQwApplyPublicDetail();
            detail.setApplyId(k.getId());
            detail.setPublicId(hsQwApplyPublic.getId());
            detail.setHsQwApply(k);
            // 获取家庭成员信息-应付可研文档，没有合理性
            StringBuilder familyNames = new StringBuilder();
            StringBuilder familyOrganization = new StringBuilder();
            HsQwApplyer query = new HsQwApplyer();
            query.setApplyId(k.getId());
            query.setStatus(HsQwApplyer.STATUS_NORMAL);
            List<HsQwApplyer> applyerList = hsQwApplyerService.findList(query);
            if (applyerList != null) {
                for (HsQwApplyer applyer : applyerList) {
                    if (applyer.getApplyRole() != null && !applyer.getApplyRole().equals("0")) {
                        if (familyNames.length() > 0) {
                            familyNames.append(",");
                        }
                        familyNames.append(applyer.getName());

                        if (familyOrganization.length() > 0) {
                            familyOrganization.append(",");
                        }
                        familyOrganization.append(applyer.getOrganization());
                    }
                }
            }
            detail.setFamilyNames(familyNames.toString());
            detail.setFamilyOrganization(familyOrganization.toString());
            detailList.add(detail);
        });
        hsQwApplyPublic.setHsQwApplyPublicDetailList(detailList);
        return hsQwApplyList;
    }

    /**
     * 更新状态
     *
     * @param hsQwApplyPublic
     */
    @Override
    @Transactional
    public void updateStatus(HsQwApplyPublic hsQwApplyPublic) {
        String publicType = "";
        HsQwApplyPublic aPublic = this.get(hsQwApplyPublic);
        if (aPublic == null) {
            publicType = HsQwApplyPublic.PUBLIC_TYPE_FIRST;
            HsQwApplyPublicDetail detail = new HsQwApplyPublicDetail();
            detail.setApplyId(hsQwApplyPublic.getId());
            detail.setOrderBy("a.create_date desc");
            List<HsQwApplyPublicDetail> hsDetails = hsQwApplyPublicDetailDao.findList(detail);
            if (hsDetails != null && !hsDetails.isEmpty()) {
                hsQwApplyPublic.setId(hsDetails.get(0).getPublicId());
                hsQwApplyPublic.setHsQwApplyPublicDetailList(hsDetails);
            } else {
                return;
            }
        } else {
            publicType = HsQwApplyPublic.PUBLIC_TYPE_CHECK;
            hsQwApplyPublic.setHsQwApplyPublicDetailList(aPublic.getHsQwApplyPublicDetailList());
        }
        if (hsQwApplyPublic.getStatus().equals(HsQwApplyPublic.PUBLIC_STATUS_COMPLETE)) {
            // 保存 HsQwApplyPublic子表
            List<HsQwApply> hsQwApplyList = this.getPublicDetailList(hsQwApplyPublic);
            if (publicType.equals(HsQwApplyPublic.PUBLIC_TYPE_CHECK)) {
                this.submitTask(hsQwApplyList, "admin");
            }
        }
        super.updateStatus(hsQwApplyPublic);
    }

    /**
     * 删除数据
     *
     * @param hsQwApplyPublic
     */
    @Override
    @Transactional
    public void delete(HsQwApplyPublic hsQwApplyPublic) {
        super.delete(hsQwApplyPublic);
        HsQwApplyPublicHouse hsQwApplyPublicHouse = new HsQwApplyPublicHouse();
        hsQwApplyPublicHouse.setPublicId(hsQwApplyPublic.getId());
        hsQwApplyPublicHouseDao.deleteByEntity(hsQwApplyPublicHouse);
        HsQwApplyPublicDetail hsQwApplyPublicDetail = new HsQwApplyPublicDetail();
        hsQwApplyPublicDetail.setApplyId(hsQwApplyPublic.getId());
        hsQwApplyPublicDetailDao.deleteByEntity(hsQwApplyPublicDetail);
    }

    public void initPublic(HsQwApplyPublic hsQwApplyPublic, String pids) {
        hsQwApplyPublic.setPids(pids);
        this.getPublicDetailList(hsQwApplyPublic);
    }

    public Page<HsQwApplyPublic> findAuditPageByTask(HsQwApplyPublic hsQwApplyPublic, String bpmStatus) {
        return commonBpmService.findTaskList(null, "rent_apply_public", hsQwApplyPublic , bpmStatus);
    }

    public void saveRent(HsQwApplyPublic hsQwApplyPublic) {
        super.save(hsQwApplyPublic);
        super.updateStatus(hsQwApplyPublic);
        // 保存公示附件
        this.savePublicFile(hsQwApplyPublic);
        this.processRentApplyTask(hsQwApplyPublic);
    }

    public void initHouses(HsQwApplyPublic hsQwApplyPublic) {
        if (hsQwApplyPublic.getPublicHouseIds() == null) {
            return;
        }
        hsQwApplyPublic.setPublicHouseNames(this.dao.getHouseInfoByIds(hsQwApplyPublic.getPublicHouseIds().split(",")));
    }

    /**
     * 根据公示单生成配置通知单-人员
     *
     * @param hsQwApplyPublic
     * @param response
     */
    public void exporRecheckNames(HsQwApplyPublic hsQwApplyPublic, HttpServletResponse response) {
        this.exporRecheckNames(hsQwApplyPublic, response, null);
    }
    /**
     * 根据公示单生成配置通知单-人员
     *
     * @param hsQwApplyPublic
     * @param response
     */
    public String exporRecheckNames(HsQwApplyPublic hsQwApplyPublic, HttpServletResponse response, String filePath) {
        // 查询申请单列表
        List<HsQwApplyPublicDetail> hDetails = hsQwApplyPublic.getHsQwApplyPublicDetailList();
        // 获取所有的申请单id
        List<String> ids = new ArrayList<>();
        hDetails.forEach(k -> ids.add(k.getApplyId()));
        // 查询所有的申请单信息
        HsQwApply hsQwApply = new HsQwApply();
        Page page = new Page();
        page.setPageSize(Page.PAGE_SIZE_NOT_PAGING);
        hsQwApply.setPage(page);
        hsQwApply.setId_in(ids.toArray(new String[]{}));
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        Page<HsQwApply> hApplyList = hsQwApplyService.findPage(hsQwApply);
        // 获取导出列表
        List<HsQwApplyExport> list = hsQwApplyService.findExportList(hApplyList);

        // 1. 根据applyScorePre排序并设置preOrder
        list.sort((a, b) -> {
            Double scoreA = a.getApplyScorePre() != null ? a.getApplyScorePre() : a.getApplyScore();
            Double scoreB = b.getApplyScorePre() != null ? b.getApplyScorePre() : b.getApplyScore();
            return scoreB.compareTo(scoreA); // 降序排序
        });
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setPreOrder((i + 1) + ""); // 设置preOrder排名
        }

        // 2. 根据applyScore排序并设置order
        list.sort((a, b) -> b.getApplyScore().compareTo(a.getApplyScore())); // 降序排序
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setOrder(i + 1 + ""); // 设置order排名
        }

        // 导出数据准备
        List<List<String>> dataList = new ArrayList<>();
        for (HsQwApplyExport apply : list) {
            List<String> data = new ArrayList<>();
            data.add(apply.getMainApplyer().getName());
            data.add(apply.getMainApplyer().getOrganization());
            data.add(apply.getApplyScorePre() != null ? apply.getApplyScorePre().toString() : apply.getApplyScore().toString());
            data.add(apply.getSecondApplyer() != null ? apply.getSecondApplyer().getName() : "");
            data.add(apply.getSecondApplyer() != null ? apply.getSecondApplyer().getOrganization() : "");
            data.add(apply.getOther1Name());
            data.add(apply.getOther2Name());
            data.add(DateUtils.formatDate(apply.getApplyTime()));
            data.add(apply.getId());
            data.add(String.valueOf(apply.getPreOrder()));
            data.add(apply.getApplyScore().toString());
            data.add(String.valueOf(apply.getOrder())); // 复查后排名
            data.add(apply.getRemarks());
            dataList.add(data);
        }
        // 导出表头
        List<String> headerList = new ArrayList<>();
        headerList.add("申请人");
        headerList.add("工作单位");
        headerList.add("复查前截" + DateUtils.getDate() + "至得分");
        headerList.add("家庭成员姓名");
        headerList.add("工作单位");
        headerList.add("家庭成员姓名");
        headerList.add("家庭成员姓名");
        headerList.add("轮候起算日");
        headerList.add("收件单号");
        headerList.add("原轮候排名");
        headerList.add("复查后得分");
        headerList.add("配租选房排名");
        headerList.add("备注");

        // 创建一个Sheet表，并导入数据
        try (ExcelExport ee = new ExcelExport("公租房配租通知", "人员名单", headerList, null)) {
            for (int i = 0; i < dataList.size(); i++) {
                Row row = ee.addRow();
                for (int j = 0; j < dataList.get(i).size(); j++) {
                    ee.addCell(row, j, dataList.get(i).get(j));
                }
            }
            String fileName = "公租房配租通知人员" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            // 输出到文件
            if (filePath!= null) {
                String filePathName = filePath + fileName;
                ee.writeFile(filePathName);
                return filePathName;
            } else {
                ee.write(response, fileName);
                return fileName;
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 根据公示单生成配置通知单-房源
     *
     * @param hsQwApplyPublic
     * @param response
     */
    public void exporRecheckHouses(HsQwApplyPublic hsQwApplyPublic, HttpServletResponse response) {
        String houseIds = hsQwApplyPublic.getPublicHouseIds();
        // 获取所有的房屋id
        List<String> ids = new ArrayList<>();
        if (houseIds != null) {
            for (String id : houseIds.split(",")) {
                ids.add(id);
            }
        }
        // 查询所有的房屋信息
        HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
        query.sqlMap().getWhere().and("a.id", QueryType.IN, ids.toArray(new String[]{}));
        query.setStatus(HsQwApplyPublicHouse.STATUS_NORMAL);
        List<HsQwPublicRentalHouse> hHouseList = hsQwPublicRentalHouseService.findList(query);
        List<String> paragraphs = this.generateParagraph(hHouseList);
        // 使用POI方式导出数据到word中文档中
        try (XWPFDocument document = new XWPFDocument();
             OutputStream out = response.getOutputStream()) {

            // 设置响应头和字符编码
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8");
            String fileName = "公租房房屋信息_" + DateUtils.getDate("yyyyMMddHHmmss") + ".docx";
            // 对文件名进行编码处理
            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 创建标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            // 修改字体为方正小标宋体，字号为二号（22 磅）
            titleRun.setText("可供配租房源明细");
            titleRun.setFontFamily("方正小标宋简体");
            titleRun.setFontSize(22);
            titleRun.setVerticalAlignment("center");

            // 按点分段输出内容
            for (int i = 0; i < paragraphs.size(); i++) {
                XWPFParagraph para = document.createParagraph();
                // 设置首行缩进，这里设置为 2 个字符，根据实际情况调整
                para.setFirstLineIndent(420); // 一个字符约为 210 缇
                XWPFRun run = para.createRun();
                run.setText(paragraphs.get(i));
                run.setFontFamily("仿宋_GB2312");
                run.setFontSize(16);
            }

            // 写入文档
            document.write(out);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String[][] roomTypes = {
            {"单居室", "建筑面积 45 平方米左右的单居室户型"},
            {"两居室", "建筑面积 60 平方米左右的双居室户型"},
            {"2.5居室", "建筑面积 80 平方米左右的2.5居室户型"},
            {"其他", "其他户型 "}
    };

    private List<String> generateParagraph(List<HsQwPublicRentalHouse> hHouseList) {
        List<String> paragraphs = new ArrayList<>();
        // 用于按楼号和户型分组存储房屋信息
        Map<String, Map<String, List<HsQwPublicRentalHouse>>> buildingMap = new HashMap<>();
        // 针对房源集合进行楼盘estateId分组，楼盘内再以楼号buildingNum和户型houseType分组
        for (HsQwPublicRentalHouse house : hHouseList) {
            String estateName = house.getEstate().getName(); // 假设存在该属性表示楼盘
            buildingMap.computeIfAbsent(estateName, k -> new HashMap<>())
                    .computeIfAbsent(DictUtils.getDictLabel("hs_house_house_type", house.getHouseType(), "其他"), k -> new ArrayList<>())
                    .add(house); // 房号
        }
        int i = 0;
        // 遍历 estateMap 中的每个楼盘
        for (Map.Entry<String, Map<String, List<HsQwPublicRentalHouse>>> entry : buildingMap.entrySet()) {
            String estateName = entry.getKey(); // 楼盘ID
            // 生成第一段文字
            StringBuilder result = new StringBuilder();

            result.append(WordUtils.toChineseNumeral((i + 1)) + "、" +"位于").append(estateName)
                    .append("的公共租赁住房 ")
                    .append(entry.getValue().values().stream()
                            .flatMap(List::stream) // 展平所有户型的房源
                            .count());
            // 初始化结果字符串构建器
            StringBuilder typeInfo = new StringBuilder(" 套，其中：");
            boolean hasNonZeroType = false;

            // 遍历户型信息数组
            for (String[] roomType : roomTypes) {
                int count = entry.getValue().getOrDefault(roomType[0], new ArrayList<>()).size();
                if (count > 0) {
                    if (hasNonZeroType) {
                        typeInfo.append("，");
                    }
                    typeInfo.append(roomType[1])
                            .append(" ")
                            .append(count)
                            .append(" 套");
                    hasNonZeroType = true;
                }
            }

            // 如果有非零套数的户型，添加后续信息
            if (hasNonZeroType) {
                typeInfo.append("。具体房号如下：\n");
                result.append(typeInfo);
            } else {
                result.append(" 套。具体房号如下：\n");
            }
            //概括段落结束
            paragraphs.add(result.toString());
            // 以楼号、户型分组，生成各楼的信息
            for (Map.Entry<String, List<HsQwPublicRentalHouse>> entryBuilding : entry.getValue().entrySet()) {
                List<HsQwPublicRentalHouse> housesInBuilding = entryBuilding.getValue(); // 该楼的所有房源
                Map<String, List<String>> roomTypeMap = new HashMap<>();
                for (HsQwPublicRentalHouse house : housesInBuilding) {
                    roomTypeMap.computeIfAbsent(house.getBuildingNum() + "|" + house.getHouseType(), k -> new ArrayList<>())
                           .add(house.getUnitNum()); // 房号
                }
                // 生成该楼的信息
                for (Map.Entry<String, List<String>> entryRoomType : roomTypeMap.entrySet()) {
                    String roomBuildType = entryRoomType.getKey(); // 户型
                    String buildingNumber = roomBuildType.split("\\|")[0]; // 楼号
                    String roomType = roomBuildType.split("\\|")[1]; // 户型
                    List<String> roomNumbers = entryRoomType.getValue(); // 该户型的所有房号
                    StringBuilder subResult = new StringBuilder();
                    String roomDesc = "";
                    //根据roomTypes数组，获取对应的中文描述
                    for (String[] roomTypeInfo : roomTypes) {
                        if (roomTypeInfo[0].equals(DictUtils.getDictLabel("hs_house_house_type", roomType, "其他"))) {
                            roomDesc = roomTypeInfo[1]; // 替换为中文描述
                            break;
                        }
                    }
                    subResult.append(buildingNumber)
                            .append("号楼（")
                            .append(roomDesc)
                            .append("住房，共 ")
                            .append(roomNumbers.size())
                            .append(" 套）：")
                            .append(String.join("、", roomNumbers))
                            .append("；\n");
                    paragraphs.add(subResult.toString());
                }
            }
            i++;
        }
        return paragraphs;
    }

    @Transactional
    public void saveRentBatch(String pids) {
        String[] ids = pids.split(",");
        HsQwApplyPublic query = new HsQwApplyPublic();
        query.setId_in(ids);
        query.setStatus(HsQwApplyPublic.PUBLIC_STATUS_NORMAL);
        List<HsQwApplyPublic> list = this.findList(query);
        for (HsQwApplyPublic apply : list) {
            BpmTask bpmTask = bpmTaskService.getTaskByBusinessKey("rent_apply_public", apply.getId(),
                UserUtils.getUser().getUserCode());
            if (bpmTask != null) {
                BpmParams params = new BpmParams();
                params.setTaskId(bpmTask.getId());
                params.setProcInsId(bpmTask.getProcIns().getId());
                params.setComment("一键批量审批");
                params.setActivityId(bpmTask.getActivityId());
                apply.setBpm(params);
                // 先做任务签收
                bpmTaskService.claimTask(bpmTask);
            }
            // 生成配租通知单
            this.saveRent(apply);
        }
        
    }

    /**
     * 一键发布资格轮候名单，生成本地资格轮候名单文件，调用数字空间接口发布
     *
     * @param hsQwApplyPublic
     **/
    public void publicStatus(HsQwApplyPublic hsQwApplyPublic) {
        // 生成临时发布文件
        String filePath = this.exporRecheckNames(hsQwApplyPublic, null, "./");
        // 调用数字空间接口发布
        try {
            // 调用数字空间接口上传文件
            List<String> files  = new ArrayList<>();
            files.add(filePath);
            Api2ResponseBody result = apiSzkjService.uploadFile(files, hsQwApplyPublic.getId());
            if(result.getCode() != 1) {
                throw new ServiceException(result.getMessage());
            }
            String fileId = result.getData().toString();
            if (StringUtils.isBlank(fileId)) {
                throw new ServiceException("上传文件失败");
            }
            // 调用数字空间接口上传通知
            Api2NoticeBody body = new Api2NoticeBody();
            body.setMsgId(hsQwApplyPublic.getId());
            body.setMsgType("1");		// 公告
            body.setMsgSource("资格轮候名单网上公示");
            body.setTopic(hsQwApplyPublic.getPublicName());
            body.setContent(hsQwApplyPublic.getPublicName());
            body.setFileIds("");
            body.setFileIds(fileId);
            body.setPublishUnit(EmpUtils.getOffice().getOfficeName());
            body.setPublishUnitId(EmpUtils.getOffice().getOfficeCode());
            body.setPublishTime(hsQwApplyPublic.getPublicDate());
            body.setReceiveUserIds("all");
            Api2ResponseBody result2 = apiSzkjService.uploadNotice(body);
            if(result2.getCode() != 1) {
                throw new ServiceException(result2.getMessage());
            }
            // 发布成功删除临时文件
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 更新公示单状态
        hsQwApplyPublic.setStatus(HsQwApplyPublic.PUBLIC_STATUS_COMPLETE);
        this.updateStatus(hsQwApplyPublic);
    }
}