package com.hsobs.hs.modules.checkrule.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;
import com.hsobs.hs.modules.checkrule.service.HsQwCheckRuleService;

/**
 * 租赁资格核查规则表Controller
 * <AUTHOR>
 * @version 2025-02-11
 */
@Controller
@RequestMapping(value = "${adminPath}/checkrule/hsQwCheckRule")
public class HsQwCheckRuleController extends BaseController {

	@Autowired
	private HsQwCheckRuleService hsQwCheckRuleService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwCheckRule get(String id, boolean isNewRecord) {
		return hsQwCheckRuleService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("checkrule:hsQwCheckRule:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwCheckRule hsQwCheckRule, Model model) {
		model.addAttribute("hsQwCheckRule", hsQwCheckRule);
		return "modules/checkrule/hsQwCheckRuleList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("checkrule:hsQwCheckRule:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwCheckRule> listData(HsQwCheckRule hsQwCheckRule, HttpServletRequest request, HttpServletResponse response) {
		hsQwCheckRule.setPage(new Page<>(request, response));
		Page<HsQwCheckRule> page = hsQwCheckRuleService.findPage(hsQwCheckRule);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("checkrule:hsQwCheckRule:view")
	@RequestMapping(value = "form")
	public String form(HsQwCheckRule hsQwCheckRule, Model model) {
		model.addAttribute("hsQwCheckRule", hsQwCheckRule);
		return "modules/checkrule/hsQwCheckRuleForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("checkrule:hsQwCheckRule:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwCheckRule hsQwCheckRule) {
		hsQwCheckRuleService.save(hsQwCheckRule);
		return renderResult(Global.TRUE, text("保存租赁资格核查规则表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("checkrule:hsQwCheckRule:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwCheckRule hsQwCheckRule) {
		hsQwCheckRuleService.delete(hsQwCheckRule);
		return renderResult(Global.TRUE, text("删除租赁资格核查规则表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("checkrule:hsQwCheckRule:view")
	@RequestMapping(value = "hsQwCheckRuleSelect")
	public String hsQwCheckRuleSelect(HsQwCheckRule hsQwCheckRule, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwCheckRule", hsQwCheckRule);
		return "modules/checkrule/hsQwCheckRuleSelect";
	}
	
}