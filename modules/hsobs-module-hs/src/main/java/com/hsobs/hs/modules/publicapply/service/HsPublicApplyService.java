package com.hsobs.hs.modules.publicapply.service;

import java.io.*;
import java.util.*;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceClearance;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.dao.HsQwPublicRentalHouseDao;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.hsobs.hs.modules.pricelimitplan.entity.HsPriceLimitPlan;
import com.hsobs.hs.modules.pricelimitpublic.entity.HsPriceLimitPublic;
import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.hsobs.hs.modules.publicsaleapply.service.HsPublicSaleApplyService;
import com.hsobs.hs.modules.utils.WordUtils;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.mapper.query.QueryWhere;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicapply.dao.HsPublicApplyDao;

import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.hsobs.hs.modules.publicapplyer.dao.HsPublicApplyerDao;

import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.service.OfficeService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 公有住房-购房申请Service
 * <AUTHOR>
 * @version 2024-12-10
 */
@Service
public class HsPublicApplyService extends CrudService<HsPublicApplyDao, HsPublicApply> {

	@Autowired
	private HsPublicApplyDao hsPublicApplyDao;

	@Autowired
	private HsPublicApplyerDao hsPublicApplyerDao;

	@Autowired
	private HsQwPublicRentalHouseDao hsPublicRentalHouseDao;

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

	@Autowired
	private EmpUserService empUserService;

	@Autowired
	private OfficeService officeService;

	@Autowired
	private BpmTaskService bpmTaskService;

	@Autowired
	private CommonBpmService commonBpmService;

	@Autowired
	ResourceLoader resourceLoader;

	@Autowired
	FileUploadService fileUploadService;
	@Autowired
	private ApiSzkjService apiSzkjService;

	public static final String[] staticDigit = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"};
    @Autowired
    private HsPublicSaleApplyService hsPublicSaleApplyService;


	private void findAndSetActivityId(HsPublicApply hsPublicApply){

		HsBpmTask params = new HsBpmTask();
		params.setStatus(BpmTask.STATUS_UNFINISHED);
		params.getProcIns().setFormKey("public_apply");
		params.getProcIns().setBizKey(hsPublicApply.getId());
		Page<BpmTask> myHsTaskPage = this.bpmTaskService.findTaskPage(params);

		int activityIdInt = 1;

		BpmTask bpmTask = !myHsTaskPage.getList().isEmpty() ? (BpmTask) myHsTaskPage.getList().get(0) : null;
		if (bpmTask != null) {

			hsPublicApply.setFlowStatus(bpmTask.getName());
			String activityId = bpmTask.getActivityId();
			activityId = activityId.substring(Math.max(0, activityId.length() - 3));
			while (activityId.startsWith("0")) {
				activityId = StringUtils.removeStart(activityId, "0");
			}
			if (StringUtils.isNumeric(activityId)) {
				activityIdInt = Integer.parseInt(activityId);
			}
		}

		hsPublicApply.setApplyStatus(activityIdInt);
	}

	/**
	 * 获取单条数据
	 * @param hsPublicApply
	 * @return
	 */
	@Override
	public HsPublicApply get(HsPublicApply hsPublicApply) {
		HsPublicApply entity = super.get(hsPublicApply);
		if (entity != null){
			Office office = new Office();
			office.setOfficeCode(entity.getOfficeCode());
			entity.setOffice(officeService.get(office));

			HsPublicApplyer hsPublicApplyer = new HsPublicApplyer();
			hsPublicApplyer.setApplyId(entity.getId());
			hsPublicApplyer.sqlMap().getWhere().disableAutoAddStatusWhere();
			List<HsPublicApplyer> lstApplyer = hsPublicApplyerDao.findList(hsPublicApplyer);
			entity.setHsPublicApplyerList(lstApplyer);

			for(HsPublicApplyer applyer : lstApplyer){
				if(applyer.getApplyRole().equals("0")){
					hsPublicApply.setMainApplyer(applyer);
					break;
				}
			}

			if(entity.getSalePlanId() != null && !entity.getSalePlanId().equals("")){
				HsPublicSaleApply plan = new HsPublicSaleApply();
				plan.setId(entity.getSalePlanId());
				plan.sqlMap().getWhere().disableAutoAddStatusWhere();
				entity.setPublicSaleApply(hsPublicSaleApplyService.get(plan));
			}

			if(entity.getHouseId()!=null && entity.getHouseId().length()>0) {
				HsQwPublicRentalHouse house = new HsQwPublicRentalHouse();
				house.setId(entity.getHouseId());
				house.sqlMap().getWhere().disableAutoAddStatusWhere();
				entity.setHouse(hsPublicRentalHouseDao.get(house));

				if(entity.getHouse() != null){
					entity.setHouseInfo(entity.getHouse().getSimpleInfo());
				}
			}

			findAndSetActivityId(entity);

			if(entity.getIsNewRecord() || entity.getApplyStatus() <= 2)
				entity.setReadOnly("false");
			else
				entity.setReadOnly("true");
            entity.setBugFlag("1");
		}
		return entity;
	}

	List<HsPublicApplyer> getApplyerList(HsPublicApply hsPublicApply, String role){
		List<HsPublicApplyer> applyerList = new ArrayList<>();

		for(HsPublicApplyer applyer : hsPublicApply.getHsPublicApplyerList()){
			if(applyer.getApplyRole().equals(role)){

				applyerList.add(applyer);
			}
		}
		return applyerList;
	}
	
	/**
	 * 查询分页数据
	 * @param hsPublicApply 查询条件
	 * @param hsPublicApply page 分页对象
	 * @return
	 */
	@Override
	public Page<HsPublicApply> findPage(HsPublicApply hsPublicApply) {
		hsPublicApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findPage(hsPublicApply);
	}
	
	/**
	 * 查询列表数据
	 * @param hsPublicApply
	 * @return
	 */
	@Override
	public List<HsPublicApply> findList(HsPublicApply hsPublicApply) {
		hsPublicApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findList(hsPublicApply);
	}

	@Override
	public void addDataScopeFilter(HsPublicApply hsPublicApply) {
		hsPublicApply.sqlMap().getDataScope().addFilter("dsf", "Office", "a.office_code", DataScope.CTRL_PERMI_HAVE);
	}
	
	/**
	 * 查询子表分页数据
	 * @param hsPublicApplyer
	 * @param hsPublicApplyer page 分页对象
	 * @return
	 */
	public Page<HsPublicApplyer> findSubPage(HsPublicApplyer hsPublicApplyer) {
		Page<HsPublicApplyer> page = hsPublicApplyer.getPage();
		page.setList(hsPublicApplyerDao.findList(hsPublicApplyer));
		return page;
	}

	/**
	 * 更新住房状态  2待售 3已售
	 *
	 * @param houseId
	 * @param status		2待售 3已售
	 */
	public void updateHouseStatus(String houseId, String status) {
		HsQwPublicRentalHouse hsQwApplyHouse = hsQwPublicRentalHouseService.get(houseId);
		if(hsQwApplyHouse != null) {
			hsQwApplyHouse.setHouseStatus(status);
		}
		hsQwPublicRentalHouseService.update(hsQwApplyHouse);
	}

	public boolean checkHouseId(HsPublicApply hsApply, String houseId){
		List<Map<String,Object>> list = hsPublicApplyDao.checkHouse(hsApply.getId(), houseId);
		return (list.size() == 0)?true:false;
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsPublicApply
	 */
	@Override
	@Transactional
	public void save(HsPublicApply hsPublicApply) {
		final int[] mainNum = {0};
		final String[] maritalStatus = {null};
		hsPublicApply.getHsPublicApplyerList().forEach(k -> {
			if (k.getApplyRole().equals("0")){
				mainNum[0]++;
				maritalStatus[0] = k.getMaritalStatus();
			}
		});
		if (mainNum[0] == 0) {
			throw new ServiceException("未添加主申请人信息");
		}
		if (mainNum[0] > 1) {
			throw new ServiceException("主申请人只能有一个");
		}

		// 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
		if (HsPublicApply.STATUS_NORMAL.equals(hsPublicApply.getStatus())){
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		String houseId = hsPublicApply.getHouseId();
		if(houseId != null && !houseId.equals("")) {
			int index = houseId.indexOf(',');
			if (index > 0) {
				houseId = houseId.substring(index + 1);
				hsPublicApply.setHouseId(houseId);
			}
			if(!checkHouseId(hsPublicApply, hsPublicApply.getHouseId())){
				throw new ServiceException(text("房源选择错误，该房源已被占用！"));
			}
		}

		if(hsPublicApply.getOfficeCode() == null) {
			if (UserUtils.getUser().isAdmin()) {
				throw new ServiceException(text("获取不到工作单位，不能以管理员身份发起！"));
			}
			String officeCode = EmpUtils.getCurrentOfficeCode();
			hsPublicApply.setOfficeCode(officeCode);
		}

		if(hsPublicApply.getHouseId() != null && !hsPublicApply.getHouseId().equals("")) {
			try {
				List<Map<String,Object>> list = hsPublicApplyDao.selectHouseId(hsPublicApply.getId());
				if(list != null && list.size() > 0) {
					Map<String, Object> map = list.get(0);
					Object ob = map.get("HOUSE_ID");
					if(ob != null) {
						String oldHouseId = ob.toString();
						if(!oldHouseId.equals(hsPublicApply.getHouseId())) {
							ob = map.get("HOUSE_TYPE");
							if(ob != null && ob.toString().equals("2")) {		// 公用住房
								this.updateHouseStatus(oldHouseId, "2");	// 待售
							}
							else {
								this.updateHouseStatus(oldHouseId, "0");	// 待租
							}
						}
					}
				}
			}
			catch (Exception e) {
			}
		}

		// 如果未设置状态，则指定状态为审核状态，以提交审核流程
		if (StringUtils.isBlank(hsPublicApply.getStatus())){
			hsPublicApply.setStatus(HsPublicApply.STATUS_AUDIT);
		}

		// 过合同签订流程，就认为已签订合同
		if(hsPublicApply.getFlowStatus() != null && HsPublicApply.APPLY_STATUS_AUDIT_CONTRACT.equals(hsPublicApply.getFlowStatus())){
			hsPublicApply.setContractStatus("1");
		}
		
		// 如果状态为草稿或审核状态，才可以保存业务数据
		if (HsPublicApply.STATUS_DRAFT.equals(hsPublicApply.getStatus())
				|| HsPublicApply.STATUS_AUDIT.equals(hsPublicApply.getStatus())){
			this.updateStatus(hsPublicApply);
			super.save(hsPublicApply);
		}

		// 户口簿复印件
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_hr_file");
		// 身份证复印件
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_id_file");
		// 申请人未婚声明书/结婚证/离婚证和离婚协议或离婚判决书/配偶死亡证明复印件
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_marital_file");
		// 子女身份证或相关户籍证明材料复印件
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_child_file");
		// 申请人离退休证复印件
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_old_file");
		// 不动产登记信息查询结果
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_real_estate_file");
		// 其他相关材料
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_other_file");

		// 申请确认单
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_apply_file");

		// 下发批文
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_approve_file");

		// 估算材料
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_estimated_file");
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_report_file");
		// 备案材料
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_putonfile_file");
		// 核准材料
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_approval_file");

		// 合同材料
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_contract_file");

		// 交易确认书
		FileUploadUtils.saveFileUpload(hsPublicApply, hsPublicApply.getId(), "hsPublicApply_Transaction_file");

		/*int fileHrUploads = FileUploadUtils.findFileUpload(hsPublicApply.getId(), "hsPublicApply_hr_file").size();
		if(fileHrUploads == 0){
			throw new ServiceException("没有上传户口簿复印件");
		}
		int fileIdUploads = FileUploadUtils.findFileUpload(hsPublicApply.getId(), "hsPublicApply_id_file").size();
		if(fileIdUploads == 0){
			throw new ServiceException("没有上传申请人身份证复印件");
		}

		// 申请人未婚声明书/结婚证/离婚证和离婚协议或离婚判决书/配偶死亡证明复印件
		int fileMaritalUploads = FileUploadUtils.findFileUpload(hsPublicApply.getId(), "hsPublicApply_marital_file").size();
		switch (maritalStatus[0]) {
			case "0"://未婚
				if(fileMaritalUploads == 0)throw new ServiceException("没有上传申请人未婚声明书复印件");
				break;
			case "1"://已婚
				if(fileMaritalUploads == 0)throw new ServiceException("没有上传申请人结婚证复印件");
				break;
			case "2"://离婚
				if(fileMaritalUploads == 0)throw new ServiceException("没有上传申请人离婚证和离婚协议或离婚判决书复印件");
				break;
			case "3"://丧偶
				if(fileMaritalUploads == 0)throw new ServiceException("没有上传申请人配偶死亡证明复印件");
				break;
			case "4"://其他
				break;
		}*/

		if (StringUtils.isNotBlank(hsPublicApply.getHouseId())){
			this.updateHouseStatus(hsPublicApply.getHouseId(), "3");//已售
		}

		// 如果为审核状态，则进行审批流操作
		if (HsPublicApply.STATUS_AUDIT.equals(hsPublicApply.getStatus())){
			
			// 指定流程变量，作为流程条件，决定流转方向
			Map<String, Object> variables = MapUtils.newHashMap();

			variables.put("bugFlag", hsPublicApply.getBugFlag());
			//variables.put("leaveDays", hsPublicApply.getLeaveDays());
			// 如果流程实例为空，任务编号也为空，则：启动流程
			if (StringUtils.isBlank(hsPublicApply.getBpm().getProcInsId())
					&& StringUtils.isBlank(hsPublicApply.getBpm().getTaskId())){
				BpmUtils.start(hsPublicApply, "public_apply", variables, null);
			}
			// 如果有任务信息，则：提交任务
			else{
				BpmUtils.complete(hsPublicApply, variables, null);
			}
		}

		// 保存 HsPublicApply子表
		for (HsPublicApplyer hsPublicApplyer : hsPublicApply.getHsPublicApplyerList()){
			if (!HsPublicApplyer.STATUS_DELETE.equals(hsPublicApplyer.getStatus())){
				hsPublicApplyer.setApplyId(hsPublicApply.getId());
				hsPublicApplyer.setUserId(hsPublicApply.getId());
				if (hsPublicApplyer.getIsNewRecord()){
					hsPublicApplyerDao.insert(hsPublicApplyer);
				}else{
					hsPublicApplyerDao.update(hsPublicApplyer);
				}
			}else{
				hsPublicApplyerDao.delete(hsPublicApplyer);
			}
		}

		if(hsPublicApply.getFlowStatus() != null){
			if(hsPublicApply.getFlowStatus().equals(HsPublicApply.APPLY_STATUS_AUDIT_ACCOUNTING)){
				if(hsPublicApply.getBugFlag().toString().equals(HsPublicApply.BUYFLAG_NO)){//不购买，流程结束

					hsPublicApply.setBuyStatus(HsPublicApply.BUYSTATUS_FORGO);	// 放弃购买
					hsPublicApply.setStatus("88");
					this.updateStatus(hsPublicApply);
					this.updateHouseStatus(hsPublicApply.getHouseId(), "2");	// 待售
				}
			}
			if(hsPublicApply.getFlowStatus().equals(HsPublicApply.APPLY_STATUS_AUDIT_CONFIRMATION)) {// 交易通知书

				hsPublicApply.setBuyStatus(HsPublicApply.BUYSTATUS_YES);	// 确认购买
				hsPublicApply.setStatus("88");
				this.updateStatus(hsPublicApply);
			}
		}
	}
	
	/**
	 * 更新状态
	 * @param hsPublicApply
	 */
	@Override
	@Transactional
	public void updateStatus(HsPublicApply hsPublicApply) {
		super.updateStatus(hsPublicApply);
	}
	
	/**
	 * 删除数据
	 * @param hsPublicApply
	 */
	@Override
	@Transactional
	public void delete(HsPublicApply hsPublicApply) {
		super.delete(hsPublicApply);
		HsPublicApplyer hsPublicApplyer = new HsPublicApplyer();
		hsPublicApplyer.setApplyId(hsPublicApply.getId());
		hsPublicApplyerDao.deleteByEntity(hsPublicApplyer);
	}

	public List<HsPublicApply> findAuditListByTask(HsPublicApply hsApply, String flowStatus, String[] flowStatuss, String status){

		Page<HsPublicApply> page = findAuditPageByTask(hsApply, flowStatus, flowStatuss, status);
		return page.getList();
	}
	public Page<HsPublicApply> findAuditPageByTask(HsPublicApply hsApply, String flowStatus) {

		return findAuditPageByTask(hsApply, flowStatus, null, BpmTask.STATUS_UNFINISHED);
	}
	public List<HsPublicApply> findAuditListByTasks(HsPublicApply hsApply, String status) {

		Page<HsPublicApply> page = findAuditPageByTasks(hsApply, status);
		return  page.getList();
	}

	public Page<HsPublicApply> findAuditPageByTasks(HsPublicApply hsApply, String status) {
		String[] flowStatuss = new String[]{
				HsPublicApply.APPLY_STATUS_DRAFT, // "公有住房申请";//0
				HsPublicApply.APPLY_STATUS_CHECK_APPLY,
				HsPublicApply.APPLY_STATUS_AUDIT_ORGHAND_FIRST, // "经办初审";//1
				HsPublicApply.APPLY_STATUS_AUDIT_ORGOFFICE_FIRST, // "处室领导初审";//2
				HsPublicApply.APPLY_STATUS_AUDIT_RESPONSIBLE_FIRST, // "分管领导初审";//3
				HsPublicApply.APPLY_STATUS_AUDIT_BUREAU_FIRST, // "局务会初审";//4
				//HsPublicApply.APPLY_STATUS_AUDIT_APPROVAL, // "下发批文";//5
				HsPublicApply.APPLY_STATUS_AUDIT_APPRAISE, 	// "估价";//6
				HsPublicApply.APPLY_STATUS_AUDIT_RECORD, 	//	"备案";//6
				HsPublicApply.APPLY_STATUS_AUDIT_RECORD_CHECK, 	//	"备案审核";//6
				HsPublicApply.APPLY_STATUS_AUDIT_COCOUNT,	// "核算";//6
				HsPublicApply.APPLY_STATUS_AUDIT_ACCOUNTING, // "核准价格";//8
				HsPublicApply.APPLY_STATUS_AUDIT_CONTRACT, // "签订合同";//9
				//HsPublicApply.APPLY_STATUS_AUDIT_CONFIRMATION // "交易通知书";//13
		};
		return this.findAuditPageByTask(hsApply, "", flowStatuss, status);
	}

	public Page<BpmTask> findBpmTaskPage(String flowStatus, String[] flowStatuss, String status){

		HsBpmTask params = new HsBpmTask();
		//params.setStatus(BpmTask.STATUS_UNFINISHED);
		if (StringUtils.isNotBlank(status)) {
			params.setStatus(status);
		}
		if(!UserUtils.getUser().isAdmin()) {// 是管理员不设置usercode
			params.setUserCode(params.currentUser().getUserCode());
		}
		params.getProcIns().setFormKey("public_apply");//限价房申请key
		if (StringUtils.isNotBlank(flowStatus)) {
			params.setName(flowStatus);
		}
		if (flowStatuss != null && flowStatuss.length > 0) {
			params.setNames(Arrays.stream(flowStatuss).collect(Collectors.toList()));
		}
		return this.bpmTaskService.findTaskPage(params);
	}

	public Page<HsPublicApply> findAuditPageByTaskOld(HsPublicApply hsApply, String flowStatus, String[] flowStatuss, String status){

		if(hsApply.getFlowStatus() != null && !"".equals(hsApply.getFlowStatus())){
			flowStatus = hsApply.getFlowStatus();
			flowStatuss = null;
		}
		Page<BpmTask> myHsTaskPage = findBpmTaskPage(flowStatus, flowStatuss, status);

		//获取所有待办任务的申请单id
		List<String> hsIds = new ArrayList<>();
		myHsTaskPage.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		hsApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());

		Page<HsPublicApply> page = hsApply.getPage();
		if (!hsIds.isEmpty()) {

			page = this.findPage(hsApply);
			page.getList().forEach(k -> this.setTaskInfo(k,k.getId(),myHsTaskPage));
		}
		return page;
	}

	public Page<HsPublicApply> findAuditPageByTask(HsPublicApply hsApply, String flowStatus, String[] flowStatuss, String status) {

		if(hsApply.getFlowStatus() != null && !"".equals(hsApply.getFlowStatus())){
			flowStatus = hsApply.getFlowStatus();
		}
		if(flowStatus != null && !"".equals(flowStatus)){
			flowStatuss = new String[]{flowStatus};
		}
		return commonBpmService.findTaskList(flowStatuss, "public_apply", hsApply, status);
	}
	private void setTaskInfo(HsPublicApply k, String id, Page<BpmTask> myHsTaskPage) {
		for (BpmTask bpmTask : myHsTaskPage.getList()) {
			if (bpmTask.getProcIns().getBizKey().equals(id)) {

				k.setFlowStatus(bpmTask.getName());
				return;
			}
		}
	}

    public Page<HsPublicApply> findMztPage(HsPublicApply hsApply) {
		BpmTask params = new BpmTask();

		hsApply.setCreateBy(params.currentUser().getUserCode());
		hsApply.setStatus(HsPublicApply.STATUS_DRAFT);
		Page<HsPublicApply> hsApplyPage = this.findPage(hsApply);
		return hsApplyPage;
    }

	public Page<HsPublicApply> findOnlyArchivesPage(HsPublicApply hsApply) {

		hsApply.sqlMap().getWhere().and("status", QueryType.IN, new String[]{"88"});
		hsApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsPublicApply> hsApplyPage = this.findPage(hsApply);

		List<HsPublicApply> lstApply = hsApplyPage.getList();
		for(HsPublicApply apply : lstApply){
			if(apply.getStatus().equals("88")){
				apply.setFlowStatus("配售完成");
			}
		}
		return hsApplyPage;
	}

	public Page<HsPublicApply> findArchivesPage(HsPublicApply hsApply) {

		String officeCode = hsApply.getOfficeCode();
		if(officeCode != null && !"".equals(officeCode)) {
			Office office = new Office();
			office.setOfficeCode(officeCode);
			office = officeService.get(office);
			String officeParentCodes = office.getParentCodes();

			Consumer<QueryWhere> nestQueryTypeConsumer = (queryWhere) -> {
				queryWhere.or("office.parent_codes", QueryType.LIKE, officeParentCodes+officeCode+",%").or("office.office_code", QueryType.EQ, officeCode);
			};
			hsApply.sqlMap().getWhere().and(nestQueryTypeConsumer);
		}
		addDataScopeFilter(hsApply);

		if(hsApply.getFlowStatus() != null && !hsApply.getFlowStatus().equals("")) {
			if(hsApply.getFlowStatus().equals("配售完成")){
				return findOnlyArchivesPage(hsApply);
			}
			else{
				return findAuditPageByTask(hsApply, hsApply.getFlowStatus(), null, BpmTask.STATUS_UNFINISHED);
			}
		}

		hsApply.sqlMap().getWhere().and("status", QueryType.IN, new String[]{"4","88"});
		hsApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		Page<HsPublicApply> hsApplyPage = this.findPage(hsApply);

		Page<BpmTask> myHsTaskPage = findBpmTaskPage("", null, BpmTask.STATUS_UNFINISHED);

		List<HsPublicApply> lstApply = hsApplyPage.getList();
		for(HsPublicApply apply : lstApply){
			if(apply.getStatus().equals("88")){
				apply.setFlowStatus("配售完成");
			}
			else{
				setTaskInfo(apply, apply.getId(), myHsTaskPage);
			}
		}

		return hsApplyPage;
	}

	public void flushTaskStatus(HsPublicApply hsApply) {
		if (hsApply.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		hsApply = this.get(hsApply.getId());
		if (hsApply == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		HsBpmTask params = new HsBpmTask();
		params.setStatus(BpmTask.STATUS_UNFINISHED);
		params.getProcIns().setFormKey("public_apply");
		params.getProcIns().setBizKey(hsApply.getId());

		Page<BpmTask> page = this.bpmTaskService.findTaskPage(params);

		BpmTask bpmTask = !page.getList().isEmpty() ? (BpmTask) page.getList().get(0) : null;
		if (bpmTask != null) {

		}
	}

	private void putMap(Map<String, Object> map, String key, String val){
		map.put(key, val);
	}

	// 生成申请表
	public void sendApply(HsPublicApply hsApply, HttpServletResponse response) {

		Map<String, Object> map = new HashMap<>();
		// 购房人
		HsPublicApplyer mainApplayer = hsApply.getMainApplyer();
		putMap(map,"mnm", mainApplayer.getName());
		putMap(map,"mphone", mainApplayer.getPhone());
		putMap(map,"morg", mainApplayer.getOrganization());
		putMap(map,"ml", (mainApplayer.getWorkLevel()!=null)?mainApplayer.getWorkLevel():"");		// 职级
		putMap(map,"my1", "");		// 工作时间
		if(mainApplayer.getWorkDate() != null){
			putMap(map,"my1", String.format("%04d-%02d-%02d", mainApplayer.getWorkDate().getYear()+1900, mainApplayer.getWorkDate().getMonth()+1, mainApplayer.getWorkDate().getDate()));
		}
		putMap(map,"my2", "");		// 本单位工作时间
		if(mainApplayer.getWorkDateL() != null){
			putMap(map,"my2", String.format("%04d-%02d-%02d", mainApplayer.getWorkDateL().getYear()+1900, mainApplayer.getWorkDateL().getMonth()+1, mainApplayer.getWorkDateL().getDate()));
		}
		putMap(map,"ms", (mainApplayer.getWorkingYear()!=null)?mainApplayer.getWorkingYear().toString():"");		// 工龄
		// 配偶
		putMap(map,"snm", "");
		putMap(map,"sphone", "");
		putMap(map,"sorg", "");
		putMap(map,"sl", "");
		putMap(map,"sy1", "");
		putMap(map,"sy2", "");
		List<HsPublicApplyer> lstApplyer = getApplyerList(hsApply, "1");	// 配偶
		if(lstApplyer.size()>0) {
			HsPublicApplyer hsPublicApplyer = lstApplyer.get(0);
			putMap(map,"snm", hsPublicApplyer.getName());
			putMap(map,"sphone", hsPublicApplyer.getPhone());
			putMap(map,"sorg", hsPublicApplyer.getOrganization());
			putMap(map,"sl", (hsPublicApplyer.getWorkLevel()!=null)?hsPublicApplyer.getWorkLevel():"");		// 职级
			putMap(map,"sy1", "");		// 工作时间
			if(hsPublicApplyer.getWorkDate() != null){
				putMap(map,"sy1", String.format("%04d-%02d-%02d", hsPublicApplyer.getWorkDate().getYear()+1900, hsPublicApplyer.getWorkDate().getMonth()+1, hsPublicApplyer.getWorkDate().getDate()));
			}
			putMap(map,"sy2", "");		// 本单位工作时间
			if(hsPublicApplyer.getWorkDateL() != null){
				putMap(map,"sy2", String.format("%04d-%02d-%02d", hsPublicApplyer.getWorkDateL().getYear()+1900, hsPublicApplyer.getWorkDateL().getMonth()+1, hsPublicApplyer.getWorkDateL().getDate()));
			}
		}

		putMap(map,"nm1", "");
		putMap(map,"nm2", "");
		putMap(map,"nm3", "");
		putMap(map,"phone1", "");
		putMap(map,"phone2", "");
		putMap(map,"phone3", "");
		lstApplyer = getApplyerList(hsApply, "3");	// 子女
		if(lstApplyer.size()>0) {
			HsPublicApplyer hsPublicApplyer = lstApplyer.get(0);
			putMap(map,"nm1", hsPublicApplyer.getName());
			putMap(map,"phone1", hsPublicApplyer.getPhone());
		}
		if(lstApplyer.size()>1) {
			HsPublicApplyer hsPublicApplyer = lstApplyer.get(1);
			putMap(map,"nm2", hsPublicApplyer.getName());
			putMap(map,"phone2", hsPublicApplyer.getPhone());
		}
		if(lstApplyer.size()>1) {
			HsPublicApplyer hsPublicApplyer = lstApplyer.get(2);
			putMap(map,"nm3", hsPublicApplyer.getName());
			putMap(map,"phone3", hsPublicApplyer.getPhone());
		}

		putMap(map,"level", "");			// 购房标准
		putMap(map,"houseinfo", hsApply.getHouseInfo());		// 房屋座落及单元号

		putMap(map,"zy", "");
		putMap(map,"zn", "");

		putMap(map,"z1", "");
		putMap(map,"z2", "");
		putMap(map,"z3", "");
		putMap(map,"z4", "");

		if(!lstApplyer.isEmpty()) {
			if (hsApply.getHaveHouse().equals("1"))
				putMap(map, "zy", "√");
			else
				putMap(map, "zn", "√");

			switch (hsApply.getHouseType()) {
				case "1":
					putMap(map, "z1", "√");
					break;
				case "2":
					putMap(map, "z2", "√");
					break;
				case "3":
					putMap(map, "z3", "√");
					break;
				case "4":
					putMap(map, "z4", "√");
					break;
			}
		}
		putMap(map,"zaddress", (hsApply.getHouseAddress()!=null)?hsApply.getHouseAddress():"");

		Resource resource = resourceLoader.getResource("classpath:file.template/PublicApply.docx");
		if (!resource.exists()) {
			return;
		}
		ServletOutputStream outputStream = null;
		try{

			response.setCharacterEncoding("utf-8");
			response.setContentType("application/x-download");
			String fileName = "公有住房购房申请表"+ DateUtils.getDate("yyyyMMddHHmmss")+".docx";
			fileName = new String(fileName.getBytes(), "ISO-8859-1");
			response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(fileName)));
			outputStream = response.getOutputStream();
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
		ConfigureBuilder configureBuilder = Configure.builder();
		Configure config = configureBuilder.build();

		try (InputStream templateStream = resource.getInputStream();
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
			template.render(map);
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	// 生成审核表
	public void createExamine(HsPublicApply hsApply, HttpServletResponse response) {

		if(hsApply.getHouseId() == null || "".equals(hsApply.getHouseId())){
			throw new ServiceException("没有选择房源");
		}
		if(hsApply.getHouse() == null){
			HsQwPublicRentalHouse house = new HsQwPublicRentalHouse();
			house.setId(hsApply.getHouseId());
			house.sqlMap().getWhere().disableAutoAddStatusWhere();
			hsApply.setHouse(hsPublicRentalHouseDao.get(house));
			if(hsApply.getHouse() == null) {
				throw new ServiceException("没有选择房源");
			}
		}

		Map<String, Object> map = new HashMap<>();
		// 购房人
		putMap(map,"main_name", hsApply.getMainApplyer().getName());
		putMap(map,"main_phone", hsApply.getMainApplyer().getPhone());
		putMap(map,"main_rank", (hsApply.getMainApplyer().getWorkLevel()!=null)?hsApply.getMainApplyer().getWorkLevel():"");
		// 配偶
		putMap(map,"spouse_name", "");
		putMap(map,"spouse_phone", "");
		putMap(map,"spouse_rank", "");
		List<HsPublicApplyer> lstApplyer = getApplyerList(hsApply, "1");	// 配偶
		if(lstApplyer.size()>0) {
			HsPublicApplyer hsPublicApplyer = lstApplyer.get(0);
			putMap(map,"spouse_name", hsPublicApplyer.getName());
			putMap(map,"spouse_phone", hsPublicApplyer.getPhone());
			putMap(map,"spouse_rank", (hsPublicApplyer.getWorkLevel()!=null)?hsPublicApplyer.getWorkLevel():"");
		}

		putMap(map,"house_level", "");			// 购房标准
		putMap(map,"house_info", hsApply.getHouseInfo());		// 房屋座落及单元号

		HsQwPublicRentalHouse house = hsApply.getHouse();
		putMap(map,"aa", (house.getBuildingArea()!=null)?house.getBuildingArea():"");		// 住宅建筑面积
		putMap(map,"ab", (house.getSupportArea()!=null)?house.getSupportArea().toString():"");		// 附属建筑面积
		putMap(map,"ac", "");		// 合计建筑面积
		if(house.getBuildingArea() != null && house.getSupportArea() != null){
			Double dValue = Double.valueOf(house.getBuildingArea().toString()) + Double.valueOf(house.getSupportArea().toString());
			putMap(map,"ac", dValue.toString());
		}

		putMap(map,"pa", (hsApply.getUnitPrice()!=null)?hsApply.getUnitPrice():"");		// 标准内单价
		putMap(map,"pb", (hsApply.getExceedUnitPrice()!=null)?hsApply.getExceedUnitPrice():"");		// 超标部分单价
		putMap(map,"pc", (hsApply.getOtherUnitPrice()!=null)?hsApply.getOtherUnitPrice():"");		// 附属间单价
		putMap(map,"pd", (hsApply.getPrice()!=null)?hsApply.getPrice():"");		// 总价款

		map.put("a", "");		// 前面扩展的
		putMap(map,"b", "");		// 拾
		putMap(map,"c", "");		// 万
		putMap(map,"d", "");		// 千
		putMap(map,"e", "");		// 百
		putMap(map,"f", "");		// 拾
		putMap(map,"g", "");		// 元
		putMap(map,"h", "");		// 角
		putMap(map,"i", "");		// 分
		String strE = "";
		if(hsApply.getPrice() != null) {
			Integer price = Integer.valueOf(hsApply.getPrice());
			Integer c = price / 10000000;	// 千万
			if(c > 0)
				strE += String.format("%s仟", staticDigit[c]);
			price = price-c*10000000;
			c = price / 1000000;	// 百万
			if(c > 0 || !strE.isEmpty()){
				strE += String.format("%s佰", staticDigit[c]);
			}
			putMap(map,"a", strE);
			price = price-c*1000000;

			c = price / 100000;	// 十万
			putMap(map,"b", staticDigit[c]);	// 拾
			price = price-c*100000;

			c = price / 10000;	// 万
			putMap(map,"c", staticDigit[c]);
			price = price-c*10000;

			c = price / 1000;	// 千
			putMap(map,"d", staticDigit[c]);
			price = price-c*1000;

			c = price / 100;	// 百
			putMap(map,"e", staticDigit[c]);
			price = price-c*100;

			c = price / 10;	// 十
			putMap(map,"f", staticDigit[c]);
			price = price-c*10;
			putMap(map,"g", staticDigit[price]);
			putMap(map,"h", staticDigit[0]);
			putMap(map,"i", staticDigit[0]);
		}

		putMap(map,"pid", (hsApply.getEstinmateId()!=null)?hsApply.getEstinmateId():"");		//

		Resource resource = resourceLoader.getResource("classpath:file.template/PublicExamine.docx");
		if (!resource.exists()) {
			return;
		}
		ServletOutputStream outputStream = null;
		try {
			response.setCharacterEncoding("utf-8");
			response.setContentType("application/x-download");
			String fileName = "公有住房出售价格核准表"+ DateUtils.getDate("yyyyMMddHHmmss")+".docx";
			fileName = new String(fileName.getBytes(), "ISO-8859-1");
			response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(fileName)));
			outputStream = response.getOutputStream();

		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
		ConfigureBuilder configureBuilder = Configure.builder();
		Configure config = configureBuilder.build();

		try (InputStream templateStream = resource.getInputStream();
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
			template.render(map);
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	private Map<String, Object> createApprovalData(HsPublicApply hsApply){

		Map<String, Object> map = new HashMap<>();

		hsApply = this.get(hsApply);
		putMap(map, "name", hsApply.getMainApplyer().getName());		// 申请人
		putMap(map, "send_office", "福建省直住房制度改革办公室");
		putMap(map, "office_name", hsApply.getOffice().getFullName());
		putMap(map, "address", hsApply.getHouse().getEstate().getAddress());
		putMap(map, "build", hsApply.getHouse().getBuildingNum());
		putMap(map, "unit", hsApply.getHouse().getUnitNum());

		Date now = new Date();
		putMap(map, "date", String.format("%04d年%d月%d日", now.getYear()+1900,now.getMonth()+1,now.getDate()));

		return map;
	}

	// 生成下发批文
	public void sendApproval(HsPublicApply hsApply, HttpServletResponse response) {

		Map<String, Object> map = createApprovalData(hsApply);

		Resource resource = resourceLoader.getResource("classpath:file.template/PublicApproval.docx");
		if (!resource.exists()) {
			return;
		}

		ServletOutputStream outputStream = null;
		try {
			response.setCharacterEncoding("utf-8");
			response.setContentType("application/x-download");
			String fileName = "公有住房批文"+ DateUtils.getDate("yyyyMMddHHmmss")+".docx";
			fileName = new String(fileName.getBytes(), "ISO-8859-1");
			response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(fileName)));
			outputStream = response.getOutputStream();

		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
		ConfigureBuilder configureBuilder = Configure.builder();
		Configure config = configureBuilder.build();

		try (InputStream templateStream = resource.getInputStream();
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
			template.render(map);
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	public void sendApprovals(String pids) {

		Resource resource = resourceLoader.getResource("classpath:file.template/PublicApproval.docx");
		if (!resource.exists()) {
			return;
		}

		String[] ps = pids.split(",");
		for(String p : ps) {
			HsPublicApply hsApply = new HsPublicApply();
			hsApply.setId(p);

			List<FileUpload> listUpload = FileUploadUtils.findFileUpload(hsApply.getId(), "hsPublicApply_approve_file");
			//if(listUpload.size() > 0)
			//	continue;

			Map<String, Object> map = createApprovalData(hsApply);

			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
			ConfigureBuilder configureBuilder = Configure.builder();
			Configure config = configureBuilder.build();

			try (InputStream templateStream = resource.getInputStream();
				 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
				template.render(map);
				template.write(outputStream);
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
				//WordUtils.exportWord(map, "PublicApproval.ftl", fileName, "public_approval");

			String md5 = DigestUtils.md5Hex(outputStream.toByteArray());

			FileUploadParams fileParams = new FileUploadParams();
			MultipartFile multipartFile = new MockMultipartFile(
					"公有住房批文.docx",
					"公有住房批文.docx",
					"application/octet-stream",
					outputStream.toByteArray()
			);
			fileParams.setBizKey(hsApply.getId());
			fileParams.setBizType("hsPublicApply_approve_file");
			fileParams.setFile(multipartFile);
			fileParams.setFileMd5(md5);
			fileParams.setFileName("公有住房批文.docx");
			FileUpload fileUpload = new FileUpload();
			fileUpload.setBizKey(hsApply.getId());
			fileUpload.setBizType("hsPublicApply_approve_file");
			Map<String, Object> uploadedFileMap = fileUploadService.uploadFile(fileUpload, fileParams);
			hsApply.setDataMap(uploadedFileMap);
			FileUploadUtils.saveFileUpload(hsApply, hsApply.getId(), "hsPublicApply_approve_file");

			//file = new File(fileName);
			// 增加下发批文到附件
			//FileUploadUtils.saveFileUpload(file, fileName, hsApply.getId(), "hsPublicApply_approve_file", "file");
			hsApply.setApprovalStatus(HsPublicApply.APPROVAL_STATUS_YES);
			this.update(hsApply);


		}
	}

	private Map<String, Object> createCommitData(HsPublicApply hsApply){

		hsApply = this.get(hsApply);
		if(hsApply.getHouseId() == null || "".equals(hsApply.getHouseId())){
			throw new ServiceException("没有选择房源");
		}
		if(hsApply.getHouse() == null){
			HsQwPublicRentalHouse house = new HsQwPublicRentalHouse();
			house.setId(hsApply.getHouseId());
			house.sqlMap().getWhere().disableAutoAddStatusWhere();
			hsApply.setHouse(hsPublicRentalHouseDao.get(house));
			if(hsApply.getHouse() == null) {
				throw new ServiceException("没有选择房源");
			}
		}

		Map<String, Object> map = new HashMap<>();

		putMap(map, "num", "");		// 第 X 号
		putMap(map, "name", hsApply.getMainApplyer().getName());		// 申请人
		putMap(map, "office_name", hsApply.getOffice().getFullName());
		putMap(map, "address", hsApply.getHouse().getEstate().getAddress());
		putMap(map, "build", hsApply.getHouse().getBuildingNum());
		putMap(map, "unit", hsApply.getHouse().getUnitNum());

		HsQwPublicRentalHouse house = hsApply.getHouse();
		Double dSub = Double.valueOf(0);
		if(house.getSupportArea() != null){
			dSub = house.getSupportArea();
		}
		putMap(map, "sub", (dSub>0)?"1":"0");	// 是否有附属间
		putMap(map, "room", "1");

		putMap(map, "area", hsApply.getHouse().getBuildingArea());
		if(house.getBuildingArea() != null && house.getSupportArea() != null){
			Double dValue = Double.valueOf(house.getBuildingArea().toString()) + Double.valueOf(house.getSupportArea().toString());
			putMap(map,"area", dValue.toString());
		}
		putMap(map,"price", (hsApply.getPrice()!=null)?hsApply.getPrice():"");

		Date now = new Date();
		putMap(map, "date", String.format("%04d年%d月%d日", now.getYear()+1900,now.getMonth()+1,now.getDate()));

		return map;
	}

	// 生成通知书
	public void createCommit(HsPublicApply hsApply, HttpServletResponse response) {

		Map<String, Object> map = createCommitData(hsApply);

		Resource resource = resourceLoader.getResource("classpath:file.template/PublicCommit.docx");
		if (!resource.exists()) {
			return;
		}

		ServletOutputStream outputStream = null;
		try {
			response.setCharacterEncoding("utf-8");
			response.setContentType("application/x-download");
			String fileName = "公有住房交易确认书"+ DateUtils.getDate("yyyyMMddHHmmss")+".docx";
			fileName = new String(fileName.getBytes(), "ISO-8859-1");
			response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(fileName)));
			outputStream = response.getOutputStream();

		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
		ConfigureBuilder configureBuilder = Configure.builder();
		Configure config = configureBuilder.build();

		try (InputStream templateStream = resource.getInputStream();
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
			template.render(map);
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	// 生成通知书
	public void createCommits(String pids) {
		Resource resource = resourceLoader.getResource("classpath:file.template/PublicCommit.docx");
		if (!resource.exists()) {
			return;
		}

		String[] ps = pids.split(",");
		for(String p : ps) {
			HsPublicApply hsApply = new HsPublicApply();
			hsApply.setId(p);

			List<FileUpload> listUpload = FileUploadUtils.findFileUpload(hsApply.getId(), "hsPublicApply_Transaction_file");
			//if(listUpload.size() > 0)
			//	continue;

			Map<String, Object> map = createCommitData(hsApply);
			String fileName = "公有住房交易确认书"+DateUtils.getDate("yyyyMMddHHmmss")+".docx";

			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
			ConfigureBuilder configureBuilder = Configure.builder();
			Configure config = configureBuilder.build();

			try (InputStream templateStream = resource.getInputStream();
				 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
				template.render(map);
				template.write(outputStream);
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
			//outputStream.close();
			//WordUtils.exportWord(map, "PublicCommit.ftl", fileName, "public_commit");

			String md5 = DigestUtils.md5Hex(outputStream.toByteArray());
			FileUploadParams fileParams = new FileUploadParams();
			MultipartFile multipartFile = new MockMultipartFile(
					"公有住房交易确认书.docx",
					"公有住房交易确认书.docx",
					"application/octet-stream",
					outputStream.toByteArray()
			);
			fileParams.setBizKey(hsApply.getId());
			fileParams.setBizType("hsPublicApply_Transaction_file");
			fileParams.setFile(multipartFile);
			fileParams.setFileMd5(md5);
			fileParams.setFileName("公有住房交易确认书.docx");
			FileUpload fileUpload = new FileUpload();
			fileUpload.setBizKey(hsApply.getId());
			fileUpload.setBizType("hsPublicApply_Transaction_file");
			Map<String, Object> uploadedFileMap = fileUploadService.uploadFile(fileUpload, fileParams);
			hsApply.setDataMap(uploadedFileMap);
			FileUploadUtils.saveFileUpload(hsApply, hsApply.getId(), "hsPublicApply_Transaction_file");

			//file = new File(fileName);
			// 增加交易确认书到附件
			//FileUploadUtils.saveFileUpload(file, fileName, hsApply.getId(), "hsPublicApply_Transaction_file", "file");
		}
	}

	// 国资备案
	public void createRegister(HsPublicApply hsApply, HttpServletResponse response) {

		Map<String, Object> map = createCommitData(hsApply);

		String fileName = "公有住房备案表"+DateUtils.getDate("yyyyMMddHHmmss")+".docx";

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
		ConfigureBuilder configureBuilder = Configure.builder();
		Configure config = configureBuilder.build();

		Resource resource = resourceLoader.getResource("classpath:file.template/PublicRegister.docx");
		if (!resource.exists()) {
			return;
		}
		try (InputStream templateStream = resource.getInputStream();
			 XWPFTemplate template = XWPFTemplate.compile(templateStream, config)) {
			template.render(map);
			template.write(outputStream);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		//outputStream.close();
		//WordUtils.exportWord(map, "PublicCommit.ftl", fileName, "public_commit");

		String md5 = DigestUtils.md5Hex(outputStream.toByteArray());
		FileUploadParams fileParams = new FileUploadParams();
		MultipartFile multipartFile = new MockMultipartFile(
				fileName,
				fileName,
				"application/octet-stream",
				outputStream.toByteArray()
		);
		fileParams.setBizKey(hsApply.getId());
		fileParams.setBizType("hsPublicApply_putonfile_file");
		fileParams.setFile(multipartFile);
		fileParams.setFileMd5(md5);
		fileParams.setFileName(fileName);
		FileUpload fileUpload = new FileUpload();
		fileUpload.setBizKey(hsApply.getId());
		fileUpload.setBizType("hsPublicApply_putonfile_file");
		Map<String, Object> uploadedFileMap = fileUploadService.uploadFile(fileUpload, fileParams);
		hsApply.setDataMap(uploadedFileMap);
		FileUploadUtils.saveFileUpload(hsApply, hsApply.getId(), "hsPublicApply_putonfile_file");
	}

	public void UpdatePublic(HsPublicApply hsApply){

		Api2ResponseBody responseBody;
		Api2NoticeBody body = new Api2NoticeBody();
		body.setMsgId(hsApply.getId());
		body.setMsgType("1");		// 公告
		body.setMsgSource("公有住房备案");
		body.setTopic("公有住房备案");

		body.setContent(String.format("%s按照闽政办〔2011〕32号文件规定向%s同志出售位于%s%s座%s单元的公有住房，申报售房手续符合要求，经我办审核批准，特向你处进行报备！",
				hsApply.getOffice().getFullName(), hsApply.getMainApplyer().getName(), hsApply.getHouse().getEstate().getAddress(), hsApply.getHouse().getBuildingNum(), hsApply.getHouse().getUnitNum()));
		body.setFileIds("");
		body.setPublishUnit(hsApply.getOffice().getFullName());
		body.setPublishUnitId(hsApply.getOfficeCode());
		body.setPublishTime(new Date());
		body.setReceiveUserIds("all");
		responseBody = apiSzkjService.uploadNotice(body);
		if(responseBody.getCode() != 1)
			throw new ServiceException(responseBody.getMessage());
	}

	public void sendRegister(HsPublicApply hsApply, HttpServletResponse response) {

		if(hsApply.getRegisterStatus() != null && hsApply.getRegisterStatus().equals("1")){
			throw new ServiceException("已经向国资上报过，不能重复上报");
		}

		hsApply = this.get(hsApply);
		UpdatePublic(hsApply);
		hsApply.setRegisterStatus("1");
		super.update(hsApply);
	}
}