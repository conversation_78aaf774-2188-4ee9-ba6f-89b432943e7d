package com.hsobs.hs.modules.apply.service.HsQwApplyProcess.rental;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HsQwApplyProcessHouseSelect extends HsQwApplyProcessDefault {

    @Autowired
    HsQwPublicRentalHouseService hsQwPublicRentalHouseService;
    @Override
    public String getStatus() {
        return "房源配租";
    }

    @Override
    public void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        this.process(hsQwApply, hsQwApplyService);
        //更新房源为待审核状态
        HsQwPublicRentalHouse house = new HsQwPublicRentalHouse();
        house.setId(hsQwApply.getHouseId());
        house.setStatus("4");
        hsQwPublicRentalHouseService.updateStatus(house);
    }

}
