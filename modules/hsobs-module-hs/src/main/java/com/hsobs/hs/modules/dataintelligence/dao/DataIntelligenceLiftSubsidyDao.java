package com.hsobs.hs.modules.dataintelligence.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceLiftSubsidy;

import java.util.List;
import java.util.Map;

/**
 * 住房保障数据统计DAO接口  加装电梯补助统计
 * <AUTHOR>
 * @version 2024-12-16
 */
@MyBatisDao
public interface DataIntelligenceLiftSubsidyDao extends CrudDao<DataIntelligenceLiftSubsidy> {

    List<Map<String, Object>> countLiftSubsidyTotal(String sqlWhere);

    // String sqlWhere, String sqlOrderBy
    List<Map<String, Object>> countLiftSubsidy(Map<String, Object> map);

    List<Map<String, Object>> countLiftSubsidyByEstate(Map<String, Object> map);

    List<Map<String, Object>> countLiftSubsidyCompare(String sqlWhere);

}