package com.hsobs.hs.modules.checkrule.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格核查规则表Entity
 * <AUTHOR>
 * @version 2025-02-11
 */
@Table(name="hs_qw_check_rule", alias="a", label="租赁资格核查规则表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="name", attrName="name", label="规则名称", queryType=QueryType.LIKE),
		@Column(name="rule_code", attrName="ruleCode", label="规则编码", queryType=QueryType.LIKE),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsQwCheckRule extends DataEntity<HsQwCheckRule> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 规则名称
	private String ruleCode;		// 规则编码

	public HsQwCheckRule() {
		this(null);
	}
	
	public HsQwCheckRule(String id){
		super(id);
	}
	
	@NotBlank(message="规则名称不能为空")
	@Size(min=0, max=100, message="规则名称长度不能超过 100 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@NotBlank(message="规则编码不能为空")
	@Size(min=0, max=255, message="规则编码长度不能超过 255 个字符")
	public String getRuleCode() {
		return ruleCode;
	}

	public void setRuleCode(String ruleCode) {
		this.ruleCode = ruleCode;
	}
	
}