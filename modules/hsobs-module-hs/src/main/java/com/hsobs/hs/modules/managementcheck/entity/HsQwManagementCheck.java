package com.hsobs.hs.modules.managementcheck.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.checkdetail.entity.HsQwManagementCheckDetail;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.bpm.entity.BpmEntity;

/**
 * 租赁资格轮候物业核验Entity
 * <AUTHOR>
 * @version 2025-01-22
 */
@Table(name="hs_qw_management_check", alias="a", label="租赁资格轮候物业核验信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="record_id", attrName="recordId", label="资格核验编号", isQuery=true),
		@Column(name="apply_id", attrName="applyId", label="申请单编号", isQuery=true),
		@Column(name="check_back", attrName="checkBack", label="核验反馈", isQuery=false),
		@Column(name="check_result", attrName="checkResult", label="核验结果", isQuery=false),
		@Column(name="check_type", attrName="checkType", label="核验类型", comment="核验类型（0入户核验 1退租核验 2资格核验）"),
		@Column(name="back_date", attrName="backDate", label="反馈时间", isUpdateForce=true),
		@Column(name="check_date", attrName="checkDate", label="核验时间", isUpdateForce=true),
		@Column(includeEntity=DataEntity.class),
		@Column(name="check_user", attrName="checkUser", label="核验人员"),
		@Column(name="back_user", attrName="backUser", label="反馈人员"),
		@Column(name="water_fee", attrName="waterFee", label="水费"),
		@Column(name="eletric_fee", attrName="eletricFee", label="电费"),
		@Column(name="gas_fee", attrName="gasFee", label="燃气费"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApply.class, alias = "ha",
				on = "a.apply_id = ha.id", attrName="hsQwApply",
				columns = {@Column(includeEntity = HsQwApply.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "o",
				on = "o.apply_id = ha.id and o.apply_role = 0 and o.status=0", attrName = "hsQwApply.mainApplyer",
				columns = {@Column(includeEntity = HsQwApplyer.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwCompact.class, alias = "m",
				on = "m.apply_id = ha.id and m.status=0", attrName = "hsQwApply.compact",
				columns = {@Column(includeEntity = HsQwCompact.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "h",
				on = "h.id = ha.house_id", attrName = "hsQwApply.hsQwApplyHouse",
				columns = {@Column(includeEntity = HsQwPublicRentalHouse.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "p",
				on = "p.id = h.estate_id", attrName="hsQwApply.hsQwApplyHouse.estate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)})
	}, orderBy="a.update_date DESC"
)
public class HsQwManagementCheck extends BpmEntity<HsQwManagementCheck> {

	public static String CHECK_TYPE_IN = "0"; //入户核验
	public static String CHECK_TYPE_OUT = "1"; //退租核验
	public static String CHECK_TYPE_QUALIFICATION = "2"; //资格核验

	
	private static final long serialVersionUID = 1L;
	private String recordId;
	private String applyId;		// 申请单编号
	private String checkBack;		// 核验反馈
	private String checkResult;		// 核验结果
	private String checkType;		// 核验类型（0入户核验 1退租核验）
	private Date backDate;		// 反馈时间
	private Date checkDate;		// 核验时间
	private String checkUser;		// 核验人员
	private String backUser;		// 反馈人员
	private HsQwApply hsQwApply;
	private List<HsQwManagementCheckDetail> hsQwObjectCheckList;
	private List<HsQwRentalFee> hsQwRentalFeeList;
	private String gasFee;
	private String eletricFee;
	private String waterFee;
	private String hasRentalFee;  //是否已缴清租金
	private String formKey;
	private String dataType; //数据类型：4入户核验数据 5退租核验数据
	private String tmpTaskId;
	private String isRead;

	public HsQwManagementCheck() {
		this(null);
	}
	
	public HsQwManagementCheck(String id){
		super(id);
	}
	
	@NotBlank(message="申请单编号不能为空")
	@Size(min=0, max=64, message="申请单编号长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@Size(min=0, max=500, message="核验反馈长度不能超过 500 个字符")
	public String getCheckBack() {
		return checkBack;
	}

	public void setCheckBack(String checkBack) {
		this.checkBack = checkBack;
	}
	
//	@NotBlank(message="核验结果不能为空")
	@Size(min=0, max=50, message="核验结果长度不能超过 50 个字符")
	public String getCheckResult() {
		return checkResult;
	}

	public void setCheckResult(String checkResult) {
		this.checkResult = checkResult;
	}
	
	@NotBlank(message="核验类型不能为空")
	@Size(min=0, max=1, message="核验类型长度不能超过 1 个字符")
	public String getCheckType() {
		return checkType;
	}

	public void setCheckType(String checkType) {
		this.checkType = checkType;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getBackDate() {
		return backDate;
	}

	public void setBackDate(Date backDate) {
		this.backDate = backDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}
	
	@Size(min=0, max=64, message="核验人员长度不能超过 64 个字符")
	public String getCheckUser() {
		return checkUser;
	}

	public void setCheckUser(String checkUser) {
		this.checkUser = checkUser;
	}
	
	@Size(min=0, max=64, message="反馈人员长度不能超过 64 个字符")
	public String getBackUser() {
		return backUser;
	}

	public void setBackUser(String backUser) {
		this.backUser = backUser;
	}

	public HsQwApply getHsQwApply() {
		return hsQwApply;
	}

	public void setHsQwApply(HsQwApply hsQwApply) {
		this.hsQwApply = hsQwApply;
	}

    public List<HsQwManagementCheckDetail> getHsQwObjectCheckList() {
        return hsQwObjectCheckList;
    }

    public void setHsQwObjectCheckList(List<HsQwManagementCheckDetail> hsQwObjectCheckList) {
        this.hsQwObjectCheckList = hsQwObjectCheckList;
    }

    public String getHasRentalFee() {
        return this.hsQwRentalFeeList==null?"否": this.hsQwRentalFeeList.isEmpty() ?"否":"是";
    }

    public void setHasRentalFee(String hasRentalFee) {
        this.hasRentalFee = hasRentalFee;
    }

    public List<HsQwRentalFee> getHsQwRentalFeeList() {
        return hsQwRentalFeeList;
    }

    public void setHsQwRentalFeeList(List<HsQwRentalFee> hsQwRentalFeeList) {
        this.hsQwRentalFeeList = hsQwRentalFeeList;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

	public String getGasFee() {
		return gasFee;
	}

	public void setGasFee(String gasFee) {
		this.gasFee = gasFee;
	}

	public String getEletricFee() {
		return eletricFee;
	}

	public void setEletricFee(String eletricFee) {
		this.eletricFee = eletricFee;
	}

	public String getWaterFee() {
		return waterFee;
	}

	public void setWaterFee(String waterFee) {
		this.waterFee = waterFee;
	}

	public String getFormKey() {
		if (formKey == null && this.checkType !=null){
			if (this.checkType.equals(CHECK_TYPE_IN)){
				this.formKey = "management_check_in";
			} else if (this.checkType.equals(CHECK_TYPE_OUT)){
				this.formKey = "management_check_out";
			}
		}
		return formKey;
	}

	public void setFormKey(String formKey) {
		this.formKey = formKey;
	}


	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getIsRead() {
		return isRead;
	}

	public void setIsRead(String isRead) {
		this.isRead = isRead;
	}

    public String getTmpTaskId() {
        return tmpTaskId;
    }

    public void setTmpTaskId(String tmpTaskId) {
        this.tmpTaskId = tmpTaskId;
    }
}