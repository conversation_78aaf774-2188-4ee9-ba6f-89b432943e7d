package com.hsobs.hs.modules.dataintelligence.web;

import com.hsobs.hs.modules.dataintelligence.entity.HsEstateAddress;
import com.hsobs.hs.modules.dataintelligence.service.HsEstateAddressService;
import com.jeesite.common.web.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping(value = "${adminPath}/hsestateaddress/")
public class HsEstateAddressController extends BaseController {

    @Autowired
    private HsEstateAddressService hsEstateAddressService;
    /**
     * 获取数据
     */
    @ModelAttribute
    public HsEstateAddress get(String id) {
        return hsEstateAddressService.get(id);
    }

    /**
     * 小区地址
     */
    @RequestMapping(value = "estateAddress")
    public String estateAddress(HsEstateAddress hsEstateAddress, Model model) {
        model.addAttribute("hsEstateAddress", hsEstateAddress);
        return "modules/dataintelligence/estateAddressForm";
    }
}
