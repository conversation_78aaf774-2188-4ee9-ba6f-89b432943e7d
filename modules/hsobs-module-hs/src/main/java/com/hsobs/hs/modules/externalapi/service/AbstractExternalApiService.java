package com.hsobs.hs.modules.externalapi.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.externalapi.config.ExternalApiConfig;
import com.hsobs.hs.modules.externalapi.util.ExternalApiHttpClient;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.BaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部API服务抽象实现类
 * 
 * <AUTHOR>
 * @version 2024-05-20
 */
public abstract class AbstractExternalApiService extends BaseService implements ExternalApiService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractExternalApiService.class);
    
    /**
     * 获取部门配置
     * 
     * @return 部门配置
     */
    protected abstract ExternalApiConfig.DepartmentConfig getDepartmentConfig();
    
    @Override
    public JSONObject callApi(String apiName, Map<String, Object> params) {
        ExternalApiConfig.DepartmentConfig config = getDepartmentConfig();
        if (config == null || !config.isEnabled()) {
            logger.warn("Department {} is not enabled", getDepartmentName());
            return createMockResponse(apiName, params);
        }
        
        ExternalApiConfig.ApiConfig apiConfig = config.getApis().get(apiName);
        if (apiConfig == null || !apiConfig.isEnabled()) {
            logger.warn("API {} in department {} is not enabled", apiName, getDepartmentName());
            return createMockResponse(apiName, params);
        }
        
        // 构建请求URL
        String url = buildRequestUrl(config, apiConfig);
        
        // 构建请求头
        Map<String, String> headers = buildRequestHeaders(config, apiConfig);
        
        // 构建请求体
        Object body = buildRequestBody(params, config, apiConfig);
        
        // 发送请求
        HttpMethod method = HttpMethod.valueOf(apiConfig.getMethod());
        return ExternalApiHttpClient.sendRequest(url, method, headers, body, apiConfig.getTimeout());
    }
    
    @Override
    public <T> T callApi(String apiName, Map<String, Object> params, Class<T> clazz) {
        JSONObject response = callApi(apiName, params);
        return JSON.toJavaObject(response, clazz);
    }
    
    @Override
    public boolean isEnabled() {
        ExternalApiConfig.DepartmentConfig config = getDepartmentConfig();
        return config != null && config.isEnabled();
    }
    
    /**
     * 构建请求URL
     */
    protected String buildRequestUrl(ExternalApiConfig.DepartmentConfig config, ExternalApiConfig.ApiConfig apiConfig) {
        String baseUrl = config.getBaseUrl();
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        
        String path = apiConfig.getPath();
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        
        return baseUrl + path;
    }
    
    /**
     * 构建请求头
     */
    protected Map<String, String> buildRequestHeaders(ExternalApiConfig.DepartmentConfig config, ExternalApiConfig.ApiConfig apiConfig) {
        Map<String, String> headers = new HashMap<>();
        
        // 添加认证信息
        if (StringUtils.isNotBlank(config.getToken())) {
            headers.put("Authorization", "Bearer " + config.getToken());
        }
        
        if (StringUtils.isNotBlank(config.getAppId())) {
            headers.put("App-Id", config.getAppId());
        }
        
        // 添加自定义请求头
        if (apiConfig.getParams() != null) {
            apiConfig.getParams().forEach((key, value) -> {
                if (key.startsWith("header.")) {
                    String headerName = key.substring("header.".length());
                    headers.put(headerName, value);
                }
            });
        }
        
        return headers;
    }
    
    /**
     * 构建请求体
     */
    protected Object buildRequestBody(Map<String, Object> params, ExternalApiConfig.DepartmentConfig config, ExternalApiConfig.ApiConfig apiConfig) {
        // 合并配置参数和请求参数
        Map<String, Object> body = new HashMap<>();
        
        // 添加配置参数
        if (apiConfig.getParams() != null) {
            apiConfig.getParams().forEach((key, value) -> {
                if (!key.startsWith("header.")) {
                    body.put(key, value);
                }
            });
        }
        
        // 添加公共参数
        body.put("appId", config.getAppId());
        body.put("timestamp", System.currentTimeMillis());
        
        // 添加请求参数
        if (params != null) {
            body.putAll(params);
        }
        
        return body;
    }
    
    /**
     * 创建模拟响应
     */
    protected JSONObject createMockResponse(String apiName, Map<String, Object> params) {
        logger.info("Creating mock response for API {} in department {}", apiName, getDepartmentName());
        
        JSONObject response = new JSONObject();
        response.put("code", "200");
        response.put("message", "success");
        response.put("data", new JSONObject());
        
        // 根据不同的API创建不同的模拟数据
        JSONObject data = response.getJSONObject("data");
        
        // 这里可以根据apiName添加特定的模拟数据
        data.put("mock", true);
        data.put("apiName", apiName);
        data.put("departmentName", getDepartmentName());
        
        return response;
    }
}
