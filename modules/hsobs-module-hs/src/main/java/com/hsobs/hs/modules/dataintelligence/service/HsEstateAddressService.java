package com.hsobs.hs.modules.dataintelligence.service;

import com.hsobs.hs.modules.dataintelligence.dao.HsEstateAddressDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceTotal;
import com.hsobs.hs.modules.dataintelligence.entity.HsEstateAddress;
import com.jeesite.common.service.CrudService;
import org.springframework.stereotype.Service;

@Service
public class HsEstateAddressService extends CrudService<HsEstateAddressDao, HsEstateAddress> {

    @Override
    public HsEstateAddress get(String id) {
        HsEstateAddress hsEstateAddress = new HsEstateAddress();
        hsEstateAddress.setId(id);
        HsEstateAddress entity = super.get(hsEstateAddress);
        if(entity != null){

            // 可能是String格式，转换成double
            try
            {
                if(entity.getLatitude_d() == null){
                    entity.setLatitude_d((entity.getLatitude()==null)?26.103770:Double.valueOf(entity.getLatitude()));
                }
                if(entity.getLongitude_d() == null) {
                    entity.setLongitude_d((entity.getLongitude() == null) ? 119.2904000 : Double.valueOf(entity.getLongitude()));
                }
            }catch (Exception e){
                entity.setLongitude_d(119.2904000);
                entity.setLatitude_d(26.103770);
            }
        }
        return entity;
    }
}
