package com.hsobs.hs.modules.testimport;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.house.dao.HsQwPublicRentalHouseDao;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.rentfee.service.HsQwRentalFeeService;
import com.hsobs.hs.modules.utils.ChineseToPinyinUtil;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.SpringUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.service.EmpUserService;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.transaction.Transactional;
import java.io.File;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 测试数据导入
 */
@Controller
@RequestMapping(value = "/testCaseLh")
public class TestCaseDataImportLhController extends BaseController {

	@Autowired
	private HsQwRentalFeeService hsQwRentalFeeService;

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

	@Autowired
	private HsQwPublicRentalHouseDao hsQwPublicRentalHouseDao;

	@Autowired
	private HsQwApplyerService hsQwApplyerService;

	@Autowired
	private HsQwApplyService hsQwApplyService;

	@Autowired
	private HsQwApplyDao hsQwApplyDao;

	@Autowired
	private EmpUserService empUserService;

	private BpmTaskService bpmTaskService = BpmUtils.getBpmTaskService();

	/**
	 * 导入excel数据
	 * @return
	 */
	@RequestMapping(value = "importByExcel")
	public void importByExcel() {
		// 从本地excel读取
		try {
			File classPath = new File(TestCaseDataImportLhController.class.getResource("/").getFile());
			String fileName = classPath.getParentFile().getAbsoluteFile() + "/公租房档案数据含入住情况.xlsx";
			// 解析申请信息
			this.insertApplyInfo(new ExcelImport(fileName, 1,1));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("导入失败：" + e.getMessage());
		}
	}

	private void insertApplyInfo(ExcelImport ei) {
		// 初始化数据
		List<Map<String, String>> dataList = this.initMap(ei);
		
		logger.error("开始处理数据，总计 " + dataList.size() + " 条记录");
		long startTime = System.currentTimeMillis();

		// 处理数据并映射到实体
		processDataToEntities(dataList);
		
		long endTime = System.currentTimeMillis();
		logger.error("导入成功，共处理 " + dataList.size() + " 条记录，耗时 " + (endTime - startTime) / 1000 + " 秒");
	}

	/**
	 * 初始化数据
	 * @param ei
	 * @return
	 */
	private List<Map<String, String>> initMap(ExcelImport ei){
		// 存储所有行数据
		List<Map<String, String>> dataList = new ArrayList<>();

		// 获取表头
		Row headerRow = ei.getRow(ei.getDataRowNum() - 1);
		List<String> headers = new ArrayList<>();
		for (int j = 0; j < ei.getLastCellNum(); j++) {
			Object headerVal = ei.getCellValue(headerRow, j);
			headers.add(headerVal != null ? headerVal.toString() : "");
		}

		// 读取数据行
		for (int i = ei.getDataRowNum(); i < ei.getLastDataRowNum(); i++) {
			Row row = ei.getRow(i);
			if (row == null) {
				continue;
			}

			Map<String, String> rowData = new HashMap<>();
			for (int j = 0; j < ei.getLastCellNum(); j++) {
				Object val = ei.getCellValue(row, j);
				String headerName = j < headers.size() ? headers.get(j) : "Column" + j;
				rowData.put(headerName, val != null ? val.toString() : "");
			}
			dataList.add(rowData);
		}
		return dataList;
	}

	/**
	 * 将数据映射到不同的实体
	 * @param dataList Excel数据列表
	 */
    public void processDataToEntities(List<Map<String, String>> dataList) {
		// 创建线程池
		int processors = Runtime.getRuntime().availableProcessors();
		int threadCount = Math.min(processors * 2, 10); // 线程数为CPU核心数的2倍，但不超过10
		ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
		
		logger.error("创建线程池，使用 " + threadCount + " 个线程进行处理");
		
		// 计算每个线程处理的数据量
		int totalSize = dataList.size();
		int batchSize = Math.max(1, totalSize / threadCount);
//		int batchSize = 20;

		logger.error("每个线程处理约 " + batchSize + " 条数据");
		
		// 创建任务列表
		List<Future<?>> futures = new ArrayList<>();
		
		// 创建进度计数器
		final AtomicInteger processedCount = new AtomicInteger(0);
		final int reportInterval = Math.max(1, totalSize / 20); // 每处理5%的数据报告一次进度
		
		// 创建错误记录收集器
		final ConcurrentHashMap<String, String> errorRecords = new ConcurrentHashMap<>();
		
		// 分批提交任务
		for (int i = 0; i < totalSize; i += batchSize) {
			final int batchIndex = i / batchSize + 1;
			int endIndex = Math.min(i + batchSize, totalSize);
			List<Map<String, String>> subList = dataList.subList(i, endIndex);
			
			logger.info("提交批次 {}，数据范围: {}-{}", batchIndex, (i+1), endIndex);
			
			// 提交任务
			Future<?> future = executorService.submit(() -> {
				logger.info("批次 {} 开始处理，共 {} 条数据", batchIndex, subList.size());
				long batchStartTime = System.currentTimeMillis();
				
				// 移除批次级别的事务，直接处理每条记录
				for (Map<String, String> row : subList) {
					String recordIdentifier = row.get("申请人姓名") + "_" + row.get("身份证件号码");
					
					// 为每条记录创建独立的事务
					TransactionTemplate singleRowTransaction = new TransactionTemplate(
							SpringUtils.getBean(PlatformTransactionManager.class));
					
					try {
						singleRowTransaction.execute(rowStatus -> {
							try {
								processOneRow(row);
								return null;
							} catch (Exception e) {
								rowStatus.setRollbackOnly();
								String errorMsg = String.format("处理记录失败: %s, 原因: %s", 
									recordIdentifier, e.getMessage());
								logger.error(errorMsg, e);
								errorRecords.put(recordIdentifier, errorMsg);
								return null;
							}
						});
					} catch (Exception e) {
						String errorMsg = String.format("事务执行异常: %s, 原因: %s", 
							recordIdentifier, e.getMessage());
						logger.error(errorMsg, e);
						errorRecords.put(recordIdentifier, errorMsg);
					}
					
					// 更新进度计数
					int currentCount = processedCount.incrementAndGet();
					if (currentCount % reportInterval == 0 || currentCount == totalSize) {
						double percentage = (double) currentCount / totalSize * 100;
						logger.info("总进度: {:.2f}% ({}/{})", percentage, currentCount, totalSize);
					}
				}
				
				long batchEndTime = System.currentTimeMillis();
				logger.info("批次 {} 处理完成，耗时 {} 秒", batchIndex, 
					(batchEndTime - batchStartTime) / 1000);
				
				// 输出本批次的错误统计
				int errorCount = (int) errorRecords.values().stream()
					.filter(error -> error.contains("批次 " + batchIndex)).count();
				if (errorCount > 0) {
					logger.warn("批次 {} 处理完成，有 {} 条记录失败", batchIndex, errorCount);
				}
			});
			
			futures.add(future);
		}
		
		logger.error("所有批次已提交，等待处理完成...");
		
		// 等待所有任务完成
		for (Future<?> future : futures) {
			try {
				future.get();
			} catch (InterruptedException | ExecutionException e) {
				System.err.println("任务执行异常: " + e.getMessage());
				e.printStackTrace();
			}
		}
		
		logger.error("所有批次处理完成，关闭线程池");
		
		// 关闭线程池
		executorService.shutdown();
		try {
			if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
				logger.error("线程池未能在60秒内关闭，强制关闭");
				executorService.shutdownNow();
			}
		} catch (InterruptedException e) {
			System.err.println("关闭线程池时被中断: " + e.getMessage());
			executorService.shutdownNow();
			Thread.currentThread().interrupt();
		}
		
		// 在所有批次处理完成后，输出错误统计
		if (!errorRecords.isEmpty()) {
			logger.error("数据导入完成，共有 {} 条记录失败：", errorRecords.size());
			errorRecords.forEach((key, value) -> 
				logger.error("记录 {}: {}", key, value));
		}
	}
	
	/**
	 * 处理单行数据
	 */
	@Transactional
    public void processOneRow(Map<String, String> row) {
		try {
			// 1. 创建并保存申请单
			HsQwApply apply = this.createHsQwApply(row);
			List<HsQwApplyer> applyerList = new ArrayList<>();

			// 2. 创建主申请人信息
			String mainName = row.get("申请人姓名");
			String mainIdNum = row.get("身份证件号码");
			String mainOrg = row.get("工作单位");
			HsQwApplyer mainApplyer = createHsQwApplyerEntity(mainName, mainIdNum, mainOrg, apply.getId(), "0");
			mainApplyer.setWorkTime(new Date());
			mainApplyer.setWorkAge("3");
			// 3.1 创建主申请用户并赋予普通用户角色
			EmpUser user = this.createEmpUser(mainApplyer);
			mainApplyer.setUserId(user.getId());
			apply.setMainApplyer(mainApplyer);
			applyerList.add(mainApplyer);

			// 5. 创建并保存家庭成员信息
			for (int i = 1; i <= 4; i++) {
				if (hasValue(row, "家庭成员姓名" + i)) {
					HsQwApplyer familyMember = createHsQwApplyerEntity(row.get("家庭成员姓名" + i), row.get("身份证件号码" + i),  row.get("工作单位" + i), apply.getId(),  row.get("与申请人关系" + i));
					familyMember.setWorkTime(new Date());
					familyMember.setWorkAge("");
					familyMember.setUserId(user.getId());
					applyerList.add(familyMember);
				}
			}

			// 家庭成员数量
			apply.setFamilyPeoples(applyerList.size() + "");
			apply.setHsQwApplyerList(applyerList);

			// 设置主申请用户信息
			apply.setProxyUserId(user.getId());
			// 提交表单
			hsQwApplyService.save(apply);

			// 轮候分设置
			apply.setApplyScore(Double.valueOf(row.get("截至2024年12月31日得分")));
			hsQwApplyService.update(apply);

			// 直接签收，并转到最后一个环节
			BpmTask bpmTask = bpmTaskService.getTaskByBusinessKey("rent_apply", apply.getId(), null);
			if (bpmTask != null) {
				bpmTaskService.claimTask(bpmTask);
				bpmTask.setActivityId("sfc1279869");
				bpmTask.setNextUserCodes("13509314691_zblk");
				bpmTaskService.moveTask(bpmTask);
			}
		} catch (Exception e){
			System.out.println("处理异常" + e.getMessage());
			e.printStackTrace();
		}
	}

	private EmpUser createEmpUser(HsQwApplyer mainApplyer) {
		EmpUser empUser = new EmpUser();
		EmpUser query = new EmpUser();
		String loginCode = ChineseToPinyinUtil.getFirstLetters(mainApplyer.getName()) + mainApplyer.getIdNum().substring(mainApplyer.getIdNum().length() - 4);
		String userCode = loginCode + "_0001";
		query.setLoginCode(loginCode);
		query.setUserCode(userCode);
		empUser = empUserService.get(query);
		if (empUser == null) {
			empUser = new EmpUser();
			empUser.setUserName(mainApplyer.getName());
			empUser.setPassword("123456");
			empUser.setUserType("employee");
			empUser.setUserCode(userCode);
			empUser.setLoginCode(loginCode);
			empUser.setMgrType("0");
			empUser.setStatus("0");
			empUser.setCreateBy("13509314691_zblk");
			empUser.setUpdateBy("13509314691_zblk");
			empUser.setCreateDate(new Date());
			empUser.setUpdateDate(new Date());
			empUserService.save(empUser);
		}
		return empUser;
	}

	private HsQwApplyer createHsQwApplyerEntity(String name, String idNum, String org, String applyId, String applyRole) {
		HsQwApplyer hsQwApplyer = new HsQwApplyer();
		hsQwApplyer.setApplyId(applyId);
		hsQwApplyer.setName(name);
		hsQwApplyer.setIdNum(idNum);
		hsQwApplyer.setOrganization(org);
		hsQwApplyer.setApplyRole(this.getApplyRole(applyRole));
		return hsQwApplyer;
	}

	private String getApplyRole(String applyRole) {
		// 申请人角色（0主申请人 1配偶 2父母 3子女 4其他）
		if (applyRole.equals("0")){
			return "0";
		} else if (applyRole.equals("配偶")){
			return "1";
		} else if (applyRole.equals("儿子")
					||applyRole.equals("女儿") ){
			return "3";
		}
		return "4";
	}


	@SneakyThrows
    private HsQwApply createHsQwApply(Map<String, String> row) {
		HsQwApply apply = new HsQwApply();
		// 取100000-500000之间随机值
		Random random = new Random();
		int randomNumber = random.nextInt(500000 - 100000 + 1) + 100000;
		apply.setFamilyIncome(randomNumber + "");
		apply.setApplyMatter("0");
		apply.setCreateBy("13509314691_zblk");
		apply.setUpdateBy("13509314691_zblk");
		apply.setCreateDate(new Date());
		apply.setUpdateDate(new Date());
		apply.setStatus("4");
		apply.setOfficeCode("0000064");
		apply.setApplyTime(DateUtils.parseDate(row.get("轮候起算日"), "yyyyMMdd"));
		apply.setApplyAccepted("0");
		apply.setEligible("0");
		apply.setRecheckStatus("0");
		apply.setRecheckAudit("0");
		apply.setOfflineRent("0");
		apply.setRentTime(new Date());
		apply.setApplyScore(Double.valueOf(row.get("截至2024年12月31日得分")));
		apply.setAvgIncome(row.get("人均年收入"));
		return apply;
	}


	/**
	 * 检查字段是否有值
	 */
	private boolean hasValue(Map<String, String> row, String key) {
		return row.containsKey(key) && row.get(key) != null && !row.get(key).trim().isEmpty();
	}

}