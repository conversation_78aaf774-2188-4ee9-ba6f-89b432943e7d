package com.hsobs.hs.modules.talent.entity;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import com.hsobs.hs.modules.talent.bean.HsTalentRecordMergeApply;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;

/**
 * 人才补助档案表Entity
 * <AUTHOR>
 * @version 2025-01-03
 */
@Table(name="hs_talent_record", alias="a", label="人才补助档案表信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="unit_id", attrName="unitId", label="工作单位"),
		@Column(name="user_name", attrName="userName", label="申请人姓名", queryType=QueryType.LIKE),
		@Column(name="user_tel", attrName="userTel", label="申请人电话"),
		@Column(name="user_no", attrName="userNo", label="申请人身份证号码"),
		@Column(name="edu_back", attrName="eduBack", label="学历"),
		@Column(name="title_id", attrName="titleId", label="职称"),
		@Column(name="arrival_time", attrName="arrivalTime", label="引进时间", isUpdateForce=true),
		@Column(name="talent_type", attrName="talentType", label="人才类型"),
		@Column(name="talent_level", attrName="talentLevel", label="人才等级"),
		@Column(name="remark", attrName="remark", label="备注"),
		@Column(name="check_time", attrName="checkTime", label="审批时间", isUpdateForce=true),
		@Column(name="check_status", attrName="checkStatus", label="审批状态", isUpdateForce=true),
		@Column(name="subsidy_fund", attrName="subsidyFund", label="补助总金额", isUpdateForce=true),
		@Column(name="subsidy_period", attrName="subsidyPeriod", label="补助周期", isUpdateForce=true),
		@Column(name="issued_period", attrName="issuedPeriod", label="已发放周期", isUpdateForce=true),
		@Column(name="status", attrName="status", label="状态", isUpdate=true),
		@Column(name="create_by", attrName="createBy", label="申请日期", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="申请人", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o",
				on = "o.office_code = a.unit_id", attrName = "applyOffice",
				columns = {
					@Column(includeEntity = Office.class)
		        }
		)
    }, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere", orderBy="a.update_date DESC"
)
public class HsTalentRecord extends DataEntity<HsTalentRecord> {
	
	private static final long serialVersionUID = 1L;
	private String unitId;		// 工作单位
	private String userName;		// 申请人姓名
	private String userTel;		// 申请人电话
	private String userNo;		// 申请人身份证号码
	private String eduBack;		// 学历
	private String titleId;		// 职称
	private Date arrivalTime;		// 引进时间
	private String talentType;		// 人才类型
	private String talentLevel;		//
	private String remark;		// 备注
	private Date checkTime;		// 审批时间
	private Integer checkStatus;		// 审批状态  0-无 1-审核中 2-发放中 3-停发
	private Double subsidyFund;		// 补助总金额
	private Integer subsidyPeriod;		// 补助周期
	private Integer issuedPeriod;		// 已发放周期
	private String validTag;		// 是否有效

	private Office applyOffice;

	private List<HsTalentIntroductionApply> applyList;
	private List<HsTalentRecordMergeApply> mergeApplyList;

	@ExcelFields({
			@ExcelField(title="编号", attrName="id", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="工作单位", attrName="applyOffice.treeNames", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="姓名", attrName="userName", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="电话", attrName="userTel", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="身份证", attrName="userNo", align= ExcelField.Align.CENTER, sort=60),
			@ExcelField(title="学历", attrName="eduBack", dictType="talent_edu_type", align= ExcelField.Align.CENTER, sort=70),
			@ExcelField(title="职称", attrName="titleId", dictType="talent_title", align= ExcelField.Align.LEFT, sort=80),
			@ExcelField(title="引进时间", attrName="arrivalTime", align= ExcelField.Align.CENTER, words=20, sort=90, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="审批时间", attrName="checkTime", align= ExcelField.Align.CENTER, words=20, sort=100, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="补助总额", attrName="subsidyFund", align= ExcelField.Align.LEFT, sort=110),
			@ExcelField(title="补助周期", attrName="subsidyPeriod", align= ExcelField.Align.LEFT, sort=120),
			@ExcelField(title="已发放周期", attrName="issuedPeriod", align= ExcelField.Align.LEFT, sort=130),
			@ExcelField(title="审批状态", attrName="checkStatus", dictType="talent_status",  align= ExcelField.Align.LEFT, sort=140),
			@ExcelField(title="创建日期", attrName="createDate", align= ExcelField.Align.CENTER, words=20, sort=150, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="更新日期", attrName="updateDate", align= ExcelField.Align.CENTER, words=20, sort=160, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
	})

	public HsTalentRecord() {
		this(null);
	}

	public HsTalentRecord(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="工作单位长度不能超过 64 个字符")
	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}
	
	@Size(min=0, max=30, message="申请人姓名长度不能超过 30 个字符")
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
	
	@Size(min=0, max=20, message="申请人电话长度不能超过 20 个字符")
	public String getUserTel() {
		return userTel;
	}

	public void setUserTel(String userTel) {
		this.userTel = userTel;
	}
	
	@Size(min=0, max=255, message="申请人身份证号码长度不能超过 255 个字符")
	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}
	
	@Size(min=0, max=32, message="学历长度不能超过 32 个字符")
	public String getEduBack() {
		return eduBack;
	}

	public void setEduBack(String eduBack) {
		this.eduBack = eduBack;
	}
	
	@Size(min=0, max=64, message="职称长度不能超过 64 个字符")
	public String getTitleId() {
		return titleId;
	}

	public void setTitleId(String titleId) {
		this.titleId = titleId;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getArrivalTime() {
		return arrivalTime;
	}

	public void setArrivalTime(Date arrivalTime) {
		this.arrivalTime = arrivalTime;
	}

	@Size(min=0, max=255, message="人才类型长度不能超过 255 个字符")
	public String getTalentType() {
		return talentType;
	}

	public void setTalentType(String talentType) {
		this.talentType = talentType;
	}

	@Size(min=0, max=255, message="人才等级长度不能超过 255 个字符")
	public String getTalentLevel() {
		return talentLevel;
	}

	public void setTalentLevel(String talentLevel) {
		this.talentLevel = talentLevel;
	}

	@Size(min=0, max=900, message="备注长度不能超过 900 个字符")
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Date checkTime) {
		this.checkTime = checkTime;
	}
	
	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}
	
	public Double getSubsidyFund() {
		return subsidyFund;
	}

	public void setSubsidyFund(Double subsidyFund) {
		this.subsidyFund = subsidyFund;
	}
	
	public Integer getSubsidyPeriod() {
		return subsidyPeriod;
	}

	public void setSubsidyPeriod(Integer subsidyPeriod) {
		this.subsidyPeriod = subsidyPeriod;
	}
	
	public Integer getIssuedPeriod() {
		return issuedPeriod;
	}

	public void setIssuedPeriod(Integer issuedPeriod) {
		this.issuedPeriod = issuedPeriod;
	}
	
	@Size(min=0, max=1, message="是否有效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public Office getApplyOffice() {
		return applyOffice;
	}

	public void setApplyOffice(Office applyOffice) {
		this.applyOffice = applyOffice;
	}

	public List<HsTalentIntroductionApply> getApplyList() {
		return applyList;
	}

	public void setApplyList(List<HsTalentIntroductionApply> applyList) {
		this.applyList = applyList;
	}

	public List<HsTalentRecordMergeApply> getMergeApplyList() {
		return mergeApplyList;
	}

	public void setMergeApplyList(List<HsTalentRecordMergeApply> mergeApplyList) {
		this.mergeApplyList = mergeApplyList;
	}
}