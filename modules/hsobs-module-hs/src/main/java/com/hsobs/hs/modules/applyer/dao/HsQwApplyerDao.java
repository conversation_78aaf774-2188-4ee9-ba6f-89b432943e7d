package com.hsobs.hs.modules.applyer.dao;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租赁资格轮候申请人DAO接口
 * <AUTHOR>
 * @version 2024-11-21
 */
@MyBatisDao
public interface HsQwApplyerDao extends CrudDao<HsQwApplyer> {

    List<String> findGroupUserIds(@Param("applyer") HsQwApplyer applyer);
}