package com.hsobs.hs.modules.house.entity;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.housrvr.entity.HsQwHouseVr;
import com.hsobs.hs.modules.province.entity.HsQwApplyProvince;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * 租赁公租房房源房源信息表-空闲房源
 *
 * <AUTHOR>
 * @version 2025-04-23
 */
public class HsQwPublicRentalHouseIdel extends HsQwPublicRentalHouse {

    @ExcelFields({
            @ExcelField(title="楼盘编号", attrName="estateId", align=Align.CENTER, sort=20),
            @ExcelField(title="楼盘位置", attrName="location", align=Align.CENTER, sort=30),
            @ExcelField(title="楼号", attrName="buildingNum", align=Align.CENTER, sort=40),
            @ExcelField(title="住房编号", attrName="houseNum", align=Align.CENTER, sort=50),
            @ExcelField(title="所处楼层", attrName="floor", align=Align.CENTER, sort=60),
            @ExcelField(title="建筑面积", attrName="buildingArea", align=Align.CENTER, sort=70),
    })
    public HsQwPublicRentalHouseIdel() {
        this(null);
    }

    public HsQwPublicRentalHouseIdel(String id) {
        super(id);
    }

}