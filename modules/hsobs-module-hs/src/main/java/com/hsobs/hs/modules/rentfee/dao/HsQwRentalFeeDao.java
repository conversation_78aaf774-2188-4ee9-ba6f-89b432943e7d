package com.hsobs.hs.modules.rentfee.dao;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;

import io.lettuce.core.dynamic.annotation.Param;

import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;

import java.util.List;

/**
 * 租赁账单表DAO接口
 * <AUTHOR>
 * @version 2025-01-20
 */
@MyBatisDao
public interface HsQwRentalFeeDao extends CrudDao<HsQwRentalFee> {

    List<HsQwPublicRentalHouse> getApplyHouseByEstate(String estateId);

    /**
     * 查询所有需要生成租金账单的申请单
     * @param feeMonth 费用月份
     * @param feeType 费用类型（0-物业费，1-租金）
     * @return 需要生成账单的申请单列表
     */
    List<HsQwApply> findAppliesNeedGenerateFee(@Param("feeMonth") String feeMonth, @Param("feeType") String feeType);

    /**
     * 批量插入租金账单
     * @param feeList 租金账单列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<HsQwRentalFee> feeList);
}