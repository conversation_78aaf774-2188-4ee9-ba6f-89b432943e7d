package com.hsobs.hs.modules.apply.service.HsQwApplyProcess;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;

import java.util.Map;

public interface HsQwApplyProcess {

    String getStatus();

    String getFormKey();

    void setBpmParams(Map<String, Object> bpmParams, HsQwApply hsQwApply, HsQwApply hsQwApplyOld);

//    void updateStatus(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService);

    String getNextStatus(HsQwApply hsQwApply);

    String getPreviousStatus(HsQwApply hsQwApply);

    void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService);

    default void process(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
//        hsQwApply.setStatus(this.getNextStatus(hsQwApply));
        hsQwApplyService.applyCheck(hsQwApply);//核验资格
        this.save(hsQwApply, hsQwApplyService);//保存申请单
        this.saveApplyer(hsQwApply,hsQwApplyService);//保存申请人
        this.bpmSave(hsQwApply, hsQwApplyService);//保存流程工单
        this.saveFile(hsQwApply, hsQwApplyService);//保存所有图片材料文件
        this.saveHouse(hsQwApply, hsQwApplyService);//保存配租房源信息
    }

    default void saveFile(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService){
        // 保存附件
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_file");
        // 保存房产图片
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_house");
        // 保存身份证明材料
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_idnum");
        // 保存婚姻证明材料
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_marry");
        // 保存申请家庭住房情况
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_house");
        // 保存人员类型证明材料
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_userType");
        // 保存申请家庭成员住房公积金证明
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_fund");
        // 保存申请家庭成员承诺材料
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_promise");
        // 保存申请家庭成员住房、收入及相关情况证明材料
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_income");
        // 保存其他
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_others");
        // 初审证明材料
        FileUploadUtils.saveFileUpload(hsQwApply, hsQwApply.getId(), "hsQwApply_first_check");
    }


    default void bpmSave(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        // 如果为审核状态，则进行审批流操作
        if (!HsQwApply.STATUS_DRAFT.equals(hsQwApply.getStatus())) {
            // 指定流程变量，作为流程条件，决定流转方向
            Map<String, Object> variables = MapUtils.newHashMap();
            variables.put("officeCode", hsQwApply.getOfficeCode());
            HsQwApply applyed = null;
            if (StringUtils.isNotBlank(hsQwApply.getApplyedId())){
                applyed = hsQwApplyService.get(hsQwApply.getApplyedId());
            }
            this.setBpmParams(variables,hsQwApply,applyed);
            // 如果流程实例为空，任务编号也为空，则：启动流程
            if (StringUtils.isBlank(hsQwApply.getBpm().getProcInsId())
                    && StringUtils.isBlank(hsQwApply.getBpm().getTaskId())) {
                BpmUtils.start(hsQwApply, this.getFormKey(), variables, null);
            }
            // 如果有任务信息，则：提交任务
            else {
                BpmUtils.complete(hsQwApply, variables, null);
            }
        }
    }

    default void initBpm(HsQwApply hsQwApply, BpmTask task){
        BpmParams bpm = hsQwApply.getBpm();
        bpm.setTaskId(task.getId());
        bpm.setProcInsId(task.getProcIns().getId());
        bpm.setNextUserCodes(task.getNextUserCodes());
        bpm.setActivityId(task.getActivityId());
        bpm.setDueDate(task.getDueDate());
        bpm.setComment(task.getComment());
    }

    default void saveApplyer(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService){
        // 保存 HsQwApply子表
        for (HsQwApplyer hsQwApplyer : hsQwApply.getHsQwApplyerList()) {
            if (!HsQwApplyer.STATUS_DELETE.equals(hsQwApplyer.getStatus())) {
                hsQwApplyer.setApplyId(hsQwApply.getId());
                if (StringUtils.isBlank(hsQwApplyer.getUserId())){
                    hsQwApplyer.setUserId(UserUtils.getUser().getId());//先默认值
                }
                if (hsQwApplyer.getIsNewRecord()) {
                    hsQwApplyService.getHsQwApplyerDao().insert(hsQwApplyer);
                } else {
                    hsQwApplyService.getHsQwApplyerDao().update(hsQwApplyer);
                }
            } else {
                hsQwApplyService.getHsQwApplyerDao().delete(hsQwApplyer);
            }
        }
    }

    default void saveHouse(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {

        // 保存 HsQwApplyHouse 子表
        for (HsQwApplyHouse hsQwApplyHouse : hsQwApply.getHsQwApplyHouseList()) {
            if (!HsQwApplyHouse.STATUS_DELETE.equals(hsQwApplyHouse.getStatus())) {
                hsQwApplyHouse.setApplyId(hsQwApply.getId());
                if (hsQwApplyHouse.getIsNewRecord()) {
                    hsQwApplyService.getHsQwApplyHouseDao().insert(hsQwApplyHouse);
                } else {
                    hsQwApplyService.getHsQwApplyHouseDao().update(hsQwApplyHouse);
                }
            } else {
                hsQwApplyService.getHsQwApplyHouseDao().delete(hsQwApplyHouse);
            }
        }
    }

    default void save(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        // 如果未设置状态，则指定状态为草稿状态
        if (StringUtils.isBlank(hsQwApply.getStatus())) {
            hsQwApply.setStatus(HsQwApply.STATUS_DRAFT);
        }
        if (StringUtils.isBlank(hsQwApply.getOfficeCode())) {
            hsQwApply.setOfficeCode(EmpUtils.getCurrentOfficeCode());
        }
        hsQwApplyService.saveProcess(hsQwApply);
//        hsQwApplyService.realUpdateStatus(hsQwApply);
    }
}
