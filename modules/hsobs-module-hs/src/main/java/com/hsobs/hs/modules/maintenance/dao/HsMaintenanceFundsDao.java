package com.hsobs.hs.modules.maintenance.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFunds;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 维修资金信息DAO接口
 * <AUTHOR>
 * @version 2024-11-28
 */
@MyBatisDao
public interface HsMaintenanceFundsDao extends CrudDao<HsMaintenanceFunds> {

    @Update("update hs_maintenance_funds set input_funds = input_funds + #{fund} where id = #{id}")
    void updateInputFunds(@Param("id") String id, @Param("fund") Double inputFunds);

    @Update("update hs_maintenance_funds set USED_FUNDS = USED_FUNDS + #{fund}, APPLY_FUNDS = APPLY_FUNDS - #{fund}  where id = #{id} ")
    void decreaseInputFund(@Param("id") String id, @Param("fund") Double fund);

    @Update("update hs_maintenance_funds set APPLY_FUNDS = APPLY_FUNDS + #{fund}  where id = #{id} ")
    void applyFund(@Param("id") String id, @Param("fund") Double fund);
}