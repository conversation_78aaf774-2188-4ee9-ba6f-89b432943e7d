package com.hsobs.hs.modules.formmanage.service;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplateField;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.word.WordExport;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplate;
import com.hsobs.hs.modules.formmanage.dao.HsDataFormTemplateDao;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 数据表单模板Service
 * <AUTHOR>
 * @version 2025-02-06
 */
@Service
public class HsDataFormTemplateService extends CrudService<HsDataFormTemplateDao, HsDataFormTemplate> {

	@Autowired
	private HsDataFormTemplateFieldService hsDataFormTemplateFieldService;

	/**
	 * 获取单条数据
	 * @param hsDataFormTemplate
	 * @return
	 */
	@Override
	public HsDataFormTemplate get(HsDataFormTemplate hsDataFormTemplate) {
		return super.get(hsDataFormTemplate);
	}
	
	/**
	 * 查询分页数据
	 * @param hsDataFormTemplate 查询条件
	 * @param hsDataFormTemplate page 分页对象
	 * @return
	 */
	@Override
	public Page<HsDataFormTemplate> findPage(HsDataFormTemplate hsDataFormTemplate) {
		return super.findPage(hsDataFormTemplate);
	}
	
	/**
	 * 查询列表数据
	 * @param hsDataFormTemplate
	 * @return
	 */
	@Override
	public List<HsDataFormTemplate> findList(HsDataFormTemplate hsDataFormTemplate) {
		return super.findList(hsDataFormTemplate);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsDataFormTemplate
	 */
	@Override
	@Transactional
	public void save(HsDataFormTemplate hsDataFormTemplate) {
		super.save(hsDataFormTemplate);
	}

	/**
	 * 保存表单字段
	 * @param hsDataFormTemplate
	 */
	public void saveField(HsDataFormTemplate hsDataFormTemplate) {
		if (hsDataFormTemplate.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		if (hsDataFormTemplate.getFieldList() != null) {
			Map<String, Integer> dupMap = new HashMap<String, Integer>();
			for (HsDataFormTemplateField field : hsDataFormTemplate.getFieldList()) {
				if (field == null) {
					continue;
				}
				if (dupMap.containsKey(field.getFieldKey())) {
					throw new ServiceException(text("数据异常，fieldKey不允许出现重复！"));
				}
				dupMap.put(field.getFieldKey(), 1);
			}
		}
		HsDataFormTemplateField query = new HsDataFormTemplateField();
		query.setTemplateId(hsDataFormTemplate.getId());
		query.sqlMap().getWhere().disableAutoAddStatusWhere();

		Map<String, HsDataFormTemplateField> dbMap = new HashMap<String, HsDataFormTemplateField>();

		List<HsDataFormTemplateField> dbList = hsDataFormTemplateFieldService.findList(query);
		if (dbList != null && !dbList.isEmpty()) {
			for (HsDataFormTemplateField field : dbList) {
				dbMap.put(field.getFieldKey(), field);
			}
		}
		if (hsDataFormTemplate.getFieldList() != null) {
			int index = 0;
			for (HsDataFormTemplateField field : hsDataFormTemplate.getFieldList()) {
				if (field == null || field.getFieldKey() == null || field.getFieldKey().isEmpty()) {
					continue;
				}
				field.setIndex(index);
				field.setTemplateId(hsDataFormTemplate.getId());
				field.setOrderNum(String.valueOf(100 + index));
				field.setValidTag("1");
				field.setStatus(HsDataFormTemplateField.STATUS_NORMAL);
				if (dbMap.containsKey(field.getFieldKey())) {
					field.setId(dbMap.get(field.getFieldKey()).getId());
					dbMap.remove(field.getFieldKey());
				}
				hsDataFormTemplateFieldService.save(field);
				index++;
			}
		}
		if (!dbMap.isEmpty()) {
			for (HsDataFormTemplateField field : dbMap.values()) {
				hsDataFormTemplateFieldService.delete(field);
			}
		}
	}

	/**
	 * 更新状态
	 * @param hsDataFormTemplate
	 */
	@Override
	@Transactional
	public void updateStatus(HsDataFormTemplate hsDataFormTemplate) {
		super.updateStatus(hsDataFormTemplate);
	}
	
	/**
	 * 删除数据
	 * @param hsDataFormTemplate
	 */
	@Override
	@Transactional
	public void delete(HsDataFormTemplate hsDataFormTemplate) {
		hsDataFormTemplate.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsDataFormTemplate);
	}


	public void exportFormData(HsDataFormTemplate hsDataFormTemplate, HttpServletResponse response) {
		if (hsDataFormTemplate.getId() == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}
		HsDataFormTemplate templateDb = this.get(hsDataFormTemplate.getId());
		if (templateDb == null) {
			throw new ServiceException(text("非法操作，前端数据被劫持！"));
		}

		String fileName = "表单配置-" + DateUtils.getDate("yyyyMMddHHmmss") + ".docx";
		try {

			WordExport export = new WordExport();
			String templateFileName = "classpath:file.template/hs_form_template_view.docx";
			export.setTemplate(templateFileName);

			Map<String, String> content = new HashMap<String, String>();
			content.put("title", templateDb.getFormName());
			content.put("formDesc", templateDb.getFormDesc());
			export.replaceBookMark(content);

			List<Map<String, String>> tableList = new ArrayList<Map<String, String>>();
			Map<String, String> table = null;
			String fieldType = "";

			HsDataFormTemplateField query = new HsDataFormTemplateField();
			query.setTemplateId(hsDataFormTemplate.getId());
			List<HsDataFormTemplateField> fieldList = hsDataFormTemplateFieldService.findList(query);
			if (fieldList != null && !fieldList.isEmpty()) {
				for (HsDataFormTemplateField field : fieldList) {
					table = new HashMap<String, String>();
					table.put("HS_FIELD_NAME", field.getFieldName());
					table.put("HS_FIELD_KEY", field.getFieldKey());

					// 3-单选 5-下拉 4-多选
					fieldType = ("3".equals(field.getFieldType()) || "5".equals(field.getFieldType())) ? "该字段为单选,请从以下值(逗号分隔)中选择一个: " + field.getFieldSubkey()
							: "4".equals(field.getFieldType()) ? "该字段为多选,请从以下值(逗号分隔)中选择一个或多个: " + field.getFieldSubkey() : "该字段为文本输入,请填写.";

					table.put("HS_FIELD_DESC", String.format("填写描述：%s, %s", field.getFieldDesc(), fieldType));
					tableList.add(table);
				}
			}
			export.fillTableAtBookMark("fieldTable", tableList);

			response.reset();
			response.setContentType("application/octet-stream; charset=utf-8");
			response.addHeader("Content-Disposition", "attachment; filename*=utf-8'zh_cn'"+ EncodeUtils.encodeUrl(fileName));
			export.write(response.getOutputStream());
		} catch (Exception e) {
			logger.error("export-form-template-exception", e);
		} finally {
		}
	}
}