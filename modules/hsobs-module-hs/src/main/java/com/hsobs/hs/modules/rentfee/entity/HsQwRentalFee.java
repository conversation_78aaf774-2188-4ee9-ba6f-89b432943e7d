package com.hsobs.hs.modules.rentfee.entity;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;
import java.util.Date;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.payment.entity.HsQwFeePayment;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.List;
import com.jeesite.common.collect.ListUtils;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁账单表Entity
 * <AUTHOR>
 * @version 2025-01-20
 */
@Table(name="hs_qw_rental_fee", alias="a", label="租赁账单表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="compact_id", attrName="compactId", label="合同编号", queryType=QueryType.LIKE),
		@Column(name="user_id", attrName="userId", label="用户编号"),
		@Column(name="rental_fee", attrName="rentalFee", label="租金费", isQuery=false),
		@Column(name="fee_month", attrName="feeMonth", label="缴费周期，按月生存账单"),
		@Column(includeEntity=DataEntity.class),
		@Column(name="fee_date", attrName="feeDate", label="缴费时间", isUpdateForce=true),
		@Column(name="expect_fee_date", attrName="expectFeeDate", label="预期缴费时间"),
		@Column(name="fee_type", attrName="feeType", label="租金类型", comment="租金类型（0物业费 1租金 2水费 3电费 4燃气费）"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwCompact.class, alias = "o",
				on = "o.id = a.compact_id", attrName="hsQwApply.compact",
				columns = {@Column(includeEntity = HsQwCompact.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApply.class, alias = "h",
				on = "h.id = o.apply_id", attrName="hsQwApply",
				columns = {@Column(includeEntity = HsQwApply.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "he",
				on = "he.apply_id = h.id and he.apply_role = 0 and he.status=0", attrName = "hsQwApply.mainApplyer",
				columns = {@Column(includeEntity = HsQwApplyer.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "hh",
				on = "hh.id = h.house_id ", attrName = "hsQwApply.hsQwApplyHouse",
				columns = {@Column(includeEntity = HsQwPublicRentalHouse.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "hre",
				on = "hre.id = hh.estate_id ", attrName="hsQwApply.hsQwApplyHouse.estate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)})
	},extWhereKeys = "extWhere", orderBy="a.update_date DESC"
)
public class HsQwRentalFee extends DataEntity<HsQwRentalFee> {
	
	private static final long serialVersionUID = 1L;
	private String compactId;		// 合同编号
	private Double rentalFee;		// 租金费
	private String feeMonth;		// 缴费周期，按月生存账单
	private Date feeDate;		// 缴费时间
	private Date expectFeeDate;		// 预期缴费时间
	private String userId;
	private String feeType;		// 租金类型（0物业费 1租金费）
	private List<HsQwFeePayment> hsQwFeePaymentList = ListUtils.newArrayList();		// 子表列表
	private HsQwApply hsQwApply;

	public HsQwRentalFee() {
		this(null);
	}
	
	public HsQwRentalFee(String id){
		super(id);
	}
	
	@NotBlank(message="合同编号不能为空")
	@Size(min=0, max=64, message="合同编号长度不能超过 64 个字符")
	public String getCompactId() {
		return compactId;
	}

	public void setCompactId(String compactId) {
		this.compactId = compactId;
	}
	
	@NotNull(message="租金费不能为空")
	public Double getRentalFee() {
		return rentalFee;
	}

	public void setRentalFee(Double rentalFee) {
		this.rentalFee = rentalFee;
	}
	
	@NotBlank(message="缴费周期，按月生存账单不能为空")
	@Size(min=0, max=20, message="缴费周期，按月生存账单长度不能超过 20 个字符")
	public String getFeeMonth() {
		return feeMonth;
	}

	public void setFeeMonth(String feeMonth) {
		this.feeMonth = feeMonth;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getFeeDate() {
		return feeDate;
	}

	public void setFeeDate(Date feeDate) {
		this.feeDate = feeDate;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="预期缴费时间不能为空")
	public Date getExpectFeeDate() {
		return expectFeeDate;
	}

	public void setExpectFeeDate(Date expectFeeDate) {
		this.expectFeeDate = expectFeeDate;
	}
	
	@NotBlank(message="租金类型不能为空")
	@Size(min=0, max=1, message="租金类型长度不能超过 1 个字符")
	public String getFeeType() {
		return feeType;
	}

	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}
	
	public Date getFeeDate_gte() {
		return sqlMap.getWhere().getValue("fee_date", QueryType.GTE);
	}

	public void setFeeDate_gte(Date feeDate) {
		sqlMap.getWhere().and("fee_date", QueryType.GTE, feeDate);
	}
	
	public Date getFeeDate_lte() {
		return sqlMap.getWhere().getValue("fee_date", QueryType.LTE);
	}

	public void setFeeDate_lte(Date feeDate) {
		sqlMap.getWhere().and("fee_date", QueryType.LTE, feeDate);
	}
	
	public Date getExpectFeeDate_gte() {
		return sqlMap.getWhere().getValue("expect_fee_date", QueryType.GTE);
	}

	public void setExpectFeeDate_gte(Date expectFeeDate) {
		sqlMap.getWhere().and("expect_fee_date", QueryType.GTE, expectFeeDate);
	}
	
	public Date getExpectFeeDate_lte() {
		return sqlMap.getWhere().getValue("expect_fee_date", QueryType.LTE);
	}

	public void setExpectFeeDate_lte(Date expectFeeDate) {
		sqlMap.getWhere().and("expect_fee_date", QueryType.LTE, expectFeeDate);
	}
	
	@Valid
	public List<HsQwFeePayment> getHsQwFeePaymentList() {
		return hsQwFeePaymentList;
	}

	public void setHsQwFeePaymentList(List<HsQwFeePayment> hsQwFeePaymentList) {
		this.hsQwFeePaymentList = hsQwFeePaymentList;
	}

	public HsQwApply getHsQwApply() {
		return hsQwApply;
	}

	public void setHsQwApply(HsQwApply hsQwApply) {
		this.hsQwApply = hsQwApply;
	}

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}