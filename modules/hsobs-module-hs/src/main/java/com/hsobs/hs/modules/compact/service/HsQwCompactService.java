package com.hsobs.hs.modules.compact.service;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.dao.HsQwCompactDao;

/**
 * 租赁资格轮候合同Service
 * <AUTHOR>
 * @version 2025-01-02
 */
@Service
public class HsQwCompactService extends CrudService<HsQwCompactDao, HsQwCompact> {
	
	/**
	 * 获取单条数据
	 * @param hsQwCompact
	 * @return
	 */
	@Override
	public HsQwCompact get(HsQwCompact hsQwCompact) {
		return super.get(hsQwCompact);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwCompact 查询条件
	 * @param hsQwCompact page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwCompact> findPage(HsQwCompact hsQwCompact) {
		return super.findPage(hsQwCompact);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwCompact
	 * @return
	 */
	@Override
	public List<HsQwCompact> findList(HsQwCompact hsQwCompact) {
		return super.findList(hsQwCompact);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwCompact
	 */
	@Override
	@Transactional
	public void save(HsQwCompact hsQwCompact) {
		super.save(hsQwCompact);
	}
	
	/**
	 * 更新状态
	 * @param hsQwCompact
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwCompact hsQwCompact) {
		super.updateStatus(hsQwCompact);
	}
	
	/**
	 * 删除数据
	 * @param hsQwCompact
	 */
	@Override
	@Transactional
	public void delete(HsQwCompact hsQwCompact) {
		super.delete(hsQwCompact);
	}

	public HsQwCompact getByApplyId(String applyId) {
		HsQwCompact query = new HsQwCompact();
		query.setApplyId(applyId);
		query.sqlMap().getOrder().setOrderBy("create_date desc");
		return this.findList(query).stream().findFirst().orElse(null);
	}
}