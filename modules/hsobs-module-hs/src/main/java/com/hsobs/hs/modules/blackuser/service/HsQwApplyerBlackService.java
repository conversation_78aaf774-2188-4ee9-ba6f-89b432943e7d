package com.hsobs.hs.modules.blackuser.service;

import java.util.List;

import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.dao.HsQwApplyerBlackDao;

/**
 * 租赁资格轮候租户黑名单Service
 * <AUTHOR>
 * @version 2024-12-19
 */
@Service
public class HsQwApplyerBlackService extends CrudService<HsQwApplyerBlackDao, HsQwApplyerBlack> {
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyerBlack
	 * @return
	 */
	@Override
	public HsQwApplyerBlack get(HsQwApplyerBlack hsQwApplyerBlack) {
		return super.get(hsQwApplyerBlack);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyerBlack 查询条件
	 * @param hsQwApplyerBlack page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyerBlack> findPage(HsQwApplyerBlack hsQwApplyerBlack) {
		return super.findPage(hsQwApplyerBlack);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyerBlack
	 * @return
	 */
	@Override
	public List<HsQwApplyerBlack> findList(HsQwApplyerBlack hsQwApplyerBlack) {
		return super.findList(hsQwApplyerBlack);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyerBlack
	 */
	@Override
	@Transactional
	public void save(HsQwApplyerBlack hsQwApplyerBlack) {
		if (!this.hadBlackUser(hsQwApplyerBlack.getId(),hsQwApplyerBlack.getUserId())){
			throw new ServiceException("该用户已录入黑名单");
		}
		super.save(hsQwApplyerBlack);
	}

	private boolean hadBlackUser(String id, String userId) {
		HsQwApplyerBlack hsQwApplyerBlack = new HsQwApplyerBlack();
		hsQwApplyerBlack.setUserId(userId);
		if (StringUtils.isNotBlank(id)){
			hsQwApplyerBlack.sqlMap().getWhere().and("id", QueryType.NE, id);
		}
		return this.findList(hsQwApplyerBlack).isEmpty();
	}

	/**
	 * 更新状态
	 * @param hsQwApplyerBlack
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyerBlack hsQwApplyerBlack) {
		super.updateStatus(hsQwApplyerBlack);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyerBlack
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyerBlack hsQwApplyerBlack) {
		super.delete(hsQwApplyerBlack);
	}

	/**
	 * 录入黑名单用户
	 * @param hsQwApplyerBlack
	 */
	@Transactional
	public void saveAndStatus(HsQwApplyerBlack hsQwApplyerBlack) {
		if (this.hadBlackUser(hsQwApplyerBlack.getId(), hsQwApplyerBlack.getUserId())){
			this.save(hsQwApplyerBlack);
		}
		super.updateStatus(hsQwApplyerBlack);
	}
	
}