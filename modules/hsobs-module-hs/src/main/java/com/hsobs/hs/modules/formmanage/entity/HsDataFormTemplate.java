package com.hsobs.hs.modules.formmanage.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;

import java.util.List;

/**
 * 数据表单模板Entity
 * <AUTHOR>
 * @version 2025-02-06
 */
@Table(name="hs_data_form_template", alias="a", label="数据表单模板信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="form_name", attrName="formName", label="表单名称", queryType=QueryType.LIKE),
		@Column(name="form_desc", attrName="formDesc", label="表单描述", queryType=QueryType.LIKE),
		@Column(name="form_type", attrName="formType", label="表单类型;1-普通 2-专项"),
		@Column(name="status", attrName="status", label="状态;1-正常 2-锁定 3-已删除", isUpdate=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=true),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=true, isUpdateForce=false),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = EmpUser.class, alias = "u",
			on = "u.user_code = a.create_by", attrName = "user",
			columns = {@Column(includeEntity = EmpUser.class)}
		),
		@JoinTable(type= JoinTable.Type.JOIN, entity= Employee.class, alias="e",
		    on="e.emp_code=u.ref_code AND u.user_type='employee'",
		    attrName="employee", columns={@Column(includeEntity= Employee.class)}
		),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= Office.class, alias="o",
				on="o.office_code = e.office_code",
				columns={@Column(includeEntity=Office.class)})
    }, orderBy="a.update_date DESC"
)
public class HsDataFormTemplate extends DataEntity<HsDataFormTemplate> {
	
	private static final long serialVersionUID = 1L;
	private String formName;		// 表单名称
	private String formDesc;		// 表单描述
	private String formType;		// 表单类型;1-普通 2-专项
	private String validTag;		// 是否有效;1-有效 0-无效

	private EmpUser user;
	private Employee employee;
	private Office office;


	private List<HsDataFormTemplateField> fieldList = ListUtils.newArrayList();

	public HsDataFormTemplate() {
		this(null);
	}
	
	public HsDataFormTemplate(String id){
		super(id);
	}
	
	@Size(min=0, max=255, message="表单名称长度不能超过 255 个字符")
	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}
	
	@Size(min=0, max=255, message="表单描述长度不能超过 255 个字符")
	public String getFormDesc() {
		return formDesc;
	}

	public void setFormDesc(String formDesc) {
		this.formDesc = formDesc;
	}
	
	@Size(min=0, max=64, message="表单类型;1-普通 2-专项长度不能超过 64 个字符")
	public String getFormType() {
		return formType;
	}

	public void setFormType(String formType) {
		this.formType = formType;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public List<HsDataFormTemplateField> getFieldList() {
		return fieldList;
	}

	public void setFieldList(List<HsDataFormTemplateField> fieldList) {
		this.fieldList = fieldList;
	}

	public EmpUser getUser() {
		return user;
	}

	public void setUser(EmpUser user) {
		this.user = user;
	}

	public Employee getEmployee() {
		return employee;
	}

	public void setEmployee(Employee employee) {
		this.employee = employee;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}
}