package com.hsobs.hs.modules.pricelimitapply.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;

import java.util.List;
import java.util.Map;

/**
 * 限价房-购房申请DAO接口
 * <AUTHOR>
 * @version 2024-11-28
 */
@MyBatisDao
public interface HsPriceLimitApplyDao extends CrudDao<HsPriceLimitApply> {

    List<Map<String, Object>> checkHouse(String applyId, String houseId);

    List<Map<String, Object>> selectHouseId(String applyId);
}