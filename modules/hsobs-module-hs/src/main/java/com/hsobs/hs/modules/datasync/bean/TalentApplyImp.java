package com.hsobs.hs.modules.datasync.bean;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * <AUTHOR>
 */
public class TalentApplyImp {

    private String serialNumber;
    private String name;
    private String degree;
    private String title;
    private String introductionUnit;
    private String introductionTime;
    private double distributionStandard;
    private double distributionSituationIn2024;

    @ExcelFields({
            @ExcelField(title = "序号", attrName = "serialNumber", align = ExcelField.Align.CENTER, sort = 10),
            @ExcelField(title = "姓名", attrName = "name", align = ExcelField.Align.CENTER, sort = 20),
            @ExcelField(title = "学位", attrName = "degree", align = ExcelField.Align.CENTER, sort = 30),
            @ExcelField(title = "职称", attrName = "title", align = ExcelField.Align.CENTER, sort = 40),
            @ExcelField(title = "引进单位", attrName = "introductionUnit", align = ExcelField.Align.CENTER, sort = 50),
            @ExcelField(title = "引进时间", attrName = "introductionTime", align = ExcelField.Align.CENTER, sort = 60),
            @ExcelField(title = "发放标准 (单位:万元)", attrName = "distributionStandard", align = ExcelField.Align.CENTER, sort = 70),
            @ExcelField(title = "24年发放情况(单位:万元)", attrName = "distributionSituationIn2024", align = ExcelField.Align.CENTER, sort = 80)
    })

    public TalentApplyImp() {
        super();
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIntroductionUnit() {
        return introductionUnit;
    }

    public void setIntroductionUnit(String introductionUnit) {
        this.introductionUnit = introductionUnit;
    }

    public String getIntroductionTime() {
        return introductionTime;
    }

    public void setIntroductionTime(String introductionTime) {
        this.introductionTime = introductionTime;
    }

    public double getDistributionStandard() {
        return distributionStandard;
    }

    public void setDistributionStandard(double distributionStandard) {
        this.distributionStandard = distributionStandard;
    }

    public double getDistributionSituationIn2024() {
        return distributionSituationIn2024;
    }

    public void setDistributionSituationIn2024(double distributionSituationIn2024) {
        this.distributionSituationIn2024 = distributionSituationIn2024;
    }
}
