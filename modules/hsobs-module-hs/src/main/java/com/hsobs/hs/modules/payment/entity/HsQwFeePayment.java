package com.hsobs.hs.modules.payment.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候物业费催单Entity
 * <AUTHOR>
 * @version 2025-01-20
 */
@Table(name="hs_qw_fee_payment", alias="a", label="租赁资格轮候物业费催单信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="fee_id", attrName="feeId", label="账单编号"),
		@Column(name="create_by", attrName="createBy", label="创建者", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新者", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false),
		@Column(name="remarks", attrName="remarks", label="备注信息", queryType=QueryType.LIKE),
		@Column(name="notice_type", attrName="noticeType", label="催收类型", comment="催收类型（0人工催收 1短信催收 2邮件催收 3小程序催收 4站内信催收）"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwRentalFee.class, alias = "o",
				on = "o.id = a.fee_id", attrName="hsQwRentalFee",
				columns = {@Column(includeEntity = HsQwRentalFee.class)})
	}, orderBy="a.update_date DESC"
)
public class HsQwFeePayment extends DataEntity<HsQwFeePayment> {
	
	private static final long serialVersionUID = 1L;
	private String feeId;		// 账单编号
	private String noticeType;		// 催收类型（0人工催收 1短信催收 2邮件催收 3小程序催收 4站内信催收）
	private HsQwRentalFee hsQwRentalFee;

	public HsQwFeePayment() {
		this(null);
	}
	
	public HsQwFeePayment(String id){
		super(id);
	}
	
	@NotBlank(message="账单编号不能为空")
	@Size(min=0, max=64, message="账单编号长度不能超过 64 个字符")
	public String getFeeId() {
		return feeId;
	}

	public void setFeeId(String feeId) {
		this.feeId = feeId;
	}
	
	@NotBlank(message="催收类型不能为空")
	@Size(min=0, max=1, message="催收类型长度不能超过 1 个字符")
	public String getNoticeType() {
		return noticeType;
	}

	public void setNoticeType(String noticeType) {
		this.noticeType = noticeType;
	}

    public HsQwRentalFee getHsQwRentalFee() {
        return hsQwRentalFee;
    }

    public void setHsQwRentalFee(HsQwRentalFee hsQwRentalFee) {
        this.hsQwRentalFee = hsQwRentalFee;
    }
}