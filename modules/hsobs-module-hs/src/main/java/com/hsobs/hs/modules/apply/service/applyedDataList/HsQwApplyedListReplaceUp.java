package com.hsobs.hs.modules.apply.service.applyedDataList;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.entity.Page;
import org.springframework.stereotype.Service;

/**
 * 公租房承租置换申请
 */
@Service
public class HsQwApplyedListReplaceUp implements HsQwApplyedList{

    @Override
    public String getDataType() {
        return "2";
    }

    @Override
    public Page<HsQwApply> execute(HsQwApply hsQwApply,  HsQwApplyService hsQwApplyService) {
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwApply.setStatus(null);
        hsQwApply.sqlMap().add("extWhere", " and (a.STATUS = 0\n" +
                "\t\tAND a.APPLY_MATTER in (0))");
        return hsQwApplyService.findPage(hsQwApply);
    }
}
