package com.hsobs.hs.modules.formmanage.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormDeliveryRecord;

/**
 * 数据表单下发发送记录表DAO接口
 * <AUTHOR>
 * @version 2025-02-16
 */
@MyBatisDao
public interface HsDataFormDeliveryRecordDao extends CrudDao<HsDataFormDeliveryRecord> {

    long updateReadStatus(HsDataFormDeliveryRecord record);
}