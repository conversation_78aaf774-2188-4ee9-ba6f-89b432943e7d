package com.hsobs.hs.modules.housrvr.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.housrvr.entity.HsQwHouseVr;
import com.hsobs.hs.modules.housrvr.dao.HsQwHouseVrDao;

/**
 * 房源VR信息表Service
 * <AUTHOR>
 * @version 2025-03-20
 */
@Service
public class HsQwHouseVrService extends CrudService<HsQwHouseVrDao, HsQwHouseVr> {
	
	/**
	 * 获取单条数据
	 * @param hsQwHouseVr
	 * @return
	 */
	@Override
	public HsQwHouseVr get(HsQwHouseVr hsQwHouseVr) {
		return super.get(hsQwHouseVr);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwHouseVr 查询条件
	 * @param hsQwHouseVr page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwHouseVr> findPage(HsQwHouseVr hsQwHouseVr) {
		return super.findPage(hsQwHouseVr);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwHouseVr
	 * @return
	 */
	@Override
	public List<HsQwHouseVr> findList(HsQwHouseVr hsQwHouseVr) {
		return super.findList(hsQwHouseVr);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwHouseVr
	 */
	@Override
	@Transactional
	public void save(HsQwHouseVr hsQwHouseVr) {
		super.save(hsQwHouseVr);
	}
	
	/**
	 * 更新状态
	 * @param hsQwHouseVr
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwHouseVr hsQwHouseVr) {
		super.updateStatus(hsQwHouseVr);
	}
	
	/**
	 * 删除数据
	 * @param hsQwHouseVr
	 */
	@Override
	@Transactional
	public void delete(HsQwHouseVr hsQwHouseVr) {
		super.delete(hsQwHouseVr);
	}
	
}