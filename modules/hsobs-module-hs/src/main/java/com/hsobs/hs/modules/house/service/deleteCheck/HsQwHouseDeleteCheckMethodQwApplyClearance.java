package com.hsobs.hs.modules.house.service.deleteCheck;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.bureau.dao.HsQwApplyBureauDao;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.clearance.dao.HsQwClearanceDao;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 清退申请，查询所有房源的申请单
 */
@Service
public class HsQwHouseDeleteCheckMethodQwApplyClearance implements HsQwHouseDeleteCheckMethod {
    @Autowired
    private HsQwClearanceDao hsQwClearanceDao;

    @Override
    public void execute(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        HsQwClearance query = new HsQwClearance();
        query.setStatus_in(new String[]{ HsQwApply.STATUS_AUDIT});
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.sqlMap().getWhere().and("ha.house_id", QueryType.EQ, hsQwPublicRentalHouse.getId());
        long count = hsQwClearanceDao.findCount(query);
        if (count>0){
            throw new ServiceException("该房源存在关联的清退申请单，请先取消申请单后再重新操作！");
        }
    }
}
