package com.hsobs.hs.modules.apply.web;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.sys.utils.EmpUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 租赁资格轮候申请Controller
 *
 * <AUTHOR>
 * @version 2024-11-21
 */
@Controller
@RequestMapping(value = "${adminPath}/apply/hsQwApplyMzt")
public class HsQwApplyMztController extends BaseController {

    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private BpmTaskService bpmTaskService;

    /**
     * 获取数据
     */
    @ModelAttribute
    public HsQwApply get(String id, boolean isNewRecord) {
        return hsQwApplyService.get(id, isNewRecord);
    }

    /**
     * 查询列表
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = {"list", ""})
    public String list(HsQwApply hsQwApply, Model model) {
        model.addAttribute("hsQwApply", hsQwApply);
        return "modules/apply/mzt/hsQwApplyListMzt";
    }

    /**
     * 查询列表数据
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public Page<HsQwApply> listData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
        hsQwApply.setPage(new Page<>(request, response));
        Page<HsQwApply> page = hsQwApplyService.findMztPage(hsQwApply, "rent_apply");
        return page;
    }

    /**
     * 查看编辑表单
     */
    @RequiresPermissions("apply:hsQwApply:view")
    @RequestMapping(value = "form")
    public String form(HsQwApply hsQwApply, Model model) {
        if (hsQwApply.getIsNewRecord()) {
            hsQwApply.setOfficeCode(EmpUtils.getOffice().getOfficeCode());
            hsQwApply.setOffice(EmpUtils.getOffice());
            hsQwApply.setHsQwApplyerList(hsQwApplyService.getCurrentUserInfo(hsQwApply));
        }
        model.addAttribute("hsQwApply", hsQwApply);
        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
        return "modules/apply/mzt/hsQwApplyFormMzt";
    }

}