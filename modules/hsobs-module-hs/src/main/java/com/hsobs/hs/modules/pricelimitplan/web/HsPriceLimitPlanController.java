package com.hsobs.hs.modules.pricelimitplan.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.pricelimitplan.entity.HsPriceLimitPlan;
import com.hsobs.hs.modules.pricelimitplan.service.HsPriceLimitPlanService;

/**
 * 拟定限价房方案Controller
 * <AUTHOR>
 * @version 2025-02-13
 */
@Controller
@RequestMapping(value = "${adminPath}/pricelimitplan/hsPriceLimitPlan")
public class HsPriceLimitPlanController extends BaseController {

	@Autowired
	private HsPriceLimitPlanService hsPriceLimitPlanService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPriceLimitPlan get(String id, boolean isNewRecord) {
		return hsPriceLimitPlanService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPriceLimitPlan hsPriceLimitPlan, Model model) {
		model.addAttribute("hsPriceLimitPlan", hsPriceLimitPlan);
		return "modules/pricelimitplan/hsPriceLimitPlanList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsPriceLimitPlan> listData(HsPriceLimitPlan hsPriceLimitPlan, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitPlan.setPage(new Page<>(request, response));
		Page<HsPriceLimitPlan> page = hsPriceLimitPlanService.findPage(hsPriceLimitPlan);
		return page;
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:view")
	@RequestMapping(value = "exportData")
	public void exportAuditData(HsPriceLimitPlan hsPriceLimitPlan, HttpServletResponse response) {
		List<HsPriceLimitPlan> list = hsPriceLimitPlanService.findList(hsPriceLimitPlan);
		String fileName = "限价房方案信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("限价房方案信息表", HsPriceLimitPlan.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:view")
	@RequestMapping(value = "form")
	public String form(HsPriceLimitPlan hsPriceLimitPlan, Model model) {
		model.addAttribute("hsPriceLimitPlan", hsPriceLimitPlan);
		return "modules/pricelimitplan/hsPriceLimitPlanForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsPriceLimitPlan hsPriceLimitPlan) {
		hsPriceLimitPlanService.save(hsPriceLimitPlan);

		if (hsPriceLimitPlan.getIsNewRecord()) {
			return renderResult(Global.TRUE, text("新增限价房方案成功！"));
		}
		return renderResult(Global.TRUE, text("编辑限价房方案成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsPriceLimitPlan hsPriceLimitPlan) {
		hsPriceLimitPlan.setStatus(HsPriceLimitPlan.STATUS_DISABLE);
		hsPriceLimitPlanService.updateStatus(hsPriceLimitPlan);
		return renderResult(Global.TRUE, text("停用限价房方案成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsPriceLimitPlan hsPriceLimitPlan) {
		hsPriceLimitPlan.setStatus(HsPriceLimitPlan.STATUS_NORMAL);
		hsPriceLimitPlanService.updateStatus(hsPriceLimitPlan);
		return renderResult(Global.TRUE, text("启用限价房方案成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsPriceLimitPlan hsPriceLimitPlan) {
		hsPriceLimitPlanService.delete(hsPriceLimitPlan);
		return renderResult(Global.TRUE, text("删除限价房方案成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("pricelimitplan:hsPriceLimitPlan:view")
	@RequestMapping(value = "hsPriceLimitPlanSelect")
	public String hsPriceLimitPlanSelect(HsPriceLimitPlan hsPriceLimitPlan, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsPriceLimitPlan", hsPriceLimitPlan);
		return "modules/pricelimitplan/hsPriceLimitPlanSelect";
	}

}