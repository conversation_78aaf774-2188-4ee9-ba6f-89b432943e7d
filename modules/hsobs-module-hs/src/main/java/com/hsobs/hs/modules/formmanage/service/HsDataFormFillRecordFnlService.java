package com.hsobs.hs.modules.formmanage.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormFillRecordFnl;
import com.hsobs.hs.modules.formmanage.dao.HsDataFormFillRecordFnlDao;

/**
 * 数据表单填写详情记录表Service
 * <AUTHOR>
 * @version 2025-05-05
 */
@Service
public class HsDataFormFillRecordFnlService extends CrudService<HsDataFormFillRecordFnlDao, HsDataFormFillRecordFnl> {
	
	/**
	 * 获取单条数据
	 * @param hsDataFormFillRecordFnl
	 * @return
	 */
	@Override
	public HsDataFormFillRecordFnl get(HsDataFormFillRecordFnl hsDataFormFillRecordFnl) {
		return super.get(hsDataFormFillRecordFnl);
	}
	
	/**
	 * 查询分页数据
	 * @param hsDataFormFillRecordFnl 查询条件
	 * @param hsDataFormFillRecordFnl page 分页对象
	 * @return
	 */
	@Override
	public Page<HsDataFormFillRecordFnl> findPage(HsDataFormFillRecordFnl hsDataFormFillRecordFnl) {
		return super.findPage(hsDataFormFillRecordFnl);
	}
	
	/**
	 * 查询列表数据
	 * @param hsDataFormFillRecordFnl
	 * @return
	 */
	@Override
	public List<HsDataFormFillRecordFnl> findList(HsDataFormFillRecordFnl hsDataFormFillRecordFnl) {
		return super.findList(hsDataFormFillRecordFnl);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsDataFormFillRecordFnl
	 */
	@Override
	@Transactional
	public void save(HsDataFormFillRecordFnl hsDataFormFillRecordFnl) {
		super.save(hsDataFormFillRecordFnl);
	}
	
	/**
	 * 更新状态
	 * @param hsDataFormFillRecordFnl
	 */
	@Override
	@Transactional
	public void updateStatus(HsDataFormFillRecordFnl hsDataFormFillRecordFnl) {
		super.updateStatus(hsDataFormFillRecordFnl);
	}
	
	/**
	 * 删除数据
	 * @param hsDataFormFillRecordFnl
	 */
	@Override
	@Transactional
	public void delete(HsDataFormFillRecordFnl hsDataFormFillRecordFnl) {
		super.delete(hsDataFormFillRecordFnl);
	}
	
}