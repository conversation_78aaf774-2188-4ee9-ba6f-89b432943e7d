package com.hsobs.hs.modules.dataintelligence.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.dataintelligence.service.DataIntelligenceRepairFundService;

import java.util.List;

/**
 * 住房保障数据统计Controller  维修资金情况统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Controller
@RequestMapping(value = "${adminPath}/dataintelligencerepair/")
public class DataIntelligenceRepairFundController extends BaseController {

	@Autowired
	private DataIntelligenceRepairFundService dataIntelligenceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public DataIntelligenceRepairFund get(Integer type, boolean isNewRecord) {
		return dataIntelligenceService.get(type, isNewRecord);
	}

	/**
	 * 维修资金情况统计
	 */
	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = {"dataIntelligenceRepairFund", ""})
	public String dataIntelligenceRepairFund(DataIntelligenceRepairFund dataIntelligenceRepairFund, Model model) {
		model.addAttribute("dataIntelligence", dataIntelligenceRepairFund);
		return "modules/dataintelligence/dataIntelligenceRepairFund";
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "repairFundStatData")
	@ResponseBody
	public Page<DataIntelligenceRepairFund> repairFundStatData(DataIntelligenceRepairFund dataIntelligenceRepairFund, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceRepairFund.setPage(new Page<>(request, response));
		Page<DataIntelligenceRepairFund> page = dataIntelligenceService.findResourceDataPage(dataIntelligenceRepairFund, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "repairFundStatTotal")
	@ResponseBody
	public Page<DataIntelligenceRepairFund> repairFundStatTotal(DataIntelligenceRepairFund dataIntelligenceRepairFund, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceRepairFund.setPage(new Page<>(request, response));
		Page<DataIntelligenceRepairFund> page = dataIntelligenceService.findRepairFundStatTotalPage(dataIntelligenceRepairFund, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "repairFundStatOffice")
	@ResponseBody
	public Page<DataIntelligenceRepairFund> repairFundStatOffice(DataIntelligenceRepairFund dataIntelligenceRepairFund, HttpServletRequest request, HttpServletResponse response) {
		dataIntelligenceRepairFund.setPage(new Page<>(request, response));
		Page<DataIntelligenceRepairFund> page = dataIntelligenceService.findRepairFundStatOfficePage(dataIntelligenceRepairFund, true);
		return page;
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "repairFundTotal")
	@ResponseBody
	public String repairFundTotal(DataIntelligenceRepairFund dataIntelligenceRepairFund, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findLiftRepairFundTotal(dataIntelligenceRepairFund);
	}

	/**
	 * 查询办公用房资源情况统计视图数据
	 */
	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "repairFundCompare")
	@ResponseBody
	public String repairFundCompare(DataIntelligenceRepairFund dataIntelligenceRepairFund, HttpServletRequest request, HttpServletResponse response) {
		return dataIntelligenceService.findLiftRepairFundCompare(dataIntelligenceRepairFund);
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("dataintelligencerepair::view")
	@RequestMapping(value = "exportData")
	public void exportData(DataIntelligenceRepairFund dataIntelligenceRepairFund, HttpServletResponse response) {
		String fileName = "维修资金情况统计表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try {
			ExcelExport ee = new ExcelExport("维修资金情况统计表", DataIntelligenceRepairFund.class);
			List<DataIntelligenceRepairFund> list = dataIntelligenceService.findRepairFundStatAreaData(dataIntelligenceRepairFund);
			ee.setDataList(list);

			ee.write(response, fileName);
		}
		catch (Exception e){
		}
	}
}