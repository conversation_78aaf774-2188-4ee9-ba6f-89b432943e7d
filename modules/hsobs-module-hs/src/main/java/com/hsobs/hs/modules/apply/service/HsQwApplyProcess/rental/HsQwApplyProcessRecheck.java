package com.hsobs.hs.modules.apply.service.HsQwApplyProcess.rental;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyrule.service.HsQwApplyRuleService;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import com.hsobs.hs.modules.formalarm.service.HsQwFormAlarmService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class HsQwApplyProcessRecheck extends HsQwApplyProcessDefault {

    @Autowired
    HsQwFormAlarmService hsQwFormAlarmService;

    @Autowired
    HsQwApplyRuleService hsQwApplyRuleService;

    @Override
    public String getStatus() {
        return "配租复查确认";
    }

    @Override
    public void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        Map<String, HsQwFormAlarm> hsQwFormAlarmMap = new HashMap<>();
        // 修改信息比对
        hsQwApplyService.applyModifyCheck(hsQwApply, hsQwFormAlarmMap);
        // 主流程执行
        this.process(hsQwApply, hsQwApplyService);
        // 计算轮候得分
        if (hsQwApply.getScoreUpdate()==null || hsQwApply.getScoreUpdate().equals("0")) {
            hsQwApplyService.refreshByApply(hsQwApply);
        }
        // 资格核验
        hsQwApplyService.applyRuleCheck(hsQwApply, hsQwFormAlarmMap);

        if (hsQwFormAlarmMap.size() > 0) {
            // 先删除
            HsQwFormAlarm where = new HsQwFormAlarm();
            where.setObjectId(hsQwApply.getId());
            hsQwFormAlarmService.deleteBatch(where);
            // 解析出map中的key，value，批量插入数据库，整合成list
            List<HsQwFormAlarm> list = new ArrayList<>();
            hsQwFormAlarmMap.forEach((k, v) -> {
                if (k.indexOf("recheckStatus")>-1){
                    return;
                }
                list.add(v);
            });
            hsQwFormAlarmService.saveBatch(list);
        }

    }

}
