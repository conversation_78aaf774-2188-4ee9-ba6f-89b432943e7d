package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取单位下用户列表响应实体 角色数据
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleData implements java.io.Serializable {

    public static final long serialVersionUID = 1L;

    private String roleId;
    private String roleName;
    private String roleCode;
    private String intro;

}
