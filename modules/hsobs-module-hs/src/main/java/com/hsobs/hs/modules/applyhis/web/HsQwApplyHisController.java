package com.hsobs.hs.modules.applyhis.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.applyhis.entity.HsQwApplyHis;
import com.hsobs.hs.modules.applyhis.service.HsQwApplyHisService;

/**
 * hs_qw_apply_hisController
 * <AUTHOR>
 * @version 2025-07-12
 */
@Controller
@RequestMapping(value = "${adminPath}/applyhis/hsQwApplyHis")
public class HsQwApplyHisController extends BaseController {

	@Autowired
	private HsQwApplyHisService hsQwApplyHisService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyHis get(String id, boolean isNewRecord) {
		return hsQwApplyHisService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("applyhis:hsQwApplyHis:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyHis hsQwApplyHis, Model model) {
		model.addAttribute("hsQwApplyHis", hsQwApplyHis);
		return "modules/applyhis/hsQwApplyHisList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("applyhis:hsQwApplyHis:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyHis> listData(HsQwApplyHis hsQwApplyHis, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyHis.setPage(new Page<>(request, response));
		Page<HsQwApplyHis> page = hsQwApplyHisService.findPage(hsQwApplyHis);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("applyhis:hsQwApplyHis:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyHis hsQwApplyHis, Model model) {
		model.addAttribute("hsQwApplyHis", hsQwApplyHis);
		return "modules/applyhis/hsQwApplyHisForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("applyhis:hsQwApplyHis:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyHis hsQwApplyHis) {
		hsQwApplyHisService.save(hsQwApplyHis);
		return renderResult(Global.TRUE, text("保存hs_qw_apply_his成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("applyhis:hsQwApplyHis:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyHis hsQwApplyHis) {
		hsQwApplyHisService.delete(hsQwApplyHis);
		return renderResult(Global.TRUE, text("删除hs_qw_apply_his成功！"));
	}
	
}