package com.hsobs.hs.modules.apply.service.serviceTask;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyhouse.service.HsQwApplyHouseService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 申请资格审核-满足资格审核，执行更新原租赁信息
 */
@Component
public class HsQwApplyPersonUpdateServiceTask implements JavaDelegate {
    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private HsQwCompactService hsQwCompactService;

    @Transactional
    @Override
    public void execute(DelegateExecution delegateExecution) {
        /*****************获取新旧申请单信息********************/
        HsQwApply realBean = hsQwApplyService.get(BpmUtils.getBizKey(delegateExecution));
        // 信息申请单生效，原申请单所有信息失效
        // 获取旧订单信息
        HsQwApply history = hsQwApplyService.getBase(realBean.getApplyedId());
        /*****************旧申请单信息更新********************/
        // 如果是个人调租申请单，并且状态为成功的，需要将原申请单的状态设置为不可用
        hsQwApplyService.invalidApplyInfo(history);
        /*****************新申请单信息更新********************/
        // 新房源状态更新
        if (history.getHouseId() != null) {
            realBean.setHouseId(history.getHouseId());//强制更新同步
            hsQwApplyService.updateHouseStatus(history.getHouseId(), "1");// 流程结束，已配租
        }
        // 新合同更新
        if (realBean.getCompact() == null){
            if (history.getCompact() != null ) {
                //深拷贝
                HsQwCompact compact = history.getCompact();
                compact.setId(null);//置空，完成拷贝复制新增
                compact.setStatus("0");
                compact.setApplyId(realBean.getId());
                hsQwCompactService.save(compact);
            }
        } else {
            //更新合同
            realBean.getCompact().setStatus(HsQwCompact.STATUS_NORMAL);// 流程结束，更新合同状态
            hsQwCompactService.updateStatus(realBean.getCompact());
        }
        //信息变更更新
        realBean.setApplyTime(history.getApplyTime());
        realBean.setRentTime(history.getRentTime());
        hsQwApplyService.update(realBean);
    }
}
