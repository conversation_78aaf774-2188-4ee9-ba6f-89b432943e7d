package com.hsobs.hs.modules.apply.service.serviceTask;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.apply.service.updateStatus.HsQwApplyStatusBase;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyhouse.service.HsQwApplyHouseService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 申请资格审核-满足资格审核，执行更新原租赁信息
 */
@Component
public class HsQwApplyPersonUpdateServiceTask implements JavaDelegate {
    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private HsQwCompactService hsQwCompactService;

    @Autowired
    private HsQwApplyStatusBase hsQwApplyStatusBase;

    @Override
    public void execute(DelegateExecution delegateExecution) {
        /*****************获取新旧申请单信息********************/
        // 获取新申请单
        HsQwApply newHsQwApply = hsQwApplyService.get(BpmUtils.getBizKey(delegateExecution));
        // 获取旧订单信息
        HsQwApply oldHsQwApply = hsQwApplyService.getBase(newHsQwApply.getApplyedId());

        /*****************旧申请单信息归档备份********************/
        hsQwApplyStatusBase.saveToApplyHis(oldHsQwApply);

        /*****************
         • 旧申请单关联人：删除之后拷贝新申请单关联人
         • 旧申请单关联房：删除之后拷贝新申请单关联房
         • 新申请单关联人：拷贝
         • 新申请单关联房：拷贝
         ********************/
        hsQwApplyStatusBase.copyAndInvalidApplyer(oldHsQwApply, newHsQwApply);
        hsQwApplyStatusBase.copyAndInvalidApplyHouse(oldHsQwApply, newHsQwApply);
    }
}
