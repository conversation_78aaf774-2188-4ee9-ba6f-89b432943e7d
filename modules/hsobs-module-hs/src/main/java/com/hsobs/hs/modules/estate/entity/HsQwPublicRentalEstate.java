package com.hsobs.hs.modules.estate.entity;

import javax.validation.constraints.*;
import java.util.Date;

import com.hsobs.hs.modules.external.entity.HsMapTo;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.checkerframework.checker.units.qual.min;

/**
 * 公租房房源楼盘信息表Entity
 * <AUTHOR>
 * @version 2024-11-20
 */
@Table(name="hs_qw_public_rental_estate", alias="a", label="公租房房源楼盘信息表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="name", attrName="name", label="楼盘名称", queryType=QueryType.LIKE),
		@Column(name="year", attrName="year", label="建造年代", isUpdateForce=true),
		@Column(name="term", attrName="term", label="产权年限", isQuery=false, isUpdateForce=true),
		@Column(name="developer", attrName="developer", label="建设单位", queryType=QueryType.LIKE),
		@Column(name="plot_ratio", attrName="plotRatio", label="容积率", isQuery=false),
		@Column(name="green_rate", attrName="greenRate", label="绿化率", isQuery=false),
		@Column(name="households", attrName="households", label="小区户数", isQuery=false, isUpdateForce=true),
		@Column(name="park_num", attrName="parkNum", label="车位数", isQuery=false, isUpdateForce=true),
		@Column(name="longitude", attrName="longitude", label="经度", isQuery=false),
		@Column(name="latitude", attrName="latitude", label="纬度", isQuery=false),
		@Column(name="management_company", attrName="managementCompany", label="物业公司", queryType=QueryType.LIKE),
		@Column(name="management_fee", attrName="managementFee", label="物业费", isQuery=false),
		@Column(name="power_supply", attrName="powerSupply", label="供电方式", comment="供电方式（0-，1-，2-）", isQuery=false),
		@Column(name="water_supply", attrName="waterSupply", label="供水方式", comment="供水方式（0-，1-，2-）", isQuery=false),
		@Column(name="vr_info", attrName="vrInfo", label="VR信息", isQuery=false),
		@Column(name="surrounding_plan", attrName="surroundingPlan", label="周边规划", isQuery=false),
		@Column(includeEntity=DataEntity.class),
		@Column(name="address", attrName="address", label="楼盘地址", queryType=QueryType.LIKE),
		@Column(name="public_area", attrName="publicArea", label="公摊总面积", comment="公摊总面积", isQuery=false),
		@Column(name="water_fee", attrName="waterFee", label="公摊水费", comment="公摊水费（每平米）", isQuery=false),
		@Column(name="electric_fee", attrName="electricFee", label="公摊电费", comment="公摊电费（每平米）", isQuery=false),
		@Column(name="estate_status", attrName="estateStatus", label="楼盘状态", comment="楼盘状态楼盘状态", isQuery=false),
		@Column(name="city", attrName="city", label="城市", comment="城市", isQuery=false),
		@Column(name="area", attrName="area", label="区域", comment="区域", isQuery=false),
		@Column(name="property_developer", attrName="propertyDeveloper", label="开发商", comment="开发商", isQuery=false),
	}, orderBy="a.update_date DESC"
)
public class HsQwPublicRentalEstate extends DataEntity<HsQwPublicRentalEstate> {
	
	private static final long serialVersionUID = 1L;
	@HsMapTo("lpmc")
	private String name;		// 楼盘名称
	private Date year;		// 建造年代
	private Long term;		// 产权年限
	private String developer;		// 建设单位
	private String plotRatio;		// 容积率
	private String greenRate;		// 绿化率
	private Long households;		// 小区户数
	private Long parkNum;		// 车位数
	@HsMapTo("longitude")
	private String longitude;		// 经度
	@HsMapTo("latitude")
	private String latitude;		// 纬度
	private String managementCompany;		// 物业公司
	private Double managementFee;		// 物业费
	private String powerSupply;		// 供电方式（0-，1-，2-）
	private String waterSupply;		// 供水方式（0-，1-，2-）
	private String vrInfo;		// VR信息
	private String surroundingPlan;		// 周边规划
	@HsMapTo("lpdz")
	private String address;		// 楼盘地址
	private Double waterFee;		// 公摊水费（每平米）
	private Double electricFee;		// 公摊电费（每平米）
	private Double publicArea;
	private String estateStatus;
	@HsMapTo("qhbm")
	private String city;
	private String area;
	private String propertyDeveloper;

	private String fylx;

	public HsQwPublicRentalEstate() {
		this(null);
	}
	
	public HsQwPublicRentalEstate(String id){
		super(id);
	}
	
	@NotBlank(message="楼盘名称不能为空")
	@Size(min=0, max=255, message="楼盘名称长度不能超过 255 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@JsonFormat(pattern = "yyyy-MM")
	public Date getYear() {
		return year;
	}

	public void setYear(Date year) {
		this.year = year;
	}
	
	public Long getTerm() {
		return term;
	}

	public void setTerm(Long term) {
		this.term = term;
	}
	
	@Size(min=0, max=255, message="建设单位长度不能超过 255 个字符")
	public String getDeveloper() {
		return developer;
	}

	public void setDeveloper(String developer) {
		this.developer = developer;
	}
	
	@Size(min=0, max=20, message="容积率长度不能超过 20 个字符")
	@Max(value = 100, message = "容积率不能大于100%")
	@Min(value = 0, message = "容积率不能小于0%")
	public String getPlotRatio() {
		return plotRatio;
	}

	public void setPlotRatio(String plotRatio) {
		this.plotRatio = plotRatio;
	}
	
	@Size(min=0, max=20, message="绿化率长度不能超过 20 个字符")
	@Max(value = 100, message = "绿化率不能大于100%")
	@Min(value = 0, message = "绿化率不能小于0%")
	public String getGreenRate() {
		return greenRate;
	}

	public void setGreenRate(String greenRate) {
		this.greenRate = greenRate;
	}
	
	public Long getHouseholds() {
		return households;
	}

	public void setHouseholds(Long households) {
		this.households = households;
	}
	
	public Long getParkNum() {
		return parkNum;
	}

	public void setParkNum(Long parkNum) {
		this.parkNum = parkNum;
	}
	
	@Size(min=0, max=20, message="经度长度不能超过 20 个字符")
	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}
	
	@Size(min=0, max=20, message="纬度长度不能超过 20 个字符")
	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}
	
	@Size(min=0, max=255, message="物业公司长度不能超过 255 个字符")
	public String getManagementCompany() {
		return managementCompany;
	}

	public void setManagementCompany(String managementCompany) {
		this.managementCompany = managementCompany;
	}
	
	@NotNull(message="物业费不能为空")
	public Double getManagementFee() {
		return managementFee;
	}

	public void setManagementFee(Double managementFee) {
		this.managementFee = managementFee;
	}
	
	@Size(min=0, max=100, message="供电方式长度不能超过 100 个字符")
	public String getPowerSupply() {
		return powerSupply;
	}

	public void setPowerSupply(String powerSupply) {
		this.powerSupply = powerSupply;
	}
	
	@Size(min=0, max=100, message="供水方式长度不能超过 100 个字符")
	public String getWaterSupply() {
		return waterSupply;
	}

	public void setWaterSupply(String waterSupply) {
		this.waterSupply = waterSupply;
	}
	
	@Size(min=0, max=255, message="VR信息长度不能超过 255 个字符")
	public String getVrInfo() {
		return vrInfo;
	}

	public void setVrInfo(String vrInfo) {
		this.vrInfo = vrInfo;
	}
	
	@Size(min=0, max=500, message="周边规划长度不能超过 500 个字符")
	public String getSurroundingPlan() {
		return surroundingPlan;
	}

	public void setSurroundingPlan(String surroundingPlan) {
		this.surroundingPlan = surroundingPlan;
	}
	
	@Size(min=0, max=255, message="楼盘地址长度不能超过 255 个字符")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	@NotNull(message="公摊水费不能为空")
	public Double getWaterFee() {
		return waterFee;
	}

	public void setWaterFee(Double waterFee) {
		this.waterFee = waterFee;
	}

	@NotNull(message="公摊电费不能为空")
	public Double getElectricFee() {
		return electricFee;
	}

	public void setElectricFee(Double electricFee) {
		this.electricFee = electricFee;
	}

	public Double getPublicArea() {
		return publicArea;
	}

	public void setPublicArea(Double publicArea) {
		this.publicArea = publicArea;
	}

	public Date getYear_gte() {
		return sqlMap.getWhere().getValue("year", QueryType.GTE);
	}

	public void setYear_gte(Date year) {
		sqlMap.getWhere().and("year", QueryType.GTE, year);
	}

	public Date getYear_lte() {
		return sqlMap.getWhere().getValue("year", QueryType.LTE);
	}

	public void setYear_lte(Date year) {
		sqlMap.getWhere().and("year", QueryType.LTE, year);
	}

	public String getEstateStatus() {
		return estateStatus;
	}

	public void setEstateStatus(String estateStatus) {
		this.estateStatus = estateStatus;
	}

	public String getFylx() {
		return fylx;
	}

	public void setFylx(String fylx) {
		this.fylx = fylx;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getPropertyDeveloper() {
		return propertyDeveloper;
	}

	public void setPropertyDeveloper(String propertyDeveloper) {
		this.propertyDeveloper = propertyDeveloper;
	}
}