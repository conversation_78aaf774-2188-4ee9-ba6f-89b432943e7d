package com.hsobs.hs.modules.dataintelligence.service;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceReviewDao;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTalentSubsidyDao;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceRepairFundDao;

/**
 * 住房保障数据统计Service   维修资金情况统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class DataIntelligenceRepairFundService extends CrudService<DataIntelligenceRepairFundDao, DataIntelligenceRepairFund> {

	@Autowired
	private DataIntelligenceRepairFundDao dataIntelligenceRepairFundDao;
	@Autowired
	private DataIntelligenceTotalService dataIntelligenceTotalService;
	@Autowired
	private OfficeService officeService;

	/**
	 * 获取单条数据
	 * @param dataIntelligenceRepairFund
	 * @return
	 */
	@Override
	public DataIntelligenceRepairFund get(DataIntelligenceRepairFund dataIntelligenceRepairFund) {
		return super.get(dataIntelligenceRepairFund);
	}
	
	/**
	 * 查询分页数据
	 * @param dataIntelligenceRepairFund 查询条件
	 * @param dataIntelligenceRepairFund page 分页对象
	 * @return
	 */
	@Override
	public Page<DataIntelligenceRepairFund> findPage(DataIntelligenceRepairFund dataIntelligenceRepairFund) {
		return super.findPage(dataIntelligenceRepairFund);
	}
	
	/**
	 * 查询列表数据
	 * @param dataIntelligenceRepairFund
	 * @return
	 */
	@Override
	public List<DataIntelligenceRepairFund> findList(DataIntelligenceRepairFund dataIntelligenceRepairFund) {
		return super.findList(dataIntelligenceRepairFund);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param dataIntelligenceRepairFund
	 */
	@Override
	@Transactional
	public void save(DataIntelligenceRepairFund dataIntelligenceRepairFund) {
		super.save(dataIntelligenceRepairFund);
	}
	
	/**
	 * 更新状态
	 * @param dataIntelligenceRepairFund
	 */
	@Override
	@Transactional
	public void updateStatus(DataIntelligenceRepairFund dataIntelligenceRepairFund) {
		super.updateStatus(dataIntelligenceRepairFund);
	}
	
	/**
	 * 删除数据
	 * @param dataIntelligenceRepairFund
	 */
	@Override
	@Transactional
	public void delete(DataIntelligenceRepairFund dataIntelligenceRepairFund) {
		dataIntelligenceRepairFund.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataIntelligenceRepairFund);
	}

	private  String getSqlOtherWhere(DataIntelligenceRepairFund dataIntelligenceRepairFund, Date startDate, Date endDate) {
		String sqlOtherWhere = "";

		if (startDate != null || dataIntelligenceRepairFund.getStartDate() != null) {
			Date date = (startDate!=null)?startDate:dataIntelligenceRepairFund.getStartDate();
			sqlOtherWhere += " AND a.create_date >= '" + String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}
		if (endDate != null || dataIntelligenceRepairFund.getEndDate() != null) {
			Date date = (endDate!=null)?endDate:dataIntelligenceRepairFund.getEndDate();
			sqlOtherWhere += " AND a.create_date <= '" + String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}

		/*if(dataIntelligenceRepairFund.getEstateName() != null && dataIntelligenceRepairFund.getEstateName().length() > 0) {
			sqlOtherWhere += " AND estate.name like '%" + dataIntelligenceRepairFund.getEstateName() + "%'";
		}*/
		if(dataIntelligenceRepairFund.getEstateId() != null && dataIntelligenceRepairFund.getEstateId().length() > 0) {
			sqlOtherWhere += " AND estate.id = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceRepairFund.getEstateId()) + "'";
		}
		if(dataIntelligenceRepairFund.getCity() != null && !"".equals(dataIntelligenceRepairFund.getCity())){
			sqlOtherWhere += " AND estate.city = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceRepairFund.getCity()) + "'";
		}
		if(dataIntelligenceRepairFund.getArea() != null && !"".equals(dataIntelligenceRepairFund.getArea())){
			sqlOtherWhere += " AND estate.area = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceRepairFund.getArea()) + "'";
		}

		dataIntelligenceRepairFund.sqlMap().getDataScope().addFilter("dsf", "Office", "jso.office_code", DataScope.CTRL_PERMI_HAVE);
		SqlMap sqlMap = dataIntelligenceRepairFund.sqlMap();
		Object obWhere = sqlMap.get("dsf");
		if(obWhere != null){
			sqlOtherWhere += obWhere.toString();
		}
		String officeCode = null;
		if(dataIntelligenceRepairFund.getOfficeCode() != null && dataIntelligenceRepairFund.getOfficeCode().length() > 0) {
			officeCode = dataIntelligenceRepairFund.getOfficeCode();
		}
		sqlOtherWhere += dataIntelligenceTotalService.getOfficePermission(officeCode);

		return  sqlOtherWhere;
	}

	public Page<DataIntelligenceRepairFund> findResourceDataPage(DataIntelligenceRepairFund dataIntelligenceRepairFund, boolean findpage){

		String otherWhere = getSqlOtherWhere(dataIntelligenceRepairFund, null, null);
		//String sqlOrderBy = (dataIntelligenceRepairFund.getOrderBy()!=null&&dataIntelligenceRepairFund.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceRepairFund.getOrderBy()):"";
		String sqlOrderBy = "";

		Page<DataIntelligenceRepairFund> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("otherWhere", otherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceRepairFund.getPageNo());
			pageMap.setPageSize(dataIntelligenceRepairFund.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceRepairFundDao.countRepairFund(mapPara);
		List<DataIntelligenceRepairFund> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceRepairFund fundStat = new DataIntelligenceRepairFund();

			fundStat.setStartDate(dataIntelligenceRepairFund.getStartDate());
			fundStat.setEndDate(dataIntelligenceRepairFund.getEndDate());
			fundStat.setOfficeCode(dataIntelligenceRepairFund.getOfficeCode());
			fundStat.setEstateId((dataIntelligenceRepairFund.getEstateId()==null)?"":dataIntelligenceRepairFund.getEstateId());

			Object ob = map.get("REPAIR_TYPE");
			fundStat.setRepairType((ob!=null)?ob.toString():"");
			ob = map.get("DISBURSEMENT_COUNT");
			fundStat.setDisbursementCount((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("DISBURSEMENT_FUND");
			fundStat.setDisbursementFund((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(fundStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

    public List<DataIntelligenceRepairFund> findResourceData(DataIntelligenceRepairFund dataIntelligenceRepairFund) {

		return findResourceDataPage(dataIntelligenceRepairFund, false).getList();
    }

	public Page<DataIntelligenceRepairFund> findRepairFundStatAreaPage(DataIntelligenceRepairFund dataIntelligenceRepairFund, boolean findpage){

		String otherWhere = getSqlOtherWhere(dataIntelligenceRepairFund, null, null);
		//String sqlOrderBy = (dataIntelligenceRepairFund.getOrderBy()!=null&&dataIntelligenceRepairFund.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceRepairFund.getOrderBy()):"";
		String sqlOrderBy = "";

		Page<DataIntelligenceRepairFund> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("otherWhere", otherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceRepairFund.getPageNo());
			pageMap.setPageSize(dataIntelligenceRepairFund.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceRepairFundDao.countRepairFundArea(mapPara);
		List<DataIntelligenceRepairFund> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceRepairFund fundStat = new DataIntelligenceRepairFund();

			fundStat.setStartDate(dataIntelligenceRepairFund.getStartDate());
			fundStat.setEndDate(dataIntelligenceRepairFund.getEndDate());
			fundStat.setOfficeCode(dataIntelligenceRepairFund.getOfficeCode());

			Object ob = map.get("ESTATE_ID");
			fundStat.setEstateId((ob!=null)?ob.toString():"");
			ob = map.get("ESTATE_NAME");
			fundStat.setEstateName((ob!=null)?ob.toString():"");
			ob = map.get("DISBURSEMENT_COUNT");
			fundStat.setDisbursementCount((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("DISBURSEMENT_FUND");
			fundStat.setDisbursementFund((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(fundStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}
	public List<DataIntelligenceRepairFund> findRepairFundStatAreaData(DataIntelligenceRepairFund dataIntelligenceRepairFund) {

		return findRepairFundStatAreaPage(dataIntelligenceRepairFund, false).getList();
	}


	public Page<DataIntelligenceRepairFund> findRepairFundStatOfficePage(DataIntelligenceRepairFund dataIntelligenceRepairFund, boolean findpage){

		String otherWhere = getSqlOtherWhere(dataIntelligenceRepairFund, null, null);
		//String sqlOrderBy = (dataIntelligenceRepairFund.getOrderBy()!=null&&dataIntelligenceRepairFund.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceRepairFund.getOrderBy()):"";
		String sqlOrderBy = "";

		Page<DataIntelligenceRepairFund> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("otherWhere", otherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceRepairFund.getPageNo());
			pageMap.setPageSize(dataIntelligenceRepairFund.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceRepairFundDao.countRepairFundOffice(mapPara);
		List<DataIntelligenceRepairFund> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceRepairFund fundStat = new DataIntelligenceRepairFund();

			fundStat.setStartDate(dataIntelligenceRepairFund.getStartDate());
			fundStat.setEndDate(dataIntelligenceRepairFund.getEndDate());
			fundStat.setEstateId((dataIntelligenceRepairFund.getEstateId()==null)?"":dataIntelligenceRepairFund.getEstateId());

			Object ob = map.get("OFFICE_CODE");
			fundStat.setOfficeCode((ob!=null)?ob.toString():"");
			ob = map.get("OFFICE_NAME");
			fundStat.setOfficeName((ob!=null)?ob.toString():"");
			ob = map.get("DISBURSEMENT_COUNT");
			fundStat.setDisbursementCount((ob!=null)?Long.valueOf(ob.toString()):0);
			ob = map.get("DISBURSEMENT_FUND");
			fundStat.setDisbursementFund((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(fundStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}
	public List<DataIntelligenceRepairFund> findRepairFundStatOfficeData(DataIntelligenceRepairFund dataIntelligenceRepairFund) {

		return findRepairFundStatOfficePage(dataIntelligenceRepairFund, false).getList();
	}

	public Page<DataIntelligenceRepairFund> findRepairFundStatTotalPage(DataIntelligenceRepairFund dataIntelligenceRepairFund, boolean findpage){

		String otherWhere = getSqlOtherWhere(dataIntelligenceRepairFund, null, null);
		List<Map<String,Object>> list = dataIntelligenceRepairFundDao.countRepairFundTotal(otherWhere);

		DataIntelligenceRepairFund fundStat = new DataIntelligenceRepairFund();
		fundStat.setStartDate(dataIntelligenceRepairFund.getStartDate());
		fundStat.setEndDate(dataIntelligenceRepairFund.getEndDate());
		fundStat.setOfficeCode(dataIntelligenceRepairFund.getOfficeCode());
		fundStat.setCity(dataIntelligenceRepairFund.getCity());
		fundStat.setArea(dataIntelligenceRepairFund.getArea());
		fundStat.setEstateId(dataIntelligenceRepairFund.getEstateId());

		Page<DataIntelligenceRepairFund> pageMap = new Page<>();
		List<DataIntelligenceRepairFund> statList = new ArrayList<>();
		statList.add(fundStat);
		pageMap.setList(statList);

		if(list.size() != 1){
			fundStat.setDisbursementCount(0L);
			fundStat.setDisbursementFund(0L);
		}

		Map<String, Object> map = list.get(0);
		Object ob = map.get("DISBURSEMENT_COUNT");
		fundStat.setDisbursementCount((ob!=null)?Long.valueOf(ob.toString()):0);
		ob = map.get("DISBURSEMENT_FUND");
		fundStat.setDisbursementFund((ob!=null)?Float.valueOf(ob.toString()):0);
		statList.add(fundStat);

		return pageMap;
	}

	public String findLiftRepairFundTotal(DataIntelligenceRepairFund dataIntelligenceRepairFund) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 3; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "总额");break;
				case 1:dataTypeO.put("name", "已使用");break;
				case 2:dataTypeO.put("name", "未使用");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		String otherWhere = getSqlOtherWhere(dataIntelligenceRepairFund, null, null);
		List<Map<String,Object>> list = dataIntelligenceRepairFundDao.countRepairFundTotal(otherWhere);

		Map<String, Object> unitMap = list.get(0);
		Object ob;
		ob = unitMap.get("APPLY_FUND");
		dataType.get(0).add((ob!=null)?ob.toString():"0");
		ob = unitMap.get("DISBURSEMENT_FUND");
		dataType.get(1).add((ob!=null)?ob.toString():"0");
		ob = unitMap.get("UNUSED_FUND");
		dataType.get(2).add((ob!=null)?ob.toString():"0");

		dataClass.add("维修资金总体统计");

		Integer max = dataIntelligenceTotalService.calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}

	public String findLiftRepairFundCompare(DataIntelligenceRepairFund dataIntelligenceRepairFund) {

		String otherWhere = getSqlOtherWhere(dataIntelligenceRepairFund, null, null);
		//String sqlOrderBy = (dataIntelligenceRepairFund.getOrderBy()!=null&&dataIntelligenceRepairFund.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceRepairFund.getOrderBy()):"";
		String sqlOrderBy = "";
		List<Map<String,Object>> list = dataIntelligenceRepairFundDao.countRepairFund(otherWhere, sqlOrderBy);

		List<Map<String, Object>> newList = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> unitMap = list.get(i);
			Map<String, Object> map = new HashMap<>();

			Object ob = unitMap.get("REPAIR_TYPE");
			map.put("name", (ob!=null)?ob.toString():"");
			ob = unitMap.get("DISBURSEMENT_FUND");
			map.put("value", (ob!=null)?ob.toString():"0");

			newList.add(map);
		}
		return JSON.toJSONString(newList);
	}
}