package com.hsobs.hs.modules.formmanage.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;

/**
 * 数据表单下发发送记录表Entity
 * <AUTHOR>
 * @version 2025-02-16
 */
@Table(name="hs_data_form_delivery_record", alias="a", label="数据表单下发发送记录表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="delivery_id", attrName="deliveryId", label="所属消息"),
		@Column(name="receive_user_code", attrName="receiveUserCode", label="接受者用户编码"),
		@Column(name="receive_user_name", attrName="receiveUserName", label="接受者用户姓名", queryType=QueryType.LIKE),
		@Column(name="fill_status", attrName="fillStatus", label="填写状态 ", comment="填写状态 (0未填写 1已填写)"),
		@Column(name="fill_date", attrName="fillDate", label="填写时间", isUpdateForce=true),
		@Column(name="read_status", attrName="readStatus", label="读取状态", comment="读取状态（0未送达 1已读 2未读）"),
		@Column(name="read_date", attrName="readDate", label="阅读时间", isUpdateForce=true),
		@Column(name="upload_status", attrName="uploadStatus", label="上传状态", comment="上传状态"),
		@Column(name="upload_date", attrName="uploadDate", label="上传时间", isUpdateForce=true),
		@Column(name="is_star", attrName="isStar", label="是否标星"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsDataFormFill.class, alias = "h",
				on = "h.delivery_record_id = a.id", attrName = "hsDataFormFill",
				columns = {@Column(includeEntity = HsDataFormFill.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsDataFormDelivery.class, alias = "m",
				on = "m.id = a.delivery_id", attrName = "hsDataFormDelivery",
				columns = {@Column(includeEntity = HsDataFormDelivery.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsDataFormTemplate.class, alias = "t",
				on = "t.id = m.template_code", attrName = "formTemplate",
				columns = {
					@Column(includeEntity = HsDataFormTemplate.class)
				}
		)
    }, orderBy="a.id DESC"
)
public class HsDataFormDeliveryRecord extends DataEntity<HsDataFormDeliveryRecord> {

	// 读取状态（0未送达 1已读 2未读）
	public static final String READ_STATUS_READ = "1";
	public static final String READ_STATUS_UNREAD = "2";
	// 填写状态（0未操作 1已填写 2未填写）
	public static final String FILL_STATUS_FILL = "1";
	public static final String FILL_STATUS_UNFILL = "2";
	// 上传状态（0未上传 1已上传 2待重新上传）
	public static final String UPLOAD_STATUS_UN = "0";
	public static final String UPLOAD_STATUS_ED = "1";
	public static final String UPLOAD_STATUS_RE = "2";

	private static final long serialVersionUID = 1L;
	private String deliveryId;		// 所属消息
	private String receiveUserCode;		// 接受者用户编码
	private String receiveUserName;		// 接受者用户姓名
	private String fillStatus;		// 填写状态 (0未填写 1已填写)
	private Date fillDate;		// 填写时间
	private String readStatus;		// 读取状态（0未送达 1已读 2未读）
	private Date readDate;		// 阅读时间
	private String uploadStatus;		// 上传状态（0未上传 1已上传 2待重新上传）  data_form_upload_status
	private Date uploadDate;		// 阅读时间
	private String isStar;		// 是否标星

	private HsDataFormFill hsDataFormFill;
	private HsDataFormDelivery hsDataFormDelivery;
	private HsDataFormTemplate formTemplate;

	private String opType;

	@ExcelFields({
		@ExcelField(title="编号", attrName="id", align= ExcelField.Align.CENTER, sort=20),
		@ExcelField(title="标题", attrName="hsDataFormDelivery.msgTitle", align= ExcelField.Align.CENTER, sort=30),
		@ExcelField(title="类型", attrName="formTemplate.formType", dictType="data_form_type", align= ExcelField.Align.CENTER, sort=40),
		@ExcelField(title="填写人名称", attrName="receiveUserName", align= ExcelField.Align.CENTER, sort=50),
		@ExcelField(title="填写状态", attrName="fillStatus", dictType="data_form_fill_status", align= ExcelField.Align.CENTER, sort=60),
		@ExcelField(title="填写时间", attrName="fillDate", align= ExcelField.Align.CENTER, words=20, sort=100, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
		@ExcelField(title="报送时间", attrName="readDate", align= ExcelField.Align.CENTER, words=20, sort=110, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
	})

	public HsDataFormDeliveryRecord() {
		this(null);
	}
	
	public HsDataFormDeliveryRecord(String id){
		super(id);
	}
	
	@NotBlank(message="所属消息不能为空")
	@Size(min=0, max=64, message="所属消息长度不能超过 64 个字符")
	public String getDeliveryId() {
		return deliveryId;
	}

	public void setDeliveryId(String deliveryId) {
		this.deliveryId = deliveryId;
	}
	
	@NotBlank(message="接受者用户编码不能为空")
	@Size(min=0, max=64, message="接受者用户编码长度不能超过 64 个字符")
	public String getReceiveUserCode() {
		return receiveUserCode;
	}

	public void setReceiveUserCode(String receiveUserCode) {
		this.receiveUserCode = receiveUserCode;
	}
	
	@NotBlank(message="接受者用户姓名不能为空")
	@Size(min=0, max=100, message="接受者用户姓名长度不能超过 100 个字符")
	public String getReceiveUserName() {
		return receiveUserName;
	}

	public void setReceiveUserName(String receiveUserName) {
		this.receiveUserName = receiveUserName;
	}
	
	@NotBlank(message="填写状态 不能为空")
	@Size(min=0, max=1, message="填写状态 长度不能超过 1 个字符")
	public String getFillStatus() {
		return fillStatus;
	}

	public void setFillStatus(String fillStatus) {
		this.fillStatus = fillStatus;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getFillDate() {
		return fillDate;
	}

	public void setFillDate(Date fillDate) {
		this.fillDate = fillDate;
	}
	
	@NotBlank(message="读取状态不能为空")
	@Size(min=0, max=1, message="读取状态长度不能超过 1 个字符")
	public String getReadStatus() {
		return readStatus;
	}

	public void setReadStatus(String readStatus) {
		this.readStatus = readStatus;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getReadDate() {
		return readDate;
	}

	public void setReadDate(Date readDate) {
		this.readDate = readDate;
	}
	
	@Size(min=0, max=1, message="是否标星长度不能超过 1 个字符")
	public String getIsStar() {
		return isStar;
	}

	public void setIsStar(String isStar) {
		this.isStar = isStar;
	}

	public HsDataFormFill getHsDataFormFill() {
		return hsDataFormFill;
	}

	public void setHsDataFormFill(HsDataFormFill hsDataFormFill) {
		this.hsDataFormFill = hsDataFormFill;
	}

	public HsDataFormDelivery getHsDataFormDelivery() {
		return hsDataFormDelivery;
	}

	public void setHsDataFormDelivery(HsDataFormDelivery hsDataFormDelivery) {
		this.hsDataFormDelivery = hsDataFormDelivery;
	}

	public HsDataFormTemplate getFormTemplate() {
		return formTemplate;
	}

	public void setFormTemplate(HsDataFormTemplate formTemplate) {
		this.formTemplate = formTemplate;
	}

	public String getUploadStatus() {
		return uploadStatus;
	}

	public void setUploadStatus(String uploadStatus) {
		this.uploadStatus = uploadStatus;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	public Date getUploadDate() {
		return uploadDate;
	}

	public void setUploadDate(Date uploadDate) {
		this.uploadDate = uploadDate;
	}

	public String getOpType() {
		return opType;
	}

	public void setOpType(String opType) {
		this.opType = opType;
	}
}