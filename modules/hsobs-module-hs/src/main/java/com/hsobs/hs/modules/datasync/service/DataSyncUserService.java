package com.hsobs.hs.modules.datasync.service;

import com.hsobs.hs.modules.datasync.bean.UserDetailData;
import com.hsobs.hs.modules.datasync.bean.UserUnderOrgData;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DataSyncUserService {

    @Autowired
    private EmpUserService empUserService;
    @Autowired
    private UserService userService;

    public EmpUser syncUser(UserUnderOrgData userUnderOrgData, Office office, UserDetailData userDetailData) {

        EmpUser user = null;

        EmpUser query = new EmpUser();
        query.setExtAspId(userUnderOrgData.getUserId());
        List<EmpUser> userList = empUserService.findList(query);
        if (userList == null || userList.isEmpty()) {
            query = new EmpUser();
            query.setMobile(userUnderOrgData.getMobile());
            userList = empUserService.findList(query);
        }
        if (userList != null && !userList.isEmpty()) {
            user = userList.get(0);
        } else {
            user = new EmpUser();
        }

        user.setExtAspId(userUnderOrgData.getUserId());
        user.setUserType("employee");
        user.setLoginCode(userUnderOrgData.getAccount());
        user.setMobile(userUnderOrgData.getMobile());
        user.setUserName(userUnderOrgData.getName());
        user.setIdCard(userDetailData.getIdCard());
        
//        user.setIdNum(userDetailData.getIdNum());
        user.setExtAspUserType(userDetailData.getType());
        user.setExtAspOrgId(userDetailData.getOrgId());

        //0-禁用 1-启用
        if (userDetailData.getStatus() != null && userDetailData.getStatus() == 0) {
            user.setStatus("2");
        } else {
            // 0：正常；1：删除；2：停用；3：冻结；4：审核、待审核；5：审核驳回；9：草稿
            user.setStatus("0");
        }

        user.setCertType(userDetailData.getCertType());

        if (StringUtils.isEmpty(user.getSex())) {
            user.setSex("9");
        }

        Employee employee = user.getEmployee();
        if (employee == null) {
            employee = new Employee();
            employee.setEstablishmentType("30");
            employee.setJoinedDate(new Date());
            employee.setChangeType("1");
            employee.setStatus("1");
            employee.setEmpNo(userUnderOrgData.getAccount());
            employee.setOfficeUsedArea(0.0d);
        }
        employee.setOffice(office);
        empUserService.save(user);
        return user;
    }

    public void syncUserRole(EmpUser user, List<String> roleCodeList) {
        user.setUserRoleString(StringUtils.join(roleCodeList, ","));
        userService.saveAuth(user);
    }
}
