package com.hsobs.hs.modules.applypublicdetail.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applypublic.entity.HsQwApplyPublic;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候公示复查详情表Entity
 * 
 * <AUTHOR>
 * @version 2024-12-05
 */
@Table(name = "hs_qw_apply_public_detail", alias = "a", label = "租赁资格轮候公示复查详情表信息", columns = {
        @Column(name = "id", attrName = "id", label = "编号", isPK = true),
        @Column(name = "public_id", attrName = "publicId", label = "公示id"),
        @Column(name = "apply_id", attrName = "applyId", label = "申请单id"),
        @Column(name = "process_id", attrName = "processId", label = "流程id"),
        @Column(name = "status", attrName = "status", label = "状态", comment = "状态（0待审批 1已通过 2已取消）", isUpdate = false),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyPublic.class, alias = "o", on = "a.public_id = o.id", attrName = "hsQwApplyPublic", columns = {
                @Column(includeEntity = HsQwApplyPublic.class) }),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApply.class, alias = "p", on = "a.apply_id = p.id", attrName = "hsQwApply", columns = {
                @Column(includeEntity = HsQwApply.class) })
}, orderBy = "a.id DESC")
public class HsQwApplyPublicDetail extends DataEntity<HsQwApplyPublicDetail> {

    private static final long serialVersionUID = 1L;
    private String publicId; // 公示id
    private String applyId; // 申请单id
    private String processId; // 流程id
    private HsQwApply hsQwApply;
    private HsQwApplyPublic hsQwApplyPublic;
    private String familyNames;
    private String familyOrganization;

    public String getFamilyNames() {
        return familyNames;
    }

    public void setFamilyNames(String familyNames) {
        this.familyNames = familyNames;
    }

    public String getFamilyOrganization() {
        return familyOrganization;
    }

    public void setFamilyOrganization(String familyOrganization) {
        this.familyOrganization = familyOrganization;
    }

    public HsQwApplyPublicDetail() {
        this(null);
    }

    public HsQwApplyPublicDetail(String id) {
        super(id);
    }

    @NotBlank(message = "公示id不能为空")
    @Size(min = 0, max = 64, message = "公示id长度不能超过 64 个字符")
    public String getPublicId() {
        return publicId;
    }

    public void setPublicId(String publicId) {
        this.publicId = publicId;
    }

    @NotBlank(message = "申请单id不能为空")
    @Size(min = 0, max = 64, message = "申请单id长度不能超过 64 个字符")
    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @NotBlank(message = "流程id不能为空")
    @Size(min = 0, max = 64, message = "流程id长度不能超过 64 个字符")
    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public HsQwApply getHsQwApply() {
        return hsQwApply;
    }

    public void setHsQwApply(HsQwApply hsQwApply) {
        this.hsQwApply = hsQwApply;
    }

    public HsQwApplyPublic getHsQwApplyPublic() {
        return hsQwApplyPublic;
    }

    public void setHsQwApplyPublic(HsQwApplyPublic hsQwApplyPublic) {
        this.hsQwApplyPublic = hsQwApplyPublic;
    }
}