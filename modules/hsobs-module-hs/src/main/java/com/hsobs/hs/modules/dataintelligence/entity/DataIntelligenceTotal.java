package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.utils.UserUtils;
import com.jeesite.modules.sys.utils.EmpUtils;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.text.SimpleDateFormat;

/**
 * 住房保障数据统计Entity   总房源信息统计
 * <AUTHOR>
 * @version 2025-1-2
 */
public class DataIntelligenceTotal extends DataEntity<DataIntelligenceTotal> {

	@ExcelFields({
			@ExcelField(title = "单位名称", attrName = "officeName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "总套数", attrName = "roomTotalNumber", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "总面积(m²)", attrName = "roomTotalArea", align = ExcelField.Align.CENTER, sort = 50),
			@ExcelField(title = "公租房套数", attrName = "roomRentalNumber", align = ExcelField.Align.CENTER, sort = 60),
			@ExcelField(title = "公租房面积(m²)", attrName = "roomRentalArea", align = ExcelField.Align.CENTER, sort = 70),
			@ExcelField(title = "限价房套数", attrName = "roomPriceLimitNumber", align = ExcelField.Align.CENTER, sort = 80),
			@ExcelField(title = "限价房面积(m²)", attrName = "roomPriceLimitArea", align = ExcelField.Align.CENTER, sort = 90),
			@ExcelField(title = "公有住房套数", attrName = "roomPublicNumber", align = ExcelField.Align.CENTER, sort = 100),
			@ExcelField(title = "公有住房面积(m²)", attrName = "roomPublicArea", align = ExcelField.Align.CENTER, sort = 110)
	})

	private static final long serialVersionUID = 1L;
	private String parentCodeTree;

	private String city;
	private String area;

	private String estateId;	//区域
	private String estateName;	//区域
	private String officeCode;		// 单位编码
	private Date startDate;		//开始日期
	private Date endDate;	//结束日期

	private String officeName;    //使用单位

	private Integer roomTotalNumber;    //房源数
	private float roomTotalArea;    //使用单位
	private Integer roomRentalNumber;    //房源数（公租房）
	private float roomRentalArea;    //使用单位（公租房）
	private Integer roomPriceLimitNumber;    //房源数（限价房）
	private float roomPriceLimitArea;    //使用单位（限价房）
	private Integer roomPublicNumber;    //房源数（公有住房）
	private float roomPublicArea;    //使用单位（公有住房）

	public DataIntelligenceTotal() {
		this(null);

		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			endDate = new Date();
			startDate = dateFormat.parse(String.format("%04d-01-01", endDate.getYear()+1900));  // 解析指定日期字符串
		} catch (Exception e) {
		}

		parentCodeTree = "";
		if(!UserUtils.getUser().isAdmin()){
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		else{
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
	}
	
	public DataIntelligenceTotal(String id){
		super(id);
	}

	public String getParentCodeTree() {
		return parentCodeTree;
	}

	public void setParentCodeTree(String parentCodeTree) {
		this.parentCodeTree = parentCodeTree;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getEstateId() {
		return estateId;
	}

	public void setEstateId(String estateId) {
		this.estateId = estateId;
	}

	public String getEstateName() {
		return estateName;
	}

	public void setEstateName(String estateName) {
		this.estateName = estateName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}



	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public Integer getRoomTotalNumber() {
		return roomTotalNumber;
	}

	public void setRoomTotalNumber(Integer roomTotalNumber) {
		this.roomTotalNumber = roomTotalNumber;
	}

	public float getRoomTotalArea() {
		return roomTotalArea;
	}

	public void setRoomTotalArea(float roomTotalArea) {
		this.roomTotalArea = roomTotalArea;
	}

	public Integer getRoomRentalNumber() {
		return roomRentalNumber;
	}

	public void setRoomRentalNumber(Integer roomRentalNumber) {
		this.roomRentalNumber = roomRentalNumber;
	}

	public float getRoomRentalArea() {
		return roomRentalArea;
	}

	public void setRoomRentalArea(float roomRentalArea) {
		this.roomRentalArea = roomRentalArea;
	}

	public Integer getRoomPriceLimitNumber() {
		return roomPriceLimitNumber;
	}

	public void setRoomPriceLimitNumber(Integer roomPriceLimitNumber) {
		this.roomPriceLimitNumber = roomPriceLimitNumber;
	}

	public float getRoomPriceLimitArea() {
		return roomPriceLimitArea;
	}

	public void setRoomPriceLimitArea(float roomPriceLimitArea) {
		this.roomPriceLimitArea = roomPriceLimitArea;
	}

	public Integer getRoomPublicNumber() {
		return roomPublicNumber;
	}

	public void setRoomPublicNumber(Integer roomPublicNumber) {
		this.roomPublicNumber = roomPublicNumber;
	}

	public float getRoomPublicArea() {
		return roomPublicArea;
	}

	public void setRoomPublicArea(float roomPublicArea) {
		this.roomPublicArea = roomPublicArea;
	}
}
