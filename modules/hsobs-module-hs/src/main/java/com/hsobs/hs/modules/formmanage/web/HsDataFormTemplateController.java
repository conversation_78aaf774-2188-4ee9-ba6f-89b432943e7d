package com.hsobs.hs.modules.formmanage.web;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.formmanage.bean.FormItemBean;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplateField;
import com.hsobs.hs.modules.formmanage.service.HsDataFormTemplateFieldService;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplate;
import com.hsobs.hs.modules.formmanage.service.HsDataFormTemplateService;

/**
 * 数据表单模板Controller
 * <AUTHOR>
 * @version 2025-02-06
 */
@Controller
@RequestMapping(value = "${adminPath}/formmanage/hsDataFormTemplate")
public class HsDataFormTemplateController extends BaseController {

	@Autowired
	private HsDataFormTemplateService hsDataFormTemplateService;
	@Autowired
	private HsDataFormTemplateFieldService hsDataFormTemplateFieldService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsDataFormTemplate get(String id, boolean isNewRecord) {
		return hsDataFormTemplateService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsDataFormTemplate hsDataFormTemplate, Model model) {
		model.addAttribute("hsDataFormTemplate", hsDataFormTemplate);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/formmanage/hsDataFormTemplateList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsDataFormTemplate> listData(HsDataFormTemplate hsDataFormTemplate, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormTemplate.setPage(new Page<>(request, response));
		Page<HsDataFormTemplate> page = hsDataFormTemplateService.findPage(hsDataFormTemplate);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:view")
	@RequestMapping(value = "form")
	public String form(HsDataFormTemplate hsDataFormTemplate, Model model) {
		model.addAttribute("hsDataFormTemplate", hsDataFormTemplate);
		return "modules/formmanage/hsDataFormTemplateForm";
	}
	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:view")
	@RequestMapping(value = "fieldConfigForm")
	public String fieldConfigForm(HsDataFormTemplate hsDataFormTemplate, Model model) {
		model.addAttribute("hsDataFormTemplate", hsDataFormTemplate);

		AtomicInteger fileIndex = new AtomicInteger(0);
		if (hsDataFormTemplate.getId() != null) {
			HsDataFormTemplateField query = new HsDataFormTemplateField();
			query.setTemplateId(hsDataFormTemplate.getId());
			List<HsDataFormTemplateField> fieldList = hsDataFormTemplateFieldService.findList(query);
			if (fieldList != null && !fieldList.isEmpty()) {
				fieldList.forEach(field -> {
					field.setIndex(fileIndex.getAndIncrement());
					if ("3".equals(field.getFieldType()) || "4".equals(field.getFieldType()) || "5".equals(field.getFieldType())) {
						List<FormItemBean> itemList = new ArrayList<>();
						if (field.getFieldSubkey() != null) {
							String[] subKeyArr = field.getFieldSubkey().split(",");
							for (String subKey : subKeyArr) {
								itemList.add(new FormItemBean(subKey, subKey));
							}
						}
						field.setItems(itemList);
					}
				});
				hsDataFormTemplate.setFieldList(fieldList);
			}
		}
		model.addAttribute("fieldArrStr", JSONObject.toJSONString(hsDataFormTemplate.getFieldList()));
		model.addAttribute("fieldIndex", fileIndex.get());
		return "modules/formmanage/hsDataFormTemplateFieldConfigForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsDataFormTemplate hsDataFormTemplate) {
		hsDataFormTemplateService.save(hsDataFormTemplate);
		return renderResult(Global.TRUE, text("保存数据表单模板成功！"));
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:edit")
	@PostMapping(value = "saveField")
	@ResponseBody
	public String saveField(@Validated HsDataFormTemplate hsDataFormTemplate) {
		hsDataFormTemplateService.saveField(hsDataFormTemplate);
		return renderResult(Global.TRUE, text("保存数据表单字段成功！"));
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsDataFormTemplate hsDataFormTemplate) {
		hsDataFormTemplateService.delete(hsDataFormTemplate);
		return renderResult(Global.TRUE, text("删除数据表单模板成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:view")
	@RequestMapping(value = "hsDataFormTemplateSelect")
	public String hsDataFormTemplateSelect(HsDataFormTemplate hsDataFormTemplate, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsDataFormTemplate", hsDataFormTemplate);
		return "modules/formmanage/hsDataFormTemplateSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplate:view")
	@RequestMapping(value = "exportFormData")
	public void exportFormData(HsDataFormTemplate hsDataFormTemplate, HttpServletResponse response) {
		hsDataFormTemplateService.exportFormData(hsDataFormTemplate, response);
	}

}