//package com.hsobs.hs.modules.apply.web.adjust;
//
//import com.alibaba.fastjson.JSONValidator;
//import com.hsobs.hs.modules.apply.entity.HsQwApply;
//import com.hsobs.hs.modules.apply.service.HsQwApplyService;
//import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
//import com.jeesite.common.codec.EncodeUtils;
//import com.jeesite.common.config.Global;
//import com.jeesite.common.entity.Page;
//import com.jeesite.common.web.BaseController;
//import org.apache.shiro.authz.annotation.RequiresPermissions;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.Model;
//import org.springframework.web.bind.annotation.ModelAttribute;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
///**
// * 个人变更申请Controller
// *
// * <AUTHOR>
// * @version 2025-1-6
// */
//@Controller
////@RequestMapping(value = "${adminPath}/apply/hsQwApplyInfo")
//public class HsQwApplyInfoController1 extends BaseController {
//
//    @Autowired
//    private HsQwApplyService hsQwApplyService;
//
//    /**
//     * 获取数据
//     */
//    @ModelAttribute
//    public HsQwApply get(String id, boolean isNewRecord) {
//        return hsQwApplyService.get(id, isNewRecord);
//    }
//
//
//    /**
//     * 查询列表-
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = {"list", ""})
//    public String listGreen(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/applyPerson/hsQwApplyReplaceList";
//    }
//
//    /**
//     * 租房居室变更申请单（以小换大）
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formReplace")
//    public String formReplace(Model model) {
//        model.addAttribute("hsQwApply", hsQwApplyService.getUserApplyInfo("2"));
//        return "modules/apply/applyPerson/hsQwApplyReplaceFormMzt";
//    }
//
//
//    /**
//     * 租房个人信息变更申请单
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formChange")
//    public String formChange(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApplyService.getUserApplyInfo("1"));
//        return "modules/apply/applyPerson/hsQwApplyChangeFormMzt";
//    }
//
//
//    /**
//     * 租房个人承租信息变更申请单(平换)
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formRentMzt")
//    public String formRentMzt(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApplyService.getUserApplyInfo( "4"));
//        return "modules/apply/applyPerson/hsQwApplyRentFormMzt";
//    }
//
//    /**
//     * 租房个人承租信息变更申请单(平换)
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formRent")
//    public String formRent(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/applyPerson/hsQwApplyRentForm";
//    }
//
//    /**
//     * 租房个人承租信息变更申请单(平换)-房源选择
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formRentHouse")
//    public String formRentHouse(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/applyPerson/hsQwApplyRentFormHouse";
//    }
//
//
//    /**
//     * 租房个人承租信息变更申请单(平换)-合同签订
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formRentCompact")
//    public String formRentCompact(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/applyPerson/hsQwApplyRentFormCompact";
//    }
//
//    /**
//     * 租房居室变更申请单
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "formInfoCheck")
//    public String formInfoCheck(HsQwApply hsQwApply, Model model, String isRead) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        model.addAttribute("isRead", isRead);
//        model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
//        return "modules/apply/applyPerson/hsQwApplyInfoForm";
//    }
//
//    /**
//     * 查询列表-个人信息修改代办
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = {"listInfo", ""})
//    public String listInfo(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/applyPerson/hsQwApplyInfoList";
//    }
//
//    /**
//     * 查询列表-个人信息修改代办已处理
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = {"listInfoDone", ""})
//    public String listInfoDone(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/applyPerson/hsQwApplyInfoDoneList";
//    }
//
//    /**
//     * 查询代办的审批任务-个人信息修改代办
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "listAuditInfoData")
//    @ResponseBody
//    public Page<HsQwApply> listAuditInfoData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
//        hsQwApply.setPage(new Page<>(request, response));
//        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTask(hsQwApply, null, "1", "rent_apply_info");
//        return page;
//    }
//
//    /**
//     * 查询已办的审批任务-个人信息修改已办
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "listAuditedInfoData")
//    @ResponseBody
//    public Page<HsQwApply> listAuditedInfoData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
//        hsQwApply.setPage(new Page<>(request, response));
//        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTask(hsQwApply, null, "2", "rent_apply_info");
//        return page;
//    }
//
//    /**
//     * 查询列表-承租房信息修改代办
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = {"listRent", ""})
//    public String listRent(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/applyPerson/hsQwApplyRentList";
//    }
//
//    /**
//     * 查询列表-承租房信息已处理
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = {"listRentDone", ""})
//    public String listRentDone(HsQwApply hsQwApply, Model model) {
//        model.addAttribute("hsQwApply", hsQwApply);
//        return "modules/apply/applyPerson/hsQwApplyRentDoneList";
//    }
//
//    /**
//     * 查询代办的审批任务-承租房信息代办
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "listAuditRentData")
//    @ResponseBody
//    public Page<HsQwApply> listAuditRentData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
//        hsQwApply.setPage(new Page<>(request, response));
//        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTask(hsQwApply, null, "1", "rent_apply_house");
//        return page;
//    }
//
//    /**
//     * 查询已办的审批任务-承租房信息已办
//     */
//    @RequiresPermissions("apply:hsQwApply:view")
//    @RequestMapping(value = "listAuditedRentData")
//    @ResponseBody
//    public Page<HsQwApply> listAuditedChangeData(HsQwApply hsQwApply, HttpServletRequest request, HttpServletResponse response) {
//        hsQwApply.setPage(new Page<>(request, response));
//        Page<HsQwApply> page = hsQwApplyService.findApplyPageByTask(hsQwApply, null, "2", "rent_apply_house");
//        return page;
//    }
//
//    /**
//     * 保存数据
//     */
//    @RequiresPermissions("apply:hsQwApply:edit")
//    @PostMapping(value = "save")
//    @ResponseBody
//    public String save(HsQwApply hsQwApply) {
//        hsQwApplyService.save(hsQwApply);
//        return renderResult(Global.TRUE, text("保存个人调租申请成功！"));
//    }
//
//    /**
//     * 选择房源对话框
//     */
//    @RequiresPermissions("apply:hsQwApply:edit")
//    @RequestMapping(value = "houseSelect")
//    public String houseSelect(HsQwPublicRentalHouse hsQwPublicRentalHouse, String selectData, Model model, String applyId) {
//        String selectDataJson = EncodeUtils.decodeUrl(selectData);
//        if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()) {
//            model.addAttribute("selectData", selectDataJson);
//        }
//        model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
//        return "modules/house/hsQwPublicRentalHouseListSelectApply";
//    }
//
//}