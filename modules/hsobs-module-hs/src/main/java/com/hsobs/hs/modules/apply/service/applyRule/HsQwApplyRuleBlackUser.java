package com.hsobs.hs.modules.apply.service.applyRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.service.HsQwApplyerBlackService;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 申请人曾经进入黑名单中
 */
@Service
public class HsQwApplyRuleBlackUser implements HsQwApplyRuleMethod {

    @Autowired
    HsQwApplyerBlackService hsQwApplyerBlackService;

    @Override
    public void execute(HsQwApply hsQwApply, Map<String, HsQwFormAlarm> hsQwFormAlarmMap) {
        // 申请人需为在榕省直单位或中央驻榕单位的在职、退休干部职工，或服务满5年的合同制员工。
        HsQwApplyer mainApplyer = null;
        // 获取hsQwApplyerList中applyRole为0的主申请人的List序列号
        String id = "";
        for (int i = 0; i < hsQwApply.getHsQwApplyerList().size(); i++) {
            if (hsQwApply.getHsQwApplyerList().get(i).getApplyRole().equals("0")) {
                mainApplyer = hsQwApply.getHsQwApplyerList().get(i);
                id = mainApplyer.getId();
                break;
            }
        }
        //判定申请人是否进入黑名单
        HsQwApplyerBlack entity = new HsQwApplyerBlack();
        entity.setUserId(this.getRealApplyUser(hsQwApply));
        entity.sqlMap().getWhere().disableAutoAddStatusWhere();
        entity.sqlMap().getWhere().and("status", QueryType.IN, new String[]{HsQwApplyer.STATUS_NORMAL, HsQwApplyer.STATUS_DISABLE});
        List<HsQwApplyerBlack> list = hsQwApplyerBlackService.findList(entity);
        if (list.size() > 0) {
            HsQwApplyRuleMethod.putAlarmMap(hsQwFormAlarmMap,
                    "hsQwApply.hsQwApplyerList[" + id + "].name",
                    "申请人曾为黑名单用户，黑名单解除时间为" + DateUtils.formatDate(list.get(0).getEndTime()),
                    "2", hsQwApply.getId());
        }
    }

    private String getRealApplyUser(HsQwApply hsQwApply) {
        if (hsQwApply.getProxyUserId() != null) {
            return hsQwApply.getProxyUserId();
        }
        return UserUtils.getUser().getUserCode();
    }
}
