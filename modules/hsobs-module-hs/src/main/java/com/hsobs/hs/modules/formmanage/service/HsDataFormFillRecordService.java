package com.hsobs.hs.modules.formmanage.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormFillRecord;
import com.hsobs.hs.modules.formmanage.dao.HsDataFormFillRecordDao;

/**
 * 数据表单填写详情记录表Service
 * <AUTHOR>
 * @version 2025-02-21
 */
@Service
public class HsDataFormFillRecordService extends CrudService<HsDataFormFillRecordDao, HsDataFormFillRecord> {
	
	/**
	 * 获取单条数据
	 * @param hsDataFormFillRecord
	 * @return
	 */
	@Override
	public HsDataFormFillRecord get(HsDataFormFillRecord hsDataFormFillRecord) {
		return super.get(hsDataFormFillRecord);
	}
	
	/**
	 * 查询分页数据
	 * @param hsDataFormFillRecord 查询条件
	 * @param hsDataFormFillRecord page 分页对象
	 * @return
	 */
	@Override
	public Page<HsDataFormFillRecord> findPage(HsDataFormFillRecord hsDataFormFillRecord) {
		return super.findPage(hsDataFormFillRecord);
	}
	
	/**
	 * 查询列表数据
	 * @param hsDataFormFillRecord
	 * @return
	 */
	@Override
	public List<HsDataFormFillRecord> findList(HsDataFormFillRecord hsDataFormFillRecord) {
		return super.findList(hsDataFormFillRecord);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsDataFormFillRecord
	 */
	@Override
	@Transactional
	public void save(HsDataFormFillRecord hsDataFormFillRecord) {
		super.save(hsDataFormFillRecord);
	}
	
	/**
	 * 更新状态
	 * @param hsDataFormFillRecord
	 */
	@Override
	@Transactional
	public void updateStatus(HsDataFormFillRecord hsDataFormFillRecord) {
		super.updateStatus(hsDataFormFillRecord);
	}
	
	/**
	 * 删除数据
	 * @param hsDataFormFillRecord
	 */
	@Override
	@Transactional
	public void delete(HsDataFormFillRecord hsDataFormFillRecord) {
		hsDataFormFillRecord.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsDataFormFillRecord);
	}
	
}