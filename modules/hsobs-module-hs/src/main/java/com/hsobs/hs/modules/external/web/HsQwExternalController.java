package com.hsobs.hs.modules.external.web;

import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.checkrecord.entity.HsQwCheckRecord;
import com.hsobs.hs.modules.checkrecord.service.HsQwCheckRecordService;
import com.hsobs.hs.modules.external.entity.*;
import com.hsobs.hs.modules.external.entity.validated.NotNullApplyConfirm;
import com.hsobs.hs.modules.external.entity.validated.NotNullApplyMatterGroup;
import com.hsobs.hs.modules.external.entity.validated.NotNullApplyReplaceUp;
import com.hsobs.hs.modules.external.entity.validated.NotNullApplyShowing;
import com.hsobs.hs.modules.external.service.HsQwExternalService;
import com.hsobs.hs.modules.managementcheck.entity.HsQwManagementCheck;
import com.hsobs.hs.modules.utils.FlowableCleanupService;
import com.hsobs.hs.modules.utils.HsBeanMapperUtil;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.web.BaseController;
import com.jeesite.common.web.http.ServletUtils;
import org.beetl.ext.fn.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.ui.Model;

import java.util.*;

/**
 * 住房保障对位接口
 */
@Controller
@RequestMapping(value = "${adminPath}/hs")
public class HsQwExternalController extends BaseController {

    @Autowired
    private HsQwExternalService hsQwExternalService;

    @Autowired
    private FlowableCleanupService flowableCleanupService;

    @Autowired
    private HsQwCheckRecordService hsQwCheckRecordService;

    /**
     * 处理所有未捕获的异常，确保不会暴露系统内部错误
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public String handleException(Exception e) {
        if (logger.isDebugEnabled()){
            e.printStackTrace();
        }
        logger.error(e.getMessage());
        return renderResult(Global.FALSE, "后台服务处理异常：" + text(e.getMessage()));
    }

    /**
     * 6.3.4账号权限获取接口
     *
     * @param apiAccountPermission
     * @return
     */
    @PostMapping(value = "accountPermission")
    @ResponseBody
    public Page<ApiAccountPermission> accountPermission(@Validated ApiAccountPermission apiAccountPermission) {
        //todo 待用户中心接口调通
        return hsQwExternalService.accountPermission(apiAccountPermission);
    }


    /**
     * 6.3.6用户流程操作权限判断接口
     *
     * @param apiProcessPermission
     * @return
     */
    @PostMapping(value = "processPermission")
    @ResponseBody
    public ApiProcessPermission processPermission(@Validated ApiProcessPermission apiProcessPermission) {
        return hsQwExternalService.processPermission(apiProcessPermission);
    }

    /**
     * 6.3.7获取事项记录列表接口
     *
     * @param apiHsQwApplyMatter
     * @return
     */
    @PostMapping(value = "applyMatterList")
    @ResponseBody
    public Page<ApiHsQwApplyMatter> applyMatterList(@Validated ApiHsQwApplyMatter apiHsQwApplyMatter) {
        Page page =  hsQwExternalService.applyMatterList(apiHsQwApplyMatter);
        return page;
    }

    /**
     * 6.3.8获取事项详情接口
     *
     * @param apiHsQwApplyMatter
     * @return
     */
    @PostMapping(value = "applyDetail")
    @ResponseBody
    public ApiHsQwApplyMatter applyDetail(@Validated(NotNullApplyMatterGroup.class) ApiHsQwApplyMatter apiHsQwApplyMatter) {
        return hsQwExternalService.applyDetail(apiHsQwApplyMatter);
    }

    /**
     * 6.3.9获取用户公租房主流程信息接口
     *
     * @param apiHsBpmProcess
     * @return
     */
    @PostMapping(value = "applyMasterDetail")
    @ResponseBody
    public Page<ApiHsBpmProcess> applyMasterDetail(@Validated ApiHsBpmProcess apiHsBpmProcess) {
        return hsQwExternalService.applyMasterDetail(apiHsBpmProcess);
    }

    /**
     * 6.3.10获取工作单位信息接口
     *
     * @param apiHsOffice
     * @return
     */
    @PostMapping(value = "officeList")
    @ResponseBody
    public Page<ApiHsOffice> officeList(@Validated ApiHsOffice apiHsOffice) {
        return hsQwExternalService.officeList(apiHsOffice);
    }

    /**
     * 6.3.11获取人员类型接口
     *
     * @param apiHsUser
     * @return
     */
    @PostMapping(value = "userType")
    @ResponseBody
    public Page<ApiHsUser> userType(@Validated ApiHsUser apiHsUser) {
        return hsQwExternalService.userType(apiHsUser);
    }

    /**
     * 6.3.12获取职级类型接口
     *
     * @return
     */
    @PostMapping(value = "position")
    @ResponseBody
    public Page<ApiHsPosition> position(@Validated ApiHsPosition apiHsPosition) {
        return hsQwExternalService.position(apiHsPosition);
    }

    /**
     * 6.3.13获取年度总收入接口
     *
     * @return
     */
    @PostMapping(value = "manualIncome")
    @ResponseBody
    public ApiHsManualIncome manualIncome(@Validated ApiHsManualIncome apiHsManualIncome) {
        return hsQwExternalService.manualIncome(apiHsManualIncome);
    }

    /**
     * 6.3.14 获取申请人配偶信息接口
     *
     * @param apiHsPartnerInfo 请求参数
     * @return ApiHsPartnerInfo 响应数据
     */
    @PostMapping(value = "partnerInfo")
    @ResponseBody
    public ApiHsPartnerInfo partnerInfo(@Validated ApiHsPartnerInfo apiHsPartnerInfo) {
        return hsQwExternalService.partnerInfo(apiHsPartnerInfo);
    }

    /**
     * 6.3.15 获取家庭成员信息接口
     *
     * @param apiHsFamilyInfoList 请求参数
     * @return ApiHsFamilyInfoList 响应数据
     */
    @PostMapping(value = "familyInfoList")
    @ResponseBody
    public Page<ApiHsFamilyInfoList> familyInfoList(@Validated ApiHsFamilyInfoList apiHsFamilyInfoList) {
        return hsQwExternalService.familyInfoList(apiHsFamilyInfoList);
    }

    /**
     * 6.3.16 获取不动产信息接口
     *
     * @param apiHsEstateInfoList 请求参数
     * @return Page<ApiHsEstateInfoList> 响应数据
     */
    @PostMapping(value = "estateInfoList")
    @ResponseBody
    public Page<ApiHsEstateInfoList> estateInfoList(@Validated ApiHsEstateInfoList apiHsEstateInfoList) {
        return hsQwExternalService.estateInfoList(apiHsEstateInfoList);
    }

    /**
     * 6.3.17申请信息提交接口
     *
     * @param apiHsQwApply
     * @return
     */
    @PostMapping(value = "applySave")
    @ResponseBody
    public JSONObject applySave(@Validated ApiHsQwApply apiHsQwApply) {
        return new ApiMapBuilder().putAppend("sqdh", hsQwExternalService.saveApply(apiHsQwApply));
    }

    /**
     * 6.3.18获取配租申请已填写的信息接口
     *
     * @param apiHsQwApply
     * @return
     */
    @PostMapping(value = "applyInfo")
    @ResponseBody
    public ApiHsQwApply applyInfo(ApiHsQwApply apiHsQwApply) {
        return hsQwExternalService.applyInfo(apiHsQwApply);
    }

    /**
     * 6.3.19 配租申请线下看房确认提交接口
     *
     * @return 处理结果
     */
    @PostMapping("/showings")
    @ResponseBody
    public JSONObject confirmShowings(@Validated(NotNullApplyShowing.class) ApiHsQwApply apiHsQwApply) {
        return new ApiMapBuilder().putAppend("sqdh", hsQwExternalService.saveProcessApply(apiHsQwApply).getId());
    }

    /**
     * 6.3.20配租申请确认承租提交接口
     *
     * @return 处理结果
     */
    @PostMapping("/applyConfirm")
    @ResponseBody
    public JSONObject applyConfirm(@Validated(NotNullApplyConfirm.class) ApiHsQwApply apiHsQwApply) {
        return new ApiMapBuilder().putAppend("sqdh", hsQwExternalService.saveProcessApply(apiHsQwApply).getId());
    }


    /**
     * 6.3.21居室变更复查确认提交接口
     *
     * @return 处理结果
     */
    @PostMapping("/replaceUp")
    @ResponseBody
    public JSONObject replaceUp(@Validated(NotNullApplyReplaceUp.class) ApiHsQwApply apiHsQwApply) {
        return new ApiMapBuilder().putAppend("sqdh", hsQwExternalService.saveProcessApply(apiHsQwApply).getId());
    }

    /**
     * 6.3.22 申请进度查询接口
     *
     * @param apiHsApplyProcessData 查询条件
     * @return 查询结果
     */
    @PostMapping("/applyProcess")
    @ResponseBody
    public Page<ApiHsApplyProcessData> applyProcess(@Validated ApiHsApplyProcessData apiHsApplyProcessData) {
        Page<ApiHsApplyProcessData> response = hsQwExternalService.applyProcess(apiHsApplyProcessData);
        return response;
    }

    /**
     * 6.3.23 资格年审查询接口
     *
     * @param request 查询条件
     * @return 查询结果
     */
    @PostMapping("/qualificationList")
    @ResponseBody
    public Page<ApiHsQualificationReviewData> queryQualificationList(@Validated ApiHsQualificationReviewData request) {
        return hsQwExternalService.queryQualificationList(request);
    }

    /**
     * 6.3.24 资格年审确认接口
     * @param request 资格年审确认请求
     * @return 处理结果
     */
    @PostMapping("/qualificationConfirm")
    @ResponseBody
    public ApiHsQualificationConfirm confirmQualification(@Validated ApiHsQualificationConfirm request) {
        HsQwCheckRecord bean = hsQwCheckRecordService.get(request.getHcbh());
        bean.setRenewal(request.getIsCompact());
        ApiHsQualificationConfirm response = hsQwExternalService.saveProcessApply(bean);
        return response;
    }

    /**
     * 6.3.25资格年审申诉接口
     * @return 处理结果
     */
    @PostMapping("/complaint")
    @ResponseBody
    public ApiHsQualificationConfirm complaint(@Validated ApiHsQualificationConfirm request) {
        HsQwCheckRecord bean = hsQwCheckRecordService.get(request.getHcbh());
        bean.setComplaint(request.getComplaint());
        bean.setComplaintReason(request.getSsxx());
        bean.setComplaintDate(new Date());
        ApiHsQualificationConfirm response = hsQwExternalService.saveProcessApply(bean);
        return response;
    }

    /**
     * 6.3.26获取申诉详情接口
     * @return 处理结果
     */
    @PostMapping("/complaintDetail")
    @ResponseBody
    public ApiHsQualificationConfirm complaintDetail(@Validated ApiHsQualificationConfirm request) {
        ApiHsQualificationConfirm response = hsQwExternalService.complaintDetail(request);
        return response;
    }


    /**
     * 6.3.27.1 获取楼盘房屋类型统计信息
     * @param request 查询请求
     * @return 统计结果
     */
    @PostMapping("/houseStatistics")
    @ResponseBody
    public ApiHsHouseStatistics getHouseStatistics(@Validated ApiHsHouseStatistics request) {
        return hsQwExternalService.getHouseStatistics(request);
    }

    /**
     * 6.3.28.1 获取模板房源列表
     * @param request 查询请求
     * @return 查询结果
     */
    @PostMapping("/houseTemplateList")
    @ResponseBody
    public Page<ApiHsHouseTemplateList> getHouseTemplateList(@Validated ApiHsHouseTemplateList request) {
        return  hsQwExternalService.getHouseTemplateList(request);
    }

    /**
     * 6.3.29根据住房编号获取VR视频流接口
     * @param request 查询请求
     * @return 查询结果
     */
    @PostMapping("/xxx")
    public String xxx(@Validated ApiHsHouseTemplateList request) {
        return null;
    }

    /**
     * 6.3.30根据住房编号获取平面图接口
     * @param request 查询请求
     * @return 查询结果
     */
    @PostMapping("/housePlanarList")
    @ResponseBody
    public Page<ApiFile> housePlanarList(@Validated ApiPlanar request) {
        return hsQwExternalService.planarList(request);
    }

    /**
     * 6.3.31根据证件号获取人员类型信息接口
     * @return 查询结果
     */
    @PostMapping("/userMangerType")
    @ResponseBody
    public ApiUserType userType(@Validated ApiBody apiBody) {
        return hsQwExternalService.userMangerType();
    }

    /**
     * 6.3.32获取物业核验房源列表接口
     * @return 查询结果
     */
    @PostMapping("/checkHouseList")
    @ResponseBody
    public Page<ApiHsCheckRecord> checkHouseList(@Validated ApiHsCheckRecord apiHsCheckRecord) {
        return hsQwExternalService.checkHouseList(apiHsCheckRecord);
    }

    /**
     * 6.3.33获取物品清单接口
     * @return 查询结果
     */
    @PostMapping("/objectList")
    @ResponseBody
    public Page<ApiHsCheckObject> objectList(@Validated ApiHsCheckObject apiHsCheckObject) {
        return hsQwExternalService.objectList(apiHsCheckObject);
    }

    /**
     * 6.3.34发起核验提交接口
     * @return 查询结果
     */
    @PostMapping("/checkSave")
    @ResponseBody
    public JSONObject checkSave(@Validated ApiHsCheckInfo apiHsCheckInfo) {
        return new ApiMapBuilder().putAppend("hybh",  hsQwExternalService.checkSave(apiHsCheckInfo));
    }

    /**
     * 6.3.35获取核验记录列表接口
     * @return 查询结果
     */
    @PostMapping("/checkRecordList")
    @ResponseBody
    public Page<ApiHsCheckRecord> checkRecordList(@Validated ApiHsCheckRecord apiHsCheckRecord) {
        return hsQwExternalService.checkRecordList(apiHsCheckRecord);
    }

    /**
     * 6.3.36获取核验记录详情接口
     * @return 查询结果
     */
    @PostMapping("/checkDetail")
    @ResponseBody
    public ApiHsCheckInfo checkDetail(@Validated ApiHsCheckRecord apiHsCheckRecord) {
        if (StringUtils.isBlank(apiHsCheckRecord.getHybh())){
            throw new ServiceException("房源编号不可为空！");
        }
        return hsQwExternalService.checkDetail(apiHsCheckRecord);
    }

    /**
     * 6.3.37获取承租人核验确认信息接口
     * @return 查询结果
     */
    @PostMapping("/checkProcess")
    @ResponseBody
    public ApiHsCheckInfo checkProcess(@Validated ApiHsCheckInfo apiHsCheckInfo) {
        return hsQwExternalService.checkProcess(apiHsCheckInfo);
    }

    /**
     * 6.3.38核验审批提交接口
     * @return 查询结果
     */
    @PostMapping("/checkAudit")
    @ResponseBody
    public JSONObject checkAudit(@Validated ApiHsCheckInfo apiHsCheckInfo) {
        return new ApiMapBuilder().putAppend("hybh",  hsQwExternalService.checkAudit(apiHsCheckInfo));
    }

    /**
     * 6.3.39获取缴费记录列表接口
     * @return 查询结果
     */
    @PostMapping("/managementFeeList")
    @ResponseBody
    public Page<ApiHsManagementFee> managementFeeList(@Validated ApiHsManagementFee apiHsManagementFee) {
        return hsQwExternalService.managementFeeList(apiHsManagementFee);
    }

    /**
     * 6.3.40 6.3.40 限价房源列表查询接口
     * @param request 查询请求
     * @return 查询结果
     */
    @PostMapping("/priceLimitHouseTemplateList")
    @ResponseBody
    public Page<ApiHsPriceLimitHouseTemplateList> getPriceLimitHouseTemplateList(@Validated ApiHsPriceLimitHouseTemplateList request) {
        return  hsQwExternalService.getPriceLimitHouseTemplateList(request);
    }

    /**
     * 清除垃圾数据
     *
     * @return 查询结果
     */
    @PostMapping("/file/test")
    public void file() {

    }

    /**
     * 清除垃圾数据
     *
     * @return 查询结果
     */
    @PostMapping("/clear")
    public void clear() {
        flowableCleanupService.deleteInvalidHistoricInstances();
        flowableCleanupService.deleteInvalidRunningInstances();
    }

    public static void main(String[] args) {
        // 创建申请人
        HsQwApplyer applyer = new HsQwApplyer();
        applyer.setUserId("U123456");
        applyer.setName("张三");
        applyer.setWorkTime(new Date());
        applyer.setOrganization("某某公司");
        applyer.setIdNum("123456789012345678");
        applyer.setPhone("13800138000");
        applyer.setWorkPosition("工程师");
        applyer.setWorkTitle("高级工程师");
        applyer.setAnnualIncome("120000");
        applyer.setApplyId("A10001");
        applyer.setApplyRole("0");

        // 创建申请房屋
        HsQwApplyHouse applyHouse = new HsQwApplyHouse();
        applyHouse.setApplyId("A10001");
        applyHouse.setAddress("北京市朝阳区某小区10号楼2单元301");
        applyHouse.setFloorArea("80");
        applyHouse.setPropertyRight("公租房");

        // 创建申请单
        HsQwApply apply = new HsQwApply();
        apply.setApplyTitle("张三公租房申请");
        apply.setFamilyPeoples("3");
        apply.setFamilyIncome("150000");
        apply.setApplyMatter(HsQwApply.APPLY_MATTER_RENTAL);
        apply.setHouseId("H1001");
        apply.setProcessName(HsQwApply.APPLY_STATUS_AUDIT_UNIT_FIRST);
        apply.setApplyTime(new Date());
        apply.setMainApplyer(applyer);

        // 关联申请人和房屋
        List<HsQwApplyer> applyerList = new ArrayList<>();
        applyerList.add(applyer);
        apply.setHsQwApplyerList(applyerList);

        List<HsQwApplyHouse> houseList = new ArrayList<>();
        houseList.add(applyHouse);
        apply.setHsQwApplyHouseList(houseList);

        Map m = new HashMap();
        m.put("ss", "ss1");
        apply.setDataMap(m);

        // 输出测试数据
        System.out.println("申请信息: " + apply.getApplyTitle());
        System.out.println("申请人: " + applyer.getName() + "，身份证: " + applyer.getIdNum());
        System.out.println("申请房屋: " + applyHouse.getAddress() + "，建筑面积: " + applyHouse.getFloorArea() + "平方米");
        ApiHsQwApply apiHsQwApply = HsBeanMapperUtil.convertBean(apply, ApiHsQwApply.class);
        System.out.println(apiHsQwApply);
    }

}
