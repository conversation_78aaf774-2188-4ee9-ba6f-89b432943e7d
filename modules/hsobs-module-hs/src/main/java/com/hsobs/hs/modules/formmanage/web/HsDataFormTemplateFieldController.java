package com.hsobs.hs.modules.formmanage.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplateField;
import com.hsobs.hs.modules.formmanage.service.HsDataFormTemplateFieldService;

/**
 * 数据表单模板字段Controller
 * <AUTHOR>
 * @version 2025-02-06
 */
@Controller
@RequestMapping(value = "${adminPath}/formmanage/hsDataFormTemplateField")
public class HsDataFormTemplateFieldController extends BaseController {

	@Autowired
	private HsDataFormTemplateFieldService hsDataFormTemplateFieldService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsDataFormTemplateField get(String id, boolean isNewRecord) {
		return hsDataFormTemplateFieldService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplateField:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsDataFormTemplateField hsDataFormTemplateField, Model model) {
		model.addAttribute("hsDataFormTemplateField", hsDataFormTemplateField);
		return "modules/formmanage/hsDataFormTemplateFieldList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplateField:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsDataFormTemplateField> listData(HsDataFormTemplateField hsDataFormTemplateField, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormTemplateField.setPage(new Page<>(request, response));
		Page<HsDataFormTemplateField> page = hsDataFormTemplateFieldService.findPage(hsDataFormTemplateField);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplateField:view")
	@RequestMapping(value = "form")
	public String form(HsDataFormTemplateField hsDataFormTemplateField, Model model) {
		model.addAttribute("hsDataFormTemplateField", hsDataFormTemplateField);
		return "modules/formmanage/hsDataFormTemplateFieldForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplateField:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsDataFormTemplateField hsDataFormTemplateField) {
		hsDataFormTemplateFieldService.save(hsDataFormTemplateField);
		return renderResult(Global.TRUE, text("保存数据表单模板字段成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplateField:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsDataFormTemplateField hsDataFormTemplateField) {
		hsDataFormTemplateFieldService.delete(hsDataFormTemplateField);
		return renderResult(Global.TRUE, text("删除数据表单模板字段成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("formmanage:hsDataFormTemplateField:view")
	@RequestMapping(value = "hsDataFormTemplateFieldSelect")
	public String hsDataFormTemplateFieldSelect(HsDataFormTemplateField hsDataFormTemplateField, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsDataFormTemplateField", hsDataFormTemplateField);
		return "modules/formmanage/hsDataFormTemplateFieldSelect";
	}
	
}