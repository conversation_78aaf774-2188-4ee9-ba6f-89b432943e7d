package com.hsobs.hs.modules.checkdetail.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.checkdetail.entity.HsQwManagementCheckDetail;
import com.hsobs.hs.modules.checkdetail.dao.HsQwManagementCheckDetailDao;

/**
 * 租赁资格轮候物业核验详细表Service
 * <AUTHOR>
 * @version 2025-02-14
 */
@Service
public class HsQwManagementCheckDetailService extends CrudService<HsQwManagementCheckDetailDao, HsQwManagementCheckDetail> {
	
	/**
	 * 获取单条数据
	 * @param hsQwManagementCheckDetail
	 * @return
	 */
	@Override
	public HsQwManagementCheckDetail get(HsQwManagementCheckDetail hsQwManagementCheckDetail) {
		return super.get(hsQwManagementCheckDetail);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwManagementCheckDetail 查询条件
	 * @param hsQwManagementCheckDetail page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwManagementCheckDetail> findPage(HsQwManagementCheckDetail hsQwManagementCheckDetail) {
		return super.findPage(hsQwManagementCheckDetail);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwManagementCheckDetail
	 * @return
	 */
	@Override
	public List<HsQwManagementCheckDetail> findList(HsQwManagementCheckDetail hsQwManagementCheckDetail) {
		return super.findList(hsQwManagementCheckDetail);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwManagementCheckDetail
	 */
	@Override
	@Transactional
	public void save(HsQwManagementCheckDetail hsQwManagementCheckDetail) {
		super.save(hsQwManagementCheckDetail);
	}
	
	/**
	 * 更新状态
	 * @param hsQwManagementCheckDetail
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwManagementCheckDetail hsQwManagementCheckDetail) {
		super.updateStatus(hsQwManagementCheckDetail);
	}
	
	/**
	 * 删除数据
	 * @param hsQwManagementCheckDetail
	 */
	@Override
	@Transactional
	public void delete(HsQwManagementCheckDetail hsQwManagementCheckDetail) {
		super.delete(hsQwManagementCheckDetail);
	}
	
}