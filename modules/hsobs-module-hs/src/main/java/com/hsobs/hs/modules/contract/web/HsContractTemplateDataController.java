package com.hsobs.hs.modules.contract.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.contract.entity.HsContractTemplateData;
import com.hsobs.hs.modules.contract.service.HsContractTemplateDataService;

/**
 * 合同模板详情表Controller
 * <AUTHOR>
 * @version 2025-01-21
 */
@Controller
@RequestMapping(value = "${adminPath}/contract/hsContractTemplateData")
public class HsContractTemplateDataController extends BaseController {

	@Autowired
	private HsContractTemplateDataService hsContractTemplateDataService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsContractTemplateData get(String id, boolean isNewRecord) {
		return hsContractTemplateDataService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("contract:hsContractTemplateData:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsContractTemplateData hsContractTemplateData, Model model) {
		model.addAttribute("hsContractTemplateData", hsContractTemplateData);
		return "modules/contract/hsContractTemplateDataList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("contract:hsContractTemplateData:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsContractTemplateData> listData(HsContractTemplateData hsContractTemplateData, HttpServletRequest request, HttpServletResponse response) {
		hsContractTemplateData.setPage(new Page<>(request, response));
		Page<HsContractTemplateData> page = hsContractTemplateDataService.findPage(hsContractTemplateData);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("contract:hsContractTemplateData:view")
	@RequestMapping(value = "form")
	public String form(HsContractTemplateData hsContractTemplateData, Model model) {
		model.addAttribute("hsContractTemplateData", hsContractTemplateData);
		return "modules/contract/hsContractTemplateDataForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("contract:hsContractTemplateData:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsContractTemplateData hsContractTemplateData) {
		hsContractTemplateDataService.save(hsContractTemplateData);
		return renderResult(Global.TRUE, text("保存合同模板详情表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("contract:hsContractTemplateData:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsContractTemplateData hsContractTemplateData) {
		hsContractTemplateDataService.delete(hsContractTemplateData);
		return renderResult(Global.TRUE, text("删除合同模板详情表成功！"));
	}
	
}