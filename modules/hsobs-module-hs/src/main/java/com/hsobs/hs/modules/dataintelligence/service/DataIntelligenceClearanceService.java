package com.hsobs.hs.modules.dataintelligence.service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.utils.DictUtils;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceClearanceDao;
import com.jeesite.modules.sys.service.OfficeService;
/**
 * 住房保障数据统计Service  公租房清退统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class DataIntelligenceClearanceService extends CrudService<DataIntelligenceClearanceDao, DataIntelligenceClearance> {

	private final DataIntelligenceClearanceDao dataIntelligenceClearanceDao;
	@Autowired
	private DataIntelligenceTotalService dataIntelligenceTotalService;

	public DataIntelligenceClearanceService(DataIntelligenceClearanceDao dataIntelligenceClearanceDao) {
		this.dataIntelligenceClearanceDao = dataIntelligenceClearanceDao;
	}

	/**
	 * 获取单条数据
	 * @param dataIntelligenceClearance
	 * @return
	 */
	@Override
	public DataIntelligenceClearance get(DataIntelligenceClearance dataIntelligenceClearance) {
		return super.get(dataIntelligenceClearance);
	}

	/**
	 * 查询分页数据
	 * @param dataIntelligenceClearance 查询条件
	 * @param dataIntelligenceClearance page 分页对象
	 * @return
	 */
	@Override
	public Page<DataIntelligenceClearance> findPage(DataIntelligenceClearance dataIntelligenceClearance) {
		return super.findPage(dataIntelligenceClearance);
	}

	/**
	 * 查询列表数据
	 * @param dataIntelligenceClearance
	 * @return
	 */
	@Override
	public List<DataIntelligenceClearance> findList(DataIntelligenceClearance dataIntelligenceClearance) {
		return super.findList(dataIntelligenceClearance);
	}

	/**
	 * 保存数据（插入或更新）
	 * @param dataIntelligenceClearance
	 */
	@Override
	@Transactional
	public void save(DataIntelligenceClearance dataIntelligenceClearance) {
		super.save(dataIntelligenceClearance);
	}

	/**
	 * 更新状态
	 * @param dataIntelligenceClearance
	 */
	@Override
	@Transactional
	public void updateStatus(DataIntelligenceClearance dataIntelligenceClearance) {
		super.updateStatus(dataIntelligenceClearance);
	}

	/**
	 * 删除数据
	 * @param dataIntelligenceClearance
	 */
	@Override
	@Transactional
	public void delete(DataIntelligenceClearance dataIntelligenceClearance) {
		dataIntelligenceClearance.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataIntelligenceClearance);
	}

	private  String getSqlOtherWhere(DataIntelligenceClearance dataIntelligenceClearance, Date startDate, Date endDate){
		String sqlOtherWhere = "";

		if (startDate != null || dataIntelligenceClearance.getStartDate() != null) {
			Date date = (startDate!=null)?startDate:dataIntelligenceClearance.getStartDate();
			sqlOtherWhere += " AND a.create_date >= '" + String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}
		if (endDate != null || dataIntelligenceClearance.getEndDate() != null) {
			Date date = (endDate!=null)?endDate:dataIntelligenceClearance.getEndDate();
			sqlOtherWhere += " AND a.create_date <= '" + String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}

		/*if(dataIntelligenceClearance.getEstateName() != null && dataIntelligenceClearance.getEstateName().length() > 0) {
			sqlOtherWhere += " AND estate.name like '%" + dataIntelligenceClearance.getEstateName() + "%'";
		}*/
		if(dataIntelligenceClearance.getEstateId() != null && dataIntelligenceClearance.getEstateId().length() > 0) {
			sqlOtherWhere += " AND estate.id = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceClearance.getEstateId()) + "'";
		}
		if(dataIntelligenceClearance.getCity() != null && !"".equals(dataIntelligenceClearance.getCity())){
			sqlOtherWhere += " AND estate.city = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceClearance.getCity()) + "'";
		}
		if(dataIntelligenceClearance.getArea() != null && !"".equals(dataIntelligenceClearance.getArea())){
			sqlOtherWhere += " AND estate.area = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceClearance.getArea()) + "'";
		}

		dataIntelligenceClearance.sqlMap().getDataScope().addFilter("dsf", "Office", "jso.office_code", DataScope.CTRL_PERMI_HAVE);
		SqlMap sqlMap = dataIntelligenceClearance.sqlMap();
		Object obWhere = sqlMap.get("dsf");
		if(obWhere != null){
			sqlOtherWhere += obWhere.toString();
		}
		String officeCode = null;
		if(dataIntelligenceClearance.getOfficeCode() != null && dataIntelligenceClearance.getOfficeCode().length() > 0) {
			officeCode = dataIntelligenceClearance.getOfficeCode();
		}
		sqlOtherWhere += dataIntelligenceTotalService.getOfficePermission(officeCode);

		return sqlOtherWhere;
	}

	public Page<DataIntelligenceClearance> findClearanceStatPage(DataIntelligenceClearance dataIntelligenceClearance, boolean findpage){

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceClearance, null, null);
		String sqlOrderBy = (dataIntelligenceClearance.getOrderBy()!=null&&dataIntelligenceClearance.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceClearance.getOrderBy()):"";

		Page<DataIntelligenceClearance> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceClearance.getPageNo());
			pageMap.setPageSize(dataIntelligenceClearance.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceClearanceDao.countClearanceStat(mapPara);
		List<DataIntelligenceClearance> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceClearance clearanceStat = new DataIntelligenceClearance();

			Object ob = map.get("ESTATE_ID");
			clearanceStat.setEstateId((ob!=null)?ob.toString():"");
			ob = map.get("ESTATE_NAME");
			clearanceStat.setEstateName((ob!=null)?ob.toString():"");

			// 公租房总数
			ob = map.get("TOTAL_COUNT");
			clearanceStat.setTotalCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 租赁到期户数
			ob = map.get("ARREARS_COUNT");
			clearanceStat.setArrearsCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 租金拖欠户数
			ob = map.get("MATURITY_COUNT");
			clearanceStat.setMaturityCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 违规数
			ob = map.get("VIOLATIONS_COUNT");
			clearanceStat.setViolationsCount((ob!=null)?Long.valueOf(ob.toString()):0);

			statList.add(clearanceStat);
		}

		pageMap.setList(statList);
		return pageMap;
	}

	public List<DataIntelligenceClearance> countClearanceStat(DataIntelligenceClearance dataIntelligenceClearance) {

		return findClearanceStatPage(dataIntelligenceClearance, false).getList();
	}

	public void SetTypeData(List<Map<String,Object>> value, List<List<String>> dataType){
		if(value.size() == 0) {
			dataType.get(0).add("0");
			dataType.get(1).add("0");
			dataType.get(2).add("0");
			dataType.get(3).add("0");
		}
		else {
			Map<String,Object> map = value.get(0);

			// 公租房总数
			Object ob = map.get("TOTAL_COUNT");
			dataType.get(0).add((ob!=null)?ob.toString():"0");
			// 租赁到期户数
			ob = map.get("ARREARS_COUNT");
			dataType.get(1).add((ob!=null)?ob.toString():"0");
			// 租金拖欠户数
			ob = map.get("MATURITY_COUNT");
			dataType.get(2).add((ob!=null)?ob.toString():"0");
			// 违规数
			ob = map.get("VIOLATIONS_COUNT");
			dataType.get(3).add((ob!=null)?ob.toString():"0");
		}
	}

	public String countClearanceCompare(DataIntelligenceClearance dataIntelligenceClearance) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 4; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "总数");break;
				case 1:dataTypeO.put("name", "到期户数");break;
				case 2:dataTypeO.put("name", "拖欠户数");break;
				case 3:dataTypeO.put("name", "违规户数");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		Map<String, Object> mapPara = dataIntelligenceTotalService.getComparePara(dataIntelligenceClearance.getCompareType());

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceClearance, (Date)mapPara.get("last_start_date"), (Date)mapPara.get("last_end_date"));
		List<Map<String,Object>> value = dataIntelligenceClearanceDao.countClearanceCompare(sqlOtherWhere);
		SetTypeData(value, dataType);

		sqlOtherWhere = getSqlOtherWhere(dataIntelligenceClearance, (Date)mapPara.get("now_start_date"), (Date)mapPara.get("now_end_date"));
		value = dataIntelligenceClearanceDao.countClearanceCompare(sqlOtherWhere);
		SetTypeData(value, dataType);

		dataClass.add((String)mapPara.get("last_title"));
		dataClass.add((String)mapPara.get("now_title"));

		Integer max = dataIntelligenceTotalService.calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}


	public String countClearanceTypeStat(DataIntelligenceClearance dataIntelligenceClearance) {

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceClearance, null, null);
		List<Map<String,Object>> list = dataIntelligenceClearanceDao.countClearanceTypeStat(sqlOtherWhere);

		List<Map<String, Object>> newList = new ArrayList<>();
		String key;
		String name;
		for (int i = 0; i < list.size(); i++) {
			Map<String, Object> unitMap = list.get(i);
			Map<String, Object> map = new HashMap<>();

			Object ob = unitMap.get("CLEARANCE_COUNT");
			if (ob == null || "0".equals(ob.toString()))
				continue;
			map.put("value", ob.toString());
			ob = unitMap.get("CLEARANCE_TYPE");

			key = (ob != null) ? ob.toString() : "";
			name = DictUtils.getDictLabels("hs_qw_clearance_type", key, "未知");
			map.put("name", name);

			newList.add(map);
		}

		Map<String, Object> map = new HashMap<>();
		map.put("data", newList);
		map.put("title", "违规类型占比");
		return JSON.toJSONString(map);
	}

	public String countClearanceTypeTop(DataIntelligenceClearance dataIntelligenceClearance) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 1; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "违规户数");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceClearance, null, null);
		List<Map<String,Object>> lst = dataIntelligenceClearanceDao.countClearanceTypeStat(sqlOtherWhere);

		for(Map<String, Object> val: lst){

			Object ob = val.get("CLEARANCE_COUNT");
			if (ob == null || "0".equals(ob.toString()))
				continue;
			dataType.get(0).add((ob!=null)?ob.toString():"0");

			ob = val.get("CLEARANCE_TYPE");
			String key = (ob != null) ? ob.toString() : "";
			String name = DictUtils.getDictLabels("hs_qw_clearance_type", key, "未知");
			dataClass.add(name);
		}

		Integer max = dataIntelligenceTotalService.calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}

}