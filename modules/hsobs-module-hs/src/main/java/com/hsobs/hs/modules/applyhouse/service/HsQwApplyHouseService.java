package com.hsobs.hs.modules.applyhouse.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyhouse.dao.HsQwApplyHouseDao;

/**
 * 租赁资格轮候申请人房屋情况表Service
 * <AUTHOR>
 * @version 2024-11-21
 */
@Service
public class HsQwApplyHouseService extends CrudService<HsQwApplyHouseDao, HsQwApplyHouse> {
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyHouse
	 * @return
	 */
	@Override
	public HsQwApplyHouse get(HsQwApplyHouse hsQwApplyHouse) {
		return super.get(hsQwApplyHouse);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyHouse 查询条件
	 * @param hsQwApplyHouse page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyHouse> findPage(HsQwApplyHouse hsQwApplyHouse) {
		return super.findPage(hsQwApplyHouse);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyHouse
	 * @return
	 */
	@Override
	public List<HsQwApplyHouse> findList(HsQwApplyHouse hsQwApplyHouse) {
		return super.findList(hsQwApplyHouse);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyHouse
	 */
	@Override
	@Transactional
	public void save(HsQwApplyHouse hsQwApplyHouse) {
		super.save(hsQwApplyHouse);
	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyHouse
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyHouse hsQwApplyHouse) {
		super.updateStatus(hsQwApplyHouse);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyHouse
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyHouse hsQwApplyHouse) {
		super.delete(hsQwApplyHouse);
	}
	
}