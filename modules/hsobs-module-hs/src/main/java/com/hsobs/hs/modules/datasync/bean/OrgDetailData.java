package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取单位详细信息响应实体
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgDetailData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String orgName;
    private String provinceCode;
    private String cityCode;
    private String areaCode;
    private String administrativeName;
    private String address;
    private String orgCode;
    private String orgType;
    private String extendField;
    private String socialCreditCode;

}
