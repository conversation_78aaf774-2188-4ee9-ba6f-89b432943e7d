package com.hsobs.hs.modules.formmanage.service;

import com.hsobs.hs.modules.formmanage.entity.HsDataFormFillRecord;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplateField;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.utils.EmpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 表单填写智能填报
 * <AUTHOR>
 */
@Service
public class HsDataFormIntelligentFillService {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 智能填报
     * @param fillRecord  填写对象
     * @param field       字段属性信息
     * @param deliveryId  下发表单消息ID
     */
    public void fill(HsDataFormFillRecord fillRecord, HsDataFormTemplateField field, String deliveryId) {
        //TODO 智能填报
        try {
            User user = fillRecord.currentUser();
            String officeName = EmpUtils.get(user).getOffice().getOfficeName();

            String key = field.getFieldKey();
            String fieldName = field.getFieldName();

            if (fieldName.contains("用户名") || "userName".equals(key) || "name".equals(key)) {
                fillRecord.setFieldValue(user.getUserName());
            } else if (fieldName.contains("邮箱") || "email".equals(key)) {
                fillRecord.setFieldValue(user.getEmail());
            } else if (fieldName.contains("手机号码") || "mobile".equals(key) || "phone".equals(key)) {
                fillRecord.setFieldValue(user.getMobile());
            } else if (fieldName.contains("工作单位") || "workUnit".equals(key)) {
                fillRecord.setFieldValue(officeName);
            }
            //TODO 根据所属下发记录已填写数据获取单项填写重复率超过10%的值，多个默认取第一个
        } catch (Exception e) {
            logger.error("智能填充表单字段值异常", e);
        }
    }
}
