package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.applypublicdetail.service.HsQwApplyPublicDetailService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 公租房申请，配租房源：选择已经公示的房源、没有配租的房源1，进行选择配租
 */
@Service
public class HsQwHouseSelectListRent implements HsQwHouseSelectList {

    @Autowired
    private HsQwApplyPublicDetailService hsQwApplyPublicDetailService;

    public String getDataType() {
        return "3";
    }// 公租房配租

    public Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse,
            HsQwPublicRentalHouseService hsQwPublicRentalHouseService) {
        hsQwPublicRentalHouse.setStatus_in(new String[]{HsQwPublicRentalHouse.STATUS_NORMAL,HsQwPublicRentalHouse.STATUS_AUDIT});
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_RENTAL);
        hsQwPublicRentalHouse.setHouseStatus("0");
        hsQwPublicRentalHouse.setIsPublic("1");
        hsQwPublicRentalHouse.sqlMap().getWhere().disableAutoAddStatusWhere();
        String houseIds = hsQwApplyPublicDetailService.getHasApplyIdStr(hsQwPublicRentalHouse.getApplyId());
        hsQwPublicRentalHouse.sqlMap().getWhere().and("id", QueryType.IN, houseIds.split(","));
        StringBuffer sb = new StringBuffer(" and a.id not in " +
                " (select hqa1.house_id from hs_qw_apply hqa1 where " +
                "  hqa1.status = 4 AND hqa1.HOUSE_ID  IS NOT NULL ");
        if (StringUtils.isNotBlank(hsQwPublicRentalHouse.getSelectId())){
            sb.append(" and hqa1.house_id != " + hsQwPublicRentalHouse.getSelectId());
        }
        sb.append(")");
        hsQwPublicRentalHouse.sqlMap().add("extWhere", sb.toString());
        return hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
    }
}
