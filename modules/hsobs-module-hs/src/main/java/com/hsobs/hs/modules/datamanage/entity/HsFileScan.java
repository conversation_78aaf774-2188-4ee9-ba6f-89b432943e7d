package com.hsobs.hs.modules.datamanage.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;

/**
 * 文档扫描信息Entity
 * <AUTHOR>
 * @version 2025-01-19
 */
@Table(name="hs_file_scan", alias="a", label="文档扫描信息信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="class_id", attrName="classId", label="档案目录分类ID"),
		@Column(name="class_code", attrName="classCode", label="档案目录分类编码"),
		@Column(name="file_name", attrName="fileName", label="文档名称", queryType=QueryType.LIKE),
		@Column(name="business_type", attrName="businessType", label="业务类别", queryType=QueryType.LIKE),
		@Column(name="file_url", attrName="fileUrl", label="文档保存URL"),
		@Column(name="file_type", attrName="fileType", label="文档类型"),
		@Column(name="file_remark", attrName="fileRemark", label="文档备注信息"),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, joinTable={
		@JoinTable(type= JoinTable.Type.JOIN, entity= HsFileClass.class, alias="f",
				on="f.id=a.class_id",
				attrName="hsFileClass", columns={
				@Column(includeEntity= BaseEntity.class),
				@Column(includeEntity=DataEntity.class),
				@Column(name="class_name", attrName="className", label="分类名称"),
				@Column(name="group_type", attrName="groupType", label="文件分组类型"),
				@Column(name="business_type", attrName="businessType", label="业务类别", queryType=QueryType.LIKE),
				@Column(name="class_code", attrName="classCode", label="分类编码")
		}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = EmpUser.class, alias = "u",
				on = "u.user_code = a.create_by", attrName = "user",
				columns = {@Column(includeEntity = EmpUser.class)}
		),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= Employee.class, alias="e",
				on="e.emp_code=u.ref_code AND u.user_type='employee'",
				attrName="employee", columns={@Column(includeEntity= Employee.class)}
		),
		@JoinTable(type= JoinTable.Type.LEFT_JOIN, entity= Office.class, alias="o",
				on="o.office_code = e.office_code",
				columns={@Column(includeEntity=Office.class)})
    }, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere", orderBy="a.update_date DESC"
)
public class HsFileScan extends DataEntity<HsFileScan> {
	
	private static final long serialVersionUID = 1L;
	private String classId;		// 档案目录分类ID
	private String classCode;		// 档案目录分类编码
	private String fileName;		// 文档名称
	private String businessType;		// 业务类别
	private String fileUrl;		// 文档保存URL
	private String fileType;		// 文档类型
	private String fileRemark;		// 文档备注信息
	private String validTag;		// 是否有效;1-有效 0-无效

	private HsFileClass hsFileClass;

	private EmpUser user;
	private Employee employee;
	private Office office;

	@ExcelFields({
		@ExcelField(title="档案编号", attrName="id", align= ExcelField.Align.CENTER, sort=20),
		@ExcelField(title="文档名称", attrName="fileName", align= ExcelField.Align.CENTER, sort=30),
		@ExcelField(title="档案分类", attrName="hsFileClass.className", align= ExcelField.Align.CENTER, sort=40),
		@ExcelField(title="分类编码", attrName="hsFileClass.classCode", align= ExcelField.Align.CENTER, sort=50),
		@ExcelField(title="业务类别", attrName="businessType", align= ExcelField.Align.CENTER, sort=60),
		@ExcelField(title="文档备注信息", attrName="fileRemark", align= ExcelField.Align.CENTER, sort=70),
		@ExcelField(title="创建日期", attrName="createDate", align= ExcelField.Align.CENTER, words=20, sort=100, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
		@ExcelField(title="更新日期", attrName="updateDate", align= ExcelField.Align.CENTER, words=20, sort=110, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
	})

	public HsFileScan() {
		this(null);
	}
	
	public HsFileScan(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="档案目录分类ID长度不能超过 64 个字符")
	public String getClassId() {
		return classId;
	}

	public void setClassId(String classId) {
		this.classId = classId;
	}
	
	@Size(min=0, max=255, message="档案目录分类编码长度不能超过 255 个字符")
	public String getClassCode() {
		return classCode;
	}

	public void setClassCode(String classCode) {
		this.classCode = classCode;
	}
	
	@Size(min=0, max=90, message="文档名称长度不能超过 90 个字符")
	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
	@Size(min=0, max=255, message="业务类别长度不能超过 255 个字符")
	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}
	
	@Size(min=0, max=255, message="文档保存URL长度不能超过 255 个字符")
	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}
	
	@Size(min=0, max=255, message="文档类型长度不能超过 255 个字符")
	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}
	
	@Size(min=0, max=120, message="文档备注信息长度不能超过 120 个字符")
	public String getFileRemark() {
		return fileRemark;
	}

	public void setFileRemark(String fileRemark) {
		this.fileRemark = fileRemark;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public HsFileClass getHsFileClass() {
		return hsFileClass;
	}

	public void setHsFileClass(HsFileClass hsFileClass) {
		this.hsFileClass = hsFileClass;
	}

	public EmpUser getUser() {
		return user;
	}

	public void setUser(EmpUser user) {
		this.user = user;
	}

	public Employee getEmployee() {
		return employee;
	}

	public void setEmployee(Employee employee) {
		this.employee = employee;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}
}