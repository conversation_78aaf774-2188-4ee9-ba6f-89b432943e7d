package com.hsobs.hs.modules.hsalarm.service;

import java.util.*;
import java.math.BigDecimal;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.hsalarmset.entity.HsAlarmSet;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.service.support.HsBpmTask;
import com.jeesite.modules.sys.entity.DictData;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.service.DictDataService;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.service.OfficeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.hsalarm.entity.HsAlarm;
import com.hsobs.hs.modules.hsalarm.dao.HsAlarmDao;

import com.hsobs.hs.modules.hsalarmset.service.HsAlarmSetService;

/**
 * 告警信息Service
 * <AUTHOR>
 * @version 2025-01-03
 */
@Service
public class HsAlarmService extends CrudService<HsAlarmDao, HsAlarm> {

	@Autowired
	private HsAlarmSetService hsAlarmSetService;

	@Autowired
	private HsAlarmDao hsAlarmDao;

	@Autowired
	private HsQwApplyService hsQwApplyService;
	@Autowired
	private BpmTaskService bpmTaskService;
	@Autowired
	private OfficeService officeService;
	@Autowired
	private EmpUserService empUserService;
	@Autowired
	private ApiSzkjService apiSzkjService;
	@Autowired
	private DictDataService dictDataService;

	/**
	 * 获取单条数据
	 * @param hsAlarm
	 * @return
	 */
	@Override
	public HsAlarm get(HsAlarm hsAlarm) {

		HsAlarm alarm = super.get(hsAlarm);
		return alarm;
	}

	/**
	 * 查询分页数据
	 * @param hsAlarm 查询条件
	 * @param hsAlarm page 分页对象
	 * @return
	 */
	@Override
	public Page<HsAlarm> findPage(HsAlarm hsAlarm) {

		hsAlarm.sqlMap().getDataScope().addFilter("dsf", "Office", "a.office_code", DataScope.CTRL_PERMI_HAVE);

		if(hsAlarm.getStartDate() != null) {
			Date date = hsAlarm.getStartDate();
			hsAlarm.sqlMap().getWhere().and("create_date", QueryType.GTE, String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()));
		}
		if(hsAlarm.getEndDate() != null) {
			Date date = hsAlarm.getEndDate();
			hsAlarm.sqlMap().getWhere().and("create_date", QueryType.LTE, String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()));
		}
		return super.findPage(hsAlarm);
	}

	/**
	 * 查询列表数据
	 * @param hsAlarm
	 * @return
	 */
	@Override
	public List<HsAlarm> findList(HsAlarm hsAlarm) {
		return super.findList(hsAlarm);
	}

	/**
	 * 保存数据（插入或更新）
	 * @param hsAlarm
	 */
	@Override
	@Transactional
	public void save(HsAlarm hsAlarm) {
		super.save(hsAlarm);
	}

	/**
	 * 更新状态
	 * @param hsAlarm
	 */
	@Override
	@Transactional
	public void updateStatus(HsAlarm hsAlarm) {
		super.updateStatus(hsAlarm);
	}

	/**
	 * 删除数据
	 * @param hsAlarm
	 */
	@Override
	@Transactional
	public void delete(HsAlarm hsAlarm) {
		super.delete(hsAlarm);
	}

	public void sendNewAlarm(HsAlarm hsAlarm) {

		if(hsAlarm.getPublicStatus() != null && hsAlarm.getPublicStatus().equals("1")) {
			throw new ServiceException("已经发布过告警信息，不能重复发布");
		}

		DictData data = new DictData();
		data.setDictType("hs_alarm_type");
		data.setDictValue(hsAlarm.getAlarmType());
		List<DictData> lstDict = dictDataService.findList(data);
		if(lstDict == null || lstDict.isEmpty()) {
			return;
		}
		String alarmType = lstDict.get(0).getDictLabel();

		Office office = new Office();
		office.setOfficeCode(hsAlarm.getOfficeCode());
		office = officeService.get(office);
		String officeCode = office.getExtAspId();
		if(officeCode == null || "".equals(officeCode)) {
			officeCode = office.getOfficeCode();
		}

		EmpUser empUser = new EmpUser();
		empUser.setUserCode(hsAlarm.getUserId());
		empUser = empUserService.get(empUser);
		String receiveUserIds = empUser.getExtAspId();
		if(receiveUserIds == null || "".equals(receiveUserIds)) {
			receiveUserIds = empUser.getLoginCode();
		}

		Api2NoticeBody body = new Api2NoticeBody();
		body.setMsgId(hsAlarm.getId());
		body.setMsgType("3");		// 公告
		body.setMsgSource("智能分析预警");
		body.setTopic(alarmType);
		body.setContent(hsAlarm.getAlarmInfo());
		body.setFileIds("");
		body.setPublishUnit(office.getFullName());
		body.setPublishUnitId(officeCode);
		body.setPublishTime(hsAlarm.getCreateDate());
		body.setReceiveUserIds(receiveUserIds);

		Api2ResponseBody responseBody = apiSzkjService.uploadNotice(body);
		if(responseBody.getCode() != 1)
			throw new ServiceException(responseBody.getMessage());

		hsAlarm.setPublicStatus("1");
		super.save(hsAlarm);
	}
	// 插入告警
	private void saveNewAlarm(String userId, String officeCode, String type, String alarmInfo, String jumpToUrl){

		if(officeCode == null || officeCode.length() == 0){
			return;
		}

		HsAlarm alarm = new HsAlarm();

		alarm.setUserId(userId);
		alarm.setOfficeCode(officeCode);
		alarm.setAlarmType(type);	// 公租房未签合同 0   公租房租金拖欠 1	资格核查异常 2	维修补助额度不足 3
		alarm.setAlarmStatus("0");
		alarm.setJumpToUrl(jumpToUrl);
		alarm.setAlarmInfo(alarmInfo);
		save(alarm);

		//sendNewAlarm(alarm);
	}

	// 未签合同
	public void executeUnsignedContract()
	{
		HsBpmTask params = new HsBpmTask();
		params.setStatus("1");
		params.getProcIns().setFormKey("rent_apply");//动态设置formKey
		params.setName("签订租赁合同");
		Page<BpmTask> myHsTaskPage = this.bpmTaskService.findTaskPage(params);

		//公租房个人换租
		params = new HsBpmTask();
		params.setStatus("1");
		params.getProcIns().setFormKey("rent_apply_house");//动态设置formKey
		params.setName("重新签订合同");
		Page<BpmTask> myHsTaskPageCopy = this.bpmTaskService.findTaskPage(params);

		//集合进程中id
		List<String> hsIds = new ArrayList();
		myHsTaskPage.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		myHsTaskPageCopy.getList().forEach((k -> hsIds.add(k.getProcIns().getBizKey())));
		HsQwApply hsQwApply = new HsQwApply();
		hsQwApply.sqlMap().getWhere().and("id", QueryType.IN, hsIds.toArray());
		List<HsQwApply> result = hsQwApplyService.findList(hsQwApply);

		for(int j = 0; j < result.size(); j ++){
			HsQwApply apply = result.get(j);
			// 生成告警
			HsQwApplyer player = apply.getMainApplyer();
			this.saveNewAlarm(apply.getCreateBy(), apply.getOfficeCode(), "0", player.getOrganization()+"的"+player.getName()+"("+player.getPhone()+")未签合同", "");
		}

	}
	// 拖欠租金
	public void executeRentArrears()
	{
		Date currentDate = new Date();
		if(currentDate.getDate()!=1) {
			return;// 必须每个月的第一天
		}

		HsAlarmSet alarmSet = new HsAlarmSet();
		alarmSet.setId("0123456789");
		alarmSet = hsAlarmSetService.get(alarmSet);
		if(alarmSet == null){
			return;
		}
		if(alarmSet.getRentArrearsCycle() == 2){// 按年告警时，1月份告警
			if(currentDate.getMonth() != Calendar.JANUARY){
				return;
			}
		}

		// 处理告警
		Object ob;
		List<Map<String,Object>> list = hsAlarmDao.countRentArrears();
		for (int j = 0; j < list.size(); j++) {
			Map map = list.get(j);

			ob = map.get("DIFF_MONTH");
			int diffMonth = (ob==null)?0:Integer.valueOf(ob.toString());

			ob = map.get("MONTH_FEE");
			int nonthFee = (ob==null)?0:Integer.valueOf(ob.toString());
			int montheFees = diffMonth*nonthFee;

			if(diffMonth >= alarmSet.getRentArrearsTime()
					|| montheFees >= alarmSet.getRentArrearsPrice()){
			}
			else {
				continue;
			}

			// 生成告警
			ob = map.get("OFFICE_CODE");
			String officeCode = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_ORGANIZATION");
			String applyerOrganization = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_NAME");
			String applyerName = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_PHONE");
			String applyerPhone = (ob!=null)?ob.toString():"";
			// 生成告警
			this.saveNewAlarm((String)map.get("APPLY_CREATE_BY"), officeCode, "1", String.format("%s的%s(%s)拖欠租金%d月,月租%d元累计%d元",
					applyerOrganization, applyerName, applyerPhone, diffMonth, nonthFee, montheFees), "");
		}
	}
	// 时间异常
	public void executeTimeAnomaly()
	{
		Date currentDate = new Date();
		if(currentDate.getDate()!=1) {
			return;// 必须每个月的第一天
		}
		HsAlarmSet alarmSet = new HsAlarmSet();
		alarmSet.setId("0123456789");
		alarmSet = hsAlarmSetService.get(alarmSet);
		if(alarmSet == null){
			return;
		}
		if(alarmSet.getRentArrearsCycle() == 2){// 按年告警时，1月份告警
			if(currentDate.getMonth() != Calendar.JANUARY){
				return;
			}
		}

		// 处理告警
		List<Map<String,Object>> list = hsAlarmDao.countTimeAnomaly();
		for (int j = 0; j < list.size(); j++) {
			Map map = list.get(j);

			Object ob = map.get("OFFICE_CODE");
			String officeCode = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_ORGANIZATION");
			String applyerOrganization = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_NAME");
			String applyerName = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_PHONE");
			String applyerPhone = (ob!=null)?ob.toString():"";
			// 生成告警
			this.saveNewAlarm((String)map.get("APPLY_CREATE_BY"), officeCode, "2", applyerOrganization+"的"+applyerName+"("+applyerPhone+")时间异常", "");
		}
	}
	// 资格异常
	public void executeAbnormalQualification()
	{
		Date currentDate = new Date();
		if(currentDate.getDate()!=1) {
			return;// 必须每个月的第一天
		}
		HsAlarmSet alarmSet = new HsAlarmSet();
		alarmSet.setId("0123456789");
		alarmSet = hsAlarmSetService.get(alarmSet);
		if(alarmSet == null){
			return;
		}
		if(alarmSet.getRentArrearsCycle() == 2){// 按年告警时，1月份告警
			if(currentDate.getMonth() != Calendar.JANUARY){
				return;
			}
		}

		// 处理告警
		List<Map<String,Object>> list = hsAlarmDao.countAbnormalQualification();
		for (int j = 0; j < list.size(); j++) {
			Map map = list.get(j);

			Object ob = map.get("OFFICE_CODE");
			String officeCode = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_ORGANIZATION");
			String applyerOrganization = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_NAME");
			String applyerName = (ob!=null)?ob.toString():"";
			ob = map.get("APPLYER_PHONE");
			String applyerPhone = (ob!=null)?ob.toString():"";
			// 生成告警
			this.saveNewAlarm((String)map.get("APPLY_CREATE_BY"), officeCode, "2", applyerOrganization+"的"+applyerName+"("+applyerPhone+")资格异常", "");
		}
	}
	// 维修补助额度不足
	public void executeInsufficientCreditLimit()
	{
		HsAlarmSet alarmSet = new HsAlarmSet();
		alarmSet.setId("0123456789");
		alarmSet = hsAlarmSetService.get(alarmSet);
		if(alarmSet == null){
			return;
		}
		Date currentDate = new Date();
		if(alarmSet.getReminderDate() != null) {
			long diff = currentDate.getTime()-alarmSet.getReminderDate().getTime();
			long days = diff / (1000 * 60 * 60 * 24);
			if(days < alarmSet.getReminderCycle()) {
				return;
			}
		}
		alarmSet.setReminderDate(currentDate);
		hsAlarmSetService.update(alarmSet);

		// 处理告警
		List<Map<String,Object>> list = hsAlarmDao.countInsufficientCreditLimit(alarmSet.getRemainingUndrawn(), alarmSet.getLimitUndrawn());
		for (int j = 0; j < list.size(); j++) {
			Map map = list.get(j);
			// 生成告警
			Object ob = map.get("ESTATE");
			String estate = "";
			if(ob != null){
				estate = ob.toString();
			}
			BigDecimal totalAmount = (BigDecimal)map.get("TOTAL_AMOUNT");
			BigDecimal remainingAmount = (BigDecimal)map.get("REMAINING_AMOUNT");
			this.saveNewAlarm((String)map.get("USERID"), (String)map.get("OFFICE_CODE"), "3", "小区："+estate+",维修资金额度:"+totalAmount.toString()+",剩余额度:"+remainingAmount.toString(), "");
		}
	}

	public void executeAlarmTask(){
		//saveNewAlarm("SD006", "0", "nihaoa");
		// 未签合同
		//executeUnsignedContract();
		// 拖欠租金
		//executeRentArrears();
		// 时间异常
		//executeTimeAnomaly();
		// 资格异常
		//executeAbnormalQualification();
		// 维修补助额度不足
		//executeInsufficientCreditLimit();
	}
}