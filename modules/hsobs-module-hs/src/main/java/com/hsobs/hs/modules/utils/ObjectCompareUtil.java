package com.hsobs.hs.modules.utils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.web.http.ServletUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 对象对比工具类
 */
public class ObjectCompareUtil {

    /**
     * 从文件ID获取文件名
     */
    private static String getFileName(String fileId) {
        if (StringUtils.isEmpty(fileId)) {
            return "";
        }
        String fileName = FileUploadUtils.getFileUploadService().get(fileId).getFileName();
        if (StringUtils.isEmpty(fileName)) {
            return fileId;
        }
        return fileName;
    }

    /**
     * 获取类的所有字段（包括父类字段）
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * 比较两个对象的属性值差异
     * 
     * @param oldObj        原对象
     * @param newObj        新对象
     * @param objectName    对象名称（如：hsQwApply）
     * @param excludeFields 需要排除的字段名数组
     * @param <T>           对象类型
     * @return 差异Map，key为属性路径，value为差异说明
     */
    public static <T> Map<String, String> compareObjects(T oldObj, T newObj, String objectName,
            String... excludeFields) {
        Map<String, String> differences = new HashMap<>();

        if (oldObj == null || newObj == null) {
            return differences;
        }

        // 将排除字段转换为List，方便判断
        List<String> excludeFieldList = excludeFields != null ? Arrays.asList(excludeFields) : new ArrayList<>();

        // 获取对象的所有字段（包括父类字段）
        List<Field> fields = getAllFields(oldObj.getClass());

        for (Field field : fields) {
            // 跳过需要排除的字段
            if (excludeFieldList.contains(field.getName())) {
                continue;
            }

            field.setAccessible(true);
            try {
                Object oldValue = field.get(oldObj);
                Object newValue = field.get(newObj);

                // 特殊处理 dataMap 字段
                if ("dataMap".equals(field.getName()) && oldValue instanceof Map && newValue instanceof Map) {
                    compareDataMap(objectName, (Map<String, Object>) oldValue,
                            (Map<String, Object>) newValue, differences);
                    continue;
                }

                // 处理不同类型的字段
                if (oldValue instanceof List) {
                    compareLists(objectName + "." + field.getName(), (List<?>) oldValue, (List<?>) newValue,
                            differences);
                } else if (oldValue != null && !isPrimitiveOrWrapper(oldValue.getClass())) {
                    compareNestedObject(objectName + "." + field.getName(), oldValue, newValue, differences);
                } else if (!Objects.equals(oldValue, newValue)) {
                    differences.put(objectName + "." + field.getName(),
                            String.format("原值: %s, 新值: %s",
                                    formatValue(oldValue), formatValue(newValue)));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        return differences;
    }

    /**
     * 比较 dataMap 的差异
     */
    private static void compareDataMap(String fieldName, Map<String, Object> oldMap, Map<String, Object> newMap,
            Map<String, String> differences) {
        if (oldMap == null || newMap == null) {
            if (oldMap != newMap) {
                differences.put(fieldName, "文件映射为空状态不一致");
            }
            return;
        }

        // 检查删除的文件
        oldMap.forEach((key, value) -> {
            if (!newMap.containsKey(key) && !newMap.containsKey(key + "__del")) {
                String[] oldFileIds = value.toString().split(",");
                String[] oldFileNames = Arrays.stream(oldFileIds)
                        .map(ObjectCompareUtil::getFileName)
                        .toArray(String[]::new);
                differences.put(fieldName + "." + key, "文件被删除: " + String.join(", ", oldFileNames));
            }
        });

        // 检查新增的文件和标记删除的文件
        newMap.forEach((key, value) -> {
            if (StringUtils.isEmpty(value.toString())) {
                return;
            }
            if (key.endsWith("__del")) {
                String originalKey = key.substring(0, key.length() - 5);
                String[] delFileIds = value.toString().split(",");
                String[] delFileNames = Arrays.stream(delFileIds)
                        .map(ObjectCompareUtil::getFileName)
                        .toArray(String[]::new);
                differences.put(fieldName + "." + originalKey, "文件被标记删除: " + String.join(", ", delFileNames));
                return;
            }
            if (!oldMap.containsKey(key)) {
                String[] newFileIds = value.toString().split(",");
                String[] newFileNames = Arrays.stream(newFileIds)
                        .map(ObjectCompareUtil::getFileName)
                        .toArray(String[]::new);
                differences.put(fieldName + "." + key, "新增文件: " + String.join(", ", newFileNames));
            } else {
                // 比较文件列表的变化
                String[] oldFileIds = oldMap.get(key).toString().split(",");
                String[] newFileIds = value.toString().split(",");
                Set<String> oldSet = new HashSet<>(Arrays.asList(oldFileIds));
                Set<String> newSet = new HashSet<>(Arrays.asList(newFileIds));

                // 找出删除的文件
                Set<String> deletedFiles = new HashSet<>(oldSet);
                deletedFiles.removeAll(newSet);
                if (!deletedFiles.isEmpty()) {
                    String[] deletedFileNames = deletedFiles.stream()
                            .map(ObjectCompareUtil::getFileName)
                            .toArray(String[]::new);
                    differences.put(fieldName + "." + key, "文件被删除: " + String.join(", ", deletedFileNames));
                }

                // 找出新增的文件
                Set<String> addedFiles = new HashSet<>(newSet);
                addedFiles.removeAll(oldSet);
                if (!addedFiles.isEmpty()) {
                    String[] addedFileNames = addedFiles.stream()
                            .map(ObjectCompareUtil::getFileName)
                            .toArray(String[]::new);
                    differences.put(fieldName + "." + key, "新增文件: " + String.join(", ", addedFileNames));
                }
            }
        });
    }

    private static void putAppend(Map<String, String> differences, String key, String value) {
        if (differences.containsKey(key)) {
            differences.put(key, differences.get(key) + "," + value);
        } else {
            differences.put(key, value);
        }
    }

    /**
     * 比较两个列表的差异
     */
    private static void compareLists(String fieldName, List<?> oldList, List<?> newList,
            Map<String, String> differences) {
        if (oldList == null || newList == null) {
            if (oldList != newList) {
                differences.put(fieldName, "列表为空状态不一致");
            }
            return;
        }

        // 如果列表元素是对象，进行深度比较
        if (!oldList.isEmpty() && !isPrimitiveOrWrapper(oldList.get(0).getClass())) {
            // 创建ID到对象的映射
            Map<String, Object> oldMap = new HashMap<>();
            Map<String, Object> newMap = new HashMap<>();

            // 将列表元素按ID分组
            for (Object obj : oldList) {
                String id = getId(obj);
                oldMap.put(id, obj);
            }
            for (Object obj : newList) {
                String id = getId(obj);
                newMap.put(id, obj);
            }

            // 检查删除的元素
            oldMap.forEach((id, oldObj) -> {
                if (!newMap.containsKey(id)) {
                    differences.put(fieldName + "[" + id + "]", "原列表有此元素，新列表无此元素");
                }
            });

            // 检查新增的元素和比较共同元素
            newMap.forEach((id, newObj) -> {
                if (!oldMap.containsKey(id)) {
                    differences.put(fieldName + "[" + id + "]", "新列表有此元素，原列表无此元素");
                } else {
                    // 比较共同元素
                    compareNestedObject(fieldName + "[" + id + "]", oldMap.get(id), newObj, differences);
                }
            });
        } else {
            // 如果是基本类型列表，直接比较
            if (!oldList.equals(newList)) {
                differences.put(fieldName,
                        String.format("原列表: %s, 新列表: %s",
                                formatValue(oldList), formatValue(newList)));
            }
        }
    }

    private static String getId(Object obj) {
        if (obj instanceof BaseEntity) {
            return ((BaseEntity) obj).getId();
        }
        // 如果不是 BaseEntity，尝试通过反射获取 id 字段
        try {
            Field idField = obj.getClass().getDeclaredField("id");
            idField.setAccessible(true);
            Object idValue = idField.get(obj);
            return idValue != null ? idValue.toString() : "0";
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 比较嵌套对象的差异
     */
    private static void compareNestedObject(String path, Object oldObj, Object newObj,
            Map<String, String> differences) {
        if (oldObj == null || newObj == null) {
            if (oldObj != newObj) {
                differences.put(path, "对象为空状态不一致");
            }
            return;
        }

        // 获取嵌套对象的所有字段（子对象不用扫描父类）
        List<Field> fields = Arrays.asList(oldObj.getClass().getDeclaredFields());
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object oldValue = field.get(oldObj);
                Object newValue = field.get(newObj);
                String fieldPath = path + "." + field.getName();

                if (oldValue instanceof List) {
                    compareLists(fieldPath, (List<?>) oldValue, (List<?>) newValue, differences);
                } else if (oldValue != null && !isPrimitiveOrWrapper(oldValue.getClass())) {
                    compareNestedObject(fieldPath, oldValue, newValue, differences);
                } else if (!Objects.equals(oldValue, newValue)) {
                    differences.put(fieldPath,
                            String.format("原值: %s, 新值: %s",
                                    formatValue(oldValue), formatValue(newValue)));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 判断是否为基本类型或其包装类型
     */
    private static boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz == String.class ||
                clazz == Boolean.class ||
                clazz == Character.class ||
                clazz == Byte.class ||
                clazz == Short.class ||
                clazz == Integer.class ||
                clazz == Long.class ||
                clazz == Float.class ||
                clazz == Double.class ||
                clazz == Date.class;
    }

    /**
     * 格式化值的显示
     */
    private static String formatValue(Object value) {
        if (value == null) {
            return "null";
        }
        if (value instanceof Date) {
            return value.toString();
        }
        if (value instanceof Collection) {
            return ((Collection<?>) value).stream()
                    .map(ObjectCompareUtil::formatValue)
                    .collect(Collectors.joining(", ", "[", "]"));
        }
        return value.toString();
    }
}