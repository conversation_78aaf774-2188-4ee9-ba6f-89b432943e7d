package com.hsobs.hs.modules.checkdetail.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.checkobject.entity.HsQwManagementCheckObject;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.external.entity.HsMapTo;
import com.hsobs.hs.modules.managementcheck.entity.HsQwManagementCheck;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候物业核验详细表Entity
 * <AUTHOR>
 * @version 2025-02-14
 */
@Table(name="hs_qw_management_check_detail", alias="a", label="租赁资格轮候物业核验详细表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="check_id", attrName="checkId", label="资格审查id"),
		@Column(name="object_id", attrName="objectId", label="物品id"),
		@Column(name="damage", attrName="damage", label="是否损坏", comment="是否损坏（0否 1是）"),
		@Column(name="maintenance", attrName="maintenance", label="是否维修", comment="是否维修（0否 1是）"),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwManagementCheckObject.class, alias = "o",
				on = "a.object_id = o.id", attrName="checkObject",
				columns = {@Column(includeEntity = HsQwManagementCheckObject.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwManagementCheck.class, alias = "m",
				on = "m.id = a.check_id", attrName = "managementCheck",
				columns = {@Column(includeEntity = HsQwManagementCheck.class)})
	}, orderBy="a.update_date DESC"
)
public class HsQwManagementCheckDetail extends DataEntity<HsQwManagementCheckDetail> {
	
	private static final long serialVersionUID = 1L;
	@HsMapTo("checkId")
	private String checkId;		// 资格审查id
	@HsMapTo("wpbh")
	private String objectId;		// 物品id
	@HsMapTo("wpmc")
	private String objectName; //物品名称
	@HsMapTo("sfsh")
	private String damage;		// 是否损坏（0否 1是）
	@HsMapTo("sfwx")
	private String maintenance;		// 是否维修（0否 1是）
	private HsQwManagementCheckObject checkObject;
	private HsQwManagementCheck managementCheck;

	public HsQwManagementCheckDetail() {
		this(null);
	}
	
	public HsQwManagementCheckDetail(String id){
		super(id);
	}
	
	@NotBlank(message="资格审查id不能为空")
	@Size(min=0, max=64, message="资格审查id长度不能超过 64 个字符")
	public String getCheckId() {
		return checkId;
	}

	public void setCheckId(String checkId) {
		this.checkId = checkId;
	}
	
	@NotBlank(message="物品id不能为空")
	@Size(min=0, max=64, message="物品id长度不能超过 64 个字符")
	public String getObjectId() {
		return objectId;
	}

	public void setObjectId(String objectId) {
		this.objectId = objectId;
	}
	
	@NotBlank(message="是否损坏不能为空")
	@Size(min=0, max=1, message="是否损坏长度不能超过 1 个字符")
	public String getDamage() {
		return damage;
	}

	public void setDamage(String damage) {
		this.damage = damage;
	}
	
	@NotBlank(message="是否维修不能为空")
	@Size(min=0, max=1, message="是否维修长度不能超过 1 个字符")
	public String getMaintenance() {
		return maintenance;
	}

	public void setMaintenance(String maintenance) {
		this.maintenance = maintenance;
	}

    public HsQwManagementCheckObject getCheckObject() {
        return checkObject;
    }

    public void setCheckObject(HsQwManagementCheckObject checkObject) {
        this.checkObject = checkObject;
    }

    public HsQwManagementCheck getManagementCheck() {
        return managementCheck;
    }

    public void setManagementCheck(HsQwManagementCheck managementCheck) {
        this.managementCheck = managementCheck;
    }

	public String getObjectName() {
		return objectName;
	}

	public void setObjectName(String objectName) {
		this.objectName = objectName;
	}
}