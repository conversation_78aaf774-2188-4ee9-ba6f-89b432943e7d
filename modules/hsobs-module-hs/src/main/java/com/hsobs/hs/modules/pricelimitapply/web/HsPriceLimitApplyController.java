package com.hsobs.hs.modules.pricelimitapply.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.modules.bpm.entity.BpmTask;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.hsobs.hs.modules.pricelimitapply.service.HsPriceLimitApplyService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 限价房-购房申请Controller
 * <AUTHOR>
 * @version 2024-12-10
 */
@Controller
@RequestMapping(value = "${adminPath}/pricelimitapply/hsPriceLimitApply")
public class HsPriceLimitApplyController extends BaseController {

	@Autowired
	private HsPriceLimitApplyService hsPriceLimitApplyService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPriceLimitApply get(String id, boolean isNewRecord) {
		return hsPriceLimitApplyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		return "modules/pricelimitapply/hsPriceLimitApplyList";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = {"listDone", ""})
	public String listDone(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		return "modules/pricelimitapply/hsPriceLimitApplyListDone";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = {"archives", ""})
	public String archives(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		return "modules/pricelimitapply/hsPriceLimitApplyListArchives";
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = {"listPublic", ""})
	public String orderListPublic(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		return "modules/pricelimitapply/hsPriceLimitApplyListPublic";
	}

    /**
     * 查询代办的审批任务
     */
    @RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
    @RequestMapping(value = "listAuditData")
    @ResponseBody
    public Page<HsPriceLimitApply> listAuditData(HsPriceLimitApply hsPriceLimitApply, HttpServletRequest request, HttpServletResponse response) {
        hsPriceLimitApply.setPage(new Page<>(request, response));
        Page<HsPriceLimitApply> page = hsPriceLimitApplyService.findAuditPageByTasks(hsPriceLimitApply, BpmTask.STATUS_UNFINISHED);
        return page;
    }

	/**
	 * 查询代办的审批任务
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "listAuditDoneData")
	@ResponseBody
	public Page<HsPriceLimitApply> listAuditDoneData(HsPriceLimitApply hsPriceLimitApply, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApply.setPage(new Page<>(request, response));
		Page<HsPriceLimitApply> page = hsPriceLimitApplyService.findAuditPageByTasks(hsPriceLimitApply, BpmTask.STATUS_FINISHED);
		return page;
	}

	/**
	 * 查询代办的审批任务
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "listArchivesData")
	@ResponseBody
	public Page<HsPriceLimitApply> listArchivesData(HsPriceLimitApply hsPriceLimitApply, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApply.setPage(new Page<>(request, response));
		Page<HsPriceLimitApply> page = hsPriceLimitApplyService.findArchivesPage(hsPriceLimitApply);
		return page;
	}

	/**
	 * 查询网上公示
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "listPublicData")
	@ResponseBody
	public Page<HsPriceLimitApply> listPublicData(HsPriceLimitApply hsPriceLimitApply, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApply.setPage(new Page<>(request, response));
		Page<HsPriceLimitApply> page = hsPriceLimitApplyService.findPublicPage(hsPriceLimitApply);
		return page;
	}
	
	/**
	 * 查询子表数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "hsPriceLimitApplyerListData")
	@ResponseBody
	public Page<HsPriceLimitApplyer> subListData(HsPriceLimitApplyer hsPriceLimitApplyer, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApplyer.setPage(new Page<>(request, response));
		Page<HsPriceLimitApplyer> page = hsPriceLimitApplyService.findSubPage(hsPriceLimitApplyer);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "form")
	public String form(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/pricelimitapply/hsPriceLimitApplyForm";
	}

	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "formPublic")
	public String showpublic(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/pricelimitapply/hsPriceLimitApplyFormPublic";
	}

	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "archives/form")
	public String form_archives(HsPriceLimitApply hsPriceLimitApply, Model model) {
		model.addAttribute("hsPriceLimitApply", hsPriceLimitApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/pricelimitapply/hsPriceLimitApplyFormArchives";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsPriceLimitApply hsPriceLimitApply) {
		hsPriceLimitApplyService.save(hsPriceLimitApply);
		hsPriceLimitApply = hsPriceLimitApplyService.get(hsPriceLimitApply.getId());
		hsPriceLimitApplyService.uploadTaskRealTimeSaveFinished(hsPriceLimitApply);
		hsPriceLimitApplyService.uploadTaskRealTimeSave(hsPriceLimitApply);
		if (hsPriceLimitApply.getIsNewRecord()) {
			return renderResult(Global.TRUE, text("新增限价房购房申请成功！"));
		}
		return renderResult(Global.TRUE, text("审核成功！"));
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:edit")
	@PostMapping(value = "flushTaskStatus")
	@ResponseBody
	public String flushTaskStatus(@Validated HsPriceLimitApply hsPriceLimitApply) {
		hsPriceLimitApplyService.flushTaskStatus(hsPriceLimitApply);
		return renderResult(Global.TRUE, text("刷新限价房申请状态成功！"));
	}

	/**
	 * 资格确认，生成资格确认单
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:edit")
	@RequestMapping(value = "createExamine")
	public void createExamine(HsPriceLimitApply hsPriceLimitApply, HttpServletResponse response) {
		hsPriceLimitApplyService.createExamine(hsPriceLimitApply, response);
	}


	/**
	 * 资格确认，生成资格确认单
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:edit")
	@RequestMapping(value = "getSM4")
	@ResponseBody
	public String getSM4(HsPriceLimitApply hsPriceLimitApply, HttpServletResponse response) {
		hsPriceLimitApplyService.getSM4(hsPriceLimitApply, response);
		return renderResult(Global.FALSE, text("获取信息成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "exportAuditData")
	public void exportAuditData(HsPriceLimitApply hsPriceLimitApply, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApply.setPage(new Page<>(request, response));
		Page<HsPriceLimitApply> page = hsPriceLimitApplyService.findAuditPageByTasks(hsPriceLimitApply, BpmTask.STATUS_UNFINISHED);
		String fileName = "限价房申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("限价房申请信息表", HsPriceLimitApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "exportAuditDoneData")
	public void exportAuditDoneData(HsPriceLimitApply hsPriceLimitApply, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApply.setPage(new Page<>(request, response));
		Page<HsPriceLimitApply> page = hsPriceLimitApplyService.findAuditPageByTasks(hsPriceLimitApply, BpmTask.STATUS_FINISHED);
		String fileName = "限价房申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("限价房申请信息表", HsPriceLimitApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "exportArchivesData")
	public void exportArchivesData(HsPriceLimitApply hsPriceLimitApply, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitApply.setPage(new Page<>(request, response));
		Page<HsPriceLimitApply> page = hsPriceLimitApplyService.findArchivesPage(hsPriceLimitApply);
		String fileName = "限价房申请信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("限价房申请信息表", HsPriceLimitApply.class)){
			ee.setDataList(page.getList()).write(response, fileName);
		}
	}

	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		hsPriceLimitApplyService.importTemplate(response);
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = hsPriceLimitApplyService.importData(file);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}

	/**
	 * 停用数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsPriceLimitApply hsPriceLimitApply) {
		hsPriceLimitApply.setStatus(HsPriceLimitApply.STATUS_DISABLE);
		hsPriceLimitApplyService.updateStatus(hsPriceLimitApply);
		return renderResult(Global.TRUE, text("停用限价房购房申请成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsPriceLimitApply hsPriceLimitApply) {
		hsPriceLimitApply.setStatus(HsPriceLimitApply.STATUS_NORMAL);
		hsPriceLimitApplyService.updateStatus(hsPriceLimitApply);
		return renderResult(Global.TRUE, text("启用限价房购房申请成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("pricelimitapply:hsPriceLimitApply:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsPriceLimitApply hsPriceLimitApply) {
		if (!HsPriceLimitApply.STATUS_DRAFT.equals(hsPriceLimitApply.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		hsPriceLimitApply.setBuyStatus(HsPublicApply.BUYSTATUS_FORGO);
		hsPriceLimitApplyService.update(hsPriceLimitApply);
		if (StringUtils.isNotBlank(hsPriceLimitApply.getHouseId())) {
			hsPriceLimitApplyService.updateHouseStatus(hsPriceLimitApply.getHouseId(), "2");//恢复待售
		}
		hsPriceLimitApplyService.delete(hsPriceLimitApply);
		return renderResult(Global.TRUE, text("删除限价房购房申请成功！"));
	}
}