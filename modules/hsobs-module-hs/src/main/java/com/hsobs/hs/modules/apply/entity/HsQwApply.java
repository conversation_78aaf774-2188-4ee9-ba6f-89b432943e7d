package com.hsobs.hs.modules.apply.entity;

import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyscore.entity.HsQwApplyScoreDetail;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.external.entity.HsMapTo;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.UserRole;
import org.flowable.engine.delegate.DelegateExecution;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 租赁资格轮候申请Entity
 *
 * <AUTHOR>
 * @version 2024-11-21
 */
@Table(name = "hs_qw_apply", alias = "a", label = "租赁资格轮候申请信息", columns = {
        @Column(name = "id", attrName = "id", label = "编号", isPK = true),
        @Column(name = "family_peoples", attrName = "familyPeoples", label = "人口数量"),
        @Column(name = "family_income", attrName = "familyIncome", label = "家庭年收入"),
        @Column(name = "apply_matter", attrName = "applyMatter", label = "申请事项", comment = "申请事项（0公租申请 1承租变更 2承租置换 3绿色通道）"),
        @Column(name = "house_id", attrName = "houseId", label = "配租房源id"),
        @Column(name = "office_code", attrName = "officeCode", label = "单位编码"),
        @Column(name = "avg_area", attrName = "avgArea", label = "人均住房面积"),
        @Column(name = "avg_income", attrName = "avgIncome", label = "人均家庭收入"),
        @Column(name = "apply_time", attrName = "applyTime", label = "资格轮候如表时间"),
        @Column(name = "apply_score", attrName = "applyScore", label = "资格轮候评分"),
        @Column(name = "apply_score_pre", attrName = "applyScorePre", label = "资格轮候得分-复查前"),
        @Column(name = "applyed_id", attrName = "applyedId", label = "已配租的申请流程id，用于调租管理"),
        @Column(name = "change_reason", attrName = "changeReason", label = "申请变更理由"),
        @Column(name = "eligible", attrName = "eligible", label = "是否符合要求"),
        @Column(name = "apply_accepted", attrName = "applyAccepted", label = "是否承租（0是 1否）"),
        @Column(name = "recheck_status", attrName = "recheckStatus", label = "复查状态（0：参加并前往填写复查信息 1：放弃本次配租（本次不参加） 2：放弃本次配租（已不符合条件））"),
        @Column(name = "recheck_audit", attrName = "recheckAudit", label = "复查审核（0：通过 1材料问题 2资格不满足）"),
        @Column(name = "offline_rent", attrName = "offlineRent", label = "线下配租（0：参与 1不参与）"),
        @Column(name = "rent_time", attrName = "rentTime", label = "租赁日期"),
        @Column(name = "priority_order", attrName = "priorityOrder", label = "优先对象，权重值"),
        @Column(name = "score_update", attrName = "scoreUpdate", label = "是否更新资格轮候得分"),
        @Column(includeEntity = DataEntity.class),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "o", on = "o.apply_id = a.id and o.apply_role = 0 and o.status=0", attrName = "mainApplyer", columns = {
                @Column(includeEntity = HsQwApplyer.class) }),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwCompact.class, alias = "m", on = "m.apply_id = a.id and m.status in (0,4)", attrName = "compact", columns = {
                @Column(includeEntity = HsQwCompact.class) }),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalHouse.class, alias = "h", on = "h.id = a.house_id", attrName = "hsQwApplyHouse", columns = {
                @Column(includeEntity = HsQwPublicRentalHouse.class) }),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "p", on = "p.id = h.estate_id", attrName = "hsQwApplyHouse.estate", columns = {
                @Column(includeEntity = HsQwPublicRentalEstate.class) }),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "off", on = "a.office_code = off.office_code", attrName = "office", columns = {
                @Column(includeEntity = Office.class) })
}, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere", orderBy = "a.update_date DESC")
public class HsQwApply extends BpmEntity<HsQwApply> {

    public static final String APPLY_STATUS_DEFAULT = "-1"; // 默认、删除的申请单
    public static final String APPLY_STATUS_DRAFT = "用户申请";// 0:个人申请草稿
    public static final String APPLY_STATUS_AUDIT_UNIT_FIRST = "单位初审";// 1:待单位初审
    public static final String APPLY_STATUS_AUDIT_ORGHAND_FIRST = "经办初审";// 2:待机关经办初审
    public static final String APPLY_STATUS_AUDIT_ORGOFFICE_FIRST = "处室领导初审";// 3:待机关处室初审
    public static final String APPLY_STATUS_AUDIT_ORG_FIRST_COMPLETE = "选择资格轮候公示名单";// 4:初审完成
    public static final String APPLY_STATUS_FIRST_PUBLIC_WAIT = "资格轮候网上公示";// 5:待初审公示
    public static final String APPLY_STATUS_FIRST_PUBLIC_COMPLETE = "发起配租复查";// 6:初审公示完成
    public static final String APPLY_STATUS_AUDIT_USER_CHECK = "配租复查确认";// 7:待配租复查确认
    public static final String APPLY_STATUS_STOP_RENT = "不轮候申请";// 7.1:不轮候申请
    public static final String APPLY_STATUS_STOP_RENT_AUDIT = "处室领导审核";// 7.1:不轮候申请
    public static final String APPLY_STATUS_AUDIT_UNIT_CHECK = "单位配租复查审批";// 8:待配租复查单位审批
    public static final String APPLY_STATUS_AUDIT_ORGHAND_CHECK = "经办配租复查审批";// 9:待配租复查机关经办审批
    public static final String APPLY_STATUS_AUDIT_CHECK_COMPLETE = "选择配租名单";// 10:配租复查完成
    public static final String APPLY_STATUS_AUDIT_ORGOFFICE_CHECK = "处室领导配租名单审批";// 11:待配租名单处室领导审批
    public static final String APPLY_STATUS_AUDIT_ORGBUREAU_CHECK = "局级领导配租名单审批";// 12:待配租名单局级领导审批
    public static final String APPLY_STATUS_CHECK_PUBLIC = "配租名单网上公示";// 13:待配租名单公示
    public static final String APPLY_STATUS_CHECK_PUBLIC_COMPLETE = "草拟配租方案";// 14:配租公示完成
    public static final String APPLY_STATUS_AUDIT_ORGOFFICE_OPTION = "处室领导配租方案审核";// 15:待配租方案处室领导审批
    public static final String APPLY_STATUS_AUDIT_ORGBUREAU_OPTION = "局级领导配租方案审核";// 16:待配租方案局级领导审批
    public static final String APPLY_STATUS_OFFLINE_RENT_CONFIRM = "线下配租确认";// 16.1:线下配租确认
    public static final String APPLY_STATUS_RENT_CONFIRM = "配租复查确认";// 17:配租方案完成
    public static final String APPLY_STATUS_OPTION_COMPLETE = "房源配租";// 17:配租方案完成
    public static final String APPLY_STATUS_AUDIT_HOUSE_RENT = "房管机构确认";// 18:待房管机构配租确认
    public static final String APPLY_STATUS_AUDIT_USER_RENT = "申请人确认";// 19:待申请人配租确认
    public static final String APPLY_STATUS_AUDIT_COMPACT = "签订租赁合同";// 20:待签合同
    public static final String APPLY_STATUS_AUDIT_COMPACT_CHECK = "确认合同信息";// 21:待确认合同

    public static final String APPLY_MATTER_RENTAL = "0";// 申请单类型：公租房申请
    public static final String APPLY_MATTER_INFO = "1";// 申请单类型：个人信息变更申请
    public static final String APPLY_MATTER_REPLACEUP = "2";// 申请单类型：换房（以小换大）申请
    public static final String APPLY_MATTER_REPLACE = "4";// 申请单类型：换房申请
    public static final String APPLY_MATTER_GREEN = "3";// 申请单类型：绿色租赁申请
    public static final String APPLY_MATTER_BUREAU = "5";// 申请单类型：局直公房申请

    private static final long serialVersionUID = 1L;

    @HsMapTo("jtzrs")
    private String familyPeoples; // 人口数量
    @HsMapTo("jtzsr")
    private String familyIncome; // 家庭年收入
    @HsMapTo("sqlx")
    private String applyMatter; // 申请事项（0公租申请 1承租变更 2承租置换 3绿色通道）
    private String houseId; // 配租房源id
    private String pids;
    @HsMapTo("sqzfdwbm")
    private String officeCode; // 状态
    private String avgArea;
    private String avgIncome;
    private Date applyTime;
    private Double applyScore;
    private Double applyScoreLte;
    private Double applyScoreGte;
    private Integer priorityOrder;
    private HsQwApplyer mainApplyer;
    private String applyAccepted;
    private Office office;
    private HsQwApplyer hsQwApplyer;
    private String houseRead;
    private String compactRead;
    private String isRead;
    private HsQwCompact compact;
    private String applyedId;
    private HsQwApply applyedInfo;
    @HsMapTo("bgly")
    private String changeReason;
    private String eligible;// 是否仍然符合租赁资格
    private String offlineRent;
    private String recheckAudit;
    private String recordId;
    private String recheckStatus;
    private String dataType;// 关联数据类型 0-个人信息修改 1-个人承租信息修改 2-个人租房变更 3-房屋清退 4-入户核验 5-退租核验
    private HsQwPublicRentalHouse hsQwApplyHouse;
    private Date rentTime;
    private String scoreUpdate;
    private String compactSign;
    private boolean isQuery;
    private Double applyScorePre;

    private String proxyUserId;// 代理申请用户编码

    private boolean isClear;// 是否清退流程
    @HsMapTo("gtsqList")
    private List<HsQwApplyer> hsQwApplyerList = ListUtils.newArrayList(); // 子表列表
    @HsMapTo("jtzfqkList")
    private List<HsQwApplyHouse> hsQwApplyHouseList = ListUtils.newArrayList(); // 子表列表

    private List<HsQwApplyScoreDetail> scoreDetails = ListUtils.newArrayList(); // 子表列表

    public HsQwApply() {
        this(null);
    }

    public HsQwApply(String id) {
        super(id);
    }

    @NotBlank(message = "人口数量不能为空")
    @Size(min = 0, max = 20, message = "人口数量长度不能超过 20 个字符")
    public String getFamilyPeoples() {
        return familyPeoples;
    }

    public void setFamilyPeoples(String familyPeoples) {
        this.familyPeoples = familyPeoples;
    }

    @NotBlank(message = "家庭年收入不能为空")
    @Size(min = 0, max = 20, message = "家庭年收入长度不能超过 20 个字符")
    public String getFamilyIncome() {
        return familyIncome;
    }

    public void setFamilyIncome(String familyIncome) {
        this.familyIncome = familyIncome;
    }

    @NotBlank(message = "申请事项不能为空")
    @Size(min = 0, max = 255, message = "申请事项长度不能超过 255 个字符")
    public String getApplyMatter() {
        return applyMatter;
    }

    public void setApplyMatter(String applyMatter) {
        this.applyMatter = applyMatter;
        if (StringUtils.isEmpty(this.getFormKey())) {
            this.setFormKey(this.getFormKeyByApplyMatter(this.applyMatter));
        }
    }

    private String getFormKeyByApplyMatter(String applyMatter) {
        if(applyMatter == null){
            return null;
        }
        if (applyMatter.equals("0") || applyMatter.equals("2")) {
            return "rent_apply";
        } else if (applyMatter.equals("1")) {
            return "rent_apply_info";
        } else if (applyMatter.equals("4")) {
            return "rent_apply_house";
        } else if (applyMatter.equals("5")) {
            return "rent_apply_bureau";
        }
        return null;
    }

    @Size(min = 0, max = 64, message = "配租房源id长度不能超过 64 个字符")
    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        if (!StringUtils.isBlank(houseId)) {
            houseId = houseId.replaceAll("^,+|,+$", "");
            ;
        }
        this.houseId = houseId;
    }

    @Valid
    public List<HsQwApplyer> getHsQwApplyerList() {
        return hsQwApplyerList;
    }

    public void setHsQwApplyerList(List<HsQwApplyer> hsQwApplyerList) {
        this.hsQwApplyerList = hsQwApplyerList;
    }

    @Valid
    public List<HsQwApplyHouse> getHsQwApplyHouseList() {
        return hsQwApplyHouseList;
    }

    public void setHsQwApplyHouseList(List<HsQwApplyHouse> hsQwApplyHouseList) {
        this.hsQwApplyHouseList = hsQwApplyHouseList;
    }

    @NotBlank(message = "单位编码不能为空")
    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    @Override
    public String getStatus() {
        return status;
    }

    @Override
    public void setStatus(String status) {
        this.status = status;
    }

    public HsQwApplyer getHsQwApplyer() {
        return hsQwApplyer;
    }

    public void setHsQwApplyer(HsQwApplyer hsQwApplyer) {
        this.hsQwApplyer = hsQwApplyer;
    }

    public HsQwPublicRentalHouse getHsQwApplyHouse() {
        return hsQwApplyHouse;
    }

    public void setHsQwApplyHouse(HsQwPublicRentalHouse hsQwApplyHouse) {
        this.hsQwApplyHouse = hsQwApplyHouse;
    }

    public Office getOffice() {
        return office;
    }

    public void setOffice(Office office) {
        this.office = office;
    }

    public HsQwApplyer getMainApplyer() {
        return mainApplyer;
    }

    public void setMainApplyer(HsQwApplyer mainApplyer) {
        this.mainApplyer = mainApplyer;
    }

    public String getApplyTitle() {
        return this.mainApplyer != null
                ? this.mainApplyer.getName() + " 于" + DateUtils.formatDateTime(this.getCreateDate()) + " 发起了" +
                        this.getApplyMatterInfo()
                : "";
    }

    private String getApplyMatterInfo() {
        if (this.getApplyMatter() == null) {
            return null;
        }
        if (this.getApplyMatter().equals("0")) {
            return "公租房申请";
        } else if (this.getApplyMatter().equals("1")) {
            return "承租变更";
        } else if (this.getApplyMatter().equals("2")) {
            return "承租置换";
        } else if (this.getApplyMatter().equals("3")) {
            return "绿色通道";
        }
        return "";
    }

    public String getAvgArea() {
        return avgArea;
    }

    public void setAvgArea(String avgArea) {
        this.avgArea = avgArea;
    }

    public String getAvgIncome() {
        return avgIncome;
    }

    public void setAvgIncome(String avgIncome) {
        this.avgIncome = avgIncome;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Double getApplyScore() {
        return applyScore;
    }

    public void setApplyScore(Double applyScore) {
        this.applyScore = applyScore;
    }

    public static void test(DelegateExecution execution) {
        System.out.println(execution.getEventName());
    }

    public String getApplyAccepted() {
        return applyAccepted;
    }

    public void setApplyAccepted(String applyAccepted) {
        this.applyAccepted = applyAccepted;
    }

    public String getHouseRead() {
        return houseRead;
    }

    public void setHouseRead(String houseRead) {
        this.houseRead = houseRead;
    }

    public String getCompactRead() {
        return compactRead;
    }

    public void setCompactRead(String compactRead) {
        this.compactRead = compactRead;
    }

    public HsQwCompact getCompact() {
        return compact;
    }

    public void setCompact(HsQwCompact compact) {
        this.compact = compact;
        this.compactSign = "1";
    }

    public String getApplyedId() {
        return applyedId;
    }

    public HsQwApply getApplyedInfo() {
        return applyedInfo;
    }

    public void setApplyedInfo(HsQwApply applyedInfo) {
        this.applyedInfo = applyedInfo;
    }

    public void setApplyedId(String applyedId) {
        this.applyedId = applyedId;
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason;
    }

    public String getEligible() {
        return eligible;
    }

    public void setEligible(String eligible) {
        this.eligible = eligible;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getRecheckStatus() {
        return recheckStatus;
    }

    public void setRecheckStatus(String recheckStatus) {
        this.recheckStatus = recheckStatus;
    }

    public String getRecheckAudit() {
        return recheckAudit;
    }

    public void setRecheckAudit(String recheckAudit) {
        this.recheckAudit = recheckAudit;
    }

    public String getOfflineRent() {
        return offlineRent;
    }

    public void setOfflineRent(String offlineRent) {
        this.offlineRent = offlineRent;
    }

    public String getIsRead() {
        return isRead;
    }

    public void setIsRead(String isRead) {
        this.isRead = isRead;
    }

    public Date getRentTime() {
        return rentTime;
    }

    public void setRentTime(Date rentTime) {
        this.rentTime = rentTime;
    }

    public boolean isClear() {
        return isClear;
    }

    public void setClear(boolean clear) {
        isClear = clear;
    }

    public String getProxyUserId() {
        return proxyUserId;
    }

    public void setProxyUserId(String proxyUserId) {
        this.proxyUserId = proxyUserId;
    }

    public Integer getPriorityOrder() {
        return priorityOrder;
    }

    public void setPriorityOrder(Integer priorityOrder) {
        this.priorityOrder = priorityOrder;
    }

    public Double getApplyScoreLte() {
        return applyScoreLte;
    }

    public void setApplyScoreLte(Double applyScoreLte) {
        this.applyScoreLte = applyScoreLte;
    }

    public Double getApplyScoreGte() {
        return applyScoreGte;
    }

    public void setApplyScoreGte(Double applyScoreGte) {
        this.applyScoreGte = applyScoreGte;
    }

    public List<HsQwApplyScoreDetail> getScoreDetails() {
        return scoreDetails;
    }

    public void setScoreDetails(List<HsQwApplyScoreDetail> scoreDetails) {
        this.scoreDetails = scoreDetails;
    }

    public Date getRentTime_gte() {
        return sqlMap.getWhere().getValue("rent_time", QueryType.GTE);
    }

    public void setRentTime_gte(Date rentTime) {
        sqlMap.getWhere().and("rent_time", QueryType.GTE, rentTime);
    }

    public Date getRentTime_lte() {
        return sqlMap.getWhere().getValue("rent_time", QueryType.LTE);
    }

    public void setRentTime_lte(Date rentTime) {
        sqlMap.getWhere().and("rent_time", QueryType.LTE, rentTime);
    }

    public boolean isQuery() {
        return isQuery;
    }

    public void setQuery(boolean query) {
        isQuery = query;
    }

    public String getCompactSign() {
        return this.compactSign;
    }

    public void setCompactSign(String compactSign) {
        this.compactSign = compactSign;
    }

    public Double getApplyScorePre() {
        if (this.applyScorePre == null){
            return applyScore;
        }
        return applyScorePre;
    }

    public void setApplyScorePre(Double applyScorePre) {
        this.applyScorePre = applyScorePre;
    }

    public String getScoreUpdate() {
        return scoreUpdate;
    }

    public void setScoreUpdate(String scoreUpdate) {
        this.scoreUpdate = scoreUpdate;
    }

    public String getPids() {
        return pids;
    }

    public void setPids(String pids) {
        this.pids = pids;
    }

    /**
     * 获取整体房产信息
     * @return
     */
    public String getTotalHouseInfo(){
        if (this.hsQwApplyHouseList == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (HsQwApplyHouse qwApplyHouse : this.hsQwApplyHouseList) {
            sb.append(qwApplyHouse.getAddress()).append(";");
        }
        return sb.toString();
    }
}