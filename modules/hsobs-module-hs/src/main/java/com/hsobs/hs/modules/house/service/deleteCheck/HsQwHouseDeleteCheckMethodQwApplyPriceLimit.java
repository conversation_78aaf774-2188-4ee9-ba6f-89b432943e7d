package com.hsobs.hs.modules.house.service.deleteCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.pricelimitapply.dao.HsPriceLimitApplyDao;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 公租申请单核验，查询所有房源的申请单
 */
@Service
public class HsQwHouseDeleteCheckMethodQwApplyPriceLimit implements HsQwHouseDeleteCheckMethod {
    @Autowired
    private HsPriceLimitApplyDao hsPriceLimitApplyDao;

    @Override
    public void execute(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        HsPriceLimitApply query = new HsPriceLimitApply();
        query.setHouseId(hsQwPublicRentalHouse.getId());
        query.setStatus_in(new String[]{HsQwApply.STATUS_NORMAL, HsQwApply.STATUS_AUDIT, "88"});
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        long count = hsPriceLimitApplyDao.findCount(query);
        if (count>0){
            throw new ServiceException("该房源存在关联的限价房申请单，不能取消该房源！");
        }
    }
}
