package com.hsobs.hs.modules.hsalarm.entity;

import javax.validation.constraints.Size;

import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;

import java.util.Date;

/**
 * 告警信息Entity
 * <AUTHOR>
 * @version 2025-01-03
 */
@Table(name="hs_alarm", alias="a", label="告警信息信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="user_id", attrName="userId", label="处理人id"),
		@Column(name="office_code", attrName="officeCode", label="所属单位"),
		@Column(name="alarm_type", attrName="alarmType", label="告警类型"),
		@Column(name="alarm_status", attrName="alarmStatus", label="告警状态"),
		@Column(name="alarm_info", attrName="alarmInfo", label="告警信息"),
		@Column(name="jump_to_url", attrName="jumpToUrl", label="跳转url"),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新者", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false),
		@Column(name="create_by", attrName="createBy", label="创建者", isUpdate=false, isQuery=false),
		@Column(name="public_status", attrName="publicStatus", label="发布状态"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Employee.class, alias = "o",
				on = "o.emp_code = a.user_id", attrName="employee",
				columns = {@Column(includeEntity = Employee.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "oi",
				on="oi.office_code = a.office_code", attrName="office",
				columns = {@Column(includeEntity = Office.class)})
	},
		extWhereKeys="dsf",
		orderBy="a.update_date DESC"
)
public class HsAlarm extends DataEntity<HsAlarm> {

	@ExcelFields({
			@ExcelField(title = "处理人员", attrName = "employee.empName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "所属单位", attrName = "office.officeName", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "预警类型", attrName = "alarmType", align = ExcelField.Align.CENTER, sort = 40, dictType="hs_alarm_type"),
			@ExcelField(title = "预警时间", attrName = "createDate", align = ExcelField.Align.CENTER, sort = 50),
			@ExcelField(title = "预警信息", attrName = "alarmInfo", align = ExcelField.Align.CENTER, sort = 60),
			@ExcelField(title = "预警状态", attrName = "alarmStatus", align = ExcelField.Align.CENTER, sort = 70, dictType="early_warn_state"),
	})
	private static final long serialVersionUID = 1L;
	private String userId;		// 处理人id
	private String officeCode;		// 所属单位
	private String alarmType;		// 告警类型
	private String alarmStatus;		// 告警状态
	private String alarmInfo;		// 告警信息
	private String jumpToUrl;		// 跳转url
	private String publicStatus;

	private Employee employee; // 人员信息
	private Office office; // 机构信息
	private String parentCodeTree;

	private Date startDate;
	private Date endDate;

	public HsAlarm() {
		this(null);

		parentCodeTree = "";
		if(!UserUtils.getUser().isAdmin()){
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
	}
	
	public HsAlarm(String id){
		super(id);
	}

	public String getParentCodeTree() {
		return parentCodeTree;
	}

	public void setParentCodeTree(String parentCodeTree) {
		this.parentCodeTree = parentCodeTree;
	}
	
	@Size(min=0, max=64, message="处理人id长度不能超过 64 个字符")
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	@Size(min=0, max=64, message="所属单位长度不能超过 64 个字符")
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
	
	@Size(min=0, max=1, message="告警类型长度不能超过 1 个字符")
	public String getAlarmType() {
		return alarmType;
	}

	public void setAlarmType(String alarmType) {
		this.alarmType = alarmType;
	}
	
	@Size(min=0, max=1, message="告警状态长度不能超过 1 个字符")
	public String getAlarmStatus() {
		return alarmStatus;
	}

	public void setAlarmStatus(String alarmStatus) {
		this.alarmStatus = alarmStatus;
	}
	
	@Size(min=0, max=900, message="告警信息长度不能超过 900 个字符")
	public String getAlarmInfo() {
		return alarmInfo;
	}

	public void setAlarmInfo(String alarmInfo) {
		this.alarmInfo = alarmInfo;
	}

	public String getJumpToUrl() {
		return jumpToUrl;
	}

	public void setJumpToUrl(String jumpToUrl) {
		this.jumpToUrl = jumpToUrl;
	}

	public String getPublicStatus() {
		return publicStatus;
	}

	public void setPublicStatus(String publicStatus) {
		this.publicStatus = publicStatus;
	}

	public Employee getEmployee() {
		return employee;
	}

	public void setEmployee(Employee employee) {
		this.employee = employee;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
}