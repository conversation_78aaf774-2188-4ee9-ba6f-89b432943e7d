package com.hsobs.hs.modules.bureau.web;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.modules.msg.entity.content.PcMsgContent;
import com.jeesite.modules.msg.utils.MsgPushUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import com.hsobs.hs.modules.bureau.service.HsQwApplyBureauService;

/**
 * 局直管公房申请表Controller
 * <AUTHOR>
 * @version 2025-02-19
 */
@Controller
@RequestMapping(value = "${adminPath}/bureau/hsQwApplyBureau")
public class HsQwApplyBureauController extends BaseController {

	@Autowired
	private HsQwApplyBureauService hsQwApplyBureauService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyBureau get(String id, boolean isNewRecord) {
		return hsQwApplyBureauService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyBureau hsQwApplyBureau, Model model) {
		model.addAttribute("hsQwApplyBureau", hsQwApplyBureau);
		return "modules/bureau/hsQwApplyBureauList";
	}



	/**
	 * 查询列表-局直公房历史承租人信息
	 */
	@RequiresPermissions("applyer:hsQwApplyer:view")
	@RequestMapping(value = {"listBureau", ""})
	public String listBureau(HsQwApplyBureau hsQwApplyBureau, Model model) {
		model.addAttribute("hsQwApplyBureau", hsQwApplyBureau);
		return "modules/bureau/hsQwBureauHisApplyerList";
	}


	/**
	 * 查询列表-待办
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = {"listAudit", ""})
	public String listAudit(HsQwApplyBureau hsQwApplyBureau, Model model) {
		model.addAttribute("hsQwApplyBureau", hsQwApplyBureau);
		return "modules/bureau/hsQwApplyBureauListAudit";
	}


	/**
	 * 查询列表-已办
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = {"listAuditDone", ""})
	public String listAuditDone(HsQwApplyBureau hsQwApplyBureau, Model model) {
		model.addAttribute("hsQwApplyBureau", hsQwApplyBureau);
		return "modules/bureau/hsQwApplyBureauListAuditDone";
	}

	/**
	 * 查询列表数据-历史承租人列表
	 */
	@RequiresPermissions("applyer:hsQwApplyer:view")
	@RequestMapping(value = "listBureauData")
	@ResponseBody
	public Page<HsQwApplyBureau> listBureauData(HsQwApplyBureau hsQwApplyBureau, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyBureau.setPage(new Page<>(request, response));
		Page<HsQwApplyBureau> page = hsQwApplyBureauService.findBureauPage(hsQwApplyBureau);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyBureau> listData(HsQwApplyBureau hsQwApplyBureau, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyBureau.setPage(new Page<>(request, response));
		Page<HsQwApplyBureau> page = hsQwApplyBureauService.findPage(hsQwApplyBureau);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = "listAuditData")
	@ResponseBody
	public Page<HsQwApplyBureau> listAuditData(HsQwApplyBureau hsQwApplyBureau, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyBureau.setPage(new Page<>(request, response));
		Page<HsQwApplyBureau> page = hsQwApplyBureauService.findPageByTask(hsQwApplyBureau, null, "1");
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = "listAuditDoneData")
	@ResponseBody
	public Page<HsQwApplyBureau> listAuditDoneData(HsQwApplyBureau hsQwApplyBureau, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyBureau.setPage(new Page<>(request, response));
		Page<HsQwApplyBureau> page = hsQwApplyBureauService.findPageByTask(hsQwApplyBureau, null, "2");
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyBureau hsQwApplyBureau, Model model) {
		model.addAttribute("hsQwApplyBureau", hsQwApplyBureau);
		return "modules/bureau/hsQwApplyBureauForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyBureau hsQwApplyBureau) {
		hsQwApplyBureauService.save(hsQwApplyBureau);
		return renderResult(Global.TRUE, text("保存局直管公房申请表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyBureau hsQwApplyBureau) {
		if (!HsQwApplyBureau.STATUS_DRAFT.equals(hsQwApplyBureau.getStatus())){
			return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
		}
		hsQwApplyBureauService.delete(hsQwApplyBureau);
		return renderResult(Global.TRUE, text("删除局直管公房申请表成功！"));
	}


	/**
	 * 删除数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:edit")
	@RequestMapping(value = "clear")
	@ResponseBody
	public String clear(HsQwApplyBureau hsQwApplyBureau) {
		hsQwApplyBureauService.clear(hsQwApplyBureau);
		return renderResult(Global.TRUE, text("删除局直管公房申请表成功！"));
	}
	/**
	 * 导出数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwApplyBureau hsQwApplyBureau, HttpServletResponse response) {
		List<HsQwApplyBureau> list = hsQwApplyBureauService.findList(hsQwApplyBureau);
		String fileName = "局直管公房申请表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("局直管公房申请表", HsQwApplyBureau.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}


	/**
	 * 导出数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:view")
	@RequestMapping(value = "test")
	public void test(HsQwApplyBureau hsQwApplyBureau, HttpServletResponse response) {
		PcMsgContent msgContent = new PcMsgContent();
		msgContent.setTitle("提示信息");
		msgContent.setContent("您有1条新的任务");
		msgContent.addButton("办理", "/a/task/execute?id=123");
		// 即时推送消息
		MsgPushUtils.push(msgContent, "BizKey", "BizType", "system");

	}
}