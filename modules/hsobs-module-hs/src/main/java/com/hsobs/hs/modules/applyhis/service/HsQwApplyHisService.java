package com.hsobs.hs.modules.applyhis.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.applyhis.entity.HsQwApplyHis;
import com.hsobs.hs.modules.applyhis.dao.HsQwApplyHisDao;

/**
 * hs_qw_apply_hisService
 * <AUTHOR>
 * @version 2025-07-12
 */
@Service
public class HsQwApplyHisService extends CrudService<HsQwApplyHisDao, HsQwApplyHis> {
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyHis
	 * @return
	 */
	@Override
	public HsQwApplyHis get(HsQwApplyHis hsQwApplyHis) {
		return super.get(hsQwApplyHis);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyHis 查询条件
	 * @param hsQwApplyHis page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyHis> findPage(HsQwApplyHis hsQwApplyHis) {
		return super.findPage(hsQwApplyHis);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyHis
	 * @return
	 */
	@Override
	public List<HsQwApplyHis> findList(HsQwApplyHis hsQwApplyHis) {
		return super.findList(hsQwApplyHis);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyHis
	 */
	@Override
	@Transactional
	public void save(HsQwApplyHis hsQwApplyHis) {
		super.save(hsQwApplyHis);
	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyHis
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyHis hsQwApplyHis) {
		super.updateStatus(hsQwApplyHis);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyHis
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyHis hsQwApplyHis) {
		super.delete(hsQwApplyHis);
	}
	
}