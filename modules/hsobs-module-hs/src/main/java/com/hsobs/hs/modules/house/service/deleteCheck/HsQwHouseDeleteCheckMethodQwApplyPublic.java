package com.hsobs.hs.modules.house.service.deleteCheck;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.publicapply.dao.HsPublicApplyDao;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicsaleapply.dao.HsPublicSaleApplyDao;
import com.hsobs.hs.modules.publicsaleapply.entity.HsPublicSaleApply;
import com.hsobs.hs.modules.publicsaleestate.dao.HsPublicSaleEstateDao;
import com.hsobs.hs.modules.publicsaleestate.entity.HsPublicSaleEstate;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 公租申请单核验，查询所有房源的申请单
 */
@Service
public class HsQwHouseDeleteCheckMethodQwApplyPublic implements HsQwHouseDeleteCheckMethod {
    @Autowired
    private HsPublicSaleEstateDao hsPublicSaleEstateDao;

    @Override
    public void execute(HsQwPublicRentalHouse hsQwPublicRentalHouse) {

        long count = hsPublicSaleEstateDao.countApplyHouseCount(hsQwPublicRentalHouse.getId());
        if (count>0){
            throw new ServiceException("该房源存在关联的公有住房配售申请，不能删除该房源！");
        }
    }
}
