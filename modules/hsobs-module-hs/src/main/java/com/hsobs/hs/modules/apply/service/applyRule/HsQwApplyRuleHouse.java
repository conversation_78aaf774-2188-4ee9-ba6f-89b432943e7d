package com.hsobs.hs.modules.apply.service.applyRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 申请人及其家庭成员在福州市城区及所在县（市、区）无房产，或人均住房面积不超过15平方米。
 * 申请人及共同申请人在申请之日前3年内将拥有的福州市城区和单位主体所在县（市、区）范围内的私有住房产权转移的（不含该房产合并计算后家庭人均住房建筑面积在15平方米以下和配偶婚前转移非政策性住房的情形）
 * 申请人曾经离异，以前配偶名义购买过政策性住房；或与前配偶共有的非政策性住房在离异时分割给前配偶，但离异时间不足3年的。
 * 申请人及共同申请人已领取征收公有住房货币补偿款的。
 * 申请人及共同申请人已承租单位自管公房，或福州市的公共租赁住房、保障性租赁住房，或已享受租金优惠、租金补助政策的。
 */
@Service
public class HsQwApplyRuleHouse implements HsQwApplyRuleMethod {

    @Override
    public void execute(HsQwApply hsQwApply, Map<String, HsQwFormAlarm> hsQwFormAlarmMap) {
        // todo 申请人及其家庭成员在福州市城区及所在县（市、区）无房产,调用外部接口

        // 或人均住房面积不超过15平方米。取hsQwApplyHouseList中所有房产的面积，计算人均住房面积
        HsQwApplyer mainApplyer = new HsQwApplyer();
        for (int i = 0; i < hsQwApply.getHsQwApplyerList().size(); i++) {
            if (hsQwApply.getHsQwApplyerList().get(i).getApplyRole().equals("0")) {
                mainApplyer = hsQwApply.getHsQwApplyerList().get(i);
                break;
            }
        }
        double totalArea = 0;
        for (HsQwApplyHouse house : hsQwApply.getHsQwApplyHouseList()) {
            totalArea += Double.parseDouble(house.getFloorArea());
        }
        double avgArea = totalArea / Integer.parseInt(hsQwApply.getFamilyPeoples());
        if (avgArea > 15) {
            HsQwApplyRuleMethod.putAlarmMap(hsQwFormAlarmMap, "hsQwApply.hsQwApplyHouseList["+hsQwApply.getHsQwApplyHouseList().get(0).getId()+"].floorArea",
                    "人均住房面积为" + avgArea + "平方米，超过15平方米", "0",
                    hsQwApply.getId());
        }

        // todo  申请人及共同申请人在申请之日前3年内将拥有的福州市城区和单位主体所在县（市、区）范围内的私有住房产权转移的（不含该房产合并计算后家庭人均住房建筑面积在15平方米以下和配偶婚前转移非政策性住房的情形）

        // todo  申请人曾经离异，以前配偶名义购买过政策性住房；或与前配偶共有的非政策性住房在离异时分割给前配偶，但离异时间不足3年的。

        // todo 申请人及共同申请人已领取征收公有住房货币补偿款的。
        // todo 申请人及共同申请人已承租单位自管公房，或福州市的公共租赁住房、保障性租赁住房，或已享受租金优惠、租金补助政策的。
    }
}
