package com.hsobs.hs.modules.pricelimitpublic.entity;

import javax.validation.constraints.Size;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applypublic.entity.HsQwApplyPublic;
import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.hsobs.hs.modules.pricelimitapplyer.entity.HsPriceLimitApplyer;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 限价房网上公示Entity
 * <AUTHOR>
 * @version 2025-03-10
 */
@Table(name="hs_price_limit_public_detail", alias="a", label="限价房网上公示信息", columns={
		@Column(name="id", attrName="id", label="ID", isPK=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="apply_id", attrName="applyId", label="限价房申请id"),
		@Column(name="public_id", attrName="publicId", label="网上公示id"),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
	},joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsPriceLimitApply.class, alias = "p",
				on = "a.apply_id = p.id", attrName = "hsPriceLimitApply",
				columns = {@Column(includeEntity = HsPriceLimitApply.class)}),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsPriceLimitApplyer.class, alias = "o",
				on = "o.apply_id = p.id and o.apply_role = 0", attrName="hsPriceLimitApply.mainApplyer",
				columns = {@Column(includeEntity = HsPriceLimitApplyer.class)})
	}, orderBy="a.create_date ASC"
)
public class HsPriceLimitPublicDetail extends DataEntity<HsPriceLimitPublicDetail> {
	
	private static final long serialVersionUID = 1L;
	private String applyId;		// 限价房申请id 父类
	private String publicId;		// 网上公示id
	//private HsPriceLimitPublic hsPriceLimitPublic;
	private HsPriceLimitApply hsPriceLimitApply;

	public HsPriceLimitPublicDetail() {
		this(null);
	}

	public HsPriceLimitPublicDetail(String publicId){
		this.publicId = publicId;
	}
	
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@Size(min=0, max=64, message="网上公示id长度不能超过 64 个字符")
	public String getPublicId() {
		return publicId;
	}

	public void setPublicId(String publicId) {
		this.publicId = publicId;
	}

	/*public HsPriceLimitPublic getHsPriceLimitPublic() {
		return hsPriceLimitPublic;
	}

	public void setHsPriceLimitPublic(HsPriceLimitPublic hsPriceLimitPublic) {
		this.hsPriceLimitPublic = hsPriceLimitPublic;
	}*/

	public HsPriceLimitApply getHsPriceLimitApply() {
		return hsPriceLimitApply;
	}

	public void setHsPriceLimitApply(HsPriceLimitApply hsPriceLimitApply) {
		this.hsPriceLimitApply = hsPriceLimitApply;
	}
}