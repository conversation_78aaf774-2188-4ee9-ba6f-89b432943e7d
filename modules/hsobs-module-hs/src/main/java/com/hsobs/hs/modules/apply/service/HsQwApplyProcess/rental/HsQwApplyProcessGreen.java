package com.hsobs.hs.modules.apply.service.HsQwApplyProcess.rental;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class HsQwApplyProcessGreen extends HsQwApplyProcessDefault {
    @Autowired
    private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

    @Override
    public String getStatus() {
        return "租赁绿色通道";
    }

    @Override
    public void execute(HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        // 判断是否含有主申请人信息
        final int[] mainNum = { 0 };
        hsQwApply.getHsQwApplyerList().forEach(k -> {
            if (k.getApplyRole().equals("0")) {
                mainNum[0]++;
            }
        });
        if (mainNum[0] == 0) {
            throw new ServiceException("未添加主申请人信息");
        }
        if (mainNum[0] > 1) {
            throw new ServiceException("主申请人只能有一个");
        }
        this.save(hsQwApply, hsQwApplyService);
        this.saveApplyer(hsQwApply, hsQwApplyService);
        // 更新租赁时间
        hsQwApply.setRentTime(new Date());
        hsQwApplyService.update(hsQwApply);
        //
        hsQwApplyService.updateHouseStatus(hsQwApply.getHouseId(), "1");// 更新房源为已配租
    }

}
