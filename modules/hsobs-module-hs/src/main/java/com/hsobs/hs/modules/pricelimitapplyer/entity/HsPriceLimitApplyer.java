package com.hsobs.hs.modules.pricelimitapplyer.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 限价房-购房申请人Entity
 * <AUTHOR>
 * @version 2024-11-28
 */
@Table(name="hs_price_limit_applyer", alias="a", label="限价房-购房申请人信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="user_id", attrName="userId", label="用户编号", isQuery=false, queryType=QueryType.EQ),
		@Column(name="apply_role", attrName="applyRole", label="申请人角色", comment="申请人角色（0主申请人 1配偶 2父母 3子女 4其他）"),
		@Column(name="apply_id", attrName="applyId", label="申请表id",  queryType=QueryType.EQ),
		@Column(name="name", attrName="name", label="申请人姓名", queryType=QueryType.LIKE),
		@Column(name="organization", attrName="organization", label="工作单位", queryType=QueryType.LIKE),
		@Column(name="id_num", attrName="idNum", label="身份证号", queryType=QueryType.LIKE),
		@Column(name="phone", attrName="phone", label="联系电话", queryType=QueryType.LIKE),
		@Column(name="census", attrName="census", label="户籍"),
		@Column(name="marital_status", attrName="maritalStatus", label="婚姻状况", isQuery=false),
		@Column(name="annual_income", attrName="annualIncome", label="年收入", isQuery=false),
		@Column(name="area", attrName="area", label="住房", comment="住房(面积？)"),
		@Column(name="work_level", attrName="workLevel", label="职级"),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.apply_role asc"
)
public class HsPriceLimitApplyer extends DataEntity<HsPriceLimitApplyer> {
	
	private static final long serialVersionUID = 1L;
	private String userId;		// 用户编号
	private String applyRole;		// 申请人角色（0主申请人 1配偶 2父母 3子女 4其他）
	private String applyId;		// 申请表id
	private String name;		// 申请人姓名
	private String organization;		// 工作单位
	private String idNum;		// 身份证号
	private String phone;		// 联系电话
	private String census;		// 户籍
	private String maritalStatus;		// 婚姻状况
	private Long annualIncome;		// 年收入
	private String area;		// 住房(面积？)

	private String workLevel;

	public HsPriceLimitApplyer() {
		this(null);
	}
	
	public HsPriceLimitApplyer(String id){
		super(id);
	}
	
	//@NotBlank(message="用户编号不能为空")
	@Size(min=0, max=64, message="用户编号长度不能超过 64 个字符")
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	@Size(min=0, max=1, message="申请人角色长度不能超过 1 个字符")
	public String getApplyRole() {
		return applyRole;
	}

	public void setApplyRole(String applyRole) {
		this.applyRole = applyRole;
	}
	
	@Size(min=0, max=64, message="申请表id长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@NotBlank(message="申请人姓名不能为空")
	@Size(min=0, max=64, message="申请人姓名长度不能超过 64 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@NotBlank(message="工作单位不能为空")
	@Size(min=0, max=255, message="工作单位长度不能超过 255 个字符")
	public String getOrganization() {
		return organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}
	
	@NotBlank(message="身份证号不能为空")
	@Size(min=0, max=20, message="身份证号长度不能超过 20 个字符")
	public String getIdNum() {
		return idNum;
	}

	public void setIdNum(String idNum) {
		this.idNum = idNum;
	}
	
	@NotBlank(message="联系电话不能为空")
	@Size(min=0, max=32, message="联系电话长度不能超过 32 个字符")
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	//@NotBlank(message="户籍不能为空")
	@Size(min=0, max=64, message="户籍长度不能超过 64 个字符")
	public String getCensus() {
		return census;
	}

	public void setCensus(String census) {
		this.census = census;
	}
	
	//@NotBlank(message="婚姻状况不能为空")
	@Size(min=0, max=3, message="婚姻状况长度不能超过 3 个字符")
	public String getMaritalStatus() {
		return maritalStatus;
	}

	public void setMaritalStatus(String maritalStatus) {
		this.maritalStatus = maritalStatus;
	}
	
	//@NotNull(message="年收入不能为空")
	public Long getAnnualIncome() {
		return annualIncome;
	}

	public void setAnnualIncome(Long annualIncome) {
		this.annualIncome = annualIncome;
	}
	
	//@NotBlank(message="住房不能为空")
	@Size(min=0, max=255, message="住房长度不能超过 255 个字符")
	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getWorkLevel() {
		return workLevel;
	}

	public void setWorkLevel(String workLevel) {
		this.workLevel = workLevel;
	}
}