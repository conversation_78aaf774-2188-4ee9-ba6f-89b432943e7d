package com.hsobs.hs.modules.utils;

import com.github.promeg.pinyinhelper.Pinyin;

public class ChineseToPinyinUtil {
    
    /**
     * 获取中文字符串的拼音首字母
     * @param chineseStr 中文字符串
     * @return 拼音首字母字符串
     */
    public static String getFirstLetters(String chineseStr) {
        if (chineseStr == null || chineseStr.isEmpty()) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        for (char c : chineseStr.toCharArray()) {
            String pinyin = Pinyin.toPinyin(c);
            if (!pinyin.equals(String.valueOf(c))) {
                // 是中文字符
                result.append(pinyin.charAt(0));
            } else {
                // 非中文字符保持原样
                result.append(c);
            }
        }
        
        return result.toString().toLowerCase();
    }
    
    public static void main(String[] args) {
        String chineseName = "张三";
        System.out.println(getFirstLetters(chineseName)); // 输出: ZS
    }
}