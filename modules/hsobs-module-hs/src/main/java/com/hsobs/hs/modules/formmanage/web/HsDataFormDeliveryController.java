package com.hsobs.hs.modules.formmanage.web;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormDelivery;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormTemplate;
import com.hsobs.hs.modules.formmanage.service.HsDataFormDeliveryService;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 数据表单下发Controller
 * <AUTHOR>
 * @version 2025-02-16
 */
@Controller
@RequestMapping(value = "${adminPath}/formmanage/hsDataFormDelivery")
public class HsDataFormDeliveryController extends BaseController {

	@Autowired
	private HsDataFormDeliveryService hsDataFormDeliveryService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsDataFormDelivery get(String id, boolean isNewRecord) {
		return hsDataFormDeliveryService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsDataFormDelivery hsDataFormDelivery, Model model) {
		model.addAttribute("hsDataFormDelivery", hsDataFormDelivery);
		return "modules/formmanage/hsDataFormDeliveryList";
	}

	// 数据报送列表
	@RequiresPermissions("formmanage:hsDataFormDelivery:view")
	@RequestMapping(value = {"reportList", ""})
	public String reportList(HsDataFormDelivery hsDataFormDelivery, Model model) {
		model.addAttribute("hsDataFormDelivery", hsDataFormDelivery);
		if(StringUtils.isEmpty(hsDataFormDelivery.getPageType())) {
			hsDataFormDelivery.setPageType("0");
		} else {
			if (hsDataFormDelivery.getFormTemplate() == null) {
				hsDataFormDelivery.setFormTemplate(new HsDataFormTemplate());
			}
			hsDataFormDelivery.getFormTemplate().setFormType(hsDataFormDelivery.getPageType());
		}
		model.addAttribute("pageType", hsDataFormDelivery);
		return "modules/formmanage/hsDataFormDeliveryReportList";
	}

	// 数据报送
	@RequiresPermissions("formmanage:hsDataFormDelivery:view")
	@RequestMapping(value = {"report", ""})
	public String report(HsDataFormDelivery hsDataFormDelivery, Model model,  String ids) {
		hsDataFormDelivery.setIds(ids);
		model.addAttribute("hsDataFormDelivery", hsDataFormDelivery);
		return "modules/formmanage/hsDataFormDeliveryReport";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsDataFormDelivery> listData(HsDataFormDelivery hsDataFormDelivery, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormDelivery.setPage(new Page<>(request, response));
		Page<HsDataFormDelivery> page = hsDataFormDeliveryService.findPage(hsDataFormDelivery);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:view")
	@RequestMapping(value = "reportListData")
	@ResponseBody
	public Page<HsDataFormDelivery> reportListData(HsDataFormDelivery hsDataFormDelivery, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormDelivery.setPage(new Page<>(request, response));
		Page<HsDataFormDelivery> page = hsDataFormDeliveryService.reportListPage(hsDataFormDelivery);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:view")
	@RequestMapping(value = "form")
	public String form(HsDataFormDelivery hsDataFormDelivery, Model model) {

		if (StringUtils.isBlank(hsDataFormDelivery.getReceiveType())){
			hsDataFormDelivery.setReceiveType(HsDataFormDelivery.RECEIVE_TYPE_USER);
		}

		model.addAttribute("hsDataFormDelivery", hsDataFormDelivery);
		return "modules/formmanage/hsDataFormDeliveryForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsDataFormDelivery hsDataFormDelivery) {
		hsDataFormDeliveryService.save(hsDataFormDelivery);
		return renderResult(Global.TRUE, text("保存数据表单下发成功！"));
	}

	@RequiresPermissions("formmanage:hsDataFormDelivery:edit")
	@PostMapping(value = "saveReport")
	@ResponseBody
	public String saveReport(HsDataFormDelivery hsDataFormDelivery) {
		hsDataFormDeliveryService.saveReport(hsDataFormDelivery);
		return renderResult(Global.TRUE, text("数据报送成功！"));
	}

	/**
	 * 停用数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsDataFormDelivery hsDataFormDelivery) {
		hsDataFormDelivery.setStatus(HsDataFormDelivery.STATUS_DISABLE);
		hsDataFormDeliveryService.updateStatus(hsDataFormDelivery);
		return renderResult(Global.TRUE, text("停用数据表单下发成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsDataFormDelivery hsDataFormDelivery) {
		hsDataFormDelivery.setStatus(HsDataFormDelivery.STATUS_NORMAL);
		hsDataFormDeliveryService.updateStatus(hsDataFormDelivery);
		return renderResult(Global.TRUE, text("启用数据表单下发成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsDataFormDelivery hsDataFormDelivery) {
		hsDataFormDeliveryService.delete(hsDataFormDelivery);
		return renderResult(Global.TRUE, text("删除数据表单下发成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("formmanage:hsDataFormDelivery:view")
	@RequestMapping(value = "hsDataFormDeliverySelect")
	public String hsDataFormDeliverySelect(HsDataFormDelivery hsDataFormDelivery, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsDataFormDelivery", hsDataFormDelivery);
		return "modules/formmanage/hsDataFormDeliverySelect";
	}
	
}