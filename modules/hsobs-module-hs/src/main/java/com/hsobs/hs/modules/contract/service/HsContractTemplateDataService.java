package com.hsobs.hs.modules.contract.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.contract.entity.HsContractTemplateData;
import com.hsobs.hs.modules.contract.dao.HsContractTemplateDataDao;

/**
 * 合同模板详情表Service
 * <AUTHOR>
 * @version 2025-01-21
 */
@Service
public class HsContractTemplateDataService extends CrudService<HsContractTemplateDataDao, HsContractTemplateData> {
	
	/**
	 * 获取单条数据
	 * @param hsContractTemplateData
	 * @return
	 */
	@Override
	public HsContractTemplateData get(HsContractTemplateData hsContractTemplateData) {
		return super.get(hsContractTemplateData);
	}
	
	/**
	 * 查询分页数据
	 * @param hsContractTemplateData 查询条件
	 * @param hsContractTemplateData page 分页对象
	 * @return
	 */
	@Override
	public Page<HsContractTemplateData> findPage(HsContractTemplateData hsContractTemplateData) {
		return super.findPage(hsContractTemplateData);
	}
	
	/**
	 * 查询列表数据
	 * @param hsContractTemplateData
	 * @return
	 */
	@Override
	public List<HsContractTemplateData> findList(HsContractTemplateData hsContractTemplateData) {
		return super.findList(hsContractTemplateData);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsContractTemplateData
	 */
	@Override
	@Transactional
	public void save(HsContractTemplateData hsContractTemplateData) {
		super.save(hsContractTemplateData);
	}
	
	/**
	 * 更新状态
	 * @param hsContractTemplateData
	 */
	@Override
	@Transactional
	public void updateStatus(HsContractTemplateData hsContractTemplateData) {
		super.updateStatus(hsContractTemplateData);
	}
	
	/**
	 * 删除数据
	 * @param hsContractTemplateData
	 */
	@Override
	@Transactional
	public void delete(HsContractTemplateData hsContractTemplateData) {
		super.delete(hsContractTemplateData);
	}
	
}