package com.hsobs.hs.modules.external.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowableProcess {

    @JsonProperty("elements")
    private List<FlowalbeElement> elements;

    @JsonProperty("currentActivities")
    private List<String> currentActivities;

    @JsonProperty("completedActivities")
    private List<String> completeActivities;

    // Getters and Setters
    public List<FlowalbeElement> getElements() {
        return elements;
    }

    public void setElements(List<FlowalbeElement> elements) {
        this.elements = elements;
    }

    public List<String> getCurrentActivities() {
        return currentActivities;
    }

    public void setCurrentActivities(List<String> currentActivities) {
        this.currentActivities = currentActivities;
    }

    public List<String> getCompleteActivities() {
        return completeActivities;
    }

    public void setCompleteActivities(List<String> completeActivities) {
        this.completeActivities = completeActivities;
    }
}
