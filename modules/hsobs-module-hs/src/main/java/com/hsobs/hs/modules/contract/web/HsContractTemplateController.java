package com.hsobs.hs.modules.contract.web;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.contract.entity.HsContractTemplateData;
import com.hsobs.hs.modules.contract.entity.HsContractTemplateField;
import com.hsobs.hs.modules.contract.service.HsContractTemplateDataService;
import com.hsobs.hs.modules.contract.service.HsContractTemplateFieldService;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFunds;
import com.jeesite.common.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.contract.entity.HsContractTemplate;
import com.hsobs.hs.modules.contract.service.HsContractTemplateService;

/**
 * 合同模板Controller
 * <AUTHOR>
 * @version 2025-01-21
 */
@Controller
@RequestMapping(value = "${adminPath}/contract/hsContractTemplate")
public class HsContractTemplateController extends BaseController {

	@Autowired
	private HsContractTemplateService hsContractTemplateService;
	@Autowired
	private HsContractTemplateDataService hsContractTemplateDataService;
	@Autowired
	private HsContractTemplateFieldService hsContractTemplateFieldService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsContractTemplate get(String id, boolean isNewRecord) {
		return hsContractTemplateService.get(id, isNewRecord);
	}

	@RequiresPermissions("contract:hsContractTemplate:view")
	@RequestMapping(value = "singleData")
	@ResponseBody
	public HsContractTemplate singleData(HsContractTemplate hsContractTemplate, HttpServletRequest request, HttpServletResponse response) {
		if (StringUtils.isEmpty(hsContractTemplate.getId())) {
			return null;
		} else {
			HsContractTemplateData data = hsContractTemplateDataService.get(hsContractTemplate.getId());
			hsContractTemplate.setHsContractTemplateData(data);

			HsContractTemplateField fieldQuery = new HsContractTemplateField();
			fieldQuery.setTemplateId(hsContractTemplate.getId());
			List<HsContractTemplateField> fieldList = hsContractTemplateFieldService.findList(fieldQuery);
			if (fieldList == null) {
				fieldList = new ArrayList<HsContractTemplateField>();
			}
			hsContractTemplate.setHsContractTemplateFieldList(fieldList);

			return hsContractTemplate;
		}
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("contract:hsContractTemplate:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsContractTemplate hsContractTemplate, Model model) {
		model.addAttribute("hsContractTemplate", hsContractTemplate);
		return "modules/contract/hsContractTemplateList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("contract:hsContractTemplate:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsContractTemplate> listData(HsContractTemplate hsContractTemplate, HttpServletRequest request, HttpServletResponse response) {
		hsContractTemplate.setPage(new Page<>(request, response));
		Page<HsContractTemplate> page = hsContractTemplateService.findPage(hsContractTemplate);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("contract:hsContractTemplate:view")
	@RequestMapping(value = "form")
	public String form(HsContractTemplate hsContractTemplate, Model model) {

		// 获取合同模板内容
		if (hsContractTemplate != null && hsContractTemplate.getId() != null) {
			HsContractTemplateData data = hsContractTemplateDataService.get(hsContractTemplate.getId());
			hsContractTemplate.setHsContractTemplateData(data);

			HsContractTemplateField fieldQuery = new HsContractTemplateField();
			fieldQuery.setTemplateId(hsContractTemplate.getId());
			List<HsContractTemplateField> fieldList = hsContractTemplateFieldService.findList(fieldQuery);
			if (fieldList == null) {
				fieldList = new ArrayList<HsContractTemplateField>();
			}
			hsContractTemplate.setHsContractTemplateFieldList(fieldList);
		}
		model.addAttribute("hsContractTemplate", hsContractTemplate);
		model.addAttribute("baseFieldList", hsContractTemplateService.getBaseFieldList());
		return "modules/contract/hsContractTemplateForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("contract:hsContractTemplate:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsContractTemplate hsContractTemplate) {
		hsContractTemplateService.save(hsContractTemplate);
		return renderResult(Global.TRUE, text("保存合同模板成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("contract:hsContractTemplate:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsContractTemplate hsContractTemplate) {
		hsContractTemplateService.delete(hsContractTemplate);
		return renderResult(Global.TRUE, text("删除合同模板成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("contract:hsContractTemplate:view")
	@RequestMapping(value = "hsContractTemplateSelect")
	public String hsContractTemplateSelect(HsContractTemplate hsContractTemplate, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsContractTemplate", hsContractTemplate);
		return "modules/contract/hsContractTemplateSelect";
	}
	
}