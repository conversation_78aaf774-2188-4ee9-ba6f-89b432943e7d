package com.hsobs.hs.modules.externalapi.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jeesite.common.lang.StringUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;

/**
 * 外部API HTTP客户端工具类
 * 
 * <AUTHOR>
 * @version 2024-05-20
 */
public class ExternalApiHttpClient {

    private static final Logger logger = LoggerFactory.getLogger(ExternalApiHttpClient.class);
    
    /**
     * 发送HTTP请求
     * 
     * @param url 请求URL
     * @param method 请求方法
     * @param headers 请求头
     * @param body 请求体
     * @param timeout 超时时间（毫秒）
     * @return 响应结果
     */
    public static JSONObject sendRequest(String url, HttpMethod method, Map<String, String> headers, Object body, int timeout) {
        try {
            String requestId = UUID.randomUUID().toString();
            logger.debug("External API request [{}]: URL={}, Method={}", requestId, url, method);
            
            // 创建RestTemplate
            RestTemplate restTemplate = createRestTemplate(timeout);
            
            // 设置请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            
            // 添加自定义请求头
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(httpHeaders::add);
            }
            
            // 创建请求实体
            HttpEntity<?> requestEntity = new HttpEntity<>(body, httpHeaders);
            
            // 发送请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, method, requestEntity, String.class);
            
            // 解析响应
            String responseBody = responseEntity.getBody();
            logger.debug("External API response [{}]: Status={}, Body={}", requestId, responseEntity.getStatusCode(), 
                    StringUtils.abbreviate(responseBody, 1000));
            
            if (responseBody != null) {
                return JSON.parseObject(responseBody);
            }
            
            return new JSONObject();
        } catch (Exception e) {
            logger.error("Failed to send external API request: " + url, e);
            throw new RuntimeException("Failed to send external API request: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建支持HTTPS的RestTemplate
     */
    private static RestTemplate createRestTemplate(int timeout) throws Exception {
        // 创建一个信任所有证书的 SSLContext
        SSLContext sslContext = new SSLContextBuilder()
                .loadTrustMaterial(null, (certificate, authType) -> true) // 信任所有证书
                .build();

        // 创建一个 SSLConnectionSocketFactory，跳过主机名验证
        SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        // 创建 HttpClient
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(socketFactory)
                .build();

        // 配置 RestTemplate 使用自定义的 HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory(httpClient);
        
        // 设置超时时间
        requestFactory.setConnectTimeout(timeout);
        requestFactory.setReadTimeout(timeout);

        return new RestTemplate(requestFactory);
    }
}
