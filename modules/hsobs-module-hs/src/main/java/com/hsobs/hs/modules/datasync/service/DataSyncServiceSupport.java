package com.hsobs.hs.modules.datasync.service;

import com.hsobs.hs.modules.datasync.bean.*;
import com.jeesite.common.codec.SM4Utils;
import com.jeesite.common.config.Global;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.Role;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import com.jeesite.modules.sys.service.support.EmpUserServiceSupport;
import com.jeesite.modules.sys.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DataSyncServiceSupport {

    @Autowired
    private ApiZcptService apiZcptService;
    @Autowired
    private DataSyncOrgService dataSyncOrgService;
    @Autowired
    private DataSyncUserService dataSyncUserService;
    @Autowired
    private DataSyncRoleService dataSyncRoleService;

    private void sm4() {
        String encryptData = "";
        String decryptData = SM4Utils.decode(encryptData, apiZcptService.getSm4Key());
    }

    /***
     * 同步全量数据
     */
    public void syncAll() {
        try {

            String syncLevel = Global.getConfig("zcpt.syncLevel", "1");

            log.info("开始同步全量数据");
            String rootOrgPid = "0";
//            apiZcptService.getUserOrg("2BE4F2ABCC6E4BBFE06300000000C127");
            // 获取 system 用户
            User systemUser = UserUtils.get("system");

            Api2ResponseBody<List<FullOrgData>> fullOrgResp =  apiZcptService.getFullOrg();
            List<FullOrgData> fullOrgDataList = fullOrgResp.getData();

            Map<String, String> orgIdMap = new HashMap<String, String>();
            Map<String, FullOrgData> orgMap = new HashMap<String, FullOrgData>();
            for (FullOrgData fullOrgData : fullOrgDataList) {
                orgIdMap.put(fullOrgData.getOrgId(), fullOrgData.getOrgPid());
                orgMap.put(fullOrgData.getOrgId(), fullOrgData);
            }

            Map<String, UserRoleAndDataAuthData> roleMap = new HashMap<String, UserRoleAndDataAuthData>();
            Map<String, Office> officeMap = new HashMap<String, Office>();

            for (FullOrgData fullOrgData : fullOrgDataList) {

                // 0001184   住房保障处
//                if (!"0001184".equals(fullOrgData.getOrgId())) {
//                    continue;
//                }

                // 单位详细信息
                Office office = dataSyncOrgService.syncOrg(fullOrgData, orgIdMap, orgMap, rootOrgPid);
                officeMap.put(fullOrgData.getOrgId(), office);
                // 获取单位下用户数据
                Api2ResponseBody<List<UserUnderOrgData>> userUnderOrgResp = apiZcptService.getUserUnderOrg(fullOrgData.getOrgId(), 0, 1);
                List<UserUnderOrgData> userUnderOrgDataList = userUnderOrgResp.getData();
                if (userUnderOrgDataList != null && !userUnderOrgDataList.isEmpty()) {
                    // 批量获取用户详细信息
                    List<String> userIds = new java.util.ArrayList<String>();
                    for (UserUnderOrgData userUnderOrgData : userUnderOrgDataList) {
                        userIds.add(userUnderOrgData.getUserId());
                    }
                    Api2ResponseBody<List<UserDetailData>> userDetailResp = apiZcptService.listUserInfo(userIds);
                    Map<String, UserDetailData> userDetailDataMap = new HashMap<String, UserDetailData>();
                    for (UserDetailData userDetailData : userDetailResp.getData()) {
                        userDetailDataMap.put(userDetailData.getUserId(), userDetailData);
                    }
                    for (UserUnderOrgData userUnderOrgData : userUnderOrgDataList) {
                        EmpUser user = dataSyncUserService.syncUser(userUnderOrgData, office, userDetailDataMap.get(userUnderOrgData.getUserId()));

                        //TODO 用户拥有角色数据后续处理
                        // 获取用户的关联角色及角色数据权限
                        if ("2".equals(syncLevel)) {
                            List<String> roleCodeList = new ArrayList<>();
                            Api2ResponseBody<List<UserRoleAndDataAuthData>> userRoleAndDataAuthResp = apiZcptService.getUserAndRoleOrg(userUnderOrgData.getUserId());
                            if (userRoleAndDataAuthResp.getData() != null && !userRoleAndDataAuthResp.getData().isEmpty()) {
                                for (UserRoleAndDataAuthData userRoleAndDataAuthData : userRoleAndDataAuthResp.getData()) {
                                    roleMap.put(userRoleAndDataAuthData.getCode(), userRoleAndDataAuthData);
                                    roleCodeList.add(userRoleAndDataAuthData.getCode());

                                    String priType = userRoleAndDataAuthData.getPriType();
                                    if (StringUtils.isNotBlank(priType)) {
                                        String[] priTypeArr = StringUtils.split(priType, ",");
                                        if (priTypeArr.length >= 2) {
                                            boolean flag3 = false;
                                            String priTypeTmp = "1";
                                            for (String tmp : priTypeArr) {
                                                if ("3".equals(tmp)) {
                                                    flag3 = true;
                                                } else if ("2".equals(tmp)) {
                                                    priTypeTmp = "2";
                                                }
                                            }
                                            // 拆分角色 数据权限
                                            if (flag3) {
                                                userRoleAndDataAuthData.setPriType("3");
                                                UserRoleAndDataAuthData roleCopy = new UserRoleAndDataAuthData();
                                                BeanUtils.copyProperties(userRoleAndDataAuthData, roleCopy);
                                                roleCopy.setPriType(priTypeTmp);
                                                roleCopy.setCopyFlag(true);
                                                roleCopy.setCode(String.format("%s_COPY_FF_OTHER", userRoleAndDataAuthData.getCode()));
                                                roleCopy.setRoleName(String.format("%s_COPY_FF_OTHER", userRoleAndDataAuthData.getRoleName()));

                                                roleCodeList.add(roleCopy.getCode());
                                                roleMap.put(roleCopy.getCode(), roleCopy);
                                            } else {
                                                userRoleAndDataAuthData.setPriType(priTypeTmp);
                                            }
                                        }
                                    }

                                }
                            }
                            // 更新用户的角色信息
                            user.currentUser(systemUser);
                            dataSyncUserService.syncUserRole(user, roleCodeList);
                        }
                    }
                }
            }
            if ("2".equals(syncLevel)) {
                // 更新用户授权资源信息
                for (UserRoleAndDataAuthData roleData : roleMap.values()) {
                    // 角色同步
                    Role role = dataSyncRoleService.syncRole(roleData, systemUser);
                    role.setIsNewRecord(false);
                    // 菜单授权信息同步
                    if (!roleData.getCopyFlag()) {
                        Api2ResponseBody<List<RoleResourceAuthData>> roleResourceResp = apiZcptService.getRoleResource(roleData.getRoleId(), roleData.getCode(), apiZcptService.getSystemId());
                        dataSyncRoleService.syncRoleAuth(role, systemUser, roleResourceResp);
                    }
                    // 数据权限同步
                    dataSyncRoleService.syncRoleAuthDataScope(role, systemUser, roleData, officeMap);
                }
            }
        } catch (Exception e) {
            log.error("同步全量数据失败", e);
        }
        log.info("同步全量数据结束");
    }

}
