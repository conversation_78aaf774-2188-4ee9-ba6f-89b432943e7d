package com.hsobs.hs.modules.blackrule.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 租赁资格轮候租户黑名单规则表Entity
 * <AUTHOR>
 * @version 2024-12-19
 */
@Table(name="hs_qw_applyer_black_rule", alias="a", label="租赁资格轮候租户黑名单规则表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="name", attrName="name", label="规则名称", queryType=QueryType.LIKE),
		@Column(name="rule_type", attrName="ruleType", label="规则类型", comment="规则类型（0已配租不签合同 1租金拖欠 2违规）"),
		@Column(name="time_num", attrName="timeNum", label="黑名单期限", comment="黑名单期限（月）", isUpdateForce=true),
		@Column(includeEntity=DataEntity.class),
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyerBlackRule extends DataEntity<HsQwApplyerBlackRule> {
	
	private static final long serialVersionUID = 1L;
	private String name;		// 规则名称
	private String ruleType;		// 规则类型（0已配租不签合同 1租金拖欠 2违规）
	private Long timeNum;		// 黑名单期限（月）

	public HsQwApplyerBlackRule() {
		this(null);
	}
	
	public HsQwApplyerBlackRule(String id){
		super(id);
	}
	
	@NotBlank(message="规则名称不能为空")
	@Size(min=0, max=100, message="规则名称长度不能超过 100 个字符")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@NotBlank(message="规则类型不能为空")
	@Size(min=0, max=1, message="规则类型长度不能超过 1 个字符")
	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}
	
	public Long getTimeNum() {
		return timeNum;
	}

	public void setTimeNum(Long timeNum) {
		this.timeNum = timeNum;
	}
	
}