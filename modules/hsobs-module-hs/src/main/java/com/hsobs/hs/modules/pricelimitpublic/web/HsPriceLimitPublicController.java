package com.hsobs.hs.modules.pricelimitpublic.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.pricelimitpublic.entity.HsPriceLimitPublic;
import com.hsobs.hs.modules.pricelimitpublic.entity.HsPriceLimitPublicDetail;
import com.hsobs.hs.modules.pricelimitpublic.service.HsPriceLimitPublicService;

/**
 * 限价房网上公示Controller
 * <AUTHOR>
 * @version 2025-03-10
 */
@Controller
@RequestMapping(value = "${adminPath}/pricelimitpublic/hsPriceLimitPublic")
public class HsPriceLimitPublicController extends BaseController {

	@Autowired
	private HsPriceLimitPublicService hsPriceLimitPublicService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsPriceLimitPublic get(String id, boolean isNewRecord) {
		return hsPriceLimitPublicService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsPriceLimitPublic hsPriceLimitPublic, Model model) {
		model.addAttribute("hsPriceLimitPublic", hsPriceLimitPublic);
		return "modules/pricelimitpublic/hsPriceLimitPublicList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:view")
	@RequestMapping(value = "listOwnerData")
	@ResponseBody
	public Page<HsPriceLimitPublic> listOwnerData(HsPriceLimitPublic hsPriceLimitPublic, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitPublic.setPage(new Page<>(request, response));
		Page<HsPriceLimitPublic> page = hsPriceLimitPublicService.findOwnerPage(hsPriceLimitPublic);
		return page;
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:view")
	@RequestMapping(value = {"listQuery", ""})
	public String listQuery(HsPriceLimitPublic hsPriceLimitPublic, Model model) {
		model.addAttribute("hsPriceLimitPublic", hsPriceLimitPublic);
		return "modules/pricelimitpublic/hsPriceLimitPublicListArchives";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsPriceLimitPublic> listData(HsPriceLimitPublic hsPriceLimitPublic, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitPublic.setPage(new Page<>(request, response));
		Page<HsPriceLimitPublic> page = hsPriceLimitPublicService.findPage(hsPriceLimitPublic);
		return page;
	}
	
	/**
	 * 查询子表数据
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:view")
	@RequestMapping(value = "hsPriceLimitPublicDetailListData")
	@ResponseBody
	public Page<HsPriceLimitPublicDetail> subListData(HsPriceLimitPublicDetail hsPriceLimitPublicDetail, HttpServletRequest request, HttpServletResponse response) {
		hsPriceLimitPublicDetail.setPage(new Page<>(request, response));
		Page<HsPriceLimitPublicDetail> page = hsPriceLimitPublicService.findSubPage(hsPriceLimitPublicDetail);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:view")
	@RequestMapping(value = "form")
	public String form(HsPriceLimitPublic hsPriceLimitPublic, Model model, String pids) {
		hsPriceLimitPublicService.initPublic(hsPriceLimitPublic, pids);
		model.addAttribute("hsPriceLimitPublic", hsPriceLimitPublic);
		return "modules/pricelimitpublic/hsPriceLimitPublicForm";
	}

	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:view")
	@RequestMapping(value = "archives/form")
	public String archives_form(HsPriceLimitPublic hsPriceLimitPublic, Model model, String pids) {
		hsPriceLimitPublicService.initPublic(hsPriceLimitPublic, pids);
		model.addAttribute("hsPriceLimitPublic", hsPriceLimitPublic);
		return "modules/pricelimitpublic/hsPriceLimitPublicFormArchives";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsPriceLimitPublic hsPriceLimitPublic) {
		hsPriceLimitPublicService.save(hsPriceLimitPublic);

		switch(hsPriceLimitPublic.getSubmitType()) {
			case "0":// 提交公示
				return renderResult(Global.TRUE, text("保存网上公示成功！"));
			case "1":	// 批量提交
				return renderResult(Global.TRUE, text("提交网上公示成功！"));
			case "2":// 批量拒绝
				return renderResult(Global.TRUE, text("拒绝网上公示成功！"));
			case "3":// 保存
				return renderResult(Global.TRUE, text("拒绝网上公示成功！"));
		}
		return renderResult(Global.TRUE, text("保存网上公示成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsPriceLimitPublic hsPriceLimitPublic) {
		hsPriceLimitPublic.setStatus(HsPriceLimitPublic.STATUS_DISABLE);
		hsPriceLimitPublicService.updateStatus(hsPriceLimitPublic);
		return renderResult(Global.TRUE, text("停用网上公示成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsPriceLimitPublic hsPriceLimitPublic) {
		hsPriceLimitPublic.setStatus(HsPriceLimitPublic.STATUS_NORMAL);
		hsPriceLimitPublicService.updateStatus(hsPriceLimitPublic);
		return renderResult(Global.TRUE, text("启用网上公示成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsPriceLimitPublic hsPriceLimitPublic) {
		hsPriceLimitPublicService.delete(hsPriceLimitPublic);
		return renderResult(Global.TRUE, text("删除网上公示成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:view")
	@RequestMapping(value = "hsPriceLimitPublicSelect")
	public String hsPriceLimitPublicSelect(HsPriceLimitPublic hsPriceLimitPublic, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsPriceLimitPublic", hsPriceLimitPublic);
		return "modules/pricelimitpublic/hsPriceLimitPublicSelect";
	}

	/**
	 * 网上公示发布
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:edit")
	@PostMapping(value = "sendPublic")
	@ResponseBody
	public String sendPublic(HsPriceLimitPublic hsPriceLimitPublic) {
		hsPriceLimitPublicService.sendPublic(hsPriceLimitPublic);
		return renderResult(Global.FALSE, text("网上公示发布成功！"));
	}

	/**
	 * 导出网上公示
	 */
	@RequiresPermissions("pricelimitpublic:hsPriceLimitPublic:edit")
	@RequestMapping(value = "outputPublic")
	public void outputPublic(HsPriceLimitPublic hsPriceLimitPublic, HttpServletResponse response) {
		hsPriceLimitPublicService.outputPublic(hsPriceLimitPublic, response);
	}
}