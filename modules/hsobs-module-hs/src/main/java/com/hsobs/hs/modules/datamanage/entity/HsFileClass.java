package com.hsobs.hs.modules.datamanage.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import com.jeesite.common.entity.BaseEntity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.entity.TreeEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 档案目录Entity
 * <AUTHOR>
 * @version 2025-01-19
 */
@Table(name="hs_file_class", alias="a", label="档案目录信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="class_name", attrName="className", label="分类名称", queryType=QueryType.LIKE, isTreeName=true),
		@Column(name="group_type", attrName="groupType", label="文件分组类型"),
		@Column(name="business_type", attrName="businessType", label="业务类别", queryType=QueryType.LIKE),
		@Column(name="class_code", attrName="classCode", label="分类编码"),
		@Column(name="parent_code", attrName="parent.id", label="父级编号", isParentCode=true),
		@Column(name="tree_sort", attrName="treeSort", label="排序号", comment = "本级升序", isQuery=true),
		@Column(includeEntity=TreeEntity.class),
		@Column(includeEntity=DataEntity.class),
		@Column(includeEntity=BaseEntity.class),
	}, orderBy="a.tree_sorts, a.id"
)
public class HsFileClass extends TreeEntity<HsFileClass> {
	
	private static final long serialVersionUID = 1L;
	private String className;		// 分类名称
	private String groupType = "global";		// 文件分组类型
	private String businessType;		// 业务类别
	private String classCode;		// 分类编码

	public HsFileClass() {
		this(null);
	}
	
	public HsFileClass(String id){
		super(id);
	}
	
	@Override
	public HsFileClass getParent() {
		return parent;
	}

	@Override
	public void setParent(HsFileClass parent) {
		this.parent = parent;
	}
	
	@NotBlank(message="分类名称不能为空")
	@Size(min=0, max=100, message="分类名称长度不能超过 100 个字符")
	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}
	
	@Size(min=0, max=64, message="文件分组类型长度不能超过 64 个字符")
	public String getGroupType() {
		return groupType;
	}

	public void setGroupType(String groupType) {
		this.groupType = groupType;
	}
	
	@Size(min=0, max=255, message="业务类别长度不能超过 255 个字符")
	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}
	
	@Size(min=0, max=255, message="分类编码长度不能超过 255 个字符")
	public String getClassCode() {
		return classCode;
	}

	public void setClassCode(String classCode) {
		this.classCode = classCode;
	}
	
}