package com.hsobs.hs.modules.external.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人员类型配置类
 * 
 * <AUTHOR>
 * @version 2024-06-18
 */
@Component
@ConfigurationProperties(prefix = "personnel")
public class PersonnelTypeConfig {

    /**
     * 人员类型列表
     */
    private List<PersonnelType> types;

    public List<PersonnelType> getTypes() {
        return types;
    }

    public void setTypes(List<PersonnelType> types) {
        this.types = types;
    }

    /**
     * 人员类型内部类
     */
    public static class PersonnelType {
        /**
         * 类型编码
         */
        private String code;
        
        /**
         * 类型名称
         */
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "PersonnelType{" +
                    "code='" + code + '\'' +
                    ", name='" + name + '\'' +
                    '}';
        }
    }
}
