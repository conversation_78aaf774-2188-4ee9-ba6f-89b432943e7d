package com.hsobs.hs.modules.bpm.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.jeesite.modules.bpm.entity.BpmEntity;
import java.util.List;
import java.util.Map;

@MyBatisDao
public interface CommonBpmDao extends CrudDao<BpmEntity> {

    /**
     * 合并查询待办和已办任务
     */
    List<Map<String, Object>> findAllTaskList(Map<String, Object> params);
    /**
     * 查询业务表单，含任务信息
     */
    List<Map<String, Object>> findAllObjecList(Map<String, Object> params);
}