package com.hsobs.hs.modules.externalapi.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hsobs.hs.modules.externalapi.config.ExternalApiConfig;
import com.hsobs.hs.modules.externalapi.service.AbstractExternalApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 房产部门API服务实现类
 * 
 * <AUTHOR>
 * @version 2024-05-20
 */
@Service
public class RealEstateApiServiceImpl extends AbstractExternalApiService {

    @Autowired
    private ExternalApiConfig externalApiConfig;
    
    @Override
    protected ExternalApiConfig.DepartmentConfig getDepartmentConfig() {
        return externalApiConfig.getRealEstate();
    }
    
    @Override
    public String getDepartmentName() {
        return "realEstate";
    }
    
    /**
     * 房产信息查询
     * 
     * @param idCard 身份证号
     * @return 房产信息
     */
    public JSONObject queryPropertyInfo(String idCard) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        
        return callApi("queryPropertyInfo", params);
    }
    
    /**
     * 房产核验
     * 
     * @param idCard 身份证号
     * @param propertyId 房产编号
     * @return 核验结果
     */
    public JSONObject verifyProperty(String idCard, String propertyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        params.put("propertyId", propertyId);
        
        return callApi("verifyProperty", params);
    }
    
    /**
     * 房产交易记录查询
     * 
     * @param idCard 身份证号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 交易记录
     */
    public JSONObject queryTransactionRecords(String idCard, String startDate, String endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("idCard", idCard);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        
        return callApi("queryTransactionRecords", params);
    }
    
    @Override
    protected JSONObject createMockResponse(String apiName, Map<String, Object> params) {
        JSONObject response = super.createMockResponse(apiName, params);
        JSONObject data = response.getJSONObject("data");
        
        // 根据不同的API创建不同的模拟数据
        switch (apiName) {
            case "queryPropertyInfo":
                JSONArray properties = new JSONArray();
                JSONObject property1 = new JSONObject();
                property1.put("propertyId", "P123456");
                property1.put("address", "模拟地址1");
                property1.put("area", 120.5);
                property1.put("type", "住宅");
                property1.put("ownerName", "模拟业主姓名");
                property1.put("ownerIdCard", params.get("idCard"));
                properties.add(property1);
                
                JSONObject property2 = new JSONObject();
                property2.put("propertyId", "P789012");
                property2.put("address", "模拟地址2");
                property2.put("area", 85.3);
                property2.put("type", "商业");
                property2.put("ownerName", "模拟业主姓名");
                property2.put("ownerIdCard", params.get("idCard"));
                properties.add(property2);
                
                data.put("properties", properties);
                data.put("total", properties.size());
                break;
                
            case "verifyProperty":
                data.put("verified", true);
                data.put("propertyId", params.get("propertyId"));
                data.put("idCard", params.get("idCard"));
                data.put("ownerName", "模拟业主姓名");
                data.put("address", "模拟地址");
                data.put("area", 120.5);
                data.put("type", "住宅");
                break;
                
            case "queryTransactionRecords":
                JSONArray records = new JSONArray();
                JSONObject record1 = new JSONObject();
                record1.put("transactionId", "T123456");
                record1.put("propertyId", "P123456");
                record1.put("transactionType", "购买");
                record1.put("transactionDate", "2020-01-01");
                record1.put("price", 1500000);
                record1.put("buyerName", "模拟买家姓名");
                record1.put("buyerIdCard", params.get("idCard"));
                record1.put("sellerName", "模拟卖家姓名");
                record1.put("sellerIdCard", "模拟卖家身份证号");
                records.add(record1);
                
                data.put("records", records);
                data.put("total", records.size());
                break;
        }
        
        return response;
    }
}
