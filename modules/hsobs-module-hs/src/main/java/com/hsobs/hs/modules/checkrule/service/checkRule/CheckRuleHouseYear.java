package com.hsobs.hs.modules.checkrule.service.checkRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;
import org.springframework.stereotype.Service;

/**
 * 离异3年内未曾购买政策性住房
 * 若申请人再婚且前配偶曾拥有政策性住房但已分割或转移，离婚时间需超过3年。
 */
@Service
public class CheckRuleHouseYear implements ICheckRule {
    @Override
    public CheckRuleResult execute(HsQwApply hsQwApply, HsQwCheckRule rule) {
        return CheckRuleResult.success("成功");
    }
}
