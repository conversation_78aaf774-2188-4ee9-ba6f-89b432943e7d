package com.hsobs.hs.modules.housrvr.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;

/**
 * 房源VR信息表Entity
 * <AUTHOR>
 * @version 2025-03-20
 */
@Table(name="hs_qw_house_vr", alias="a", label="房源VR信息表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="estate_name", attrName="estateName", label="小区名称", queryType=QueryType.LIKE),
		@Column(name="house_unit", attrName="houseUnit", label="楼栋单元"),
		@Column(name="house_area", attrName="houseArea", label="面积"),
		@Column(includeEntity=DataEntity.class),
		@Column(name="vr_url", attrName="vrUrl", label="vr地址"),
	}, orderBy="a.update_date DESC"
)
public class HsQwHouseVr extends DataEntity<HsQwHouseVr> {
	
	private static final long serialVersionUID = 1L;
	private String estateName;		// 小区名称
	private String houseUnit;		// 楼栋单元
	private Double houseArea;		// 面积
	private String vrUrl;		// vr地址

	@ExcelFields({
			@ExcelField(title="编号", attrName="id", align=Align.CENTER, sort=10),
			@ExcelField(title="小区名称", attrName="estateName", align=Align.CENTER, sort=20),
			@ExcelField(title="楼栋单元", attrName="houseUnit", align=Align.CENTER, sort=30),
			@ExcelField(title="面积", attrName="houseArea", align=Align.CENTER, sort=40),
			@ExcelField(title="vr地址", attrName="vrUrl", align=Align.CENTER, sort=60),
	})
	public HsQwHouseVr() {
		this(null);
	}
	
	public HsQwHouseVr(String id){
		super(id);
	}
	
	@NotBlank(message="小区名称不能为空")
	@Size(min=0, max=100, message="小区名称长度不能超过 100 个字符")
	public String getEstateName() {
		return estateName;
	}

	public void setEstateName(String estateName) {
		this.estateName = estateName;
	}
	
	@NotBlank(message="楼栋单元不能为空")
	@Size(min=0, max=100, message="楼栋单元长度不能超过 100 个字符")
	public String getHouseUnit() {
		return houseUnit;
	}

	public void setHouseUnit(String houseUnit) {
		this.houseUnit = houseUnit;
	}
	
	@NotNull(message="面积不能为空")
	public Double getHouseArea() {
		return houseArea;
	}

	public void setHouseArea(Double houseArea) {
		this.houseArea = houseArea;
	}
	
	@NotBlank(message="vr地址不能为空")
	@Size(min=0, max=200, message="vr地址长度不能超过 200 个字符")
	public String getVrUrl() {
		return vrUrl;
	}

	public void setVrUrl(String vrUrl) {
		this.vrUrl = vrUrl;
	}
	
}