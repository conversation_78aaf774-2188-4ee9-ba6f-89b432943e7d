package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据角色ID获取单位下用户列表响应实体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserByRoleIdData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String name;
    private String userId;
    private String account;
    private String orgId;
    private String orgName;
    private String orgCode;
    private String mobile;

}
