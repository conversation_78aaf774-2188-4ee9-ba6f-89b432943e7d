package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;

import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 住房保障数据统计Entity  公租房资格年审统计
 * <AUTHOR>
 * @version 2025-1-2
 */
public class DataIntelligenceReview extends DataEntity<DataIntelligenceReview> {

	@ExcelFields({
			@ExcelField(title = "小区名称", attrName = "estateName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "总量", attrName = "totalCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "正常量", attrName = "normalCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "异常量", attrName = "abnormalCount", align = ExcelField.Align.CENTER, sort = 40)
	})

	private static final long serialVersionUID = 1L;
	private String parentCodeTree;

	private String city;
	private String area;

	private String estateId;
	private String estateName;	//区域
	private Date startDate;		//开始日期
	private Date endDate;	//结束日期
	private Integer compareType;

	private String abnormalType;
	private Integer	totalCount;
	private Integer	normalCount;
	private Integer	abnormalCount;

	public DataIntelligenceReview() {
		this(null);
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			endDate = new Date();
			startDate = dateFormat.parse(String.format("%04d-01-01", endDate.getYear()+1900));  // 解析指定日期字符串
		} catch (Exception e) {
		}
		parentCodeTree = "";
		if(!UserUtils.getUser().isAdmin()){
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		else{
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		compareType = 19;
	}
	
	public DataIntelligenceReview(String id){
		super(id);
	}

	public String getParentCodeTree() {
		return parentCodeTree;
	}

	public void setParentCodeTree(String parentCodeTree) {
		this.parentCodeTree = parentCodeTree;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getEstateId() {
		return estateId;
	}

	public void setEstateId(String estateId) {
		this.estateId = estateId;
	}

	public String getEstateName() {
		return estateName;
	}

	public void setEstateName(String estateName) {
		this.estateName = estateName;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getCompareType() {
		return compareType;
	}

	public void setCompareType(Integer compareType) {
		this.compareType = compareType;
	}

	public String getAbnormalType() {
		return abnormalType;
	}

	public void setAbnormalType(String abnormalType) {
		this.abnormalType = abnormalType;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public Integer getNormalCount() {
		return normalCount;
	}

	public void setNormalCount(Integer normalCount) {
		this.normalCount = normalCount;
	}

	public Integer getAbnormalCount() {
		return abnormalCount;
	}

	public void setAbnormalCount(Integer abnormalCount) {
		this.abnormalCount = abnormalCount;
	}
}