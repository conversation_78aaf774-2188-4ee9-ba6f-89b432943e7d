package com.hsobs.hs.modules.applyrule.service;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyrule.dao.HsQwApplyRuleDao;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRuleResult;
import com.hsobs.hs.modules.applyrule.service.HsQwApplyRule.IHsQwApplyRule;
import com.hsobs.hs.modules.applyscore.entity.HsQwApplyScoreDetail;
import com.hsobs.hs.modules.applyscore.service.HsQwApplyScoreDetailService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.CrudService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 租赁资格轮候规则配置Service
 *
 * <AUTHOR>
 * @version 2024-12-06
 */
@Service
public class HsQwApplyRuleService extends CrudService<HsQwApplyRuleDao, HsQwApplyRule> implements ApplicationContextAware {

    @Autowired
    private HsQwApplyDao hsQwApplyDao;

    @Autowired
    private HsQwApplyScoreDetailService hsQwApplyScoreDetailService;
    /**
     * 存储所有规则方法
     **/
    private Map<String, IHsQwApplyRule> ruleMap = new HashMap<>();
    /**
     * 存储所有规则配置
     **/
    private Map<String, List<HsQwApplyRule>> ruleType = new HashMap<>();

    /**
     * 获取单条数据
     *
     * @param hsQwApplyRule
     * @return
     */
    @Override
    public HsQwApplyRule get(HsQwApplyRule hsQwApplyRule) {
        return super.get(hsQwApplyRule);
    }

    /**
     * 查询分页数据
     *
     * @param hsQwApplyRule 查询条件
     * @param hsQwApplyRule page 分页对象
     * @return
     */
    @Override
    public Page<HsQwApplyRule> findPage(HsQwApplyRule hsQwApplyRule) {
        return super.findPage(hsQwApplyRule);
    }

    /**
     * 查询列表数据
     *
     * @param hsQwApplyRule
     * @return
     */
    @Override
    public List<HsQwApplyRule> findList(HsQwApplyRule hsQwApplyRule) {
        return super.findList(hsQwApplyRule);
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param hsQwApplyRule
     */
    @Override
    @Transactional
    public void save(HsQwApplyRule hsQwApplyRule) {
        super.save(hsQwApplyRule);
        this.refreshRuleType();
//        this.refreshScore(); 会全量刷新，谨慎开启
    }

    private void refreshScore() {
        HsQwApply hsQwApply = new HsQwApply();
        hsQwApply.setStatus(HsQwApply.STATUS_AUDIT);
        List<HsQwApply> hsQwApplies = hsQwApplyDao.findList(hsQwApply);
        for (HsQwApply qwApply : hsQwApplies) {
            try {
                this.refreshScore(qwApply);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void refreshRuleType() {
        ruleType.clear();
        HsQwApplyRule hsQwApplyRule = new HsQwApplyRule();
        hsQwApplyRule.setStatus(HsQwApplyRule.STATUS_NORMAL);
        List<HsQwApplyRule> ruleList = this.findList(new HsQwApplyRule());
        ruleList.forEach(this::putRuleType);
    }

    private void putRuleType(HsQwApplyRule hsQwApplyRule) {
        List<HsQwApplyRule> ruleList = ruleType.get(hsQwApplyRule.getRuleType());
        if (ruleList == null) {
            ruleList = new ArrayList<>();
        }
        ruleList.add(hsQwApplyRule);
        ruleType.put(hsQwApplyRule.getRuleType(), ruleList);
    }


    /**
     * 更新状态
     *
     * @param hsQwApplyRule
     */
    @Override
    @Transactional
    public void updateStatus(HsQwApplyRule hsQwApplyRule) {
        super.updateStatus(hsQwApplyRule);
        this.refreshRuleType();
    }

    /**
     * 删除数据
     *
     * @param hsQwApplyRule
     */
    @Override
    @Transactional
    public void delete(HsQwApplyRule hsQwApplyRule) {
        super.delete(hsQwApplyRule);
        this.refreshRuleType();
    }

    /**
     * 刷新单个申请表计分
     *
     * @param hsQwApply
     */
    public void refreshScore(HsQwApply hsQwApply) {
        //计算共同申请人得分
        HsQwApplyRuleResult scorePerson = this.computeAvgPersons(hsQwApply);
        //计算人均住房面积得分
        HsQwApplyRuleResult scoreArea = this.computeAvgArea(hsQwApply);
        //计算人均收入得分
        HsQwApplyRuleResult scoreIncome = this.computeAvgIncome(hsQwApply);
        //计算累计工龄得分
        HsQwApplyRuleResult scoreWork = this.computeWork(hsQwApply);
        //计算轮候时间累计得分
        HsQwApplyRuleResult scoreLh = this.computeLh(hsQwApply);
        //计算总得分
        this.computeTotal(scoreArea, scorePerson, scoreIncome, scoreWork, scoreLh, hsQwApply);
        //将各项得分按不同分类写入hs_qw_apply_score_detail数据库
        this.saveScoreDetail(hsQwApply, scoreArea, scorePerson, scoreIncome, scoreWork, scoreLh);
    }

    private void saveScoreDetail(HsQwApply hsQwApply, HsQwApplyRuleResult scoreArea,
                                 HsQwApplyRuleResult scorePerson, HsQwApplyRuleResult scoreIncome,
                                 HsQwApplyRuleResult scoreWork, HsQwApplyRuleResult scoreLh) {
        //删除原有得分
        hsQwApplyScoreDetailService.deleteByApplyId(hsQwApply.getId());
        List<HsQwApplyScoreDetail> scoreDetails = new ArrayList<>();
        //添加家庭人均住房面积得分
        HsQwApplyScoreDetail scoreDetail = new HsQwApplyScoreDetail();
        scoreDetail = this.newScoreDetail(scoreArea, hsQwApply.getId(), "0");
        scoreDetails.add(scoreDetail);
        //添加共同申请人数得分
        scoreDetail = this.newScoreDetail(scorePerson, hsQwApply.getId(), "1");
        scoreDetails.add(scoreDetail);
        //添加家庭人均年收入得分
        scoreDetail = this.newScoreDetail(scoreIncome, hsQwApply.getId(), "2");
        scoreDetails.add(scoreDetail);
        //添加申请人累计工龄得分
        scoreDetail = this.newScoreDetail(scoreWork, hsQwApply.getId(), "3");
        scoreDetails.add(scoreDetail);
        //添加轮候时间累计得分
        scoreDetail = this.newScoreDetail(scoreLh, hsQwApply.getId(), "4");
        scoreDetails.add(scoreDetail);
        hsQwApplyScoreDetailService.saveBatch(scoreDetails);
    }

    private HsQwApplyScoreDetail newScoreDetail(HsQwApplyRuleResult result, String applyId, String ruleType) {
        HsQwApplyScoreDetail scoreDetail = new HsQwApplyScoreDetail();
        scoreDetail.setApplyId(applyId);
        scoreDetail.setRuleType(ruleType);
        scoreDetail.setScore(result.getScore());
        scoreDetail.setRemarks(result.getContent());
        return scoreDetail;
    }

    private void computeTotal(HsQwApplyRuleResult scoreArea, HsQwApplyRuleResult scorePerson,
                              HsQwApplyRuleResult scoreIncome, HsQwApplyRuleResult scoreWork,
                              HsQwApplyRuleResult scoreLh, HsQwApply hsQwApply) {
        hsQwApply.setApplyScore((scoreArea.getScore()
                + scorePerson.getScore() +
                scoreIncome.getScore() +
                scoreWork.getScore() +
                scoreLh.getScore()));
    }

    private HsQwApplyRuleResult computeLh(HsQwApply hsQwApply) {
        //获取轮候规则配置集合
        List<HsQwApplyRule> ruleList = ruleType.get("4");
        if (hsQwApply.getApplyTime() == null) {
            return HsQwApplyRuleResult.empty(DateUtils.formatDate(hsQwApply.getApplyTime(), "yyyy-MM-dd"));
        }
        return this.compute(DateUtils.formatDate(hsQwApply.getApplyTime(), "yyyy-MM-dd"), ruleList);
    }

    private HsQwApplyRuleResult computeWork(HsQwApply hsQwApply) {
        //获取工龄规则配置集合
        List<HsQwApplyRule> ruleList = ruleType.get("3");
        return this.compute(DateUtils.formatDate(hsQwApply.getMainApplyer().getWorkTime(), "yyyy-MM"), ruleList);
    }

    private HsQwApplyRuleResult computeAvgIncome(HsQwApply hsQwApply) {
        //获取收入规则配置集合
        List<HsQwApplyRule> ruleList = ruleType.get("2");
        //计算总收入数
        this.checkIncome(hsQwApply);
        String avgIncome = (Double.parseDouble(hsQwApply.getFamilyIncome()) / Integer.parseInt(hsQwApply.getFamilyPeoples())) + "";
        hsQwApply.setAvgIncome(avgIncome);
        return this.compute(avgIncome, ruleList);
    }

    /**
     * 是否需要与外接口统计总收入，目前取申请表填写数量
     *
     * @param hsQwApply
     * @return
     */
    private void checkIncome(HsQwApply hsQwApply) {
        return;
    }

    private HsQwApplyRuleResult computeAvgPersons(HsQwApply hsQwApply) {
        //获取人数规则配置集合
        List<HsQwApplyRule> ruleList = ruleType.get("1");
        //计算总人数
        this.checkPeople(hsQwApply);
        return this.compute(hsQwApply.getFamilyPeoples() + "", ruleList);
    }

    /**
     * 是否需要与外接口统计家庭人数，目前取申请表填写数量
     *
     * @param hsQwApply
     * @return
     */
    private void checkPeople(HsQwApply hsQwApply) {
        return;
    }

    private HsQwApplyRuleResult computeAvgArea(HsQwApply hsQwApply) {
        //获取面积规则配置集合
        List<HsQwApplyRule> ruleList = ruleType.get("0");
        //计算总面积数
        double areaNum = 0;
        List<HsQwApplyHouse> houseList = hsQwApply.getHsQwApplyHouseList();
        if (houseList.isEmpty()) {
            areaNum = 0;
        } else {
            for (HsQwApplyHouse hsQwApplyHouse : houseList) {
                Double area = Double.valueOf(hsQwApplyHouse.getFloorArea());
                areaNum += area;
            }
        }
        String avgArea = (areaNum / Integer.parseInt(hsQwApply.getFamilyPeoples())) + "";
        hsQwApply.setAvgArea(avgArea);
        return this.compute(avgArea, ruleList);
    }

    private HsQwApplyRuleResult compute(String ob, List<HsQwApplyRule> ruleList) {
        for (HsQwApplyRule hsQwApplyRule : ruleList) {
            IHsQwApplyRule rule = ruleMap.get(hsQwApplyRule.getRuleConfig());
            HsQwApplyRuleResult ruleResult = rule.execute(ob, hsQwApplyRule);
            if (ruleResult.isResult()) {
                ruleResult.setContent("统计值：" + ob + "，" +
                        "满足规则：" + hsQwApplyRule.getRuleName() + "，" +
                        "得分：" + ruleResult.getScore());
                return ruleResult;
            }
        }
        return HsQwApplyRuleResult.empty(ob);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IHsQwApplyRule> beanMap = applicationContext.getBeansOfType(IHsQwApplyRule.class);
        beanMap.forEach((k, v) -> ruleMap.put(v.getRuleConfig(), v));
        this.refreshRuleType();
    }

}