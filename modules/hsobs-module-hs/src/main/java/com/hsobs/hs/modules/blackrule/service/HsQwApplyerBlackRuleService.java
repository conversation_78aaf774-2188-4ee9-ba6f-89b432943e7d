package com.hsobs.hs.modules.blackrule.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.blackrule.entity.HsQwApplyerBlackRule;
import com.hsobs.hs.modules.blackrule.dao.HsQwApplyerBlackRuleDao;

/**
 * 租赁资格轮候租户黑名单规则表Service
 * <AUTHOR>
 * @version 2024-12-19
 */
@Service
public class HsQwApplyerBlackRuleService extends CrudService<HsQwApplyerBlackRuleDao, HsQwApplyerBlackRule> {
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyerBlackRule
	 * @return
	 */
	@Override
	public HsQwApplyerBlackRule get(HsQwApplyerBlackRule hsQwApplyerBlackRule) {
		return super.get(hsQwApplyerBlackRule);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyerBlackRule 查询条件
	 * @param hsQwApplyerBlackRule page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyerBlackRule> findPage(HsQwApplyerBlackRule hsQwApplyerBlackRule) {
		return super.findPage(hsQwApplyerBlackRule);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyerBlackRule
	 * @return
	 */
	@Override
	public List<HsQwApplyerBlackRule> findList(HsQwApplyerBlackRule hsQwApplyerBlackRule) {
		return super.findList(hsQwApplyerBlackRule);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyerBlackRule
	 */
	@Override
	@Transactional
	public void save(HsQwApplyerBlackRule hsQwApplyerBlackRule) {
		super.save(hsQwApplyerBlackRule);
	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyerBlackRule
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyerBlackRule hsQwApplyerBlackRule) {
		super.updateStatus(hsQwApplyerBlackRule);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyerBlackRule
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyerBlackRule hsQwApplyerBlackRule) {
		super.delete(hsQwApplyerBlackRule);
	}

    public HsQwApplyerBlackRule getApplyerBlackByReansonType(String s) {
		HsQwApplyerBlackRule rule = new HsQwApplyerBlackRule();
		rule.setRuleType(s);
		return this.findList(rule).get(0);
    }
}