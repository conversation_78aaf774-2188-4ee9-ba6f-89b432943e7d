package com.hsobs.hs.modules.external.service;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.apply.service.applyCheck.HsQwApplyCheck;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.checkdetail.entity.HsQwManagementCheckDetail;
import com.hsobs.hs.modules.checkdetail.service.HsQwManagementCheckDetailService;
import com.hsobs.hs.modules.checkobject.entity.HsQwManagementCheckObject;
import com.hsobs.hs.modules.checkobject.service.HsQwManagementCheckObjectService;
import com.hsobs.hs.modules.checkrecord.entity.HsQwCheckRecord;
import com.hsobs.hs.modules.checkrecord.service.HsQwCheckRecordService;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.estate.service.HsQwPublicRentalEstateService;
import com.hsobs.hs.modules.external.entity.*;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.managementcheck.entity.HsQwManagementCheck;
import com.hsobs.hs.modules.managementcheck.service.HsQwManagementCheckService;
import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.hsobs.hs.modules.rentfee.service.HsQwRentalFeeService;
import com.hsobs.hs.modules.utils.HsBeanMapperUtil;
import com.hsobs.hs.modules.utils.HsBpmCommonUtils;
import com.hsobs.hs.modules.utils.RequestMergeUtil;
import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.entity.BpmBackActivity;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.Role;
import com.jeesite.modules.sys.service.EmployeeService;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.UserUtils;
import com.mchange.lang.IntegerUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;

@Service
public class HsQwExternalService extends BaseService implements ApplicationContextAware {

    @Autowired
    private HsQwApplyService hsQwApplyService;

    private BpmTaskService bpmTaskService = BpmUtils.getBpmTaskService();

    @Autowired
    HsQwCheckRecordService hsQwCheckRecordService;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private OfficeService officeService;

    @Autowired
    private CommonBpmService commonBpmService;

    @Autowired
    private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

    @Autowired
    private HsQwPublicRentalEstateService hsQwPublicRentalEstateService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private HsQwManagementCheckObjectService hsQwManagementCheckObjectService;

    @Autowired
    private HsQwManagementCheckDetailService hsQwManagementCheckDetailService;

    @Autowired
    private HsQwManagementCheckService hsQwManagementCheckService;

    @Autowired
    private HsBpmCommonUtils hsBpmCommonUtils;

    @Autowired
    private HsQwRentalFeeService hsQwRentalFeeService;

    private static Map<String, String> statusMap = new HashMap();
    /**
     * 存储所有方法实现-申请资格检查方法
     **/
    private Map<String, HsQwApplyCheck> checkMap = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 资格审查方法集合
        Map<String, HsQwApplyCheck> beanCheckMap = applicationContext.getBeansOfType(HsQwApplyCheck.class);
        beanCheckMap.forEach((k, v) -> checkMap.put(v.getApplyType(), v));
        // 物业核验状态信息表
        statusMap.put("发起入住核验", "0");
        statusMap.put("租户入住核验审核", "1");
        statusMap.put("物业入住核验审核", "2");
        statusMap.put("租户发起退租申请", "3");
        statusMap.put("物业退租申请核验", "4");
        statusMap.put("租户退租申请确认", "5");

    }

    /**
     * 用户流程操作权限判断接口
     */
    public ApiProcessPermission processPermission(ApiProcessPermission apiProcessPermission) {
        HsQwApply hsQwApply = new HsQwApply();
        try {
            // 自定义流程操作
            HsQwApplyCheck check = checkMap.get(apiProcessPermission.getProcessType());
            if (check == null) {
                throw new ServiceException("申请资格验证失败，存在空的类型检查方法！");
            }
            check.execute(hsQwApply);// 自定义流程操作
        } catch (ServiceException e) {
            return generateResult(false, e.getMessage());
        }
        return generateResult(true, "");
    }

    private ApiProcessPermission generateResult(boolean flag, String msg) {
        ApiProcessPermission result = new ApiProcessPermission();
        result.setIsHandle(flag ? "1" : "0");
        if (!flag) {
            result.setReason(msg);
        }
        return result;
    }

    /**
     * 获取事项记录列表接口
     *
     * @param apiHsQwApplyMatter
     * @return
     */
    public Page<ApiHsQwApplyMatter> applyMatterList(ApiHsQwApplyMatter apiHsQwApplyMatter) {
        if (apiHsQwApplyMatter.getSxzt() != null && apiHsQwApplyMatter.getSxzt().equals("3")) {
            return new Page<>();// 目前没有逾期未处理状态
        }
        HsQwApply hsQwApply = new HsQwApply();
        this.initPage(hsQwApply, apiHsQwApplyMatter);
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwApply.setProcessName(apiHsQwApplyMatter.getSxmc());
        return this.findPageWithMapping(() -> hsQwApplyService.findApplyPageByTaskNew(hsQwApply,
                new String[] { HsQwApply.APPLY_STATUS_DRAFT, HsQwApply.APPLY_STATUS_AUDIT_USER_RENT,
                        HsQwApply.APPLY_STATUS_OFFLINE_RENT_CONFIRM, HsQwApply.APPLY_STATUS_RENT_CONFIRM, "配租确认" },
                apiHsQwApplyMatter.getSxzt(), "rent_apply%"), k -> { // 传入转换逻辑
                    ApiHsQwApplyMatter applyMatter = new ApiHsQwApplyMatter();
                    applyMatter.setSqdh(k.getId());
                    applyMatter.setSxdh(k.getTaskId());
                    applyMatter.setSxmc(k.getProcessName());
                    applyMatter.setSxzt(k.getBpmStatus());
                    this.setSxcl(k.getProcessName(), k.getBpmStatus(), k, applyMatter);
                    applyMatter.setPzsj(DateUtils.getDateTime());// todo
                    applyMatter.setPzdd("鼓楼区");// todo
                    applyMatter.setZysx("");// todo
                    applyMatter.setClsxks(k.getTaskStartDate());
                    applyMatter.setClsxjz(k.getTaskDueDate());
                    if (k.getHsQwApplyHouse() != null) {
                        applyMatter.setFwdz(k.getHsQwApplyHouse().getLocation());
                        applyMatter.setMph(k.getHsQwApplyHouse().getHouseNum());
                    }
                    return applyMatter;
                });
    }

    /**
     * 事项类型：申请单类型applyMater（0 公租房申请 1个人信息变更 2居室变更 3承租变更） + 过程类型（1申请 2配租复查 3线下选房
     * 4承租确认）
     * 公租房申请：01申请、02配租复查、03线下选房确认、04承租确认
     * 个人信息变更申请：11申请
     * 居室变更申请：21申请、22配租复查、23线下选房确认、24承租确认
     * 承租变更申请：41申请、42配租确认
     * 事项处理结果：只有过程为2和3的时候存在1、2、3的情况，其他的只有0和5,4不考虑
     * 0：待处理 1：已处理-参加/承租 2:已处理-不参加/本次不参加/不承租 3：已处理-已不符合条件 4:逾期未处理 5:已处理
     *
     * @param processName
     * @param bpmStatus
     * @param k
     * @param apiHsQwApplyMatter
     */
    private void setSxcl(String processName, String bpmStatus, HsQwApply k, ApiHsQwApplyMatter apiHsQwApplyMatter) {
        String applyMatter = k.getApplyMatter();// 获取申请单类型
        String processType = this.getProcessType(processName);// 获取过程类型
        // 事项类型拼接
        apiHsQwApplyMatter.setSxlx(applyMatter + processType);
        k.setBpmStatus(bpmStatus);
        // 事项处理结果
        apiHsQwApplyMatter.setSxclsj(this.getSxclsj(apiHsQwApplyMatter.getSxlx(), k));
    }

    /**
     * 根据事项类型来确认取值内容
     * 02、22取recheckStatus
     * 03、23取offlineRent
     * 04、24取applyAccepted
     *
     * @param sxlx
     * @param k
     * @return
     */
    private String getSxclsj(String sxlx, HsQwApply k) {
        if (k.getBpmStatus().equals("1")) {
            return "0";
        }
        if (sxlx.equals("02") || sxlx.equals("22")) {
            return (IntegerUtils.parseInt(k.getRecheckStatus(), 0) + 1) + "";
        } else if (sxlx.equals("03") || sxlx.equals("23")) {
            return k.getOfflineRent() != null ? k.getOfflineRent().equals("0") ? "1" : "2" : "0";
        } else if (sxlx.equals("04") || sxlx.equals("24")) {
            return k.getApplyAccepted() != null ? k.getApplyAccepted().equals("0") ? "1" : "2" : "0";
        }
        return "5";
    }

    /**
     * 过程类型（1申请 2配租复查 3线下选房 4承租确认）
     *
     * @param processName
     * @return
     */
    private String getProcessType(String processName) {
        if (processName.equals(HsQwApply.APPLY_STATUS_DRAFT)) {
            return "1";
        } else if (processName.equals(HsQwApply.APPLY_STATUS_RENT_CONFIRM) || processName.equals("配租确认")) {
            return "2";
        } else if (processName.equals(HsQwApply.APPLY_STATUS_OFFLINE_RENT_CONFIRM)) {
            return "3";
        } else if (processName.equals(HsQwApply.APPLY_STATUS_AUDIT_USER_RENT)) {
            return "4";
        }
        return "0";
    }

    public ApiHsQwApplyMatter applyDetail(ApiHsQwApplyMatter apiHsQwApplyMatter) {
        BpmTask task = bpmTaskService.getTask(apiHsQwApplyMatter.getSxdh());
        if (task == null) {
            throw new ServiceException("不存在的任务单信息！");
        }
        HsQwApply hsQwApply = hsQwApplyService.get(task.getProcIns().getBizKey());
        ApiHsQwApplyMatter applyMatter = new ApiHsQwApplyMatter();
        applyMatter.setSqdh(task.getId());
        applyMatter.setSxmc(task.getName());
        this.setSxcl(task.getName(), task.getStatus(), hsQwApply, applyMatter);
        applyMatter.setPzsj(DateUtils.getDateTime());
        applyMatter.setPzdd("鼓楼区");// todo
        applyMatter.setZysx("");// todo
        applyMatter.setSxzt(task.getStatus());
        applyMatter.setClsxks(DateUtils.formatDate(task.getClaimTime(), "yyyy-MM-dd HH:mm:ss"));
        applyMatter.setClsxjz(DateUtils.formatDate(task.getDueDate(), "yyyy-MM-dd HH:mm:ss"));
        applyMatter.setFwdz(hsQwApply.getHsQwApplyHouse() != null ? hsQwApply.getHsQwApplyHouse().getLocation() : "");
        applyMatter.setMph(hsQwApply.getHsQwApplyHouse() != null ? hsQwApply.getHsQwApplyHouse().getHouseNum() : "");
        return applyMatter;
    }

    /**
     * 根据当前用户手机号码获取公租房主流程信息列表
     *
     * @param apiHsBpmProcess
     * @return
     */
    public Page<ApiHsBpmProcess> applyMasterDetail(ApiHsBpmProcess apiHsBpmProcess) {
        HsQwApply main = this.getUserMainApply();
        if (main == null) {
            return new Page<>();
        }
        return this.getProcessInfo(main.getId());
    }

    /**
     * 获取用户当前流程中或者已审核的申请
     *
     * @return
     */
    private HsQwApply getUserMainApply() {
        HsQwApply hsQwApply = new HsQwApply();
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwApply.setStatus_in(
                new String[] { HsQwApply.STATUS_NORMAL, HsQwApply.STATUS_DISABLE, HsQwApply.STATUS_AUDIT });
        hsQwApply.setApplyMatter(HsQwApply.APPLY_MATTER_RENTAL);
        hsQwApply.setMainApplyer(new HsQwApplyer());
        hsQwApply.getMainApplyer().setUserId(UserUtils.getUser().getId());
        List<HsQwApply> list = hsQwApplyService.findList(hsQwApply);
        if (list.size() == 0) {
            return null;
        }
        return hsQwApplyService.get(this.getSingleInfo(list));
    }

    /**
     * 申请单获取顺序，待审批-已审批-已作废
     *
     * @param list
     * @return
     */
    private HsQwApply getSingleInfo(List<HsQwApply> list) {
        HsQwApply hasApply = null;
        HsQwApply inApply = null;
        HsQwApply disApply = null;

        for (HsQwApply hsQwApply : list) {
            if (hsQwApply.getStatus().equals(HsQwApply.STATUS_NORMAL)) {
                hasApply = hsQwApply;
            } else if (hsQwApply.getStatus().equals(HsQwApply.STATUS_DISABLE)) {
                disApply = hsQwApply;
            } else if (hsQwApply.getStatus().equals(HsQwApply.STATUS_AUDIT)) {
                inApply = hsQwApply;
            }
        }
        if (inApply != null) {
            return inApply;
        } else if (hasApply != null) {
            return hasApply;
        } else {
            return disApply;
        }
    }

    private Page<ApiHsBpmProcess> getProcessInfo(String bizKey) {
        try {
            FlowableProcess process = hsBpmCommonUtils.getFlowableProcess("rent_apply", bizKey);
            return this.returnProcess(process);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new Page<>();
    }

    private Page<ApiHsBpmProcess> returnProcess(FlowableProcess process) {
        Page<ApiHsBpmProcess> result = new Page<>();
        List<ApiHsBpmProcess> list = new ArrayList<>();
        process.getElements().forEach(e -> {
            if (e.getType().equals("UserTask") || e.getType().equals("StartEvent")) {
                ApiHsBpmProcess p = new ApiHsBpmProcess();
                p.setNodeName(e.getType().equals("StartEvent") ? "发起申请" : e.getName());
                boolean isCurrent = process.getCurrentActivities() == null ? false
                        : process.getCurrentActivities().contains(e.getId());
                p.setCurrentNode(isCurrent ? "1" : "0");
                boolean isAudited = process.getCurrentActivities() == null ? true
                        : process.getCompleteActivities().contains(e.getId());
                p.setNodeStatusDes(isAudited ? "审核通过" : "待审核");
                list.add(p);
            }
        });
        result.setList(list);
        result.setCount(list.size());
        return result;
    }

    /**
     * 获取工作单位信息接口
     *
     * @param apiHsOffice
     * @return
     */
    public Page<ApiHsOffice> officeList(ApiHsOffice apiHsOffice) {
        Office query = new Office();
        this.initPage(query, apiHsOffice);
        query.getPage().setPageSize(99999);
        query.sqlMap().getWhere().and("office_name", QueryType.LIKE, apiHsOffice.getDwmc());
        return this.findListWithMapping(() -> officeService.findList(query), k -> { // 传入转换逻辑
            ApiHsOffice data = new ApiHsOffice();
            data.setDwbm(k.getOfficeCode());
            data.setDwmc(k.getOfficeName());
            return data;
        });
    }

    /**
     * 6.3.11获取人员类型接口
     */
    public Page<ApiHsUser> userType(ApiHsUser apiHsUser) {
        List<ApiHsUser> list = new ArrayList<>();
        // todo
        ApiHsUser bean = new ApiHsUser();
        bean.setCode("JGJB");
        bean.setName("在编在职");
        list.add(bean);
        Page<ApiHsUser> result = new Page<>();
        result.setList(list);
        result.setCount(list.size());
        return result;
    }

    public Page<ApiHsPosition> position(ApiHsPosition apiHsPosition) {
        List<ApiHsPosition> list = new ArrayList<>();
        // todo
        ApiHsPosition bean = new ApiHsPosition();
        bean.setCode("JGJB");
        bean.setName("在编在职");
        list.add(bean);
        Page<ApiHsPosition> result = new Page<>();
        result.setList(list);
        result.setCount(list.size());
        return result;
    }

    public ApiHsManualIncome manualIncome(ApiHsManualIncome apiHsManualIncome) {
        // todo
        ApiHsManualIncome result = new ApiHsManualIncome();
        result.setYear(apiHsManualIncome.getYear());
        result.setZsr("100000");
        return result;
    }

    /**
     * 获取申请人配偶信息
     *
     * @param apiHsPartnerInfo 请求参数
     * @return ApiHsPartnerInfo 响应数据
     */
    public ApiHsPartnerInfo partnerInfo(ApiHsPartnerInfo apiHsPartnerInfo) {
        // todo
        ApiHsPartnerInfo result = new ApiHsPartnerInfo();
        result.setMzjh("35000000000000");
        result.setMxm("李四");
        result.setWzjh("35000000000001");
        result.setWxm("张三");
        return result;
    }

    /**
     * 获取家庭成员信息
     *
     * @param apiHsFamilyInfoList 请求参数
     * @return ApiHsFamilyInfoList 响应数据
     */
    public Page<ApiHsFamilyInfoList> familyInfoList(ApiHsFamilyInfoList apiHsFamilyInfoList) {
        List<ApiHsFamilyInfoList> list = new ArrayList<>();
        // todo
        ApiHsFamilyInfoList bean = new ApiHsFamilyInfoList();
        bean.setXm("张三");
        bean.setZjh("35011111111");
        bean.setRelation(0);
        list.add(bean);
        Page<ApiHsFamilyInfoList> result = new Page<>();
        result.setList(list);
        result.setCount(list.size());
        return result;
    }

    /**
     * 获取不动产信息
     *
     * @param apiHsEstateInfoList 请求参数
     * @return Page<ApiHsEstateInfoList> 响应数据
     */
    public Page<ApiHsEstateInfoList> estateInfoList(ApiHsEstateInfoList apiHsEstateInfoList) {
        List<ApiHsEstateInfoList> list = new ArrayList<>();
        // todo
        ApiHsEstateInfoList bean = new ApiHsEstateInfoList();
        bean.setFwdz("福州市");
        bean.setJzmj(100);
        bean.setCqxz("自由产权");
        list.add(bean);
        Page<ApiHsEstateInfoList> result = new Page<>();
        result.setList(list);
        result.setCount(list.size());
        return result;
    }

    /**
     * 6.3.17申请信息提交接口
     *
     * @param apiHsQwApply
     * @return
     */
    public String saveApply(ApiHsQwApply apiHsQwApply) {
        HsQwApply hsQwApply = null;
        // 配租确认单独处理，垃圾代码，前端不配合修改
        if (apiHsQwApply.getSqlx().equals("99")) {
            if (StringUtils.isEmpty(apiHsQwApply.getFczt())) {
                throw new ServiceException("配租确认信息未填写");
            }
            // 设置业务流程节点
            apiHsQwApply.setProcessName("配租复查确认");
            return this.saveProcessApply(apiHsQwApply).getId();
        } else {
            // 设置业务流程节点，新申请都是用户申请节点
            apiHsQwApply.setProcessName("用户申请");
        }

        hsQwApply = this.getHsQwApply(apiHsQwApply);
        hsQwApplyService.save(hsQwApply);
        return hsQwApply.getId();
    }

    private HsQwApply getHsQwApply(ApiHsQwApply apiHsQwApply) {
        HsQwApply hsQwApply = new HsQwApply();
        // 获取申请单信息
        if (apiHsQwApply.getSqlx().equals(HsQwApply.APPLY_MATTER_RENTAL)) {
            // 获取数据库对象
            if (StringUtils.isNotBlank(apiHsQwApply.getSqdh())) {
                hsQwApply = hsQwApplyService.get(apiHsQwApply.getSqdh());
                // 签订对象转换
                HsQwApply hsQwApplyR = HsBeanMapperUtil.convertBean(apiHsQwApply, HsQwApply.class);
                // 覆盖修改属性
                hsQwApply = RequestMergeUtil.mergeRequestData(hsQwApply, hsQwApplyR);
            } else {
                hsQwApply = HsBeanMapperUtil.convertBean(apiHsQwApply, HsQwApply.class);
                hsQwApply.setApplyMatter(HsQwApply.APPLY_MATTER_RENTAL);
            }
        } else if (apiHsQwApply.getSqlx().equals(HsQwApply.APPLY_MATTER_INFO)) {
            // 获取旧申请单信息
            HsQwApply history = hsQwApplyService.getOldApplyInfo(HsQwApply.APPLY_MATTER_INFO, hsQwApply);
            // 获取数据库对象
            hsQwApply = hsQwApplyService.get(apiHsQwApply.getSqdh());
            // 签订对象转换
            HsQwApply hsQwApplyR = HsBeanMapperUtil.convertBean(apiHsQwApply, HsQwApply.class);
            // 覆盖修改属性
            hsQwApply = RequestMergeUtil.mergeRequestData(hsQwApply, hsQwApplyR);
            hsQwApply.setApplyedId(history.getId());
            hsQwApply.setIsNewRecord(false);
            hsQwApply.setChangeReason(apiHsQwApply.getBgly());
        } else if (apiHsQwApply.getSqlx().equals(HsQwApply.APPLY_MATTER_REPLACEUP)) {
            hsQwApply = hsQwApplyService.getUserApplyInfo("2", hsQwApply);
            hsQwApply.setIsNewRecord(false);
            hsQwApply.setChangeReason(apiHsQwApply.getBgly());
        } else if (apiHsQwApply.getSqlx().equals(HsQwApply.APPLY_MATTER_REPLACE)) {
            hsQwApply = hsQwApplyService.getUserApplyInfo("4", hsQwApply);
            hsQwApply.setIsNewRecord(false);
            hsQwApply.setChangeReason(apiHsQwApply.getBgly());
        } else {
            // 获取数据库对象
            hsQwApply = hsQwApplyService.get(apiHsQwApply.getSqdh());
            // 签订对象转换
            HsQwApply hsQwApplyR = HsBeanMapperUtil.convertBean(apiHsQwApply, HsQwApply.class);
            // 覆盖修改属性
            hsQwApply = RequestMergeUtil.mergeRequestData(hsQwApply, hsQwApplyR);
        }
//        // 填充过程信息
//        BpmTask bpmTask = bpmTaskService.getTaskByBusinessKey("rent_apply", hsQwApply.getId(),
//                UserUtils.getUser().getUserCode());
//        if (bpmTask != null) {
//            BpmParams params = new BpmParams();
//            params.setTaskId(bpmTask.getId());
//            params.setProcInsId(bpmTask.getProcIns().getId());
//            params.setActivityId(bpmTask.getActivityId());
//            hsQwApply.setBpm(params);
//        }
//        if (!StringUtils.isBlank(hsQwApply.getBpm().getProcInsId())
//                && !StringUtils.isBlank(hsQwApply.getBpm().getTaskId())) {
//            this.claimTask(bpmTask);
//        }
        // 设置业务流程节点
        hsQwApply.setProcessName(apiHsQwApply.getProcessName());
        hsQwApply.setStatus(HsQwApply.STATUS_AUDIT);
        return hsQwApply;
    }

    /**
     * 获取配租申请已填写的信息
     *
     * @param apiHsQwApply
     * @return
     */
    public ApiHsQwApply applyInfo(ApiHsQwApply apiHsQwApply) {
        HsQwApply hsQwApply;
        if (apiHsQwApply.getSqlx().equals(HsQwApply.APPLY_MATTER_RENTAL)) {
            hsQwApply = this.getUserMainApply();
        } else {
            hsQwApply = this.getExistOrder(apiHsQwApply.getSqlx());
            if (hsQwApply == null) {
                hsQwApply = hsQwApplyService.getUserApplyInfoNullCheck(apiHsQwApply.getSqlx(), hsQwApply);
            } else {
                hsQwApply = hsQwApplyService.get(hsQwApply.getId());
            }
        }

        if (hsQwApply == null) {
            throw new ServiceException("没有已申请的公租申请单");
        }
        hsQwApply.setDataMap(this.getFileDataMap(hsQwApply.getId(), null));
        ApiHsQwApply result = HsBeanMapperUtil.convertBean(hsQwApply, ApiHsQwApply.class);
        return this.genApplyResult(result, hsQwApply);
    }

    private HsQwApply getExistOrder(String sqlx) {
        HsQwApply query = new HsQwApply();
        query.setApplyMatter(sqlx);
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setStatus_in(new String[] { HsQwApply.STATUS_NORMAL, HsQwApply.STATUS_AUDIT, HsQwApply.STATUS_DRAFT });
        query.sqlMap().getWhere().and("o.user_id", QueryType.EQ, UserUtils.getUser().getId());
        return hsQwApplyService.findList(query).stream().findFirst().orElse(null);
    }

    private ApiHsQwApply genApplyResult(ApiHsQwApply result, HsQwApply hsQwApply) {
        result.setSqdh(hsQwApply.getId());
        return result;
    }

    private Map<String, Object> getFileDataMap(String id, String bizType) {
        Map dataMap = new HashMap();
        List<FileUpload> fileUploads = this.getFileUploadList(id, bizType);
        fileUploads.forEach(fileUpload -> {
            if (dataMap.containsKey(fileUpload.getBizType())) {
                String value = dataMap.get(fileUpload.getBizType()).toString();
                dataMap.put(fileUpload.getBizType(), value + "," + fileUpload.getId());
            } else {
                dataMap.put(fileUpload.getBizType(), fileUpload.getId());
            }
        });
        return dataMap;
    }

    private List<FileUpload> getFileUploadList(String id, String bizType) {
        FileUpload query = new FileUpload();
        query.setBizKey(id);
        if (bizType != null) {
            query.setBizType(bizType);
        }
        return fileUploadService.findList(query);
    }

    /**
     * 6.3.22申请进度查询接口
     *
     * @param apiHsApplyProcessData
     * @return
     */
    public Page<ApiHsApplyProcessData> applyProcess(ApiHsApplyProcessData apiHsApplyProcessData) {
        HsQwApply query = new HsQwApply();
        this.initPage(query, apiHsApplyProcessData);
        query.setId(apiHsApplyProcessData.getSqdh());
        query.sqlMap().getWhere().and("apply_matter", QueryType.IN, new String[] { HsQwApply.APPLY_MATTER_RENTAL,
                HsQwApply.APPLY_MATTER_INFO, HsQwApply.APPLY_MATTER_REPLACEUP, HsQwApply.APPLY_MATTER_REPLACE });
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setStatus_in(new String[] { "0", "2", "4" });
        query.setMainApplyer(new HsQwApplyer());
        query.getMainApplyer().setUserId(UserUtils.getUser().getUserCode());
        return this.findPageWithMapping(() -> hsQwApplyService.findPage(query), k -> { // 传入转换逻辑
            ApiHsApplyProcessData data = new ApiHsApplyProcessData();
            data.setSqdh(k.getId());
            data.setSqrmc(k.getMainApplyer().getName());
            data.setSqrzjh(k.getMainApplyer().getIdNum());
            data.setSqsj(DateUtils.formatDate(k.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
            data.setSqlx(k.getApplyMatter());
            data.setSqzt(k.getStatus());
            this.setApplyProcess(data, k);
            return data;
        });
    }

    private void setApplyProcess(ApiHsApplyProcessData data, HsQwApply hsQwApply) {
        List<ApiHsProcessDetail> list = new ArrayList<>();
        List<FlowableTraceProcess> processList = hsBpmCommonUtils.getProcessList(this.getHsQwApplyFormKey(hsQwApply),
                hsQwApply.getId());
        if (processList!=null){
            processList.forEach(p -> {
                ApiHsProcessDetail detail = new ApiHsProcessDetail();
                detail.setHandleUser(p.getAssigneeInfo().substring(4));
                detail.setHandleOpion(p.getComment());
                detail.setProcessTime(p.getEndTime());
                detail.setHandleResult(p.getDeleteReason() == null ? p.getStatus() : "3");
                list.add(detail);
            });
            data.setProcessList(list);
        }

    }

    private String getHsQwApplyFormKey(HsQwApply hsQwApply) {
        if (hsQwApply.getFormKey() != null) {
            return hsQwApply.getFormKey();
        } else if (hsQwApply.getApplyMatter().equals(HsQwApply.APPLY_MATTER_RENTAL)) {
            return "rent_apply";
        } else if (hsQwApply.getApplyMatter().equals(HsQwApply.APPLY_MATTER_INFO)) {
            return "rent_apply_info";
        } else if (hsQwApply.getApplyMatter().equals(HsQwApply.APPLY_MATTER_REPLACEUP)) {
            return "rent_apply";
        } else if (hsQwApply.getApplyMatter().equals(HsQwApply.APPLY_MATTER_REPLACE)) {
            return "rent_apply_house";
        }
        return "rent_apply";
    }

    /**
     * 查询资格年审列表
     *
     * @param request 查询条件
     * @return 查询结果
     */
    public Page<ApiHsQualificationReviewData> queryQualificationList(ApiHsQualificationReviewData request) {
        HsQwCheckRecord query = new HsQwCheckRecord();
        this.initPage(query, request);
        query.getPage().setPageSize(99999);
        query.sqlMap().getWhere().and("hae.user_id", QueryType.EQ, UserUtils.getUser().getUserCode());
        query.setStatus_in(new String[] { "0", "4" });
        return this.findPageWithMapping(() -> commonBpmService.findObjectList(null, "rent_apply_check", query, "0"), j -> { // 传入转换逻辑
            HsQwCheckRecord k = (HsQwCheckRecord) j;
            if (k.getBpmStatus().equals("2")){
                k.setProcessName("已完成");
            }
            ApiHsQualificationReviewData data = new ApiHsQualificationReviewData();
            data.setHcbh(k.getId());
            data.setHcbt("对" + k.getHouse().getSimpleInfo() + "发起的核查");
            data.setHcsj(DateUtils.formatDate(k.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
            data.setHcfw(k.getHouse().getSimpleInfo());
            data.setHcjg(k.getViolation());
            data.setClzt(this.getClzt(k));
            data.setJgsm(this.getJgsm(k));
            data.setFwdz(k.getHouse().getEstate().getAddress());
            data.setMph(k.getHouse().getUnitNum());
            data.setQysj(DateUtils.getDateTime());
            data.setZysx("");
            data.setDqsj(DateUtils.formatDate(k.getCompact().getEndDate(), "yyyy-MM-dd HH:mm:ss"));
            data.setQtsj(this.getQtsj(k));
            return data;
        });
    }

    private String getQtsj(HsQwCheckRecord k) {
        if ((k.getRenewal()!=null && k.getRenewal().equals("1"))
                || (k.getComplaint()!=null && k.getComplaint().equals("1"))
                || (k.getReasonable()!=null && k.getReasonable().equals("1"))) {
            return DateUtils.formatDate(k.getUpdateDate(), "yyyy-MM-dd HH:mm:ss");
        }
        return "";
    }

    private String getJgsm(HsQwCheckRecord k) {
        StringBuffer sb = new StringBuffer();
        if (k.getRenewal()!=null){
            if (k.getRenewal().equals("0")) {
                sb.append("资格年审已续签");
            } else if (k.getRenewal().equals("1")) {
                sb.append("资格年审不续签，执行清退");
            }
        }
        if (k.getComplaint()!=null){
            if (k.getComplaint().equals("0")) {
                sb.append("，资格年审用户发起申诉");
            } else if (k.getComplaint().equals("1")) {
                sb.append("，资格年审用户不发起申诉，执行清退");
            }
        }
        if (k.getReasonable()!=null){
            if (k.getReasonable().equals("0")) {
                sb.append("，审核申诉合理");
            } else if (k.getReasonable().equals("1")) {
                sb.append("，审核申诉不合理，执行清退");
            }
        }
        return sb.toString();
    }

    private int getClzt(HsQwCheckRecord hsQwCheckRecord) {
        if(hsQwCheckRecord.getProcessName().equals("合同续签确认")){
            return 1;
        }
        if (hsQwCheckRecord.getProcessName().equals("核查结果确认")) {
            return 4;
        }
        if (hsQwCheckRecord.getRenewal()!=null){
            if (hsQwCheckRecord.getRenewal().equals("0")) {
                return 2;
            } else if (hsQwCheckRecord.getRenewal().equals("1")) {
                return 3;
            }
        }
        if (hsQwCheckRecord.getComplaint()!=null){
            if (hsQwCheckRecord.getComplaint().equals("0")) {
                return 5;
            } else if (hsQwCheckRecord.getComplaint().equals("1")) {
                return 6;
            }
        }
        return 0;
    }

    public HsQwApply saveProcessApply(ApiHsQwApply apiHsQwApply) {
        HsQwApply hsQwApply = hsQwApplyService.get(apiHsQwApply.getSqdh());
        apiHsQwApply.setSqlx(hsQwApply.getApplyMatter());
        hsQwApply.setOfflineRent(apiHsQwApply.getKfqrzt());
        hsQwApply.setApplyAccepted(apiHsQwApply.getCzqrzt());
        hsQwApply.setRecheckStatus(apiHsQwApply.getFczt());
        hsQwApply.setProcessName(apiHsQwApply.getProcessName());
        BpmTask bpmTask = bpmTaskService.getTaskByBusinessKey(this.getHsQwApplyFormKey(hsQwApply), hsQwApply.getId(),
                UserUtils.getUser().getUserCode());
        if (bpmTask != null) {
            this.claimTask(bpmTask);
            BpmParams params = new BpmParams();
            params.setTaskId(bpmTask.getId());
            params.setProcInsId(bpmTask.getProcIns().getId());
            params.setActivityId(bpmTask.getActivityId());
            hsQwApply.setBpm(params);
        }
        hsQwApplyService.save(hsQwApply);
        return hsQwApply;
    }

    private void claimTask(BpmTask bpmTask) {
        bpmTaskService.claimTask(bpmTask);
    }

    private ApiHsQualificationConfirm geneResponse(HsQwCheckRecord bean) {
        ApiHsQualificationConfirm result = new ApiHsQualificationConfirm();
        result.setQysj(DateUtils.getDateTime());
        result.setQydd("鼓楼区");
        result.setZysx("");
        return result;
    }

    /**
     * 资格年审保存接口
     *
     * @param bean
     * @return
     */
    public ApiHsQualificationConfirm saveProcessApply(HsQwCheckRecord bean) {
        BpmTask bpmTask = bpmTaskService.getTaskByBusinessKey("rent_apply_check", bean.getId(),
                UserUtils.getUser().getUserCode());
        if (bpmTask != null) {
            BpmParams params = new BpmParams();
            params.setTaskId(bpmTask.getId());
            params.setProcInsId(bpmTask.getProcIns().getId());
            params.setActivityId(bpmTask.getActivityId());
            bean.setBpm(params);
        }
        hsQwCheckRecordService.save(bean);
        return this.geneResponse(bean);
    }

    public ApiHsQualificationConfirm complaintDetail(ApiHsQualificationConfirm apiHsQualificationConfirm) {
        HsQwCheckRecord bean = hsQwCheckRecordService.get(apiHsQualificationConfirm.getHcbh());
        bean.setDataMap(this.getFileDataMap(bean.getId(), "hsQwCheck_appeal"));
        ApiHsQualificationConfirm result = HsBeanMapperUtil.convertBean(bean, ApiHsQualificationConfirm.class);
        return result;
    }

    public ApiHsHouseStatistics getHouseStatistics(ApiHsHouseStatistics request) {
        // 查询小区信息
        HsQwPublicRentalEstate queryEstate = new HsQwPublicRentalEstate();
        queryEstate.setId(request.getLpbh());
        HsQwPublicRentalEstate estate = hsQwPublicRentalEstateService.findList(queryEstate).stream().findFirst()
                .orElse(null);
        if (estate == null) {
            throw new ServiceException("编号对应的小区信息不存在！");
        }
        HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
        query.setEstateId(request.getLpbh());
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setHouseType("0");
        long djsfw = hsQwPublicRentalHouseService.findCount(query);
        query.setHouseType("1");
        long ljsfw = hsQwPublicRentalHouseService.findCount(query);
        query.setHouseType("2");
        long lgbfw = hsQwPublicRentalHouseService.findCount(query);
        return this.genHsHouseStatistics(estate, djsfw, ljsfw, lgbfw);
    }

    private ApiHsHouseStatistics genHsHouseStatistics(HsQwPublicRentalEstate estate, long djsfw, long ljsfw,
            long lgbfw) {
        ApiHsHouseStatistics result = new ApiHsHouseStatistics();
        result.setLpmc(estate.getName());
        result.setQhbm("");
        result.setLpdz(estate.getAddress());
        result.setDjsfw((int) djsfw);
        result.setLjsfw((int) ljsfw);
        result.setLgbfw((int) lgbfw);
        return result;
    }

    public Page<ApiHsHouseTemplateList> getHouseTemplateList(ApiHsHouseTemplateList request) {
        HsQwPublicRentalEstate query = new HsQwPublicRentalEstate();
        this.initPage(query, request);
        query.setPage(request.getPage());
        query.setId(request.getLpbh());
        query.setPage(request.getPage());
        query.setName(request.getKeyWord());
        query.setFylx(request.getFwlx());
        // 传入转换逻辑
        return this.findPageWithMapping(() -> hsQwPublicRentalEstateService.findStaticEstate(query),
                k -> this.getHouseTemplate(k, query));
    }

public Page<ApiHsPriceLimitHouseTemplateList> getPriceLimitHouseTemplateList(ApiHsPriceLimitHouseTemplateList request) {
        HsQwPublicRentalEstate query = new HsQwPublicRentalEstate();
        this.initPage(query, request);
        query.setPage(request.getPage());
        query.setId(request.getLpbh());
        query.setPage(request.getPage());
        query.setName(request.getKeyWord());
        // 传入转换逻辑
        return this.findPageWithMapping(() -> hsQwPublicRentalEstateService.findPriceLimitStaticEstate(query),
                k -> this.getPriceLimitHouseTemplate(k, query));
    }

    private ApiHsPriceLimitHouseTemplateList getPriceLimitHouseTemplate(HsQwPublicRentalEstate k, HsQwPublicRentalEstate query) {
        ApiHsPriceLimitHouseTemplateList data = HsBeanMapperUtil.convertBean(k, ApiHsPriceLimitHouseTemplateList.class);
        data.setLpbh(k.getId());
        data.setLpdz(k.getAddress());
        data.setQhbm(k.getArea());
        data.setFyxxList(this.getPriceLimitHouseTemplate(k.getId(), query));
        return data;
    }
    private List<ApiHsPriceLimitHouseTemplate> getPriceLimitHouseTemplate(String id, HsQwPublicRentalEstate query) {
        return hsQwPublicRentalHouseService.findPriceLimitHouseTemplateList(id, query);
    }

    private <T extends BaseEntity, R extends ApiBody> void initPage(T query, R request) {
        query.setPage(request.getPage());
        query.getPage().setPageNo(request.getPageIndex() != 0 ? request.getPageIndex() : 1);
        query.getPage().setPageSize(request.getPageSize() != null ? request.getPageSize() : 20);
    }

    private ApiHsHouseTemplateList getHouseTemplate(HsQwPublicRentalEstate k, HsQwPublicRentalEstate query) {
        ApiHsHouseTemplateList data = HsBeanMapperUtil.convertBean(k, ApiHsHouseTemplateList.class);
        data.setLpbh(k.getId());
        data.setXqgk(k.getRemarks());
        data.setZbpt(k.getSurroundingPlan());
        data.setQhbm(k.getArea());
        data.setFyxxList(this.getHouseTemplate(k.getId(), query));
        return data;
    }

    private List<ApiHsHouseTemplate> getHouseTemplate(String id, HsQwPublicRentalEstate query) {
        return hsQwPublicRentalHouseService.findHouseTemplateList(id, query);
    }

    public Page<ApiFile> planarList(ApiPlanar request) {
        FileUpload query = new FileUpload();
        this.initPage(query, request);
        query.getPage().setPageSize(99999);
        query.setBizKey(request.getFybh());
        query.setBizType("hsQwHouse_plane");
        return this.findListWithMapping(() -> fileUploadService.findList(query), k -> { // 传入转换逻辑
            ApiFile apiFile = new ApiFile();
            apiFile.setFileId(k.getId());
            apiFile.setFileType(k.getBizType());
            return apiFile;
        });
    }

    public ApiUserType userMangerType() {
        ApiUserType apiUserType = new ApiUserType();
        List<String> roleStrList = this.getUserRoleListStr();
        if (roleStrList.contains("FG_JG")) {
            apiUserType.setRylx("0");
            Employee user = employeeService.get(UserUtils.getUser().getId());
            apiUserType.setSsdw(user.getOffice().getOfficeCode());
            return apiUserType;
        }
        HsQwApply hsQwApply = this.getUserMainApply();
        if (hsQwApply != null) {
            apiUserType.setRylx("1");
            if(hsQwApply.getHsQwApplyHouse()!=null){
                apiUserType.setSsdw(hsQwApply.getHsQwApplyHouse().getSimpleInfo());
            }
        } else {
            apiUserType.setRylx("2");
        }
        return apiUserType;
    }

    private List<String> getUserRoleListStr() {
        List<String> roleStrList = new ArrayList<>();
        for (Role userRole : UserUtils.getUser().getRoleList()) {
            roleStrList.add(userRole.getRoleCode());
        }
        return roleStrList;
    }

    public Page<ApiHsCheckRecord> checkHouseList(ApiHsCheckRecord apiHsCheckRecord) {
        HsQwApply query = HsBeanMapperUtil.convertBean(apiHsCheckRecord, HsQwApply.class);
        query.sqlMap().getWhere().and("p.name", QueryType.LIKE, apiHsCheckRecord.getLpmc());
        query.sqlMap().getWhere().and("h.unit_num", QueryType.LIKE, apiHsCheckRecord.getDyh());
        this.initPage(query, apiHsCheckRecord);
        if (apiHsCheckRecord.getHylx().equals("0")) {
            query.setDataType("4");
        } else {
            query.setDataType("5");
        }
        return this.findPageWithMapping(() -> hsQwApplyService.getApplyedPage(query), // 传入分页查询方法
                k -> { // 传入转换逻辑
                    ApiHsCheckRecord checkRecord = new ApiHsCheckRecord();
                    checkRecord.setFybh(k.getHsQwApplyHouse().getId());
                    checkRecord.setLpmc(k.getHsQwApplyHouse().getEstate().getName());
                    checkRecord.setLpdz(k.getHsQwApplyHouse().getEstate().getAddress());
                    checkRecord.setLh(k.getHsQwApplyHouse().getBuildingNum());
                    checkRecord.setDyh(k.getHsQwApplyHouse().getUnitNum());
                    checkRecord.setJzmj(k.getHsQwApplyHouse().getBuildingArea());
                    return checkRecord;
                });
    }

    public Page<ApiHsCheckObject> objectList(ApiHsCheckObject apiHsCheckObject) {
        HsQwManagementCheckObject query = new HsQwManagementCheckObject();
        this.initPage(query, apiHsCheckObject);
        query.setName(apiHsCheckObject.getWpmc());
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setStatus("0");
        return this.findPageWithMapping(() -> hsQwManagementCheckObjectService.findPage(query), // 传入分页查询方法
                k -> { // 传入转换逻辑
                    ApiHsCheckObject object = new ApiHsCheckObject();
                    object.setWpbh(k.getId());
                    object.setWpmc(k.getName());
                    return object;
                });
    }

    public String checkSave(ApiHsCheckInfo apiHsCheckInfo) {
        HsQwManagementCheck check = HsBeanMapperUtil.convertBean(apiHsCheckInfo, HsQwManagementCheck.class);
        HsQwApply hsQwApply = hsQwApplyService.getByApplyedHouse(apiHsCheckInfo.getFybh());
        if (hsQwApply == null) {
            throw new ServiceException("未查询到申请单信息，请验证输入房源编号是否已配租！");
        }
        check.setApplyId(hsQwApply.getId());
        check.setStatus(HsQwManagementCheck.STATUS_AUDIT);
        check.setFormKey(this.getManagementFormKey(apiHsCheckInfo.getHylx()));
        return this.checkSaveBase(hsQwApply, check, apiHsCheckInfo);
    }

    private String checkSaveBase(HsQwApply hsQwApply, HsQwManagementCheck check, ApiHsCheckInfo apiHsCheckInfo) {
        BpmParams params = new BpmParams();
        BpmTask bpmTask = bpmTaskService.getTaskByBusinessKey(check.getFormKey(), check.getId(),
                UserUtils.getUser().getUserCode());
        if (bpmTask != null) {
            params.setTaskId(bpmTask.getId());
            params.setProcInsId(bpmTask.getProcIns().getId());
            params.setComment(apiHsCheckInfo.getFkms());
            params.setActivityId(bpmTask.getActivityId());
            check.setBpm(params);
            // 先做任务签收
            this.claimTask(bpmTask);
        }

        if (apiHsCheckInfo.getShlx() != null && apiHsCheckInfo.getShlx().equals("0")) {
            List<BpmBackActivity> backActivities = bpmTaskService.getBackActivity(bpmTask);
            if (backActivities == null || backActivities.size() == 0) {
                throw new ServiceException("没有上一个流程节点，请确认操作！");
            }
            BpmBackActivity activity = backActivities.get(backActivities.size() - 1);
            BpmTask backTask = new BpmTask();
            backTask.setAssignee(activity.getAssignee());
            backTask.setActivityId(activity.getActivityId());
            backTask.setId(bpmTask.getId());
            backTask.setComment(apiHsCheckInfo.getShms());
            bpmTaskService.backTask(backTask);
            return apiHsCheckInfo.getHybh();
        } else {
            check.setHsQwApply(hsQwApply);
            check.setApplyId(hsQwApply.getId());
            hsQwManagementCheckService.save(check);
            return check.getId();
        }
    }

    private String getManagementFormKey(String hylx) {
        if(hylx == null){
            return "management_check%";
        }
        if (hylx.equals("0")) {
            return "management_check_in";
        } else if (hylx.equals("1")) {
            return "management_check_out";
        }
        throw new ServiceException("核验类型不可为空！");
    }

    public Page<ApiHsCheckRecord> checkRecordList(ApiHsCheckRecord apiHsCheckRecord) {
        HsQwManagementCheck query = HsBeanMapperUtil.convertBean(apiHsCheckRecord, HsQwManagementCheck.class);
        this.initPage(query, apiHsCheckRecord);
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setFormKey(this.getManagementFormKey(apiHsCheckRecord.getHylx()));
        query.setCheckType(apiHsCheckRecord.getHylx());
        query.setBpmStatus(this.getCheckStatus(apiHsCheckRecord));
        query.sqlMap().getWhere().and("check_type", QueryType.IN, new String[]{"0", "1"});
        if (StringUtils.isNotBlank(apiHsCheckRecord.getLpmc())){
            query.sqlMap().getWhere().and("p.name", QueryType.LIKE, apiHsCheckRecord.getLpmc());
        }
//        query.setProcessName(this.getHylc(null, apiHsCheckRecord.getHylc()));
        query.sqlMap().getWhere().and("bpmt.TASK_NAME", QueryType.EQ, this.getHylc(null, apiHsCheckRecord.getHylc()));
        query.sqlMap().getWhere().and("ha.id", QueryType.IS_NOT_NULL, null);
//        if (!this.userMangerType().equals("0")){
//            query.sqlMap().getWhere().and("o.user_id", QueryType.EQ, UserUtils.getUser().getId());
//        }
        return this.findPageWithMapping(() -> commonBpmService.findObjectList(null, query.getFormKey(), query,
                        query.getBpmStatus()), // 传入分页查询方法
                k -> { // 传入转换逻辑
                    ApiHsCheckRecord data = this.getApiHsCheckRecodeDetail(k);
                    return data;
                });
    }

    private String getCheckStatus(ApiHsCheckRecord apiHsCheckRecord) {
        if (apiHsCheckRecord.getHyzt() == null) {
            return null;
        }
        if (apiHsCheckRecord.getHyzt().equals("0")) {
            return "2";
        }
        if (apiHsCheckRecord.getHyzt().equals("1")) {
            return "1";
        }
        return null;
    }

    private List<ApiHsCheckProcess> getLcProcessList(String id, String formKey) {
        List<ApiHsCheckProcess> list = new ArrayList<>();
        List<FlowableTraceProcess> processList = hsBpmCommonUtils.getProcessList(formKey, id);
        List<FileUpload> fileUploads = this.getFileUploadList(id, null);
        if (processList == null) {
            return null;
        }
        for (FlowableTraceProcess p : processList) {
            ApiHsCheckProcess detail = new ApiHsCheckProcess();
            String hylc = this.getHylc(p.getName(), null);
            detail.setHylc(hylc);
            detail.setCzsj(p.getEndTime());
            detail.setFkms(p.getComment());
            detail.setFileList(this.setByHylc(hylc, fileUploads));
            list.add(detail);
        }
        return list;
    }

    private List<ApiFile> setByHylc(String hylc, List<FileUpload> fileUploads) {
        List<ApiFile> files = new ArrayList<>();
        fileUploads.forEach(f -> {
            ApiFile apiFile = new ApiFile();
            if (f.getBizType().endsWith("_" + hylc)) {
                apiFile.setFileId(f.getId());
                apiFile.setFileType(f.getBizType());
                files.add(apiFile);
            }
        });
        return files;
    }

    private List<ApiHsCheckDetai> getCheckObjectDetail(String id) {
        List<ApiHsCheckDetai> list = new ArrayList<>();
        // 查询所有核查详单
        HsQwManagementCheckDetail query = new HsQwManagementCheckDetail();
        query.setCheckId(id);
        List<HsQwManagementCheckDetail> detailList = hsQwManagementCheckDetailService.findList(query);
        for (HsQwManagementCheckDetail detail : detailList) {
            detail.setObjectName(detail.getCheckObject().getName());
            ApiHsCheckDetai data = HsBeanMapperUtil.convertBean(detail, ApiHsCheckDetai.class);
            data.setBz(detail.getRemarks());
            list.add(data);
        }
        return list;
    }

    private ApiHsCheckInfo getApiHsCheckRecodeDetail(HsQwManagementCheck k) {
        ApiHsCheckInfo data = new ApiHsCheckInfo();
        data.setHybh(k.getId());
        if (k.getHsQwApply().getHsQwApplyHouse() != null) {
            data.setFybh(k.getHsQwApply().getHsQwApplyHouse().getId());
            if (k.getHsQwApply().getHsQwApplyHouse().getEstate() != null) {
                data.setLpmc(k.getHsQwApply().getHsQwApplyHouse().getEstate().getName());
                data.setLpdz(k.getHsQwApply().getHsQwApplyHouse().getEstate().getAddress());
            }
            data.setLh(k.getHsQwApply().getHsQwApplyHouse().getBuildingNum());
            data.setDyh(k.getHsQwApply().getHsQwApplyHouse().getUnitNum());
            data.setJzmj(k.getHsQwApply().getHsQwApplyHouse().getBuildingArea());
            data.setFwlx(k.getHsQwApply().getHsQwApplyHouse().getHouseType());
        }
        data.setHylx(k.getCheckType());
        data.setHyzt(k.getBpmStatus().equals("2") ? "0" : k.getBpmStatus());
        data.setHylc(this.getHylc(k.getProcessName(), null));
        data.setSbds(k.getWaterFee());
        data.setDbds(k.getEletricFee());
        data.setRqds(k.getGasFee());
        return data;
    }

    private String getHylc(String processName, String hylc) {
        AtomicReference<String> value = new AtomicReference<>("");
        statusMap.forEach((k, v) -> {
            if (processName != null && processName.equals(k)) {
                value.set(v);
            }
            if (hylc != null && hylc.equals(v)) {
                value.set(k);
            }
        });
        return value.get();
    }

    public ApiHsCheckInfo checkDetail(ApiHsCheckRecord apiHsCheckRecord) {
        HsQwManagementCheck query = hsQwManagementCheckService.get(apiHsCheckRecord.getHybh());
        query.setPage(new Page<>());
        Page<HsQwManagementCheck> hsQwManagementCheckList = commonBpmService.findObjectList(null, query.getFormKey(), query, null);
        if (query == null || hsQwManagementCheckList.getCount() == 0) {
            throw new ServiceException("核验单不存在！");
        }
        HsQwManagementCheck hsQwManagementCheck = hsQwManagementCheckList.getList().stream().findFirst().get();
        ApiHsCheckInfo data = this.getApiHsCheckRecodeDetail(hsQwManagementCheck);
        data.setWpjlList(this.getCheckObjectDetail(hsQwManagementCheck.getId()));
        data.setLcxxList(this.getLcProcessList(hsQwManagementCheck.getId(), hsQwManagementCheck.getFormKey()));
        return data;
    }

    public String checkAudit(ApiHsCheckInfo apiHsCheckInfo) {
        apiHsCheckInfo.setId(apiHsCheckInfo.getHybh());
        HsQwManagementCheck check = hsQwManagementCheckService.get(apiHsCheckInfo.getHybh());
        if (check == null) {
            throw new ServiceException("未查询到核验单信息，请验证输入核验编号是否已配租！");
        }
        HsQwApply hsQwApply = hsQwApplyService.getByApplyedHouse(check.getHsQwApply().getHouseId());
        if (hsQwApply == null) {
            throw new ServiceException("未查询到申请单信息，请验证输入房源编号是否已配租！");
        }
        check = HsBeanMapperUtil.convertBean(apiHsCheckInfo,HsQwManagementCheck.class, check);
        return this.checkSaveBase(hsQwApply, check, apiHsCheckInfo);
    }

    public Page<ApiHsManagementFee> managementFeeList(ApiHsManagementFee apiHsManagementFee) {
        HsQwRentalFee query = new HsQwRentalFee();
        this.initPage(query, apiHsManagementFee);
        query.setUserId(UserUtils.getUser().getId());
        query.setFeeDate_gte(DateUtils.parseDate(apiHsManagementFee.getJfkssj()));
        query.setFeeDate_lte(DateUtils.parseDate(apiHsManagementFee.getJfjssj()));
        query.setFeeType(apiHsManagementFee.getJflx());
        query.setStatus(apiHsManagementFee.getJfzt());
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        return this.findPageWithMapping(() -> hsQwRentalFeeService.findPage(query), k -> { // 传入转换逻辑
            ApiHsManagementFee data = new ApiHsManagementFee();
            data.setId(k.getId());
            data.setJflx(k.getFeeType());
            data.setJfsj(DateUtils.formatDateTime(k.getFeeDate()));
            data.setJfzq(k.getFeeMonth());
            data.setJfje(k.getRentalFee() + "");
            data.setJffw(k.getHsQwApply().getHsQwApplyHouse().getSimpleInfo());
            data.setJfzt(k.getStatus());
            return data;
        });
    }

    public static <T, R, P> Page<R> findPageWithMapping(Supplier<Page<T>> findPageFunction,
            Function<T, R> mappingFunction) {

        Page<R> result = new Page<>();
        List<R> list = new ArrayList<>();
        // 调用 findPageFunction 获取分页数据
        Page<T> beans = findPageFunction.get();

        // 通过 mappingFunction 进行转换
        beans.getList().forEach(k -> {
            if (k == null) {
                return;
            }
            list.add(mappingFunction.apply(k));
        });
        result.setCount(beans.getCount());
        result.setList(list);
        return result;
    }

    public static <T, R, P> Page<R> findListWithMapping(Supplier<List<T>> findPageFunction,
            Function<T, R> mappingFunction) {

        Page<R> result = new Page<>();
        List<R> list = new ArrayList<>();
        // 调用 findPageFunction 获取分页数据
        List<T> beans = findPageFunction.get();

        // 通过 mappingFunction 进行转换
        beans.forEach(k -> list.add(mappingFunction.apply(k)));
        result.setCount(beans.size());
        result.setList(list);
        return result;
    }

    public ApiHsCheckInfo checkProcess(ApiHsCheckInfo apiHsCheckInfo) {
        HsQwManagementCheck query = new HsQwManagementCheck();
        this.initPage(query, apiHsCheckInfo);
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setFormKey("management_check%");
        query.setProcessName(this.getHylc(null, apiHsCheckInfo.getHylc()));
        query.sqlMap().getWhere().and("check_type", QueryType.IN, new String[] { "0", "1" });
        return this.findPageWithMapping(
                () -> hsQwManagementCheckService.findApplyPageByTask(query, new String[] { "租户入住核验审核", "租户退租申请确认" },
                        "1"), // 传入分页查询方法
                k -> { // 传入转换逻辑
                    ApiHsCheckInfo data = this.getApiHsCheckRecodeDetail(k);
                    data.setWpjlList(this.getCheckObjectDetail(k.getId()));
                    data.setLcxxList(this.getLcProcessList(k.getId(), k.getFormKey()));
                    return data;
                }).getList().stream().findFirst().orElse(null);
    }
}
