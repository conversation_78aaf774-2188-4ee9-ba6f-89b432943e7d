package com.hsobs.hs.modules.applyhis.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * hs_qw_apply_hisEntity
 * <AUTHOR>
 * @version 2025-07-12
 */
@Table(name="hs_qw_apply_his", alias="a", label="hs_qw_apply_his信息", columns={
		@Column(name="id", attrName="id", label="id", isPK=true),
		@Column(name="apply_id", attrName="applyId", label="apply_id"),
		@Column(name="family_peoples", attrName="familyPeoples", label="family_peoples"),
		@Column(name="family_income", attrName="familyIncome", label="family_income"),
		@Column(name="apply_matter", attrName="applyMatter", label="apply_matter"),
		@Column(includeEntity=DataEntity.class),
		@Column(name="house_id", attrName="houseId", label="house_id"),
		@Column(name="office_code", attrName="officeCode", label="office_code"),
		@Column(name="avg_area", attrName="avgArea", label="avg_area"),
		@Column(name="avg_income", attrName="avgIncome", label="avg_income"),
		@Column(name="apply_time", attrName="applyTime", label="apply_time", isUpdateForce=true),
		@Column(name="apply_score", attrName="applyScore", label="apply_score", isUpdateForce=true),
		@Column(name="apply_accepted", attrName="applyAccepted", label="apply_accepted"),
		@Column(name="applyed_id", attrName="applyedId", label="applyed_id"),
		@Column(name="change_reason", attrName="changeReason", label="change_reason"),
		@Column(name="eligible", attrName="eligible", label="eligible"),
		@Column(name="recheck_status", attrName="recheckStatus", label="recheck_status"),
		@Column(name="recheck_audit", attrName="recheckAudit", label="recheck_audit"),
		@Column(name="offline_rent", attrName="offlineRent", label="offline_rent"),
		@Column(name="rent_time", attrName="rentTime", label="rent_time", isUpdateForce=true),
		@Column(name="priority_order", attrName="priorityOrder", label="priority_order", isUpdateForce=true),
		@Column(name="score_update", attrName="scoreUpdate", label="score_update"),
		@Column(name="apply_score_pre", attrName="applyScorePre", label="apply_score_pre", isUpdateForce=true),
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyHis extends DataEntity<HsQwApplyHis> {
	
	private static final long serialVersionUID = 1L;
	private String applyId;		// apply_id
	private String familyPeoples;		// family_peoples
	private String familyIncome;		// family_income
	private String applyMatter;		// apply_matter
	private String houseId;		// house_id
	private String officeCode;		// office_code
	private String avgArea;		// avg_area
	private String avgIncome;		// avg_income
	private Date applyTime;		// apply_time
	private Long applyScore;		// apply_score
	private String applyAccepted;		// apply_accepted
	private String applyedId;		// applyed_id
	private String changeReason;		// change_reason
	private String eligible;		// eligible
	private String recheckStatus;		// recheck_status
	private String recheckAudit;		// recheck_audit
	private String offlineRent;		// offline_rent
	private Date rentTime;		// rent_time
	private Long priorityOrder;		// priority_order
	private String scoreUpdate;		// score_update
	private Long applyScorePre;		// apply_score_pre

	public HsQwApplyHis() {
		this(null);
	}
	
	public HsQwApplyHis(String id){
		super(id);
	}
	
	@NotBlank(message="apply_id不能为空")
	@Size(min=0, max=64, message="apply_id长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@NotBlank(message="family_peoples不能为空")
	@Size(min=0, max=20, message="family_peoples长度不能超过 20 个字符")
	public String getFamilyPeoples() {
		return familyPeoples;
	}

	public void setFamilyPeoples(String familyPeoples) {
		this.familyPeoples = familyPeoples;
	}
	
	@NotBlank(message="family_income不能为空")
	@Size(min=0, max=20, message="family_income长度不能超过 20 个字符")
	public String getFamilyIncome() {
		return familyIncome;
	}

	public void setFamilyIncome(String familyIncome) {
		this.familyIncome = familyIncome;
	}
	
	@NotBlank(message="apply_matter不能为空")
	@Size(min=0, max=255, message="apply_matter长度不能超过 255 个字符")
	public String getApplyMatter() {
		return applyMatter;
	}

	public void setApplyMatter(String applyMatter) {
		this.applyMatter = applyMatter;
	}
	
	@Size(min=0, max=64, message="house_id长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}
	
	@NotBlank(message="office_code不能为空")
	@Size(min=0, max=64, message="office_code长度不能超过 64 个字符")
	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
	
	@Size(min=0, max=100, message="avg_area长度不能超过 100 个字符")
	public String getAvgArea() {
		return avgArea;
	}

	public void setAvgArea(String avgArea) {
		this.avgArea = avgArea;
	}
	
	@Size(min=0, max=100, message="avg_income长度不能超过 100 个字符")
	public String getAvgIncome() {
		return avgIncome;
	}

	public void setAvgIncome(String avgIncome) {
		this.avgIncome = avgIncome;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}
	
	public Long getApplyScore() {
		return applyScore;
	}

	public void setApplyScore(Long applyScore) {
		this.applyScore = applyScore;
	}
	
	@Size(min=0, max=1, message="apply_accepted长度不能超过 1 个字符")
	public String getApplyAccepted() {
		return applyAccepted;
	}

	public void setApplyAccepted(String applyAccepted) {
		this.applyAccepted = applyAccepted;
	}
	
	@Size(min=0, max=64, message="applyed_id长度不能超过 64 个字符")
	public String getApplyedId() {
		return applyedId;
	}

	public void setApplyedId(String applyedId) {
		this.applyedId = applyedId;
	}
	
	@Size(min=0, max=255, message="change_reason长度不能超过 255 个字符")
	public String getChangeReason() {
		return changeReason;
	}

	public void setChangeReason(String changeReason) {
		this.changeReason = changeReason;
	}
	
	@Size(min=0, max=255, message="eligible长度不能超过 255 个字符")
	public String getEligible() {
		return eligible;
	}

	public void setEligible(String eligible) {
		this.eligible = eligible;
	}
	
	@Size(min=0, max=1, message="recheck_status长度不能超过 1 个字符")
	public String getRecheckStatus() {
		return recheckStatus;
	}

	public void setRecheckStatus(String recheckStatus) {
		this.recheckStatus = recheckStatus;
	}
	
	@Size(min=0, max=1, message="recheck_audit长度不能超过 1 个字符")
	public String getRecheckAudit() {
		return recheckAudit;
	}

	public void setRecheckAudit(String recheckAudit) {
		this.recheckAudit = recheckAudit;
	}
	
	@Size(min=0, max=1, message="offline_rent长度不能超过 1 个字符")
	public String getOfflineRent() {
		return offlineRent;
	}

	public void setOfflineRent(String offlineRent) {
		this.offlineRent = offlineRent;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getRentTime() {
		return rentTime;
	}

	public void setRentTime(Date rentTime) {
		this.rentTime = rentTime;
	}
	
	public Long getPriorityOrder() {
		return priorityOrder;
	}

	public void setPriorityOrder(Long priorityOrder) {
		this.priorityOrder = priorityOrder;
	}
	
	@Size(min=0, max=1, message="score_update长度不能超过 1 个字符")
	public String getScoreUpdate() {
		return scoreUpdate;
	}

	public void setScoreUpdate(String scoreUpdate) {
		this.scoreUpdate = scoreUpdate;
	}
	
	public Long getApplyScorePre() {
		return applyScorePre;
	}

	public void setApplyScorePre(Long applyScorePre) {
		this.applyScorePre = applyScorePre;
	}
	
}