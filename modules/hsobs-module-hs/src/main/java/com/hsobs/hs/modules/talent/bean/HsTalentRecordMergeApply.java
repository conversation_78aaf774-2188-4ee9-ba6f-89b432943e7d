package com.hsobs.hs.modules.talent.bean;

import com.jeesite.common.entity.DataEntity;

/**
 * <AUTHOR>
 */
public class HsTalentRecordMergeApply extends DataEntity<HsTalentRecordMergeApply> {

    private Integer applyStatus;
    /** 1:补助申请 2:停发报备 */
    private Integer type; // 1:补助申请 2:停发报备
    private String remark;
    private Integer isView = 1;

    public Integer getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsView() {
        return isView;
    }

    public void setIsView(Integer isView) {
        this.isView = isView;
    }

    @Override
    public String toString() {
        return "HsTalentRecordMergeApply{" +
                "applyStatus=" + applyStatus +
                ", type=" + type +
                ", remark='" + remark + '\'' +
                ", isView=" + isView +
                '}';
    }
}
