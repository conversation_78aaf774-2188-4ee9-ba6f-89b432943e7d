package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 获取具有访问对应系统权限的角色列表响应实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleSystemAccessData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String roleId;
    private String roleName;
    private String roleCode;
    private String intro;

}
