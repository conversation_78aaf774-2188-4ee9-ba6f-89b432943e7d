package com.hsobs.hs.modules.checkrecord.service;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.hsobs.hs.modules.checkrecord.dao.HsQwCheckRecordDao;
import com.hsobs.hs.modules.checkrecord.entity.HsQwCheckRecord;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.hsobs.hs.modules.formalarm.dao.HsQwFormAlarmDao;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import com.hsobs.hs.modules.formalarm.service.HsQwFormAlarmService;
import com.hsobs.hs.modules.managementcheck.entity.HsQwManagementCheck;
import com.hsobs.hs.modules.managementcheck.service.HsQwManagementCheckService;
import com.hsobs.hs.modules.utils.HsBpmService;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 租赁资格核查台账表Service
 *
 * <AUTHOR>
 * @version 2025-02-15
 */
@Service
public class HsQwCheckRecordService extends CrudService<HsQwCheckRecordDao, HsQwCheckRecord> {

    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private BpmTaskService bpmTaskService;

    @Autowired
    private HsQwCompactService hsQwCompactService;

    @Autowired
    private HsQwManagementCheckService hsQwManagementCheckService;

    @Autowired
    private HsQwApplyerService hsQwApplyerService;

    @Autowired
    private HsQwFormAlarmService hsQwFormAlarmService;

    @Autowired
    private HsQwFormAlarmDao hsQwFormAlarmDao;

    private HsBpmService<HsQwCheckRecord> hsBpmService;



    @PostConstruct
    public void init() {
        // 在注入后对 hsBpmService 的属性进行设置
        hsBpmService = new HsBpmService<>(HsQwCheckRecord.class);
        hsBpmService.setCrudService(this);
    }

    /**
     * 获取单条数据
     *
     * @param hsQwCheckRecord
     * @return
     */
    @Override
    public HsQwCheckRecord get(HsQwCheckRecord hsQwCheckRecord) {
        return super.get(hsQwCheckRecord);
    }

    /**
     * 查询分页数据
     *
     * @param hsQwCheckRecord 查询条件
     * @param hsQwCheckRecord page 分页对象
     * @return
     */
    @Override
    public Page<HsQwCheckRecord> findPage(HsQwCheckRecord hsQwCheckRecord) {
        return super.findPage(hsQwCheckRecord);
    }

    /**
     * 查询列表数据
     *
     * @param hsQwCheckRecord
     * @return
     */
    @Override
    public List<HsQwCheckRecord> findList(HsQwCheckRecord hsQwCheckRecord) {
        return super.findList(hsQwCheckRecord);
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param hsQwCheckRecord
     */
    @Override
    @Transactional
    public void save(HsQwCheckRecord hsQwCheckRecord) {
        // 如果未设置状态，则指定状态为审核状态，以提交审核流程
        if (StringUtils.isBlank(hsQwCheckRecord.getStatus())) {
            hsQwCheckRecord.setStatus(HsQwCheckRecord.STATUS_AUDIT);
        }

        // 如果状态为正常，则代表不正常操作，可能前端进行了数据参数修改
        if (HsQwCheckRecord.STATUS_NORMAL.equals(hsQwCheckRecord.getStatus())) {
            throw new ServiceException(text("非法操作，前端数据被劫持！"));
        }

        // 如果状态为草稿或审核状态，才可以保存业务数据
        if (HsQwCheckRecord.STATUS_DRAFT.equals(hsQwCheckRecord.getStatus())
                || HsQwCheckRecord.STATUS_AUDIT.equals(hsQwCheckRecord.getStatus())) {
            super.save(hsQwCheckRecord);
        }

        // 重新进行资格核验
        this.recheckHsQwApply(hsQwCheckRecord.getApplyId());
        this.saveFile(hsQwCheckRecord);

        // 如果为审核状态，则进行审批流操作
        if (HsQwCheckRecord.STATUS_AUDIT.equals(hsQwCheckRecord.getStatus())) {
            // 指定流程变量，作为流程条件，决定流转方向
            Map<String, Object> variables = MapUtils.newHashMap();
            variables.put("officeCode", hsQwCheckRecord.getOfficeCode());
            variables.put("violation", hsQwCheckRecord.getViolation());
            variables.put("renewal", hsQwCheckRecord.getRenewal());
            variables.put("complaint", hsQwCheckRecord.getComplaint());
            variables.put("reasonable", hsQwCheckRecord.getReasonable());
            variables.put("applyer", hsQwCheckRecord.getHsQwApply().getMainApplyer().getUserId());

            // 如果流程实例为空，任务编号也为空，则：启动流程
            if (StringUtils.isBlank(hsQwCheckRecord.getBpm().getProcInsId())
                    && StringUtils.isBlank(hsQwCheckRecord.getBpm().getTaskId())) {
                //进行资格条件的核查
//				this.applyRuleCheck(hsQwCheckRecord);
                BpmUtils.start(hsQwCheckRecord, "rent_apply_check", variables, null);
            }
            // 如果有任务信息，则：提交任务
            else {
                BpmUtils.complete(hsQwCheckRecord, variables, null);
            }
        }
    }

    /**
     * 对资格核查申请单重新进行资格核验
     * @param applyId
     */
    private void recheckHsQwApply(String applyId) {
        HsQwApply hsQwApply = hsQwApplyService.get(applyId);
        Map<String, HsQwFormAlarm> hsQwFormAlarmMap = new HashMap<>();
        // 资格核验
        hsQwApplyService.applyRuleCheck(hsQwApply, hsQwFormAlarmMap);
        if (hsQwFormAlarmMap.size() > 0) {
            // 先删除
            HsQwFormAlarm where = new HsQwFormAlarm();
            where.setObjectId(hsQwApply.getId());
            HsQwFormAlarm entity = new HsQwFormAlarm();
            entity.setStatus(HsQwFormAlarm.STATUS_DELETE);
            hsQwFormAlarmDao.updateStatusByEntity(entity, where);
            // 解析出map中的key，value，批量插入数据库，整合成list
            List<HsQwFormAlarm> list = new ArrayList<>();
            hsQwFormAlarmMap.forEach((k, v) -> {
                list.add(v);
            });
            hsQwFormAlarmService.saveBatch(list);
        }
    }


    private void saveFile(HsQwCheckRecord hsQwCheckRecord) {
        // 保存附件
        FileUploadUtils.saveFileUpload(hsQwCheckRecord, hsQwCheckRecord.getId(), "hsQwCheck_appeal");
    }

    /**
     * 更新状态
     *
     * @param hsQwCheckRecord
     */
    @Override
    @Transactional
    public void updateStatus(HsQwCheckRecord hsQwCheckRecord) {
        super.updateStatus(hsQwCheckRecord);
    }

    /**
     * 删除数据
     *
     * @param hsQwCheckRecord
     */
    @Override
    @Transactional
    public void delete(HsQwCheckRecord hsQwCheckRecord) {
        super.delete(hsQwCheckRecord);
    }

    /**
     * 根据选择的房源信息进行资格单生成
     *
     * @param houseIdStr
     */
    @Transactional
    public void recordSave(String houseIdStr,String objectIdStr, Date checkDate, String violationType) {
        //执行在途核验单检查
        this.checkExist(houseIdStr);
        HsQwApply query = new HsQwApply();
        query.sqlMap().getWhere().and("house_id", QueryType.IN, houseIdStr.split(","));
        query.setStatus(HsQwApply.STATUS_NORMAL);
        query.setApplyMatter(HsQwApply.APPLY_MATTER_RENTAL);
        List<HsQwApply> applyList = hsQwApplyService.findList(query);
        applyList.forEach(apply -> {
            HsQwCheckRecord checkRecord = new HsQwCheckRecord();
            checkRecord.setApplyId(apply.getId());
            if (apply.getCompact() != null) {
                checkRecord.setCompactId(apply.getCompact().getId());
            } else {
                throw new ServiceException("该申请单未包含合同信息，请核查申请单！");
            }
            checkRecord.setHouseId(apply.getHouseId());
            checkRecord.setOfficeCode(apply.getOfficeCode());
            checkRecord.setHsQwApply(apply);
            checkRecord.setCheckDate(checkDate);
            checkRecord.setCheckObjects(objectIdStr);
            checkRecord.setViolationType(violationType);
            this.save(checkRecord);
        });
    }

    /**
     * 核验待生成核验单的房源id是否已存在流程中的核验单
     *
     * @param houseIdStr
     */
    private void checkExist(String houseIdStr) {
        StringBuilder errorMsg = new StringBuilder();
        HsQwCheckRecord query = new HsQwCheckRecord();
        query.setStatus(HsQwCheckRecord.STATUS_AUDIT);
        query.sqlMap().getWhere().and("house_id", QueryType.IN, houseIdStr.split(","));
        List<HsQwCheckRecord> list = this.findList(query);
        list.forEach(checkRecord -> errorMsg.append(checkRecord.getHouse().getSimpleInfo()).append(","));
        if (!list.isEmpty()) {
            throw new ServiceException("以下房源存在在途的核验单：" + errorMsg.toString());
        }
    }

    @Autowired
    private CommonBpmService commonBpmService;

    public Page<HsQwCheckRecord> findPageByTask(HsQwCheckRecord hsQwCheckRecord, String[] status, String bpmStatus) {
        return commonBpmService.findTaskList(status, "rent_apply_check", hsQwCheckRecord, bpmStatus);
    }

    /**
     * 获取指定申请单信息
     *
     * @param hsQwCheckRecord
     * @return
     */
    public HsQwApply getApplyForm(HsQwCheckRecord hsQwCheckRecord) {
        return hsQwApplyService.get(hsQwCheckRecord.getApplyId());
    }

    /**
     * 保存房管机构核验信息
     *
     * @param hsQwManagementCheck
     */
    public void save(HsQwManagementCheck hsQwManagementCheck) {
        //保存核查基础信息
        hsQwManagementCheckService.saveBase(hsQwManagementCheck);
        //保存核查流程信息
        HsQwCheckRecord hsQwCheckRecord = this.get(hsQwManagementCheck.getRecordId());
        hsQwCheckRecord.setBpm(hsQwManagementCheck.getBpm());
        this.save(hsQwCheckRecord);
    }

    /**
     * 保存省直单位核验信息
     *
     * @param hsQwApply
     */
    @Transactional
    public void save(HsQwApply hsQwApply, HsQwCheckRecord hsQwCheckRecord) {
        HsQwApplyer mainApplyer = hsQwApplyerService.getMainApplyer(hsQwApply.getId());
        //保存核查基础信息
        hsQwApply.getHsQwApplyerList().forEach(applyer -> {
            applyer.setUserId(mainApplyer.getUserId());
        });
        hsQwApplyService.save(hsQwApply, "rent_apply_check");
        //保存核查流程信息
        HsQwCheckRecord bean = this.get(hsQwApply.getRecordId());
        bean.setBpm(hsQwCheckRecord.getBpm());
        //保存主申请人信息
        hsQwApply.setMainApplyer(mainApplyer);
        bean.setHsQwApply(hsQwApply);
        this.save(bean);
    }

    /**
     * 保存更新合同信息
     *
     * @param checkRecord
     */
    public void saveByCompact(HsQwCheckRecord checkRecord) {
        hsQwCompactService.save(checkRecord.getCompact());
        //保存流程
        this.save(checkRecord);
    }
}