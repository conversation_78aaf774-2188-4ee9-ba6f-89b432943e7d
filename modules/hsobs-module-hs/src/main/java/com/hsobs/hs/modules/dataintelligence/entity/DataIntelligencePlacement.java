package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.system.plat.win32.Winspool;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;

import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 住房保障数据统计Entity  公有住房配售统计
 * <AUTHOR>
 * @version 2025-1-2
 */
public class DataIntelligencePlacement extends DataEntity<DataIntelligencePlacement> {

	@ExcelFields({
			@ExcelField(title = "工作单位", attrName = "officeName", align = ExcelField.Align.CENTER, sort = 20),
			@ExcelField(title = "申请数", attrName = "applyTotalCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "审批通过数", attrName = "approvedCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "已售面积数(m²)", attrName = "approvedRoomArea", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "交易金额(元)", attrName = "approvedRoomPrice", align = ExcelField.Align.CENTER, sort = 40)
	})

	private static final long serialVersionUID = 1L;
	private String parentCodeTree;
	private String stype;	// 区分限价房或公有住房，0限价房，1公有住房

	private String city;
	private String area;

	private String estateId;	//区域Id
	private String estateName;	//区域
	private String officeCode;	// 单位编码
	private String officeName;	// 单位名称
	private Date startDate;		//开始日期
	private Date endDate;	//结束日期
	private Integer roomAreaType;	// 房源面积分类

	private Long applyTotalCount;	// 申请数
	private Long applyingCount;	// 申请数
	private Long approvedCount;	// 审核通过数

	private float totalRoomArea;    // 总面积
	private float applyingRoomArea;	// 申请中面积
	private float approvedRoomArea;	// 已售面积

	private float totalRoomPrice;    // 总金额
	private float applyingRoomPrice;    // 申请中金额
	private float approvedRoomPrice;    // 已售金额

	public DataIntelligencePlacement() {
		this(null);
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			endDate = new Date();
			startDate = dateFormat.parse(String.format("%04d-01-01", endDate.getYear()+1900));  // 解析指定日期字符串
		} catch (Exception e) {
		}
		parentCodeTree = "";
		if(!UserUtils.getUser().isAdmin()){
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		else{
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		roomAreaType = 1;
	}
	
	public DataIntelligencePlacement(String id){
		super(id);
	}

	public String getParentCodeTree() {
		return parentCodeTree;
	}

	public void setParentCodeTree(String parentCodeTree) {
		this.parentCodeTree = parentCodeTree;
	}

	public String getStype() {
		return stype;
	}

	public void setStype(String stype) {
		this.stype = stype;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getEstateId() {
		return estateId;
	}

	public void setEstateId(String estateId) {
		this.estateId = estateId;
	}

	public String getEstateName() {
		return estateName;
	}

	public void setEstateName(String estateName) {
		this.estateName = estateName;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getRoomAreaType() {
		return roomAreaType;
	}

	public void setRoomAreaType(Integer roomAreaType) {
		this.roomAreaType = roomAreaType;
	}

	public Long getApplyTotalCount() {
		return applyTotalCount;
	}

	public void setApplyTotalCount(Long applyTotalCount) {
		this.applyTotalCount = applyTotalCount;
	}

	public Long getApplyingCount() {
		return applyingCount;
	}

	public void setApplyingCount(Long applyingCount) {
		this.applyingCount = applyingCount;
	}

	public Long getApprovedCount() {
		return approvedCount;
	}

	public void setApprovedCount(Long approvedCount) {
		this.approvedCount = approvedCount;
	}

	public float getTotalRoomArea() {
		return totalRoomArea;
	}

	public void setTotalRoomArea(float totalRoomArea) {
		this.totalRoomArea = totalRoomArea;
	}

	public float getApplyingRoomArea() {
		return applyingRoomArea;
	}

	public void setApplyingRoomArea(float applyingRoomArea) {
		this.applyingRoomArea = applyingRoomArea;
	}

	public float getApprovedRoomArea() {
		return approvedRoomArea;
	}

	public void setApprovedRoomArea(float approvedRoomArea) {
		this.approvedRoomArea = approvedRoomArea;
	}

	public float getTotalRoomPrice() {
		return totalRoomPrice;
	}

	public void setTotalRoomPrice(float totalRoomPrice) {
		this.totalRoomPrice = totalRoomPrice;
	}

	public float getApplyingRoomPrice() {
		return applyingRoomPrice;
	}

	public void setApplyingRoomPrice(float applyingRoomPrice) {
		this.applyingRoomPrice = applyingRoomPrice;
	}

	public float getApprovedRoomPrice() {
		return approvedRoomPrice;
	}

	public void setApprovedRoomPrice(float approvedRoomPrice) {
		this.approvedRoomPrice = approvedRoomPrice;
	}
}