package com.hsobs.hs.modules.hsalarm.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.pricelimitapply.entity.HsPriceLimitApply;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.hsalarm.entity.HsAlarm;
import com.hsobs.hs.modules.hsalarm.service.HsAlarmService;

/**
 * 告警信息Controller
 * <AUTHOR>
 * @version 2025-01-03
 */
@Controller
@RequestMapping(value = "${adminPath}/hsalarm/")
public class HsAlarmController extends BaseController {

	@Autowired
	private HsAlarmService hsAlarmService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsAlarm get(String id, boolean isNewRecord) {
		return hsAlarmService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("hsalarm::view")
	@RequestMapping(value = {"list", ""})
	public String list(HsAlarm hsAlarm, Model model) {
		model.addAttribute("hsAlarm", hsAlarm);
		return "modules/hsalarm/hsAlarmList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("hsalarm::view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsAlarm> listData(HsAlarm hsAlarm, HttpServletRequest request, HttpServletResponse response) {
		hsAlarm.setPage(new Page<>(request, response));
		Page<HsAlarm> page = hsAlarmService.findPage(hsAlarm);

		// 测试告警
		//hsAlarmService.executeAlarmTask();

		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("hsalarm::view")
	@RequestMapping(value = "form")
	public String form(HsAlarm hsAlarm, Model model) {
		model.addAttribute("hsAlarm", hsAlarm);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/hsalarm/hsAlarmForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("hsalarm::edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsAlarm hsAlarm) {
		hsAlarmService.save(hsAlarm);
		return renderResult(Global.TRUE, text("保存告警信息成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("hsalarm::edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsAlarm hsAlarm) {
		hsAlarm.setStatus(HsAlarm.STATUS_DISABLE);
		hsAlarmService.updateStatus(hsAlarm);
		return renderResult(Global.TRUE, text("停用告警信息成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("hsalarm::edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsAlarm hsAlarm) {
		hsAlarm.setStatus(HsAlarm.STATUS_NORMAL);
		hsAlarmService.updateStatus(hsAlarm);
		return renderResult(Global.TRUE, text("启用告警信息成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("hsalarm::edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsAlarm hsAlarm) {
		hsAlarmService.delete(hsAlarm);
		return renderResult(Global.TRUE, text("删除告警信息成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("hsalarm::view")
	@RequestMapping(value = "exportAlarmData")
	public void exportAlarmData(HsAlarm hsAlarm, HttpServletRequest request, HttpServletResponse response) {
		hsAlarm.setPage(new Page<>(request, response));
		List<HsAlarm> list = hsAlarmService.findPage(hsAlarm).getList();
		String fileName = "告警信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("告警信息表", HsAlarm.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("hsalarm::view")
	@RequestMapping(value = "publicAlarm")
	@ResponseBody
	public String publicAlarm(HsAlarm hsAlarm, HttpServletRequest request, HttpServletResponse response) {

		hsAlarmService.sendNewAlarm(hsAlarm);
		return renderResult(Global.TRUE, text("发布告警信息成功！"));
	}
}