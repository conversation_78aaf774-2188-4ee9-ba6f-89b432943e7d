package com.hsobs.hs.modules.apply.service.serviceTask;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.blackrule.entity.HsQwApplyerBlackRule;
import com.hsobs.hs.modules.blackrule.service.HsQwApplyerBlackRuleService;
import com.hsobs.hs.modules.blackuser.entity.HsQwApplyerBlack;
import com.hsobs.hs.modules.blackuser.service.HsQwApplyerBlackService;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.modules.bpm.entity.BpmProcIns;
import com.jeesite.modules.bpm.utils.BpmUtils;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 租赁申请-用户拒绝承租，执行黑名单处理
 */
@Component
public class HsQwApplyBpmServiceTask implements JavaDelegate {
    @Autowired
    private HsQwApplyerBlackService hsQwApplyerBlackService;
    @Autowired
    private HsQwApplyerBlackRuleService hsQwApplyerBlackRuleService;
    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Override
    public void execute(DelegateExecution delegateExecution) {
        HsQwApplyerBlack hsQwApplyerBlack = new HsQwApplyerBlack();
        ExecutionEntity proins = ((ExecutionEntity) delegateExecution).getProcessInstance();
        String userid =  proins.getStartUserId();
        hsQwApplyerBlack.setUserId(userid);
        hsQwApplyerBlack.setReasonType("0");
        HsQwApplyerBlackRule rule = hsQwApplyerBlackRuleService.getApplyerBlackByReansonType("0");
        hsQwApplyerBlack.setEndTime(DateUtils.addMonths(new Date(), Math.toIntExact(rule.getTimeNum())));
        hsQwApplyerBlackService.saveAndStatus(hsQwApplyerBlack);
        BpmProcIns procIns = new BpmProcIns();
        procIns.setId(proins.getProcessInstanceId());
        procIns.setDeleteReason("用户拒绝承租");
        BpmUtils.getBpmRuntimeService().stopProcessInstance(procIns);
        HsQwApply hsQwApply = new HsQwApply();
        hsQwApply.setId(BpmUtils.getBizKey(delegateExecution));
        hsQwApply.setStatus("2");
        hsQwApplyService.updateStatus(hsQwApply);
    }
}
