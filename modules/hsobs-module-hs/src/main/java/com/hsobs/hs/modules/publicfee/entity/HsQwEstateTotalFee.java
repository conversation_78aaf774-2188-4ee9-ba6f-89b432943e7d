package com.hsobs.hs.modules.publicfee.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 公摊物业费用表Entity
 * <AUTHOR>
 * @version 2025-01-21
 */
@Table(name="hs_qw_estate_total_fee", alias="a", label="公摊物业费用表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="estate_id", attrName="estateId", label="楼盘编号"),
		@Column(name="water_fee", attrName="waterFee", label="总水费"),
		@Column(name="fee_month", attrName="feeMonth", label="缴费周期，按月生成账单，yyyyMMdd"),
		@Column(name="electri_fee", attrName="electriFee", label="总电费"),
		@Column(name="create_by", attrName="createBy", label="创建者", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false),
		@Column(name="update_by", attrName="updateBy", label="更新者", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false),
		@Column(name="remarks", attrName="remarks", label="备注信息", queryType=QueryType.LIKE),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "o",
				on = "o.id = a.estate_id", attrName="estate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)})
	}, orderBy="a.update_date DESC"
)
public class HsQwEstateTotalFee extends DataEntity<HsQwEstateTotalFee> {
	
	private static final long serialVersionUID = 1L;
	private String estateId;		// 楼盘编号
	private Double waterFee;		// 总水费
	private String feeMonth;		// 缴费周期，按月生成账单，yyyyMMdd
	private Double electriFee;		// 总电费
	private HsQwPublicRentalEstate estate;

	public HsQwEstateTotalFee() {
		this(null);
	}
	
	public HsQwEstateTotalFee(String id){
		super(id);
	}
	
	@NotBlank(message="楼盘编号不能为空")
	@Size(min=0, max=64, message="楼盘编号长度不能超过 64 个字符")
	public String getEstateId() {
		return estateId;
	}

	public void setEstateId(String estateId) {
		this.estateId = estateId;
	}
	
	@NotNull(message="总水费不能为空")
	public Double getWaterFee() {
		return waterFee;
	}

	public void setWaterFee(Double waterFee) {
		this.waterFee = waterFee;
	}
	
	@NotBlank(message="缴费周期不能为空")
	@Size(min=0, max=8, message="缴费周期，长度不能超过 8 个字符")
	public String getFeeMonth() {
		return feeMonth;
	}

	public void setFeeMonth(String feeMonth) {
		this.feeMonth = feeMonth;
	}
	
	@NotNull(message="总电费不能为空")
	public Double getElectriFee() {
		return electriFee;
	}

	public void setElectriFee(Double electriFee) {
		this.electriFee = electriFee;
	}

	public HsQwPublicRentalEstate getEstate() {
		return estate;
	}

	public void setEstate(HsQwPublicRentalEstate estate) {
		this.estate = estate;
	}
}