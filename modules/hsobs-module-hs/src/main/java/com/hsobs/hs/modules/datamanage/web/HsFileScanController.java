package com.hsobs.hs.modules.datamanage.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.datamanage.entity.HsFileClass;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.datamanage.entity.HsFileScan;
import com.hsobs.hs.modules.datamanage.service.HsFileScanService;

/**
 * 文档扫描信息Controller
 * <AUTHOR>
 * @version 2025-01-19
 */
@Controller
@RequestMapping(value = "${adminPath}/datamanage/hsFileScan")
public class HsFileScanController extends BaseController {

	@Autowired
	private HsFileScanService hsFileScanService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsFileScan get(String id, boolean isNewRecord) {
		return hsFileScanService.get(id, isNewRecord);
	}

	/**
	 * 管理主页
	 */
	@RequiresPermissions("datamanage:hsFileScan:view")
	@RequestMapping(value = "index")
	public String index(HsFileScan hsFileScan, Model model) {
		model.addAttribute("hsFileScan", hsFileScan);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/datamanage/hsFileScanIndex";
	}


	/**
	 * 查询列表
	 */
	@RequiresPermissions("datamanage:hsFileScan:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsFileScan hsFileScan, Model model) {
		model.addAttribute("hsFileScan", hsFileScan);
		return "modules/datamanage/hsFileScanList";
	}

	@RequiresPermissions("datamanage:hsFileScan:view")
	@RequestMapping(value = "searchIndex")
	public String searchIndex(HsFileScan hsFileScan, Model model) {
		model.addAttribute("hsFileScan", hsFileScan);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/datamanage/hsFileScanSearchIndex";
	}


	/**
	 * 查询列表
	 */
	@RequiresPermissions("datamanage:hsFileScan:view")
	@RequestMapping(value = {"searchList", ""})
	public String searchList(HsFileScan hsFileScan, Model model) {
		model.addAttribute("hsFileScan", hsFileScan);
		return "modules/datamanage/hsFileScanSearchList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("datamanage:hsFileScan:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsFileScan> listData(HsFileScan hsFileScan, HttpServletRequest request, HttpServletResponse response) {
		hsFileScan.setPage(new Page<>(request, response));
		hsFileScanService.addDataScopeFilter(hsFileScan);
		Page<HsFileScan> page = hsFileScanService.findPage(hsFileScan);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("datamanage:hsFileScan:view")
	@RequestMapping(value = "form")
	public String form(HsFileScan hsFileScan, Model model, String isRead) {
		model.addAttribute("hsFileScan", hsFileScan);
		if (StringUtils.isEmpty(isRead)) {
			isRead = "false";
		}
		model.addAttribute("isRead", isRead);
		return "modules/datamanage/hsFileScanForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("datamanage:hsFileScan:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsFileScan hsFileScan) {
		hsFileScanService.save(hsFileScan);
		return renderResult(Global.TRUE, text("保存文档扫描信息成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("datamanage:hsFileScan:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsFileScan hsFileScan) {
		hsFileScanService.delete(hsFileScan);
		return renderResult(Global.TRUE, text("删除文档扫描信息成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("datamanage:hsFileScan:view")
	@RequestMapping(value = "hsFileScanSelect")
	public String hsFileScanSelect(HsFileScan hsFileScan, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsFileScan", hsFileScan);
		return "modules/datamanage/hsFileScanSelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("datamanage:hsFileScan:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsFileScan hsFileScan, HttpServletResponse response) {
		hsFileScanService.addDataScopeFilter(hsFileScan);
		List<HsFileScan> list = hsFileScanService.findList(hsFileScan);
		String fileName = "档案查询信息" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("档案查询信息", HsFileScan.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	
}