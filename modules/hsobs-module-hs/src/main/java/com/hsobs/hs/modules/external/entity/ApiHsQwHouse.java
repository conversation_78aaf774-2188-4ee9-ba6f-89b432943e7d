package com.hsobs.hs.modules.external.entity;

import javax.validation.constraints.NotBlank;

public class ApiHsQwHouse extends ApiBody{

    @HsMapTo("address")
    @NotBlank(message = "房屋坐落信息不能为空")
    private String fwzlxx;

    @HsMapTo("floorArea")
    @NotBlank(message = "建筑面积不能为空")
    private String jzmj;

    @HsMapTo("propertyRight")
    @NotBlank(message = "产权性质不能为空")
    private String cqxz;

    public String getFwzlxx() {
        return fwzlxx;
    }

    public void setFwzlxx(String fwzlxx) {
        this.fwzlxx = fwzlxx;
    }

    public String getJzmj() {
        return jzmj;
    }

    public void setJzmj(String jzmj) {
        this.jzmj = jzmj;
    }

    public String getCqxz() {
        return cqxz;
    }

    public void setCqxz(String cqxz) {
        this.cqxz = cqxz;
    }

}
