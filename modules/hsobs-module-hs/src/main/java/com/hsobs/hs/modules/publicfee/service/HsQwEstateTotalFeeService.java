package com.hsobs.hs.modules.publicfee.service;

import java.util.Date;
import java.util.List;

import com.hsobs.hs.modules.utils.HsDateUtil;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.publicfee.entity.HsQwEstateTotalFee;
import com.hsobs.hs.modules.publicfee.dao.HsQwEstateTotalFeeDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 公摊物业费用表Service
 * <AUTHOR>
 * @version 2025-01-21
 */
@Service
public class HsQwEstateTotalFeeService extends CrudService<HsQwEstateTotalFeeDao, HsQwEstateTotalFee> {
	
	/**
	 * 获取单条数据
	 * @param hsQwEstateTotalFee
	 * @return
	 */
	@Override
	public HsQwEstateTotalFee get(HsQwEstateTotalFee hsQwEstateTotalFee) {
		return super.get(hsQwEstateTotalFee);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwEstateTotalFee 查询条件
	 * @param hsQwEstateTotalFee page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwEstateTotalFee> findPage(HsQwEstateTotalFee hsQwEstateTotalFee) {
		return super.findPage(hsQwEstateTotalFee);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwEstateTotalFee
	 * @return
	 */
	@Override
	public List<HsQwEstateTotalFee> findList(HsQwEstateTotalFee hsQwEstateTotalFee) {
		return super.findList(hsQwEstateTotalFee);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwEstateTotalFee
	 */
	@Override
	@Transactional
	public void save(HsQwEstateTotalFee hsQwEstateTotalFee) {
		this.checkExistFee(hsQwEstateTotalFee);
		super.save(hsQwEstateTotalFee);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsQwEstateTotalFee, hsQwEstateTotalFee.getId(), "hsQwEstateTotalFee_file");
	}

	private void checkExistFee(HsQwEstateTotalFee hsQwEstateTotalFee) {
		HsQwEstateTotalFee query = new HsQwEstateTotalFee();
		query.setEstateId(hsQwEstateTotalFee.getEstateId());
		query.setFeeMonth(HsDateUtil.getCurrentMonthDate());
		query.setStatus(HsQwEstateTotalFee.STATUS_NORMAL);
		if (this.findCount(query) >0){
			throw new ServiceException("该楼盘公摊费用已导入生成");
		}
	}

	/**
	 * 更新状态
	 * @param hsQwEstateTotalFee
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwEstateTotalFee hsQwEstateTotalFee) {
		super.updateStatus(hsQwEstateTotalFee);
	}
	
	/**
	 * 删除数据
	 * @param hsQwEstateTotalFee
	 */
	@Override
	@Transactional
	public void delete(HsQwEstateTotalFee hsQwEstateTotalFee) {
		super.delete(hsQwEstateTotalFee);
	}

}