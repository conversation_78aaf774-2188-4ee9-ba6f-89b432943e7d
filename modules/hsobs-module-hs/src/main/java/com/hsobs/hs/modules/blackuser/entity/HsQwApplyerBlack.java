package com.hsobs.hs.modules.blackuser.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.TreeEntity;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;

/**
 * 租赁资格轮候租户黑名单Entity
 * <AUTHOR>
 * @version 2024-12-19
 */
@Table(name="hs_qw_applyer_black", alias="a", label="租赁资格轮候租户黑名单信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="house_info", attrName="houseInfo", label="住房信息"),
		@Column(name="reason_type", attrName="reasonType", label="纳入黑名单原因", comment="纳入黑名单原因（0已配租不签合同 1租金拖欠 2违规 3直接录入）"),
		@Column(includeEntity=DataEntity.class),
		@Column(name="user_id", attrName="userId", label="用户编号"),
		@Column(name="reason", attrName="reason", label="纳入原因"),
		@Column(name="apply_id", attrName="applyId", label="申请单id"),
		@Column(name="end_time", attrName="endTime", label="黑名单结束时间"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = EmpUser.class, alias = "o",
				on = "o.user_code = a.user_id ", attrName = "user",
				columns = {@Column(includeEntity = EmpUser.class)}),
		@JoinTable(type=Type.JOIN, entity=Employee.class, alias="e",
				on="e.emp_code=o.ref_code AND o.user_type=#{USER_TYPE_EMPLOYEE}",
				attrName="user.employee", columns={
				@Column(includeEntity= BaseEntity.class),
				@Column(includeEntity=DataEntity.class),
				@Column(name="emp_code", 	attrName="empCode", 	label="员工编码", isPK=true),
				@Column(name="emp_no", 		attrName="empNo", 		label="员工工号"),
				@Column(name="emp_name", 	attrName="empName", 	label="员工姓名", queryType=QueryType.LIKE),
				@Column(name="emp_name_en", attrName="empNameEn", 	label="英文名", queryType=QueryType.LIKE),
				@Column(name="establishment_type", attrName="establishmentType", 	label="岗位编制", isQuery = true, queryType = QueryType.EQ),
				@Column(name="age", attrName="age", label="年龄", isQuery=false),
				@Column(name="joined_date", attrName="joinedDate", label="入职日期", isQuery=false),
				@Column(name="change_type", attrName="changeType", label="变动类型", isQuery=false),
				@Column(name="change_date", attrName="changeDate", label="变动时间", isQuery=false),
				@Column(name="change_remarks", attrName="changeRemarks", label="变动备注", isQuery=false),
				@Column(name="position_status", attrName="positionStatus", label="在职状态", isQuery=false),
		}),
		@JoinTable(type=Type.LEFT_JOIN, entity=Office.class, alias="off",
				on="off.office_code=e.office_code", attrName="office",
				columns = {
						@Column(name="office_code", attrName="officeCode",label="机构编码", isPK=true),
						@Column(name="view_code", 	attrName="viewCode",label="机构代码"),
						@Column(name="office_name", attrName="officeName",label="机构名称", queryType=QueryType.LIKE),
						@Column(name="full_name", 	attrName="fullName",label="机构全称"),
						@Column(name="office_type", attrName="officeType",label="机构类型"),
						@Column(name="leader", 		attrName="leader",label="负责人")})
	}, orderBy="a.update_date DESC"
)
public class HsQwApplyerBlack extends DataEntity<HsQwApplyerBlack> {

	public static final String USER_TYPE_EMPLOYEE = "employee";
	private static final long serialVersionUID = 1L;
	private String houseInfo;		// 住房信息
	private String reasonType;		// 纳入黑名单原因（0已配租不签合同 1租金拖欠 2违规 3直接录入）
	private String userId;		// 用户编号
	private String reason;		// 纳入原因
	private String applyId;		// 申请单id
	private Date endTime;		// 黑名单结束时间
	private EmpUser user;

	private Office office;

	public HsQwApplyerBlack() {
		this(null);
	}
	
	public HsQwApplyerBlack(String id){
		super(id);
	}
	
	@Size(min=0, max=100, message="住房信息长度不能超过 100 个字符")
	public String getHouseInfo() {
		return houseInfo;
	}

	public void setHouseInfo(String houseInfo) {
		this.houseInfo = houseInfo;
	}
	
	@NotBlank(message="纳入黑名单原因不能为空")
	@Size(min=0, max=1, message="纳入黑名单原因长度不能超过 1 个字符")
	public String getReasonType() {
		return reasonType;
	}

	public void setReasonType(String reasonType) {
		this.reasonType = reasonType;
	}
	
	@NotBlank(message="用户编号不能为空")
	@Size(min=0, max=64, message="用户编号长度不能超过 64 个字符")
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	@Size(min=0, max=255, message="纳入原因长度不能超过 255 个字符")
	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}
	
	@Size(min=0, max=64, message="申请单id长度不能超过 64 个字符")
	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="黑名单结束时间不能为空")
	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getEndTime_gte() {
		return sqlMap.getWhere().getValue("end_time", QueryType.GTE);
	}

	public void setEndTime_gte(Date workTime) {
		sqlMap.getWhere().and("end_time", QueryType.GTE, workTime);
	}

	public Date getEndTime_lte() {
		return sqlMap.getWhere().getValue("end_time", QueryType.LTE);
	}

	public void setEndTime_lte(Date workTime) {
		sqlMap.getWhere().and("end_time", QueryType.LTE, workTime);
	}

	public EmpUser getUser() {
		return user;
	}

	public void setUser(EmpUser user) {
		this.user = user;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}
}