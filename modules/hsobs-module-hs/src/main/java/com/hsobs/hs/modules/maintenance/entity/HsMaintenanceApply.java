package com.hsobs.hs.modules.maintenance.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 维修申请Entity
 * <AUTHOR>
 * @version 2024-12-08
 */
@Table(name="hs_maintenance_apply", alias="a", label="维修申请信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="house_id", attrName="houseId", label="房屋信息"),
		@Column(name="unit_id", attrName="unitId", label="申请单位"),
		@Column(name="fund_id", attrName="fundId", label="维修资金账户"),
		@Column(name="contact_name", attrName="contactName", label="联系人", queryType=QueryType.LIKE),
		@Column(name="contact_tel", attrName="contactTel", label="联系人电话", queryType=QueryType.LIKE),
		@Column(name="completed_time", attrName="completedTime", label="竣工年份", isUpdateForce=true),
		@Column(name="house_total", attrName="houseTotal", label="总套数", isUpdateForce=true),
		@Column(name="house_sold", attrName="houseSold", label="已售套数", isUpdateForce=true),
		@Column(name="exist_fund", attrName="existFund", label="现存资金", isUpdateForce=true),
		@Column(name="apply_fund", attrName="applyFund", label="申请金额", isUpdateForce=true),
		@Column(name="disbursement_fund", attrName="disbursementFund", label="拨付金额", isUpdateForce=true),
		@Column(name="repair_type", attrName="repairType", label="维修类别", queryType=QueryType.LIKE),
		@Column(name="apply_reason", attrName="applyReason", label="申请原由"),
		@Column(name="review_msg", attrName="reviewMsg", label="核检说明"),
		@Column(name="survey_analysis", attrName="surveyAnalysis", label="勘察分析"),
		@Column(name="survey_conclusion", attrName="surveyConclusion", label="勘察结论"),
		@Column(name="fav_no", attrName="favNo", label="资金拨付单编号", isUpdateForce=true),
		@Column(name="adn_start", attrName="adnStart", label="资金拨付单批文号起", isUpdateForce=true),
		@Column(name="adn_end", attrName="adnEnd", label="资金拨付单批文号止", isUpdateForce=true),
		@Column(name="apply_status", attrName="applyStatus", label="申请状态", isUpdateForce=true),
		@Column(name="status", attrName="status", label="申请状态", isUpdate = true),
		@Column(name="create_by", attrName="createBy", label="申请日期", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="申请人", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
		@Column(name="survey_flag", attrName="surveyFlag", label="是否勘测", isQuery=false),
		@Column(name="house_address", attrName="houseAddress", label="房屋坐落", queryType=QueryType.LIKE),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o",
				on = "o.office_code = a.unit_id", attrName = "applyOffice",
				columns = {@Column(includeEntity = Office.class) }
		),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "h",
				on = "h.id = a.house_id", attrName = "publicRentalEstate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class) }
		),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsMaintenanceFunds.class, alias = "f",
				on = "f.id = a.fund_id", attrName = "hsMaintenanceFunds",
				columns = {@Column(includeEntity = HsMaintenanceFunds.class) }
		)
    }, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere", orderBy="a.update_date DESC"
)
public class HsMaintenanceApply extends BpmEntity<HsMaintenanceApply> {

//	维修预算申请     经办初审  现场勘测 经办复核  处室领导复核 工程施工  经办审核  保障处核验  开具资金拨付单
//  省直单位申请草稿  待机关经办初审  待机关经办现场勘测  待机关经办复核  待机关处室领导复核  待提交工程施工材料  待机关经办审核施工材料  待机关保障处核验  待开具资金拨付单

	public static final String APPLY_STATUS_DEFAULT = "-1"; //默认、删除的申请单
	public static final String APPLY_STATUS_DRAFT = "维修预算申请";//0:省直单位申请草稿
	public static final String APPLY_STATUS_AUDIT_ORGHAND_FIRST = "经办初审";//1:待机关经办初审
	public static final String APPLY_STATUS_AUDIT_SITE_INVESTIG = "现场勘测";//2:待机关经办现场勘测
	public static final String APPLY_STATUS_AUDIT_ORGHAND_SECOND = "经办复核";//3:待机关经办复核
	public static final String APPLY_STATUS_AUDIT_ORGOFFICE_FIRST = "处室领导复核";//4:待机关处室领导复核

	// 下发批复申请表
	// 组织施工
	// 提交工程材料
    public static final String APPLY_STATUS_AUDIT_ISSUE_APPROVAL_APPLICATION_FORM = "下发批复申请表";
    public static final String APPLY_STATUS_AUDIT_ORGANIZE_CONSTRUCTION = "组织施工";
    public static final String APPLY_STATUS_AUDIT_SUBMIT_CONSTRUCTION_MATERIALS = "提交工程材料";

	public static final String APPLY_STATUS_AUDIT_ORGHAND_THIRD = "经办审核";//6:待机关经办审核施工材料
	public static final String APPLY_STATUS_AUDIT_SECDEPT_VERIFY = "保障处核检";//7:待机关保障处核验
	public static final String APPLY_STATUS_AUDIT_ISSUE_FUND_SLIP = "开具资金拨付单";//8:待开具资金拨付单

	
	private static final long serialVersionUID = 1L;
	private String houseId;		// 房屋信息
	private String unitId;		// 申请单位
	private String fundId;
	private String contactName;		// 联系人
	private String contactTel;		// 联系人电话
	private Date completedTime;		// 竣工年份
	private Integer houseTotal;		// 总套数
	private Integer houseSold;		// 已售套数
	private Double existFund;		// 现存资金
	private Double applyFund;		// 申请金额
	private Double disbursementFund;		// 拨付金额
	private String repairType;		// 维修类别
	private String applyReason;		// 申请原由
	private Long favNo;		// 资金拨付单编号
	private Long adnStart;		// 资金拨付单批文号起
	private Long adnEnd;		// 资金拨付单批文号止
	private Integer applyStatus;		// 申请状态
	private String validTag;		// 是否有效;1-有效 0-无效
	private String surveyFlag;		// 是否需要勘测;1-是 0-否
    private String reviewMsg;  // 核检意见
    private String surveyAnalysis;  // 勘察分析
    private String surveyConclusion;  // 勘察结论
	private String houseAddress; //  房屋坐落

	private String flowStatus;
	private Office applyOffice;
	private Integer adnNum;

	//TODO 通用参数？
	private String createByOfficeName;
	private String createByName;

	private HsQwPublicRentalEstate publicRentalEstate;
	private HsMaintenanceFunds hsMaintenanceFunds;

	@ExcelFields({
			@ExcelField(title="申请单号", attrName="id", align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="房屋信息", attrName="publicRentalEstate.name", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="房屋坐落", attrName="houseAddress", align= ExcelField.Align.CENTER, sort=31),
			@ExcelField(title="申请单位", attrName="applyOffice.treeNames", align= ExcelField.Align.CENTER, sort=40),
			@ExcelField(title="联系人", attrName="contactName", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="联系人电话", attrName="contactTel", align= ExcelField.Align.CENTER, sort=60),
			@ExcelField(title="申请金额", attrName="applyFund", align= ExcelField.Align.CENTER, sort=70),
			@ExcelField(title="维修类别", attrName="repairType", align= ExcelField.Align.LEFT, sort=80),
			@ExcelField(title="申请状态", attrName="applyStatus", dictType="maintenance_apply_status_f",  align= ExcelField.Align.LEFT, sort=90),
			@ExcelField(title="创建日期", attrName="createDate", align= ExcelField.Align.CENTER, words=20, sort=100, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="更新日期", attrName="updateDate", align= ExcelField.Align.CENTER, words=20, sort=110, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
	})

	public HsMaintenanceApply() {
		this(null);
	}
	
	public HsMaintenanceApply(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="房屋信息长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}
	
	@Size(min=0, max=64, message="申请单位长度不能超过 64 个字符")
	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getFundId() {
		return fundId;
	}

	public void setFundId(String fundId) {
		this.fundId = fundId;
	}

	@Size(min=0, max=30, message="联系人长度不能超过 30 个字符")
	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}
	
	@Size(min=0, max=20, message="联系人电话长度不能超过 20 个字符")
	public String getContactTel() {
		return contactTel;
	}

	public void setContactTel(String contactTel) {
		this.contactTel = contactTel;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getCompletedTime() {
		return completedTime;
	}

	public void setCompletedTime(Date completedTime) {
		this.completedTime = completedTime;
	}
	
	public Integer getHouseTotal() {
		return houseTotal;
	}

	public void setHouseTotal(Integer houseTotal) {
		this.houseTotal = houseTotal;
	}
	
	public Integer getHouseSold() {
		return houseSold;
	}

	public void setHouseSold(Integer houseSold) {
		this.houseSold = houseSold;
	}
	
	public Double getExistFund() {
		return existFund;
	}

	public void setExistFund(Double existFund) {
		this.existFund = existFund;
	}
	
	public Double getApplyFund() {
		return applyFund;
	}

	public void setApplyFund(Double applyFund) {
		this.applyFund = applyFund;
	}
	
	public Double getDisbursementFund() {
		return disbursementFund;
	}

	public void setDisbursementFund(Double disbursementFund) {
		this.disbursementFund = disbursementFund;
	}
	
	@Size(min=0, max=32, message="维修类别长度不能超过 32 个字符")
	public String getRepairType() {
		return repairType;
	}

	public void setRepairType(String repairType) {
		this.repairType = repairType;
	}
	
	@Size(min=0, max=900, message="申请原由长度不能超过 900 个字符")
	public String getApplyReason() {
		return applyReason;
	}

	public void setApplyReason(String applyReason) {
		this.applyReason = applyReason;
	}
	
	public Long getFavNo() {
		return favNo;
	}

	public void setFavNo(Long favNo) {
		this.favNo = favNo;
	}
	
	public Long getAdnStart() {
		return adnStart;
	}

	public void setAdnStart(Long adnStart) {
		this.adnStart = adnStart;
	}
	
	public Long getAdnEnd() {
		return adnEnd;
	}

	public void setAdnEnd(Long adnEnd) {
		this.adnEnd = adnEnd;
	}
	
	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

    public String getSurveyFlag() {
        return surveyFlag;
    }

    public void setSurveyFlag(String surveyFlag) {
        this.surveyFlag = surveyFlag;
    }

    public String getFlowStatus() {
		return flowStatus;
	}

	public void setFlowStatus(String flowStatus) {
		this.flowStatus = flowStatus;
	}

	public Office getApplyOffice() {
		return applyOffice;
	}

	public void setApplyOffice(Office applyOffice) {
		this.applyOffice = applyOffice;
	}

	public Integer getAdnNum() {
		return adnNum;
	}

	public void setAdnNum(Integer adnNum) {
		this.adnNum = adnNum;
	}

	public HsQwPublicRentalEstate getPublicRentalEstate() {
		return publicRentalEstate;
	}

	public void setPublicRentalEstate(HsQwPublicRentalEstate publicRentalEstate) {
		this.publicRentalEstate = publicRentalEstate;
	}

	@Size(min=0, max=500, message="核检长度不能超过 500 个字符")
	public String getReviewMsg() {
		return reviewMsg;
	}

	public void setReviewMsg(String reviewMsg) {
		this.reviewMsg = reviewMsg;
	}

	@Size(min=0, max=500, message="勘察分析长度不能超过 500 个字符")
	public String getSurveyAnalysis() {
		return surveyAnalysis;
	}

	public void setSurveyAnalysis(String surveyAnalysis) {
		this.surveyAnalysis = surveyAnalysis;
	}

	@Size(min=0, max=500, message="勘察结论长度不能超过 500 个字符")
	public String getSurveyConclusion() {
		return surveyConclusion;
	}

	public void setSurveyConclusion(String surveyConclusion) {
		this.surveyConclusion = surveyConclusion;
	}

	public HsMaintenanceFunds getHsMaintenanceFunds() {
        return hsMaintenanceFunds;
    }

    public void setHsMaintenanceFunds(HsMaintenanceFunds hsMaintenanceFunds) {
        this.hsMaintenanceFunds = hsMaintenanceFunds;
    }

	public String getCreateByOfficeName() {
		return createByOfficeName;
	}

	public void setCreateByOfficeName(String createByOfficeName) {
		this.createByOfficeName = createByOfficeName;
	}

	@Override
	public String getCreateByName() {
		return createByName;
	}

	@Override
	public void setCreateByName(String createByName) {
		this.createByName = createByName;
	}

	@Size(min=0, max=200, message="房屋坐落长度不能超过 200 个字符")
	public String getHouseAddress() {
		return houseAddress;
	}

	public void setHouseAddress(String houseAddress) {
		this.houseAddress = houseAddress;
	}
}