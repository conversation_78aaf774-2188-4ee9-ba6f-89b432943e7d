package com.hsobs.hs.modules.estate.service;

import java.util.List;

import com.hsobs.hs.modules.external.entity.ApiHsHouseTemplateList;
import com.hsobs.hs.modules.house.dao.HsQwPublicRentalHouseDao;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.estate.dao.HsQwPublicRentalEstateDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 公租房房源楼盘信息表Service
 * 
 * <AUTHOR>
 * @version 2024-11-20
 */
@Service
public class HsQwPublicRentalEstateService extends CrudService<HsQwPublicRentalEstateDao, HsQwPublicRentalEstate> {

    @Autowired
    HsQwPublicRentalHouseDao hsQwPublicRentalHouseDao;

    /**
     * 获取单条数据
     * 
     * @param hsQwPublicRentalEstate
     * @return
     */
    @Override
    public HsQwPublicRentalEstate get(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
        return super.get(hsQwPublicRentalEstate);
    }

    /**
     * 查询分页数据
     * 
     * @param hsQwPublicRentalEstate 查询条件
     * @param hsQwPublicRentalEstate page 分页对象
     * @return
     */
    @Override
    public Page<HsQwPublicRentalEstate> findPage(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
        return super.findPage(hsQwPublicRentalEstate);
    }

    /**
     * 查询列表数据
     * 
     * @param hsQwPublicRentalEstate
     * @return
     */
    @Override
    public List<HsQwPublicRentalEstate> findList(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
        return super.findList(hsQwPublicRentalEstate);
    }

    /**
     * 保存数据（插入或更新）
     * 
     * @param hsQwPublicRentalEstate
     */
    @Override
    @Transactional
    public void save(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
        super.save(hsQwPublicRentalEstate);
        // 保存上传图片
        FileUploadUtils.saveFileUpload(hsQwPublicRentalEstate, hsQwPublicRentalEstate.getId(),
                "hsQwPublicRentalEstate_image");
        // 保存上传附件
        FileUploadUtils.saveFileUpload(hsQwPublicRentalEstate, hsQwPublicRentalEstate.getId(),
                "hsQwPublicRentalEstate_file");
    }

    /**
     * 更新状态
     * 
     * @param hsQwPublicRentalEstate
     */
    @Override
    @Transactional
    public void updateStatus(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
        super.updateStatus(hsQwPublicRentalEstate);
    }

    /**
     * 删除数据
     * 
     * @param hsQwPublicRentalEstate
     */
    @Override
    @Transactional
    public void delete(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
        this.deleteCheckRelateHouse(hsQwPublicRentalEstate);
        super.delete(hsQwPublicRentalEstate);
    }

    private void deleteCheckRelateHouse(HsQwPublicRentalEstate hsQwPublicRentalEstate) {
        HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
        query.setEstateId(hsQwPublicRentalEstate.getId());
        query.setStatus_in(new String[]{HsQwPublicRentalHouse.STATUS_NORMAL,HsQwPublicRentalHouse.STATUS_AUDIT});
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        long count = hsQwPublicRentalHouseDao.findCount(query);
        if (count > 0){
            throw new ServiceException("该楼盘配置了房源，请先到房源管理界面进行删除后再操作！");
        }
    }

    public Page<HsQwPublicRentalEstate> findStaticEstate(HsQwPublicRentalEstate query) {
        // 确保有分页对象
        if (query.getPage() == null) {
            query.setPage(new Page<>());
        }
        // 查询列表数据
        List<HsQwPublicRentalEstate> list = this.dao.findStaticEstate(query);

        // 查询总数
        int count = this.dao.findStaticEstateCount(query);

        // 构建分页对象
        Page<HsQwPublicRentalEstate> page = query.getPage();
        page.setList(list);
        page.setCount(count);
        return page;
    }


    public Page<HsQwPublicRentalEstate> findPriceLimitStaticEstate(HsQwPublicRentalEstate query) {
        // 确保有分页对象
        if (query.getPage() == null) {
            query.setPage(new Page<>());
        }

        // 查询列表数据
        List<HsQwPublicRentalEstate> list = this.dao.findPriceLimitStaticEstate(query);

        // 查询总数
        int count = this.dao.findPriceLimitStaticEstateCount(query);

        // 构建分页对象
        Page<HsQwPublicRentalEstate> page = query.getPage();
        page.setList(list);
        page.setCount(count);
        return page;
    }
}
