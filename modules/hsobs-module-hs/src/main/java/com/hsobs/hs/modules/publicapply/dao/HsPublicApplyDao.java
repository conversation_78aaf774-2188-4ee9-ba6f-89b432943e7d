package com.hsobs.hs.modules.publicapply.dao;

import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;

import java.util.List;
import java.util.Map;

/**
 * 公有住房-购房申请DAO接口
 * <AUTHOR>
 * @version 2024-12-10
 */
@MyBatisDao
public interface HsPublicApplyDao extends CrudDao<HsPublicApply> {

    List<Map<String, Object>> checkHouse(String applyId, String houseId);

    List<Map<String, Object>> selectHouseId(String applyId);
}