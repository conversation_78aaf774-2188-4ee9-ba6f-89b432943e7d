package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;

public interface HsQwHouseSelectList {
    String getDataType();

    Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse, HsQwPublicRentalHouseService hsQwPublicRentalHouseService);
}
