package com.hsobs.hs.modules.external.entity;

import com.hsobs.hs.modules.external.entity.validated.NotNullApplyMatterGroup;
import com.jeesite.common.entity.BaseEntity;

import javax.validation.constraints.NotBlank;

public class ApiHsQwApplyMatter extends ApiBody {

    private String sqdh;//申请单号
    @NotBlank(message = "事项单号不可为空" , groups = {NotNullApplyMatterGroup.class})
    private String sxdh;//事项单号
    private String sxmc;//事项名称
    private String sxlx;//事项类型（0：公租房配租复查确认 1：公租房线下配租选房确认 2：公租房承租确认 3：公租房居室变更复查确认）
    private String sxzt;//事项状态（0：待处理 1：已处理 2：逾期未处理）
    private String sxclsj;//事项处理结果（0：待处理 1：已处理-参加/承租 2:已处理-不参加/本次不参加/不承租 3：已处理-已不符合条件 4:逾期未处理）
    private String pzsj;//配租时间描述/签约时间描述
    private String pzdd;//配租地点描述/签约地点描述
    private String zysx;//注意事项
    private String clsxks;//用户处理时限开始（yyyy-MM-dd格式）
    private String clsxjz;//用户处理时限截止（yyyy-MM-dd格式）
    private String fwdz;//房屋地址
    private String mph;//门牌号

    public String getSqdh() {
        return sqdh;
    }

    public void setSqdh(String sqdh) {
        this.sqdh = sqdh;
    }

    public String getSxmc() {
        return sxmc;
    }

    public void setSxmc(String sxmc) {
        this.sxmc = sxmc;
    }

    public String getSxlx() {
        return sxlx;
    }

    public void setSxlx(String sxlx) {
        this.sxlx = sxlx;
    }

    public String getSxzt() {
        return sxzt;
    }

    public void setSxzt(String sxzt) {
        this.sxzt = sxzt;
    }

    public String getSxclsj() {
        return sxclsj;
    }

    public void setSxclsj(String sxclsj) {
        this.sxclsj = sxclsj;
    }

    public String getPzsj() {
        return pzsj;
    }

    public void setPzsj(String pzsj) {
        this.pzsj = pzsj;
    }

    public String getPzdd() {
        return pzdd;
    }

    public void setPzdd(String pzdd) {
        this.pzdd = pzdd;
    }

    public String getZysx() {
        return zysx;
    }

    public void setZysx(String zysx) {
        this.zysx = zysx;
    }

    public String getClsxks() {
        return clsxks;
    }

    public void setClsxks(String clsxks) {
        this.clsxks = clsxks;
    }

    public String getClsxjz() {
        return clsxjz;
    }

    public void setClsxjz(String clsxjz) {
        this.clsxjz = clsxjz;
    }

    public String getFwdz() {
        return fwdz;
    }

    public void setFwdz(String fwdz) {
        this.fwdz = fwdz;
    }

    public String getMph() {
        return mph;
    }

    public void setMph(String mph) {
        this.mph = mph;
    }

    public String getSxdh() {
        return sxdh;
    }

    public void setSxdh(String sxdh) {
        this.sxdh = sxdh;
    }
}
