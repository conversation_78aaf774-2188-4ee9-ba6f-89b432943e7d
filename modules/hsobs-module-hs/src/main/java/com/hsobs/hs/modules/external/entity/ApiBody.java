package com.hsobs.hs.modules.external.entity;

import com.jeesite.common.entity.BaseEntity;
import com.jeesite.common.entity.Page;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotBlank;
import java.util.List;

public class ApiBody extends BaseEntity {

    private String sjh;

    public ApiBody() {
        if (super.getPage() == null){
            this.page = new Page(1, 10);
        }
    }

    @HsMapFile
    private List<ApiFile> fileList;

    private int pageIndex;//否	分页索引
    public String getSjh() {
        return sjh;
    }

    public void setSjh(String sjh) {
        this.sjh = sjh;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
        this.pageNo = pageIndex;
        this.page.setPageNo(pageIndex);
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
        this.page.setPageSize(pageSize);
    }

    public List<ApiFile> getFileList() {
        return fileList;
    }

    public void setFileList(List<ApiFile> fileList) {
        this.fileList = fileList;
    }
}
