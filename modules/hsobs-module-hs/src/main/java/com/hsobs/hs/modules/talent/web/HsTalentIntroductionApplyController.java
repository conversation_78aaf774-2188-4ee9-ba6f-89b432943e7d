package com.hsobs.hs.modules.talent.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.elevator.entity.HsElevatorApply;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceFunds;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionConf;
import com.hsobs.hs.modules.talent.service.HsTalentIntroductionConfService;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.alibaba.fastjson.JSONValidator;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionApply;
import com.hsobs.hs.modules.talent.service.HsTalentIntroductionApplyService;

/**
 * 人才补助申请表Controller
 * <AUTHOR>
 * @version 2025-01-03
 */
@Controller
@RequestMapping(value = "${adminPath}/talent/hsTalentIntroductionApply")
public class HsTalentIntroductionApplyController extends BaseController {

	@Autowired
	private HsTalentIntroductionApplyService hsTalentIntroductionApplyService;
	@Autowired
	private HsTalentIntroductionConfService hsTalentIntroductionConfService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsTalentIntroductionApply get(String id, boolean isNewRecord) {
		return hsTalentIntroductionApplyService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsTalentIntroductionApply hsTalentIntroductionApply, Model model) {
		model.addAttribute("hsTalentIntroductionApply", hsTalentIntroductionApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentIntroductionApplyList";
	}

	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = {"applyList", ""})
	public String applyList(HsTalentIntroductionApply hsTalentIntroductionApply, Model model) {
		model.addAttribute("hsTalentIntroductionApply", hsTalentIntroductionApply);
		model.addAttribute("currentUser", hsTalentIntroductionApply.currentUser());
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentIntroductionApplyList";
	}
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = {"auditList", ""})
	public String auditList(HsTalentIntroductionApply hsTalentIntroductionApply, Model model) {
		model.addAttribute("hsTalentIntroductionApply", hsTalentIntroductionApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentIntroductionApplyAuditList";
	}
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = {"listDone", ""})
	public String listDone(HsTalentIntroductionApply hsTalentIntroductionApply, Model model) {
		model.addAttribute("hsTalentIntroductionApply", hsTalentIntroductionApply);
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentIntroductionApplyDoneList";
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsTalentIntroductionApply> listData(HsTalentIntroductionApply hsTalentIntroductionApply, HttpServletRequest request, HttpServletResponse response) {
		hsTalentIntroductionApply.setPage(new Page<>(request, response));
		hsTalentIntroductionApplyService.addDataScopeFilter(hsTalentIntroductionApply);
		Page<HsTalentIntroductionApply> page = hsTalentIntroductionApplyService.findPage(hsTalentIntroductionApply);
		return page;
	}

	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = "auditListData")
	@ResponseBody
	public Page<HsTalentIntroductionApply> auditListData(HsTalentIntroductionApply hsTalentIntroductionApply, HttpServletRequest request, HttpServletResponse response) {
		hsTalentIntroductionApply.setPage(new Page<>(request, response));
		Page<HsTalentIntroductionApply> page = hsTalentIntroductionApplyService.findAuditPageByTask(hsTalentIntroductionApply);
		return page;
	}

	/**
	 * 查询已办的审批任务-人才引进补助申请已办
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = "listAuditedData")
	@ResponseBody
	public Page<HsTalentIntroductionApply> listAuditedData(HsTalentIntroductionApply hsTalentIntroductionApply, HttpServletRequest request, HttpServletResponse response) {
		hsTalentIntroductionApply.setPage(new Page<>(request, response));
		Page<HsTalentIntroductionApply> page = hsTalentIntroductionApplyService.findApplyPageByTask(hsTalentIntroductionApply, null, "2", "talent_subsidy_apply");
		return page;
	}


	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = "form")
	public String form(HsTalentIntroductionApply hsTalentIntroductionApply, Model model) {
		model.addAttribute("hsTalentIntroductionApply", hsTalentIntroductionApply);

		if (hsTalentIntroductionApply.getApplyStatus() == null) {
			hsTalentIntroductionApply.setApplyStatus(-1);
		}

		String isRead = "false";
		if (hsTalentIntroductionApply.getApplyStatus() >= 2) {
			isRead = "true";
		}
		model.addAttribute("isRead", isRead);

		model.addAttribute("confList", hsTalentIntroductionConfService.findList(new HsTalentIntroductionConf()));
		model.addAttribute("ctrlPermi", Global.getConfig("user.adminCtrlPermi", "2"));
		return "modules/talent/hsTalentIntroductionApplyForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsTalentIntroductionApply hsTalentIntroductionApply) {
		boolean updateFlag = hsTalentIntroductionApply.getApplyStatus() != null && hsTalentIntroductionApply.getApplyStatus() == 0;
		if ("update".equals(hsTalentIntroductionApply.getOpType())) {
			updateFlag = true;
		}
		hsTalentIntroductionApplyService.save(hsTalentIntroductionApply);
		if (hsTalentIntroductionApply.getApplyStatus() == null || hsTalentIntroductionApply.getApplyStatus() <= 1) {
			if (updateFlag) {
				return renderResult(Global.TRUE, text("更新并保存人才住房补助申请成功！"));
			}
			return renderResult(Global.TRUE, text("保存人才住房补助申请成功！"));
		} else {
			return renderResult(Global.TRUE, text("提交成功！"));
		}
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:edit")
	@PostMapping(value = "flushTaskStatus")
	@ResponseBody
	public String flushTaskStatus(@Validated HsTalentIntroductionApply hsTalentIntroductionApply) {
		hsTalentIntroductionApplyService.flushTaskStatus(hsTalentIntroductionApply);
		return renderResult(Global.TRUE, text("刷新人才住房补助申请状态成功！"));
	}
	
	/**
	 * 停用数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:edit")
	@RequestMapping(value = "disable")
	@ResponseBody
	public String disable(HsTalentIntroductionApply hsTalentIntroductionApply) {
		hsTalentIntroductionApply.setStatus(HsTalentIntroductionApply.STATUS_DISABLE);
		hsTalentIntroductionApplyService.updateStatus(hsTalentIntroductionApply);
		return renderResult(Global.TRUE, text("停用人才住房补助申请表成功"));
	}
	
	/**
	 * 启用数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:edit")
	@RequestMapping(value = "enable")
	@ResponseBody
	public String enable(HsTalentIntroductionApply hsTalentIntroductionApply) {
		hsTalentIntroductionApply.setStatus(HsTalentIntroductionApply.STATUS_NORMAL);
		hsTalentIntroductionApplyService.updateStatus(hsTalentIntroductionApply);
		return renderResult(Global.TRUE, text("启用人才住房补助申请表成功"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsTalentIntroductionApply hsTalentIntroductionApply) {
		if (!HsTalentIntroductionApply.STATUS_DRAFT.equals(hsTalentIntroductionApply.getStatus())){
			if (hsTalentIntroductionApply.getApplyStatus() > 0 && hsTalentIntroductionApply.getApplyStatus() != 100) {
				return renderResult(Global.FALSE, text("只能删除草稿状态的数据！"));
			}
		}
		hsTalentIntroductionApplyService.delete(hsTalentIntroductionApply);
		return renderResult(Global.TRUE, text("删除人才住房补助申请表成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = "hsTalentIntroductionApplySelect")
	public String hsTalentIntroductionApplySelect(HsTalentIntroductionApply hsTalentIntroductionApply, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsTalentIntroductionApply", hsTalentIntroductionApply);
		return "modules/talent/hsTalentIntroductionApplySelect";
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsTalentIntroductionApply hsTalentIntroductionApply, HttpServletResponse response) {
		hsTalentIntroductionApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		List<HsTalentIntroductionApply> list = hsTalentIntroductionApplyService.findList(hsTalentIntroductionApply);
		String fileName = "人才住房补助申请信息" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("人才住房补助申请信息", HsTalentIntroductionApply.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}
	/**
	 * 导出数据
	 */
	@RequiresPermissions("talent:hsTalentIntroductionApply:view")
	@RequestMapping(value = "exportAuditData")
	public void exportAuditData(HsTalentIntroductionApply hsTalentIntroductionApply, HttpServletResponse response) {
		hsTalentIntroductionApply.sqlMap().getWhere().disableAutoAddStatusWhere();
		List<HsTalentIntroductionApply> list = hsTalentIntroductionApplyService.findAuditByTask(hsTalentIntroductionApply);
		String fileName = "人才住房补助申请信息" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("人才住房补助申请信息", HsTalentIntroductionApply.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	
}