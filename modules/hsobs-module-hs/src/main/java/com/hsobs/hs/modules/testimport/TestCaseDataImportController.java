package com.hsobs.hs.modules.testimport;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.hsobs.hs.modules.house.dao.HsQwPublicRentalHouseDao;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.hsobs.hs.modules.rentfee.service.HsQwRentalFeeService;
import com.hsobs.hs.modules.utils.ChineseToPinyinUtil;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.SpringUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.bpm.entity.BpmParams;
import com.jeesite.modules.bpm.entity.BpmTask;
import com.jeesite.modules.bpm.service.BpmTaskService;
import com.jeesite.modules.bpm.utils.BpmUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.service.EmpUserService;

import com.jeesite.modules.sys.utils.UserUtils;
import dm.jdbc.b.l;

import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.transaction.Transactional;
import java.io.File;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 测试数据导入
 */
@Controller
@RequestMapping(value = "/testCase")
public class TestCaseDataImportController extends BaseController {

	@Autowired
	private HsQwRentalFeeService hsQwRentalFeeService;

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

	@Autowired
	private HsQwPublicRentalHouseDao hsQwPublicRentalHouseDao;

	@Autowired
	private HsQwApplyerService hsQwApplyerService;

	@Autowired
	private HsQwApplyService hsQwApplyService;

	@Autowired
	private HsQwApplyDao hsQwApplyDao;

	@Autowired
	private EmpUserService empUserService;

	@Autowired
	private HsQwCompactService hsQwCompactService;

	private BpmTaskService bpmTaskService = BpmUtils.getBpmTaskService();

	// 小区名映射map
	Map<String, String> estMap = new HashMap(){
		{
			put("五四北公租房一期", "1915957186090897408");
			put("屏西公租房", "1915955991355985920");
		}
	};

	/**
	 * 导入excel数据
	 * @return
	 */
	@RequestMapping(value = "importByExcel")
	public void importByExcel() {
		// 从本地excel读取
		try {
			File classPath = new File(TestCaseDataImportController.class.getResource("/").getFile());
			String fileName = classPath.getParentFile().getAbsoluteFile() + "/公租房档案数据含入住情况.xlsx";
			// 插入修改楼盘信息
			this.insertOrUpdateEstateInfo(new ExcelImport(fileName, 1));
			// 解析房源、申请信息
			this.insertApplyInfo(new ExcelImport(fileName, 1));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("导入失败：" + e.getMessage());
		}
	}

	private void insertApplyInfo(ExcelImport ei) {
		// 初始化数据
		List<Map<String, String>> dataList = this.initMap(ei);
		
		logger.error("开始处理数据，总计 " + dataList.size() + " 条记录");
		long startTime = System.currentTimeMillis();

		// 处理数据并映射到实体
		processDataToEntities(dataList);
		
		long endTime = System.currentTimeMillis();
		logger.error("导入成功，共处理 " + dataList.size() + " 条记录，耗时 " + (endTime - startTime) / 1000 + " 秒");
	}

	/**
	 * 初始化数据
	 * @param ei
	 * @return
	 */
	private List<Map<String, String>> initMap(ExcelImport ei){
		// 存储所有行数据
		List<Map<String, String>> dataList = new ArrayList<>();

		// 获取表头
		Row headerRow = ei.getRow(ei.getDataRowNum() - 1);
		List<String> headers = new ArrayList<>();
		for (int j = 0; j < ei.getLastCellNum(); j++) {
			Object headerVal = ei.getCellValue(headerRow, j);
			headers.add(headerVal != null ? headerVal.toString() : "");
		}

		// 读取数据行
		for (int i = ei.getDataRowNum(); i < ei.getLastDataRowNum(); i++) {
			Row row = ei.getRow(i);
			if (row == null) {
				continue;
			}

			Map<String, String> rowData = new HashMap<>();
			for (int j = 0; j < ei.getLastCellNum(); j++) {
				Object val = ei.getCellValue(row, j);
				String headerName = j < headers.size() ? headers.get(j) : "Column" + j;
				rowData.put(headerName, val != null ? val.toString() : "");
			}
			dataList.add(rowData);
		}
		return dataList;
	}

	private void insertOrUpdateEstateInfo(ExcelImport ei) {
		// List<Map<String, String>> dataList = this.initMap(ei);
		// for (Map<String, String> row : dataList) {
		// 	// 创建并保存楼盘信息
		// 	Stringrow.get("楼盘名称");
		// 	row.get("楼盘地址");
		// }
		// // TODO Auto-generated method stub
		// throw new UnsupportedOperationException("Unimplemented method 'insertOrUpdateEstateInfo'");
	}

	/**
	 * 将数据映射到不同的实体
	 * @param dataList Excel数据列表
	 */
    public void processDataToEntities(List<Map<String, String>> dataList) {
		// 创建线程池
		int processors = Runtime.getRuntime().availableProcessors();
		int threadCount = Math.min(processors * 2, 10); // 线程数为CPU核心数的2倍，但不超过10
		ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
		
		logger.error("创建线程池，使用 " + threadCount + " 个线程进行处理");
		
		// 计算每个线程处理的数据量
		int totalSize = dataList.size();
		int batchSize = Math.max(1, totalSize / threadCount);
		
		logger.error("每个线程处理约 " + batchSize + " 条数据");
		
		// 创建任务列表
		List<Future<?>> futures = new ArrayList<>();
		
		// 创建进度计数器
		final AtomicInteger processedCount = new AtomicInteger(0);
		final int reportInterval = Math.max(1, totalSize / 20); // 每处理5%的数据报告一次进度
		
		// 分批提交任务
		for (int i = 0; i < totalSize; i += batchSize) {
			final int batchIndex = i / batchSize + 1;
			int endIndex = Math.min(i + batchSize, totalSize);
			List<Map<String, String>> subList = dataList.subList(i, endIndex);
			
			logger.error("提交批次 " + batchIndex + "，数据范围: " + (i+1) + "-" + endIndex);
			
			// 提交任务
			Future<?> future = executorService.submit(() -> {
				// 为每个线程创建独立的事务
				TransactionTemplate transactionTemplate = new TransactionTemplate(
						SpringUtils.getBean(PlatformTransactionManager.class));
				
				logger.error("批次 " + batchIndex + " 开始处理，共 " + subList.size() + " 条数据");
				long batchStartTime = System.currentTimeMillis();
				
				transactionTemplate.execute(status -> {
					try {
						// 处理每个批次的数据
						for (Map<String, String> row : subList) {
							// 为每条记录创建独立的事务
							TransactionTemplate singleRowTransaction = new TransactionTemplate(
									SpringUtils.getBean(PlatformTransactionManager.class));
							
							singleRowTransaction.execute(rowStatus -> {
								try {
									processOneRow(row);
									return null;
								} catch (Exception e) {
									rowStatus.setRollbackOnly();
									System.err.println("处理记录失败: " + e.getMessage());
									e.printStackTrace();
									return null;
								}
							});
							
							// 更新进度计数
							int currentCount = processedCount.incrementAndGet();
							if (currentCount % reportInterval == 0 || currentCount == totalSize) {
								double percentage = (double) currentCount / totalSize * 100;
								logger.error(String.format("总进度: %.2f%% (%d/%d)", percentage, currentCount, totalSize));
							}
						}
						return null;
					} catch (Exception e) {
						status.setRollbackOnly();
						System.err.println("批次 " + batchIndex + " 处理失败: " + e.getMessage());
						e.printStackTrace();
						return null;
					}
				});
				
				long batchEndTime = System.currentTimeMillis();
				logger.error("批次 " + batchIndex + " 处理完成，耗时 " + (batchEndTime - batchStartTime) / 1000 + " 秒");
			});
			
			futures.add(future);
		}
		
		logger.error("所有批次已提交，等待处理完成...");
		
		// 等待所有任务完成
		for (Future<?> future : futures) {
			try {
				future.get();
			} catch (InterruptedException | ExecutionException e) {
				System.err.println("任务执行异常: " + e.getMessage());
				e.printStackTrace();
			}
		}
		
		logger.error("所有批次处理完成，关闭线程池");
		
		// 关闭线程池
		executorService.shutdown();
		try {
			if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
				logger.error("线程池未能在60秒内关闭，强制关闭");
				executorService.shutdownNow();
			}
		} catch (InterruptedException e) {
			System.err.println("关闭线程池时被中断: " + e.getMessage());
			executorService.shutdownNow();
			Thread.currentThread().interrupt();
		}
	}
	
	/**
	 * 处理单行数据
	 */
	@Transactional
    public void processOneRow(Map<String, String> row) {
		// 1. 创建并保存房屋信息
		HsQwPublicRentalHouse house = createHouseEntity(row);
//		// 保存house实体到数据库
//		if (house.getId() != null) {
//			hsQwPublicRentalHouseService.update(house);
//			logger.error("更新房屋信息: ID=" + house.getId());
//		} else {
//			hsQwPublicRentalHouseService.save(house);
//			logger.error("新增房屋信息: ID=" + house.getId());
//		}
//
//		// 2. 创建并保存申请单
		HsQwApply apply = this.createHsQwApply(row, house);
//		List<HsQwApplyer> applyerList = new ArrayList<>();
//
//		// 3. 创建主申请人信息
//		String mainName = row.get("申请人");
//		String mainIdNum = row.get("身份证号码");
//		String mainOrg = row.get("工作单位");
//		HsQwApplyer mainApplyer = createHsQwApplyerEntity(mainName, mainIdNum, mainOrg, apply.getId(), "0");
//		mainApplyer.setPhone(row.get("联系电话"));
//		mainApplyer.setWorkTime(new Date());
//		mainApplyer.setWorkAge("3");
//		// 3.1 创建主申请用户并赋予普通用户角色
//		EmpUser user = this.createEmpUser(mainApplyer);
//		mainApplyer.setUserId(user.getId());
//		apply.setMainApplyer(mainApplyer);
//		applyerList.add(mainApplyer);
//
//		// 4. 创建并保存配偶信息
//		if (hasValue(row, "配偶")) {
//			HsQwApplyer spouse = createHsQwApplyerEntity(row.get("配偶"), row.get("配偶身份证号码"), "", apply.getId(), "1");
//			spouse.setPhone(mainApplyer.getPhone());
//			spouse.setWorkTime(new Date());
//			spouse.setWorkAge("3");
//			spouse.setUserId(user.getId());
//			applyerList.add(spouse);
//		}
//
//		// 5. 创建并保存家庭成员信息
//		for (int i = 1; i <= 3; i++) {
//			if (hasValue(row, "家庭成员" + i)) {
//				HsQwApplyer familyMember = createHsQwApplyerEntity(row.get("家庭成员" + i), row.get("家庭成员" + i + "身份证号码"), "", apply.getId(), "1");
//				familyMember.setPhone(mainApplyer.getPhone());
//				familyMember.setWorkTime(new Date());
//				familyMember.setWorkAge("3");
//				familyMember.setUserId(user.getId());
//				applyerList.add(familyMember);
//			}
//		}
//
//		apply.setFamilyPeoples(applyerList.size() + "");
//		apply.setHsQwApplyerList(applyerList);
//
//		// 6.设置主申请用户信息
//		apply.setProxyUserId(user.getId());
//		// 7.提交表单
//		hsQwApplyService.save(apply);
//		// 8.刷新评分
//		hsQwApplyService.refreshByApply(apply);
//		// 9.直接签收，并转到最后一个环节
//		BpmTask bpmTask = bpmTaskService.getTaskByBusinessKey("rent_apply", apply.getId(), null);
//		if (bpmTask != null) {
//			bpmTaskService.claimTask(bpmTask);
//			bpmTask.setActivityId("sefd4bae82");
//			bpmTask.setNextUserCodes("13509314691_zblk");
//			bpmTaskService.moveTask(bpmTask);
//		}
//		// 10.签收并完成流程
//		bpmTask = bpmTaskService.getTaskByBusinessKey("rent_apply", apply.getId(), null);
//		if (bpmTask != null) {
//			bpmTaskService.claimTask(bpmTask);
//			apply.setIsNewRecord(false);
//			this.setApplyerIsNewStatus(apply.getHsQwApplyerList());
//			BpmParams params = new BpmParams();
//			params.setTaskId(bpmTask.getId());
//			params.setProcInsId(bpmTask.getProcIns().getId());
//			params.setActivityId(bpmTask.getActivityId());
//			apply.setBpm(params);
//			hsQwApplyService.save(apply);
//		}
//		// 11.更新状态
//		apply.setStatus("0");
//		hsQwApplyService.updateStatus(apply);

		// 12.生成合同信息
		HsQwCompact compact = this.createCompact(apply);
		if (compact.getId()!= null) {
			hsQwCompactService.update(compact);
		} else {
			hsQwCompactService.save(compact);
		}
	}

	/**
	 *
	 * @param apply
	 * @return
	 */
	private HsQwCompact createCompact(HsQwApply apply) {
		HsQwCompact compact = this.getExistCompact(apply.getId());
		compact.setApplyId(apply.getId());
		// 随机生成合同编号
		Random random = new Random();
		int randomNumber = random.nextInt(500000 - 100000 + 1) + 100000;
		compact.setCompactCode("ZJHT" + DateUtils.getDate("yyyyMMddHHmmss") + randomNumber + "");
		// 随机生成3-10之间的随机数
		int randomYear = random.nextInt(10 - 3 + 1) + 3;
		compact.setStartDate(DateUtils.addDays(new Date(), -(randomYear * 365)));
		// 随机生成1-3之间的随机数
		int randomDual = random.nextInt(3 - 1 + 1) + 1;
		compact.setEndDate(DateUtils.addDays(new Date(), -(randomYear - randomDual)*365));
		// 随机生成1000-3000之间的随机数
		int randomRent = random.nextInt(3000 - 1000 + 1) + 1000;
		compact.setMonthFee(Double.valueOf(randomRent));
		compact.setStatus("0");
		compact.setCreateBy(UserUtils.getUser().getId());
		compact.setUpdateBy(UserUtils.getUser().getId());
		compact.setCreateDate(new Date());
		compact.setUpdateDate(new Date());
		return compact;
	}

	private HsQwCompact getExistCompact(String id) {
		HsQwCompact query = new HsQwCompact();
		query.setApplyId(id);
		query.setStatus("0");
		HsQwCompact compact = hsQwCompactService.findList(query).stream().findFirst().orElse(null);
		if (compact == null) {
			compact = new HsQwCompact();
		}
		return compact;
	}

	private void setApplyerIsNewStatus(List<HsQwApplyer> hsQwApplyerList) {
		hsQwApplyerList.forEach(hsQwApplyer -> hsQwApplyer.setIsNewRecord(false));
	}

	private EmpUser createEmpUser(HsQwApplyer mainApplyer) {
		EmpUser empUser = new EmpUser();
		EmpUser query = new EmpUser();
		String loginCode = ChineseToPinyinUtil.getFirstLetters(mainApplyer.getName()) + mainApplyer.getIdNum().substring(mainApplyer.getIdNum().length() - 4);
		String userCode = loginCode + "_0001";
		query.setLoginCode(loginCode);
		query.setUserCode(userCode);
		empUser = empUserService.get(query);
		if (empUser == null) {
			empUser = new EmpUser();
			empUser.setUserName(mainApplyer.getName());
			empUser.setPassword("123456");
			empUser.setUserType("employee");
			empUser.setUserCode(userCode);
			empUser.setLoginCode(loginCode);
			empUser.setMgrType("0");
			empUser.setStatus("0");
			empUser.setCreateBy("13509314691_zblk");
			empUser.setUpdateBy("13509314691_zblk");
			empUser.setCreateDate(new Date());
			empUser.setUpdateDate(new Date());
			empUserService.save(empUser);
		}
		return empUser;
	}

	private HsQwApplyer createHsQwApplyerEntity(String name, String idNum, String org, String applyId, String applyRole) {
		HsQwApplyer hsQwApplyer = new HsQwApplyer();
		hsQwApplyer.setApplyId(applyId);
		hsQwApplyer.setName(name);
		hsQwApplyer.setIdNum(idNum);
		hsQwApplyer.setOrganization(org);
		hsQwApplyer.setApplyRole(applyRole);
		return hsQwApplyer;
	}



	private HsQwApply createHsQwApply(Map<String, String> row, HsQwPublicRentalHouse house) {
		HsQwApply apply = this.getExistApply(house.getId());
		// 取100000-500000之间随机值
		Random random = new Random();
		int randomNumber = random.nextInt(500000 - 100000 + 1) + 100000;
		apply.setFamilyIncome(randomNumber + "");
		// 取2-5之间的随机数
		int randomFamily = random.nextInt(5 - 2 + 1) + 2;
		apply.setFamilyPeoples(randomFamily + "");
		apply.setHouseId(house.getId());
		apply.setApplyMatter("0");
		apply.setCreateBy("13509314691_zblk");
		apply.setUpdateBy("13509314691_zblk");
		apply.setCreateDate(new Date());
		apply.setUpdateDate(new Date());
		apply.setStatus("0");
		apply.setOfficeCode("0000064");
		apply.setApplyTime(new Date());
		apply.setApplyAccepted("0");
		apply.setEligible("0");
		apply.setRecheckStatus("0");
		apply.setRecheckAudit("0");
		apply.setOfflineRent("0");
		apply.setRentTime(new Date());
		return apply;
	}

	private HsQwApply getExistApply(String id) {
		HsQwApply query = new HsQwApply();
		query.setHouseId(id);
		query.setStatus("0");
		List<HsQwApply> list = hsQwApplyService.findList(query);
		return list!= null && list.size() > 0? list.get(0) : new HsQwApply();
	}

	/**
	 * 创建房屋实体
	 */
	private HsQwPublicRentalHouse createHouseEntity(Map<String, String> row) {
		HsQwPublicRentalHouse house = null;
		String houseInfo = row.get("现租房位置");
		String estateInfo ="";
		String buildingNum = "";
		String unitInfo = "";
		// 添加空值检查，避免NullPointerException
		if (houseInfo != null && !houseInfo.isEmpty()) {
			// 定义正则表达式
			String regex = "(.*?)(\\d+)-([\\d+]*)";
			// 使用正则表达式进行匹配
			java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
			java.util.regex.Matcher matcher = pattern.matcher(houseInfo);
			if (matcher.find()) {
				estateInfo = matcher.group(1);
				buildingNum = matcher.group(2);
				unitInfo = matcher.group(3);
				house = new HsQwPublicRentalHouse();
				house.setBuildingNum(buildingNum);
				// 解析unitInfo，最后两位是房间号，前面余下的是楼层号
				if (unitInfo!= null &&!unitInfo.isEmpty()) {
					String floorNum = unitInfo.substring(0, unitInfo.length() - 2);
					house.setFloor(Long.valueOf(floorNum));
					house.setUnitNum(unitInfo);
					house.setHouseNum(unitInfo);
					house.setBuildingArea(row.get("面积"));
					// 根据estMap获取estateId
					String estateId = estMap.get(estateInfo);
					house.setEstateId(estateId);
					// 判断居室类型
					house = this.getExistHouse(house);
					String houseType = this.getHouseType(house.getBuildingArea());
					house.setHouseType(houseType);
					house.setIsPublic("1");
					house.setStatus("0");
					house.setCreateBy("13509314691_zblk");
					house.setUpdateBy("13509314691_zblk");
					house.setCreateDate(new Date());
					house.setUpdateDate(new Date());
					house.setType("0");
					house.setOfficeCode("0000064");
					house.setHouseStatus("1");
					house.setSharedArea(5.0);
					house.setHouseSaleStatus("0");
					house.setSupportArea(0.0);
					house.setLocation(estateInfo);
				}
			}
		}
		return house;
	}

	private HsQwPublicRentalHouse getExistHouse(HsQwPublicRentalHouse house) {
		HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
		query.setEstateId(house.getEstateId());
		query.setBuildingNum(house.getBuildingNum());
		query.setFloor(house.getFloor());
		query.setUnitNum(house.getUnitNum());
		query.setHouseNum(house.getHouseNum());
		List<HsQwPublicRentalHouse> list = hsQwPublicRentalHouseService.findList(query);
		if (list!= null && list.size() > 0) {
			return list.get(0);
		}
		return house;
	}

	/**
	 *      {"单居室", "建筑面积 45 平方米左右的单居室户型"},
            {"两居室", "建筑面积 60 平方米左右的双居室户型"},
            {"2.5居室", "建筑面积 80 平方米左右的2.5居室户型"},
            {"其他", "其他户型 "}
	 * @param buildingArea
	 * @return
	 */
	private String getHouseType(
			String buildingArea) {
		// TODO Auto-generated method stub
		if (buildingArea!= null &&!buildingArea.isEmpty()) {
			Double area = Double.valueOf(buildingArea);
			if (area < 45) {
				return "0";
			}
			if (area < 60) {
				return "1";
			}
			if (area < 80) {
				return "2";
			}
			return "3";
		} else {
			return "3";
		}
	}

	/**
	 * 检查字段是否有值
	 */
	private boolean hasValue(Map<String, String> row, String key) {
		return row.containsKey(key) && row.get(key) != null && !row.get(key).trim().isEmpty();
	}

}