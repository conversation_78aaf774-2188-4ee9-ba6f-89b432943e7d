package com.hsobs.hs.modules.formmanage.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormDeliveryRecord;
import com.hsobs.hs.modules.formmanage.dao.HsDataFormDeliveryRecordDao;

/**
 * 数据表单下发发送记录表Service
 * <AUTHOR>
 * @version 2025-02-16
 */
@Service
public class HsDataFormDeliveryRecordService extends CrudService<HsDataFormDeliveryRecordDao, HsDataFormDeliveryRecord> {
	
	/**
	 * 获取单条数据
	 * @param hsDataFormDeliveryRecord
	 * @return
	 */
	@Override
	public HsDataFormDeliveryRecord get(HsDataFormDeliveryRecord hsDataFormDeliveryRecord) {
		return super.get(hsDataFormDeliveryRecord);
	}
	
	/**
	 * 查询分页数据
	 * @param hsDataFormDeliveryRecord 查询条件
	 * @param hsDataFormDeliveryRecord page 分页对象
	 * @return
	 */
	@Override
	public Page<HsDataFormDeliveryRecord> findPage(HsDataFormDeliveryRecord hsDataFormDeliveryRecord) {
		hsDataFormDeliveryRecord.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findPage(hsDataFormDeliveryRecord);
	}
	
	/**
	 * 查询列表数据
	 * @param hsDataFormDeliveryRecord
	 * @return
	 */
	@Override
	public List<HsDataFormDeliveryRecord> findList(HsDataFormDeliveryRecord hsDataFormDeliveryRecord) {
		return super.findList(hsDataFormDeliveryRecord);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsDataFormDeliveryRecord
	 */
	@Override
	@Transactional
	public void save(HsDataFormDeliveryRecord hsDataFormDeliveryRecord) {
		super.save(hsDataFormDeliveryRecord);
	}
	
	/**
	 * 更新状态
	 * @param hsDataFormDeliveryRecord
	 */
	@Override
	@Transactional
	public void updateStatus(HsDataFormDeliveryRecord hsDataFormDeliveryRecord) {
		super.updateStatus(hsDataFormDeliveryRecord);
	}
	
	/**
	 * 删除数据
	 * @param hsDataFormDeliveryRecord
	 */
	@Override
	@Transactional
	public void delete(HsDataFormDeliveryRecord hsDataFormDeliveryRecord) {
		super.delete(hsDataFormDeliveryRecord);
	}

	public void insertBatch(List<HsDataFormDeliveryRecord> recordList, int batchNum) {
		if (recordList == null || recordList.isEmpty()) {
			return;
		}
		//TODO 待修改为批量插入
		recordList.forEach(this::save);
//		this.dao.insertBatch(recordList, num);
	}

    public void updateReadStatus(HsDataFormDeliveryRecord record) {
		this.dao.updateReadStatus(record);
    }
}