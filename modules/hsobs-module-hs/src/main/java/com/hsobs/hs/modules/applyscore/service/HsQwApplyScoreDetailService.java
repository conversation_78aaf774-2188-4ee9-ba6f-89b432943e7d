package com.hsobs.hs.modules.applyscore.service;

import com.hsobs.hs.modules.applyscore.dao.HsQwApplyScoreDetailDao;
import com.hsobs.hs.modules.applyscore.entity.HsQwApplyScoreDetail;
import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * hs_qw_apply_score_detailService
 *
 * <AUTHOR>
 * @version 2025-03-22
 */
@Service
public class HsQwApplyScoreDetailService extends CrudService<HsQwApplyScoreDetailDao, HsQwApplyScoreDetail> {

    /**
     * 获取单条数据
     *
     * @param hsQwApplyScoreDetail
     * @return
     */
    @Override
    public HsQwApplyScoreDetail get(HsQwApplyScoreDetail hsQwApplyScoreDetail) {
        return super.get(hsQwApplyScoreDetail);
    }

    /**
     * 查询分页数据
     *
     * @param hsQwApplyScoreDetail 查询条件
     * @param hsQwApplyScoreDetail page 分页对象
     * @return
     */
    @Override
    public Page<HsQwApplyScoreDetail> findPage(HsQwApplyScoreDetail hsQwApplyScoreDetail) {
        return super.findPage(hsQwApplyScoreDetail);
    }

    /**
     * 查询列表数据
     *
     * @param hsQwApplyScoreDetail
     * @return
     */
    @Override
    public List<HsQwApplyScoreDetail> findList(HsQwApplyScoreDetail hsQwApplyScoreDetail) {
        return super.findList(hsQwApplyScoreDetail);
    }

    /**
     * 保存数据（插入或更新）
     *
     * @param hsQwApplyScoreDetail
     */
    @Override
    @Transactional
    public void save(HsQwApplyScoreDetail hsQwApplyScoreDetail) {
        super.save(hsQwApplyScoreDetail);
    }

    /**
     * 更新状态
     *
     * @param hsQwApplyScoreDetail
     */
    @Override
    @Transactional
    public void updateStatus(HsQwApplyScoreDetail hsQwApplyScoreDetail) {
        super.updateStatus(hsQwApplyScoreDetail);
    }

    /**
     * 删除数据
     *
     * @param hsQwApplyScoreDetail
     */
    @Override
    @Transactional
    public void delete(HsQwApplyScoreDetail hsQwApplyScoreDetail) {
        super.delete(hsQwApplyScoreDetail);
    }

    public void saveBatch(List<HsQwApplyScoreDetail> scoreDetails) {
        // 批量插入
        scoreDetails.forEach(scoreDetail -> {
            this.save(scoreDetail);
        });
    }

    public void deleteByApplyId(String id) {
        HsQwApplyScoreDetail where = new HsQwApplyScoreDetail();
        where.setApplyId(id);
        HsQwApplyScoreDetail entity = new HsQwApplyScoreDetail();
        entity.setStatus(HsQwApplyScoreDetail.STATUS_DELETE);
        this.dao.updateStatusByEntity(entity, where);
    }
}