package com.hsobs.hs.modules.external.entity;

import com.hsobs.hs.modules.external.entity.validated.NotNullApplyConfirm;
import com.hsobs.hs.modules.external.entity.validated.NotNullApplyMatterGroup;
import com.hsobs.hs.modules.external.entity.validated.NotNullApplyReplaceUp;
import com.hsobs.hs.modules.external.entity.validated.NotNullApplyShowing;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

public class ApiHsQwApply extends ApiBody{


    @HsMapTo("id")
    @NotBlank(message = "申请单号不能为空", groups = {NotNullApplyShowing.class, NotNullApplyConfirm.class, NotNullApplyReplaceUp.class})
    private String sqdh; //申请单号

    @HsMapTo("officeCode")
    private String sqzfdwbm;

    @HsMapTo("familyIncome")
    private String jtzsr;

    @HsMapTo("familyPeoples")
    private String jtzrs;

    @HsMapTo("applyedId")
    private String jsqdh;

    @HsMapTo("remarks")
    private String remark;

    @HsMapTo("changeReason")
    private String bgly; //变更理由

    @HsMapTo("applyAccepted")
    @NotBlank(message = "看房确认状态不能为空", groups = { NotNullApplyConfirm.class})
    private String czqrzt;

    @HsMapTo("recheckStatus")
    @NotBlank(message = "复查不能为空", groups = { NotNullApplyReplaceUp.class})
    private String fczt; //复查状态（0：参加并前往填写复查信息 1：放弃本次配租（本次不参加） 2：放弃本次配租（已不符合条件））

    @NotBlank(message = "申请类型不可为空")
//    @HsMapTo("applyMatter")
    private String sqlx;

    @HsMapTo("processName")
    private String processName; //流程名称

    @HsMapTo("offlineRent")
    @NotBlank(message = "看房确认状态不能为空", groups = {NotNullApplyShowing.class})
    private String kfqrzt;

    @HsMapTo("hsQwApplyerList")
    @Valid
    private List<ApiHsQwApplyer> gtsqList;

    @HsMapTo("hsQwApplyHouseList")
    @Valid
    private List<ApiHsQwHouse> jtzfqkList;

    @HsMapFile
    private List<ApiFile> fileList;

    @Size(min = 0, max = 20, message = "单位编码不能超过 20 个字符")
    public String getSqzfdwbm() {
        return sqzfdwbm;
    }

    public void setSqzfdwbm(String sqzfdwbm) {
        this.sqzfdwbm = sqzfdwbm;
    }

    public String getJtzsr() {
        return jtzsr;
    }

    public void setJtzsr(String jtzsr) {
        this.jtzsr = jtzsr;
    }

    public String getJtzrs() {
        return jtzrs;
    }

    public void setJtzrs(String jtzrs) {
        this.jtzrs = jtzrs;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSqlx() {
        return sqlx;
    }

    public void setSqlx(String sqlx) {
        this.sqlx = sqlx;
    }

    public List<ApiHsQwApplyer> getGtsqList() {
        return gtsqList;
    }

    public void setGtsqList(List<ApiHsQwApplyer> gtsqList) {
        this.gtsqList = gtsqList;
    }

    public List<ApiHsQwHouse> getJtzfqkList() {
        return jtzfqkList;
    }

    public void setJtzfqkList(List<ApiHsQwHouse> jtzfqkList) {
        this.jtzfqkList = jtzfqkList;
    }

    public List<ApiFile> getFileList() {
        return fileList;
    }

    public void setFileList(List<ApiFile> fileList) {
        this.fileList = fileList;
    }

    public String getSqdh() {
        return sqdh;
    }

    public void setSqdh(String sqdh) {
        this.sqdh = sqdh;
    }

    public String getJsqdh() {
        return jsqdh;
    }

    public void setJsqdh(String jsqdh) {
        this.jsqdh = jsqdh;
    }

    public String getBgly() {
        return bgly;
    }

    public void setBgly(String bgly) {
        this.bgly = bgly;
    }

    public String getCzqrzt() {
        return czqrzt;
    }

    public void setCzqrzt(String czqrzt) {
        this.czqrzt = czqrzt;
    }

    public String getFczt() {
        return fczt;
    }

    public void setFczt(String fczt) {
        this.fczt = fczt;
    }

    public String getKfqrzt() {
        return kfqrzt;
    }

    public void setKfqrzt(String kfqrzt) {
        this.kfqrzt = kfqrzt;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }
}
