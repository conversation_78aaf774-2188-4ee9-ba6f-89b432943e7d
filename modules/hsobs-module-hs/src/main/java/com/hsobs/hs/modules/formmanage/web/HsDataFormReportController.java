package com.hsobs.hs.modules.formmanage.web;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.formmanage.entity.HsDataFormReport;
import com.hsobs.hs.modules.formmanage.service.HsDataFormReportService;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 数据表单报送记录Controller
 * <AUTHOR>
 * @version 2025-02-21
 */
@Controller
@RequestMapping(value = "${adminPath}/formmanage/hsDataFormReport")
public class HsDataFormReportController extends BaseController {

	@Autowired
	private HsDataFormReportService hsDataFormReportService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsDataFormReport get(String id, boolean isNewRecord) {
		return hsDataFormReportService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("formmanage:hsDataFormReport:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsDataFormReport hsDataFormReport, Model model) {
		model.addAttribute("hsDataFormReport", hsDataFormReport);
		return "modules/formmanage/hsDataFormReportList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("formmanage:hsDataFormReport:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsDataFormReport> listData(HsDataFormReport hsDataFormReport, HttpServletRequest request, HttpServletResponse response) {
		hsDataFormReport.setPage(new Page<>(request, response));
		Page<HsDataFormReport> page = hsDataFormReportService.findPage(hsDataFormReport);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("formmanage:hsDataFormReport:view")
	@RequestMapping(value = "form")
	public String form(HsDataFormReport hsDataFormReport, Model model) {
		model.addAttribute("hsDataFormReport", hsDataFormReport);
		return "modules/formmanage/hsDataFormReportForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("formmanage:hsDataFormReport:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsDataFormReport hsDataFormReport) {
		hsDataFormReportService.save(hsDataFormReport);
		return renderResult(Global.TRUE, text("保存数据表单报送记录成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("formmanage:hsDataFormReport:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsDataFormReport hsDataFormReport) {
		hsDataFormReportService.delete(hsDataFormReport);
		return renderResult(Global.TRUE, text("删除数据表单报送记录成功！"));
	}
	
	/**
	 * 列表选择对话框
	 */
	@RequiresPermissions("formmanage:hsDataFormReport:view")
	@RequestMapping(value = "hsDataFormReportSelect")
	public String hsDataFormReportSelect(HsDataFormReport hsDataFormReport, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsDataFormReport", hsDataFormReport);
		return "modules/formmanage/hsDataFormReportSelect";
	}
	
}