package com.hsobs.hs.modules.utils;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.maintenance.entity.HsMaintenanceApply;
import com.hsobs.hs.modules.maintenance.service.HsMaintenanceApplyService;
import com.hsobs.hs.modules.managementcheck.entity.HsQwManagementCheck;
import com.hsobs.hs.modules.managementcheck.service.HsQwManagementCheckService;
import com.hsobs.hs.modules.publicapply.entity.HsPublicApply;
import com.hsobs.hs.modules.publicapply.service.HsPublicApplyService;
import com.jeesite.modules.file.utils.FileUploadUtils;
import org.apache.tomcat.util.http.fileupload.FileUpload;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.job.api.Job;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class FlowableCleanupService {

    private static final Logger logger = LoggerFactory.getLogger(FlowableCleanupService.class);

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private ManagementService managementService;

    @Autowired
    private HsQwApplyService hsQwApplyService;

    @Autowired
    private HsPublicApplyService hsPublicApplyService;

    @Autowired
    private HsMaintenanceApplyService hsMaintenanceApplyService;

    @Autowired
    private HsQwManagementCheckService hsQwManagementCheckService;
    // 定期删除无效的历史流程实例
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨 2 点执行
    @Transactional
    public void deleteInvalidHistoricInstances() {
        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .finished()
                .list();

        for (HistoricProcessInstance instance : instances) {
            if (!isValidBusinessData(instance)) {
                try {
                    // 删除相关的定时任务
                    deleteRelatedJobs(instance.getId());
                    // 删除历史流程实例
                    historyService.deleteHistoricProcessInstance(instance.getId());
                    logger.info("Successfully deleted historic process instance: {}", instance.getId());
                } catch (Exception e) {
                    logger.error("Error deleting historic process instance: " + instance.getId(), e);
                }
            }
        }
    }

    // 定期删除无效的运行中流程实例
    @Scheduled(cron = "0 30 2 * * ?") // 每天凌晨 2:30 执行
    @Transactional
    public void deleteInvalidRunningInstances() {
        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .unfinished()
                .list();

        for (HistoricProcessInstance instance : instances) {
            if (!isValidBusinessData(instance)) {
                try {
                    // 删除相关的定时任务
                    deleteRelatedJobs(instance.getId());
                    // 删除运行中的流程实例
                    runtimeService.deleteProcessInstance(instance.getId(), "Business data deleted");
                    logger.info("Successfully deleted running process instance: {}", instance.getId());
                } catch (Exception e) {
                    logger.error("Error deleting running process instance: " + instance.getId(), e);
                }
            }
        }
    }

    /**
     * 删除流程实例相关的所有定时任务
     * 
     * @param processInstanceId 流程实例ID
     */
    private void deleteRelatedJobs(String processInstanceId) {
        // 查找并删除所有相关的定时任务
        List<Job> jobs = managementService.createJobQuery()
                .processInstanceId(processInstanceId)
                .list();

        for (Job job : jobs) {
            try {
                managementService.deleteJob(job.getId());
                logger.debug("Successfully deleted job: {} for process instance: {}",
                        job.getId(), processInstanceId);
            } catch (Exception e) {
                logger.warn("Failed to delete job: {} for process instance: {}",
                        job.getId(), processInstanceId, e);
            }
        }

        // 查找并删除所有暂停的定时任务
        List<Job> suspendedJobs = managementService.createSuspendedJobQuery()
                .processInstanceId(processInstanceId)
                .list();

        for (Job job : suspendedJobs) {
            try {
                managementService.deleteJob(job.getId());
                logger.debug("Successfully deleted suspended job: {} for process instance: {}",
                        job.getId(), processInstanceId);
            } catch (Exception e) {
                logger.warn("Failed to delete suspended job: {} for process instance: {}",
                        job.getId(), processInstanceId, e);
            }
        }

        // 查找并删除所有死信任务
        List<Job> deadLetterJobs = managementService.createDeadLetterJobQuery()
                .processInstanceId(processInstanceId)
                .list();

        for (Job job : deadLetterJobs) {
            try {
                managementService.deleteDeadLetterJob(job.getId());
                logger.debug("Successfully deleted dead letter job: {} for process instance: {}",
                        job.getId(), processInstanceId);
            } catch (Exception e) {
                logger.warn("Failed to delete dead letter job: {} for process instance: {}",
                        job.getId(), processInstanceId, e);
            }
        }
    }

    private boolean isValidBusinessData(HistoricProcessInstance instance) {
        String processKey = instance.getProcessDefinitionKey();
        String businessKey = instance.getBusinessKey();

        // 公租申请相关流程
        if (processKey.equals("rent_apply") ||
                processKey.equals("rent_apply_house") ||
                processKey.equals("rent_apply_info") ||
                processKey.equals("rent_apply_bureau")) {
            String applyId = businessKey.substring(processKey.length() + 1);
            HsQwApply hsQwApply = hsQwApplyService.get(applyId);
            if (hsQwApply == null) {
                return false;
            }
        }

        // 限价房申请流程
        else if (processKey.equals("public_apply")) {
            String applyId = businessKey;
            HsPublicApply publicApply = hsPublicApplyService.get(applyId);
            if (publicApply == null) {
                return false;
            }
        }

        // 维修资金申请流程
        else if (processKey.equals("maintenance_apply")) {
            String applyId = businessKey;
            HsMaintenanceApply maintenanceApply = hsMaintenanceApplyService.get(applyId);
            if (maintenanceApply == null) {
                return false;
            }
        }

        // 物业核验流程
        else if (processKey.indexOf("management_check")>0) {
            String applyId = businessKey;
            HsQwManagementCheck bean = hsQwManagementCheckService.get(applyId);
            if (bean == null) {
                return false;
            }
        }
        return true;
    }

}
