package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.utils.UserUtils;
import com.jeesite.modules.sys.utils.EmpUtils;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.text.SimpleDateFormat;

/**
 * 住房保障数据统计Entity   kanban
 * <AUTHOR>
 * @version 2025-1-2
 */
public class HsKanban extends DataEntity<HsKanban> {

	private String userId;
	private String mobile;
	private String kanbanName;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getKanbanName() {
		return kanbanName;
	}

	public void setKanbanName(String kanbanName) {
		this.kanbanName = kanbanName;
	}
}
