package com.hsobs.hs.modules.applyrule.service.HsQwApplyRule;

import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRuleResult;
import com.jeesite.common.service.ServiceException;
import org.springframework.stereotype.Service;

@Service
public class HsQwApplyRuleGte implements IHsQwApplyRule {
    @Override
    public String getRuleConfig() {
        return "7";
    }

    @Override
    public HsQwApplyRuleResult execute(String content, HsQwApplyRule rule) {
        HsQwApplyRuleResult ruleResult = new HsQwApplyRuleResult();
        try {
            // 将字符串转换为浮动数字
            double num1 = Double.parseDouble(content);
            double num2 = Double.parseDouble(rule.getRuleContent());

            // 比较两个数字
            if (num2 >= num1) {
                ruleResult.setData(rule.getRuleResult());
                ruleResult.setResult(true);
            }
        } catch (NumberFormatException e) {
            throw new ServiceException("资格轮候评分规则配置【<=】错误，影响评分计算，请及时纠正！");
        }
        return ruleResult;
    }
}
