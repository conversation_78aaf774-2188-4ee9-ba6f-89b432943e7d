package com.hsobs.hs.modules.external.entity;

import java.util.List;

public class ApiHsApplyProcessData extends ApiBody {
    private String sqdh;
    private String sqrmc;
    private String sqrzjh;
    private String sqsj;
    private String sqlx;
    private String sqlr;
    private String sqzt;
    private List<ApiHsProcessDetail> processList;

    public String getSqdh() {
        return sqdh;
    }

    public void setSqdh(String sqdh) {
        this.sqdh = sqdh;
    }

    public String getSqrmc() {
        return sqrmc;
    }

    public void setSqrmc(String sqrmc) {
        this.sqrmc = sqrmc;
    }

    public String getSqrzjh() {
        return sqrzjh;
    }

    public void setSqrzjh(String sqrzjh) {
        this.sqrzjh = sqrzjh;
    }

    public String getSqsj() {
        return sqsj;
    }

    public void setSqsj(String sqsj) {
        this.sqsj = sqsj;
    }

    public String getSqlx() {
        return sqlx;
    }

    public void setSqlx(String sqlx) {
        this.sqlx = sqlx;
    }

    public String getSqlr() {
        return sqlr;
    }

    public void setSqlr(String sqlr) {
        this.sqlr = sqlr;
    }

    public String getSqzt() {
        return sqzt;
    }

    public void setSqzt(String sqzt) {
        this.sqzt = sqzt;
    }

    public List<ApiHsProcessDetail> getProcessList() {
        return processList;
    }

    public void setProcessList(List<ApiHsProcessDetail> processList) {
        this.processList = processList;
    }
}

