package com.hsobs.hs.modules.talent.entity;

import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

import javax.validation.constraints.Size;

/**
 * 人才补助停发报备Entity
 * <AUTHOR>
 * @version 2025-01-03
 */
@Table(name="hs_talent_record_stop", alias="a", label="人才补助停发报备信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true, queryType = QueryType.LIKE),
		@Column(name="talent_id", attrName="talentId", label="人才信息ID"),
		@Column(name="user_no", attrName="userNo", label="人才身份证号码", queryType = QueryType.LIKE),
		@Column(name="stop_reason", attrName="stopReason", label="停发原因"),
		@Column(name="apply_status", attrName="applyStatus", label="申请状态", isUpdateForce=true),
		@Column(name="status", attrName="status", label="状态", isUpdate=true),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsTalentRecord.class, alias = "h",
				on = "h.id = a.talent_id", attrName = "talentRecord",
				columns = {@Column(includeEntity = HsTalentRecord.class)}
		),
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "o",
				on = "o.office_code = h.unit_id", attrName = "applyOffice",
				columns = {
					@Column(includeEntity = Office.class)
		        }
		)
    }, extColumnKeys = "extColumns", extFromKeys = "extForm", extWhereKeys = "extWhere", orderBy="a.update_date DESC"
)
public class HsTalentRecordStop extends BpmEntity<HsTalentRecordStop> {

	public static final String APPLY_STATUS_DEFAULT = "-1"; //默认、删除的申请单
	public static final String APPLY_STATUS_DRAFT = "人才补贴停发报备";//0:省直单位申请草稿

	// 人才补贴停发报备   主管部门审核   机关经办审核  机关处室领导复核   机关局领导审核
    // talent_stop_apply_status   talent_stop_flow_status
	public static final String APPLY_STATUS_AUDIT_SUPERUNIT_FIRST = "主管部门审核";//1:待主管部门审核
	public static final String APPLY_STATUS_AUDIT_ORGHAND_FIRST = "机关经办审核";//2:待机关经办审核
	public static final String APPLY_STATUS_AUDIT_ORGOFFICE_FIRST = "机关处室领导复核";//3:待机关处室领导复核
	public static final String APPLY_STATUS_AUDIT_ORGBUREAU_FIRST = "机关局领导审核";//4:待机关局领导审核

	private static final long serialVersionUID = 1L;
	private String talentId;		// 人才信息ID
	private String userNo;		// 人才身份证号码
	private String stopReason;		// 停发原因
	private Integer applyStatus;		// 申请状态
	private String validTag;		// 是否有效

	private String flowStatus;
	private Office applyOffice;
	private HsTalentRecord talentRecord;

	private Integer isView = 0;

	@ExcelFields({
		@ExcelField(title="人才编号", attrName="id", align= ExcelField.Align.CENTER, sort=20),
		@ExcelField(title="申请单位", attrName="applyOffice.treeNames", align= ExcelField.Align.CENTER, sort=30),
		@ExcelField(title="姓名", attrName="talentRecord.userName", align= ExcelField.Align.CENTER, sort=40),
		@ExcelField(title="身份证号码", attrName="userNo", align= ExcelField.Align.CENTER, sort=50),
		@ExcelField(title="停发原因", attrName="stopReason", align= ExcelField.Align.CENTER, sort=60),
		@ExcelField(title="申请状态", attrName="applyStatus", dictType="talent_stop_apply_status",  align= ExcelField.Align.LEFT, sort=70),
		@ExcelField(title="创建日期", attrName="createDate", align= ExcelField.Align.CENTER, words=20, sort=120, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
		@ExcelField(title="更新日期", attrName="updateDate", align= ExcelField.Align.CENTER, words=20, sort=130, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
	})

	public HsTalentRecordStop() {
		this(null);
	}
	
	public HsTalentRecordStop(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="人才信息ID长度不能超过 64 个字符")
	public String getTalentId() {
		return talentId;
	}

	public void setTalentId(String talentId) {
		this.talentId = talentId;
	}
	
	@Size(min=0, max=255, message="人才身份证号码长度不能超过 255 个字符")
	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}
	
	@Size(min=0, max=900, message="停发原因长度不能超过 900 个字符")
	public String getStopReason() {
		return stopReason;
	}

	public void setStopReason(String stopReason) {
		this.stopReason = stopReason;
	}
	
	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}
	
	@Size(min=0, max=1, message="是否有效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public String getFlowStatus() {
		return flowStatus;
	}

	public void setFlowStatus(String flowStatus) {
		this.flowStatus = flowStatus;
	}

	public Office getApplyOffice() {
		return applyOffice;
	}

	public void setApplyOffice(Office applyOffice) {
		this.applyOffice = applyOffice;
	}

	public HsTalentRecord getTalentRecord() {
		return talentRecord;
	}

	public void setTalentRecord(HsTalentRecord talentRecord) {
		this.talentRecord = talentRecord;
	}

	public Integer getIsView() {
		return isView;
	}

	public void setIsView(Integer isView) {
		this.isView = isView;
	}
}