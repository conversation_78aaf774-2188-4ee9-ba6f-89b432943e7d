package com.hsobs.hs.modules.contract.entity;


import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 合同模板详情表Entity
 * <AUTHOR>
 * @version 2025-01-21
 */
@Table(name="hs_contract_template_data", alias="a", label="合同模板详情表信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="content", attrName="content", label="合同模板内容"),
	}, orderBy="a.id DESC"
)
public class HsContractTemplateData extends DataEntity<HsContractTemplateData> {
	
	private static final long serialVersionUID = 1L;
	private String content;		// 合同模板内容

	public HsContractTemplateData() {
		this(null);
	}
	
	public HsContractTemplateData(String id){
		super(id);
	}
	
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
}