package com.hsobs.hs.modules.apply.service.applyedDataList;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.bpm.service.CommonBpmService;
import com.jeesite.common.entity.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 批量导出配租确认单-可选择申请单列表
 */
@Service
public class HsQwApplyedListRentOrder implements HsQwApplyedList{

    @Autowired
    private CommonBpmService commonBpmService;

    @Override
    public String getDataType() {
        return "6";
    }

    @Override
    public Page<HsQwApply> execute(HsQwApply hsQwApply,  HsQwApplyService hsQwApplyService) {
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwApply.setStatus(null);
        hsQwApply.sqlMap().add("extWhere", " and (a.STATUS = 0\n" +
                "\t\tAND a.APPLY_MATTER in (0,1,2,4))");
        return commonBpmService.findObjectList(new String[]{"房管机构确认", "申请人确认" , "签订租赁合同" , "确认合同信息"}, "rent_apply%", hsQwApply, null);

    }
}
