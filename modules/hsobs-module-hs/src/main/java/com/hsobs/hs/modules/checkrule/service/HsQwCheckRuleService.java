package com.hsobs.hs.modules.checkrule.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyrule.entity.HsQwApplyRule;
import com.hsobs.hs.modules.applyrule.service.HsQwApplyRule.IHsQwApplyRule;
import com.hsobs.hs.modules.checkrule.service.checkRule.CheckRuleResult;
import com.hsobs.hs.modules.checkrule.service.checkRule.ICheckRule;
import com.jeesite.common.lang.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;
import com.hsobs.hs.modules.checkrule.dao.HsQwCheckRuleDao;

/**
 * 租赁资格核查规则表Service
 * <AUTHOR>
 * @version 2025-02-11
 */
@Service
public class HsQwCheckRuleService extends CrudService<HsQwCheckRuleDao, HsQwCheckRule> implements ApplicationContextAware {

	/**
	 * 存储所有规则方法
	 **/
	private Map<String, ICheckRule> ruleMap = new HashMap<>();
	/**
	 * 存储所有规则配置
	 **/
	private List<HsQwCheckRule> ruleConfigs = new ArrayList<>();
	/**
	 * 获取单条数据
	 * @param hsQwCheckRule
	 * @return
	 */
	@Override
	public HsQwCheckRule get(HsQwCheckRule hsQwCheckRule) {
		return super.get(hsQwCheckRule);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwCheckRule 查询条件
	 * @param hsQwCheckRule page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwCheckRule> findPage(HsQwCheckRule hsQwCheckRule) {
		return super.findPage(hsQwCheckRule);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwCheckRule
	 * @return
	 */
	@Override
	public List<HsQwCheckRule> findList(HsQwCheckRule hsQwCheckRule) {
		return super.findList(hsQwCheckRule);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwCheckRule
	 */
	@Override
	@Transactional
	public void save(HsQwCheckRule hsQwCheckRule) {
		super.save(hsQwCheckRule);
	}
	
	/**
	 * 更新状态
	 * @param hsQwCheckRule
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwCheckRule hsQwCheckRule) {
		super.updateStatus(hsQwCheckRule);
	}
	
	/**
	 * 删除数据
	 * @param hsQwCheckRule
	 */
	@Override
	@Transactional
	public void delete(HsQwCheckRule hsQwCheckRule) {
		super.delete(hsQwCheckRule);
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		Map<String, ICheckRule> beanMap = applicationContext.getBeansOfType(ICheckRule.class);
		beanMap.forEach((k, v) -> ruleMap.put(v.getClass().getSimpleName(), v));
		this.refreshRule();
	}

	private void refreshRule() {
		ruleConfigs.clear();
		HsQwApplyRule hsQwApplyRule = new HsQwApplyRule();
		hsQwApplyRule.setStatus(HsQwApplyRule.STATUS_NORMAL);
		ruleConfigs = this.findList(new HsQwCheckRule());
	}

	/**
	 * 根据申请单，对申请单进行资格审核
	 * @param apply
	 * @return
	 */
	public List<CheckRuleResult> checkRule(HsQwApply apply){
		List<CheckRuleResult> resultList = new ArrayList<>();
		List<HsQwCheckRule> rules = this.findList(new HsQwCheckRule());
		for (HsQwCheckRule rule : rules) {
			ICheckRule checkRule = ruleMap.get(StringUtils.camelCase(rule.getRuleCode()));
			if (checkRule!=null){
				CheckRuleResult result = checkRule.execute(apply, rule);
				result.setHsQwCheckRule(rule);
				resultList.add(result);
			}
		}
		return resultList;
	}

}