package com.hsobs.hs.modules.house.service.deleteCheck;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.checkrecord.dao.HsQwCheckRecordDao;
import com.hsobs.hs.modules.checkrecord.entity.HsQwCheckRecord;
import com.hsobs.hs.modules.clearance.dao.HsQwClearanceDao;
import com.hsobs.hs.modules.clearance.entity.HsQwClearance;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 资格核查申请单，查询所有房源的申请单
 */
@Service
public class HsQwHouseDeleteCheckMethodQwApplyCheckRecord implements HsQwHouseDeleteCheckMethod {
    @Autowired
    private HsQwCheckRecordDao hsQwCheckRecordDao;

    @Override
    public void execute(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        HsQwCheckRecord query = new HsQwCheckRecord();
        query.setStatus_in(new String[]{HsQwApply.STATUS_AUDIT});
        query.sqlMap().getWhere().disableAutoAddStatusWhere();
        query.setHouseId(hsQwPublicRentalHouse.getId());
        long count = hsQwCheckRecordDao.findCount(query);
        if (count>0){
            throw new ServiceException("该房源存在关联的资格核查申请单，请先取消申请单后再重新操作！");
        }
    }
}
