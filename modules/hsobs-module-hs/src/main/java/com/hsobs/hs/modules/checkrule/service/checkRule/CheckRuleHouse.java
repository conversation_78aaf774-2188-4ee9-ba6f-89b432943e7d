package com.hsobs.hs.modules.checkrule.service.checkRule;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.checkrule.entity.HsQwCheckRule;
import org.springframework.stereotype.Service;

/**
 * 未曾购买政策性住房
 * 申请人及其家庭成员未曾购买政策性住房。
 */
@Service
public class CheckRuleHouse implements ICheckRule {
    @Override
    public CheckRuleResult execute(HsQwApply hsQwApply, HsQwCheckRule rule) {
        return CheckRuleResult.success("成功");
    }
}
