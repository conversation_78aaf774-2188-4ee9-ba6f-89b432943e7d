package com.hsobs.hs.modules.datamanage.web;

import java.util.Map;
import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.idgen.IdGen;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.datamanage.entity.HsFileClass;
import com.hsobs.hs.modules.datamanage.service.HsFileClassService;

/**
 * 档案目录Controller
 * <AUTHOR>
 * @version 2025-01-19
 */
@Controller
@RequestMapping(value = "${adminPath}/datamanage/hsFileClass")
public class HsFileClassController extends BaseController {

	@Autowired
	private HsFileClassService hsFileClassService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsFileClass get(String id, boolean isNewRecord) {
		return hsFileClassService.get(id, isNewRecord);
	}

	/**
	 * 管理主页
	 */
	@RequiresPermissions("datamanage:hsFileClass:view")
	@RequestMapping(value = "index")
	public String index(HsFileClass hsFileClass, Model model) {
		model.addAttribute("hsFileClass", hsFileClass);
		return "modules/datamanage/hsFileClassIndex";
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("datamanage:hsFileClass:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsFileClass hsFileClass, Model model) {
		model.addAttribute("hsFileClass", hsFileClass);
		return "modules/datamanage/hsFileClassList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("datamanage:hsFileClass:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public List<HsFileClass> listData(HsFileClass hsFileClass) {
		if (StringUtils.isBlank(hsFileClass.getParentCode())) {
			hsFileClass.setParentCode(HsFileClass.ROOT_CODE);
		}
		if (StringUtils.isNotBlank(hsFileClass.getClassName())){
			hsFileClass.setParentCode(null);
		}
		if (StringUtils.isNotBlank(hsFileClass.getGroupType())){
			hsFileClass.setParentCode(null);
		}
		if (StringUtils.isNotBlank(hsFileClass.getBusinessType())){
			hsFileClass.setParentCode(null);
		}
		if (StringUtils.isNotBlank(hsFileClass.getClassCode())){
			hsFileClass.setParentCode(null);
		}
		if (StringUtils.isNotBlank(hsFileClass.getRemarks())){
			hsFileClass.setParentCode(null);
		}
		List<HsFileClass> list = hsFileClassService.findList(hsFileClass);
		return list;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("datamanage:hsFileClass:view")
	@RequestMapping(value = "form")
	public String form(HsFileClass hsFileClass, Model model) {
		// 创建并初始化下一个节点信息
		hsFileClass = createNextNode(hsFileClass);
		model.addAttribute("hsFileClass", hsFileClass);
		return "modules/datamanage/hsFileClassForm";
	}
	
	/**
	 * 创建并初始化下一个节点信息，如：排序号、默认值
	 */
	@RequiresPermissions("datamanage:hsFileClass:edit")
	@RequestMapping(value = "createNextNode")
	@ResponseBody
	public HsFileClass createNextNode(HsFileClass hsFileClass) {
		if (StringUtils.isNotBlank(hsFileClass.getParentCode())){
			hsFileClass.setParent(hsFileClassService.get(hsFileClass.getParentCode()));
		}
		if (hsFileClass.getIsNewRecord()) {
			HsFileClass where = new HsFileClass();
			where.setParentCode(hsFileClass.getParentCode());
			HsFileClass last = hsFileClassService.getLastByParentCode(where);
			// 获取到下级最后一个节点
			if (last != null){
				hsFileClass.setTreeSort(last.getTreeSort() + 30);
			}
		}
		// 以下设置表单默认数据
		if (hsFileClass.getTreeSort() == null){
			hsFileClass.setTreeSort(HsFileClass.DEFAULT_TREE_SORT);
		}
		return hsFileClass;
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("datamanage:hsFileClass:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsFileClass hsFileClass) {
		hsFileClassService.save(hsFileClass);
		return renderResult(Global.TRUE, text("保存档案目录成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("datamanage:hsFileClass:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsFileClass hsFileClass) {
		hsFileClassService.delete(hsFileClass);
		return renderResult(Global.TRUE, text("删除档案目录成功！"));
	}
	
	/**
	 * 获取树结构数据
	 * @param excludeCode 排除的Code
	 * @param parentCode 设置父级编码返回一级
	 * @param isShowCode 是否显示编码（true or 1：显示在左侧；2：显示在右侧；false or null：不显示）
	 * @return
	 */
	@RequiresPermissions("datamanage:hsFileClass:view")
	@RequestMapping(value = "treeData")
	@ResponseBody
	public List<Map<String, Object>> treeData(String excludeCode, String parentCode, String isShowCode) {
		List<Map<String, Object>> mapList = ListUtils.newArrayList();
		HsFileClass where = new HsFileClass();
		where.setStatus(HsFileClass.STATUS_NORMAL);
		if (StringUtils.isNotBlank(parentCode)){
			where.setParentCode(parentCode);
		}
		List<HsFileClass> list = hsFileClassService.findList(where);
		for (int i=0; i<list.size(); i++){
			HsFileClass e = list.get(i);
			// 过滤非正常的数据
			if (!HsFileClass.STATUS_NORMAL.equals(e.getStatus())){
				continue;
			}
			// 过滤被排除的编码（包括所有子级）
			if (StringUtils.isNotBlank(excludeCode)){
				if (e.getId().equals(excludeCode)){
					continue;
				}
				if (e.getParentCodes().contains("," + excludeCode + ",")){
					continue;
				}
			}
			Map<String, Object> map = MapUtils.newHashMap();
			map.put("id", e.getId());
			map.put("pId", e.getParentCode());
			map.put("name", e.getClassName());
			map.put("isParent", !e.getIsTreeLeaf());
			mapList.add(map);
		}
		return mapList;
	}

	/**
	 * 修复表结构相关数据
	 */
	@RequiresPermissions("datamanage:hsFileClass:edit")
	@RequestMapping(value = "fixTreeData")
	@ResponseBody
	public String fixTreeData(HsFileClass hsFileClass){
		if (!hsFileClass.currentUser().isAdmin()){
			return renderResult(Global.FALSE, "操作失败，只有管理员才能进行修复！");
		}
		hsFileClassService.fixTreeData();
		return renderResult(Global.TRUE, "数据修复成功");
	}
	
}