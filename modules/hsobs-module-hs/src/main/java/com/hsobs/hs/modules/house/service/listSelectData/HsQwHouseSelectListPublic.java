package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;
import org.springframework.stereotype.Service;

/**
 * 公租房房源公示，选择房源：选择待配租、没有在流程中的房源1，进行选择公示
 */
@Service
public class HsQwHouseSelectListPublic implements HsQwHouseSelectList {
    public String getDataType() {
        return "6";
    }

    public Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse,
            HsQwPublicRentalHouseService hsQwPublicRentalHouseService) {
        hsQwPublicRentalHouse.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_RENTAL);
        hsQwPublicRentalHouse.setHouseStatus("0");
        hsQwPublicRentalHouse.setIsPublic("1");
        hsQwPublicRentalHouse.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwPublicRentalHouse.sqlMap().add("extWhere", " and a.id not in " +
                "(select house_id from hs_qw_apply where " +
                "status = 4 AND HOUSE_ID  IS NOT NULL )");
        return hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
    }
}
