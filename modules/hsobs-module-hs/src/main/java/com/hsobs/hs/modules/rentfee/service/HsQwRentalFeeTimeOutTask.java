//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.hsobs.hs.modules.rentfee.service;

import com.hsobs.hs.modules.payment.entity.HsQwFeePayment;
import com.hsobs.hs.modules.payment.service.HsQwFeePaymentService;
import com.hsobs.hs.modules.rentfee.entity.HsQwRentalFee;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.BaseService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.modules.msg.entity.content.BaseMsgContent;
import com.jeesite.modules.msg.entity.content.PcMsgContent;
import com.jeesite.modules.msg.utils.MsgPushUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class HsQwRentalFeeTimeOutTask extends BaseService {

    @Autowired
    private HsQwRentalFeeService hsQwRentalFeeService;

    @Autowired
    private HsQwFeePaymentService hsQwFeePaymentService;

    Map<String, Double> mapEstateArea;

    /**
     * 查询所有逾期未缴费的租金记录，并将逾期信息通过站内信发送给用户，已经发送的就不重复发送
     *
     */
    @Transactional
    public void execute() {

        logger.info("开始执行逾期未缴费租金通知任务");

        try {
            // 1. 查询所有逾期未缴费的租金记录
            HsQwRentalFee hsQwRentalFee = new HsQwRentalFee();
            hsQwRentalFee.setStatus("0"); // 未缴费
            hsQwRentalFee.setExpectFeeDate_lte(new Date()); // 预期缴费日期小于等于当前日期（已逾期）

            // 添加通知状态查询条件，只查询未通知的记录
            hsQwRentalFee.sqlMap().add("extWhere",
                    "and a.id NOT IN (SELECT fee_id FROM hs_qw_fee_payment WHERE notice_type = '4')");

            List<HsQwRentalFee> list = hsQwRentalFeeService.findList(hsQwRentalFee);

            logger.info("查询到{}条逾期未缴费租金记录需要发送通知", list.size());

            // 2. 遍历记录，发送站内信
            for (HsQwRentalFee rentalFee : list) {
                try {
                    // 获取用户ID
                    String userId = rentalFee.getUserId();

                    // 如果用户ID为空，尝试从申请信息中获取
                    if (StringUtils.isBlank(userId) && rentalFee.getHsQwApply() != null &&
                            rentalFee.getHsQwApply().getMainApplyer() != null) {
                        userId = rentalFee.getHsQwApply().getMainApplyer().getUserId();
                    }

                    // 如果用户ID仍然为空，跳过此记录
                    if (StringUtils.isBlank(userId)) {
                        logger.warn("租金记录ID：{}，无法获取用户ID，跳过通知", rentalFee.getId());
                        continue;
                    }

                    // 构建消息内容
                    StringBuilder content = new StringBuilder();
                    content.append("您有一笔租金已逾期未缴费，详情如下：\n");
                    content.append("账单编号：").append(rentalFee.getId()).append("\n");
                    content.append("缴费周期：").append(rentalFee.getFeeMonth()).append("\n");
                    content.append("租金金额：").append(rentalFee.getRentalFee()).append("元\n");
                    content.append("预期缴费时间：").append(DateUtils.formatDate(rentalFee.getExpectFeeDate(), "yyyy-MM-dd"))
                            .append("\n");
                    content.append("逾期天数：")
                            .append(DateUtils.getDistanceOfTwoDate(rentalFee.getExpectFeeDate(), new Date()))
                            .append("天\n");
                    content.append("请尽快缴纳租金，避免影响您的信用记录。");

                    // 创建消息内容对象
                    PcMsgContent msgContent = new PcMsgContent();
                    msgContent.setTitle("租金逾期未缴费通知");
                    msgContent.setContent(content.toString());

                    // 发送站内信
                    MsgPushUtils.push(msgContent, rentalFee.getId(), HsQwRentalFee.class.getSimpleName(), userId);

                    // 记录通知信息
                    HsQwFeePayment feePayment = new HsQwFeePayment();
                    feePayment.setFeeId(rentalFee.getId());
                    feePayment.setNoticeType("4"); // 4表示站内信通知
                    feePayment.setRemarks("系统自动发送逾期未缴费通知");

                    // 保存通知记录
                    hsQwFeePaymentService.save(feePayment);

                    logger.info("成功发送逾期未缴费通知，租金记录ID：{}", rentalFee.getId());

                } catch (Exception e) {
                    logger.error("发送逾期未缴费通知失败，租金记录ID：" + rentalFee.getId(), e);
                    // 继续处理下一条记录
                }
            }

            logger.info("逾期未缴费租金通知任务执行完成");

        } catch (Exception e) {
            logger.error("执行逾期未缴费租金通知任务失败", e);
            throw new ServiceException("执行逾期未缴费租金通知任务失败", e);
        }
    }

}
