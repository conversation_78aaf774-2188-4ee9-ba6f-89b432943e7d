package com.hsobs.hs.modules.utils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Component;
import java.beans.PropertyDescriptor;
import java.util.Arrays;

@Component
public class RequestMergeUtil {

    /**
     * 通过数据库查询的旧实体，并用 RequestBody 传递的数据覆盖非空字段
     *
     * @param dbEntity    数据库中的旧实体对象
     * @param jsonRequest RequestBody 传递的新数据
     * @param <T>         泛型
     * @return 合并后的对象
     */
    public static <T> T mergeRequestData(T dbEntity, T jsonRequest) {
        if (jsonRequest != null) {
            BeanUtils.copyProperties(jsonRequest, dbEntity, getNullPropertyNames(jsonRequest));
        }
        return dbEntity;
    }

    /**
     * 获取对象中值为 null 的字段
     *
     * @param source 源对象
     * @return null 字段的名称数组
     */
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        return Arrays.stream(pds)
                .map(PropertyDescriptor::getName)
                .filter(name -> src.getPropertyValue(name) == null)
                .toArray(String[]::new);
    }
}
