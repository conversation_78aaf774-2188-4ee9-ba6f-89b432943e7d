package com.hsobs.hs.modules.house.service.listSelectData;

import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.springframework.stereotype.Service;

/**
 * 资格核查，选择房源：选择所有已配租的、没有在核查流程中的房源1，进行核查
 */
@Service
public class HsQwHouseSelectListCheck implements HsQwHouseSelectList {
    public String getDataType() {
        return "7";
    }

    public Page<HsQwPublicRentalHouse> execute(HsQwPublicRentalHouse hsQwPublicRentalHouse,
                                               HsQwPublicRentalHouseService hsQwPublicRentalHouseService) {
        hsQwPublicRentalHouse.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_RENTAL);
        hsQwPublicRentalHouse.setHouseStatus("1");//已配租
        hsQwPublicRentalHouse.setIsPublic("1");
        hsQwPublicRentalHouse.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwPublicRentalHouse.sqlMap().getWhere().and("ha.APPLY_MATTER", QueryType.IN, new String[]{"0"});
        hsQwPublicRentalHouse.sqlMap().add("extWhere", " and a.id not in " +
                "(select hqcr.HOUSE_ID from hs_qw_check_record hqcr JOIN HS_QW_APPLY hqa ON hqa.id = hqcr.APPLY_ID where hqcr.status = 4 )");
        return hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
    }
}
