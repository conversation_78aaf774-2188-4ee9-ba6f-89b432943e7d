package com.hsobs.hs.modules.utils;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.FontProgram;
import com.itextpdf.io.font.FontProgramFactory;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.layout.font.FontProvider;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * <AUTHOR>
 */
public class UeditorExporter {

    /**
     * 导出为Word文档
     * @param htmlContent Ueditor的HTML内容
     * @param outputStream  输出流
     */
    public static void exportToWord(String htmlContent, OutputStream outputStream) throws IOException {
        try (XWPFDocument document = new XWPFDocument()) {
            Document doc = Jsoup.parse(htmlContent);
            Elements paragraphs = doc.select("p");

            for (Element p : paragraphs) {
                // 创建Word段落
                XWPFParagraph wordPara = document.createParagraph();

                // 设置对齐方式
                String align = p.attr("style");
                if (align.contains("center")) {
                    wordPara.setAlignment(ParagraphAlignment.CENTER);
                } else {
                    wordPara.setAlignment(ParagraphAlignment.LEFT);
                }

                // 处理文本内容
                String text = p.html().replace("<br>", "\n").replaceAll("<.*?>", "");
                XWPFRun run = wordPara.createRun();
                run.setText(text);
                run.setFontFamily("宋体");
            }
            // 保存文件
            document.write(outputStream);
        }
    }

    /**
     * 导出为PDF文档
     * @param htmlContent Ueditor的HTML内容
     * @param outputPath  输出文件路径
     */
    public static void exportToPdf(String htmlContent, String outputPath) throws IOException {
        com.itextpdf.kernel.pdf.PdfWriter writer = new com.itextpdf.kernel.pdf.PdfWriter(outputPath);
        com.itextpdf.kernel.pdf.PdfDocument pdf = new com.itextpdf.kernel.pdf.PdfDocument(writer);

        // 配置字体
        ConverterProperties props = new ConverterProperties();
        props.setCharset("UTF-8");

        FontProvider fontProvider = new FontProvider();

//        fontProvider.addStandardPdfFonts();
//        fontProvider.addSystemFonts();
        String fontPath = "simhei.ttf";
        try {
            Resource resource = new ClassPathResource("font/simhei.ttf");
            fontPath = resource.getFile().getAbsolutePath();

            fontProvider.getFontSet().addFont(fontPath, PdfEncodings.IDENTITY_H);
            props.setFontProvider(fontProvider);
            // 正确加载字体方式
            FontProgram fontProgram = FontProgramFactory.createFont(fontPath);
            fontProvider.addFont(fontProgram, PdfEncodings.IDENTITY_H);
        } catch (IOException e) {
            throw new RuntimeException("字体文件加载失败: " + fontPath, e);
        }
        // 设置默认字体（关键配置）
        props.setFontProvider(fontProvider);
        // 转换HTML到PDF
        HtmlConverter.convertToPdf(htmlContent, pdf, props);
        pdf.close();
    }

}
