package com.hsobs.hs.modules.dataintelligence.service;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTotalDao;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTalentSubsidyDao;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 住房保障数据统计Service   人才住房补助统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class DataIntelligenceTalentSubsidyService extends CrudService<DataIntelligenceTalentSubsidyDao, DataIntelligenceTalentSubsidy> {

	@Autowired
	private DataIntelligenceTalentSubsidyDao dataIntelligenceTalentSubsidyDao;
	@Autowired
	private DataIntelligenceTotalService dataIntelligenceTotalService;

	/**
	 * 获取单条数据
	 * @param dataIntelligenceTalentSubsidy
	 * @return
	 */
	@Override
	public DataIntelligenceTalentSubsidy get(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {
		return super.get(dataIntelligenceTalentSubsidy);
	}
	
	/**
	 * 查询分页数据
	 * @param dataIntelligenceTalentSubsidy 查询条件
	 * @param dataIntelligenceTalentSubsidy page 分页对象
	 * @return
	 */
	@Override
	public Page<DataIntelligenceTalentSubsidy> findPage(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {
		return super.findPage(dataIntelligenceTalentSubsidy);
	}
	
	/**
	 * 查询列表数据
	 * @param dataIntelligenceTalentSubsidy
	 * @return
	 */
	@Override
	public List<DataIntelligenceTalentSubsidy> findList(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {
		return super.findList(dataIntelligenceTalentSubsidy);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param dataIntelligenceTalentSubsidy
	 */
	@Override
	@Transactional
	public void save(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {
		super.save(dataIntelligenceTalentSubsidy);
	}
	
	/**
	 * 更新状态
	 * @param dataIntelligenceTalentSubsidy
	 */
	@Override
	@Transactional
	public void updateStatus(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {
		super.updateStatus(dataIntelligenceTalentSubsidy);
	}
	
	/**
	 * 删除数据
	 * @param dataIntelligenceTalentSubsidy
	 */
	@Override
	@Transactional
	public void delete(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {
		dataIntelligenceTalentSubsidy.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataIntelligenceTalentSubsidy);
	}

	private  String getSqlOtherWhere(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, Date startDate, Date endDate) {
		String sqlOtherWhere = "";
		if (startDate != null || dataIntelligenceTalentSubsidy.getStartDate() != null) {
			Date date = (startDate!=null)?startDate:dataIntelligenceTalentSubsidy.getStartDate();
			sqlOtherWhere += " AND a.create_date >= '" + String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}
		if (endDate != null || dataIntelligenceTalentSubsidy.getEndDate() != null) {
			Date date = (endDate!=null)?endDate:dataIntelligenceTalentSubsidy.getEndDate();
			sqlOtherWhere += " AND a.create_date <= '" + String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}

		if(dataIntelligenceTalentSubsidy.getCity() != null && !"".equals(dataIntelligenceTalentSubsidy.getCity())){
			sqlOtherWhere += " AND a.city = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceTalentSubsidy.getCity()) + "'";
		}
		if(dataIntelligenceTalentSubsidy.getArea() != null && !"".equals(dataIntelligenceTalentSubsidy.getArea())){
			sqlOtherWhere += " AND a.area = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceTalentSubsidy.getArea()) + "'";
		}

		dataIntelligenceTalentSubsidy.sqlMap().getDataScope().addFilter("dsf", "Office", "jso.office_code", DataScope.CTRL_PERMI_HAVE);
		SqlMap sqlMap = dataIntelligenceTalentSubsidy.sqlMap();
		Object obWhere = sqlMap.get("dsf");
		if(obWhere != null){
			sqlOtherWhere += obWhere.toString();
		}
		String officeCode = null;
		if(dataIntelligenceTalentSubsidy.getOfficeCode() != null && dataIntelligenceTalentSubsidy.getOfficeCode().length() > 0) {
			officeCode = dataIntelligenceTalentSubsidy.getOfficeCode();
		}
		sqlOtherWhere += dataIntelligenceTotalService.getOfficePermission(officeCode);

		/*if(dataIntelligenceTalentSubsidy.getTalentType() != null && dataIntelligenceTalentSubsidy.getTalentType().length() > 0) {
			sqlWhere += String.format(" AND TALENT_TYPE='%s'", dataIntelligenceTalentSubsidy.getTalentType());
		}*/
		/*if(dataIntelligenceTalentSubsidy.getTalentLevel() != null && dataIntelligenceTalentSubsidy.getTalentLevel().length() > 0) {
			sqlWhere += String.format(" AND TALENT_LEVEL='%s'", dataIntelligenceTalentSubsidy.getTalentLevel());
		}*/

		return sqlOtherWhere;
	}

	public Page<DataIntelligenceTalentSubsidy> findTalentSubsidyStatDataPage(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, boolean findpage){

		// 人才住房补助统计
		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceTalentSubsidy, null, null);
		String sqlOrderBy = (dataIntelligenceTalentSubsidy.getOrderBy()!=null&&dataIntelligenceTalentSubsidy.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceTalentSubsidy.getOrderBy()):"";

		Page<DataIntelligenceTalentSubsidy> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("talentType", dataIntelligenceTalentSubsidy.getTalentType());
		mapPara.put("talentLevel", dataIntelligenceTalentSubsidy.getTalentLevel());
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceTalentSubsidy.getPageNo());
			pageMap.setPageSize(dataIntelligenceTalentSubsidy.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceTalentSubsidyDao.countTalentSubsidy(mapPara);
		List<DataIntelligenceTalentSubsidy> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceTalentSubsidy talentStat = new DataIntelligenceTalentSubsidy();

			Object ob = map.get("TALENT_LEVEL");
			talentStat.setTalentLevel((ob!=null)?ob.toString():"");
			// 申请总数
			ob = map.get("APPLY_TOTAL_COUNT");
			talentStat.setApplyTotalCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 申请中总数
			ob = map.get("APPLYING_COUNT");
			talentStat.setApplyingCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 审核通过数
			ob = map.get("APPROVED_COUNT");
			talentStat.setApprovedCount((ob!=null)?Long.valueOf(ob.toString()):0);

			// 总申请资金汇总
			ob = map.get("APPLY_TOTAL_SUBSIDY_FUND");
			talentStat.setApplyTotalSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);
			// 申请中资金汇总
			ob = map.get("APPLYING_SUBSIDY_FUND");
			talentStat.setApplyingSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);
			// 审核通过资金汇总
			ob = map.get("APPROVED_SUBSIDY_FUND");
			talentStat.setApprovedSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(talentStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

	public Page<DataIntelligenceTalentSubsidy> findTalentSubsidyStatAreaDataPage(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy, boolean findpage){

		// 人才住房补助统计
		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceTalentSubsidy, null, null);
		String sqlOrderBy = (dataIntelligenceTalentSubsidy.getOrderBy()!=null&&dataIntelligenceTalentSubsidy.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceTalentSubsidy.getOrderBy()):"";

		Page<DataIntelligenceTalentSubsidy> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlOtherWhere", sqlOtherWhere);
		mapPara.put("talentType", dataIntelligenceTalentSubsidy.getTalentType());
		mapPara.put("talentLevel", dataIntelligenceTalentSubsidy.getTalentLevel());
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceTalentSubsidy.getPageNo());
			pageMap.setPageSize(dataIntelligenceTalentSubsidy.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceTalentSubsidyDao.countTalentSubsidyArea(mapPara);
		List<DataIntelligenceTalentSubsidy> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceTalentSubsidy talentStat = new DataIntelligenceTalentSubsidy();

			Object ob = map.get("AREA_NAME");
			talentStat.setArea((ob!=null)?ob.toString():"");
			// 申请总数
			ob = map.get("APPLY_TOTAL_COUNT");
			talentStat.setApplyTotalCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 申请中总数
			ob = map.get("APPLYING_COUNT");
			talentStat.setApplyingCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 审核通过数
			ob = map.get("APPROVED_COUNT");
			talentStat.setApprovedCount((ob!=null)?Long.valueOf(ob.toString()):0);

			// 总申请资金汇总
			ob = map.get("APPLY_TOTAL_SUBSIDY_FUND");
			talentStat.setApplyTotalSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);
			// 申请中资金汇总
			ob = map.get("APPLYING_SUBSIDY_FUND");
			talentStat.setApplyingSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);
			// 审核通过资金汇总
			ob = map.get("APPROVED_SUBSIDY_FUND");
			talentStat.setApprovedSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(talentStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

    public List<DataIntelligenceTalentSubsidy> countTalentSubsidy(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {

		return findTalentSubsidyStatDataPage(dataIntelligenceTalentSubsidy, false).getList();
    }

	public void SetTypeData(List<Map<String,Object>> value, List<List<String>> dataType){
		if(value.size() == 0) {
			dataType.get(0).add("0");
			dataType.get(1).add("0");
		}
		else {
			Map<String,Object> map = value.get(0);

			// 申请数
			Object ob = map.get("APPLY_TOTAL_COUNT");
			dataType.get(0).add((ob!=null)?ob.toString():"0");
			// 审核通过数
			ob = map.get("APPROVED_COUNT");
			dataType.get(1).add((ob!=null)?ob.toString():"0");
		}
	}

	public String findTalentSubsidyCompare(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 2; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "申请数");break;
				case 1:dataTypeO.put("name", "审核通过数");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		Map<String, Object> mapPara = dataIntelligenceTotalService.getComparePara(dataIntelligenceTalentSubsidy.getCompareType());

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceTalentSubsidy, (Date)mapPara.get("last_start_date"), (Date)mapPara.get("last_end_date"));
		List<Map<String,Object>> value = dataIntelligenceTalentSubsidyDao.countTalentSubsidyCompare(sqlOtherWhere, dataIntelligenceTalentSubsidy.getTalentType(), dataIntelligenceTalentSubsidy.getTalentLevel());
		SetTypeData(value, dataType);

		sqlOtherWhere = getSqlOtherWhere(dataIntelligenceTalentSubsidy, (Date)mapPara.get("now_start_date"), (Date)mapPara.get("now_end_date"));
		value = dataIntelligenceTalentSubsidyDao.countTalentSubsidyCompare(sqlOtherWhere, dataIntelligenceTalentSubsidy.getTalentType(), dataIntelligenceTalentSubsidy.getTalentLevel());
		SetTypeData(value, dataType);

		dataClass.add((String)mapPara.get("last_title"));
		dataClass.add((String)mapPara.get("now_title"));

		Integer max = dataIntelligenceTotalService.calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}

	public void SetPayData(List<Map<String,Object>> value, List<List<String>> dataType){
		if(value.size() == 0) {
			dataType.get(0).add("0");
		}
		else {
			Map<String,Object> map = value.get(0);

			// 补助费用
			Object ob = map.get("APPROVED_SUBSIDY_FUND");
			dataType.get(0).add((ob!=null)?ob.toString():"0");
		}
	}
	public String findTalentSubsidyPayCompare(DataIntelligenceTalentSubsidy dataIntelligenceTalentSubsidy) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 1; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "补助费用");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		Map<String, Object> mapPara = dataIntelligenceTotalService.getComparePara(dataIntelligenceTalentSubsidy.getCompareType());

		String sqlOtherWhere = getSqlOtherWhere(dataIntelligenceTalentSubsidy, (Date)mapPara.get("last_start_date"), (Date)mapPara.get("last_end_date"));
		List<Map<String,Object>> value = dataIntelligenceTalentSubsidyDao.countTalentSubsidyCompare(sqlOtherWhere, dataIntelligenceTalentSubsidy.getTalentType(), dataIntelligenceTalentSubsidy.getTalentLevel());
		SetPayData(value, dataType);

		sqlOtherWhere = getSqlOtherWhere(dataIntelligenceTalentSubsidy, (Date)mapPara.get("now_start_date"), (Date)mapPara.get("now_end_date"));
		value = dataIntelligenceTalentSubsidyDao.countTalentSubsidyCompare(sqlOtherWhere, dataIntelligenceTalentSubsidy.getTalentType(), dataIntelligenceTalentSubsidy.getTalentLevel());
		SetPayData(value, dataType);

		dataClass.add((String)mapPara.get("last_title"));
		dataClass.add((String)mapPara.get("now_title"));

		Integer max = dataIntelligenceTotalService.calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}
}