package com.hsobs.hs.modules.apply.service.updateStatus;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.jeesite.common.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class HsQwApplyStatusReplaceUp implements HsQwApplyStatus {

    @Autowired
    HsQwCompactService hsQwCompactService;

    @Override
    public String getApplyMater() {
        return "2";
    }

    @Override
    public void execute(HsQwApply realBean, HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        //执行清退
        if (hsQwApply.isClear()){
            hsQwApplyService.invalidApplyInfo(realBean);
        } else {
            //状态更新
            hsQwApplyService.realUpdateStatus(hsQwApply);
            //信息申请单生效，原申请单所有信息失效
            if (hsQwApply.getStatus().equals(HsQwApply.STATUS_NORMAL)) {
                //获取旧订单信息
                HsQwApply history = hsQwApplyService.getBase(realBean.getApplyedId());
                //如果是个人调租申请单，并且状态为成功的，需要将原申请单的状态设置为不可用
                hsQwApplyService.invalidApplyInfo(history);
            }
            //新房源状态更新
            if (hsQwApply.getStatus().equals(HsQwApply.STATUS_NORMAL)) {
                hsQwApplyService.updateHouseStatus(realBean.getHouseId(), "1");//流程结束，已配租
            } else if (StringUtils.isNotBlank(hsQwApply.getHouseId())) {
                hsQwApplyService.updateHouseStatus(realBean.getHouseId(), "0");//流程异常，恢复待配租
            }
            //新合同更新
            if (hsQwApply.getStatus().equals(HsQwApply.STATUS_NORMAL)) {
                realBean.getCompact().setStatus(HsQwCompact.STATUS_NORMAL);//流程结束，更新合同状态
                hsQwCompactService.updateStatus(realBean.getCompact());
            } else if (hsQwApply.getCompact() != null) {
                realBean.getCompact().setStatus(HsQwCompact.STATUS_DISABLE);//流程异常，失效合同
                hsQwCompactService.updateStatus(realBean.getCompact());
            }
        }
    }
}
