package com.hsobs.hs.modules.province.web;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.hsobs.hs.modules.bureau.entity.HsQwApplyBureau;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.province.entity.HsQwApplyProvince;
import com.hsobs.hs.modules.province.service.HsQwApplyProvinceService;

/**
 * 省直单位自管公房申请表Controller
 * <AUTHOR>
 * @version 2025-02-19
 */
@Controller
@RequestMapping(value = "${adminPath}/province/hsQwApplyProvince")
public class HsQwApplyProvinceController extends BaseController {

	@Autowired
	private HsQwApplyProvinceService hsQwApplyProvinceService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwApplyProvince get(String id, boolean isNewRecord) {
		return hsQwApplyProvinceService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("province:hsQwApplyProvince:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwApplyProvince hsQwApplyProvince, Model model) {
		model.addAttribute("hsQwApplyProvince", hsQwApplyProvince);
		return "modules/province/hsQwApplyProvinceList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("province:hsQwApplyProvince:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwApplyProvince> listData(HsQwApplyProvince hsQwApplyProvince, HttpServletRequest request, HttpServletResponse response) {
		hsQwApplyProvince.setPage(new Page<>(request, response));
		Page<HsQwApplyProvince> page = hsQwApplyProvinceService.findPage(hsQwApplyProvince);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("province:hsQwApplyProvince:view")
	@RequestMapping(value = "form")
	public String form(HsQwApplyProvince hsQwApplyProvince, Model model) {
		model.addAttribute("hsQwApplyProvince", hsQwApplyProvince);
		return "modules/province/hsQwApplyProvinceForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("province:hsQwApplyProvince:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwApplyProvince hsQwApplyProvince) {
		hsQwApplyProvinceService.save(hsQwApplyProvince);
		return renderResult(Global.TRUE, text("保存省直单位自管公房申请表成功！"));
	}
	
	/**
	 * 删除数据
	 */
	@RequiresPermissions("province:hsQwApplyProvince:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwApplyProvince hsQwApplyProvince) {
		hsQwApplyProvinceService.delete(hsQwApplyProvince);
		return renderResult(Global.TRUE, text("删除省直单位自管公房申请表成功！"));
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("bureau:hsQwApplyBureau:edit")
	@RequestMapping(value = "clear")
	@ResponseBody
	public String clear(HsQwApplyProvince hsQwApplyProvince) {
		hsQwApplyProvinceService.clear(hsQwApplyProvince);
		return renderResult(Global.TRUE, text("删除省直自管公房申请成功！"));
	}
	
}