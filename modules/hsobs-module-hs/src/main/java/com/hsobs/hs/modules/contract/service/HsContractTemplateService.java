package com.hsobs.hs.modules.contract.service;

import com.hsobs.hs.modules.contract.constants.ContractConstants;
import com.hsobs.hs.modules.contract.dao.HsContractTemplateDao;
import com.hsobs.hs.modules.contract.entity.HsContractTemplate;
import com.hsobs.hs.modules.contract.entity.HsContractTemplateData;
import com.hsobs.hs.modules.contract.entity.HsContractTemplateField;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合同模板Service
 * <AUTHOR>
 * @version 2025-01-21
 */
@Service
public class HsContractTemplateService extends CrudService<HsContractTemplateDao, HsContractTemplate> {

	@Autowired
	private HsContractTemplateDataService hsContractTemplateDataService;
	@Autowired
	private HsContractTemplateFieldService hsContractTemplateFieldService;

	/**
	 * 获取单条数据
	 * @param hsContractTemplate
	 * @return
	 */
	@Override
	public HsContractTemplate get(HsContractTemplate hsContractTemplate) {
		return super.get(hsContractTemplate);
	}
	
	/**
	 * 查询分页数据
	 * @param hsContractTemplate 查询条件
	 * @param hsContractTemplate page 分页对象
	 * @return
	 */
	@Override
	public Page<HsContractTemplate> findPage(HsContractTemplate hsContractTemplate) {
		return super.findPage(hsContractTemplate);
	}
	
	/**
	 * 查询列表数据
	 * @param hsContractTemplate
	 * @return
	 */
	@Override
	public List<HsContractTemplate> findList(HsContractTemplate hsContractTemplate) {
		return super.findList(hsContractTemplate);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsContractTemplate
	 */
	@Override
	@Transactional
	public void save(HsContractTemplate hsContractTemplate) {

		Map<String, Integer> nameDupMap = new HashMap<String, Integer>();
		Map<String, Integer> codeDupMap = new HashMap<String, Integer>();
		// 校验参数
		if (hsContractTemplate.getHsContractTemplateFieldList() != null) {
			for (HsContractTemplateField templateField : hsContractTemplate.getHsContractTemplateFieldList()) {
				if (nameDupMap.containsKey(templateField.getFieldName()) || codeDupMap.containsKey(templateField.getFieldCode())) {
					throw new ServiceException(text("字段属性不允许重复,请检查字段属性列表！"));
				}
				nameDupMap.put(templateField.getFieldName(), 1);
				codeDupMap.put(templateField.getFieldCode(), 1);
			}
		}

		boolean newFlag = false;
		// 校验
		HsContractTemplate query = new HsContractTemplate();
		query.setContractName(hsContractTemplate.getContractName());
		query.setContractVersion(hsContractTemplate.getContractVersion());
		List<HsContractTemplate> dbList = this.findList(query);

		if (hsContractTemplate.getId() == null) {
			if (dbList != null && !dbList.isEmpty()) {
				throw new ServiceException(text(String.format("模板名称: %s ,模板版本: %s 已存在,请到对应模板信息中进行编辑！", hsContractTemplate.getContractName(), hsContractTemplate.getContractVersion())));
			}
			newFlag = true;
		} else if (dbList != null && !dbList.isEmpty() && !hsContractTemplate.getId().equals(dbList.get(0).getId())) {
			throw new ServiceException(text(String.format("模板名称: %s ,模板版本: %s 已存在,请到对应模板信息中进行编辑！", hsContractTemplate.getContractName(), hsContractTemplate.getContractVersion())));
		}

		super.save(hsContractTemplate);
		// 内容存储
		HsContractTemplateData data = new HsContractTemplateData();
		data.setId(hsContractTemplate.getId());
		data.setContent(hsContractTemplate.getHsContractTemplateData().getContent());
		data.setStatus(HsContractTemplateData.STATUS_NORMAL);
		if (hsContractTemplateDataService.get(data.getId()) == null) {
			hsContractTemplateDataService.insert(data);
		} else {
			hsContractTemplateDataService.update(data);
		}

		Map<String, HsContractTemplateField> dbFieldMap = new HashMap<>();
		// 字段存储
		if (!newFlag) {
			HsContractTemplateField queryField = new HsContractTemplateField();
			queryField.setTemplateId(hsContractTemplate.getId());
			List<HsContractTemplateField>  fieldDbList = hsContractTemplateFieldService.findList(queryField);
			if (fieldDbList != null && !fieldDbList.isEmpty()) {
				for (HsContractTemplateField templateField : fieldDbList) {
					dbFieldMap.put(templateField.getFieldCode(), templateField);
				}
			}
		}
		int seq = 1;
		HsContractTemplateField field = null;
		for (HsContractTemplateField templateField : hsContractTemplate.getHsContractTemplateFieldList()) {
			if (StringUtils.isEmpty(templateField.getFieldName()) || StringUtils.isEmpty(templateField.getFieldCode())) {
				continue;
			}
			if (ContractConstants.BASE_FIELE_MAP.containsKey(templateField.getFieldCode())) {
				continue;
			}
			if (dbFieldMap.containsKey(templateField.getFieldCode())) {
				field = dbFieldMap.get(templateField.getFieldCode());
				field.setFieldName(templateField.getFieldName());
				field.setOrderNum(seq);
				hsContractTemplateFieldService.update(field);
				dbFieldMap.remove(templateField.getFieldCode());
			} else {
				field = new HsContractTemplateField();
				field.setFieldName(templateField.getFieldName());
				field.setFieldCode(templateField.getFieldCode());
				field.setTemplateId(hsContractTemplate.getId());
				field.setOrderNum(seq);
				field.setStatus(HsContractTemplateField.STATUS_NORMAL);
				field.setValidTag("1");
				hsContractTemplateFieldService.insert(field);
			}
			seq++;
		}
	}

	public List<HsContractTemplateField> getBaseFieldList() {
		List<HsContractTemplateField> baseFieldList = new ArrayList<>();
		ContractConstants.BASE_FIELE_MAP.forEach((key, value) -> baseFieldList.add(new HsContractTemplateField(null, value, key)));
		return baseFieldList;
	}

	/**
	 * 更新状态
	 * @param hsContractTemplate
	 */
	@Override
	@Transactional
	public void updateStatus(HsContractTemplate hsContractTemplate) {
		super.updateStatus(hsContractTemplate);
	}
	
	/**
	 * 删除数据
	 * @param hsContractTemplate
	 */
	@Override
	@Transactional
	public void delete(HsContractTemplate hsContractTemplate) {
		super.delete(hsContractTemplate);
	}
	
}