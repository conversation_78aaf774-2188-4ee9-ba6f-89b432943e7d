package com.hsobs.hs.modules.qualification.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.qualification.entity.HsQwApplyQualification;
import com.hsobs.hs.modules.qualification.dao.HsQwApplyQualificationDao;
import com.jeesite.modules.file.utils.FileUploadUtils;

/**
 * 租赁资格轮候资格配置表Service
 * <AUTHOR>
 * @version 2025-03-13
 */
@Service
public class HsQwApplyQualificationService extends CrudService<HsQwApplyQualificationDao, HsQwApplyQualification> {
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyQualification
	 * @return
	 */
	@Override
	public HsQwApplyQualification get(HsQwApplyQualification hsQwApplyQualification) {
		return super.get(hsQwApplyQualification);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyQualification 查询条件
	 * @param hsQwApplyQualification page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyQualification> findPage(HsQwApplyQualification hsQwApplyQualification) {
		return super.findPage(hsQwApplyQualification);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyQualification
	 * @return
	 */
	@Override
	public List<HsQwApplyQualification> findList(HsQwApplyQualification hsQwApplyQualification) {
		return super.findList(hsQwApplyQualification);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyQualification
	 */
	@Override
	@Transactional
	public void save(HsQwApplyQualification hsQwApplyQualification) {
		super.save(hsQwApplyQualification);
		// 保存上传附件
		FileUploadUtils.saveFileUpload(hsQwApplyQualification, hsQwApplyQualification.getId(), "hsQwApplyQualification_file");
	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyQualification
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyQualification hsQwApplyQualification) {
		super.updateStatus(hsQwApplyQualification);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyQualification
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyQualification hsQwApplyQualification) {
		super.delete(hsQwApplyQualification);
	}
	
}