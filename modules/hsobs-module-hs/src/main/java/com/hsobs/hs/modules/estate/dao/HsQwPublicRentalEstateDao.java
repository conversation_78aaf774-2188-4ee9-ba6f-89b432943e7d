package com.hsobs.hs.modules.estate.dao;

import com.hsobs.hs.modules.external.entity.ApiHsHouseTemplateList;
import com.jeesite.common.dao.CrudDao;
import com.jeesite.common.entity.Page;
import com.jeesite.common.mybatis.annotation.MyBatisDao;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公租房房源楼盘信息表DAO接口
 * 
 * <AUTHOR>
 * @version 2024-11-20
 */
@MyBatisDao
public interface HsQwPublicRentalEstateDao extends CrudDao<HsQwPublicRentalEstate> {

        List<HsQwPublicRentalEstate> findStaticEstate(@Param("estate") HsQwPublicRentalEstate query);

        int findStaticEstateCount(@Param("estate") HsQwPublicRentalEstate query);


        List<HsQwPublicRentalEstate> findPriceLimitStaticEstate(@Param("estate") HsQwPublicRentalEstate query);

        int findPriceLimitStaticEstateCount(@Param("estate") HsQwPublicRentalEstate query);
}