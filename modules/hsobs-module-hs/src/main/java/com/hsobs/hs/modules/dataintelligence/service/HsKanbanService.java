package com.hsobs.hs.modules.dataintelligence.service;

import com.hsobs.hs.modules.dataintelligence.dao.HsKanbanDao;
import com.hsobs.hs.modules.dataintelligence.entity.DataIntelligenceTotal;
import com.hsobs.hs.modules.dataintelligence.entity.HsKanban;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.User;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.utils.UserUtils;
import com.jeesite.modules.sys.utils.EmpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriUtils;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Map;

/**
 * 住房保障数据统计Entity   kanban
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class HsKanbanService extends CrudService<HsKanbanDao, HsKanban> {

    @Autowired
    private EmpUserService empUserService;
    @Autowired
    private HsKanbanDao hsKanbanDao;

    public Map<String, String> countKanbanCount(HsKanban hsKanban){

        String kanbanName = UriUtils.decode(hsKanban.getKanbanName(), "UTF-8");

        String sqlTable = null;
        String sqlWhere = "a.status='4'";
        Map<String, Object> mapV = null;
        switch (kanbanName) {
            // 住房保障
            case "公有住房出售申请审批":
                sqlTable = "hs_public_apply";
                //sqlWhere = "a.status='4'";
                break;
            case "省直公租房申请审批":
                sqlTable = "HS_QW_APPLY";
                break;
            case "维修资金申请审批":
                sqlTable = "HS_MAINTENANCE_APPLY";
                break;
            case "人才住房补贴申请审批":
                sqlTable = "HS_TALENT_INTRODUCTION_APPLY";
                break;
            case "加装电梯补助申请审批":
                sqlTable = "HS_ELEVATOR_APPLY";
                break;

            // 办公用房
            case "用房使用申请审批":
                sqlTable = "OB_APPLY";
                break;
            case "用房维护申请审批":
                sqlTable = "OB_REPAIR_REQUEST";
                break;
            case "用房调配申请审批":
                sqlTable = "OB_ARRANGE";
                break;
            case "权属移交申请审批":
                sqlTable = "OB_OWNERSHIP_REGISTRATION";
                break;
            case "权属登记申请审批":
                break;
            case "处置利用申请审批":
                break;
            case "监督问责申请审批":
                break;
        }

        if(sqlTable == null || sqlWhere == null){
            throw new ServiceException("看板名称未知:" + kanbanName);
        }
        mapV = hsKanbanDao.countKanbanCount(sqlTable, sqlWhere);
        if(mapV == null || mapV.get("APPROVED_COUNT") == null){
            throw new ServiceException("获取看板数据失败:" + kanbanName);
        }

        Map<String, String> map = new HashMap<>();
        map.put("dataName", hsKanban.getKanbanName());
        map.put("dataCount", mapV.get("APPROVED_COUNT").toString());
        return map;
    }

}
