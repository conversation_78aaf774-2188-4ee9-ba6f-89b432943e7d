package com.hsobs.hs.modules.datasync.web;

import com.hsobs.hs.modules.datasync.bean.OrgSyncData;
import com.hsobs.hs.modules.datasync.bean.UserSyncData;
import com.jeesite.modules.sys.entity.api.Api2ResponseBody;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "${adminPath}/data/change")
public class DataChangeReceiveController {

    /**
     * 8.1 单位数据同步到业务系统
     */
    @RequestMapping(value = "org")
    @ResponseBody
    public Api2ResponseBody<String> orgChangeData(OrgSyncData orgSyncData) {

        return Api2ResponseBody.sucess("接收成功");
    }

    /**
     * 8.2 用户数据同步到业务系统
     */
    @RequestMapping(value = "user")
    @ResponseBody
    public Api2ResponseBody<String> userChangeData(UserSyncData userSyncData) {

        return Api2ResponseBody.sucess("接收成功");
    }


}
