package com.hsobs.hs.modules.talent.entity;

import java.util.Date;

import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.JoinTable.Type;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.Office;

/**
 * 人才引进补助配置表Entity
 * <AUTHOR>
 * @version 2025-01-03
 */
@Table(name="hs_talent_introduction_conf", alias="a", label="人才引进补助配置表信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="arrival_time", attrName="arrivalTime", label="到岗时间", isUpdateForce=true),
		@Column(name="title_id", attrName="titleId", label="职称类别ID"),
		@Column(name="edu_back_id", attrName="eduBackId", label="学历"),
		@Column(name="talent_level", attrName="talentLevel", label="人才级别", queryType=QueryType.LIKE),
		@Column(name="subsidy_name", attrName="subsidyName", label="补助名称", queryType=QueryType.LIKE),
		@Column(name="subsidy_fund", attrName="subsidyFund", label="补助总金额", isUpdateForce=true),
		@Column(name="subsidy_period", attrName="subsidyPeriod", label="补助周期", isUpdateForce=true),
		@Column(name="subsidy_remark", attrName="subsidyRemark", label="补助依据", queryType=QueryType.LIKE),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="申请日期", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="申请人", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = EmpUser.class, alias = "u",
				on = "u.user_code = a.create_by", attrName = "user",
				columns = {@Column(includeEntity = EmpUser.class)}
		)
    }, orderBy="a.update_date DESC"
)
public class HsTalentIntroductionConf extends DataEntity<HsTalentIntroductionConf> {
	
	private static final long serialVersionUID = 1L;
	private Date arrivalTime;		// 到岗时间
	private String titleId;		// 职称类别ID
	private String eduBackId;		// 学历
	private String talentLevel;		// 人才级别
	private String subsidyName;		// 补助名称
	private Double subsidyFund;		// 补助总金额
	private Integer subsidyPeriod;		// 补助周期
	private String subsidyRemark;		// 补助依据
	private String validTag;		// 是否有效

	private EmpUser user;

	@ExcelFields({
			@ExcelField(title="编号", attrName="id", words=20, align= ExcelField.Align.CENTER, sort=20),
			@ExcelField(title="补助名称", attrName="subsidyName", align= ExcelField.Align.CENTER, sort=30),
			@ExcelField(title="引进时间", attrName="createDate", align= ExcelField.Align.CENTER, words=20, sort=40, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="职称", attrName="titleId", dictType="talent_title", align= ExcelField.Align.CENTER, sort=50),
			@ExcelField(title="学历", attrName="eduBackId", dictType="talent_edu_type", align= ExcelField.Align.CENTER, sort=60),
			@ExcelField(title="人才级别", attrName="talentLevel", align= ExcelField.Align.CENTER, sort=70),
			@ExcelField(title="补助总金额", attrName="subsidyFund", align= ExcelField.Align.LEFT, sort=80),
			@ExcelField(title="补助周期", attrName="subsidyPeriod", align= ExcelField.Align.LEFT, sort=90),
			@ExcelField(title="创建日期", attrName="createDate", align= ExcelField.Align.CENTER, words=20, sort=100, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
			@ExcelField(title="创建人", attrName="user.userName", align= ExcelField.Align.CENTER, words=20, sort=110, type=ExcelField.Type.EXPORT),
	})

	public HsTalentIntroductionConf() {
		this(null);
	}

	public HsTalentIntroductionConf(String id){
		super(id);
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	public Date getArrivalTime() {
		return arrivalTime;
	}

	public void setArrivalTime(Date arrivalTime) {
		this.arrivalTime = arrivalTime;
	}

	public Date getArrivalTime_gte() {
		return sqlMap.getWhere().getValue("arrival_time", QueryType.GTE);
	}

	public void setArrivalTime_gte(Date arrivalTime) {
		sqlMap.getWhere().and("arrival_time", QueryType.GTE, arrivalTime);
	}

	public Date getArrivalTime_lte() {
		return sqlMap.getWhere().getValue("arrival_time", QueryType.LTE);
	}

	public void setArrivalTime_lte(Date arrivalTime) {
		sqlMap.getWhere().and("arrival_time", QueryType.LTE, arrivalTime);
	}
	
	@Size(min=0, max=64, message="职称类别ID长度不能超过 64 个字符")
	public String getTitleId() {
		return titleId;
	}

	public void setTitleId(String titleId) {
		this.titleId = titleId;
	}

	@Size(min=0, max=64, message="学历长度不能超过 64 个字符")
	public String getEduBackId() {
		return eduBackId;
	}

	public void setEduBackId(String eduBackId) {
		this.eduBackId = eduBackId;
	}

	@Size(min=0, max=32, message="人才级别长度不能超过 64 个字符")
	public String getTalentLevel() {
		return talentLevel;
	}

	public void setTalentLevel(String talentLevel) {
		this.talentLevel = talentLevel;
	}
	
	@Size(min=0, max=255, message="补助名称长度不能超过 255 个字符")
	public String getSubsidyName() {
		return subsidyName;
	}

	public void setSubsidyName(String subsidyName) {
		this.subsidyName = subsidyName;
	}
	
	public Double getSubsidyFund() {
		return subsidyFund;
	}

	public void setSubsidyFund(Double subsidyFund) {
		this.subsidyFund = subsidyFund;
	}
	
	public Integer getSubsidyPeriod() {
		return subsidyPeriod;
	}

	public void setSubsidyPeriod(Integer subsidyPeriod) {
		this.subsidyPeriod = subsidyPeriod;
	}
	
	@Size(min=0, max=900, message="补助依据长度不能超过 900 个字符")
	public String getSubsidyRemark() {
		return subsidyRemark;
	}

	public void setSubsidyRemark(String subsidyRemark) {
		this.subsidyRemark = subsidyRemark;
	}
	
	@Size(min=0, max=1, message="是否有效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public EmpUser getUser() {
		return user;
	}

	public void setUser(EmpUser user) {
		this.user = user;
	}
}