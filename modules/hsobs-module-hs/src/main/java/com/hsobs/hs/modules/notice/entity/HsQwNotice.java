package com.hsobs.hs.modules.notice.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

import com.jeesite.common.mybatis.annotation.JoinTable;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.validation.constraints.NotNull;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;

/**
 * 租赁资格轮候公告Entity
 * <AUTHOR>
 * @version 2025-01-23
 */
@Table(name="hs_qw_notice", alias="a", label="租赁资格轮候公告信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true),
		@Column(name="notice_title", attrName="noticeTitle", label="公告主题", queryType=QueryType.LIKE),
		@Column(name="notice_content", attrName="noticeContent", label="公共内容", isQuery=false),
		@Column(name="public_date", attrName="publicDate", label="发布时间"),
		@Column(name="public_org", attrName="publicOrg", label="发布单位"),
		@Column(includeEntity=DataEntity.class),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "ha",
				on = "a.public_org = ha.office_code", attrName="office",
				columns = {@Column(includeEntity = Office.class)}),
	}, orderBy="a.update_date DESC"
)
public class HsQwNotice extends DataEntity<HsQwNotice> {
	
	private static final long serialVersionUID = 1L;
	private String noticeTitle;		// 公告主题
	private String noticeContent;		// 公共内容
	private Date publicDate;		// 发布时间
	private String publicOrg;		// 发布单位
	private Office office;

	@ExcelFields({
			@ExcelField(title="编号", attrName="id", align=Align.CENTER, sort=10),
			@ExcelField(title="公告主题", attrName="noticeTitle", align=Align.CENTER, sort=20),
			@ExcelField(title="公共内容", attrName="noticeContent", align=Align.CENTER, sort=30),
			@ExcelField(title="发布时间", attrName="publicDate", align=Align.CENTER, sort=40, dataFormat="yyyy-MM-dd hh:mm"),
			@ExcelField(title="发布单位", attrName="office.officeName", align=Align.CENTER, sort=50),
			@ExcelField(title="公告状态", attrName="status", dictType="hs_notice_status", align=Align.CENTER, sort=60),
			@ExcelField(title="创建者", attrName="createBy", align=Align.CENTER, sort=70),
			@ExcelField(title="备注信息", attrName="remarks", align=Align.CENTER, sort=110),
	})

	public HsQwNotice() {
		this(null);
	}
	
	public HsQwNotice(String id){
		super(id);
	}
	
	@NotBlank(message="公告主题不能为空")
	@Size(min=0, max=100, message="公告主题长度不能超过 100 个字符")
	public String getNoticeTitle() {
		return noticeTitle;
	}

	public void setNoticeTitle(String noticeTitle) {
		this.noticeTitle = noticeTitle;
	}
	
	@NotBlank(message="公共内容不能为空")
	@Size(min=0, max=500, message="公共内容长度不能超过 500 个字符")
	public String getNoticeContent() {
		return noticeContent;
	}

	public void setNoticeContent(String noticeContent) {
		this.noticeContent = noticeContent;
	}
	
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
	@NotNull(message="发布时间不能为空")
	public Date getPublicDate() {
		return publicDate;
	}

	public void setPublicDate(Date publicDate) {
		this.publicDate = publicDate;
	}
	
	@NotBlank(message="发布单位不能为空")
	@Size(min=0, max=100, message="发布单位长度不能超过 100 个字符")
	public String getPublicOrg() {
		return publicOrg;
	}

	public void setPublicOrg(String publicOrg) {
		this.publicOrg = publicOrg;
	}
	
	public Date getPublicDate_gte() {
		return sqlMap.getWhere().getValue("public_date", QueryType.GTE);
	}

	public void setPublicDate_gte(Date publicDate) {
		sqlMap.getWhere().and("public_date", QueryType.GTE, publicDate);
	}
	
	public Date getPublicDate_lte() {
		return sqlMap.getWhere().getValue("public_date", QueryType.LTE);
	}

	public void setPublicDate_lte(Date publicDate) {
		sqlMap.getWhere().and("public_date", QueryType.LTE, publicDate);
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}
}