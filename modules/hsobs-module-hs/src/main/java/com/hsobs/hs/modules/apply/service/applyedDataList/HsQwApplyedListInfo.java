package com.hsobs.hs.modules.apply.service.applyedDataList;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.jeesite.common.entity.Page;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import org.springframework.stereotype.Service;

/**
 * 个人信息变更-可选择申请单列表
 */
@Service
public class HsQwApplyedListInfo implements HsQwApplyedList{

    @Override
    public String getDataType() {
        return "0";
    }

    @Override
    public Page<HsQwApply> execute(HsQwApply hsQwApply,  HsQwApplyService hsQwApplyService) {
        hsQwApply.sqlMap().getWhere().disableAutoAddStatusWhere();
        hsQwApply.setStatus(null);
        hsQwApply.sqlMap().add("extWhere", " and ((a.STATUS IN (0, 4)\n" +
                "\t\tAND a.APPLY_MATTER = 0)\n") ;
        return hsQwApplyService.findPage(hsQwApply);
    }
}
