package com.hsobs.hs.modules.formalarm.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.formalarm.entity.HsQwFormAlarm;
import com.hsobs.hs.modules.formalarm.dao.HsQwFormAlarmDao;

/**
 * 表单信息预警表Service
 * 
 * <AUTHOR>
 * @version 2025-03-22
 */
@Service
public class HsQwFormAlarmService extends CrudService<HsQwFormAlarmDao, HsQwFormAlarm> {

    /**
     * 获取单条数据
     * 
     * @param hsQwFormAlarm
     * @return
     */
    @Override
    public HsQwFormAlarm get(HsQwFormAlarm hsQwFormAlarm) {
        return super.get(hsQwFormAlarm);
    }

    /**
     * 查询分页数据
     * 
     * @param hsQwFormAlarm 查询条件
     * @param hsQwFormAlarm page 分页对象
     * @return
     */
    @Override
    public Page<HsQwFormAlarm> findPage(HsQwFormAlarm hsQwFormAlarm) {
        return super.findPage(hsQwFormAlarm);
    }

    /**
     * 查询列表数据
     * 
     * @param hsQwFormAlarm
     * @return
     */
    @Override
    public List<HsQwFormAlarm> findList(HsQwFormAlarm hsQwFormAlarm) {
        return super.findList(hsQwFormAlarm);
    }

    /**
     * 保存数据（插入或更新）
     * 
     * @param hsQwFormAlarm
     */
    @Override
    @Transactional
    public void save(HsQwFormAlarm hsQwFormAlarm) {
        super.save(hsQwFormAlarm);
    }

    /**
     * 更新状态
     * 
     * @param hsQwFormAlarm
     */
    @Override
    @Transactional
    public void updateStatus(HsQwFormAlarm hsQwFormAlarm) {
        super.updateStatus(hsQwFormAlarm);
    }

    /**
     * 删除数据
     * 
     * @param hsQwFormAlarm
     */
    @Override
    @Transactional
    public void delete(HsQwFormAlarm hsQwFormAlarm) {
        super.delete(hsQwFormAlarm);
    }

    public void saveBatch(List<HsQwFormAlarm> list) {
        // TODO Auto-generated method stub
        list.forEach(a -> {
            dao.insert(a);
        });
    }

    public void dismiss(HsQwFormAlarm hsQwFormAlarm) {
        // 根据attrKey和ObjectId进行更新状态，使用where,entity更新
        HsQwFormAlarm where = new HsQwFormAlarm();
        where.setObjectId(hsQwFormAlarm.getObjectId());
        where.setAttrKey(hsQwFormAlarm.getAttrKey());
        where.sqlMap().getWhere().disableAutoAddStatusWhere();
        HsQwFormAlarm entity = new HsQwFormAlarm();
        entity.setStatus(HsQwFormAlarm.STATUS_DISABLE);
        this.dao.updateStatusByEntity(entity, where);
    }

    public void deleteBatch(HsQwFormAlarm where) {
        where.sqlMap().getWhere().disableAutoAddStatusWhere();
        HsQwFormAlarm entity = new HsQwFormAlarm();
        entity.setStatus(HsQwFormAlarm.STATUS_DELETE);
        this.dao.updateStatusByEntity(entity, where);
    }
}