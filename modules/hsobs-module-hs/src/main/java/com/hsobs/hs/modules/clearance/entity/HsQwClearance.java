package com.hsobs.hs.modules.clearance.entity;


import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.common.utils.excel.annotation.ExcelField.Align;
import com.jeesite.common.utils.excel.annotation.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 租赁资格轮候清退申请Entity
 *
 * <AUTHOR>
 * @version 2024-12-24
 */
@Table(name = "hs_qw_clearance", alias = "a", label = "租赁资格轮候清退申请信息", columns = {
        @Column(name = "id", attrName = "id", label = "编号", isPK = true),
        @Column(name = "compact_id", attrName = "compactId", label = "配租合同编号"),
        @Column(name = "apply_id", attrName = "applyId", label = "配租申请单编号"),
        @Column(name = "applyer_id", attrName = "applyerId", label = "主申请人编号"),
        @Column(name = "type", attrName = "type", label = "清退类型", comment = "清退类型（0租金拖欠 1租赁到期 2违规清退 ）"),
        @Column(name = "black_user", attrName = "blackUser", label = "是否黑名单用户", comment = "是否黑名单用户"),
        @Column(name = "reason", attrName = "reason", label = "清退原因", queryType = QueryType.LIKE),
        @Column(name = "imple_desc", attrName = "impleDesc", label = "实施说明", queryType = QueryType.LIKE),
        @Column(includeEntity = DataEntity.class),
}, joinTable = {
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApply.class, alias = "ha",
                on = "a.apply_id = ha.id", attrName = "hsQwApply",
                columns = {@Column(includeEntity = HsQwApply.class)}),
        @JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwApplyer.class, alias = "o",
                on = "o.apply_id = ha.id and o.apply_role = 0", attrName = "hsQwApply.mainApplyer", columns = {
                @Column(includeEntity = HsQwApplyer.class) }),
}, extColumnKeys = "extColumns", extFromKeys = "extForm", orderBy = "a.update_date DESC"
)
public class HsQwClearance extends BpmEntity<HsQwClearance> {

    private static final long serialVersionUID = 1L;
    private String compactId;        // 配租合同编号
    private String applyId;        // 配租申请单编号
    private String applyerId;        // 主申请人编号
    private String type;        // 清退类型（0租金拖欠 1租赁到期 2违规清退 ）
    private String reason;        // 清退原因
    private String impleDesc;        // 实施说明
    private String applyName;
    private HsQwApply hsQwApply;
    private String blackUser;

    private String isRead;

    @ExcelFields({
            @ExcelField(title="编号", attrName="id", align=Align.CENTER, sort=10),
            @ExcelField(title="申请人", attrName="hsQwApply.mainApplyer.name", align= Align.CENTER, sort=50),
            @ExcelField(title="清退类型", attrName="type", dictType="hs_qw_clearance_type", align= Align.CENTER, sort=50),
            @ExcelField(title="清退原因", attrName="reason", align=Align.CENTER, sort=60),
            @ExcelField(title="状态", attrName="status", dictType="sys_status", align=Align.CENTER, sort=80),
            @ExcelField(title="审核意见", attrName="remarks", align=Align.CENTER, sort=130),
    })
    public HsQwClearance() {
        this(null);
    }

    public HsQwClearance(String id) {
        super(id);
    }

    @Size(min = 0, max = 64, message = "配租合同编号长度不能超过 64 个字符")
    public String getCompactId() {
        return compactId;
    }

    public void setCompactId(String compactId) {
        this.compactId = compactId;
    }

    @NotBlank(message = "配租申请单编号不能为空")
    @Size(min = 0, max = 64, message = "配租申请单编号长度不能超过 64 个字符")
    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    @NotBlank(message = "主申请人编号不能为空")
    @Size(min = 0, max = 64, message = "主申请人编号长度不能超过 64 个字符")
    public String getApplyerId() {
        return applyerId;
    }

    public void setApplyerId(String applyerId) {
        this.applyerId = applyerId;
    }

    @NotBlank(message = "清退类型不能为空")
    @Size(min = 0, max = 255, message = "清退类型长度不能超过 255 个字符")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @NotBlank(message = "清退原因不能为空")
    @Size(min = 0, max = 255, message = "清退原因长度不能超过 255 个字符")
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Size(min = 0, max = 255, message = "实施说明长度不能超过 255 个字符")
    public String getImpleDesc() {
        return impleDesc;
    }

    public void setImpleDesc(String impleDesc) {
        this.impleDesc = impleDesc;
    }

    public String getApplyTitle() {
        return this.applyName + " 于" + DateUtils.formatDateTime(this.getCreateDate()) + " 发起了公租房申请";
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public HsQwApply getHsQwApply() {
        return hsQwApply;
    }

    public void setHsQwApply(HsQwApply hsQwApply) {
        this.hsQwApply = hsQwApply;
    }

    public String getBlackUser() {
        return blackUser;
    }

    public void setBlackUser(String blackUser) {
        this.blackUser = blackUser;
    }

    public String getIsRead() {
        return isRead;
    }

    public void setIsRead(String isRead) {
        this.isRead = isRead;
    }
}