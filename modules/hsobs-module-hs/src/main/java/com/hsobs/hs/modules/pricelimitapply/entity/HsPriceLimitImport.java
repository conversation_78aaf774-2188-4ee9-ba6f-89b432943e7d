package com.hsobs.hs.modules.pricelimitapply.entity;

import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;

import javax.validation.constraints.NotBlank;

public class HsPriceLimitImport extends BpmEntity<HsPriceLimitImport> {

    @ExcelFields({
            @ExcelField(title = "楼盘地址", attrName = "address", align = ExcelField.Align.CENTER, sort = 10),
            @ExcelField(title = "楼号", attrName = "buildingNum", align = ExcelField.Align.CENTER, sort = 20),
            @ExcelField(title = "楼层号", attrName = "floor", align = ExcelField.Align.CENTER, sort = 30),
            @ExcelField(title = "单元号", attrName = "unitNum", align = ExcelField.Align.CENTER, sort = 40),
            @ExcelField(title = "住房编号", attrName = "houseNum", align = ExcelField.Align.CENTER, sort = 50),
            @ExcelField(title = "申请人姓名", attrName = "name", align = ExcelField.Align.CENTER, sort = 60),
            @ExcelField(title = "身份证号", attrName = "idNum", align = ExcelField.Align.CENTER, sort = 70),
    })
    public HsPriceLimitImport() {
        super();
    }

    private String address;
    private String buildingNum;
    private String floor;
    private String unitNum;
    private String houseNum;
    private String name;
    private String idNum;

    @NotBlank(message = "楼盘地址不能为空")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @NotBlank(message = "楼号不能为空")
    public String getBuildingNum() {
        return buildingNum;
    }

    public void setBuildingNum(String buildingNum) {
        this.buildingNum = buildingNum;
    }

    @NotBlank(message = "楼层号不能为空")
    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    @NotBlank(message = "单元号不能为空")
    public String getUnitNum() {
        return unitNum;
    }

    public void setUnitNum(String unitNum) {
        this.unitNum = unitNum;
    }

    @NotBlank(message = "住房编号不能为空")
    public String getHouseNum() {
        return houseNum;
    }

    public void setHouseNum(String houseNum) {
        this.houseNum = houseNum;
    }

    @NotBlank(message = "申请人姓名不能为空")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @NotBlank(message = "身份证号不能为空")
    public String getIdNum() {
        return idNum;
    }

    public void setIdNum(String idNum) {
        this.idNum = idNum;
    }
}