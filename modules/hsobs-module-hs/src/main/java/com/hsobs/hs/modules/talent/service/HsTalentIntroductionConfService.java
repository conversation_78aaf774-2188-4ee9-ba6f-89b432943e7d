package com.hsobs.hs.modules.talent.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.talent.entity.HsTalentIntroductionConf;
import com.hsobs.hs.modules.talent.dao.HsTalentIntroductionConfDao;

/**
 * 人才引进补助配置表Service
 * <AUTHOR>
 * @version 2025-01-03
 */
@Service
public class HsTalentIntroductionConfService extends CrudService<HsTalentIntroductionConfDao, HsTalentIntroductionConf> {
	
	/**
	 * 获取单条数据
	 * @param hsTalentIntroductionConf
	 * @return
	 */
	@Override
	public HsTalentIntroductionConf get(HsTalentIntroductionConf hsTalentIntroductionConf) {
		return super.get(hsTalentIntroductionConf);
	}
	
	/**
	 * 查询分页数据
	 * @param hsTalentIntroductionConf 查询条件
	 * @param hsTalentIntroductionConf page 分页对象
	 * @return
	 */
	@Override
	public Page<HsTalentIntroductionConf> findPage(HsTalentIntroductionConf hsTalentIntroductionConf) {
		return super.findPage(hsTalentIntroductionConf);
	}
	
	/**
	 * 查询列表数据
	 * @param hsTalentIntroductionConf
	 * @return
	 */
	@Override
	public List<HsTalentIntroductionConf> findList(HsTalentIntroductionConf hsTalentIntroductionConf) {
		return super.findList(hsTalentIntroductionConf);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsTalentIntroductionConf
	 */
	@Override
	@Transactional
	public void save(HsTalentIntroductionConf hsTalentIntroductionConf) {
		super.save(hsTalentIntroductionConf);
	}
	
	/**
	 * 更新状态
	 * @param hsTalentIntroductionConf
	 */
	@Override
	@Transactional
	public void updateStatus(HsTalentIntroductionConf hsTalentIntroductionConf) {
		super.updateStatus(hsTalentIntroductionConf);
	}
	
	/**
	 * 删除数据
	 * @param hsTalentIntroductionConf
	 */
	@Override
	@Transactional
	public void delete(HsTalentIntroductionConf hsTalentIntroductionConf) {
		hsTalentIntroductionConf.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(hsTalentIntroductionConf);
	}
	
}