package com.hsobs.hs.modules.dataintelligence.service;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceTalentSubsidyDao;
import com.hsobs.hs.modules.dataintelligence.entity.*;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.dataintelligence.dao.DataIntelligenceLiftSubsidyDao;

/**
 * 住房保障数据统计Service  加装电梯补助统计
 * <AUTHOR>
 * @version 2025-1-2
 */
@Service
public class DataIntelligenceLiftSubsidyService extends CrudService<DataIntelligenceLiftSubsidyDao, DataIntelligenceLiftSubsidy> {

	@Autowired
	private DataIntelligenceLiftSubsidyDao dataIntelligenceLiftSubsidyDao;
	@Autowired
	private DataIntelligenceTotalService dataIntelligenceTotalService;

	/**
	 * 获取单条数据
	 * @param dataIntelligenceLiftSubsidy
	 * @return
	 */
	@Override
	public DataIntelligenceLiftSubsidy get(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {
		return super.get(dataIntelligenceLiftSubsidy);
	}
	
	/**
	 * 查询分页数据
	 * @param dataIntelligenceLiftSubsidy 查询条件
	 * @param dataIntelligenceLiftSubsidy page 分页对象
	 * @return
	 */
	@Override
	public Page<DataIntelligenceLiftSubsidy> findPage(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {
		return super.findPage(dataIntelligenceLiftSubsidy);
	}
	
	/**
	 * 查询列表数据
	 * @param dataIntelligenceLiftSubsidy
	 * @return
	 */
	@Override
	public List<DataIntelligenceLiftSubsidy> findList(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {
		return super.findList(dataIntelligenceLiftSubsidy);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param dataIntelligenceLiftSubsidy
	 */
	@Override
	@Transactional
	public void save(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {
		super.save(dataIntelligenceLiftSubsidy);
	}
	
	/**
	 * 更新状态
	 * @param dataIntelligenceLiftSubsidy
	 */
	@Override
	@Transactional
	public void updateStatus(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {
		super.updateStatus(dataIntelligenceLiftSubsidy);
	}
	
	/**
	 * 删除数据
	 * @param dataIntelligenceLiftSubsidy
	 */
	@Override
	@Transactional
	public void delete(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {
		dataIntelligenceLiftSubsidy.sqlMap().markIdDelete(); // 逻辑删除时标记ID值
		super.delete(dataIntelligenceLiftSubsidy);
	}

	private  String getSqlWhere(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, Date startDate, Date endDate){
		String sqlWhere = "";
		if (startDate != null || dataIntelligenceLiftSubsidy.getStartDate() != null) {
			Date date = (startDate!=null)?startDate:dataIntelligenceLiftSubsidy.getStartDate();
			sqlWhere += " AND a.create_date >= '" + String.format("%04d-%02d-%02d", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}
		if (endDate != null || dataIntelligenceLiftSubsidy.getEndDate() != null) {
			Date date = (endDate!=null)?endDate:dataIntelligenceLiftSubsidy.getEndDate();
			sqlWhere += " AND a.create_date <= '" + String.format("%04d-%02d-%02d 23:59:59", date.getYear()+1900, date.getMonth()+1, date.getDate()) + "'";
		}

		/*if(dataIntelligenceLiftSubsidy.getEstateName() != null && dataIntelligenceLiftSubsidy.getEstateName().length() > 0) {
			sqlWhere += " AND estate.name like '%" + dataIntelligenceLiftSubsidy.getEstateName() + "%'";
		}*/
		if(dataIntelligenceLiftSubsidy.getCity() != null && !"".equals(dataIntelligenceLiftSubsidy.getCity())){
			sqlWhere += " AND a.city = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceLiftSubsidy.getCity()) + "'";
		}
		if(dataIntelligenceLiftSubsidy.getArea() != null && !"".equals(dataIntelligenceLiftSubsidy.getArea())){
			sqlWhere += " AND a.area = '" + dataIntelligenceTotalService.CheckDigitStr(dataIntelligenceLiftSubsidy.getArea()) + "'";
		}

		dataIntelligenceLiftSubsidy.sqlMap().getDataScope().addFilter("dsf", "Office", "jso.office_code", DataScope.CTRL_PERMI_HAVE);
		SqlMap sqlMap = dataIntelligenceLiftSubsidy.sqlMap();
		Object obWhere = sqlMap.get("dsf");
		if(obWhere != null){
			sqlWhere += obWhere.toString();
		}
		String officeCode = null;
		if(dataIntelligenceLiftSubsidy.getOfficeCode() != null && dataIntelligenceLiftSubsidy.getOfficeCode().length() > 0) {
			officeCode = dataIntelligenceLiftSubsidy.getOfficeCode();
		}
		sqlWhere += dataIntelligenceTotalService.getOfficePermission(officeCode);

		if(sqlWhere.length() > 5){
			return "WHERE " + sqlWhere.substring(5);
		}
		return "";
	}

	public Page<DataIntelligenceLiftSubsidy> findLiftSubsidyStatTotalPage(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, boolean findpage){

		String sqlWhere = getSqlWhere(dataIntelligenceLiftSubsidy, null, null);

		Page<DataIntelligenceLiftSubsidy> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		List<Map<String,Object>> list = dataIntelligenceLiftSubsidyDao.countLiftSubsidyTotal(sqlWhere);
		List<DataIntelligenceLiftSubsidy> statList = new ArrayList<>();

		DataIntelligenceLiftSubsidy liftStat = new DataIntelligenceLiftSubsidy();
		statList.add(liftStat);
		pageMap.setList(statList);

		if(list.size() != 1){
			liftStat.setApplyTotalCount(0L);
			liftStat.setApplyingCount(0L);
			liftStat.setApprovedCount(0L);

			liftStat.setApplyTotalSubsidyFund(0);
			liftStat.setApplyingSubsidyFund(0);
			liftStat.setApprovedSubsidyFund(0);
		}

		Map<String, Object> map = list.get(0);
		// 申请总人数
		Object ob = map.get("APPLY_TOTAL_COUNT");
		liftStat.setApplyTotalCount((ob!=null)?Long.valueOf(ob.toString()):0);
		// 申请中人数
		ob = map.get("APPLYING_COUNT");
		liftStat.setApplyingCount((ob!=null)?Long.valueOf(ob.toString()):0);
		// 审核通过人数
		ob = map.get("APPROVED_COUNT");
		liftStat.setApprovedCount((ob!=null)?Long.valueOf(ob.toString()):0);

		// 申请总资金
		ob = map.get("APPLY_TOTAL_SUBSIDY_FUND");
		liftStat.setApplyTotalSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);
		// 申请中补助资金
		ob = map.get("APPLYING_SUBSIDY_FUND");
		liftStat.setApplyingSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);
		// 审核通过补助资金
		ob = map.get("APPROVED_SUBSIDY_FUND");
		liftStat.setApprovedSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);

		return pageMap;
	}

	public Page<DataIntelligenceLiftSubsidy> findLiftSubsidyStatPage(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy, boolean findpage){

		String sqlWhere = getSqlWhere(dataIntelligenceLiftSubsidy, null, null);
		String sqlOrderBy = (dataIntelligenceLiftSubsidy.getOrderBy()!=null&&dataIntelligenceLiftSubsidy.getOrderBy().length()>3)?"ORDER BY "+ dataIntelligenceTotalService.CheckOrderBy(dataIntelligenceLiftSubsidy.getOrderBy()):"";

		Page<DataIntelligenceLiftSubsidy> pageMap = new Page<>();
		Map<String, Object> mapPara = new HashMap<>();
		mapPara.put("sqlWhere", sqlWhere);
		mapPara.put("sqlOrderBy", sqlOrderBy);
		if(findpage){
			pageMap.setPageNo(dataIntelligenceLiftSubsidy.getPageNo());
			pageMap.setPageSize(dataIntelligenceLiftSubsidy.getPageSize());
			mapPara.put("page", pageMap);
		}
		List<Map<String,Object>> list = dataIntelligenceLiftSubsidyDao.countLiftSubsidyByEstate(mapPara);
		List<DataIntelligenceLiftSubsidy> statList = new ArrayList<>();

		for (int j = 0; j < list.size(); j++) {
			Map<String, Object> map = list.get(j);
			DataIntelligenceLiftSubsidy liftStat = new DataIntelligenceLiftSubsidy();

			Object ob = map.get("AREA_NAME");
			liftStat.setArea((ob!=null)?ob.toString():"");

			// 申请总人数
			ob = map.get("APPLY_TOTAL_COUNT");
			liftStat.setApplyTotalCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 申请中人数
			ob = map.get("APPLYING_COUNT");
			liftStat.setApplyingCount((ob!=null)?Long.valueOf(ob.toString()):0);
			// 审核通过人数
			ob = map.get("APPROVED_COUNT");
			liftStat.setApprovedCount((ob!=null)?Long.valueOf(ob.toString()):0);

			// 申请总资金
			ob = map.get("APPLY_TOTAL_SUBSIDY_FUND");
			liftStat.setApplyTotalSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);
			// 申请中补助资金
			ob = map.get("APPLYING_SUBSIDY_FUND");
			liftStat.setApplyingSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);
			// 审核通过补助资金
			ob = map.get("APPROVED_SUBSIDY_FUND");
			liftStat.setApprovedSubsidyFund((ob!=null)?Float.valueOf(ob.toString()):0);

			statList.add(liftStat);
		}
		pageMap.setList(statList);
		return pageMap;
	}

    public List<DataIntelligenceLiftSubsidy> countLiftSubsidyStat(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {

		// 加装电梯补助统计
		return findLiftSubsidyStatPage(dataIntelligenceLiftSubsidy, false).getList();
    }

	public void SetTypeData(List<Map<String,Object>> value, List<List<String>> dataType){
		if(value.size() == 0) {
			dataType.get(0).add("0");
			dataType.get(1).add("0");
		}
		else {
			Map<String,Object> map = value.get(0);

			// 申请数
			Object ob = map.get("APPLY_TOTAL_COUNT");
			dataType.get(0).add((ob!=null)?ob.toString():"0");
			// 审批通过数
			ob = map.get("APPROVED_COUNT");
			dataType.get(1).add((ob!=null)?ob.toString():"0");
		}
	}
	public String findLiftSubsidyCompare(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 2; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "申请数");break;
				case 1:dataTypeO.put("name", "审批通过数");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		Map<String, Object> mapPara = dataIntelligenceTotalService.getComparePara(dataIntelligenceLiftSubsidy.getCompareType());

		String sqlWhere = getSqlWhere(dataIntelligenceLiftSubsidy, (Date)mapPara.get("last_start_date"), (Date)mapPara.get("last_end_date"));
		List<Map<String,Object>> value = dataIntelligenceLiftSubsidyDao.countLiftSubsidyCompare(sqlWhere);
		SetTypeData(value, dataType);

		sqlWhere = getSqlWhere(dataIntelligenceLiftSubsidy, (Date)mapPara.get("now_start_date"), (Date)mapPara.get("now_end_date"));
		value = dataIntelligenceLiftSubsidyDao.countLiftSubsidyCompare(sqlWhere);
		SetTypeData(value, dataType);

		dataClass.add((String)mapPara.get("last_title"));
		dataClass.add((String)mapPara.get("now_title"));

		Integer max = dataIntelligenceTotalService.calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}

	public void SetPayData(List<Map<String,Object>> value, List<List<String>> dataType){
		if(value.size() == 0) {
			dataType.get(0).add("0");
		}
		else {
			Map<String,Object> map = value.get(0);

			// 补助费用
			Object ob = map.get("APPROVED_SUBSIDY_FUND");
			dataType.get(0).add((ob!=null)?ob.toString():"0");
		}
	}

	public String findLiftSubsidyPayCompare(DataIntelligenceLiftSubsidy dataIntelligenceLiftSubsidy) {

		Map<String, Object> map = new HashMap<>();
		List<String> dataClass = new ArrayList<>();

		List<List<String>> dataType = new ArrayList<>();
		for(int i = 0; i < 1; i ++){
			Map<String, Object> dataTypeO = new HashMap<>();
			switch(i) {
				case 0:dataTypeO.put("name", "补助费用");break;
			}
			map.put(String.format("type%d", i+1), dataTypeO);

			List<String> dataTypeD = new ArrayList<>();
			dataTypeO.put("value", dataTypeD);
			dataType.add(dataTypeD);
		}

		Map<String, Object> mapPara = dataIntelligenceTotalService.getComparePara(dataIntelligenceLiftSubsidy.getCompareType());

		String sqlWhere = getSqlWhere(dataIntelligenceLiftSubsidy, (Date)mapPara.get("last_start_date"), (Date)mapPara.get("last_end_date"));
		List<Map<String,Object>> value = dataIntelligenceLiftSubsidyDao.countLiftSubsidyCompare(sqlWhere);
		SetPayData(value, dataType);

		sqlWhere = getSqlWhere(dataIntelligenceLiftSubsidy, (Date)mapPara.get("now_start_date"), (Date)mapPara.get("now_end_date"));
		value = dataIntelligenceLiftSubsidyDao.countLiftSubsidyCompare(sqlWhere);
		SetPayData(value, dataType);

		dataClass.add((String)mapPara.get("last_title"));
		dataClass.add((String)mapPara.get("now_title"));

		Integer max = dataIntelligenceTotalService.calcMaxValue(dataType);
		map.put("max", max);
		map.put("interval", max/5);

		map.put("class", dataClass.stream()
				.distinct()
				.collect(Collectors.toList()));
		return JSON.toJSONString(map);
	}
}