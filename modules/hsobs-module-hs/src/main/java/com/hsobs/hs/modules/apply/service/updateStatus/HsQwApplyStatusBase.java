package com.hsobs.hs.modules.apply.service.updateStatus;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.applyer.dao.HsQwApplyerDao;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyer.service.HsQwApplyerService;
import com.hsobs.hs.modules.applyhis.entity.HsQwApplyHis;
import com.hsobs.hs.modules.applyhis.service.HsQwApplyHisService;
import com.hsobs.hs.modules.applyhouse.dao.HsQwApplyHouseDao;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import com.hsobs.hs.modules.applyhouse.service.HsQwApplyHouseService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.hsobs.hs.modules.utils.HsBeanMapperUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class HsQwApplyStatusBase {

    @Autowired
    HsQwCompactService hsQwCompactService;

    @Autowired
    HsQwApplyHisService hsQwApplyHisService;

    @Autowired
    HsQwApplyerDao hsQwApplyerDao;

    @Autowired
    HsQwApplyHouseDao hsQwApplyHouseDao;


    /**
     * 申请单变更前，信息归档处理
     * @param hsQwApply
     */
    public void saveToApplyHis(HsQwApply hsQwApply) {
        HsQwApplyHis hsQwApplyHis = new HsQwApplyHis();
        BeanUtils.copyProperties(hsQwApply, hsQwApplyHis);
        hsQwApplyHis.setApplyId(hsQwApply.getId());
        hsQwApplyHis.setId(null);
        hsQwApplyHisService.save(hsQwApplyHis);
    }

    /**
     * 执行房源状态变更
     * @param houseId
     * @param status
     */
    public void updateHouseStatus(String houseId, String status, HsQwApplyService hsQwApplyService) {
        hsQwApplyService.updateHouseStatus(houseId, status);
    }

    /**
     * 执行申请单关联人员深拷贝
     */
    public void copyAndInvalidApplyer(HsQwApply oldHsQwApply,HsQwApply newHsQwApply) {
        // 旧申请人员信息失效
        HsQwApplyer query = new HsQwApplyer();
        query.setApplyId(oldHsQwApply.getId());
        HsQwApplyer updateEntity = new HsQwApplyer();
        updateEntity.setStatus(HsQwApplyer.STATUS_DISABLE);
        hsQwApplyerDao.updateStatusByEntity(updateEntity, query);
        // 拷贝新申请人员信息
        List<HsQwApplyer> list = new ArrayList<>();
        newHsQwApply.getHsQwApplyerList().stream().forEach(k -> {
            HsQwApplyer newObj = new HsQwApplyer();
            BeanUtils.copyProperties(k, newObj);
            newObj.setId(null);
            newObj.setApplyId(oldHsQwApply.getId());
            newObj.setCreateBy(null);
            newObj.setCreateDate(null);
            list.add(newObj);
        });
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                hsQwApplyerDao.insert(list.get(i));
            }
            oldHsQwApply.setHsQwApplyerList(list);
        }
    }

    /**
     * 执行申请单关联房源深拷贝
     */
    public void copyAndInvalidApplyHouse(HsQwApply oldHsQwApply,HsQwApply newHsQwApply) {
        // 旧申请房源信息失效
        HsQwApplyHouse query = new HsQwApplyHouse();
        query.setApplyId(oldHsQwApply.getId());
        HsQwApplyHouse updateEntity = new HsQwApplyHouse();
        updateEntity.setStatus(HsQwApplyHouse.STATUS_DISABLE);
        hsQwApplyHouseDao.updateStatusByEntity(updateEntity, query);
        // 拷贝新申请房源信息
        List<HsQwApplyHouse> list = new ArrayList<>();
        newHsQwApply.getHsQwApplyHouseList().stream().forEach(k -> {
            HsQwApplyHouse newObj = new HsQwApplyHouse();
            BeanUtils.copyProperties(k, newObj);
            newObj.setId(null);
            newObj.setApplyId(oldHsQwApply.getId());
            newObj.setCreateBy(null);
            newObj.setCreateDate(null);
            list.add(newObj);
        });
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                hsQwApplyHouseDao.insert(list.get(i));
            }
            oldHsQwApply.setHsQwApplyHouseList(list);
        }
    }

    /**
     * 执行申请单关联合同深拷贝
     */
    public void copyAndInvalidCompact(HsQwApply oldHsQwApply,HsQwApply newHsQwApply){
        // 旧申请合同信息失效
        HsQwCompact hsQwCompact = oldHsQwApply.getCompact();
        hsQwCompact.setStatus(HsQwCompact.STATUS_DISABLE);
        hsQwCompactService.updateStatus(hsQwCompact);
        // 新申请合同信息生效
        HsQwCompact compact = newHsQwApply.getCompact();
        compact.setStatus(HsQwCompact.STATUS_NORMAL);
        hsQwCompactService.updateStatus(compact);
        // 拷贝新申请合同信息
        HsQwCompact newHsQwCompact = new HsQwCompact();
        BeanUtils.copyProperties(compact, newHsQwCompact);
        newHsQwCompact.setId(null);
        newHsQwCompact.setApplyId(oldHsQwApply.getId());
        newHsQwCompact.setCreateBy(null);
        newHsQwCompact.setCreateDate(null);
        newHsQwCompact.setStatus(HsQwCompact.STATUS_NORMAL);
        hsQwCompactService.insert(newHsQwCompact);
        oldHsQwApply.setCompact(newHsQwCompact);
    }
}
