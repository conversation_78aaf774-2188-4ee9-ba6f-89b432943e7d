package com.hsobs.hs.modules.maintenance.service;

import com.hsobs.hs.modules.maintenance.dao.HsSequenceDao;
import com.hsobs.hs.modules.maintenance.entity.HsSequence;
import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import groovy.lang.Tuple2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 序列表Service
 * <AUTHOR>
 * @version 2024-12-19
 */
@Service
public class HsSequenceService extends CrudService<HsSequenceDao, HsSequence> {

	private static final ReentrantLock COMMON_LOCK = new ReentrantLock();
	private static final ReentrantLock MAINTENANCE_FAV_NO_LOCK = new ReentrantLock();
	private static final ReentrantLock MAINTENANCE_ADN_LOCK = new ReentrantLock();


	/**
	 * 获取单条数据
	 * @param hsSequence
	 * @return
	 */
	@Override
	public HsSequence get(HsSequence hsSequence) {
		return super.get(hsSequence);
	}
	
	/**
	 * 查询分页数据
	 * @param hsSequence 查询条件
	 * @param hsSequence page 分页对象
	 * @return
	 */
	@Override
	public Page<HsSequence> findPage(HsSequence hsSequence) {
		return super.findPage(hsSequence);
	}
	
	/**
	 * 查询列表数据
	 * @param hsSequence
	 * @return
	 */
	@Override
	public List<HsSequence> findList(HsSequence hsSequence) {
		hsSequence.sqlMap().getWhere().disableAutoAddStatusWhere();
		return super.findList(hsSequence);
	}

	public HsSequence findOne(HsSequence hsSequence) {
		List<HsSequence> list = findList(hsSequence);
		if (list != null && !list.isEmpty()) {
			return list.get(0);
		}
		return null;
	}

	/**
	 * 保存数据（插入或更新）
	 * @param hsSequence
	 */
	@Override
	@Transactional
	public void save(HsSequence hsSequence) {
		super.save(hsSequence);
	}
	
	/**
	 * 更新状态
	 * @param hsSequence
	 */
	@Override
	@Transactional
	public void updateStatus(HsSequence hsSequence) {
		super.updateStatus(hsSequence);
	}
	
	/**
	 * 删除数据
	 * @param hsSequence
	 */
	@Override
	@Transactional
	public void delete(HsSequence hsSequence) {
		super.delete(hsSequence);
	}

    public Long getNextValue(String seqName) {
		return getNextValue(seqName, 1).getV1();
    }

	Tuple2<Long, Long> getNextRangeValue(String seqName, int step) {
		return getNextValue(seqName, step);
	}

    private Tuple2<Long, Long> getNextValue(String seqName, int step) {
		Long retVal1 = null;
		Long retVal2 = null;
		COMMON_LOCK.lock();
		try {
			HsSequence hsSequence = new HsSequence();
			hsSequence.setSeqName(seqName);
			hsSequence = this.findOne(hsSequence);
			if (hsSequence == null) {
				hsSequence = new HsSequence();
				hsSequence.setSeqName(seqName);
				retVal1 = 1L;
				retVal2 = Integer.valueOf(step).longValue();
				hsSequence.setCurrentVal(retVal2);
				super.save(hsSequence);
			} else {
				retVal1 = hsSequence.getCurrentVal() + 1;
				retVal2 = hsSequence.getCurrentVal() + step;
				hsSequence.setCurrentVal(retVal2);
				super.update(hsSequence);
			}
		} catch (Exception e) {
			retVal1 = null;
			retVal2 = null;
		} finally {
			COMMON_LOCK.unlock();
		}
		return new Tuple2<>(retVal1, retVal2);
    }
}