package com.hsobs.hs.modules.dataintelligence.entity;

import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.UserUtils;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.text.SimpleDateFormat;

/**
 * 住房保障数据统计Entity   人才住房补助统计
 * <AUTHOR>
 * @version 2025-1-2
 */
public class DataIntelligenceTalentSubsidy extends DataEntity<DataIntelligenceTalentSubsidy> {

	@ExcelFields({
			@ExcelField(title = "人才类型", attrName = "talentLevel", align = ExcelField.Align.CENTER, sort = 40),
			@ExcelField(title = "申请数", attrName = "applyingCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "审批通过数", attrName = "approvedCount", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "申请资金(元)", attrName = "applyingSubsidyFund", align = ExcelField.Align.CENTER, sort = 30),
			@ExcelField(title = "审批通过资金(元)", attrName = "approvedSubsidyFund", align = ExcelField.Align.CENTER, sort = 30)
	})

	private static final long serialVersionUID = 1L;
	private String parentCodeTree;

	private String city;
	private String area;

	private String officeCode;		// 单位编码
	private String talentType;		// 人才类别（1\2\3类，低中高）
	private Date startDate;		//开始日期
	private Date endDate;	//结束日期
	private Integer compareType;

	private String talentLevel;		// 人才类型（学位）
	private Long applyTotalCount;  // 申请总数
	private Long applyingCount;		// 申请中总数
	private Long approvedCount;		// 审核通过数
	private float applyTotalSubsidyFund;	// 总申请资金汇总
	private float applyingSubsidyFund;    // 申请中资金汇总
	private float approvedSubsidyFund;    // 审核通过资金汇总

	public DataIntelligenceTalentSubsidy() {
		this(null);
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			endDate = new Date();
			startDate = dateFormat.parse(String.format("%04d-01-01", endDate.getYear()+1900));  // 解析指定日期字符串
		} catch (Exception e) {
		}
		parentCodeTree = "";
		if(!UserUtils.getUser().isAdmin()){
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		else{
			//parentCodeTree = "?parentCode=" + EmpUtils.getCurrentOfficeCode() + "&isAll=true";
		}
		compareType = 19;
	}
	
	public DataIntelligenceTalentSubsidy(String id){
		super(id);
	}

	public String getParentCodeTree() {
		return parentCodeTree;
	}

	public void setParentCodeTree(String parentCodeTree) {
		this.parentCodeTree = parentCodeTree;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getTalentType() {
		return talentType;
	}

	public void setTalentType(String talentType) {
		this.talentType = talentType;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getCompareType() {
		return compareType;
	}

	public void setCompareType(Integer compareType) {
		this.compareType = compareType;
	}


	public String getTalentLevel() {
		return talentLevel;
	}

	public void setTalentLevel(String talentLevel) {
		this.talentLevel = talentLevel;
	}

	public Long getApplyTotalCount() {
		return applyTotalCount;
	}

	public void setApplyTotalCount(Long applyTotalCount) {
		this.applyTotalCount = applyTotalCount;
	}

	public Long getApplyingCount() {
		return applyingCount;
	}

	public void setApplyingCount(Long applyingCount) {
		this.applyingCount = applyingCount;
	}

	public Long getApprovedCount() {
		return approvedCount;
	}

	public void setApprovedCount(Long approvedCount) {
		this.approvedCount = approvedCount;
	}

	public float getApplyTotalSubsidyFund() {
		return applyTotalSubsidyFund;
	}

	public void setApplyTotalSubsidyFund(float applyTotalSubsidyFund) {
		this.applyTotalSubsidyFund = applyTotalSubsidyFund;
	}

	public float getApplyingSubsidyFund() {
		return applyingSubsidyFund;
	}

	public void setApplyingSubsidyFund(float applyingSubsidyFund) {
		this.applyingSubsidyFund = applyingSubsidyFund;
	}

	public float getApprovedSubsidyFund() {
		return approvedSubsidyFund;
	}

	public void setApprovedSubsidyFund(float approvedSubsidyFund) {
		this.approvedSubsidyFund = approvedSubsidyFund;
	}
}