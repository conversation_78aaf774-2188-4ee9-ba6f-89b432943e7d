package com.hsobs.hs.modules.maintenance.entity;

import javax.validation.constraints.Size;

import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.sys.entity.Office;

/**
 * 维修资金信息Entity
 * <AUTHOR>
 * @version 2024-11-28
 */
@Table(name="hs_maintenance_funds", alias="a", label="维修资金信息表信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="house_id", attrName="houseId", label="房屋信息"),
		@Column(name="input_funds", attrName="inputFunds", label="维修资金总额", isUpdate = false),
		@Column(name="used_funds", attrName="usedFunds", label="已使用资金额度", isUpdate = false),
		@Column(name="apply_funds", attrName="applyFunds", label="申请中资金额度", isUpdate = false),
		@Column(name="status", attrName="status", label="资金状态-预留", isUpdate=false, isUpdateForce=true),
		@Column(name="remark", attrName="remark", label="资金来源简述"),
		@Column(name="input_year", attrName="inputYear", label="录入年份", queryType=QueryType.LIKE),
		@Column(name="input_month", attrName="inputMonth", label="录入月份"),
		@Column(name="fund_name", attrName="fundName", label="帐户名称" , isUpdate = false),
		@Column(name="create_by", attrName="createBy", label="录入人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="录入日期", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = HsQwPublicRentalEstate.class, alias = "h",
				on = "h.id = a.house_id", attrName = "publicRentalEstate",
				columns = {@Column(includeEntity = HsQwPublicRentalEstate.class)}
		)
    }, orderBy="a.update_date DESC"
)
public class HsMaintenanceFunds extends DataEntity<HsMaintenanceFunds> {
	
	private static final long serialVersionUID = 1L;
	private String houseId;		// 房屋信息
	private Double inputFunds;		// 维修资金总额
	private Double usedFunds;		// 已使用资金额度
	private Double applyFunds;		// 申请中资金额度
	private String remark;		// 资金来源简述
	private String inputYear;		// 录入年份
	private String inputMonth;		// 录入月份
	private String validTag;		// 是否有效;1-有效 0-无效

	private String fundName;  // 专项维修资金帐户名称

	private Double changeFund;  // 变动金额

	private HsQwPublicRentalEstate publicRentalEstate;

	@ExcelFields({
        @ExcelField(title="编号", attrName="id", align= ExcelField.Align.CENTER, sort=20),
        @ExcelField(title="楼盘名称", attrName="publicRentalEstate.name", align= ExcelField.Align.CENTER, sort=30),
        @ExcelField(title="账户名称", attrName="fundName", align= ExcelField.Align.CENTER, sort=40),
        @ExcelField(title="资金总额", attrName="inputFunds", align= ExcelField.Align.CENTER, sort=50),
        @ExcelField(title="已使用额度", attrName="usedFunds", align= ExcelField.Align.CENTER, sort=60),
        @ExcelField(title="申请中资金额度", attrName="applyFunds", align= ExcelField.Align.CENTER, sort=70),
        @ExcelField(title="简述", attrName="remark", align= ExcelField.Align.LEFT, sort=80),
		@ExcelField(title="创建日期", attrName="createDate", align= ExcelField.Align.CENTER, words=15, sort=90, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
		@ExcelField(title="更新日期", attrName="updateDate", align= ExcelField.Align.CENTER, words=15, sort=100, type=ExcelField.Type.EXPORT, dataFormat="yyyy-MM-dd HH:mm:ss"),
	})
	public HsMaintenanceFunds() {
		this(null);
	}
	
	public HsMaintenanceFunds(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="房屋信息长度不能超过 64 个字符")
	public String getHouseId() {
		return houseId;
	}

	public void setHouseId(String houseId) {
		this.houseId = houseId;
	}
	
	public Double getInputFunds() {
		return inputFunds;
	}

	public void setInputFunds(Double inputFunds) {
		this.inputFunds = inputFunds;
	}
	
	public Double getUsedFunds() {
		return usedFunds;
	}

	public void setUsedFunds(Double usedFunds) {
		this.usedFunds = usedFunds;
	}
	
	public Double getApplyFunds() {
		return applyFunds;
	}

	public void setApplyFunds(Double applyFunds) {
		this.applyFunds = applyFunds;
	}
	
	@Size(min=0, max=900, message="资金来源简述长度不能超过 900 个字符")
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	@Size(min=0, max=4, message="录入年份长度不能超过 4 个字符")
	public String getInputYear() {
		return inputYear;
	}

	public void setInputYear(String inputYear) {
		this.inputYear = inputYear;
	}
	
	@Size(min=0, max=4, message="录入月份长度不能超过 4 个字符")
	public String getInputMonth() {
		return inputMonth;
	}

	public void setInputMonth(String inputMonth) {
		this.inputMonth = inputMonth;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public Double getChangeFund() {
		return changeFund;
	}

	public void setChangeFund(Double changeFund) {
		this.changeFund = changeFund;
	}

	public HsQwPublicRentalEstate getPublicRentalEstate() {
		return publicRentalEstate;
	}

	public void setPublicRentalEstate(HsQwPublicRentalEstate publicRentalEstate) {
		this.publicRentalEstate = publicRentalEstate;
	}
}