package com.hsobs.hs.modules.datasync.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 获取用户所在单位下的用户列表响应实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserUnderUserOrgData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String userId;
    private String mobile;
    private String name;
    private String orgId;
    private String orgName;
    private String account;
    private Integer status;
    private String certType;
    private String certTypeName;
    private String idCard;
    private Integer type;

}
