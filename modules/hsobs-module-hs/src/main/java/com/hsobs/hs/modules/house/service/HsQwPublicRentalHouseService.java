package com.hsobs.hs.modules.house.service;

import com.hsobs.hs.modules.apply.dao.HsQwApplyDao;
import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.applyedDataList.HsQwApplyedList;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.estate.service.HsQwPublicRentalEstateService;
import com.hsobs.hs.modules.external.entity.ApiHsHouseTemplate;
import com.hsobs.hs.modules.external.entity.ApiHsPriceLimitHouseTemplate;
import com.hsobs.hs.modules.house.dao.HsQwPublicRentalHouseDao;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.deleteCheck.HsQwHouseDeleteCheckMethod;
import com.hsobs.hs.modules.house.service.listSelectData.HsQwHouseSelectList;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.DataScope;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.mybatis.mapper.SqlMap;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.service.CrudService;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.utils.excel.ExcelImport;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.modules.file.utils.FileUploadUtils;
import com.jeesite.modules.sys.entity.Office;
import com.jeesite.modules.sys.entity.api.Api2NoticeBody;
import com.jeesite.modules.sys.service.ApiSzkjService;
import com.jeesite.modules.sys.service.ApiSzpsService;
import com.jeesite.modules.sys.service.OfficeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租赁公租房房源房源信息表Service
 *
 * <AUTHOR>
 * @version 2024-11-20
 */
@Service
public class HsQwPublicRentalHouseService extends CrudService<HsQwPublicRentalHouseDao, HsQwPublicRentalHouse> implements ApplicationContextAware {

    @Autowired
    private HsQwPublicRentalEstateService hsQwPublicRentalEstateService;

    @Autowired
    private HsQwApplyDao hsQwApplyDao;

    @Autowired
    private OfficeService officeService;

    @Autowired
    private ApiSzkjService apiSzkjService;

    @Autowired
    private ApiSzpsService apiSzpsService;

    private Map<String, HsQwHouseSelectList> hsQwHouseSelectListMap = new HashMap<>();

    private List<HsQwHouseDeleteCheckMethod> hsQwHouseDeleteCheckMethods = new ArrayList<>();

    /**
     * 获取单条数据
     *
     * @param hsQwPublicRentalHouse
     * @return
     */
    @Override
    public HsQwPublicRentalHouse get(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        return super.get(hsQwPublicRentalHouse);
    }

    /**
     * 查询分页数据
     *
     * @param hsQwPublicRentalHouse 查询条件
     * @param hsQwPublicRentalHouse page 分页对象
     * @return
     */
    @Override
    public Page<HsQwPublicRentalHouse> findPage(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        if (hsQwPublicRentalHouse.getType().equals(HsQwPublicRentalHouse.HOUSE_TYPE_BUREAU)){
            return this.findBureauPage(hsQwPublicRentalHouse);
        } else if (hsQwPublicRentalHouse.getType().equals(HsQwPublicRentalHouse.HOUSE_TYPE_PROVINCE)){
            return this.findProvincePage(hsQwPublicRentalHouse);
        }
        return super.findPage(hsQwPublicRentalHouse);
    }

    @Override
    public void addDataScopeFilter(HsQwPublicRentalHouse entity) {
        SqlMap sqlMap = entity.sqlMap(); // v5.3.0+ 及之后版本
        // 举例2：部门数据权限过滤，实体类@Table注解extWhereKeys="dsf"
        sqlMap.getDataScope().addFilter("extWhere", "Office",
                "a.office_code", "a.create_by", DataScope.CTRL_PERMI_HAVE, "hsQwPublicRentalHouse");
    }

    public Page<HsQwPublicRentalHouse> findBureauPage(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_BUREAU);
        hsQwPublicRentalHouse.sqlMap().add("extColumns", " hqa.name as \"bureau.mainApplyer.name\", " +
                "hqb.status as \"bureau.status\"," +
                "hqb.id as \"bureau.id\"," +
                "hqb.auto_check as \"bureau.autoCheck\"," +
                "hqb.check_date as \"bureau.checkDate\"");
        hsQwPublicRentalHouse.sqlMap().add("extForm",
                " left join hs_qw_apply_bureau hqb on a.id = hqb.house_id and hqb.status in ('0','4') " +
                        " left join hs_qw_applyer hqa on hqb.id = hqa.apply_id and hqa.status=0 and hqa.apply_role=0 ");
        return super.findPage(hsQwPublicRentalHouse);
    }


    /**
     * 查询分页数据-可配租的房源分页
     *
     * @param hsQwPublicRentalHouse 查询条件
     * @param hsQwPublicRentalHouse page 分页对象
     * @return
     */
    public Page<HsQwPublicRentalHouse> findApplyPage(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        if (StringUtils.isNotBlank(hsQwPublicRentalHouse.getApplyedIdStr())) {
            hsQwPublicRentalHouse.sqlMap().getWhere().and("id", QueryType.IN, hsQwPublicRentalHouse.getApplyedIdStr().split(","));
        }
        hsQwPublicRentalHouse.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);//正常状态，非配租中
        if(hsQwPublicRentalHouse.getSelectTypes()!=null){
            hsQwPublicRentalHouse.sqlMap().getWhere().and("ha.apply_matter", QueryType.IN, hsQwPublicRentalHouse.getSelectTypes().split(","));
        }
//        hsQwPublicRentalHouse.setHouseStatus("0");//待配租的房源
        return this.findPage(hsQwPublicRentalHouse);
    }

    /**
     * 查询分页数据-查询配售房房源信息
     *
     * @param hsQwPublicRentalHouse 查询条件
     * @param hsQwPublicRentalHouse page 分页对象
     * @return
     */
    public Page<HsQwPublicRentalHouse> findPlacementHousePage(HsQwPublicRentalHouse hsQwPublicRentalHouse) {

		if(Objects.equals(hsQwPublicRentalHouse.getType(), "1")){// 限价房
		}
		else if(Objects.equals(hsQwPublicRentalHouse.getType(), "2")){// 公有住房
			hsQwPublicRentalHouse.setHouseSaleStatus("0");
			//hsQwPublicRentalHouse.setType(null);
			//hsQwPublicRentalHouse.sqlMap().getWhere().and("type", QueryType.IN, new String[]{"2","3"});	// 公有住房和省直公房
			//
		}
		hsQwPublicRentalHouse.setStatus("0");//必须是正常状态的房源
		hsQwPublicRentalHouse.setHouseStatus("2");	// 未售出状态
		hsQwPublicRentalHouse.setIsPublic("1");//必须是已发布的房源
		return this.findPage(hsQwPublicRentalHouse);
	}

	/**
	 * 更新房源配售状态，针对公有住房
	 * @param hsQwPublicRentalHouse 查询条件
	 * @param status 0未配售，1已配售
	 * @return
	 */
	public void UpdateHouseSaleStatus(HsQwPublicRentalHouse hsQwPublicRentalHouse, String status){

		hsQwPublicRentalHouse.setHouseSaleStatus(status);
		this.update(hsQwPublicRentalHouse);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwPublicRentalHouse
	 * @return
	 */
	@Override
	public List<HsQwPublicRentalHouse> findList(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
		return super.findList(hsQwPublicRentalHouse);
	}

    /**
     * 保存数据（插入或更新）
     *
     * @param hsQwPublicRentalHouse
     */
    @Override
    @Transactional
    public void save(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        this.checkOnly(hsQwPublicRentalHouse);
        super.save(hsQwPublicRentalHouse);
        // 保存上传图片
        FileUploadUtils.saveFileUpload(hsQwPublicRentalHouse, hsQwPublicRentalHouse.getId(), "hsQwHouse_plane");
    }

    private void checkOnly(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
        query.setEstateId(hsQwPublicRentalHouse.getEstateId());
        query.setBuildingNum(hsQwPublicRentalHouse.getBuildingNum());
        query.setFloor(hsQwPublicRentalHouse.getFloor());
        query.setHouseNum(hsQwPublicRentalHouse.getHouseNum());
        query.setBuildingArea(hsQwPublicRentalHouse.getBuildingArea());
        query.setUnitNum(hsQwPublicRentalHouse.getUnitNum());
        if (StringUtils.isNotBlank(hsQwPublicRentalHouse.getId())){
            query.sqlMap().getWhere().and("id", QueryType.NOT_IN, hsQwPublicRentalHouse.getId());
        }
        long count  = this.findCount(query);
        if (count > 0){
            throw new ServiceException("对应房源信息楼盘号："+ query.getEstateId()
                    + "；楼号：" + query.getBuildingNum()
                    + "；楼层：" + query.getFloor()
                    + "；住房编号：" + query.getHouseNum()
                    + "；单元号：" + query.getUnitNum() + ";已存在，请核查！"
            );
        }
    }

    /**
     * 导入数据
     *
     * @param file 导入的数据文件
     */
    @Transactional
    public String importData(MultipartFile file, String type) {
        if (file == null) {
            throw new ServiceException(text("请选择导入的数据文件！"));
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        try (ExcelImport ei = new ExcelImport(file, 2, 0)) {
            List<HsQwPublicRentalHouse> list = ei.getDataList(HsQwPublicRentalHouse.class);
            for (HsQwPublicRentalHouse hsQwPublicRentalHouse : list) {
                try {
                    //初始化数据
                    this.initDate(hsQwPublicRentalHouse, type);
                    ValidatorUtils.validateWithException(hsQwPublicRentalHouse);
                    this.save(hsQwPublicRentalHouse);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、编号 " + hsQwPublicRentalHouse.getId() + " 导入成功");
                } catch (Exception e) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、编号 " + hsQwPublicRentalHouse.getId() + " 导入失败：";
                    if (e instanceof ConstraintViolationException) {
                        ConstraintViolationException cve = (ConstraintViolationException) e;
                        for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
                            msg += Global.getText(violation.getMessage());
                        }
                    } else {
                        msg += e.getMessage();
                    }
                    failureMsg.append(msg);
                    logger.error(msg, e);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            failureMsg.append(e.getMessage());
            return failureMsg.toString();
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    private void initDate(HsQwPublicRentalHouse hsQwPublicRentalHouse, String type) {
        //查询单位编号是否存在
        if (StringUtils.isNotBlank(hsQwPublicRentalHouse.getOfficeCode())){
            Office  office = this.getOfficeByCode(hsQwPublicRentalHouse.getOfficeCode());
            if (office == null){
                throw new ServiceException("单位编号"+ hsQwPublicRentalHouse.getEstateId()+ "不存在");
            }
        } else {
            throw new ServiceException("单位编号"+ hsQwPublicRentalHouse.getOfficeCode()+ "为空");
        }
        //查询楼盘是否存在
        if (StringUtils.isNotBlank(hsQwPublicRentalHouse.getEstateId())){
            HsQwPublicRentalEstate estate = hsQwPublicRentalEstateService.get(hsQwPublicRentalHouse.getEstateId());
            if (estate == null){
                throw new ServiceException("楼盘编号"+ hsQwPublicRentalHouse.getEstateId()+ "不存在");
            }
        }
        if (StringUtils.isNotBlank(hsQwPublicRentalHouse.getLocation())){
            HsQwPublicRentalEstate query = new HsQwPublicRentalEstate();
            query.setAddress(hsQwPublicRentalHouse.getLocation());
            HsQwPublicRentalEstate estate = hsQwPublicRentalEstateService.findList(query).stream().findFirst().orElse(null);
            if (estate == null){
                throw new ServiceException("楼盘编号"+ hsQwPublicRentalHouse.getEstateId()+ "不存在");
            } else {
                hsQwPublicRentalHouse.setEstateId(estate.getId());
            }
        } else {
            throw new ServiceException("楼盘信息为空");
        }
        hsQwPublicRentalHouse.setIsPublic("0");
        hsQwPublicRentalHouse.setType(type);
        hsQwPublicRentalHouse.setHouseStatus(this.initHouseStatus(type));
    }

    private Office getOfficeByCode(String officeCode) {
        Office query = new Office();
        query.setOfficeCode(officeCode);
        return officeService.findList(query).stream().findFirst().orElse(null);
    }

    private String initHouseStatus(String type) {
        if (type.equals("0") ||
                type.equals("3") ||
                type.equals("4")) {
            return "0";
        }
        return "2";
    }

    /**
     * 更新状态
     *
     * @param hsQwPublicRentalHouse
     */
    @Override
    @Transactional
    public void updateStatus(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        super.updateStatus(hsQwPublicRentalHouse);
    }

    /**
     * 删除数据
     *
     * @param hsQwPublicRentalHouse
     */
    @Override
    @Transactional
    public void delete(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        this.deleteCheckRelateOrder(hsQwPublicRentalHouse);
        super.delete(hsQwPublicRentalHouse);
    }

    /**
     * 根据各种房源相关的资源信息进行判断，某个资源依赖存在正常或者审核中的状态，则房源不允许删除
     * @param hsQwPublicRentalHouse
     */
    private void deleteCheckRelateOrder(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        // 自定义流程操作-遍历所有删除核验操作
        for (HsQwHouseDeleteCheckMethod hsQwHouseDeleteCheckMethod : hsQwHouseDeleteCheckMethods) {
            hsQwHouseDeleteCheckMethod.execute(hsQwPublicRentalHouse);
        }
    }

    public List<Map<String, Object>> findTreeHouse() {

        List<Map<String, Object>> mapList = ListUtils.newArrayList();
        List<HsQwPublicRentalEstate> listEstate = hsQwPublicRentalEstateService.findList(new HsQwPublicRentalEstate());
        List<HsQwPublicRentalHouse> listHouse = this.findList(new HsQwPublicRentalHouse());
        for (int i = 0; i < listHouse.size(); i++) {
            HsQwPublicRentalHouse e = listHouse.get(i);
            // 过滤非正常的数据
            if (!HsQwPublicRentalHouse.STATUS_NORMAL.equals(e.getStatus())) {
                continue;
            }
            Map<String, Object> map = MapUtils.newHashMap();
            map.put("id", e.getId());
            map.put("pId", e.getEstateId());
            String name = e.getBuildingNum();
            map.put("code", e.getHouseNum());
            map.put("name", name);
            map.put("title", e.getLocation());
            // 返回是否是父节点，如果需要加载用户，则全部都是父节点，来加载用户数据
            map.put("isParent", false);
            mapList.add(map);
        }
        return mapList;
    }

    /**
     * 获取可配租的房源,排除已租赁或者正在流程中的房源
     *
     * @param hsQwPublicRentalHouse
     * @return
     */
    public Page<HsQwPublicRentalHouse> findIdleBureauPage(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_BUREAU);
        hsQwPublicRentalHouse.sqlMap().add("extForm",
                " LEFT JOIN (SELECT a.id, a.HOUSE_ID,a.STATUS FROM hs_qw_apply_bureau a WHERE a.STATUS IN (0,4)) hqb ON " +
                        "a.id = hqb.house_id ");
        hsQwPublicRentalHouse.sqlMap().getWhere().andBracket("hqb.status", QueryType.NOT_IN, new String[]{"0", "4"})
                .or("hqb.id", QueryType.IS_NULL, null).endBracket();
        return super.findPage(hsQwPublicRentalHouse);
    }

    public Page<HsQwPublicRentalHouse> findProvincePage(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_PROVINCE);
        hsQwPublicRentalHouse.sqlMap().add("extColumns", " hqa.name as \"province.mainApplyer.name\", " +
                "hqp.status as \"province.status\"," +
                "hqp.id as \"province.id\"," +
                "hqp.auto_check as \"province.autoCheck\"," +
                "hqp.check_date as \"province.checkDate\"");
        hsQwPublicRentalHouse.sqlMap().add("extForm",
                " left join hs_qw_apply_province hqp on a.id = hqp.house_id and hqp.status in ('0','4') " +
                        " left join hs_qw_applyer hqa on hqp.id = hqa.apply_id and hqa.status=0 and hqa.apply_role=0 ");
        return super.findPage(hsQwPublicRentalHouse);
    }

    public Page<HsQwPublicRentalHouse> findIdleProvincePage(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_PROVINCE);
        hsQwPublicRentalHouse.sqlMap().add("extForm",
                " LEFT JOIN (SELECT a.id, a.HOUSE_ID,a.STATUS FROM hs_qw_apply_province a WHERE a.STATUS IN (0,4)) hqb ON " +
                        "a.id = hqb.house_id ");
        hsQwPublicRentalHouse.sqlMap().getWhere().andBracket("hqb.status", QueryType.NOT_IN, new String[]{"0", "4"})
                .or("hqb.id", QueryType.IS_NULL, null).endBracket();
        return this.findPage(hsQwPublicRentalHouse);
    }

    public List<ApiHsHouseTemplate> findHouseTemplateList(String id, HsQwPublicRentalEstate query) {
        return this.dao.findHouseTemplateList(id);
    }

    public List<ApiHsPriceLimitHouseTemplate> findPriceLimitHouseTemplateList(String id, HsQwPublicRentalEstate query) {
        return this.dao.findPriceLimitHouseTemplateList(id);
    }

    public void publicHouse(String houseStr) {
        // houseIds
        String[] houseIds = houseStr.split(",");
        // 拼接房源发布信息
        String noticeContent = this.getPublicHouseInfo(houseIds);
        // todo 调用数字屏山服务接口发布房源信息
        apiSzpsService.submitPublicNotice("0",
                "房源发布信息",
                DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"),
                noticeContent,
                null,
                "住房保障局");
        // 调用数字空间服务接口发布房源信息
        apiSzkjService.uploadNotice(this.genNoticeBody(
                "房源发布信息",
                new Date(),
                noticeContent,
                null));
        // 修改状态
        HsQwPublicRentalHouse entity = new HsQwPublicRentalHouse();
        entity.setIsPublic("1");
        HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
        query.setId_in(houseIds);
        this.dao.updateByEntity(entity, query);
    }

    private String getPublicHouseInfo(String[] houseIds) {
        StringBuilder sb = new StringBuilder();
        HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
        query.setId_in(houseIds);
        List<HsQwPublicRentalHouse> list = this.dao.findList(query);
        if (list != null && list.size() > 0) {
            sb.append("本期新发布房源信息如下：").append("\n");
            for (HsQwPublicRentalHouse item : list) {
                sb.append("    ").append(item.getSimpleInfo()).append("\n");
            }
        }
        return sb.toString();
    }

    private Api2NoticeBody genNoticeBody(String title, Date publicTime, String content, List<String> fileIds) {
        Api2NoticeBody body = new Api2NoticeBody();
        body.setMsgId(UUID.randomUUID().toString());
        body.setMsgType("1");		// 公告
        body.setMsgSource("住房保障房源管理");
        body.setTopic(title);
        body.setContent(content);
        body.setFileIds(fileIds.stream().collect(Collectors.joining(",")));
        body.setPublishUnit(EmpUtils.getOffice().getOfficeName());
        body.setPublishUnitId(EmpUtils.getOffice().getOfficeCode());
        body.setPublishTime(publicTime);
        return body;
    }

    public List<HsQwPublicRentalHouse> findListHouse(String houseStr) {

        String[] houseIds = houseStr.split(",");
        HsQwPublicRentalHouse query = new HsQwPublicRentalHouse();
        query.setId_in(houseIds);
        return this.dao.findList(query);
    }

    public Page<HsQwPublicRentalHouse> findSelectData(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
        // 自定义流程操作
        HsQwHouseSelectList houseSelectListMethod = hsQwHouseSelectListMap.get(hsQwPublicRentalHouse.getDataType());
        if (houseSelectListMethod == null) {
            throw new ServiceException("获取房源列表失败，数据类型：" + hsQwPublicRentalHouse.getDataType());
        }
        return houseSelectListMethod.execute(hsQwPublicRentalHouse, this);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 已申请单稽核
        Map<String, HsQwHouseSelectList> beanMap = applicationContext.getBeansOfType(HsQwHouseSelectList.class);
        beanMap.forEach((k, v) -> hsQwHouseSelectListMap.put(v.getDataType(), v));
        // 删除房源核验
        Map<String, HsQwHouseDeleteCheckMethod> listMap = applicationContext.getBeansOfType(HsQwHouseDeleteCheckMethod.class);
        listMap.forEach((k, v) -> hsQwHouseDeleteCheckMethods.add(v));
    }

    public void updateHouseStatus(String id) {
        HsQwPublicRentalHouse house = this.get(id);
        house.setStatus(HsQwPublicRentalHouse.STATUS_NORMAL);
        house.setHouseStatus("0");
        this.update(house);
        this.updateStatus(house);
    }
}