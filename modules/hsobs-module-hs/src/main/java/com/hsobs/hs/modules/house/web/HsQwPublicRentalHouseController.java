package com.hsobs.hs.modules.house.web;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouseIdel;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.collect.MapUtils;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Office;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.jeesite.common.config.Global;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import org.springframework.web.multipart.MultipartFile;
import com.jeesite.common.web.BaseController;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;

/**
 * 租赁公租房房源房源信息表Controller
 * <AUTHOR>
 * @version 2024-11-20
 */
@Controller
@RequestMapping(value = "${adminPath}/house/hsQwPublicRentalHouse")
public class HsQwPublicRentalHouseController extends BaseController {

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;
	
	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwPublicRentalHouse get(String id, boolean isNewRecord) {
		return hsQwPublicRentalHouseService.get(id, isNewRecord);
	}
	
	/**
	 * 查询列表
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwPublicRentalHouse hsQwPublicRentalHouse, Model model) {
		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
		return "modules/house/hsQwPublicRentalHouseList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwPublicRentalHouse> listData(HsQwPublicRentalHouse hsQwPublicRentalHouse, HttpServletRequest request, HttpServletResponse response) {
		hsQwPublicRentalHouse.setPage(new Page<>(request, response));
		// todo 控制数据权限
		hsQwPublicRentalHouseService.addDataScopeFilter(hsQwPublicRentalHouse);
		Page<HsQwPublicRentalHouse> page = hsQwPublicRentalHouseService.findPage(hsQwPublicRentalHouse);
		return page;
	}


	/**
	 * 查询列表数据-查询可配租状态的房源
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "listApplyData")
	@ResponseBody
	public Page<HsQwPublicRentalHouse> listApplyData(HsQwPublicRentalHouse hsQwPublicRentalHouse, HttpServletRequest request, HttpServletResponse response) {
		hsQwPublicRentalHouse.setPage(new Page<>(request, response));
		Page<HsQwPublicRentalHouse> page = hsQwPublicRentalHouseService.findApplyPage(hsQwPublicRentalHouse);
		return page;
	}

	/**
	 * 查询列表数据-查询可配租状态的房源
	 * dataType:1-绿色租赁
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "listSelectData")
	@ResponseBody
	public Page<HsQwPublicRentalHouse> listSelectData(HsQwPublicRentalHouse hsQwPublicRentalHouse, HttpServletRequest request, HttpServletResponse response) {
		hsQwPublicRentalHouse.setPage(new Page<>(request, response));
		Page<HsQwPublicRentalHouse> page = hsQwPublicRentalHouseService.findSelectData(hsQwPublicRentalHouse);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "form")
	public String form(HsQwPublicRentalHouse hsQwPublicRentalHouse, Model model) {
		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
		return "modules/house/hsQwPublicRentalHouseForm";
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwPublicRentalHouse hsQwPublicRentalHouse) {
		hsQwPublicRentalHouseService.save(hsQwPublicRentalHouse);
		return renderResult(Global.TRUE, text("保存房源信息表成功！"));
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwPublicRentalHouse hsQwPublicRentalHouse, HttpServletResponse response) {
		List<HsQwPublicRentalHouse> list = hsQwPublicRentalHouseService.findList(hsQwPublicRentalHouse);
		String fileName = "房源信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("房源信息表", HsQwPublicRentalHouse.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导出数据
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "exportData2")
	public void exportData2(String houseStr, HttpServletResponse response) {
		List<HsQwPublicRentalHouse> list = hsQwPublicRentalHouseService.findListHouse(houseStr);
		String fileName = "房源信息表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("房源信息表", HsQwPublicRentalHouse.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		HsQwPublicRentalHouse hsQwPublicRentalHouse = new HsQwPublicRentalHouse();
		List<HsQwPublicRentalHouse> list = ListUtils.newArrayList(hsQwPublicRentalHouse);
		String fileName = "房源信息表模板.xlsx";
		try(ExcelExport ee = new ExcelExport("房源信息表", HsQwPublicRentalHouse.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file, String type) {
		try {
			String message = hsQwPublicRentalHouseService.importData(file,type);
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}

	/**
	 * 下载模板-空闲房源
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "importTemplateIdle")
	public void importTemplateIdle(HttpServletResponse response) {
		HsQwPublicRentalHouseIdel hsQwPublicRentalHouseIdel = new HsQwPublicRentalHouseIdel();
		List<HsQwPublicRentalHouseIdel> list = ListUtils.newArrayList(hsQwPublicRentalHouseIdel);
		String fileName = "空闲房源信息模板.xlsx";
		try(ExcelExport ee = new ExcelExport("空闲房源信息", HsQwPublicRentalHouseIdel.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 删除数据
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@RequestMapping(value = "delete")
	@ResponseBody
	public String delete(HsQwPublicRentalHouse hsQwPublicRentalHouse) {
		hsQwPublicRentalHouseService.delete(hsQwPublicRentalHouse);
		return renderResult(Global.TRUE, text("删除房源信息表成功！"));
	}


	/**
	 * 获取房源树结构数据
	 * @return
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@RequestMapping(value = "treeData")
	@ResponseBody
	public List<Map<String, Object>> treeData() {
		return hsQwPublicRentalHouseService.findTreeHouse();
	}

	/**
	 * 选择员工对话框
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@RequestMapping(value = "houseSelect")
	public String houseSelect(HsQwPublicRentalHouse hsQwPublicRentalHouse, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
		return "modules/house/hsQwPublicRentalHouseListSelect";
	}

	/**
	 * 选择房源-根据dataType类型
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@RequestMapping(value = "houseSelectByType")
	public String houseSelectByType(HsQwPublicRentalHouse hsQwPublicRentalHouse, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
		return "modules/house/hsQwPublicRentalHouseListSelectByType";
	}



	/**
	 * 选择员工对话框
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@RequestMapping(value = "housePlacementSelect")
	public String housePlacementSelect(HsQwPublicRentalHouse hsQwPublicRentalHouse, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
		return "modules/house/hsQwPublicRentalPlacementHouseListSelect";
	}

	/**
	 * 查询列表数据-查询配售房房源
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "listPlacementHouseData")
	@ResponseBody
	public Page<HsQwPublicRentalHouse> listPlacementHouseData(HsQwPublicRentalHouse hsQwPublicRentalHouse, HttpServletRequest request, HttpServletResponse response) {
		hsQwPublicRentalHouse.setPage(new Page<>(request, response));
		Page<HsQwPublicRentalHouse> page = hsQwPublicRentalHouseService.findPlacementHousePage(hsQwPublicRentalHouse);
		return page;
	}

	/**
	 * 一键发布
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "public")
	@ResponseBody
	public String publicHouse(String houseStr) {
		hsQwPublicRentalHouseService.publicHouse(houseStr);
		return renderResult(Global.TRUE, text("房源发布成功！"));
	}
}