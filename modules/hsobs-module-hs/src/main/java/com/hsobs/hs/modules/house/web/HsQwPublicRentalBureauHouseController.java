package com.hsobs.hs.modules.house.web;

import com.alibaba.fastjson.JSONValidator;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.hsobs.hs.modules.house.service.HsQwPublicRentalHouseService;
import com.jeesite.common.codec.EncodeUtils;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.entity.Page;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.utils.excel.ExcelExport;
import com.jeesite.common.utils.excel.annotation.ExcelField.Type;
import com.jeesite.common.web.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 局直自管公房管理Controller
 * <AUTHOR>
 * @version 2024-11-20
 */
@Controller
@RequestMapping(value = "${adminPath}/house/hsQwPublicRentalBureauHouse")
public class HsQwPublicRentalBureauHouseController extends BaseController {

	@Autowired
	private HsQwPublicRentalHouseService hsQwPublicRentalHouseService;

	/**
	 * 获取数据
	 */
	@ModelAttribute
	public HsQwPublicRentalHouse get(String id, boolean isNewRecord) {
		return hsQwPublicRentalHouseService.get(id, isNewRecord);
	}

	/**
	 * 保存数据
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@PostMapping(value = "save")
	@ResponseBody
	public String save(@Validated HsQwPublicRentalHouse hsQwPublicRentalHouse) {
		hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_BUREAU);
		hsQwPublicRentalHouse.setIsPublic("1");
		hsQwPublicRentalHouseService.save(hsQwPublicRentalHouse);
		return renderResult(Global.TRUE, text("保存局直自管公房成功！"));
	}

	/**
	 * 查询列表
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = {"list", ""})
	public String list(HsQwPublicRentalHouse hsQwPublicRentalHouse, Model model) {
		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
		return "modules/house/bureaupublichouse/hsQwPublicRentalBureauHouseList";
	}
	
	/**
	 * 查询列表数据
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "listData")
	@ResponseBody
	public Page<HsQwPublicRentalHouse> listData(HsQwPublicRentalHouse hsQwPublicRentalHouse, HttpServletRequest request, HttpServletResponse response) {
		hsQwPublicRentalHouse.setPage(new Page<>(request, response));
		Page<HsQwPublicRentalHouse> page = hsQwPublicRentalHouseService.findBureauPage(hsQwPublicRentalHouse);
		return page;
	}

	/**
	 * 查询列表数据-闲置房源
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "listIdleData")
	@ResponseBody
	public Page<HsQwPublicRentalHouse> listIdleData(HsQwPublicRentalHouse hsQwPublicRentalHouse, HttpServletRequest request, HttpServletResponse response) {
		hsQwPublicRentalHouse.setPage(new Page<>(request, response));
		Page<HsQwPublicRentalHouse> page = hsQwPublicRentalHouseService.findIdleBureauPage(hsQwPublicRentalHouse);
		return page;
	}

	/**
	 * 查看编辑表单
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "form")
	public String form(HsQwPublicRentalHouse hsQwPublicRentalHouse, Model model) {
		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
		return "modules/house/bureaupublichouse/hsQwPublicRentalBureauHouseForm";
	}
	
	/**
	 * 导出数据
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "exportData")
	public void exportData(HsQwPublicRentalHouse hsQwPublicRentalHouse, HttpServletResponse response) {
		hsQwPublicRentalHouse.setType(HsQwPublicRentalHouse.HOUSE_TYPE_BUREAU);
		List<HsQwPublicRentalHouse> list = hsQwPublicRentalHouseService.findList(hsQwPublicRentalHouse);
		String fileName = "局直自管公房" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
		try(ExcelExport ee = new ExcelExport("局直自管公房", HsQwPublicRentalHouse.class)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 下载模板
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:view")
	@RequestMapping(value = "importTemplate")
	public void importTemplate(HttpServletResponse response) {
		HsQwPublicRentalHouse hsQwPublicRentalHouse = new HsQwPublicRentalHouse();
		List<HsQwPublicRentalHouse> list = ListUtils.newArrayList(hsQwPublicRentalHouse);
		String fileName = "局直自管公房模板.xlsx";
		try(ExcelExport ee = new ExcelExport("局直自管公房", HsQwPublicRentalHouse.class, Type.IMPORT)){
			ee.setDataList(list).write(response, fileName);
		}
	}

	/**
	 * 导入数据
	 */
	@ResponseBody
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@PostMapping(value = "importData")
	public String importData(MultipartFile file) {
		try {
			String message = hsQwPublicRentalHouseService.importData(file, "3");
			return renderResult(Global.TRUE, "posfull:"+message);
		} catch (Exception ex) {
			return renderResult(Global.FALSE, "posfull:"+ex.getMessage());
		}
	}


	/**
	 * 选择员工对话框
	 */
	@RequiresPermissions("house:hsQwPublicRentalHouse:edit")
	@RequestMapping(value = "houseSelect")
	public String houseSelect(HsQwPublicRentalHouse hsQwPublicRentalHouse, String selectData, Model model) {
		String selectDataJson = EncodeUtils.decodeUrl(selectData);
		if (selectDataJson != null && JSONValidator.from(selectDataJson).validate()){
			model.addAttribute("selectData", selectDataJson);
		}
		model.addAttribute("hsQwPublicRentalHouse", hsQwPublicRentalHouse);
		return "modules/house/hsQwPublicRentalHouseListSelectBureau";
	}

}