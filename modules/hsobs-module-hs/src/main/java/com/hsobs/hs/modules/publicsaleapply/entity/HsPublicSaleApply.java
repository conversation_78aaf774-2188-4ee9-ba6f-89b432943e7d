package com.hsobs.hs.modules.publicsaleapply.entity;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import com.hsobs.hs.modules.publicapplyer.entity.HsPublicApplyer;
import com.hsobs.hs.modules.publicsaleestate.entity.HsPublicSaleEstate;
import com.jeesite.common.collect.ListUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.JoinTable;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;
import com.jeesite.common.utils.excel.annotation.ExcelField;
import com.jeesite.common.utils.excel.annotation.ExcelFields;
import com.jeesite.modules.bpm.entity.BpmEntity;
import com.jeesite.modules.sys.entity.Office;

import java.util.List;

/**
 * 公有住房配售申请Entity
 * <AUTHOR>
 * @version 2025-02-12
 */
@Table(name="hs_public_sale_apply", alias="a", label="公有住房配售申请信息", columns={
		@Column(name="id", attrName="id", label="编号", isPK=true, queryType=QueryType.LIKE),
		@Column(includeEntity=DataEntity.class),
		@Column(name="office_code", attrName="officeCode", label="单位名称"),
		@Column(name="property_unit", attrName="propertyUnit", label="产权单位", queryType=QueryType.LIKE),
		@Column(name="property_phone", attrName="propertyPhone", label="联系方式", queryType=QueryType.LIKE),
		@Column(name="title", attrName="title", label="配售主题", queryType=QueryType.LIKE),
		@Column(name="result", attrName="result", label="审批结果", queryType=QueryType.LIKE),
	}, joinTable = {
		@JoinTable(type = JoinTable.Type.LEFT_JOIN, entity = Office.class, alias = "oi",
				on="oi.office_code = a.office_code", attrName="office",
				columns = {@Column(includeEntity = Office.class)})
	},
		extWhereKeys="dsf",
		orderBy="a.update_date DESC"
)
public class HsPublicSaleApply extends BpmEntity<HsPublicSaleApply> {

	public static final String APPLY_STATUS_DEFAULT = "-1"; //默认、删除的申请单
	public static final String APPLY_STATUS_DRAFT = "配售申请";//0
	public static final String APPLY_STATUS_AUDIT_ORGHAND = "经办审核";//1

	@ExcelFields({
			@ExcelField(title = "申请编号", attrName = "id", align = ExcelField.Align.LEFT, sort = 20),
			@ExcelField(title = "配售主题", attrName = "title", align = ExcelField.Align.LEFT, sort = 30),
			@ExcelField(title = "申请单位", attrName = "office.fullName", align = ExcelField.Align.LEFT, sort = 40),
			@ExcelField(title = "产权单位", attrName = "propertyUnit", align = ExcelField.Align.LEFT, sort = 50),
			@ExcelField(title = "申请理由", attrName = "remarks", align = ExcelField.Align.LEFT, sort = 60),
			@ExcelField(title = "申请时间", attrName = "createDate", align = ExcelField.Align.LEFT, sort = 70, dataFormat = "yyyy-MM-dd"),
	})

	private static final long serialVersionUID = 1L;
	private String officeCode;		// office_code
	private String title;			// 配售主题
	private String propertyUnit;		// 产权单位
	private String propertyPhone;	// 联系方式
	private String result;
	private Integer applyStatus;		// 申请状态
	private Office office;
	private String applyTitle;
	private String flowStatus;		// 申请状态(字符串)

	private List<HsPublicSaleEstate> hsPublicSaleEstateList = ListUtils.newArrayList();		// 子表列表

	private String readOnly;

	public HsPublicSaleApply() {
		this(null);
		readOnly = "false";
	}
	
	public HsPublicSaleApply(String id){
		super(id);
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Size(min=0, max=128, message="产权单位长度不能超过 128 个字符")
	public String getPropertyUnit() {
		return propertyUnit;
	}

	public void setPropertyUnit(String propertyUnit) {
		this.propertyUnit = propertyUnit;
	}

	public String getPropertyPhone() {
		return propertyPhone;
	}

	public void setPropertyPhone(String propertyPhone) {
		this.propertyPhone = propertyPhone;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}

	public Office getOffice() {
		return office;
	}

	public void setOffice(Office office) {
		this.office = office;
	}

	public String getApplyTitle() {
        return this.office.getFullName() + " 于" + DateUtils.formatDateTime(this.getCreateDate()) + " 发起了公有住房配售申请";
    }
    public void setApplyTitle(String applyTitle) {
        this.applyTitle = applyTitle;
    }

	public String getFlowStatus() {
		return this.flowStatus;
	}

	public void setFlowStatus(String flowStatus) {
		this.flowStatus = flowStatus;
	}

	public String getReadOnly() {
		return this.readOnly;
	}

	public void setReadOnly(String readOnly) {
		this.readOnly = readOnly;
	}

	@Valid
	public List<HsPublicSaleEstate> getHsPublicSaleEstateList() {
		return hsPublicSaleEstateList;
	}

	public void setHsPublicSaleEstateList(List<HsPublicSaleEstate> hsPublicSaleEstateList) {
		this.hsPublicSaleEstateList = hsPublicSaleEstateList;
	}
}