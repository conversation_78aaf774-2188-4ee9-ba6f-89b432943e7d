package com.hsobs.hs.modules.formmanage.entity;

import javax.validation.constraints.Size;

import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 数据表单填写详情记录表Entity
 * <AUTHOR>
 * @version 2025-02-21
 */
@Table(name="hs_data_form_fill_record", alias="a", label="数据表单填写详情记录表信息", columns={
		@Column(name="id", attrName="id", label="主识别ID", isPK=true),
		@Column(name="fill_id", attrName="fillId", label="填写记录ID"),
		@Column(name="template_id", attrName="templateId", label="表单模板ID"),
		@Column(name="field_id", attrName="fieldId", label="字段ID"),
		@Column(name="field_type", attrName="fieldType", label="字段类型"),
		@Column(name="field_name", attrName="fieldName", label="字段名称", queryType=QueryType.LIKE),
		@Column(name="field_key", attrName="fieldKey", label="字段标识"),
		@Column(name="field_value", attrName="fieldValue", label="字段填写值"),
		@Column(name="status", attrName="status", label="状态", isUpdate=false),
		@Column(name="create_by", attrName="createBy", label="创建人", isUpdate=false, isQuery=false),
		@Column(name="create_date", attrName="createDate", label="创建时间", isUpdate=false, isQuery=false, isUpdateForce=true),
		@Column(name="update_by", attrName="updateBy", label="更新人", isQuery=false),
		@Column(name="update_date", attrName="updateDate", label="更新时间", isQuery=false, isUpdateForce=true),
		@Column(name="valid_tag", attrName="validTag", label="是否有效;1-有效 0-无效"),
	}, orderBy="a.update_date DESC"
)
public class HsDataFormFillRecord extends DataEntity<HsDataFormFillRecord> {
	
	private static final long serialVersionUID = 1L;
	private String fillId;		// 填写记录ID
	private String templateId;		// 表单模板ID
	private String fieldId;		// 字段ID
	private String fieldType;		// 字段类型
	private String fieldName;		// 字段名称
	private String fieldKey;		// 字段标识
	private String fieldValue;		// 字段填写值
	private String validTag;		// 是否有效;1-有效 0-无效

	private HsDataFormTemplateField templateField;

	public HsDataFormFillRecord() {
		this(null);
	}
	
	public HsDataFormFillRecord(String id){
		super(id);
	}
	
	@Size(min=0, max=64, message="填写记录ID长度不能超过 64 个字符")
	public String getFillId() {
		return fillId;
	}

	public void setFillId(String fillId) {
		this.fillId = fillId;
	}
	
	@Size(min=0, max=64, message="表单模板ID长度不能超过 64 个字符")
	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	
	@Size(min=0, max=32, message="字段ID长度不能超过 32 个字符")
	public String getFieldId() {
		return fieldId;
	}

	public void setFieldId(String fieldId) {
		this.fieldId = fieldId;
	}
	
	@Size(min=0, max=32, message="字段类型长度不能超过 32 个字符")
	public String getFieldType() {
		return fieldType;
	}

	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	
	@Size(min=0, max=255, message="字段名称长度不能超过 255 个字符")
	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	
	@Size(min=0, max=255, message="字段标识长度不能超过 255 个字符")
	public String getFieldKey() {
		return fieldKey;
	}

	public void setFieldKey(String fieldKey) {
		this.fieldKey = fieldKey;
	}
	
	@Size(min=0, max=800, message="字段填写值长度不能超过 800 个字符")
	public String getFieldValue() {
		return fieldValue;
	}

	public void setFieldValue(String fieldValue) {
		this.fieldValue = fieldValue;
	}
	
	@Size(min=0, max=1, message="是否有效;1-有效 0-无效长度不能超过 1 个字符")
	public String getValidTag() {
		return validTag;
	}

	public void setValidTag(String validTag) {
		this.validTag = validTag;
	}

	public HsDataFormTemplateField getTemplateField() {
		return templateField;
	}

	public void setTemplateField(HsDataFormTemplateField templateField) {
		this.templateField = templateField;
	}
}