package com.hsobs.hs.modules.hsqwapplybureaucheck.service;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jeesite.common.entity.Page;
import com.jeesite.common.service.CrudService;
import com.hsobs.hs.modules.hsqwapplybureaucheck.entity.HsQwApplyBureauCheck;
import com.hsobs.hs.modules.hsqwapplybureaucheck.dao.HsQwApplyBureauCheckDao;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.config.Global;
import com.jeesite.common.validator.ValidatorUtils;
import com.jeesite.common.utils.excel.ExcelImport;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

/**
 * 公房房源智能核验Service
 * <AUTHOR>
 * @version 2025-05-03
 */
@Service
public class HsQwApplyBureauCheckService extends CrudService<HsQwApplyBureauCheckDao, HsQwApplyBureauCheck> {
	
	/**
	 * 获取单条数据
	 * @param hsQwApplyBureauCheck
	 * @return
	 */
	@Override
	public HsQwApplyBureauCheck get(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		return super.get(hsQwApplyBureauCheck);
	}
	
	/**
	 * 查询分页数据
	 * @param hsQwApplyBureauCheck 查询条件
	 * @param hsQwApplyBureauCheck page 分页对象
	 * @return
	 */
	@Override
	public Page<HsQwApplyBureauCheck> findPage(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		hsQwApplyBureauCheck.getActualClearanceDate();
		return super.findPage(hsQwApplyBureauCheck);
	}
	
	/**
	 * 查询列表数据
	 * @param hsQwApplyBureauCheck
	 * @return
	 */
	@Override
	public List<HsQwApplyBureauCheck> findList(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		return super.findList(hsQwApplyBureauCheck);
	}
	
	/**
	 * 保存数据（插入或更新）
	 * @param hsQwApplyBureauCheck
	 */
	@Override
	@Transactional
	public void save(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		super.save(hsQwApplyBureauCheck);
	}

	/**
	 * 导入数据
	 * @param file 导入的数据文件
	 */
	@Transactional
	public String importData(MultipartFile file) {
		if (file == null){
			throw new ServiceException(text("请选择导入的数据文件！"));
		}
		int successNum = 0; int failureNum = 0;
		StringBuilder successMsg = new StringBuilder();
		StringBuilder failureMsg = new StringBuilder();
		try(ExcelImport ei = new ExcelImport(file, 2, 0)){
			List<HsQwApplyBureauCheck> list = ei.getDataList(HsQwApplyBureauCheck.class);
			for (HsQwApplyBureauCheck hsQwApplyBureauCheck : list) {
				try{
					ValidatorUtils.validateWithException(hsQwApplyBureauCheck);
					this.save(hsQwApplyBureauCheck);
					successNum++;
					successMsg.append("<br/>" + successNum + "、编号 " + hsQwApplyBureauCheck.getId() + " 导入成功");
				} catch (Exception e) {
					failureNum++;
					String msg = "<br/>" + failureNum + "、编号 " + hsQwApplyBureauCheck.getId() + " 导入失败：";
					if (e instanceof ConstraintViolationException){
						ConstraintViolationException cve = (ConstraintViolationException)e;
						for (ConstraintViolation<?> violation : cve.getConstraintViolations()) {
							msg += Global.getText(violation.getMessage()) + " ("+violation.getPropertyPath()+")";
						}
					}else{
						msg += e.getMessage();
					}
					failureMsg.append(msg);
					logger.error(msg, e);
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			failureMsg.append(e.getMessage());
			return failureMsg.toString();
		}
		if (failureNum > 0) {
			failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
			throw new ServiceException(failureMsg.toString());
		}else{
			successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
		}
		return successMsg.toString();
	}
	
	/**
	 * 更新状态
	 * @param hsQwApplyBureauCheck
	 */
	@Override
	@Transactional
	public void updateStatus(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		super.updateStatus(hsQwApplyBureauCheck);
	}
	
	/**
	 * 删除数据
	 * @param hsQwApplyBureauCheck
	 */
	@Override
	@Transactional
	public void delete(HsQwApplyBureauCheck hsQwApplyBureauCheck) {
		super.delete(hsQwApplyBureauCheck);
	}
	
}