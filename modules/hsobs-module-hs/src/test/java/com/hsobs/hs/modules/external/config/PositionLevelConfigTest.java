package com.hsobs.hs.modules.external.config;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * 职级配置测试类
 * 
 * <AUTHOR>
 * @version 2024-06-18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PositionLevelConfigTest {

    @Autowired
    private PositionLevelConfig positionLevelConfig;

    @Test
    public void testPositionLevelConfig() {
        assertNotNull("PositionLevelConfig should not be null", positionLevelConfig);
        assertNotNull("Position levels should not be null", positionLevelConfig.getLevels());
        assertEquals("Should have 9 position levels", 9, positionLevelConfig.getLevels().size());
        
        // 验证各个职级
        String[] expectedCodes = {"1", "2", "3", "4", "5", "6", "7", "8", "9"};
        String[] expectedNames = {
            "正厅级", "副厅级", "正高职称", "正处级", "副处级", 
            "副高职称", "科级", "中级职称", "一般干部职工"
        };
        
        for (int i = 0; i < expectedCodes.length; i++) {
            PositionLevelConfig.PositionLevel level = positionLevelConfig.getLevels().get(i);
            assertEquals("Position level " + (i+1) + " code should be '" + expectedCodes[i] + "'", 
                        expectedCodes[i], level.getCode());
            assertEquals("Position level " + (i+1) + " name should be '" + expectedNames[i] + "'", 
                        expectedNames[i], level.getName());
        }
        
        // 打印配置信息
        System.out.println("Position Levels Configuration:");
        for (PositionLevelConfig.PositionLevel level : positionLevelConfig.getLevels()) {
            System.out.println("Code: " + level.getCode() + ", Name: " + level.getName());
        }
    }
}
