package com.hsobs.hs.modules.external.service;

import com.hsobs.hs.modules.external.entity.ApiHsUser;
import com.hsobs.hs.modules.external.entity.ApiHsPosition;
import com.jeesite.common.entity.Page;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * HsQwExternalService 测试类
 * 
 * <AUTHOR>
 * @version 2024-06-18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HsQwExternalServiceTest {

    @Autowired
    private HsQwExternalService hsQwExternalService;

    @Test
    public void testUserType() {
        // 创建请求参数
        ApiHsUser apiHsUser = new ApiHsUser();
        
        // 调用服务方法
        Page<ApiHsUser> result = hsQwExternalService.userType(apiHsUser);
        
        // 验证结果
        assertNotNull("Result should not be null", result);
        assertNotNull("Result list should not be null", result.getList());
        assertEquals("Should have 3 user types", 3, result.getList().size());
        assertEquals("Count should be 3", 3, result.getCount());
        
        // 验证第一个类型
        ApiHsUser firstType = result.getList().get(0);
        assertEquals("First type code should be '1'", "1", firstType.getCode());
        assertEquals("First type name should be '在编在职'", "在编在职", firstType.getName());
        
        // 验证第二个类型
        ApiHsUser secondType = result.getList().get(1);
        assertEquals("Second type code should be '2'", "2", secondType.getCode());
        assertEquals("Second type name should be '离退休'", "离退休", secondType.getName());
        
        // 验证第三个类型
        ApiHsUser thirdType = result.getList().get(2);
        assertEquals("Third type code should be '3'", "3", thirdType.getCode());
        assertEquals("Third type name should be '编外合同制干部职工'", "编外合同制干部职工", thirdType.getName());
        
        // 打印结果
        System.out.println("User Types API Response:");
        for (ApiHsUser userType : result.getList()) {
            System.out.println("Code: " + userType.getCode() + ", Name: " + userType.getName());
        }
    }

    @Test
    public void testPosition() {
        // 创建请求参数
        ApiHsPosition apiHsPosition = new ApiHsPosition();

        // 调用服务方法
        Page<ApiHsPosition> result = hsQwExternalService.position(apiHsPosition);

        // 验证结果
        assertNotNull("Result should not be null", result);
        assertNotNull("Result list should not be null", result.getList());
        assertEquals("Should have 9 position levels", 9, result.getList().size());
        assertEquals("Count should be 9", 9, result.getCount());

        // 验证各个职级
        String[] expectedCodes = {"1", "2", "3", "4", "5", "6", "7", "8", "9"};
        String[] expectedNames = {
            "正厅级", "副厅级", "正高职称", "正处级", "副处级",
            "副高职称", "科级", "中级职称", "一般干部职工"
        };

        for (int i = 0; i < expectedCodes.length; i++) {
            ApiHsPosition position = result.getList().get(i);
            assertEquals("Position " + (i+1) + " code should be '" + expectedCodes[i] + "'",
                        expectedCodes[i], position.getCode());
            assertEquals("Position " + (i+1) + " name should be '" + expectedNames[i] + "'",
                        expectedNames[i], position.getName());
        }

        // 打印结果
        System.out.println("Position Levels API Response:");
        for (ApiHsPosition position : result.getList()) {
            System.out.println("Code: " + position.getCode() + ", Name: " + position.getName());
        }
    }
}
