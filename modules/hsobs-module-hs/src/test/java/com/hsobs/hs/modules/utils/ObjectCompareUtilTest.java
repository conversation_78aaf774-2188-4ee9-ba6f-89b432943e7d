package com.hsobs.hs.modules.utils;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.applyhouse.entity.HsQwApplyHouse;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ObjectCompareUtilTest {

    @Test
    public void testCompareObjects() {
        // 创建第一个对象
        HsQwApply apply1 = createTestApply("1");
        // 创建第二个对象，并修改一些值
        HsQwApply apply2 = createTestApply("1");
        apply2.setApplyTitle("测试申请2");
        apply2.getMainApplyer().setName("张三2");
        apply2.getHsQwApplyerList().get(0).setName("李四2");
        apply2.getHsQwApplyHouseList().get(0).setFloorArea("100");

        // 修改文件数据
        Map<String, Object> dataMap1 = new HashMap<>();
        dataMap1.put("marry", "file1.jpg"); // 结婚证
        dataMap1.put("house", "file2.jpg"); // 房产证
        apply1.setDataMap(dataMap1);

        Map<String, Object> dataMap2 = new HashMap<>();
        dataMap2.put("marry", "file1.jpg"); // 结婚证未变
        dataMap2.put("house__del", "file2.jpg"); // 房产证被删除
        dataMap2.put("salary", "file3.jpg"); // 新增工资证明
        apply2.setDataMap(dataMap2);

        // 比较两个对象
        Map<String, String> differences = ObjectCompareUtil.compareObjects(apply1, apply2, "HsQwApply");

        // 打印差异
        System.out.println("=== 对象比较结果 ===");
        differences.forEach((key, value) -> {
            // 跳过日期类型的比较
            if (key.contains("workTime") || key.contains("createDate") || key.contains("updateDate")) {
                return;
            }

            System.out.println("字段: " + key);
            System.out.println("差异: " + value);
            System.out.println("-------------------");
        });

        // 单独打印文件相关的差异
        System.out.println("=== 文件变更 ===");
        if (apply1.getDataMap() != null && apply2.getDataMap() != null) {
            // 检查删除的文件
            apply1.getDataMap().forEach((key, value) -> {
                if (!apply2.getDataMap().containsKey(key) && !apply2.getDataMap().containsKey(key + "__del")) {
                    System.out.println("删除文件: " + key + " -> " + value);
                }
            });

            // 检查新增的文件
            apply2.getDataMap().forEach((key, value) -> {
                if (!key.endsWith("__del") && !apply1.getDataMap().containsKey(key)) {
                    System.out.println("新增文件: " + key + " -> " + value);
                }
            });

            // 检查标记删除的文件
            apply2.getDataMap().forEach((key, value) -> {
                if (key.endsWith("__del")) {
                    String originalKey = key.substring(0, key.length() - 5);
                    System.out.println("标记删除文件: " + originalKey + " -> " + value);
                }
            });
        }
    }

    private HsQwApply createTestApply(String id) {
        HsQwApply apply = new HsQwApply();
        apply.setId(id);
        apply.setApplyTitle("测试申请");
        apply.setApplyScore(85.5);
        apply.setStatus("0");
        apply.setCreateDate(new Date());
        apply.setUpdateDate(new Date());

        // 设置主申请人
        HsQwApplyer mainApplyer = new HsQwApplyer();
        mainApplyer.setId("applyer_" + id);
        mainApplyer.setName("张三");
        mainApplyer.setIdNum("110101199001011234");
        mainApplyer.setPhone("13800138000");
        mainApplyer.setWorkTime(new Date());
        mainApplyer.setApplyRole("0");
        mainApplyer.setStatus("0");
        apply.setMainApplyer(mainApplyer);

        // 设置申请人列表
        List<HsQwApplyer> applyerList = new ArrayList<>();
        HsQwApplyer applyer = new HsQwApplyer();
        applyer.setId("applyer2_" + id);
        applyer.setName("李四");
        applyer.setIdNum("110101199001011235");
        applyer.setPhone("13800138001");
        applyer.setWorkTime(new Date());
        applyer.setApplyRole("1");
        applyer.setStatus("0");
        applyerList.add(applyer);
        apply.setHsQwApplyerList(applyerList);

        // 设置房屋列表
        List<HsQwApplyHouse> houseList = new ArrayList<>();
        HsQwApplyHouse house = new HsQwApplyHouse();
        house.setId("house_" + id);
        house.setFloorArea("80");
        house.setAddress("测试地址");
        house.setStatus("0");
        houseList.add(house);
        apply.setHsQwApplyHouseList(houseList);

        return apply;
    }
}