package com.hsobs.hs.modules.external.config;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * 人员类型配置测试类
 * 
 * <AUTHOR>
 * @version 2024-06-18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PersonnelTypeConfigTest {

    @Autowired
    private PersonnelTypeConfig personnelTypeConfig;

    @Test
    public void testPersonnelTypeConfig() {
        assertNotNull("PersonnelTypeConfig should not be null", personnelTypeConfig);
        assertNotNull("Personnel types should not be null", personnelTypeConfig.getTypes());
        assertEquals("Should have 3 personnel types", 3, personnelTypeConfig.getTypes().size());
        
        // 验证第一个类型
        PersonnelTypeConfig.PersonnelType firstType = personnelTypeConfig.getTypes().get(0);
        assertEquals("First type code should be '1'", "1", firstType.getCode());
        assertEquals("First type name should be '在编在职'", "在编在职", firstType.getName());
        
        // 验证第二个类型
        PersonnelTypeConfig.PersonnelType secondType = personnelTypeConfig.getTypes().get(1);
        assertEquals("Second type code should be '2'", "2", secondType.getCode());
        assertEquals("Second type name should be '离退休'", "离退休", secondType.getName());
        
        // 验证第三个类型
        PersonnelTypeConfig.PersonnelType thirdType = personnelTypeConfig.getTypes().get(2);
        assertEquals("Third type code should be '3'", "3", thirdType.getCode());
        assertEquals("Third type name should be '编外合同制干部职工'", "编外合同制干部职工", thirdType.getName());
        
        // 打印配置信息
        System.out.println("Personnel Types Configuration:");
        for (PersonnelTypeConfig.PersonnelType type : personnelTypeConfig.getTypes()) {
            System.out.println("Code: " + type.getCode() + ", Name: " + type.getName());
        }
    }
}
