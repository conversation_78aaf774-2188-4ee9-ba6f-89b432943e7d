package com.hsobs.hs.modules.utils;

import com.hsobs.hs.modules.applyer.entity.HsQwApplyer;
import com.hsobs.hs.modules.estate.entity.HsQwPublicRentalEstate;
import com.hsobs.hs.modules.estate.service.HsQwPublicRentalEstateService;
import com.hsobs.hs.modules.house.entity.HsQwPublicRentalHouse;
import com.jeesite.common.utils.excel.ExcelImport;
import org.apache.poi.ss.usermodel.Row;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:application.xml"})
public class TestHsQwApplyInfoImportUtil {

    @Autowired
    private HsQwPublicRentalEstateService hsQwPublicRentalEstateService;

    @Test
    public void testImport() {


        // 从本地excel读取
        try {
            File classPath = new File(TestHsQwApplyInfoImportUtil.class.getResource("/").getFile());
            System.out.println(classPath.getParentFile().getAbsoluteFile());
            String fileName = classPath.getParentFile().getAbsoluteFile() + "/公租房档案数据含入住情况.xlsx";
            ExcelImport ei = new ExcelImport(fileName, 1);
            
            // 存储所有行数据
            List<Map<String, String>> dataList = new ArrayList<>();
            
            // 获取表头
            Row headerRow = ei.getRow(ei.getDataRowNum() - 1);
            List<String> headers = new ArrayList<>();
            for (int j = 0; j < ei.getLastCellNum(); j++) {
                Object headerVal = ei.getCellValue(headerRow, j);
                headers.add(headerVal != null ? headerVal.toString() : "");
            }
            
            // 读取数据行
            for (int i = ei.getDataRowNum(); i < ei.getLastDataRowNum(); i++) {
                Row row = ei.getRow(i);
                if (row == null) {
                    continue;
                }
                
                Map<String, String> rowData = new HashMap<>();
                for (int j = 0; j < ei.getLastCellNum(); j++) {
                    Object val = ei.getCellValue(row, j);
                    String headerName = j < headers.size() ? headers.get(j) : "Column" + j;
                    rowData.put(headerName, val != null ? val.toString() : "");
                }
                dataList.add(rowData);
            }
            
            // 处理数据并映射到实体
            processDataToEntities(dataList);
            
            System.out.println("导入成功，共处理 " + dataList.size() + " 条记录");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("导入失败：" + e.getMessage());
        }
    }
    
    /**
     * 将数据映射到不同的实体
     * @param dataList Excel数据列表
     */
    private void processDataToEntities(List<Map<String, String>> dataList) {
        for (Map<String, String> row : dataList) {
            // 1. 创建并保存房屋信息
            HsQwPublicRentalHouse house = createHouseEntity(row);
            // TODO: 保存house实体到数据库
            
            // 2. 创建并保存申请人信息
            HsQwApplyer applicant = createApplicantEntity(row);
            // TODO: 保存applicant实体到数据库
            
            // 3. 创建并保存配偶信息
            if (hasValue(row, "配偶")) {
                HsQwApplyer spouse = createSpouseEntity(row);
                // TODO: 保存spouse实体到数据库
            }
            
            // 4. 创建并保存家庭成员信息
            for (int i = 1; i <= 3; i++) {
                if (hasValue(row, "家庭成员" + i)) {
                    HsQwApplyer familyMember = createFamilyMemberEntity(row, i);
                    // TODO: 保存familyMember实体到数据库
                }
            }
        }
    }
    
    /**
     * 创建房屋实体
     */
    private HsQwPublicRentalHouse createHouseEntity(Map<String, String> row) {
        HsQwPublicRentalHouse house = null;
        String houseInfo = row.get("现租房位置");
        String estateInfo ="";
        String buildingNum = "";
        String unitInfo = "";
        
        // 添加空值检查，避免NullPointerException
        if (houseInfo != null && !houseInfo.isEmpty()) {
            // 五四北公租房一期1-2707，比如这样的租房信息，五四北公租房一期是小区名称，1-是1号楼，27是楼层，07是房间号，一般房间号不超过2位数，所以可以用正则表达式来匹配
            // 根据以上规则进行解析，编写正则表达式，识别1-2707的格式，最后两位是房间号，前面的是楼层号，中间的是楼栋号，最后是小区名称
            // 定义正则表达式
            String regex = "(.*?)(\\d+)-([\\d+]*)";
            // 使用正则表达式进行匹配
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
            java.util.regex.Matcher matcher = pattern.matcher(houseInfo);
            if (matcher.find()) {
                estateInfo = matcher.group(1);
                // 调用HsQwPublicRentalEstateService的findByName方法，根据小区名称查询小区信息
                
                buildingNum = matcher.group(2);
                unitInfo = matcher.group(3);
                house = new HsQwPublicRentalHouse();
                house.setBuildingNum(buildingNum);
                // 解析unitInfo，最后两位是房间号，前面余下的是楼层号
                if (unitInfo!= null &&!unitInfo.isEmpty()) {
                    String floorNum = unitInfo.substring(0, unitInfo.length() - 2);
                    house.setFloor(Long.valueOf(floorNum));
                    house.setUnitNum(unitInfo);
                }
                
            }
        }
          
        return house;
    }
    
    /**
     * 创建申请人实体
     */
    private HsQwApplyer createApplicantEntity(Map<String, String> row) {
        HsQwApplyer applicant = new HsQwApplyer();
        applicant.setName(row.get("申请人"));
//        applicant.setIdNumber(row.get("身份证号码"));
//        applicant.setWorkUnit(row.get("工作单位"));
//        applicant.setPhoneNumber(row.get("联系电话"));
//        applicant.setRelationship("申请人");
        return applicant;
    }
    
    /**
     * 创建配偶实体
     */
    private HsQwApplyer createSpouseEntity(Map<String, String> row) {
        HsQwApplyer spouse = new HsQwApplyer();
        spouse.setName(row.get("配偶"));
//        spouse.setIdNumber(row.get("配偶身份证号码"));
//        spouse.setRelationship("配偶");
        return spouse;
    }
    
    /**
     * 创建家庭成员实体
     */
    private HsQwApplyer createFamilyMemberEntity(Map<String, String> row, int memberIndex) {
        HsQwApplyer member = new HsQwApplyer();
        member.setName(row.get("家庭成员" + memberIndex));
//        member.setIdNumber(row.get("家庭成员" + memberIndex + "身份证号码"));
//        member.setMemberIdentity(row.get("家庭成员" + memberIndex + "身份"));
//        member.setRelationship("家庭成员" + memberIndex);
        return member;
    }
    
    /**
     * 检查字段是否有值
     */
    private boolean hasValue(Map<String, String> row, String key) {
        return row.containsKey(key) && row.get(key) != null && !row.get(key).trim().isEmpty();
    }

    public static void main(String[] args) throws Throwable {

        File classPath = new File(TestHsQwApplyInfoImportUtil.class.getResource("/").getFile());
        String fileName = classPath.getParentFile().getAbsoluteFile() + "/export.xlsx";
        ExcelImport ei = new ExcelImport(fileName, 1);

        for (int i = ei.getDataRowNum(); i < ei.getLastDataRowNum(); i++) {
            Row row = ei.getRow(i);
            if (row == null){
                continue;
            }
            for (int j = 0; j < ei.getLastCellNum(); j++) {
                Object val = ei.getCellValue(row, j);
                System.out.print(val+", ");
            }
            System.out.println();
        }

        System.out.println("Import success.");
    }
    
}
