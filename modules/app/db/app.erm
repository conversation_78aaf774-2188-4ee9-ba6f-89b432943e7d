<?xml version="1.0" encoding="UTF-8"?>
<diagram>
	<page_setting>
		<direction_horizontal>true</direction_horizontal>
		<scale>100</scale>
		<paper_size>A4 210 x 297 mm</paper_size>
		<top_margin>30</top_margin>
		<left_margin>30</left_margin>
		<bottom_margin>30</bottom_margin>
		<right_margin>30</right_margin>
	</page_setting>
	<category_index>0</category_index>
	<zoom>1.0</zoom>
	<x>0</x>
	<y>0</y>
	<default_color>
		<r>128</r>
		<g>128</g>
		<b>192</b>
	</default_color>
	<color>
		<r>255</r>
		<g>255</g>
		<b>255</b>
	</color>
	<font_name>Arial</font_name>
	<font_size>14</font_size>
	<settings>
		<database>StandardSQL</database>
		<capital>false</capital>
		<table_style></table_style>
		<notation></notation>
		<notation_level>0</notation_level>
		<notation_expand_group>true</notation_expand_group>
		<view_mode>2</view_mode>
		<outline_view_mode>1</outline_view_mode>
		<view_order_by>1</view_order_by>
		<auto_ime_change>false</auto_ime_change>
		<validate_physical_name>true</validate_physical_name>
		<use_bezier_curve>false</use_bezier_curve>
		<suspend_validator>false</suspend_validator>
		<export_setting>
			<export_ddl_setting>
				<output_path>db/app.sql</output_path>
				<encoding>UTF-8</encoding>
				<line_feed>CR+LF</line_feed>
				<is_open_after_saved>false</is_open_after_saved>
				<environment_id>7be191506f9daa8070b3ac14921dffd44063d2bb</environment_id>
				<category_id>null</category_id>
				<ddl_target>
					<create_comment>true</create_comment>
					<create_foreignKey>false</create_foreignKey>
					<create_index>true</create_index>
					<create_sequence>false</create_sequence>
					<create_table>true</create_table>
					<create_tablespace>false</create_tablespace>
					<create_trigger>false</create_trigger>
					<create_view>false</create_view>
					<drop_index>false</drop_index>
					<drop_sequence>false</drop_sequence>
					<drop_table>false</drop_table>
					<drop_tablespace>false</drop_tablespace>
					<drop_trigger>false</drop_trigger>
					<drop_view>false</drop_view>
					<inline_column_comment>false</inline_column_comment>
					<inline_table_comment>true</inline_table_comment>
					<comment_value_description>false</comment_value_description>
					<comment_value_logical_name>true</comment_value_logical_name>
					<comment_value_logical_name_description>false</comment_value_logical_name_description>
					<comment_replace_line_feed>false</comment_replace_line_feed>
					<comment_replace_string></comment_replace_string>
				</ddl_target>
			</export_ddl_setting>
			<export_excel_setting>
				<category_id>null</category_id>
				<output_path>db/app.xls</output_path>
				<template></template>
				<template_path></template_path>
				<used_default_template_lang>en</used_default_template_lang>
				<image_output></image_output>
				<is_open_after_saved>true</is_open_after_saved>
				<is_put_diagram>true</is_put_diagram>
				<is_use_logical_name>true</is_use_logical_name>
			</export_excel_setting>
			<export_html_setting>
				<output_dir></output_dir>
				<with_category_image>true</with_category_image>
				<with_image>true</with_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_html_setting>
			<export_image_setting>
				<output_file_path>db/app.png</output_file_path>
				<category_dir_path></category_dir_path>
				<with_category_image>true</with_category_image>
				<is_open_after_saved>true</is_open_after_saved>
			</export_image_setting>
			<export_java_setting>
				<java_output></java_output>
				<package_name></package_name>
				<class_name_suffix></class_name_suffix>
				<src_file_encoding></src_file_encoding>
				<with_hibernate>false</with_hibernate>
			</export_java_setting>
			<export_testdata_setting>
				<file_encoding></file_encoding>
				<file_path></file_path>
				<format>0</format>
			</export_testdata_setting>
		</export_setting>
		<category_settings>
			<free_layout>false</free_layout>
			<show_referred_tables>false</show_referred_tables>
			<categories>
			</categories>
		</category_settings>
		<translation_settings>
			<use>false</use>
			<translations>
			</translations>
		</translation_settings>
		<model_properties>
			<id></id>
			<height>-1</height>
			<width>-1</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>50</x>
			<y>50</y>
			<color>
				<r>255</r>
				<g>255</g>
				<b>255</b>
			</color>
			<connections>
			</connections>
			<display>false</display>
			<creation_date>2016-12-25 17:25:00</creation_date>
			<model_property>
				<name>Project Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Model Name</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Version</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Company</name>
				<value></value>
			</model_property>
			<model_property>
				<name>Author</name>
				<value></value>
			</model_property>
		</model_properties>
		<table_properties>
			<schema></schema>
		</table_properties>
		<environment_setting>
			<environment>
				<id>7be191506f9daa8070b3ac14921dffd44063d2bb</id>
				<name>Default</name>
			</environment>
		</environment_setting>
	</settings>
	<dictionary>
		<word>
			<id>2578ef48d1195629bb388537610bf6065269ff1d</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>APK下载地址</logical_name>
			<physical_name>apk_url</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b1c38fc18770e7498d8e022dfa151cfb173f334c</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>应用编号</logical_name>
			<physical_name>app_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>2c62d1b796c9f410f173fd467969f7a2ae70f93c</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>问题分类</logical_name>
			<physical_name>category</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>9fc6e78fd91670bd9c2cb2101cab6cd6f2bba436</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>联系方式</logical_name>
			<physical_name>contact</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>56cd0b6d6af5b51958700d13764ef73c1addeca4</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>问题和意见</logical_name>
			<physical_name>content</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>c71533e4d2f429ff8466fd6a8de5719f1741377b</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>租户代码</description>
			<logical_name>租户代码</logical_name>
			<physical_name>corp_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>11c59294fe142d108ca4dac5a03033f50188a450</id>
			<length>100</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>租户名称</description>
			<logical_name>租户名称</logical_name>
			<physical_name>corp_name</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>创建者</logical_name>
			<physical_name>create_by</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>263a88db11c2cbd69dfdcfeb6a2a7964e61582ed</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>提问人员姓名</logical_name>
			<physical_name>create_by_name</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>170410257b4d8e712b8c1d499b415de82ce9683c</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>创建时间</logical_name>
			<physical_name>create_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>a63e3fda50530388ba263296184d8a6919a75791</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>设备信息</logical_name>
			<physical_name>device_info</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>038ea74a700b763fa1f8fcdbd2c27e7941f82f7a</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 1</logical_name>
			<physical_name>extend_d1</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>19aa2e40abff2bb4e908f18f5f5e5b30a074cd8b</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 2</logical_name>
			<physical_name>extend_d2</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>06f59428b50e62e636a58a2f3cbcee2c75764506</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 3</logical_name>
			<physical_name>extend_d3</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>8f08aeb4b9ceb072f0232d3d23fd5371ef8f379c</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Date 4</logical_name>
			<physical_name>extend_d4</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>0733a829c52af8af725ace50d4ed3e0179969f56</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 1</logical_name>
			<physical_name>extend_f1</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>0b7230df29d6df213507c8855d019d2d55c9a561</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 2</logical_name>
			<physical_name>extend_f2</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>6cb3dad83f3e1f358155892dcee3167ef55bacc7</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 3</logical_name>
			<physical_name>extend_f3</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>e54b261fc7ab81e0ef19a89e657453664abe5593</id>
			<length>19</length>
			<decimal>4</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Float 4</logical_name>
			<physical_name>extend_f4</physical_name>
			<type>decimal(p,s)</type>
		</word>
		<word>
			<id>b67071354a8d43cabe9a0ac29303f8b144a15985</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 1</logical_name>
			<physical_name>extend_i1</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>70b8240f049f658b97394b7d4784481fdb477f5c</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 2</logical_name>
			<physical_name>extend_i2</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>ce377c081415847061b2efc58b701ff847dcaaca</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 3</logical_name>
			<physical_name>extend_i3</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>fb96f6dc60d31576c278a3c64024154eda67d3fe</id>
			<length>19</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 Integer 4</logical_name>
			<physical_name>extend_i4</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>3962f4b130a803841a193a163e42679946b5ae1f</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 JSON</logical_name>
			<physical_name>extend_json</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b1acb2df17677e0ed818dea6e1150bfc088fcd50</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 1</logical_name>
			<physical_name>extend_s1</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>d27290b84b207a7c87f4d78d80e62048c67fac04</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 2</logical_name>
			<physical_name>extend_s2</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>5e25d7f0e327d2d760a04b32140b639bb96bfd8d</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 3</logical_name>
			<physical_name>extend_s3</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>d05572af90653d4ddfd880402a15ed9b27c81888</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 4</logical_name>
			<physical_name>extend_s4</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>acc372d31709a1133f6c13fc8abfef7881ca26ed</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 5</logical_name>
			<physical_name>extend_s5</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>59763a16504e4136a7a2fbed38476272d337105c</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 6</logical_name>
			<physical_name>extend_s6</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>600b96b04bf4f65919eabfc6613d3c5e370931e1</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 7</logical_name>
			<physical_name>extend_s7</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>a9dc80e5c484dc7b9d5fff9294eaa383819edc26</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>扩展 String 8</logical_name>
			<physical_name>extend_s8</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>编号</logical_name>
			<physical_name>id</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>e4465172e2cc64907a386237cc7d0c7d79a074dc</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>父级编号</logical_name>
			<physical_name>parent_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b4f9837136b0a84afc9a611a563fb51141fdac1f</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>所有父级编号</logical_name>
			<physical_name>parent_codes</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>备注信息</logical_name>
			<physical_name>remarks</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>11c8c4b9e171ed03f04eb135472213904d90b5d1</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>回复意见</logical_name>
			<physical_name>reply_content</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>5cd4dcea8b3a75a9507ee8ed8f52ef1f93699e31</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>回复时间</logical_name>
			<physical_name>reply_date</physical_name>
			<type>date</type>
		</word>
		<word>
			<id>4908d39186280a485543b320f6f714f4609621b7</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>回复人员</logical_name>
			<physical_name>reply_user_code</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>a4e45f7c519ddabe676921eb3e9366bbd0288caf</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>回复人员姓名</logical_name>
			<physical_name>reply_user_name</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>509b87a5672e76182851763474a19dddd7645c3d</id>
			<length>500</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>资源下载地址</logical_name>
			<physical_name>res_url</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>a5d0a391d0d09acab89c829d271b0a96a7058ca0</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>状态（0正常 1删除 2停用 3冻结 4审核 5驳回 9草稿）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>70d065a29d01e3875167a20441e0ebbfaf5a05f8</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>状态（0正常 1删除 2停用）</logical_name>
			<physical_name>status</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>8fb086ad8b2a35cdf18c1a1e247005cdb140faf0</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>是否最末级</logical_name>
			<physical_name>tree_leaf</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>f22022df4ccf9c7081b4d8f98deed04c1d8c987c</id>
			<length>4</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>层次级别</logical_name>
			<physical_name>tree_level</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>44f852382fba107fe668304530dea0233f38a321</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>全节点名</logical_name>
			<physical_name>tree_names</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>65265fefa00907a60e8fe2ebd6d2ad58760415e5</id>
			<length>10</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>排序号（升序）</logical_name>
			<physical_name>tree_sort</physical_name>
			<type>decimal(p)</type>
		</word>
		<word>
			<id>cf5f1e3cc5cc0869f539c3c5b2adf05677e367b8</id>
			<length>767</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>所有排序号</logical_name>
			<physical_name>tree_sorts</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>c60616703cf07aaf21f84215d092e2101efb9bca</id>
			<length>64</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>更新者</logical_name>
			<physical_name>update_by</physical_name>
			<type>varchar(n)</type>
		</word>
		<word>
			<id>b5c3a59170184a01c15c78a6778cb73083fe6321</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>更新时间</logical_name>
			<physical_name>update_date</physical_name>
			<type>datetime</type>
		</word>
		<word>
			<id>7b7b27274e1739a930b8f238c1f96b28bd322b7f</id>
			<length>1000</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>升级内容</logical_name>
			<physical_name>up_content</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>857a66841a83999b4232dc2f7e5fb1555dbfea71</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description></description>
			<logical_name>发布时间</logical_name>
			<physical_name>up_date</physical_name>
			<type>date</type>
		</word>
		<word>
			<id>a5dd184ae6f69fe7519b2ec91e140cc359388152</id>
			<length>200</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>如：V4.2.3 发布，新增某某功能</description>
			<logical_name>升级标题</logical_name>
			<physical_name>up_title</physical_name>
			<type>nvarchar(n)</type>
		</word>
		<word>
			<id>53ffc7afa48fa7e3ca6bb59a23e8a51886b655ab</id>
			<length>1</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>1提示升级 2不提示升级  3强制升级</description>
			<logical_name>升级类型</logical_name>
			<physical_name>up_type</physical_name>
			<type>character(n)</type>
		</word>
		<word>
			<id>62431038f1adc96d0580d3859464fc4a052aa331</id>
			<length>null</length>
			<decimal>null</decimal>
			<array>false</array>
			<array_dimension>null</array_dimension>
			<unsigned>false</unsigned>
			<zerofill>false</zerofill>
			<binary>false</binary>
			<args></args>
			<char_semantics>false</char_semantics>
			<description>内部版本号、数值类型</description>
			<logical_name>升级版本</logical_name>
			<physical_name>up_version</physical_name>
			<type>numeric</type>
		</word>
	</dictionary>
	<tablespace_set>
	</tablespace_set>
	<contents>
		<table>
			<id>2c60884d099bae254d29f16c5919af91ae8a8a82</id>
			<height>438</height>
			<width>383</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>600</x>
			<y>108</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_app_comment</physical_name>
			<logical_name>APP意见反馈</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>9c3bc3da1865da952bfe06f4d3f6d8dd935e538b</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2c62d1b796c9f410f173fd467969f7a2ae70f93c</word_id>
					<id>e01fa6cd91e27ef2ee3aa300f0c035bb50f86630</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>56cd0b6d6af5b51958700d13764ef73c1addeca4</word_id>
					<id>277ea5cd5e610b46fc4d3db3fb9d8a2a4868fd33</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>9fc6e78fd91670bd9c2cb2101cab6cd6f2bba436</word_id>
					<id>6aa0973786dfcc49debb8b751529c08fbe5161e4</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
				<normal_column>
					<word_id>263a88db11c2cbd69dfdcfeb6a2a7964e61582ed</word_id>
					<id>87ce14b463a5a5f8a376d123415d661a99b98358</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a63e3fda50530388ba263296184d8a6919a75791</word_id>
					<id>127b8c2472604032a1df7662f7009b114b07013c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>5cd4dcea8b3a75a9507ee8ed8f52ef1f93699e31</word_id>
					<id>d4bb91272bafe7595953350bbf7901be28b7bfb9</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>date</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>11c8c4b9e171ed03f04eb135472213904d90b5d1</word_id>
					<id>1119aeef86bde8fa3a61e50c356a5084940bc723</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>4908d39186280a485543b320f6f714f4609621b7</word_id>
					<id>b2f66f0a613c2bf2042a6bbd95d6f8927fc0ae40</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a4e45f7c519ddabe676921eb3e9366bbd0288caf</word_id>
					<id>3479dad53bbde9e629923602b9606264202e5aee</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
		<table>
			<id>9680b7d7eb26dcd11f9688429a712f9fc2a16cd0</id>
			<height>438</height>
			<width>383</width>
				<font_name>Arial</font_name>
				<font_size>14</font_size>
			<x>132</x>
			<y>108</y>
			<color>
				<r>128</r>
				<g>128</g>
				<b>192</b>
			</color>
			<connections>
			</connections>
			<physical_name>js_app_upgrade</physical_name>
			<logical_name>APP升级版本</logical_name>
			<description></description>
			<constraint></constraint>
			<primary_key_name></primary_key_name>
			<option></option>
			<columns>
				<normal_column>
					<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
					<id>e66aca95de48cc89815ea6442afcbd674439e956</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>true</not_null>
					<primary_key>true</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>b1c38fc18770e7498d8e022dfa151cfb173f334c</word_id>
					<id>29702ae4ad71280d2137a05f3b9dfa88caa703ca</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>a5dd184ae6f69fe7519b2ec91e140cc359388152</word_id>
					<id>d72c3195fee0e8e5fc0c6b0205e04d63e8a00234</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>7b7b27274e1739a930b8f238c1f96b28bd322b7f</word_id>
					<id>00a558b39060becbfe297005f8ca74820a126cc9</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>nvarchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>62431038f1adc96d0580d3859464fc4a052aa331</word_id>
					<id>8abbdf5a771f2b560d5a6e9497b5d2c6f0645c6c</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>numeric</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>53ffc7afa48fa7e3ca6bb59a23e8a51886b655ab</word_id>
					<id>1066e3c4816efac8aabd82758fde02ff3ef617ec</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>character(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>857a66841a83999b4232dc2f7e5fb1555dbfea71</word_id>
					<id>1053739ace2eb3e568f4b522ab0fee630abb9b04</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>date</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>2578ef48d1195629bb388537610bf6065269ff1d</word_id>
					<id>2513eb28d57e193f4e930b5a7b228494df4778da</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<normal_column>
					<word_id>509b87a5672e76182851763474a19dddd7645c3d</word_id>
					<id>2c3e36b6a840092e0b682461cce5963308a6a2d8</id>
					<description></description>
					<unique_key_name></unique_key_name>
					<logical_name></logical_name>
					<physical_name></physical_name>
					<type>varchar(n)</type>
					<constraint></constraint>
					<default_value></default_value>
					<auto_increment>false</auto_increment>
					<foreign_key>false</foreign_key>
					<not_null>false</not_null>
					<primary_key>false</primary_key>
					<unique_key>false</unique_key>
					<character_set></character_set>
					<collation></collation>
					<sequence>
						<name></name>
						<schema></schema>
						<increment></increment>
						<min_value></min_value>
						<max_value></max_value>
						<start></start>
						<cache></cache>
						<nocache>false</nocache>
						<cycle>false</cycle>
						<order>false</order>
						<description></description>
						<data_type></data_type>
						<decimal_size>0</decimal_size>
					</sequence>
				</normal_column>
				<column_group>35ae805d1da92afdb99b2fe8c536d1649356fccd</column_group>
			</columns>
			<indexes>
			</indexes>
			<complex_unique_key_list>
			</complex_unique_key_list>
			<table_properties>
				<schema></schema>
			</table_properties>
		</table>
	</contents>
	<column_groups>
			<column_group>
				<id>845c82ebd869d5620b1ef2c2b6f438b11a045082</id>
				<group_name>BaseEntity</group_name>
				<columns>
					<normal_column>
						<word_id>e3325b6b7dc29b87da73eedcfff2a9c4cd245579</word_id>
						<id>02ecedc0de5850cba25bc91919ed39d414b74111</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a5d0a391d0d09acab89c829d271b0a96a7058ca0</word_id>
						<id>2fe6a36385238c1b21c76deae00a7afa00ff5538</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>ea920cba2fe0eaee64a2310ece7cda4b198b37ec</id>
				<group_name>BaseEntityCorp</group_name>
				<columns>
					<normal_column>
						<word_id>c71533e4d2f429ff8466fd6a8de5719f1741377b</word_id>
						<id>b8ea4b73d6d32a222e5abfd453287575ae518480</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value>0</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>11c59294fe142d108ca4dac5a03033f50188a450</word_id>
						<id>b94f5fe344185c40739cf93d1090686001bb11e0</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value>JeeSite</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>35ae805d1da92afdb99b2fe8c536d1649356fccd</id>
				<group_name>DataEntity</group_name>
				<columns>
					<normal_column>
						<word_id>70d065a29d01e3875167a20441e0ebbfaf5a05f8</word_id>
						<id>f0036584bd8711715579d21994a0105935605a7e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value>0</default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</word_id>
						<id>c391a15752a8eb58bc558a39d1b431f7ee125e0e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>170410257b4d8e712b8c1d499b415de82ce9683c</word_id>
						<id>e2e82ba86e15fd67397355e711255b1625078ae1</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>c60616703cf07aaf21f84215d092e2101efb9bca</word_id>
						<id>fd0546fc2d4e01c35dcbc23913add68a99fabd73</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b5c3a59170184a01c15c78a6778cb73083fe6321</word_id>
						<id>f8ea4fc4a778a0b94398a661a1ed8608f0e8d28d</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</word_id>
						<id>69e01b6d4f42df40a09540ef4ba10ed8e006abaa</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>85024a2953cf3e3c9c1cce49b2351853ab0d125b</id>
				<group_name>DataEntityNoStatus</group_name>
				<columns>
					<normal_column>
						<word_id>f60ca81cf3f5c08c518d0266d2b8fd1b219a9802</word_id>
						<id>e5355faba5ec3c9128507dd4c48ea9230631cf83</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>170410257b4d8e712b8c1d499b415de82ce9683c</word_id>
						<id>6bed374c39d181003a4f92d76d79a4119176ba0c</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>c60616703cf07aaf21f84215d092e2101efb9bca</word_id>
						<id>f9db19bb567760bbdd554d75bbfdc891c89f9da9</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b5c3a59170184a01c15c78a6778cb73083fe6321</word_id>
						<id>ee78b079f7d319bf8119fd01439cd97424ff49fa</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>1a77879eeba6dd8a2ca8de0ca0cbd6db5ddeb7af</word_id>
						<id>f7b88ecec0ef386bb384c228842a7587432112fb</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>118dab95fc1f792cd468b9f66af2d4fabd98c39b</id>
				<group_name>ExtendEntity</group_name>
				<columns>
					<normal_column>
						<word_id>b1acb2df17677e0ed818dea6e1150bfc088fcd50</word_id>
						<id>6ccadddab6ce48441ca7abd798cda6f3debf4a0c</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>d27290b84b207a7c87f4d78d80e62048c67fac04</word_id>
						<id>93ab0ba3b47b01934614dbd3e572358c9f99e6ea</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>5e25d7f0e327d2d760a04b32140b639bb96bfd8d</word_id>
						<id>a78c7961910a5e697027d1a3530b1afaa8ea8c94</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>d05572af90653d4ddfd880402a15ed9b27c81888</word_id>
						<id>40085364ec7a58653e96f8659aadd258d7556bc7</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>acc372d31709a1133f6c13fc8abfef7881ca26ed</word_id>
						<id>9787d7fe93ee31c5b4979fd620ff6e4b2777eccf</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>59763a16504e4136a7a2fbed38476272d337105c</word_id>
						<id>95c55b81b7e9e1a9bb01aa3d88fb90c648641c4e</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>600b96b04bf4f65919eabfc6613d3c5e370931e1</word_id>
						<id>16f44dfc7964796f109293bc49afd58dcb4eec1f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>a9dc80e5c484dc7b9d5fff9294eaa383819edc26</word_id>
						<id>39b1dffa083f74afc30df621845cf7f0ed71394f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>nvarchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b67071354a8d43cabe9a0ac29303f8b144a15985</word_id>
						<id>7584cc6360ae7edc99e1f619042eba5865b2c4c7</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>70b8240f049f658b97394b7d4784481fdb477f5c</word_id>
						<id>f0b5383e05c6b3f6e5f65b33b33009826c83d014</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>ce377c081415847061b2efc58b701ff847dcaaca</word_id>
						<id>260d5f31009fff18000d1e64f4f877926e621306</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>fb96f6dc60d31576c278a3c64024154eda67d3fe</word_id>
						<id>a83144f40e7ae64e46a4b4ed651379774a953b17</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>0733a829c52af8af725ace50d4ed3e0179969f56</word_id>
						<id>2a5203a275171a250870cf6cb224a910aa9354ec</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>0b7230df29d6df213507c8855d019d2d55c9a561</word_id>
						<id>3ef5bd65a7dcd74b9a9d8a292ec395f66b7de32b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>6cb3dad83f3e1f358155892dcee3167ef55bacc7</word_id>
						<id>01d0849bdda56a8d8f24befdadc3fc9b007ae92b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>e54b261fc7ab81e0ef19a89e657453664abe5593</word_id>
						<id>1c8ed63d72f40f0fe2f05815675771bdf3f824f8</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p,s)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>038ea74a700b763fa1f8fcdbd2c27e7941f82f7a</word_id>
						<id>2b49e875138bfb329aaa352629650b7881435123</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>19aa2e40abff2bb4e908f18f5f5e5b30a074cd8b</word_id>
						<id>5c6ec16226d85b0411b7077cb9a6e0c7aa8d74d1</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>06f59428b50e62e636a58a2f3cbcee2c75764506</word_id>
						<id>d92b8f7fa7a2be49c7f00c447a603b136e84261d</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8f08aeb4b9ceb072f0232d3d23fd5371ef8f379c</word_id>
						<id>095a76f07a3cd2bdc6cc442757c11012e1974f4a</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>datetime</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>3962f4b130a803841a193a163e42679946b5ae1f</word_id>
						<id>42c5d8f490f69b93e77698efa030ca23988ae696</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>false</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
			<column_group>
				<id>a535b6c506004a7fdf4d48984c9ff2cfa59c157a</id>
				<group_name>TreeEntity</group_name>
				<columns>
					<normal_column>
						<word_id>e4465172e2cc64907a386237cc7d0c7d79a074dc</word_id>
						<id>394369b90c0a5b6efeed3cf823c642605d7a1653</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>b4f9837136b0a84afc9a611a563fb51141fdac1f</word_id>
						<id>e8d877396943acfec73023dba2c1c6e3d7802d62</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>65265fefa00907a60e8fe2ebd6d2ad58760415e5</word_id>
						<id>23f973124aedd0244533f4e7b3b103c548b966be</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>cf5f1e3cc5cc0869f539c3c5b2adf05677e367b8</word_id>
						<id>984d5eac2b3221118a61655e4a5a49c78e0f0151</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>8fb086ad8b2a35cdf18c1a1e247005cdb140faf0</word_id>
						<id>b2f246a3f0ade317eaa9915e2fd539abae5a5ec8</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>character(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>f22022df4ccf9c7081b4d8f98deed04c1d8c987c</word_id>
						<id>f5a9968479420f08da2e98d21136b3ed4b6e396f</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>decimal(p)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
					<normal_column>
						<word_id>44f852382fba107fe668304530dea0233f38a321</word_id>
						<id>618194ebfc8c6c42efcef3a4af0b8054f6af209b</id>
						<description></description>
						<unique_key_name></unique_key_name>
						<logical_name></logical_name>
						<physical_name></physical_name>
						<type>varchar(n)</type>
						<constraint></constraint>
						<default_value></default_value>
						<auto_increment>false</auto_increment>
						<foreign_key>false</foreign_key>
						<not_null>true</not_null>
						<primary_key>false</primary_key>
						<unique_key>false</unique_key>
						<character_set></character_set>
						<collation></collation>
						<sequence>
							<name></name>
							<schema></schema>
							<increment></increment>
							<min_value></min_value>
							<max_value></max_value>
							<start></start>
							<cache></cache>
							<nocache>false</nocache>
							<cycle>false</cycle>
							<order>false</order>
							<description></description>
							<data_type></data_type>
							<decimal_size>0</decimal_size>
						</sequence>
					</normal_column>
				</columns>
			</column_group>
	</column_groups>
	<test_data_list>
	</test_data_list>
	<sequence_set>
	</sequence_set>
	<trigger_set>
	</trigger_set>
	<change_tracking_list>
	</change_tracking_list>
</diagram>
