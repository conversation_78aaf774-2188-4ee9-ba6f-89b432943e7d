<?xml version="1.0" encoding="UTF-8"?><ureport><cell expand="None" name="A1" row="1" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bgcolor="240,240,240" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[登录名]]></simple-value></cell><cell expand="None" name="B1" row="1" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bgcolor="240,240,240" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[用户名]]></simple-value></cell><cell expand="None" name="C1" row="1" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bgcolor="240,240,240" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[创建时间]]></simple-value></cell><cell expand="None" name="D1" row="1" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" bgcolor="240,240,240" bold="true" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><simple-value><![CDATA[用户类型]]></simple-value></cell><cell expand="Down" name="A2" row="2" col="1"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="用户列表" aggregate="select" property="login_code" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="B2" row="2" col="2"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="用户列表" aggregate="select" property="user_name" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="C2" row="2" col="3"><cell-style font-size="10" format="yyyy-MM-dd HH:mm" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="用户列表" aggregate="select" property="create_date" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="D2" row="2" col="4"><cell-style font-size="10" align="center" valign="middle"><left-border width="1" style="solid" color="0,0,0"/><right-border width="1" style="solid" color="0,0,0"/><top-border width="1" style="solid" color="0,0,0"/><bottom-border width="1" style="solid" color="0,0,0"/></cell-style><dataset-value dataset-name="用户列表" aggregate="select" property="user_type" order="none" mapping-type="simple"></dataset-value></cell><cell expand="Down" name="A3" row="3" col="1" col-span="4"><cell-style font-size="10" align="center" valign="middle"></cell-style><chart-value><dataset dataset-name="用户汇总" type="bar" category-property="user_type" series-property="user_type" series-type="property" series-text="44,33" value-property="count(1)" collect-type="select"/></chart-value></cell><cell expand="None" name="A4" row="4" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B4" row="4" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C4" row="4" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D4" row="4" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="A5" row="5" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B5" row="5" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C5" row="5" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D5" row="5" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="A6" row="6" col="1"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="B6" row="6" col="2"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="C6" row="6" col="3"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><cell expand="None" name="D6" row="6" col="4"><cell-style font-size="9" forecolor="0,0,0" font-family="宋体" align="center" valign="middle"></cell-style><simple-value><![CDATA[]]></simple-value></cell><row row-number="1" height="22" band="headerrepeat"/><row row-number="2" height="21"/><row row-number="3" height="164"/><row row-number="4" height="19"/><row row-number="5" height="19"/><row row-number="6" height="19"/><column col-number="1" width="106"/><column col-number="2" width="107"/><column col-number="3" width="125"/><column col-number="4" width="74"/><datasource name="JeeSite" type="buildin"><dataset name="用户列表" type="sql"><sql><![CDATA[${
  "select * from js_sys_user where 1=1" 
  + (emptyparam("loginCode") == false
     ? " and login_code=:loginCode" : "")
  + (emptyparam("userName") == false
     ? " and user_name like concat(\'%\',:userName,\'%\')" : "")
  + " order by user_name"
}]]></sql><field name="user_code"/><field name="login_code"/><field name="user_name"/><field name="password"/><field name="email"/><field name="mobile"/><field name="phone"/><field name="sex"/><field name="avatar"/><field name="sign"/><field name="wx_openid"/><field name="mobile_imei"/><field name="user_type"/><field name="ref_code"/><field name="ref_name"/><field name="mgr_type"/><field name="pwd_security_level"/><field name="pwd_update_date"/><field name="pwd_update_record"/><field name="pwd_question"/><field name="pwd_question_answer"/><field name="pwd_question_2"/><field name="pwd_question_answer_2"/><field name="pwd_question_3"/><field name="pwd_question_answer_3"/><field name="pwd_quest_update_date"/><field name="last_login_ip"/><field name="last_login_date"/><field name="freeze_date"/><field name="freeze_cause"/><field name="user_weight"/><field name="status"/><field name="create_by"/><field name="create_date"/><field name="update_by"/><field name="update_date"/><field name="remarks"/><field name="corp_code"/><field name="corp_name"/><field name="extend_s1"/><field name="extend_s2"/><field name="extend_s3"/><field name="extend_s4"/><field name="extend_s5"/><field name="extend_s6"/><field name="extend_s7"/><field name="extend_s8"/><field name="extend_i1"/><field name="extend_i2"/><field name="extend_i3"/><field name="extend_i4"/><field name="extend_f1"/><field name="extend_f2"/><field name="extend_f3"/><field name="extend_f4"/><field name="extend_d1"/><field name="extend_d2"/><field name="extend_d3"/><field name="extend_d4"/><field name="extend_json"/><parameter name="loginCode" type="String" default-value=""/><parameter name="userName" type="String" default-value=""/></dataset><dataset name="用户汇总" type="sql"><sql><![CDATA[select user_type, count(1) from js_sys_user group by user_type]]></sql><field name="user_type"/><field name="count(1)"/></dataset></datasource><paper type="A4" left-margin="90" right-margin="90"
    top-margin="72" bottom-margin="72" paging-mode="fitpage" fixrows="0"
    width="595" height="842" orientation="portrait" html-report-align="left" bg-image="" html-interval-refresh-value="0" column-enabled="false"></paper></ureport>