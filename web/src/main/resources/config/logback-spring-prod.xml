<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">

	<!-- Log file path -->
	<property name="log.path" value="${logPath:-${java.io.tmpdir:-.}}/logs" />
	
	<!-- Framework level setting -->
	<include resource="config/logger-core.xml"/>
	<logger name="org.mybatis.spring.transaction" level="INFO" />
	<logger name="org.flowable.ui.modeler.domain" level="INFO" />
	<logger name="org.flowable.idm.engine.impl.persistence" level="INFO" />
	<logger name="org.flowable.task.service.impl.persistence" level="INFO" />
	<logger name="org.flowable.identitylink.service.impl.persistence" level="INFO" />
	<logger name="org.flowable.variable.service.impl.persistence" level="INFO" />
	<logger name="org.flowable.engine.impl.persistence" level="INFO" />
	<logger name="com.jeesite" level="INFO" />
	<logger name="com.jeesite.common.mybatis.mapper" level="INFO" />
	
	<!-- Project level setting -->
	<!-- <logger name="your.package" level="DEBUG" /> -->
	
	<!-- Console log output -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{MM-dd HH:mm:ss.SSS} %clr(%-5p) %clr([%-39logger{39}]){cyan} - %m%n%wEx</pattern>
		</encoder>
	</appender>

	<!-- Log file debug output -->
	<appender name="debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.path}/debug.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${log.path}/debug.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
			<maxFileSize>50MB</maxFileSize>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p ${PID:- } [%15.15t] [%-39logger{39}] [%X{TRACE_ID}] - %m%n%wEx</pattern>
		</encoder>
		<!--<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>DENY</onMatch>
			<onMismatch>NEUTRAL</onMismatch>
		</filter>-->
	</appender>

	<!-- Log file error output -->
	<appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.path}/error.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${log.path}/error.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
			<maxFileSize>50MB</maxFileSize>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p ${PID:- } [%15.15t] [%-39logger{39}] [%X{TRACE_ID}] - %m%n%wEx</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
	</appender>

	<!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
	<root level="WARN">
		<appender-ref ref="console" />
		<appender-ref ref="debug" />
		<appender-ref ref="error" />
	</root>

</configuration>